import { Factor } from '@tutum/hermes/bff/catalog_goa_common';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  Contract,
  ContractChargeSystems,
  ContractDetails,
  ContractFuntions,
  HpmFunctionType,
} from '@tutum/hermes/bff/legacy/app_mvz_contract';
import { Highlight } from '@tutum/hermes/bff/legacy/common';
import { Certainty } from '@tutum/hermes/bff/service_domains_patient_file';
import { DoctorFunctionType } from '@tutum/hermes/bff/service_domains_patient_participation';
import {
  IMessageEvent,
  TMessageEventStatus,
} from '@tutum/infrastructure/webworker';

const CONTRACT_ADD = 'CONTRACT_ADD';
const CONTRACT_SEARCH = 'CONTRACT_SEARCH';
const DOES_CONTRACT_SUPPORT_FUNCTIONS = 'DOES_CONTRACT_SUPPORT_FUNCTIONS';
const DOES_CONTRACT_SUPPORT_HPM_FUNCTIONS =
  'DOES_CONTRACT_SUPPORT_HPM_FUNCTIONS';
const CONTRACT_GET_META_DATA = 'CONTRACT_GET_META_DATA';
const CONTRACT_ADD_META_DATA = 'CONTRACT_ADD_META_DATA';
const GET_CONTRACT_BY_ID = 'GET_CONTRACT_BY_ID';

export abstract class BaseContractSearchMessageEvent implements IMessageEvent {
  id: string;
  status: TMessageEventStatus;
  type: string;
  data: IContractSearchRequest;
}

export interface IComplianceCondition {
  checkDate?: Date;
  kvRegion?: string;
  ikGroup?: number;
}
export interface IContractSearchRequest {
  chargeSystemId?: string;
  query?: string;
  functions?: string[];
  hpmFunctions?: string[];
  encounterDate?: number;
  contractId?: string;
  contractIds?: string[];
  complianceCondition?: IComplianceCondition;
  contract: ContractDetails;
  contracts: Contract[];
}

export interface IContractAcuteDiagnose {
  v: string;
  validFrom?: number;
  validTo?: number;
}
export interface IContractIndex {
  indexer;
  identification: string;
  functions: ContractFuntions[];
  hpmFunctions?: HpmFunctionType[];
  chargeSystems: ContractChargeSystems[];
  moduleChargeSystems: ContractChargeSystems[];
}

export interface IContractData extends IReferenceDate {
  id: number;
  rules?: IContractRules;
  conditions?: IContractConditions;
  code?: string;
  description?: string;
  group?: boolean;
  mainGroup?: MainGroup;
  evaluation?: number;
  unit?: string;
  price?: number;
  factor?: Factor;

  isSelfCreated?: boolean;
  isBlankService: boolean;
  isActive: boolean;
  chargeSystemId?: string;
  isGroup?: boolean;
  highlight?: { [key: string]: Highlight }
}

export interface IContractRules {
  excludedServices?: IRuleExcludedServices[];
  // includedDiagnose?: IRuleIncludedDiagnose[];
  requiredInformation?: IRequiredInformation[];
  doctorFunctionTypes?: DoctorFunctionType[];
}

export interface IRequiredInformation {
  requiredLanr: boolean;
  requiredBsnr: boolean;
}

export interface IRuleExcludedServices extends IReferenceDate {
  excludedService: string;
  conditions?: IContractConditions[];
}

export interface IRuleIncludedDiagnose {
  includedDiagnoses: IIncludedDiagnoses;
  conditions?: IContractConditions[];
}

export interface IIncludedDiagnoses {
  permanent: boolean;
  certainty: Certainty;
  includedDiagnose: IIncludedDiagnose[];
}

export interface IIncludedDiagnose {
  code: string;
  genericEntry: boolean;
  maxAge: number;
}

export interface IContractConditions {
  kvRegionInclusions: IKvRegionInclusion[];
  iKInclusions: IKInclusion[];
  iKGroupInclusion: IKGroupInclusion[];
}

export interface IKvRegionInclusion extends IReferenceDate {
  okv: string;
}

export interface IKInclusion extends IReferenceDate {
  ik: string;
}

export interface IKGroupInclusion extends IReferenceDate {
  id: string;
}

export interface IReferenceDate {
  validFrom: number;
  validTo?: number;
}

export default {
  CONTRACT_ADD,
  CONTRACT_SEARCH,
  DOES_CONTRACT_SUPPORT_FUNCTIONS,
  DOES_CONTRACT_SUPPORT_HPM_FUNCTIONS,
  CONTRACT_ADD_META_DATA,
  CONTRACT_GET_META_DATA,
  GET_CONTRACT_BY_ID,
};
