import { isNumber } from '@tutum/design-system/infrastructure/utils';
// import { UnitStatistiks } from '@tutum/hermes/bff/catalog_sdebm_common';
import {
  // BlankService,
  Contract,
  ContractDetails,
  getContractById,
  GetContractByIdRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_contract';
import { getDocResult } from '@tutum/infrastructure/shared/flexsearch';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { IMessageEvent } from '@tutum/infrastructure/webworker';
import { execute } from '@tutum/infrastructure/webworker/background';
import { Document } from 'flexsearch';
import ContractSearchType, {
  BaseContractSearchMessageEvent,
  IContractData,
  IContractIndex,
  IContractSearchRequest,
} from './contract-search.type';
import { ResponseType } from '@tutum/hermes/bff/legacy/api_client';

const contractIndexCache = new Map<string, IContractIndex>();
const contractBlankServiceIndexCache = new Map<string, IContractIndex>();
const contractMetaIndexCache = new Map<string, Contract>();
const contractPromiseCache = new Map<string, Promise<any>>();

async function contractSearchHandler(
  messageEvent: IMessageEvent
): Promise<void> {
  const { id, type, data } = messageEvent as BaseContractSearchMessageEvent;
  switch (type) {
    case ContractSearchType.CONTRACT_ADD:
      await execute<void>({
        id,
        type,
        func: () =>
          addContract(
            data.chargeSystemId || '',
            data.contractId || '',
            data.contract
          ),
      });
      break;
    case ContractSearchType.CONTRACT_SEARCH:
      await execute<IContractData[]>({
        id,
        type,
        func: () => contractSearch(data),
      });
      break;
    case ContractSearchType.DOES_CONTRACT_SUPPORT_FUNCTIONS:
      await execute<boolean>({
        id,
        type,
        func: () => doesContractSupportFunctions(data),
      });
      break;
    case ContractSearchType.DOES_CONTRACT_SUPPORT_HPM_FUNCTIONS:
      await execute<boolean>({
        id,
        type,
        func: () => doesContractSupportHpmFunctions(data),
      });
      break;
    case ContractSearchType.CONTRACT_ADD_META_DATA:
      await execute<void>({
        id,
        type,
        func: () => addContractsMetaData(data.contracts),
      });
      break;
    case ContractSearchType.CONTRACT_GET_META_DATA:
      await execute<Contract | undefined>({
        id,
        type,
        func: () => getContractMetaData(data),
      });
      break;
    case ContractSearchType.GET_CONTRACT_BY_ID:
      await execute<IContractIndex | undefined>({
        id,
        type,
        func: () =>
          workerGetContractById(data.contractId || '', data.chargeSystemId),
      });
      break;
    default:
      break;
  }
}

export default {
  contractSearchHandler,
};
// -------------------------------------------------------------------------- //

async function addContractsMetaData(contracts: Contract[]): Promise<void> {
  contracts.forEach((contract) => {
    contractMetaIndexCache.set(contract.contractId, contract);
  });
  return;
}

function newIndexer() {
  return new Document<IContractData, true>({
    stemmer: false,
    document: {
      id: 'id',
      store: true,
      index: [
        {
          field: 'code',
          tokenize: 'forward',
        },
        {
          field: 'description',
          tokenize: 'forward',
        },
      ],
    },
  });
}

async function addContract(
  chargeSystemId: string,
  contractId: string,
  data: ContractDetails
): Promise<void> {
  const indexer = newIndexer();

  const blankServiceIndexer = new Document<IContractData, true>({
    stemmer: false,
    matcher: {
      '[äÄ]': 'ae',
      '[öÖ]': 'oe',
      '[üÜ]': 'ue',
      '[ß]': 'ss',
    },
    document: {
      id: 'id',
      store: true,
      index: [
        {
          field: 'code',
          tokenize: 'forward',
        },
      ],
    },
  });

  const chargeSystem = !chargeSystemId
    ? data?.chargeSystems?.[0]
    : data?.chargeSystems?.find((item) => item.id === chargeSystemId);
  if (chargeSystem) {
    (chargeSystem.services || []).forEach((service, index) => {
      const indexData = {
        ...service,
        id: index,
        isBlankService: false,
        isActive: false,
        chargeSystemId: chargeSystem.id,
      };
      indexer.add(indexData as any);
    });
    if (chargeSystem.blankServices) {
      const servicesLength = chargeSystem?.services?.length || 0;
      chargeSystem.blankServices.forEach((blankService, index) => {
        const blankServiceIndexData = {
          id: servicesLength + index,
          code: blankService.code,
          description: blankService.description,
          validFrom: datetimeUtil.add(-1, 'day').unix() * 1000,
          isBlankService: true,
          isActive: false,
          chargeSystemId: chargeSystem.id,
        };
        // blankServiceIndexer.add(blankServiceIndexData as any);
        indexer.add(blankServiceIndexData as any);
      });
    }
  }

  if (data?.moduleChargeSystems && data.moduleChargeSystems.length > 0) {
    const moduleIndexer = newIndexer();
    const defaultModuleChargeSystem = data.moduleChargeSystems[0];
    (defaultModuleChargeSystem.services || []).forEach((s, index) => {
      const indexData = {
        ...s,
        id: index,
        isBlankService: false,
        isActive: false,
        chargeSystemId: defaultModuleChargeSystem.id,
      };
      moduleIndexer.add(indexData as any);
    });
    if (defaultModuleChargeSystem.blankServices) {
      const servicesLength = defaultModuleChargeSystem?.services?.length || 0;
      defaultModuleChargeSystem.blankServices.forEach((blankService, index) => {
        const blankServiceIndexData = {
          id: servicesLength + index,
          code: blankService.code,
          description: blankService.description,
          validFrom: datetimeUtil.add(-1, 'day').unix() * 1000,
          isBlankService: true,
          isActive: false,
          chargeSystemId: chargeSystem?.id,
        };
        // blankServiceIndexer.add(blankServiceIndexData as any);
        moduleIndexer.add(blankServiceIndexData as any);
      });
    }
    contractIndexCache.set(`${contractId} ${defaultModuleChargeSystem.id}`, {
      indexer: moduleIndexer,
      identification: data?.identification,
      functions: data?.functions,
      hpmFunctions: data?.hpmFunctions,
      chargeSystems: data?.chargeSystems,
      moduleChargeSystems: data?.moduleChargeSystems,
    });
  }

  // if (activeBlankServices) {
  //   activeBlankServices.forEach((activeBlankService, index) => {
  //     const indexData = {
  //       id: index,
  //       code: activeBlankService.code,
  //       description: activeBlankService.description,
  //       validFrom: datetimeUtil.add(-1, 'day').unix() * 1000,
  //       isBlankService: true,
  //       isActive: true,
  //       unit: UnitStatistiks.UnitStatistiks_Euros,
  //       evaluation: activeBlankService.price,
  //     };
  //     indexer.add(indexData as any);
  //   });
  // }

  contractIndexCache.set(`${contractId} ${chargeSystem?.id}`, {
    indexer,
    identification: data?.identification,
    functions: data?.functions,
    hpmFunctions: data?.hpmFunctions,
    chargeSystems: data?.chargeSystems,
    moduleChargeSystems: data?.moduleChargeSystems,
  });

  contractBlankServiceIndexCache.set(contractId, {
    indexer: blankServiceIndexer,
    identification: data?.identification,
    functions: data?.functions,
    hpmFunctions: data?.hpmFunctions,
    chargeSystems: data?.chargeSystems,
    moduleChargeSystems: data?.moduleChargeSystems,
  });
}

async function getContractMetaData({
  contractId,
}: IContractSearchRequest): Promise<Contract | undefined> {
  return Promise.resolve(contractMetaIndexCache.get(contractId || ''));
}

async function loadContractIndex(
  contractId: string,
  chargeSystemId?: string
): Promise<IContractIndex | undefined> {
  const key = `${contractId} ${chargeSystemId || contractId}`;
  const contractIndex = contractIndexCache.get(key);

  if (contractIndex && contractIndex.indexer) return contractIndex;

  const res = await getContractByIdWithCache({
    selectedDate: datetimeUtil.getNowStartDay(),
    contractId: contractId,
  });
  const contract = res?.data;
  await addContract(chargeSystemId || '', contractId, contract);

  const result = contractIndexCache.get(key);
  return result;
}

async function workerGetContractById(
  contractId: string,
  chargeSystemId?: string
): Promise<IContractIndex | undefined> {
  const cacheContractIndex = await loadContractIndex(
    contractId,
    chargeSystemId
  );

  if (cacheContractIndex) {
    delete cacheContractIndex.indexer;
  }

  return cacheContractIndex;
}

// async function loadContractBlankServiceIndex(
//   chargeSystemId: string,
//   contractId: string
// ): Promise<IContractIndex> {
//   return contractBlankServiceIndexCache.get(contractId);
// }

async function contractSearch({
  query = '',
  contractId,
  chargeSystemId,
}: IContractSearchRequest): Promise<IContractData[]> {
  const isQueryNumber = query.length === 1 && isNumber(query);
  const searchable = isQueryNumber || query.length >= 2;

  if (!searchable) {
    return new Array<IContractData>();
  }

  const cacheContractIndex = await loadContractIndex(
    contractId || '',
    chargeSystemId
  );

  const _query = isQueryNumber ? `000${query}` : query;

  const serviceCodes = extractServiceCodes(_query);

  let results: IContractData[];

  if (serviceCodes.length > 0 && cacheContractIndex != null) {
    results = serviceCodeSearch(cacheContractIndex.indexer, serviceCodes, 100);
    if (results && results.length > 0) {
      return results;
    }
  }

  // const cacheBlankService = await loadContractBlankServiceIndex(
  //   chargeSystemId,
  //   contractId
  // );

  // if (serviceCodes.length > 0 && cacheBlankService != null) {
  //   results = serviceCodeSearch(cacheBlankService.indexer, serviceCodes, 10);
  //   if (results && results.length > 0) {
  //     return results;
  //   }
  // }

  if (cacheContractIndex == null) {
    return new Array<IContractData>();
  }

  const { indexer } = cacheContractIndex;

  results = textSearch(indexer, _query);

  if (!results) {
    return new Array<IContractData>();
  }

  return results;
}

function textSearch(
  index: Document<IContractData, true>,
  query: string,
  limit?: number
): IContractData[] {
  const result = index.search(query, limit, {
    enrich: true,
  });
  return getDocResult(result);
}

async function doesContractSupportFunctions({
  contractId,
  chargeSystemId,
  functions,
  complianceCondition,
}: IContractSearchRequest): Promise<boolean> {
  if (!contractId) return Promise.resolve(false);
  const cacheContractIndex = await loadContractIndex(
    contractId,
    chargeSystemId
  );
  const matchedFunction = (cacheContractIndex?.functions || []).find((item) => {
    if (complianceCondition?.kvRegion) {
      return !item.conditions?.kvRegionInclusions.some(
        (kv) =>
          datetimeUtil.isBetween(
            complianceCondition.checkDate || 0,
            kv.validFrom,
            kv.validTo || 0
          ) && kv.okv === complianceCondition.kvRegion
      );
    }
    if (complianceCondition?.ikGroup) {
      return !item.conditions?.iKGroupInclusion.some(
        (ik) =>
          datetimeUtil.isBetween(
            complianceCondition.checkDate || 0,
            ik.validFrom,
            ik.validTo || 0
          ) && ik.iKs.includes(complianceCondition.ikGroup || 0)
      );
    }
    return (functions || []).indexOf(item.id) !== -1;
  });
  return Promise.resolve(matchedFunction !== undefined);
}

async function doesContractSupportHpmFunctions({
  contractId,
  chargeSystemId,
  hpmFunctions,
}: IContractSearchRequest): Promise<boolean> {
  if (!contractId) return Promise.resolve(false);
  const cacheContractIndex = await loadContractIndex(
    contractId,
    chargeSystemId
  );

  const matchedFunction = (cacheContractIndex?.hpmFunctions || []).find(
    (hpmFn) => hpmFunctions?.includes(hpmFn)
  );
  return Promise.resolve(matchedFunction !== undefined);
}

function serviceCodeSearch(
  index: Document<IContractData, true>,
  serviceCodes: string[],
  limit: number = 1
): IContractData[] {
  const results: IContractData[] = [];
  serviceCodes.forEach((code) => {
    results.push(
      ...getDocResult(
        index.search(code, limit, { enrich: true, index: 'code' })
      )
    );
  });

  return results;
}

function extractServiceCodes(query: string): string[] {
  const icdCodes = query.split('-');

  const uniqueIcdCodes = icdCodes.filter((code, pos, self) => {
    return self.indexOf(code) === pos;
  });

  return uniqueIcdCodes;
}

async function getContractByIdWithCache(
  request: GetContractByIdRequest
): Promise<ResponseType<ContractDetails>> {
  const cacheKey = `${request.selectedDate}_${request.contractId}`;

  if (contractPromiseCache[cacheKey]) {
    return contractPromiseCache[cacheKey];
  }

  const contractPromise = getContractById(request);
  contractPromiseCache[cacheKey] = contractPromise;

  try {
    const result = await contractPromise;
    return result;
  } catch (error) {
    delete contractPromiseCache[cacheKey];
    throw error;
  }
}
