import React from 'react';
import HelpText from '@tutum/design-system/components/ErrorHelpText';
import { ErrorField } from '@tutum/design-system/components/ErrorHelpText/component';

export default (
  submitCount: number | undefined,
  touched: boolean,
  err: string | ErrorField[],
  warning?: string | ErrorField[],
  className?: string
) => {
  return (
    <HelpText
      submitCount={submitCount!}
      touched={touched}
      err={err}
      warning={warning}
      className={className}
    />
  );
};
