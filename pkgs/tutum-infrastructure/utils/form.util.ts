import { Classes, Intent } from '@tutum/design-system/components/Core';
import { ICustomInsuranceInfo } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import { FormikErrors } from 'formik';
import { parsePhoneNumberFromString } from 'libphonenumber-js/mobile';
import moment from 'moment';
import React from 'react';

import { Nullable } from '@tutum/design-system/infrastructure/models';
import { Form } from '@tutum/hermes/bff/service_domains_patient_file';
import renderFormHelperText from './HelpText';
import datetimeUtil from './datetime.util';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';

export interface ValidateField {
  fieldName: string;
  validateRule: () => boolean;
  errorMessage: string | React.ReactElement;
}

export interface ValidateForm {
  firstInvalidElement: Element | undefined;
  errors: FormikErrors<any>;
}

function renderFormClass(
  submitCount: number | undefined,
  touched: boolean,
  err?: string,
  warning?: string
) {
  if (!err && warning) {
    return Classes.INTENT_WARNING;
  }
  return (submitCount && submitCount > 0) || (touched && err)
    ? err
      ? Classes.INTENT_DANGER
      : ''
    : '';
}

function renderFormClassForSelection(
  submitCount: number | undefined,
  touched: boolean,
  err?: string,
  warning?: string
) {
  if (!err && warning) {
    return 'selection-warning';
  }
  return (submitCount && submitCount > 0) || (touched && err)
    ? err
      ? 'selection-error'
      : ''
    : '';
}

function renderFormClassForRadio(
  submitCount: number | undefined,
  touched: boolean,
  err?: string,
  warning?: string
) {
  if (!err && warning) {
    return 'radio-warning';
  }
  return (submitCount && submitCount > 0) || (touched && err)
    ? err
      ? 'radio-error'
      : ''
    : '';
}

function getFormInputIntent(
  submitCount: number | undefined,
  touched: boolean,
  err?: string,
  warning?: string
) {
  if (!err && warning) {
    return Intent.WARNING;
  }
  return (submitCount && submitCount > 0) || (touched && err)
    ? err
      ? Intent.DANGER
      : Intent.NONE
    : Intent.NONE;
}

// getFullName return showing name based on https://www.notion.so/silenteer/How-to-show-practice-and-patient-names-1c353cee3f87457ba610e445ada745cf
const getFullName = (
  title: Nullable<string>,
  intentWords: Nullable<string>,
  lastName: string,
  firstName: string,
  isEmployee?: boolean
): string => {
  if (isEmployee) {
    return `${title && title != '-' ? title + ' ' : ''}${firstName ? firstName + ' ' : ''
      }${intentWords ? intentWords + ' ' : ''}${lastName ?? ''}`;
  }
  return `${title && title != '-' ? title + ' ' : ''}${intentWords ? intentWords + ' ' : ''
    }${lastName ?? ''}, ${firstName ?? ''}`;
};

function validateForm(
  validateFields: ValidateField[],
  errMap?: Map<string, string | React.Component> | null
): ValidateForm {
  const errors = {} as FormikErrors<any>;
  let firstInvalidElement: Element | undefined = undefined;
  validateFields.forEach((value) => {
    if (value.validateRule() === true) {
      errors[value.fieldName] = value.errorMessage as string;
      if (!firstInvalidElement) {
        firstInvalidElement = document.getElementsByName(value.fieldName)[0];
      }
    }
    if (errMap && errMap[value.fieldName]) {
      errors[value.fieldName] = errMap[value.fieldName];
      if (!firstInvalidElement) {
        firstInvalidElement = document.getElementsByName(value.fieldName)[0];
      }
    }
  });
  return { firstInvalidElement, errors };
}

function parsePhoneNumber(value: string) {
  if (!value) {
    return undefined;
  }
  const phoneNumber = parsePhoneNumberFromString(
    value.startsWith('+49') ? value : `+49${value}`,
    'DE'
  );
  return phoneNumber ? phoneNumber.number.toString() : undefined;
}

function validatePhoneNumber(value: string) {
  if (!value) {
    return false;
  }

  return 6 <= value.length && value.length <= 13;
}

function calculateBMI({ weight, height }: { weight: number; height: number }) {
  if (height && weight) {
    return (weight / (height / 100) ** 2).toFixed(2);
  }
}

const changeToDate = (date: Date | string | number): string => {
  try {
    if (moment(date).isValid()) return moment(date).format('MM-DD-YYYY');
    return '';
  } catch (err) {
    console.error(err);
    return '';
  }
};

interface RequestCheckValidActiveInsurance {
  insuranceInfos: ICustomInsuranceInfo[];
}
const checkValidActiveInsurance = ({
  insuranceInfos,
}: RequestCheckValidActiveInsurance): {
  errIndexs: number[];
  activeIndex: number;
} => {
  let activeIndex = 0;
  const errIndexs: number[] = [];
  insuranceInfos.every((ins: ICustomInsuranceInfo, index: number) => {
    const current = changeToDate(datetimeUtil.date());
    const startDate = ins.startDate
      ? changeToDate(new Date(ins.startDate))
      : null;
    const endDate = ins.endDate ? changeToDate(new Date(ins.endDate)) : null;
    if (endDate) {
      const currentTime = +new Date(current);
      const startTime = +new Date(startDate || 0);
      const endTime = +new Date(endDate);
      if (currentTime >= startTime && currentTime <= endTime) {
        activeIndex = index;
        return false;
      }
    } else {
      if (+new Date(startDate || 0) >= +new Date(current)) {
        activeIndex = index;
        return false;
      }
    }
    return true;
  });
  if (insuranceInfos.length > 0) {
    insuranceInfos.forEach((ins: ICustomInsuranceInfo, index: number) => {
      if (activeIndex !== index) {
        const activeIns = insuranceInfos[activeIndex];
        const insStartDate = ins.startDate ? changeToDate(ins.startDate) : null;
        const insEndDate = ins.endDate ? changeToDate(ins.endDate) : null;
        const activeInsStartDate = activeIns.startDate
          ? changeToDate(activeIns.startDate)
          : null;
        const activeInsEndDate = activeIns.endDate
          ? changeToDate(activeIns.endDate)
          : null;

        if (!!insEndDate && !!activeInsEndDate) {
          const insStartTime = insStartDate ? +new Date(insStartDate) : 0;
          const insEndTime = +new Date(insEndDate);
          const activeInsStartTime = activeInsStartDate
            ? +new Date(activeInsStartDate)
            : 0;
          const activeInsEndTime = +new Date(activeInsEndDate);

          if (
            (insStartTime <= activeInsEndTime &&
              insEndTime >= activeInsStartTime) ||
            (insEndTime >= activeInsStartTime &&
              insStartTime <= activeInsEndTime)
          ) {
            errIndexs.push(index);
          }
        } else if (insEndDate) {
          const insEndTime = insEndDate ? +new Date(insEndDate) : 0;
          const activeInsStartTime = activeInsStartDate
            ? +new Date(activeInsStartDate)
            : 0;

          if (insEndTime >= activeInsStartTime) {
            errIndexs.push(index);
          }
        } else if (activeInsEndDate) {
          const insStartTime = insStartDate ? +new Date(insStartDate) : 0;
          const activeInsEndTime = activeInsEndDate
            ? +new Date(activeInsEndDate)
            : 0;

          if (insStartTime <= activeInsEndTime) {
            errIndexs.push(index);
          }
        } else {
          errIndexs.push(index);
        }
      }
    });
  }
  return { errIndexs, activeIndex };
};

/**
 * @param bsnrId - Assigned bsnrId of this records
 */
const convertFormData = (form: Form, doctor?: IEmployeeProfile): any => {
  return {
    prescriptionDate: form.prescribe.prescribeDate,
    doctor: {
      name: doctor?.fullName,
      value:
        doctor?.bsnrId && form.prescribe.treatmentDoctorId
          ? `${form.prescribe.treatmentDoctorId}-${doctor.bsnrId}`
          : form.prescribe.treatmentDoctorId,
      data: doctor,
    },
    currentFormSetting: JSON.parse(form?.prescribe?.payload),
    currentFormName: form?.prescribe?.formName,
    formPrescription: form?.prescribe,
  };
};
export default {
  renderFormHelperText,
  renderFormClass,
  getFormInputIntent,
  getFullName,
  validateForm,
  validatePhoneNumber,
  parsePhoneNumber,
  renderFormClassForSelection,
  renderFormClassForRadio,
  calculateBMI,
  changeToDate,
  checkValidActiveInsurance,
  convertFormData,
};
