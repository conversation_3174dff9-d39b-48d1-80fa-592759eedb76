import moment from 'moment';
import { isNil } from 'lodash';

import {
  DATE_FORMAT,
  DATE_TIME_TRANSFER_UTC,
  HOUR_MIN_TIME_FORMAT,
} from '@tutum/infrastructure/shared/date-format';

export type TDate = Date | string | number; // null | undefined

export type TDateInclusivity = '()' | '[)' | '(]' | '[]';

export type DateRange = {
  startDate: number;
  endDate: number;
};

let systemDate = 0;

export const saveSystemDate = (val: number) => {
  systemDate = val;
};

// Temporarily hardcode for locale here
// When we have enough i18n files for all language, will switch to i18next.language as locale
const dateTimeFormat = (
  date: TDate | undefined,
  format: string,
  // locale = i18next.language
  locale = 'de'
) => {
  if (isNil(date)) {
    return '-';
  }
  return moment(date).locale(locale).format(format);
};

const dateTimeNumberFormat = (date: number, format: string) => {
  return moment(date).format(format);
};
const hourMinTimeFormat = (date: number, format = HOUR_MIN_TIME_FORMAT) => {
  return moment(date).format(format);
};

const utc = (date: TDate) => {
  return +moment.utc(date);
};

export const now = () => {
  if (systemDate) {
    return systemDate;
  }

  return Date.now();
};

export const roundToHour = <T extends TDate>(date: T): T => {
  if (!date) {
    return null as unknown as T;
  }

  switch (typeof date) {
    case 'string':
      return moment(date).startOf('hour').toDate() as T;
    case 'number':
      return moment(date).startOf('hour').toDate().getTime() as T;
    case 'object':
      return moment(date).startOf('hour').toDate() as T;
    default:
      return null as unknown as T;
  }
};

export const date = (inp?: number) => {
  if (!isNil(inp)) {
    return new Date(inp);
  }

  if (systemDate) {
    return new Date(systemDate);
  }

  return new Date();
};

const nowInUnix = () => moment.utc(now()).unix();

const startOf = (date: TDate, unitOfTime: moment.unitOfTime.StartOf) =>
  moment(date).startOf(unitOfTime);

const startOfSelectedDay = (selectedDate = date()) => {
  return startOf(selectedDate, 'day');
};

const strToDate = (str: string, format: string, locale = 'de') => {
  if (!str) return null;
  // format if not enough length
  if (str.length === 6 && !str.includes('.')) {
    const newValue = `${str.slice(0, 4)}20${str.slice(4)}`;
    return moment(newValue, DATE_FORMAT.replace('.', ''), locale).toDate();
  }
  if (/^\d{8}$/.test(str)) {
    const [day, month, year] = [str.slice(0, 2), str.slice(2, 4), str.slice(4)];
    const formattedStr = `${day}.${month}.${year}`;
    const parsedDate = moment(formattedStr, DATE_FORMAT, locale, true);
    return parsedDate.isValid() ? parsedDate.toDate() : null;
  }

  return moment(str, format, locale).toDate();
};

const endOf = (date: TDate, unitOfTime: moment.unitOfTime.StartOf) =>
  moment(date).endOf(unitOfTime);

const endOfSelectedDay = (selectedDate = date()) => {
  return endOf(selectedDate, 'day');
};

const dateToMoment = (date: TDate = now(), format?: string) =>
  moment(date, format);

const isBefore = (date: TDate, comparedDate: TDate) => {
  return moment(date).isBefore(comparedDate);
};

const diff = (
  date: TDate,
  comparedDate: TDate,
  unitOfTime?: moment.unitOfTime.Diff
) => {
  return moment(date).diff(comparedDate, unitOfTime);
};

const getCurrentTime = () => {
  return moment().diff(moment().startOf('day'));
};

const isBetween = (
  date: TDate,
  startDate: TDate,
  endDate: TDate,
  granularity?: moment.unitOfTime.StartOf,
  inclusivity?: TDateInclusivity
) => {
  if (!startDate) return moment(date).isBefore(endDate, granularity);
  if (!endDate) return moment(date).isAfter(startDate, granularity);
  return moment(date).isBetween(startDate, endDate, granularity, inclusivity);
};

function getTimeUTC(time: moment.MomentInput, format?: string) {
  return moment.utc(time, format);
}

function getStartOfQuarter(time: moment.MomentInput) {
  return getTimeUTC(time).startOf('quarter');
}

function getStartOfQuarterWithoutUTC(time: moment.MomentInput) {
  return moment(time).startOf('quarter');
}

function getEndOfQuarter(time: moment.MomentInput) {
  return getTimeUTC(time).endOf('quarter');
}

function getEndOfQuarterWithoutUTC(time: moment.MomentInput) {
  return moment(time).endOf('quarter');
}

function getStartOfBySelectedQuarter(
  selectedQuarter: number,
  selectedYear: number,
  isEndOfDay = true
) {
  if (!selectedYear || !selectedYear) {
    return moment(new Date());
  }

  const date = moment(`${selectedYear}`).quarter(selectedQuarter);

  if (isEndOfDay) {
    date.endOf('day');
  } else {
    date.startOf('day');
  }

  return moment(date);
}

function getEndOfSelectedQuarter(
  selectedQuarter: number,
  selectedYear: number
) {
  if (!selectedQuarter || !selectedYear) {
    return moment(new Date()).endOf('quarter');
  }

  const date = moment(`${selectedYear}`).quarter(selectedQuarter);
  return moment(date).endOf('quarter');
}

function getDateString(time: moment.MomentInput, format?: string) {
  return getTimeUTC(time).local().format(format);
}

function getDate(time: moment.MomentInput, format?: string) {
  return getTimeUTC(time, format);
}

function getQuarter(time?: moment.MomentInput) {
  return getTimeUTC(time || date()).quarter();
}

function getYear(time?: moment.MomentInput, isGetFullYear = true) {
  const year = getTimeUTC(time).year();

  if (isGetFullYear) {
    return year;
  }

  return +String(year).slice(2, String(year).length);
}

function getPreviousQuarters(time: moment.MomentInput, numOfQuarters: number) {
  return getTimeUTC(time).subtract(numOfQuarters, 'Q');
}

function getNextQuarters(time: moment.MomentInput, numberOfQuarters: number) {
  return getTimeUTC(time).add(numberOfQuarters, 'Q');
}

function getQuarterOfTheYear(date) {
  return Math.ceil((date.getMonth() + 1) / 3);
}

function getDayDuration(startDate: Date, endDate: Date) {
  const startDateMoment = moment(startDate);
  const endDateMoment = moment(endDate);
  return moment.duration(endDateMoment.diff(startDateMoment));
}

function unixToMoment(unix: number): moment.Moment {
  return moment(unix);
}

export function isFormatValid(dateString: string, format: string): boolean {
  const formatted = moment(dateString, format, true);
  return formatted.isValid();
}

export function subtractByNumberOfDay(numberOfDate: number) {
  return moment(now()).subtract(numberOfDate, 'd');
}

export function subtract(
  amount?: moment.DurationInputArg1,
  unit?: moment.unitOfTime.DurationConstructor,
  inp: moment.MomentInput = now()
) {
  return moment(inp).subtract(amount, unit);
}

export const getTime = (date: Date): number => {
  return moment(date).valueOf();
};

export const toQuarterYear = (time?: number) => {
  const quarter = getQuarter(time);
  const year = time ? new Date(time).getFullYear() : 0;
  return {
    quarter,
    year,
  };
};

export const getLastDayOfQuarter = (date: number) => {
  const quarterEncounterDate = getQuarter(date);
  const lastMonthOfEncounterQuarter = quarterEncounterDate * 3;
  const yearEncounterDate = getYear(date);
  const lastDayOfQuarter = new Date(
    yearEncounterDate,
    lastMonthOfEncounterQuarter,
    0
  );
  return lastDayOfQuarter;
};

export const getFirstDayOfMonth = (date: number) => {
  return moment(date).clone().startOf('month');
};

export const getEndDayOfMonth = (date: number) => {
  return moment(date).clone().endOf('month');
};

export const getFirstDayOfQuarterByQuarterAndYear = (
  quarter: number,
  year: number
) => {
  const firstMonthOfEncounterQuarter = quarter * 3 - 2;
  const firstDayOfQuarter = new Date(year, firstMonthOfEncounterQuarter, 1);
  return firstDayOfQuarter;
};

export const getLastDayOfQuarterByQuarterAndYear = (
  quarter: number,
  year: number
) => {
  const lastMonthOfEncounterQuarter = quarter * 3;
  const lastDayOfQuarter = new Date(year, lastMonthOfEncounterQuarter, 0);
  return lastDayOfQuarter;
};

export const convertMilisecondsToMomentUTC = (mili: number) => {
  return moment.utc(moment(mili).format(DATE_TIME_TRANSFER_UTC));
};

export const add = (
  amount: moment.DurationInputArg1,
  unit: moment.unitOfTime.DurationConstructor,
  inp: moment.MomentInput = now()
) => {
  return moment(inp).add(amount, unit);
};

export const getNowStartDay = (): number => {
  return +startOf(now(), 'day');
};

export const durationMilisecondsToDays = (milliseconds: number) => {
  const duration = moment.duration(milliseconds);
  return duration.asDays();
};

export const durationDaysToMilliseconds = (days: number) => {
  return days * 24 * 60 * 60 * 1000;
};

export const isFirstDayOfQuarter = (date: Date) => {
  return moment(date).isSame(moment(date).startOf('quarter'), 'day');
};

export default {
  getNowStartDay,
  add,
  dateTimeFormat,
  dateTimeNumberFormat,
  hourMinTimeFormat,
  utc,
  now,
  nowInUnix,
  startOf,
  startOfSelectedDay,
  strToDate,
  endOf,
  endOfSelectedDay,
  dateToMoment,
  diff,
  getCurrentTime,
  isBefore,
  isBetween,
  unixToMoment,
  getStartOfQuarter,
  getStartOfQuarterWithoutUTC,
  getEndOfQuarter,
  getEndOfQuarterWithoutUTC,
  getStartOfBySelectedQuarter,
  getEndOfSelectedQuarter,
  getDateString,
  getDate,
  getQuarter,
  getYear,
  getPreviousQuarters,
  getQuarterOfTheYear,
  getNextQuarters,
  getDayDuration,
  getTime,
  subtractByNumberOfDay,
  toQuarterYear,
  getLastDayOfQuarter,
  getFirstDayOfMonth,
  getEndDayOfMonth,
  getFirstDayOfQuarterByQuarterAndYear,
  getLastDayOfQuarterByQuarterAndYear,
  getTimeUTC,
  date,
  convertMilisecondsToMomentUTC,
  subtract,
  durationMilisecondsToDays,
  durationDaysToMilliseconds,
  isFirstDayOfQuarter,
  roundToHour,
};
