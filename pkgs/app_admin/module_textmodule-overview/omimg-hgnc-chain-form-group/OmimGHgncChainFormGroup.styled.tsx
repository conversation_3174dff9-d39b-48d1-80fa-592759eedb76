import Theme from '@tutum/admin/theme';
import { COLOR } from '@tutum/design-system/themes/styles';

const styled = Theme.styled;

const OmimGHgncChainFormGroupWrapper = styled.div`
  width: 100%;
  border: 1px solid ${COLOR.BACKGROUND_TERTIARY_DIM};
  border-radius: 4px;

  .sl-freetext-form-body {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 8px 16px;
    min-height: 98px;
    max-height: 280px;
    overflow-y: auto;
  }

  .sl-lexical-plaintext-placeholder {
    z-index: auto;
  }
`;

export default OmimGHgncChainFormGroupWrapper;
