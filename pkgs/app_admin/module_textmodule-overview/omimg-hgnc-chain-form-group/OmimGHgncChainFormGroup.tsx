import React, { useEffect } from 'react';
import { useFormikContext } from 'formik';
import I18n from '@tutum/infrastructure/i18n';
import {
  ExposeEditorInstancePlugin,
  useEditorInstance,
} from '@tutum/design-system/lexical/plugins/ExposeEditorInstance';
import {
  $createParagraphNode,
  $getRoot,
  $getSelection,
  $isParagraphNode,
  $isRangeSelection,
} from 'lexical';
import type { LexicalNode } from 'lexical';
import {
  TextModuleUseFor,
  type TextModuleContent,
  type TextModuleNode,
} from '@tutum/hermes/bff/text_module_common';
import type { TextModule } from '@tutum/hermes/bff/text_module_common';
import OmimGChainFormGroupWrapper from './OmimGHgncChainFormGroup.styled';
import StyledFreetextComposer from '../freetext-form-group/StyledFreetextComposer';
import { RESET_TEXTMODULE_FORM_SIGNAL } from '../textmodule-dialog/TextmoduleDialog.constant';
import {
  exportTextModuleContents,
  importTextModuleData,
} from '@tutum/admin/module_textmodule-editor/import-export-data';
import { AvoidLineBreakPlugin } from '@tutum/design-system/textmodule/plugins/AvoidLineBreak.plugin';
import { AdditionalInfoOmimGBlockNode } from '@tutum/admin/module_textmodule-editor/nodes/AdditionalInfoOmimGBlock.node';
import { AdditionalInfoHgncBlockNode } from '@tutum/admin/module_textmodule-editor/nodes/AdditionalInfoHgncBlock.node';
import OmimGHgncChainTriggerPlugin from '@tutum/admin/module_textmodule-editor/plugins/OmimGHgncChainTriggerPlugin';
import { AdditionalInfoOmimHgncPlugin } from '@tutum/admin/module_textmodule-editor/plugins/AdditionalInfoOmimHgncPlugin';

export const TEXTMODULE_OMIMG_CONFIG = [AdditionalInfoOmimGBlockNode];
export const TEXTMODULE_HGNC_CONFIG = [AdditionalInfoHgncBlockNode];

interface OmimGHgncChainFormGroupProps {
  defaultValue?: TextModuleContent;
  onChange: (newValues: TextModuleContent) => void;
  triggerNodeType: TextModuleUseFor;
}

function fillContentDataIntoComposer(data: TextModuleNode[], triggerNodeType: TextModuleUseFor) {
  const nodes = data.reduce<LexicalNode[]>((_nodes, contentData) => {
    const newNode = importTextModuleData({ data: contentData, importFor: triggerNodeType });
    if (newNode) {
      _nodes.push(newNode);
    }
    return _nodes;
  }, []);

  const selection = $getSelection();
  if ($isRangeSelection(selection)) {
    selection.insertNodes(nodes);
    return;
  }

  const root = $getRoot();
  const firstChild = root.getFirstChildOrThrow();
  if ($isParagraphNode(firstChild)) {
    firstChild.append(...nodes);
    return;
  }
}

function resetComposer() {
  const root = $getRoot();
  const newParagraph = $createParagraphNode();
  const firstChild = root.getFirstChildOrThrow();
  firstChild.replace(newParagraph);
}
const OmimGHgncChainFormGroup: React.FC<OmimGHgncChainFormGroupProps> = (props) => {
  const { defaultValue, onChange, triggerNodeType } = props;

  const { t } = I18n.useTranslation({
    namespace: 'Textmodule',
    nestedTrans: 'Dialog',
  });

  const { t: additionalInfoTranslator } = I18n.useTranslation<any>({
    namespace: 'Textmodule',
    nestedTrans: 'AdditionalInfo',
  });

  const [_editor, setEditorRef, updateEditor] = useEditorInstance();

  const { status: formStatus } = useFormikContext<TextModule>();

  useEffect(() => {
    if (!_editor || !defaultValue?.data?.length) return;
    updateEditor(() => {
      fillContentDataIntoComposer(defaultValue.data, triggerNodeType);
    });
  }, [defaultValue?.data, _editor, updateEditor, triggerNodeType]);

  useEffect(() => {
    if (formStatus !== RESET_TEXTMODULE_FORM_SIGNAL) return;
    updateEditor(() => {
      resetComposer();
    });
  }, [formStatus, updateEditor]);

  return (
    <OmimGChainFormGroupWrapper>
      <div className="sl-freetext-form-body">
        <StyledFreetextComposer
          nodes={triggerNodeType === TextModuleUseFor.TextModuleUseFor_OmimGChain ? TEXTMODULE_OMIMG_CONFIG : TEXTMODULE_HGNC_CONFIG}
          placeholder={triggerNodeType === TextModuleUseFor.TextModuleUseFor_OmimGChain ? t('omimGChainPlaceholderContent') : t('hgncChainPlaceholderContent')}
          onChange={(editorState) => {
            editorState.read(() => {
              const newValues = exportTextModuleContents(
                $getRoot(),
                additionalInfoTranslator
              );
              if (!newValues) return;
              onChange(newValues);
            });
          }}
        >
          <ExposeEditorInstancePlugin setEditorRef={setEditorRef} />
          <OmimGHgncChainTriggerPlugin triggerNodeType={triggerNodeType} />
          <AdditionalInfoOmimHgncPlugin triggerNodeType={triggerNodeType} />
          <AvoidLineBreakPlugin />
        </StyledFreetextComposer>
      </div>
    </OmimGChainFormGroupWrapper>
  );
};

export default React.memo(OmimGHgncChainFormGroup);
