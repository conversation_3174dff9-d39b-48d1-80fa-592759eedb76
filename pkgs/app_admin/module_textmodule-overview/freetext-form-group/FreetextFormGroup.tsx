import React, { useEffect } from 'react';
import { useFormikContext } from 'formik';
import I18n from '@tutum/infrastructure/i18n';
import { Flex } from '@tutum/design-system/components/Flexbox';
import {
  ExposeEditorInstancePlugin,
  useEditorInstance,
} from '@tutum/design-system/lexical/plugins/ExposeEditorInstance';
import {
  $createParagraphNode,
  $getRoot,
  $getSelection,
  $isParagraphNode,
  $isRangeSelection,
} from 'lexical';
import {
  INSERT_PLACEHOLDER_NODE_COMMAND,
  PlaceholderPlugin,
} from '@tutum/admin/module_textmodule-editor/plugins/Placeholder';
import Lexical, { LexicalNode } from 'lexical';
import type {
  TextModuleContent,
  TextModuleNode,
} from '@tutum/hermes/bff/text_module_common';
import type { TextModule } from '@tutum/hermes/bff/text_module_common';
import FreetextFormGroupWrapper from './FreetextFormGroup.styled';
import StyledFreetextComposer from './StyledFreetextComposer';
import { RESET_TEXTMODULE_FORM_SIGNAL } from '../textmodule-dialog/TextmoduleDialog.constant';
import { TEXTMODULE_CONFIG } from './FreetextFormGroup.constant';
import {
  exportTextModuleContents,
  importTextModuleData,
} from '@tutum/admin/module_textmodule-editor/import-export-data';
import { AvoidLineBreakPlugin } from '@tutum/design-system/textmodule/plugins/AvoidLineBreak.plugin';

interface FreetextFormGroupProps {
  defaultValue?: TextModuleContent;
  onChange: (newValues: TextModuleContent) => void;
}

function fillContentDataIntoComposer(data: TextModuleNode[]) {
  const nodes = data.reduce<LexicalNode[]>((_nodes, contentData) => {
    const newNode = importTextModuleData({ data: contentData });
    if (newNode) {
      _nodes.push(newNode);
    }
    return _nodes;
  }, []);

  const selection = $getSelection();
  if ($isRangeSelection(selection)) {
    selection.insertNodes(nodes);
    return;
  }

  const root = $getRoot();
  const firstChild = root.getFirstChildOrThrow();
  if ($isParagraphNode(firstChild)) {
    firstChild.append(...nodes);
    return;
  }
}

function resetComposer() {
  const root = $getRoot();
  const newParagraph = $createParagraphNode();
  const firstChild = root.getFirstChildOrThrow();
  firstChild.replace(newParagraph);
}

const FreetextFormGroup: React.FC<FreetextFormGroupProps> = (props) => {
  const { defaultValue, onChange } = props;

  const { t } = I18n.useTranslation({
    namespace: 'Textmodule',
    nestedTrans: 'Dialog',
  });

  const { t: additionalInfoTranslator } = I18n.useTranslation<any>({
    namespace: 'Textmodule',
    nestedTrans: 'AdditionalInfo',
  });

  const [_editor, setEditorRef, updateEditor] = useEditorInstance();

  const { status: formStatus } = useFormikContext<TextModule>();

  useEffect(() => {
    if (!_editor || !defaultValue?.data?.length) return;
    updateEditor(() => {
      fillContentDataIntoComposer(defaultValue.data);
    });
  }, [defaultValue?.data, _editor, updateEditor]);

  useEffect(() => {
    if (formStatus !== RESET_TEXTMODULE_FORM_SIGNAL) return;
    updateEditor(() => {
      resetComposer();
    });
  }, [formStatus, updateEditor]);

  function insertPlaceholderNode() {
    _editor?.dispatchCommand(INSERT_PLACEHOLDER_NODE_COMMAND, null);
  }

  return (
    <FreetextFormGroupWrapper>
      <Flex className="sl-action-panel">
        <span
          className="sl-action-panel-button"
          onClick={insertPlaceholderNode}
        >
          {t('freeTextButton')}
        </span>
      </Flex>
      <div className="sl-freetext-form-body">
        <StyledFreetextComposer
          nodes={TEXTMODULE_CONFIG}
          placeholder={t('freeTextPlaceholderContent')}
          onChange={(editorState) => {
            editorState.read(() => {
              const newValues = exportTextModuleContents(
                $getRoot(),
                additionalInfoTranslator
              );
              if (!newValues) return;
              onChange(newValues);
            });
          }}
        >
          <PlaceholderPlugin />
          {/* // NOTE: set ref to editor instance for later usages */}
          <ExposeEditorInstancePlugin setEditorRef={setEditorRef} />
          <AvoidLineBreakPlugin />
        </StyledFreetextComposer>
      </div>
    </FreetextFormGroupWrapper>
  );
};

export default React.memo(FreetextFormGroup);
