import React, { useMemo, useEffect, useState } from 'react';
import get from 'lodash/get';
import { Field, useFormikContext } from 'formik';
import { DragDropContext, Droppable, DropResult } from 'react-beautiful-dnd';
import I18n from '@tutum/infrastructure/i18n';
import { Flex } from '@tutum/design-system/components/Flexbox';
import { Button } from '@tutum/design-system/components/Button';
import type { FieldArrayRenderProps } from 'formik';
import type { TextModule } from '@tutum/hermes/bff/text_module_common';
import type { TextModuleQuestionnaireNode } from '@tutum/hermes/bff/text_module_common';
import QuestionnaireFormGroupStyleWrapper from './QuestionnaireFormGroup.styled';
import QuestionnaireBlock from './components/QuestionnaireBlock';
import { getContentDataFieldName } from './QuestionnaireFormGroup.util';
import { defaultQuestionnaireData } from './QuestionnaireFormGroup.constant';

const QuestionnaireFormGroup: React.FC<FieldArrayRenderProps> = (props) => {
  const { t } = I18n.useTranslation({
    namespace: 'Textmodule',
    nestedTrans: 'Dialog',
  });

  const [selectQuestionIdx, setSelectQuestionIdx] = useState<number>(0);

  const { values, setFieldValue, errors, setErrors } =
    useFormikContext<TextModule>();

  const questionnaires = useMemo<TextModuleQuestionnaireNode[]>(
    () => get(values, 'content.data')?.map((d) => d.questionnaire!) ?? [],
    [values]
  );

  const hasMoreThanOneQuestion = useMemo(
    () => questionnaires?.length >= 1,
    [questionnaires]
  );

  useEffect(() => {
    if (hasMoreThanOneQuestion) return;
    // NOTE: auto-fill if questionnaire list is empty
    setFieldValue('content.data.0', defaultQuestionnaireData);
  }, [hasMoreThanOneQuestion, setFieldValue]);

  function onDragEnd(result: DropResult) {
    if (!result.destination) {
      return;
    }

    if (result.destination.index === result.source.index) {
      return;
    }

    // NOTE: reorder
    props.move(result.source.index, result.destination.index);

    if (!errors.content?.data) {
      setErrors({
        ...errors,
        content: undefined,
      });
    }

    // NOTE: keep focusing index in sync
    if (selectQuestionIdx === result.source.index) {
      setSelectQuestionIdx(result.destination.index);
    }
  }

  if (!values?.content?.data || !questionnaires) {
    return null;
  }

  return (
    <QuestionnaireFormGroupStyleWrapper>
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="QUESTIONNAIRE_SORTABLE_LIST">
          {(provided) => {
            if (!hasMoreThanOneQuestion) return <></>;
            return (
              <Flex
                className="sl-questionnaire-block-wrapper"
                column
                gap={24}
                ref={provided.innerRef}
                {...provided.droppableProps}
              >
                {questionnaires.map((questionnaire, dataIndex) => (
                  <Field
                    key={dataIndex}
                    name={getContentDataFieldName(dataIndex)}
                  >
                    {() => (
                      <QuestionnaireBlock
                        questionIndex={dataIndex}
                        totalQuestions={questionnaires.length}
                        questionnaire={questionnaire}
                        isSelected={dataIndex === selectQuestionIdx}
                        onClick={() => setSelectQuestionIdx(dataIndex)}
                        fieldName={getContentDataFieldName(dataIndex)}
                        onRemoveQuestion={() => props.remove(dataIndex)}
                      />
                    )}
                  </Field>
                ))}
                {provided.placeholder}
              </Flex>
            );
          }}
        </Droppable>
      </DragDropContext>

      <Flex>
        <Button
          small
          minimal
          outlined
          intent="primary"
          onClick={() => {
            setSelectQuestionIdx(questionnaires?.length);
            props.push(defaultQuestionnaireData);
          }}
        >
          {t('addQuestionnaireLabel')}
        </Button>
      </Flex>
    </QuestionnaireFormGroupStyleWrapper>
  );
};

export default QuestionnaireFormGroup;
