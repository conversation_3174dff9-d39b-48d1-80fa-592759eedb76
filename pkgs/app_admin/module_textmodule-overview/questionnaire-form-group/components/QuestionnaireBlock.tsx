import React, { useMemo, useEffect, useId } from 'react';
import get from 'lodash/get';
import { Field, FieldArray, useFormikContext } from 'formik';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import I18n from '@tutum/infrastructure/i18n';
import { BodyTextM } from '@tutum/design-system/components/Typography';
import { Flex } from '@tutum/design-system/components/Flexbox';
import { Svg } from '@tutum/design-system/components/Svg';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import InputGroup from '@tutum/design-system/components/Core/InputGroup';
import CreatePatientUtil from '@tutum/infrastructure/utils/form.util';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import {
  Divider,
  RadioGroup,
  Radio,
  Checkbox,
} from '@tutum/design-system/components/Core';
import {
  QuestionnaireQuestionType,
  AnswerType,
} from '@tutum/hermes/bff/text_module_common';
import type { TextModule } from '@tutum/hermes/bff/text_module_common';
import type {
  Answer,
  TextModuleQuestionnaireNode,
} from '@tutum/hermes/bff/text_module_common';
import {
  defaultAnswerData,
  DRAG_HANDLE_HORIZONTAL_ICON_URL,
  DRAG_HANDLE_ICON_URL,
  MINUS_CIRCLE_ICON_URL,
} from '../QuestionnaireFormGroup.constant';
import { getQuestionnaireAnswersDataFieldName } from '../QuestionnaireFormGroup.util';

const QuestionnaireBlock: React.FC<{
  fieldName: string;
  questionIndex: number;
  totalQuestions: number;
  isSelected: boolean;
  onClick(): void;
  onRemoveQuestion(): void;
  questionnaire?: TextModuleQuestionnaireNode;
}> = (props) => {
  const {
    isSelected,
    onClick,
    fieldName,
    questionIndex,
    totalQuestions,
    questionnaire,
    onRemoveQuestion,
  } = props;

  const { t } = I18n.useTranslation({
    namespace: 'Textmodule',
    nestedTrans: 'Dialog',
  });

  const _id = useId();

  const { values, errors, touched, submitCount, setFieldValue, setErrors } =
    useFormikContext<TextModule>();

  const questionnaireLabelFieldName = `${fieldName}.questionnaire.label`;
  const questionnaireTypeFieldName = `${fieldName}.questionnaire.questionType`;
  const questionnaireAnswersFieldName = `${fieldName}.questionnaire.answers`;

  const selectedQuestionTypeValue = useMemo(
    () => get(values, questionnaireTypeFieldName),
    [values, questionnaireTypeFieldName]
  );

  const answersList = useMemo<Answer[]>(
    () => get(values, questionnaireAnswersFieldName) ?? [],
    [values, questionnaireAnswersFieldName]
  );

  const defaultOtherAnswerData: Answer = {
    label: t('answerOther'),
    value: t('answerOther'),
    answerType: AnswerType.AnswerType_Others,
  };

  const isQuestionInputType = useMemo(
    () =>
      selectedQuestionTypeValue ===
      QuestionnaireQuestionType.QuestionnaireQuestionType_Freetext,
    [selectedQuestionTypeValue]
  );

  const hasAnswerOtherOption = useMemo(
    () =>
      !isQuestionInputType &&
      answersList?.length &&
      answersList.some((a) => a.answerType === AnswerType.AnswerType_Others),
    [isQuestionInputType, answersList]
  );

  // NOTE: reset answer list
  useEffect(() => {
    if (!isQuestionInputType) return;
    setFieldValue(questionnaireAnswersFieldName, [defaultAnswerData]);
  }, [setFieldValue, isQuestionInputType, questionnaireAnswersFieldName]);

  return (
    <Draggable draggableId={_id} index={questionIndex}>
      {(provided) => (
        <div
          onClick={onClick}
          className={getCssClass({
            'sl-textmodule-dialog-questionnaire-block': isSelected,
            'sl-textmodule-dialog-questionnaire-block-preview': !isSelected,
          })}
          // DRAG n DROP props
          ref={provided.innerRef}
          {...provided.draggableProps}
        >
          {isSelected ? (
            <>
              <Flex className="sl-textmodule-dialog-questionnaire-block-drag-handler">
                <Svg
                  {...provided.dragHandleProps}
                  src={DRAG_HANDLE_HORIZONTAL_ICON_URL}
                />
              </Flex>

              <FormGroup2
                isRequired
                name={questionnaireLabelFieldName}
                label={t('formFields.questionnaireLabel', {
                  number: questionIndex + 1,
                })}
                submitCount={submitCount}
                errors={errors}
                touched={touched}
              >
                <Field name={questionnaireLabelFieldName}>
                  {({ field }) => (
                    <div className="sl-questionnaire-label-input-group-container">
                      <InputGroup
                        {...field}
                        onChange={(e) => {
                          field.onChange?.(e);
                          if (questionIndex === 0) {
                            setFieldValue(
                              'content.text',
                              e.target.value as string
                            );
                          }
                        }}
                        placeholder={t('questionnaireLabelPlaceholder')}
                        data-tab-id={field.label}
                        intent={CreatePatientUtil.getFormInputIntent(
                          submitCount,
                          touched[questionnaireLabelFieldName],
                          errors[questionnaireLabelFieldName]
                        )}
                      />
                      <Svg
                        className="sl-remove-icon"
                        src={MINUS_CIRCLE_ICON_URL}
                        onClick={onRemoveQuestion}
                        style={{
                          visibility:
                            questionIndex === 0 && totalQuestions === 1
                              ? 'hidden'
                              : 'visible',
                        }}
                      />
                    </div>
                  )}
                </Field>
              </FormGroup2>

              <FormGroup2
                isRequired
                name={questionnaireTypeFieldName}
                submitCount={submitCount}
                errors={errors}
                touched={touched}
              >
                <Field name={questionnaireTypeFieldName}>
                  {({ field, form }) => (
                    <RadioGroup
                      inline
                      selectedValue={field.value}
                      onChange={(e) => {
                        form.setFieldValue(field.name, e.currentTarget.value);
                      }}
                    >
                      <Radio
                        label={t('questionType.single')}
                        value={
                          QuestionnaireQuestionType.QuestionnaireQuestionType_SingleSelection
                        }
                      />
                      <Radio
                        label={t('questionType.multi')}
                        value={
                          QuestionnaireQuestionType.QuestionnaireQuestionType_MultipleSelection
                        }
                      />
                      <Radio
                        label={t('questionType.freetext')}
                        value={
                          QuestionnaireQuestionType.QuestionnaireQuestionType_Freetext
                        }
                      />
                    </RadioGroup>
                  )}
                </Field>
              </FormGroup2>

              <Divider style={{ marginInline: 0 }} />

              {isQuestionInputType ? (
                <FormGroup2
                  isRequired
                  name={questionnaireAnswersFieldName}
                  submitCount={submitCount}
                  errors={errors}
                  touched={touched}
                >
                  <div className="sl-questionnaire-answer-container">
                    {answersList?.map((_, answerIndex) => {
                      const answerDataLabelFieldName =
                        getQuestionnaireAnswersDataFieldName(
                          questionnaireAnswersFieldName,
                          answerIndex
                        );

                      return (
                        <Field
                          key={answerIndex}
                          name={answerDataLabelFieldName}
                        >
                          {() => (
                            <div className="sl-questionnaire-answer">
                              <InputGroup
                                disabled
                                placeholder={t('textAnswerLabelPlaceholder')}
                              />
                            </div>
                          )}
                        </Field>
                      );
                    })}
                  </div>
                </FormGroup2>
              ) : (
                <FormGroup2
                  isRequired
                  name={questionnaireAnswersFieldName}
                  submitCount={submitCount}
                  errors={errors}
                  touched={touched}
                >
                  <FieldArray name={questionnaireAnswersFieldName}>
                    {(arrayHelpers) => (
                      <DragDropContext
                        onDragEnd={(result) => {
                          if (!result.destination) {
                            return;
                          }

                          if (
                            result.destination.index === result.source.index
                          ) {
                            return;
                          }

                          // NOTE: reorder
                          arrayHelpers.move(
                            result.source.index,
                            result.destination.index
                          );

                          // NOTE: reset errors
                          if (!errors.content?.data?.length) {
                            setErrors({
                              ...errors,
                              content: undefined,
                            });
                          }
                        }}
                      >
                        <Droppable droppableId="ANSWER_SORTABLE_LIST">
                          {(droppableProvided) => (
                            <div
                              className="sl-questionnaire-answer-container"
                              ref={droppableProvided.innerRef}
                              {...droppableProvided.droppableProps}
                            >
                              {answersList?.map(
                                (answer: Answer, answerIndex) => {
                                  const answerDataLabelFieldName =
                                    getQuestionnaireAnswersDataFieldName(
                                      questionnaireAnswersFieldName,
                                      answerIndex
                                    );

                                  const answerDataValueFieldName =
                                    getQuestionnaireAnswersDataFieldName(
                                      questionnaireAnswersFieldName,
                                      answerIndex,
                                      'value'
                                    );

                                  const isOtherType =
                                    answer.answerType ===
                                    AnswerType.AnswerType_Others;

                                  const answerKey =
                                    _id + '::anwser::' + answerIndex;

                                  return (
                                    <Field
                                      key={answerIndex}
                                      name={answerDataLabelFieldName}
                                    >
                                      {({
                                        field: answerField,
                                        form: answerForm,
                                      }) => (
                                        <Draggable
                                          key={answerKey}
                                          draggableId={answerKey}
                                          index={answerIndex}
                                        >
                                          {(draggableAnswerProvided) => (
                                            <div
                                              className="sl-questionnaire-answer"
                                              // DRAG n DROP props
                                              ref={
                                                draggableAnswerProvided.innerRef
                                              }
                                              {...draggableAnswerProvided.draggableProps}
                                            >
                                              <Svg
                                                {...draggableAnswerProvided.dragHandleProps}
                                                src={DRAG_HANDLE_ICON_URL}
                                              />

                                              {selectedQuestionTypeValue ===
                                                QuestionnaireQuestionType.QuestionnaireQuestionType_SingleSelection && (
                                                  <Radio
                                                    className="sl-type-button-view-only"
                                                    disabled
                                                    readOnly
                                                  />
                                                )}

                                              {selectedQuestionTypeValue ===
                                                QuestionnaireQuestionType.QuestionnaireQuestionType_MultipleSelection && (
                                                  <Checkbox
                                                    className="sl-type-button-view-only"
                                                    disabled
                                                    readOnly
                                                  />
                                                )}

                                              {isOtherType === true ? (
                                                <InputGroup
                                                  disabled
                                                  value={t('answerOther')}
                                                />
                                              ) : (
                                                <InputGroup
                                                  {...answerField}
                                                  placeholder={t(
                                                    'answerLabelPlaceholder',
                                                    {
                                                      number: answerIndex + 1,
                                                    }
                                                  )}
                                                  onChange={(e) => {
                                                    answerForm.setFieldValue(
                                                      answerDataValueFieldName,
                                                      e.target.value as string
                                                    );
                                                    answerField?.onChange(e);
                                                  }}
                                                />
                                              )}

                                              <Svg
                                                // className="sl-remove-icon"
                                                className={getCssClass({
                                                  'sl-remove-icon':
                                                    answerIndex !== 0,
                                                  'sl-remove-icon sl-remove-icon-hidden':
                                                    answerIndex === 0,
                                                })}
                                                src={MINUS_CIRCLE_ICON_URL}
                                                onClick={() =>
                                                  arrayHelpers.remove(
                                                    answerIndex
                                                  )
                                                }
                                              />
                                            </div>
                                          )}
                                        </Draggable>
                                      )}
                                    </Field>
                                  );
                                }
                              )}

                              <Flex align="flex-end">
                                <BodyTextM
                                  className="sl-questionnaire-answer-add-option-button"
                                  onClick={() =>
                                    arrayHelpers.push(defaultAnswerData)
                                  }
                                >
                                  {t('addOption')}
                                </BodyTextM>
                                {hasAnswerOtherOption === false && (
                                  <>
                                    <div className="sl-vertical-divider" />
                                    <BodyTextM
                                      className="sl-questionnaire-answer-add-option-button"
                                      onClick={() =>
                                        arrayHelpers.push(
                                          defaultOtherAnswerData
                                        )
                                      }
                                    >
                                      {t('addOtherOption')}
                                    </BodyTextM>
                                  </>
                                )}
                              </Flex>
                            </div>
                          )}
                        </Droppable>
                      </DragDropContext>
                    )}
                  </FieldArray>
                </FormGroup2>
              )}
            </>
          ) : (
            <>
              <Flex className="sl-textmodule-dialog-questionnaire-block-drag-handler">
                <Svg
                  {...provided.dragHandleProps}
                  src={DRAG_HANDLE_HORIZONTAL_ICON_URL}
                />
              </Flex>
              {!questionnaire || !questionnaire.label ? (
                <BodyTextM>{t('emptyQuestion')}</BodyTextM>
              ) : (
                <Flex column>
                  <BodyTextM>{questionnaire?.label}</BodyTextM>
                  <ul className="sl-answers-list">
                    {questionnaire?.answers?.map((a, idx) => (
                      <li key={idx} className="sl-answers-item">
                        {questionnaire?.questionType ===
                          QuestionnaireQuestionType.QuestionnaireQuestionType_SingleSelection && (
                            <Radio
                              className="sl-type-button-view-only"
                              disabled
                              readOnly
                            />
                          )}

                        {questionnaire?.questionType ===
                          QuestionnaireQuestionType.QuestionnaireQuestionType_MultipleSelection && (
                            <Checkbox
                              className="sl-type-button-view-only"
                              disabled
                              readOnly
                            />
                          )}
                        <BodyTextM>{a.value}</BodyTextM>
                      </li>
                    ))}
                  </ul>
                </Flex>
              )}
            </>
          )}
        </div>
      )}
    </Draggable>
  );
};

export default QuestionnaireBlock;
