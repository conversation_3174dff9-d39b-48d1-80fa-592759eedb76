import { proxy, useSnapshot } from 'valtio';
import {
  getTextModules,
  createTextModule as createTextModuleOnServer,
  updateTextModule as updateTextModuleOnServer,
} from '@tutum/hermes/bff/legacy/app_admin_text_module';
import type {
  CreateTextModuleRequest,
  UpdateTextModuleRequest,
} from '@tutum/hermes/bff/app_admin_text_module';
import type {
  TextModule,
  TextModulePaginationRequest,
  TextModulePaginationResponse,
} from '@tutum/hermes/bff/text_module_common';

interface ITextModuleStore {
  isLoading: boolean;
  error?: Error | any;
  textmodules: TextModule[];
  page?: number;
  pageSize?: number;
  total?: number;
}

interface ITextModuleActions {
  fetchTextmodulesList: (
    payload: TextModulePaginationRequest
  ) => Promise<TextModulePaginationResponse | undefined>;
  createTextmodule: (payload: CreateTextModuleRequest) => Promise<void>;
  setPaginationOptions: (payload: { page?: number; pageSize?: number }) => void;
  editTextmodule: (payload: UpdateTextModuleRequest) => Promise<void>;
}

const initialStore: ITextModuleStore = {
  isLoading: false,
  textmodules: [],
  page: 1,
  pageSize: 10,
  total: 0,
};

const store = proxy<ITextModuleStore>(initialStore);

export const TextModuleActionsManager: ITextModuleActions = {
  fetchTextmodulesList: async ({ query, page = 1, pageSize = 10, bsnrId }) => {
    try {
      store.isLoading = true;
      const ressponse = await getTextModules({
        query,
        page,
        pageSize,
        useFors: [],
        bsnrId,
      });
      const res = ressponse?.data;
      const modules = res?.textModules ?? [];
      store.textmodules = modules;
      store.page = res.page || page;
      store.total = res.total;
      store.isLoading = false;
      store.error = null;
      return res;
    } catch (error) {
      store.isLoading = false;
      store.error = error;
    }
  },
  createTextmodule: async (payload) => {
    try {
      store.isLoading = true;
      await createTextModuleOnServer(payload);
    } catch (error) {
      store.isLoading = false;
      store.error = error;
      throw error;
    }
  },
  setPaginationOptions: (payload: { page?: number; pageSize?: number }) => {
    if (payload.page) {
      store.page = payload.page;
    }

    if (payload.pageSize) {
      store.pageSize = payload.pageSize;
    }
  },
  editTextmodule: async (payload) => {
    try {
      store.isLoading = true;
      await updateTextModuleOnServer(payload);
      const newList = _optimisticEditTextmodule(
        payload.textModule,
        store.textmodules
      );
      store.textmodules = newList;
      store.page = 1;
      store.pageSize = 10;
      store.isLoading = false;
      store.error = null;
    } catch (error) {
      store.isLoading = false;
      store.error = error;
      throw error;
    }
  },
};

function _optimisticEditTextmodule(
  newModule: TextModule,
  oldModules: TextModule[]
): TextModule[] {
  const newModuleList = [...oldModules].filter((t) => t.id !== newModule.id);
  newModuleList.unshift(newModule);
  return newModuleList;
}

export function useTextModuleStore() {
  return useSnapshot(store);
}
