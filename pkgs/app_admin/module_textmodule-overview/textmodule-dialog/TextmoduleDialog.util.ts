import type { TextModule } from '@tutum/hermes/bff/legacy/text_module_common';

export function formatTextShortcut(shortcut: string): string {
  return (shortcut ?? '').replaceAll(/\s/g, '_');
}

export function filterInvalidQuestionnaire(
  values: TextModule
) {
  if (!values.content) {
    return undefined;
  }
  const validModules = [...values.content.data]?.filter(
    (d) =>
      !d.questionnaire || (d.questionnaire && d.questionnaire?.label !== '')
  );

  return { ...values.content, data: validModules };
}
