import {
  Field,
  FieldArray,
  Formik,
  FormikErrors,
  Form as FormikForm,
  FormikHelpers,
} from 'formik';
import { get } from 'lodash';
import intersection from 'lodash/intersection';
import isEmpty from 'lodash/isEmpty';
import React, { useState, useEffect, useRef, useMemo } from 'react';

import {
  BodyTextM,
  Button,
  Flex,
  FormGroup2,
  IMenuItem,
  IMultiSelectProps,
  LeaveConfirmModal,
  MessageBar,
  Modal,
  ModalSize,
  MultiSelect,
} from '@tutum/design-system/components';
import { Tab, Tabs } from '@tutum/design-system/components/Core';
import { Classes } from '@tutum/design-system/components/Core';
import { Radio, RadioGroup } from '@tutum/design-system/components/Core';
import InputGroup from '@tutum/design-system/components/Core/InputGroup';
import { isValid } from '@tutum/hermes/bff/legacy/app_admin_text_module';
import type { TextModule } from '@tutum/hermes/bff/legacy/text_module_common';
import {
  ModuleType,
  TextModuleContent,
  TextModuleStatus,
  TextModuleUseFor,
} from '@tutum/hermes/bff/text_module_common';
import I18n from '@tutum/infrastructure/i18n';
import CreatePatientUtil from '@tutum/infrastructure/utils/form.util';
import FreetextFormGroup from '../freetext-form-group';
import OmimGHgncChainFormGroup from '../omimg-hgnc-chain-form-group';
import QuestionnaireFormGroup from '../questionnaire-form-group';
import {
  RESET_TEXTMODULE_FORM_SIGNAL,
  TEXT_SHORTCUT_REGEXP,
  defaultFormValue,
  defaultUsedForOptions,
} from './TextmoduleDialog.constant';
import { TextmoduleDialogProps } from './TextmoduleDialog.type';
import {
  filterInvalidQuestionnaire,
  formatTextShortcut,
} from './TextmoduleDialog.util';
import { useQueryGetAllTimelineDocumentType } from '@tutum/hermes/bff/legacy/app_admin_timeline_document_type';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { SystemDateKey, useQueryGetSetting } from '@tutum/hermes/bff/legacy/app_mvz_settings';
import { SettingsFeatures } from '@tutum/hermes/bff/settings_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

type UsedForMultiSelectOption = IMenuItem<TextModuleUseFor>;
const OMIMG_CHAIN = TextModuleUseFor.TextModuleUseFor_OmimGChain;
const HGNC = TextModuleUseFor.TextModuleUseFor_HGNC;
const TEXT_MODULE_NOT_AVAILABLE_SAME_TIME = [OMIMG_CHAIN, HGNC]

const usedForSelectStyles = {
  container(base) {
    return { ...base, zIndex: 2 };
  },
};

const UsedForMultiSelect: React.FC<
  Omit<IMultiSelectProps<IMenuItem<any>>, 'theme'>
> = (props) => {
  const { t } = I18n.useTranslation({
    namespace: 'Textmodule',
    nestedTrans: 'Dialog',
  });
  const { data, isSuccess } = useQueryGetSetting({
    feature: SettingsFeatures.SettingsFeatures_SystemDate,
    settings: [SystemDateKey.SystemDateKey_CurrentDate],
  });

  const systemDate = useMemo(() => {
    return isSuccess && data?.settings?.[SystemDateKey.SystemDateKey_CurrentDate]
      ? new Date(
        parseInt(data.settings[SystemDateKey.SystemDateKey_CurrentDate])
      )
      : datetimeUtil.date();

  }, [isSuccess, data?.settings]);
  const hgncAvailableDate = new Date("2025-07-01T00:00:00")
  const hgncOrOmimGChainHidden = useMemo(() => {
    return systemDate < hgncAvailableDate ? TextModuleUseFor.TextModuleUseFor_HGNC : TextModuleUseFor.TextModuleUseFor_OmimGChain
  }, [systemDate, hgncAvailableDate])


  const {
    data: listCustomDocumentType,
    isLoading: isLoadingListCustomDocumentType,
  } = useQueryGetAllTimelineDocumentType({
    pagination: {
      page: 1,
      pageSize: 1000,
      order: Order.ASC,
      sortBy: 'createdAt',
    },
    isCustomOnly: true,
    isActiveOnly: true,
  });

  const listCustomDocumentTypeOptions = useMemo(() => {
    const options = defaultUsedForOptions.reduce<IMenuItem[]>((acc, v) => {
      if (v === hgncOrOmimGChainHidden) {
        return acc;
      }
      acc.push({
        label: t(v),
        value: v,
      });
      return acc;
    }, []);
    if (listCustomDocumentType?.documentTypes) {
      options.push(
        ...listCustomDocumentType.documentTypes.map((v) => ({
          label: v.abbreviation,
          value: v.abbreviation as TextModuleUseFor,
        }))
      );
    }
    return options;
  }, [listCustomDocumentType, defaultUsedForOptions]);

  return (
    <MultiSelect
      {...props}
      isLoading={isLoadingListCustomDocumentType}
      options={listCustomDocumentTypeOptions}
      styles={usedForSelectStyles}
      isClearable={false}
      isOptionDisabled={(opt, selectedValue) => {
        if (selectedValue && selectedValue.length === 0) return false;

        const selectValues = (selectedValue || []).map(
          (v) => v.value
        );
        const hasOmimGOrHGNC = selectValues.some(v => TEXT_MODULE_NOT_AVAILABLE_SAME_TIME.includes(v as TextModuleUseFor))
        if (hasOmimGOrHGNC) {
          return !TEXT_MODULE_NOT_AVAILABLE_SAME_TIME.includes(opt.value as TextModuleUseFor)
        }
        return TEXT_MODULE_NOT_AVAILABLE_SAME_TIME.includes(opt.value as TextModuleUseFor);
      }}
    />
  );
};

const TextmoduleDialog: React.FC<TextmoduleDialogProps> = (props) => {
  const { defaultValues } = props;
  const { t: translator } = I18n.useTranslation({
    namespace: 'Textmodule',
    nestedTrans: 'Dialog',
  });

  const prevTabId = useRef<ModuleType>(ModuleType.ModuleType_FreeText);

  const tabDataMap = useRef<Map<ModuleType, TextModuleContent>>(
    new Map<ModuleType, TextModuleContent>()
  ).current;

  const [isOpenConfirmDialog, showConfirmDialog] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  const [selectedTextModuleOmimOrHgnc, setSelectedTextModuleOmimOrHgnc] = useState<TextModuleUseFor | null>(null);
  const [hasEmptyError, setHasEmptyError] = useState(false);

  function closeTextmoduleDialog() {
    showConfirmDialog(true);
  }

  async function handleSubmit(
    values: TextModule,
    formikBag: FormikHelpers<TextModule>
  ): Promise<void> {
    try {
      setIsLoading(true);
      const payload = {
        ...values,
        status: TextModuleStatus.TextModuleStatus_Active,
      };
      // NOTE: if list empty -> means select all
      if (!values?.useFor?.length) {
        values.useFor = defaultUsedForOptions;
      }

      // NOTE: filter out empty questionnaire
      if (values.content.data?.length) {
        payload.content = filterInvalidQuestionnaire(values);
      }

      const validationResponse = await isValid({
        textModule: payload,
      });
      const errorKeys = Object.keys(validationResponse.data.errors);
      if (errorKeys.length > 0) {
        errorKeys.forEach((errorKey) => {
          const err = validationResponse.data.errors[errorKey];
          formikBag.setFieldError(
            err.field,
            translator(`errors.${err.errorCode}`, {
              fieldName: translator(`formFields.${err.field}`),
            })
          );
        });
        setIsLoading(false);
        return;
      }

      await props.onSubmit?.(payload);

      formikBag.resetForm({ values: defaultFormValue });
      formikBag.setStatus(RESET_TEXTMODULE_FORM_SIGNAL);
      props.onClose();
      // eslint-disable-next-line no-useless-catch
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  }

  function validateForm(
    values: TextModule
  ): void | object | Promise<FormikErrors<TextModule>> {
    const errors: FormikErrors<TextModule> = {};
    const containForm = !isEmpty(
      intersection(values.useFor, [
        TextModuleUseFor.TextModuleUseFor_Form,
        TextModuleUseFor.TextModuleUseFor_BMP,
      ])
    );
    if (
      containForm &&
      values.moduleType === ModuleType.ModuleType_Questionnaire
    ) {
      errors.useFor = translator('notSupportQuestionnaires');
    } else {
      delete errors['useFor'];
    }
    if (!values.useFor?.length) {
      errors.useFor = translator('usedForError');
    }

    const textShortcut = formatTextShortcut(values.textShortcut);
    const isShortcutValid = textShortcut.match(TEXT_SHORTCUT_REGEXP);

    if (!isShortcutValid) {
      errors.textShortcut = translator('textShortcutError');
    }

    const validContent = filterInvalidQuestionnaire(values);

    if (!validContent || !validContent.data?.length) {
      errors.content = {
        data: translator('missingContentError'),
      };
    }

    return !isEmpty(errors) ? errors : undefined;
  }

  // NOTE: auto-fill default data
  useEffect(() => {
    const moduleId = defaultValues?.moduleType ?? defaultFormValue.moduleType;
    const content = defaultValues?.content ?? defaultFormValue.content;
    prevTabId.current = moduleId;
    tabDataMap.set(moduleId, content);

    if (defaultValues?.useFor) {
      setSelectedTextModuleOmimOrHgnc(
        defaultValues?.useFor?.find(v => TEXT_MODULE_NOT_AVAILABLE_SAME_TIME.includes(v))!
      );
    }
  }, [defaultValues]);

  return (
    <>
      <Modal
        title={props.title}
        isOpen={props.isOpen}
        onClose={closeTextmoduleDialog}
        size={ModalSize.FULLSCREEN}
        className={props.className}
      >
        <Formik<TextModule>
          onSubmit={handleSubmit}
          initialValues={{
            ...defaultFormValue,
            ...defaultValues,
          }}
          validateOnMount={false}
          validateOnBlur
          validateOnChange
          validate={validateForm}
        >
          {({
            values,
            errors,
            touched,
            submitCount,
            dirty,
            initialValues,
            setFieldValue,
          }) => (
            <FormikForm className="sl-textmodule-form">
              <Flex className={Classes.DIALOG_BODY}>
                <Flex className="sl-textmodule-dialog-body" auto column>
                  <FormGroup2
                    name="useFor"
                    isRequired
                    label={translator('formFields.usedFor')}
                    submitCount={submitCount}
                    errors={errors}
                    touched={{ useFor: true }}
                  >
                    <Field name="useFor">
                      {({ field, form }) => (
                        <UsedForMultiSelect
                          name="useFor"
                          placeholder=""
                          defaultValue={initialValues?.useFor?.map((u) => ({
                            label: translator(u, undefined, { fallback: u }),
                            value: u,
                          }))}
                          onChange={(
                            newValues: Array<IMenuItem<TextModuleUseFor>>
                          ) => {
                            form.setFieldValue(
                              field.name,
                              newValues?.map((v) => v.value)
                            );

                            if (newValues) {
                              const isContainOmimGOrHgnc =
                                newValues.find(v => TEXT_MODULE_NOT_AVAILABLE_SAME_TIME.includes(v.value))?.value!
                              setSelectedTextModuleOmimOrHgnc(isContainOmimGOrHgnc);
                              if (isContainOmimGOrHgnc) {
                                setFieldValue(
                                  '@tutum/mvz/moduleType',
                                  ModuleType.ModuleType_FreeText
                                );
                              }
                            }
                          }}
                        />
                      )}
                    </Field>
                  </FormGroup2>
                  <FormGroup2
                    isRequired
                    name="textShortcut"
                    label={translator('formFields.trigger')}
                    submitCount={submitCount}
                    errors={errors}
                    touched={touched}
                  >
                    <Field name="textShortcut">
                      {({ field }) => (
                        <InputGroup
                          {...field}
                          onChange={(e) => {
                            const refinedValue = formatTextShortcut(
                              e.target.value as string
                            );
                            e.target.value = refinedValue;
                            field.onChange(e);
                          }}
                          className="sl-text-shortcut-input"
                          placeholder={translator('triggerPlaceholder')}
                          data-tab-id={field.textShortcut}
                          intent={CreatePatientUtil.getFormInputIntent(
                            submitCount,
                            !!touched.textShortcut,
                            errors.textShortcut
                          )}
                        />
                      )}
                    </Field>
                  </FormGroup2>
                  {!selectedTextModuleOmimOrHgnc && (
                    <FormGroup2
                      isRequired
                      name="moduleType"
                      label={translator('formFields.content')}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name="moduleType">
                        {({ field, form }) => (
                          <RadioGroup
                            inline
                            selectedValue={field.value}
                            onChange={(e) => {
                              const newTabId = e.currentTarget
                                .value as ModuleType;

                              // NOTE: write prev tab value into map
                              tabDataMap.set(prevTabId.current, values.content);

                              // NOTE: read next tab value from map
                              const tabValue = tabDataMap.get(newTabId);

                              // NOTE: use the current tab value as form value
                              form.setFieldValue('content', tabValue);

                              // NOTE: update moduleType field
                              form.setFieldValue(
                                field.name,
                                e.currentTarget.value
                              );

                              prevTabId.current = newTabId;
                            }}
                          >
                            <Radio
                              label={translator('freeText')}
                              value={ModuleType.ModuleType_FreeText}
                            />
                            <Radio
                              label={translator('questionnaire')}
                              value={ModuleType.ModuleType_Questionnaire}
                            />
                          </RadioGroup>
                        )}
                      </Field>
                    </FormGroup2>
                  )}
                  <FormGroup2
                    isRequired
                    name="content.data"
                    submitCount={submitCount}
                    errors={errors}
                    touched={touched}
                  >
                    <>
                      {selectedTextModuleOmimOrHgnc && (
                        <Field name="content.data">
                          {({ form: _form }) => {
                            const triggerNodeType = selectedTextModuleOmimOrHgnc === OMIMG_CHAIN ? TextModuleUseFor.TextModuleUseFor_OmimGChain : TextModuleUseFor.TextModuleUseFor_HGNC;
                            return <OmimGHgncChainFormGroup
                              defaultValue={
                                initialValues.moduleType ===
                                  ModuleType.ModuleType_FreeText
                                  ? initialValues?.content
                                  : undefined
                              }
                              triggerNodeType={triggerNodeType}
                              onChange={(newValues) => {
                                const hasEmptyError = newValues.data.some(
                                  (item) => {
                                    const hasNotFoundCode =
                                      get(item, 'omimG.value') === '999999';
                                    const hasEmptyDescription = isEmpty(
                                      get(
                                        item,
                                        'omimG.children[0].value',
                                        ''
                                      ).trim()
                                    );

                                    return (
                                      hasNotFoundCode && hasEmptyDescription
                                    );
                                  }
                                );

                                setHasEmptyError(hasEmptyError);
                                _form.setFieldValue('content', newValues);
                              }}
                            />
                          }}
                        </Field>
                      )}
                      {!selectedTextModuleOmimOrHgnc && (
                        <Tabs
                          id="sl-tab-textmodule-id"
                          selectedTabId={values.moduleType}
                          onChange={() => {
                            // NOTE: reset to empty content data
                            setFieldValue('content.data', []);
                          }}
                        >
                          <Tab
                            id={ModuleType.ModuleType_FreeText}
                            panel={
                              <Field name="content.data">
                                {({ form: _form }) => (
                                  <FreetextFormGroup
                                    defaultValue={
                                      initialValues.moduleType ===
                                        ModuleType.ModuleType_FreeText && values.useFor.length
                                        ? initialValues?.content
                                        : undefined
                                    }
                                    onChange={(newValues) => {
                                      if (
                                        values.moduleType !==
                                        ModuleType.ModuleType_FreeText
                                      ) {
                                        return;
                                      }
                                      _form.setFieldValue('content', newValues);
                                    }}
                                  />
                                )}
                              </Field>
                            }
                          />
                          <Tab
                            id={ModuleType.ModuleType_Questionnaire}
                            panel={
                              <FieldArray
                                name="content.data"
                                render={(props) => (
                                  <QuestionnaireFormGroup {...props} />
                                )}
                              />
                            }
                          />
                        </Tabs>
                      )}
                    </>
                  </FormGroup2>
                  {selectedTextModuleOmimOrHgnc && hasEmptyError && (
                    <BodyTextM>
                      <MessageBar
                        type="error"
                        content={translator('errors.Empty999999Description')}
                      />
                    </BodyTextM>
                  )}
                </Flex>
              </Flex>
              <Flex
                align="center"
                justify="flex-end"
                className={Classes.DIALOG_FOOTER}
              >
                <Flex
                  gap={16}
                  justify="flex-end"
                  className="sl-textmodule-footer-action-section"
                >
                  <Button
                    large
                    type="button"
                    minimal
                    outlined
                    intent="primary"
                    className="sl-textmodule-btn sl-textmodule-btn--large"
                    onClick={closeTextmoduleDialog}
                  >
                    {translator('cancel')}
                  </Button>
                  <Button
                    large
                    type="submit"
                    intent="primary"
                    className="sl-textmodule-btn sl-textmodule-btn--large"
                    loading={isLoading}
                    disabled={
                      isLoading ||
                      !dirty ||
                      hasEmptyError ||
                      errors?.useFor === translator('notSupportQuestionnaires')
                    }
                  >
                    {props.confirmText || translator('create')}
                  </Button>
                </Flex>
              </Flex>
            </FormikForm>
          )}
        </Formik>
      </Modal>
      <LeaveConfirmModal
        isOpen={isOpenConfirmDialog}
        onConfirm={() => {
          props.onClose();
          showConfirmDialog(false);
        }}
        onClose={() => showConfirmDialog(false)}
      />
    </>
  );
};

export default React.memo(TextmoduleDialog);
