import {
  ModuleType,
  TextModuleUseFor,
} from '@tutum/hermes/bff/text_module_common';
import type { TextModule } from '@tutum/hermes/bff/text_module_common';
import { TextModuleStatus } from '@tutum/hermes/bff/legacy/text_module_common';


export const defaultUsedForOptions: TextModuleUseFor[] = [
  TextModuleUseFor.TextModuleUseFor_Anamnesis,
  TextModuleUseFor.TextModuleUseFor_Findings,
  TextModuleUseFor.TextModuleUseFor_Cave,
  TextModuleUseFor.TextModuleUseFor_Note,
  TextModuleUseFor.TextModuleUseFor_Therapy,
  TextModuleUseFor.TextModuleUseFor_Form,
  TextModuleUseFor.TextModuleUseFor_BMP,
  TextModuleUseFor.TextModuleUseFor_Doctorletter,
];

export const fullUsedForList: TextModuleUseFor[] = [
  TextModuleUseFor.TextModuleUseFor_Anamnesis,
  TextModuleUseFor.TextModuleUseFor_Findings,
  TextModuleUseFor.TextModuleUseFor_Cave,
  TextModuleUseFor.TextModuleUseFor_Note,
  TextModuleUseFor.TextModuleUseFor_Therapy,
  TextModuleUseFor.TextModuleUseFor_Form,
  TextModuleUseFor.TextModuleUseFor_OmimGChain,
  TextModuleUseFor.TextModuleUseFor_HGNC,
  TextModuleUseFor.TextModuleUseFor_BMP,
  TextModuleUseFor.TextModuleUseFor_Doctorletter,
];

export const defaultFormValue: TextModule = {
  textShortcut: '',
  content: {
    text: '',
    data: [],
  },
  moduleType: ModuleType.ModuleType_FreeText,
  status: TextModuleStatus.TextModuleStatus_Active,
  useFor: defaultUsedForOptions,
};

export const TEXT_SHORTCUT_REGEXP = new RegExp('(\\w)+', 'gm');

export const RESET_TEXTMODULE_FORM_SIGNAL = 'reset-free-text-form-group';
