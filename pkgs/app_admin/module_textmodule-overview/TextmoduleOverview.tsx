import React, { useEffect, useState, useMemo } from 'react';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import useTranslation from 'next-translate/useTranslation';

import { Flex, H1, Button, BodyTextM } from '@tutum/design-system/components';
import { Divider } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import { InputGroup } from '@tutum/design-system/components/Core';
import { Svg } from '@tutum/design-system/components/Svg';
import { genColumns, customStyles } from './TextmoduleOverview.config';
import type { TextModuleOverviewProps } from './TextmoduleOverview.type';
import {
  defaultPaginationOptions,
  NoTextmoduleSvgURL,
  PlusIconSvgURL,
  SearchIconSvgURL,
} from './TextmoduleOverview.constant';
import {
  TextModuleActionsManager,
  useTextModuleStore,
} from './TextmoduleOverview.store';
import CreateTextModuleDialog from './textmodule-dialog-create';
import EditTextmoduleDialog from './textmodule-dialog-edit';
import DeleteConfirmationDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { deactivateTextModule } from '@tutum/hermes/bff/legacy/app_admin_text_module';
import type {
  TextModule,
  TextModulePaginationRequest,
} from '@tutum/hermes/bff/legacy/text_module_common';
import { alertSuccessfully } from '@tutum/design-system/components/Toaster';
import { GetTextModulesRequest } from '@tutum/hermes/bff/legacy/app_mvz_text_module';
import { COLOR } from '@tutum/design-system/themes/styles';
import SelectBsnr from '@tutum/admin/components/SelectBsnr/SelectBsnr';

const TextModuleOverview: React.FC<TextModuleOverviewProps> = (props) => {
  const { className } = props;
  const { t } = useTranslation('Textmodule');

  const [searchQuery, setSearchQuery] = useState<string>('');

  const {
    textmodules,
    page,
    pageSize,
    total: totalRows,
  } = useTextModuleStore();

  const textmodulesList = useMemo(() => cloneDeep(textmodules), [textmodules]);

  const [isOpenCreateDialog, setOpenCreateDialog] = useState(false);

  const [editedModule, setEditedModule] = useState<TextModule | null>(null);
  const [removingTextmoduleId, setRemovingTextmoduleId] =
    useState<string | undefined>(undefined);
  const [bsnrId, setBsnrId] = useState<string>();

  const queryPayload: TextModulePaginationRequest = useMemo(
    () => ({
      query: searchQuery,
      page,
      pageSize,
      useFors: [],
      bsnrId: bsnrId,
    }),
    [searchQuery, page, pageSize, bsnrId]
  );

  const debouncedFetchModules = useMemo(
    () =>
      debounce(
        (query: GetTextModulesRequest) => {
          TextModuleActionsManager.fetchTextmodulesList(query);
        },
        400,
        { trailing: true }
      ),
    [TextModuleActionsManager.fetchTextmodulesList]
  );

  useEffect(() => {
    debouncedFetchModules(queryPayload);
  }, [queryPayload]);

  function openCreateDialog() {
    setOpenCreateDialog(true);
  }

  function closeCreateDialog() {
    TextModuleActionsManager.fetchTextmodulesList(queryPayload);
    setOpenCreateDialog(false);
  }

  function onChangePage(pageNo: number) {
    TextModuleActionsManager.setPaginationOptions({ page: pageNo });
  }

  function onChangeRowsPerPage(pageSize: number) {
    TextModuleActionsManager.setPaginationOptions({ pageSize });
  }

  const onChangeSearchInput: React.FormEventHandler<HTMLInputElement> = (e) => {
    setSearchQuery((e.target as HTMLInputElement).value);
    TextModuleActionsManager.setPaginationOptions(defaultPaginationOptions);
  };

  function openEditDialog(editModule: TextModule) {
    setEditedModule(editModule);
  }

  function closeEditDialog() {
    setEditedModule(null);
  }

  function onRemoveTextmodule(textModuleId: string) {
    setRemovingTextmoduleId(textModuleId);
  }

  const columns = genColumns({
    t,
    openEditDialog,
    onRemoveTextmodule,
  });

  return (
    <div className={className}>
      <Flex p="28px 16px">
        <H1>{t('textmodule')}</H1>
      </Flex>
      <Divider style={{ margin: 0 }} />
      <Flex p={16} w="100%">
        <Flex gap={16} align="center">
          <InputGroup
            className="sl-panel-search-input"
            type="text"
            leftElement={<Svg src={SearchIconSvgURL} />}
            placeholder={t('search')}
            value={searchQuery}
            onInput={onChangeSearchInput}
          />
          <SelectBsnr
            selectedValue={bsnrId!}
            onItemSelect={(item) => {
              setBsnrId(item.value);
            }}
          />
        </Flex>
        <div className="sl-spacer" />
        <Button
          intent="primary"
          id="createTextModule"
          className="sl-textmodule-create-btn"
          onClick={openCreateDialog}
          icon={<Svg src={PlusIconSvgURL} />}
          small
        />
      </Flex>
      <Table
        className="sl-textmodule-table"
        columns={columns}
        data={textmodulesList}
        customStyles={customStyles}
        noHeader
        persistTableHead
        striped
        noDataComponent={
          <Flex className="sl-no-data-image">
            <Svg src={NoTextmoduleSvgURL} />
            <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
              {t('noTextModuleMessage')}
            </BodyTextM>
          </Flex>
        }
        progressPending={false}
        pagination
        paginationServer
        paginationDefaultPage={1}
        paginationResetDefaultPage
        paginationTotalRows={totalRows}
        paginationPerPage={pageSize}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
      />
      {isOpenCreateDialog && (
        <CreateTextModuleDialog
          title={t('createTextmodule')}
          isOpen={isOpenCreateDialog}
          onClose={closeCreateDialog}
          defaultValues={{
            bsnrId: bsnrId,
          }}
        />
      )}
      {editedModule !== null && (
        <EditTextmoduleDialog
          title={t('editTextmodule')}
          isOpen={editedModule !== null}
          onClose={closeEditDialog}
          confirmText={t('save')}
          defaultValues={editedModule}
        />
      )}
      {removingTextmoduleId && (
        <DeleteConfirmationDialog
          close={() => {
            setRemovingTextmoduleId(undefined);
          }}
          confirm={() => {
            deactivateTextModule({
              id: removingTextmoduleId,
            }).then(() => {
              setRemovingTextmoduleId(undefined);
              TextModuleActionsManager.fetchTextmodulesList(queryPayload);
              alertSuccessfully(
                t('RemoveConfirmationDialog.removeTextmoduleSuccess')
              );
            });
          }}
          text={{
            btnCancel: t('RemoveConfirmationDialog.no'),
            btnOk: t('RemoveConfirmationDialog.yesRemove'),
            title: t('RemoveConfirmationDialog.removeTitle'),
            message: t('RemoveConfirmationDialog.actionCannotBeUndone'),
          }}
        />
      )}
    </div>
  );
};

export default TextModuleOverview;
