import React from 'react';
import {
  IDataTableColumn,
  IDataTableStyles,
  CellPopoverMenuStyleWrapper,
  SLHeaderCol,
} from '@tutum/design-system/components/Table';
import { BodyTextM } from '@tutum/design-system/components/Typography';
import { Flex } from '@tutum/design-system/components/Flexbox';
import { Svg } from '@tutum/design-system/components/Svg';
import { Popover } from '@tutum/design-system/components/Core';
import type { TextModule } from '@tutum/hermes/bff/text_module_common';
import {
  RemoveIconSvgURL,
  EditIconSvgURL,
  MoreVerticalIconSvgURL,
} from './TextmoduleOverview.constant';
import { COLOR } from '@tutum/design-system/themes/styles';

interface GenerateTableColumns {
  t: (token: string) => string;
  openEditDialog: (editModule: TextModule) => void;
  onRemoveTextmodule: (id: string) => void;
}

export const genColumns = ({
  t,
  openEditDialog,
  onRemoveTextmodule,
}: GenerateTableColumns): Array<IDataTableColumn<TextModule>> => [
    {
      name: <SLHeaderCol label={t('textShortcut')} sortable={false} />,
      width: '420px',
      cell: (row) => {
        return (
          <Flex>
            <BodyTextM>{row?.textShortcut}</BodyTextM>
          </Flex>
        );
      },
    },
    {
      name: <SLHeaderCol label={t('content')} sortable={false} />,
      cell: (row) => {
        return (
          <Flex>
            <BodyTextM limitLines={2} whiteSpace="pre-line">
              {row?.content.text}
            </BodyTextM>
          </Flex>
        );
      },
    },
    {
      name: <SLHeaderCol label={''} sortable={false} />,
      width: '40px',
      cell: (row) => {
        return (
          <Popover
            content={
              <CellPopoverMenuStyleWrapper>
                <div className="sl-row" onClick={() => openEditDialog(row)}>
                  <Svg className="sl-icon" src={EditIconSvgURL} />
                  <p className="sl-text">{t('edit')}</p>
                </div>
                <div
                  className="sl-row sl-row--danger"
                  onClick={() => onRemoveTextmodule(row.id!)}
                >
                  <Svg className="sl-icon" src={RemoveIconSvgURL} />
                  <p className="sl-text">{t('remove')}</p>
                </div>
              </CellPopoverMenuStyleWrapper>
            }
          >
            <Svg style={{ cursor: 'pointer' }} src={MoreVerticalIconSvgURL} />
          </Popover>
        );
      },
    },
  ];

export const customStyles: IDataTableStyles = {
  rows: {
    style: {
      paddingLeft: '8px',
    },
  },
  headRow: {
    style: {
      paddingLeft: '8px',
      textTransform: 'uppercase',
    },
  },
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
      borderRight: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
    },
  },
  cells: {
    style: {
      padding: '8px',
      alignItems: 'center',
      borderRight: `1px solid ${COLOR.BACKGROUND_SECONDARY_SHINE}`,
    },
  },
  tableWrapper: {
    style: {
      minHeight: '100%',
    },
  },
  table: {
    style: {
      minHeight: '500px',
    },
  },
  noData: {
    style: {
      minHeight: '100%',
      flex: 1,
    },
  },
};
