import CommonI18n from '@tutum/admin/locales/en/Common.json';
import InterfaceI18n from '@tutum/admin/locales/en/Interface.json';
import {
  alertError,
  alertSuccessfully,
  Button,
  Dialog,
  Flex,
  FormGroup2,
  H3,
  MultiSelect,
  ReactSelect,
} from '@tutum/design-system/components';
import {
  Checkbox,
  InputGroup,
  Radio,
  RadioGroup,
  TextArea,
} from '@tutum/design-system/components/Core';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import { useQueryGetAllTrustedUserDeviceList } from '@tutum/hermes/bff/legacy/app_admin';
import {
  useMutationCreateExternalDocumentSetting,
  useMutationUpdateExternalDocumentSetting,
  useMutationCreateDmExtension,
  useQueryGetDmExtensions,
} from '@tutum/hermes/bff/legacy/app_document_setting';
import {
  ExternalDocument,
  SourceTypeEnum,
} from '@tutum/hermes/bff/legacy/document_setting_common';
import I18n from '@tutum/infrastructure/i18n';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import {
  default as FormUtils,
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import { Field, FieldProps, Form, Formik, FormikProps } from 'formik';
import { useRef } from 'react';

export interface CreateExternalDocumentSettingDialogProps {
  isOpen: boolean;
  onClose: (dirty?: boolean) => void;
  className?: string;
  onSuccess?: () => void;
  initialData?: ExternalDocument;
  settingId?: string;
}
const initialValues: ExternalDocument = {
  deviceId: '',
  name: '',
  description: '',
  sourceType: SourceTypeEnum.SourceTypeEnum_LOCAL,
  browse: '',
  recursive: false,
  fileFormats: [],
};

const CreateExternalDocumentSetting: React.FC<CreateExternalDocumentSettingDialogProps> =
  ({
    isOpen,
    onClose,
    className,
    onSuccess,
    initialData = initialValues,
    settingId = '',
  }) => {
    const isEditDialog = !!settingId;
    const { t } = I18n.useTranslation<Paths<typeof InterfaceI18n>>({
      namespace: 'Interface',
    });

    const { t: tCommon } = I18n.useTranslation<Paths<typeof CommonI18n>>({
      namespace: 'Common',
    });
    const submitBtnName = isEditDialog
      ? tCommon('ButtonActions.updatedText')
      : tCommon('ButtonActions.create');
    const titleDialog = isEditDialog
      ? t('ExternalDocument.EditExternalDocumentSetting')
      : t('ExternalDocument.CreateExternalDocumentSetting');

    const { isLoading, data: { devices } = { devices: [] } } =
      useQueryGetAllTrustedUserDeviceList();

    const deviceItems = devices.map((item) => {
      return {
        label: item.deviceName,
        value: item.id,
      };
    });

    const { mutateAsync: createExternalDocumentSetting } =
      useMutationCreateExternalDocumentSetting({
        onSuccess: () => {
          onSuccess?.();
          alertSuccessfully(
            t('ExternalDocument.CreateExternalDocumentSettingSuccessfully')
          );
          onClose();
        },
        onError: (error) => {
          alertError(error.message);
        },
      });
    const { mutateAsync: updateExternalDocumentSetting } =
      useMutationUpdateExternalDocumentSetting({
        onSuccess: () => {
          onSuccess?.();
          alertSuccessfully(
            t('ExternalDocument.UpdatedExternalDocumentSettingSuccessfully')
          );
          onClose();
        },
        onError: (error) => {
          alertError(error.message);
        },
      });
    const { mutateAsync: createDmpExtension } = useMutationCreateDmExtension({
      onError: (error) => {
        alertError(error.message);
      },
    });

    const { data: dmExtensionsData, refetch } = useQueryGetDmExtensions({
      select: (res) =>
        res.data.items?.sort((a, b) => a.name.localeCompare(b.name)),
    });

    const handleSubmit = async (value: ExternalDocument) => {
      const data = { ...value };
      if (data.sourceType === SourceTypeEnum.SourceTypeEnum_LOCAL) {
        delete data.host;
        delete data.username;
        delete data.password;
      }

      if (isEditDialog) {
        return updateExternalDocumentSetting({ id: settingId, data });
      }
      return createExternalDocumentSetting({ data });
    };
    const onValidateForm = async (values: ExternalDocument) => {
      const errorMessage = tCommon('FormValidation.fieldRequired');
      const validateFields: ValidateField[] = [
        {
          fieldName: 'name',
        },
        {
          fieldName: 'sourceType',
        },
        {
          fieldName: 'deviceId',
        },
        {
          fieldName: 'browse',
        },
      ].map(({ fieldName }) => ({
        fieldName,
        validateRule: () => isEmpty(values[fieldName], true),
        errorMessage,
      }));
      validateFields.push({
        fieldName: 'host',
        validateRule: () => {
          if (values.sourceType === SourceTypeEnum.SourceTypeEnum_LOCAL) {
            return false;
          }
          return isEmpty(values['host'], true);
        },
        errorMessage,
      });

      const { errors } = FormUtils.validateForm(validateFields, null);
      return errors;
    };

    const formikRef = useRef<FormikProps<ExternalDocument>>(null);
    const handleCreate = async (inputValue: string) => {
      if (!inputValue) return;
      createDmpExtension({ name: inputValue }).then(() => {
        refetch();
      });
    };

    const renderForm = (formikProps: FormikProps<ExternalDocument>) => {
      const { isSubmitting, setFieldValue } = formikProps;

      return (
        <Form>
          <Flex gap={16} px={16} py={24} column>
            <H3>{t('GDTSetting.General')}</H3>
            <FormGroup2
              label={t('ExternalDocument.Name')}
              isRequired
              {...formikProps}
              name="name"
            >
              <Field name="name">
                {({
                  field,
                }: FieldProps<ExternalDocument['name'], ExternalDocument>) => {
                  return <InputGroup {...field} disabled={isSubmitting} />;
                }}
              </Field>
            </FormGroup2>

            <FormGroup2
              label={t('Description')}
              {...formikProps}
              name="description"
            >
              <Field name="description">
                {({
                  field,
                }: FieldProps<
                  ExternalDocument['description'],
                  ExternalDocument
                >) => {
                  return <TextArea {...field} fill disabled={isSubmitting} />;
                }}
              </Field>
            </FormGroup2>

            <FormGroup2
              label={t('getFileFrom')}
              isRequired
              {...formikProps}
              name="sourceType"
            >
              <Field name="sourceType">
                {({
                  field,
                }: FieldProps<
                  ExternalDocument['sourceType'],
                  ExternalDocument
                >) => {
                  return (
                    <RadioGroup
                      {...field}
                      inline
                      selectedValue={field.value}
                      disabled={isSubmitting}
                    >
                      <Radio
                        label={t('localFile')}
                        value={SourceTypeEnum.SourceTypeEnum_LOCAL}
                      />
                      <Radio
                        label={t('FTP')}
                        value={SourceTypeEnum.SourceTypeEnum_FTP}
                      />
                      <Radio
                        label={t('SMB')}
                        value={SourceTypeEnum.SourceTypeEnum_SMB}
                      />
                    </RadioGroup>
                  );
                }}
              </Field>
            </FormGroup2>

            {formikProps.values.sourceType !=
              SourceTypeEnum.SourceTypeEnum_LOCAL && (
                <>
                  <Field name="host">
                    {({
                      field,
                    }: FieldProps<
                      ExternalDocument['host'],
                      ExternalDocument
                    >) => (
                      <FormGroup2
                        label={t('hostIP')}
                        {...formikProps}
                        isRequired
                        name="host"
                      >
                        <InputGroup {...field} disabled={isSubmitting} />
                      </FormGroup2>
                    )}
                  </Field>
                  <Flex gap={16}>
                    <Field name="username">
                      {({
                        field,
                      }: FieldProps<
                        ExternalDocument['username'],
                        ExternalDocument
                      >) => (
                        <FormGroup2
                          label={t('userName')}
                          {...formikProps}
                          name="username"
                          fill
                        >
                          <InputGroup {...field} disabled={isSubmitting} />
                        </FormGroup2>
                      )}
                    </Field>
                    <Field name="password">
                      {({
                        field,
                      }: FieldProps<
                        ExternalDocument['password'],
                        ExternalDocument
                      >) => (
                        <FormGroup2
                          label={t('password')}
                          {...formikProps}
                          name="password"
                          fill
                        >
                          <InputGroup {...field} disabled={isSubmitting} />
                        </FormGroup2>
                      )}
                    </Field>
                  </Flex>
                </>
              )}

            <FormGroup2
              label={t('Workplace')}
              isRequired
              {...formikProps}
              name="deviceId"
            >
              <Field name="deviceId">
                {({
                  field,
                  form,
                }: FieldProps<
                  ExternalDocument['deviceId'],
                  ExternalDocument
                >) => {
                  const { setFieldValue, isSubmitting } = form;
                  return (
                    <ReactSelect
                      id="device"
                      instanceId={'device'}
                      isSearchable={false}
                      selectedValue={field.value}
                      items={deviceItems}
                      onItemSelect={(item) => {
                        setFieldValue(field.name, item.value);
                      }}
                      isDisabled={isSubmitting}
                    />
                  );
                }}
              </Field>
            </FormGroup2>

            <FormGroup2
              label={t('ExternalDocument.FilePath')}
              isRequired
              {...formikProps}
              name="browse"
              subLabel={t('ExternalDocument.HintFolderPath')}
            >
              <Field name="browse">
                {({
                  field,
                }: FieldProps<
                  ExternalDocument['browse'],
                  ExternalDocument
                >) => {
                  return (
                    <InputGroup
                      {...field}
                      disabled={isSubmitting}
                      placeholder={t('ExternalDocument.FolderPathPlaceholder')}
                    />
                  );
                }}
              </Field>
            </FormGroup2>

            <FormGroup2
              {...formikProps}
              name="fileFormats"
              label={t('ExternalDocument.DocumentsToImport')}
            >
              <Field name="fileFormats">
                {({
                  field,
                }: FieldProps<
                  ExternalDocument['fileFormats'],
                  ExternalDocument
                >) => {
                  return (
                    <MultiSelect
                      isCreatable
                      options={dmExtensionsData.map((item) => ({
                        label: item.name,
                        value: item.name,
                      }))}
                      value={field.value?.map((v) => ({ label: v, value: v }))}
                      onChange={(value) => {
                        if (Array.isArray(value)) {
                          setFieldValue(
                            field.name,
                            value.map((v) => v.value)
                          );
                        }
                      }}
                      onCreate={handleCreate}
                      isDisabled={isSubmitting}
                    />
                  );
                }}
              </Field>
            </FormGroup2>

            <FormGroup2 {...formikProps} name="recursive">
              <Field name="recursive">
                {({
                  field,
                }: FieldProps<
                  ExternalDocument['recursive'],
                  ExternalDocument
                >) => {
                  const { value, ...validFields } = field;
                  const checkBoxProps = {
                    ...validFields,
                    checked: value,
                    id: validFields.name,
                    label: t('ExternalDocument.Recursive'),
                    disabled: isSubmitting,
                  };
                  return <Checkbox {...checkBoxProps} />;
                }}
              </Field>
            </FormGroup2>
          </Flex>
        </Form>
      );
    };
    if (isLoading) return null;

    return (
      <Dialog
        className={className}
        title={titleDialog}
        isOpen={isOpen}
        onClose={() => onClose(formikRef.current?.dirty)}
        size="half"
        actions={
          <Flex gap={16} justify="flex-end">
            <Button
              outlined
              intent="primary"
              onClick={() => {
                onClose(formikRef.current?.dirty);
              }}
              large
              disabled={formikRef.current?.isSubmitting}
            >
              {tCommon('ButtonActions.cancelText')}
            </Button>
            <Button
              intent="primary"
              type="submit"
              large
              loading={formikRef.current?.isSubmitting}
              onClick={() => formikRef.current.handleSubmit()}
            >
              {submitBtnName}
            </Button>
          </Flex>
        }
      >
        <Formik<ExternalDocument>
          innerRef={formikRef}
          onSubmit={handleSubmit}
          validate={onValidateForm}
          component={renderForm}
          initialValues={initialData}
        />
      </Dialog>
    );
  };

export default CreateExternalDocumentSetting;
