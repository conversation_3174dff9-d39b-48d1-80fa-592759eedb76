import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { Flex, BodyTextM, Svg } from '@tutum/design-system/components';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import { Popover, Menu, MenuItem } from '@tutum/design-system/components/Core';
import { COLOR } from '@tutum/design-system/themes/styles';
import InterfaceI18n from '@tutum/admin/locales/en/Interface.json';
import CommonI18n from '@tutum/admin/locales/en/Common.json';
import {
  DocumentSettingItem,
  DocumentSettingType,
  GdtExport,
} from '@tutum/hermes/bff/legacy/document_setting_common';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import { TrustedDeviceResponse } from '@tutum/hermes/bff/legacy/app_admin';

interface IGenColumnsParams {
  t: IFixedNamespaceTFunction<Paths<typeof InterfaceI18n>>;
  tCommon: IFixedNamespaceTFunction<Paths<typeof CommonI18n>>;
  deviceList: TrustedDeviceResponse[];
  removedSettingById: (id: string) => void;
  onEdit: (i: DocumentSettingItem) => void;
}

const MoreIcon = '/images/more-vertical.svg';
const EditIcon = '/images/edit-2.svg';
const RemoveIcon = '/images/trash-bin-red.svg';

const getItem = (item: DocumentSettingItem) => {
  switch (item.documentSetting.documentSettingType) {
    case DocumentSettingType.DocumentSettingType_GdtExport:
      return item.documentSetting.gdtExport;
    case DocumentSettingType.DocumentSettingType_GdtImport:
      return item.documentSetting.gdtImport;
    case DocumentSettingType.DocumentSettingType_LdtExport:
      return item.documentSetting.ldtExport;
    case DocumentSettingType.DocumentSettingType_LdtImport:
      return item.documentSetting.ldtImport;
    default:
      return item.documentSetting.externalDocument;
  }
};
export const genColumns = ({
  t,
  tCommon,
  deviceList,
  removedSettingById,
  onEdit,
}: IGenColumnsParams): IDataTableColumn<DocumentSettingItem>[] => [
    {
      name: t('Name'),
      selector: (row) => getItem(row)?.name!,
      width: '240px',
    },
    {
      name: t('Type'),
      selector: (row) => t(row.documentSetting.documentSettingType),
      width: '240px',
    },
    {
      name: t('Workplace'),
      selector: (row) =>
        deviceList.find((d) => d.id === getItem(row)?.deviceId)?.deviceName!,
      width: '240px',
    },
    {
      name: t('Description'),
      selector: (row) => getItem(row)?.description!,
    },
    {
      name: '',
      width: '40px',
      right: true,
      cell: (row) => (
        <Popover
          className="sl-tooltip-more-icon"
          placement="bottom"
          content={
            <Menu>
              <MenuItem
                text={
                  <Flex align="center" gap={8}>
                    <Svg src={EditIcon} />
                    <BodyTextM>{tCommon('ButtonActions.edit')}</BodyTextM>
                  </Flex>
                }
                onClick={() => onEdit(row)}
              />
              <MenuItem
                text={
                  <Flex align="center" gap={8}>
                    <Svg src={RemoveIcon} />
                    <BodyTextM color={COLOR.TAG_BACKGROUND_RED}>
                      {tCommon('ButtonActions.removeText')}
                    </BodyTextM>
                  </Flex>
                }
                onClick={() => removedSettingById(row.id)}
              />
            </Menu>
          }
        >
          <Svg src={MoreIcon} alt="more-icon" />
        </Popover>
      ),
    },
  ];

export const GDT_FILE_EXTENSION = '.gdt';

export function updateFileNameOfExportSetting(setting?: GdtExport) {
  if (!setting) return;
  const { fileName } = setting;
  const newFileName = fileName.replace(GDT_FILE_EXTENSION, '');
  return { ...setting, fileName: newFileName };
}
