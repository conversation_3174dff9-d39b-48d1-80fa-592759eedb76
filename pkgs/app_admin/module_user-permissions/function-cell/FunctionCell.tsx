import React from 'react';
import { Checkbox } from '@tutum/design-system/components/Core';
import { IAccount } from '@tutum/admin/types/account.type';

export interface IFunctionCellProps {
  className?: string;
  functionName: string;
  account: IAccount;
  updateAccounts: (account: IAccount) => void;
}

const FunctionCell = (props: IFunctionCellProps) => {
  const { account, functionName, updateAccounts } = props;
  const checked = props.account.userPermissions?.some(
    (userPermission) => userPermission.function === props.functionName
  );
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    const updatedAccount = { ...account, isUpdated: true };
    if (checked) {
      updatedAccount.userPermissions && updatedAccount.userPermissions.push({
        accountId: account.id!,
        careProviderId: account.careProviderId,
        function: functionName,
      });
    } else {
      updatedAccount.userPermissions = updatedAccount.userPermissions?.filter(
        (userPermission) => userPermission.function !== functionName
      );
    }
    updateAccounts(updatedAccount);
  };
  return <Checkbox onChange={onChange} checked={checked} />;
};

export default FunctionCell;
