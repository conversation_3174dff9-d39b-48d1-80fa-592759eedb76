import { useState } from 'react';

type Props = { label: string; value: string }[];

export const useTabList = (tabs: Props) => {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  const handleTabChange = (val: string) => {
    const nexttab = tabs.find((tab) => tab.value === val);
    setSelectedTabIndex(tabs.indexOf(nexttab!));
  };

  return {
    selectedTab: tabs[selectedTabIndex],
    handleTabChange,
  };
};
