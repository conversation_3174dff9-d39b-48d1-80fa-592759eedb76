import { useRouter } from 'next/router';
import { memo, useEffect, useState } from 'react';

import EmployeeInfo from '@tutum/admin/components/sidebar/employee-info/EmployeeInfo.styled';
import GlobalContext from '@tutum/admin/contexts/Global.context';
import { FFWrapper } from '@tutum/admin/hooks/useFeatureFlag';
import { SVWrapper } from '@tutum/admin/hooks/useSVFeatureEnable';
import type SidebarI18n from '@tutum/admin/locales/en/Sidebar.json';
import {
  eABSettingAction,
  useEabSettingStore,
} from '@tutum/admin/module_edoctor_letter/EdoctorLetterSetting.store';
import type { IAdminTheme } from '@tutum/admin/theme';
import { NAV_ITEMS, ROUTING } from '@tutum/admin/types/route.type';
import {
  Avatar,
  BodyTextM,
  BodyTextS,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import {
  Alignment,
  Button,
  Classes,
  Popover,
  PopoverInteractionKind,
  Position,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { useQueryGetAllTrustedUserDeviceList } from '@tutum/hermes/bff/legacy/app_admin';
import { useQueryGetSettings } from '@tutum/hermes/bff/legacy/app_admin_settings';
import { useQueryGetSecurityLink } from '@tutum/hermes/bff/legacy/app_auth';
import { useQueryGetSetting } from '@tutum/hermes/bff/legacy/app_eab';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';
import {
  SettingsFeatures,
  VOSKeys,
} from '@tutum/hermes/bff/legacy/settings_common';
import { BuildVersion, getAppVersion } from '@tutum/infrastructure/app-version';
import I18n from '@tutum/infrastructure/i18n';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import ButtonWithTooltip from './ButtonWithTooltip';
import NavigationItem from './NavigationItem';
import NavOpenButton from './NavOpenButton';

export interface ISidebarProps {
  isShrinkMenu: boolean;
  toggleShrinkMenu: () => void;
  className?: string;
  theme?: IAdminTheme;
}

const checkActive = (subPaths: string[]) => {
  return subPaths.some((element) => location.pathname.includes(element));
};

const Users = '/images/users.svg';
const Devices = '/images/devices.svg';
const Settings = '/images/settings.svg';
const AppConfigs = '/images/configs.svg';
const OpenConfig = '/images/chevron-down.svg';
const CloseConfig = '/images/chevron-up.svg';
const Building = '/images/building.svg';
const Billing = '/images/billing-overview.svg';
const Clipboard = '/images/clipboard.svg';
const FileDocument = '/images/file-document.svg';
const DevicesGrey = '/images/devices-grey.svg';
const FileDocumentGrey = '/images/file-document-grey.svg';
const SettingsGrey = '/images/settings-grey.svg';
const UsersGrey = '/images/users-grey.svg';
const RepairGrey = '/images/repair-grey.svg';
const BuildingGrey = '/images/building-grey.svg';
const BillingGrey = '/images/billing-overview-grey.svg';
const ClipboardGrey = '/images/clipboard-grey.svg';
const IconAlertCicleSolid = '/images/alert-circle-solid-normal.svg';
const CollapseIcon = '/images/menu-collapse-icon.svg';
const IconMenuExpand = '/images/menu-expand-icon.svg';

const genDataTestIdFromRoutingName = (routingName: string): string => {
  const routingSplit = routingName.split('/');
  //return when have dynamic route
  if (routingName.includes('[')) return '';
  // return when have nest route
  if (routingSplit.length > 2) {
    return routingSplit.splice(1).join('-');
  }
  return routingSplit[1];
};

const Sidebar = ({
  toggleShrinkMenu,
  isShrinkMenu,
  className,
}: ISidebarProps) => {
  const { t } = I18n.useTranslation<keyof typeof SidebarI18n>({
    namespace: 'Sidebar',
  });
  const router = useRouter();
  const { saveEdoctorSetting } = useEabSettingStore();
  const { userProfile } = GlobalContext.useContext();
  const [isOpenPracticeSettings, setOpenPracticeSettings] =
    useState<boolean>(false);
  const [isOpenDocumentation, setOpenDocumentation] = useState<boolean>(false);
  const [isOpenDocuments, setOpenDocuments] = useState<boolean>(false);
  const [isOpenBilling, setOpenBilling] = useState<boolean>(false);
  const [isOpenSystem, setOpenSystem] = useState<boolean>(false);
  const [appVersion, setAppVersion] = useState<BuildVersion | undefined>();

  const { data: vosSetting, isSuccess: isSuccessGetVosSetting } =
    useQueryGetSettings({
      feature: SettingsFeatures.SettingsFeatures_Enable_VOS,
      settings: [VOSKeys.VOSKeys_Enable],
    });

  const { data: eDoctorSettings, isSuccess: isSuccessGetSettingEAB } =
    useQueryGetSetting();

  useEffect(() => {
    setOpenDocumentation(checkActive(NAV_ITEMS.DOCUMENTATION));
    setOpenSystem(checkActive(NAV_ITEMS.SYSTEM));
    setOpenPracticeSettings(checkActive(NAV_ITEMS.GENERAL_SETUP));
    setOpenBilling(checkActive(NAV_ITEMS.BILLING));
    setOpenDocuments(checkActive(NAV_ITEMS.DOCUMENTS));
    getAppVersion().then((version) => {
      setAppVersion(version);
    });
  }, []);

  useEffect(() => {
    if (isSuccessGetSettingEAB) {
      eABSettingAction.setEabSetting(eDoctorSettings);
    }
  }, [isSuccessGetSettingEAB]);

  const navRailIcon = isShrinkMenu ? IconMenuExpand : CollapseIcon;
  const navRailTooltip = isShrinkMenu ? t('expandMenu') : t('collapseMenu');

  const toggleNavItem = (item: string) => {
    const funcMap = {
      practiceSettings: () =>
        setOpenPracticeSettings((previousState) => !previousState),
      documentation: () =>
        setOpenDocumentation((previousState) => !previousState),
      documents: () => setOpenDocuments((previousState) => !previousState),
      billing: () => setOpenBilling((previousState) => !previousState),
      system: () => setOpenSystem((previousState) => !previousState),
    };
    return funcMap[item]();
  };

  const getMinimal = (type: string) => {
    return !location.pathname.includes(type);
  };

  const handleNavigation = (route: string) => router.push(route);

  const getNavItemCssClass = (paths: string[]) => {
    return getCssClass({
      'no-highlight': true,
      'is-opening': checkActive(paths),
      active: checkActive(paths),
    });
  };

  const getSubItemCss = (path: string) => {
    return getCssClass(
      Classes.POPOVER_DISMISS,
      'sub-setting',
      checkActive([path]) ? 'active' : '',
      isShrinkMenu ? 'shrinkMenu' : ''
    );
  };

  const { data: deviceList = [] } = useQueryGetAllTrustedUserDeviceList({
    select: (data) => data.data.devices,
  });
  const currentDeviceName = deviceList.find(
    (device) => device.id === userProfile?.deviceId
  )?.deviceName;

  const { data: securityLink = '' } = useQueryGetSecurityLink({
    select: (res) => res.data.url,
  });

  return (
    <div className={className}>
      <Flex column>
        <Tooltip content={navRailTooltip} position="right">
          <Svg
            src={navRailIcon}
            className="collapse-icon"
            onClick={toggleShrinkMenu}
          />
        </Tooltip>
        <div className="avatar-content__wrapper">
          <Popover
            interactionKind={PopoverInteractionKind.HOVER}
            content={
              <EmployeeInfo
                userProfile={userProfile!}
                key="employeeProfile"
                appVersion={appVersion}
                deviceList={deviceList}
                securityLink={securityLink}
              />
            }
            position={Position.RIGHT}
            fill
          >
            <Flex
              mb={8}
              mr={8}
              px={8}
              py={8}
              gap={8}
              className={getCssClass({ 'avatar-content__main': !isShrinkMenu })}
            >
              <Avatar
                className="avatar"
                initial={userProfile?.initial || ''}
                size="medium"
              />
              {!isShrinkMenu && (
                <Flex column w="100%">
                  <Flex
                    className="employee-name"
                    justify="space-between"
                    color="white"
                  >
                    <BodyTextM
                      className="sl-text-name"
                      limitLines={1}
                      color="white"
                      fontWeight={600}
                    >
                      {nameUtils.getDoctorName(userProfile)}
                    </BodyTextM>
                  </Flex>
                  <Flex className="practice-name">
                    <BodyTextS limitLines={1} color="white">
                      {currentDeviceName}
                    </BodyTextS>
                  </Flex>
                </Flex>
              )}
            </Flex>
          </Popover>
        </div>
        <div className="sidebar-menu">
          <ButtonWithTooltip tooltipContent={t('deviceManagement')}>
            <NavigationItem
              t={t}
              path={ROUTING.CAREPRPOVIDER_TRUSTED_DEVICES}
              routeGroup={NAV_ITEMS.DEVICE_MANAGEMENT}
              text={!isShrinkMenu ? t('deviceManagement') : ''}
              activeIcon={Devices}
              inactiveIcon={DevicesGrey}
              handleNavigation={handleNavigation}
              checkActive={checkActive}
              dataTestId={genDataTestIdFromRoutingName(
                ROUTING.CAREPRPOVIDER_TRUSTED_DEVICES
              )}
              getMinimal={getMinimal}
            />
          </ButtonWithTooltip>
          <ButtonWithTooltip tooltipContent={t('bsnrOverview')}>
            <NavigationItem
              t={t}
              routeGroup={NAV_ITEMS.BSNR_OVERVIEW}
              path={ROUTING.PRACTICE_SETTINGS_BSNR_OVERVIEW}
              text={!isShrinkMenu ? t('bsnrOverview') : ''}
              activeIcon={Building}
              inactiveIcon={BuildingGrey}
              handleNavigation={handleNavigation}
              checkActive={checkActive}
              dataTestId={genDataTestIdFromRoutingName(
                ROUTING.PRACTICE_SETTINGS_BSNR_OVERVIEW
              )}
              getMinimal={getMinimal}
            />
          </ButtonWithTooltip>
          <ButtonWithTooltip tooltipContent={t('accountManagement')}>
            <NavigationItem
              t={t}
              path={ROUTING.EMPLOYEES}
              routeGroup={NAV_ITEMS.ACCOUNT_MANAGEMENT}
              text={!isShrinkMenu ? t('accountManagement') : ''}
              dataTestId={genDataTestIdFromRoutingName(ROUTING.EMPLOYEES)}
              activeIcon={Users}
              inactiveIcon={UsersGrey}
              handleNavigation={handleNavigation}
              checkActive={checkActive}
              getMinimal={getMinimal}
            />
          </ButtonWithTooltip>
          <NavOpenButton
            isShrinkMenu={isShrinkMenu}
            t={t}
            text="practiceSettings"
            openConfig={OpenConfig}
            activeIcon={Settings}
            closeConfig={CloseConfig}
            inactiveIcon={SettingsGrey}
            isOpen={isOpenPracticeSettings}
            routing={NAV_ITEMS.GENERAL_SETUP}
            dataTestId="practice-settings"
            toggleNavItem={toggleNavItem}
            getMinimal={getMinimal}
            getNavItemCssClass={getNavItemCssClass}
          >
            {(isOpenPracticeSettings || isShrinkMenu) && (
              <Flex column>
                <Button
                  className={getSubItemCss(ROUTING.TI_CONNECTOR)}
                  alignText={Alignment.LEFT}
                  text={t('connector')}
                  onClick={() => handleNavigation(ROUTING.TI_CONNECTOR)}
                  minimal={getMinimal('connector')}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.TI_CONNECTOR
                  )}
                />
                <Button
                  className={getSubItemCss(
                    ROUTING.APP_CONFIGURATIONS_CARD_READER
                  )}
                  alignText={Alignment.LEFT}
                  text={t('cardReader')}
                  onClick={() =>
                    handleNavigation(ROUTING.APP_CONFIGURATIONS_CARD_READER)
                  }
                  minimal={getMinimal(ROUTING.APP_CONFIGURATIONS_CARD_READER)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.APP_CONFIGURATIONS_CARD_READER
                  )}
                />
                <Button
                  className={getSubItemCss(
                    ROUTING.APP_CONFIGURATIONS_MOBILE_CARD_READER
                  )}
                  alignText={Alignment.LEFT}
                  text={t('mobileCardReader')}
                  onClick={() =>
                    handleNavigation(
                      ROUTING.APP_CONFIGURATIONS_MOBILE_CARD_READER
                    )
                  }
                  minimal={getMinimal(
                    ROUTING.APP_CONFIGURATIONS_MOBILE_CARD_READER
                  )}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.APP_CONFIGURATIONS_MOBILE_CARD_READER
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.KIM_ACCOUNTS)}
                  alignText={Alignment.LEFT}
                  text={t('kimAccounts')}
                  onClick={() => handleNavigation(ROUTING.KIM_ACCOUNTS)}
                  minimal={getMinimal(ROUTING.KIM_ACCOUNTS)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.KIM_ACCOUNTS
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.KV_CONNECT_ACCOUNTS)}
                  alignText={Alignment.LEFT}
                  text={t('kvConnectAccounts')}
                  onClick={() => handleNavigation(ROUTING.KV_CONNECT_ACCOUNTS)}
                  minimal={getMinimal(ROUTING.KV_CONNECT_ACCOUNTS)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.KIM_ACCOUNTS
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.PRINTER_PROFILE)}
                  alignText={Alignment.LEFT}
                  text={t('printerProfile')}
                  onClick={() => handleNavigation(ROUTING.PRINTER_PROFILE)}
                  minimal={getMinimal(ROUTING.PRINTER_PROFILE)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.PRINTER_PROFILE
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.WAITING_ROOM)}
                  alignText={Alignment.LEFT}
                  text={t('waitingRoom')}
                  onClick={() => handleNavigation(ROUTING.WAITING_ROOM)}
                  minimal={getMinimal(ROUTING.WAITING_ROOM)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.WAITING_ROOM
                  )}
                />
                <SVWrapper>
                  <Button
                    className={getSubItemCss(ROUTING.SELECTIVE_CONTRACTS)}
                    alignText={Alignment.LEFT}
                    text={t('selectiveContrats')}
                    onClick={() =>
                      handleNavigation(ROUTING.SELECTIVE_CONTRACTS)
                    }
                    minimal={getMinimal(ROUTING.SELECTIVE_CONTRACTS)}
                    data-test-id={genDataTestIdFromRoutingName(
                      ROUTING.SELECTIVE_CONTRACTS
                    )}
                  />
                </SVWrapper>
                <Button
                  className={getSubItemCss(ROUTING.INTERFACE)}
                  alignText={Alignment.LEFT}
                  text={t('interface')}
                  onClick={() => handleNavigation(ROUTING.INTERFACE)}
                  minimal={getMinimal(ROUTING.INTERFACE)}
                  data-test-id={genDataTestIdFromRoutingName(ROUTING.INTERFACE)}
                />
                {isSuccessGetVosSetting &&
                  vosSetting.settings &&
                  vosSetting.settings[VOSKeys.VOSKeys_Enable] && (
                    <Button
                      className={getSubItemCss(ROUTING.VOS_CONFIGURATION)}
                      alignText={Alignment.LEFT}
                      text={t('vosConfiguration')}
                      onClick={() =>
                        handleNavigation(ROUTING.VOS_CONFIGURATION)
                      }
                      minimal={getMinimal(ROUTING.VOS_CONFIGURATION)}
                      data-test-id={genDataTestIdFromRoutingName(
                        ROUTING.VOS_CONFIGURATION
                      )}
                    />
                  )}
              </Flex>
            )}
          </NavOpenButton>

          <NavOpenButton
            isShrinkMenu={isShrinkMenu}
            t={t}
            text="documentation"
            openConfig={OpenConfig}
            closeConfig={CloseConfig}
            activeIcon={Clipboard}
            inactiveIcon={ClipboardGrey}
            dataTestId="documentation"
            isOpen={isOpenDocumentation}
            routing={NAV_ITEMS.DOCUMENTATION}
            toggleNavItem={toggleNavItem}
            getMinimal={getMinimal}
            getNavItemCssClass={getNavItemCssClass}
          >
            {(isOpenDocumentation || isShrinkMenu) && (
              <Flex column>
                <Button
                  className={getSubItemCss(ROUTING.TEXTMODULE)}
                  alignText={Alignment.LEFT}
                  text={t('textModuleOverview')}
                  onClick={() => handleNavigation(ROUTING.TEXTMODULE)}
                  minimal={getMinimal(ROUTING.TEXTMODULE)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.TEXTMODULE
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.ACTION_CHAIN)}
                  alignText={Alignment.LEFT}
                  text={t('actionChain')}
                  onClick={() => handleNavigation(ROUTING.ACTION_CHAIN)}
                  minimal={getMinimal(ROUTING.ACTION_CHAIN)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.ACTION_CHAIN
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.TIMELINE_ENTRY_COLORS)}
                  alignText={Alignment.LEFT}
                  text={t('timelineEntryColors')}
                  onClick={() =>
                    handleNavigation(ROUTING.TIMELINE_ENTRY_COLORS)
                  }
                  minimal={getMinimal(ROUTING.TIMELINE_ENTRY_COLORS)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.TIMELINE_ENTRY_COLORS
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.DELETION_SETTINGS)}
                  alignText={Alignment.LEFT}
                  text={t('deletionSettings')}
                  onClick={() => handleNavigation(ROUTING.DELETION_SETTINGS)}
                  minimal={getMinimal(ROUTING.DELETION_SETTINGS)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.DELETION_SETTINGS
                  )}
                />
                {/* <Button
                  className={getSubItemCss(ROUTING.REPORT_QUERIES)}
                  alignText={Alignment.LEFT}
                  text={t('reportQueries')}
                  onClick={() => handleNavigation(ROUTING.REPORT_QUERIES)}
                  minimal={getMinimal(ROUTING.REPORT_QUERIES)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.REPORT_QUERIES
                  )}
                /> */}
              </Flex>
            )}
          </NavOpenButton>

          <NavOpenButton
            isShrinkMenu={isShrinkMenu}
            t={t}
            text="documents"
            openConfig={OpenConfig}
            closeConfig={CloseConfig}
            activeIcon={FileDocument}
            dataTestId="documents"
            inactiveIcon={FileDocumentGrey}
            isOpen={isOpenDocuments}
            routing={NAV_ITEMS.DOCUMENTS}
            toggleNavItem={toggleNavItem}
            getMinimal={getMinimal}
            getNavItemCssClass={getNavItemCssClass}
          >
            {(isOpenDocuments || isShrinkMenu) && (
              <Flex column>
                <Button
                  className={getSubItemCss(ROUTING.TEMPLATE_OVERVIEW)}
                  alignText={Alignment.LEFT}
                  text={t('template')}
                  onClick={() => handleNavigation(ROUTING.TEMPLATE_OVERVIEW)}
                  minimal={getMinimal(ROUTING.TEMPLATE_OVERVIEW)}
                />
              </Flex>
            )}
          </NavOpenButton>

          <NavOpenButton
            isShrinkMenu={isShrinkMenu}
            t={t}
            text="billing"
            openConfig={OpenConfig}
            closeConfig={CloseConfig}
            activeIcon={Billing}
            inactiveIcon={BillingGrey}
            dataTestId="billing"
            isOpen={isOpenBilling}
            routing={NAV_ITEMS.BILLING}
            toggleNavItem={toggleNavItem}
            getMinimal={getMinimal}
            getNavItemCssClass={getNavItemCssClass}
          >
            {(isOpenBilling || isShrinkMenu) && (
              <Flex column>
                <FFWrapper
                  ffKey={FeatureFlagKey.FeatureFlagKey_PVS_BILLING}
                  initialState={false}
                >
                  <Button
                    className={getSubItemCss(ROUTING.PVS_BILLING)}
                    alignText={Alignment.LEFT}
                    text={t('pvsBilling')}
                    onClick={() => handleNavigation(ROUTING.PVS_BILLING)}
                    minimal={getMinimal(ROUTING.PVS_BILLING)}
                    data-test-id={genDataTestIdFromRoutingName(
                      ROUTING.PVS_BILLING
                    )}
                  />{' '}
                </FFWrapper>
                <Button
                  className={getSubItemCss(ROUTING.PRIVATE_BILLING)}
                  alignText={Alignment.LEFT}
                  text={t('privateBilling')}
                  onClick={() => handleNavigation(ROUTING.PRIVATE_BILLING)}
                  minimal={getMinimal(ROUTING.PRIVATE_BILLING)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.PRIVATE_BILLING
                  )}
                />
                <Button
                  className={getSubItemCss(ROUTING.E_DOCTOR_LETTER)}
                  alignText={Alignment.LEFT}
                  text={t('eDoctorLetter')}
                  onClick={() => handleNavigation(ROUTING.E_DOCTOR_LETTER)}
                  minimal={getMinimal(ROUTING.E_DOCTOR_LETTER)}
                  rightIcon={
                    saveEdoctorSetting?.isQuarterReset && (
                      <Svg src={IconAlertCicleSolid} />
                    )
                  }
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.E_DOCTOR_LETTER
                  )}
                />
                <Button
                  className={getSubItemCss(
                    ROUTING.PRACTICE_SETTINGS_CODING_RULES
                  )}
                  alignText={Alignment.LEFT}
                  text={t('codingRules')}
                  onClick={() =>
                    handleNavigation(ROUTING.PRACTICE_SETTINGS_CODING_RULES)
                  }
                  minimal={getMinimal(ROUTING.PRACTICE_SETTINGS_CODING_RULES)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.PRACTICE_SETTINGS_CODING_RULES
                  )}
                />
              </Flex>
            )}
          </NavOpenButton>
          <NavOpenButton
            isShrinkMenu={isShrinkMenu}
            t={t}
            text="system"
            openConfig={OpenConfig}
            closeConfig={CloseConfig}
            activeIcon={AppConfigs}
            dataTestId="system"
            inactiveIcon={RepairGrey}
            isOpen={isOpenSystem}
            routing={NAV_ITEMS.SYSTEM}
            toggleNavItem={toggleNavItem}
            getMinimal={getMinimal}
            getNavItemCssClass={getNavItemCssClass}
          >
            {(isOpenSystem || isShrinkMenu) && (
              <Flex column>
                <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_SystemDate}>
                  <Button
                    className={getSubItemCss(ROUTING.SYSTEM_DATE)}
                    alignText={Alignment.LEFT}
                    text={t('systemDate')}
                    onClick={() => handleNavigation(ROUTING.SYSTEM_DATE)}
                    minimal={getMinimal(ROUTING.SYSTEM_DATE)}
                    data-test-id={genDataTestIdFromRoutingName(
                      ROUTING.SYSTEM_DATE
                    )}
                  />
                </FFWrapper>
                <Button
                  className={getSubItemCss(ROUTING.LOCK_APP_SETTING)}
                  alignText={Alignment.LEFT}
                  text={t('lockAppSetting')}
                  onClick={() => handleNavigation(ROUTING.LOCK_APP_SETTING)}
                  minimal={getMinimal(ROUTING.LOCK_APP_SETTING)}
                  data-test-id={genDataTestIdFromRoutingName(
                    ROUTING.LOCK_APP_SETTING
                  )}
                />
                {/* <Button
                className={getSubItemCss(ROUTING.XPM_VERSION)}
                alignText={Alignment.LEFT}
                text={t('xpmVersion')}
                onClick={() => handleNavigation(ROUTING.XPM_VERSION)}
                minimal={getMinimal(ROUTING.XPM_VERSION)}
              /> */}
              </Flex>
            )}
          </NavOpenButton>
        </div>
      </Flex>
    </div>
  );
};

export default memo(Sidebar);
