import { Svg } from '@tutum/design-system/components';
import Button from '@tutum/design-system/components/Button/Button';
import { Alignment } from '@tutum/design-system/components/Core';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { PropsWithChildren } from 'react';
import { styled } from '@tutum/design-system/themes';

const ButtonItem = styled<any>(Button)`
  .active-svg {
    display: none;
  }
  .inactive-svg {
    display: block;
  }

  &:hover,
  &.active {
    .active-svg {
      display: block;
    }
    .inactive-svg {
      display: none;
    }
  }
`;

type NavigationItemProps = {
  t: any;
  path: string;
  text: string;
  activeIcon: string;
  inactiveIcon: string;
  routeGroup: string[];
  handleNavigation: (routing: string) => void;
  dataTestId?: string;
  checkActive: (type: string[]) => boolean;
  getMinimal?: (routing: string) => boolean;
};
const NavigationItem = (props: PropsWithChildren<NavigationItemProps>) => {
  const {
    t,
    path,
    text,
    activeIcon,
    dataTestId,
    inactiveIcon,
    handleNavigation,
    checkActive,
    getMinimal,
    routeGroup,
  } = props;

  const icon = (
    <div style={{ margin: '4px 0 0 0' }}>
      <div className="active-svg">
        <Svg src={activeIcon} />
      </div>
      <div className="inactive-svg">
        <Svg src={inactiveIcon} />
      </div>
    </div>
  );

  return (
    <ButtonItem
      icon={icon}
      className={getCssClass({
        active: checkActive(routeGroup),
      })}
      large
      data-test-id={dataTestId}
      alignText={Alignment.LEFT}
      text={text}
      onClick={() => handleNavigation(path)}
      minimal={getMinimal?.(path)}
    />
  );
};

export default NavigationItem;
