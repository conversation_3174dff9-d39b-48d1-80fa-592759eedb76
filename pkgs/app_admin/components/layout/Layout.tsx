import React, { useContext, useEffect, useState } from 'react';
import { Flex } from '@tutum/design-system/components';
import { IAdminTheme } from '@tutum/admin/theme';
import Sidebar from '@tutum/admin/components/sidebar';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';

import GlobalContext from '@tutum/admin/contexts/Global.context';
import {
  COMMON_SOCKET_OPEN_EVENT,
  startBFFCommonSocket,
  stopBFFCommonSocket,
} from '@tutum/infrastructure/websocket';

export interface ILayoutProps {
  className?: string;
  theme?: IAdminTheme;
  isAuthenticated: boolean;
}

const Layout = ({
  className,
  children,
}: React.PropsWithChildren<ILayoutProps>) => {
  const [isShrinkMenu, setIsShrinkMenu] = React.useState<boolean>(true);
  const context = useContext(GlobalContext.instance);
  const userProfile = context?.userProfile;
  const [isSocketOpened, setIsSocketOpened] = useState(false);

  const toggleShrinkMenu = () => {
    setIsShrinkMenu((prev) => !prev);
  };

  const _init = () => {
    if (!userProfile) {
      return;
    }

    startBFFCommonSocket();
    global.addEventListener(COMMON_SOCKET_OPEN_EVENT, _socketOpenEventListener);
  };

  const _destroy = () => {
    stopBFFCommonSocket();
    global.removeEventListener(
      COMMON_SOCKET_OPEN_EVENT,
      _socketOpenEventListener
    );
  };

  const _socketOpenEventListener = () => {
    setIsSocketOpened(true);
  };

  useEffect(() => {
    if (userProfile?.id) {
      _init();
    }

    return () => {
      _destroy();
    };
  }, [userProfile?.id]);

  if (!isSocketOpened && userProfile?.id) {
    return null;
  }

  return (
    <Flex className={className}>
      <div
        className={getCssClass('sl-sidebar', { 'sticky-menu': isShrinkMenu })}
      >
        <Sidebar
          isShrinkMenu={isShrinkMenu}
          toggleShrinkMenu={toggleShrinkMenu}
        />
      </div>
      <Flex auto className="sl-content">
        {children}
      </Flex>
    </Flex>
  );
};

export default Layout;
