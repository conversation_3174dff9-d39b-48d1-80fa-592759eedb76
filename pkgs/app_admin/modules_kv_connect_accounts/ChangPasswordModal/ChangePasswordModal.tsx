import React, { memo, useRef, useState } from 'react';
import { Field, Form, Formik, FormikProps } from 'formik';

import type KVConnectAccountsI18n from '@tutum/admin/locales/en/KVConnectAccounts.json';
import type CommonI18n from '@tutum/admin/locales/en/Common.json';
import { useMutationChangePassword } from '@tutum/hermes/bff/legacy/app_kv_connect';
import { KVConnectAccount } from '@tutum/hermes/bff/legacy/kv_connect_common';
import i18n from '@tutum/infrastructure/i18n';
import {
  Button,
  Flex,
  Modal,
  alertError,
  alertSuccessfully,
  FormGroup2,
  Svg,
} from '@tutum/design-system/components';
import {
  Classes,
  Divider,
  InputGroup,
} from '@tutum/design-system/components/Core';
import {
  onValidateAccount,
  FieldsName,
  AccountValue,
} from '@tutum/admin/modules_kv_connect_accounts/kvConnectAccount.helper';

const eyeOff = '/images/eye-off.svg';
const eyeOn = '/images/eye-on.svg';

export interface IChangePassWordModal {
  className?: string;
  onClose: () => void;
  kvConnectAccount: KVConnectAccount;
  accountValue: AccountValue;
}

const ChangPassWordModal = (props: IChangePassWordModal) => {
  const { className, onClose, kvConnectAccount, accountValue } = props;
  const formikRef = useRef<FormikProps<AccountValue>>(null);
  const [showPass, setShowPass] = useState<boolean>(false);
  const [showPassConfirm, setShowConfirmPass] = useState<boolean>(false);
  const [showPassNew, setShowNewPass] = useState<boolean>(false);

  const { t: tFormValidation } = i18n.useTranslation<
    keyof typeof CommonI18n.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonI18n.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t } = i18n.useTranslation<keyof typeof KVConnectAccountsI18n>({
    namespace: 'KVConnectAccounts',
  });

  const { mutate } = useMutationChangePassword({
    onError: (error) => {
      alertError(error.message);
      formikRef.current?.setSubmitting(false);
    },
    onSuccess: () => {
      alertSuccessfully(t('changeSuccess'));
      onClose();
      formikRef.current?.resetForm();
    },
    throwOnError: false,
  });
  const onSubmit = (data: AccountValue) => {
    const payload = {
      id: kvConnectAccount.id,
      username: kvConnectAccount.username,
      newPassword: data.newPassWord,
      currentPassword: data.password,
    };
    mutate(payload);
  };

  const EyeOffIcon = (
    <Svg
      src={eyeOff}
      onClick={() => {
        setShowPass(true);
      }}
    />
  );
  const EyeOnIcon = (
    <Svg
      src={eyeOn}
      onClick={() => {
        setShowPass(false);
      }}
    />
  );
  const EyeOffIconConfirm = (
    <Svg
      src={eyeOff}
      onClick={() => {
        setShowConfirmPass(true);
      }}
    />
  );
  const EyeOnIconConfirm = (
    <Svg
      src={eyeOn}
      onClick={() => {
        setShowConfirmPass(false);
      }}
    />
  );
  const EyeOffIconNew = (
    <Svg
      src={eyeOff}
      onClick={() => {
        setShowNewPass(true);
      }}
    />
  );
  const EyeOnIconNew = (
    <Svg
      src={eyeOn}
      onClick={() => {
        setShowNewPass(false);
      }}
    />
  );

  return (
    <Formik<AccountValue>
      initialValues={accountValue}
      onSubmit={onSubmit}
      enableReinitialize
      validate={onValidateAccount(tFormValidation, t)}
      innerRef={formikRef}
      isInitialValid={false}
    >
      {(formikProps) => {
        const { errors, submitCount, touched, isSubmitting, dirty } =
          formikProps;
        const formGroupProps = {
          isRequired: true,
          submitCount,
          errors,
          touched,
        };
        return (
          <Modal
            className={className}
            title={t('title')}
            isOpen
            onClose={onClose}
            usePortal
            shouldReturnFocusOnClose
            canEscapeKeyClose={true}
          >
            <Divider />
            <Form>
              <Flex column className="sl-form-container">
                <Field name={FieldsName.Password}>
                  {({ field }) => {
                    return (
                      <FormGroup2
                        label={t('currentPassword')}
                        {...field}
                        {...formGroupProps}
                      >
                        <InputGroup
                          {...field}
                          type={showPass ? 'text' : 'password'}
                          rightElement={showPass ? EyeOnIcon : EyeOffIcon}
                        />
                      </FormGroup2>
                    );
                  }}
                </Field>
                <Field name={FieldsName.NewPassWord}>
                  {({ field }) => {
                    return (
                      <FormGroup2
                        label={t('newPassword')}
                        {...field}
                        {...formGroupProps}
                      >
                        <InputGroup
                          {...field}
                          type={showPassNew ? 'text' : 'password'}
                          rightElement={
                            showPassNew ? EyeOnIconNew : EyeOffIconNew
                          }
                        />
                      </FormGroup2>
                    );
                  }}
                </Field>
                <Field name={FieldsName.ConfirmPassWord}>
                  {({ field }) => {
                    return (
                      <FormGroup2
                        label={t('confirmPassword')}
                        {...field}
                        {...formGroupProps}
                      >
                        <InputGroup
                          {...field}
                          type={showPassConfirm ? 'text' : 'password'}
                          rightElement={
                            showPassConfirm
                              ? EyeOnIconConfirm
                              : EyeOffIconConfirm
                          }
                        />
                      </FormGroup2>
                    );
                  }}
                </Field>
              </Flex>
              <Divider />
              <Flex className={Classes.DIALOG_FOOTER_ACTIONS} align="flex-end">
                <Flex>
                  <Button
                    large
                    intent="primary"
                    outlined
                    disabled={isSubmitting}
                    onClick={onClose}
                  >
                    {tButtonActions('cancelText')}
                  </Button>
                  <Button
                    large
                    type="submit"
                    intent="primary"
                    loading={isSubmitting}
                    disabled={!dirty}
                  >
                    {tButtonActions('saveText')}
                  </Button>
                </Flex>
              </Flex>
            </Form>
          </Modal>
        );
      }}
    </Formik>
  );
};
export default memo(ChangPassWordModal);
