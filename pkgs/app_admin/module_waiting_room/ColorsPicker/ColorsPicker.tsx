import React, { useEffect, useState, useCallback } from 'react';

import { COLOR_LIST } from '@tutum/design-system/themes/styles/color/colorList';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { Popover } from '@tutum/design-system/components/Core';
import { ColorsPickerMenu } from './ColorsPicker.styled';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface ColorsPickerProps {
  className?: string;
  defaultColor: string;
  colorsOptions?: string[];
  onChange: (selectedColor: string) => void;
}

const DEFAULT_COLORS_OPTIONS: string[] = COLOR_LIST.reduce<string[]>((newList, color) => {
  if (color.textColor === COLOR.TEXT_WHITE) {
    newList.push(color.bgColor);
  }

  return newList;
}, []);

const ColorsPicker = ({
  className,
  defaultColor,
  colorsOptions = DEFAULT_COLORS_OPTIONS,
  onChange,
}: ColorsPickerProps) => {
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [isOpen, setOpen] = useState<boolean>(false);

  const toggleClick = useCallback(() => {
    setOpen(!isOpen);
  }, []);

  const onChangeColor = useCallback((color: string) => {
    onChange(color);
    setSelectedColor(color);
    setOpen(false);
  }, []);

  useEffect(() => {
    if (defaultColor) {
      setSelectedColor(defaultColor);
    } else {
      const randomIndex = Math.floor(Math.random() * colorsOptions.length);
      onChangeColor(colorsOptions[randomIndex]);
    }
  }, [defaultColor]);

  return (
    <Popover
      isOpen={isOpen}
      minimal
      className={className}
      transitionDuration={0}
      placement="bottom-start"
      content={
        <ColorsPickerMenu className="sl-colors-picker__color-list">
          {colorsOptions.map((color, index) => (
            <div
              key={index}
              className={getCssClass(
                'sl-colors-picker__color-item',
                {
                  '--selected': selectedColor === color,
                },
              )}
              onClick={() => onChangeColor(color)}
            >
              <div
                className="sl-colors-picker__color-item__content"
                style={{ backgroundColor: color }}
              />
            </div>
          ))}
        </ColorsPickerMenu>
      }
      onClose={() => setOpen(false)}
    >
      <div
        className="sl-colors-picker__content"
        style={{ backgroundColor: selectedColor }}
        onClick={toggleClick}
      />
    </Popover>
  );
};

export default ColorsPicker;
