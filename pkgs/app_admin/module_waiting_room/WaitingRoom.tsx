import React, { useState, useEffect } from 'react';

import type CommonLocales from '@tutum/admin/locales/en/Common.json';
import type WaitingRoomI18n from '@tutum/admin/locales/en/WaitingRoom.json';

import {
  getWaitingRooms,
  remove,
  toggleMeasurement,
} from '@tutum/hermes/bff/legacy/app_admin_waiting_room';
import CreateWaitingRoom from './CreateWaitingRoom/CreateWaitingRoom.styled';
import Table from '@tutum/design-system/components/Table';
import I18n from '@tutum/infrastructure/i18n';
import {
  BodyTextL,
  Button,
  Flex,
  H1,
  Svg,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { Divider } from '@tutum/design-system/components/Core';
import { genColumns } from './WaitingRoom.utils';
import { WaitingRoomView } from '@tutum/hermes/bff/legacy/waiting_room_common';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { TEXT } from '@tutum/design-system/themes/styles/color/text';
import { ErrorCode } from '@tutum/hermes/bff/error_code';

export interface IWaitingRoomProps {
  className?: string;
}
const Plus = '/images/plus-white.svg';
const WaitingRoom = (props: IWaitingRoomProps) => {
  const tTable = I18n.useTranslation<
    keyof typeof WaitingRoomI18n.WaitingRoomTable
  >({
    namespace: 'WaitingRoom',
    nestedTrans: 'WaitingRoomTable',
  }).t;
  const { t } = I18n.useTranslation<keyof typeof WaitingRoomI18n>({
    namespace: 'WaitingRoom',
  });
  const { t: tRemoveRoomModal } = I18n.useTranslation<
    keyof typeof WaitingRoomI18n.RemoveRoomModal
  >({
    namespace: 'WaitingRoom',
    nestedTrans: 'RemoveRoomModal',
  });
  const { t: tRemovePatientFailed } = I18n.useTranslation<
    keyof typeof WaitingRoomI18n.RemovePatientFailed
  >({
    namespace: 'WaitingRoom',
    nestedTrans: 'RemovePatientFailed',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { className } = props;
  const [waitingRooms, setWaitingRooms] = useState<WaitingRoomView[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [roomId, setRoomId] = useState<string>('');
  const [deleteRoomId, setDeleteRoomId] = useState<string>('');
  const [isOpenConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [hasRemovedError, setRemovedError] = useState<boolean>(false);

  const getDatas = async () => {
    setIsLoading(true);
    try {
      const res = await getWaitingRooms({ roomId: undefined });
      setWaitingRooms(res.data.waitingRoomViews || []);
    } finally {
      setIsLoading(false);
    }
  };

  const onEdit = (row: WaitingRoomView) => {
    setRoomId(row.id || '');
  };

  const onOpenConfirmDelete = (row: WaitingRoomView) => {
    setDeleteRoomId(row.id || '');
    setOpenConfirmDelete(true);
  };

  const onCloseConfirmDelete = () => {
    setDeleteRoomId('');
    setOpenConfirmDelete(false);
    setRemovedError(false);
  };

  const onRemoveRoom = async () => {
    try {
      await remove({ roomId: deleteRoomId });

      alertSuccessfully(tRemoveRoomModal('successMessage'));
      getDatas();
      onCloseConfirmDelete();
    } catch (error) {
      console.error(error);

      if (
        error?.response?.data?.serverError ===
        ErrorCode.ErrorCode_Waiting_Room_Have_Patient
      ) {
        setRemovedError(true);
        return;
      }

      throw error;
    }
  };

  const toggleMeansure = async (rowId: string) => {
    await toggleMeasurement({ roomId: rowId });
    getDatas();
  };

  useEffect(() => {
    getDatas();
  }, []);

  return (
    <Flex className={className} column>
      <Flex p="16px">
        <H1>{t('waitingRoom')}</H1>
      </Flex>
      <Divider style={{ margin: 0 }} />
      <Flex p="16px" justify="flex-end">
        <Button
          className="sl-WaitingRoom__add-room"
          intent="primary"
          icon={<Svg src={Plus} />}
          onClick={() => {
            setRoomId('create');
          }}
        />
      </Flex>
      <Table
        className="sl-table"
        columns={genColumns({
          t: tTable,
          onEdit,
          onOpenConfirmDelete,
          toggleMeansure,
        })}
        data={waitingRooms}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        progressPending={isLoading}
        fixedHeader
        noDataComponent={
          <BodyTextL padding={24} color={TEXT.TEXT_PLACEHOLDER}>
            {tTable('noResultFound')}
          </BodyTextL>
        }
      />
      <CreateWaitingRoom
        roomId={roomId}
        onCloseModal={() => setRoomId('')}
        onReFetchData={getDatas}
      />
      <DeleteConfirmDialog
        className={getCssClass(
          hasRemovedError
            ? 'confirm-dialog--has-error'
            : 'confirm-dialog--hide-icon'
        )}
        isOpen={isOpenConfirmDelete}
        close={onCloseConfirmDelete}
        confirm={hasRemovedError ? onCloseConfirmDelete : onRemoveRoom}
        text={{
          btnCancel: (!hasRemovedError && tButtonActions('noText')) || '',
          btnOk: hasRemovedError
            ? tRemovePatientFailed('confirmText')
            : tButtonActions('yesRemove'),
          title: (hasRemovedError ? tRemovePatientFailed : tRemoveRoomModal)(
            'title'
          ),
          message: (hasRemovedError ? tRemovePatientFailed : tRemoveRoomModal)(
            'description'
          ),
        }}
      />
    </Flex>
  );
};

export default WaitingRoom;
