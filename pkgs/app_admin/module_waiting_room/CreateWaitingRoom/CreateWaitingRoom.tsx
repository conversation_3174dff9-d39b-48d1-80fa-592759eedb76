import { useState, ChangeEvent, useEffect, useMemo } from 'react';
import { Formik, FormikProps, Field, FormikHelpers } from 'formik';

import type CommonLocales from '@tutum/admin/locales/en/Common.json';

import {
  Button,
  Flex,
  ReactSelect,
  IMenuItem,
  Modal,
  ModalSize,
  FormGroup2,
} from '@tutum/design-system/components';
import { WaitingRoomModel } from '@tutum/hermes/bff/legacy/waiting_room_common';
import {
  InputGroup,
  Checkbox,
  Classes,
  NumericInput,
} from '@tutum/design-system/components/Core';
import {
  checkExistWaitingRoomName,
  createWaitingRoom,
  editWaitingRoom,
  getWaitingRooms,
} from '@tutum/hermes/bff/legacy/app_admin_waiting_room';
import { AdminApi } from '@tutum/infrastructure/resource/AdminResource';
import { EmployeeDetailResponse } from '@tutum/hermes/bff/legacy/app_admin';
import { getBSNR } from '@tutum/hermes/bff/legacy/app_bsnr';
import { BSNR, BSNRName } from '@tutum/hermes/bff/legacy/bsnr_common';
import I18n from '@tutum/infrastructure/i18n';
import PatientManagementUtil, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import ColorsPicker from '../ColorsPicker/ColorsPicker.styled';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { IMenuBSNRItem } from '@tutum/admin/types/bsnr.type';

export interface ICreateWaitingRoomProps {
  className?: string;
  roomId?: string;
  onCloseModal?: () => void;
  onReFetchData: () => void;
}

const initialValues = {
  id: undefined,
  bsnrId: undefined!,
  treatingDoctorId: undefined!,
  roomColor: undefined!,
  name: undefined!,
  acceptableWaitingTimeInMinutes: 15,
  activeTimeMeasurement: true,
  patientIds: [],
};

const Content = (
  props: FormikProps<WaitingRoomModel> & {
    onCloseDialog: () => void;
    employees: EmployeeDetailResponse[];
    bsnrs: IMenuBSNRItem[];
    isCreate: boolean;
    setEmployees: (data: EmployeeDetailResponse[]) => void;
  }
) => {
  const {
    submitCount,
    errors,
    touched,
    values,
    isSubmitting,
    setFieldValue,
    setValues,
    submitForm,
    onCloseDialog,
    employees,
    bsnrs,
    isCreate,
    setEmployees,
  } = props,
    { t } = I18n.useTranslation({
      namespace: 'WaitingRoom',
      nestedTrans: 'WaitingRoomModal',
    }),
    { t: tButtonActions } = I18n.useTranslation<
      keyof typeof CommonLocales.ButtonActions
    >({
      namespace: 'Common',
      nestedTrans: 'ButtonActions',
    });

  useEffect(() => {
    if (values.bsnrId) {
      AdminApi.getEmployeeProfilesByBsnrId({
        bsnrId: values.bsnrId,
      }).then((res) => {
        setEmployees(res?.data?.employees || []);
      });
    }
  }, [values.bsnrId]);
  const sortedEmployees = useMemo(() => {
    if (!values.bsnrId) {
      return [];
    }

    return employees
      .reduce<{
        label: string;
        value: string;
      }[]>((filteredList, employee) => {
        if (employee.bsnrIds?.includes(values.bsnrId)) {
          filteredList.push({
            label: nameUtils.getDoctorName(employee),
            value: employee.id || employee.employeeProfileId!,
          });
        }

        return filteredList;
      }, [])
      .sort((employeeA, employeeB) => {
        if (employeeA.label > employeeB.label) {
          return -1;
        }
        if (employeeA.label < employeeB.label) {
          return 1;
        }
        return 0;
      });
  }, [employees, values.bsnrId]);

  return (
    <>
      <Flex w="100%" gap={12} column p={16} className="body sl-form-content">
        <FormGroup2
          label={t('bsnr')}
          isRequired
          name={'bsnrId'}
          submitCount={submitCount}
          errors={errors}
          touched={touched}
        >
          <Field name="bsnrId">
            {({ field }) => (
              <ReactSelect
                id={field.name}
                instanceId={field.name}
                isSearchable={false}
                selectedValue={field?.value}
                items={bsnrs.map((e) => ({ label: e.label, value: e.id }))}
                onItemSelect={(item: IMenuItem) => {
                  if (item) {
                    setValues({
                      ...values,
                      [field.name]: item.value || '',
                      treatingDoctorId: '',
                    });
                  }
                }}
              />
            )}
          </Field>
        </FormGroup2>
        <Flex gap={16}>
          <FormGroup2
            label={t('treatingDoctor')}
            name={'treatingDoctorId'}
            submitCount={submitCount}
            errors={errors}
            touched={touched}
          >
            <Field name="treatingDoctorId">
              {({ field }) => (
                <ReactSelect
                  id={field.name}
                  instanceId={field.name}
                  selectedValue={field?.value}
                  isDisabled={!values.bsnrId}
                  items={sortedEmployees}
                  onItemSelect={(item: IMenuItem) => {
                    setValues({
                      ...values,
                      [field.name]: item?.value || '',
                      name: item?.label || '',
                    });
                  }}
                />
              )}
            </Field>
          </FormGroup2>
          <FormGroup2
            label={t('color')}
            name={'color'}
            submitCount={submitCount}
            errors={errors}
            touched={touched}
          >
            <Field name="roomColor">
              {({ field }) => (
                <ColorsPicker
                  {...field}
                  defaultColor={field.value}
                  onChange={(selectedColor: string) => {
                    setFieldValue(field.name, selectedColor);
                  }}
                />
              )}
            </Field>
          </FormGroup2>
        </Flex>
        <FormGroup2
          label={t('name')}
          isRequired
          name={'name'}
          submitCount={submitCount}
          errors={errors}
          touched={touched}
        >
          <Field name="name">
            {({ field }) => (
              <InputGroup
                {...field}
                width="100%"
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  const value = e.target.value;
                  setFieldValue(field.name, value);
                }}
                id={field.name}
                data-tab-id={field.name}
              />
            )}
          </Field>
        </FormGroup2>
        <Flex w="50%">
          <FormGroup2
            label={t('acceptableWaitTime')}
            isRequired
            name={'acceptableWaitingTimeInMinutes'}
            submitCount={submitCount}
            errors={errors}
            touched={touched}
          >
            <Field name="acceptableWaitingTimeInMinutes">
              {({ field }) => (
                <NumericInput
                  {...field}
                  buttonPosition="none"
                  id={field.name}
                  data-tab-id={field.name}
                  fill
                  min={0}
                  max={9999}
                  maxLength={4}
                  rightElement={<p>{t('minutes')}</p>}
                  onValueChange={(valueAsNumber) => {
                    setFieldValue(
                      field.name,
                      isNaN(valueAsNumber) ? field.value : valueAsNumber
                    );
                  }}
                />
              )}
            </Field>
          </FormGroup2>
        </Flex>
        <Flex w="50%">
          <FormGroup2>
            <Flex>
              <Field name="activeTimeMeasurement">
                {({ field }) => (
                  <Checkbox
                    {...field}
                    checked={field.value}
                    id={`${field.name}`}
                    name={`${field.name}`}
                    label={t('activeTimeMeasurement')}
                  />
                )}
              </Field>
            </Flex>
          </FormGroup2>
        </Flex>
      </Flex>
      <Flex className={`${Classes.DIALOG_FOOTER_ACTIONS} footer`}>
        <Button
          large
          outlined
          minimal
          intent="primary"
          onClick={onCloseDialog}
          disabled={isSubmitting}
          loading={isSubmitting}
        >
          {tButtonActions('cancelText')}
        </Button>
        <Button
          large
          type="submit"
          intent="primary"
          onClick={submitForm}
          disabled={isSubmitting}
          loading={isSubmitting}
        >
          {tButtonActions(isCreate ? 'create' : 'edit')}
        </Button>
      </Flex>
    </>
  );
};

const CreateWaitingRoom = (props: ICreateWaitingRoomProps) => {
  const { className, roomId, onCloseModal, onReFetchData } = props,
    [isOpen, setIsOpen] = useState<boolean>(false),
    [initValues, setInitValues] = useState<WaitingRoomModel>(initialValues),
    [employees, setEmployees] = useState<EmployeeDetailResponse[]>([]),
    [bsnrs, setBsnrs] = useState<IMenuBSNRItem[]>([]),
    { t } = I18n.useTranslation({
      namespace: 'WaitingRoom',
      nestedTrans: 'WaitingRoomModal',
    }),
    isCreate = useMemo(() => {
      return roomId === 'create';
    }, [roomId]);

  useEffect(() => {
    if (!roomId && bsnrs?.length === 1) {
      setInitValues((prevValues) => ({
        ...prevValues,
        bsnrId: bsnrs[0]?.id!,
      }));
    }
  }, [roomId, bsnrs]);

  useEffect(() => {
    if (roomId) {
      setIsOpen(true);
      if (roomId !== 'create') {
        getWaitingRooms({ roomId }).then((res) => {
          if (res.data.waitingRoomViews[0]) {
            const data = res.data.waitingRoomViews[0];
            setInitValues({
              id: data.id,
              bsnrId: data.bSNR.id!,
              treatingDoctorId: data.treatingDoctor.id,
              roomColor: data.roomColor,
              name: data.name,
              acceptableWaitingTimeInMinutes:
                data.acceptableWaitingTimeInMinutes,
              activeTimeMeasurement: data.activeTimeMeasurement,
              patientIds: data.patientIds,
            });
          }
        });
      }
    }
    AdminApi.getEmployeeDetails({
      name: '',
      pagination: {
        page: 1,
        pageSize: 10,
        sortBy: 'type',
        order: Order.ASC,
      },
    }).then((res) => {
      setEmployees(res.data?.employees || []);
    });
    getBSNR().then((res) => {
      const bsnrList = (res.data?.data || []).reduce((bsnrList: IMenuBSNRItem[], item) => {
        const { bsnr, nbsnr } = item;

        const bsnrData = {
          label: `${bsnr.code}-${bsnr.name}`,
          value: bsnr.code,
          isBSNR: true,
          id: bsnr.id!,
          data: bsnr as unknown as BSNRName,
          facilityType: bsnr.facilityType,
        };
        const nbsnrData = (nbsnr || []).map((nbsnrItem) => {
          const nbsnr = {
            label: `${nbsnrItem.code}-${nbsnrItem.name}`,
            value: nbsnrItem.code,
            isBSNR: false,
            id: nbsnrItem.id!,
            data: nbsnrItem as unknown as BSNRName,
            facilityType: nbsnrItem.facilityType,
          };
          return nbsnr;
        });

        return [...bsnrList, bsnrData, ...nbsnrData];
      }, []);

      setBsnrs(bsnrList);
    });
  }, [roomId]);

  const onCloseDialog = () => {
    setIsOpen(false);
    onCloseModal?.();
    setInitValues(initialValues);
  };

  const onSubmit = async (
    data: WaitingRoomModel,
    { setErrors }: FormikHelpers<WaitingRoomModel>
  ) => {
    try {
      const submitData = {
        waitingRoom: {
          ...data,
          acceptableWaitingTimeInMinutes: Number(
            data.acceptableWaitingTimeInMinutes
          ),
          treatingDoctorId: employees.find(
            (e) =>
              e.id === data.treatingDoctorId ||
              e.employeeProfileId === data.treatingDoctorId
          )?.id!,
        },
      };

      const respExistWaitingRoom = await checkExistWaitingRoomName({
        name: data.name,
        roomId: data.id,
      });

      if (respExistWaitingRoom?.data?.isExist) {
        setErrors({
          name: t('nameExisted'),
        });
        return;
      }

      const event = isCreate ? createWaitingRoom : editWaitingRoom;
      const res = await event(submitData);

      if (res.status !== 200) {
        return;
      }

      onCloseDialog();
      onReFetchData();
    } catch (error) {
      console.error(error);
      throw error;
    }
  };
  return (
    <Modal
      isOpen={isOpen}
      className={className}
      size={ModalSize.HALFSCREEN}
      title={isCreate ? t('createTitle') : t('editTitle')}
      canEscapeKeyClose
      onClose={onCloseDialog}
    >
      <Formik
        initialValues={initValues}
        onSubmit={onSubmit}
        enableReinitialize
        validate={(values) => {
          const validateFields: ValidateField[] = [
            {
              fieldName: 'bsnrId',
              validateRule: () => !values.bsnrId,
              errorMessage: t('bsnrRequired'),
            },
            {
              fieldName: 'name',
              validateRule: () => !values.name,
              errorMessage: t('nameRequired'),
            },
            {
              fieldName: 'acceptableWaitingTimeInMinutes',
              validateRule: () => values.acceptableWaitingTimeInMinutes < 0,
              errorMessage: t('acceptableWaitTimeRequired'),
            },
          ];

          const { errors } = PatientManagementUtil.validateForm(
            validateFields,
            null
          );

          return errors;
        }}
        render={(p: FormikProps<WaitingRoomModel>) => (
          <Content
            {...p}
            onCloseDialog={onCloseDialog}
            employees={employees}
            setEmployees={setEmployees}
            bsnrs={bsnrs}
            isCreate={isCreate}
          />
        )}
      />
    </Modal>
  );
};

export default CreateWaitingRoom;
