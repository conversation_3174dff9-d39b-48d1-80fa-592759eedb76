import { StylesConfig } from 'react-select';

import { CardItem, GROUP_FIELDS, INIT_VALUES } from './constants';
import PatientManagementUtil, {
  <PERSON><PERSON><PERSON><PERSON>ield,
} from '@tutum/infrastructure/utils/form.util';
import { IMenuBSNRItem } from '@tutum/admin/types/bsnr.type';
import {
  EmailSetting,
  MailOverviewItem,
} from '@tutum/hermes/bff/legacy/mail_common';
import { CardTypeType } from '@tutum/hermes/bff/card_common';
import {
  Pop3TestRequest
} from '@tutum/hermes/bff/legacy/app_mail';
import { ConnectorItem } from '../KIMAccounts';

type EmailFormModel = {
  [GROUP_FIELDS.BSNR]: string;
  [GROUP_FIELDS.CARD]: CardItem;
  [GROUP_FIELDS.EMAIL]: string;
  [GROUP_FIELDS.PASSWORD]: string;
  [GROUP_FIELDS.HOSTNAME]: string;
  [GROUP_FIELDS.POP3_PORT]: number;
  [GROUP_FIELDS.SMTP_PORT]: number;
  [GROUP_FIELDS.SYSTEM_NAME]: string;
  [GROUP_FIELDS.VERSION]: string;
  [GROUP_FIELDS.PROVIDER_HOSTNAME]: string;
  [GROUP_FIELDS.PROVIDER_POP3_PORT]: number;
  [GROUP_FIELDS.PROVIDER_SMTP_PORT]: number;
  [GROUP_FIELDS.CONNECTOR]: ConnectorItem;
};

export const formatDataModalHelper = (
  isEdit: boolean,
  dataModal: MailOverviewItem,
  cardList: CardItem[],
  connectorList: ConnectorItem[]
) => {
  if (!isEdit) {
    return INIT_VALUES;
  }

  const valueCard = cardList.find(
    (item) => item.value === dataModal.emailSetting.cardInformation.cardNumber
  );
  const valueConnector = connectorList.find(
    (item) => item.connectorId === dataModal.emailSetting.connectorId
  );
  if (valueConnector) {
    valueConnector.deviceId = dataModal.emailSetting.deviceId;
  }

  return {
    [GROUP_FIELDS.BSNR]: dataModal.emailSetting.general.bsnr,
    [GROUP_FIELDS.CARD]: valueCard,
    [GROUP_FIELDS.EMAIL]: dataModal.emailSetting.accountInfor.email,
    [GROUP_FIELDS.PASSWORD]: dataModal.emailSetting.accountInfor.passwordHash,
    [GROUP_FIELDS.HOSTNAME]: dataModal.emailSetting.clientModule.hostName,
    [GROUP_FIELDS.POP3_PORT]: dataModal.emailSetting.clientModule.pop3Port,
    [GROUP_FIELDS.SMTP_PORT]: dataModal.emailSetting.clientModule.smtpPort,
    [GROUP_FIELDS.SYSTEM_NAME]: dataModal.emailSetting.clientModule.systemName,
    [GROUP_FIELDS.VERSION]: dataModal.emailSetting.clientModule.version,
    [GROUP_FIELDS.PROVIDER_HOSTNAME]: dataModal.emailSetting.provider.hostName,
    [GROUP_FIELDS.PROVIDER_POP3_PORT]: dataModal.emailSetting.provider.pop3Port,
    [GROUP_FIELDS.PROVIDER_SMTP_PORT]: dataModal.emailSetting.provider.smtpPort,
    [GROUP_FIELDS.CONNECTOR]: valueConnector,
  };
};

export const formatValuesFormHelper = (
  values: EmailFormModel
): EmailSetting => {
  return {
    general: {
      bsnr: values[GROUP_FIELDS.BSNR],
    },
    cardInformation: {
      cardNumber: values[GROUP_FIELDS.CARD].value,
      cardType: values[GROUP_FIELDS.CARD].cardType,
    },
    accountInfor: {
      email: values[GROUP_FIELDS.EMAIL],
      passwordHash: values[GROUP_FIELDS.PASSWORD],
    },
    clientModule: {
      hostName: values[GROUP_FIELDS.HOSTNAME],
      pop3Port: values[GROUP_FIELDS.POP3_PORT],
      smtpPort: values[GROUP_FIELDS.SMTP_PORT],
      systemName: values[GROUP_FIELDS.SYSTEM_NAME],
      version: values[GROUP_FIELDS.VERSION],
    },
    provider: {
      hostName: values[GROUP_FIELDS.PROVIDER_HOSTNAME],
      pop3Port: values[GROUP_FIELDS.PROVIDER_POP3_PORT],
      smtpPort: values[GROUP_FIELDS.PROVIDER_SMTP_PORT],
    },
    connectorId: values[GROUP_FIELDS.CONNECTOR].connectorId,
    deviceId: values[GROUP_FIELDS.CONNECTOR].deviceId,
  };
};

// map EmailFormModel to Pop3TestRequest
export const formatPop3TestRequest = (
  values: EmailFormModel
): Pop3TestRequest => {
  return {
    account: {
      user: values[GROUP_FIELDS.EMAIL],
      password: values[GROUP_FIELDS.PASSWORD],
      mandantId: values[GROUP_FIELDS.CONNECTOR]?.mandantId,
      clientSystemId: values[GROUP_FIELDS.CONNECTOR]?.clientSystemId,
      workplaceId: values[GROUP_FIELDS.CONNECTOR]?.workplaceId,
    },
    pop3Config: {
      host: values[GROUP_FIELDS.PROVIDER_HOSTNAME],
      port: values[GROUP_FIELDS.POP3_PORT],
      fdServer: values[GROUP_FIELDS.HOSTNAME],
    },
    smtpConfig: {
      host: values[GROUP_FIELDS.PROVIDER_HOSTNAME],
      port: values[GROUP_FIELDS.SMTP_PORT],
      fdServer: values[GROUP_FIELDS.HOSTNAME],
    },
  };
};

export const onValidateFieldHelper = (values: EmailFormModel, t: any) => {
  const validateFields: ValidateField[] = [
    {
      fieldName: GROUP_FIELDS.BSNR,
      validateRule: () => !values[GROUP_FIELDS.BSNR],
      errorMessage: t('generalInfo.bsnrRequired'),
    },
    {
      fieldName: GROUP_FIELDS.CARD,
      validateRule: () => !values[GROUP_FIELDS.CARD],
      errorMessage: t('cardInfo.cardRequired'),
    },
    {
      fieldName: GROUP_FIELDS.EMAIL,
      validateRule: () => !values[GROUP_FIELDS.EMAIL],
      errorMessage: t('accountInfo.emailRequired'),
    },
    {
      fieldName: GROUP_FIELDS.PASSWORD,
      validateRule: () => !values[GROUP_FIELDS.PASSWORD],
      errorMessage: t('accountInfo.passwordRequired'),
    },
    {
      fieldName: GROUP_FIELDS.HOSTNAME,
      validateRule: () => !values[GROUP_FIELDS.HOSTNAME],
      errorMessage: t('clientModule.hostNameRequired'),
    },
    {
      fieldName: GROUP_FIELDS.POP3_PORT,
      validateRule: () => !values[GROUP_FIELDS.POP3_PORT],
      errorMessage: t('clientModule.pop3PortRequired'),
    },
    {
      fieldName: GROUP_FIELDS.SMTP_PORT,
      validateRule: () => !values[GROUP_FIELDS.SMTP_PORT],
      errorMessage: t('clientModule.smtpPortRequired'),
    },
    {
      fieldName: GROUP_FIELDS.SYSTEM_NAME,
      validateRule: () => !values[GROUP_FIELDS.SYSTEM_NAME],
      errorMessage: t('clientModule.systemNameRequired'),
    },
    {
      fieldName: GROUP_FIELDS.VERSION,
      validateRule: () => !values[GROUP_FIELDS.VERSION],
      errorMessage: t('clientModule.versionRequired'),
    },
    {
      fieldName: GROUP_FIELDS.PROVIDER_HOSTNAME,
      validateRule: () => !values[GROUP_FIELDS.PROVIDER_HOSTNAME],
      errorMessage: t('provider.hostNameRequired'),
    },
    {
      fieldName: GROUP_FIELDS.PROVIDER_POP3_PORT,
      validateRule: () => !values[GROUP_FIELDS.PROVIDER_POP3_PORT],
      errorMessage: t('provider.pop3PortRequired'),
    },
    {
      fieldName: GROUP_FIELDS.PROVIDER_SMTP_PORT,
      validateRule: () => !values[GROUP_FIELDS.PROVIDER_SMTP_PORT],
      errorMessage: t('provider.smtpPortRequired'),
    },
    {
      fieldName: GROUP_FIELDS.CONNECTOR,
      validateRule: () => !values[GROUP_FIELDS.CONNECTOR],
      errorMessage: t('connector.connectorRequired'),
    },
  ];
  const { errors } = PatientManagementUtil.validateForm(validateFields, null);

  return errors;
};

export const colourStylesHelper: StylesConfig = {
  control: (styles: any) => ({
    ...styles,
    backgroundColor: 'white',
    marginBottom: '4px',
  }),
  option: (styles: any, { data }: any) => {
    const { isBSNR } = data as IMenuBSNRItem;
    return {
      ...styles,
      fontWeight: isBSNR ? '600' : null,
      paddingLeft: !isBSNR ? '30px' : null,
    };
  },
};

export const getPinTypeByCardType = (cardType: CardTypeType) => {
  if (cardType === 'SMC-B') {
    return 'PIN.SMC';
  }

  return 'PIN.CH';
};
