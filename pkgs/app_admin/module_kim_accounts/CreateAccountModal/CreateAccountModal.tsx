import React, {
  ChangeEvent,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { isEmpty, isNil } from 'lodash';
import { Field, Form, Formik } from 'formik';

import type CommonLocales from '@tutum/admin/locales/en/Common.json';
import type ErrorCodeLocales from '@tutum/admin/locales/en/ErrorCode.json';

import { Modal, ModalSize } from '@tutum/design-system/components/Modal';
import {
  BodyTextM,
  BodyTextL,
  Flex,
  Button,
  LeaveConfirmModal,
  H2,
  Svg,
  BodyTextS,
  ReactSelect,
  coreComponents,
  IMenuItem,
  FormGroup2,
  LoadingState,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import {
  Divider,
  InputGroup,
  NumericInput,
} from '@tutum/design-system/components/Core';
// import { useHotkeys } from 'react-hotkeys-hook';
import { IMenuBSNRItem } from '@tutum/admin/types/bsnr.type';
import {
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components/Toaster';
import {
  pop3Test,
  useMutationCreateMailSetting,
  useMutationEditMailSetting,
} from '@tutum/hermes/bff/legacy/app_mail';
import { MailOverviewItem } from '@tutum/hermes/bff/mail_common';
import { Card } from '@tutum/hermes/bff/card_common';
import { COLOR } from '@tutum/design-system/themes/styles';
import { GROUPS_INFO, GROUP_FIELDS } from './constants';
import {
  formatDataModalHelper,
  formatValuesFormHelper,
  onValidateFieldHelper,
  colourStylesHelper,
  getPinTypeByCardType,
  formatPop3TestRequest,
} from './helpers';
import { ConnectorItem } from '../KIMAccounts';
import { CardTypeType } from '@tutum/hermes/bff/legacy/card_common';
import {
  useQueryGetCards,
  useQueryGetPinStatus,
} from '@tutum/hermes/bff/legacy/app_card_operation';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { BannerSetting } from '@tutum/design-system/components/BannerSetting';
import { OptionProps } from 'react-select';

export interface CreateAccountModalProps {
  className?: string;
  bsnrList: IMenuBSNRItem[];
  connectorList: ConnectorItem[];
  isOpen: boolean;
  dataModal: MailOverviewItem | null;
  onClose: () => void;
  onRefetchData: () => void;
}

const InfoIcon = '/images/information-circle.svg';
const EyeOn = '/images/eye-on.svg';
const EyeOff = '/images/eye-off.svg';
const CardType = '/images/layout-grid.svg';
const CardOwner = '/images/user.svg';
const CardNumber = '/images/card-chip.svg';

const CreateAccountModal: React.FC<CreateAccountModalProps> = ({
  className,
  bsnrList,
  connectorList,
  isOpen,
  dataModal,
  onClose,
  onRefetchData,
}) => {
  const isEdit = !isEmpty(dataModal);
  const { t } = I18n.useTranslation({
    namespace: 'KIMAccounts',
    nestedTrans: 'createModal',
  });
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });
  const { t: tErrorCode } = I18n.useTranslation<keyof typeof ErrorCodeLocales>({
    namespace: 'ErrorCode',
  });
  const { t: tCommonTable } = I18n.useTranslation<
    keyof typeof CommonLocales.Table
  >({
    namespace: 'Common',
    nestedTrans: 'Table',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [sectionCurrent, setSectionCurrent] = useState(1);
  const [isConfirmLeaveOpen, setIsConfirmLeaveOpen] = useState(false);
  const [showPass, setShowPass] = useState<boolean>(false);
  const [isPop3Testing, setIsPop3Testing] = useState(false);

  const generalInfoRef = useRef<HTMLDivElement | null>(null);
  const cardInfoRef = useRef<HTMLDivElement | null>(null);
  const accountInfoRef = useRef<HTMLDivElement | null>(null);
  const clientModuleRef = useRef<HTMLDivElement | null>(null);
  const providerRef = useRef<HTMLDivElement | null>(null);
  const connectorRef = useRef<HTMLDivElement | null>(null);

  const {
    data: unMappedCardList,
    isLoading,
    isSuccess: isSuccessGetCards,
    isError,
  } = useQueryGetCards(
    {
      connectorId: connectorList?.[0]?.connectorId,
    },
    {
      enabled: !!connectorList?.[0]?.connectorId,
      throwOnError: false,
    }
  );

  const { data: pinStatusRes, isLoading: isPinLoading } = useQueryGetPinStatus(
    {
      getPinStatus: unMappedCardList?.cards.map((card) => {
        return {
          cardHandle: card.cardHandle,
          cardType: card.cardType,
          connectorId: connectorList[0].connectorId,
          iccsn: card.iccsn,
          pinType: null!,
        };
      })!,
    },
    {
      enabled:
        isSuccessGetCards &&
        Boolean(unMappedCardList?.cards.length) &&
        Boolean(connectorList?.length),
    }
  );

  const { mutateAsync: createMailSetting } = useMutationCreateMailSetting({
    onSuccess: () => {
      t('createSuccessfully');
      onRefetchData();
      onClose();
    },
  });

  const { mutateAsync: editMailSetting } = useMutationEditMailSetting({
    onSuccess: () => {
      t('editSuccessfully');
      onRefetchData();
      onClose();
    },
  });

  const supportedKimCard = [
    CardTypeType.CardTypeTypeHBA,
    CardTypeType.CardTypeTypeSMCB,
  ];

  const isLoadingCard =
    isLoading || isPinLoading || !connectorList?.[0]?.connectorId;

  const cardList = (unMappedCardList?.cards || [])
    .map((card) => ({
      ...card,
      value: card.iccsn,
      label: card.iccsn,
    }))
    .filter((card) => supportedKimCard.includes(card.cardType));

  const handleClickSection = (section: number) => {
    setSectionCurrent(section);
  };

  const onConfirmCloseModal = () => {
    setIsConfirmLeaveOpen(true);
  };

  const formatDataModal = useMemo(() => {
    return formatDataModalHelper(isEdit, dataModal!, cardList, connectorList);
  }, [isEdit, dataModal, cardList, connectorList]);

  const checkNotActivatedCard = (selectedCard: Card) => {
    if (isEmpty(selectedCard)) {
      return false;
    }

    if (!isSuccessGetCards || !pinStatusRes?.cardPins.length) {
      return false;
    }

    const pinType = getPinTypeByCardType(selectedCard.cardType);
    const pinData = pinStatusRes.cardPins.find(
      (pin) => pin.pinType === pinType
    );

    return !['VERIFIABLE', 'VERIFIED'].includes(pinData?.pinStatus!);
  };

  const onSubmit = async (values, { setSubmitting }) => {
    try {
      if (checkNotActivatedCard(values[GROUP_FIELDS.CARD])) {
        return;
      }

      if (isEdit) {
        await editMailSetting({
          iD: dataModal.id,
          emailSetting: formatValuesFormHelper(values),
        });
        return;
      }

      await createMailSetting({
        emailSetting: formatValuesFormHelper(values),
      });
    } finally {
      setSubmitting(false);
    }
  };

  const onPop3Test = async (values) => {
    try {
      setIsPop3Testing(true);
      const pop3TestRequest = formatPop3TestRequest(values);
      const resp = await pop3Test(pop3TestRequest);

      if (!resp?.data?.success) {
        const errorMessage = resp?.data?.errorMessage;
        alertError(errorMessage || t('provider.testFailed'));
        return;
      }

      alertSuccessfully(t('provider.testSuccessfully'));
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsPop3Testing(false);
    }
  };

  const renderExtendInfoItem = (value: string, text: string, icon?: string) => {
    return (
      <Flex className="sl-extend-info__item" align="center">
        <Flex align="center">
          {icon && <Svg className="sl-extend-info__item__icon" src={icon} />}
          <BodyTextS
            className="sl-extend-info__item__text-type"
            color={COLOR.TEXT_SECONDARY_NAVAL}
            fontWeight={500}
          >
            {text}
          </BodyTextS>
        </Flex>
        <Flex>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>{value}</BodyTextM>
        </Flex>
      </Flex>
    );
  };

  const renderExtendInfoCard = (selectedCard: Card) => {
    if (!selectedCard) {
      return null;
    }

    return (
      <div className="sl-extend-info">
        {renderExtendInfoItem(
          selectedCard.cardType,
          t('cardInfo.cardType'),
          CardType
        )}
        {renderExtendInfoItem(
          selectedCard.cardOwner,
          t('cardInfo.cardOwner'),
          CardOwner
        )}
        {renderExtendInfoItem(
          selectedCard.iccsn,
          t('cardInfo.cardNumber'),
          CardNumber
        )}
      </div>
    );
  };

  const renderExtendInfoConnector = (
    selectedConnector: ConnectorItem | null
  ) => {
    if (!selectedConnector) {
      return null;
    }

    return (
      <div className="sl-extend-info">
        {renderExtendInfoItem(
          selectedConnector.clientSystemId,
          t('connector.clientSystemId')
        )}
        {renderExtendInfoItem(
          selectedConnector.mandantId,
          t('connector.mandantId')
        )}
        {renderExtendInfoItem(
          selectedConnector.workplaceId,
          t('connector.workplaceId')
        )}
      </div>
    );
  };

  // useHotkeys('alt+up', () => {
  //   setSectionCurrent((prevSection) =>
  //     prevSection > 1 ? prevSection - 1 : prevSection
  //   );
  // });

  // useHotkeys('alt+down', () => {
  //   setSectionCurrent((prevSection) =>
  //     prevSection < GROUPS_INFO.length ? prevSection + 1 : prevSection
  //   );
  // });

  useLayoutEffect(() => {
    let tempRef: HTMLDivElement | null = null;

    switch (sectionCurrent) {
      case 1:
        tempRef = generalInfoRef.current;
        break;
      case 2:
        tempRef = cardInfoRef.current;
        break;
      case 3:
        tempRef = accountInfoRef.current;
        break;
      case 4:
        tempRef = clientModuleRef.current;
        break;
      case 5:
        tempRef = providerRef.current;
        break;
      case 6:
        tempRef = connectorRef.current;
        break;
      default:
        break;
    }

    tempRef?.scrollIntoView({
      behavior: 'auto',
    });
  }, [sectionCurrent]);

  return (
    <Modal
      className={className}
      title={isEdit ? t('titleEdit') : t('titleCreate')}
      isOpen={isOpen}
      size={ModalSize.AUTO}
      onClose={onConfirmCloseModal}
      shouldReturnFocusOnClose
      canEscapeKeyClose={false}
    >
      <Divider style={{ margin: 0 }} />
      <Formik
        initialValues={formatDataModal}
        onSubmit={onSubmit}
        enableReinitialize
        validate={(values) => onValidateFieldHelper(values, t)}
      >
        {({
          values,
          isSubmitting,
          submitCount,
          touched,
          errors,
          setFieldValue,
          setValues,
          setTouched,
        }) => (
          <Form
            className="sl-form-content"
            onKeyDown={(e) =>
              e.key === 'Enter' && !e.shiftKey && e.preventDefault()
            }
          >
            <Flex className="sl-form-content__body">
              <Flex className="sl-navigation-side" column gap={24}>
                {GROUPS_INFO.map((item, index) => (
                  <BodyTextL
                    key={item}
                    className="sl-navigation-side__item"
                    fontWeight={sectionCurrent === index + 1 ? 600 : 'Normal'}
                    onClick={() => handleClickSection(index + 1)}
                    fontSize={16}
                  >
                    {t(`${item}.titleTab`)}
                  </BodyTextL>
                ))}
              </Flex>
              <Flex className="sl-content-side" column auto gap={24}>
                <Flex ref={generalInfoRef} column gap={16}>
                  <H2>{t('generalInfo.title')}</H2>
                  <FormGroup2
                    label={t('generalInfo.bsnr')}
                    isRequired
                    name={GROUP_FIELDS.BSNR}
                    submitCount={submitCount}
                    errors={errors}
                    touched={touched}
                  >
                    <Field name={GROUP_FIELDS.BSNR}>
                      {({ field }) => (
                        <ReactSelect
                          isSearchable={false}
                          selectedValue={field.value}
                          items={bsnrList}
                          styles={colourStylesHelper}
                          onItemSelect={(item: IMenuItem) => {
                            const value = item?.value || '';
                            setValues({
                              ...values,
                              [field.name]: value,
                              [GROUP_FIELDS.CARD]: '',
                              [GROUP_FIELDS.CONNECTOR]: '',
                            });
                            setTouched({
                              ...touched,
                              [GROUP_FIELDS.CARD]: false,
                              [GROUP_FIELDS.CONNECTOR]: false,
                            });
                          }}
                        />
                      )}
                    </Field>
                  </FormGroup2>
                </Flex>
                <Flex ref={cardInfoRef} column gap={16}>
                  <H2>{t('cardInfo.title')}</H2>
                  {isError && (
                    <BannerSetting
                      className="banner-setting"
                      title={tErrorCode(
                        ErrorCode.ErrorCode_TIConnector_Not_Found as any
                      )}
                    />
                  )}
                  <Flex className="sl-message-bar">
                    {<Svg src={InfoIcon} />}
                    <BodyTextM>{t('cardInfo.note')}</BodyTextM>
                  </Flex>
                  <FormGroup2
                    label={t('cardInfo.card')}
                    isRequired
                    name={GROUP_FIELDS.CARD}
                    submitCount={submitCount}
                    errors={errors}
                    touched={touched}
                    warning={
                      checkNotActivatedCard(values[GROUP_FIELDS.CARD])
                        ? t('cardInfo.notActivated')
                        : ''
                    }
                  >
                    {isLoadingCard ? (
                      <LoadingState size={32} />
                    ) : (
                      <Field name={GROUP_FIELDS.CARD}>
                        {({ field }) => (
                          <ReactSelect
                            selectedValue={field.value?.value}
                            items={
                              values[GROUP_FIELDS.BSNR] && !isLoadingCard
                                ? cardList
                                : []
                            }
                            isLoading={isLoadingCard}
                            loadingMessage={() => tCommonTable('loading')}
                            components={{
                              Option: (item: any) => (
                                <coreComponents.Option {...item}>
                                  <div className="sl-card-number-option">
                                    <BodyTextM
                                      color={COLOR.TEXT_SECONDARY_NAVAL}
                                    >
                                      {item.data.iccsn}
                                    </BodyTextM>
                                    <BodyTextM
                                      color={COLOR.TEXT_SECONDARY_NAVAL}
                                    >
                                      {item.data.cardType} •{' '}
                                      {item.data.cardOwner}
                                    </BodyTextM>
                                  </div>
                                </coreComponents.Option>
                              ),
                            }}
                            onItemSelect={(item) => {
                              setFieldValue(field.name, item || '');
                            }}
                          />
                        )}
                      </Field>
                    )}
                  </FormGroup2>
                  {!isLoadingCard &&
                    renderExtendInfoCard(values[GROUP_FIELDS.CARD])}
                </Flex>
                <Flex ref={accountInfoRef} column gap={16}>
                  <H2>{t('accountInfo.title')}</H2>
                  <Flex auto justify="space-between" style={{ gap: '16px' }}>
                    <FormGroup2
                      label={t('accountInfo.email')}
                      isRequired
                      name={GROUP_FIELDS.EMAIL}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.EMAIL}>
                        {({ field }) => (
                          <InputGroup
                            {...field}
                            type="email"
                            width="100%"
                            onChange={(e: ChangeEvent<HTMLInputElement>) => {
                              const value = e.target.value;
                              setFieldValue(field.name, value);
                            }}
                            id={field.name}
                            data-tab-id={field.name}
                          />
                        )}
                      </Field>
                    </FormGroup2>
                    <FormGroup2
                      label={t('accountInfo.password')}
                      isRequired
                      name={GROUP_FIELDS.PASSWORD}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.PASSWORD}>
                        {({ field }) => (
                          <InputGroup
                            {...field}
                            maxLength={45}
                            type={showPass ? 'text' : 'password'}
                            rightElement={
                              <Svg
                                src={showPass ? EyeOn : EyeOff}
                                onClick={() => {
                                  setShowPass(!showPass);
                                }}
                              />
                            }
                          />
                        )}
                      </Field>
                    </FormGroup2>
                  </Flex>
                </Flex>
                <Flex ref={clientModuleRef} column gap={16}>
                  <H2>{t('clientModule.title')}</H2>
                  <FormGroup2
                    label={t('clientModule.hostName')}
                    isRequired
                    name={GROUP_FIELDS.HOSTNAME}
                    submitCount={submitCount}
                    errors={errors}
                    touched={touched}
                  >
                    <Field name={GROUP_FIELDS.HOSTNAME}>
                      {({ field }) => (
                        <InputGroup
                          {...field}
                          width="100%"
                          onChange={(e: ChangeEvent<HTMLInputElement>) => {
                            const value = e.target.value;
                            setFieldValue(field.name, value);
                          }}
                          id={field.name}
                          data-tab-id={field.name}
                        />
                      )}
                    </Field>
                  </FormGroup2>
                  <Flex auto justify="space-between" style={{ gap: '16px' }}>
                    <FormGroup2
                      label={t('clientModule.pop3Port')}
                      isRequired
                      name={GROUP_FIELDS.POP3_PORT}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.POP3_PORT}>
                        {({ field }) => (
                          <NumericInput
                            {...field}
                            buttonPosition="none"
                            id={field.name}
                            data-tab-id={field.name}
                            fill
                            min={0}
                            onValueChange={(valueAsNumber) => {
                              setFieldValue(
                                field.name,
                                isNaN(valueAsNumber)
                                  ? !isNil(field.value)
                                    ? field.value
                                    : ''
                                  : valueAsNumber
                              );
                            }}
                          />
                        )}
                      </Field>
                    </FormGroup2>
                    <FormGroup2
                      label={t('clientModule.smtpPort')}
                      isRequired
                      name={GROUP_FIELDS.SMTP_PORT}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.SMTP_PORT}>
                        {({ field }) => (
                          <NumericInput
                            {...field}
                            buttonPosition="none"
                            id={field.name}
                            data-tab-id={field.name}
                            fill
                            min={0}
                            onValueChange={(valueAsNumber) => {
                              setFieldValue(
                                field.name,
                                isNaN(valueAsNumber)
                                  ? !isNil(field.value)
                                    ? field.value
                                    : ''
                                  : valueAsNumber
                              );
                            }}
                          />
                        )}
                      </Field>
                    </FormGroup2>
                  </Flex>
                  <Flex auto justify="space-between" style={{ gap: '16px' }}>
                    <FormGroup2
                      label={t('clientModule.systemName')}
                      isRequired
                      name={GROUP_FIELDS.SYSTEM_NAME}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.SYSTEM_NAME}>
                        {({ field }) => (
                          <InputGroup
                            {...field}
                            width="100%"
                            onChange={(e: ChangeEvent<HTMLInputElement>) => {
                              const value = e.target.value;
                              setFieldValue(field.name, value);
                            }}
                            id={field.name}
                            data-tab-id={field.name}
                          />
                        )}
                      </Field>
                    </FormGroup2>
                    <FormGroup2
                      label={t('clientModule.version')}
                      isRequired
                      name={GROUP_FIELDS.VERSION}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.VERSION}>
                        {({ field }) => (
                          <InputGroup
                            {...field}
                            width="100%"
                            onChange={(e: ChangeEvent<HTMLInputElement>) => {
                              const value = e.target.value;
                              setFieldValue(field.name, value);
                            }}
                            id={field.name}
                            data-tab-id={field.name}
                          />
                        )}
                      </Field>
                    </FormGroup2>
                  </Flex>
                </Flex>
                <Flex ref={providerRef} column gap={16}>
                  <H2>{t('provider.title')}</H2>
                  <FormGroup2
                    label={t('provider.hostName')}
                    isRequired
                    name={GROUP_FIELDS.PROVIDER_HOSTNAME}
                    submitCount={submitCount}
                    errors={errors}
                    touched={touched}
                  >
                    <Field name={GROUP_FIELDS.PROVIDER_HOSTNAME}>
                      {({ field }) => (
                        <InputGroup
                          {...field}
                          width="100%"
                          onChange={(e: ChangeEvent<HTMLInputElement>) => {
                            const value = e.target.value;
                            setFieldValue(field.name, value);
                          }}
                          id={field.name}
                          data-tab-id={field.name}
                        />
                      )}
                    </Field>
                  </FormGroup2>
                  <Flex auto justify="space-between" style={{ gap: '16px' }}>
                    <FormGroup2
                      label={t('provider.pop3Port')}
                      isRequired
                      name={GROUP_FIELDS.PROVIDER_POP3_PORT}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.PROVIDER_POP3_PORT}>
                        {({ field }) => (
                          <NumericInput
                            {...field}
                            buttonPosition="none"
                            id={field.name}
                            data-tab-id={field.name}
                            fill
                            min={0}
                            onValueChange={(valueAsNumber) => {
                              setFieldValue(
                                field.name,
                                isNaN(valueAsNumber)
                                  ? !isNil(field.value)
                                    ? field.value
                                    : ''
                                  : valueAsNumber
                              );
                            }}
                          />
                        )}
                      </Field>
                    </FormGroup2>
                    <FormGroup2
                      label={t('provider.smtpPort')}
                      isRequired
                      name={GROUP_FIELDS.PROVIDER_SMTP_PORT}
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name={GROUP_FIELDS.PROVIDER_SMTP_PORT}>
                        {({ field }) => (
                          <NumericInput
                            {...field}
                            buttonPosition="none"
                            id={field.name}
                            data-tab-id={field.name}
                            fill
                            min={0}
                            onValueChange={(valueAsNumber) => {
                              setFieldValue(
                                field.name,
                                isNaN(valueAsNumber)
                                  ? !isNil(field.value)
                                    ? field.value
                                    : ''
                                  : valueAsNumber
                              );
                            }}
                          />
                        )}
                      </Field>
                    </FormGroup2>
                  </Flex>
                  <Button
                    className="sl-pop3-test-btn"
                    intent="primary"
                    outlined
                    minimal
                    disabled={isPop3Testing}
                    loading={isPop3Testing}
                    onClick={() => onPop3Test(values)}
                  >
                    {t('provider.pop3Test')}
                  </Button>
                </Flex>
                <Flex ref={connectorRef} column gap={16}>
                  <H2>{t('connector.title')}</H2>
                  <FormGroup2
                    label={t('connector.connector')}
                    isRequired
                    name={GROUP_FIELDS.CONNECTOR}
                    submitCount={submitCount}
                    errors={errors}
                    touched={touched}
                  >
                    {!connectorList ? (
                      <LoadingState size={32} />
                    ) : (
                      <Field name={GROUP_FIELDS.CONNECTOR}>
                        {({ field }) => (
                          <ReactSelect
                            menuPlacement="auto"
                            selectedValue={field.value?.value}
                            items={
                              values[GROUP_FIELDS.BSNR] ? connectorList : []
                            }
                            components={{
                              Option: (item: any) => {
                                const clientSystemIdText = item.data
                                  .clientSystemId
                                  ? `${t('connector.clientSystemId')}: ${item.data.clientSystemId
                                  }`
                                  : '';
                                const mandantIdText = item.data.mandantId
                                  ? ` • ${t('connector.mandantId')}: ${item.data.mandantId
                                  }`
                                  : '';
                                return (
                                  <coreComponents.Option {...item}>
                                    <div className="sl-connector-option">
                                      <BodyTextM
                                        color={COLOR.TEXT_SECONDARY_NAVAL}
                                      >
                                        {item.data.label}
                                      </BodyTextM>
                                      <BodyTextM
                                        color={COLOR.TEXT_SECONDARY_NAVAL}
                                      >
                                        {clientSystemIdText}
                                        {mandantIdText}
                                      </BodyTextM>
                                    </div>
                                  </coreComponents.Option>
                                );
                              },
                            }}
                            onItemSelect={(item: IMenuItem) => {
                              setFieldValue(field.name, item || '');
                            }}
                          />
                        )}
                      </Field>
                    )}
                  </FormGroup2>
                  {connectorList &&
                    renderExtendInfoConnector(values[GROUP_FIELDS.CONNECTOR])}
                </Flex>
              </Flex>
            </Flex>
            <Flex className="sl-form-content__footer">
              <BodyTextM fontSize={12}>
                {tCommon('keyboardCommandNavigateBetweenSections')}
              </BodyTextM>
              <Flex gap={8}>
                <Button
                  large
                  intent="primary"
                  outlined
                  minimal
                  onClick={onConfirmCloseModal}
                  disabled={isSubmitting}
                  loading={isSubmitting}
                >
                  {tButtonActions('cancelText')}
                </Button>
                <Button
                  large
                  type="submit"
                  intent="primary"
                  disabled={isSubmitting}
                  loading={isSubmitting}
                >
                  {isEdit
                    ? tButtonActions('saveText')
                    : tButtonActions('addText')}
                </Button>
              </Flex>
            </Flex>
          </Form>
        )}
      </Formik>
      <LeaveConfirmModal
        isOpen={isConfirmLeaveOpen}
        onConfirm={() => {
          onClose();
          setIsConfirmLeaveOpen(false);
        }}
        onClose={() => setIsConfirmLeaveOpen(false)}
      />
    </Modal>
  );
};

export default CreateAccountModal;
