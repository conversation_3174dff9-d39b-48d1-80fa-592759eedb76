import React, { useEffect, useMemo, useState } from 'react';
import debounce from 'lodash/debounce';

import type DoctorLetterI18n from '@tutum/admin/locales/en/DoctorLetter.json';

import { Flex, Button, BodyTextM, Svg } from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { InputGroup } from '@tutum/design-system/components/Core';
import {
  PlusIconSvgURL,
  SearchIconSvgURL,
} from '@tutum/admin/module_textmodule-overview/TextmoduleOverview.constant';
import {
  PAGE_DEFAULT,
  PAGE_SIZE_DEFAULT,
} from '@tutum/design-system/consts/table';
import type { LetterTemplate } from '@tutum/hermes/bff/legacy/doctor_letter_common';
import {
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components/Toaster';
import type { Order } from '@tutum/hermes/bff/common';
import I18n from '@tutum/infrastructure/i18n';
import {
  genColumns,
  customStyles,
} from '@tutum/admin/module_doctor_letter/template-overview/setting-table';
import {
  useDoctorLetterStore,
  DoctorLetterActions,
} from '@tutum/admin/module_doctor_letter/DoctorLetter.store';
import CreateTemplateStyled from '@tutum/admin/module_doctor_letter/template-overview/create-template-dialog/CreateTemplate.styled';
import {
  createTemplate,
  useQueryGetTemplates,
  useMutationDeleteTemplate,
} from '@tutum/hermes/bff/legacy/app_doctor_letter';
import type { SortType } from '@tutum/admin/module_doctor_letter/DoctorLetter.const';
import { COLOR } from '@tutum/design-system/themes/styles';
import { useSVFeatureEnable } from '@tutum/admin/hooks/useSVFeatureEnable';

export interface ITemplateProps {
  className?: string;
  t: any;
  bsnrId: string;
}

const Template = ({ className, t, bsnrId }: ITemplateProps) => {
  const { isEditTemplate, isOpenCreateTemplate } = useDoctorLetterStore();
  const { t: tCreateTemplate } = I18n.useTranslation<
    keyof typeof DoctorLetterI18n.CreateTemplate
  >({
    namespace: 'DoctorLetter',
    nestedTrans: 'CreateTemplate',
  });
  const [searchQuery, setSearchQuery] = useState('');

  const [isConfirmDelete, setIsConfirmDelete] = useState({
    isDelete: false,
    templateId: '',
  });
  const [pagination, setPagination] = useState({
    page: PAGE_DEFAULT,
    pageSize: PAGE_SIZE_DEFAULT,
  });

  const [sortField, setSortField] = useState<SortType | ''>('');
  const [sortOrder, setSortOrder] = useState<Order | ''>('');
  const { isLoading, isExistFAVDoctor } = useSVFeatureEnable();

  const getExcludeTags = () => {
    const excludeTags: string[] = [];

    if (!isExistFAVDoctor) {
      excludeTags.push('FAV');
    }
    return excludeTags;
  };

  const { data, isPending, refetch } = useQueryGetTemplates(
    {
      paginationRequest: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        sortBy: sortField.toString(),
        order: sortOrder || null!,
      },
      query: searchQuery,
      bsnrId,
      excludeTags: getExcludeTags(),
    },
    {
      throwOnError: false,
      enabled: !isLoading && !!bsnrId,
    }
  );

  const { mutate: deleteTemplate } = useMutationDeleteTemplate({
    throwOnError: false,
    onError: (error) => {
      let message = `${t('deleteFailed')}: ${error.message}`;
      if (error?.response?.data?.message) {
        message = t(error?.response?.data?.message);
      }
      alertError(message);
    },
    onSuccess: () => {
      alertSuccessfully('Successfully');
      setIsConfirmDelete(null!);
      refetch();
    },
  });

  useEffect(() => {
    if (!bsnrId) return;
    DoctorLetterActions.getListHeaderFootersForTemplate(bsnrId, searchQuery);
  }, [bsnrId]);

  const onChangeSearchInput = useMemo(
    () =>
      debounce(
        (e) => setSearchQuery((e.target as HTMLInputElement).value),
        400
      ),
    []
  );

  const onCreateTemplate = () => {
    DoctorLetterActions.setIsOpenCreateTemplate(true);
  };

  const onEditTemplate = (template: LetterTemplate) => {
    DoctorLetterActions.setIsEditTemplate(true);
    DoctorLetterActions.setCurrentTemplate(template);
    DoctorLetterActions.setIsOpenCreateTemplate(true);
  };

  const onConfirmDeleteTemplate = (template: LetterTemplate) => {
    setIsConfirmDelete({
      isDelete: true,
      templateId: template.id!,
    });
  };

  const onChangePage = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const onCloseModal = () => {
    if (isEditTemplate) {
      DoctorLetterActions.setIsEditTemplate(false);
    }
    DoctorLetterActions.setCurrentTemplate(null!);
    DoctorLetterActions.setIsOpenCreateTemplate(false);
  };

  const onDeleteTemplate = (id: string) => {
    deleteTemplate({ id });
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination({
      ...pagination,
      pageSize: currentRowsPerPage,
    });
  };

  const onChangeSort = (type: SortType, order: Order) => {
    setSortOrder(order);
    setSortField(type);
  };

  const onDuplicate = (values: LetterTemplate, bsnrId: string) => {
    const newTemplate = {
      letterTemplate: { ...values, id: undefined, name: `${values?.name}- copy` },
      bsnrId,
    };
    createTemplate(newTemplate)
      .then(() => {
        alertSuccessfully(tCreateTemplate('success'));
        refetch();
      })
      .catch((error) => {
        console.error(error);
        alertError(
          `${tCreateTemplate('failed')}: ${tCreateTemplate('nameAlready')}`
        );
      });
  };

  return (
    <div className={className}>
      <Flex p={16} justify="space-between">
        <InputGroup
          type="text"
          className="sl-panel-search-input"
          defaultValue=""
          isNumeric={false}
          placeholder={t('search')}
          leftElement={<Svg src={SearchIconSvgURL} />}
          onChange={onChangeSearchInput}
        />
        <Button
          intent="primary"
          iconOnly
          onClick={onCreateTemplate}
          icon={<Svg src={PlusIconSvgURL} />}
        />
      </Flex>
      <Table
        columns={genColumns({
          t,
          sortType: sortField,
          onEdit: onEditTemplate,
          onConfirmDelete: onConfirmDeleteTemplate,
          onChangeSort,
          onDuplicate,
          bsnrId,
        })}
        data={data ? data.letterTemplates : []}
        customStyles={customStyles}
        highlightOnHover
        noHeader
        persistTableHead
        noDataComponent={
          <BodyTextM color={COLOR.TEXT_PLACEHOLDER} style={{ marginTop: 24 }}>
            {t('noData', { value: 'Template' })}
          </BodyTextM>
        }
        progressPending={isPending}
        fixedHeader
        pagination
        paginationServer
        paginationDefaultPage={1}
        paginationTotalRows={data ? data.paginationResponse.total : 0}
        paginationPerPage={pagination.pageSize}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
      />

      {isOpenCreateTemplate && (
        <CreateTemplateStyled
          isOpen
          onClose={onCloseModal}
          refetch={refetch}
          bsnrId={bsnrId}
        />
      )}

      <DeleteConfirmDialog
        className="confirm-dialog--hide-icon"
        isOpen={!!isConfirmDelete?.isDelete}
        close={() => setIsConfirmDelete(null!)}
        confirm={() => onDeleteTemplate(isConfirmDelete.templateId)}
        text={{
          btnCancel: t('cancelButton'),
          btnOk: t('confirmButton'),
          title: t('title', { value: 'template' }),
          message: t('message', { value: 'Template' }),
        }}
      />
    </div>
  );
};

export default Template;
