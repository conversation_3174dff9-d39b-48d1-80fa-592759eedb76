import type DoctorLetterI18n from '@tutum/admin/locales/en/DoctorLetter.json';

import {
  BodyTextM,
  Box,
  Button,
  Flex,
  Svg,
  Tooltip,
  Tag,
} from '@tutum/design-system/components';
import type {
  IDataTableColumn,
  IDataTableStyles,
} from '@tutum/design-system/components/Table';
import { PatientInfoTemplateType } from '@tutum/hermes/bff/legacy/patient_info_template_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { COLOR } from '@tutum/design-system/themes/styles';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '@tutum/infrastructure/shared/date-format';
import type { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type { TemplateResponse } from '@tutum/hermes/bff/legacy/app_patient_info_template';

const MedicalHistory = '/images/medical-history.svg';
const Billing = '/images/billing.svg';
const LivingWill = '/images/living-will.svg';
const Privacy = '/images/privacy.svg';
const TrashBin = '/images/trash-bin.svg';
const ContactConsent = '/images/phone-mail.svg';

export const fillPatientInfoTemplateFileName = (rowId: string) => {
  return `PATIENT_INFO_TEMPLATE_${rowId}.pdf`;
};

export const iconMapping = (type: PatientInfoTemplateType) => {
  const mapping: Record<PatientInfoTemplateType, string> = {
    [PatientInfoTemplateType.MedicalHistory]: MedicalHistory,
    [PatientInfoTemplateType.Billing]: Billing,
    [PatientInfoTemplateType.LivingWill]: LivingWill,
    [PatientInfoTemplateType.Privacy]: Privacy,
    [PatientInfoTemplateType.ContactConsent]: ContactConsent,
  };
  return <Svg src={mapping[type]} />;
};

export const tagMapping = (t: any, type: PatientInfoTemplateType) => {
  const textMapping: Record<PatientInfoTemplateType, string> = {
    [PatientInfoTemplateType.MedicalHistory]: t('medicalHistory'),
    [PatientInfoTemplateType.Billing]: t('billing'),
    [PatientInfoTemplateType.LivingWill]: t('livingWill'),
    [PatientInfoTemplateType.Privacy]: t('privacy'),
    [PatientInfoTemplateType.ContactConsent]: t('contactConsent'),
  };
  return (
    <Tag icon={iconMapping(type)} slStyle="fill" slState="warning">
      <Box mt={2}>{textMapping[type]}</Box>
    </Tag>
  );
};

export const genColumns = (
  t: IFixedNamespaceTFunction<keyof typeof DoctorLetterI18n.TemplateOverview>,
  handleFileChange: (row: TemplateResponse) => void,
  onRemoveFile: (rowId: string) => void
): IDataTableColumn<TemplateResponse>[] => [
    {
      name: t('fileName'),
      selector: (row) => row.template.fileName!,
      minWidth: '35%',
      maxWidth: '35%',
      cell: (row) => {
        return (
          <Flex>
            {!!row.template.fileName ? (
              row.template.fileName
            ) : (
              <BodyTextM
                style={{ fontStyle: 'italic' }}
                color={COLOR.TEXT_PLACEHOLDER}
              >
                {t('noFileUploaded')}
              </BodyTextM>
            )}
          </Flex>
        );
      },
    },
    {
      name: t('uploadedOn'),
      selector: (row) => row.template.uploadedAt!,
      minWidth: '30%',
      maxWidth: '30%',
      cell: (row) => {
        return (
          <Flex>
            {row.template.uploadedAt ? (
              datetimeUtil.dateTimeNumberFormat(
                row.template.uploadedAt,
                DATE_TIME_WITHOUT_SECONDS_FORMAT
              )
            ) : (
              <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER}>--</BodyTextM>
            )}
          </Flex>
        );
      },
    },
    {
      name: t('type'),
      selector: (row) => row.template.type,
      minWidth: '20%',
      maxWidth: '20%',
      cell: (row) => {
        return <Flex>{tagMapping(t, row.template.type)}</Flex>;
      },
    },
    {
      name: '',
      right: true,
      cell: (row) => (
        <Flex w="100%" align="center" justify="flex-start" gap={16}>
          <Tooltip content={t('uploadFile') + ' (.pdf)'} position="top">
            <Button
              small
              intent="primary"
              outlined
              onClick={() => {
                document.getElementById(row.id)?.click();
              }}
            >
              {t('uploadFile')}
            </Button>
          </Tooltip>
          <input
            id={row.id}
            type="file"
            hidden
            accept="application/pdf"
            className="Input__input"
            onInput={() => handleFileChange(row)}
          />
          {row.template.fileName && (
            <Box>
              <Tooltip content={t('removeFileTooltip')} position="top">
                <Svg
                  src={TrashBin}
                  onClick={() => onRemoveFile(row.id)}
                  style={{ cursor: 'pointer' }}
                />
              </Tooltip>
            </Box>
          )}
        </Flex>
      ),
    },
  ];

export const customStyles: IDataTableStyles = {
  cells: {
    style: {
      padding: '4px 16px',
    },
  },
};
