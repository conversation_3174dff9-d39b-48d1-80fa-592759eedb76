import Table from '@tutum/design-system/components/Table';

import {
  TemplateResponse,
  useMutationRemove,
  useMutationUpdate,
  useQueryGetPatientInfoTemplate,
} from '@tutum/hermes/bff/legacy/app_patient_info_template';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { getPresignedURL } from '@tutum/hermes/bff/legacy/app_mvz_file';
import { customStyles, fillPatientInfoTemplateFileName } from './helper';
import {
  LoadingState,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { genColumns } from './helper';

interface Props {
  t: any;
  className?: string;
  bsnrId: string;
}
const PatientInfoTemplate = (props: Props) => {
  const { t, className } = props;

  const { data, refetch, isLoading } = useQueryGetPatientInfoTemplate(
    { bsnrId: props.bsnrId },
    { enabled: !!props.bsnrId }
  );
  const { mutate } = useMutationUpdate({
    onSuccess: () => {
      refetch();
      alertSuccessfully(t('fileUploaded'));
    },
  });
  const { mutate: removeFile } = useMutationRemove({
    onSuccess: () => {
      refetch();
      alertSuccessfully(t('fileRemoved'));
    },
  });

  const handleFileChange = async (row: TemplateResponse) => {
    try {
      const filesElement = document.getElementById(row.id!);
      const files = (filesElement as HTMLInputElement).files;
      if (!files?.[0]?.size) {
        return;
      }
      const fileToUpload = files[0];
      const fileName = fillPatientInfoTemplateFileName(row.id);
      const payload = {
        bucketName: 'bucket-doctor-letter',
        objectName: fileName,
      };
      const { data } = await getPresignedURL(payload);
      if (!data?.presignedURL) throw new Error('Failed to upload');

      const presignedURL = data.presignedURL;
      await fetch(`${presignedURL}`, {
        method: 'PUT',
        body: fileToUpload,
      });

      mutate({
        id: row.id,
        fileName: fileToUpload.name,
        pDFUrl: fileName,
        uploadedAt: datetimeUtil.now(),
      });
    } catch (error) {
      alertError(error.message);
    }
  };

  const onRemoveFile = (rowId: string) => {
    removeFile({ id: rowId });
  };

  if (isLoading) return <LoadingState />;

  return (
    <div className={className}>
      <br />
      <Table
        className="sl-table"
        columns={genColumns(t, handleFileChange, onRemoveFile)}
        data={data?.template || []}
        responsive={false}
        persistTableHead
        customStyles={customStyles}
        fixedHeader
      />
    </div>
  );
};

export default PatientInfoTemplate;
