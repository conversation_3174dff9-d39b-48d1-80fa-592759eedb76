import React, { useState, useEffect, useMemo } from 'react';
import debounce from 'lodash/debounce';
import { Flex, Button, BodyTextM, Svg } from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { InputGroup } from '@tutum/design-system/components/Core';
import {
  genColumns,
  customStyles,
} from '@tutum/admin/module_doctor_letter/template-overview/setting-table';
import {
  useDoctorLetterStore,
  DoctorLetterActions,
} from '@tutum/admin/module_doctor_letter/DoctorLetter.store';
import {
  PlusIconSvgURL,
  SearchIconSvgURL,
} from '@tutum/admin/module_textmodule-overview/TextmoduleOverview.constant';
import {
  PAGE_DEFAULT,
  PAGE_SIZE_DEFAULT,
} from '@tutum/design-system/consts/table';
import { SortType } from '@tutum/admin/module_doctor_letter/DoctorLetter.const';
import { alertError } from '@tutum/design-system/components/Toaster';
import { Order } from '@tutum/hermes/bff/common';
import { HeaderFooter } from '@tutum/hermes/bff/legacy/doctor_letter_common';
import CreateHeaderFooter from '@tutum/admin/module_doctor_letter/template-overview/create-header-footer-dialog/CreateHeaderFooter.styled';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IHeaderFooterProps {
  className?: string;
  t: any;
  bsnrId: string;
}

const HeaderFooterApp = ({ className, t, bsnrId }: IHeaderFooterProps) => {
  const {
    isLoadingHeadFoot,
    listHeaderFooters,
    totalOverview,
    pageSizeOverview,
    isEditHeaderFooter,
    isOpenCreateHeaderFooter,
  } = useDoctorLetterStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [isConfirmDelete, setIsConfirmDelete] = useState({
    isDelete: false,
    templateId: '',
  });
  const [pagination, setPagination] = useState({
    page: PAGE_DEFAULT,
    pageSize: PAGE_SIZE_DEFAULT,
  });

  const [sortField, setSortField] = useState<SortType | ''>('');
  const [sortOrder, setSortOrder] = useState<Order | ''>('');

  useEffect(() => {
    if (!bsnrId) return;
    const { page, pageSize } = pagination;
    DoctorLetterActions.getListHeaderFooters(
      page,
      pageSize,
      sortOrder ? sortOrder : null!,
      sortField.toString(),
      bsnrId,
      searchQuery
    );
  }, [searchQuery, JSON.stringify(pagination), sortField, sortOrder, bsnrId]);

  const onChangeSearchInput = useMemo(
    () =>
      debounce(
        (e) => setSearchQuery((e.target as HTMLInputElement).value),
        400
      ),
    []
  );

  const onCreateHeaderFooter = () => {
    DoctorLetterActions.setIsOpenCreateHeaderFooter(true);
  };

  const onEditHeaderFooter = (template: HeaderFooter) => {
    DoctorLetterActions.setIsEditHeaderFooter(true);
    DoctorLetterActions.setCurrentHeaderFooter(template);
    DoctorLetterActions.setIsOpenCreateHeaderFooter(true);
  };

  const onConfirmDeleteHeaderFooter = (template: HeaderFooter) => {
    setIsConfirmDelete({
      isDelete: true,
      templateId: template.id!,
    });
  };

  const onChangePage = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const onCloseModal = () => {
    if (isEditHeaderFooter) {
      DoctorLetterActions.setIsEditHeaderFooter(false);
    }
    DoctorLetterActions.setCurrentHeaderFooter(null!);
    DoctorLetterActions.setIsOpenCreateHeaderFooter(false);
  };

  const onDeleteHeaderFooter = async (id: string) => {
    setIsConfirmDelete(null!);
    try {
      await DoctorLetterActions.deleteHeaderFooter(id);
      DoctorLetterActions.getListHeaderFooters(
        1,
        PAGE_SIZE_DEFAULT,
        null!,
        null!,
        bsnrId
      );
    } catch (error) {
      alertError(t('deleteFailed') + ':' + error.message);
    }
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination({
      ...pagination,
      pageSize: currentRowsPerPage,
    });
  };

  const onChangeSort = (type: SortType, order: Order) => {
    setSortOrder(order);
    setSortField(type);
  };

  return (
    <div className={className}>
      <Flex p={16} justify="space-between">
        <InputGroup
          type="text"
          className="sl-panel-search-input"
          defaultValue=""
          isNumeric={false}
          placeholder={t('search')}
          leftElement={<Svg src={SearchIconSvgURL} />}
          onChange={onChangeSearchInput}
        />
        <Button
          intent="primary"
          iconOnly
          onClick={onCreateHeaderFooter}
          icon={<Svg src={PlusIconSvgURL} />}
        />
      </Flex>
      <Table
        columns={genColumns({
          t,
          sortType: sortField!,
          onEdit: onEditHeaderFooter,
          onConfirmDelete: onConfirmDeleteHeaderFooter,
          onChangeSort,
          isHeaderFooter: true,
          bsnrId,
        })}
        data={listHeaderFooters}
        customStyles={customStyles}
        highlightOnHover
        noHeader
        persistTableHead
        noDataComponent={
          <BodyTextM color={COLOR.TEXT_PLACEHOLDER} style={{ marginTop: 24 }}>
            {t('noDataheaderFooter')}
          </BodyTextM>
        }
        progressPending={isLoadingHeadFoot}
        keyField="key"
        fixedHeader
        pagination
        paginationServer
        paginationDefaultPage={1}
        paginationResetDefaultPage
        paginationTotalRows={totalOverview}
        paginationPerPage={pageSizeOverview}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
      />

      {isOpenCreateHeaderFooter && (
        <CreateHeaderFooter
          isOpen
          onClose={onCloseModal}
          t={t}
          bsnrId={bsnrId}
        />
      )}

      <DeleteConfirmDialog
        className="confirm-dialog--hide-icon"
        isOpen={!!isConfirmDelete?.isDelete}
        close={() => setIsConfirmDelete(null!)}
        confirm={() => onDeleteHeaderFooter(isConfirmDelete.templateId)}
        text={{
          btnCancel: t('cancelButton'),
          btnOk: t('confirmButton'),
          title: t('title', { value: 'header/Footer' }),
          message: t('message', { value: 'Header/Footer' }),
        }}
      />
    </div>
  );
};

export default HeaderFooterApp;
