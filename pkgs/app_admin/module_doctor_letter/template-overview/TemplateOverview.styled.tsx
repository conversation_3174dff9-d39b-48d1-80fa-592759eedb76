import React from 'react';

import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import Theme from '@tutum/admin/theme';
import OriginalTemplateOverview, {
  ITemplateOverviewProps,
} from './TemplateOverview';
import { scaleSpacePx } from '@tutum/design-system/styles';
import { COLOR } from '@tutum/design-system/themes/styles';

const styled = Theme.styled;

const TemplateOverview: React.ComponentType<ITemplateOverviewProps> = styled(
  OriginalTemplateOverview
).attrs(({ className }) => ({
  className: getCssClass('sl-TemplateOverview', className),
}))`
  width: 100%;
  
  .rdt_Table {
    height: calc(100vh - 279px);
    overflow: auto;
  }

  .rdt_TableCell:last-child {
    justify-content: center;
  }

  .rdt_TableRow {
    min-height: auto;
    &:hover {
      border-bottom-color: ${COLOR.BACKGROUND_TERTIARY_DIM};
    }
  }

  .sl-panel-search-input {
    display: flex;
    align-items: center;
    min-width: 210px;
    height: 32px;

    input.bp5-input {
      padding-left: 32px !important;
    }

    .bp5-input-left-container {
      top: ${scaleSpacePx(2)};
      left: ${scaleSpacePx(2)};
    }

    .bp5-input {
      color: ${COLOR.TEXT_PRIMARY_BLACK};
    }
  }

  .sl-more-icon {
    cursor: pointer;
  }
`;

export default TemplateOverview;
