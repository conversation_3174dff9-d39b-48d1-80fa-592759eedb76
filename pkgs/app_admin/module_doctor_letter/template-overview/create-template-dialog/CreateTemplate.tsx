import React, { useEffect, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import { Formik, Form, Field } from 'formik';

import type { LetterTemplate } from '@tutum/hermes/bff/doctor_letter_common';
import type DoctorLetterI18n from '@tutum/admin/locales/en/DoctorLetter.json';
import type CommonLocales from '@tutum/admin/locales/en/Common.json';

import {
  DoctorLetterDialog,
  DoctorLetter,
} from '@tutum/design-system/lexical/components/doctor-letter';
import {
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components/Toaster';
import {
  Flex,
  Button,
  LeaveConfirmModal,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import {
  createTemplate,
  editTemplate,
} from '@tutum/hermes/bff/legacy/app_doctor_letter';
import {
  DoctorLetterActions,
  useDoctorLetterStore,
} from '@tutum/admin/module_doctor_letter/DoctorLetter.store';
import {
  FIELD_NAME,
  INITIAL_VALUE,
} from '@tutum/admin/module_doctor_letter/DoctorLetter.const';
import {
  getTemplateInUse,
  getTypeOfLetterTemplate,
  onValidateTemplateForm,
} from '@tutum/admin/module_doctor_letter/DoctorLetter.helper';
import SidebarTemplate from './sidebar/SidebarTemplate.styled';
import { MultipleEditorStorePlugin } from '@tutum/mvz/module_doctor-letter/lexical/MultiplEditorStore.plugin';
import { DEFAULT_EDITOR_ID } from '@tutum/mvz/module_doctor-letter/lexical/EditorProvider';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { useQueryGetPrivateBillingSetting } from '@tutum/hermes/bff/legacy/private_billing_setting';
import { LetterTemplateType } from '../../DoctorLetter.type';

export interface ICreateTemplateProps {
  className?: string;
  onClose: () => void;
  isOpen: boolean;
  refetch: () => void;
  bsnrId: string;
}

const CreateTemplate = ({
  className,
  isOpen,
  onClose,
  refetch,
  bsnrId,
}: ICreateTemplateProps) => {
  const { t: tCreateTemplate } = I18n.useTranslation<
    keyof typeof DoctorLetterI18n.CreateTemplate
  >({
    namespace: 'DoctorLetter',
    nestedTrans: 'CreateTemplate',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: tFormValidation } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const { isOpenCreateTemplate, isEditTemplate, currentTemplate } =
    useDoctorLetterStore();

  const privateBillingSetting = useQueryGetPrivateBillingSetting({});

  const [isLeaving, setLeaving] = useState(false);

  const initialValue = isEditTemplate ? currentTemplate : INITIAL_VALUE;

  useEffect(() => {
    DoctorLetterActions.getListVariable();
  }, []);

  const onCloseDialog = () => {
    onClose();
  };

  const onCreate = async (values: LetterTemplate, bsnrId: string) => {
    const modifiedName = values.name.replaceAll(' ', '_');
    const payload = {
      letterTemplate: { ...values, name: modifiedName },
      bsnrId,
    };

    createTemplate(payload)
      .then(() => {
        alertSuccessfully(tCreateTemplate('success'));
        refetch();
        onCloseDialog();
      })
      .catch((error) => {
        const { data } = error?.response;
        let errorMessage = error.message;
        if (data?.serverError === ErrorCode.ErrorCode_Template_Is_Exist) {
          errorMessage = tCreateTemplate('nameAlready');
        }
        alertError(tCreateTemplate('failed') + ': ' + errorMessage);
      });
  };

  const onEdit = async (values: LetterTemplate) => {
    const templateInUse = getTemplateInUse(privateBillingSetting.data);
    if (
      getTypeOfLetterTemplate(values.type) !== LetterTemplateType.Private &&
      values.id &&
      templateInUse.includes(values['id'])
    ) {
      return alertError(tCreateTemplate('privateTemplateInUse'));
    }

    if (!values.id) {
      console.error('payload.id is required');
      return;
    }
    const payload = {
      id: values.id,
      letterTemplate: { ...values },
    };

    editTemplate(payload)
      .then(() => {
        alertSuccessfully(tCreateTemplate('success'));
        refetch();
        onCloseDialog();
      })
      .catch((error) => {
        console.error(error);
        alertError(
          tCreateTemplate('failed') + ':' + tCreateTemplate('nameAlready')
        );
      });
  };

  if (!isOpen) return null;

  return (
    <>
      <Formik<LetterTemplate>
        initialValues={initialValue}
        onSubmit={async (v) => {
          if (isEditTemplate) {
            await onEdit(v);
          } else {
            await onCreate(v, bsnrId);
          }
          // NOTE: dont need to reset form since we prevent this component from mounting if isOpen = false
          // f.resetForm();
        }}
        validate={onValidateTemplateForm({ t: tFormValidation })}
        enableReinitialize
      >
        {(props) => (
          <Form>
            <DoctorLetterDialog
              title={
                isEditTemplate
                  ? tCreateTemplate('titleEdit', {
                    value: tCreateTemplate('template'),
                  })
                  : tCreateTemplate('title', {
                    value: tCreateTemplate('template'),
                  })
              }
              isOpen={isOpenCreateTemplate}
              canEscapeKeyClose={false}
              className={className}
              onClose={() => {
                setLeaving(true);
                props.resetForm();
              }}
              sidebar={<SidebarTemplate {...props} t={tCreateTemplate} />}
              actions={
                <Flex>
                  <Button
                    large
                    outlined
                    minimal
                    intent="primary"
                    onClick={() => setLeaving(true)}
                    disabled={props.isSubmitting}
                    loading={props.isSubmitting}
                  >
                    {tButtonActions('cancelText')}
                  </Button>
                  <Button
                    large
                    type="submit"
                    intent="primary"
                    onClick={() => props.submitForm()}
                    disabled={props.isSubmitting || !isEmpty(props.errors)}
                    loading={props.isSubmitting}
                  >
                    {isEditTemplate
                      ? tButtonActions('saveText')
                      : tButtonActions('create')}
                  </Button>
                </Flex>
              }
            >
              <Field name={FIELD_NAME.BODY}>
                {({ form }) => (
                  <DoctorLetter.Editor
                    initFromAdmin={true}
                    initTemplateData={initialValue}
                    onChange={(body, template) => {
                      form.setFieldValue(FIELD_NAME.BODY, body);
                      form.setFieldValue(
                        FIELD_NAME.VARIABLES,
                        template?.variables ?? []
                      );
                    }}
                  >
                    <MultipleEditorStorePlugin id={DEFAULT_EDITOR_ID} />
                  </DoctorLetter.Editor>
                )}
              </Field>
            </DoctorLetterDialog>
          </Form>
        )}
      </Formik>

      <LeaveConfirmModal
        isOpen={isLeaving}
        onConfirm={onCloseDialog}
        onClose={() => setLeaving(false)}
      />
    </>
  );
};

export default CreateTemplate;
