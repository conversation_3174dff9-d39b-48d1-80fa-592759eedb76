import React, { useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import { Formik, Form, Field } from 'formik';
import { styled } from '@tutum/design-system/themes';

import type CommonLocales from '@tutum/admin/locales/en/Common.json';
import type DoctorLetterI18n from '@tutum/admin/locales/en/DoctorLetter.json';

import {
  Flex,
  Button,
  LeaveConfirmModal,
} from '@tutum/design-system/components';
import { Divider } from '@tutum/design-system/components/Core';
import TabsList from '@tutum/design-system/components/TabsList/TabsList.styled';
import { HeaderFooterDialog } from '@tutum/admin/module_doctor_letter/template-overview/doctor-letter-dialog/HeaderFooter.dialog';
import {
  useDoctorLetterStore,
  DoctorLetterActions,
} from '@tutum/admin/module_doctor_letter/DoctorLetter.store';
import {
  updateHeaderFooter,
  createHeaderFooter,
} from '@tutum/hermes/bff/legacy/app_doctor_letter';
import { PAGE_SIZE_DEFAULT } from '@tutum/design-system/consts/table';
import {
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components/Toaster';
import { DefaultValuePlugin } from '@tutum/design-system/lexical/components/doctor-letter/DefaultValue.plugin';
import {
  FIELD_NAME,
  INITIAL_VALUE_HEADER_FOOTER,
  ERROR,
} from '@tutum/admin/module_doctor_letter/DoctorLetter.const';
import I18n from '@tutum/infrastructure/i18n';
import type { HeaderFooter } from '@tutum/hermes/bff/doctor_letter_common';
import SidebarHeaderFooter from './sidebar/SidebarHeaderFooter.styled';
import { onValidateHeaderFooterForm } from '../../DoctorLetter.helper';
import {
  FooterEditorContext,
  HeaderEditorContext,
  HeaderFooterEditor,
} from '@tutum/design-system/lexical/components/doctor-letter/HeaderFooter.component';

export interface ICreateHeaderFooterProps {
  className?: string;
  onClose: () => void;
  isOpen: boolean;
  t?: any;
  bsnrId: string;
}

const EditorWrapper = styled.div<any>`
  width: 100%;
  height: 560px;
  position: absolute;
  top: 41px;
  left: ${(props) => (props.showHeader ? '0px' : '99999px')};
`;

const CreateHeaderFooter = ({
  className,
  isOpen,
  onClose,
  bsnrId,
}: ICreateHeaderFooterProps) => {
  const { isOpenCreateHeaderFooter, isEditHeaderFooter, currentHeaderFooter } =
    useDoctorLetterStore();
  const { t: tCreateTemplate } = I18n.useTranslation<
    keyof typeof DoctorLetterI18n.CreateTemplate
  >({
    namespace: 'DoctorLetter',
    nestedTrans: 'CreateTemplate',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: tFormValidation } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const [isLeaving, setLeaving] = useState(false);

  const initialValue = isEditHeaderFooter
    ? currentHeaderFooter
    : INITIAL_VALUE_HEADER_FOOTER;

  const tabsList = [
    {
      label: tCreateTemplate('HeaderTab'),
      value: 'Header',
    },
    {
      label: tCreateTemplate('FooterTab'),
      value: 'Footer',
    },
  ];

  const [selectedTab, setSelectedTab] = useState<(typeof tabsList)[0]>(
    tabsList[0]
  );

  const onCloseDialog = () => {
    onClose();
  };

  const onCreate = async (values: HeaderFooter, bsnrId: string) => {
    const modifiedName = values.name.replaceAll(' ', '_');
    const payload = {
      ...values,
      name: modifiedName,
      bsnrId,
    };
    createHeaderFooter(payload)
      .then(() => {
        alertSuccessfully(tCreateTemplate('success'));
        DoctorLetterActions.getListHeaderFooters(
          1,
          PAGE_SIZE_DEFAULT,
          null,
          null,
          bsnrId
        );
        onCloseDialog();
      })
      .catch((error) => {
        const { data } = error?.response;
        let errorMessage = error.message;
        if (data?.serverError === ERROR.NAME_EXISTS) {
          errorMessage = tCreateTemplate('nameAlready');
        }
        alertError(tCreateTemplate('failed') + ': ' + errorMessage);
      })
      .finally(() => {
        DoctorLetterActions.setIsLoadingHeaderFooter(false);
      });
  };

  const onEdit = async (values: HeaderFooter, bsnrId: string) => {
    const modifiedName = values.name.replaceAll(' ', '_');
    const payload = {
      ...values,
      id: values.id,
      name: modifiedName,
    };
    try {
      await updateHeaderFooter(payload);
      alertSuccessfully(tCreateTemplate('success'));
      DoctorLetterActions.getListHeaderFooters(
        1,
        PAGE_SIZE_DEFAULT,
        null,
        null,
        bsnrId
      );
      onCloseDialog();
    } catch (error) {
      const { data } = error;
      let errorMessage = error.message;
      if (data?.serverError === ERROR.NAME_EXISTS) {
        errorMessage = tCreateTemplate('nameAlready');
      }
      alertError(tCreateTemplate('failed') + ': ' + errorMessage);
    } finally {
      DoctorLetterActions.setIsLoadingHeaderFooter(false);
    }
  };

  const showHeader = selectedTab.value === 'Header';

  if (!isOpen) return null;

  return (
    <>
      <Formik<HeaderFooter>
        initialValues={initialValue}
        onSubmit={async (v) => {
          if (isEditHeaderFooter) {
            await onEdit(v, bsnrId);
          } else {
            await onCreate(v, bsnrId);
          }
        }}
        validate={onValidateHeaderFooterForm({ t: tFormValidation })}
        enableReinitialize
      >
        {(props) => (
          <Form>
            <HeaderFooterDialog
              className={className}
              title={
                isEditHeaderFooter
                  ? tCreateTemplate('titleEdit', {
                    value: tCreateTemplate('header/footer'),
                  })
                  : tCreateTemplate('title', {
                    value: tCreateTemplate('header/footer'),
                  })
              }
              isOpen={isOpenCreateHeaderFooter}
              canEscapeKeyClose={false}
              onClose={() => {
                setLeaving(true);
                props.resetForm();
              }}
              canOutsideClickClose={false}
              sidebar={<SidebarHeaderFooter {...props} t={tCreateTemplate} />}
              actions={
                <Flex>
                  <Button
                    large
                    outlined
                    minimal
                    intent="primary"
                    onClick={() => setLeaving(true)}
                    disabled={props.isSubmitting}
                    loading={props.isSubmitting}
                  >
                    {tButtonActions('cancelText')}
                  </Button>
                  <Button
                    large
                    type="submit"
                    intent="primary"
                    onClick={() => props.submitForm()}
                    disabled={props.isSubmitting || !isEmpty(props.errors)}
                    loading={props.isSubmitting}
                  >
                    {isEditHeaderFooter
                      ? tButtonActions('saveText')
                      : tButtonActions('create')}
                  </Button>
                </Flex>
              }
            >
              <Field name={FIELD_NAME.BODY}>
                {({ form }) => (
                  <Flex className="body-content">
                    <Flex>
                      <TabsList
                        tabsList={tabsList}
                        selectedTab={selectedTab.value}
                        setSelectedTab={(tab) => setSelectedTab(tab)}
                      />
                    </Flex>
                    <Divider style={{ margin: 0 }} />
                    <EditorWrapper showHeader={showHeader}>
                      <HeaderEditorContext>
                        <HeaderFooterEditor
                          type={FIELD_NAME.BODYHEADER}
                          onChange={(payload) =>
                            form.setFieldValue(FIELD_NAME.BODYHEADER, payload)
                          }
                        >
                          <DefaultValuePlugin
                            initialData={initialValue}
                            getEditorState={(data) => data.bodyHeader?.content}
                          />
                        </HeaderFooterEditor>
                      </HeaderEditorContext>
                    </EditorWrapper>
                    <EditorWrapper showHeader={!showHeader}>
                      <FooterEditorContext>
                        <HeaderFooterEditor
                          type={FIELD_NAME.BODYFOOTER}
                          onChange={(payload) =>
                            form.setFieldValue(FIELD_NAME.BODYFOOTER, payload)
                          }
                        >
                          <DefaultValuePlugin
                            initialData={initialValue}
                            getEditorState={(data) => data.bodyFooter?.content}
                          />
                        </HeaderFooterEditor>
                      </FooterEditorContext>
                    </EditorWrapper>
                  </Flex>
                )}
              </Field>
            </HeaderFooterDialog>
          </Form>
        )}
      </Formik>

      <LeaveConfirmModal
        isOpen={isLeaving}
        onConfirm={onCloseDialog}
        onClose={() => setLeaving(false)}
      />
    </>
  );
};

export default CreateHeaderFooter;
