import React from 'react';
import moment from 'moment';

import type DoctorLetterI18n from '@tutum/admin/locales/en/DoctorLetter.json';
import type { LetterTemplate } from '@tutum/hermes/bff/doctor_letter_common';

import {
  IDataTableColumn,
  IDataTableStyles,
} from '@tutum/design-system/components/Table';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import {
  BodyTextM,
  Flex,
  Svg,
  SortComponent,
  Tag,
} from '@tutum/design-system/components';
import { MenuPopover } from '@tutum/design-system/components/MenuPopover';
import { Order } from '@tutum/hermes/bff/common';
import { SortType } from '../../DoctorLetter.const';
import { getTypeOfLetterTemplate } from '../../DoctorLetter.helper';
import { COLOR } from '@tutum/design-system/themes/styles';
import { LetterTemplateType } from '../../DoctorLetter.type';

const MoreIcon = '/images/more-vertical.svg';
const EditIcon = '/images/edit-2.svg';
const DuplicateIcon = '/images/duplicate.svg';
const RemoveIcon = '/images/trash-bin-red.svg';

interface IGenColumnsParams {
  t: IFixedNamespaceTFunction<keyof typeof DoctorLetterI18n.TemplateOverview>;
  sortType: SortType | '';
  onEdit: (template: any) => void;
  onConfirmDelete: (template: any) => void;
  onChangeSort: (sortType: SortType, sortOrder: Order) => void;
  isHeaderFooter?: boolean;
  onDuplicate?: (template: LetterTemplate, bsnrId: string) => void;
  bsnrId: string;
}

export const genColumns = ({
  t,
  sortType,
  onEdit,
  onConfirmDelete,
  onChangeSort,
  isHeaderFooter,
  onDuplicate,
  bsnrId,
}: IGenColumnsParams): IDataTableColumn<any>[] => {
  return [
    {
      id: 'name',
      name: (
        <Flex justify="space-between" align="center">
          {t('name')}
          {isHeaderFooter ? (
            <SortComponent
              isSorted={sortType === SortType.NAME}
              onChangeSortOrder={(sortOder: Order) =>
                onChangeSort(SortType.NAME, sortOder)
              }
            />
          ) : null}
        </Flex>
      ),
      selector: (row) => row.name,
    },
    {
      id: 'lastUpdated',
      name: (
        <Flex justify="space-between" align="center">
          {t('lastUpdated')}
          <SortComponent
            isSorted={
              isHeaderFooter
                ? sortType === SortType.UPDATED_AT
                : sortType === SortType.LAST_UPDATED
            }
            onChangeSortOrder={(sortOder: Order) =>
              onChangeSort(
                isHeaderFooter ? SortType.UPDATED_AT : SortType.LAST_UPDATED,
                sortOder
              )
            }
          />
        </Flex>
      ),
      cell: (row) => {
        return (
          <Flex column>
            <BodyTextM id={`lastUpdated${row?.updatedAt}`}>
              {row?.updatedAt &&
                moment.unix(row.updatedAt / 1000).format('DD.MM.YYYY HH:mm')}
            </BodyTextM>
          </Flex>
        );
      },
    },
    {
      id: 'type',
      width: '300px',
      name: (
        <Flex justify="space-between" align="center">
          {t('type')}
        </Flex>
      ),
      cell: (row) => {
        const letterType = getTypeOfLetterTemplate(row.type);
        const isTag = [
          LetterTemplateType.Private,
          LetterTemplateType.Bg,
          LetterTemplateType.Eab,
        ].includes(letterType);

        return (
          <Flex column>
            <BodyTextM id={`lastUpdated${row?.updatedAt}`}>
              {isTag && (
                <Tag
                  slStyle="fill"
                  slColor={COLOR.TAG_BACKGROUND_PURPLE_SUBTLE}
                >
                  <BodyTextM fontWeight={600} color={COLOR.TAG_CONTENT_PURPLE}>
                    {t(getTypeOfLetterTemplate(row.type))}
                  </BodyTextM>
                </Tag>
              )}
            </BodyTextM>
          </Flex>
        );
      },
    },
    {
      name: '',
      width: '48px',
      right: true,
      cell: (row) => {
        const isEabTemplate =
          getTypeOfLetterTemplate(row.type) === LetterTemplateType.Eab;
        const options: any[] = [
          {
            label: t('edit'),
            icon: EditIcon,
            onClick: () => onEdit(row),
          },
        ];
        if (!isEabTemplate) {
          options.push({
            label: (
              <BodyTextM color={COLOR.TAG_BACKGROUND_RED}>
                {t('remove')}
              </BodyTextM>
            ),
            icon: RemoveIcon,
            className: 'sl-danger-text',
            onClick: () => onConfirmDelete(row),
          });
          if (!isHeaderFooter) {
            options.push({
              label: t('duplicate'),
              icon: DuplicateIcon,
              onClick: () => onDuplicate?.(row, bsnrId),
            });
          }
        }
        return (
          <MenuPopover options={options}>
            <Svg className="sl-more-icon" src={MoreIcon} />
          </MenuPopover>
        );
      },
    },
  ];
};
export const customStyles: IDataTableStyles = {
  rows: {
    style: {
      paddingLeft: '8px',
      border: 'unset',
    },
  },
  headRow: {
    style: {
      paddingLeft: '8px',
      textTransform: 'uppercase',
      border: 'none',
    },
  },
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
    },
  },
  cells: {
    style: {
      padding: '8px',
      alignItems: 'center',
    },
  },
};
