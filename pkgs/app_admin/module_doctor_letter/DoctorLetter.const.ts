import type Doctor<PERSON>etterI18n from '@tutum/admin/locales/en/DoctorLetter.json';
import { TemplateType } from '@tutum/hermes/bff/doctor_letter_common';

import {
  Category,
  CategoryItemName,
  type Variable,
  type BodyHeader,
  type BodyFooter,
} from '@tutum/hermes/bff/legacy/doctor_letter_common';

export enum FIELD_NAME {
  NAME = 'name',
  BODY = 'body',
  VARIABLES = 'variables',
  HEADERFOOTER_ID = 'headerFooterID',
  LAST_UPDATED = 'lastUpdated',
  BODYFOOTER = 'bodyFooter',
  BODYHEADER = 'bodyHeader',
  ISDELETED = 'isDeleted',
  UPDATED_AT = 'updatedAt',
  TYPE = 'type',
}

export enum SortType {
  NAME = FIELD_NAME.NAME,
  LAST_UPDATED = FIELD_NAME.LAST_UPDATED,
  UPDATED_AT = FIELD_NAME.UPDATED_AT,
}

export interface IFormValue {
  [FIELD_NAME.NAME]: string;
  [FIELD_NAME.BODY]: string;
  [FIELD_NAME.VARIABLES]: Variable[];
  [FIELD_NAME.HEADERFOOTER_ID]?: string;
  [FIELD_NAME.TYPE]: TemplateType;
}

export interface IFormHeaderFooterValue {
  [FIELD_NAME.NAME]: string;
  [FIELD_NAME.BODYFOOTER]: BodyFooter | null;
  [FIELD_NAME.BODYHEADER]: BodyHeader | null;
  [FIELD_NAME.ISDELETED]: boolean;
}

export const INITIAL_VALUE: IFormValue = {
  [FIELD_NAME.NAME]: '',
  [FIELD_NAME.BODY]: '',
  [FIELD_NAME.VARIABLES]: [],
  [FIELD_NAME.HEADERFOOTER_ID]: undefined,
  [FIELD_NAME.TYPE]: TemplateType.TemplateType_DoctorLetter,
};

export const INITIAL_VALUE_HEADER_FOOTER: IFormHeaderFooterValue = {
  [FIELD_NAME.NAME]: '',
  [FIELD_NAME.BODYFOOTER]: null,
  [FIELD_NAME.BODYHEADER]: null,
  [FIELD_NAME.ISDELETED]: false,
};

export const CATEGORY_MAPPING: Record<
  Category,
  keyof typeof DoctorLetterI18n.CreateTemplate
> = {
  [Category.General]: 'general',
  [Category.BSNR]: 'bsnrInfo',
  [Category.DoctorInformation]: 'doctorInfo',
  [Category.PatientInformation]: 'patientInfo',
  [Category.EmployerInformation]: 'employerInfo',
  [Category.InsuranceInformation]: 'insuranceInfo',
  [Category.VitalParamenter]: 'vitalParameter',
  [Category.MedicalDocumentation]: 'medicalDocumentation',
  [Category.Invoice]: 'invoice',
  [Category.SDAV]: 'SDAV',
  [Category.BG]: 'BG',
};

export const ERROR = {
  NAME_EXISTS: 'ErrorCode_HeaderFooter_Is_Exist',
};

export const CATEGORY_ITEM_MAPPING: Record<
  CategoryItemName,
  keyof typeof DoctorLetterI18n.CreateTemplate
> = {
  [CategoryItemName.General_CurentDate]: 'currentDate',
  [CategoryItemName.General_ReceiverSalutation]: 'receiverSalutation',
  [CategoryItemName.General_PracticeEmail]: 'practiceEmail',
  [CategoryItemName.General_PracticePhone]: 'practicePhone',
  [CategoryItemName.General_PracticeFax]: 'practiceFax',
  [CategoryItemName.General_PracticePostalCodeCity]: 'practicePostalCodeCity',
  [CategoryItemName.General_PracticeStreetNo]: 'practiceStreetNo',
  [CategoryItemName.General_Sender_BSNR_LANR]: 'senderBsnrLanr',
  [CategoryItemName.General_ReceiverMail]: 'receiverEmail',
  [CategoryItemName.General_ReceiverName]: 'receiverName',
  [CategoryItemName.General_ReceiverStreetNo]: 'receiverStreetNo',
  [CategoryItemName.General_ReceiverPostalCodeCity]: 'receiverPostalCodeCity',
  [CategoryItemName.General_ReceiverPhone]: 'receiverPhone',
  [CategoryItemName.General_SenderName]: 'senderName',
  [CategoryItemName.BSNR_PracticeName]: 'practiceName',
  [CategoryItemName.BSNR_BSNR]: 'bsnr',
  // [CategoryItemName.BSNR_PostalCodeCity]: 'postalCodeCity',
  // [CategoryItemName.BSNR_StreetNumber]: 'bsnrStreetAndNo',
  // [CategoryItemName.BSNR_Phone]: 'bsnrPhone',
  // [CategoryItemName.BSNR_Fax]: 'bsnrFax',
  // [CategoryItemName.BSNR_Email]: 'bsnrEmail',
  [CategoryItemName.BSNR_BankName]: 'bsnrBankName',
  [CategoryItemName.BSNR_Stamp]: 'bsnrStamp',

  [CategoryItemName.DoctorInformation_Name]: 'doctorInfoName',
  [CategoryItemName.DoctorInformation_LANR]: 'doctorInfoLanr',
  [CategoryItemName.DoctorInformation_Stamp]: 'doctorInfoStamp',

  [CategoryItemName.PatientInformation_Title]: 'patientInfoTitle',
  [CategoryItemName.PatientInformation_FirstName]: 'patientInfoFirstName',
  [CategoryItemName.PatientInformation_AdditionalName]: 'patientInfoAddName',
  [CategoryItemName.PatientInformation_IntentWord]: 'patientInfoIntendWord',
  [CategoryItemName.PatientInformation_Dob]: 'patientInfoDob',
  [CategoryItemName.PatientInformation_LastName]: 'patientInfoLastname',
  [CategoryItemName.PatientInformation_Gender]: 'patientInfoGender',
  [CategoryItemName.PatientInformation_ContactNumerLandline]:
    'patientInfoLandline',
  [CategoryItemName.PatientInformation_ContactNumerMobile]: 'patientInfoMobile',
  [CategoryItemName.PatientInformation_Email]: 'patientInfoEmail',
  [CategoryItemName.MedicalDocumentation_GdtImported]: 'gdtImported',
  [CategoryItemName.PatientInformation_StreetNumber]: 'patientInfoStreetNumber',
  [CategoryItemName.PatientInformation_PostalCodeCity]:
    'patientInfoPostalCodeCity',
  [CategoryItemName.PatientInformation_AdditionalAdress]:
    'patientInfoAdditionalAddress',
  [CategoryItemName.PatientInformation_InsuranceNumber]:
    'patientInfoInsuranceNumber',
  [CategoryItemName.PatientInformation_JobDescription]:
    'patientInfoJobDescription',

  [CategoryItemName.InsuranceInformation_InsuranceName]: 'insuranceInfoName',
  [CategoryItemName.InsuranceInformation_StreetNumber]: 'insuranceInfoStreetNo',
  [CategoryItemName.InsuranceInformation_PostalCodeCity]:
    'insuranceInfoPostalCodeCity',
  [CategoryItemName.InsuranceInformation_Ik]: 'insuranceInfoIk',
  [CategoryItemName.InsuranceInformation_InsuranceStatus]: 'insuranceInfoType',

  [CategoryItemName.EmployerInformation_EmployerName]: 'employerInfo',
  [CategoryItemName.EmployerInformation_StreetNumber]: 'employerStreetNo',
  [CategoryItemName.EmployerInformation_PostalCodeCity]:
    'employerPostalCodeCity',

  [CategoryItemName.MedicalDocumentation_Anamnesis]: 'medicalInfoAnamnesis',
  [CategoryItemName.MedicalDocumentation_AnamnesticDiagnosis]:
    'medicalInfoAnamnesisDiagnosis',
  [CategoryItemName.MedicalDocumentation_Findings]: 'medicalInfoFindings',
  [CategoryItemName.MedicalDocumentation_AcuteDiagnosis]:
    'medicalInfoAcuteDiagnosis',
  [CategoryItemName.MedicalDocumentation_PermanentDiagnosis]:
    'medicalInfoPermanentDiagnosis',
  [CategoryItemName.MedicalDocumentation_PrescribedMedication]:
    'medicalInfoPrescribedMedication',
  [CategoryItemName.MedicalDocumentation_Therapy]: 'medicalInfoTherapy',
  [CategoryItemName.MedicalDocumentation_Note]: 'medicalInfoNote',
  [CategoryItemName.MedicalDocumentation_LabParameter]: 'medicalInfoLab',

  [CategoryItemName.VitalParamenter_Cave]: 'vitalCave',
  [CategoryItemName.VitalParamenter_Allergies]: 'vitalAllergies',
  [CategoryItemName.VitalParamenter_BMI]: 'vitalBmi',
  [CategoryItemName.VitalParamenter_BloodPressure]: 'vitalBloodPressure',
  [CategoryItemName.VitalParamenter_Weight]: 'vitalWeight',
  [CategoryItemName.VitalParamenter_Height]: 'vitalHeight',

  [CategoryItemName.MedicalDocumentation_GoaNumber]: 'medicalInfoGoaNumber',
  [CategoryItemName.MedicalDocumentation_GoaDescription]:
    'medicalInfoGoaDescription',
  [CategoryItemName.MedicalDocumentation_GoaDocumentDate]:
    'medicalInfoGoaDocumentDate',
  [CategoryItemName.MedicalDocumentation_GoaQuantity]: 'medicalInfoGoaQuantity',
  [CategoryItemName.MedicalDocumentation_GoaFactor]: 'medicalInfoGoaFactor',
  [CategoryItemName.MedicalDocumentation_GoaPrice]: 'medicalInfoGoaPrice',
  [CategoryItemName.MedicalDocumentation_MaterialCostName]:
    'medicalInfoMaterialCostName',
  [CategoryItemName.MedicalDocumentation_MaterialCostPrice]:
    'medicalInfoMaterialCostPrice',

  [CategoryItemName.Invoice_Date]: 'invoiceDate',
  [CategoryItemName.Invoice_Number]: 'invoiceNumber',
  [CategoryItemName.Invoice_SubTotal]: 'invoiceSubTotal',
  [CategoryItemName.Invoice_Discount]: 'invoiceDiscount',
  [CategoryItemName.Invoice_ValueAddTax]: 'invoiceValueAddTax',
  [CategoryItemName.Invoice_Amount]: 'invoiceAmount',
  [CategoryItemName.Invoice_OpenAmount]: 'invoiceOpenAmount',
  [CategoryItemName.Invoice_ReminderFee]: 'invoiceReminderFee',
  [CategoryItemName.Invoice_Discount_Unit]: 'invoiceDiscountUnit',

  [CategoryItemName.DoctorInformation_BankAccount]: 'doctorBankAccount',
  [CategoryItemName.DoctorInformation_Bank]: 'doctorBank',
  [CategoryItemName.DoctorInformation_IBAN]: 'doctorIban',
  [CategoryItemName.DoctorInformation_BIC]: 'doctorBic',

  [CategoryItemName.General_Counter]: 'generalCounter',
  [CategoryItemName.General_Day]: 'generalDay',
  [CategoryItemName.General_Month]: 'generalMonth',
  [CategoryItemName.General_Year]: 'generalYear',

  [CategoryItemName.PatientInformation_PatientID]: 'patientId',
  [CategoryItemName.MedicalDocumentation_GoaTable]: 'medicalInfoGoaTable',
  [CategoryItemName.MedicalDocumentation_UvGoaTable]: 'medicalInfoUvGoaTable',
  [CategoryItemName.PatientInformation_Patient]: 'patientPatientin',
  [CategoryItemName.PatientInformation_Salutation]: 'patientHerrFrau',
  [CategoryItemName.PatientInformation_SalutationAkkusativ]: 'patientHerrnFrau',
  [CategoryItemName.Invoice_ReminderHeading]: 'invoiceReminderHeading',

  [CategoryItemName.BG_CostUnit_Name]: 'bgCostUnitName',
  [CategoryItemName.BG_CostUnit_Street]: 'bgCostUnitStreet',
  [CategoryItemName.BG_CostUnit_HouseNumber]: 'bgCostUnitHouseNumber',
  [CategoryItemName.BG_CostUnit_City]: 'bgCostUnitCity',
  [CategoryItemName.BG_CostUnit_PostalCode]: 'bgCostUnitPostalCode',
  [CategoryItemName.BG_Accidentday]: 'bgAccidentday',
  [CategoryItemName.MedicalDocumentation_SpecialCost]: 'specialCost',

  [CategoryItemName.MedicalDocumentation_UvGoaNumber]: 'medicalInfoUvGoaNumber',
  [CategoryItemName.MedicalDocumentation_UvGoaDescription]:
    'medicalInfoUvGoaDescription',
  [CategoryItemName.MedicalDocumentation_UvGoaQuantity]:
    'medicalInfoUvGoaQuantity',
  [CategoryItemName.MedicalDocumentation_UvGoaMaterial]:
    'medicalInfoUvGoaMaterial',
  [CategoryItemName.MedicalDocumentation_UvGoaPrice]: 'medicalInfoUvGoaPrice',
};
