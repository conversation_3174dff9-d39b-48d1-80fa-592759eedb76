import React, { memo, useEffect, useRef, useState } from 'react';
import { withRout<PERSON>, NextRouter } from 'next/router';
import { Button, OverlayToaster } from '@tutum/design-system/components/Core';
import { WEB_SOCKET_PAIRING_TIMEOUT } from '@tutum/hermes/websocket';
import { Flex, BodyTextM, alertError } from '@tutum/design-system/components';
import Theme, { IAdminTheme } from '@tutum/admin/theme';
import { COLOR } from '@tutum/design-system/themes/styles';
// import SysadminTrustedDeviceService from './SysadminTrustedDevice.service';
// import { ROUTING } from '@tutum/admin/types/route.type';
// import GlobalContext from '@tutum/admin/contexts/Global.context';

export interface ISysadminPairingFormProps {
  className?: string;
  theme?: IAdminTheme;
}

let timeout: any;

function SysadminPairingForm(
  props: ISysadminPairingFormProps & { router: NextRouter }
): React.FunctionComponentElement<ISysadminPairingFormProps> {
  const [loading, setLoading] = useState(false);
  // const [yourCode, setYourCode] = useState();
  // SysadminTrustedDeviceService.getPhraseCode()
  const [errorMessage, setErrorMessage] = useState('');
  const toasterRef = useRef<OverlayToaster>(null);
  const codeInputRef = useRef<HTMLInputElement>(null);
  // const { reloadUserProfile } = GlobalContext.useContext();

  /////////////////////////////////////////////////////////////////////////////////////
  useEffect(() => {
    if (loading) {
      timeout = setTimeout(() => {
        setLoading(false);
        clearTimeout(timeout);
        // SysadminTrustedDeviceService.closeWS();
        alertError('Something happened while pairing. Please try again.');
      }, WEB_SOCKET_PAIRING_TIMEOUT);
    }

    return () => clearTimeout(timeout);
  }, [loading]);

  /////////////////////////////////////////////////////////////////////////////////////

  const onTrustDevice = () => {
    const code = codeInputRef.current?.value.trim();
    if (!code) {
      setErrorMessage('Code from practice admin is required.');
      return;
    }

    setLoading(true);

    // return SysadminTrustedDeviceService.onAdminTrustDevice(
    //   yourCode + code,
    //   async () => {
    //     await reloadUserProfile();
    //     await props.router.push(ROUTING.EMPLOYEES);
    //   },
    //   (_, error) => {
    //     if (error) {
    //       setLoading(false);
    //       SysadminTrustedDeviceService.closeWS();
    //       toasterRef.current.show({
    //         message: 'Oops!! Server error. Please try again.',
    //         intent: 'danger',
    //         icon: 'error',
    //       });
    //       console.error(error);
    //     }
    //   }
    // );
  };

  /////////////////////////////////////////////////////////////////////////////////////

  const onRenewCode = () => {
    // setYourCode(SysadminTrustedDeviceService.getPhraseCode());
  };

  /////////////////////////////////////////////////////////////////////////////////////

  const onSilentiumCodeInputChanged = () => {
    if (errorMessage) {
      setErrorMessage('');
    }
    const code = codeInputRef.current.value.trim();
    if (code) {
      props.router.replace(`?code=${codeInputRef.current.value}`, undefined, {
        shallow: true,
      });
    } else {
      props.router.replace('', undefined, {
        shallow: true,
      });
    }
  };

  /////////////////////////////////////////////////////////////////////////////////////

  const onCancel = () => {
    setLoading(false);
    clearTimeout(timeout);
    // SysadminTrustedDeviceService.closeWS();
  };

  /////////////////////////////////////////////////////////////////////////////////////

  return (
    <div className="sl-login-box">
      <Flex {...props} auto column align="center" justify="center">
        <h1>Add practice</h1>
        <Flex align="center" column>
          <input
            autoFocus
            ref={codeInputRef}
            className={`bp5-input bp5-large cy-code-input ${errorMessage ? 'bp5-intent-danger' : ''
              }`}
            placeholder={'___ ___ ___ ___ ___ ___'}
            disabled={loading}
            onChange={onSilentiumCodeInputChanged}
          />
          <Flex mt="0.25rem">
            <BodyTextM color={COLOR.TEXT_NEGATIVE}>{errorMessage}</BodyTextM>
          </Flex>
        </Flex>
        <Flex column align="center" pt=".5rem">
          <p>
            Please send the code below to practice admin to initiate the
            registration process.
          </p>
          {/* <label className="code-label">{yourCode}</label> */}
          <span className="sl-issue">
            {`Trouble with the code? `}
            {loading ? (
              <a onClick={onCancel}>Cancel</a>
            ) : (
              <a onClick={onRenewCode}>Get a new code</a>
            )}
          </span>
        </Flex>
        <Button
          intent="primary"
          onClick={onTrustDevice}
          className="bp5-button bp5-large bp5-fill cy-trust-btn"
          disabled={loading}
        >
          <Flex align="center">{loading ? 'Syncing...' : 'Sync'}</Flex>
        </Button>
      </Flex>
      <OverlayToaster ref={toasterRef} />
    </div>
  );
}

export default memo(withRouter(SysadminPairingForm));
