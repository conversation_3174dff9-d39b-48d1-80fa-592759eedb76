import React, {
  FunctionComponentElement,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useRouter } from 'next/router';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import {
  H1,
  Flex,
  BodyTextM,
  Svg,
  Avatar,
  BodyTextL,
  LoadingState,
  H2,
  BodyTextS,
} from '@tutum/design-system/components';
import { ISysadminTheme } from '@tutum/sysadmin/theme';
import GlobalContext, {
  IGlobalContext,
} from '@tutum/sysadmin/contexts/Global.context';
import { LEGACY_TOPIC_Login } from '@tutum/hermes/bff/legacy/app_admin';
import { BASE_PATH_ADMIN } from '@tutum/infrastructure/utils/string.util';
import { LOGGED_IN_ADMIN_USERS } from '@tutum/admin/constants/account';
import nameUtil from '@tutum/infrastructure/utils/name.utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import I18n from '@tutum/infrastructure/i18n';
import type LoginI18n from '@tutum/admin/locales/en/Login.json';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { isEmpty } from 'lodash';

export interface ILoginProps {
  className?: string;
  theme?: ISysadminTheme;
}

const Logo = '/images/logo-garrio.svg';
const DownArrow = '/images/chevron-down.svg';
const AddUserIcon = '/images/add-user-1.svg';

function Login({
  className,
}: ILoginProps & IGlobalContext): FunctionComponentElement<ILoginProps> {
  const { t } = I18n.useTranslation<keyof typeof LoginI18n>({
    namespace: 'Login',
  });

  const router = useRouter();

  const [isLoading, setLoading] = useState(false);
  const [sortType, setSortType] = useState<Order>(Order.ASC);

  const userList = useMemo(() => {
    const cacheData = JSON.parse(
      localStorage.getItem(LOGGED_IN_ADMIN_USERS) || '{}'
    );

    return Object.values(cacheData) as IEmployeeProfile[];
  }, []);

  const userListSorted = useMemo(() => {
    const isAsc = sortType === Order.ASC;

    return userList.sort((a, b) => {
      const fullNameA = nameUtil.getDoctorName(isAsc ? a : b);
      const fullNameB = nameUtil.getDoctorName(isAsc ? b : a);

      return fullNameA.localeCompare(fullNameB);
    });
  }, [userList, sortType]);

  const handleNewAccount = () => {
    setLoading(true);
    router.push(
      `${LEGACY_TOPIC_Login}?redirectUrl=${BASE_PATH_ADMIN}/employees`
    );
  };

  const renderUserList = useMemo(() => {
    return (
      <>
        <Flex gap={8} ml={32}>
          <Flex gap={4}>
            <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER}>
              {t('sortBy')}
            </BodyTextM>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
              {t(sortType === Order.ASC ? 'type' : 'typeZA')}
            </BodyTextM>
          </Flex>
          <Popover
            content={
              <Menu className="action-menu">
                <MenuItem
                  key="a-z"
                  text={t('type')}
                  data-test-id="login-sort-asc"
                  onClick={() => setSortType(Order.ASC)}
                />
                <MenuItem
                  key="z-a"
                  text={t('typeZA')}
                  data-test-id="login-sort-desc"
                  onClick={() => setSortType(Order.DESC)}
                />
              </Menu>
            }
          >
            <Flex>
              <Svg
                className="cursor-pointer"
                src={DownArrow}
                width={16}
                height={16}
                data-test-id="login-sort-icon"
              />
            </Flex>
          </Popover>
        </Flex>
        <Flex column mt={16}>
          {userListSorted.map((user, index) => (
            <Flex
              key={user.id}
              className="employee"
              gap={16}
              align="center"
              data-test-id={`login-user-${index}`}
              onClick={() => {
                setLoading(true);
                router.push(
                  `${LEGACY_TOPIC_Login}?username=${encodeURIComponent(
                    user.userName!
                  )}&orgId=${user.orgId
                  }&redirectUrl=${BASE_PATH_ADMIN}/employees`
                );
              }}
            >
              <Flex className="avatar-wrapper">
                <Avatar className="avatar-content" initial={user.initial} />
              </Flex>
              <Flex column>
                <BodyTextL color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
                  {nameUtil.getDoctorName(user)}
                </BodyTextL>
                <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL}>
                  {user.userName}
                </BodyTextM>
              </Flex>
            </Flex>
          ))}
          <Flex
            className="employee"
            gap={16}
            align="center"
            data-test-id="login-user-new"
            onClick={handleNewAccount}
          >
            <Flex
              className="add-user-icon"
              borderRadius="50%"
              w={64}
              h={64}
              justify="center"
              align="center"
            >
              <Svg width={44} height={44} src={AddUserIcon} />
            </Flex>
            <Flex column>
              <BodyTextL color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
                {t('otherAccount')}
              </BodyTextL>
            </Flex>
          </Flex>
        </Flex>
      </>
    );
  }, [userListSorted, sortType]);

  useEffect(() => {
    if (isEmpty(userListSorted)) {
      handleNewAccount();
    }
  }, [userListSorted]);

  return (
    <Flex
      className={className}
      data-test-id="login"
      align="center"
      justify="center"
      w="100vw"
      h="100vh"
      column
      gap={16}
    >
      {isLoading && <LoadingState dataTestId="login-loading" />}
      <Flex className="sl-Login__inner" column>
        <Flex column gap={16} align="center">
          <Svg
            className="logo"
            src={Logo}
            alt="Logo garrioPRO"
            width={200}
            height="auto"
          />
          <H2 fontSize="24px" lineHeight="32px" fontWeight={700}>
            {t('guide')}
          </H2>
        </Flex>

        <Flex column my={24}>
          {renderUserList}
        </Flex>
      </Flex>
      <Flex>
        <BodyTextS>{t('note')}</BodyTextS>
      </Flex>
    </Flex>
  );
}

export default GlobalContext.withContext(Login);
