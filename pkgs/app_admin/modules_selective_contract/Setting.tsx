import { FFWrapper } from '@tutum/admin/hooks/useFeatureFlag';
import type SettingI18n from '@tutum/admin/locales/en/Settings.json';
import {
  BodyTextM,
  Flex,
  FormGroup2,
  H3,
  Link,
} from '@tutum/design-system/components';
import {
  Button,
  Classes,
  Divider,
  InputGroup,
  Intent,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import {
  alertSuccessfully,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components/Toaster';
import {
  SelectiveContractsKey,
  useMutationSaveSettings,
  useQueryGetSetting,
} from '@tutum/hermes/bff/legacy/app_mvz_settings';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';
import {
  EnrollmentType,
  SettingsFeatures,
} from '@tutum/hermes/bff/legacy/settings_common';
import I18n from '@tutum/infrastructure/i18n';
import { Field, Form, Formik } from 'formik';
import React, { memo, useEffect, useState } from 'react';

export interface ISettingProps {
  className?: string;
}

interface ISelectiveContract {
  arribaPath?: string;
  billingSubmissionType: string;
}

const Setting = ({ className }: ISettingProps) => {
  const { t: tSetting } = I18n.useTranslation<keyof typeof SettingI18n>({
    namespace: 'Settings',
  });
  const { t } = I18n.useTranslation<keyof typeof SettingI18n.SelectiveContract>(
    {
      namespace: 'Settings',
      nestedTrans: 'SelectiveContract',
    }
  );
  const { data, isSuccess, refetch } = useQueryGetSetting({
    feature: SettingsFeatures.SettingsFeatures_SelectiveContacts,
    settings: [SelectiveContractsKey.SelectiveContracts_BillingSubmission],
  });

  const mutateSaveSetting = useMutationSaveSettings({
    onSuccess: async () => {
      await refetch();
      alertSuccessfully(tSetting('settingsSaved'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });

  const [initialValues, setInitialValues] = useState<ISelectiveContract>({
    billingSubmissionType: null!,
    arribaPath: null!,
  });

  useEffect(() => {
    (async () => {
      if (isSuccess) {
        setInitialValues({
          billingSubmissionType:
            data.settings[
            SelectiveContractsKey.SelectiveContracts_BillingSubmission
            ] || EnrollmentType.EnrollmentType_ONLINE,
          arribaPath:
            data.settings[SelectiveContractsKey.SelectiveContracts_ArribaPath],
        });
      }
    })();
  }, [isSuccess, data]);

  const handleSubmit = async (data: ISelectiveContract) => {
    await mutateSaveSetting.mutateAsync({
      settings: {
        [SelectiveContractsKey.SelectiveContracts_ArribaPath]:
          data.arribaPath?.trim()!,
        [SelectiveContractsKey.SelectiveContracts_BillingSubmission]:
          data.billingSubmissionType,
      },
      feature: SettingsFeatures.SettingsFeatures_SelectiveContacts,
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  return (
    <div className={className}>
      <Formik<ISelectiveContract>
        initialValues={initialValues}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ submitCount, errors, touched }) => {
          return (
            <Form onKeyDown={handleKeyDown} className="sl-form">
              <Flex
                column
                justify="center"
                className="sl-align-layout title-layout"
              >
                <H3 style={{ fontSize: '20px' }}>{t('title')}</H3>
              </Flex>
              <Divider
                style={{ marginTop: 0 }}
                className={`sl-divider ${Classes.FILL}`}
              />
              <Flex column className="sl-align-layout">
                <Flex className="sl-divider">
                  <Flex className="sl-flex-left">
                    <H3>{t('billingSubmission')}</H3>
                  </Flex>
                  <Flex className="sl-flex-right" column>
                    <Field name="billingSubmissionType">
                      {({ field, form }) => {
                        return (
                          <RadioGroup
                            onChange={(e) => {
                              form.setFieldValue(
                                field.name,
                                e.currentTarget.value
                              );
                            }}
                            selectedValue={field.value}
                          >
                            <Radio
                              label={t('billingSubmissionOnline')}
                              value={EnrollmentType.EnrollmentType_ONLINE}
                            />
                            <Radio
                              label={t('billingSubmissionOffline')}
                              value={EnrollmentType.EnrollmentType_OFFLINE}
                            />
                          </RadioGroup>
                        );
                      }}
                    </Field>
                  </Flex>
                </Flex>
                <Divider className={`sl-divider ${Classes.FILL}`}></Divider>
                <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_SV}>
                  <Flex className="sl-divider">
                    <Flex className="sl-flex-left">
                      <H3>{t('arriba')}</H3>
                    </Flex>
                    <Flex className="sl-flex-right" column gap={8}>
                      <BodyTextM fontSize={14}>
                        {t('arribaConnect')}{' '}
                        <Link
                          href="https://arriba-hausarzt.de/arriba-download"
                          target="_blank"
                          title={t('arribaLink')}
                          fontSize={14}
                        >
                          {t('arribaLink')}
                        </Link>{' '}
                        {t('arribaDownload')}
                      </BodyTextM>
                      <FormGroup2
                        name="arribaPath"
                        label={t('arribaPath')}
                        submitCount={submitCount}
                        errors={errors}
                        touched={touched}
                      >
                        <Field name="arribaPath">
                          {({ field, form }) => {
                            return (
                              <InputGroup
                                {...field}
                                placeholder={t('arribaPlaceholder')}
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                  </Flex>
                  <Divider className={`sl-divider ${Classes.FILL}`}></Divider>
                </FFWrapper>

                <Flex justify="flex-end">
                  <Button
                    loading={mutateSaveSetting.isPending}
                    disabled={mutateSaveSetting.isPending}
                    type="submit"
                    intent={Intent.PRIMARY}
                  >
                    {t('save')}
                  </Button>
                </Flex>
              </Flex>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default memo(Setting);
