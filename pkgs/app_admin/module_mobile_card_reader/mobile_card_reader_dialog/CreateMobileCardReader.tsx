import React, { memo, useState, useMemo, useRef } from 'react';
import Select from 'react-select';
import { Formik, Form, FastField, FormikProps } from 'formik';

import type MobileCardReaderJson from '@tutum/admin/locales/en/MobileCardReader.json';
import type CommonLocales from '@tutum/admin/locales/en/Common.json';

import { Modal, ModalSize } from '@tutum/design-system/components/Modal';
import I18n from '@tutum/infrastructure/i18n';
import { InputGroup } from '@tutum/design-system/components/Core';
import {
  Flex,
  Button,
  LeaveConfirmModal,
  alertSuccessfully,
  alertError,
} from '@tutum/design-system/components';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import {
  FIELD_NAMES,
  onValidate,
  initValues,
} from './CreateMobileCardReader.helper';
import { PAGE_SIZE_DEFAULT } from '@tutum/design-system/consts/table';
import {
  useMobileCardReaderStore,
  MobileCardReaderActions,
} from '@tutum/admin/module_mobile_card_reader/mobile_card_reader_overview/MobileCardReader.store';
import {
  useMutationCreateMobileCardReader,
  useMutationEditMobileCardReader,
} from '@tutum/hermes/bff/legacy/app_mobile_card_reader';
import { useErrorCodeI18n } from '@tutum/admin/hooks/useErrorCode';
import { MobileCardReader } from '@tutum/hermes/bff/mobile_card_reader_common';

export interface ICreateMobileCardReaderProps {
  className?: string;
  isOpen: boolean;
  onClose: () => void;
}

const CreateMobileCardReader = ({
  className,
  isOpen,
  onClose,
}: ICreateMobileCardReaderProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof MobileCardReaderJson.CreateMobileCardReader
  >({
    namespace: 'MobileCardReader',
    nestedTrans: 'CreateMobileCardReader',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: tFormValidation } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const [isConfirmLeaveOpen, setIsConfirmLeaveOpen] = useState(false);
  const formRef = useRef<FormikProps<any> | null>(null);

  const { isEditMobileCardReader, currentMobileCardReader, devices } =
    useMobileCardReaderStore();
  const errorCodeT = useErrorCodeI18n();

  const initialValue = useMemo(
    () => (isEditMobileCardReader ? currentMobileCardReader : initValues),
    [isEditMobileCardReader, currentMobileCardReader]
  );

  const { mutate: createMobileCardReader } = useMutationCreateMobileCardReader({
    onSuccess: () => {
      alertSuccessfully(t('createSuccessfully'));
      MobileCardReaderActions.getListMobileCardReader(1, PAGE_SIZE_DEFAULT);
      MobileCardReaderActions.setIsLoading(false);
      onClose();
    },
    onError: (error) => {
      MobileCardReaderActions.setIsLoading(false);
      formRef.current?.setSubmitting(false);
      alertError(errorCodeT(error as any));
    },
  });

  const { mutate: editMobileCardReader } = useMutationEditMobileCardReader({
    onSuccess: () => {
      alertSuccessfully(t('editSuccessfully'));
      MobileCardReaderActions.getListMobileCardReader(1, PAGE_SIZE_DEFAULT);
      MobileCardReaderActions.setIsLoading(false);
      onClose();
    },
    onError: (error) => {
      formRef.current?.setSubmitting(false);
      MobileCardReaderActions.setIsLoading(false);
      alertError(errorCodeT(error as any));
    },
  });

  const onCreate = (values: MobileCardReader) => {
    createMobileCardReader({ mobileCardReader: values });
  };

  const onEdit = (values: MobileCardReader) => {
    const payload = {
      ...values,
      id: currentMobileCardReader?.id,
    };
    editMobileCardReader({ mobileCardReader: payload });
    MobileCardReaderActions.setIsLoading(false);
  };

  const onCloseCreateMobileCardReader = (dirty: boolean) => {
    if (dirty) {
      setIsConfirmLeaveOpen(true);
    } else {
      onClose();
    }
  };

  const renderOption = () => {
    return devices.map((item) => {
      return {
        label: item.deviceName,
        value: item,
      };
    });
  };

  return (
    <Modal
      className={className}
      title={isEditMobileCardReader ? t('editTitle') : t('createTitle')}
      isOpen={isOpen}
      size={ModalSize.AUTO}
      onClose={() => onCloseCreateMobileCardReader(false)}
      shouldReturnFocusOnClose
      canEscapeKeyClose={true}
      hasHeaderLine
    >
      <Formik<any>
        innerRef={formRef}
        initialValues={initialValue}
        validate={onValidate({ t: tFormValidation })}
        onSubmit={(v) => {
          if (isEditMobileCardReader) {
            onEdit(v);
          } else {
            onCreate(v);
          }
        }}
        enableReinitialize
        validateOnChange={false}
        validateOnBlur={false}
      >
        {({ isSubmitting, submitCount, touched, errors, dirty }) => (
          <Form
            onKeyDown={(e) =>
              e.key === 'Enter' && !e.shiftKey && e.preventDefault()
            }
          >
            <Flex className="sl-form-body" column gap={16}>
              <FormGroup2
                label={t('workPlace')}
                isRequired
                name={FIELD_NAMES.WORKPLACE}
                submitCount={submitCount}
                errors={errors}
                touched={touched}
              >
                <FastField name={FIELD_NAMES.WORKPLACE}>
                  {({ field, form }) => (
                    <Select
                      options={renderOption()}
                      defaultValue={
                        isEditMobileCardReader
                          ? {
                            label:
                              currentMobileCardReader?.workPlace?.deviceName,
                            value: currentMobileCardReader?.workPlace,
                          }
                          : {
                            label: '',
                            value: null,
                          }
                      }
                      onChange={(newValue) => {
                        const { value } = newValue as { value: any };
                        if (!value) {
                          throw new Error('missing device id');
                        }
                        form.setFieldValue(field.name, value);
                      }}
                    />
                  )}
                </FastField>
              </FormGroup2>
              <FormGroup2
                label={t('driver')}
                isRequired
                name={FIELD_NAMES.DRIVER}
                submitCount={submitCount}
                errors={errors}
                touched={touched}
              >
                <FastField name={FIELD_NAMES.DRIVER}>
                  {({ field }) => (
                    <InputGroup {...field} data-tab-id={field.name} />
                  )}
                </FastField>
              </FormGroup2>
              <FormGroup2
                label={t('comPort')}
                isRequired
                name={FIELD_NAMES.PORT}
                submitCount={submitCount}
                errors={errors}
                touched={touched}
              >
                <FastField name={FIELD_NAMES.PORT}>
                  {({ field }) => (
                    <InputGroup
                      {...field}
                      data-tab-id={field.name}
                      maxLength={20}
                    />
                  )}
                </FastField>
              </FormGroup2>
            </Flex>
            <Flex className="sl-form-footer">
              <Flex gap={8}>
                <Button
                  large
                  intent="primary"
                  outlined
                  minimal
                  onClick={() => onCloseCreateMobileCardReader(dirty)}
                  disabled={isSubmitting}
                  loading={isSubmitting}
                >
                  {tButtonActions('cancelText')}
                </Button>
                <Button
                  large
                  type="submit"
                  intent="primary"
                  disabled={isSubmitting || !dirty}
                  loading={isSubmitting}
                >
                  {isEditMobileCardReader
                    ? tButtonActions('saveText')
                    : tButtonActions('create')}
                </Button>
              </Flex>
            </Flex>
            <LeaveConfirmModal
              isOpen={isConfirmLeaveOpen}
              onConfirm={() => {
                onClose();
                setIsConfirmLeaveOpen(false);
              }}
              onClose={() => setIsConfirmLeaveOpen(false)}
            />
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default memo(CreateMobileCardReader);
