import React, { useState, useEffect } from 'react';

import type MobileCardReaderJson from '@tutum/admin/locales/en/MobileCardReader.json';

import {
  Flex,
  H1,
  Button,
  Intent,
  Svg,
  BodyTextM,
  alertSuccessfully,
} from '@tutum/design-system/components';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { Divider } from '@tutum/design-system/components/Core';
import i18n from '@tutum/infrastructure/i18n';
import {
  genColumns,
  customStyles,
} from '@tutum/admin/module_mobile_card_reader/mobile_card_reader_overview/MobileCardReader.helper';
import Table from '@tutum/design-system/components/Table';
import CreateMobileCardReader from '@tutum/admin/module_mobile_card_reader/mobile_card_reader_dialog';
import {
  useMobileCardReaderStore,
  MobileCardReaderActions,
} from '@tutum/admin/module_mobile_card_reader/mobile_card_reader_overview/MobileCardReader.store';
import {
  PAGE_DEFAULT,
  PAGE_SIZE_DEFAULT,
} from '@tutum/design-system/consts/table';
import type { MobileCardReader } from '@tutum/hermes/bff/mobile_card_reader_common';
import CareProviderDeviceService from '@tutum/admin/module_careprovider-trusted-device/CareProviderTrustedDevice.service';
import { COLOR } from '@tutum/design-system/themes/styles';

const PlusIcon = '/images/plus-white.svg';

export interface IMobileCardReader {
  className?: string;
}

const MobileCardReaderOverview = (props: IMobileCardReader) => {
  const { className } = props;
  const [isConfirmDelete, setIsConfirmDelete] = useState({
    isDelete: false,
    mobileCardReaderId: '',
  });
  const [pagination, setPagination] = useState({
    page: PAGE_DEFAULT,
    pageSize: PAGE_SIZE_DEFAULT,
  });
  const {
    isEditMobileCardReader,
    isOpenCreateMobileCR,
    totalOverview,
    pageSizeOverview,
    isLoading,
    mobileCardReader,
  } = useMobileCardReaderStore();

  const { t } = i18n.useTranslation<keyof typeof MobileCardReaderJson>({
    namespace: 'MobileCardReader',
  });

  const onEdit = (data: MobileCardReader) => {
    MobileCardReaderActions.setCurrentMobileCard(data);
    MobileCardReaderActions.setIsEditMobileCardReadere(true);
    MobileCardReaderActions.setIsOpenCreateMobileCR(true);
  };

  const onCreateMobileCardReader = () => {
    MobileCardReaderActions.setIsOpenCreateMobileCR(true);
  };

  const onClickRemove = (data: MobileCardReader) => {
    setIsConfirmDelete({
      isDelete: true,
      mobileCardReaderId: data?.id,
    });
  };

  const onConfirmDelete = async () => {
    setIsConfirmDelete(null);
    await MobileCardReaderActions.deleteMobileCardReader(
      isConfirmDelete.mobileCardReaderId
    );
    alertSuccessfully(t('deleteSuccess'));
    MobileCardReaderActions.getListMobileCardReader(1, PAGE_SIZE_DEFAULT);
  };

  useEffect(() => {
    CareProviderDeviceService.getAllDevices().then((deviceList) => {
      MobileCardReaderActions.setDevices(deviceList);
    });
  }, []);

  useEffect(() => {
    const { page, pageSize } = pagination;
    MobileCardReaderActions.getListMobileCardReader(page, pageSize);
  }, [JSON.stringify(pagination)]);

  const onChangePage = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination({
      ...pagination,
      pageSize: currentRowsPerPage,
    });
  };

  const onCloseModal = () => {
    if (isEditMobileCardReader) {
      MobileCardReaderActions.setIsEditMobileCardReadere(false);
    }
    MobileCardReaderActions.setIsOpenCreateMobileCR(false);
  };

  useEffect(() => {
    MobileCardReaderActions.getListMobileCardReader(
      pagination?.page,
      pagination?.pageSize
    );
  }, []);

  return (
    <Flex className={className} column>
      <Flex p="16px" align="center">
        <H1>{t('title')}</H1>
      </Flex>
      <Divider />
      <Flex p="16px" justify="flex-end">
        <Button
          intent={Intent.PRIMARY}
          iconOnly
          icon={<Svg src={PlusIcon} />}
          onClick={onCreateMobileCardReader}
        />
      </Flex>
      <Flex column justify="space-between" h="100%">
        <Table
          columns={genColumns({
            t,
            onEdit,
            onClickRemove,
          })}
          data={mobileCardReader}
          customStyles={customStyles}
          highlightOnHover
          noHeader
          persistTableHead
          noDataComponent={
            <BodyTextM
              color={COLOR.TEXT_TERTIARY_SILVER}
              style={{ marginTop: 24 }}
            >
              {t('noData')}
            </BodyTextM>
          }
          progressPending={isLoading}
          keyField="key"
          fixedHeader
          pagination
          paginationServer
          paginationDefaultPage={1}
          paginationResetDefaultPage
          paginationTotalRows={totalOverview}
          paginationPerPage={pageSizeOverview}
          onChangePage={onChangePage}
          onChangeRowsPerPage={onChangeRowsPerPage}
        />
      </Flex>
      {isOpenCreateMobileCR && (
        <CreateMobileCardReader isOpen onClose={onCloseModal} />
      )}

      <DeleteConfirmDialog
        className="confirm-dialog--hide-icon"
        isOpen={!!isConfirmDelete?.isDelete}
        close={() => setIsConfirmDelete(null)}
        confirm={onConfirmDelete}
        text={{
          btnCancel: t('cancelButton'),
          btnOk: t('confirmButton'),
          title: t('titleRemove'),
          message: t('message'),
        }}
      />
    </Flex>
  );
};

export default MobileCardReaderOverview;
