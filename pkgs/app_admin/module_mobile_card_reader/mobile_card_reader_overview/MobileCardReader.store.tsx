import { proxy, useSnapshot } from 'valtio';
import { PAGE_SIZE_DEFAULT } from '@tutum/design-system/consts/table';
import {
  createMobileCardReader,
  getListMobileCardReader,
  editMobileCardReader,
  deleteMobileCardReader,
} from '@tutum/hermes/bff/legacy/app_mobile_card_reader';
import { MobileCardReader } from '@tutum/hermes/bff/mobile_card_reader_common';
import { TrustedDeviceResponse } from '@tutum/hermes/bff/service_domains_admin';
interface MobileCardReaderStore {
  isEditMobileCardReader: boolean;
  isLoading: boolean;
  isOpenCreateMobileCR: boolean;
  pageSizeOverview: number;
  totalOverview: number;
  mobileCardReader: MobileCardReader[];
  currentMobileCardReader: MobileCardReader | null;
  devices: TrustedDeviceResponse[];
}
interface IActions {
  setIsOpenCreateMobileCR: (value: boolean) => void;
  setIsEditMobileCardReadere: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
  setCurrentMobileCard: (item: MobileCardReader | null) => void;
  getListMobileCardReader: (page: number, pageSize: number) => Promise<any>;
  createMobileCardReader: (payload: MobileCardReader) => Promise<any>;
  editMobileCardReader: (payload: MobileCardReader) => Promise<any>;
  deleteMobileCardReader: (id: string) => Promise<any>;
  setDevices: (value: TrustedDeviceResponse[]) => void;
}

const initStore: MobileCardReaderStore = {
  isLoading: false,
  isEditMobileCardReader: false,
  isOpenCreateMobileCR: false,
  pageSizeOverview: PAGE_SIZE_DEFAULT,
  totalOverview: 1,
  mobileCardReader: [],
  currentMobileCardReader: null,
  devices: [],
};

const store = proxy<MobileCardReaderStore>(initStore);

export const MobileCardReaderActions: IActions = {
  setIsOpenCreateMobileCR: (value: boolean) => {
    store.isOpenCreateMobileCR = value;
  },

  setDevices: (value: TrustedDeviceResponse[]) => {
    store.devices = value;
  },

  setIsEditMobileCardReadere: (value: boolean) => {
    store.isEditMobileCardReader = value;
  },

  setIsLoading: (value: boolean) => {
    store.isLoading = value;
  },

  getListMobileCardReader: async (page: number, pageSize: number) => {
    store.isLoading = true;
    const { data } = await getListMobileCardReader({
      pagination: {
        page: page,
        pageSize: pageSize,
        order: null!,
        sortBy: null!,
      },
    });
    if (data?.paginationResponse) {
      store.pageSizeOverview = data.paginationResponse.totalPage;
      store.totalOverview = data.paginationResponse.total;
    }
    if (data?.mobileCardReader) {
      store.mobileCardReader = data.mobileCardReader ?? [];
    }
    store.isLoading = false;
    return data;
  },

  createMobileCardReader: async (payload: MobileCardReader) => {
    await createMobileCardReader({ mobileCardReader: payload });
  },

  setCurrentMobileCard: (item: MobileCardReader | null) => {
    store.currentMobileCardReader = item ? {
      ...item,
    } : null;
  },

  editMobileCardReader: async (payload: MobileCardReader) => {
    await editMobileCardReader({ mobileCardReader: payload });
  },

  deleteMobileCardReader: async (id: string) => {
    await deleteMobileCardReader({ iD: id });
  },
};

export function useMobileCardReaderStore() {
  return useSnapshot(store);
}
