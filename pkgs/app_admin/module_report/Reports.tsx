import React, { useState } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import type ReportsI18n from '@tutum/admin/locales/en/Reports.json';
import type CommonLocales from '@tutum/admin/locales/en/Common.json';
import type { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import type {
  Column,
  QueryViewModel,
} from '@tutum/hermes/bff/legacy/report_common';
import { ReportHook } from './hooks/useReport';
import {
  Flex,
  H1,
  Svg,
  Button,
  LoadingState,
  BodyTextL,
  H3,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import { Divider } from '@tutum/design-system/components/Core';
import { genColumns } from './Report.helper';
import useFetchReportUtils from '@tutum/design-system/query-builder/hooks/useFetchReportUtils';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import CreateReport from './components/CreateReport/CreateReport.styled';
import { DEFAULT_REPORT_NAME } from './Report.constant';
import { COLOR } from '@tutum/design-system/themes/styles';

const PlusIconSvgURL = '/images/plus-white.svg';

export interface Props {
  className: string;
}

export type VisibleColumns = Record<Column['name'], boolean>;

const Reports = (props: Props) => {
  const { className } = props;
  const { t } = I18n.useTranslation<Paths<typeof ReportsI18n>>({
    namespace: 'Reports',
  });
  const { t: tCommon } = I18n.useTranslation<Paths<typeof CommonLocales>>({
    namespace: 'Common',
  });

  const [queryModel, setQueryModel] = useState<QueryViewModel | null>(null);
  const [isOpenCreate, setIsOpenCreate] = useState(false);
  const [isOpenConfirm, setIsOpenConfirm] = useState(false);

  const reportUtilData = useFetchReportUtils();

  const {
    queryList,
    refetchList,
    isLoading: isLoadingList,
  } = ReportHook.useListQuery();
  const { definition, isLoading: isLoadingDefinition } =
    ReportHook.useReportDefinition({
      reportName: DEFAULT_REPORT_NAME,
    });
  const queryAction = ReportHook.useActionQuery({
    t,
    onDeleteQuery: {
      onClose: (refetch) => onCloseCreate(refetch),
    },
  });

  const onOpenCreateReport = () => {
    setIsOpenCreate(true);
  };

  const onCloseCreate = (refetch = false) => {
    if (refetch) {
      refetchList();
    }
    setQueryModel(null);
    setIsOpenCreate(false);
  };

  const onEdit = (row: QueryViewModel) => {
    setQueryModel(row);
    onOpenCreateReport();
  };

  const onRemove = async (row: QueryViewModel) => {
    setIsOpenConfirm(true);
    setQueryModel(row);
  };

  const handleRemove = () => {
    queryAction.removeQuery({ queryId: queryModel?.id! });
    setQueryModel(null);
    setIsOpenConfirm(false);
  };

  return (
    <div className={className}>
      <Flex p="24px 16px">
        <H1>{t('header')}</H1>
      </Flex>
      <Divider style={{ margin: 0 }} />
      <Flex p={16} justify="flex-end">
        <Button
          intent="primary"
          className="sl-create-btn"
          icon={<Svg src={PlusIconSvgURL} />}
          onClick={onOpenCreateReport}
        >
          {t('createTitle')}
        </Button>
      </Flex>
      <Table
        className="sl-table"
        columns={genColumns({
          t,
          onEdit,
          onRemove,
          bsnrData: reportUtilData.bsnrData!,
        })}
        data={queryList}
        highlightOnHover
        responsive={false}
        noHeader
        persistTableHead
        striped
        progressPending={isLoadingList || isLoadingDefinition}
        fixedHeader
        noDataComponent={
          <Flex column align="center">
            <H3 color={COLOR.TEXT_SECONDARY_NAVAL}>{t('noReport1')}</H3>
            <BodyTextL color={COLOR.TEXT_SECONDARY_NAVAL2}>
              {t('noReport2')}
            </BodyTextL>
          </Flex>
        }
      />
      {isOpenCreate && (
        <CreateReport
          t={t}
          tCommon={tCommon}
          className="sl-CreateReport"
          isOpen={true}
          item={queryModel!}
          definition={definition}
          reportUtilData={reportUtilData}
          onClose={onCloseCreate}
        />
      )}
      <DeleteConfirmDialog
        isOpen={isOpenConfirm}
        close={() => setIsOpenConfirm(false)}
        confirm={handleRemove}
        text={{
          btnCancel: tCommon('ButtonActions.cancelText'),
          btnOk: tCommon('ButtonActions.okText'),
          title: t('removeReportTitle'),
          message: t('removeReportDesc'),
        }}
      />
    </div>
  );
};

export default Reports;
