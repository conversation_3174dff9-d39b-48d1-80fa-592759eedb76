import type { CommonBSNR } from '@tutum/hermes/bff/legacy/app_bsnr';
import type { BSNR } from '@tutum/hermes/bff/bsnr_common';
import type { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import type { IMenuItem } from '@tutum/design-system/components';
import type ReportsI18n from '@tutum/admin/locales/en/Reports.json';

const extractBsnrs = (bsnrData: CommonBSNR[]): BSNR[] => {
  return bsnrData.flatMap((item) => {
    return [item.bsnr].concat(item.nbsnr);
  });
};

const toMenuOptions = <T extends { name?: string; id?: string }>(
  t: IFixedNamespaceTFunction<Paths<typeof ReportsI18n>>,
  items: T[],
  getLabel?: (item: T) => string
): IMenuItem[] => {
  const getLabelFn =
    getLabel ?? ((item) => t(item.name! as Paths<typeof ReportsI18n>));

  return items.map((item) => ({
    label: getLabelFn(item),
    value: item.id,
  }));
};

export { extractBsnrs, toMenuOptions };
