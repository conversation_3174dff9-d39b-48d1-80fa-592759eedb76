import React, { useState } from 'react';
import {
  Button,
  Classes,
  InputGroup,
  Dialog,
} from '@tutum/design-system/components/Core';
import {
  alertError,
  alertSuccessfully,
  Flex,
  BannerSetting,
} from '@tutum/design-system/components';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import { IAdminTheme } from '@tutum/admin/theme';
import I18n from '@tutum/infrastructure/i18n';
import type CareProviderTrustedDeviceI18n from '@tutum/admin/locales/en/CareProviderTrustedDevice.json';
import { useMutationAddDevice } from '@tutum/hermes/bff/legacy/app_admin';
import { useErrorCodeI18n } from '@tutum/admin/hooks/useErrorCode';

export interface ICareProviderPairingDialogProps {
  className?: string;
  theme?: IAdminTheme;
  isOpen: boolean;
  onClose: () => void;
  onCreateDevice?: () => void;
  errorTitle?: string;
}

function CareProviderPairingDialog({
  className,
  isOpen,
  errorTitle,
  onClose,
  onCreateDevice,
}: ICareProviderPairingDialogProps) {
  const { t } = I18n.useTranslation<keyof typeof CareProviderTrustedDeviceI18n>(
    {
      namespace: 'CareProviderTrustedDevice',
    }
  );
  const tErr = useErrorCodeI18n();
  const [deviceName, setDeviceName] = useState('');

  const onSubmit = async (e) => {
    e.preventDefault();
  };

  const { isPending: isLoadingAddDevice, mutate } = useMutationAddDevice({
    onError: (err) => {
      alertError(tErr(`${err?.response?.data?.serverError}` as any));
    },
    onSuccess: () => {
      alertSuccessfully(t('deviceAddedSuccessfully'));
      onClose();
      onCreateDevice?.();
    },
    throwOnError: false,
  });

  const renderDeviceName = () => {
    return (
      <form onSubmit={onSubmit}>
        {errorTitle && (
          <Flex pt={19} pl={16} pr={16}>
            <BannerSetting title={errorTitle} />
          </Flex>
        )}
        <FormGroup2
          className="device-name"
          label={t('deviceName')}
          name="deviceName"
          isRequired
        >
          <InputGroup
            disabled={isLoadingAddDevice}
            large
            autoFocus
            name="deviceName"
            placeholder={t('addDevicePlaceholder')}
            data-test-id="device-name"
            onChange={(e) => setDeviceName(e.target.value.trim())}
          />
        </FormGroup2>
        <div className={Classes.DIALOG_FOOTER}>
          <Button
            loading={isLoadingAddDevice}
            type="submit"
            intent="primary"
            disabled={!isLoadingAddDevice && !deviceName}
            data-test-id="add-device"
            className="bp5-button bp5-large bp5-fill"
            onClick={() => {
              mutate({ deviceName });
            }}
          >
            <Flex align="center">{t('addDevice')}</Flex>
          </Button>
        </div>
      </form>
    );
  };
  return (
    <Dialog
      isOpen={isOpen}
      onClose={() => {
        onClose();
        setDeviceName('');
      }}
      title={t('addDeviceTitle')}
      canOutsideClickClose={false}
    >
      <Flex auto column className={`${className} cy-trusted-device-dialog`}>
        {renderDeviceName()}
      </Flex>
    </Dialog>
  );
}

export default CareProviderPairingDialog;
