import { memo, useState, useCallback, useMemo } from 'react';
import { Form, Formik } from 'formik';

import {
  Classes,
  Dialog,
  InputGroup,
} from '@tutum/design-system/components/Core';
import {
  Flex,
  Button,
  FormGroup2,
  ReactSelect,
  IMenuItem,
  BannerSetting,
  InfoConfirmDialog,
} from '@tutum/design-system/components';
import {
  AdminApi,
  AdminApiModel,
} from '@tutum/infrastructure/resource/AdminResource';
import { IAdminTheme } from '@tutum/admin/theme';
import I18n from '@tutum/infrastructure/i18n';
import type CommonLocales from '@tutum/admin/locales/en/Common.json';
import type CareProviderTrustedDeviceLocales from '@tutum/admin/locales/en/CareProviderTrustedDevice.json';
import Table from '@tutum/design-system/components/Table';
import {
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components/Toaster';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { genColumns } from './setting-table';
import { StyledButtonActions } from './CareProviderTrustedDeviceList.styled';
import { Terminal } from '@tutum/hermes/bff/legacy/app_card_operation';
import FormUtils, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import {
  useMutationRevokeDevice,
  useMutationUpdateDevice,
} from '@tutum/hermes/bff/legacy/app_admin';
import { useErrorCodeI18n } from '@tutum/admin/hooks/useErrorCode';

export interface ICareProviderTrustedDeviceListProps {
  className?: string;
  theme?: IAdminTheme;
  devices: AdminApiModel.TrustedDeviceResponse[];
  onUpdateDevice: () => void;
  terminals: Terminal[];
  isErrorTerminal?: boolean;
}

const CareProviderTrustedDeviceList = ({
  className,
  devices,
  onUpdateDevice,
  terminals,
  isErrorTerminal,
}: ICareProviderTrustedDeviceListProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof CareProviderTrustedDeviceLocales
  >({
    namespace: 'CareProviderTrustedDevice',
  });
  const { t: tFormErrors } = I18n.useTranslation<
    keyof typeof CareProviderTrustedDeviceLocales.form.errors
  >({
    namespace: 'CareProviderTrustedDevice',
    nestedTrans: 'form.errors',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const tError = useErrorCodeI18n();
  const [openEdit, setOpenEditState] = useState(false);
  const [editDevice, setEditDevice] =
    useState<AdminApiModel.TrustedDeviceResponse | null>(null);
  const [isOpenConfirm, setIsOpenConfirm] = useState(false);

  const onEditDevice = (device: AdminApiModel.TrustedDeviceResponse) => {
    setOpenEditState(true);
    setEditDevice(device);
  };

  const onCancelEdit = () => {
    setOpenEditState(false);
    setEditDevice(null);
  };

  const onSelectTerminal = (item: IMenuItem) => {
    if (!editDevice) return;
    setEditDevice({
      ...editDevice,
      terminalId: `${item.value}`,
    });
  };

  const { isPending: isLoadingUpdateDevice, mutate: updateDeviceFunc } =
    useMutationUpdateDevice({
      onError: (err) => {
        alertError(t('renameFailedToaster'));
        console.error(err);
      },
      onSuccess: () => {
        alertSuccessfully(t('renameSuccessfulToaster'));
        onUpdateDevice();
      },
      throwOnError: false,
    });
  const { mutate: revokeDeviceFunc } = useMutationRevokeDevice({
    onError: (err) => {
      alertError(t('removeFailedToaster'));
      console.error(err);
    },
    onSuccess: () => {
      alertSuccessfully(t('removeSuccessfulToaster'));
      onUpdateDevice();
    },
  });

  const onSaveEditDevice = (values: AdminApi.TrustedDeviceResponse) => {
    setOpenEditState(false);
    if (editDevice) {
      updateDeviceFunc({
        deviceId: values.id,
        deviceName: values.deviceName,
        terminalId: editDevice.terminalId,
      });
    }
  };

  const onRemove = (device: AdminApiModel.TrustedDeviceResponse) => {
    setEditDevice(device);
    setIsOpenConfirm(true);
  };

  const onCloseConfirmDialog = useCallback(() => {
    setIsOpenConfirm(false);
    setEditDevice(null);
  }, []);

  const onRevokeDevice = () => {
    if (!editDevice || !editDevice.id) return;
    onCloseConfirmDialog();
    revokeDeviceFunc({
      deviceId: editDevice.id,
    });
  };

  const dataDevices = useMemo(() => {
    return devices?.map((item) => ({
      ...item,
      terminalName:
        terminals?.find((terminal) => terminal.terminalId === item.terminalId)
          ?.terminalName ?? '',
    }));
  }, [devices, terminals]);

  const validateForm = async (values: AdminApiModel.TrustedDeviceResponse) => {
    const validateFields: ValidateField[] = [];
    validateFields.push({
      fieldName: 'deviceName',
      validateRule: () =>
        !values.deviceName || isEmpty(values.deviceName, true),
      errorMessage: tFormErrors('deviceNameRequired'),
    });
    const { errors } = FormUtils.validateForm(validateFields, null);

    return errors;
  };

  return (
    <Flex className={className} auto column>
      <Table
        className="sl-table"
        columns={genColumns({
          t,
          onEdit: onEditDevice,
          onRemove,
        })}
        data={dataDevices}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        fixedHeader
      />
      <Dialog
        onClose={onCancelEdit}
        title={t('editTitle')}
        isOpen={openEdit}
        canOutsideClickClose={false}
      >
        {!!editDevice && (
          <Formik<AdminApiModel.TrustedDeviceResponse>
            onSubmit={onSaveEditDevice}
            initialValues={editDevice}
            validate={validateForm}
            validateOnChange
            validateOnBlur
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              submitCount,
              dirty,
            }) => (
              <Form>
                <Flex className={Classes.DIALOG_BODY} column gap={16}>
                  {isErrorTerminal && (
                    <BannerSetting
                      title={tError('ErrorCode_Ticonnector_Not_Available')}
                    />
                  )}
                  <FormGroup2
                    label={t('deviceName')}
                    name="deviceName"
                    errors={errors}
                    touched={touched}
                    submitCount={submitCount}
                    isRequired
                  >
                    <InputGroup
                      name="deviceName"
                      value={values.deviceName}
                      onChange={handleChange}
                      className="bp5-large"
                      data-test-id="device-name-edit"
                      placeholder={t('renamePlaceholder')}
                    />
                  </FormGroup2>
                  <FormGroup2 label={t('fixedCardReaderForDevice')}>
                    <ReactSelect
                      className="select-terminal"
                      selectedValue={editDevice?.terminalId}
                      items={terminals?.map((item) => ({
                        ...item,
                        value: item.terminalId,
                        label: item.terminalName,
                      }))}
                      onItemSelect={onSelectTerminal}
                      menuPlacement="auto"
                    />
                  </FormGroup2>
                </Flex>
                <StyledButtonActions>
                  <Button
                    large
                    intent="primary"
                    outlined
                    minimal
                    data-test-id="cancel-edit-device"
                    onClick={onCancelEdit}
                  >
                    {tButtonActions('cancelText')}
                  </Button>
                  <Button
                    large
                    loading={isLoadingUpdateDevice}
                    type="submit"
                    intent="primary"
                    disabled={
                      !editDevice ||
                      !editDevice.deviceName ||
                      editDevice.deviceName.length === 0 ||
                      !dirty
                    }
                    data-test-id="save-edit-device"
                  >
                    {tButtonActions('saveText')}
                  </Button>
                </StyledButtonActions>
              </Form>
            )}
          </Formik>
        )}
      </Dialog>
      <InfoConfirmDialog
        type="secondary"
        isOpen={isOpenConfirm}
        title={t('removeDeviceTitle')}
        confirmText={tButtonActions('yesRemove')}
        cancelText={tButtonActions('noText')}
        isCloseButtonShown={false}
        onClose={onCloseConfirmDialog}
        onConfirm={onRevokeDevice}
      >
        {t('removeDeviceDesc')}
      </InfoConfirmDialog>
    </Flex>
  );
};

export default memo(CareProviderTrustedDeviceList);
