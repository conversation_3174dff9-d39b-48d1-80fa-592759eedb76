import React, { useEffect, useMemo, useState } from 'react';
import orderBy from 'lodash/orderBy';

import type CodingRulesI18n from '@tutum/admin/locales/en/CodingRules.json';

import I18n from '@tutum/infrastructure/i18n';
import {
  H1,
  Button,
  Svg,
  alertSuccessfully,
  Flex,
  SortComponent,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import {
  IcdRule,
  CheckTime,
  RuleApplication,
} from '@tutum/hermes/bff/masterdata_common';
import { getSettingIcdRules, updateIcdRule } from './Rules.services';
import IconFilter from './components/IconFilter';
import InfoSign from './components/InfoSign';
import FilterDialog from './FilterDialog.styled';
import { FilterData, FilterRule } from './FilterDialog';
import { Status, StatusComponentType } from './types';
import { AltStyle } from './Alternative.global.styled';
import RuleDetailDialog from './RuleDetailDialog.styled';
import {
  MenuItem,
  RadioGroup,
  Radio,
  Switch,
} from '@tutum/design-system/components/Core';
import { Select } from '@tutum/design-system/components/Select';
import ChangeLogDialog from './ChangelogDialog';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { COLOR } from '@tutum/design-system/themes/styles';

interface CustomizedEvent extends Partial<React.MouseEvent> {
  customizedValue: any;
}

interface CustomizedSyntheticEvent extends Partial<React.SyntheticEvent> {
  customizedValue: any;
}

export enum Categories {
  QUATER = 'quarter',
  TREATMENT_CASE = 'treatmentCase',
}

export class DeviceProps {
  className?: string;
}

type APIBuffer = {
  data: IcdRule[];
};

type CheckTimeRuleOption = { data: CheckTime; text: string };

export const sortByString = (
  str1: string,
  str2: string,
  isSortASC: boolean
) => {
  const str1Compare = (Array.isArray(str1) ? str1.join(', ') : str1) || '';
  const str2Compare = (Array.isArray(str2) ? str2.join(', ') : str2) || '';

  return isSortASC
    ? str1Compare.localeCompare(str2Compare)
    : str2Compare.localeCompare(str1Compare);
};

const apiBuffer: APIBuffer = {} as APIBuffer;
const getApiBufferData = () => {
  return Array.isArray(apiBuffer.data) ? apiBuffer.data : [];
};

const toaster = (message: string) => {
  alertSuccessfully(message);
};

const Rules = ({ className }: DeviceProps) => {
  const { t } = I18n.useTranslation<keyof typeof CodingRulesI18n>({
    namespace: 'CodingRules',
  });

  const [sortType, setSortType] = useState<{
    field: string;
    order: Order | string;
  }>({
    field: 'id',
    order: Order.ASC,
  });
  const checkTimeMapper = useMemo(() => {
    const mapper: Partial<Record<CheckTime, keyof typeof CheckTime>> = {};
    for (const key in CheckTime) {
      mapper[CheckTime[key]] = key;
    }
    return mapper;
  }, []);

  const { treatmentRelatedRuleOptions, quarterlyRuleOptions } = useMemo(() => {
    const treatmentRelatedRuleOptions: CheckTimeRuleOption[] = [
      {
        data: CheckTime.CheckTime_Coding,
        text: t(checkTimeMapper[CheckTime.CheckTime_Coding]),
      },
      {
        data: CheckTime.CheckTime_Billing,
        text: t(checkTimeMapper[CheckTime.CheckTime_Billing]),
      },
      {
        data: CheckTime.CheckTime_CodingAndBilling,
        text: t(checkTimeMapper[CheckTime.CheckTime_CodingAndBilling]),
      },
    ];

    const quarterlyRuleOptions: CheckTimeRuleOption[] = [
      {
        data: CheckTime.CheckTime_Billing,
        text: t(checkTimeMapper[CheckTime.CheckTime_Billing]),
      },
      {
        data: CheckTime.CheckTime_Selectable,
        text: t(checkTimeMapper[CheckTime.CheckTime_Selectable]),
      },
      {
        data: CheckTime.CheckTime_SelectableAndBilling,
        text: t(checkTimeMapper[CheckTime.CheckTime_SelectableAndBilling]),
      },
    ];

    return { treatmentRelatedRuleOptions, quarterlyRuleOptions };
  }, [checkTimeMapper]);

  const [icdRules, setICDRules] = useState<IcdRule[]>([]);
  const [filter, setFilter] = useState<FilterData>({} as FilterData);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [selectedIcdRuleId, setSelectedIcdRuleId] = useState<string>(null);
  const [filtersCount, setFiltersCount] = useState(0);
  const [isChangeLogOpen, setOpenChangeLog] = useState(false);

  // handle view dialog
  const [isRuleDetailModalOpen, setIsRuleDetailModalOpen] = useState(false);
  const ruleDetailModalCloseHandler = () => setIsRuleDetailModalOpen(false);

  const sortedRules = useMemo(() => {
    if (sortType.field !== '' && icdRules.length > 0) {
      const sortMap = {
        '': false,
        [Order.ASC]: 'asc',
        [Order.DESC]: 'desc',
      };
      const sorted = orderBy(
        icdRules,
        [sortType.field],
        [sortMap[sortType.order] as boolean | 'asc' | 'desc']
      );

      return sorted;
    }

    return [];
  }, [sortType, icdRules]);

  const filterData = () => {
    const filterRuleApplications: string[] = Object.keys(
      filter.ruleApplications || {}
    ).filter((raKey: string) => filter.ruleApplications[raKey]);
    const filterCheckTime: string[] = Object.keys(
      filter.checkTimes || {}
    ).filter((ctKey: string) => filter.checkTimes[ctKey]);
    const filterStatuses = Object.keys(filter.statuses || {})
      .filter((stKey: string) => filter.statuses[stKey])
      .map((stKey: string) => stKey === Status.Active.toString());
    const isRules = filter.rules && filter.rules.length > 0,
      isAllQuaters = !filter.category,
      isFilterRuleApplications = filterRuleApplications.length > 0,
      isShortDescription = filter.shortDescription,
      isFilterCheckTime = filterCheckTime.length > 0,
      isFilterStatuses = filterStatuses.length > 0;
    setFiltersCount(
      +!!isRules +
      +!isAllQuaters +
      +!!isFilterRuleApplications +
      +!!isShortDescription +
      +!!isFilterCheckTime +
      +!!isFilterStatuses
    );

    const isValidCheckTime = (
      filterCheckTime: Record<CheckTime, boolean>,
      checkTime: CheckTime
    ) => {
      if (checkTime === CheckTime.CheckTime_CodingAndBilling) {
        return (
          filterCheckTime[CheckTime.CheckTime_Coding] ||
          filterCheckTime[CheckTime.CheckTime_Billing]
        );
      }

      if (checkTime === CheckTime.CheckTime_SelectableAndBilling) {
        return (
          filterCheckTime[CheckTime.CheckTime_Selectable] ||
          filterCheckTime[CheckTime.CheckTime_Billing]
        );
      }

      return filterCheckTime[checkTime];
    };
    return getApiBufferData().filter((item: IcdRule) => {
      return (
        // rules
        (isRules
          ? filter.rules.findIndex(
            (flItem: FilterRule) => flItem.key === item.id
          ) !== -1
          : true) &&
        // quarter
        (isAllQuaters
          ? true
          : filter.category === Categories.QUATER
            ? !!item.quarters.charCodeAt(0)
            : !item.quarters.charCodeAt(0)) &&
        // rule applications
        (isFilterRuleApplications
          ? filterRuleApplications.indexOf(item.ruleApplication) !== -1
          : true) &&
        // short description
        (isShortDescription
          ? item.shortDescription
            .toLowerCase()
            .indexOf(filter.shortDescription.trim().toLowerCase()) !== -1
          : true) &&
        // check time
        (isFilterCheckTime
          ? isValidCheckTime(filter.checkTimes, item.checkTime)
          : true) &&
        // status
        (isFilterStatuses ? filterStatuses.indexOf(item.active) !== -1 : true)
      );
    });
  };

  const loadIcdRules = async () => {
    await getSettingIcdRules().then((data: IcdRule[]) => {
      apiBuffer.data = data;
      setICDRules(filterData());
    });
  };

  const updateRule = async (rule: IcdRule): Promise<void> => {
    return updateIcdRule(rule).then(async (rs) => {
      if (rs.status === 200) {
        await loadIcdRules();
      } else {
        throw rs;
      }
    });
  };

  useEffect(() => {
    loadIcdRules();
  }, []);

  useEffect(() => {
    setICDRules(filterData());
  }, [filter]);

  const viewDetailHandler = (ruleId: string) => {
    setSelectedIcdRuleId(ruleId);
    setIsRuleDetailModalOpen(true);
  };

  const dropdownCheckTimeSelectRender = (
    item: CheckTimeRuleOption,
    { handleClick }
  ) => {
    return (
      <MenuItem
        key={item.text}
        text={item.text}
        onClick={(event: React.MouseEvent<HTMLElement, MouseEvent>) => {
          const customizedEvent: CustomizedEvent = {
            ...event,
            customizedValue: item.data,
          };
          handleClick(customizedEvent);
        }}
        shouldDismissPopover={true}
      />
    );
  };

  const onChangeStatus = (rule: IcdRule, newStatus: string) => {
    const item2Update = apiBuffer.data.find((item) => item.id === rule.id);
    if (!item2Update || item2Update.active === (newStatus === Status.Active)) {
      return;
    }
    item2Update.active = Status.Active === newStatus;
    updateRule(item2Update).then(() => {
      toaster(t('statusSaved'));
    });
  };

  const renderRuleStatus = (
    rule: IcdRule,
    componentType: StatusComponentType
  ) => {
    const title = t(rule.active ? 'statusActive' : 'statusInactive');
    if (rule.ruleApplication !== RuleApplication.RuleApplication_Optional) {
      return (
        <span className={`status${rule.active ? ' active' : ' inactive'}`}>
          {title}
        </span>
      );
    }

    if (componentType === StatusComponentType.Radio) {
      return (
        <RadioGroup
          onChange={(e) => onChangeStatus(rule, e.currentTarget.value)}
          inline
          selectedValue={rule.active ? Status.Active : Status.Inactive}
        >
          <Radio label={t('statusActive')} value={Status.Active} />
          <Radio label={t('statusInactive')} value={Status.Inactive} />
        </RadioGroup>
      );
    }

    return (
      <Switch
        label={title}
        checked={rule.active}
        onChange={() =>
          onChangeStatus(rule, rule.active ? Status.Inactive : Status.Active)
        }
      />
    );
  };

  const onChangeCheckTime = (rule: IcdRule, checkTimeData: CheckTime) => {
    const index = apiBuffer.data.findIndex((item) => item.id === rule.id);
    const item2Update = apiBuffer.data[index];
    const hasNoFoundIndex = index === -1;
    const hasNoChangeCheckTime = checkTimeData === item2Update.checkTime;
    const hasIncorrectRuleCase =
      checkTimeData === CheckTime.CheckTime_Selectable &&
      !rule.quarters.charCodeAt(0);
    if (hasNoFoundIndex || hasNoChangeCheckTime || hasIncorrectRuleCase) {
      return;
    }

    item2Update.checkTime = checkTimeData;
    updateRule(item2Update).then(() => {
      toaster(t('checkTimeSaved'));
    });
  };

  const renderCheckTime = (
    rule: IcdRule,
    componentType: StatusComponentType
  ) => {
    const selectedValue = treatmentRelatedRuleOptions
      .concat(quarterlyRuleOptions)
      .find((ruleOption) => ruleOption.data === rule.checkTime);
    const options = !rule.quarters.charCodeAt(0)
      ? treatmentRelatedRuleOptions
      : quarterlyRuleOptions;

    if (componentType === StatusComponentType.Radio) {
      return (
        <RadioGroup
          onChange={(e) =>
            onChangeCheckTime(rule, e.currentTarget.value as CheckTime)
          }
          inline
          selectedValue={selectedValue.data}
        >
          {options.map((option) => (
            <Radio key={option.text} label={option.text} value={option.data} />
          ))}
        </RadioGroup>
      );
    }

    return (
      <Select
        items={options}
        itemRenderer={dropdownCheckTimeSelectRender}
        filterable={false}
        noResults={<MenuItem disabled={true} text={t('noResults')} />}
        onItemSelect={(_, event: React.SyntheticEvent) => {
          const checkTimeData: CheckTime = (event as CustomizedSyntheticEvent)
            .customizedValue;
          onChangeCheckTime(rule, checkTimeData);
        }}
        popoverProps={{ usePortal: true }}
      >
        <Button
          text={selectedValue?.text}
          rightIcon="caret-down"
          className="button-select"
        />
      </Select>
    );
  };

  const handleSort = (field: string, _order: Order | '') => {
    setSortType({
      field,
      order: _order,
    });
  };

  const genColumns = () => {
    return [
      {
        name: (
          <Flex justify="space-between">
            <span style={{ paddingTop: 3 }}>{t('tableHeaderID')}</span>
            <SortComponent
              className="sl-sort-icon"
              isSorted={sortType.field === 'id'}
              onChangeSortOrder={(order) => handleSort('id', order)}
            />
          </Flex>
        ),
        selector: (row) => row.id,
        width: '10%',
      },
      {
        name: (
          <Flex justify="space-between">
            <span style={{ paddingTop: 3 }}>
              {t('tableHeaderShortDescription')}
            </span>
            <SortComponent
              className="sl-sort-icon"
              isSorted={sortType.field === 'shortDescription'}
              onChangeSortOrder={(order) =>
                handleSort('shortDescription', order)
              }
            />
          </Flex>
        ),
        selector: (row) => row.shortDescription,
        cell: (row: IcdRule) => {
          return (
            <a onClick={() => viewDetailHandler(row.id)}>
              {row.shortDescription}
            </a>
          );
        },
        width: '44%',
      },
      {
        name: (
          <Flex justify="space-between">
            <span style={{ paddingTop: 3 }}>
              {t('tableHeaderRuleApplication')}
            </span>
            <SortComponent
              className="sl-sort-icon"
              isSorted={sortType.field === 'ruleApplication'}
              onChangeSortOrder={(order) =>
                handleSort('ruleApplication', order)
              }
            />
          </Flex>
        ),
        selector: (row) => row.ruleApplication,
        width: '11%',
      },
      {
        name: (
          <Flex justify="space-between">
            <span style={{ paddingTop: 3 }}>{t('tableHeaderQuarters')}</span>
            <SortComponent
              className="sl-sort-icon"
              isSorted={sortType.field === 'quarters'}
              onChangeSortOrder={(order) => handleSort('quarters', order)}
            />
          </Flex>
        ),
        selector: (row) => row.quarters,
        cell: (row: IcdRule) => {
          const currentQuarter = row.quarters.charCodeAt(0);
          return currentQuarter ? row.quarters.charCodeAt(0) : null;
        },
        width: '11%',
      },
      {
        name: (
          <Flex justify="space-between">
            <span style={{ paddingTop: 3 }}>{t('tableHeaderCheckTime')}</span>
            <SortComponent
              className="sl-sort-icon"
              isSorted={sortType.field === 'checkTime'}
              onChangeSortOrder={(order) => handleSort('checkTime', order)}
            />
          </Flex>
        ),
        selector: (row) => row.checkTime,
        width: '14%',
        cell: (row: IcdRule) => {
          return renderCheckTime(row, StatusComponentType.Selection);
        },
      },
      {
        name: (
          <Flex justify="space-between">
            <span style={{ paddingTop: 3 }}>{t('tableHeaderStatus')}</span>
            <SortComponent
              className="sl-sort-icon"
              isSorted={sortType.field === 'active'}
              onChangeSortOrder={(order) => handleSort('active', order)}
            />
          </Flex>
        ),
        selector: (row) => row.active,
        cell: (row: IcdRule) => {
          return renderRuleStatus(row, StatusComponentType.Toggle);
        },
        width: '8%',
      },
      {
        name: '',
        cell: (row: IcdRule) => {
          return (
            <a onClick={() => viewDetailHandler(row.id)}>
              <InfoSign />
            </a>
          );
        },
        minWidth: '40px',
        maxWidth: '40px',
      },
    ];
  };

  const filterBtnClickHandler = () => setIsFilterModalOpen(true);
  const filterModalCloseHandler = (filterData: FilterData | null) => {
    setIsFilterModalOpen(false);
    if (filterData != null) {
      setFilter(filterData);
    }
  };

  return (
    <div className={className}>
      <div className="rule__header">
        <H1>{t('codingRules')}</H1>
      </div>
      <div className="rule__content">
        <div className="rule__header__actions">
          <Button
            icon={<IconFilter color={filtersCount > 0 && COLOR.ICON_INFO} />}
            className={'filter ' + (filtersCount > 0 ? 'active' : '')}
            text={t('filter') + (filtersCount > 0 ? ` (${filtersCount})` : '')}
            onClick={filterBtnClickHandler}
          />
          <Button
            intent="primary"
            icon={<Svg src="/images/refresh.svg" />}
            text={'Update'}
            onClick={() => setOpenChangeLog(true)}
          />
        </div>
        <Table
          noHeader={true}
          columns={genColumns()}
          data={sortedRules}
          pagination
          fixedHeader={true}
          highlightOnHover={true}
          selectableRowsHighlight={true}
          fixedHeaderScrollHeight="calc(100vh - 230px)"
        />
        <RuleDetailDialog
          renderRuleStatus={renderRuleStatus}
          renderCheckTime={renderCheckTime}
          isOpen={isRuleDetailModalOpen}
          onClose={ruleDetailModalCloseHandler}
          rule={sortedRules?.find((r) => r.id === selectedIcdRuleId)}
        />
      </div>
      <FilterDialog
        rules={
          getApiBufferData().map((r) => ({
            key: r.id,
            value: r.id,
          })) || []
        }
        data={filter}
        isOpen={isFilterModalOpen}
        onChange={filterModalCloseHandler}
      />

      <ChangeLogDialog
        isOpen={isChangeLogOpen}
        onClose={() => setOpenChangeLog(false)}
      />
      <AltStyle />
    </div>
  );
};

export default Rules;
