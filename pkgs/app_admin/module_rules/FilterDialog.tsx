import React, { useEffect } from 'react';

import type I18nJson from '@tutum/admin/locales/en/CodingRules.json';
import { Button, Dialog } from '@tutum/design-system/components/Core';
import { alertSuccessfully, Flex } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import FilterForm from './filter-form/FilterForm.styled';
import { Form, Formik } from 'formik';
import { KeyPairValue, RuleApplicationType, StatusType } from './types';
import { Categories } from './Rules';
import { CheckTime } from '@tutum/hermes/bff/masterdata_common';

export type FilterDialogProps = {
  className?: string;
  rules: KeyPairValue[];
  isOpen: boolean;
  data: FilterData;
  onChange: (data: FilterData | null) => void;
};

export type FilterRule = {
  key: string;
  value: string;
};
export type FilterData = {
  category?: Categories;
  rules: FilterRule[];
  shortDescription?: string;
  ruleApplications: { [key in RuleApplicationType]: boolean };
  checkTimes: Record<CheckTime, boolean>;
  statuses: { [key in StatusType]: boolean };
};

const FilterDialog = ({
  className,
  rules,
  isOpen,
  data,
  onChange,
}: FilterDialogProps) => {
  let currentFormik: any = null;
  const { t } = I18n.useTranslation<keyof typeof I18nJson>({
    namespace: 'CodingRules',
  });
  const showFilterAppliedMsg = () => {
    alertSuccessfully(t('filterApplied'));
  };
  const onFilterModalCloseHandler = () => {
    onChange?.(null);
  };
  const onSubmitDataHandler = (values: FilterData) => {
    if (onChange) {
      onChange(values);
      showFilterAppliedMsg();
    }
  };
  const handleReset = () => {
    if (currentFormik) {
      currentFormik.setValues({} as FilterData);
      onChange({} as FilterData);
      showFilterAppliedMsg();
    }
  };

  useEffect(() => {
    if (currentFormik) {
      currentFormik.setValues(data);
    }
  }, [data]);

  return (
    <Dialog
      className="dialog-right"
      isOpen={isOpen}
      title={t('filter')}
      onClose={onFilterModalCloseHandler}
      canOutsideClickClose={false}
    >
      <Formik initialValues={data} onSubmit={onSubmitDataHandler}>
        {(formProps) => {
          currentFormik = formProps;
          return (
            <Form className={className}>
              <FilterForm rules={rules} />
              <Flex className="footer" justify="flex-end">
                <Button intent="none" onClick={handleReset}>
                  {t('reset')}
                </Button>
                <Button type="submit" intent="primary">
                  {t('applyFilter')}
                </Button>
              </Flex>
            </Form>
          );
        }}
      </Formik>
    </Dialog>
  );
};

export default FilterDialog;
