import type CodingRulesI18n from '@tutum/admin/locales/en/CodingRules.json';

import { useMemo } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import { BodyTextM, Flex } from '@tutum/design-system/components';
import {
  ChangeLogType,
  getSDKRWChangeLogs,
} from '@tutum/hermes/bff/legacy/app_admin';
import { useQuery } from '@tutum/infrastructure/hook';
import {
  StyledChangeLogItem,
  StyledChangeLogList,
  StyledInfoConfirmDialog,
} from './ChangelogDialog.styled';
import { COLOR } from '@tutum/design-system/themes/styles';

const loadChangeLogs = async () => {
  const res = await getSDKRWChangeLogs();
  if (!res?.data?.changeLogs?.length) return null!;
  return res?.data;
};

export default function ChangeLogDialog({ isOpen, onClose }) {
  const { t } = I18n.useTranslation<keyof typeof CodingRulesI18n>({
    namespace: 'CodingRules',
  });
  const { data, isLoading } = useQuery(loadChangeLogs);

  const changeLogs = useMemo(() => data?.changeLogs ?? [], [data?.changeLogs]);

  return (
    <StyledInfoConfirmDialog
      title="ICD - Kodierregel Aktualisierungen"
      isConfirmButtonShown={false}
      isCancelButtonShown={false}
      isOpen={isOpen}
      onClose={onClose}
    >
      <StyledChangeLogList column gap={16}>
        {isLoading ? (
          <BodyTextM>{t('calculatingChangeLog')}</BodyTextM>
        ) : (
          <>
            <Flex column gap={8}>
              {!changeLogs?.length ? (
                <BodyTextM>{t('noChanges')}</BodyTextM>
              ) : (
                <>
                  <BodyTextM fontWeight="Bold">
                    Aktualisierungen zum:&nbsp;
                    {/* {formatUnixToDateString(data?.effectFrom)} */}
                  </BodyTextM>

                  <BodyTextM>Geänderte Regeln</BodyTextM>
                </>
              )}
            </Flex>

            {changeLogs.map(
              ({
                ruleId,
                checkTime,
                ruleApplication,
                description,
                shortDescription,
                errorHandlingNote,
                changeLogType,
              }) => (
                <StyledChangeLogItem
                  key={ruleId}
                  column
                  gap={2}
                  style={{
                    backgroundColor:
                      changeLogType === ChangeLogType.ChangeLogType_New
                        ? COLOR.TAG_BACKGROUND_GREEN_SUBTLE
                        : COLOR.TAG_BACKGROUND_RED_SUBTLE,
                  }}
                >
                  <Flex column gap={1}>
                    <BodyTextM>
                      <strong>ID:&nbsp;</strong>
                      <strong>{ruleId}</strong> ({checkTime}) {ruleApplication}
                    </BodyTextM>
                    <BodyTextM>
                      <strong>Kurzbeschreibung:&nbsp;</strong>
                      {shortDescription}
                    </BodyTextM>
                    <BodyTextM>
                      <strong>Beschreibung:&nbsp;</strong>
                      {description}
                    </BodyTextM>
                    <BodyTextM>
                      <strong>Hinweis:&nbsp;</strong>
                      <i>{errorHandlingNote}</i>
                    </BodyTextM>
                  </Flex>
                </StyledChangeLogItem>
              )
            )}
          </>
        )}
      </StyledChangeLogList>
    </StyledInfoConfirmDialog>
  );
}
