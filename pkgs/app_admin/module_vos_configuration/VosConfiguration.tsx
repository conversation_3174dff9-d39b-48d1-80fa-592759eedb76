import React from 'react';
import isEmpty from 'lodash/isEmpty';

import type Settings from '@tutum/admin/locales/en/Settings.json';
import type Common from '@tutum/admin/locales/en/Common.json';

import {
  Button,
  Flex,
  FormGroup2,
  H1,
  H3,
  LoadingState,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { Divider, InputGroup } from '@tutum/design-system/components/Core';
import { SPACE_NUMBER } from '@tutum/design-system/styles';
import {
  SettingsFeatures,
  TLSKeys,
  VOSKeys,
  TLSTransport,
} from '@tutum/hermes/bff/legacy/settings_common';
import I18n from '@tutum/infrastructure/i18n';
import Tls from '@tutum/admin/components/tls';
import { FastField, Form, Formik } from 'formik';
import {
  useMutationSaveSetting,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_admin_settings';
import FormUtils, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';

const FIELD_NAMES = {
  TLS: TLSKeys.TLSKeys_Tls,
  HOST: VOSKeys.VOSKeys_Host,
  PORT: VOSKeys.VOSKeys_Port,
  USERNAME: TLSKeys.TLSKeys_UserName,
  PASSWORD: TLSKeys.TLSKeys_Password,
  CERTIFICATE_PATH: TLSKeys.TLSKeys_Certificate,
} as const;

const initialValues = {
  [FIELD_NAMES.HOST]: '',
  [FIELD_NAMES.PORT]: '',
  [FIELD_NAMES.TLS]: '',
  [FIELD_NAMES.USERNAME]: '',
  [FIELD_NAMES.PASSWORD]: '',
  [FIELD_NAMES.CERTIFICATE_PATH]: '',
};

export interface VosConfigurationProps {
  className?: string;
}

const VosConfiguration = ({ className }: VosConfigurationProps) => {
  const { t } = I18n.useTranslation<keyof typeof Settings.VosConfiguration>({
    namespace: 'Settings',
    nestedTrans: 'VosConfiguration',
  });
  const { t: tTls } = I18n.useTranslation<keyof typeof Settings.Tls>({
    namespace: 'Settings',
    nestedTrans: 'Tls',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof Common.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: tFormValidation } = I18n.useTranslation<
    keyof typeof Common.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const { data, isLoading, isRefetching, refetch } = useQueryGetSettings(
    {
      feature: SettingsFeatures.SettingsFeatures_VOS,
      settings: [],
    },
    {
      enabled: true,
    }
  );
  const { mutateAsync: mutateSaveSetting, isPending } = useMutationSaveSetting({
    onSuccess: () => {
      alertSuccessfully(t('vosConfigurationSaved'));
    },
  });

  const onSubmit = async (payload) => {
    await mutateSaveSetting({
      feature: SettingsFeatures.SettingsFeatures_VOS,
      settings: payload,
    });
    await refetch();
  };

  const onValidate = (values) => {
    const validateFields: ValidateField[] = [];
    const tlsValue = values[FIELD_NAMES.TLS];
    validateFields.push({
      fieldName: FIELD_NAMES.HOST,
      validateRule: () => isEmpty(values[FIELD_NAMES.HOST]),
      errorMessage: tFormValidation('fieldRequired'),
    });
    validateFields.push({
      fieldName: FIELD_NAMES.PORT,
      validateRule: () => isEmpty(values[FIELD_NAMES.PORT]),
      errorMessage: tFormValidation('fieldRequired'),
    });
    validateFields.push({
      fieldName: FIELD_NAMES.TLS,
      validateRule: () => isEmpty(tlsValue),
      errorMessage: tFormValidation('fieldRequired'),
    });
    if (tlsValue === TLSTransport.TLS_TLSAuthenByUserNameAndPassword) {
      validateFields.push({
        fieldName: FIELD_NAMES.USERNAME,
        validateRule: () => isEmpty(values?.tLSUsername),
        errorMessage: tFormValidation('fieldRequired'),
      });
      validateFields.push({
        fieldName: FIELD_NAMES.PASSWORD,
        validateRule: () => isEmpty(values?.tLSPassword),
        errorMessage: tFormValidation('fieldRequired'),
      });
    }
    if (tlsValue === TLSTransport.TLS_TLSAuthenByClientCertificate) {
      validateFields.push({
        fieldName: FIELD_NAMES.CERTIFICATE_PATH,
        validateRule: () => isEmpty(values?.certificatePath),
        errorMessage: tFormValidation('fieldRequired'),
      });
    }

    const { errors } = FormUtils.validateForm(validateFields, null);
    return errors;
  };

  return (
    <Flex column w={'100%'} className={className}>
      <Flex className="sl-vos-configuration__header">
        <H1>{t('title')}</H1>
      </Flex>
      <Divider style={{ margin: 0 }} />
      <Flex column className="sl-vos-configuration__main">
        {isLoading ? (
          <LoadingState />
        ) : (
          <Formik
            initialValues={data?.settings || initialValues}
            onSubmit={onSubmit}
            validate={onValidate}
            enableReinitialize
            validateOnChange={false}
            validateOnBlur={true}
          >
            {({ submitCount, touched, errors, dirty, resetForm }) => {
              const isDisabled = !dirty || isRefetching;
              return (
                <Form>
                  <Flex className="sl-vos-configuration__form-body" column>
                    <Flex gap={160}>
                      <Flex w={85}>
                        <H3>{t('general')}</H3>
                      </Flex>

                      <Flex column gap={SPACE_NUMBER.SPACE_S} w="100%">
                        <FormGroup2
                          label={t('host')}
                          isRequired
                          name={FIELD_NAMES.HOST}
                          submitCount={submitCount}
                          errors={errors}
                          touched={touched}
                        >
                          <FastField name={FIELD_NAMES.HOST}>
                            {({ field }) => (
                              <InputGroup
                                {...field}
                                data-tab-id={field.name}
                                maxLength={40}
                              />
                            )}
                          </FastField>
                        </FormGroup2>
                        <FormGroup2
                          label={t('port')}
                          isRequired
                          name={FIELD_NAMES.PORT}
                          submitCount={submitCount}
                          errors={errors}
                          touched={touched}
                        >
                          <FastField name={FIELD_NAMES.PORT}>
                            {({ field }) => (
                              <InputGroup
                                {...field}
                                data-tab-id={field.name}
                                isNumeric
                                maxLength={20}
                              />
                            )}
                          </FastField>
                        </FormGroup2>
                        <FormGroup2
                          isRequired
                          label={tTls('tls')}
                          name={FIELD_NAMES.TLS}
                          submitCount={submitCount}
                          errors={errors}
                          touched={touched}
                        >
                          <Tls
                            submitCount={submitCount}
                            touched={touched}
                            errors={errors}
                          />
                        </FormGroup2>
                      </Flex>
                    </Flex>
                  </Flex>
                  <Flex
                    className="sl-vos-configuration__form-footer"
                    justify="flex-end"
                    py={SPACE_NUMBER.SPACE_M}
                  >
                    <Flex gap={SPACE_NUMBER.SPACE_XS}>
                      <Button
                        large
                        intent="primary"
                        outlined
                        minimal
                        disabled={isDisabled}
                        loading={isPending}
                        onClick={resetForm}
                      >
                        {tButtonActions('cancelText')}
                      </Button>
                      <Button
                        disabled={isDisabled}
                        loading={isPending}
                        large
                        type="submit"
                        intent="primary"
                      >
                        {tButtonActions('saveText')}
                      </Button>
                    </Flex>
                  </Flex>
                </Form>
              );
            }}
          </Formik>
        )}
      </Flex>
    </Flex>
  );
};

export default VosConfiguration;
