import React, { memo, useState, useMemo, useCallback, useEffect } from 'react';
import debounce from 'lodash/debounce';

import type BsnrManagementI18n from '@tutum/admin/locales/en/BsnrManagement.json';

import { Flex, MessageBar, Button } from '@tutum/design-system/components';
import { InputGroup, Icon } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import I18n from '@tutum/infrastructure/i18n';
import { handleReduceRVSA } from '@tutum/admin/module_bsnr-management/create-bsnr/CreateBsnr.helper';
import { useCreateBsnrStore } from '@tutum/admin/module_bsnr-management/create-bsnr/CreateBsnr.store';
import { FIELD_NAMES } from '@tutum/admin/module_bsnr-management/create-bsnr/CreateBsnr.const';
import {
  RVSACertificateStatus,
  RVSACertificateReagents,
  RVSACertificate,
} from '@tutum/hermes/bff/legacy/bsnr_common';
import { handleChangeCheckbox } from '../RvsaCertificate.helper';
import { IRVSACustom } from '../RvsaCertificate';
import {
  genColumns,
  customStyles,
  conditionalRowStyles,
} from './setting-table';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IRvsaTableProps {
  className?: string;
  rvsaCertificateValues?: IRVSACustom[];
  rvsaCertificateReagents?: RVSACertificateReagents;
  setFieldValue?: (field: string, value: any) => void;
}

const RvsaTable = ({
  className,
  rvsaCertificateValues,
  rvsaCertificateReagents,
  setFieldValue,
}: IRvsaTableProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof BsnrManagementI18n.CreateBsnr.RvsaCertificate
  >({
    namespace: 'BsnrManagement',
    nestedTrans: 'CreateBsnr.RvsaCertificate',
  });

  const { listRVSACertificate, isEdit, isEditRVSA } = useCreateBsnrStore();

  const [collapses, setCollapses] = useState<Record<string, boolean>>({});
  const [listRVSAState, setListRVSAState] = useState<RVSACertificate[]>([]);

  useEffect(() => {
    setListRVSAState(listRVSACertificate);
  }, [listRVSACertificate]);

  const mapData = useMemo(() => {
    const dataResult: IRVSACustom[] = [];
    const reducedData = handleReduceRVSA(listRVSAState);
    const collapseObj = {};
    for (const key in reducedData) {
      collapseObj[key] = true;
      dataResult.push({ material: key, isTitleGroup: true } as IRVSACustom);
      reducedData[key].forEach((item: IRVSACustom) => {
        dataResult.push(item);
      });
    }
    setCollapses(collapseObj);
    return dataResult;
  }, [listRVSAState]);

  const onChangeCheckbox = useCallback(
    debounce((row: IRVSACustom) => {
      const handledData = handleChangeCheckbox(
        row,
        rvsaCertificateValues!,
        rvsaCertificateReagents!,
        mapData
      );
      setFieldValue?.(FIELD_NAMES.RVSA_CERTIFICATE, handledData);
    }, 300),
    [rvsaCertificateValues, mapData, rvsaCertificateReagents]
  );

  const handleCollapse = useCallback(
    debounce((row: IRVSACustom) => {
      setCollapses({
        ...collapses,
        [row.material]: !collapses[row.material],
      });
    }, 300),
    [collapses]
  );

  const handleChangeCertificateStatus = useCallback(
    (row: IRVSACustom, value: string) => {
      const changedData = rvsaCertificateValues?.map((item) => {
        if (item.labParameter === row.labParameter) {
          return {
            ...item,
            certificateStatus: value,
          };
        }
        return item;
      });
      setFieldValue?.(FIELD_NAMES.RVSA_CERTIFICATE, changedData);
    },
    [rvsaCertificateValues]
  );

  const handlerChangeCertificateStatus = useCallback(
    (value) => {
      const changedData = rvsaCertificateValues?.map((item) => {
        return {
          ...item,
          certificateStatus: value,
        };
      });
      setFieldValue?.(FIELD_NAMES.RVSA_CERTIFICATE, changedData);
    },
    [rvsaCertificateValues]
  );

  const listCertificateStatus = [
    {
      label: t('yes'),
      value: RVSACertificateStatus.YES,
    },
    {
      label: t('no'),
      value: RVSACertificateStatus.NO,
    },
    {
      label: t('pnSDAnalyse'),
      value: RVSACertificateStatus.PNSD_UU_ANALYSE,
    },
  ];

  const memoColumns = useMemo(() => {
    return genColumns({
      rvsaValues: rvsaCertificateValues!,
      collapses,
      isEdit,
      isEditRVSA,
      listCertificateStatus,
      t,
      onChangeCheckbox,
      handleCollapse,
      rvsaCertificateReagents: rvsaCertificateReagents!,
      handleChangeCertificateStatus,
    });
  }, [
    rvsaCertificateValues,
    onChangeCheckbox,
    collapses,
    handleCollapse,
    isEdit,
    isEditRVSA,
  ]);

  const handleSearch = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (!value) {
      setListRVSAState(listRVSACertificate);
      return;
    }
    const filteredList = listRVSACertificate?.filter((item) =>
      JSON.stringify(item).toLowerCase().includes(value.toLowerCase())
    );
    setListRVSAState(filteredList);
  }, 500);

  return (
    <Flex className={className} column gap={16}>
      <InputGroup
        defaultValue=""
        placeholder={t('searchPlaceholder')}
        onChange={handleSearch}
        className="sl-input-search"
        leftIcon={<Icon icon="search" color={COLOR.TEXT_TERTIARY_SILVER} />}
      />
      {isEdit && (
        <MessageBar
          type="info"
          content={t('highlightServiceCodeNote')}
          hasBullet={false}
        />
      )}
      <Flex className="container-lst-btn">
        <Button
          className="btn-set-all"
          disabled={rvsaCertificateValues && rvsaCertificateValues.length === 0}
          style={{ marginRight: '4px' }}
          onClick={() =>
            handlerChangeCertificateStatus(
              RVSACertificateStatus.PNSD_UU_ANALYSE
            )
          }
        >
          {t('btnSetCertificateStatusPnSd')}
        </Button>
        <Button
          className="btn-set-all"
          disabled={rvsaCertificateValues && rvsaCertificateValues.length === 0}
          onClick={() =>
            handlerChangeCertificateStatus(RVSACertificateStatus.NO)
          }
        >
          {t('btnSetCertificateStatusYes')}
        </Button>
      </Flex>
      <Table
        columns={memoColumns}
        data={mapData}
        customStyles={customStyles}
        striped
        noHeader
        persistTableHead
        fixedHeader
        fixedHeaderScrollHeight="700px"
        conditionalRowStyles={conditionalRowStyles(collapses)}
      />
    </Flex>
  );
};

export default memo(RvsaTable);
