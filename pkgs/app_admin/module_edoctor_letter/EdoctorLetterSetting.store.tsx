import { proxy, useSnapshot } from 'valtio';

import { EABSetting } from '@tutum/hermes/bff/eab_common';

interface EdoctorLetterStore {
  saveEdoctorSetting: EABSetting | undefined;
}

interface IActions {
  setEabSetting: (setting: EABSetting) => void;
}

const initStore: EdoctorLetterStore = {
  saveEdoctorSetting: undefined,
};

const store = proxy<EdoctorLetterStore>(initStore);

export const eABSettingAction: IActions = {
  setEabSetting: (setting) => {
    store.saveEdoctorSetting = setting;
  },
};

export function useEabSettingStore() {
  return useSnapshot(store);
}
