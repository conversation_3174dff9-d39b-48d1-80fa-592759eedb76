import React from 'react';
import { Link } from '@tutum/design-system/components';

export interface IActionsCellProps {
  className?: string;
  account?: any;
  configureAccount?: (account: any) => void;
  resetPassword?: (account: any) => void;
  removeAccount?: (account: any) => void;
}

const ActionsCell: React.ComponentType<IActionsCellProps> = (props) => {
  const {
    className,
    account,
    configureAccount,
    removeAccount,
    resetPassword,
  } = props;
  return (
    <div className={className}>
      <Link
        onClick={() => configureAccount?.(account)}
        className="accounts__actionsCell__item accounts__actionsCell__configure-item"
      >
        Configure
      </Link>
      <Link
        onClick={() => resetPassword?.(account)}
        className="accounts__actionsCell__item accounts__actionsCell__reset-password-item"
      >
        Reset password
      </Link>
      <Link
        onClick={() => removeAccount?.(account)}
        className="accounts__actionsCell__item accounts__actionsCell__remove-item"
      >
        Remove
      </Link>
    </div>
  );
};

export default ActionsCell;
