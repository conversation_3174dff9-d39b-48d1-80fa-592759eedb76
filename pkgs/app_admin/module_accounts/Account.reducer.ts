import { UpdateWithSideEffect, Update } from 'use-reducer-with-side-effects';
import AccountService from '@tutum/admin/module_accounts/Account.service';
import { IAccount } from '../types/account.type';

export const ACTION_TYPES = {
  FETCH_ACCOUNTS: 'FETCH_ACCOUNTS',
  FETCH_ACCOUNTS_SUCCESS: 'FETCH_ACCOUNTS_SUCCESS',
  FETCH_ACCOUNTS_FAILURE: 'FETCH_ACCOUNTS_FAILURE',
  ADD_ACCOUNT: 'ADD_ACCOUNT',
  ADD_ACCOUNT_SUCCESS: 'ADD_ACCOUNT_SUCCESS',
  ADD_ACCOUNT_FAILURE: 'ADD_ACCOUNT_FAILURE',
  UPDATE_ACCOUNT: 'UPDATE_ACCOUNT',
  UPDATE_ACCOUNT_SUCCESS: 'UPDATE_ACCOUNT_SUCCESS',
  UPDATE_ACCOUNT_FAILURE: 'UPDATE_ACCOUNT_FAILURE',
  REMOVE_ACCOUNT: 'REMOVE_ACCOUNT',
  REMOVE_ACCOUNT_SUCCESS: 'REMOVE_ACCOUNT_SUCCESS',
  REMOVE_ACCOUNT_FAILURE: 'REMOVE_ACCOUNT_FAILURE',
  UPDATE_PERMISSIONS: 'UPDATE_PERMISSIONS',
  UPDATE_PERMISSIONS_SUCCESS: 'UPDATE_PERMISSIONS_SUCCESS',
  UPDATE_PERMISSIONS_FAILURE: 'UPDATE_PERMISSIONS_FAILURE',
  RESET_PASSWORD: 'RESET_PASSWORD',
  RESET_PASSWORD_SUCCESS: 'RESET_PASSWORD_SUCCESS',
  RESET_PASSWORD_FAILURE: 'RESET_PASSWORD_FAILURE',
};

export const initialState = {
  accounts: [],
  loading: false,
};

export const AccountsReducer = (state: {
  accounts: IAccount[];
  loading: boolean;
} = initialState, action) => {
  switch (action.type) {
    case ACTION_TYPES.FETCH_ACCOUNTS: {
      return UpdateWithSideEffect(
        { ...state, loading: true },
        (_state, dispatch) => {
          AccountService.getAccounts()
            .then((accounts) => {
              dispatch({
                type: ACTION_TYPES.FETCH_ACCOUNTS_SUCCESS,
                accounts,
              });
            })
            .catch((error) => {
              console.error(error);
              dispatch({
                type: ACTION_TYPES.FETCH_ACCOUNTS_FAILURE,
                error,
              });
            });
        }
      );
    }
    case ACTION_TYPES.FETCH_ACCOUNTS_SUCCESS: {
      return Update({
        ...state,
        loading: false,
        accounts: action.accounts,
      });
    }
    case ACTION_TYPES.FETCH_ACCOUNTS_FAILURE: {
      return Update({
        ...state,
        loading: false,
      });
    }

    case ACTION_TYPES.ADD_ACCOUNT: {
      return UpdateWithSideEffect(
        { ...state, loading: true },
        (state, dispatch) => {
          AccountService.createAccount(action.account)
            .then((account) => {
              dispatch({
                type: ACTION_TYPES.ADD_ACCOUNT_SUCCESS,
                account,
              });
            })
            .catch((error) => {
              console.error(error);
              dispatch({
                type: ACTION_TYPES.ADD_ACCOUNT_FAILURE,
                error,
              });
            });
        }
      );
    }
    case ACTION_TYPES.ADD_ACCOUNT_SUCCESS: {
      return Update({
        ...state,
        loading: false,
        accounts: [action.account, ...state.accounts],
      });
    }
    case ACTION_TYPES.ADD_ACCOUNT_FAILURE: {
      return Update({
        ...state,
        loading: false,
      });
    }

    case ACTION_TYPES.UPDATE_ACCOUNT: {
      return UpdateWithSideEffect(
        { ...state, loading: true },
        (state, dispatch) => {
          AccountService.updateAccount(action.account)
            .then((account) => {
              dispatch({
                type: ACTION_TYPES.UPDATE_ACCOUNT_SUCCESS,
                account,
              });
            })
            .catch((error) => {
              console.error(error);
              dispatch({
                type: ACTION_TYPES.UPDATE_ACCOUNT_FAILURE,
                error,
              });
            });
        }
      );
    }
    case ACTION_TYPES.UPDATE_ACCOUNT_SUCCESS: {
      const updatedAccount = action.account;
      const accounts = state.accounts.map((account) => {
        if (account.id === updatedAccount.id) {
          return { ...account, ...updatedAccount };
        }
        return account;
      });
      return Update({
        ...state,
        loading: false,
        accounts,
      });
    }
    case ACTION_TYPES.UPDATE_ACCOUNT_FAILURE: {
      return Update({
        ...state,
        loading: false,
      });
    }

    case ACTION_TYPES.REMOVE_ACCOUNT: {
      return UpdateWithSideEffect(
        { ...state, loading: true },
        (_state, dispatch) => {
          AccountService.removeAccount(action.account)
            .then((account) => {
              dispatch({
                type: ACTION_TYPES.REMOVE_ACCOUNT_SUCCESS,
                account,
              });
            })
            .catch((error) => {
              console.error(error);
              dispatch({
                type: ACTION_TYPES.REMOVE_ACCOUNT_FAILURE,
                error,
              });
            });
        }
      );
    }
    case ACTION_TYPES.REMOVE_ACCOUNT_SUCCESS: {
      const accounts = state.accounts.filter(
        (account) => account.id !== action.account.id
      );
      return Update({
        ...state,
        loading: false,
        accounts,
      });
    }
    case ACTION_TYPES.REMOVE_ACCOUNT_FAILURE: {
      return Update({
        ...state,
        loading: false,
      });
    }
    case ACTION_TYPES.UPDATE_PERMISSIONS: {
      return UpdateWithSideEffect(
        { ...state, loading: true },
        (_state, dispatch) => {
          Promise.all(
            action.accounts.map((account) =>
              AccountService.updatePermissions(account)
            )
          )
            .then((accounts) => {
              dispatch({
                type: ACTION_TYPES.UPDATE_PERMISSIONS_SUCCESS,
                accounts,
              });
            })
            .catch((error) => {
              console.error(error);
              dispatch({
                type: ACTION_TYPES.UPDATE_PERMISSIONS_FAILURE,
                error,
              });
            });
        }
      );
    }
    case ACTION_TYPES.UPDATE_PERMISSIONS_SUCCESS: {
      const updatedAccounts = action.accounts;
      const accounts = state.accounts.map((account) => {
        const updatedAccount = updatedAccounts.find(
          (acc) => acc.id === account.id
        );
        if (updatedAccount) {
          return { ...updatedAccount };
        }
        return account;
      });
      return Update({
        ...state,
        loading: false,
        accounts,
      });
    }
    case ACTION_TYPES.UPDATE_PERMISSIONS_FAILURE: {
      return Update({
        ...state,
        loading: false,
      });
    }

    case ACTION_TYPES.RESET_PASSWORD: {
      return UpdateWithSideEffect(
        { ...state, loading: true },
        (_state, dispatch) => {
          AccountService.resetPassword(action.id, action.newPassword)
            .then((account) => {
              dispatch({
                type: ACTION_TYPES.RESET_PASSWORD_SUCCESS,
                account,
              });
            })
            .catch((error) => {
              console.error(error);
              dispatch({
                type: ACTION_TYPES.RESET_PASSWORD_FAILURE,
                error,
              });
            });
        }
      );
    }
    case ACTION_TYPES.RESET_PASSWORD_SUCCESS: {
      return Update({
        ...state,
        loading: false,
      });
    }
    case ACTION_TYPES.RESET_PASSWORD_FAILURE: {
      return Update({
        ...state,
        loading: false,
      });
    }

    default: {
      return state;
    }
  }
};

export default AccountsReducer;
