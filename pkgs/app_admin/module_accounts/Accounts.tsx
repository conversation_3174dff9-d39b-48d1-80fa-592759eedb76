import React, { useEffect, useState } from 'react';
import useReducerWithSideEffects from 'use-reducer-with-side-effects';
import { H1, LoadingState } from '@tutum/design-system/components';
import { Button, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';

import {
  AccountsReducer,
  ACTION_TYPES,
  initialState,
} from '@tutum/admin/module_accounts/Account.reducer';
import ActionsCell from '@tutum/admin/module_accounts/actions-cell/ActionsCell.styled';
import AccountModal from '@tutum/admin/module_accounts/account-modal/AccountModal.styled';
import ResetPasswordModal from '@tutum/admin/module_accounts/reset-password-modal/ResetPasswordModal.styled';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog';

const Accounts = (props: any) => {
  const { className } = props;
  const [isAccountDialogOpen, setAccountDialogOpen] = useState(false);
  const [isRemoveConfirmationDialogOpen, setRemoveConfirmationDialogOpen] =
    useState(false);
  const [isResetPasswordDialogOpen, setResetPasswordDialogOpen] =
    useState(false);
  const [selectedAccount, setSelectedAccount] = useState<any>(null);
  const [{ accounts, loading }, dispatch] = useReducerWithSideEffects(
    AccountsReducer,
    initialState
  );
  useEffect(() => {
    dispatch({
      type: ACTION_TYPES.FETCH_ACCOUNTS,
    });
  }, []);

  const openAccountDialog = () => {
    setAccountDialogOpen(true);
  };
  const closeAccountDialog = () => {
    setSelectedAccount(null);
    setAccountDialogOpen(false);
  };

  const configureAccount = (account: any) => {
    setSelectedAccount(account);
    openAccountDialog();
  };

  const saveAccount = (account: any) => {
    closeAccountDialog();
    if (!account.id) {
      dispatch({
        type: ACTION_TYPES.ADD_ACCOUNT,
        account,
      });
      return;
    }
    dispatch({
      type: ACTION_TYPES.UPDATE_ACCOUNT,
      account,
    });
  };

  const openRemoveConfirmationDialog = (account) => {
    setSelectedAccount(account);
    setRemoveConfirmationDialogOpen(true);
  };

  const closeRemoveConfirmationDialog = () => {
    setSelectedAccount(null);
    setRemoveConfirmationDialogOpen(false);
  };

  const removeAccount = (account: any) => {
    dispatch({
      type: ACTION_TYPES.REMOVE_ACCOUNT,
      account,
    });
    setSelectedAccount(null);
    setRemoveConfirmationDialogOpen(false);
  };
  const openResetPasswordDialog = (account) => {
    setSelectedAccount(account);
    setResetPasswordDialogOpen(true);
  };
  const closeResetPasswordDialog = () => {
    setSelectedAccount(null);
    setResetPasswordDialogOpen(false);
  };
  const resetPassword = (account, newPassword) => {
    dispatch({
      type: ACTION_TYPES.RESET_PASSWORD,
      id: account.id,
      newPassword,
    });
    closeResetPasswordDialog();
  };
  const columns = [
    {
      name: 'Title',
      selector: (row) => row.employeeProfile?.title,
      sortable: true,
    },
    {
      name: 'First Name',
      selector: (row) => row.employeeProfile?.firstName,
      sortable: true,
    },
    {
      name: 'Last Name',
      selector: (row) => row.employeeProfile?.lastName,
      sortable: true,
    },
    {
      name: '',
      selector: (row) => row.actions,
      sortable: false,
      minWidth: '500px',
      cell: (row) => (
        <ActionsCell
          account={row}
          configureAccount={configureAccount}
          removeAccount={openRemoveConfirmationDialog}
          resetPassword={openResetPasswordDialog}
        />
      ),
    },
  ];
  if (loading) {
    return <LoadingState />;
  }
  return (
    <div className={className}>
      <div className="accounts__header">
        <H1>Accounts</H1>
        <div className="accounts__header__actions">
          <Button
            onClick={openAccountDialog}
            text="Create Account"
            intent={Intent.PRIMARY}
          />
        </div>
      </div>
      <div className="accounts__content">
        <Table
          noHeader={true}
          columns={columns}
          data={accounts}
          pagination
          fixedHeader={true}
          highlightOnHover={true}
          selectableRowsHighlight={true}
          fixedHeaderScrollHeight="calc(100vh - 200px)"
        />
      </div>
      <AccountModal
        closeModal={closeAccountDialog}
        isOpen={isAccountDialogOpen}
        save={saveAccount}
        account={selectedAccount}
      />
      <InfoConfirmDialog
        type="secondary"
        isOpen={isRemoveConfirmationDialogOpen}
        title="Remove"
        confirmText="Remove"
        cancelText="Cancel"
        isShowIconTitle={false}
        isCloseButtonShown={false}
        onClose={() => closeRemoveConfirmationDialog()}
        onConfirm={() => removeAccount(selectedAccount)}
      >
        Are you sure you want to remove{' '}
        {selectedAccount?.employeeProfile?.firstName}? This action cannot be
        undone.
      </InfoConfirmDialog>
      <ResetPasswordModal
        isOpen={isResetPasswordDialogOpen}
        account={selectedAccount}
        closeModal={closeResetPasswordDialog}
        save={resetPassword}
      />
    </div>
  );
};

export default Accounts;
