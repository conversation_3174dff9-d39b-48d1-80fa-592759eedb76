import React from 'react';
import useTranslation from 'next-translate/useTranslation';
import { Form, Formik } from 'formik';
import { isEmpty } from 'lodash';
import {
  Dialog,
  Classes,
  Button,
  Intent,
} from '@tutum/design-system/components/Core';
import { H3 } from '@tutum/design-system/components';
import { TITLES, GENDERS } from '@tutum/design-system/consts/employee';
import AccountForm from '@tutum/admin/module_accounts/account-form/AccountForm.styled';
import { IAccount } from '@tutum/admin/types/account.type';

export interface IAccountModalProps {
  isOpen: boolean;
  className?: string;
  closeModal: () => void;
  save: (account: IAccount) => void;
  account?: IAccount;
}

interface IAccountForm {
  title: string;
  gender: string;
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
}

const renderForm = (props, isEdit, errors, values) => {
  const { closeModal } = props;
  return (
    <Form className="account-modal__form">
      <div className={`${Classes.DIALOG_BODY} account-modal__body`}>
        <div className="account-modal__content">
          <div className="account-modal__content__left">
            <H3>Account info</H3>
          </div>
          <div className="account-modal__content__right">
            <AccountForm isEdit={isEdit} errors={errors} values={values} />
          </div>
        </div>
      </div>
      <div className={Classes.DIALOG_FOOTER}>
        <div
          className={`${Classes.DIALOG_FOOTER_ACTIONS} account-modal__content account-modal__footer`}
        >
          <Button onClick={closeModal} intent={Intent.NONE}>
            Close
          </Button>
          <Button type="submit" intent={Intent.PRIMARY}>
            Save
          </Button>
        </div>
      </div>
    </Form>
  );
};
const AccountModal = (props: IAccountModalProps) => {
  const { isOpen, className, closeModal, account, save } = props;
  const isEdit = !isEmpty(account);
  const { t } = useTranslation('Account');
  const onSubmitForm = (values: IAccountForm) => {
    const employeeProfile = {
      gender: values.gender,
      firstName: values.firstName,
      lastName: values.lastName,
      title: values.title,
      phoneNumber: values.phoneNumber,
    };
    if (isEmpty(account)) {
      const newAccount: IAccount = {
        password: values.password,
        employeeProfile,
      };
      save(newAccount);
      return;
    }
    const updatedAccount = { ...account };
    updatedAccount.employeeProfile = employeeProfile;
    save(updatedAccount);
  };
  const defaultValue = isEmpty(account)
    ? {
      title: t(TITLES[0]),
      gender: t(GENDERS[0]),
      firstName: '',
      lastName: '',
      password: '',
      confirmPassword: '',
      phoneNumber: '',
    }
    : {
      title: account.employeeProfile?.title,
      gender: account.employeeProfile?.gender,
      firstName: account.employeeProfile?.firstName,
      lastName: account.employeeProfile?.lastName,
      password: '',
      confirmPassword: '',
      phoneNumber: account.employeeProfile?.phoneNumber,
    };
  const dialogTitle = isEdit ? 'Edit Account' : 'Create Account';
  return (
    <Dialog
      className={`bp5-dialog-fullscreen ${className}`}
      isOpen={isOpen}
      title={dialogTitle}
      onClose={closeModal}
      canOutsideClickClose={false}
    >
      <Formik
        onSubmit={onSubmitForm}
        initialValues={defaultValue}
        render={({ errors, values }) =>
          renderForm(props, isEdit, errors, values)
        }
      />
    </Dialog>
  );
};

export default AccountModal;
