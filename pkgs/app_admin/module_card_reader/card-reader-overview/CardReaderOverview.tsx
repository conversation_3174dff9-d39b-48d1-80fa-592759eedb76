import type {
  Card,
  PinType,
  TerminalWithCards,
  CardPin,
} from '@tutum/hermes/bff/card_common';
import type {
  AssignBSNRRequest,
  AssignDoctorRequest,
  AssignedCard,
} from '@tutum/hermes/bff/app_card_operation';
import type CardReaderI18n from '@tutum/admin/locales/en/CardReader.json';
import type CommonLocales from '@tutum/admin/locales/en/Common.json';
import type {
  CardAction,
  CardOperationConfirmDialogProps,
  OnCardClickFunc,
} from './CardReaderOverview.type';

import React, { useCallback, useEffect, useMemo, useState } from 'react';

import I18n from '@tutum/infrastructure/i18n';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import {
  FormGroup2,
  alertError,
  alertSuccessfully,
  ReactSelect,
  IMenuItem,
  InfoConfirmDialog,
  BannerSetting,
} from '@tutum/design-system/components';
import {
  useBsnrOverviewStore,
  bsnrOverviewActions,
} from '@tutum/admin/module_bsnr-management/bsnr-overview/BsnrOverview.store';
import {
  assignBSNR,
  assignDoctor,
  PinResult,
  unassignDoctor,
  unassignBSNR,
  useQueryGetCardsWithAssignedDoctorOrPractice,
  useQueryGetTerminals,
  GetTerminalsAndCardsResponse,
  useQueryGetCards,
  useQueryGetPinStatus,
  GetPinStatus,
} from '@tutum/hermes/bff/legacy/app_card_operation';
import { UserType } from '@tutum/hermes/bff/common';
import nameUtil from '@tutum/infrastructure/utils/name.utils';
import {
  StyledConfirmDialogBody,
  StyledHeading,
  StyledLayout,
  StyledNavBar,
} from './CardReaderOverview.styled';
import {
  CUSTOM_TABLE_STYLE,
  CUSTOM_EXPANDABLE_ICON,
  CUSTOM_INNER_TABLE_STYLE,
} from './CardReaderOverview.constant';
import {
  mainTableColumns,
  subTableColumns,
} from './CardReaderOverview.columns';
import {
  cardReaderOverviewActions,
  useCardReaderOverviewStore,
} from './CardReaderOverview.store';
import { CardReaderError, isCardReaderError } from './CardReaderOverview.util';
import { useErrorCodeI18n } from '@tutum/admin/hooks/useErrorCode';
import { ROUTING } from '@tutum/admin/types/route.type';
import { ErrorCode } from '@tutum/hermes/bff/error_code';

const DynamicConfirmDialog: React.FC<
  CardOperationConfirmDialogProps & {
    terminalName: string;
    onClose: () => void;
    onAssignToPractice: (payload: AssignBSNRRequest) => Promise<void>;
    onAssignToDoctor: (payload: AssignDoctorRequest) => Promise<void>;
    refetchTerminalsAndCards: () => Promise<any>;
    cardsAssigned: AssignedCard[];
  }
> = ({
  title,
  card,
  mandant,
  terminalName,
  onAssignToPractice,
  onAssignToDoctor,
  onClose,
  refetchTerminalsAndCards,
  buttonTitle,
  action,
  cardsAssigned,
}) => {
    const { t } = I18n.useTranslation<keyof typeof CardReaderI18n>({
      namespace: 'CardReader',
    });
    const {
      listBSNRs,
      listDoctors,
      isLoading: isBsnrStoreLoading,
      isLoadingDoctors,
    } = useBsnrOverviewStore();
    const [selectedBsnrCode, setSelectedBsnrCode] = useState<string>('');
    const [selectedDoctor, setSelectedDoctor] = useState<string>('');

    useEffect(() => {
      bsnrOverviewActions.getListBSNRs();
    }, []);

    useEffect(() => {
      if (!listBSNRs?.length || selectedBsnrCode != null) {
        return;
      }
      setSelectedBsnrCode(listBSNRs[0].bsnr?.code);
    }, [listBSNRs, selectedBsnrCode]);

    const getListDoctor = useCallback(() => {
      const doctors = cardsAssigned.length
        ? listDoctors.filter((doctor) =>
          cardsAssigned.some(
            (cardAssigned) => cardAssigned.doctorId !== doctor.id
          )
        )
        : listDoctors;

      return doctors
        .filter((doctor) => doctor.types.includes(UserType.DOCTOR))
        .map((doctor) => ({
          label: nameUtil.getDoctorName(doctor),
          value: doctor.id,
        }));
    }, [cardsAssigned, listDoctors]);

    if (action === 'assign' || action === 'assignToDoctor') {
      return (
        <InfoConfirmDialog
          isOpen
          title={title}
          confirmText={buttonTitle}
          isShowIconTitle={false}
          isCancelButtonShown={false}
          disableConfirm={selectedBsnrCode === card?.bSNRCoce}
          onClose={onClose}
          onConfirm={() => {
            if (!card?.iccsn) {
              return;
            }
            if (selectedDoctor) {
              onAssignToDoctor({
                doctorId: selectedDoctor,
                isscn: card.iccsn,
              }).finally(async () => {
                onClose();
                await refetchTerminalsAndCards();
              });
              return;
            }

            if (selectedBsnrCode === card?.bSNRCoce) return;

            onAssignToPractice({
              bSNRCode: selectedBsnrCode,
              isscn: card.iccsn,
            }).finally(async () => {
              onClose();
              await refetchTerminalsAndCards();
            });
          }}
        >
          <StyledConfirmDialogBody>
            {action === 'assignToDoctor' ? (
              <FormGroup2 name="doctor" isRequired label={t('doctor')}>
                <ReactSelect
                  isLoading={isLoadingDoctors}
                  selectedValue={selectedDoctor}
                  items={getListDoctor()}
                  onItemSelect={(opt: IMenuItem<string>) => {
                    setSelectedDoctor(opt.value);
                  }}
                />
              </FormGroup2>
            ) : (
              <FormGroup2 name="bsnrName" isRequired label={t('bsnrName')}>
                <ReactSelect
                  isLoading={isBsnrStoreLoading}
                  selectedValue={selectedBsnrCode}
                  items={listBSNRs.map((bsnr) => ({
                    label: bsnr.bsnr.name,
                    value: bsnr.bsnr.code,
                  }))}
                  onItemSelect={(opt: IMenuItem<string>) => {
                    setSelectedBsnrCode(opt.value);
                  }}
                  filterOption={(opt) => opt.value !== card?.bSNRCoce}
                />
              </FormGroup2>
            )}
          </StyledConfirmDialogBody>
        </InfoConfirmDialog>
      );
    }

    const { workplaceId, mandantId, clientSystemId } = mandant || {};

    return (
      <InfoConfirmDialog
        isOpen
        title={title}
        cancelText={t('cancel')}
        isShowIconTitle={false}
        isCloseButtonShown={false}
        isConfirmButtonShown={false}
        onClose={onClose}
      >
        <StyledConfirmDialogBody>
          <p id="title-card">{t('pleaseGoToTerminal')}</p>
          <p>
            <b>{t('cardReader')}:</b>&nbsp;{terminalName}&nbsp;(
            {card.iccsn})
          </p>
          <p>
            <b>{t('contextCallInfos')}</b>
            <p>
              {mandantId}&nbsp;/&nbsp;{clientSystemId}
              &nbsp;/&nbsp;{workplaceId}
            </p>
          </p>
        </StyledConfirmDialogBody>
      </InfoConfirmDialog>
    );
  };

const handleErrorMessages = (
  error: any,
  getCommonMsg: () => string,
  getRejectedMsg: (error: CardReaderError) => string,
  getBlockedMsg: (error: CardReaderError) => string
) => {
  if (!isCardReaderError(error)) {
    alertError(getCommonMsg());
    return;
  }

  const pinResult = error.getPinResult();

  switch (pinResult) {
    case PinResult.PinResultEnumREJECTED: {
      alertError(getRejectedMsg(error));
      break;
    }

    case PinResult.PinResultEnumWASBLOCKED:
    case PinResult.PinResultEnumNOWBLOCKED: {
      alertError(getBlockedMsg(error));
      break;
    }
  }
};

interface CardsRowsProps {
  data?: TerminalWithCards;
  onOpenConfirmDialog: (data: CardOperationConfirmDialogProps) => void;
  onCloseConfirmDialog: () => Promise<void>;
  isFetching: boolean;
}

type card = {
  title: string;
  card: Card;
  buttonTitle: string;
  action: CardAction;
  selectedPinType: PinType;
};

const CardsRows: React.FC<CardsRowsProps> = ({
  data,
  onCloseConfirmDialog,
  onOpenConfirmDialog,
  isFetching,
}) => {
  const { mandant, cards, terminalName } = data || {};
  const { connectorId } = mandant || {};
  const { t } = I18n.useTranslation<keyof typeof CardReaderI18n>({
    namespace: 'CardReader',
  });
  const { t: tConfirmSetNewPinDialog } = I18n.useTranslation<
    keyof typeof CardReaderI18n.ConfirmSetNewPinDialog
  >({
    namespace: 'CardReader',
    nestedTrans: 'ConfirmSetNewPinDialog',
  });

  const { listDoctors } = useBsnrOverviewStore();

  const [openConfirmSetNewPinDialog, setOpenConfirmSetNewPinDialog] =
    useState<boolean>(false);

  const [selectedCard, setSelectedCard] = useState<card | null>(null);

  const handleUnblockCard = useCallback(
    async (isSetNewPin: boolean) => {
      if (!selectedCard) return;
      try {
        onOpenConfirmDialog({
          ...selectedCard,
          mandant,
          terminalName,
        });
        await cardReaderOverviewActions.cardUnblockPin(
          connectorId as string,
          selectedCard.card,
          selectedCard.selectedPinType,
          isSetNewPin
        );
        setSelectedCard(null);
        setOpenConfirmSetNewPinDialog(false);
        alertSuccessfully(t('pinUnblocked'));
      } catch (error) {
        handleErrorMessages(
          error,
          () => t('pinUnblockedFailed'),
          (_error) =>
            `${t('pinUnblockedFailed')} ${t('leftTriesMessage', {
              retries: _error.getLeftTries(),
            })}`,
          () => t('pinUnblockedBlocked')
        );
      } finally {
        await onCloseConfirmDialog();
      }
    },
    [selectedCard]
  );

  const handleCardClick = useCallback<OnCardClickFunc>(
    async ({ title, card, buttonTitle, action, selectedPinType }) => {
      if (isFetching) {
        return;
      }

      if (action === 'unassignToDoctor' && card.iccsn) {
        try {
          await unassignDoctor({
            isscn: card.iccsn,
          });
          alertSuccessfully(t('doctorUnassigned'));
        } catch (error) {
          console.error('error', error);
          alertError(t('unassignDoctorFailed'));
        } finally {
          await onCloseConfirmDialog();
        }
        return;
      }

      if (action == 'unassign') {
        return unassignBSNR({
          isscn: card?.iccsn,
        })
          .then(() => {
            alertSuccessfully(t('practiceUnassigned'));
          })
          .catch(() => {
            alertError(t('practiceUnassigned'));
          })
          .finally(async () => {
            await onCloseConfirmDialog();
          });
      }

      if (action === 'unblock') {
        setSelectedCard({
          title,
          card,
          buttonTitle: buttonTitle || '',
          action,
          selectedPinType,
        });
        setOpenConfirmSetNewPinDialog(true);
        return;
      }

      // NOTE: show dialog
      onOpenConfirmDialog({
        action,
        title,
        buttonTitle,
        card,
        mandant,
        terminalName,
      });

      if (action === 'change' || action === 'initialize') {
        try {
          await cardReaderOverviewActions.cardChangePin(
            connectorId as string,
            card,
            selectedPinType
          );
          alertSuccessfully(t('pinChanged'));
        } catch (error) {
          handleErrorMessages(
            error,
            () => t('pinChangedFailed'),
            (_error) =>
              `${t('pinVerifiedFailed')} ${t('leftTriesMessage', {
                retries: _error.getLeftTries(),
              })}`,
            () => t('pinVerifiedBlocked')
          );
        } finally {
          await onCloseConfirmDialog();
        }
        return;
      }

      if (action === 'verify') {
        try {
          await cardReaderOverviewActions.cardVerifyPin(
            connectorId as string,
            card,
            selectedPinType
          );
          alertSuccessfully(t('pinVerified'));
        } catch (error) {
          handleErrorMessages(
            error,
            () => t('pinVerifiedFailed'),
            (_error) =>
              `${t('pinVerifiedFailed')} ${t('leftTriesMessage', {
                retries: _error.getLeftTries(),
              })}`,
            () => t('pinVerifiedBlocked')
          );
        } finally {
          await onCloseConfirmDialog();
        }
        return;
      }
    },
    [mandant, isFetching]
  );

  const columns = useMemo<IDataTableColumn<Card>[]>(
    () => subTableColumns(listDoctors, t, handleCardClick),
    [t, handleCardClick]
  );

  return (
    <>
      <Table
        striped
        noHeader
        noTableHead
        data={cards || []}
        columns={columns}
        customStyles={CUSTOM_INNER_TABLE_STYLE} // NOTE: to reverse the striped logic
      />

      {openConfirmSetNewPinDialog && (
        <InfoConfirmDialog
          isOpen={openConfirmSetNewPinDialog}
          title={tConfirmSetNewPinDialog('title')}
          confirmText={tConfirmSetNewPinDialog('btnConfirm')}
          cancelText={tConfirmSetNewPinDialog('btnCancel')}
          isShowIconTitle={false}
          isCloseButtonShown={false}
          onClose={() => handleUnblockCard(false)}
          onConfirm={() => handleUnblockCard(true)}
        >
          <StyledConfirmDialogBody>
            {tConfirmSetNewPinDialog('description')}
            <p>
              <b>{t('cardReader')}:</b>&nbsp;{terminalName}&nbsp;(
              {selectedCard?.card.iccsn})
            </p>
          </StyledConfirmDialogBody>
        </InfoConfirmDialog>
      )}
    </>
  );
};

const handleDeepCompare = (
  prevProps: CardsRowsProps,
  nextProps: CardsRowsProps
) => {
  return (
    prevProps.data?.cards.length === nextProps.data?.cards.length &&
    prevProps.isFetching === nextProps.isFetching &&
    prevProps.onCloseConfirmDialog === nextProps.onCloseConfirmDialog &&
    prevProps.onOpenConfirmDialog === nextProps.onOpenConfirmDialog
  );
};

const MemoizedCardsRows = React.memo(CardsRows, handleDeepCompare);


export const CardReaderOverview: React.FC = () => {
  const { t } = I18n.useTranslation<keyof typeof CardReaderI18n>({
    namespace: 'CardReader',
  });
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });

  const tError = useErrorCodeI18n();

  const { isLoading } = useCardReaderOverviewStore();

  const [dialogProps, setDialogProps] =
    useState<CardOperationConfirmDialogProps | null>(null);

  const columns = useMemo<IDataTableColumn<TerminalWithCards>[]>(
    () => mainTableColumns(t),
    [t]
  );

  const {
    data: resAssignedCard,
    isSuccess: isSuccessGetAssignedCard,
    isLoading: isLoadingGetAssignedCard,
    refetch: refetchAssignedCard,
  } = useQueryGetCardsWithAssignedDoctorOrPractice();

  const [resTerminalsAndCards, setResTerminalsAndCards] =
    useState<GetTerminalsAndCardsResponse | null>(null);
  const [connectorId, setConnectorId] = useState<string>('');
  const {
    data: resTerminals,
    isLoading: isLoadingGetTerminals,
    isSuccess: isSuccessGetTerminals,
    error: errorsGetTerminals,
  } = useQueryGetTerminals({ throwOnError: false });

  // Use single React Query hook for cards (since we have one TI connector)
  const {
    isSuccess: isSuccessGetCards,
    data: resCard,
    refetch: refetchGetCards,
    error: errorsGetCards,
    isLoading: isLoadingGetCards,
    isRefetching: isRefetchingGetCards,
  } = useQueryGetCards(
    { connectorId },
    {
      enabled: !!connectorId,
      throwOnError: false,
    }
  );

  // Prepare pin status payloads from cards
  const pinStatusPayload = useMemo(() => {
    if (!isSuccessGetCards || !resCard?.cards) {
      return [];
    }
    return resCard.cards.map((card) => ({
      connectorId,
      cardType: card.cardType,
      cardHandle: card.cardHandle,
      iccsn: card.iccsn,
      pinType: '' as PinType,
    }));
  }, [resCard, isSuccessGetCards, connectorId]);

  // Use React Query hook for pin status
  const {
    data: resPinStatus,
    isSuccess: isSuccessGetPinStatus,
    refetch: refetchGetPinStatus,
    isRefetching: isRefetchingGetPinStatus,
  } = useQueryGetPinStatus(
    { getPinStatus: pinStatusPayload },
    { enabled: pinStatusPayload.length > 0 }
  );

  useEffect(() => {
    if (!isSuccessGetTerminals || !resTerminals) {
      return;
    }
    // Since we have one TI connector, use the first terminal's connector ID
    const firstTerminal = resTerminals.terminals[0];
    if (firstTerminal) {
      setConnectorId(firstTerminal.mandant.connectorId);
    }
    
    // Initialize terminals with empty cards
    const terminalsWithCards = resTerminals.terminals.map(terminal => ({
      ...terminal,
      cards: [] as Card[]
    }));
    
    setResTerminalsAndCards({
      terminals: terminalsWithCards,
    });
  }, [resTerminals, isSuccessGetTerminals]);

  // Combine all terminals with their cards and pin status
  useEffect(() => {
    if (!resTerminals?.terminals.length) {
      return;
    }

    const cards = resCard?.cards || [];
    const cardPins = resPinStatus?.cardPins || [];
    
    // Create a map of cardPins by iccsn for efficient lookup
    const cardPinsByIccsn = cardPins.reduce((acc, cardPin) => {
      acc[cardPin.iccsn] = cardPin;
      return acc;
    }, {} as Record<string, CardPin>);
    
    // Combine cards with their pin status using iccsn matching
    const cardsWithPins = cards.map((card) => ({
      ...card,
      pins: cardPinsByIccsn[card.iccsn] ? [cardPinsByIccsn[card.iccsn]] : [],
    }));

    const terminalsWithCards = resTerminals.terminals.map((terminal) => {
      // Filter cards that belong to this specific terminal based on ctId
      const terminalCards = cardsWithPins.filter(card => card.ctId === terminal.terminalId);
      
      return {
        ...terminal,
        cards: terminalCards,
      };
    });

    setResTerminalsAndCards({
      terminals: terminalsWithCards,
    });
  }, [resTerminals, resCard, resPinStatus, isSuccessGetPinStatus]);


  // Error handling for cards query
  useEffect(() => {
    if (errorsGetCards) {
      alertError(errorsGetCards.response?.data?.message || 'Error fetching cards');
    }
  }, [errorsGetCards]);

  useEffect(() => {
    bsnrOverviewActions.getListDoctors();
  }, []);

  async function handleClose() {
    setDialogProps(null);
    // Refetch cards and pin status
    await refetchGetCards();
    await refetchGetPinStatus();
    await refetchAssignedCard();
  }

  async function assignToPractice(payload: AssignBSNRRequest) {
    if (isRefetchingGetCards || isRefetchingGetPinStatus) return;
    
    try {
      await assignBSNR(payload);
      alertSuccessfully(t('practiceAssigned'));
    } catch (error) {
      alertError(t('assignPracticeFailed'));
      throw error;
    } finally {
      await handleClose();
    }
  }

  async function assignToDoctor(payload: AssignDoctorRequest) {
    if (isRefetchingGetCards || isRefetchingGetPinStatus) return;
    
    try {
      await assignDoctor(payload);
      alertSuccessfully(t('doctorAssigned'));
    } catch (error) {
      alertError(t('assignDoctorFailed'));
      throw error;
    } finally {
      await handleClose();
    }
  }

  return (
    <StyledLayout>
      {errorsGetTerminals && (
        <BannerSetting
          className="banner"
          title={
            tError(errorsGetTerminals.name as any) ||
            errorsGetTerminals.response.data.message
          }
          redirect={{
            url:
              errorsGetTerminals.name === ErrorCode.ErrorCode_Companion_NotReady
                ? ROUTING.APP_CONFIGURATIONS_CARD_READER
                : ROUTING.TI_CONNECTOR,
            title:
              errorsGetTerminals.name === ErrorCode.ErrorCode_Companion_NotReady
                ? tCommon('reloadPage')
                : tCommon('gotoPage'),
          }}
        />
      )}
      <StyledNavBar>
        <StyledHeading>{t('cardReader')}</StyledHeading>
      </StyledNavBar>

      <Table
        className="sl-card-reader-table"
        noHeader
        columns={columns}
        data={resTerminalsAndCards?.terminals || []}
        progressPending={
          isLoading ||
          isLoadingGetAssignedCard ||
          isLoadingGetTerminals ||
          isLoadingGetCards ||
          isRefetchingGetCards ||
          isRefetchingGetPinStatus
        }
        expandableRows
        expandableRowExpanded={() => true}
        expandOnRowClicked
        expandableIcon={CUSTOM_EXPANDABLE_ICON}
        expandableRowsComponent={(row) => {
          const terminal = row.data as TerminalWithCards;
          const isCurrentTerminalLoading = isRefetchingGetCards || isRefetchingGetPinStatus;
          
          return (
            <MemoizedCardsRows
              {...row}
              data={terminal}
              onOpenConfirmDialog={setDialogProps}
              onCloseConfirmDialog={handleClose}
              isFetching={isCurrentTerminalLoading || false}
            />
          );
        }}
        customStyles={CUSTOM_TABLE_STYLE}
      />

      {dialogProps != null && (
        <DynamicConfirmDialog
          refetchTerminalsAndCards={async () => {
            // Refetch cards and pin status
            await refetchGetCards();
            await refetchGetPinStatus();
          }}
          title={dialogProps.title}
          action={dialogProps.action}
          buttonTitle={dialogProps.buttonTitle}
          card={dialogProps.card}
          mandant={dialogProps.mandant}
          terminalName={dialogProps.terminalName || ''}
          onAssignToPractice={assignToPractice}
          onAssignToDoctor={assignToDoctor}
          cardsAssigned={
            isSuccessGetAssignedCard ? resAssignedCard.assignedCards : []
          }
          onClose={() => {
            setDialogProps(null);
          }}
        />
      )}
    </StyledLayout>
  );
};
