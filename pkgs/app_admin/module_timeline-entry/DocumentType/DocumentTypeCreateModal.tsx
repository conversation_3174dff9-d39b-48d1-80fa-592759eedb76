import { useErrorCodeI18n } from '@tutum/admin/hooks/useErrorCode';
import type TimelineEntryLocale from '@tutum/admin/locales/en/TimelineEntry.json';
import {
  Button,
  Flex,
  FormGroup2,
  IMenuItem,
  InfoConfirmDialog,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { InputGroup } from '@tutum/design-system/components/Core';
import MultiSelect from '@tutum/design-system/components/Selects/MultiSelect';
import {
  TimelineDocumentType,
  createTimelineDocumentType,
  updateTimelineDocumentType,
} from '@tutum/hermes/bff/legacy/app_admin_timeline_document_type';
import { useQueryGetBSNR } from '@tutum/hermes/bff/legacy/app_bsnr';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import I18n from '@tutum/infrastructure/i18n';
import CommonLocales from '@tutum/admin/locales/en/Common.json';
import { <PERSON><PERSON>ield, Formik, FormikHelpers } from 'formik';
import { useState } from 'react';
import DocumentCard from '@tutum/admin/module_timeline-entry/DocumentType/DocumentCard.styled';
import isEqual from 'lodash/isEqual';

interface IProps {
  defaultValue?: TimelineDocumentType;
  closeModal: () => void;
  onCreateCallback: () => void;
}

type PartialDocument = Partial<TimelineDocumentType>;

const initialValue: PartialDocument = {
  name: '',
  abbreviation: undefined,
  bsnrs: [],
  description: '',
};

const DocumentTypeCreateModal = ({
  defaultValue,
  closeModal,
  onCreateCallback,
}: IProps) => {
  const { t } = I18n.useTranslation<keyof typeof TimelineEntryLocale>({
    namespace: 'TimelineEntry',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: tForm } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });
  const tError = useErrorCodeI18n();

  // const { data: bsnrData, isFetching } = useQueryGetBSNR({
  //   select: (res) => {
  //     const data = res.data?.data ?? [];
  //     initialValue = {
  //       ...initialValue,
  //       bsnrs: data.map((item) => item.bsnr.name),
  //     };
  //     return data.map((item) => ({
  //       label: item.bsnr.name,
  //       value: item.bsnr.name,
  //     }));
  //   },
  // });
  const [isShowDialog, setIsShowDialog] = useState(false);
  const isDisableEdit = defaultValue && !defaultValue.isCustom;

  const onSubmit: (
    values: PartialDocument,
    formikHelpers: FormikHelpers<PartialDocument>
  ) => void | Promise<any> = async (values, { setFieldError }) => {
    if (values.name?.length == 0) {
      setFieldError('name', tForm('fieldRequired'));
      return;
    }
    // if (values.bsnrs?.length == 0) {
    //   setFieldError('bsnrs', tForm('fieldRequired'));
    //   return;
    // }
    if (values.abbreviation?.length == 0) {
      setFieldError('abbreviation', tForm('fieldRequired'));
      return;
    }
    try {
      if (defaultValue) {
        await updateTimelineDocumentType({
          timelineDocumentType: {
            name: values.name,
            abbreviation: values.abbreviation,
            // bsnrs: values.bsnrs,
            bsnrs: [],
            description: values.description,
            id: values.id,
            isActive: values.isActive,
            isCustom: values.isCustom,
          },
        });
        alertSuccessfully(t('updatedDocumentType'));
      } else {
        await createTimelineDocumentType({
          name: values.name,
          abbreviation: values.abbreviation,
          // bsnrs: values.bsnrs,
          bsnrs: [],
          description: values.description,
        });
        alertSuccessfully(t('createdDocumentType'));
      }
    } catch (error) {
      const serverError = error?.response?.data?.serverError;
      switch (serverError) {
        case ErrorCode.ErrorCode_DocumentType_NameExisted:
          setFieldError('name', tError(serverError));
          return;
        case ErrorCode.ErrorCode_DocumentType_AbbrExisted:
          setFieldError('abbreviation', tError(serverError));
          return;
        default:
          throw error;
      }
    }
    onCreateCallback();
    closeModal();
  };

  const [isUpdating, setIsUpdating] = useState(false);

  const onChangeStatus = async (
    values: Partial<TimelineDocumentType>,
    isStatusActive: boolean
  ) => {
    setIsUpdating(true);
    try {
      await updateTimelineDocumentType({
        timelineDocumentType: {
          name: values.name,
          abbreviation: values.abbreviation,
          bsnrs: values.bsnrs,
          description: values.description,
          id: values.id,
          isActive: isStatusActive,
          isCustom: values.isCustom,
        },
      });
      alertSuccessfully(
        t(isStatusActive ? 'activateSuccess' : 'deactivateSuccess')
      );
    } finally {
      setIsUpdating(false);
      setIsShowDialog(false);
      onCreateCallback();
      closeModal();
    }
  };

  return (
    <Flex column p={32} w="50vw">
      <Formik<PartialDocument>
        initialValues={defaultValue || initialValue}
        enableReinitialize
        validate={(values) => {
          const errors: Partial<typeof values> = {};
          return errors;
        }}
        onSubmit={onSubmit}
      >
        {({
          submitForm,
          errors,
          touched,
          values,
          isSubmitting,
          initialValues,
        }) => {
          return (
            <Flex column gap={20}>
              {/* <FormGroup2 */}
              {/*   isRequired */}
              {/*   name="bsnrs" */}
              {/*   label={t('bsnr')} */}
              {/*   errors={errors} */}
              {/*   touched={touched} */}
              {/* > */}
              {/*   <FastField name="bsnrs"> */}
              {/*     {({ field, form }) => { */}
              {/*       return ( */}
              {/*         <MultiSelect */}
              {/*           isLoading={isFetching} */}
              {/*           defaultValue={field.value.map((e) => ({ */}
              {/*             label: e, */}
              {/*             value: e, */}
              {/*           }))} */}
              {/*           options={bsnrData} */}
              {/*           onChange={(newValue: IMenuItem[]) => { */}
              {/*             const mapValues = newValue?.map((item) => item.value); */}
              {/*             form.setFieldValue(field.name, mapValues); */}
              {/*           }} */}
              {/*         /> */}
              {/*       ); */}
              {/*     }} */}
              {/*   </FastField> */}
              {/* </FormGroup2> */}
              <Flex w="100%" gap={20}>
                <FormGroup2
                  style={{ flexGrow: 1 }}
                  isRequired
                  name="name"
                  label={t('name')}
                  errors={errors}
                  touched={touched}
                >
                  <FastField name="name">
                    {({ field, form }) => {
                      return (
                        <InputGroup
                          required
                          defaultValue={field.value}
                          onValueChange={(value) =>
                            form.setFieldValue(field.name, value)
                          }
                          disabled={isDisableEdit}
                        />
                      );
                    }}
                  </FastField>
                </FormGroup2>
                <FormGroup2
                  style={{ flexGrow: 1 }}
                  isRequired
                  name="abbreviation"
                  label={t('abbreviation')}
                  errors={errors}
                  touched={touched}
                >
                  <FastField name="abbreviation">
                    {({ field, form }) => {
                      return (
                        <InputGroup
                          required
                          placeholder={t('abbreviationHint')}
                          maxLength={3}
                          value={field.value}
                          pattern="[A-Za-z0-9]+"
                          disabled={isDisableEdit}
                          onValueChange={(value) => {
                            form.setFieldValue(
                              field.name,
                              value
                                .replace(/[^A-Za-z0-9]/g, '')
                                .toLocaleUpperCase()
                            );
                          }}
                        />
                      );
                    }}
                  </FastField>
                </FormGroup2>
              </Flex>
              <FormGroup2 name="description" label={t('description')}>
                <FastField name="description">
                  {({ field, form }) => {
                    return (
                      <InputGroup
                        required
                        defaultValue={field.value || ''}
                        onValueChange={(value) =>
                          form.setFieldValue(field.name, value)
                        }
                      />
                    );
                  }}
                </FastField>
              </FormGroup2>
              <DocumentCard command={values.abbreviation} />
              <Flex mt={12} justify="space-between">
                {defaultValue?.isActive ? (
                  <>
                    <Button
                      style={{ alignSelf: 'flex-start' }}
                      outlined
                      intent={'danger'}
                      onClick={() => {
                        setIsShowDialog(true);
                      }}
                    >
                      {t('deactivate')}
                    </Button>
                    <InfoConfirmDialog
                      type="danger"
                      isOpen={isShowDialog}
                      title={t('deactivateTitle')}
                      confirmText={t('confirmDeactivate')}
                      cancelText={tButtonActions('noText')}
                      isShowIconTitle={false}
                      isCloseButtonShown={false}
                      isLoading={isUpdating}
                      onClose={() => {
                        setIsShowDialog(false);
                      }}
                      onConfirm={() => {
                        onChangeStatus(values, false);
                      }}
                    >
                      {t('deactivateHint')}
                    </InfoConfirmDialog>
                  </>
                ) : (
                  <div></div>
                )}
                <Flex gap={16}>
                  <Button
                    outlined
                    intent="primary"
                    type="button"
                    disabled={isUpdating}
                    onClick={closeModal}
                  >
                    {tButtonActions('cancelText')}
                  </Button>
                  {defaultValue?.isActive === false && (
                    <Button
                      loading={isUpdating}
                      onClick={() => onChangeStatus(values, true)}
                      outlined
                      intent="primary"
                      type="button"
                    >
                      {t('activate')}
                    </Button>
                  )}
                  <Button
                    intent="primary"
                    disabled={isUpdating || isEqual(values, initialValues)}
                    loading={isSubmitting}
                    onClick={() => submitForm()}
                    type="submit"
                  >
                    {defaultValue
                      ? tButtonActions('saved')
                      : tButtonActions('create')}
                  </Button>
                </Flex>
              </Flex>
            </Flex>
          );
        }}
      </Formik>
    </Flex>
  );
};

export default DocumentTypeCreateModal;
