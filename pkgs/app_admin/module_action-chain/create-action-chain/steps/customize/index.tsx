import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import { InputGroup } from '@tutum/design-system/components/Core';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import {
  ActionChainCategory,
  CustomizeStep,
} from '@tutum/hermes/bff/action_chain_common';
import { CustomizeResponse } from '@tutum/hermes/bff/service_domains_patient_file';
import I18n from '@tutum/infrastructure/i18n';
import { FastField } from 'formik';
import { memo } from 'react';
import { FIELD_NAMES } from '../../CreateActionChain.const';

interface ICustomizeProps {
  index: number;
  customize: CustomizeStep;
}

const Customize = ({ index, customize }: ICustomizeProps) => {
  const { t } = I18n.useTranslation<keyof typeof ActionChainI18n.Category>({
    namespace: 'ActionChain',
    nestedTrans: 'Category',
  });

  const initFreetext: CustomizeResponse = {
    patientId: null!,
    encounterId: null!,
    description: '',
    sortOrder: null!,
    command: customize?.abbreviation!,
  };

  const freetextRequest = initFreetext;

  return (
    <FormGroup2 label={t(ActionChainCategory.ActionChainCategory_Customize)}>
      <FastField name={`${FIELD_NAMES.STEPS}.${index}.customize.customize`}>
        {({ field, form }) => (
          <InputGroup
            {...field}
            value={field?.value?.description ?? ''}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const fieldValue: CustomizeResponse = {
                ...freetextRequest,
                description: e.currentTarget.value,
              };
              form.setFieldValue(field.name, fieldValue);
            }}
          />
        )}
      </FastField>
    </FormGroup2>
  );
};

export default memo(Customize);
