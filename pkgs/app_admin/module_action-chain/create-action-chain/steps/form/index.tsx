import React, { memo } from 'react';
import {
  FormGroup2,
  ReactSelect,
  IMenuItem,
} from '@tutum/design-system/components';
import { FastField } from 'formik';
import I18n from '@tutum/infrastructure/i18n';
import PrinterProfile from '@tutum/admin/locales/en/PrinterProfile.json';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import { FIELD_NAMES } from '../../CreateActionChain.const';
import { useQueryGetForms } from '@tutum/hermes/bff/legacy/app_mvz_form';
import { Spinner } from '@tutum/design-system/components/Core';

interface IFormProps {
  index: number;
}

const Form = ({ index }: IFormProps) => {
  const { t } = I18n.useTranslation<keyof typeof ActionChainI18n.Form>({
    namespace: 'ActionChain',
    nestedTrans: 'Form',
  });
  const { t: tForm } = I18n.useTranslation<keyof typeof PrinterProfile.Forms>({
    namespace: 'PrinterProfile',
    nestedTrans: 'Forms',
  });

  const { data, isFetching } = useQueryGetForms(
    {
      oKV: null!,
      ikNumber: null!,
      contractId: null!,
      chargeSystemId: null!,
    },
    {
      select: (res) => {
        return (res.data?.forms ?? [])
          .filter((item) => !!item.formTab)
          .map((item) => ({
            label: tForm(item.id as any),
            value: item.id,
          }));
      },
    }
  );

  if (isFetching) {
    return <Spinner size={16} />;
  }

  return (
    <FormGroup2 label={t('form')}>
      <FastField name={`${FIELD_NAMES.STEPS}.${index}.form.formId`}>
        {({ field, form }) => {
          return (
            <ReactSelect
              id={`${FIELD_NAMES.STEPS}.${index}.form.formId`}
              instanceId={`${FIELD_NAMES.STEPS}.${index}.form.formId`}
              selectedValue={field.value}
              items={data}
              onItemSelect={(item: IMenuItem) => {
                form.setFieldValue(field.name, item?.value);
              }}
              menuPlacement="auto"
            />
          );
        }}
      </FastField>
    </FormGroup2>
  );
};

export default memo(Form);
