import React, { memo, useMemo } from 'react';
import { FastField } from 'formik';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import I18n from '@tutum/infrastructure/i18n';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import { InputGroup } from '@tutum/design-system/components/Core';
import { FIELD_NAMES } from '../../CreateActionChain.const';
import { FreeTextResponse } from '@tutum/hermes/bff/service_domains_patient_file';
import { ActionChainCategory } from '@tutum/hermes/bff/action_chain_common';

interface IFindingsProps {
  index: number;
  freetext: FreeTextResponse;
  stepCategory: ActionChainCategory;
}

const Findings = ({ index, stepCategory, freetext }: IFindingsProps) => {
  const { t } = I18n.useTranslation<keyof typeof ActionChainI18n.Category>({
    namespace: 'ActionChain',
    nestedTrans: 'Category',
  });

  const commandFreetext = useMemo(() => {
    switch (stepCategory) {
      case ActionChainCategory.ActionChainCategory_Findings:
        return 'B';
      case ActionChainCategory.ActionChainCategory_Anamnesis:
        return 'A';
      case ActionChainCategory.ActionChainCategory_Note:
        return 'N';
      case ActionChainCategory.ActionChainCategory_Therapy:
        return 'T';
      default:
        return null;
    }
  }, [stepCategory]);

  const initFreetext: FreeTextResponse = {
    patientId: null!,
    encounterId: null!,
    note: '',
    sortOrder: null!,
    command: commandFreetext!,
  };

  const freetextRequest = freetext ?? initFreetext;

  return (
    <FormGroup2 label={t(stepCategory)}>
      <FastField name={`${FIELD_NAMES.STEPS}.${index}.freeText.freeText`}>
        {({ field, form }) => (
          <InputGroup
            {...field}
            value={field?.value?.note ?? ''}
            onChange={(e) => {
              const fieldValue: FreeTextResponse = {
                ...freetextRequest,
                note: e.target.value,
              };
              form.setFieldValue(field.name, fieldValue);
            }}
          />
        )}
      </FastField>
    </FormGroup2>
  );
};

export default memo(Findings);
