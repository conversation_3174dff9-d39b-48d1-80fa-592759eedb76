import React, { memo, useMemo } from 'react';
import {
  Flex,
  H2,
  FormGroup2,
  ReactSelect,
  IMenuItem,
  Svg,
} from '@tutum/design-system/components';
import { Field } from 'formik';
import I18n from '@tutum/infrastructure/i18n';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import {
  CustomizeStep,
  ServiceChainStep,
  ServiceStep,
  ActionChainCategory,
  MedicationStep,
  ServiceCategory,
} from '@tutum/hermes/bff/legacy/action_chain_common';
import {
  DiagnoseResponse,
  FreeTextResponse,
} from '@tutum/hermes/bff/service_domains_patient_file';
import {
  FIELD_NAMES,
  getListCategory,
  INIT_STEP,
} from '../CreateActionChain.const';
import VitalParameters from './vital-parameters';
import Medication from './medication';
import Form from './form';
import Diagnose from './diagnose';
import Findings from './findings';
import ServiceCode from './service-code';
import <PERSON><PERSON>ode<PERSON>hain from '@tutum/admin/module_action-chain/create-action-chain/steps/service-code-chain/ServiceCodeChain.styled';
import { TimelineDocumentType } from '@tutum/hermes/bff/legacy/app_admin_timeline_document_type';
import Customize from './customize';

interface IStepProps {
  index: number;
  totalSteps: number;
  stepCategory?: ActionChainCategory;
  medication?: MedicationStep;
  diagnose?: DiagnoseResponse;
  freetext?: FreeTextResponse;
  service?: ServiceStep;
  serviceChain?: ServiceChainStep;
  customize?: CustomizeStep;
  editorRefs?: any;
  errors?: any;
  onRemove: () => void;
  documentTypes: TimelineDocumentType[];
}

type IMenuCategory = IMenuItem<string, { abbreviation: string; name: string }>;

const MinusIcon = '/images/minus-circle.svg';

const Step = ({
  index,
  totalSteps,
  stepCategory,
  medication,
  diagnose,
  freetext,
  service,
  serviceChain,
  customize,
  errors,
  onRemove,
  editorRefs,
  documentTypes,
}: IStepProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof ActionChainI18n.CreateActionChain
  >({
    namespace: 'ActionChain',
    nestedTrans: 'CreateActionChain',
  });
  const { t: tCategory } = I18n.useTranslation<
    keyof typeof ActionChainI18n.Category
  >({
    namespace: 'ActionChain',
    nestedTrans: 'Category',
  });
  const canRemoveStep = totalSteps > 1;

  const renderCategory = useMemo(() => {
    switch (stepCategory) {
      case ActionChainCategory.ActionChainCategory_VitalParameter:
        return <VitalParameters index={index} />;
      case ActionChainCategory.ActionChainCategory_Medication:
        return <Medication index={index} medication={medication!} />;
      case ActionChainCategory.ActionChainCategory_Form:
        return <Form index={index} />;
      case ActionChainCategory.ActionChainCategory_AcuteDiagnose:
      case ActionChainCategory.ActionChainCategory_PermanentDiagnose:
      case ActionChainCategory.ActionChainCategory_AnamnesticDiagnose:
        return (
          <Diagnose
            key={stepCategory}
            index={index}
            diagnose={diagnose!}
            stepCategory={stepCategory}
          />
        );
      case ActionChainCategory.ActionChainCategory_Findings:
      case ActionChainCategory.ActionChainCategory_Anamnesis:
      case ActionChainCategory.ActionChainCategory_Note:
      case ActionChainCategory.ActionChainCategory_Therapy:
        return (
          <Findings
            index={index}
            freetext={freetext!}
            stepCategory={stepCategory}
          />
        );
      case ActionChainCategory.ActionChainCategory_Service:
        return (
          <ServiceCode
            index={index}
            service={service!}
            errors={errors}
            stepCategory={stepCategory}
          />
        );
      case ActionChainCategory.ActionChainCategory_ServiceChain:
        return (
          <ServiceCodeChain
            index={index}
            serviceChain={serviceChain!}
            errors={errors}
            editorRefs={editorRefs}
          />
        );
      case ActionChainCategory.ActionChainCategory_Customize:
        return <Customize index={index} customize={customize!} />;
      default:
        return null;
    }
  }, [
    stepCategory,
    medication,
    diagnose,
    freetext,
    service,
    serviceChain,
    errors,
    editorRefs,
    customize,
  ]);

  const categories: IMenuCategory[] = useMemo(() => {
    return [
      ...getListCategory(tCategory),
      ...documentTypes.map(
        (d): IMenuCategory => ({
          label: d.name,
          value: d.abbreviation,
          data: {
            abbreviation: d.abbreviation,
            name: d.name,
          },
        })
      ),
    ];
  }, [tCategory, documentTypes]);

  return (
    <Flex column gap={16}>
      <Flex align="center" justify="space-between">
        <H2>
          {t('step')} {index + 1}
        </H2>
        {canRemoveStep && (
          <Svg className="sl-minus-icon" src={MinusIcon} onClick={onRemove} />
        )}
      </Flex>
      <FormGroup2 label={t('category')}>
        <Field name={`${FIELD_NAMES.STEPS}.${index}.stepCategory`}>
          {({ field, form }) => {
            return (
              <ReactSelect
                id={`${FIELD_NAMES.STEPS}.${index}.stepCategory`}
                instanceId={`${FIELD_NAMES.STEPS}.${index}.stepCategory`}
                selectedValue={
                  field.value ===
                    ActionChainCategory.ActionChainCategory_Customize
                    ? customize?.abbreviation
                    : field.value
                }
                items={categories}
                onItemSelect={(item: IMenuCategory) => {
                  form.setFieldValue(`${FIELD_NAMES.STEPS}.${index}`, {
                    ...INIT_STEP,
                    stepCategory: item.data?.abbreviation
                      ? ActionChainCategory.ActionChainCategory_Customize
                      : item.value,
                    service:
                      item?.value !==
                        ActionChainCategory.ActionChainCategory_Service
                        ? null
                        : {
                          serviceCategory:
                            ServiceCategory.Service_SelectiveContract,
                        },
                    serviceChain:
                      item?.value !==
                        ActionChainCategory.ActionChainCategory_ServiceChain
                        ? null
                        : {
                          serviceCategory:
                            ServiceCategory.Service_SelectiveContract,
                        },
                    customize: Boolean(item.data?.abbreviation)
                      ? {
                        abbreviation: item.data.abbreviation,
                        name: item.data?.name,
                      }
                      : null,
                  });
                }}
                menuPlacement="auto"
              />
            );
          }}
        </Field>
      </FormGroup2>
      {renderCategory}
    </Flex>
  );
};

export default memo(Step);
