import { $isAdditionalInfoBlockNode } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/AdditionalInfoBlock.node';
import { ServiceChainStep } from '@tutum/hermes/bff/legacy/action_chain_common';
import { EncounterServiceTimeline } from '@tutum/hermes/bff/legacy/repo_encounter';
import { ServiceResponse } from '@tutum/hermes/bff/legacy/service_domains_patient_file';
import { $isServiceNode } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/ServiceNode';
import { $getRoot, type EditorState } from 'lexical';

export function parseServiceChainFromLexicalState(
  editorState: EditorState
): ServiceChainStep {
  const services: ServiceResponse[] = [];
  let serviceTimeLine: EncounterServiceTimeline | undefined;
  editorState.read(() => {
    const root = $getRoot();
    const children = root.getChildren();
    for (const node of children) {
      if ($isServiceNode(node)) {
        if (serviceTimeLine) {
          services.push(convertFromServiceTimeline(serviceTimeLine!));
        }
        serviceTimeLine = node.__encounterService as EncounterServiceTimeline;
        if (serviceTimeLine) {
          serviceTimeLine.additionalInfos = [];
        }
      }
      if ($isAdditionalInfoBlockNode(node)) {
        const info = node.getInfo();
        serviceTimeLine?.additionalInfos?.push({
          fK: info.fK,
          value: info.value!,
          // dataId: info.dataId,
          children: info.additionalInformations?.map((child) => ({
            fK: child.fK,
            value: child.value!,
            dataId: child.dataId!,
          })),
        });
      }
    }
  });
  return {
    serviceCategory: undefined!,
    services,
    serviceChainStepRaw: JSON.stringify(editorState.toJSON()),
  };
}

function convertFromServiceTimeline(
  service: EncounterServiceTimeline
) {
  return {
    encounterId: null!,
    code: service.code,
    description: service.description,
    serviceMainGroup: service.serviceMainGroup,
    additionalInfos: service.additionalInfos,
    materialCosts: service.materialCosts,
    id: null!,
    sortOrder: 0,
    freeText: `(${service.code}) ${service.description}`,
    command: 'L',
    isPreParticipate: false,
    referralDoctorInfo: {
      bsnr: '',
      lanr: '',
      requiredLanr: false,
      requiredBsnr: false,
    },
    scheins: [],
    price: service.price!,
  };
}
