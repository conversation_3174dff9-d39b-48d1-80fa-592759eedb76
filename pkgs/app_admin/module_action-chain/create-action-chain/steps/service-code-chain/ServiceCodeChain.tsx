import { EditorRefPlugin } from '@lexical/react/LexicalEditorRefPlugin';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import PatientManagementI18n from '@tutum/admin/locales/en/PatientManagement.json';
import GoaServiceCode<PERSON>hain from '@tutum/admin/module_action-chain/create-action-chain/steps/service-code-chain/GoaServiceCodeChain';
import {
  Flex,
  FormGroup2,
  IMenuItem,
  ReactSelect,
} from '@tutum/design-system/components';
import { Radio, RadioGroup } from '@tutum/design-system/components/Core';
import { getAdditionalInfoList } from '@tutum/design-system/composer/assets/addtional-info';
import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import AdditionalInfoOmimGChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-omimg-chain-plugins';
import {
  JUSTIFICATION_BLOCK,
  PLACE_VISIT,
  REFERRAL_BLOCK,
  TOTAL_AMOUNT,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { ServiceCategory } from '@tutum/hermes/bff/action_chain_common';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/catalog_sdebm_common';
import { MainGroup } from '@tutum/hermes/bff/common';
import { ServiceChainStep } from '@tutum/hermes/bff/legacy/action_chain_common';
import {
  OmimGChain,
  searchOmimGChain,
} from '@tutum/hermes/bff/legacy/app_catalog_omimg_chain';
import { searchEbmsComposer } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import {
  saveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import { Order } from '@tutum/hermes/bff/legacy/common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import WebWorker from '@tutum/infrastructure/web-worker-services';
import { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.const';
import CreateJusitficationModal from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/goa-service-block/justification-modal/CreateJustification.styled';
import ServiceChainTextEditor from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/ServiceChainTextEditor';
import OmimGChainOverview from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/OmimGChainOverView';
import OmimGCreateDialog from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/create-dialog/OmimGCreateDialog.styled';
import CreateMaterialCostDialog from '@tutum/mvz/module_sdebm/material-cost-dialog/MaterialCostDialog.styled';
import { Field } from 'formik';
import { NodeKey } from 'lexical';
import debounce from 'lodash/debounce';
import React, { useMemo, useState } from 'react';
import { FIELD_NAMES } from '../../CreateActionChain.const';
import { useCreateActionChainStore } from '../../CreateActionChain.store';
import { getServiceListHasContract } from '../service-code/ServiceCode.helper';
import AdditionalInfoHgncChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-hgnc-chain-plugins';
import HgncCreateDialog from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/create-dialog/HgncCreateDialog.styled';
import HgncChainOverview from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/HgncChainOverView';
import { HgncChain, searchHgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import { SystemDateKey, useQueryGetSetting } from '@tutum/hermes/bff/legacy/app_mvz_settings';
import { SettingsFeatures } from '@tutum/hermes/bff/legacy/settings_common';

export interface IProps {
  className?: string;
  index: number;
  serviceChain: ServiceChainStep;
  errors: any;
  editorRefs: any;
}

const ServiceCodeChain = ({
  className,
  index,
  serviceChain,
  errors,
  editorRefs,
}: IProps) => {
  const { t } = I18n.useTranslation<keyof typeof ActionChainI18n.ServiceCode>({
    namespace: 'ActionChain',
    nestedTrans: 'ServiceCode',
  });
  const hgncAvailableDate = new Date("2025-07-01T00:00:00")
  const { data, isSuccess } = useQueryGetSetting({
    feature: SettingsFeatures.SettingsFeatures_SystemDate,
    settings: [SystemDateKey.SystemDateKey_CurrentDate],
  });

  const systemDate = useMemo(() => {
    return isSuccess && data?.settings?.[SystemDateKey.SystemDateKey_CurrentDate]
      ? new Date(
        parseInt(data.settings[SystemDateKey.SystemDateKey_CurrentDate])
      )
      : datetimeUtil.date();

  }, [isSuccess, data?.settings]);

  const { t: tServiceNode } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: composerTranslator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const { contracts } = useCreateActionChainStore();

  const [isGoaService, setIsGoaService] = useState(
    serviceChain?.serviceCategory === 'GOA'
  );

  const [createOmimGChainDialogState, setCreateOmimGChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: OmimGChain | null;
    }>({
      isOpen: false,
      defaultValue: null,
    });
  const [createHgncChainDialogState, setCreateHgncChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: HgncChain | null;
    }>({
      isOpen: false,
      defaultValue: null,
    });
  const [listChainDialogState, setListChainDialogState] = useState<{
    isOpen: boolean;
    query: string;
    additionalInfoNodeKey: NodeKey | null;
  }>({
    isOpen: false,
    query: '',
    additionalInfoNodeKey: null,
  });

  const [isOpenCreateMaterialCost, setIsOpenCreateMaterialCostDialog] =
    useState(false);
  const { data: listSettingJustification, refetch } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });
  const [isOpenCreateJustification, setIsOpenCreateJustification] =
    useState(false);

  const currentYearQuarter = datetimeUtil.toQuarterYear(datetimeUtil.now());
  const additionalInfoListFromJson = getAdditionalInfoList(currentYearQuarter);

  const handleSearch = debounce(async (query: string, cb: any) => {
    const tasksList: Promise<any>[] = [];
    tasksList.push(
      searchEbmsComposer({
        query,
        selectedDate: datetimeUtil.now(),
      }).then((res) => {
        return (res?.data?.items || []).map(
          (item) =>
            ({
              code: item.code,
              description: item.description,
              evaluation: item.evaluation,
              unit: item.unit,
              isSelfCreated: item.isSelfCreated,
              mainGroup: MainGroup.KV,
            }) as IContractData
        );
      })
    );
    if (serviceChain?.contractId) {
      tasksList.push(
        getServiceListHasContract(query, serviceChain?.contractId)
      );
    }

    const results = await Promise.all(tasksList);

    const rs: IContractData[] = [];

    results[0].forEach((kvItem) => {
      kvItem.mainGroup = MainGroup.KV;
      rs.push(kvItem);
    });

    if (results.length === 2) {
      results[1].forEach((hzvItem) => {
        hzvItem.mainGroup = MainGroup.HZV;
        rs.push(hzvItem);
      });
    }

    cb?.(rs);
  }, 300);

  const getAdditionalInfoByServiceType = (serviceType: string) => {
    switch (serviceType) {
      case 'EBM':
        return additionalInfoListFromJson.filter(
          (info) => ![TOTAL_AMOUNT, REFERRAL_BLOCK].includes(info.fK)
        );
      case 'SelectiveContract':
        return additionalInfoListFromJson.filter((info) =>
          [PLACE_VISIT, JUSTIFICATION_BLOCK, REFERRAL_BLOCK].includes(info.fK)
        );
      case 'GOA':
        return additionalInfoListFromJson;

      default:
        return [];
    }
  };

  const additionalInfoList = React.useMemo<AdditionalInfoField[]>(() => {
    return getAdditionalInfoByServiceType(serviceChain?.serviceCategory).map(
      (info) => {
        info.label = composerTranslator(info.fK);
        return info as unknown as AdditionalInfoField;
      }
    );
  }, [serviceChain?.serviceCategory]);

  const onChange = (form, field) => (e) => {
    const value: ServiceCategory = e.currentTarget.value as ServiceCategory;
    form.setFieldValue(field.name, value);
    form.setFieldValue(
      `${FIELD_NAMES.STEPS}.${index}.serviceChain.services`,
      null
    );
    form.setFieldValue(
      `${FIELD_NAMES.STEPS}.${index}.serviceChain.goaServices`,
      null
    );

    if (value !== ServiceCategory.Service_SelectiveContract) {
      form.setFieldValue(
        `${FIELD_NAMES.STEPS}.${index}.serviceChain.contractId`,
        null
      );
    }
    if (value === ServiceCategory.Service_GOA) {
      form.setFieldValue(
        `${FIELD_NAMES.STEPS}.${index}.serviceChain.contractId`,
        null
      );
      setIsGoaService(true);
    } else {
      setIsGoaService(false);
    }
  };
  return (
    <Flex className={className} column gap={16}>
      <FormGroup2
        label={t('labelServiceType')}
        name={`${FIELD_NAMES.STEPS}.${index}.serviceChain.serviceCategory`}
        errors={errors}
        submitCount={1}
      >
        <Field
          name={`${FIELD_NAMES.STEPS}.${index}.serviceChain.serviceCategory`}
        >
          {({ field, form }) => (
            <RadioGroup
              onChange={onChange(form, field)}
              inline
              selectedValue={
                field.value ?? ServiceCategory.Service_SelectiveContract
              }
            >
              <Radio
                label={t(ServiceCategory.Service_EBM)}
                value={ServiceCategory.Service_EBM}
              />
              <Radio
                label={t(ServiceCategory.Service_SelectiveContract)}
                value={ServiceCategory.Service_SelectiveContract}
              />
              <Radio
                label={t(ServiceCategory.Service_GOA)}
                value={ServiceCategory.Service_GOA}
              />
            </RadioGroup>
          )}
        </Field>
      </FormGroup2>
      {serviceChain?.serviceCategory ===
        ServiceCategory.Service_SelectiveContract && (
          <FormGroup2 label={t('labelContract')}>
            <Field name={`${FIELD_NAMES.STEPS}.${index}.serviceChain.contractId`}>
              {({ field, form }) => (
                <ReactSelect
                  selectedValue={field.value}
                  items={
                    contracts?.map((item) => ({
                      value: item?.contractId,
                      label: item?.contractName,
                    })) ?? []
                  }
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item?.value);
                  }}
                  menuPlacement="auto"
                />
              )}
            </Field>
          </FormGroup2>
        )}
      {isGoaService ? (
        <GoaServiceCodeChain
          t={t}
          index={index}
          tServiceNode={tServiceNode}
          editorRefs={editorRefs}
          serviceChain={serviceChain}
          className="sl-GoaServiceCodeChain"
        />
      ) : (
        <FormGroup2 label={t('labelServiceCode')}>
          <Field name={`${FIELD_NAMES.STEPS}.${index}.serviceChain.services`}>
            {() => (
              <Flex className="sl-serviceChain-wrapper">
                <ServiceChainTextEditor
                  id="service_chain_text_editor"
                  additionalFields={additionalInfoList}
                  initialEditorState={serviceChain.serviceChainStepRaw}
                  onClear={() => { }}
                  onServiceSelect={async (item: IContractData) => {
                    return item as any;
                  }}
                  onSubmit={() => { }}
                  pointValue={undefined}
                  searchService={handleSearch}
                >
                  <EditorRefPlugin
                    editorRef={(editor) => {
                      editorRefs[index] = editor;
                    }}
                  />
                  <AdditionalInfoJusification
                    onSearch={(query, setData) => {
                      const _searchQuery = query || '*';
                      const listJustification: string[] = JSON.parse(
                        listSettingJustification?.settings?.[
                        SETTING_KEY_ALLERGIES_FOR_5009
                        ] || '[]'
                      );
                      const results = listJustification.filter((item) =>
                        item.includes(_searchQuery)
                      );
                      setData(results);
                      return results;
                    }}
                    onCreateJustificationClick={(isNoResult) => {
                      if (isNoResult) {
                        setIsOpenCreateJustification(true);
                        return;
                      }
                      setIsOpenCreateJustification(true);
                    }}
                    onDeletedJustificationClick={async (value: string) => {
                      const listJustification: string[] = JSON.parse(
                        listSettingJustification?.settings?.[
                        SETTING_KEY_ALLERGIES_FOR_5009
                        ] || '[]'
                      );
                      const newListJustification = listJustification.filter(
                        (v) => v != value
                      );
                      await saveSettings({
                        settings: {
                          [SETTING_KEY_ALLERGIES_FOR_5009]:
                            JSON.stringify(newListJustification),
                        },
                      });
                      await refetch();
                    }}
                  >
                    {({ onNewJustificationAdded }) => (
                      <>
                        {isOpenCreateJustification && (
                          <CreateJusitficationModal
                            isOpen={isOpenCreateJustification}
                            className="stage-modal-justification"
                            onCreateSuccess={async (ws) => {
                              if (ws) {
                                onNewJustificationAdded(ws);
                                const listJustification: string[] = JSON.parse(
                                  listSettingJustification?.settings?.[
                                  SETTING_KEY_ALLERGIES_FOR_5009
                                  ] || '[]'
                                );
                                listJustification.push(ws);
                                await saveSettings({
                                  settings: {
                                    [SETTING_KEY_ALLERGIES_FOR_5009]:
                                      JSON.stringify(listJustification),
                                  },
                                });
                                refetch();
                                setIsOpenCreateJustification(false);
                              }
                            }}
                            onClose={() => {
                              setIsOpenCreateJustification(false);
                            }}
                            t={t}
                          />
                        )}
                      </>
                    )}
                  </AdditionalInfoJusification>
                  {systemDate < hgncAvailableDate ? <>
                    <AdditionalInfoOmimGChainPlugin
                      setAdditionalInfoNodeKey={(nodeKey) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          additionalInfoNodeKey: nodeKey,
                        }));
                      }}
                      isServiceChain
                      onSearch={async (query) => {
                        const response = await searchOmimGChain({
                          name: query,
                          pagination: {
                            pageSize: 20,
                            page: 1,
                            sortBy: '',
                            order: Order.ASC,
                          },
                        });
                        return response.data.chains;
                      }}
                      onCreate={() => {
                        setCreateOmimGChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                      onViewAll={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          isOpen: true,
                          query: '',
                        }));
                      }}
                    />
                    <OmimGChainOverview
                      isOpen={listChainDialogState.isOpen}
                      query={listChainDialogState.query}
                      additionalInfoNodeKey={
                        listChainDialogState.additionalInfoNodeKey ?? ''
                      }
                      setQuery={(newQuery) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: newQuery,
                        }));
                      }}
                      onEdit={(item) => {
                        setCreateOmimGChainDialogState({
                          isOpen: true,
                          defaultValue: item,
                        });
                      }}
                      onClose={() => {
                        setListChainDialogState({
                          isOpen: false,
                          query: '',
                          additionalInfoNodeKey: null,
                        });
                      }}
                      onCreate={() => {
                        setCreateOmimGChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                    />
                  </> : <>
                    <AdditionalInfoHgncChainPlugin
                      setAdditionalInfoNodeKey={(nodeKey) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          additionalInfoNodeKey: nodeKey,
                        }));
                      }}
                      isServiceChain
                      onSearch={async (query) => {
                        const response = await searchHgncChain({
                          name: query,
                          paginationRequest: {
                            pageSize: 20,
                            page: 1,
                            sortBy: null!,
                            order: null!,
                          },
                        });
                        return response.data.hgncChains;
                      }}
                      onCreate={() => {
                        setCreateHgncChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                      onViewAll={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          isOpen: true,
                          query: '',
                        }));
                      }}
                    />
                    <HgncChainOverview
                      isOpen={listChainDialogState.isOpen}
                      query={listChainDialogState.query}
                      additionalInfoNodeKey={
                        listChainDialogState.additionalInfoNodeKey ?? ''
                      }
                      setQuery={(newQuery) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: newQuery,
                        }));
                      }}
                      onEdit={(item) => {
                        setCreateHgncChainDialogState({
                          isOpen: true,
                          defaultValue: item,
                        });
                      }}
                      onClose={() => {
                        setListChainDialogState({
                          isOpen: false,
                          query: '',
                          additionalInfoNodeKey: null,
                        });
                      }}
                      onCreate={() => {
                        setCreateHgncChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                    />
                  </>
                  }
                  {createOmimGChainDialogState.isOpen && (
                    <OmimGCreateDialog
                      isOpen
                      defaultValue={
                        createOmimGChainDialogState.defaultValue ?? undefined
                      }
                      onSuccess={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: prev.query ?? '',
                        }));
                      }}
                      onClose={() => {
                        setCreateOmimGChainDialogState({
                          isOpen: false,
                          defaultValue: null,
                        });
                      }}
                    />
                  )}
                  {createHgncChainDialogState.isOpen && (
                    <HgncCreateDialog
                      isOpen
                      defaultValue={
                        createHgncChainDialogState.defaultValue ?? undefined
                      }
                      onSuccess={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: prev.query ?? '',
                        }));
                      }}
                      onClose={() => {
                        setCreateHgncChainDialogState({
                          isOpen: false,
                          defaultValue: null,
                        });
                      }}
                    />
                  )}
                  <AdditionalInfoMaterialCostPlugin
                    onSearch={async (query, setData) => {
                      const _searchQuery = query || '*';
                      const results =
                        (await WebWorker.searchMaterialCost(_searchQuery)) ??
                        [];
                      const data = results.map((m) => ({
                        ...m,
                        itemPrice: +m.itemPrice,
                      }));
                      setData(data);
                      return data;
                    }}
                    onCreateMaterialCostClick={() => {
                      setIsOpenCreateMaterialCostDialog(true);
                    }}
                  />
                  {isOpenCreateMaterialCost && (
                    <CreateMaterialCostDialog
                      isOpen={isOpenCreateMaterialCost}
                      onClose={() => {
                        setIsOpenCreateMaterialCostDialog(false);
                      }}
                      onCreateSuccess={() => {
                        setIsOpenCreateMaterialCostDialog(false);
                      }}
                    />
                  )}
                  {/* Confirm dialog could be added here if needed */}
                </ServiceChainTextEditor>
              </Flex>
            )}
          </Field>
        </FormGroup2>
      )}
    </Flex>
  );
};

export default ServiceCodeChain;
