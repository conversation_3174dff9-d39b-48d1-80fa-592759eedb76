import { ContractData } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import WebWorker from '@tutum/infrastructure/web-worker-services';

// todo use utils from catalogsoverview
export const getServiceListHasContract = async (query, contractId: string) => {
  const serviceList = await WebWorker.searchContract(query, contractId);

  const hzvService = serviceList.filter((item) => {
    return filterServiceCodeWithValidDates(item, contractId);
  });

  return hzvService;
};

//ABRD602 - allow only documentation of contract specific services
function filterServiceCodeWithValidDates(
  data: ContractData,
  contractId?: string
) {
  return (
    !contractId ||
    DatetimeUtil.isBetween(
      DatetimeUtil.utc(DatetimeUtil.now()),
      data.validFrom,
      data.validTo!,
      'D',
      '[]'
    )
  );
}
