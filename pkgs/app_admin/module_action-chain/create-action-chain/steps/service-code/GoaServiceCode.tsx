import {
  BodyTextM,
  Flex,
  FormGroup2,
  IMenuItemWithData,
} from '@tutum/design-system/components';
import GoaServiceNodeComponent from '@tutum/design-system/composer/goa-service-node-component';
import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import { useQueryGetSettings } from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import { MainGroup } from '@tutum/hermes/bff/legacy/common';
import { EncounterGoaService } from '@tutum/hermes/bff/legacy/repo_encounter';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import materialCostService from '@tutum/infrastructure/web-worker-services/material-cost.service';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import AdditionalInfoAutoFillDefaultDataPlugin from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/lexical/plugins/additional-info-auto-fill-default-data-plugin';
import serviceBlockService from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-block/services/service-block.service';
import { Field, useField } from 'formik';
import debounce from 'lodash/debounce';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { FIELD_NAMES } from '../../CreateActionChain.const';

import { AdditionalInfoAutoTransformListNodePlugin } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-auto-transform-list-node-plugin';
import { ASV_KEY } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.const';
import serviceMetaService from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-block/services/service-meta.service';

export interface GoaServiceCodeProps {
  t: any;
  index: number;
  tServiceNode: any;
  className?: string;
}

const INIT_DATA: EncounterGoaService = {
  command: 'L',
  factor: 1,
  quantity: 1,
  code: '',
  description: '',
  freeText: '',
  isChangeDefault: false,
};

const GoaServiceCode = (props: GoaServiceCodeProps) => {
  const { t, className, index } = props;

  const { t: translator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const [fieldType] = useField(FIELD_NAMES.STEPS + '.' + index + '.service');

  const { useGetDoctorList } = useContext(GlobalContext.instance);
  const doctorLists = useGetDoctorList();

  const [filteredAdditionalInfos, setFilteredAdditionalInfos] = useState<
    AdditionalInfoField[]
  >([]);

  const { data: listSettingJustification } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });

  useEffect(() => {
    catalogOverviewActions
      .getAdditionalInfos(MainGroup.PRIVATE, '')
      .then((fields) => {
        const infors = fields.map((info) => {
          info.label = translator(info.fK);
          return info;
        });
        setFilteredAdditionalInfos(infors);
      });
  }, []);

  const searchServiceData = useMemo(
    () =>
      debounce(async (query: string, cb: (result: IContractData[]) => void) => {
        const isPrivate = true;
        const result = await serviceBlockService.searchService(
          datetimeUtil.now(),
          query,
          undefined,
          [],
          null,
          undefined,
          isPrivate,
          MainGroup.PRIVATE
        );
        cb(result.slice(0, 20));
      }, 500),
    []
  );

  const memoizedDoctorASVNumbers = useMemo(
    () =>
      doctorLists.reduce<string[]>((asvNumbers, doctor) => {
        if (doctor.teamNumbers?.length) {
          return asvNumbers.concat(doctor.teamNumbers);
        }
        return asvNumbers;
      }, []),
    [doctorLists]
  );

  const onServiceSelect = async (item: IContractData) => {
    const resultAfterValidate =
      await serviceMetaService.getServiceMetaVersionGoa(item);
    return resultAfterValidate;
  };

  return (
    <div className={className}>
      <FormGroup2 label={t('GOA')}>
        <Field name={`${FIELD_NAMES.STEPS}.${index}.service.goaService`}>
          {({ field, form }) => (
            <Flex className="sl-service-wrapper">
              <GoaServiceNodeComponent
                id="service_node_composer"
                placeholder={t('GOA')}
                additionalInfoEditorplaceholder={t('typeParenthesis', {
                  key: '(',
                })}
                hideFactor
                data={field.value ?? INIT_DATA}
                onChange={async (_data) => {
                  form.setFieldValue(field.name, {
                    ..._data,
                    id: getUUID(),
                  });
                }}
                onSubmit={async () => { }}
                onClear={() => { }}
                additionalFields={filteredAdditionalInfos}
                searchService={searchServiceData}
                onServiceSelect={onServiceSelect}
                encounterDate={datetimeUtil.now()}
                noResultsComponent={
                  <Flex
                    className="sl-no-results"
                    p="8px 16px"
                    justify="center"
                    gap={8}
                  >
                    <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
                      {t('noResultsFound')}
                    </BodyTextM>
                  </Flex>
                }
                pointValue={undefined}
                scheinId=""
              >
                <AdditionalInfoAutoFillDefaultDataPlugin autoFillInfos={[]} />
                <AdditionalInfoMaterialCostPlugin
                  onSearch={async (query, setData) => {
                    const _searchQuery = query || '*';
                    const results =
                      (await materialCostService.searchMaterialCost(
                        _searchQuery
                      )) ?? [];
                    setData(results);
                    return results;
                  }}
                  onCreateMaterialCostClick={() => { }}
                >
                  {({ onNewMaterialCostAdded }) => <></>}
                </AdditionalInfoMaterialCostPlugin>

                <AdditionalInfoJusification
                  onSearch={(query, setData) => {
                    const _searchQuery = query || '*';
                    const listJustification: string[] = JSON.parse(
                      listSettingJustification?.settings?.[
                      SETTING_KEY_ALLERGIES_FOR_5009
                      ] || '[]'
                    );
                    const results = listJustification.filter((item) =>
                      item.includes(_searchQuery)
                    );
                    setData(results);
                    return results;
                  }}
                  onDeletedJustificationClick={() =>
                    new Promise((resolve) => resolve())
                  }
                >
                  {({ onNewJustificationAdded }) => <></>}
                </AdditionalInfoJusification>
                {/* NOTE: auto-transform ASV teamnumber add.info block to list mode */}
                <AdditionalInfoAutoTransformListNodePlugin<string>
                  targetFk={ASV_KEY}
                  documentedDoctor={null}
                  onQuery={async (query, setData) => {
                    const menuItems = memoizedDoctorASVNumbers
                      .filter((asv) => asv.includes(query))
                      .map(
                        (asv) =>
                          ({
                            id: datetimeUtil.now(),
                            label: asv,
                            value: asv,
                            data: asv,
                          }) as IMenuItemWithData<string>
                      );
                    setData(menuItems);
                    return menuItems;
                  }}
                />
              </GoaServiceNodeComponent>
            </Flex>
          )}
        </Field>
      </FormGroup2>
    </div>
  );
};

export default GoaServiceCode;
