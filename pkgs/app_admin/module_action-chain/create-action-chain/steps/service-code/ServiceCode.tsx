// @ts-nocheck
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import PatientManagementI18n from '@tutum/admin/locales/en/PatientManagement.json';
import {
  Flex,
  FormGroup2,
  IMenuItem,
  ReactSelect,
} from '@tutum/design-system/components';
import { Radio, RadioGroup } from '@tutum/design-system/components/Core';
import ADD_INFO_FULL_LIST from '@tutum/design-system/composer/assets/additional-info.json';
import ServiceNodeComponent from '@tutum/design-system/composer/service-node-component';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import CreateMaterialCostDialog from '@tutum/mvz/module_sdebm/material-cost-dialog/MaterialCostDialog.styled';
import AdditionalInfoOmimGChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-omimg-chain-plugins';
import {
  JUSTIFICATION_BLOCK,
  PLACE_VISIT,
  REFERRAL_BLOCK,
  TOTAL_AMOUNT,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import {
  ActionChainCategory,
  ServiceCategory,
} from '@tutum/hermes/bff/action_chain_common';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/catalog_sdebm_common';
import { MainGroup } from '@tutum/hermes/bff/common';
import { ServiceStep } from '@tutum/hermes/bff/legacy/action_chain_common';
import {
  OmimGChain,
  searchOmimGChain,
} from '@tutum/hermes/bff/legacy/app_catalog_omimg_chain';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import WebWorker from '@tutum/infrastructure/web-worker-services';
import { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import OmimGChainOverview from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/OmimGChainOverView';
import OmimGCreateDialog from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/create-dialog/OmimGCreateDialog.styled';
import { Field } from 'formik';
import { NodeKey } from 'lexical';
import debounce from 'lodash/debounce';
import isEmpty from 'lodash/isEmpty';
import React, { memo, useMemo, useState } from 'react';
import { FIELD_NAMES } from '../../CreateActionChain.const';
import { useCreateActionChainStore } from '../../CreateActionChain.store';
import GoaServiceCode from './GoaServiceCode.styled';
import { getServiceListHasContract } from './ServiceCode.helper';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.const';
import {
  saveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import CreateJusitficationModal from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/goa-service-block/justification-modal/CreateJustification.styled';
import { searchEbmsComposer } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { HgncChain, searchHgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import AdditionalInfoHgncChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-hgnc-chain-plugins';
import HgncCreateDialog from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/create-dialog/HgncCreateDialog.styled';
import HgncChainOverview from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/HgncChainOverView';
import { SystemDateKey, useQueryGetSetting } from '@tutum/hermes/bff/legacy/app_mvz_settings';
import { SettingsFeatures } from '@tutum/hermes/bff/settings_common';

export interface IServiceCodeProps {
  className?: string;
  index: number;
  service: ServiceStep;
  stepCategory: ActionChainCategory;
  errors: any;
}

const ServiceCode = ({
  className,
  index,
  service: serviceStep,
  errors,
}: IServiceCodeProps) => {
  const { t } = I18n.useTranslation<keyof typeof ActionChainI18n.ServiceCode>({
    namespace: 'ActionChain',
    nestedTrans: 'ServiceCode',
  });

  const { t: tServiceNode } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: composerTranslator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });
  const hgncAvailableDate = new Date("2025-07-01T00:00:00")
  const { data, isSuccess } = useQueryGetSetting({
    feature: SettingsFeatures.SettingsFeatures_SystemDate,
    settings: [SystemDateKey.SystemDateKey_CurrentDate],
  });

  const systemDate = useMemo(() => {
    return isSuccess && data?.settings?.[SystemDateKey.SystemDateKey_CurrentDate]
      ? new Date(
        parseInt(data.settings[SystemDateKey.SystemDateKey_CurrentDate])
      )
      : datetimeUtil.date();

  }, [isSuccess, data?.settings]);


  const { contracts } = useCreateActionChainStore();

  const [isGoaService, setIsGoaService] = useState(
    !isEmpty(serviceStep.goaService)
  );

  const [createOmimGChainDialogState, setCreateOmimGChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: OmimGChain;
    }>({
      isOpen: false,
      defaultValue: null,
    });
  const [createHgncChainDialogState, setCreateHgncChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: HgncChain;
    }>({
      isOpen: false,
      defaultValue: null,
    });
  const [listChainDialogState, setListChainDialogState] = useState<{
    isOpen: boolean;
    query: string;
    additionalInfoNodeKey: NodeKey;
  }>({
    isOpen: false,
    query: '',
    additionalInfoNodeKey: null,
  });

  const [isOpenCreateMaterialCost, setIsOpenCreateMaterialCostDialog] =
    useState(false);
  const { data: listSettingJustification, refetch } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });
  const [isOpenCreateJustification, setIsOpenCreateJustification] =
    useState(false);

  const handleSearch = debounce(async (query: string, cb: any) => {
    const tasksList: Promise<any>[] = [];
    tasksList.push(
      searchEbmsComposer({
        query,
        selectedDate: datetimeUtil.now(),
      }).then((res) => {
        return (res?.data?.items || []).map(
          (item) =>
            ({
              code: item.code,
              description: item.description,
              evaluation: item.evaluation,
              unit: item.unit,
              isSelfCreated: item.isSelfCreated,
              mainGroup: MainGroup.KV,
            }) as IContractData
        );
      })
    );

    if (serviceStep?.contractId) {
      tasksList.push(getServiceListHasContract(query, serviceStep?.contractId));
    }

    const results = await Promise.all(tasksList);
    const rs: IContractData[] = [];

    results[0].forEach((kvItem) => {
      kvItem.mainGroup = MainGroup.KV;
      rs.push(kvItem);
    });

    if (results.length === 2) {
      results[1].forEach((hzvItem) => {
        hzvItem.mainGroup = MainGroup.HZV;
        rs.push(hzvItem);
      });
    }

    cb?.(rs);
  }, 300);

  const getAdditionalInfoByServiceType = (serviceType: string) => {
    switch (serviceType) {
      case 'EBM':
        return ADD_INFO_FULL_LIST.filter(
          (info) => ![TOTAL_AMOUNT, REFERRAL_BLOCK].includes(info.fK)
        );
      case 'SelectiveContract':
        return ADD_INFO_FULL_LIST.filter((info) =>
          [PLACE_VISIT, JUSTIFICATION_BLOCK, REFERRAL_BLOCK].includes(info.fK)
        );
      case 'GOA':
        return ADD_INFO_FULL_LIST;

      default:
        return [];
    }
  };

  const additionalInfoList = React.useMemo<AdditionalInfoField[]>(() => {
    return getAdditionalInfoByServiceType(serviceStep?.serviceCategory).map(
      (info) => {
        info.label = composerTranslator(info.fK);
        return info as AdditionalInfoField;
      }
    );
  }, [serviceStep?.serviceCategory]);

  const onChange = (form, field) => (e) => {
    const value: ServiceCategory = e.currentTarget.value as ServiceCategory;
    form.setFieldValue(field.name, value);
    form.setFieldValue(`${FIELD_NAMES.STEPS}.${index}.service.service`, null);
    form.setFieldValue(
      `${FIELD_NAMES.STEPS}.${index}.service.goaService`,
      null
    );

    if (value !== ServiceCategory.Service_SelectiveContract) {
      form.setFieldValue(
        `${FIELD_NAMES.STEPS}.${index}.service.contractId`,
        null
      );
    }
    if (value === ServiceCategory.Service_GOA) {
      form.setFieldValue(
        `${FIELD_NAMES.STEPS}.${index}.service.contractId`,
        null
      );
      setIsGoaService(true);
    } else {
      setIsGoaService(false);
    }
  };

  return (
    <Flex className={className} column gap={16}>
      <FormGroup2
        label={t('labelServiceType')}
        name={`${FIELD_NAMES.STEPS}.${index}.service.serviceCategory`}
        errors={errors}
        submitCount={1}
      >
        <Field name={`${FIELD_NAMES.STEPS}.${index}.service.serviceCategory`}>
          {({ field, form }) => (
            <RadioGroup
              onChange={onChange(form, field)}
              inline
              selectedValue={
                field.value ?? ServiceCategory.Service_SelectiveContract
              }
            >
              <Radio
                label={t(ServiceCategory.Service_EBM)}
                value={ServiceCategory.Service_EBM}
              />
              <Radio
                label={t(ServiceCategory.Service_SelectiveContract)}
                value={ServiceCategory.Service_SelectiveContract}
              />
              <Radio
                label={t(ServiceCategory.Service_GOA)}
                value={ServiceCategory.Service_GOA}
              />
            </RadioGroup>
          )}
        </Field>
      </FormGroup2>
      {serviceStep?.serviceCategory ===
        ServiceCategory.Service_SelectiveContract && (
          <FormGroup2 label={t('labelContract')}>
            <Field name={`${FIELD_NAMES.STEPS}.${index}.service.contractId`}>
              {({ field, form }) => (
                <ReactSelect
                  selectedValue={field.value}
                  items={
                    contracts?.map((item) => ({
                      value: item?.contractId,
                      label: item?.contractName,
                    })) ?? []
                  }
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item?.value);
                  }}
                  menuPlacement="auto"
                />
              )}
            </Field>
          </FormGroup2>
        )}
      {isGoaService ? (
        <GoaServiceCode
          t={t}
          index={index}
          tServiceNode={tServiceNode}
          className="sl-GoaServiceCode"
        />
      ) : (
        <FormGroup2 label={t('labelServiceCode')}>
          <Field name={`${FIELD_NAMES.STEPS}.${index}.service.service`}>
            {({ field, form }) => (
              <Flex className="sl-service-wrapper">
                <ServiceNodeComponent
                  className="sl-service-node"
                  disabled={false}
                  placeholder={tServiceNode('typeService')}
                  additionalInfoEditorplaceholder={tServiceNode(
                    'typeParenthesis',
                    {
                      key: '(',
                    }
                  )}
                  additionalFields={additionalInfoList}
                  data={field.value}
                  encounterDate={datetimeUtil.now()}
                  searchService={handleSearch}
                  onServiceSelect={async (item) => {
                    const _data = item;
                    return {
                      ..._data,
                      id: '',
                      sortOrder: 0,
                      code: _data.code,
                      description: _data.description,
                      serviceMainGroup: _data.mainGroup,
                      freeText: `(${_data.code}) ${_data.description}`,
                      command: 'L',
                      isPreParticipate: false,
                      referralDoctorInfo: {
                        bsnr: '',
                        lanr: '',
                        requiredLanr: false,
                        requiredBsnr: false,
                      },
                      scheins: [],
                    };
                  }}
                  onChange={async (_data) => {
                    form.setFieldValue(field.name, {
                      ..._data,
                      id: getUUID(),
                    });
                  }}
                  onSubmit={async () => { }}
                  onClear={() => { }}
                  pointValue={undefined}
                  id={''}
                >
                  {systemDate < hgncAvailableDate ? <>
                    <AdditionalInfoOmimGChainPlugin
                      setAdditionalInfoNodeKey={(nodeKey) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          additionalInfoNodeKey: nodeKey,
                        }));
                      }}
                      onSearch={async (query) => {
                        const response = await searchOmimGChain({
                          name: query,
                          pagination: {
                            pageSize: 20,
                            page: 1,
                            sortBy: null,
                            order: null,
                          },
                        });
                        return response.data.chains;
                      }}
                      onCreate={() => {
                        setCreateOmimGChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                      onViewAll={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          isOpen: true,
                          query: '',
                        }));
                      }}
                    />
                    <OmimGChainOverview
                      isOpen={listChainDialogState.isOpen}
                      query={listChainDialogState.query}
                      additionalInfoNodeKey={
                        listChainDialogState.additionalInfoNodeKey
                      }
                      setQuery={(newQuery) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: newQuery,
                        }));
                      }}
                      onEdit={(item) => {
                        setCreateOmimGChainDialogState({
                          isOpen: true,
                          defaultValue: item,
                        });
                      }}
                      onClose={() => {
                        setListChainDialogState({
                          isOpen: false,
                          query: '',
                          additionalInfoNodeKey: null,
                        });
                      }}
                      onCreate={() => {
                        setCreateOmimGChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                    />
                  </> : <>
                    <AdditionalInfoHgncChainPlugin
                      setAdditionalInfoNodeKey={(nodeKey) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          additionalInfoNodeKey: nodeKey,
                        }));
                      }}
                      onSearch={async (query) => {
                        const response = await searchHgncChain({
                          name: query,
                          paginationRequest: {
                            pageSize: 20,
                            page: 1,
                            sortBy: null!,
                            order: null!,
                          },
                        });
                        return response.data.hgncChains;
                      }}
                      onCreate={() => {
                        setCreateHgncChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                      onViewAll={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          isOpen: true,
                          query: '',
                        }));
                      }}
                    />
                    <HgncChainOverview
                      isOpen={listChainDialogState.isOpen}
                      query={listChainDialogState.query}
                      additionalInfoNodeKey={
                        listChainDialogState.additionalInfoNodeKey
                      }
                      setQuery={(newQuery) => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: newQuery,
                        }));
                      }}
                      onEdit={(item) => {
                        setCreateHgncChainDialogState({
                          isOpen: true,
                          defaultValue: item,
                        });
                      }}
                      onClose={() => {
                        setListChainDialogState({
                          isOpen: false,
                          query: '',
                          additionalInfoNodeKey: null,
                        });
                      }}
                      onCreate={() => {
                        setCreateHgncChainDialogState({
                          isOpen: true,
                          defaultValue: null,
                        });
                      }}
                    />
                  </>
                  }

                  {createOmimGChainDialogState.isOpen && (
                    <OmimGCreateDialog
                      isOpen
                      defaultValue={createOmimGChainDialogState.defaultValue}
                      onSuccess={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: prev.query === '' ? null : '',
                        }));
                      }}
                      onClose={() => {
                        setCreateOmimGChainDialogState({
                          isOpen: false,
                          defaultValue: null,
                        });
                      }}
                    />
                  )}
                  {createHgncChainDialogState.isOpen && (
                    <HgncCreateDialog
                      isOpen
                      defaultValue={createHgncChainDialogState.defaultValue}
                      onSuccess={() => {
                        setListChainDialogState((prev) => ({
                          ...prev,
                          query: prev.query === '' ? null : '',
                        }));
                      }}
                      onClose={() => {
                        setCreateHgncChainDialogState({
                          isOpen: false,
                          defaultValue: null,
                        });
                      }}
                    />
                  )}
                  <AdditionalInfoJusification
                    onSearch={(query, setData) => {
                      const _searchQuery = query || '*';
                      const listJustification: string[] = JSON.parse(
                        listSettingJustification?.settings?.[
                        SETTING_KEY_ALLERGIES_FOR_5009
                        ] || '[]'
                      );
                      const results = listJustification.filter((item) =>
                        item.includes(_searchQuery)
                      );
                      setData(results);
                      return results;
                    }}
                    onCreateJustificationClick={(isNoResult) => {
                      if (isNoResult) {
                        setIsOpenCreateJustification(true);
                        return;
                      }
                      setIsOpenCreateJustification(true);
                    }}
                    onDeletedJustificationClick={async (value: string) => {
                      const listJustification: string[] = JSON.parse(
                        listSettingJustification?.settings?.[
                        SETTING_KEY_ALLERGIES_FOR_5009
                        ] || '[]'
                      );
                      const newListJustification = listJustification.filter(
                        (v) => v != value
                      );
                      await saveSettings({
                        settings: {
                          [SETTING_KEY_ALLERGIES_FOR_5009]:
                            JSON.stringify(newListJustification),
                        },
                      });
                      await refetch();
                    }}
                  >
                    {({ onNewJustificationAdded }) => (
                      <>
                        {isOpenCreateJustification && (
                          <CreateJusitficationModal
                            isOpen={isOpenCreateJustification}
                            className="stage-modal-justification"
                            onCreateSuccess={async (ws) => {
                              onNewJustificationAdded(ws);
                              const listJustification: string[] = JSON.parse(
                                listSettingJustification?.settings?.[
                                SETTING_KEY_ALLERGIES_FOR_5009
                                ] || '[]'
                              );
                              listJustification.push(ws);
                              await saveSettings({
                                settings: {
                                  [SETTING_KEY_ALLERGIES_FOR_5009]:
                                    JSON.stringify(listJustification),
                                },
                              });
                              refetch();
                              setIsOpenCreateJustification(false);
                            }}
                            onClose={() => {
                              setIsOpenCreateJustification(false);
                            }}
                            t={t}
                          />
                        )}
                      </>
                    )}
                  </AdditionalInfoJusification>
                  <AdditionalInfoMaterialCostPlugin
                    onSearch={async (query, setData) => {
                      const _searchQuery = query || '*';
                      const results =
                        (await WebWorker.searchMaterialCost(_searchQuery)) ??
                        [];
                      const data = results.map((m) => ({
                        ...m,
                        itemPrice: +m.itemPrice,
                      }));
                      setData(data);
                      return data;
                    }}
                    onCreateMaterialCostClick={() => {
                      setIsOpenCreateMaterialCostDialog(true);
                    }}
                  />
                  {isOpenCreateMaterialCost && (
                    <CreateMaterialCostDialog
                      isOpen={isOpenCreateMaterialCost}
                      onClose={() => {
                        setIsOpenCreateMaterialCostDialog(false);
                      }}
                      onCreateSuccess={() => {
                        setIsOpenCreateMaterialCostDialog(false);
                      }}
                    />
                  )}
                </ServiceNodeComponent>
              </Flex>
            )}
          </Field>
        </FormGroup2>
      )}
    </Flex>
  );
};

export default memo(ServiceCode);
