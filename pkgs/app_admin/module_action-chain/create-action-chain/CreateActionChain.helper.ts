import FormUtils, {
  <PERSON><PERSON>teField,
} from '@tutum/infrastructure/utils/form.util';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import CommonLocales from '@tutum/admin/locales/en/Common.json';
import { ActionChain } from '@tutum/hermes/bff/legacy/app_admin_action_chain';
import { FIELD_NAMES } from './CreateActionChain.const';
import {
  ActionChainCategory,
  ActionChainStep,
} from '@tutum/hermes/bff/legacy/action_chain_common';

interface IValidateParams {
  t: IFixedNamespaceTFunction<keyof typeof ActionChainI18n.CreateActionChain>;
  tFormValidation: IFixedNamespaceTFunction<
    keyof typeof CommonLocales.FormValidation
  >;
}

export const hasAnyEmptyStep = (steps: ActionChainStep[]) => {
  const emptyStepIndex = steps.findIndex((step) => {
    return isStepEmpty(step);
  });
  return emptyStepIndex !== -1;
};

export const isStepEmpty = (step: ActionChainStep): boolean => {
  if (Object.values(step).every((value) => isEmpty(value))) {
    return true;
  }
  switch (step.stepCategory) {
    case ActionChainCategory.ActionChainCategory_VitalParameter:
      const selectedItems = step.vitalParameter?.vitalParameterCategories ?? [];
      return selectedItems.length === 0;
    case ActionChainCategory.ActionChainCategory_Medication:
      const keywordSearch = step.medication?.search ?? '';
      return !keywordSearch;
    case ActionChainCategory.ActionChainCategory_Form:
      const form = step.form?.formId;
      return !form;
    case ActionChainCategory.ActionChainCategory_AcuteDiagnose:
    case ActionChainCategory.ActionChainCategory_PermanentDiagnose:
    case ActionChainCategory.ActionChainCategory_AnamnesticDiagnose:
      const diagnosis = step.diagnose?.diagnose;
      if (!diagnosis?.certainty) {
        return true;
      }
      return Object.values(diagnosis).every((key) => isEmpty(key));
    case ActionChainCategory.ActionChainCategory_Service:
      const serviceCode = step.service?.service!;
      const goaServiceCode = step.service?.goaService;
      if (!serviceCode?.code && !goaServiceCode?.code) {
        return true;
      }
      return Object.values(serviceCode ?? goaServiceCode).every((key) =>
        isEmpty(key)
      );
    case ActionChainCategory.ActionChainCategory_Findings:
    case ActionChainCategory.ActionChainCategory_Anamnesis:
    case ActionChainCategory.ActionChainCategory_Note:
    case ActionChainCategory.ActionChainCategory_Therapy:
      return !step.freeText?.freeText?.note;
    default:
      return false;
  }
};

export const onValidate =
  ({ t, tFormValidation }: IValidateParams) =>
    (values: ActionChain) => {
      const validateFields: ValidateField[] = [];
      validateFields.push({
        fieldName: FIELD_NAMES.NAME,
        validateRule: () => isEmpty(values.name, true),
        errorMessage: tFormValidation('fieldRequired'),
      });
      const serviceMap = new Map<string, number>();
      values.steps
        .filter(
          (step) =>
            step.stepCategory ===
            ActionChainCategory.ActionChainCategory_Service ||
            step.stepCategory ===
            ActionChainCategory.ActionChainCategory_ServiceChain
        )
        .forEach((step, index) => {
          if (
            step.stepCategory === ActionChainCategory.ActionChainCategory_Service
          ) {
            serviceMap.set(step.service?.serviceCategory!, index);
          }
          if (
            step.stepCategory ===
            ActionChainCategory.ActionChainCategory_ServiceChain
          ) {
            serviceMap.set(step.serviceChain?.serviceCategory!, index);
          }
        });
      if (serviceMap.size > 1) {
        for (const [_, value] of serviceMap) {
          validateFields.push({
            fieldName: `${FIELD_NAMES.STEPS}.${value}.service.serviceCategory`,
            validateRule: () => true,
            errorMessage: t('multipleServiceTypes'),
          });
          validateFields.push({
            fieldName: `${FIELD_NAMES.STEPS}.${value}.serviceChain.serviceCategory`,
            validateRule: () => true,
            errorMessage: t('multipleServiceTypes'),
          });
        }
      }

      const { errors } = FormUtils.validateForm(validateFields, null!);
      return errors;
    };
