import { <PERSON><PERSON>hain } from '@tutum/hermes/bff/legacy/app_admin_action_chain';
import {
  ActionChainStatus,
  ActionChainCategory,
  VitalParameterCategory,
} from '@tutum/hermes/bff/action_chain_common';
import { ActionChainStep } from '@tutum/hermes/bff/legacy/action_chain_common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';

export enum FIELD_NAMES {
  NAME = 'name',
  DESCRIPTION = 'description',
  STATUS = 'status',
  STEPS = 'steps',
}

export const INIT_STEP: ActionChainStep = {
  stepCategory: null!,
  vitalParameter: null!,
  diagnose: null!,
  form: null!,
  medication: null!,
  service: null!,
  freeText: null!,
  serviceChain: null!,
  customize: null!,
  isActive: undefined!,
};

export const INIT_VALUES: ActionChain = {
  [FIELD_NAMES.NAME]: '',
  [FIELD_NAMES.DESCRIPTION]: '',
  [FIELD_NAMES.STATUS]: ActionChainStatus.ActionChainStatus_Active,
  [FIELD_NAMES.STEPS]: [INIT_STEP],
};

export const getListCategory = (
  t: IFixedNamespaceTFunction<keyof typeof ActionChainI18n.Category>
) => [
    {
      label: t(ActionChainCategory.ActionChainCategory_Anamnesis),
      value: ActionChainCategory.ActionChainCategory_Anamnesis,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_Findings),
      value: ActionChainCategory.ActionChainCategory_Findings,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_AnamnesticDiagnose),
      value: ActionChainCategory.ActionChainCategory_AnamnesticDiagnose,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_AcuteDiagnose),
      value: ActionChainCategory.ActionChainCategory_AcuteDiagnose,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_PermanentDiagnose),
      value: ActionChainCategory.ActionChainCategory_PermanentDiagnose,
    },
    // {
    //   label: t(ActionChainCategory.ActionChainCategory_Service),
    //   value: ActionChainCategory.ActionChainCategory_Service,
    // },
    {
      label: t(ActionChainCategory.ActionChainCategory_ServiceChain),
      value: ActionChainCategory.ActionChainCategory_ServiceChain,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_Therapy),
      value: ActionChainCategory.ActionChainCategory_Therapy,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_Note),
      value: ActionChainCategory.ActionChainCategory_Note,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_VitalParameter),
      value: ActionChainCategory.ActionChainCategory_VitalParameter,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_Allergy),
      value: ActionChainCategory.ActionChainCategory_Allergy,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_Form),
      value: ActionChainCategory.ActionChainCategory_Form,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_Medication),
      value: ActionChainCategory.ActionChainCategory_Medication,
    },
    {
      label: t(ActionChainCategory.ActionChainCategory_Job),
      value: ActionChainCategory.ActionChainCategory_Job,
    },
  ];

export const getListVitalParameters = (
  t: IFixedNamespaceTFunction<keyof typeof ActionChainI18n.VitalParameters>
) => [
    {
      label: t(VitalParameterCategory.VitalParameter_AmountOfBirth),
      value: VitalParameterCategory.VitalParameter_AmountOfBirth,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_AmountOfChildren),
      value: VitalParameterCategory.VitalParameter_AmountOfChildren,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_AmountOfPregnancies),
      value: VitalParameterCategory.VitalParameter_AmountOfPregnancies,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_BloodPressure),
      value: VitalParameterCategory.VitalParameter_BloodPressure,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_Creatinine),
      value: VitalParameterCategory.VitalParameter_Creatinine,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_DateOfPlannedBirth),
      value: VitalParameterCategory.VitalParameter_DateOfPlannedBirth,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_HeartFrequency),
      value: VitalParameterCategory.VitalParameter_HeartFrequency,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_Height),
      value: VitalParameterCategory.VitalParameter_Height,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_IsBreastfeeding),
      value: VitalParameterCategory.VitalParameter_IsBreastfeeding,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_IsPregnant),
      value: VitalParameterCategory.VitalParameter_IsPregnant,
    },
    {
      label: t(VitalParameterCategory.VitalParameter_Weight),
      value: VitalParameterCategory.VitalParameter_Weight,
    },
  ];
