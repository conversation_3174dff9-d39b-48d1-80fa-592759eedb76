import React, { useEffect, useRef, useState } from 'react';
import type { NodeKey } from 'lexical';
import {
  Box,
  Flex,
  coreComponents,
  IMenuItemWithData,
} from '@tutum/design-system/components';
import { Classes } from '@tutum/design-system/components/Core';
import { scaleSpacePx } from '@tutum/design-system/styles';
import i18n from '@tutum/infrastructure/i18n';
import { AdditionalInfoItemUntranslated } from '@tutum/design-system/composer/add-info-lexical/components/additional-info-item';
import AsyncAdditionalInfoSuggest from '@tutum/design-system/composer/add-info-lexical/components/async-additional-info-suggest';
import { StyledAddInfoBlock } from '@tutum/design-system/composer/add-info-lexical/components/AdditionalInfoBlock.styled';
import MenuList from '@tutum/design-system/lexical/components/custom-select-menu-list';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useLexicalNodeSelection } from '@lexical/react/useLexicalNodeSelection';
import { ON_CHANGE_COMMAND } from './commands';
import { Field, FieldValue } from '@tutum/hermes/bff/catalog_sdebm_common';
import type { SelectInstance, OptionProps } from 'react-select';
import { isNil } from 'lodash';

const NOT_FOUND_CODE = '999999';

type MenuItemType = IMenuItemWithData<FieldValue>;

interface IParentAutoFillChildBlockProps {
  nodeKey: NodeKey;
  info?: Field;
  data: FieldValue;
  autoFocus?: boolean;
  loadOptions: (inputQuery: string) => Promise<MenuItemType[]>;
}

const CustomOption = (props) => {
  return (
    <coreComponents.Option {...props}>
      <Flex>
        <Box
          style={{ minWidth: scaleSpacePx(20) }}
          className={Classes.MENU_ITEM_LABEL}
        >
          {props.data?.value}
        </Box>
        <Box
          auto
          className={`${Classes.TEXT_OVERFLOW_ELLIPSIS} ${Classes.FILL}`}
        >
          {props.data?.label}
        </Box>
      </Flex>
    </coreComponents.Option>
  );
};

const AdminParentAutoFillChildBlock = (
  props: IParentAutoFillChildBlockProps
) => {
  const { nodeKey, data } = props;
  const [isFocused] = useLexicalNodeSelection(nodeKey);
  const selectRef = useRef<SelectInstance<MenuItemType, false> | null>(null);

  useEffect(() => {
    if (isFocused) {
      selectRef.current?.blur();
      selectRef.current?.focus();
      selectRef.current?.onMenuOpen();
    }
  }, [isFocused]);

  const { t: translator, lang } = i18n.useTranslation<any>({
    namespace: 'Textmodule',
    nestedTrans: 'AdditionalInfo',
  });
  const [editor] = useLexicalComposerContext();

  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItemType>({
    id: String(data.fK),
    label: String(data.value),
    value: String(data.value),
    data,
  });

  const [isQuerying, setIsQuerying] = useState(false);

  async function fetchData(query: string): Promise<MenuItemType[]> {
    if (isNil(query)) {
      return [];
    }

    try {
      // query
      setIsQuerying(true);
      const results = await props.loadOptions(query);
      if (results.length === 0) {
        return props.loadOptions(NOT_FOUND_CODE);
      }

      return results;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsQuerying(false);
    }
  }

  const loadOptions = (
    inputQuery: string,
    setInternalDatasource: (options: MenuItemType[]) => void
  ) => {
    fetchData(inputQuery).then((results) =>
      setInternalDatasource(results ?? [])
    );
  };

  const onItemSelected = (newValue: MenuItemType) => {
    if (!newValue) {
      setSelectedMenuItem(null!);
      return;
    }
    const additionalInformations = newValue.data?.additionalInformations?.map(
      (info) =>
        newValue.value !== NOT_FOUND_CODE ? info : { ...info, value: '' }
    )!;
    const newData = { ...newValue.data!, additionalInformations };
    setSelectedMenuItem({
      ...newValue,
      data: newData,
    });
    if (newValue) {
      editor.dispatchCommand(ON_CHANGE_COMMAND, {
        nodeKey,
        ...newData,
      });
    }
  };

  useEffect(() => {
    if (!props.info) return;
    fetchData(String(data?.value));
  }, [data]);

  return (
    <StyledAddInfoBlock className={isFocused ? '--is-focus' : ''}>
      <>&#40;&nbsp;{translator(props.info?.fK)}:&nbsp;</>
      <AsyncAdditionalInfoSuggest<FieldValue>
        isSearchable
        loadOptions={loadOptions}
        value={selectedMenuItem}
        onChange={onItemSelected}
        getOptionLabel={() => `${selectedMenuItem?.value}`}
        components={{
          MenuList: MenuList as any,
          Option: CustomOption,
        }}
        isLoading={isQuerying}
        getSelectRef={(el) => {
          selectRef.current = el;
        }}
      />
      {(props.info?.additionalInformations ||
        Number(props.info?.additionalInformations?.length) > 0) && <>;&nbsp;</>}
      {props.info?.additionalInformations?.map((childInfo, idx) => {
        const isLast =
          idx === (props.info?.additionalInformations?.length ?? 0) - 1;
        const value = selectedMenuItem
          ? selectedMenuItem.data?.additionalInformations?.find(
            (addInfo) => addInfo?.fK === childInfo?.fK
          )?.value
          : '';

        return (
          <React.Fragment key={childInfo.fK}>
            <AdditionalInfoItemUntranslated
              t={translator}
              lang={lang}
              {...childInfo}
              value={value}
              onChange={(value: string) => {
                const additionalInformations =
                  selectedMenuItem?.data?.additionalInformations?.map(
                    (info) => {
                      if (
                        childInfo.fK !== info.fK ||
                        selectedMenuItem?.value !== NOT_FOUND_CODE
                      ) {
                        return info;
                      }

                      return { ...info, value };
                    }
                  )!;
                const newData = {
                  ...selectedMenuItem?.data!,
                  additionalInformations,
                };

                editor.dispatchCommand(ON_CHANGE_COMMAND, {
                  nodeKey,
                  ...newData,
                });
              }}
              isReadonly={selectedMenuItem?.value !== NOT_FOUND_CODE}
            />
            {!isLast && <>;&nbsp;</>}
          </React.Fragment>
        );
      })}
      &nbsp;&#41;
    </StyledAddInfoBlock>
  );
};

export default AdminParentAutoFillChildBlock;
