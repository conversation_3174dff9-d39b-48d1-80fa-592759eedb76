import {
  TextModuleContent,
  TextModuleNode,
  TextModuleNodeType,
  TextModuleUseFor,
} from '@tutum/hermes/bff/text_module_common';
import {
  $createTextNode,
  LexicalNode,
  $isParagraphNode,
  $isTextNode,
  $isLineBreakNode,
  RootNode,
  $createLineBreakNode,
} from 'lexical';

import {
  $createPlaceholderNode,
  $isPlaceholderNode,
} from './nodes/PlaceholderNode';
import {
  OMIMG_CONFIG,
  AdditionalInfoOmimGBlockNode,
} from './nodes/AdditionalInfoOmimGBlock.node';
import {
  convertTextNodeToPlaceholderTextModuleNode,
  convertTextNodeToTextModuleNode,
} from '@tutum/admin/module_textmodule-editor/create-textmodule-nodes.util';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { AdditionalInfoHgncBlockNode, HGNC_CONFIG } from './nodes/AdditionalInfoHgncBlock.node';
import { $isAdditionalInfoBlockNode } from './nodes/BaseAddionalInfoBlock.node';

export function importTextModuleData({ data, importFor }: { data: TextModuleNode, importFor?: TextModuleUseFor }): LexicalNode | null {
  switch (data.type) {
    case TextModuleNodeType.TextModuleNodeType_Text: {
      const d = data.text;
      const node = $createTextNode(d?.value);
      node.setDetail('unmergable');
      return node;
    }
    case TextModuleNodeType.TextModuleNodeType_Placeholder: {
      const d = data.placeholder;
      if (!d) return null;
      return $createPlaceholderNode(d.value);
    }
    case TextModuleNodeType.TextModuleNodeType_AdditionalInfo: {
      const d = data.omimG;
      if (!d) return null;
      if (!importFor) return null;
      if (importFor === TextModuleUseFor.TextModuleUseFor_OmimGChain) {
        return AdditionalInfoOmimGBlockNode.$createAdditionalInfoBlockNode(OMIMG_CONFIG, {
          fK: d.fK,
          value: d.value,
          additionalInformations: d.children.map((child) => ({
            fK: child.fK,
            value: child.value,
            additionalInformations: [],
          })),
        });
      } else {
        return AdditionalInfoHgncBlockNode.$createAdditionalInfoBlockNode(HGNC_CONFIG, {
          fK: d.fK,
          value: d.value,
          additionalInformations: d.children.map((child) => ({
            fK: child.fK,
            value: child.value,
            additionalInformations: [],
          })),
        });
      }
    }
    case TextModuleNodeType.TextModuleNodeType_LineBreak: {
      return $createLineBreakNode();
    }
    default:
      return null;
  }
}

export function getTextContent(
  root: RootNode,
  t: IFixedNamespaceTFunction<any>
): string {
  let text = '';
  if (!root) return null;

  const firstChild = root.getFirstChild();

  if (!firstChild) return text;
  if (!$isParagraphNode(firstChild)) return text;

  const children = firstChild.getChildren();

  for (const node of children) {
    if ($isPlaceholderNode(node)) {
      text += node.getTextContent()?.replaceAll('%', '');
    } else if ($isAdditionalInfoBlockNode(node)) {
      const infoData = node.getInfoValue();
      const parentText = `${t(infoData.fK)}: ${infoData.value}`;
      const childrenText = infoData.additionalInformations
        .map((childInfo) => `${t(childInfo.fK)}: ${childInfo.value}`)
        ?.join('; ');
      text +=
        childrenText !== ''
          ? ` (${parentText}; ${childrenText})`
          : ` (${parentText})`;
    } else if ($isTextNode(node)) {
      text += node.getTextContent();
    } else if ($isLineBreakNode(node)) {
      text += '\n';
    }
  }

  return text;
}

export function exportTextModuleContents(
  root: RootNode,
  t: IFixedNamespaceTFunction<any>
): TextModuleContent | null {
  if (!root) return null;

  const newValues: TextModuleContent = {
    text: getTextContent(root, t),
    data: [],
  };

  const firstChild = root.getFirstChild();

  if (!firstChild) return newValues;
  if (!$isParagraphNode(firstChild)) return newValues;

  const children = firstChild.getChildren();

  const submitNodes: TextModuleNode[] = [];

  for (const node of children) {
    if ($isPlaceholderNode(node)) {
      const placeholderNode = convertTextNodeToPlaceholderTextModuleNode(node);
      submitNodes.push(placeholderNode);
    } else if ($isAdditionalInfoBlockNode(node)) {
      const infoData = node.getInfoValue();
      const newOmimGNode: TextModuleNode = {
        type: TextModuleNodeType.TextModuleNodeType_AdditionalInfo,
        omimG: {
          fK: infoData.fK,
          value: infoData.value,
          children: infoData.additionalInformations.map((childInfo) => ({
            fK: childInfo.fK,
            value: childInfo.value,
            children: [],
          })),
        },
        children: [],
      };
      submitNodes.push(newOmimGNode);
    } else if ($isTextNode(node)) {
      // NOTE: handle base on primitive type of node
      const newTextNode = convertTextNodeToTextModuleNode(node);
      submitNodes.push(newTextNode);
    } else if ($isLineBreakNode(node)) {
      submitNodes.push({
        children: [],
        type: TextModuleNodeType.TextModuleNodeType_LineBreak,
      });
    }
  }

  newValues.data = submitNodes;

  return newValues;
}
