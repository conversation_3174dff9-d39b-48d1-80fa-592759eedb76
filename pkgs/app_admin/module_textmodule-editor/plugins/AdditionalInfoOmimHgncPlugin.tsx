import { useEffect } from 'react';
import {
  $getNodeByKey,
  COMMAND_PRIORITY_EDITOR,
  KEY_TAB_COMMAND,
  COMMAND_PRIORITY_LOW,
} from 'lexical';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import {
  OnChangeCommandPayload,
  ON_CHANGE_COMMAND,
} from '../components/parent-auto-fill/commands';
import { $$handleTabPress } from '@tutum/design-system/lexical/utils/tab';
import { AdditionalInfoHgncBlockNode } from '../nodes/AdditionalInfoHgncBlock.node';
import { $isAdditionalInfoBlockNode } from '../nodes/BaseAddionalInfoBlock.node';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import { AdditionalInfoOmimGBlockNode } from '../nodes/AdditionalInfoOmimGBlock.node';

export interface AdditionalInfoOmimHgncPluginProps {
  triggerNodeType: TextModuleUseFor;
}

export function AdditionalInfoOmimHgncPlugin({ triggerNodeType }: AdditionalInfoOmimHgncPluginProps) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return mergeRegister(
      editor.registerCommand<OnChangeCommandPayload>(
        ON_CHANGE_COMMAND,
        (payload, _editor) => {
          if (!payload.value) return false;
          _editor.update(() => {
            let node: AdditionalInfoOmimGBlockNode | AdditionalInfoHgncBlockNode | null;
            if (triggerNodeType === TextModuleUseFor.TextModuleUseFor_OmimGChain) {
              node = $getNodeByKey<AdditionalInfoOmimGBlockNode>(
                payload.nodeKey
              );
            } else {
              node = $getNodeByKey<AdditionalInfoHgncBlockNode>(
                payload.nodeKey
              );
            }
            if (!node) return false;
            node?.setInfoValue(payload);
          });
          return true;
        },
        COMMAND_PRIORITY_LOW
      ),
      editor.registerCommand<KeyboardEvent>(
        KEY_TAB_COMMAND,
        (_event) => {
          const shouldPropagate = $$handleTabPress(_event, (n) => {
            return $isAdditionalInfoBlockNode(n);
          });
          return shouldPropagate;
        },
        COMMAND_PRIORITY_EDITOR
      )
    );
  }, [editor]);

  return null;
}
