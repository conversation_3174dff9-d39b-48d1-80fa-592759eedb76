import { useEffect } from 'react';
import {
  TextNode,
  $createTextNode,
  $isRangeSelection,
  $getSelection,
  $setSelection,
  $createNodeSelection,
} from 'lexical';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import {
  AdditionalInfoOmimGBlockNode,
  OMIMG_CONFIG,
} from '../nodes/AdditionalInfoOmimGBlock.node';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import { AdditionalInfoHgncBlockNode, HGNC_CONFIG } from '../nodes/AdditionalInfoHgncBlock.node';

export function transformToAdditionalInfoNode(
  node: TextNode,
  oldContent: string,
  triggerNodeType: TextModuleUseFor
) {
  const selection = $getSelection();
  const parent = node.getParentOrThrow();
  node.remove();
  let newNode: AdditionalInfoOmimGBlockNode | AdditionalInfoHgncBlockNode;
  if (triggerNodeType === TextModuleUseFor.TextModuleUseFor_OmimGChain) {
    newNode = AdditionalInfoOmimGBlockNode.$createAdditionalInfoBlockNode(OMIMG_CONFIG);
  } else {
    newNode = AdditionalInfoHgncBlockNode.$createAdditionalInfoBlockNode(HGNC_CONFIG);
  }
  const persistedTextNode = $createTextNode(oldContent);
  // check if selection is range selection
  if ($isRangeSelection(selection)) {
    selection.insertNodes([
      persistedTextNode ?? $createTextNode(' '),
      newNode,
      $createTextNode(' '),
    ]);
  } else {
    // otherwise, append to parent (element node)
    parent.append(persistedTextNode, newNode, $createTextNode(' '));
  }

  const newSelection = $createNodeSelection();
  newSelection.add(newNode.getKey());
  $setSelection(newSelection);
}

type OmimGHgncChainTriggerPluginProps = {
  triggerNodeType: TextModuleUseFor
};

// todo generic this plugin to transform TextNode to any Node with a custom trigger Eg `(`, merge with CoreEditorContainer
function OmimGHgncChainTriggerPlugin({ triggerNodeType }: OmimGHgncChainTriggerPluginProps) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return mergeRegister(
      editor.registerNodeTransform(TextNode, (node: TextNode) => {
        if (editor.isComposing()) return;
        const content = node.getTextContent().trimEnd();
        const length = content.length;
        const newContent = content.slice(0, length - 1);
        const textContentLastCharacter = content.charAt(length - 1);
        switch (textContentLastCharacter) {
          case '(': {
            return transformToAdditionalInfoNode(node, newContent, triggerNodeType);
          }
          default:
            return;
        }
      })
    );
  }, [editor]);

  return null;
}

export default OmimGHgncChainTriggerPlugin;
