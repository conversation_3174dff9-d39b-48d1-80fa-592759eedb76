import React from 'react';
import { DecoratorNode } from 'lexical';
import { Spread, NodeKey, LexicalNode, SerializedLexicalNode } from 'lexical';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import AdminParentAutoFillChildBlock from '../components/parent-auto-fill/AdminParentAutoFillChildBlock.component';
import {
  Field,
  FieldValue,
} from '@tutum/hermes/bff/catalog_sdebm_common';
import { IMenuItemWithData } from '@tutum/design-system/components';

type SerializedAdditionalInfoBlockNode = Spread<
  Field &
  FieldValue & {
    type: string;
    version: 1;
  },
  SerializedLexicalNode
>;

export interface IBaseAdditionalInfoHelper {
  $createAdditionalInfoBlockNode(info: Field, data?: FieldValue, key?: NodeKey): BaseAdditionalInfoBlockNode;
}

export class BaseAdditionalInfoBlockNode extends DecoratorNode<JSX.Element> {
  __info: Field;
  __data: FieldValue;
  __autoFocus: boolean;

  static getType(): string {
    throw new Error('getType() must be implemented by child classes');
  }
  loadOptions(inputQuery: string): Promise<Array<IMenuItemWithData<FieldValue>>> {
    throw new Error('loadOptions() must be implemented by child classes');
  }
  getConfig(): Field {
    throw new Error('getConfig() must be implemented by child classes');
  }


  static clone(node: BaseAdditionalInfoBlockNode): BaseAdditionalInfoBlockNode {
    return new BaseAdditionalInfoBlockNode(node.__info, node.__data, node.__key);
  }

  static importJSON(
    json: SerializedAdditionalInfoBlockNode
  ): BaseAdditionalInfoBlockNode {
    return new BaseAdditionalInfoBlockNode(json, json);
  }

  constructor(info: Field, data: FieldValue, key?: NodeKey) {
    super(key);
    this.__info = info;
    this.__data = data;
    this.__autoFocus = false;
  }

  exportJSON(): SerializedAdditionalInfoBlockNode {
    const self = this.getLatest();
    return {
      ...self.__info,
      ...self.__data,
      type: this.getType(),
      version: 1,
    } as any;
  }

  createDOM(): HTMLElement {
    const spanEl = document.createElement('span');
    const self = this.getLatest();
    spanEl.id = String(self.__info.dataId);
    spanEl.style.cssText =
      'display:inline-flex;align-items:center;flex-wrap:wrap;padding:0';
    return spanEl;
  }

  updateDOM(): false {
    return false;
  }

  decorate(): JSX.Element {
    const self: BaseAdditionalInfoBlockNode = this.getLatest();
    return (
      <AdminParentAutoFillChildBlock
        nodeKey={self.__key}
        info={self.__info}
        data={self.__data}
        autoFocus={self.__autoFocus}
        loadOptions={this.loadOptions.bind(this)}
      />
    );
  }

  getInfoValue(): FieldValue {
    const self = this.getLatest();
    return self.__data;
  }

  setInfoValue(newValue: FieldValue) {
    const self = this.getLatest();
    if (!self.__data) return;
    // write
    this.sync(newValue);
  }

  private sync(newData: FieldValue) {
    // write
    const self = this.getWritable();
    if (!self.__data) return;
    self.__data = newData;
  }
}
export function $isAdditionalInfoBlockNode(
  node?: LexicalNode
): node is BaseAdditionalInfoBlockNode {
  return node instanceof BaseAdditionalInfoBlockNode;
}
