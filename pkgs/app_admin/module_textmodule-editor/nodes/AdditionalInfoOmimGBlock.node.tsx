import React from 'react';
import { DecoratorNode } from 'lexical';
import { Spread, NodeKey, LexicalNode, SerializedLexicalNode } from 'lexical';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import AdminParentAutoFillChildBlock from '../components/parent-auto-fill/AdminParentAutoFillChildBlock.component';
import {
  DataType,
  Field,
  FieldValue,
  InputType,
} from '@tutum/hermes/bff/catalog_sdebm_common';
import { IMenuItemWithData } from '@tutum/design-system/components';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { searchOmimG } from '@tutum/infrastructure/masterdata-service';
import { BaseAdditionalInfoBlockNode, IBaseAdditionalInfoHelper } from './BaseAddionalInfoBlock.node';

const ADDITIONAL_INFO_BLOCK_NODE_TYPE = 'sl-additional-info-omim-g-block';

export const OMIMG_CONFIG: Field = {
  fK: '5070',
  label: 'OMIM-G-Code of the investigated Gens',
  isMany: true,
  dataType: DataType.DataTypeInt,
  inputType: InputType.InputTypeDropDownListApi,
  dataSource: 'omim-p-code.',
  dataSourceValues: [],
  isRequired: false,
  treatmentCaseWithRule: [],
  additionalInformations: [
    {
      fK: '5072',
      label: 'Gen-Name',
      minLength: 0,
      maxLength: 60,
      isMany: true,
      dataType: DataType.DataTypeString,
      inputType: InputType.InputTypeTextInput,
      dataSource: '',
      dataSourceValues: [],
      isRequired: true,
      treatmentCaseWithRule: [],
      additionalInformations: [],
    },
  ],
};


export class AdditionalInfoOmimGBlockNode extends BaseAdditionalInfoBlockNode {
  static getType(): string {
    return ADDITIONAL_INFO_BLOCK_NODE_TYPE;
  }
  constructor(info: Field, data: FieldValue, key?: NodeKey) {
    super(info, data, key);
    if (!info?.dataId) {
      info.dataId = `${this.getType()}[${getUUID()}]`;
    }
    this.__info = info;
  }
  getConfig(): Field {
    return OMIMG_CONFIG;
  }
  static clone(node: AdditionalInfoOmimGBlockNode): AdditionalInfoOmimGBlockNode {
    return new AdditionalInfoOmimGBlockNode(node.__info, node.__data, node.__key);
  }
  loadOptions(inputQuery: string): Promise<Array<IMenuItemWithData<FieldValue>>> {
    if (!inputQuery) return Promise.resolve([]);
    return searchOmimG({
      query: inputQuery,
      selectedDate: datetimeUtil.now(),
    }).then((resp) =>
      resp.map((r) => {
        const result = {
          id: r.id,
          value: r.code,
          label: r.genName,
          code: r.code,
          data: {
            fK: OMIMG_CONFIG.fK,
            value: r.code,
            additionalInformations: [
              {
                fK: OMIMG_CONFIG.additionalInformations[0].fK,
                value: r.genName,
                additionalInformations: [],
              },
            ],
          },
        };
        return result;
      })
    );
  }
  static createDefaultData(): FieldValue {
    const config = OMIMG_CONFIG;
    return {
      fK: config.fK,
      value: '',
      additionalInformations: config.additionalInformations.map(
        (childInfor) => ({
          fK: childInfor.fK,
          value: '',
          additionalInformations: [],
        })
      ),
    };
  }
  static $createAdditionalInfoBlockNode(info: Field, data?: FieldValue, key?: NodeKey): AdditionalInfoOmimGBlockNode {
    if (!data) {
      data = AdditionalInfoOmimGBlockNode.createDefaultData();
    }
    return new AdditionalInfoOmimGBlockNode(info, data, key);
  }
}
