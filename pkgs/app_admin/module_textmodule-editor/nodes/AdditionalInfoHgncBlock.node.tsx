import { <PERSON><PERSON><PERSON><PERSON> } from 'lexical';
import {
  DataType,
  Field,
  FieldValue,
  InputType,
} from '@tutum/hermes/bff/catalog_sdebm_common';
import { IMenuItemWithData } from '@tutum/design-system/components';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { searchHgnc } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { BaseAdditionalInfoBlockNode, IBaseAdditionalInfoHelper } from './BaseAddionalInfoBlock.node';
import { getUUID } from '@tutum/design-system/infrastructure/utils/string';

const ADDITIONAL_INFO_BLOCK_HGNC_NODE_TYPE = 'sl-additional-info-hgnc-block';

export const HGNC_CONFIG: Field = {
  fK: "5077",
  label: "HGNC-Gensymbol",
  minLength: undefined,
  maxLength: undefined,
  isMany: true,
  dataType: DataType.DataTypeInt,
  inputType: InputType.InputTypeDropDownListApi,
  dataSource: "hgnc-gensymbol-code.",
  dataSourceValues: [],
  isRequired: false,
  treatmentCaseWithRule: [],
  additionalInformations: [
    {
      fK: "5078",
      label: "Gen-Name",
      minLength: 0,
      maxLength: undefined,
      isMany: true,
      dataType: DataType.DataTypeString,
      inputType: InputType.InputTypeTextInput,
      dataSource: "",
      dataSourceValues: [],
      isRequired: false,
      treatmentCaseWithRule: [],
      additionalInformations: [],
    }
  ]
}

export class AdditionalInfoHgncBlockNode extends BaseAdditionalInfoBlockNode {
  static getType(): string {
    return ADDITIONAL_INFO_BLOCK_HGNC_NODE_TYPE;
  }
  constructor(info: Field, data: FieldValue, key?: NodeKey) {
    super(info, data, key);
    if (!info?.dataId) {
      info.dataId = `${this.getType()}[${getUUID()}]`;
    }
    this.__info = info;
  }
  getConfig(): Field {
    return HGNC_CONFIG;
  }
  loadOptions(inputQuery: string): Promise<Array<IMenuItemWithData<FieldValue>>> {
    if (!inputQuery) return Promise.resolve([]);
    return searchHgnc({
      query: inputQuery,
      selectedDate: datetimeUtil.now(),
    }).then((resp) =>
      resp.data.items.map((r) => {
        const result = {
          id: r.code,
          value: r.code,
          label: r.description,
          code: r.code,
          data: {
            fK: HGNC_CONFIG.fK,
            value: r.code,
            additionalInformations: [
              {
                fK: HGNC_CONFIG.additionalInformations[0].fK,
                value: r.description,
                additionalInformations: [],
              },
            ],
          },
        };
        return result;
      })
    );
  }
  static clone(node: AdditionalInfoHgncBlockNode): AdditionalInfoHgncBlockNode {
    return new AdditionalInfoHgncBlockNode(node.__info, node.__data, node.__key);
  }
  static createDefaultData(): FieldValue {
    const config = HGNC_CONFIG;
    return {
      fK: config.fK,
      value: '',
      additionalInformations: config.additionalInformations.map(
        (childInfor) => ({
          fK: childInfor.fK,
          value: '',
          additionalInformations: [],
        })
      ),
    };
  }

  static $createAdditionalInfoBlockNode(info: Field, data?: FieldValue, key?: NodeKey): AdditionalInfoHgncBlockNode {
    if (!data) {
      data = AdditionalInfoHgncBlockNode.createDefaultData();
    }
    return new AdditionalInfoHgncBlockNode(info, data, key);
  }
}
