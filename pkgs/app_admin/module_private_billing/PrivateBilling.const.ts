import {
  LetterTemplate,
  TemplateType,
} from '@tutum/hermes/bff/legacy/doctor_letter_common';
import { PrivateBillingSetting } from '@tutum/hermes/bff/legacy/private_billing_setting_common';

export const getInitSetting = (
  templates: LetterTemplate[] = []
): PrivateBillingSetting => {
  let predefinedInvoiceId = '';
  let predefinedReminder = '';

  for (const t of templates) {
    if (!t.isPredefine) {
      continue;
    }
    if (t.type === TemplateType.TemplateType_Invoice) {
      predefinedInvoiceId = t.id;
    }
    if (t.type === TemplateType.TemplateType_Reminder) {
      predefinedReminder = t.id;
    }
  }
  return {
    privateInvoiceTemplateId: predefinedInvoiceId,
    firstReminder: {
      afterDay: 14,
      fee: 0,
      privateReminderTemplateId: predefinedReminder,
    },
    secondReminder: {
      afterDay: 14,
      fee: 0,
      privateReminderTemplateId: predefinedReminder,
    },
    thirdReminder: {
      afterDay: 14,
      fee: 0,
      privateReminderTemplateId: predefinedReminder,
    },
    invoiceNumber: {
      year: false,
      month: false,
      day: false,
      patientId: false,
    },
  };
};
