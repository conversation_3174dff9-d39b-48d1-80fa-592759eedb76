import React, { Dispatch, SetStateAction } from 'react';

import type PrinterProfile from '@tutum/admin/locales/en/PrinterProfile.json';

import { Flex, BodyTextM, Svg, Button } from '@tutum/design-system/components';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import {
  Popover,
  Menu,
  MenuItem,
  Intent,
} from '@tutum/design-system/components/Core';
import { PrinterProfileGroup } from '@tutum/hermes/bff/legacy/printer_profile_common';
import { Printer } from '@tutum/hermes/bff/legacy/printer_common';
import { TrustedDeviceResponse } from '@tutum/hermes/bff/app_admin';
import { Form } from '@tutum/hermes/bff/form_common';
import { COLOR } from '@tutum/design-system/themes/styles';

const MoreIcon = '/images/more-vertical.svg';
const EditIcon = '/images/edit-2.svg';
const RemoveIcon = '/images/trash-bin-red.svg';
const CheckIcon = '/images/check-circle-solid.svg';
const OpenSection = '/images/chevron-down.svg';
const CloseSection = '/images/chevron-up.svg';

export interface IContextGenColumn {
  devices: TrustedDeviceResponse[];
  printers: Printer[];
  forms: Form[];
  showSections: string[];
  setShowSections: Dispatch<SetStateAction<string[]>>;
}

interface IGenColumnsParams {
  t: IFixedNamespaceTFunction<keyof typeof PrinterProfile>;
  onEdit: (row: PrinterProfileGroup) => void;
  onRemove: (row: PrinterProfileGroup) => void;
  context: IContextGenColumn;
}

export const genColumns = ({
  t,
  onEdit,
  onRemove,
  context,
}: IGenColumnsParams): IDataTableColumn<PrinterProfileGroup>[] => {
  return [
    {
      name: '',
      cell: (row: PrinterProfileGroup) => (
        <Button
          onClick={() => {
            const isExist = context.showSections.find((s) => s == row.id);
            if (isExist) {
              context.setShowSections(
                context.showSections.filter((p) => p != row.id)
              );
            } else {
              context.setShowSections([...context.showSections!, row.id!]);
            }
          }}
          intent={Intent.NONE}
          className="sl-Button-Collapse"
          small
          icon={
            !!row.id && row.formIds?.length > 0 ? (
              <Svg
                src={
                  context.showSections.find((s) => s == row.id)
                    ? CloseSection
                    : OpenSection
                }
              />
            ) : undefined
          }
        />
      ),
      width: '50px',
      style: {
        padding: 0,
      },
      conditionalCellStyles: [
        {
          when: (row) => !row.id,
          style: {
            borderRight: 0,
          },
        },
      ],
    },
    {
      name: t('printerProfileName'),
      selector: (row) => row.name,
      style: {
        fontWeight: 'bold',
      },
      conditionalCellStyles: [
        {
          when: (row) => !row.id,
          style: {
            borderRight: 0,
            paddingLeft: 30,
            fontWeight: 400,
          },
        },
      ],
    },
    {
      name: t('device'),
      maxWidth: '300px',
      width: '20%',
      cell: (row: PrinterProfileGroup) => (
        <BodyTextM whiteSpace="pre-wrap">
          {[
            ...new Set(
              row?.deviceIds?.map(
                (d) => context.devices.find((p) => p.id == d)?.deviceName
              )
            ),
          ]
            .filter((d) => !!d)
            .join(', ')}
        </BodyTextM>
      ),
      style: {
        paddingTop: 4,
        paddingBottom: 4,
      },
      conditionalCellStyles: [
        {
          when: (row) => !row.id,
          style: {
            borderRight: 0,
          },
        },
      ],
    },
    {
      name: t('printer'),
      maxWidth: '250px',
      width: '20%',
      cell: (row: PrinterProfileGroup) => (
        <BodyTextM whiteSpace="pre-wrap">{row?.printerName}</BodyTextM>
      ),
      style: {
        paddingTop: 4,
        paddingBottom: 4,
      },
      conditionalCellStyles: [
        {
          when: (row) => !row.id,
          style: {
            borderRight: 0,
          },
        },
      ],
    },
    {
      name: t('tray'),
      width: '80px',
      cell: (row: PrinterProfileGroup) => (
        <BodyTextM whiteSpace="pre-wrap">{row?.configuration?.tray}</BodyTextM>
      ),
      style: {
        paddingTop: 4,
        paddingBottom: 4,
      },
      conditionalCellStyles: [
        {
          when: (row) => !row.id,
          style: {
            borderRight: 0,
          },
        },
      ],
    },
    {
      name: t('blancForm'),
      maxWidth: '110px',
      cell: (row: PrinterProfileGroup) => (
        <BodyTextM whiteSpace="pre-wrap">
          {row?.isBlancForm && <Svg src={CheckIcon} />}
        </BodyTextM>
      ),
      omit: false,
      style: {
        paddingTop: 4,
        paddingBottom: 4,
        justifyContent: 'center',
      },
      conditionalCellStyles: [
        {
          when: (row) => !row.id,
          style: {
            borderRight: 0,
          },
        },
      ],
    },
    {
      name: '',
      width: '50px',
      right: true,
      cell: (row: PrinterProfileGroup) => (
        <Popover
          className="sl-tooltip-more-icon"
          placement="bottom-end"
          content={
            <Menu className="action-menu">
              <MenuItem
                text={
                  <Flex align="center" gap={8}>
                    <Svg src={EditIcon} />
                    <BodyTextM>{t('edit')}</BodyTextM>
                  </Flex>
                }
                onClick={() => onEdit(row)}
              />
              <MenuItem
                text={
                  <Flex align="center" gap={8}>
                    <Svg src={RemoveIcon} />
                    <BodyTextM color={COLOR.TAG_BACKGROUND_RED}>
                      {t('remove')}
                    </BodyTextM>
                  </Flex>
                }
                onClick={() => onRemove(row)}
              />
            </Menu>
          }
        >
          {!!row.id && (
            <Svg className="sl-more-icon" src={MoreIcon} alt="more-icon" />
          )}
        </Popover>
      ),
      conditionalCellStyles: [
        {
          when: (row) => !row.id,
          style: {
            borderRight: 0,
          },
        },
      ],
    },
  ];
};
