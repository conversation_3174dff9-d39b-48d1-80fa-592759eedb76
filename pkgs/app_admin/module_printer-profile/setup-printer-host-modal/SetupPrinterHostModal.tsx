import {
  alertError,
  alertSuccessfully,
  Button,
  FormGroup2,
  Modal,
} from '@tutum/design-system/components';
import { InputGroup } from '@tutum/design-system/components/Core';
import { Field, Form, Formik } from 'formik';
import React from 'react';
import PrinterProfile from '@tutum/admin/locales/en/PrinterProfile.json';
import I18n from '@tutum/infrastructure/i18n';
import CommonLocales from '@tutum/admin/locales/en/Common.json';
import { saveSettings } from '@tutum/hermes/bff/legacy/app_mvz_settings';
import { useQueryGetSettings } from '@tutum/hermes/bff/legacy/app_admin_settings';
import printerService from '@tutum/admin/services/printer.service';
import { SettingsFeatures } from '@tutum/hermes/bff/legacy/settings_common';

export interface SetupPrinterHostModalProps {
  className?: string;
  isOpen?: boolean;
  onClose: () => void;
  printerHost?: string;
}

interface PrinterHostForm {
  printerHost: string;
}
export const SetupPrinterHostModal = ({
  className,
  isOpen,
  onClose,
  printerHost,
}: SetupPrinterHostModalProps) => {
  const { t } = I18n.useTranslation<keyof typeof PrinterProfile.printerHost>({
    namespace: 'PrinterProfile',
    nestedTrans: 'printerHost',
  });

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const onSubmit = async (value: PrinterHostForm) => {
    return saveSettings({
      feature: SettingsFeatures.SettingsFeatures_Printer,
      settings: {
        ...value,
      },
    }).then(() => {
      alertSuccessfully(t('success'));
      onClose();
      refetch();
    });
  };

  const handleClose = () => {
    onClose();
  };

  const { refetch } = useQueryGetSettings({
    feature: SettingsFeatures.SettingsFeatures_Printer,
    settings: [],
  });

  const validateIpAddress = async (value) => {
    if (!value) return t('required');

    const canConnect = await printerService.testConnection(value);
    if (!canConnect) {
      return t('failToConnect');
    }
  };

  return (
    <Modal
      className={className}
      title={t('title')}
      isOpen={isOpen}
      onClose={handleClose}
    >
      <Formik<PrinterHostForm>
        initialValues={{ printerHost }}
        onSubmit={onSubmit}
        validateOnChange={false}
      >
        {({ errors, touched, isSubmitting, dirty }) => (
          <Form>
            <div className="container">
              <div className="content">
                <FormGroup2
                  name="printerHost"
                  label={t('label')}
                  touched={touched}
                  errors={errors}
                >
                  <Field name="printerHost" validate={validateIpAddress}>
                    {({ field }) => (
                      <InputGroup {...field} placeholder={t('placeholder')} />
                    )}
                  </Field>
                </FormGroup2>
              </div>
              <div className="action">
                <Button intent="primary" outlined onClick={handleClose}>
                  {tButtonActions('cancelText')}
                </Button>
                <Button
                  intent="primary"
                  loading={isSubmitting}
                  disabled={
                    !dirty || (!!touched.printerHost && !!errors.printerHost)
                  }
                  type="submit"
                >
                  {tButtonActions('saveText')}
                </Button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};
