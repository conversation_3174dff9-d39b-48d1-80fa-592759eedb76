import { useCallback, useEffect, useMemo, useState } from 'react';

import type CommonLocales from '@tutum/admin/locales/en/Common.json';
import type PrinterProfile from '@tutum/admin/locales/en/PrinterProfile.json';

import SetupPrinterHostModal from '@tutum/admin/module_printer-profile/setup-printer-host-modal/SetupPrinterHostModal.styled';
import printerService from '@tutum/admin/services/printer.service';
import {
  alertError,
  Button,
  Flex,
  H1,
  InfoConfirmDialog,
  Svg,
  Tooltip,
} from '@tutum/design-system/components';
import { Divider, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import { Order } from '@tutum/hermes/bff/common';
import { Form as IForm } from '@tutum/hermes/bff/form_common';
import {
  getAllTrustedUserDeviceList,
  TrustedDeviceResponse,
} from '@tutum/hermes/bff/legacy/app_admin';
import { useQueryGetSettings } from '@tutum/hermes/bff/legacy/app_admin_settings';
import { getAllForms } from '@tutum/hermes/bff/legacy/app_mvz_form';
import {
  deletePrinterProfileGroup,
  getPrinterProfileGroups,
} from '@tutum/hermes/bff/legacy/app_printer_profile';
import { Printer } from '@tutum/hermes/bff/legacy/printer_common';
import { SettingsFeatures } from '@tutum/hermes/bff/legacy/settings_common';
import { PrinterProfileGroup } from '@tutum/hermes/bff/printer_profile_common';
import I18n from '@tutum/infrastructure/i18n';
import CreatePrinterProfile from './create_printer-profile/styled';
import { genColumns } from './setting-table';

const PlusIconSvgURL = '/images/plus-white.svg';
const GlobeSvgURL = '/images/globe.svg';

export interface IPrinterProfileProps {
  className?: string;
}

const conditionalRowStyles = [
  {
    when: (row) => !row.id,
    style: {
      // paddingLeft: 20,
      border: 0,
    },
  },
];

const PrinterProfilePage = (props: IPrinterProfileProps) => {
  const { className } = props;
  const { t } = I18n.useTranslation<keyof typeof PrinterProfile>({
    namespace: 'PrinterProfile',
  });
  const { t: tPrinterHost } = I18n.useTranslation<
    keyof typeof PrinterProfile.printerHost
  >({
    namespace: 'PrinterProfile',
    nestedTrans: 'printerHost',
  });
  const { t: tForms } = I18n.useTranslation<keyof typeof PrinterProfile.Forms>({
    namespace: 'PrinterProfile',
    nestedTrans: 'Forms',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const [isOpen, setIsOpen] = useState(false);
  const [printers, setPrinters] = useState<Printer[]>([]);
  const [forms, setForms] = useState<IForm[]>([]);
  const [devices, setDevices] = useState<TrustedDeviceResponse[]>([]);
  const [printerProfileGroups, setPrinterProfileGroups] = useState<
    PrinterProfileGroup[]
  >([]);
  const [editPrinterProfileGroup, setEditPrinterProfileGroup] =
    useState<PrinterProfileGroup>();
  const [isRemovePrinterProfile, setIsRemovePrinterProfile] =
    useState<string | undefined>(undefined);
  const [showSections, setShowSections] = useState<string[]>([]);

  const [setupPrinterHostModalOpen, setSetupPrinterHostModalOpen] =
    useState(false);
  const [printerConnected, setPrinterConnected] = useState(false);

  const { data, isSuccess } = useQueryGetSettings({
    feature: SettingsFeatures.SettingsFeatures_Printer,
    settings: [],
  });
  const printerHost = data?.settings?.printerHost;

  const tableData = useMemo(() => {
    const result: PrinterProfileGroup[] = [];
    printerProfileGroups.forEach((pr) => {
      result.push(pr);
      if (showSections.find((s) => s == pr.id)) {
        pr.formIds?.forEach((f) => {
          const form = forms.find((form) => form.id == f);
          if (form) {
            result.push({
              name: `${form.id.replaceAll('_', ' ')} - ${form['name']}`,
              printerName: '',
              deviceIds: [],
              isBlancForm: false,
              configuration: undefined!,
              formIds: [],
              isPrintQueue: false,
              formType: pr.formType,
              createdDate: 0,
              formCategories: [],
            });
          }
        });
      }
    });
    return result;
  }, [printerProfileGroups, showSections, forms]);

  const getPrinters = async () => {
    const printerHost = data?.settings?.printerHost;
    if (!printerHost) {
      return;
    }
    await printerService.initQZ(printerHost);
    const res = await printerService.detailPrinters();
    setPrinters(res);
    setPrinterConnected(true);
  };

  const getPrinterGroups = useCallback(async () => {
    getPrinterProfileGroups({
      paginationRequest: {
        page: 1,
        pageSize: 100,
        sortBy: '',
        order: Order.ASC,
      },
    }).then((res) => {
      setPrinterProfileGroups(res.data.printerProfileGroups);
    });
  }, [forms, showSections]);

  const getInital = async () => {
    await getPrinters();
    getAllTrustedUserDeviceList().then((res) => {
      setDevices(res.data.devices);
    });
    const res = await getAllForms();
    const forms = res.data.forms.map((form) => ({
      ...form,
      name: tForms(form.id as keyof typeof PrinterProfile.Forms),
    }));

    setForms(forms);
    await getPrinterGroups();
  };

  useEffect(() => {
    if (
      (printers.length == 0 && forms.length == 0 && devices.length == 0) ||
      !isOpen ||
      !isRemovePrinterProfile
    ) {
      if (!isSuccess) {
        return;
      }
      getInital();
    }
  }, [isOpen, isSuccess, isRemovePrinterProfile, printerHost]);

  const onEdit = (row: PrinterProfileGroup) => {
    setEditPrinterProfileGroup(row);
    setIsOpen(true);
  };
  const onRemove = (row: PrinterProfileGroup) => {
    setIsRemovePrinterProfile(row.id);
  };

  // todo
  const onChangePage = () => { };

  const onChangeRowsPerPage = () => { };

  const onCreatePrinterProfile = () => {
    setIsOpen(true);
  };

  return (
    <Flex className={className} column>
      <Flex p="24px 16px">
        <H1>{t('printerProfiles')}</H1>
      </Flex>
      <Divider style={{ margin: 0 }} />
      <Flex p={16} justify="flex-end" gap={8}>
        <SetupPrinterHostModal
          isOpen={setupPrinterHostModalOpen}
          onClose={() => setSetupPrinterHostModalOpen(false)}
          printerHost={printerHost}
        />
        <Button
          outlined
          className="sl-create-btn"
          icon={<Svg src={GlobeSvgURL} />}
          small
          onClick={() => setSetupPrinterHostModalOpen(true)}
        >
          {tPrinterHost('title')}
        </Button>
        <Tooltip
          disabled={!!printerHost && printerConnected}
          content={
            <div className="tooltip-content">
              {tPrinterHost('failToConnect')}
            </div>
          }
        >
          <Button
            intent="primary"
            className="sl-create-btn"
            icon={<Svg src={PlusIconSvgURL} />}
            small
            disabled={!printerHost || !printerConnected}
            onClick={onCreatePrinterProfile}
          />
        </Tooltip>
      </Flex>
      <Table
        className="sl-table"
        columns={genColumns({
          t: t,
          onEdit,
          onRemove,
          context: {
            devices,
            printers,
            forms,
            showSections,
            setShowSections,
          },
        })}
        data={tableData}
        pagination
        striped
        paginationServer
        paginationPerPage={20}
        paginationTotalRows={0}
        highlightOnHover
        responsive={false}
        noHeader
        persistTableHead
        progressPending={false}
        fixedHeader
        paginationResetDefaultPage
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
        conditionalRowStyles={conditionalRowStyles}
      />
      {isOpen && (
        <CreatePrinterProfile
          isOpen={isOpen}
          onClose={() => {
            setIsOpen(false);
            setEditPrinterProfileGroup(undefined);
          }}
          printers={printers}
          forms={forms}
          devices={devices}
          printerProfileGroup={editPrinterProfileGroup}
        />
      )}

      <InfoConfirmDialog
        isOpen={!!isRemovePrinterProfile}
        type={Intent.DANGER}
        title={t('titleWarningRemove')}
        cancelText={tButtonActions('noText')}
        confirmText={tButtonActions('yesRemove')}
        isCloseButtonShown={false}
        onClose={async () => {
          setIsRemovePrinterProfile(undefined);
        }}
        onConfirm={async () => {
          await deletePrinterProfileGroup({
            printerProfileGroupId: isRemovePrinterProfile!,
          });
          setIsRemovePrinterProfile(undefined);
        }}
      >
        {t('warningRemove')}
      </InfoConfirmDialog>
    </Flex>
  );
};

export default PrinterProfilePage;
