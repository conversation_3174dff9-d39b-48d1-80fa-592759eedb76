import React, { Fragment, useState, useMemo, useEffect } from 'react';
import { MultiValue, Options } from 'react-select';
import { Formik, Form, Field } from 'formik';

import type PrinterProfileI18n from '@tutum/admin/locales/en/PrinterProfile.json';
import type CommonLocales from '@tutum/admin/locales/en/Common.json';

import {
  Flex,
  ReactSelect,
  IMenuItem,
  MultiSelect,
  FormGroup2,
  Dialog,
} from '@tutum/design-system/components';
import formCategory from '@tutum/admin/public/data/printer_profile/form_category.json';
import I18n from '@tutum/infrastructure/i18n';
import {
  InputGroup,
  RadioGroup,
  Radio,
  Divider,
  H4,
  Checkbox,
  Button,
} from '@tutum/design-system/components/Core';
import { Form as IForm } from '@tutum/hermes/bff/form_common';
import {
  createEditPrinterProfile,
  getAlreadyFormHasPrinterProfile,
} from '@tutum/hermes/bff/legacy/app_printer_profile';
import {
  PrinterProfileGroup,
  FormType,
} from '@tutum/hermes/bff/printer_profile_common';
import { Printer } from '@tutum/hermes/bff/printer_common';
import { TrustedDeviceResponse } from '@tutum/hermes/bff/legacy/app_admin';
import FormUtils, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { REGEX_FLOAT_NUMBER } from '@tutum/infrastructure/utils/match';
import {
  Interpolation,
  Margins,
  PrintUnits,
  Size,
} from '@tutum/hermes/bff/legacy/printer_common';

export interface ICreatePrinterProfileProps {
  className?: string;
  isOpen: boolean;
  onClose: () => void;
  printers: Printer[];
  forms: IForm[];
  devices: TrustedDeviceResponse[];
  printerProfileGroup?: PrinterProfileGroup;
}

const CreatePrinterProfileModal = (props: ICreatePrinterProfileProps) => {
  const {
    className,
    isOpen,
    onClose,
    printers,
    forms,
    devices,
    printerProfileGroup,
  } = props;
  const isEdit = useMemo(() => !!printerProfileGroup, [printerProfileGroup]);
  const defaultValue =
    printerProfileGroup ||
    ({
      formType: FormType.FormType_Caterogy,
      configuration: {
        scaleContent: true,
        units: PrintUnits.PrintUnits_in,
      },
    } as PrinterProfileGroup);
  const { t } = I18n.useTranslation<keyof typeof PrinterProfileI18n>({
    namespace: 'PrinterProfile',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [trays, setTrays] = useState<string[]>([]);
  const [existCategoryForms, setExistCategoryForms] = useState<
    Map<string, string[]>
  >(new Map<string, string[]>());
  const [existSpecificForms, setExistSpecificForms] = useState<
    Map<string, string[]>
  >(new Map<string, string[]>());

  const interpolationItems = [
    Interpolation.Bicubic,
    Interpolation.Bilinear,
    Interpolation.Nearest_Neighbor,
  ].map((value) => {
    return {
      label: t(value as any),
      value: value,
    };
  });

  const onCloseCreatePrinterProfile = () => {
    onClose();
  };

  useEffect(() => {
    if (printerProfileGroup) {
      setTrays(
        printers.find((p) => p.name == printerProfileGroup.printerName)
          ?.trays || []
      );
    }
  }, [printerProfileGroup]);

  const onSubmitForm = async (data: PrinterProfileGroup) => {
    const payload = { ...data };
    payload.createdDate = datetimeUtil.now();
    payload.deviceIds = payload.deviceIds?.sort() || [];

    const sizeTransformed: Size = {
      height: null,
      width: null,
    };
    if (data.configuration.size) {
      const { size } = data.configuration;
      Object.keys(size).forEach((key) => {
        const val = parseFloat(size[key]?.toString().replace(',', '.'));
        sizeTransformed[key] = isNaN(val) || val === 0 ? null : val;
      });
    }

    const marginsTransformed: Margins = {
      bottom: null,
      left: null,
      right: null,
      top: null,
    };

    if (data.configuration.margins) {
      const { margins } = data.configuration;
      Object.keys(margins).forEach((key) => {
        const val = parseFloat(margins[key]?.toString().replace(',', '.'));
        marginsTransformed[key] = isNaN(val) || val === 0 ? null : val;
      });
    }

    payload.configuration.size = sizeTransformed;
    payload.configuration.margins = marginsTransformed;

    await createEditPrinterProfile({
      printerProfileGroup: payload,
    });
    onClose();
  };

  const getMsgErrorFieldFormIds = (): string => {
    // TODO: move the validation to BE
    return '';
  };

  const getMsgErrorFieldFormCategories = (): string => {
    return '';
  };

  const validateForm = (values: PrinterProfileGroup) => {
    const validateFields: ValidateField[] = [];
    validateFields.push({
      fieldName: 'name',
      validateRule: () => !values.name || isEmpty(values.name, true),
      errorMessage: t('printerNameRequired'),
    });
    validateFields.push({
      fieldName: 'formIds',
      validateRule: () => {
        return !!getMsgErrorFieldFormIds();
      },
      errorMessage: '',
    });
    validateFields.push({
      fieldName: 'formCategories',
      validateRule: () => {
        return Boolean(getMsgErrorFieldFormCategories());
      },
      errorMessage: '',
    });
    const { errors } = FormUtils.validateForm(validateFields, null);
    if (!errors['formIds'] && !!getMsgErrorFieldFormIds()) {
      errors['formIds'] = getMsgErrorFieldFormIds();
    }
    if (!errors['formCategories'] && getMsgErrorFieldFormCategories()) {
      errors['formCategories'] = getMsgErrorFieldFormCategories();
    }
    return errors;
  };

  const renderCustomContent = (item) => {
    if (item.id === 'Doctor_Letter') {
      return ` - ${t('privateBilling')}`;
    }
    return item.id ? ` - ${item.id}` : '';
  };

  return (
    <Dialog
      className={className}
      title={isEdit ? t('editTitle') : t('createTitle')}
      isOpen={isOpen}
      onClose={onCloseCreatePrinterProfile}
      shouldReturnFocusOnClose
      canEscapeKeyClose={true}
    >
      <Formik<PrinterProfileGroup>
        onSubmit={onSubmitForm}
        initialValues={defaultValue}
        validate={validateForm}
        render={({
          isSubmitting,
          values,
          errors,
          touched,
          submitCount,
          setFieldValue,
        }) => {
          const onChangeInputNumber = (fieldName: string, val: string) => {
            if (!val) {
              setFieldValue(fieldName, null);
              return;
            }

            if (val.match(REGEX_FLOAT_NUMBER)) {
              setFieldValue(fieldName, val);
            }
          };

          return (
            <Form
              className="sl-form-content"
              onKeyDown={(e) =>
                e.key === 'Enter' && !e.shiftKey && e.preventDefault()
              }
            >
              <Flex column className="sl-container">
                <Flex className="sl-form-body" column px="16">
                  <Flex mb="12">
                    <FormGroup2
                      label={t('printerProfileName')}
                      name="name"
                      errors={errors}
                      touched={touched}
                      submitCount={submitCount}
                      isRequired
                    >
                      <Field name="name">
                        {({ field }) => {
                          return <InputGroup {...field} />;
                        }}
                      </Field>
                    </FormGroup2>
                  </Flex>
                  <Flex mb="12">
                    <FormGroup2
                      label={t('useThisPrinterProfileFor')}
                      name="formType"
                      errors={errors}
                      touched={touched}
                      submitCount={submitCount}
                      isRequired
                    >
                      <Field name={`formType`}>
                        {({ field, form }) => (
                          <RadioGroup
                            onChange={(e) => {
                              form.setFieldValue(
                                field.name,
                                e.currentTarget.value
                              );
                              form.setFieldValue('formCategories', []);
                              form.setFieldValue('formIds', []);
                            }}
                            inline
                            selectedValue={field.value}
                          >
                            <Radio
                              label={t('formCategory')}
                              value={FormType.FormType_Caterogy}
                            />
                            <Radio
                              label={t('specificForms')}
                              value={FormType.FormType_Specific}
                            />
                          </RadioGroup>
                        )}
                      </Field>
                    </FormGroup2>
                  </Flex>
                  <Flex mb="12" id="forms_wrapper">
                    {values.formType === FormType.FormType_Caterogy ? (
                      <Fragment>
                        <FormGroup2
                          label={t('formCategory')}
                          name="formCategories"
                          errors={errors}
                          touched={touched}
                          submitCount={submitCount}
                          isRequired
                        >
                          <Field name="formCategories">
                            {({ field, form }) => {
                              return (
                                <MultiSelect
                                  id={form.name}
                                  isClearable
                                  isMulti
                                  hideSelectedOptions
                                  instanceId={form.name}
                                  className={values.formType}
                                  value={formCategory
                                    .filter((f) =>
                                      field?.value?.find((v) => v == f.id)
                                    )
                                    .map(
                                      (f) =>
                                      ({
                                        label: t(f.id as any),
                                        value: f.id,
                                      } as IMenuItem)
                                    )}
                                  options={formCategory.map(
                                    (f) =>
                                    ({
                                      label: t(f.id as any),
                                      value: f.id,
                                    } as IMenuItem)
                                  )}
                                  onChange={(
                                    newValue: MultiValue<
                                      IMenuItem<string | number>
                                    >
                                  ) => {
                                    form.setFieldValue(
                                      field.name,
                                      newValue.map((n) => n.value)
                                    );
                                  }}
                                  renderContentCustom={(item) => (
                                    <div>
                                      {item.label}
                                      {item.id ? ` - ${item.id}` : ''}
                                    </div>
                                  )}
                                />
                              );
                            }}
                          </Field>
                        </FormGroup2>
                      </Fragment>
                    ) : (
                      <Fragment>
                        <FormGroup2
                          label={t('formCategory')}
                          name="formIds"
                          errors={errors}
                          touched={touched}
                          submitCount={submitCount}
                          isRequired
                        >
                          <Field name="formIds">
                            {({ field, form }) => {
                              return (
                                <MultiSelect
                                  id={form.name}
                                  isClearable
                                  isMulti
                                  hideSelectedOptions
                                  instanceId={form.name}
                                  className={values.formType}
                                  value={forms
                                    .filter((f) =>
                                      field?.value?.find((v) => v == f.id)
                                    )
                                    .map(
                                      (f) =>
                                      ({
                                        label: `${f.id.split('_').join(' ')}`,
                                        value: f.id,
                                        id: f.id,
                                      } as IMenuItem)
                                    )}
                                  options={forms.map(
                                    (f) =>
                                    ({
                                      label: `${f.id.split('_').join(' ')}`,
                                      value: f.id,
                                      id: f.id,
                                    } as IMenuItem)
                                  )}
                                  isOptionDisabled={(
                                    option: IMenuItem,
                                    selectValue: Options<IMenuItem>
                                  ) => {
                                    if (
                                      values.formType ==
                                      FormType.FormType_Caterogy
                                    ) {
                                      if (selectValue.length == 0) {
                                        return false;
                                      }
                                      const filteredForms = forms.filter((f) =>
                                        selectValue.find((n) => n.value == f.id)
                                      );
                                      const findOption = forms.find(
                                        (f) => f.id == option.value
                                      );
                                      return !(
                                        filteredForms[0]?.size ==
                                        findOption?.size
                                      );
                                    }
                                    return false;
                                  }}
                                  onChange={(
                                    newValue: MultiValue<
                                      IMenuItem<string | number>
                                    >
                                  ) => {
                                    form.setFieldValue(
                                      field.name,
                                      newValue.map((n) => n.value)
                                    );
                                  }}
                                  renderContentCustom={(item) => (
                                    <div>
                                      {item.label}
                                      {renderCustomContent(item)}
                                    </div>
                                  )}
                                />
                              );
                            }}
                          </Field>
                        </FormGroup2>
                      </Fragment>
                    )}
                  </Flex>
                  <Flex mb="12">
                    <FormGroup2
                      label={t('device')}
                      name="deviceIds"
                      errors={errors}
                      touched={touched}
                      submitCount={submitCount}
                      isRequired
                    >
                      <Field name="deviceIds">
                        {({ field, form }) => {
                          return (
                            <MultiSelect
                              id={form.name}
                              isClearable
                              isMulti
                              hideSelectedOptions
                              instanceId={form.name}
                              defaultValue={devices
                                .filter((d) =>
                                  field?.value?.find((v) => v == d.id)
                                )
                                .map(
                                  (d) =>
                                  ({
                                    label: d.deviceName,
                                    value: d.id,
                                    id: d.id,
                                  } as IMenuItem)
                                )}
                              options={devices.map(
                                (d) =>
                                ({
                                  label: d.deviceName,
                                  value: d.id,
                                  id: d.id,
                                } as IMenuItem)
                              )}
                              onChange={(
                                newValue: MultiValue<IMenuItem<string | number>>
                              ) => {
                                const deviceIds = newValue.map(
                                  (n) => n.value as string
                                );
                                form.setFieldValue(field.name, deviceIds);
                                if (deviceIds.length > 0) {
                                  getAlreadyFormHasPrinterProfile({
                                    deviceIds: deviceIds,
                                  }).then((res) => {
                                    const existForms = res.data.forms;
                                    const existCategories =
                                      res.data.formCategories;
                                    const tempexistCategoryForms =
                                      existCategoryForms;
                                    const tempExistSpecificForms =
                                      existSpecificForms;
                                    Object.keys(existCategories).forEach(
                                      (k) => {
                                        tempexistCategoryForms.set(k, [
                                          ...(tempexistCategoryForms.get(k) ??
                                            []),
                                          ...existCategories[k].formCategories,
                                        ]);
                                      }
                                    );
                                    Object.keys(existForms).forEach((k) => {
                                      tempExistSpecificForms.set(k, [
                                        ...(tempExistSpecificForms.get(k) ??
                                          []),
                                        ...existForms[k].forms,
                                      ]);
                                    });
                                    setExistCategoryForms(
                                      tempexistCategoryForms
                                    );
                                    setExistSpecificForms(
                                      tempExistSpecificForms
                                    );
                                  });
                                }
                              }}
                            />
                          );
                        }}
                      </Field>
                    </FormGroup2>
                  </Flex>
                  <Flex className="sl-Flex-Row" mb="12">
                    <Flex>
                      <FormGroup2
                        label={t('printer')}
                        name="printerName"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                        isRequired
                      >
                        <Field name="printerName">
                          {({ field, form }) => {
                            return (
                              <ReactSelect
                                id={form.name}
                                instanceId={form.name}
                                isSearchable={false}
                                selectedValue={field.value}
                                items={printers.map(
                                  (p) =>
                                  ({
                                    label: p.name,
                                    value: p.name,
                                  } as IMenuItem)
                                )}
                                onItemSelect={(item: IMenuItem) => {
                                  form.setFieldValue(field.name, item.value);
                                  setTrays(
                                    printers.find((p) => p.name == item.value)
                                      ?.trays || []
                                  );
                                  form.setFieldValue(
                                    'configuration.tray',
                                    undefined
                                  );
                                }}
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                    <Flex>
                      <FormGroup2
                        label={t('targetTray')}
                        name="configuration.tray"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                        isRequired
                      >
                        <Field name="configuration.tray">
                          {({ field, form }) => {
                            return (
                              <ReactSelect
                                id={form.name}
                                instanceId={form.name}
                                isSearchable={false}
                                selectedValue={field.value}
                                items={trays.map((t) => ({
                                  label: t,
                                  value: t,
                                }))}
                                onItemSelect={(item: IMenuItem) => {
                                  form.setFieldValue(field.name, item.value);
                                }}
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                    <Flex>
                      <FormGroup2
                        label={t('amountOfCopies')}
                        name="configuration.copies"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.copies">
                          {({ field }) => {
                            return <InputGroup type="number" {...field} />;
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                  </Flex>
                  <Flex className="sl-Flex-Row" mb="12">
                    <Flex>
                      <FormGroup2
                        label={t('density')}
                        name="configuration.density"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.density">
                          {({ field }) => {
                            return <InputGroup {...field} />;
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                    <Flex>
                      <FormGroup2
                        label={t('interpolation')}
                        name="configuration.interpolation"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.interpolation">
                          {({ field, form }) => {
                            return (
                              <ReactSelect
                                id={form.name}
                                instanceId={form.name}
                                isSearchable={false}
                                selectedValue={field.value}
                                items={interpolationItems}
                                onItemSelect={(item: IMenuItem) => {
                                  form.setFieldValue(field.name, item.value);
                                }}
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                  </Flex>
                  <Divider />
                  <H4>{t('scaling')}</H4>
                  <Flex>
                    <FormGroup2 label={t('units')} name="configuration.units">
                      <Field name={`configuration.units`}>
                        {({ field, form }) => (
                          <RadioGroup
                            onChange={(e) => {
                              form.setFieldValue(
                                field.name,
                                e.currentTarget.value
                              );
                            }}
                            inline
                            selectedValue={
                              field.value || PrintUnits.PrintUnits_in
                            }
                          >
                            <Radio
                              label={t('in')}
                              value={PrintUnits.PrintUnits_in}
                            />
                            <Radio
                              label={t('mm')}
                              value={PrintUnits.PrintUnits_mm}
                            />
                            {/*
                            Temporary not use 
                            <Radio
                            label={t('cm')}
                            value={PrintUnits.PrintUnits_cm}
                          /> */}
                          </RadioGroup>
                        )}
                      </Field>
                    </FormGroup2>
                  </Flex>
                  <Flex className="sl-Flex-Row" mb="12">
                    <Flex>
                      <FormGroup2
                        label={t('distanceFromTop')}
                        name="configuration.margins.top"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.margins.top">
                          {({ field }) => {
                            return (
                              <InputGroup
                                {...field}
                                value={
                                  field.value
                                    ? field.value?.toLocaleString('de-DE')
                                    : null
                                }
                                autoFocus
                                onChange={(event) =>
                                  onChangeInputNumber(
                                    field.name,
                                    event.target.value
                                  )
                                }
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                    <Flex>
                      <FormGroup2
                        label={t('distanceFromLeft')}
                        name="configuration.margins.left"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.margins.left">
                          {({ field }) => {
                            return (
                              <InputGroup
                                {...field}
                                value={
                                  field.value
                                    ? field.value?.toLocaleString('de-DE')
                                    : null
                                }
                                autoFocus
                                onChange={(event) =>
                                  onChangeInputNumber(
                                    field.name,
                                    event.target.value
                                  )
                                }
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                    <Flex>
                      <FormGroup2
                        label={t('distanceFromBottom')}
                        name="configuration.margins.bottom"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.margins.bottom">
                          {({ field }) => {
                            return (
                              <InputGroup
                                {...field}
                                value={
                                  field.value
                                    ? field.value?.toLocaleString('de-DE')
                                    : null
                                }
                                autoFocus
                                onChange={(event) =>
                                  onChangeInputNumber(
                                    field.name,
                                    event.target.value
                                  )
                                }
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                    <Flex>
                      <FormGroup2
                        label={t('distanceFromRight')}
                        name="configuration.margins.right"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.margins.right">
                          {({ field }) => {
                            return (
                              <InputGroup
                                {...field}
                                value={
                                  field.value
                                    ? field.value?.toLocaleString('de-DE')
                                    : null
                                }
                                autoFocus
                                onChange={(event) =>
                                  onChangeInputNumber(
                                    field.name,
                                    event.target.value
                                  )
                                }
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                  </Flex>
                  <Flex className="sl-Flex-Row" mb="12">
                    <Flex>
                      <FormGroup2
                        label={t('adjustWidth')}
                        name="configuration.size.width"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.size.width">
                          {({ field }) => {
                            return (
                              <InputGroup
                                {...field}
                                value={
                                  field.value
                                    ? field.value?.toLocaleString('de-DE')
                                    : null
                                }
                                autoFocus
                                onChange={(event) =>
                                  onChangeInputNumber(
                                    field.name,
                                    event.target.value
                                  )
                                }
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                    <Flex>
                      <FormGroup2
                        label={t('adjustHeight')}
                        name="configuration.size.height"
                        errors={errors}
                        touched={touched}
                        submitCount={submitCount}
                      >
                        <Field name="configuration.size.height">
                          {({ field }) => {
                            return (
                              <InputGroup
                                {...field}
                                value={
                                  field.value
                                    ? field.value?.toLocaleString('de-DE')
                                    : null
                                }
                                autoFocus
                                onChange={(event) =>
                                  onChangeInputNumber(
                                    field.name,
                                    event.target.value
                                  )
                                }
                              />
                            );
                          }}
                        </Field>
                      </FormGroup2>
                    </Flex>
                  </Flex>
                  <Divider />
                  <Flex my="12">
                    <Flex>
                      <Field name="configuration.scaleContent">
                        {({ field }) => {
                          return (
                            <Checkbox
                              {...field}
                              checked={!!field?.value}
                              id={field.name}
                              name={field.name}
                            >
                              <span>{t('scaleContent')}</span>
                            </Checkbox>
                          );
                        }}
                      </Field>
                    </Flex>
                    <Flex>
                      <Field name="configuration.duplex">
                        {({ field }) => {
                          return (
                            <Checkbox
                              {...field}
                              checked={!!field?.value}
                              id={field.name}
                              name={field.name}
                            >
                              <span>{t('duplex')}</span>
                            </Checkbox>
                          );
                        }}
                      </Field>
                    </Flex>
                    <Flex>
                      <Field name="configuration.rasterize">
                        {({ field }) => {
                          return (
                            <Checkbox
                              {...field}
                              checked={!!field?.value}
                              id={field.name}
                              name={field.name}
                            >
                              <span>{t('rasterize')}</span>
                            </Checkbox>
                          );
                        }}
                      </Field>
                    </Flex>
                    <Flex>
                      <Field name="isPrintQueue">
                        {({ field }) => {
                          return (
                            <Checkbox
                              {...field}
                              checked={!!field?.value}
                              id={field.name}
                              name={field.name}
                            >
                              <span>{t('printQueue')}</span>
                            </Checkbox>
                          );
                        }}
                      </Field>
                    </Flex>
                    <Flex>
                      <Field name="isBlancForm">
                        {({ field }) => {
                          return (
                            <Checkbox
                              {...field}
                              checked={!!field?.value}
                              id={field.name}
                              name={field.name}
                            >
                              <span>{t('blancFormDes')}</span>
                            </Checkbox>
                          );
                        }}
                      </Field>
                    </Flex>
                  </Flex>
                </Flex>
                <Divider style={{ margin: 0 }} />
                <Flex gap={8} className="sl-Flex-Row" justify="flex-end" p="16">
                  <Button
                    intent="primary"
                    outlined
                    minimal
                    type="button"
                    onClick={onCloseCreatePrinterProfile}
                    disabled={isSubmitting}
                    loading={isSubmitting}
                  >
                    {tButtonActions('cancelText')}
                  </Button>
                  <Button
                    type="submit"
                    intent="primary"
                    disabled={isSubmitting}
                    loading={isSubmitting}
                  >
                    {isEdit
                      ? tButtonActions('saveText')
                      : tButtonActions('create')}
                  </Button>
                </Flex>
              </Flex>
            </Form>
          );
        }}
      />
    </Dialog>
  );
};

export default CreatePrinterProfileModal;
