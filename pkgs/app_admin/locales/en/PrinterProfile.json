{"printerProfiles": "Printer Profiles", "printerProfileName": "Printer Profile Name", "device": "<PERSON><PERSON>", "printer": "Printer", "tray": "Tray", "blancForm": "<PERSON>", "edit": "Edit", "remove": "Remove", "createTitle": "Create Printer Profile", "editTitle": "Edit Printer Profile", "useThisPrinterProfileFor": "Use this printer profile for", "formCategory": "Form category", "specificForms": "Specific Forms", "targetTray": "Target Tray", "amountOfCopies": "Amount of copies", "printOrientation": "Print Orientation", "portrait": "Portrait", "landscape": "Landscape", "scaling": "Sc<PERSON>", "scaleContent": "Scale Content", "distanceFromTop": "Distance From Top", "distanceFromLeft": "Distance From Left", "distanceFromBottom": "Distance From Bottom", "distanceFromRight": "Distance From Right", "adjustWidth": "<PERSON>ju<PERSON>", "adjustHeight": "Adjust Height", "duplex": "Apply duplex printing to forms that are allowed to be printed as duplex", "printQueue": "Always print with print queue", "blancFormDes": "Print as blanc form", "printerNameRequired": "Printer profile name is required", "blancFormNotAvailable": "Blanc form is not available for {{formId}}", "warningRemove": "Printer profile will be removed permanently. This action cannot be undone.", "units": "Units", "in": "IN", "mm": "MM", "cm": "CM", "titleWarningRemove": "Remove printer profile?", "density": "Density", "interpolation": "Interpolation", "rasterize": "Rasterize", "bicubic": "Bicubic", "bilinear": "Bilinear", "nearest-neighbor": "Nearest Neighbor", "Forms": {"Muster_1": "Sick leave", "Muster_2A": "Prescription of hospital care", "Muster_2B": "Prescription of hospital care", "Muster_2C": "Prescription of hospital care", "Muster_4": "Prescription of a sick person's transport", "Muster_6": "Letter of referral", "Muster_6_cover_letter": "Muster 6 cover letter", "Muster_7": "Referral for checking the somatic symptoms before requesting Psychotherapy", "Muster_8": "Prescription of visual aids", "Muster_8A": "Prescription of increasing visual aids", "Muster_11": "Report for the medical institution of insurance company", "Muster_13": "HEIMI prescription", "Muster_15": "Prescription of hearing aids", "Muster_16": "Red form- Prescription of medicine", "Muster_26A": "Sociotherapy prescription according to § 37a SGB V", "Muster_26B": "Verordnung Soziotherapie gem. § 37a SGB V", "Muster_26C": "Verordnung Soziotherapie gem. § 37a SGB V", "Muster_39A": "Early detection of cervical cancer", "Muster_39B": "Early detection of cervical cancer", "Muster_22A": "Consultation report before starting psychotherapy", "Muster_50": "Request for responsibility of another health insurance", "Muster_51": "Request for the responsibility of another cost unit", "Muster_52": "Report for the health insurance company if the inability to work persists", "Muster_52_0_V2": "Report for the health insurance company if the inability to work persists", "Muster_52_2": "Report for the health insurance company if the inability to work persists", "Muster_55": "Certificate of a serious chronic illness", "Muster_3A": "Muster 3", "Muster_3B": "Muster 3", "Muster_5": "Billing form", "Muster_9": "Certificate of premature birth or child disability", "Muster_10": "Referral for lab examination", "Muster_10C": "Order for SARS-CoV-2 testing", "Muster_10A": "Anforderungsschein für Laboratoriumsuntersuchungen bei Laborgemeinschaften", "Muster_12A": "Prescription of home nursing care", "Muster_12B": "Prescription of home nursing care", "Muster_12C": "Prescription of home nursing care", "Muster_27A": "Sociotherapeutic care plan according to § 37a SGB V", "Muster_27B": "Sociotherapeutic care plan according to § 37a SGB V", "Muster_27C": "Sociotherapeutic care plan according to § 37a SGB V", "Muster_28A": "Regulation sociotherapy", "Muster_28B": "Regulation sociotherapy", "Muster_28C": "Regulation sociotherapy", "Gruenes_Rezept": "Green form", "T-Rezept-Muster": "T form", "PatientHeader": "<PERSON><PERSON>", "Muster_19A": "Emergency/Representation card", "Muster_19B": "Emergency/Representation card", "Muster_19C": "Emergency/Representation card", "Muster_21": "Medical certificate for the receipt of sickness benefit in case of illness of a child", "Muster_36_E_2017_07": "Prevention recommendation", "Begleitschreiben_FaV": "Begleitschreiben_FaV", "Muster_20A": "re-integration plan", "Muster_20B": "re-integration plan", "Muster_20C": "re-integration plan", "Muster_20D": "re-integration plan", "BKK_BY_HZV_Notfallplan_geriatrischer_Patient": "Emergency Plan", "Ueberleitungsbogen_EK_BKK_NO_WL": "Transition Management", "Ueberleitungsbogen_AOK_KBS_NO_WL": "Transition Management", "AOK_HH_HZV_Ueberleitungsbogen": "Transition Management", "AOK_SH_HZV_Ueberleitungsmanagement": "Transition Management", "RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen": "Transition Management", "Muster_61": "Forms for prescribing medical rehabilitation", "Muster_65A": "Medical certificate child", "Muster_65B": "Medical certificate child", "Muster_64": "Prescribing medical care for mothers or fathers", "Muster_64B": "Prescribing medical care for mothers or fathers", "Muster_70": "Treatment plan", "Muster_70_B": "Treatment plan", "Muster_70A": "Follow-up treatment plan", "Muster_70A_B": "Follow-up treatment plan", "Muster_63B": "Prescription of specialized outpatient palliative care", "Muster_63C": "Prescription of specialized outpatient palliative care", "Muster_63D": "Prescription of specialized outpatient palliative care", "Muster_56": "Application for reimbursement of rehabilitation sports/functional training", "Muster_N63A": "Prescription of specialized outpatient palliative care", "Muster_N63B": "Prescription of specialized outpatient palliative care", "Muster_N63C": "Prescription of specialized outpatient palliative care", "Muster_N63D": "Prescription of specialized outpatient palliative care", "BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung": "BKK_BOSCH_BW Schnellinfo Patientenbegleitung", "BKK_BY_HZV_Schnellinfo_Patientenbegleitung": "BKK_BY_HZV Schnellinfo Patientenbegleitung", "BKK_VAG_BW_Schnellinformation_Patientenbegleitung": "BKK_VAG_BW Schnellinformation zur Patientenbegleitung", "BKK_BOSCH_VAG_BW_Praeventionsverordnung": "BKK_BOSCH_VAG_BW Präventionsverordnung", "BKK_BY_HZVNotify": "For billing positions of patient support the signature of patient is required. Please forward one example to patient and store the signed document in your practice.", "Muster_PTV_1A": "Application for psychotherapy", "Muster_PTV_1B": "Application for psychotherapy", "Muster_PTV_1C": "Application for psychotherapy", "Muster_PTV_11A": "Individuelle Information Zur Psychotherapeutischen Sprechstunde", "Muster_PTV_11B": "Individuelle Information Zur Psychotherapeutischen Sprechstunde", "Muster_PTV_2A": "Details Therapist", "Muster_PTV_2B": "Details Therapist", "Muster_PTV_2C": "Details Therapist", "Muster_PTV_3": "Leitfaden zum erstellen des Berichts an die Gutachter*in", "Muster_PTV_10": "Ambulante Psychotherapie in der gesetzlichen Krankenversicherung", "Muster_PTV_12A": "Report Of Acute Treatment", "Muster_PTV_12B": "Report Of Acute Treatment", "G81_EHIC_Bulgarisch": "G81_EHIC_Bulgarisch", "G81_EHIC_Danisch": "G81_E<PERSON><PERSON>_<PERSON>", "G81_EHIC_Englisch": "G81_EHIC_Englisch", "G81_EHIC_Franzosisch": "G81_EHIC_<PERSON>", "G81_EHIC_Griechisch": "G81_EHIC_Griechisch", "G81_EHIC_Italienisch": "G81_EHIC_Italienisch", "G81_EHIC_Kroatisch": "G81_EHIC_Kroatisch", "G81_EHIC_Niederlandisch": "G81_EHIC_Niederlandisch", "G81_EHIC_Polnisch": "G81_EHIC_Polnisch", "G81_EHIC_Rumanisch": "G81_EHIC_Rumanisch", "G81_EHIC_Spanisch": "G81_EHIC_Spanisch", "G81_EHIC_Tschechisch": "G81_EHIC_Tschechisch", "G81_EHIC_Ungarisch": "G81_EHIC_Ungarisch", "G81_EHIC_Finnisch": "G81_EHIC_<PERSON>isch", "G81_EHIC_Estnisch": "G81_EHIC_Estnisch", "G81_EHIC_Slowenisch": "G81_EHIC_Slowenisch", "G81_EHIC_Slowakisch": "G81_EHIC_Slow<PERSON>sch", "G81_EHIC_Schwedisch": "G81_EHIC_Schwedisch", "G81_EHIC_Portugiesisch": "G81_EHIC_Portugiesisch", "G81_EHIC_Litauisch": "G81_EHIC_Litauisch", "G81_EHIC_Lettisch": "G81_EHIC_Lettisch", "G81_EHIC_All": "G81_EHIC_All", "enrollment_document": "DMP Enrollment Document", "bmp": "BMP", "Blaues_Rezept": "<PERSON><PERSON><PERSON>", "Btm_Rezept_Print": "Btm Rezept Print", "Muster_eRezept": "Muster e<PERSON>ez<PERSON>", "DMP_Documentation": "DMP Documentation", "Patient_Bill_Print_Preview": "Patient Bill", "AOK_Nordwet": "AOK Nordwet", "AOK_Bremen_impfstoff": "AOK Bremen", "Muster_16a_bay": "Muster 16a bay", "Muster_61A": "Muster 61A", "Muster_61B": "Muster 61B", "Muster_61C": "Muster 61C", "Muster_61D": "Muster 61D", "DMP_Enrollment_Form": "DMP Enrollment Form", "PHQ_9_Q3_2023": "PHQ-9", "HIMI_QUESTION_NAME": "HIMI question name", "SV_FORMS": "SV Forms", "F1000": "Durchgangsarztbericht", "F1050": "Ärztliche Unfallmeldung", "F2100": "Zwischenbericht", "F9990": "BG billing"}, "A4_landscape": "A4 - Landscape", "A4_portrait": "A4 - Portrait", "A5_landscape": "A5 - Landscape", "A5_portrait": "A5 - Portrait", "A6_landscape": "A6 - Landscape", "privateBilling": "Private Billing", "printerHost": {"title": "Set up printer host", "ipAddress": "IP Address", "required": "IP Address is required", "invalid": "Invalid IP Address", "success": "Printer host saved", "failToConnect": "Failed to connect to the printer host. Please check the IP address and network connection.", "label": "Host", "placeholder": "E.g: xxx.xxx.xxx.xxx"}}