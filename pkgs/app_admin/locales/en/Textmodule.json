{"textmodule": "Textmodule", "textShortcut": "Text shortcut", "content": "Content", "noTextModuleMessage": "No textmodule created yet", "edit": "Edit", "remove": "Remove", "search": "Search", "createTextmodule": "Create textmodule", "editTextmodule": "Edit textmodule", "save": "Save", "Dialog": {"create": "Create", "cancel": "Cancel", "triggerPlaceholder": "Input keyword without spaces", "freeText": "Freetext", "questionnaire": "Questionnaire", "freeTextButton": "Insert placeholder", "freeTextPlaceholderContent": "Type “%” to insert a placeholder, or continue typing", "questionnaireLabelPlaceholder": "Ex: Do you have increased thirst?", "answerLabelPlaceholder": "Option {{number}}", "omimGChainPlaceholderContent": "Type ( to select OMIM-G Chain", "hgncChainPlaceholderContent": "Type ( to select HGNC Chain", "notSupportQuestionnaires": "Forms and BMP do not support questionnaires", "formFields": {"trigger": "Text shortcut", "textShortcut": "Text shortcut", "content": "Content", "usedFor": "Used for", "questionnaireLabel": "Question {{number}}"}, "errors": {"IsExist": "{{fieldName}} already exists", "Empty999999Description": "Gen-Name is required for the OMIM-G 999999"}, "questionType": {"single": "Single selection", "multi": "Multiple selection", "freetext": "Freetext input"}, "addOption": "Add option", "addOtherOption": "Add \"Other\" option", "leavePageTitle": "Leave without saving?", "leavePageContent": "Unsaved changes will be discarded if you leave this page.", "confirmLeave": "Yes, leave", "cancelLeave": "No, stay here", "textShortcutError": "Text shortcut is required", "missingContentError": "Content is required", "addQuestionnaireLabel": "Add question", "emptyQuestion": "Empty question", "answerOther": "Other", "textAnswerLabelPlaceholder": "Answer", "TextModuleUseFor_Anamnesis": "Anam<PERSON><PERSON>", "TextModuleUseFor_Cave": "Cave", "TextModuleUseFor_Findings": "Finding", "TextModuleUseFor_Note": "Note", "TextModuleUseFor_Therapy": "Therapy", "TextModuleUseFor_OmimGChain": "OMIM-G chain", "createTextmoduleSuccess": "Textmodule created", "createTextmoduleFailed": "Failed to create textmodule", "editTextmoduleSuccess": "<PERSON><PERSON><PERSON><PERSON> edited", "editTextmoduleFailed": "Failed to edit textmodule", "usedForError": "This field is required", "TextModuleUseFor_Form": "Form", "TextModuleUseFor_Doctorletter": "Doctor Letter & Private Billing", "TextModuleUseFor_BMP": "BMP", "TextModuleUseFor_HGNC": "HGNC"}, "RemoveConfirmationDialog": {"removeTitle": "Remove textmodule?", "no": "No", "yesRemove": "Yes, remove", "actionCannotBeUndone": "Textmodule will be removed permanently. This action cannot be undone.", "removeTextmoduleSuccess": "Textmodule removed"}, "AdditionalInfo": {"5070": "OMIM-G-Code of the investigated Gens", "5072": "Gen-Name", "5077": "HGNC-Gensymbol", "5078": "Gen-Name"}}