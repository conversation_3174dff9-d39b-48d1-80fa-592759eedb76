{"UpdateDialog": {"copied": "<PERSON>pied", "title": "Access info", "pairingInfoDes": "Please copy below info and send it to the care provider.", "adminPROInfoEmailSubject": "Thank you for your order & welcome to our garrio Community!", "adminPROInfoTemplate": "Liebe/r <PERSON>/<PERSON>au Dr. <PERSON>.,\n\nwir freuen uns sehr, dass Sie sich für garrioPRO  entschieden haben. Anbei erhalten Sie Ihre Zugangsdaten, mit der Bitte diese sorgfältig aufzubewahren:\n\nAdmin: {{adminUrl}} \nBenutzername: {{adminUsername}}\nPassword: {{adminPwd}}\n\nViele hilfreiche Antworten sowie ein Benutzerhandbuch finden Sie auf der Webseite (https://garrio.de/kb/).\n\nSollten Sie Fragen/ Anregungen oder Wünsche haben, kontaktieren Sie uns gerne unter der Telefonnummer: (0711) 80 60 79-400.\n\nWir wünschen Ihnen und Ihrem Praxisteam viel Erfolg mit dem Start von garrioPRO & freuen uns auf Ihr Feedback!\n\nMit freundlichen Grüßen", "sendViaEmail": "Send via e-mail", "infoIsSentAndCloseThisPopup": "Info sent. Please close this popup.", "copy": "Copy", "send": "Send", "PROAccessLink": "garrioPRO Admin link:", "careProviderId": "Tenant name:", "account": "Username:", "password": "Password:", "noteLabel": "Note:", "note": "The password can only be copied at this time."}, "Employee": {"no": "No", "yes": "Yes", "confirmReset": "Yes, reset", "deactivateMessage": "Account info and history of {{name}} ({{username}}) are retained.", "reset2FAMessage": "User will be required to set up 2FA options for authentication all over again once you reset.", "resetPasswordMessage": "The current password of {{name}} ({{username}}) will no longer be valid once you reset the password", "activateMessage": "Are you sure?", "accountActivated": "Account activated", "accountDeactivated": "Account deactivated", "nameUserName": "Name/Username", "activateAccount": "Activate account", "status": "status", "createdDate": "Created Date", "reset2FA": "Reset 2FA options", "activate": "Activate", "deactivate": "Deactivate", "lanr": "LANR", "update": "Update profile", "searchName": "Search by name, username", "addByUsername": "Add by username", "accountManagement": "Account management", "createAccount": "Create account", "title": "title", "firstName": "first name", "lastName": "last name", "userName": "Username", "initials": "initials", "role": "role", "editEmployee": "Edit", "moreAction": "More", "resetPassword": "Reset password", "deactivateAccount": "Deactivate account", "resetPasswordConfirmTitle": "Reset password?", "resetPasswordConfirmContent1": "You are about to create new password for  ", "resetPasswordConfirmContent2": "You cannot recover old password once new password is set.", "resetPasswordTitle": "Reset password", "cancelResetPasswordBtn": "Cancel", "resetPasswordBtn": "Reset password", "saveResetPasswordBtn": "Save", "newPasswordLabel": "New password", "activateParticipationSuccess": "Participation activated", "deactivateParticipationSuccess": "Participation deactivated", "confirmPasswordLabel": "Confirm password", "successToaster": "Password has been reset", "errPasswordEmpty": "MUST contain an upper case letter \nMUST contain a lowercase letter \nMUST contain a number \nMUST contain a special character \nMUST contain at least 8 characters", "errConfirmPasswordEmpty": "MUST contain an upper case letter \nMUST contain a lowercase letter \nMUST contain a number \nMUST contain a special character \nMUST contain at least 8 characters", "errConfirmPasswordNotEqualPassword": "Password does not match", "errSamePassWithOldPass": "New password must be different from old password", "resetPasswordSuccess": "Password has been reset", "saveAccountSuccess": "Account created", "editAccountSuccess": "Account updated", "additionalName": "Additional name", "titleDefault": "-", "titleV1": "Dr.", "titleV2": "Dr. Dr.", "titleV3": "Prof.", "titleV4": "Prof. Dr.", "titleV5": "Prof. Dr. Dr.", "titleV6": "PD Dr.", "titleV7": "PD Dr. Dr.", "titleV8": "Sr. Dr.", "titleV9": "Sr.", "noResults": "No options. Press enter to choose this title.", "DOCTOR": "Doctor", "MANAGER": "Manager", "MFA": "MFA", "CEO": "CEO"}, "CreateEmployee": {"createEmployee": "Create account", "updateEmployee": "Update account", "accountInformation": "Bank info", "doctorInformation": "Doctor info", "doctorStamp": "Doctor stamp", "contractInformation": "Contract info", "role": "Role", "doctor": "Doctor", "mfa": "MFA", "admin": "Admin", "ceo": "CEO", "additionalAdmin": "Additional admin rights to access historization?", "isAdminNo": "No", "isAdminYes": "Yes", "gender": "Gender", "maleGender": "Male", "femaleGender": "Female", "indefiniteGender": "Indefinite", "unknownGender": "Unknown", "diversGender": "Various", "title": "Title", "titleDefault": "-", "titleV1": "Dr.", "titleV2": "Dr. Dr.", "titleV3": "Prof.", "titleV4": "Prof. Dr.", "titleV5": "Prof. Dr. Dr.", "titleV6": "PD Dr.", "titleV7": "PD Dr. Dr.", "titleV8": "Sr. Dr.", "titleV9": "Sr.", "noResults": "No options. Press enter to choose this title.", "firstName": "First name", "lastName": "Last name", "userName": "Username", "createPassword": "Password", "confirmPassword": "Confirm password", "lanr": "LANR", "pseudoLanr": "P<PERSON>udo LANR", "bsnr": "BSNR", "areaOfExpertise": "Area of expertise", "searchAreaOfExpertise": "Select expertise", "hasContracts": "Does this doctor participate in selective contract(s)?", "isContractNo": "no", "isContractYes": "yes", "hasFavContracts": "FaV contracts", "hasHzvContracts": "HzV contracts", "havgId": "HÄVG ID", "havgVpId": "HÄVG VP-ID", "noData": "No data", "havgVpIdWarningTooltip": "HÄVG-ID and Enrolment URL required for data retrieval.", "havgVpIdErrorTooltip": "Data is outdated due to connection interruption", "havgVpIDHint": "Please make sure you only update your contract information after consultation with HÄVG. Only with confirmation from HÄVG you are allowed to edit. For further questions, please contact the HÄVG support.", "mediId": "MEDI ID", "mediVpId": "MEDI VP-ID", "mediVpIdWarningTooltip": "MEDI-ID and Enrolment URL required for data retrieval.", "mediVpIdErrorTooltip": "Data is outdated due to connection interruption", "contract": "Contract", "enrollmentType": "Enrollment Type", "enrollmentType_online": "Online", "enrollmentType_offline": "Offline", "contractValidDate": "Contract valid date", "startOn": "Start on", "endOn": "End on", "selectContract": "Select contract", "contractStartDate": "Start date", "contractEndDate": "End date", "addContract": "Add a new contract", "optional": "optional", "urlRequired": "This field is required", "hpmSetup": {"title": "HPM setup", "doctorCheckbox": "Doctor participates in selective contract(s)", "url": "environment URL", "confirmTitle": "Change HPM environment URL?", "confirmDescription": "Disconnecting the practice from HPM can lead to connection issues and potential risks.", "confirmAction": "Yes, confirm", "warningNoBSNR": {"title": "No BSNR selected", "description": "A BSNR with HPM connection is required to set up selective contracts for doctors."}, "warningNoHpm": {"title": "HPM is not available", "description": "No HPM connection found in the current BSNR. Please select another one with HPM setup."}}, "firstNamePlaceholder": "e.g \"<PERSON>\"", "lastNamePlaceholder": "e.g \"<PERSON>\"", "areaOfExpertisePlaceholder": "Select expertise", "lanrPlaceholder": "e.g \"********9\"", "bsnrPlaceholder": "e.g \"********9\"", "havgIDPlaceholder": "e.g \"1234567\"", "havgVPIDPlaceholder": "e.g \"H00012345\"", "mediIDPlaceholder": "e.g \"********\"", "mediVPIDPlaceholder": "e.g \"FAABBCCDDE\"", "phoneNumberPlaceholder": "e.g 0151 ********", "finishCreateAccount": "Create", "saveAccount": "Save", "cancelAccount": "Cancel", "createAnotherAccount": "Save and create another account", "errFirstNameEmpty": "This field is required", "errLastNameEmpty": "This field is required", "errUserNameEmpty": "This field is required", "errInitialEmpty": "This field is required", "existInitial": "Initial already exists", "existEmail": "E-mail already exists", "existUserName": "Username already exists", "atLeastChars": "At least {{value}} characters", "invalidUserName": "Invalid username. Please try again.", "allowCharacter": "Allowed characters:", "allowCharacterLetters": "Letters (a-z, A-Z)", "allowCharacterNumbers": "Numbers (0-9)", "allowCharacterSpecial": "Some special characters:\n._-!#$%&‘*+/=?^_{|}~", "characterCaseSensitive": "Case sensitive. 'User123' is different from 'user123'.", "errPasswordUpperCase": "MUST contain an uppercase letter", "errPasswordLowerCase": "MUST contain a lowercase letter", "errPasswordNumber": "MUST contain a number", "errPasswordSpecial": "MUST contain a special character", "errPasswordLength": "MUST contain at least 8 characters", "errConfirmPasswordEmpty": "MUST contain an upper case letter \nMUST contain a lowercase letter \nMUST contain a number \nMUST contain a special character \nMUST contain at least 8 characters", "errConfirmPasswordNotEqualPassword": "Password does not match", "errTypeEmpty": "This field is required", "errSelectAreaOfExpertise": "Please select an option from the list", "errLanrInvalidCommonRule": "Must include 9 digits", "errLanrIsPseudoLanrFormat": "Lifelong doctor number of the contract doctor / contract psychotherapist must not be 555555", "errLanrInvalidDigitRule": "Lifelong doctor number of the contract doctor / contract psychotherapist (format nnnnnnmff) has invalid m value", "errBsnrInvalid": "Must include 9 digits", "errHzvInvalid": "Must include from 5 to 7 digits", "errHzvContractInvalid": "Please select at least one contract", "errFavInvalid": "Must include 8 digits", "errFavContractInvalid": "Please select at least one contract", "errPhoneNumberInvalid": "Invalid format. Please try again.", "errInvalidBsnr": "Invalid BSNR input, please try again.", "errInvalidDevice": "This field is required", "duplicateHzvID": "HÄVG ID already exists. Please use another ID.", "duplicateMediID": "MEDI ID already exists. Please use another ID.", "duplicateLanrID": "LANR already exists. Please use another ID.", "duplicateInitials": "Initials already exists. Please use another initial.", "leavePageTitle": "Leave without saving?", "leavePageContent": "Unsaved changes will be discarded if you leave this page.", "confirmLeave": "Yes, leave", "cancelLeave": "No, stay here", "feeCatalogue": "Fee catalogue", "captionChargeSystemIDs": "Only 1 fee catalogue will be applied to this contract. Fee catalogue cannot be changed later.", "mobileNumber": "Mobile phone number", "startDate": "start date", "endDate": "end date", "assignedPractice": "Assigned Practice", "device": "De<PERSON><PERSON>", "deviceTooltip": "Default device when the user logs in to Practice app. User can modify this setting anytime.", "markAsBillingDoctor": "<PERSON> as billing doctor", "additionalName": "Additional name", "intendWord": "Intend word", "salutation": "Salutation", "Herr": "<PERSON>", "Frau": "<PERSON><PERSON>", "Keine": "None", "initials": "initials", "jobDescription": "Job description", "errJobDescription": "This field is required", "activeDMPProgram": "Active DMPs", "include": "include", "asvTeamNumber": "ASV Team Number", "digitRule": "{{fieldName}} (format {{pattern}}) has invalid p value.", "markAsEmployedDoctor": "Billing through another Doctor", "responsibleDoctor": "Billing Doctor", "responsibleDoctorHelpText": "Select this option if the billing is not processed by the current Doctor, but by another one (e.g., in the case of a Doctor in training).", "representativeDoctor": "Representative Doctor", "errInvalidResponsibleDoctor": "This field is required", "errInvalidRepresentativeDoctor": "This field is required", "initialPassword": "Initial password", "invalidStartDate": "Invalid start date", "invalidEndDate": "Invalid end date", "eHKSSelection": "selection of ehks documentation", "eHKSDermatologist": "eHKS Dermatologist", "eHKSNonDermatologist": "eHKS Non-Dermatologist", "potentialContract": "Potential Contract", "contractAvailable": "Contract available", "endDateBeforeStartDate": "End date must be after start date", "overlapDate": "Start date overlaps with an existing contract.", "favHint": "Please be aware you can activate your specialist contract\n- For rheumatology with activating AOK Baden-Württemberg Orthopedic\n- For diabetology with activating AOK Baden-Württemberg Gastroenterology or Cardiology"}}