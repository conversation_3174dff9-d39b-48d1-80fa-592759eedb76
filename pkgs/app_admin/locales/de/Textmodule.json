{"textmodule": "Textmodule", "textShortcut": "Aufrufkü<PERSON>el", "content": "Inhalt", "noTextModuleMessage": "<PERSON><PERSON> Textmodule v<PERSON>handen", "edit": "<PERSON><PERSON><PERSON>", "remove": "Löschen", "search": "<PERSON><PERSON>", "createTextmodule": "<PERSON><PERSON><PERSON><PERSON>", "editTextmodule": "<PERSON><PERSON><PERSON><PERSON> bear<PERSON>", "save": "Speichern", "Dialog": {"create": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "triggerPlaceholder": "Eingabe ohne Leerzeichen", "freeText": "Freitext", "questionnaire": "Fragen", "freeTextButton": "Platzhalter einfügen", "freeTextPlaceholderContent": "<PERSON><PERSON>en Si<PERSON> “%” ein, um Platzhalter auswählen zu können", "questionnaireLabelPlaceholder": "Beispiel: Haben Si<PERSON> erhöhten Durst?", "answerLabelPlaceholder": "Option {{number}}", "omimGChainPlaceholderContent": "Eingabe ( zur Auswahl von OMIM-G", "hgncChainPlaceholderContent": "Eingabe ( zur Auswahl von HGNC", "notSupportQuestionnaires": "Formulare und BMP unterstützen keine Fragen", "formFields": {"trigger": "Aufrufkü<PERSON>el", "textShortcut": "Aufrufkü<PERSON>el", "content": "Inhalt", "usedFor": "<PERSON><PERSON><PERSON> für", "questionnaireLabel": "Frage {{number}}"}, "errors": {"IsExist": "{{fieldName}} existiert bereits", "Empty999999Description": "Der Gen-Name ist erforderlich für OMIM-G 999999"}, "questionType": {"single": "Auswahl", "multi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freetext": "Freitexteingabe"}, "addOption": "Weitere Option hinzufügen", "addOtherOption": "\"Sonstiges\" hinzufügen", "leavePageTitle": "Diese Se<PERSON> verlassen?", "leavePageContent": "<PERSON>cht gespeicherte Änderungen gehen verloren, wenn Sie die Seite verlassen.", "confirmLeave": "Ja, verlassen", "cancelLeave": "<PERSON><PERSON>", "textShortcutError": "Aufrufkürzel ist ein Pflichtfeld", "missingContentError": "Inhalt ist ein Pflichtfeld", "addQuestionnaireLabel": "Frage hinzufügen", "emptyQuestion": "Frage wurde noch nicht ausgefüllt", "answerOther": "Sonstiges", "textAnswerLabelPlaceholder": "Antwort", "TextModuleUseFor_Anamnesis": "Anamnese", "TextModuleUseFor_Cave": "Cave", "TextModuleUseFor_Findings": "Befund", "TextModuleUseFor_Note": "Notizen", "TextModuleUseFor_Therapy": "<PERSON><PERSON><PERSON>", "TextModuleUseFor_OmimGChain": "OMIM-G-Kette", "createTextmoduleSuccess": "<PERSON><PERSON><PERSON><PERSON> wurde erstellt", "createTextmoduleFailed": "Textmodul konnte nicht erstellt werden", "editTextmoduleSuccess": "<PERSON><PERSON><PERSON><PERSON> wurde bearbeitet", "editTextmoduleFailed": "<PERSON><PERSON><PERSON> von <PERSON>eh<PERSON>", "usedForError": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "TextModuleUseFor_Form": "Formular", "TextModuleUseFor_Doctorletter": "Brief", "TextModuleUseFor_BMP": "BMP", "TextModuleUseFor_HGNC": "HGNC"}, "RemoveConfirmationDialog": {"removeTitle": "<PERSON><PERSON><PERSON><PERSON>?", "no": "<PERSON><PERSON>", "yesRemove": "Ja, löschen", "actionCannotBeUndone": "Ihr Textmodul wird dauerhaft gelöscht. Die Aktion kann nicht rückgängig gemacht werden.", "removeTextmoduleSuccess": "Text<PERSON><PERSON><PERSON> wurde <PERSON>"}, "AdditionalInfo": {"5070": "OMIM-G-Kode", "5072": "Gen-Name", "5077": "HGNC-Gensymbol", "5078": "Gen-Name"}}