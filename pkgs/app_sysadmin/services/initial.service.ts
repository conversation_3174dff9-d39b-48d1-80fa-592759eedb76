import { SysadminApi } from '@tutum/infrastructure/resource/SysadminAppResource';

export interface IGlobalInitial {
  userProfile?: { externalId: string };
}

async function getInitial(): Promise<IGlobalInitial> {
  try {
    const loggedInUserInfo = await SysadminApi.getMyLoggedInInfo();
    return {
      userProfile: {
        externalId: loggedInUserInfo.data.externalId,
      },
    };
  } catch (error) {
    console.error('Failed to get initial data', error);
    return {
      userProfile: undefined,
    };
  }
}

export default {
  getInitial,
};
