import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AppSkeleton from '@tutum/sysadmin/components/app-skeleton';
import Layout from '@tutum/sysadmin/components/layout';
import { ROUTING } from '@tutum/sysadmin/types/route.type';
import { AppProps } from 'next/app';
import React, { useCallback, useEffect } from 'react';

import '@blueprintjs/core/lib/css/blueprint.css';
import '@blueprintjs/icons/lib/css/blueprint-icons.css';
import { setBaseUrl } from '@tutum/hermes/bff/legacy/api_client';
import { initializeOpenTelemetry } from '@tutum/infrastructure/instrument/opentelemetry';
import { BASE_PATH_SYS_ADMIN } from '@tutum/infrastructure/utils/string.util';
import Head from 'next/head';

// import 'bluebird/js//browser/bluebird.core.min';
setBaseUrl(BASE_PATH_SYS_ADMIN);
const client = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      throwOnError: (error) => {
        Promise.reject(error);
        return false;
      },
    },
    mutations: {
      throwOnError: (error) => {
        Promise.reject(error);
        return false;
      },
    },
  },
});

// Theme.instance;
const apiErrorRedirect = {
  401: ROUTING.LOGIN,
  403: ROUTING.LOGIN,
};

const ReactQueryDevtoolsProduction = React.lazy(() =>
  import('@tanstack/react-query-devtools/build/modern/production.js').then(
    (d) => ({
      default: d.ReactQueryDevtools,
    })
  )
);

function App(props: AppProps) {
  const { Component, pageProps } = props;

  const shouldUseLayout = useCallback((pathname: string) => {
    return [ROUTING.LOGIN].every((route) => !pathname.includes(route));
  }, []);
  useEffect(() => {
    initializeOpenTelemetry('web-sysadmin');
  }, []);

  const [showDevtools, setShowDevtools] = React.useState(false);

  React.useEffect(() => {
    // @ts-expect-error
    window.toggleDevtools = () => setShowDevtools((old) => !old);
  }, []);

  return (
    <>
      <Head>
        <title>garrioPRO</title>
      </Head>
      <div style={{ height: '100vh', margin: 0, padding: 0 }}>
        <QueryClientProvider client={client}>
          {showDevtools && (
            <React.Suspense fallback={null}>
              <ReactQueryDevtoolsProduction buttonPosition="bottom-left" />
            </React.Suspense>
          )}
          <AppSkeleton apiErrorRedirect={apiErrorRedirect}>
            {({ hasLoggedIn }) => {
              if (shouldUseLayout(window.location.pathname)) {
                return (
                  <Layout isAuthenticated={hasLoggedIn}>
                    <Component {...pageProps} />
                  </Layout>
                );
              }
              return <Component {...pageProps} />;
            }}
          </AppSkeleton>
        </QueryClientProvider>
      </div>
    </>
  );
}

export default App;
