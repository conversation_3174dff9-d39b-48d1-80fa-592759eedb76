import {
  ErrorBoundaryWithRouter,
  LoadingState,
} from '@tutum/design-system/components';
import { Dialog, OverlayToaster } from '@tutum/design-system/components/Core';
import GlobalContext from '@tutum/sysadmin/contexts/Global.context';
import React, { useCallback, useEffect, useState } from 'react';

import Provider from '@tutum/design-system/themes/Provider';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import I18n from '@tutum/infrastructure/i18n';
import InitialService from '@tutum/sysadmin/services/initial.service';
import moment from 'moment';
import { useErrorCodeI18n } from '../../hooks/useErrorCode';

// This code is used to solve issue here https://github.com/palantir/blueprint/issues/394
// Popover.defaultProps.modifiers = { computeStyle: { gpuAcceleration: false } };

export interface IAppSkeletonProps {
  children: (param: { hasLoggedIn: boolean }) => React.ReactNode;
  apiErrorRedirect: {
    [key: number]: string;
  };
}

export interface IAppSkeletonState {
  userProfile?: { externalId: string };
  configs?: {
    [key: string]: object;
  };
}

function AppSkeleton(props: IAppSkeletonProps) {
  const { children, apiErrorRedirect } = props;
  const [isLoading, setIsLoading] = useState(true);
  const [state, setState] = useState<IAppSkeletonState>({});
  const { lang } = I18n.useTranslation({ namespace: undefined });
  const errorCodeT = useErrorCodeI18n();

  useEffect(() => {
    InitialService.getInitial().then((initial) => {
      setState({ ...initial });
      setIsLoading(false);
    });
  }, []);

  useEffect(() => {
    moment.locale(lang);
  }, [lang]);

  const reloadUserProfile = useCallback(() => {
    return InitialService.getInitial().then((initial) => {
      setState((prevState) => ({
        ...prevState,
        userProfile: initial.userProfile,
      }));
    });
  }, []);

  return (
    <Provider>
      {isLoading && <LoadingState />}
      {!isLoading && (
        <ErrorBoundaryWithRouter
          user={state.userProfile}
          Dialog={Dialog}
          Toaster={OverlayToaster}
          apiErrorRedirect={apiErrorRedirect}
          t={errorCodeT}
          errorCodes={ErrorCode}
        >
          <GlobalContext.Provider
            userProfile={state.userProfile}
            configs={state.configs}
            reloadUserProfile={reloadUserProfile}
          >
            {children({
              hasLoggedIn: !!state.userProfile,
            })}
          </GlobalContext.Provider>
        </ErrorBoundaryWithRouter>
      )}
    </Provider>
  );
}

export default AppSkeleton;
