import React from 'react';
import { Flex } from '@tutum/design-system/components';
import { ISysadminTheme } from '@tutum/sysadmin/theme';

export interface ILayoutProps {
  className?: string;
  theme?: ISysadminTheme;
  isAuthenticated: boolean;
  children?: React.ReactNode;
}

const Layout = ({ className, children }: ILayoutProps) => {
  return (
    <Flex auto column className={className}>
      <nav className="bp5-navbar bp5-dark">
        <Flex auto justify="space-between">
          <div className="bp5-navbar-group">
            <div className="bp5-navbar-heading">Silentium.io</div>
          </div>
        </Flex>
      </nav>
      {children}
    </Flex>
  );
};

export default Layout;
