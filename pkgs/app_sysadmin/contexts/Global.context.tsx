import React, {
  FunctionComponent,
  ReactNode,
  useContext,
  useMemo,
} from 'react';

export interface IGlobalContext {
  configs: { [key: string]: object };
  userProfile: { externalId: string };
  reloadUserProfile: () => Promise<void>;
}

export interface IGlobalProviderProps {
  configs: { [key: string]: object };
  userProfile: { externalId: string };
  children: ReactNode;
  reloadUserProfile: () => Promise<void>;
}

const GlobalContext = React.createContext<IGlobalContext>({
  userProfile: undefined!,
  configs: undefined!,
  reloadUserProfile: () => Promise.resolve(undefined),
});

function GlobalContextProvider(props: IGlobalProviderProps) {
  const { configs, userProfile, reloadUserProfile, children } = props;
  const globalContext: IGlobalContext = useMemo(() => {
    return {
      configs,
      userProfile,
      reloadUserProfile,
    };
  }, [configs, userProfile, reloadUserProfile]);

  return (
    <GlobalContext.Provider value={globalContext}>
      {children}
    </GlobalContext.Provider>
  );
}

function withGlobalContext<TProps>(
  Component: FunctionComponent<TProps & IGlobalContext>
): FunctionComponent<TProps> {
  return (props: TProps) => {
    return (
      <GlobalContext.Consumer>
        {(contexts) => <Component {...props} {...contexts} />}
      </GlobalContext.Consumer>
    );
  };
}

function useGlobalContext() {
  const context = useContext(GlobalContext);
  return context;
}

export default {
  Consumer: GlobalContext.Consumer,
  Provider: GlobalContextProvider,
  instance: GlobalContext,
  withContext: withGlobalContext,
  useContext: useGlobalContext,
};
