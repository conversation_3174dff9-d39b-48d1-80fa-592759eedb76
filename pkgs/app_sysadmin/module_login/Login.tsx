import React, { useState, FunctionComponentElement, memo } from 'react';
import { with<PERSON>out<PERSON>, NextRouter } from 'next/router';
import { Button, Intent } from '@tutum/design-system/components/Core';
import { H1, Flex } from '@tutum/design-system/components';
import { ISysadminTheme } from '@tutum/sysadmin/theme';
import GlobalContext, {
  IGlobalContext,
} from '@tutum/sysadmin/contexts/Global.context';
import { ROUTING } from '@tutum/sysadmin/types/route.type';
import { LEGACY_TOPIC_LoginV2 } from '@tutum/hermes/bff/legacy/app_sysadmin';
import { BASE_PATH_SYS_ADMIN } from '@tutum/infrastructure/utils/string.util';

export interface ILoginProps {
  className?: string;
  theme?: ISysadminTheme;
}

function Login(
  props: ILoginProps & IGlobalContext & { router: NextRouter }
): FunctionComponentElement<ILoginProps> {
  const { className, router } = props;

  const [redirectingToLoginPage, setRedirectingToLoginPage] =
    useState<boolean>(false);
  const handleLogin = () => {
    setRedirectingToLoginPage(true);
    router.push(
      `${LEGACY_TOPIC_LoginV2}?redirectUrl=${BASE_PATH_SYS_ADMIN}${ROUTING.TRUSTED_DEVICES}`
    );
  };

  return (
    <Flex auto align="center" className={className} column>
      <nav className="bp5-navbar bp5-dark">
        <div className="bp5-navbar-group bp5-align-left">
          <div className="bp5-navbar-heading">Silentium</div>
        </div>
      </nav>
      <Flex
        justify="center"
        align="center"
        column
        className="sl-login-box"
        auto
      >
        <Button
          intent={Intent.NONE}
          outlined
          loading={redirectingToLoginPage}
          disabled={redirectingToLoginPage}
          data-test-id="login-sysadmin-btn"
          onClick={handleLogin}
        >
          <H1>Log In</H1>
        </Button>
        <p>
          {`By clicking "Log In" above, you acknowledge that you
        have read and understood, and agree to garrioPro's and that you have
        read and understood, and consent to garrioPro's .`}
        </p>
      </Flex>
    </Flex>
  );
}

export default memo(withRouter(GlobalContext.withContext(Login)));
