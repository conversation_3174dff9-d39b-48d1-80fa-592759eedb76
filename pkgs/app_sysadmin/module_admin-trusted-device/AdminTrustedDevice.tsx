import React, { useState, memo } from 'react';
import { Button, Intent, Dialog } from '@tutum/design-system/components/Core';
import { Flex } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import Theme, { ISysadminTheme } from '@tutum/sysadmin/theme';
import AdminTrustedDeviceList from './AdminTrustedDeviceList.styled';
import AdminPairingForm from './AdminPairingForm.styled';
import { useQueryGetAllDevice } from '@tutum/hermes/bff/legacy/app_sysadmin';
import { useListenOnCareProvider } from '@tutum/hermes/bff/app_sysadmin';

import type ErrorCode from '@tutum/sysadmin/locales/en/ErrorCode.json';

export interface IAdminTrustedDeviceProps {
  className?: string;
  theme?: ISysadminTheme;
}

function AdminTrustedDevice(props: IAdminTrustedDeviceProps) {
  const [open, setOpenState] = useState(false);
  const {
    theme: { space },
  } = props.theme ? props : { theme: Theme };

  const { t } = I18n.useTranslation<keyof typeof ErrorCode>({
    namespace: 'ErrorCode',
  });

  const {
    data: devices,
    isLoading: loading,
    refetch: reloadDeviceList,
  } = useQueryGetAllDevice({
    select: (data) => data.data.devices,
  });

  useListenOnCareProvider((data) => {
    if (data) {
      reloadDeviceList();
    }
  });

  const onCloseDialog = () => setOpenState(false);
  const onOpenDialog = () => setOpenState(true);

  return (
    <Flex auto column p={space.s}>
      <Flex justify="flex-end" pb={space.s}>
        <Button
          className="cy-add-device-btn"
          intent={Intent.PRIMARY}
          text="Add Practice's admin device"
          data-test-id="cy-add-device-btn"
          onClick={onOpenDialog}
        />
      </Flex>
      <Flex auto pt="0.6rem">
        <AdminTrustedDeviceList
          reloadDevice={reloadDeviceList}
          devices={devices}
          loading={loading}
        />
      </Flex>
      <Dialog
        isOpen={open}
        onClose={onCloseDialog}
        title="Add Practice's Admin device"
        canOutsideClickClose={false}
      >
        <AdminPairingForm
          t={t}
          onAddedDevice={() => {
            reloadDeviceList();
            onCloseDialog();
          }}
        />
      </Dialog>
    </Flex>
  );
}

export default memo(Theme.wrap(AdminTrustedDevice));
