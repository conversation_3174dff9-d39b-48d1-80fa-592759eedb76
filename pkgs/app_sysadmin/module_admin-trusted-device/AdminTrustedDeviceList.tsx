import React, { useState, useRef } from 'react';
import {
  Button,
  Dialog,
  Classes,
  InputGroup,
  Intent,
  OverlayToaster,
  HTMLTable,
} from '@tutum/design-system/components/Core';
import {
  alertError,
  alertSuccessfully,
  Flex,
  LoadingState,
} from '@tutum/design-system/components';
import { ISysadminTheme } from '@tutum/sysadmin/theme';
import { IDevice } from './AdminTrustedDevice.service';
import {
  AdminDeviceDto,
  useMutationChangeAdminDeviceName,
  useMutationCreateAdminUser,
} from '@tutum/hermes/bff/legacy/app_sysadmin';

export interface IAdminTrustedDeviceListProps {
  className?: string;
  theme?: ISysadminTheme;
  devices: AdminDeviceDto[];
  loading?: boolean;
  reloadDevice?: () => void;
}

export default function AdminTrustedDeviceList(
  props: IAdminTrustedDeviceListProps
) {
  const { devices = [] } = props;
  const [editDevice, setEditDevice] = useState<IDevice | undefined>(undefined);
  const [showEdit, setShowEdit] = useState(false);
  const { isPending: isLoadingChangeDeviceName, mutate: changeDeviceNameFunc } =
    useMutationChangeAdminDeviceName({
      onSuccess: () => {
        props?.reloadDevice();
        alertSuccessfully('Update successfully');
      },
      onError: (err) => {
        alertError(err.response?.data?.message || 'Update failed');
      },
    });

  const onEditDevice = (device: IDevice) => {
    setEditDevice(device);
    setShowEdit(true);
  };

  const onTypeDeviceName = (e) => {
    setEditDevice({
      ...editDevice,
      deviceName: e.target.value,
    });
  };

  const onSaveEditDevice = () => {
    if (editDevice) {
      setShowEdit(false);
      changeDeviceNameFunc({
        deviceId: editDevice.id,
        deviceName: editDevice.deviceName,
      });
    }
  };

  const { isPending: isCreateAdminUserLoading, mutate: createAdminUserFunc } =
    useMutationCreateAdminUser({
      onError: (err) => {
        alertError(
          err.response?.data?.serverError || 'Create admin user failed'
        );
      },
      onSuccess: () => {
        alertSuccessfully('Create admin user successfully');
        props?.reloadDevice();
      },
    });

  if (props.loading) {
    return <LoadingState />;
  }

  return (
    <Flex auto column {...props}>
      <Flex className="table-container">
        <Flex className="table-view">
          <HTMLTable>
            <thead className="header-title">
              <tr>
                <th>ID</th>
                <th>Practice Name</th>
                <th>Admin Login Account</th>
                <th>Created date</th>
                <th />
              </tr>
            </thead>
            <tbody>
              {devices &&
                devices.map((device, index) => {
                  return (
                    <tr key={device.careProviderId}>
                      <td className="name-column">{device.careProviderId}</td>
                      <td>{device.deviceName}</td>
                      <td>{device.accountLoginName}</td>
                      <td>
                        {new Date(device.createTime).toLocaleDateString('vi')}
                      </td>
                      <td className="action-column">
                        <Button
                          data-test-id={`create-admin-user-change-name-${index}`}
                          outlined
                          intent={Intent.NONE}
                          onClick={() => onEditDevice(device)}
                        >
                          Change name
                        </Button>
                        &nbsp;&nbsp;
                        <Button
                          data-test-id={`create-admin-user-${index}`}
                          outlined
                          intent={Intent.NONE}
                          loading={isCreateAdminUserLoading}
                          disabled={Boolean(device.accountLoginName)}
                          onClick={() =>
                            createAdminUserFunc({
                              careProviderId: device.careProviderId,
                            })
                          }
                        >
                          Create Admin User
                        </Button>
                      </td>
                    </tr>
                  );
                })}
            </tbody>
          </HTMLTable>
        </Flex>
      </Flex>
      <Dialog
        onClose={() => setShowEdit(false)}
        title="Edit device's name"
        isOpen={showEdit}
        canOutsideClickClose={false}
      >
        <div className={Classes.DIALOG_BODY}>
          <label>Device's name</label>
          <InputGroup
            value={editDevice ? editDevice.deviceName : ''}
            className={`bp5-large `} //${'bp5-intent-danger'}
            placeholder="Please input the device name"
            onChange={onTypeDeviceName}
          />
        </div>
        <div className={Classes.DIALOG_FOOTER}>
          <Button
            large
            fill
            intent={Intent.PRIMARY}
            loading={isLoadingChangeDeviceName}
            disabled={
              !editDevice ||
              !editDevice.deviceName ||
              editDevice.deviceName.length === 0
            }
            onClick={onSaveEditDevice}
          >
            Save
          </Button>
        </div>
      </Dialog>
    </Flex>
  );
}
