import {
  createBFFPairingWs,
  MicronautsWebSocketClient,
} from '@tutum/infrastructure/websocket';
import { SysadminApi } from '@tutum/infrastructure/resource/SysadminAppResource';

let _ws: MicronautsWebSocketClient;

export interface IDevice {
  id: string;
  careProviderId: string;
  createTime: number;
  deviceName: string;
}

function getPhraseCode(): string {
  return Math.random().toString(36).substr(2, 6);
}

function addDevice(
  identity = '',
  careProviderName = '',
  callback?: (data: IDevice[], error?: Error) => void
) {
  _ws = createBFFPairingWs(identity);

  _ws.onmessage = (evt: MessageEvent) => {
    const { data } = evt;
    try {
      const parsedDate = JSON.parse(data);
      const { type } = parsedDate;

      if (type === 'ADMIN_PAIRED_SUCCESS') {
        return getAllDevices().then(({ data }) => {
          callback?.(data.devices);
          closeWS();
        });
      }
    } catch (e) {
      closeWS();
      callback?.([], e);
    }
  };

  _ws.onopen = () => {
    console.log(`connected to ${identity} channel`);
    SysadminApi.addOrganization({ careProviderName })
      .catch((e) => {
        callback?.([], e);
        closeWS();
      })
      .catch((ex) => callback?.([], ex));
  };
}

function getAllDevices() {
  return SysadminApi.getAllDevice().then(({ data }) => ({ data }));
}

function closeWS() {
  if (_ws) {
    _ws.close();
  }
}

export default {
  getPhraseCode,
  addDevice,
  getAllDevices,
  closeWS,
};
