
import { IBase<PERSON>rovider } from '@tutum/design-system/form/shared/type';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import type { UseFormReturn } from 'react-hook-form';

const BaseProvider = <T,>({
  children,
  submitHandler,
  useFormProps,
  useFormReturn,
  className,
}: IBaseProvider<T> & {
  className?: string;
}) => {
  let methods: UseFormReturn<T, any, undefined>;
  if (useFormReturn) {
    methods = useFormReturn;
  } else if (useFormProps) {
    methods = useForm<T>(useFormProps);
  }

  if (!methods!) {
    return undefined;
  }

  const { handleSubmit } = methods;

  return (
    <FormProvider {...methods}>
      <form
        className={className}
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleSubmit(submitHandler)(e);
        }}
      >
        {children}
      </form>
    </FormProvider>
  );
};

export default BaseProvider;
