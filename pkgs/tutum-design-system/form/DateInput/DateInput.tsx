import { DateInput, DateInputProps } from '@blueprintjs/datetime';
import I18n from '@tutum/infrastructure/i18n';
import React, { KeyboardEvent, useRef } from 'react';

// Include the locale utils designed for moment
import MomentLocaleUtils from 'react-day-picker/moment';

// Make sure moment.js has the required locale data
import { FormGroup, Intent } from '@tutum/design-system/components';
import type { IBaseFormInput } from '@tutum/design-system/form/shared/common';
import { isNull, isNumber } from '@tutum/design-system/infrastructure/utils';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/mvz/constant/dateTime';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import get from 'lodash/get';
import moment from 'moment';
import 'moment/locale/de';
import { Controller, useFormContext } from 'react-hook-form';

const REGEX_DATE_TIME_IN_FORM = /(^(\+|-)[0-9]*[JMT]|)/;
enum TYPE_FORMAT_DATE_TIME {
  J = 'years', // Years, ger: Jahre
  M = 'months', // Months, ger: monate
  T = 'days', // Days, ger: Tage
}
enum PREFIX_FORMAT_DATE_TIME {
  ADD = '+',
  SUBTRACT = '-',
}

export interface SLDateInputProps
  extends Omit<DateInputProps, 'formatDate' | 'parseDate'>,
  IBaseFormInput {
  usePortal?: boolean;
  formatDate?: (date: Date, localeCode?: string) => string;
  parseDate?: (str: string, localeCode?: string) => Date | false | null;
}

export default (props: SLDateInputProps) => {
  const { t } = I18n.useTranslation({
    namespace: 'Common',
    nestedTrans: 'DateTimePicker',
  });
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales.Form>({
    namespace: 'Common',
    nestedTrans: 'Select',
  });
  const { name, label, required, validate } = props;
  const { control, setValue } = useFormContext();

  const translationKey = props.placeholder?.replace(/\./g, '_')!;
  const translatedPlaceholder =
    translationKey && t(translationKey) !== `DateTimePicker.${translationKey}`
      ? t(translationKey)
      : props.placeholder;

  const dateInputRef = useRef<any>(null);
  const locale = props.locale ?? 'de';

  const keydownHandler = (evt: KeyboardEvent) => {
    const lowercaseKey = `${evt.key}`.toLowerCase();
    switch (lowercaseKey) {
      case 'tab':
        if (dateInputRef.current) {
          dateInputRef.current?.setOpenState?.(false);
        }
        break;
      case 'enter':
        evt.preventDefault();
        const date = dateInputRef.current?.targetRef?.current?.value;
        const validFormat =
          REGEX_DATE_TIME_IN_FORM.test(date) || new RegExp('/').test(date);
        if (dateInputRef.current?.state?.isOpen) {
          evt.stopPropagation();
        }

        if (validFormat.valueOf()) {
          const getNewDate = () => {
            if (new RegExp('/').test(date)) {
              return datetimeUtil.dateToMoment();
            }

            const type: TYPE_FORMAT_DATE_TIME = date[
              date.length - 1
            ] as TYPE_FORMAT_DATE_TIME;
            const prefix = date[0];
            const quantity = Number(
              date.match(/[0-9]*/g)?.filter((c) => Boolean(c))[0]
            );

            const flag = type in TYPE_FORMAT_DATE_TIME && quantity;
            if (flag && prefix === PREFIX_FORMAT_DATE_TIME.ADD) {
              return datetimeUtil.add(quantity, TYPE_FORMAT_DATE_TIME[type]);
            }

            if (flag && prefix === PREFIX_FORMAT_DATE_TIME.SUBTRACT) {
              return datetimeUtil.subtract(
                quantity,
                TYPE_FORMAT_DATE_TIME[type]
              );
            }
            return null;
          };

          const newDate = getNewDate();
          if (newDate && dateInputRef.current) {
            dateInputRef.current?.setState?.(
              {
                ...dateInputRef.current?.state,
                valueString: newDate.format(YEAR_MONTH_DAY_FORMAT),
                value: +newDate,
                isOpen: false,
              },
              () => {
                props.onChange?.(`${+newDate}`, false);

                setTimeout(() => {
                  dateInputRef.current?.targetRef?.current?.blur?.();
                });
              }
            );
          }
        }
        break;
      default:
        break;
    }
  };

  const getInitMonth = () => {
    return datetimeUtil.date();
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: {
          value: required,
          message: tCommon('required', { name }),
        },
        validate,
      }}
      // defaultValue={datetimeUtil.now()}
      render={({ field: { value }, formState: { errors } }) => {
        const maxDate =
          props.maxDate ??
          (moment(value)?.isValid()
            ? moment(value).add(1, 'year').toDate()
            : undefined);
        const minDate =
          props.minDate ??
          (moment(value)?.isValid()
            ? moment(value).add(-1, 'year').toDate()
            : undefined);
        return (
          <FormGroup
            label={label}
            helperText={get(errors, name)?.message?.toString()}
            intent={Boolean(get(errors, name)) ? Intent.DANGER : Intent.NONE}
            className={props.className}
            labelInfo={label && required ? '*' : ''}
          >
            <DateInput
              {...props}
              clearButtonText={t('clear')}
              todayButtonText={t('today')}
              outOfRangeMessage={t('outOfRange')}
              invalidDateMessage={t('invalidDate')}
              popoverRef={dateInputRef}
              canClearSelection={false}
              closeOnSelection
              placeholder={translatedPlaceholder}
              value={value ? new Date(value).toISOString() : ''}
              onChange={(selectedDate, isUserChange) => {
                if (!isUserChange) return;
                const date = new Date(selectedDate);
                setValue(
                  name,
                  isNumber(value) || isNull(value) ? date.getTime() : date,
                  { shouldDirty: true }
                );
              }}
              inputProps={{
                onKeyDown: keydownHandler,
                ['data-test-id']: props['data-test-id'],
                name: name,
                ...props.inputProps,
              }}
              popoverProps={{
                usePortal: props.usePortal ?? true,
              }}
              locale={locale}
              localeUtils={props.localeUtils ?? MomentLocaleUtils}
              initialMonth={
                props.initialMonth || (!props.value ? getInitMonth() : null)
              }
              formatDate={(date) =>
                date ? moment(date).format(DATE_FORMAT) : ''
              }
              parseDate={(str) =>
                datetimeUtil.strToDate(str, DATE_FORMAT, locale)
              }
              minDate={minDate}
              maxDate={maxDate}
            />
          </FormGroup>
        );
      }}
    />
  );
};
