import { FormGroup, Intent } from '@tutum/design-system/components';
import type {
  GroupType,
  OptionType,
} from '@tutum/design-system/form/shared/AsyncSelect';
import {
  DropdownIndicator,
  getStyles,
} from '@tutum/design-system/form/shared/ReactSelect';
import { COLOR } from '@tutum/design-system/themes/styles';
import I18n from '@tutum/infrastructure/i18n';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import React, { useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import ReactAsyncSelect, { AsyncProps } from 'react-select/async';
import { IBaseFormInput } from '../shared/common';
import get from 'lodash/get';

export interface IAsyncSelect<T>
  extends AsyncProps<OptionType<T>, false, GroupType<T>>,
  IBaseFormInput {
  className?: string;
  name: string;
}

const AsyncSelect = <T extends object>(props: IAsyncSelect<T>) => {
  const { control, setValue } = useFormContext(),
    { styles, components, name, label, required, ...restProps } = props,
    { t } = I18n.useTranslation<keyof typeof CommonLocales.Form>({
      namespace: 'Common',
      nestedTrans: 'Select',
    });

  const customStyles = useMemo(
    () => ({
      ...getStyles(styles as any),
      control: (base: any, props: any) => ({
        ...base,
        ...styles?.control?.(base, props),
        borderColor: COLOR.BACKGROUND_TERTIARY_DIM,
        '&:hover': {
          borderColor: COLOR.BACKGROUND_SELECTED_STRONG,
        },
      }),
      option: (base: any, props: any) => ({
        ...base,
        ...styles?.option?.(base, props),
        padding: 0,
        li: {
          listStyle: 'none',
        },
      }),
      placeholder: (base: any) => ({
        ...base,
        color: COLOR.TEXT_PLACEHOLDER,
      }),
    }),
    [styles]
  );

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        required: {
          value: !!required,
          message: t('required', { name }),
        },
      }}
      render={({ field: { value }, formState: { errors } }) => (
        <FormGroup
          label={label}
          helperText={get(errors, name)?.message?.toString()}
          intent={Boolean(get(errors, name)) ? Intent.DANGER : Intent.NONE}
          labelInfo={label && required ? '*' : ''}
        >
          <ReactAsyncSelect<OptionType<T>>
            menuPlacement="auto"
            isSearchable
            isClearable
            cacheOptions
            styles={customStyles}
            components={{
              DropdownIndicator: DropdownIndicator as any,
              IndicatorSeparator: null as any,
              ClearIndicator: null as any,
              ...components,
            }}
            {...(restProps as any)}
            value={value}
            onChange={(item, metaData) => {
              restProps.onChange?.(item, metaData);
              setValue(name, item?.value);
            }}
          />
        </FormGroup>
      )}
    />
  );
};

export default AsyncSelect;
