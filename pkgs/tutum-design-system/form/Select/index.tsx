
import React from 'react';
import ReactSelect, {
  GroupBase,
  Props,
  components,
  InputProps,
} from 'react-select';
import { useTheme } from '@tutum/design-system/themes';

import { FormGroup } from '@blueprintjs/core';
import { Intent } from '@tutum/design-system/components';
import {
  DropdownIndicator,
  getStyles,
} from '@tutum/design-system/form/shared/ReactSelect';
import I18n from '@tutum/infrastructure/i18n';
import get from 'lodash/get';
import { Controller, useFormContext } from 'react-hook-form';
import ISelect from 'react-select/dist/declarations/src/Select';
import { IBaseFormInput } from '../shared/common';

export interface IMenuItem<T = number | string> {
  value: T;
  label: string;
  id?: string | number;
  isDisabled?: boolean;
  data?: any;
}

export interface IServiceCodeItem {
  value: string;
  label: string;
  id?: string;
  isDisabled?: boolean;
}

export interface IMenuItemWithData<T> {
  id: number | string;
  value: number | string;
  label: string;
  data?: T;
}

export interface ISelectProps<T extends IMenuItem>
  extends Props,
  IBaseFormInput {
  selectedValue?: number | string | Array<string | number>;
  options: T[];
  noResults?: React.ReactNode;
  className?: string;
  onOptionSelect?: (item: T) => void;
  name: string;
}

export interface ISelectCreatableProps<T extends IMenuItem> extends Props {
  options: T[];
  noResults?: React.ReactNode;
  className?: string;
  placeholder?: string;
  isOpenMenu?: boolean;
}

const handleValue = (
  options: IMenuItem[],
  selectedValue?: number | string | Array<string | number>
): IMenuItem | IMenuItem[] => {
  if (Array.isArray(selectedValue)) {
    const listItem = selectedValue?.map((value) =>
      options?.find((item) => item?.value === value)
    );
    return listItem?.length ? listItem.filter(Boolean) : null;
  }
  const itemMenu = options?.find((item) => item?.value === selectedValue);
  if (itemMenu) return itemMenu;
  return selectedValue
    ? { value: selectedValue, label: `${selectedValue}` }
    : null;
};

const Input = (props: InputProps) => {
  return <components.Input
    {...props}
    data-test-id={props.selectProps.customProps?.['data-test-id']}
  />
};

const Select = React.forwardRef(
  <T extends IMenuItem>(
    {
      selectedValue,
      options,
      noResults,
      placeholder,
      onOptionSelect,
      name,
      label,
      required,
      validate,
      menuPortalTarget = document.body,
      ...props
    }: ISelectProps<T>,
    ref: React.Ref<ISelect<unknown, boolean, GroupBase<unknown>>>
  ) => {
    const { t } = I18n.useTranslation<any>({
      namespace: 'Common',
      nestedTrans: 'Select',
    });

    const {
      control,
      setValue,
      formState: { errors },
      trigger,
    } = useFormContext();



    const inputComponentWithTestId = props['data-test-id']
      ? { Input: Input }
      : {};


    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { value } }) => (
          <FormGroup
            label={label}
            helperText={get(errors, name)?.message?.toString()}
            intent={Boolean(get(errors, name)) ? Intent.DANGER : Intent.NONE}
            className={props.className}
            labelInfo={label && required ? '*' : ''}
          >
            <ReactSelect
              {...props}
              value={handleValue(options, value)}
              classNamePrefix={props.classNamePrefix ?? 'react-select'}
              options={options}
              customProps={{ 'data-test-id': props['data-test-id'] }}
              components={{
                DropdownIndicator,
                IndicatorSeparator: null,
                ClearIndicator: null,
                ...inputComponentWithTestId,
                ...props.components,
              }}
              menuPortalTarget={menuPortalTarget}
              noOptionsMessage={() => noResults}
              onChange={(item) => {
                trigger();
                onOptionSelect?.(item as T);
                if (!item) {
                  setValue(name, null, { shouldDirty: true });
                  return;
                }
                if (props.isMulti) {
                  setValue(
                    name,
                    (item as T[])?.map((i) => i?.value),
                    { shouldDirty: true }
                  );
                  return;
                }
                setValue(name, (item as T)?.value, { shouldDirty: true });
              }}
              isClearable={props.isClearable || true}
              backspaceRemovesValue={props.backspaceRemovesValue || true}
              styles={getStyles(props?.styles)}
              menuPlacement="auto"
            />
          </FormGroup>
        )}
      />
    );
  }
);

export default Select;
