import React, {
  useMemo,
  useRef,
  useCallback,
  useState,
  useEffect,
} from 'react';
import type { SelectInstance } from 'react-select';

import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import type {
  EncounterGoaService,
  EncounterServiceTimeline,
} from '@tutum/hermes/bff/repo_encounter';

import {
  InputSuggestion,
  Flex,
  coreComponents,
  alertError,
} from '@tutum/design-system/components';
import { isNotEmpty } from '@tutum/design-system/infrastructure/utils';
import {
  DEFAULT_SELECT_COMPONENT_CONFIG,
  DEFAULT_COMPOSER_SELECT_STYLE_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
// NEW IMPORTS
import AdditionalInfoPlainEditor from '../service-node-lexical';
import AdditionalInfoSelectionPlugin from '../service-node-lexical/plugins/additional-info-selection-plugin/additional-info-selection-plugin';
import AdditionalInfoKeyboardNavigationPlugin from '../service-node-lexical/plugins/additional-info-keyboard-navigation-plugin';
import { OmimGChainTypeaheadPlugin } from '../service-node-lexical/plugins/omim-g-chain-typeahead-plugin';
import AdditionalInfoKeyEnterToSubmitPlugin from '../service-node-lexical/plugins/additional-info-enter-submit-plugin';
import ServicePopoverMenuItem from './GoaServicePopoverMenuItem';
import { PointValueModel } from '@tutum/hermes/bff/legacy/point_value_common';
import { FactorQuantityBlock } from './factor-quantity-block';
import { useQueryGetGoaFactorValue } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { useQueryGetGoaCatalogByGoaNumber } from '@tutum/hermes/bff/legacy/app_mvz_catalog_goa';
import { Spinner } from '@tutum/design-system/components/Core';

export interface IServiceNodeProps {
  // OPTIONAL
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  additionalInfoEditorplaceholder?: string;
  noResultsComponent?: JSX.Element;
  rightElement?: JSX.Element;
  encounterDate?: number; // TODO: refactor later
  hideFactor?: boolean;

  // MAIN PROPS
  data: EncounterGoaService;
  additionalFields: AdditionalInfoField[];
  pointValue?: PointValueModel;
  // METHODS
  onServiceSelect: (item: IContractData) => Promise<EncounterGoaService>;
  searchService: (
    query: string,
    setDatasource: (result: IContractData[]) => void
  ) => (Promise<void>) | undefined;
  onChange: (data: EncounterGoaService) => Promise<void>;
  onSubmit(payload: EncounterGoaService): Promise<void>;
  onClear: () => void;
  id: string;
  scheinId: string;
}

const STYLE_CONFIG = DEFAULT_COMPOSER_SELECT_STYLE_CONFIG();

const DEFAULT_FACTOR_VALUE = 0;
const DEFAULT_QUANTITY_VALUE = 1;
const DEFAULT_PRICE_VALUE = 1;

const ServiceNode: React.FC<React.PropsWithChildren<IServiceNodeProps>> = (
  props
) => {
  const {
    className,
    placeholder,
    hideFactor,
    data,
    additionalFields,
    disabled,
    rightElement,
    onChange,
    onSubmit,
    onClear,
    searchService,
    onServiceSelect,
    noResultsComponent,
    encounterDate, // TODO: refactor later, move out
    pointValue,
    scheinId,
    id,
  } = props;
  const inputSuggestionEl = useRef<SelectInstance<any, boolean> | null>(null);
  const [factor, setFactor] = useState<number>(
    data.factor || DEFAULT_FACTOR_VALUE
  );
  const [quantity, setQuantity] = useState<number>(
    data.quantity || DEFAULT_QUANTITY_VALUE
  );
  const [price, setPrice] = useState<number>(DEFAULT_PRICE_VALUE);
  const [isShowFactor, setShowFactor] = useState<boolean>(false);
  const isEnabledGetFactor = !!scheinId && !!data.code;
  const {
    isFetching: isFetchingFactor,
    data: dataFactor,
    isSuccess: isSuccessGetFactor,
  } = useQueryGetGoaFactorValue(
    {
      scheinId: scheinId,
      goaNumber: data.code,
    },
    {
      enabled: isEnabledGetFactor,
    }
  );
  const {
    isFetching: isFetchingGetCatalog,
    isError: isErrorGetCatalog,
    error: errorGetCatalog,
    data: dataCatalog,
  } = useQueryGetGoaCatalogByGoaNumber(
    {
      goaNumber: data.code,
    },
    {
      enabled: !!data?.factor && !!data.code,
    }
  );

  useEffect(() => {
    if (isErrorGetCatalog) {
      alertError(errorGetCatalog.message);
      return;
    } else if (dataCatalog) {
      setPrice(dataCatalog?.goa.price);
    }
  }, [isErrorGetCatalog, errorGetCatalog, dataCatalog]);

  useEffect(() => {
    if (data?.factor) {
      setFactor(data.factor);
      setQuantity(data.quantity);
    }
  }, [data?.factor]);

  const canSubmit = useMemo(() => {
    return (
      !isFetchingFactor && !isFetchingGetCatalog && !!quantity && factor >= 1
    );
  }, [isFetchingFactor, isFetchingGetCatalog, quantity, factor]);

  const setUpdatedService = (updatedService: EncounterGoaService) => {
    const _service = { ...updatedService };
    if (!_service.command) {
      _service.command = data.command || 'L';
    }

    onChange(_service);
    return _service;
  };

  const handleSelectService = async (item: IContractData) => {
    const inputData = `(${item.code}) ${item.description}`;
    const result = await onServiceSelect(item);

    if (result) setPrice(result.price || DEFAULT_PRICE_VALUE);
    setUpdatedService({
      ...result,
      freeText: inputData,
      factor: 0,
      quantity: DEFAULT_QUANTITY_VALUE,
    });
    setQuantity(DEFAULT_QUANTITY_VALUE);
    setShowFactor(true);
  };

  const defaultEditorState = useMemo(
    () => data?.additionalInfosRaw,
    [data?.additionalInfosRaw]
  );

  const isChangeDefaultFactor = useMemo(() => {
    return dataFactor?.value !== factor || quantity !== 1;
  }, [dataFactor?.value, factor, quantity]);

  const onComposerSubmit = useCallback(
    async (
      payload: Pick<
        EncounterGoaService,
        'additionalInfos' | 'additionalInfosRaw'
      >
    ) => {
      if (!canSubmit) {
        return;
      }
      const newServiceData = setUpdatedService({
        ...data,
        factor,
        quantity,
        price,
        additionalInfos: payload.additionalInfos,
        additionalInfosRaw: payload.additionalInfosRaw,
        isChangeDefault: isChangeDefaultFactor,
      });
      await onSubmit(newServiceData);
      setShowFactor(false);
    },
    [data, onSubmit, onChange, factor, quantity, price]
  );

  const onSubmitIDataFactor = async () => {
    const newData = setUpdatedService({
      ...data,
      factor,
      quantity,
      price,
      isChangeDefault: isChangeDefaultFactor,
    });
    await onSubmit(newData);
  };

  useEffect(() => {
    if (!isEnabledGetFactor || isFetchingFactor || data?.factor) {
      return;
    }

    const defaultFactor =
      (isSuccessGetFactor && dataFactor?.value) || DEFAULT_FACTOR_VALUE;
    setFactor(defaultFactor);
    setUpdatedService({
      ...data,
      factor: defaultFactor,
    });
  }, [
    dataFactor?.value,
    JSON.stringify(data),
    isSuccessGetFactor,
    isEnabledGetFactor,
    isFetchingFactor,
  ]);

  const showServiceOptions =
    isNotEmpty(data?.freeText) && isNotEmpty(data?.code);

  return (
    <Flex className={className} column gap={0}>
      <Flex align="center" auto>
        <InputSuggestion
          id={id}
          keyCode="description"
          autoFocus={!disabled}
          placeholder={placeholder}
          styles={STYLE_CONFIG}
          openMenuOnClick={false}
          components={{
            ...DEFAULT_SELECT_COMPONENT_CONFIG,
            Option: (props) => {
              const {
                data,
                selectProps: { inputValue },
              } = props;

              return (
                <coreComponents.Option {...props}>
                  <ServicePopoverMenuItem
                    item={data}
                    query={inputValue}
                  />
                </coreComponents.Option>
              );
            },
          }}
          defaultInputValue={data?.freeText}
          loadOptions={
            ((query: string, setDatasource: any) => {
              searchService(query, (_result) => {
                setDatasource(_result);
              });
            }) as any
          }
          onChange={handleSelectService}
          getOptionLabel={(opt) => `${opt.description} ${opt.code}`}
          noOptionsMessage={() => noResultsComponent}
          ref={inputSuggestionEl}
          onKeyDown={async (e) => {
            if (
              e.key === 'Backspace' &&
              !inputSuggestionEl?.current?.inputRef?.value
            ) {
              onClear();
            } else if (
              e.key === 'Enter' &&
              inputSuggestionEl?.current?.inputRef?.value
            ) {
              if (canSubmit) {
                onSubmit({
                  ...data,
                  factor,
                  quantity,
                  price,
                  isChangeDefault: isChangeDefaultFactor,
                });
              }
            }
          }}
        />
        {/* top right element here */}
        {rightElement}
      </Flex>
      {(isShowFactor || showServiceOptions) && !hideFactor && (
        <FactorQuantityBlock
          isError={!canSubmit}
          onSubmit={onSubmitIDataFactor}
          factor={factor}
          quantity={quantity}
          setFactor={setFactor}
          setQuantity={setQuantity}
          price={price}
          isLoadingData={isFetchingFactor || isFetchingGetCatalog}
        />
      )}

      {!data?.factor && (isFetchingFactor || isFetchingGetCatalog) && (
        <Spinner className="sl-loading" intent="primary" size={20} />
      )}

      {showServiceOptions && (
        <AdditionalInfoPlainEditor
          // placeholder={additionalInfoEditorplaceholder}
          defaultEditorState={defaultEditorState} // NOTE: keep this local
          onChange={(addInfoPayload) =>
            setUpdatedService({ ...data, ...addInfoPayload })
          }
        >
          {/* OPTIONAL PLUGINS HIGHER THAN DEFAULTs*/}
          {props.children}
          {/* Plugin for handle OMIM-G chain */}
          <OmimGChainTypeaheadPlugin />
          {/* Plugin for handle additional info keyboard navigation */}
          <AdditionalInfoKeyboardNavigationPlugin />
          {/* Plugin for handle additional info logics - all the blocks + encounterDate */}
          {/* Plugin for selecting additional info nodes */}

          <AdditionalInfoSelectionPlugin
            encounterDate={encounterDate}
            encounterService={
              { ...data, isPreParticipate: false } as EncounterServiceTimeline
            }
            additionalFields={additionalFields}
          />

          {/* Plugin for handle additional info avoid line break then */}
          {/* AND Plugin for handle additional info submit */}
          <AdditionalInfoKeyEnterToSubmitPlugin onSubmit={onComposerSubmit} />
        </AdditionalInfoPlainEditor>
      )}
    </Flex>
  );
};

export default React.memo(ServiceNode);
