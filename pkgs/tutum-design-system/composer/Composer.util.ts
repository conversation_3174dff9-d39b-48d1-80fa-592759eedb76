import { IComposerRow } from './Composer.type';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { ScheinStatus, ScheinItem } from '@tutum/hermes/bff/schein_common';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';

const statusBlockSchein = [
  ScheinStatus.ScheinStatus_Billed,
  ScheinStatus.ScheinStatus_Canceled,
  ScheinStatus.ScheinStatus_Printed,
];

function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function createTextMatcher(
  query: string,
  title: string
): Array<{
  value: string;
  match: boolean;
}> {
  const keyword = query.trim().toLowerCase();
  const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'ig');
  const strArr = title.split(regex);

  return strArr.map((item) => {
    if (item.trim().toLowerCase() === keyword) {
      return { value: item, match: true };
    }

    return { value: item, match: false };
  });
}

function focusOnNextRow(
  event: React.KeyboardEvent<HTMLSpanElement>,
  composerEl: HTMLDivElement,
  selector: string
) {
  let element: any = event.currentTarget;

  while (!element.isEqualNode(composerEl)) {
    if (element === document) {
      break;
    }
    const sibling = element.nextElementSibling;
    if (!sibling) {
      element = element.parentNode;
      continue;
    }
    const myRow = sibling.querySelector(selector);
    if (!myRow) {
      element = element.parentNode;
      continue;
    }

    myRow.focus();
    setCaretPositionAtEnd(myRow);
    break;
  }
}

function focusOnPrevRow(
  event: React.KeyboardEvent<HTMLSpanElement>,
  composerEl: HTMLDivElement,
  selector: string
) {
  let element: any = event.currentTarget;

  while (!element.isEqualNode(composerEl)) {
    if (element === document) {
      break;
    }
    const sibling = element.previousElementSibling;
    if (!sibling) {
      element = element.parentNode;
      continue;
    }
    const myRows = sibling.querySelectorAll(selector);
    if (myRows.length === 0) {
      element = element.parentNode;
      continue;
    }
    const myRow = myRows[myRows.length - 1];

    myRow.focus();
    setCaretPositionAtEnd(myRow);
    break;
  }
}

// Follow by timestamp in microseconds with 16 digits.
// in case of realtime, sort order may be duplicated, then addedDate should be in place
function generateSortOrder(currentIndex: number, rows?: IComposerRow[]) {
  //first or last row
  if (!rows || rows.length === 0 || !rows[currentIndex + 1]) {
    // timestamp in seconds (10 digits) + 6 zero padding at end
    return Number(`${DatetimeUtil.nowInUnix()}`.padEnd(16, `0`));
  }
  // row in between
  const min = rows[currentIndex].sortOrder;
  const max = rows[currentIndex + 1].sortOrder;
  return Math.random() * (max - min) + min;
}

function getCaretPosition(element: any) {
  // const selection = global['getSelection']();
  const selection = window?.getSelection();
  if (!selection || !selection.rangeCount) {
    return 0;
  }

  const range = selection?.getRangeAt(0);
  const preCaretRange = range?.cloneRange();
  preCaretRange?.selectNodeContents(element);
  preCaretRange?.setEnd(range.endContainer, range.endOffset);
  return preCaretRange?.toString()?.length;
}

function setCaretPositionAtEnd(element: any) {
  const range = document.createRange();
  range.selectNodeContents(element);
  range.collapse(false);
  const selection = global['getSelection']();
  if (selection) {
    selection.removeAllRanges();
    selection.addRange(range);
  }
}

function setContentEditbleCaretAtPosition(element: any, position: number) {
  const textNode = element.firstChild;
  if (position === 0 || textNode === null) {
    return;
  }
  if (textNode.length < position) {
    return;
  }
  const selection = global['getSelection']();
  if (selection) {
    selection.collapse(textNode, position);
  }
}
function compareDate(firstParam?: number, secondParam?: number): boolean {
  // false => date1 after date 2
  // true => date 1 before date 2
  const date1 = firstParam ? new Date(firstParam) : new Date();
  const date2 = secondParam ? new Date(secondParam) : new Date();
  if (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  ) {
    return true;
  } else if (date1 > date2) {
    return false;
  } else {
    // date 1 before date 2
    return true;
  }
}

function getTypeOfSchein(scheinItem: ScheinItem) {
  if (checkIsPrivateSchein(scheinItem)) {
    return { isPrivate: true };
  }
  return {
    isPrivate: false,
  };
}

function getValidQuarterAndYearInSchein(scheinItem: ScheinItem) {
  if (checkIsPrivateSchein(scheinItem)) {
    const quarter = DatetimeUtil.getQuarter(scheinItem.issueDate);
    const year = DatetimeUtil.getYear(scheinItem.issueDate);
    return { year, quarter };
  }
  return {
    year: scheinItem.g4101Year,
    quarter: scheinItem.g4101Quarter,
  };
}

function filterScheinByDate(scheinList: ScheinItem[], date: Date) {
  const quarterCurrent = Math.ceil((date.getMonth() + 1) / 3);

  return scheinList.filter((item) => {
    const { isPrivate } = getTypeOfSchein(item);
    if (isPrivate) {
      const isValidItemPrivate = compareDate(item.issueDate, date.getTime());
      if (
        isValidItemPrivate &&
        (item.scheinStatus === undefined ? true : !statusBlockSchein.includes(item.scheinStatus))
      ) {
        return item;
      }
    }
    if (
      item.g4101Quarter == quarterCurrent &&
      item.g4101Year == date.getFullYear() &&
      !item.markedAsBilled &&
      item?.g4110 &&
      DatetimeUtil.utc(item?.g4110) > DatetimeUtil.utc(date)
    ) {
      return item;
    }
  });
}

const parseCustomFontsize = (level?: number, isAdditionalText = false) => {
  let customTextSize: number;
  switch (level) {
    case 1:
      customTextSize = 13;
      if (isAdditionalText) customTextSize = 11;
      break;
    case 2:
      customTextSize = 16;
      if (isAdditionalText) customTextSize = 13;
      break;
    case 3:
      customTextSize = 20;
      if (isAdditionalText) customTextSize = 16;
      break;
    default:
      customTextSize = 12;
      break;
  }
  return customTextSize + 'px';
};

function isInDateRange(startDateInMs: number, endDateInMs: number, dateInMs: number) {
  const startDate = startDateInMs ? new Date(startDateInMs) : new Date()
  const endDate = endDateInMs ? new Date(endDateInMs) : new Date()
  const date = dateInMs ? new Date(dateInMs) : new Date()
  return date >= startDate && date <= endDate;
}

export default {
  generateSortOrder,
  focusOnNextRow,
  createTextMatcher,
  focusOnPrevRow,
  getCaretPosition,
  setCaretPositionAtEnd,
  setContentEditbleCaretAtPosition,
  filterScheinByDate,
  parseCustomFontsize,
  getTypeOfSchein,
  compareDate,
  getValidQuarterAndYearInSchein,
  isInDateRange,
};
