import React, { useState, useEffect, useRef } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { IMenuItem } from '@tutum/design-system/components';
import type { IAdditionalInfo } from '../../AdditionalInfoPlugin.type';
import AutoExpandInput from '../../../../components/AutoExpandInput';
import AdditionalInfoSuggest from '../../../../lexical/components/custom-select';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

interface IFlexibleInputProps extends Partial<IAdditionalInfo> {
  autoFocus?: boolean;
  inputValue: string;
  onInputChange: (newValue: string) => void;
  innerInputRef?: (inputId: string, input: HTMLInputElement) => void;
  onCustomKeyDown?: (lowercaseKey: string, event: KeyboardEvent) => void;
  onFocus?: React.FocusEventHandler<HTMLInputElement>;
}

const FlexibleInput: React.FC<IFlexibleInputProps> = (props) => {
  const [data, setData] = useState<IMenuItem[]>([]);
  const { t } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'DropDownItems',
  });

  const _inputId = useRef(`${datetimeUtil.now()}`);

  useEffect(() => {
    if (!props.dataSource) return;

    setData(
      props.dataSourceValues?.map((item) => ({
        value: item,
        label: t(`${props.dataSource}.${item}`),
      })) || []
    );
  }, [props.dataSourceValues]);

  switch (props.inputType) {
    case 'DropDownList':
      return (
        <AdditionalInfoSuggest
          items={data}
          selectedValue={data?.find((item) => item.value == props.inputValue)}
          onItemSelect={(item) => props.onInputChange(item.value)}
          autoFocus={props.autoFocus}
          preventMenuOpenDefault
          onCustomKeyDown={props.onCustomKeyDown}
          inputId={_inputId.current}
          onFocus={props.onFocus}
          innerInputRef={(inputId, inputRef) =>
            props.innerInputRef?.(inputId, inputRef)
          }
        />
      );
    case 'TextInput':
      return (
        <AutoExpandInput
          inputId={_inputId.current}
          value={props.inputValue}
          autoFocus={props.autoFocus}
          onChange={(newValue) => props.onInputChange(newValue)}
          onCustomKeyDown={props.onCustomKeyDown}
          onFocus={props.onFocus}
          innerInputRef={(inputRef) =>
            props.innerInputRef?.(_inputId.current, inputRef)
          }
        />
      );
    case 'NumberInput':
      return (
        <AutoExpandInput
          inputId={_inputId.current}
          inputType="number"
          value={props.inputValue}
          autoFocus={props.autoFocus}
          onFocus={props.onFocus}
          onChange={(newValue) => props.onInputChange(newValue)}
          onCustomKeyDown={props.onCustomKeyDown}
          innerInputRef={(inputRef) =>
            props.innerInputRef?.(_inputId.current, inputRef)
          }
        />
      );
    case 'DateInput':
      return (
        <AutoExpandInput
          inputId={_inputId.current}
          inputType="date"
          value={props.inputValue}
          autoFocus={props.autoFocus}
          onFocus={props.onFocus}
          onChange={(newValue) => props.onInputChange(newValue)}
          onCustomKeyDown={props.onCustomKeyDown}
          innerInputRef={(inputRef) =>
            props.innerInputRef?.(_inputId.current, inputRef)
          }
        />
      );
    default:
      return <>🎲{props.inputType}</>;
  }
};

interface IAdditionalInfoItemProps extends Partial<IAdditionalInfo> {
  autoFocus?: boolean;
  isReadonly?: boolean;
  onChange?: (newvalue: string) => void;
  onFocus?: React.FocusEventHandler<HTMLInputElement>;
  innerInputRef?: (inputId: string, input: HTMLInputElement) => void;
  onCustomKeyDown?: (lowercaseKey: string, event: KeyboardEvent) => void;
}

export const AdditionalInfoItemUntranslated: React.FC<
  IAdditionalInfoItemProps & II18nFixedNamespace<any>
> = (props) => {
  const { t: translator } = props;

  const [value, setValue] = useState<string>(props.value!);

  useEffect(() => {
    setValue(props.value!);
  }, [props.value]);

  if (props.isReadonly) {
    return (
      <span style={{ cursor: 'not-allowed' }}>
        {translator(props.fK)}:&nbsp;
        {props.value}
      </span>
    );
  }

  return (
    <>
      {translator(props.fK)}:&nbsp;
      <FlexibleInput
        {...props}
        inputValue={value ?? ''}
        onInputChange={(newValue) => {
          setValue(newValue);
          props.onChange?.(newValue);
        }}
        autoFocus={props.autoFocus}
        innerInputRef={(inputId, inputRef) => {
          props.innerInputRef?.(inputId, inputRef);
        }}
        onCustomKeyDown={props.onCustomKeyDown}
        onFocus={props.onFocus}
      />
    </>
  );
};

const AdditionalInfoItem = I18n.withTranslation(
  AdditionalInfoItemUntranslated,
  {
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  }
);

export default AdditionalInfoItem;
