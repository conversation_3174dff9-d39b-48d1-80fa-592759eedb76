import {
  Certainty,
  Laterality,
} from '@tutum/hermes/bff/service_domains_patient_file';
import I18n from '@tutum/infrastructure/i18n';
import Form from '@tutum/mvz/locales/en/Form.json';
import { IActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import React, { memo, useRef, useState } from 'react';

import {
  BodyTextM,
  IMenuItem,
  IMenuItemWithData,
  InputSuggestion,
  coreComponents,
} from '@tutum/design-system/components';
import {
  DEFAULT_SELECT_COMPONENT_CONFIG,
  DEFAULT_SELECT_STYLE_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
import debounce from 'lodash/debounce';
import type {
  GroupBase,
  SelectComponentsConfig,
  SelectInstance,
} from 'react-select';
import DiagnosePopoverOption from './DiagnosePopoverOption';
import StyledDiagnosisWrapper, { StyledOption } from './DiagnosisNode.styled';
import { IDiagnoseSearchResult } from './DiagnosisNode.type';
import { getCertaintyList, getLateralityList } from './DiagnosisNode.util';
import DropdownWithLabel from './dropdown-with-label';
import escapeRegExp from 'lodash/escapeRegExp';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import { BASE_PATH_ADMIN } from '@tutum/infrastructure/utils/string.util';

const DEFAULT_ICD_SELECT_COMPONENT_CONFIG: Partial<
  SelectComponentsConfig<
    {
      data: IDiagnoseSearchResult;
    },
    false,
    GroupBase<{
      data: IDiagnoseSearchResult;
    }>
  >
> = {
  ...DEFAULT_SELECT_COMPONENT_CONFIG,
  Option: (props) => {
    const {
      data: { data },
      selectProps,
    } = props;
    const { inputValue: searchKey } = selectProps;

    return (
      <coreComponents.Option {...props}>
        <StyledOption>
          <DiagnosePopoverOption item={data} query={searchKey} />
        </StyledOption>
      </coreComponents.Option>
    );
  },
};

interface DiagnosisNodeProps<
  T extends {
    code?: string;
    description?: string;
  }
> {
  autoFocus?: boolean; // NOTE: need to custom this in order to use inside Lexical context
  disabled?: boolean;
  placeholder: string;
  scaleNumber?: number;
  onSubmit(): void;
  onClear(): void;
  renderNoResults: JSX.Element;
  defaultIcdValue?: IMenuItemWithData<T>;
  freeText: string;
  onSearchIcdList(
    keyWord: string,
    setOptions?: (options: Array<IMenuItemWithData<T>>) => void
  ): Promise<void> | undefined;
  onIcdCodeSelect: (item: IMenuItemWithData<T>) => void;
  formatIcdLabel?: (option: IMenuItemWithData<T>) => React.ReactNode;
  icdOptionStyleConfig?: Partial<
    SelectComponentsConfig<
      IMenuItemWithData<T>,
      false,
      GroupBase<IMenuItemWithData<T>>
    >
  >;
  certaintyLabel: string;
  defaultCertaintyValue?: Certainty;
  onCertaintyChange: (item: Certainty) => void;
  lateralityLabel: string;
  defaultLateralityValue?: Laterality;
  onLateralityChange: (item: Laterality) => void;
  actionBarValues: IActionBarStore;
  key?: string;
  onFocusOpenSchein?: () => void;
}
const C_L_COMPONENT_CONFIG: Partial<
  SelectComponentsConfig<IMenuItem<any>, false, GroupBase<IMenuItem<any>>>
> = {
  Option: (props) => {
    const {
      data: { label, value },
    } = props;
    return (
      <coreComponents.Option {...props}>
        <StyledOption justify="space-between">
          <span className="sl-code">{value}</span>
          <BodyTextM limitLines={1} className="sl-desc">
            {label}
          </BodyTextM>
        </StyledOption>
      </coreComponents.Option>
    );
  },
};

const DiagnosisNodeComponent = <
  T extends {
    code?: string;
    description?: string;
  }
>(
  props: DiagnosisNodeProps<T>
) => {
  const {
    placeholder,
    disabled,
    autoFocus,
    scaleNumber,
    onSubmit,
    onClear,
    freeText,
    defaultIcdValue,
    onIcdCodeSelect,
    onSearchIcdList,
    formatIcdLabel,
    icdOptionStyleConfig,
    certaintyLabel,
    onCertaintyChange,
    defaultCertaintyValue,
    lateralityLabel,
    defaultLateralityValue,
    onLateralityChange,
    renderNoResults: noResults,
    actionBarValues,
  } = props;
  const { t } = I18n.useTranslation<keyof typeof Form.AddNewDiagnosisDialog>({
    namespace: 'Form',
    nestedTrans: 'AddNewDiagnosisDialog',
  });
  const { t: tCommon } = I18n.useTranslation({
    namespace: 'Common',
  });

  const { t: tAddNewDiagnosisDialogAdmin } = I18n.useTranslation<
    keyof typeof ActionChainI18n.AddNewDiagnosisDialog
  >({
    namespace: 'ActionChain',
    nestedTrans: 'AddNewDiagnosisDialog',
  });

  const isInAdminApp = window.location.pathname.includes(BASE_PATH_ADMIN);
  const certaintyList = getCertaintyList(
    isInAdminApp ? tAddNewDiagnosisDialogAdmin : t
  );
  const icdSelectRef = useRef<SelectInstance<IMenuItem<string>, boolean>>(null);

  const certaintyRef = useRef<SelectInstance<
    IMenuItem<string>,
    boolean
  > | null>(null);
  const lateralityRef = useRef<SelectInstance<
    IMenuItem<string>,
    boolean
  > | null>(null);

  const [isShow, setIsShow] = useState(false);

  function _onIcdCodeSelect(item: IMenuItemWithData<T>, meta) {
    if (!item) {
      return;
    }
    if (meta.action === 'select-option') {
      onIcdCodeSelect(item);
      certaintyRef.current?.focus();
      setIsShow(false);
      return;
    }
  }

  function onCertaintySelect(certaintyItem: IMenuItem<Certainty>) {
    if (!certaintyItem) {
      return;
    }
    onCertaintyChange(certaintyItem.value);
  }

  function onLateralitySelect(lateralityItem: IMenuItem<Laterality>) {
    if (!lateralityItem) {
      return;
    }
    onLateralityChange(lateralityItem.value);
  }

  function onFilterOptionByFreetextSearch(
    opt: { value: string; label: string },
    input: string
  ) {
    if (!input || /\\/.test(input)) return true;
    const { value } = opt;
    const freetextSearchRegex = new RegExp(escapeRegExp(input), 'i');
    return value.search(freetextSearchRegex) > -1;
  }

  return (
    <StyledDiagnosisWrapper
      className="sl-diagnosis-node-wrapper"
      column
      auto
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          const isAllValueFilled =
            Boolean(icdSelectRef.current?.inputRef?.value) &&
            Boolean(certaintyRef?.current?.inputRef?.value);

          const isAllMenuClosed =
            !icdSelectRef.current?.props?.menuIsOpen &&
            !certaintyRef.current?.props?.menuIsOpen &&
            !lateralityRef.current?.props?.menuIsOpen;

          const isValidCertainty = certaintyList.some(
            (c) => c.value === certaintyRef?.current?.inputRef?.value
          );

          const value = lateralityRef?.current?.inputRef?.value;
          const isValidLaterality =
            !value || getLateralityList().some((c) => c.value === value);

          if (
            isAllValueFilled &&
            isAllMenuClosed &&
            isValidCertainty &&
            isValidLaterality
          ) {
            onSubmit();
          }
        }
      }}
      scaleNumber={scaleNumber}
    >
      <InputSuggestion
        key={
          actionBarValues
            ? `${actionBarValues.catalog}_${actionBarValues.doctorSpecialist}`
            : 'diagnois_node_composer'
        }
        id={'diagnois_node_composer'}
        isClearable
        isSearchable
        classNamePrefix="sl-diagnose-block"
        menuIsOpen={isShow}
        tabSelectsValue={false}
        inputValue={freeText}
        autoFocus={autoFocus}
        isDisabled={disabled}
        menuPortalTarget={document.body}
        noOptionsMessage={() => noResults}
        styles={{
          ...DEFAULT_SELECT_STYLE_CONFIG(scaleNumber),
          menu: (base) => ({
            ...base,
            maxWidth: 'unset',
          }),
        }}
        components={{
          ...DEFAULT_ICD_SELECT_COMPONENT_CONFIG,
          ...icdOptionStyleConfig,
        }}
        formatOptionLabel={formatIcdLabel}
        placeholder={placeholder}
        value={defaultIcdValue}
        loadOptions={(query, setOptions) => {
          if (query?.length < 3 || query === freeText) {
            setIsShow(false);
            return;
          }
          onSearchIcdList(query, (opts) => {
            setIsShow(true);
            setOptions(opts);
          });
        }}
        keyCode="label"
        onChange={_onIcdCodeSelect}
        ref={icdSelectRef}
        onKeyDown={(event) => {
          if (event.key === 'Escape') {
            setIsShow(false);
          }
          if (
            event.key === 'Backspace' &&
            !icdSelectRef?.current?.inputRef?.value
          ) {
            onClear();
          }
        }}
        onBlur={() => setIsShow(false)}
        defaultOptions
      />
      <div className="sl-row">
        <DropdownWithLabel
          id={'certainty'}
          isRequired
          className="sl-dropdown"
          t={tCommon}
          disabled={disabled}
          label={certaintyLabel}
          keyCode="value"
          defaultValue={defaultCertaintyValue}
          defaultOptions={certaintyList}
          onInputChange={(query, actionMeta) => {
            if (actionMeta?.action === 'input-change') {
              onCertaintyChange(query as Certainty);
            }
          }}
          placeHolder={tCommon('Select.select')}
          loadOptions={debounce((input, setInteralSource) => {
            setInteralSource(certaintyList);
          }, 100)}
          filterOption={onFilterOptionByFreetextSearch}
          ref={certaintyRef}
          onItemSelect={onCertaintySelect}
          formatValueLabel={(option) => option.value}
          componentConfig={C_L_COMPONENT_CONFIG}
          tabSelectsValue={false}
          onCaretPositionChange={(position: number, event) => {
            if (position !== 0 || certaintyRef?.current?.inputRef?.value) {
              return;
            }

            if (event.key === 'ArrowLeft' || event.key === 'Backspace') {
              icdSelectRef.current?.blur();
              icdSelectRef.current?.focus();
            }
          }}
        />

        <DropdownWithLabel
          id={'laterality'}
          className="sl-dropdown"
          t={tCommon}
          disabled={disabled}
          label={lateralityLabel}
          keyCode="value"
          placeHolder={tCommon('Select.select')}
          defaultValue={defaultLateralityValue}
          defaultOptions={getLateralityList()}
          onInputChange={(query, actionMeta) => {
            if (actionMeta?.action === 'input-change') {
              onLateralityChange(query as Laterality);
            }
          }}
          loadOptions={debounce((input, setInteralSource) => {
            setInteralSource(getLateralityList());
          }, 100)}
          filterOption={onFilterOptionByFreetextSearch}
          ref={lateralityRef}
          onItemSelect={onLateralitySelect}
          formatValueLabel={(option) => option.value}
          componentConfig={C_L_COMPONENT_CONFIG}
          tabSelectsValue={false}
          onCaretPositionChange={(position: number, event) => {
            if (position !== 0 || lateralityRef?.current?.inputRef?.value) {
              return;
            }
            if (event.key === 'ArrowLeft' || event.key === 'Backspace') {
              certaintyRef.current?.blur();
              certaintyRef.current?.focus();
            }
          }}
        />
      </div>
    </StyledDiagnosisWrapper>
  );
};

export default memo(DiagnosisNodeComponent);
