import React, { useMemo } from 'react';

import { Flex, BodyTextM, Tag as SLTag } from '@tutum/design-system/components';
import { getPositionsToHighlight } from '@tutum/infrastructure/utils/match';
import I18n from '@tutum/infrastructure/i18n';
import { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import { COLOR } from '@tutum/design-system/themes/styles';
import { transformPrice } from '@tutum/infrastructure/shared/price-format';

const PriceTag = ({ price }: { price: number }) => {
  return (
    <span style={{ color: COLOR.TEXT_TERTIARY_SILVER }}>{`${transformPrice(
      price
    )}`}</span>
  );
};

const ServicePopoverMenuItem = ({
  item,
  query,
}: {
  query: string;
  item: IContractData;
}) => {
  const { t } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const createTitle = (query: string, item: IContractData) => {
    const styleTexts = getPositionsToHighlight(item.description || '', query);

    return (
      <Flex w={'calc(100% - 60px)'} column>
        <BodyTextM limitLines={styleTexts.length === 0 ? 2 : 1}>
          {styleTexts.length === 0
            ? item.description
            : styleTexts.map((itemSub, index) =>
                itemSub.highlight ? (
                  <strong key={index}>{itemSub.value}</strong>
                ) : (
                  itemSub.value
                )
              )}
        </BodyTextM>
        {item.isSelfCreated && (
          <SLTag slStyle="outline">{t('selfCreated')}</SLTag>
        )}
      </Flex>
    );
  };

  const memoizedCreateTitle = useMemo(
    () => createTitle(query, item),
    [query, item]
  );

  const showPrice = (item: IContractData) => {
    return <PriceTag price={item.price || 0} />;
  };

  return (
    <Flex auto align="flex-start" justify="space-between">
      <Flex flex={1} align="flex-start">
        <div style={{ width: 60 }}>{item.code}</div>
        {memoizedCreateTitle}
      </Flex>
      {showPrice(item)}
    </Flex>
  );
};

export default React.memo(ServicePopoverMenuItem);
