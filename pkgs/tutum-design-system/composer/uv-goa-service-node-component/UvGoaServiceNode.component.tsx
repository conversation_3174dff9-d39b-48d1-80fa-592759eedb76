import React, { use<PERSON>emo, useRef, useCallback } from 'react';
import type { SelectInstance } from 'react-select';

import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import type {
  EncounterUvGoaService,
  EncounterServiceTimeline,
} from '@tutum/hermes/bff/repo_encounter';

import {
  InputSuggestion,
  Flex,
  coreComponents,
} from '@tutum/design-system/components';
import { isNotEmpty } from '@tutum/design-system/infrastructure/utils';
import {
  DEFAULT_SELECT_COMPONENT_CONFIG,
  DEFAULT_COMPOSER_SELECT_STYLE_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
import AdditionalInfoPlainEditor from '../service-node-lexical';
import AdditionalInfoSelectionPlugin from '../service-node-lexical/plugins/additional-info-selection-plugin/additional-info-selection-plugin';
import AdditionalInfoKeyboardNavigationPlugin from '../service-node-lexical/plugins/additional-info-keyboard-navigation-plugin';
import { OmimGChainTypeaheadPlugin } from '../service-node-lexical/plugins/omim-g-chain-typeahead-plugin';
import AdditionalInfoKeyEnterToSubmitPlugin from '../service-node-lexical/plugins/additional-info-enter-submit-plugin';
import ServicePopoverMenuItem from './UvGoaServicePopoverMenuItem';
import { PointValueModel } from '@tutum/hermes/bff/legacy/point_value_common';

export interface IServiceNodeProps {
  // OPTIONAL
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  additionalInfoEditorplaceholder?: string;
  noResultsComponent?: JSX.Element;
  rightElement?: JSX.Element;
  encounterDate?: number; // TODO: refactor later

  // MAIN PROPS
  data: EncounterUvGoaService;
  additionalFields: AdditionalInfoField[];
  pointValue?: PointValueModel;
  // METHODS
  onServiceSelect: (item: IContractData) => Promise<EncounterUvGoaService>;
  searchService: (
    query: string,
    setDatasource: (result: IContractData[]) => void
  ) => Promise<void | undefined> | undefined;
  onChange: (data: EncounterUvGoaService) => Promise<void>;
  onSubmit(payload: EncounterUvGoaService): Promise<void>;
  onClear: () => void;
  id: string;
}

const STYLE_CONFIG = DEFAULT_COMPOSER_SELECT_STYLE_CONFIG();

const ServiceNode: React.FC<React.PropsWithChildren<IServiceNodeProps>> = (
  props
) => {
  const {
    className,
    placeholder,
    data,
    additionalFields,
    disabled,
    rightElement,
    onChange,
    onSubmit,
    onClear,
    searchService,
    onServiceSelect,
    noResultsComponent,
    encounterDate,
    pointValue,
    id,
  } = props;
  const inputSuggestionEl = useRef<SelectInstance<any, boolean> | null>(null);

  const setUpdatedService = (updatedService: EncounterUvGoaService) => {
    const _service = { ...updatedService };
    if (!_service.command) {
      _service.command = data.command || 'L';
    }

    onChange(_service);
    return _service;
  };

  const handleSelectService = async (item: IContractData) => {
    const inputData = `(${item.code}) ${item.description}`;
    const result = await onServiceSelect(item);
    setUpdatedService({
      ...result,
      freeText: inputData,
    });
  };

  const defaultEditorState = useMemo(
    () => data?.additionalInfosRaw,
    [data?.additionalInfosRaw]
  );

  const onComposerSubmit = useCallback(
    async (
      payload: Pick<
        EncounterUvGoaService,
        'additionalInfos' | 'additionalInfosRaw'
      >
    ) => {
      const newServiceData = setUpdatedService({
        ...data,
        additionalInfos: payload.additionalInfos,
        additionalInfosRaw: payload.additionalInfosRaw,
      });
      await onSubmit(newServiceData);
    },
    [data, onSubmit, onChange]
  );

  const showServiceOptions =
    isNotEmpty(data?.freeText) && isNotEmpty(data?.code);

  return (
    <Flex className={className} column gap={0}>
      <Flex align="center" auto>
        <InputSuggestion
          id={id}
          keyCode="description"
          autoFocus={!disabled}
          placeholder={placeholder}
          styles={STYLE_CONFIG}
          openMenuOnClick={false}
          components={{
            ...DEFAULT_SELECT_COMPONENT_CONFIG,
            Option: (props) => {
              const {
                data,
                selectProps: { inputValue },
              } = props;

              return (
                <coreComponents.Option {...props}>
                  <ServicePopoverMenuItem
                    item={data}
                    query={inputValue}
                  />
                </coreComponents.Option>
              );
            },
          }}
          defaultInputValue={data?.freeText}
          loadOptions={
            ((query: string, setDatasource: any) => {
              searchService(query, (_result) => {
                setDatasource(_result);
              });
            }) as any
          }
          onChange={handleSelectService}
          getOptionLabel={(opt) => `${opt.description} ${opt.code}`}
          noOptionsMessage={() => noResultsComponent}
          ref={inputSuggestionEl}
          onKeyDown={async (e) => {
            if (
              e.key === 'Backspace' &&
              !inputSuggestionEl?.current?.inputRef?.value
            ) {
              onClear();
            } else if (
              e.key === 'Enter' &&
              inputSuggestionEl?.current?.inputRef?.value &&
              !inputSuggestionEl?.current?.props?.menuIsOpen
            ) {
              onSubmit({
                ...data,
                price: data.price,
                description:
                  data.description ||
                  inputSuggestionEl?.current?.inputRef?.value?.trim(),
              });
            }
          }}
        />
        {/* top right element here */}
        {rightElement}
      </Flex>

      {showServiceOptions && (
        <AdditionalInfoPlainEditor
          // placeholder={additionalInfoEditorplaceholder}
          defaultEditorState={defaultEditorState} // NOTE: keep this local
          onChange={(addInfoPayload) =>
            setUpdatedService({ ...data, ...addInfoPayload })
          }
        >
          {/* OPTIONAL PLUGINS HIGHER THAN DEFAULTs*/}
          {props.children}
          {/* Plugin for handle OMIM-G chain */}
          <OmimGChainTypeaheadPlugin />
          {/* Plugin for handle additional info keyboard navigation */}
          <AdditionalInfoKeyboardNavigationPlugin />
          {/* Plugin for handle additional info logics - all the blocks + encounterDate */}
          {/* Plugin for selecting additional info nodes */}

          <AdditionalInfoSelectionPlugin
            encounterDate={encounterDate}
            encounterService={
              { ...data, isPreParticipate: false } as EncounterServiceTimeline
            }
            additionalFields={additionalFields}
          />

          {/* Plugin for handle additional info avoid line break then */}
          {/* AND Plugin for handle additional info submit */}
          <AdditionalInfoKeyEnterToSubmitPlugin onSubmit={onComposerSubmit} />
        </AdditionalInfoPlainEditor>
      )}
    </Flex>
  );
};

export default React.memo(ServiceNode);
