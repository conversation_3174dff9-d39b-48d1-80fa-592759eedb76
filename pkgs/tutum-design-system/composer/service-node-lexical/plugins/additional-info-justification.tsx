
import React from 'react';
import { createCommand, COMMAND_PRIORITY_EDITOR } from 'lexical';
import { mergeRegister } from '@lexical/utils';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { AdditionalInfoBlockNode } from './additional-info-selection-plugin/AdditionalInfoBlock.node';
import type { NodeKey } from 'lexical';

export const SEARCH_JUSTIFICATION_COMMAND = createCommand<{
  query: string;
  setData: (results: string[]) => void;
}>();

export const DELETED_JUSTIFICATION_COMMAND = createCommand<{
  nodeKey: NodeKey;
  value: string;
}>();

export const REQUEST_ADD_JUSTIFICATION_COMMAND = createCommand<NodeKey>();

export const SET_JUSTIFICATION_VALUE_COMMAND = createCommand<{
  nodeKey: NodeKey;
  newJustification?: string;
}>();

type AdditionalInfoJustificationPluginProps = {
  onSearch: (query: string, setData: (results: string[]) => void) => string[];
  onCreateJustificationClick?: (isNoResult: boolean) => void;

  // NOTE: render props
  children?: (renderProps: {
    onNewJustificationAdded: (newJustification?: string) => void;
  }) => React.ReactNode | undefined;
  onDeletedJustificationClick: (value: string) => Promise<void>;
};

const AdditionalInfoJustificationPlugin: React.FC<AdditionalInfoJustificationPluginProps> =
  ({
    onSearch,
    onCreateJustificationClick,
    children,
    onDeletedJustificationClick,
  }) => {
    const [editor] = useLexicalComposerContext();
    const [isNoResult, setIsNoResult] = React.useState(false);
    const [addNewNodeKey, setAddNewNodeKey] = React.useState<NodeKey | null>(
      null
    );

    React.useEffect(() => {
      if (!editor.hasNodes([AdditionalInfoBlockNode])) {
        throw Error(
          'Lexical editor: AdditionalInfoBlockNode is not registered'
        );
      }
    }, [editor]);

    React.useEffect(() => {
      return mergeRegister(
        editor.registerCommand(
          SEARCH_JUSTIFICATION_COMMAND,
          (payload) => {
            const { query, setData } = payload;
            const results = onSearch(query, setData);
            setIsNoResult(results.length === 0);
            return false;
          },
          COMMAND_PRIORITY_EDITOR
        ),

        editor.registerCommand(
          REQUEST_ADD_JUSTIFICATION_COMMAND,
          (requestingNodeKey) => {
            if (onCreateJustificationClick != null) {
              setAddNewNodeKey(requestingNodeKey);
              onCreateJustificationClick(isNoResult);
              return true;
            }
            return false;
          },
          COMMAND_PRIORITY_EDITOR
        ),

        editor.registerCommand(
          DELETED_JUSTIFICATION_COMMAND,
          ({ nodeKey, value }) => {
            onDeletedJustificationClick(value).then(() => {
              editor.dispatchCommand(SET_JUSTIFICATION_VALUE_COMMAND, {
                nodeKey: nodeKey,
                newJustification: '',
              });
            });
            return true;
          },
          COMMAND_PRIORITY_EDITOR
        )
      );
    }, [editor, onSearch, isNoResult, onCreateJustificationClick]);

    const onNewJustificationAdded = React.useCallback(
      (newJustification?: string) => {
        editor.dispatchCommand(SET_JUSTIFICATION_VALUE_COMMAND, {
          nodeKey: addNewNodeKey,
          newJustification,
        });
      },
      [editor, addNewNodeKey]
    );

    return (
      <>
        {children?.({
          onNewJustificationAdded,
        })}
      </>
    );
  };

export default AdditionalInfoJustificationPlugin;
