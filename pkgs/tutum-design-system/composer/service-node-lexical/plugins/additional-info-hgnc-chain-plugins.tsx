import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import {
  $isAdditionalInfoBlockNode,
  AdditionalInfoBlockNode,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/AdditionalInfoBlock.node';
import { HgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import {
  $getNodeByKey,
  $getSelection,
  $isNodeSelection,
  COMMAND_PRIORITY_EDITOR,
  NodeKey,
  SELECTION_CHANGE_COMMAND,
  createCommand,
} from 'lexical';
import { useEffect } from 'react';
import { ADD_ADDITIONAL_INFO_CMD } from './additional-info-plugin/commands';
import cloneDeep from 'lodash/cloneDeep';
import set from 'lodash/set';
import { HgncItem } from '@tutum/hermes/bff/legacy/hgnc_common';
import { HGNC_KEY } from './additional-info-selection-plugin/components/additional-info-block';
export const SEARCH_HGNC_CHAIN_COMMAND = createCommand<{
  query: string;
  setData: (results: HgncChain[]) => void;
}>('SEARCH_HGNC_CHAIN_COMMAND');
export const ADD_HGNC_CHAIN_COMMAND = createCommand('ADD_HGNC_CHAIN_COMMAND');
export const VIEW_ALL_HGNC_CHAIN_COMMAND = createCommand(
  'VIEW_ALL_HGNC_CHAIN_COMMAND'
);

export interface ReplaceHgncChainPayload {
  chain: HgncItem[];
  nodeKeyToReplace: NodeKey;
}

export const ON_SELECT_HGNC_CHAIN = createCommand<ReplaceHgncChainPayload>(
  'ON_SELECT_HGNC_CHAIN'
);

type AdditionalInfoHgncChainPluginProps = {
  onCreate: () => void;
  onViewAll: () => void;
  onSearch: (query: string) => Promise<HgncChain[]>;
  isServiceChain?: boolean;
  setAdditionalInfoNodeKey: (_: NodeKey) => void;
};

const AdditionalInfoHgncChainPlugin = (
  props: AdditionalInfoHgncChainPluginProps
) => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (!editor.hasNodes([AdditionalInfoBlockNode])) {
      throw Error('Lexical editor: AdditionalInfoBlockNode is not registered');
    }
  }, [editor]);

  useEffect(() => {
    return mergeRegister(
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          const selection = $getSelection();
          if ($isNodeSelection(selection)) {
            const node = selection.getNodes()?.[0];
            if ($isAdditionalInfoBlockNode(node)) {
              if (node.__info.fK === HGNC_KEY) {
                props.setAdditionalInfoNodeKey(node.getKey());
              }
            }
          }
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        ON_SELECT_HGNC_CHAIN,
        (payload) => {
          props.setAdditionalInfoNodeKey(payload.nodeKeyToReplace);
          const node = $getNodeByKey(payload.nodeKeyToReplace);
          // create new nodes
          for (const info of payload.chain) {
            const data = {
              info: {
                ...cloneDeep(HGNC_INFO),
                // value: info.code,
                value: info.description,
              },
              selectorNodeKey: props.isServiceChain
                ? payload.nodeKeyToReplace
                : null,
            };

            set(data, 'info.additionalInformations.0.value', info.description);
            editor.dispatchCommand(ADD_ADDITIONAL_INFO_CMD, data as any);
          }
          node?.remove();
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        SEARCH_HGNC_CHAIN_COMMAND,
        (payload) => {
          const { query, setData } = payload;
          props.onSearch(query).then((result) => setData(result));
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        ADD_HGNC_CHAIN_COMMAND,
        () => {
          props.onCreate();
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        VIEW_ALL_HGNC_CHAIN_COMMAND,
        () => {
          props.onViewAll();
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      )
    );
  });

  return null;
};

const HGNC_INFO = {
  "fK": "5077",
  "label": "HGNC-Gensymbol",
  "minLength": null,
  "maxLength": null,
  "isMany": true,
  "dataType": "int",
  "inputType": "DropDownListApi",
  "dataSource": "hgnc-gensymbol-code.",
  "isRequired": false,
  "treatmentCaseWithRule": [
    {
      "treatmentCase": "0101",
      "ruleNames": [
        "770F",
        "816F",
        "828F",
        "829F",
        "830F",
        "834F",
        "843F",
        "854F"
      ]
    },
    {
      "treatmentCase": "0102",
      "ruleNames": [
        "770F",
        "816F",
        "828F",
        "829F",
        "830F",
        "834F",
        "843F",
        "854F"
      ]
    },
    {
      "treatmentCase": "0103",
      "ruleNames": [
        "770F",
        "816F",
        "828F",
        "829F",
        "830F",
        "834F",
        "843F",
        "854F"
      ]
    },
    {
      "treatmentCase": "0104",
      "ruleNames": [
        "770F",
        "816F",
        "828F",
        "829F",
        "830F",
        "834F",
        "843F",
        "854F"
      ]
    }
  ],
  "additionalInformations": [
    {
      "fK": "5078",
      "label": "Gen-Name",
      "minLength": 0,
      "maxLength": null,
      "isMany": true,
      "dataType": "string",
      "inputType": "TextInput",
      "dataSource": "",
      "isRequired": false,
      "treatmentCaseWithRule": [
        {
          "treatmentCase": "0101",
          "ruleNames": [
            "772F"
          ]
        },
        {
          "treatmentCase": "0102",
          "ruleNames": [
            "772F"
          ]
        },
        {
          "treatmentCase": "0103",
          "ruleNames": [
            "772F"
          ]
        },
        {
          "treatmentCase": "0104",
          "ruleNames": [
            "772F"
          ]
        }
      ]
    }
  ]
}

export default AdditionalInfoHgncChainPlugin;
