import { useEffect } from 'react';
import {
  KEY_ENTER_COMMAND,
  COMMAND_PRIORITY_LOW,
  LexicalEditor,
} from 'lexical';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import type { IServiceBlock } from '../../Composer.type';
import {
  $isAdditionalInfoBlockNode,
  AdditionalInfoBlockNode,
} from './additional-info-selection-plugin/AdditionalInfoBlock.node';

type AdditionalInfoSelectionPluginProps = {
  onSubmit(
    data: Pick<IServiceBlock, 'additionalInfos' | 'additionalInfosRaw'>
  ): Promise<void>;
};

export function parseEditorStateToSubmitableData(
  editor?: LexicalEditor
): Pick<IServiceBlock, 'additionalInfos' | 'additionalInfosRaw'> {
  const submitData: Pick<
    IServiceBlock,
    'additionalInfos' | 'additionalInfosRaw'
  > = {
    additionalInfos: [],
    additionalInfosRaw: '',
  };

  if (editor == null) {
    return submitData;
  }

  try {
    const editorState = editor.getEditorState();

    const jsonState = editorState.toJSON();
    submitData.additionalInfosRaw = JSON.stringify(jsonState);
    editorState.read(() => {
      const nodeList = editorState._nodeMap;
      for (const nodeMap of nodeList) {
        const [, node] = nodeMap;

        if (!$isAdditionalInfoBlockNode(node)) {
          continue;
        }

        const additionalInfoNode = node as AdditionalInfoBlockNode;
        const info = additionalInfoNode.getInfo();

        // NOTE: format date so it's billable
        // if (AdditionalInfoUtils.shouldConvertToBillableString(info)) {
        //   info.value = AdditionalInfoUtils.convertToBillableDateString(
        //     info.value
        //   );
        // }

        submitData.additionalInfos?.push({
          fK: info.fK,
          value: info.value!,
          dataId: info.dataId!,
          children: info.additionalInformations?.map((child) => ({
            fK: child.fK,
            value: child.value!,
            dataId: child.dataId!,
          })),
        });
      }
    });
    return submitData;
  } catch (_) {
    return submitData;
  }
}

function onEnterKeyCommandHandler(
  event: KeyboardEvent,
  editor: LexicalEditor
): Pick<IServiceBlock, 'additionalInfos' | 'additionalInfosRaw'> {
  if (!event) {
    return {
      additionalInfos: [],
      additionalInfosRaw: '',
    };
  }

  return parseEditorStateToSubmitableData(editor);
}

function AdditionalInfoKeyEnterToSubmitPlugin({
  onSubmit,
}: AdditionalInfoSelectionPluginProps): null {
  const [editor] = useLexicalComposerContext();

  useEffect(
    () =>
      editor.registerCommand(
        KEY_ENTER_COMMAND,
        (event, editor) => {
          if (event?.shiftKey) {
            return false;
          }

          if (event != null) {
            event.preventDefault?.();
            event.stopImmediatePropagation?.();
          }

          const submitableData = onEnterKeyCommandHandler(event!, editor);
          onSubmit(submitableData);
          return true;
        },
        COMMAND_PRIORITY_LOW
      ),
    [editor, onSubmit]
  );
  return null;
}

export default AdditionalInfoKeyEnterToSubmitPlugin;
