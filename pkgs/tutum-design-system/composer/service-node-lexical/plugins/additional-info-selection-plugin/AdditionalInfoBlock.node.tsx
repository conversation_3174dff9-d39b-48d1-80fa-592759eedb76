import React from 'react';
import _cloneDeep from 'lodash/cloneDeep';
import _set from 'lodash/set';
import { DecoratorNode } from 'lexical';
import type {
  Spread,
  NodeKey,
  LexicalNode,
  SerializedLexicalNode,
} from 'lexical';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import { findTabbableDescendants } from '../../utils/tabbable';
import type { IAdditionalInfo } from '../additional-info-plugin/AdditionalInfoPlugin.type';
import AdditionalInfoBlock from './components/additional-info-block/AdditionalInfoBlock.component';
import {
  ASV_KEY,
  PSEUDO_GNR,
} from './components/additional-info-block/AdditionalInfoBlock.constant';
import type { IAdditionalInfoBlockProps } from './components/additional-info-block/AdditionalInfoBlock.type';
import AdditionalInfoListBlock from './components/additional-info-list-block';
import type { IMenuItemWithData } from '@tutum/design-system/components';
import {
  CustomOptionWithValueLabel,
  CustomValueLabelAsvKey,
} from './components/custom-select-menu-list/CustomSelectMenuList.component';
import type { EncounterServiceTimeline } from '@tutum/hermes/bff/legacy/repo_encounter';

const ADDITIONAL_INFO_BLOCK_NODE_TYPE = 'sl-additional-info-block';

type AdditionalInfoComponentType = 'block' | 'list';

type SerializedAdditionalInfoBlockNode = Spread<
  IAdditionalInfo & {
    encounterService: EncounterServiceTimeline;
    props: Partial<IAdditionalInfoBlockProps> | null;
    componentType: AdditionalInfoComponentType;
    type: typeof ADDITIONAL_INFO_BLOCK_NODE_TYPE;
    version: 1;
    serviceNodeKey: NodeKey;
  },
  SerializedLexicalNode
>;

const CustomItemComponentByFK = {
  [PSEUDO_GNR]: CustomOptionWithValueLabel,
  [ASV_KEY]: CustomValueLabelAsvKey,
};

export class AdditionalInfoBlockNode extends DecoratorNode<JSX.Element> {
  __encounterService: EncounterServiceTimeline;
  __info: IAdditionalInfo;
  __props: Partial<IAdditionalInfoBlockProps> | null;
  __componentType: AdditionalInfoComponentType;
  __onSelect?: (item: IMenuItemWithData<string>) => void;
  __serviceNodeKey: NodeKey;

  static getType(): typeof ADDITIONAL_INFO_BLOCK_NODE_TYPE {
    return ADDITIONAL_INFO_BLOCK_NODE_TYPE;
  }

  static clone(node: AdditionalInfoBlockNode): AdditionalInfoBlockNode {
    return new AdditionalInfoBlockNode(
      node.__encounterService,
      node.__info,
      node.__props!,
      node.__componentType,
      node.__key,
      node.__onSelect,
      node.__serviceNodeKey
    );
  }

  static importJSON(
    json: SerializedAdditionalInfoBlockNode
  ): AdditionalInfoBlockNode {
    return $createAdditionalInfoBlockNode({
      encounterService: json.encounterService,
      info: json,
      props: json.props!,
      componentType: json.componentType,
    });
  }

  constructor(
    encounterService: EncounterServiceTimeline,
    info: IAdditionalInfo,
    props?: Partial<IAdditionalInfoBlockProps>,
    componentType?: AdditionalInfoComponentType,
    key?: NodeKey,
    onSelect?: (item: IMenuItemWithData<string>) => void,
    serviceNodeKey?: NodeKey
  ) {
    super(key);
    if (!info?.dataId) {
      info.dataId = getUUID();
    }

    this.__encounterService = encounterService;
    this.__info = info;
    this.__props = props!;
    this.__componentType = componentType ?? 'block';
    this.__onSelect = onSelect;
    this.__serviceNodeKey = serviceNodeKey!;
  }

  exportJSON(): SerializedAdditionalInfoBlockNode {
    const self = this.getLatest();
    return {
      ...self.__info,
      encounterService: self.__encounterService,
      props: self.__props,
      componentType: self.__componentType,
      type: AdditionalInfoBlockNode.getType(),
      version: 1,
      serviceNodeKey: self.__serviceNodeKey,
    };
  }

  createDOM(): HTMLElement {
    const spanEl = document.createElement('span');
    const self = this.getLatest();
    spanEl.id = self.__info.dataId!;
    spanEl.style.cssText =
      'display:inline-flex;align-items:center;flex-wrap:wrap;margin:0 3px';
    return spanEl;
  }

  updateDOM(): false {
    return false;
  }

  decorate(): JSX.Element {
    const self = this.getLatest();
    const componentType = self?.__componentType;

    if (componentType === 'list') {
      return (
        <AdditionalInfoListBlock
          encounterService={self.__encounterService}
          nodeKey={self.__key}
          info={self.__info}
          onSelect={this.__onSelect}
          components={{ Option: CustomItemComponentByFK[self.__info.fK] }}
          {...self.__props}
        />
      );
    }

    return (
      <AdditionalInfoBlock
        encounterService={self.__encounterService}
        nodeKey={self.__key}
        info={self.__info}
        components={{ Option: CustomItemComponentByFK[self.__info.fK] }}
        {...self.__props}
      />
    );
  }

  // NOTE: custom methods
  selectBlock(nextSelect = true) {
    const writable = this.getWritable();
    if (writable?.__props != null) {
      writable.__props = { ...writable.__props, autoFocus: nextSelect };
    } else {
      writable.__props = { autoFocus: nextSelect };
    }
  }

  setInfo(newInfo: IAdditionalInfo) {
    const self = this.getWritable();
    if (!self.__info) return;
    self.__info = newInfo;
    return AdditionalInfoBlockNode.clone(self);
  }

  getInfo(): IAdditionalInfo {
    const self = this.getLatest();
    return self.__info;
  }

  setChildrenInfoValue(childFk: string, newValue: string) {
    const self = this.getLatest();

    if (!self.__info?.additionalInformations) {
      return;
    }

    const cloneInfo = _cloneDeep(self.__info);

    let oldChildData: IAdditionalInfo | null = null;

    const childIndex = cloneInfo.additionalInformations?.findIndex((child) => {
      if (child.fK === childFk) {
        oldChildData = child;
        return true;
      }
      oldChildData = null;
      return false;
    });

    if (oldChildData == null) return;

    if (childIndex >= 0) {
      // update child value
      const newInfo = _set(cloneInfo, `additionalInformations[${childIndex}]`, {
        ...(oldChildData as any),
        value: newValue,
      });
      self.sync(newInfo);
    }
  }

  setInfoValue(newValue: string | keyof IAdditionalInfo) {
    const self = this.getLatest();
    if (!self.__info) return;
    // write
    this.sync({ ...self.__info, value: newValue });
  }

  setInfoData(field: keyof IAdditionalInfo, value: string) {
    const self = this.getLatest();
    if (!self.__info) return;
    // write
    this.sync({ ...self.__info, [field]: value });
  }

  getTabbaleElements(): HTMLElement[] {
    const self = this.getLatest();
    const spanEl = document.getElementById(self.__info.dataId!);
    if (spanEl != null) {
      return findTabbableDescendants(spanEl);
    }
    return [];
  }

  patchProps(
    newProps: Partial<IAdditionalInfoBlockProps>
  ): AdditionalInfoBlockNode {
    const self = this.getWritable();

    if (self.__props != null) {
      self.__props = { ...self.__props, ...newProps };
    } else {
      self.__props = { ...newProps };
    }

    return self;
  }

  private sync(newInfo: IAdditionalInfo) {
    // write
    const self = this.getWritable();
    if (!self.__info) return;
    self.__info = newInfo;
  }
}

interface CreateAdditionalInfoBlockNodeProps {
  encounterService: EncounterServiceTimeline;
  info: IAdditionalInfo;
  props?: Partial<IAdditionalInfoBlockProps>;
  componentType?: AdditionalInfoComponentType;
  key?: NodeKey;
  serviceNodeKey?: NodeKey;
  onSelect?: (item: IMenuItemWithData<string>) => void;
}

export function $createAdditionalInfoBlockNode({
  encounterService,
  info,
  props,
  componentType,
  key,
  onSelect,
  serviceNodeKey,
}: CreateAdditionalInfoBlockNodeProps): AdditionalInfoBlockNode {
  return new AdditionalInfoBlockNode(
    encounterService,
    info,
    props,
    componentType,
    key,
    onSelect,
    serviceNodeKey
  );
}

export function $isAdditionalInfoBlockNode(
  node?: LexicalNode | null
): node is AdditionalInfoBlockNode {
  return node instanceof AdditionalInfoBlockNode;
}
