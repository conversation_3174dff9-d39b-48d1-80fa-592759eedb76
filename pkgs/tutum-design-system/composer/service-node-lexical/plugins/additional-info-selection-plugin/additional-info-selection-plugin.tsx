
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import AddButton from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/additional-info-add-button';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import { Field as AdditionalInfoField } from '@tutum/hermes/bff/catalog_sdebm_common';
import { EncounterServiceTimeline } from '@tutum/hermes/bff/repo_encounter';
import {
  $createTextNode,
  $getNodeByKey,
  $getRoot,
  $getSelection,
  $isParagraphNode,
  $isRangeSelection,
  COMMAND_PRIORITY_EDITOR,
  FOCUS_COMMAND,
  LexicalEditor,
  ParagraphNode,
  TextNode,
} from 'lexical';
import React, { useEffect } from 'react';
import ReactDOMServer from 'react-dom/server';
import {
  ADD_ADDITIONAL_INFO_CMD,
  CLEAR_SELECTOR_CMD,
} from '../additional-info-plugin/commands';
import { $createAdditionalInfoBlockNode } from './AdditionalInfoBlock.node';
import {
  $createAdditionalInfoSelectorNode,
  AdditionalInfoSelectorNode,
} from './AdditionalInfoSelector.node';
import I18n from '@tutum/infrastructure/i18n';
import PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import { BASE_PATH_ADMIN } from '@tutum/infrastructure/utils/string.util';

type AdditionalInfoSelectionPluginProps = {
  encounterDate?: number;
  encounterService: EncounterServiceTimeline;
  additionalFields: AdditionalInfoField[];
};

function $transformToAdditionalInfoSelectorNode(
  additionalInfos: AdditionalInfoField[],
  node: TextNode,
  oldContent: string
) {
  const selection = $getSelection();
  const parent = node.getParentOrThrow();
  node.remove();
  const additionalInfoSelectorNode =
    $createAdditionalInfoSelectorNode(additionalInfos);
  const persistedTextNode = $createTextNode(oldContent);

  // check if selection is range selection
  if ($isRangeSelection(selection)) {
    selection.insertNodes([persistedTextNode, additionalInfoSelectorNode]);
  } else {
    // otherwise, append to parent (element node)
    parent.append(persistedTextNode, additionalInfoSelectorNode);
  }
}

const TRIGGER_SYMBOL = '+';

function AdditionalInfoSelectionPlugin({
  encounterDate,
  encounterService,
  additionalFields,
}: AdditionalInfoSelectionPluginProps) {
  const [editor] = useLexicalComposerContext();

  const { t: tComposer } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tComposerAdmin } = I18n.useTranslation<
    keyof typeof ActionChainI18n.Composer
  >({
    namespace: 'ActionChain',
    nestedTrans: 'Composer',
  });

  const isInAdminApp = window.location.pathname.includes(BASE_PATH_ADMIN);
  const t = isInAdminApp ? tComposerAdmin : tComposer;

  useEffect(() => {
    appendButton(editor, t('addAdditionalInfo'), additionalFields);
    return mergeRegister(
      editor.registerNodeTransform(TextNode, (node) => {
        if (editor.isComposing()) return;
        const content = node.getTextContent().trimEnd();
        node.setTextContent('');
        const length = content.length;
        const newContent = content.slice(0, length - 1);
        const textContentLastCharacter = content.charAt(length - 1);
        switch (textContentLastCharacter) {
          case TRIGGER_SYMBOL: {
            clearAddButton(editor);
            return $transformToAdditionalInfoSelectorNode(
              additionalFields,
              node,
              newContent
            );
          }
          default:
            return;
        }
      }),

      editor.registerCommand(
        FOCUS_COMMAND,
        () => {
          const elem = editor._rootElement;
          const button = elem.nextElementSibling;
          if (button instanceof HTMLButtonElement) {
            button.onclick(null);
          }
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),

      editor.registerMutationListener(
        AdditionalInfoSelectorNode,
        (mutatedNodes) => {
          // mutatedNodes is a Map where each key is the NodeKey, and the value is the state of mutation.
          for (const [_, mutation] of mutatedNodes) {
            if (mutation === 'destroyed') {
              appendButton(editor, t('addAdditionalInfo'), additionalFields);
            }
          }
        }
      )
    );
  }, [editor, JSON.stringify(additionalFields)]);

  useEffect(
    () =>
      mergeRegister(
        editor.registerCommand(
          ADD_ADDITIONAL_INFO_CMD,
          (payload): boolean => {
            if (!payload) return false;
            const additionalInfoNode = $createAdditionalInfoBlockNode({
              encounterService,
              info: { ...payload.info, dataId: getUUID() }, // TODO: consider using uuid for dataId.
              props: {
                ...payload.initProps, // NOTE: payload.initProps may be mutated in patch props plugin
                encounterDate,
              },
            });
            if (payload.selectorNodeKey) {
              const selectorNode = $getNodeByKey(payload.selectorNodeKey);
              if (!selectorNode) return false;
              const newNode = selectorNode.replace(additionalInfoNode);

              newNode.selectBlock(true);
              return true;
            }
            // create new node and append to root
            const root = $getRoot();
            if (root && root.getChildAtIndex(0)) {
              const p = root.getChildAtIndex(0);
              if ($isParagraphNode(p)) {
                p.append(additionalInfoNode);
              }
            }
            return true;
          },
          COMMAND_PRIORITY_EDITOR
        ),
        editor.registerCommand(
          CLEAR_SELECTOR_CMD,
          (payload): boolean => {
            if (!payload?.nodeKeyToClear) return false;
            const selectorNode = $getNodeByKey(payload.nodeKeyToClear);
            if (!selectorNode) return false;
            selectorNode.remove();
            return true;
          },
          COMMAND_PRIORITY_EDITOR
        )
      ),
    [editor, encounterDate, additionalFields]
  );

  return null;
}

function htmlToNode(html: string) {
  const template = document.createElement('template');
  template.innerHTML = html;
  const nNodes = template.content.childNodes.length;
  if (nNodes !== 1) {
    throw new Error(
      `html parameter must represent a single node; got ${nNodes}. ` +
      'Note that leading or trailing spaces around an element in your ' +
      'HTML, like " <img/> ", get parsed as text nodes neighbouring ' +
      'the element; call .trim() on your input to avoid this.'
    );
  }
  return template.content.firstChild as HTMLElement;
}

function appendButton(
  editor: LexicalEditor,
  text: string,
  additionalFields: AdditionalInfoField[]
) {
  if (!editor) {
    return;
  }
  editor.getEditorState().read(() => {
    const root = $getRoot();
    clearAddButton(editor);
    const htmlString = ReactDOMServer.renderToString(<AddButton text={text} />);
    const node = htmlToNode(htmlString);
    node.onclick = () => {
      editor.update(() => {
        const additionalInfoSelectorNode =
          $createAdditionalInfoSelectorNode(additionalFields);
        const para = root.getFirstChild<ParagraphNode>();
        para.append(additionalInfoSelectorNode);
      });
      node.remove();
    };

    editor._rootElement.insertAdjacentElement('afterend', node);
  });
}

function clearAddButton(editor: LexicalEditor) {
  if (!editor) {
    return;
  }
  const elem = editor._rootElement;
  const button = elem.nextElementSibling;
  if (button instanceof HTMLButtonElement) {
    button.remove();
  }
}

export default AdditionalInfoSelectionPlugin;
