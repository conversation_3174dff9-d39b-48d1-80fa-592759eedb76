import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { isEmpty } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { GroupBase, OptionProps, StylesConfig } from 'react-select';

import {
  BodyTextM,
  BodyTextS,
  Box,
  coreComponents as CoreComponents,
  Flex,
  Tag,
} from '@tutum/design-system/components';
import { Classes } from '@tutum/design-system/components/Core';
import {
  ADD_OMIMG_CHAIN_COMMAND,
  ON_SELECT_OMIM_G_CHAIN,
  SEARCH_OMIMG_CHAIN_COMMAND,
  VIEW_ALL_OMIMG_CHAIN_COMMAND,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-omimg-chain-plugins';
import {
  OMIM_G_GenName,
  OMIM_P_typeOfIllness,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/AdditionalInfoBlock.constant';
import { IAdditionalInfoBlockProps } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/AdditionalInfoBlock.type';
import AdditionalInfoMenuList from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/components/additional-info-fields/AdditionalInfoMenuList.component';
import AdditionalInfoItem from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-item';
import AdditionalInfoSuggestionBlock from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-suggestion-block';
import {
  GroupedOption,
  ISubAdditionalInfoProps,
  ISuggestionItem,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-suggestion-block/AdditionalInfoSuggestionBlock.component';
import { scaleSpacePx } from '@tutum/design-system/styles';
import { COLOR } from '@tutum/design-system/themes/styles';
import { OmimGChain } from '@tutum/hermes/bff/legacy/app_catalog_omimg_chain';
import i18n from '@tutum/infrastructure/i18n';
import {
  searchOmimG,
  searchOmimP,
} from '@tutum/infrastructure/masterdata-service';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

const NOT_FOUND_CODE = '999999';
const LABEL_OMIMG_CHAIN = 'OmimGChains';
const LABEL_OMIMG_CODES = 'OmimGCodes';

export const SELECT_STYLE: StylesConfig<
  unknown,
  boolean,
  GroupBase<unknown>
> = {
  menu(base) {
    return { ...base, minWidth: '768px', zIndex: 2 };
  },
};

const CustomOption = (props: OptionProps<ISuggestionItem>) => {
  const { t: translator } = i18n.useTranslation({
    namespace: 'Sdebm',
  });
  const value = props.data?.value;
  const data = props.data as any;

  if (data?.chain) {
    return (
      <CoreComponents.Option {...(props as any)}>
        <Flex justify="space-between">
          <Flex gap={10} column>
            <Flex column className={Classes.MENU_ITEM_LABEL}>
              <BodyTextM fontWeight="Bold">{props.data.label}</BodyTextM>
            </Flex>
            <Flex column>
              {data.chain.map((e, idx) => {
                return (
                  <Flex
                    key={idx}
                    gap={5}
                    align="center"
                    mt={idx === 0 ? '0' : '8px'}
                  >
                    <Box
                      className={Classes.MENU_ITEM_LABEL}
                      style={{ minWidth: scaleSpacePx(18) }}
                    >
                      {e.code}
                    </Box>
                    <Box>{e.genName}</Box>
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <Tag
            slColor={COLOR.BACKGROUND_TERTIARY_DIM}
            slStyle="fill"
            color={COLOR.TEXT_PRIMARY_BLACK}
          >
            {translator('chain').toUpperCase()}
          </Tag>
        </Flex>
      </CoreComponents.Option>
    );
  }

  if (value === NOT_FOUND_CODE) {
    const defaultNote = translator(
      props.data?.[OMIM_G_GenName] ? 'DefaultOmimGNote' : 'DefaultOmimPNote'
    );
    return (
      <CoreComponents.Option {...(props as any)}>
        <Flex>
          <Box
            style={{ minWidth: scaleSpacePx(20) }}
            className={Classes.MENU_ITEM_LABEL}
          >
            {value}
          </Box>
          <Box auto>
            <BodyTextM>{translator('SubstituteValue')}</BodyTextM>
            <BodyTextS color={COLOR.TAG_BACKGROUND_YELLOW}>
              {defaultNote}
            </BodyTextS>
          </Box>
        </Flex>
      </CoreComponents.Option>
    );
  }

  return (
    <CoreComponents.Option {...(props as any)}>
      <Flex>
        <Box
          style={{ minWidth: scaleSpacePx(20) }}
          className={Classes.MENU_ITEM_LABEL}
        >
          {props.data?.value}
        </Box>

        <Box
          auto
          className={`${Classes.TEXT_OVERFLOW_ELLIPSIS} ${Classes.FILL}`}
        >
          {props.data?.label}
        </Box>
      </Flex>
    </CoreComponents.Option>
  );
};
const SubAdditionalInfo = ({
  info,
  handleOnBlockChange,
  parentInfo,
}: ISubAdditionalInfoProps) => {
  const [value, setValue] = useState(info.value);

  useEffect(() => {
    setValue(info.value);
  }, [info.value]);

  return (
    <React.Fragment>
      <AdditionalInfoItem
        {...info}
        value={value}
        onChange={(text: string) => {
          setValue(text);
          handleOnBlockChange(text, info.fK);
        }}
        // onFocus={(evt) => {
        //   const len = evt.currentTarget.value.length;
        //   evt.currentTarget.setSelectionRange(len, len);
        // }}
        isReadonly={!(parentInfo?.value === NOT_FOUND_CODE)}
        onCustomKeyDown={(evtKey: string, evt: KeyboardEvent) => {
          if (evtKey === 'backspace') {
            evt.stopPropagation();
          }
        }}
      />
    </React.Fragment>
  );
};
const renderSubAdditionalInfo = (props: ISubAdditionalInfoProps) => {
  if (props.info.value === undefined) {
    return null;
  }
  return <SubAdditionalInfo {...props} />;
};

export const OmimGBlock = (props: IAdditionalInfoBlockProps) => {
  const [editor] = useLexicalComposerContext();
  const now = datetimeUtil.now();
  const selectedDate = props.encounterDate || now;

  const { t: translator } = i18n.useTranslation({
    namespace: 'Sdebm',
  });

  const searchOmimGOptions = useCallback(
    async (inputQuery: string): Promise<GroupedOption[]> => {
      const omimChain: OmimGChain[] = await new Promise((resolve) => {
        editor.dispatchCommand(SEARCH_OMIMG_CHAIN_COMMAND, {
          query: inputQuery,
          setData(results) {
            resolve(results);
          },
        });
      });

      let omimG = await searchOmimG({
        query: inputQuery,
        selectedDate: selectedDate,
      });
      if (isEmpty(omimG)) {
        omimG = await searchOmimG({
          query: NOT_FOUND_CODE,
          selectedDate: selectedDate,
        });
      }

      const result: GroupedOption[] = [];

      if (!!omimChain?.length) {
        result.push({
          label: translator(LABEL_OMIMG_CHAIN),
          options: omimChain.map((e) => {
            return {
              id: e.id,
              value: e.id,
              label: e.name,
              chain: e.chain.map((omim) => ({
                ...omim,
                value: omim.code,
                [OMIM_G_GenName]:
                  omim.code === NOT_FOUND_CODE ? '' : omim.genName,
                data: omim,
              })),
            };
          }),
        });
      }

      if (!!omimG?.length) {
        result.push({
          label: translator(LABEL_OMIMG_CODES),
          options: omimG.map((e) => ({
            id: e.id,
            value: e.code,
            label: e.genName,
            code: e.code,
            [OMIM_G_GenName]: e.code === NOT_FOUND_CODE ? '' : e.genName,
            data: e,
          })),
        });
      }

      return result;
    },
    [editor]
  );

  const OmimGCustomMenuList = (listProps) => (
    <AdditionalInfoMenuList
      {...listProps}
      onCreate={() => {
        editor.dispatchCommand(ADD_OMIMG_CHAIN_COMMAND, null);
      }}
      onViewAll={() => {
        editor.dispatchCommand(VIEW_ALL_OMIMG_CHAIN_COMMAND, null);
      }}
    />
  );

  return (
    <AdditionalInfoSuggestionBlock
      {...props}
      styles={SELECT_STYLE}
      components={{ Option: CustomOption, MenuList: OmimGCustomMenuList }}
      renderSubAdditionalInfo={renderSubAdditionalInfo}
      getSearchedItems={searchOmimGOptions}
      onSelect={(item) => {
        // replace omim-G if select omim-G-chain item
        if (item.chain) {
          editor.dispatchCommand(ON_SELECT_OMIM_G_CHAIN, {
            chain: item.chain,
            nodeKeyToReplace: props.nodeKey,
          });
        }
      }}
    />
  );
};

export const OmimPBlock = (props: IAdditionalInfoBlockProps) => {
  const now = datetimeUtil.now();
  const selectedDate = props.encounterDate || now;
  const searchOmimPOptions = useCallback(
    async (inputQuery: string): Promise<GroupedOption[]> => {
      return searchOmimP({
        query: inputQuery,
        selectedDate: selectedDate,
      }).then((results) => {
        if (isEmpty(results)) {
          return searchOmimP({
            query: NOT_FOUND_CODE,
            selectedDate: selectedDate,
          }).then((results) => {
            return [
              {
                label: '',
                options: results.map((r) => ({
                  id: r.id,
                  value: r.code,
                  label: r.typeOfIllness,
                  code: r.code,
                  [OMIM_P_typeOfIllness]: '',
                  data: r,
                })),
              },
            ];
          });
        }
        return [
          {
            label: '',
            options: results.map((r) => {
              return {
                id: r.id,
                value: r.code,
                label: r.typeOfIllness,
                code: r.code,
                [OMIM_P_typeOfIllness]:
                  r.code === NOT_FOUND_CODE ? '' : r.typeOfIllness,
                data: r,
              };
            }),
          },
        ];
      });
    },
    []
  );

  return (
    <AdditionalInfoSuggestionBlock
      {...props}
      components={{ Option: CustomOption }}
      renderSubAdditionalInfo={renderSubAdditionalInfo}
      getSearchedItems={searchOmimPOptions}
    />
  );
};
