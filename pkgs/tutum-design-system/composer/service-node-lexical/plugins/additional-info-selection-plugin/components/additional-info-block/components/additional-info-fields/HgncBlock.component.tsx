import React, { useCallback } from 'react';

import type SdebmI18n from '@tutum/mvz/locales/en/Sdebm.json';
import {
  BodyTextM,
  BodyTextS,
  Box,
  coreComponents as CoreComponents,
  Flex,
  Tag,
} from '@tutum/design-system/components';
import { Classes } from '@tutum/design-system/components/Core';
import { OptionProps } from 'react-select';
import AdditionalInfoItem from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-item';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { IAdditionalInfoBlockProps } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/AdditionalInfoBlock.type';
import AdditionalInfoSuggestionBlock from '../../../additional-info-suggestion-block';
import {
  GroupedOption,
  ISubAdditionalInfoProps,
  ISuggestionItem,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-suggestion-block/AdditionalInfoSuggestionBlock.component';
import { useMutationSearchHgnc } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import i18n from '@tutum/infrastructure/i18n';
import { COLOR } from '@tutum/design-system/themes/styles';
import { scaleSpacePx } from '@tutum/design-system/styles';
import AdditionalInfoMenuList from './AdditionalInfoMenuList.component';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { ADD_HGNC_CHAIN_COMMAND, ON_SELECT_HGNC_CHAIN, SEARCH_HGNC_CHAIN_COMMAND, VIEW_ALL_HGNC_CHAIN_COMMAND } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-hgnc-chain-plugins';
import { HGNC_GEN_NAME } from '../../AdditionalInfoBlock.constant';
import { isEmpty } from 'lodash';
import { HgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';

const NOT_FOUND_CODE = '999999';

const LABEL_HGNC_CHAIN = 'HgncChains';
const LABEL_HGNC_CODES = 'HgncCodes';

const CustomOption = (props: OptionProps<ISuggestionItem>) => {
  const { t: translator } = i18n.useTranslation({
    namespace: 'Sdebm',
  });
  const value = props.data?.value;
  const data = props.data as any;

  if (data?.chain) {
    return (
      <CoreComponents.Option {...(props as any)}>
        <Flex justify="space-between">
          <Flex gap={10} column>
            <Flex column className={Classes.MENU_ITEM_LABEL}>
              <BodyTextM fontWeight="Bold">{props.data.label}</BodyTextM>
            </Flex>
            <Flex column>
              {data.chain.map((e, idx) => {
                return (
                  <Flex
                    key={idx}
                    gap={5}
                    align="center"
                    mt={idx === 0 ? '0' : '8px'}
                  >
                    <Box
                      className={Classes.MENU_ITEM_LABEL}
                      style={{ minWidth: scaleSpacePx(18) }}
                    >
                      {e.code}
                    </Box>
                    <Box>{e.description}</Box>
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <Tag
            slColor={COLOR.BACKGROUND_TERTIARY_DIM}
            slStyle="fill"
            color={COLOR.TEXT_PRIMARY_BLACK}
          >
            {translator('chain').toUpperCase()}
          </Tag>
        </Flex>
      </CoreComponents.Option>
    );
  }

  if (value === NOT_FOUND_CODE) {
    const defaultNote = translator(
      props.data?.[HGNC_GEN_NAME] ? 'DefaultOmimGNote' : 'DefaultOmimPNote'
    );
    return (
      <CoreComponents.Option {...(props as any)}>
        <Flex>
          <Box
            style={{ minWidth: scaleSpacePx(20) }}
            className={Classes.MENU_ITEM_LABEL}
          >
            {value}
          </Box>
          <Box auto>
            <BodyTextM>{translator('SubstituteValue')}</BodyTextM>
            <BodyTextS color={COLOR.TAG_BACKGROUND_YELLOW}>
              {defaultNote}
            </BodyTextS>
          </Box>
        </Flex>
      </CoreComponents.Option>
    );
  }

  return (
    <CoreComponents.Option {...(props as any)}>
      <Flex>
        <Box
          style={{ minWidth: scaleSpacePx(20) }}
          className={Classes.MENU_ITEM_LABEL}
        >
          {props.data?.value}
        </Box>

        <Box
          auto
          className={`${Classes.TEXT_OVERFLOW_ELLIPSIS} ${Classes.FILL}`}
        >
          {props.data?.label}
        </Box>
      </Flex>
    </CoreComponents.Option>
  );
};

const HgncBlock = (props: IAdditionalInfoBlockProps) => {
  const { mutateAsync: searchHgnc } = useMutationSearchHgnc();
  const [editor] = useLexicalComposerContext();
  const now = datetimeUtil.now();
  const selectedDate = props.encounterDate || now;
  const { t: translator } = i18n.useTranslation({
    namespace: 'Sdebm',
  });

  const renderSubAddtionalInfo = (props: ISubAdditionalInfoProps) => {
    const { info, handleOnBlockChange } = props;

    if (
      props.parentInfo?.fK === '5077' &&
      props.parentInfo?.value === NOT_FOUND_CODE
    ) {
      return (
        <AdditionalInfoItem
          {...props.info}
          fK={info.fK}
          tabSelectsValue={false}
          value={''}
          onChange={(text: string) => {
            handleOnBlockChange(text, info.fK);
          }}
        />
      );
    }

    return null;
  };

  const searchHgncOptions = useCallback(
    async (inputQuery: string): Promise<GroupedOption[]> => {
      const hgncChain: HgncChain[] = await new Promise((resolve) => {
        editor.dispatchCommand(SEARCH_HGNC_CHAIN_COMMAND, {
          query: inputQuery,
          setData(results) {
            resolve(results);
          },
        });
      });

      let hgncG = await searchHgnc({
        query: inputQuery,
        selectedDate: selectedDate,
      });
      if (isEmpty(hgncG.data?.items)) {
        hgncG = await searchHgnc({
          query: NOT_FOUND_CODE,
          selectedDate: selectedDate,
        });
      }
      const result: GroupedOption[] = [];

      if (!!hgncChain?.length) {
        result.push({
          label: translator(LABEL_HGNC_CHAIN),
          options: hgncChain.map((e) => {
            return {
              id: e.id,
              value: e.id,
              label: e.name,
              chain: e.hgncItems.map((hgnc) => ({
                ...hgnc,
                value: hgnc.code,
                description: hgnc.description,
                [HGNC_GEN_NAME]:
                  hgnc.code === NOT_FOUND_CODE ? '' : hgnc.description,
                data: hgnc,
              })),
            };
          }),
        });
      }

      if (!!hgncG.data?.items?.length) {
        result.push({
          label: translator(LABEL_HGNC_CODES),
          options: hgncG.data.items.map((e) => ({
            id: e.description,
            value: e.description,
            label: "",
            code: e.code,
            [HGNC_GEN_NAME]: e.code === NOT_FOUND_CODE ? '' : e.description,
            data: e,
          })),
        });
      }

      return result;
    },
    [editor]
  );


  const HgncCustomMenuList = (listProps) => (
    <AdditionalInfoMenuList
      {...listProps}
      onCreate={() => {
        editor.dispatchCommand(ADD_HGNC_CHAIN_COMMAND, null);
      }}
      onViewAll={() => {
        editor.dispatchCommand(VIEW_ALL_HGNC_CHAIN_COMMAND, null);
      }}
    />
  );

  return (
    <AdditionalInfoSuggestionBlock
      {...props}
      components={{
        MenuList: HgncCustomMenuList,
        Option: CustomOption,
        SingleValue: (props) => {
          return props.hasValue ? (
            <span
              style={{
                maxWidth: 400,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'pre',
                display: 'inline-block',
              }}
            >
              {props.data.value}
            </span>
          ) : (
            <CoreComponents.SingleValue {...props} />
          );
        },
      }}
      renderSubAdditionalInfo={renderSubAddtionalInfo}
      getSearchedItems={searchHgncOptions}
      onSelect={(item) => {
        if (item.chain) {
          editor.dispatchCommand(ON_SELECT_HGNC_CHAIN, {
            chain: item.chain,
            nodeKeyToReplace: props.nodeKey,
          });
        }
      }}
    />
  );
};

export default HgncBlock;
