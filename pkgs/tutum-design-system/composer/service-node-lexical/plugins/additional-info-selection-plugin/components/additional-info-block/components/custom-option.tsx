import React from 'react';
import { OptionProps } from 'react-select';

import { Tooltip, Position, Text } from '@tutum/design-system/components/Core';
import {
  BodyTextM,
  coreComponents as CoreComponents,
  Flex,
  Tag as SLTag,
} from '@tutum/design-system/components';
import { MenuOption } from './MenuOption.styled';
import i18n from '@tutum/infrastructure/i18n';
import { ISuggestionItem } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-suggestion-block/AdditionalInfoSuggestionBlock.component';
import { COLOR } from '@tutum/design-system/themes/styles';

export const CustomOption = (props: OptionProps<ISuggestionItem>) => {
  const { t } = i18n.useTranslation({
    namespace: 'Sdebm',
  });

  return (
    <CoreComponents.Option {...(props as any)}>
      <MenuOption justify="flex-start" style={{ height: 'auto' }}>
        <BodyTextM className="label__code">{props.data?.value}</BodyTextM>
        <Tooltip
          className="label__desc-wrapper"
          usePortal={false}
          position={Position.TOP}
          interactionKind="hover-target"
          content={<small>{props.data?.label}</small>}
        >
          <Flex style={{ flexDirection: 'column' }}>
            <BodyTextM style={{ maxHeight: 40, overflow: 'hidden' }}>
              {props.data?.label}
            </BodyTextM>
            {props.data?.recommanded && (
              <BodyTextM>
                <SLTag slColor={COLOR.POSITIVE_LIGHT} slStyle="fill">
                  <Text
                    style={{
                      color: COLOR.POSITIVE_PRESSED,
                      fontWeight: 'Bold',
                      display: 'inline',
                    }}
                  >
                    {t('TagRecommanded')}
                  </Text>
                </SLTag>
              </BodyTextM>
            )}
          </Flex>
        </Tooltip>
      </MenuOption>
    </CoreComponents.Option>
  );
};
