import React from 'react';

import {
  BodyTextM,
  coreComponents as CoreComponents,
  Flex,
  Tag as SLTag,
} from '@tutum/design-system/components';
import { Tooltip, Position, Text } from '@tutum/design-system/components/Core';
import AdditionalInfoItem from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-item';
import { MenuOption } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/components/MenuOption.styled';
import CustomSelectMenuList from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/custom-select-menu-list';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { useQueryGetRecommandedOpsByCode } from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdebm';
import stringUtil from '@tutum/infrastructure/utils/string.util';
import { IAdditionalInfoBlockProps } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/AdditionalInfoBlock.type';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import AdditionalInfoSuggestionBlock from '../../../additional-info-suggestion-block';
import { ISubAdditionalInfoProps } from '../../../additional-info-suggestion-block/AdditionalInfoSuggestionBlock.component';
import i18n from '@tutum/infrastructure/i18n';
import { COLOR } from '@tutum/design-system/themes/styles';
import { useMutationSearchOps } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';

export enum LocalizationOps {
  Left = 'L',
  Right = 'R',
  BothSides = 'B',
}

const renderLocalization = (props: ISubAdditionalInfoProps) => {
  const { info, handleOnBlockChange } = props;
  return (
    <AdditionalInfoItem
      {...props.info}
      fK={info.fK}
      tabSelectsValue={false}
      value={props.info.value || ''}
      dataSourceValues={[
        LocalizationOps.Right,
        LocalizationOps.Left,
        LocalizationOps.BothSides,
      ]}
      onChange={(text: string) => {
        handleOnBlockChange(text, info.fK);
      }}
    />
  );
};

const OpsBlock = (props: IAdditionalInfoBlockProps) => {
  const { t } = i18n.useTranslation({
    namespace: 'Sdebm',
  });
  const { useGetLoggedInUserProfile } = GlobalContext.useContext();
  const documentedDoctor = useGetLoggedInUserProfile();
  const { data, isSuccess } = useQueryGetRecommandedOpsByCode({
    code: props.encounterService.code,
    encounterDate: props.encounterDate || datetimeUtil.now(),
    organizationId: stringUtil.getKVRegion(documentedDoctor?.bsnr),
  });

  const { mutateAsync: searchOps } = useMutationSearchOps();

  const recommandedOpsList = isSuccess
    ? data?.data?.map((ops) => ({
      id: ops.code,
      label: ops.description,
      value: ops.code,
    }))
    : [];

  const CustomOption = (props) => {
    const hasRecommand = recommandedOpsList?.some(
      (ops) => ops.value === props.data?.value
    );

    return (
      <CoreComponents.Option {...props}>
        <MenuOption justify="flex-start" style={{ height: 'auto' }}>
          <BodyTextM className="label__code">{props.data?.value}</BodyTextM>
          <Tooltip
            className="label__desc-wrapper"
            usePortal={false}
            position={Position.TOP}
            interactionKind="hover-target"
            content={<small>{props.data?.label}</small>}
          >
            <Flex style={{ flexDirection: 'column' }}>
              <BodyTextM style={{ maxHeight: 40, overflow: 'hidden' }}>
                {props.data?.label}
              </BodyTextM>
              {hasRecommand && (
                <BodyTextM>
                  <SLTag slColor={COLOR.POSITIVE_LIGHT} slStyle="fill">
                    <Text
                      style={{
                        color: COLOR.POSITIVE_PRESSED,
                        fontWeight: 'Bold',
                        display: 'inline',
                      }}
                    >
                      {t('TagRecommanded')}
                    </Text>
                  </SLTag>
                </BodyTextM>
              )}
            </Flex>
          </Tooltip>
        </MenuOption>
      </CoreComponents.Option>
    );
  };

  return (
    <AdditionalInfoSuggestionBlock
      {...props}
      components={{
        MenuList: CustomSelectMenuList,
        Option: CustomOption,
        SingleValue: (props) => {
          return props.hasValue ? (
            <span
              style={{
                maxWidth: 400,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'pre',
                display: 'inline-block',
              }}
            >
              {props.data.value}-{props.data.label}&nbsp;
            </span>
          ) : (
            <CoreComponents.SingleValue {...props} />
          );
        },
      }}
      renderSubAdditionalInfo={renderLocalization}
      getSearchedItems={async (query) => {
        const { data } = await searchOps({
          query,
          selectedDate: props.encounterDate || datetimeUtil.now(),
        });
        if (!data?.items) {
          return [];
        }

        return data.items.map((_ops) => ({
          id: _ops.code,
          label: _ops.description,
          value: _ops.code,
        }));
      }}
    />
  );
};

export default OpsBlock;
