
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { BodyTextM, Box, Flex } from '@tutum/design-system/components';
import { IMenuItem, coreComponents } from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';
import I18n from '@tutum/infrastructure/i18n';
import { COMMAND_PRIORITY_EDITOR, NodeKey } from 'lexical';
import React, { PropsWithChildren } from 'react';
import type { MenuListProps, OptionProps } from 'react-select';

import {
  DELETED_JUSTIFICATION_COMMAND,
  REQUEST_ADD_JUSTIFICATION_COMMAND,
  SEARCH_JUSTIFICATION_COMMAND,
  SET_JUSTIFICATION_VALUE_COMMAND,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import { IAdditionalInfo } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-plugin';
import type { IAdditionalInfoBlockProps } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/AdditionalInfoBlock.type';
import AdditionalInfoSuggestionBlock from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-suggestion-block';
import { useAdditionalInfoBlockNode } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/hooks/use-additional-info-block';
import TrashIcon from '@tutum/mvz/public/images/trash-bin-red.svg';

export interface JustificationBlockProps extends IAdditionalInfoBlockProps {
  nodeKey: NodeKey;
  info?: IAdditionalInfo;
  autoFocus?: boolean;
}

interface JustificationCustomMenuListProps
  extends PropsWithChildren<
    MenuListProps<
      IMenuItem,
      false,
      {
        options: IMenuItem[];
      }
    >
  > {
  onCreateJustificationClick: () => void;
}

const JustificationCustomMenuList: React.FC<
  JustificationCustomMenuListProps
> = (props) => {
  const { t: tNavigation } = I18n.useTranslation({
    namespace: 'Common',
    nestedTrans: 'NavigationSuggest',
  });

  const { t: tMaterialCostButtonActions } = I18n.useTranslation({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { onCreateJustificationClick } = props;

  return (
    <coreComponents.MenuList {...props}>
      <div
        style={{
          fontSize: '12px',
          marginTop: '-6px',
          padding: '12px 0px 12px 12px',
          fontFamily: 'Work Sans',
          color: COLOR.TEXT_PRIMARY_BLACK,
          background: COLOR.BACKGROUND_SECONDARY_SHINE,
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
        }}
      >
        {tNavigation('listNavigation')}
      </div>
      {props.children}
      <Flex
        w="100%"
        className="group-action"
        justify="space-between"
        px={16}
        pt={10}
        pb={8}
      >
        <span
          style={{
            cursor: 'pointer',
            fontWeight: 600,
            color: COLOR.BACKGROUND_SELECTED_STRONG,
          }}
          onClick={onCreateJustificationClick} // TODO: onCreateJustificationClick
        >
          {tMaterialCostButtonActions('create')}
        </span>
      </Flex>
    </coreComponents.MenuList>
  );
};

const JustificationBlock = (props: IAdditionalInfoBlockProps) => {
  const [editor] = useLexicalComposerContext();

  const { handleOnBlockChange, setFocus } = useAdditionalInfoBlockNode(
    props.nodeKey,
    props.autoFocus
  );

  React.useEffect(
    () =>
      editor.registerCommand(
        SET_JUSTIFICATION_VALUE_COMMAND,
        (payload) => {
          if (
            payload != null &&
            payload.nodeKey != null &&
            payload.newJustification != null &&
            payload.nodeKey === props.nodeKey
          ) {
            const _newJustification = payload.newJustification;
            handleOnBlockChange(_newJustification);
            editor.focus();
            return true;
          }
          return false;
        },
        COMMAND_PRIORITY_EDITOR
      ),
    [editor, props.nodeKey]
  );

  const CustomOption = (p: OptionProps<any, false, any>) => {
    return (
      <coreComponents.Option
        {...p}
        innerProps={{ ...p.innerProps, onClick: () => { } }}
      >
        <Box px={0}>
          <Flex justify="space-between">
            <Flex w="100%" onClick={p.innerProps.onClick}>
              <BodyTextM>{p.data?.label}</BodyTextM>
            </Flex>
            <Flex
              onClick={() => {
                editor.dispatchCommand(DELETED_JUSTIFICATION_COMMAND, {
                  nodeKey: props.nodeKey,
                  value: p.data?.value,
                });
                setFocus(false);
              }}
            >
              <TrashIcon />
            </Flex>
          </Flex>
        </Box>
      </coreComponents.Option>
    );
  };
  const CustomMenuList = (listProps) => (
    <JustificationCustomMenuList
      {...listProps}
      onCreateJustificationClick={() =>
        editor.dispatchCommand(REQUEST_ADD_JUSTIFICATION_COMMAND, props.nodeKey)
      }
    />
  );
  return (
    <AdditionalInfoSuggestionBlock
      {...props}
      components={{ Option: CustomOption, MenuList: CustomMenuList }}
      getSearchedItems={(query) => {
        return new Promise((resolve) => {
          editor.dispatchCommand(SEARCH_JUSTIFICATION_COMMAND, {
            query,
            setData: (results) => {
              const menuItems = results.map((m) => ({
                id: m,
                label: m,
                value: m,
                data: m,
              }));
              resolve(menuItems);
            },
          });
        });
      }}
    />
  );
};

export default JustificationBlock;
