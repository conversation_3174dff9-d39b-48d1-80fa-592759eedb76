import { $getRoot, $getSelection, LexicalNode } from 'lexical';
import { isEmpty, isNil, once } from 'lodash';
import _debounce from 'lodash/debounce';
import React, { useEffect, useRef, useState } from 'react';
import {
  GroupBase,
  OptionProps,
  OptionsOrGroups,
  StylesConfig,
  components,
} from 'react-select';

import type { LexicalEditor, NodeKey } from 'lexical';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { Flex, IMenuItemWithData } from '@tutum/design-system/components';
import { SelectRefType } from '@tutum/design-system/composer/add-info-lexical/components/async-additional-info-suggest/AsyncAdditionalInfoSuggest.component';
import { IAdditionalInfo } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-plugin';
import { StyledAddInfoBlock } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/AdditionalInfoBlock.styled';
import { IAdditionalInfoBlockProps } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block/AdditionalInfoBlock.type';
import AsyncAdditionalInfoSuggest from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/async-additional-info-suggest';
import { useAdditionalInfoBlockNode } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/hooks/use-additional-info-block';
import i18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export interface ISuggestionItem {
  id: string | number;
  value: string | number;
  label: string;
  recommanded?: boolean;
  description?: string;
  isDisabled?: boolean;
}

export interface GroupedOption {
  label: string;
  options: ISuggestionItem[];
}

export interface ISubAdditionalInfoProps {
  info: IAdditionalInfo;
  selectedMenuItem: ISuggestionItem;
  handleOnBlockChange: (
    newValue: string,
    childFk?: string,
    cb?: (node: LexicalNode) => void
  ) => void;
  parentInfo?: IAdditionalInfo;
}
export interface IAdditionalInfoSuggestionBlockProps {
  nodeKey: NodeKey;
  className?: string;
  info?: IAdditionalInfo;
  autoFocus?: boolean;
  currentFocusElement?: string;
  allowFreeText?: boolean;
  defaultItem?: ISuggestionItem;
  renderSubAdditionalInfo?(ISubAdditionalInfoProps): React.ReactNode;
  getRecommandedItems?(): Promise<ISuggestionItem[]>;
  getSearchedItems(query: string): Promise<ISuggestionItem[] | GroupedOption[]>;
  onSelect?: (item: ISuggestionItem) => void;
  onTab?: (event?: KeyboardEvent) => boolean;
  styles?: StylesConfig<unknown, boolean, GroupBase<unknown>>;
  options?: OptionsOrGroups<
    IMenuItemWithData<object>,
    {
      options: IMenuItemWithData<object>[];
    }
  >;
}

const AdditionalInfoSuggestionBlock: React.FC<
  IAdditionalInfoSuggestionBlockProps & IAdditionalInfoBlockProps
> = (props) => {
  const {
    isNodeSelected,
    setBlockRef,
    handleOnBlockChange,
    handleSubmit,
    setFirstInputRef,
    focusOnFirstInput,
  } = useAdditionalInfoBlockNode(props.nodeKey, props.autoFocus);
  const [editor] = useLexicalComposerContext();

  const { t: translator } = i18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const inputIdRef = useRef<string>(`${datetimeUtil.now()}`);
  const selectRef = React.useRef<SelectRefType<any> | null>(null);

  const [isLoading, setLoading] = useState(false);
  const [query, setQuery] = useState('');
  const [selectedMenuItem, setSelectedMenuItem] =
    useState<ISuggestionItem | null>();
  const [suggestions, setSuggestions] = useState<
    ISuggestionItem[] | GroupedOption[]
  >([]);
  const [recommandedList, setRecommandedList] = useState<ISuggestionItem[]>([]);

  const getRecommandedList = once(async () => {
    const items = (await props.getRecommandedItems?.()) ?? [];
    return items;
  });

  useEffect(() => {
    focusOnFirstInput();
    getRecommandedList?.().then((list) => setRecommandedList(list));
    if (!isNil(props.defaultItem)) {
      setSelectedMenuItem(props.defaultItem);
      handleOnBlockChange?.(String(props.defaultItem.value));
    }
  }, []);

  const defaultValue = {
    id: '',
    label: '',
    value: props.info?.value || '',
    data: null,
  };

  const loadOptions = async (
    inputQuery: string,
    setInternalDatasource: (
      options: ISuggestionItem[] | GroupedOption[]
    ) => void
  ) => {
    try {
      setLoading(true);
      // NOTE: show recommended
      if (isEmpty(inputQuery)) {
        const recommendList = await getRecommandedList();
        setInternalDatasource(recommendList);
        return recommendList;
      }

      // NOTE: mark recommended into search result
      const items = await props.getSearchedItems(inputQuery);
      if (props.allowFreeText && isEmpty(items)) {
        setSelectedMenuItem({ ...defaultValue, value: inputQuery });
      }
      setSuggestions(items);
      setInternalDatasource(items);
      return items;
    } catch (e) {
      console.error(e);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const debouncedLoadOptionFunc = _debounce(loadOptions, 250, {
    trailing: true,
  });

  const onItemSelected = (
    item?: Partial<ISuggestionItem> & { chain?: any }
  ) => {
    if (!item) {
      handleOnBlockChange?.('');
      props.info?.additionalInformations?.forEach((child) => {
        handleOnBlockChange?.('', child.fK);
      });
      setSelectedMenuItem({ ...defaultValue, value: '' });
      return;
    }
    // no need to handle for omimGChain
    if (!item.chain) {
      handleOnBlockChange?.(String(item?.value));
    }
    props.info?.additionalInformations?.forEach((child) => {
      handleOnBlockChange?.(item[child.fK], child.fK);
    });
    setSelectedMenuItem(item as ISuggestionItem);
    props.onSelect?.(item as ISuggestionItem);
    if (props.info?.fK === '5077' && item.value === '999999') {
      return;
    }
    $focusNextInput(editor);
  };

  const MenuList = props.components?.MenuList || components.MenuList;

  const CustomOption = (optionProps: OptionProps<ISuggestionItem>) => {
    const recommanded = recommandedList.some(
      (item) => item.value === optionProps.data?.value
    );
    const customProps = {
      ...optionProps,
      data: { ...optionProps.data, recommanded: recommanded },
    } as any;
    const Option = props.components?.Option || components.Option;

    return <Option {...customProps} />;
  };
  const isOpenMenu =
    !isEmpty(query) ||
    (!isEmpty(recommandedList) && isEmpty(props.info?.value));

  return (
    <StyledAddInfoBlock
      tabIndex={datetimeUtil.now()}
      ref={setBlockRef}
      className={isNodeSelected ? '--is-focus' : ''}
    >
      <>&#40;&nbsp;{translator(props.info?.fK || '')}:&nbsp;</>
      <AsyncAdditionalInfoSuggest
        isOptionDisabled={(option) => !!option.isDisabled}
        isLoading={isLoading}
        defaultOptions
        autoFocus={false}
        tabSelectsValue={false}
        openMenuOnFocus={false}
        loadOptions={debouncedLoadOptionFunc}
        options={props?.options}
        value={selectedMenuItem as ISuggestionItem}
        inputValue={props.info?.value}
        onInputChange={(inputQuery) => {
          setQuery(inputQuery);
          handleOnBlockChange?.(inputQuery);
        }}
        onChange={(item) => {
          onItemSelected(item || undefined);
        }}
        getOptionLabel={() => String(selectedMenuItem?.value)}
        components={{
          Option: CustomOption,
          MenuList: MenuList,
        }}
        styles={props?.styles as any}
        menuIsOpen={isOpenMenu && isNodeSelected}
        inputId={inputIdRef.current}
        getSelectRef={(el) => {
          setFirstInputRef(el);
          selectRef.current = el;
        }}
        onEnter={(event) => {
          if (props.currentFocusElement) {
            (
              document.querySelector(
                `[data-test-id=${props.currentFocusElement}]`
              ) as HTMLElement
            ).click();
            return;
          }

          if (
            props.allowFreeText &&
            !isEmpty(props.info?.value) &&
            isEmpty(suggestions)
          ) {
            return handleSubmit(event);
          }

          if (isOpenMenu) {
            return;
          }

          if (selectedMenuItem) {
            return handleSubmit(event);
          }
        }}
        onTab={(event) => {
          if (selectRef.current?.props.menuIsOpen) {
            return props.onTab?.(event) ?? false;
          }
          return false;
        }}
        onBackspace={(_: KeyboardEvent) => {
          setSelectedMenuItem(null);
        }}
        onCustomKeyDown={(key, evt) => {
          if (key === 'escape' && isOpenMenu) {
            evt.stopPropagation();
          }
        }}
      // onFocus={(evt) => {
      //   const len = evt.currentTarget.value.length;
      //   evt.currentTarget.setSelectionRange(len, len);
      // }}
      />
      {props.info?.additionalInformations?.map((info, idx) => {
        const parentInfo = props.info;
        const component = props.renderSubAdditionalInfo?.({
          info,
          selectedMenuItem,
          handleOnBlockChange,
          parentInfo,
        });
        return <Flex key={idx}>{component && <>;&nbsp; {component}</>}</Flex>;
      })}
      <>&#41;&nbsp;</>
    </StyledAddInfoBlock>
  );
};

export default React.memo(AdditionalInfoSuggestionBlock);

function $getNearestInput(editor: LexicalEditor, nodeKey: NodeKey) {
  const elem = editor.getElementByKey(nodeKey);
  return elem?.querySelector('input');
}

function $get2thInput(editor: LexicalEditor, nodeKey: NodeKey) {
  const elem = editor.getElementByKey(nodeKey);
  return elem?.querySelectorAll('input')?.[1];
}

export function $focusNextInput(editor: LexicalEditor) {
  if (editor) {
    editor.update(() => {
      const currentSelect = $getSelection();
      const currentSelectNode = currentSelect?.getNodes()?.[0];
      if (currentSelectNode) {
        const nextInput = $get2thInput(editor, currentSelectNode.getKey());
        if (nextInput) {
          nextInput.focus();
          return;
        }
      }
      const root = $getRoot();
      if (root) {
        const lastNode = root.getLastChild();
        if (lastNode) {
          $getNearestInput(editor, lastNode.getKey())?.focus();
        }
      }
    });
  }
}
