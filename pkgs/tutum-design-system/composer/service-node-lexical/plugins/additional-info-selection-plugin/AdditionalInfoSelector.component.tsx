// @ts-nocheck
import React from 'react';
import { useState, useCallback } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { NodeKey } from 'lexical';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/catalog_sdebm_common';
import type { IAdditionalInfo } from '../additional-info-plugin/AdditionalInfoPlugin.type';
import {
  ADD_ADDITIONAL_INFO_CMD,
  CLEAR_SELECTOR_CMD,
} from '../additional-info-plugin/commands';
import AdditionalInfoSuggest from '@tutum/design-system/lexical/components/custom-select';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type ActionChainI18n from '@tutum/admin/locales/en/ActionChain.json';
import { BASE_PATH_ADMIN } from '@tutum/infrastructure/utils/string.util';
import { BodyTextM } from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';

interface IAdditionalInfoSelectorProps {
  pkey: NodeKey;
  infos: AdditionalInfoField[];
  serviceNodeKey?: NodeKey;
}

const AdditionalInfoSelector: React.FC<IAdditionalInfoSelectorProps> = (
  props
) => {
  const { pkey, infos = [] } = props;

  const [editor] = useLexicalComposerContext();

  const [selectedCommand, setSelectedCommand] = useState<IAdditionalInfo>();

  const dispatchAddCommand = useCallback(
    (cmd: IAdditionalInfo, options?: { isReadOnly: boolean }) => {
      editor.dispatchCommand(ADD_ADDITIONAL_INFO_CMD, {
        info: { ...cmd, isReadonly: options?.isReadOnly },
        selectorNodeKey: pkey,
      });
    },
    [editor, pkey]
  );

  const { t: tComposer } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tComposerAdmin } = I18n.useTranslation<
    keyof typeof ActionChainI18n.Composer
  >({
    namespace: 'ActionChain',
    nestedTrans: 'Composer',
  });

  const isInAdminApp = window.location.pathname.includes(BASE_PATH_ADMIN);
  const t = isInAdminApp ? tComposerAdmin : tComposer;

  const dispatchClearSelectorCommand = useCallback(() => {
    editor.dispatchCommand(CLEAR_SELECTOR_CMD, {
      nodeKeyToClear: pkey,
    });
  }, [editor, pkey]);

  return (
    <>
      <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>(</BodyTextM>
      <BodyTextM color={COLOR.TEXT_INFO} fontWeight="SemiBold">
        +
      </BodyTextM>
      <AdditionalInfoSuggest
        placeholder={t('additionalInfo')}
        preventMenuOpenDefault={true}
        // innerInputRef={(_, inputEl) => {
        //   if (!props.serviceNodeKey) {
        //     inputEl?.focus();
        //   }
        // }}
        items={infos}
        tabSelectsValue={false}
        selectedValue={
          selectedCommand
            ? {
              label: selectedCommand.label,
              value: selectedCommand.value,
            }
            : null
        }
        onItemSelect={(item: IAdditionalInfo) => {
          setSelectedCommand(item);
          dispatchAddCommand(item);
        }}
        onCustomKeyDown={(key, e, currentQuery) => {
          if (['backspace', 'delete'].includes(key) && !currentQuery) {
            e.preventDefault();
            e.stopPropagation();
            dispatchClearSelectorCommand();
          }
        }}
      />
      <BodyTextM style={{ marginLeft: -6 }} color={COLOR.TEXT_SECONDARY_NAVAL2}>
        ),
      </BodyTextM>
    </>
  );
};

export default AdditionalInfoSelector;
