import React from 'react';
import { $createTextNode, LexicalNode } from 'lexical';
import {
  TextModuleTypeaheadOption,
  TextModuleTypeaheadPlugin,
} from '@tutum/design-system/textmodule/plugins/TextModuleTypeahead';
import {
  TextModuleNode,
  TextModuleNodeType,
  TextModuleUseFor,
} from '@tutum/hermes/bff/text_module_common';
import { TextModule } from '@tutum/hermes/bff/text_module_common';
import { BodyTextM } from '@tutum/design-system/components';
import { $isQuestionnaireNode } from '@tutum/design-system/textmodule/nodes/QuestionnaireNode';
import { $isPlaceholderInputNode } from '@tutum/design-system/textmodule/nodes/PlaceholderInputNode';
import { IAdditionalInfo } from './additional-info-plugin/AdditionalInfoPlugin.type';
import {
  DataType,
  Field,
  InputType,
} from '@tutum/hermes/bff/catalog_sdebm_common';
import CompoundMenuList from '@tutum/design-system/components/CompoundMenuList';
import { $createAdditionalInfoBlockNode } from './additional-info-selection-plugin/AdditionalInfoBlock.node';
import { EncounterServiceTimeline } from '@tutum/hermes/bff/legacy/repo_encounter';
import { COLOR } from '@tutum/design-system/themes/styles';

export const OMIMG_CONFIG: Field = {
  fK: '5070',
  label: 'OMIM-G-Code of the investigated Gens',
  minLength: undefined,
  maxLength: undefined,
  isMany: true,
  dataType: DataType.DataTypeInt,
  inputType: InputType.InputTypeDropDownListApi,
  dataSource: 'omim-p-code.',
  dataSourceValues: [],
  isRequired: true,
  treatmentCaseWithRule: [],
  additionalInformations: [
    {
      fK: '5072',
      label: 'Gen-Name',
      minLength: 0,
      maxLength: 60,
      isMany: true,
      dataType: DataType.DataTypeString,
      inputType: InputType.InputTypeTextInput,
      dataSource: '',
      dataSourceValues: [],
      isRequired: true,
      treatmentCaseWithRule: [],
      additionalInformations: [],
    },
  ],
};

function _parseTextModuleNode(data: TextModuleNode): LexicalNode | null {
  switch (data.type) {
    case TextModuleNodeType.TextModuleNodeType_Text: {
      const d = data.text;
      const node = $createTextNode(d?.value);
      node.setDetail('unmergable');
      return node;
    }
    case TextModuleNodeType.TextModuleNodeType_AdditionalInfo: {
      const omimg = data.omimG;
      if (!omimg) return null;
      const omimGData: IAdditionalInfo = {
        ...OMIMG_CONFIG,
        value: omimg.value,
        additionalInformations: [
          {
            ...OMIMG_CONFIG.additionalInformations[0],
            value: omimg.children[0].value,
          },
        ],
      };
      return $createAdditionalInfoBlockNode({
        encounterService: {} as EncounterServiceTimeline,
        info: omimGData,
      }); // TODO: set encounterDate here as a props
    }
    default:
      return null;
  }
}

export function $$transformToAdditionalInfoNodes(
  textModule: TextModule
): [LexicalNode[], LexicalNode | null] {
  let selectableNode: LexicalNode | null = null;
  const nodes = textModule.content.data.reduce<LexicalNode[]>(
    (_nodes, data) => {
      const newNode = _parseTextModuleNode(data);
      if (newNode) {
        _nodes.push(newNode);
      }
      if ($isQuestionnaireNode(newNode) || $isPlaceholderInputNode(newNode!)) {
        selectableNode = newNode;
      }
      return _nodes;
    },
    []
  );

  return [nodes, selectableNode];
}

export const OmimGChainTypeaheadPlugin = (): JSX.Element => {
  const usedForList = React.useMemo(
    () => [TextModuleUseFor.TextModuleUseFor_OmimGChain],
    []
  );

  return (
    <TextModuleTypeaheadPlugin
      usedForList={usedForList}
      createNodes={(textModuleValue) => {
        const [nodes, selectableNode] =
          $$transformToAdditionalInfoNodes(textModuleValue);
        return [nodes, selectableNode];
      }}
      isBottomHeadMenu={false}
      renderFn={(modules, { selectedIndex, selectOptionAndCleanUp }) => (
        <CompoundMenuList>
          <CompoundMenuList.Header />
          <CompoundMenuList.MenuList<TextModuleTypeaheadOption>
            items={modules}
            selectedIndex={selectedIndex}
            getItemId={(item) => item.getTextModuleValue()?.id ?? ''}
            onItemClick={(item) => selectOptionAndCleanUp(item)}
            renderItem={(item) => (
              <>
                <BodyTextM>{item.getTextModuleValue()?.textShortcut}</BodyTextM>
                <BodyTextM limitLines={1} color={COLOR.TEXT_TERTIARY_SILVER}>
                  {item.getTextModuleValue()?.content.text}
                </BodyTextM>
              </>
            )}
          />
        </CompoundMenuList>
      )}
    />
  );
};
