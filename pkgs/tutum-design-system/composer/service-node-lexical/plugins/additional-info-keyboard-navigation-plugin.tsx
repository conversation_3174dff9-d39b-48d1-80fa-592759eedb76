import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import { $isAdditionalInfoSelectorNode } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/AdditionalInfoSelector.node';
import {
  $getSelection,
  COMMAND_PRIORITY_EDITOR,
  KEY_ARROW_LEFT_COMMAND,
  KEY_ARROW_RIGHT_COMMAND,
  KEY_TAB_COMMAND,
} from 'lexical';
import { useEffect } from 'react';
import {
  $$biDirectionalSelect,
  $$shouldSkipIfRangeTextSelection,
  $$shouldSkipInNodeSelection,
} from '../utils/lexical-caret-traversal';
import { $isAdditionalInfoBlockNode } from './additional-info-selection-plugin/AdditionalInfoBlock.node';

function cleanUpPrevFocusState() {
  if (typeof window === 'undefined') return;
  if (document.activeElement?.tagName === 'INPUT') {
    (document.activeElement as HTMLInputElement).blur();
  }
}

function AdditionalInfoKeyboardNavigationPlugin() {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return mergeRegister(
      editor.registerCommand<KeyboardEvent>(
        KEY_TAB_COMMAND,
        (_event, _editor) => {
          const isBackward = _event.shiftKey;
          const selection = $getSelection();
          let focusingNode = selection?.getNodes()?.[0];
          if ($isAdditionalInfoSelectorNode(focusingNode)) {
            focusingNode = selection?.getNodes()?.[1];
          }
          if (!focusingNode) {
            return false;
          }
          let tabInAdditionalBlock = false;
          if ($isAdditionalInfoBlockNode(focusingNode)) {
            const nextIsAdditionalInfo =
              $isAdditionalInfoBlockNode(
                focusingNode.getNextSibling()?.getNextSibling()
              ) || $isAdditionalInfoBlockNode(focusingNode.getNextSibling());
            const prevIsAdditionalInfo =
              $isAdditionalInfoBlockNode(
                focusingNode.getPreviousSibling()?.getPreviousSibling()
              ) ||
              $isAdditionalInfoBlockNode(focusingNode.getPreviousSibling());
            const focusOnForwardAdditionalField =
              !isBackward && nextIsAdditionalInfo;
            const focusOnBackwardAdditionalField =
              isBackward && prevIsAdditionalInfo;
            if (
              focusOnForwardAdditionalField ||
              focusOnBackwardAdditionalField
            ) {
              tabInAdditionalBlock = true;
            }

            // NOTE: focusing on middle child-field
            const els = focusingNode.getTabbaleElements();
            if (
              els[els.length - 1] !== document.activeElement &&
              els[0] !== document.activeElement
            ) {
              tabInAdditionalBlock = true;
            }
          }
          cleanUpPrevFocusState();
          _editor.update(() => {
            // NOTE: no handle tab in block node
            if (tabInAdditionalBlock) {
              return;
            }

            $$biDirectionalSelect(_event, focusingNode, isBackward);
          });
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand<KeyboardEvent>(
        KEY_ARROW_RIGHT_COMMAND,
        (_event, _editor) => {
          _editor.update(() => {
            const selection = $getSelection();
            const isBackward = _event.shiftKey;
            const focusingNode = selection?.getNodes()?.[0];
            if ($$shouldSkipIfRangeTextSelection(selection, isBackward)) {
              return true;
            }
            if ($$shouldSkipInNodeSelection(_event, selection, isBackward)) {
              return true;
            }
            cleanUpPrevFocusState();
            $$biDirectionalSelect(_event, focusingNode!, isBackward);
          });
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand<KeyboardEvent>(
        KEY_ARROW_LEFT_COMMAND,
        (_event, _editor) => {
          _editor.update(() => {
            const selection = $getSelection();
            const isBackward = !_event.shiftKey;
            const focusingNode = selection?.getNodes()?.[0];
            if ($$shouldSkipIfRangeTextSelection(selection, isBackward)) {
              return true;
            }
            if ($$shouldSkipInNodeSelection(_event, selection, isBackward)) {
              return true;
            }
            cleanUpPrevFocusState();
            $$biDirectionalSelect(_event, focusingNode!, isBackward);
          });
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      )
    );
  }, [editor]);

  return null;
}

export default AdditionalInfoKeyboardNavigationPlugin;
