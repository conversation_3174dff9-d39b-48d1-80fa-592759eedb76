
import {
  $createTextNode,
  $getNodeByKey,
  $getRoot,
  $isElementNode,
  $getSelection,
  $isNodeSelection,
  ElementNode,
  $setSelection,
} from 'lexical';
import dateUtils from '@tutum/infrastructure/utils/datetime.util';
import ADDITIONAL_INFOS from '@tutum/design-system/composer/assets/additional-info.json';
import type { NodeKey } from 'lexical';
import type { IAdditionalInfo } from './AdditionalInfoPlugin.type';
import { $$shouldSkipInNodeSelection } from '../../utils/lexical-caret-traversal';
import {
  DATE_FORMAT_BILLABLE,
  DATE_FORMAT_WITHOUT_DOTS,
} from '@tutum/infrastructure/shared/date-format';
import {
  $createAdditionalInfoBlockNode,
  $isAdditionalInfoBlockNode,
} from '../additional-info-selection-plugin/AdditionalInfoBlock.node';
import { EncounterServiceTimeline } from '@tutum/hermes/bff/legacy/repo_encounter';

const FK_OP_DATE = '5034';
const FK_RECORDING_DATE = '5025';
const FK_DATE_OF_DISCHARGE = '5026';

export interface IAutoFillInfo {
  fK: string;
  value: string;
}

function _pickAddInfoFromSource(k: string): IAdditionalInfo {
  return (ADDITIONAL_INFOS as IAdditionalInfo[]).find((info) => info.fK === k);
}

function _insertAddInfoNode(parent: ElementNode, fk: string, newValue: string) {
  const info = _pickAddInfoFromSource(fk);
  if (!info) return;
  const newInfo = Object.assign({}, { ...info, value: newValue });
  const newNode = $createAdditionalInfoBlockNode({
    encounterService: {} as EncounterServiceTimeline,
    info: newInfo,
  });
  parent.append(newNode, $createTextNode(' '));
}

function _formatToString(
  dateValue: string,
  fromFormat: string,
  toFormat: string
): string {
  const dateMoment = dateUtils.dateToMoment(dateValue, fromFormat);
  if (dateMoment?.isValid()) {
    return dateMoment.format(toFormat);
  }
  return dateValue;
}

function autofillDoctorInfoIfItIsEmpty(
  autoFillInfos: IAutoFillInfo[]
): boolean {
  const rootNode = $getRoot();
  const firstChild = rootNode.getFirstChild();
  if (!$isElementNode(firstChild)) {
    return false;
  }
  const childrenSize = firstChild.getChildrenSize();
  if (childrenSize !== 0) return false;
  // NOTE: for KV only
  autoFillInfos.forEach((item) => {
    _insertAddInfoNode(firstChild, item.fK, item.value);
  });
  return true;
}

function shouldConvertToBillableString(
  info: Partial<IAdditionalInfo>
): boolean {
  return (
    [FK_OP_DATE, FK_RECORDING_DATE, FK_DATE_OF_DISCHARGE].includes(info?.fK) &&
    info?.value &&
    info?.inputType === 'DateInput'
  );
}

function convertToBillableDateString(dateValue: string): string {
  return _formatToString(
    dateValue,
    DATE_FORMAT_WITHOUT_DOTS,
    DATE_FORMAT_BILLABLE
  );
}

function convertToReadableDateString(dateValue: string): string {
  return _formatToString(
    dateValue,
    DATE_FORMAT_BILLABLE,
    DATE_FORMAT_WITHOUT_DOTS
  );
}

export function $removeAdditionalInfoBlock(
  event: KeyboardEvent,
  nodeKey: NodeKey
): boolean {
  const nodeToRemove = $getNodeByKey(nodeKey);

  if (!$isAdditionalInfoBlockNode(nodeToRemove)) {
    return false;
  }

  const selection = $getSelection();

  if (!$isNodeSelection(selection)) {
    return false;
  }

  if (!selection.has(nodeKey)) {
    return false;
  }

  const shouldDelete = !$$shouldSkipInNodeSelection(event, selection, true);

  if (!shouldDelete) {
    return false;
  }

  nodeToRemove.remove();
  return true; // NOTE: stop propagation
}

export default {
  autofillDoctorInfo: autofillDoctorInfoIfItIsEmpty,
  shouldConvertToBillableString,
  convertToBillableDateString,
  convertToReadableDateString,
};
