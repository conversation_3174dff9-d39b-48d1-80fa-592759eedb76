
import React, { PropsWithChildren } from 'react';

// import types
import type { IAdditionalInfoPlainEditorProps } from './AdditionalInfoPlainEditor.type';
import type { IServiceBlock } from '../Composer.type';

import PlainTextEditor from '@tutum/design-system/lexical/components/PlainTextEditor';
import { parseEditorStateToSubmitableData } from './plugins/additional-info-enter-submit-plugin';

// import config
import editorConfig from './config';
import { styled } from '@tutum/design-system/themes';

const AdditionalInfoPlainEditor = (
  props: PropsWithChildren<
    IAdditionalInfoPlainEditorProps & {
      className?: string;
      onChange?: (
        payload: Pick<IServiceBlock, 'additionalInfos' | 'additionalInfosRaw'>
      ) => void;
    }
  >
) => (
  <div className={props.className}>
    <PlainTextEditor
      editorConfig={editorConfig}
      initEditorState={props.defaultEditorState}
      placeholder={props.placeholder}
      onChange={(_, e) => {
        props.onChange?.(parseEditorStateToSubmitableData(e));
      }}
    >
      {props.children} {/* plugins... */}
    </PlainTextEditor>
  </div>
);

const StyledAdditionalInfoPlainEditor = styled(AdditionalInfoPlainEditor)`
  .sl-lexical-plaintext-editor-wrapper {
    display: flex;
    flex-wrap: wrap;
  }
`;

export default StyledAdditionalInfoPlainEditor;
