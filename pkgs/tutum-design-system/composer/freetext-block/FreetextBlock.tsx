
import React, { useRef, useState } from 'react';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';

import { Classes } from '@tutum/design-system/components/Core';
import { Flex } from '@tutum/design-system/components';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import I18n from '@tutum/infrastructure/i18n';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import MvzTextmodule from '@tutum/design-system/textmodule';
import { AvoidLineBreakPlugin } from '@tutum/design-system/textmodule/plugins/AvoidLineBreak.plugin';
import { FreeTextLexicalNamespace } from '@tutum/design-system/composer/freetext-block/constants';

interface IFreetextBlockProps {
  fontsizeSetting: number;
  className?: string;
  disabled?: boolean;
  defaultFreeText?: string;
  onSubmit: (freeText: string) => void;
  setShowHintSubmit?: React.Dispatch<React.SetStateAction<boolean>>;
  onClear?: () => void;
  usedForList: TextModuleUseFor[];
}

const FreetextBlock: React.FC<IFreetextBlockProps> = (props) => {
  const {
    className,
    defaultFreeText,
    onSubmit,
    onClear,
    usedForList,
    setShowHintSubmit,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const [value, setValue] = useState(defaultFreeText);
  const shouldClear = useRef(false);

  const onContentChangeHandler = ({ text }: { text: string }) => {
    if (setShowHintSubmit) {
      setShowHintSubmit?.(!!text);
    }
    setValue(text);
  };

  return (
    <Flex
      className={className}
      onKeyDown={(event) => {
        switch (event.key) {
          case 'Enter': {
            if (!event.shiftKey && value?.length > 1) {
              onSubmit(value);
            }
            break;
          }
          case 'Backspace': {
            if (!value?.length && !shouldClear.current) {
              shouldClear.current = true;
            } else if (value?.length && shouldClear.current) {
              shouldClear.current = false;
            } else if (!value?.length && shouldClear.current) {
              onClear();
            }
          }
        }
      }}
    >
      <MvzTextmodule
        editorConfig={{ namespace: FreeTextLexicalNamespace }}
        usedForList={usedForList}
        defaultValue={defaultFreeText}
        placeholder={t('typeFreeText')}
        className={getCssClass(Classes.FILL, 'rowData')}
        onContentChange={onContentChangeHandler}
      >
        <AutoFocusPlugin />
        <AvoidLineBreakPlugin />
      </MvzTextmodule>
    </Flex>
  );
};

export type { IFreetextBlockProps };
export default React.memo(FreetextBlock);
