
import React, { useRef, useState, useEffect } from 'react';
import { Label, Keys, InputGroup } from '@tutum/design-system/components/Core';
import { Flex } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import { CareFacility } from '@tutum/hermes/bff/service_domains_patient_file';

export interface ICareFacilityBlockProps {
  className?: string;
  data?: CareFacility;
  disabled?: boolean;
  isInServiceChain?: boolean;
  onChange: (data: CareFacility) => void;
  onKeyDown: (data: CareFacility) => void;
}

const CareFacilityBlock: React.FC<ICareFacilityBlockProps> = (props) => {
  const { className, data, onChange, onKeyDown, disabled } = props;
  const { t } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });
  const [name, setName] = useState<string>(data?.name || '');
  const [ort, setOrt] = useState<string>(data?.ort || '');

  useEffect(() => {
    setName(data?.name);
    setOrt(data?.ort);
  }, [data]);

  const careFacilityBlockEl: any = useRef<HTMLDivElement>(null);
  const nameEl: any = useRef<HTMLDivElement>(null);
  const ortEl: any = useRef<HTMLDivElement>(null);

  const goToServiceBlock = () => {
    let serviceBlockElement = nameEl.current.parentElement;
    while (!serviceBlockElement.querySelector('.sl-ServiceBlock')) {
      serviceBlockElement = serviceBlockElement.parentElement;
    }
    const focusElement = serviceBlockElement.querySelector(
      '.sl-InputSuggestion__input'
    ) as HTMLSpanElement;
    focusElement?.focus();
  };

  const goToNameBlock = () => {
    const focusElement = careFacilityBlockEl.current.querySelector(
      '.sl-CareFacilityBlock__item__input.--name input'
    ) as HTMLSpanElement;
    focusElement?.focus();
  };

  const goToOrtBlock = () => {
    const focusElement = careFacilityBlockEl.current.querySelector(
      '.sl-CareFacilityBlock__item__input.--ort input'
    ) as HTMLSpanElement;
    focusElement?.focus();
  };

  const _onNameChange = (inputValue?: string) => {
    setName(inputValue);
    onChange({ ...data, name: inputValue, ort });
  };

  const _onOrtChange = (inputValue?: string) => {
    setOrt(inputValue);
    onChange({ ...data, ort: inputValue });
  };

  const _onNameKeyDown = (event: React.KeyboardEvent<HTMLSpanElement>) => {
    switch (event.keyCode) {
      case Keys.ENTER:
      case Keys.ARROW_RIGHT:
      case Keys.TAB:
        event.preventDefault();
        goToOrtBlock();
        break;
      case Keys.BACKSPACE:
      case Keys.ARROW_LEFT:
        if (!name) {
          goToServiceBlock();
        }
        break;
      default:
        break;
    }
  };

  const _onOrtKeyDown = (event: React.KeyboardEvent<HTMLSpanElement>) => {
    switch (event.keyCode) {
      case Keys.ENTER:
        event.preventDefault();
        onKeyDown({ ...data, name, ort });
        break;
      case Keys.BACKSPACE:
      case Keys.ARROW_LEFT:
        if (!ort) {
          goToNameBlock();
        }
        break;
      default:
        break;
    }
  };

  if (!data || !data.required) {
    return null;
  }

  return (
    <Flex ref={careFacilityBlockEl} className={className} auto>
      <Flex className={`sl-CareFacilityBlock__item ${className}`}>
        <Label className="sl-CareFacilityBlock__item__label">
          {t('careFacilityName')}
        </Label>
        <InputGroup
          inputRef={nameEl}
          value={name}
          className="sl-CareFacilityBlock__item__input --name"
          placeholder={t('careFacilityTypeName')}
          onChange={(e) => _onNameChange(e.target.value)}
          onKeyDown={_onNameKeyDown}
        />
      </Flex>
      <Flex className={`sl-CareFacilityBlock__item ${className}`}>
        <Label className="sl-CareFacilityBlock__item__label">
          {t('careFacilityOrt')}
        </Label>
        <InputGroup
          inputRef={ortEl}
          value={ort}
          className="sl-CareFacilityBlock__item__input --ort"
          placeholder={t('careFacilityTypeName')}
          disabled={disabled}
          onChange={(e) => _onOrtChange(e.target.value)}
          onKeyDown={_onOrtKeyDown}
        />
      </Flex>
    </Flex>
  );
};

export default React.memo(CareFacilityBlock, (prevProps, nextProps) => {
  const prevCareFacility = prevProps.data;
  const nextCareFacility = nextProps.data;

  return (
    prevCareFacility &&
    nextCareFacility &&
    prevCareFacility.required === nextCareFacility.required &&
    prevCareFacility.name === nextCareFacility.name &&
    prevCareFacility.ort === nextCareFacility.ort
  );
});
