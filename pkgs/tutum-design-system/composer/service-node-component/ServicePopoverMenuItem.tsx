import React, { useCallback } from 'react';
import { Flex, BodyTextM, Tag } from '@tutum/design-system/components';
import { Tooltip } from '@tutum/design-system/components/Core';
import { UnitStatistiks } from '@tutum/hermes/bff/catalog_sdebm_common';
import I18n from '@tutum/infrastructure/i18n';
import { getPositionsToHighlight } from '@tutum/infrastructure/utils/match';

import { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import { COLOR } from '@tutum/design-system/themes/styles';
import { PointValueModel } from '@tutum/hermes/bff/legacy/point_value_common';
import { SPACE_NUMBER } from '@tutum/design-system/styles';

const PriceTag = ({ price }: { price: number }) => {
  const formatedNumber = Intl.NumberFormat('de-DE').format(price);
  const evalType = {
    price: Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR',
      maximumFractionDigits: 2,
    }).format(price),
    point: formatedNumber,
  };

  return (
    <span
      style={{
        color: COLOR.TEXT_TERTIARY_SILVER,
        minWidth: '60px',
        justifySelf: 'flex-end',
        textAlign: 'right',
      }}
    >{`${evalType.price.toString()}`}</span>
  );
};

export interface ServicePopoverMenuItemProps {
  query: string;
  item: IContractData;
  pointValue?: PointValueModel;
  className?: string;
}

const ServicePopoverMenuItem = ({
  item,
  query,
  pointValue,
  className,
}: ServicePopoverMenuItemProps) => {
  const { t } = I18n.useTranslation({
    namespace: 'SvBlancServiceCode',
    nestedTrans: 'SvBlancServiceCodeComposer',
  });

  const createTitle = useCallback((query: string, item: IContractData) => {
    const highlight = item.highlight?.["description"];
    if (!highlight) {
      return (
        <Flex grow={1}>
          {item.isBlankService && item.isActive && (
            <>
              <Tooltip
                content={<span>{t('blancServiceCode')}</span>}
                position="top"
              >
                <span
                  style={{
                    backgroundColor: COLOR.WARNING_LIGHT,
                    padding: '2px 8px',
                    borderRadius: '12px',
                    marginRight: '8px',
                  }}
                >
                  bz
                </span>
              </Tooltip>
              <BodyTextM limitLines={2}>{item.description}</BodyTextM>
            </>
          )}
          {!item.isBlankService && (
            <BodyTextM limitLines={2}>{item.description}</BodyTextM>
          )}
        </Flex>
      );
    }
    return (
      <Flex grow={1} className="sl-service-title">
        <BodyTextM limitLines={2}>
          {highlight?.matchedTokens?.length && highlight?.snippet
            ? <BodyTextM limitLines={2} dangerouslySetInnerHTML={{ __html: highlight?.snippet || '' }} className="sl-highlight-text" />
            : <BodyTextM limitLines={2}>{item.description}</BodyTextM>}
        </BodyTextM>
      </Flex>
    );
  }, [query, item]);

  const showPrice = (item: IContractData) => {
    if (item.unit === UnitStatistiks.UnitStatistiks_Euros) {
      return <PriceTag price={item.evaluation || 0} />;
    } else if (item.unit === UnitStatistiks.UnitStatistiks_Points) {
      return (
        <PriceTag
          price={((item.evaluation || 0) * (pointValue?.value || 1)) / 100}
        />
      );
    }
    return null;
  };

  return (
    <Flex column gap={SPACE_NUMBER.SPACE_XS} className={className}>
      <Flex auto style={{ width: '100%' }}>
        <BodyTextM style={{ minWidth: 64 }}>{item.code}</BodyTextM>
        {createTitle(query, item)}
        {showPrice(item)}
      </Flex>
      {item.isSelfCreated && (
        <Flex pl={SPACE_NUMBER.SPACE_XXL}>
          <Tag slState="neutral" slStyle="outline">
            {t('typeSelfCreated')}
          </Tag>
        </Flex>
      )}
    </Flex>
  );
};

export default React.memo(ServicePopoverMenuItem);
