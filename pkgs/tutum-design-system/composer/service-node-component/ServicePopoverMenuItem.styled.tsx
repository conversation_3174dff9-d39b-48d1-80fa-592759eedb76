import React from 'react';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import Theme from '@tutum/mvz/theme';
import OriginalServicePopoverMenuItem, { ServicePopoverMenuItemProps } from './ServicePopoverMenuItem';
import { COLOR } from '@tutum/design-system/themes/styles';

const ServicePopoverMenuItem: React.ComponentType<ServicePopoverMenuItemProps> = Theme.styled(
  OriginalServicePopoverMenuItem
).attrs(({ className }) => ({
  className: getCssClass('sl-ServicePopoverMenuItem', className),
}))`
  .sl-highlight-text {
    font-weight: bold;

    mark {
      background-color: unset ;
      color: ${COLOR.TEXT_PRIMARY_BLACK};
      font-weight: normal;
    }
  }

`;
export default ServicePopoverMenuItem;