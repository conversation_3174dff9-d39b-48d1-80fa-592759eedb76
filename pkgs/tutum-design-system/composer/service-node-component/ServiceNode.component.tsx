import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import type { EncounterServiceTimeline } from '@tutum/hermes/bff/repo_encounter';
import type { CareFacility } from '@tutum/hermes/bff/service_domains_patient_file';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import type { SelectInstance } from 'react-select';
import _set from 'lodash/set';

import { EditorRefPlugin } from '@lexical/react/LexicalEditorRefPlugin';
import {
  Flex,
  InputSuggestion,
  coreComponents,
} from '@tutum/design-system/components';
import {
  $isAdditionalInfoBlockNode,
  AdditionalInfoBlockNode,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/AdditionalInfoBlock.node';
import {
  DEFAULT_COMPOSER_SELECT_STYLE_CONFIG,
  DEFAULT_SELECT_COMPONENT_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
import { isNotEmpty } from '@tutum/design-system/infrastructure/utils';
import { PointValueModel } from '@tutum/hermes/bff/legacy/point_value_common';
import {
  COMMAND_PRIORITY_LOW,
  EditorState,
  KEY_BACKSPACE_COMMAND,
  LexicalEditor,
} from 'lexical';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import AdditionalInfoPlainEditor from '../service-node-lexical';
import AdditionalInfoKeyEnterToSubmitPlugin from '../service-node-lexical/plugins/additional-info-enter-submit-plugin';
import AdditionalInfoKeyboardNavigationPlugin from '../service-node-lexical/plugins/additional-info-keyboard-navigation-plugin';
import AdditionalInfoSelectionPlugin from '../service-node-lexical/plugins/additional-info-selection-plugin/additional-info-selection-plugin';
import { REFERRAL_BLOCK } from '../service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { OmimGChainTypeaheadPlugin } from '../service-node-lexical/plugins/omim-g-chain-typeahead-plugin';
import CareFacilityBlock from './care-facility-block/CareFacilityBlock.styled';
import ServicePopoverMenuItem from './ServicePopoverMenuItem.styled';
import { EMPTY_EDITOR_STATE } from '../../lexical/utils';
import { useComposerActionChainStore } from '@tutum/mvz/module_action-chain';
import { isEmpty } from 'lodash';

function checkStateEmpty(state: EditorState): boolean {
  const nodes = state._nodeMap;
  for (const [_, node] of nodes) {
    if ($isAdditionalInfoBlockNode(node)) {
      return false;
    } else if (node.getTextContentSize()) {
      return false;
    }
  }
  return true;
}

// set autoFocus = false
function convertAdditionalInfoBlock(node: any) {
  if (!node) {
    return;
  }
  if (node.type === AdditionalInfoBlockNode.getType()) {
    node = _set(node, 'props.autoFocus', false);
  }
  for (let i = 0; i < node?.children?.length || 0; i++) {
    convertAdditionalInfoBlock(node.children[i]);
  }
}

export interface IServiceNodeProps {
  // OPTIONAL
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  additionalInfoEditorplaceholder?: string;
  noResultsComponent?: JSX.Element;
  rightElement?: JSX.Element;
  encounterDate?: number; // TODO: refactor later

  // MAIN PROPS
  data: any;
  additionalFields: AdditionalInfoField[];
  pointValue?: PointValueModel;
  // METHODS
  onServiceSelect: (item: IContractData) => Promise<EncounterServiceTimeline>;
  searchService: (
    query: string,
    setDatasource: (result: IContractData[]) => void
  ) => Promise<void> | undefined;
  onChange: (data: EncounterServiceTimeline) => Promise<void>;
  onSubmit(payload: EncounterServiceTimeline): Promise<void>;
  onClear: () => void;
  id: string;
}

const defaultService = {
  description: undefined,
  code: '',
  scheins: [],
};

const STYLE_CONFIG = DEFAULT_COMPOSER_SELECT_STYLE_CONFIG();

const ServiceNode: React.FC<React.PropsWithChildren<IServiceNodeProps>> = (
  props
) => {
  const {
    className,
    placeholder,
    additionalInfoEditorplaceholder,
    data = defaultService,
    additionalFields,
    disabled,
    rightElement,
    onChange,
    onSubmit,
    onClear,
    searchService,
    onServiceSelect,
    noResultsComponent,
    encounterDate, // TODO: refactor later, move out
    pointValue,
    id,
  } = props;
  const inputSuggestionEl = useRef<SelectInstance<any, boolean> | null>(null);

  const composerActionChainStore = useComposerActionChainStore();

  const setUpdatedService = (updatedService: EncounterServiceTimeline) => {
    const _service = { ...updatedService };
    if (!_service.command) {
      _service.command = data?.command || 'L';
    }

    onChange(_service);
    return _service;
  };

  const handleSelectService = async (item: IContractData) => {
    if (item.isBlankService && !item.isActive) {
      return;
    }
    const inputData = `${item.code ? `(${item.code})` : ''} ${item.description
      }`.trim();
    const result = await onServiceSelect(item);
    setUpdatedService({ ...result, freeText: inputData });
  };

  const defaultEditorState = useMemo(() => {
    if (!disabled || !data?.additionalInfosRaw) {
      return data?.additionalInfosRaw! || EMPTY_EDITOR_STATE;
    }
    const state = JSON.parse(data.additionalInfosRaw);
    convertAdditionalInfoBlock(state.root);
    return JSON.stringify(state);
  }, [data?.additionalInfosRaw!, disabled]);

  const onComposerSubmit = useCallback(
    async (
      payload: Pick<
        EncounterServiceTimeline,
        'additionalInfos' | 'additionalInfosRaw'
      >
    ) => {
      let additionalInfosRaw = JSON.parse(payload.additionalInfosRaw || '{}');
      let additionalInfos = payload.additionalInfos;
      let referralDoctorInfo = {};

      // for flow Action chain
      if (composerActionChainStore.currentBlock) {
        const {
          additionalInfosRaw: additionalInfosRawAC,
          additionalInfos: additionalInfosAC,
        } = composerActionChainStore.currentBlock;

        if (additionalInfosRawAC) {
          additionalInfosRaw = { ...JSON.parse(additionalInfosRawAC) };
        }

        if (!isEmpty(additionalInfosAC)) {
          additionalInfos = [...additionalInfosAC!];
        }
      }

      additionalInfosRaw = {
        ...additionalInfosRaw,
        root: {
          ...additionalInfosRaw.root,
          children: additionalInfosRaw.root.children.map((child) => {
            child.children = child.children.map((item) => {
              if (item.fK === REFERRAL_BLOCK) {
                referralDoctorInfo = item.value ? JSON.parse(item.value) : {};

                return {
                  ...item,
                  encounterService: {
                    ...item.encounterService,
                    referralDoctorInfo,
                  },
                };
              }
              return item;
            });
            return child;
          }),
        },
      };
      const newServiceData = setUpdatedService({
        ...data,
        referralDoctorInfo,
        additionalInfos,
        additionalInfosRaw: JSON.stringify(additionalInfosRaw),
      });
      await onSubmit(newServiceData);
    },
    [data, onSubmit, onChange]
  );

  const onCareFacilityChange = (careFacility: CareFacility) => {
    if (data?.careFacility === careFacility) {
      return;
    }

    setUpdatedService({
      ...data,
      careFacility,
    });
  };

  const onCareFacilitySubmit = (careFacility: CareFacility) => {
    if (data?.careFacility === careFacility) {
      return;
    }

    const updatedService = setUpdatedService({
      ...data,
      careFacility,
    });

    onSubmit(updatedService);
  };

  const showServiceOptions =
    isNotEmpty(data?.freeText) && isNotEmpty(data?.code);

  const editorRef = useRef<LexicalEditor>(null);

  useEffect(() => {
    if (editorRef.current) {
      const editor = editorRef.current;
      return editor.registerCommand(
        KEY_BACKSPACE_COMMAND,
        () => {
          const state = editor.getEditorState();
          if (checkStateEmpty(state)) {
            editor.setEditorState(editor.parseEditorState(EMPTY_EDITOR_STATE));
            // setTimeout so that we do not delete last character in inputSuggestionEl
            setTimeout(() => {
              inputSuggestionEl.current?.focus();
            });
            return true;
          }
          return false;
        },
        COMMAND_PRIORITY_LOW
      );
    }
  }, [editorRef, showServiceOptions, inputSuggestionEl]);

  return (
    <Flex className={className} column gap={0}>
      <Flex align="center" auto>
        <InputSuggestion
          id={id}
          keyCode="description"
          autoFocus={!disabled}
          placeholder={placeholder}
          styles={STYLE_CONFIG}
          openMenuOnClick={false}
          components={{
            ...DEFAULT_SELECT_COMPONENT_CONFIG,
            Option: (props) => {
              const {
                data,
                selectProps: { inputValue },
              } = props;

              return (
                <coreComponents.Option {...props} isSelected={false}>
                  <ServicePopoverMenuItem
                    item={data}
                    query={inputValue}
                    pointValue={pointValue}
                  />
                </coreComponents.Option>
              );
            },
          }}
          defaultInputValue={data?.freeText!}
          loadOptions={
            ((query: string, setDatasource: any) => {
              searchService(query, (_result) => {
                setDatasource(_result);
              });
            }) as any
          }
          onChange={handleSelectService}
          getOptionLabel={(opt) => `${opt.description} ${opt.code}`}
          noOptionsMessage={() => noResultsComponent}
          ref={inputSuggestionEl}
          onKeyDown={(e) => {
            if (
              e.key === 'Backspace' &&
              !inputSuggestionEl?.current?.inputRef?.value
            ) {
              onClear();
            } else if (
              e.key === 'Enter' &&
              inputSuggestionEl?.current?.inputRef?.value &&
              !inputSuggestionEl?.current?.props?.menuIsOpen
            ) {
              onSubmit({
                ...data,
                description:
                  data.description ||
                  inputSuggestionEl?.current?.inputRef?.value?.trim(),
              });
            }
          }}
        />
        {/* top right element here */}
        {rightElement}
      </Flex>

      {showServiceOptions && (
        <Flex column>
          <CareFacilityBlock
            data={data?.careFacility}
            onChange={onCareFacilityChange}
            onKeyDown={onCareFacilitySubmit}
            disabled={disabled}
          />
        </Flex>
      )}
      {showServiceOptions && (
        <AdditionalInfoPlainEditor
          // placeholder={additionalInfoEditorplaceholder}
          defaultEditorState={defaultEditorState} // NOTE: keep this local
          onChange={(addInfoPayload) =>
            setUpdatedService({ ...data, ...addInfoPayload })
          }
        >
          {/* OPTIONAL PLUGINS HIGHER THAN DEFAULTs*/}
          {props.children}
          <EditorRefPlugin editorRef={editorRef} />
          {/* Plugin for handle OMIM-G chain */}
          <OmimGChainTypeaheadPlugin />
          {/* Plugin for handle additional info keyboard navigation */}
          <AdditionalInfoKeyboardNavigationPlugin />
          {/* Plugin for handle additional info logics - all the blocks + encounterDate */}
          {/* Plugin for selecting additional info nodes */}
          <AdditionalInfoSelectionPlugin
            encounterDate={encounterDate}
            encounterService={data as EncounterServiceTimeline}
            additionalFields={additionalFields}
          />
          {/* Plugin for handle additional info avoid line break then */}
          {/* AND Plugin for handle additional info submit */}
          <AdditionalInfoKeyEnterToSubmitPlugin onSubmit={onComposerSubmit} />
        </AdditionalInfoPlainEditor>
      )}
    </Flex>
  );
};

export default React.memo(ServiceNode);
