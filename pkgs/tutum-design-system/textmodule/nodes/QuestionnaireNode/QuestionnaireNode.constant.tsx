import React from 'react';
import CompoundMenuList from '@tutum/design-system/components/CompoundMenuList';
import {
  IMenuItemWithData,
  coreComponents,
  CustomOptionComponent,
  customOptionStyles,
} from '@tutum/design-system/components';
import { COLOR, mixTypography } from '@tutum/design-system/themes/styles';
import type { Answer } from '@tutum/hermes/bff/text_module_common';
import type {
  SelectComponentsConfig,
  GroupBase,
  StylesConfig,
} from 'react-select';
import { StyledCustomOptionWrapper } from './QuestionnaireNode.styled';

export const defaultStyleConfig: StylesConfig<
  IMenuItemWithData<Answer>,
  boolean,
  GroupBase<IMenuItemWithData<Answer>>
> = {
  ...customOptionStyles,
  control: (base) => {
    return {
      ...base,
      border: 'none',
      background: 'transparent',
      boxShadow: 'none',
      outline: 'none',
      minHeight: 20,
      color: COLOR.TEXT_PRIMARY_BLACK,
      textAlign: 'left',
    };
  },
  container: (base) => ({ ...base, padding: 0, margin: 0 }),
  valueContainer: (base) => ({
    ...base,
    padding: 0,
    margin: 0,
  }),
  multiValue: (base) => ({
    ...base,
    height: '100%',
    padding: 0,
    margin: 0,
  }),
  input: (base) => ({
    ...base,
    height: '100%',
    padding: 0,
    margin: 0,
    minWidth: 40,
    '&': mixTypography('composer'),
  }),
  menuPortal: (provided) => ({
    ...provided,
    zIndex: 999,
  }),
  menu: (provided) => ({
    ...provided,
    minWidth: '240px',
    width: '480px',
    padding: 0,
  }),
  menuList: (provided) => ({
    ...provided,
    padding: 0,
    borderRadius: 4,
    overflow: 'hidden',
  }),
};

export const SINGLE_COMPONENT_CONFIG: Partial<
  SelectComponentsConfig<
    IMenuItemWithData<Answer>,
    boolean,
    GroupBase<IMenuItemWithData<Answer>>
  >
> = {
  DropdownIndicator: null,
  MenuList: (props) => (
    <coreComponents.MenuList {...(props as any)}>
      <CompoundMenuList>
        <CompoundMenuList.Header />
        {props.children}
      </CompoundMenuList>
    </coreComponents.MenuList>
  ),
};

export const MULTI_COMPONENT_CONFIG: Partial<
  SelectComponentsConfig<
    IMenuItemWithData<Answer>,
    true,
    GroupBase<IMenuItemWithData<Answer>>
  >
> = {
  DropdownIndicator: null,
  ClearIndicator: null!,
  MenuList: (props) => (
    <coreComponents.MenuList {...props}>
      <CompoundMenuList>
        <CompoundMenuList.Header />
        {props.children}
      </CompoundMenuList>
    </coreComponents.MenuList>
  ),
  Option: (props) => (
    <StyledCustomOptionWrapper>
      <CustomOptionComponent {...(props as any)} className="sl-multi-select" />
    </StyledCustomOptionWrapper>
  ),
  MultiValue: (props) => {
    const { data, index, getValue } = props;
    const totalLength = getValue().length;
    const isLastIndex = index === totalLength - 1;

    return (
      <span
        style={{
          display: 'inline-flex',
          alignItems: 'center',
        }}
      >
        {data.label}
        {!isLastIndex ? ', ' : ''}
      </span>
    );
  },
};

export const SEPERATOR_CHAR = ',';
