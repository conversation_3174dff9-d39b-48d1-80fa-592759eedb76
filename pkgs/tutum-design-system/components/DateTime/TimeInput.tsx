
import React, { useState } from 'react';
import { TimePicker, TimePickerProps } from '@blueprintjs/datetime';
import styled from 'styled-components';

import { Flex } from '../Flexbox';
import { Svg } from '../Svg';
import { COLOR } from '@tutum/design-system/themes/styles';

const iconDefault = '/images/clock.svg';

export interface TimeInputProps extends TimePickerProps {
  className?: string;
  wrapperStyle?: React.CSSProperties;
  icon?: string;
  showIcon?: boolean;
  onChange: (value: Date) => void;
}

const Container = styled(Flex)`
  justify-content: space-between;
  border: 1px solid ${COLOR.BORDER_DISABLED};
  border-radius: 4px;
  background-color: ${COLOR.BACKGROUND_PRIMARY_WHITE};
  align-items: center;
  .bp5-timepicker .bp5-timepicker-input-row {
    border-radius: unset;
    box-shadow: none;
  }
`;

const TimeInput: React.FC<TimeInputProps> = ({
  className,
  value,
  onChange,
  disabled = false,
  showIcon = true,
  icon,
  ...timePickerProps
}) => {
  const [time, setTime] = useState<Date | undefined>(value);

  const handleTimeChange = (newTime: Date) => {
    const currentDate = new Date();
    const normalizedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      currentDate.getDate(),
      newTime.getHours(),
      newTime.getMinutes()
    );

    setTime(normalizedDate);
    onChange(normalizedDate);
  };

  return (
    <Container>
      <TimePicker
        className={className}
        value={time}
        onChange={handleTimeChange}
        disabled={disabled}
        precision="minute"
        selectAllOnFocus
        {...timePickerProps}
      />
      {showIcon && (
        <Svg size={16} src={icon || iconDefault} style={{ margin: '0 8px' }} />
      )}
    </Container>
  );
};

export default TimeInput;
