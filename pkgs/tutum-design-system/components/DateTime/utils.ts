
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { isNil } from 'lodash';

export function nextFocusableElement(
  el,
  focusableSelector = 'input, button, select, textarea, a[href]'
) {
  if (!document) {
    return null;
  }
  const universe = document.querySelectorAll(focusableSelector);
  const list = Array.prototype.filter.call(universe, function (item) {
    return item.tabIndex >= '0';
  });
  const index = list.indexOf(el);
  return list[index + 1] || list[0];
}

export interface ObjectDateValue {
  date: string;
  month: string;
  year: string;
}

export const convertDateValueToObject = (
  dateValue: string
): ObjectDateValue => {
  const value = dateValue.replaceAll('.', '');
  const date = value.substring(0, 2) || null;
  const month = value.substring(2, 4) || null;
  const isValidDate = +date >= 0 && +date <= 31;
  const isValidMonth = +month >= 0 && +month <= 12;
  let year = value.substring(4) || null;
  const currentYear = datetimeUtil.date().getFullYear();

  // convert YY to YYYY
  if (value.length === 6 && isValidDate && isValidMonth) {
    const currentMonth = datetimeUtil.date().getMonth() + 1;
    const isValidYear = +`20${year}` <= currentYear;
    const isCurrentYear = +`20${year}` === currentYear;
    const prefix =
      !isValidYear || (isCurrentYear && +month > currentMonth) ? 19 : 20;

    year = `${prefix}${year}`;
  }

  return {
    date,
    month,
    year,
  };
};

export const convertDateValueToObjectAndAutoCompleted = (
  dateValue: string
): ObjectDateValue => {
  let value = dateValue.replaceAll('.', '');

  if (value.length < 4) {
    return {
      date: '',
      month: '',
      year: value,
    };
  }

  const year = value.slice(-4);
  value = value.slice(0, value.indexOf(year));
  const month = value.slice(-2);
  value = value.slice(0, value.indexOf(month));
  const date = value.slice(-2) || null;

  return {
    date: +date > 0 ? date : '01',
    month: +month > 0 ? month : '01',
    year,
  };
};

export const convertObjectDateValueToFormValue = (
  values: ObjectDateValue,
  isEdit = true
) => {
  const { date, month, year } = values || {};
  const textDate = !isNil(date)
    ? +date > 9
      ? `${date}`
      : `0${+date}`
    : isEdit
      ? '00'
      : '';
  const textMonth = !isNil(month)
    ? +month > 9
      ? `${month}`
      : `0${+month}`
    : isEdit
      ? '00'
      : '';
  const textYear = !isNil(year) ? `${year}` : isEdit ? '0000' : '';
  const isCorrectDate = textDate.length === 2;
  const isCorrectMonth = textMonth.length === 2;
  const isCorrectYear = textYear.length === 4;
  const isCorrectFormat = isCorrectDate && isCorrectMonth && isCorrectYear;
  const value = [textDate, textMonth, textYear].join(
    isCorrectFormat ? '.' : ''
  );

  return {
    textDate,
    textMonth,
    textYear,
    value,
  };
};
