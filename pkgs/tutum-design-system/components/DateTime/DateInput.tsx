import React, { useState, useCallback, useEffect } from 'react';
import { InputGroup } from '@tutum/design-system/components/Core';

export interface DateInputProps {
  className?: string;
  name: string;
  placeholder: string;
  value: string;
  disabled?: boolean;
  dataTabId: string;
  leftElement?: JSX.Element;
  onFocus?: () => void;
  onBlur?: (value: string) => void;
  onKeyDown?: (event: React.KeyboardEvent, value: string) => void;
}

const DateInput = ({
  className,
  name,
  placeholder,
  value,
  disabled,
  dataTabId,
  leftElement,
  onFocus,
  onBlur,
  onKeyDown,
}: DateInputProps) => {
  const [inputValue, setInputValue] = useState<string>('');

  const onChange = useCallback((evt: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(evt.target.value);
  }, []);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <InputGroup
      type="text"
      autoComplete="off"
      className={className}
      name={name}
      id={dataTabId}
      data-tab-id={dataTabId}
      placeholder={placeholder}
      disabled={disabled}
      value={inputValue || ''}
      leftElement={leftElement}
      onFocus={onFocus}
      onBlur={() => onBlur?.(inputValue)}
      onKeyDown={(event) => onKeyDown?.(event, inputValue)}
      onChange={onChange}
    />
  );
};

export default DateInput;
