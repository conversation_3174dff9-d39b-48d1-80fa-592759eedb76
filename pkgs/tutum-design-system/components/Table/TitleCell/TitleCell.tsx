import React from 'react';
import { isEmpty } from 'lodash';
import { Avatar } from '@tutum/design-system/components';

export interface ITitleCellProps {
  className?: string;
  title: string;
  description: string;
  avatarText?: string;
  highlight?: boolean;
}

const TitleCell = (props: ITitleCellProps) => {
  const { className, title, description, avatarText, highlight } = props;
  const titleClassName = highlight
    ? 'sl-TitleCell__header--highlight'
    : 'sl-TitleCell__header';
  if (!isEmpty(avatarText)) {
    return (
      <div className={className}>
        <div className="sl-TitleCell__avatar-container">
          <Avatar initial={avatarText!} />
        </div>
        <div className="sl-TitleCell__content">
          <h5 className={titleClassName}>{title}</h5>
          <p className="sl-TitleCell__description">{description}</p>
        </div>
      </div>
    );
  }
  return (
    <div className={className}>
      <h5 className={titleClassName}>{props.title}</h5>
      <p className="sl-TitleCell__description">{description}</p>
    </div>
  );
};

export default TitleCell;
