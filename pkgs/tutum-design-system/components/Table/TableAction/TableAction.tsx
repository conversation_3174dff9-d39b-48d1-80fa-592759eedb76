import React from 'react';
import { styled } from '@tutum/design-system/themes';

import {
  Popover,
  PopoverPosition,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { Svg } from '@tutum/design-system/components/Svg';
import I18n from '@tutum/infrastructure/i18n';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import { COLOR } from '@tutum/design-system/themes/styles';

const moreIcon = '/images/more-vertical.svg';

export interface TableActionItem {
  id: string;
  label: React.ReactNode;
  hoverColor?: 'normal' | 'danger';
  icon?: React.ReactNode;
  render?: React.ReactNode;
  onClick?: () => void;
}
export interface TableActionProps {
  className?: string;
  mode?: 'icon' | 'popover';
  actions?: TableActionItem[];
}

const PopoverContent = styled.div`
  display: flex;
  gap: 8px;
  flex-direction: column;
  padding: 8px;
  border-radius: 4px;
  .actions-cell__popover-item {
    padding: 4px 8px;
    display: flex;
    gap: 8px;
    align-items: center;
    border-radius: 4px;
    min-height: 28px;
    cursor: pointer;
    &:hover {
      background: ${COLOR.BACKGROUND_SELECTED};
    }
    &.danger:hover {
      background: ${COLOR.NEGATIVE_LIGHT};
    }

    .actions-cell__popover-label {
      &.danger {
        color: ${COLOR.TEXT_NEGATIVE};
      }
    }
  }
`;

export const TableAction = ({
  mode = 'icon',
  actions,
  className,
}: TableActionProps) => {
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });
  const [open, setOpen] = React.useState(false);

  mode = (actions?.length || 0) >= 2 ? 'popover' : 'icon';
  return (
    <div className={className}>
      {mode === 'icon' && (
        <div>
          {actions?.map((action) => {
            return (
              <Tooltip
                key={action.id}
                content={<>{action.label}</>}
                position={PopoverPosition.BOTTOM_LEFT}
              >
                <div
                  onClick={action?.onClick}
                  className="action-item cursor-pointer"
                >
                  {action.render ? action.render : action?.icon}
                </div>
              </Tooltip>
            );
          })}
        </div>
      )}
      {mode === 'popover' && (
        <Tooltip content={tCommon('More')}>
          <Popover
            usePortal={true}
            popoverClassName={'table-action-popover-content'}
            position={PopoverPosition.LEFT}
            isOpen={open}
            canEscapeKeyClose
            enforceFocus={false}
            onClose={() => setOpen(false)}
            content={
              <PopoverContent>
                {actions?.map((action) => {
                  return (
                    <div
                      key={action.id}
                      onClick={() => {
                        setOpen(false);
                        action?.onClick?.();
                      }}
                      className={`actions-cell__popover-item cursor-pointer ${action.hoverColor || 'normal'
                        }`}
                    >
                      {action.render ? action.render : action?.icon}
                      <span
                        className={`actions-cell__popover-label ${action.hoverColor || 'normal'
                          }`}
                      >
                        {action.label}
                      </span>
                    </div>
                  );
                })}
              </PopoverContent>
            }
            minimal={true}
          >
            <Svg
              className="actions-cell__icon"
              src={moreIcon}
              onClick={() => setOpen(true)}
            />
          </Popover>
        </Tooltip>
      )}
    </div>
  );
};
