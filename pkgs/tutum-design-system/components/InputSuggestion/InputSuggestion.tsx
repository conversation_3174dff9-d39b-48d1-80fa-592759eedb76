
import type { InputActionMeta, SelectInstance } from 'react-select';
import React, {
  memo,
  useState,
  forwardRef,
  useMemo,
  useCallback,
  useEffect,
} from 'react';
import AsyncSelect, { AsyncProps } from 'react-select/async';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IInputSuggestionProps extends AsyncProps<any, false, any> {
  keyCode: string;
}

const InputSuggestion = memo(
  forwardRef(
    (
      props: IInputSuggestionProps,
      ref: React.MutableRefObject<SelectInstance<any, boolean> | null>
    ) => {
      const [searchKey, setSearchKey] = useState(
        props.inputValue ?? props.defaultInputValue ?? ''
      );

      useEffect(() => {
        if (props.defaultInputValue) {
          setSearchKey(props.defaultInputValue ?? '');
        }
      }, [props.defaultInputValue]);

      const handleInputChanged = useCallback(
        (input: string, actionMeta: InputActionMeta) => {
          if (
            actionMeta.action === 'set-value' ||
            actionMeta.action === 'input-blur' ||
            actionMeta.action === 'menu-close'
          ) {
            return;
          }
          setSearchKey(input);
        },
        []
      );

      const stylesConfig = useMemo(
        () => ({
          indicatorsContainer(base) {
            return { ...base, flex: 0 };
          },
          ...props.styles,
          menuList: (provided) => ({
            ...provided,
            minWidth: 400,
            fontSize: 13,
            paddingBottom: 0,
          }),
          option: (base, state) => ({
            ...base,
            backgroundColor: state.isSelected
              ? COLOR.INFO_SECONDARY_PRESSED
              : state.isFocused
                ? COLOR.BACKGROUND_HOVER
                : 'initial',
            color: state.isSelected ? COLOR.TEXT_PRIMARY_BLACK : 'initial',
          }),
        }),
        [props.styles]
      );

      const componentsConfig = useMemo(
        () => ({
          SingleValue: () => null,
          IndicatorSeparator: () => null,
          IndicatorsContainer: () => null,
          ...props.components,
        }),
        [props.components]
      );

      return (
        <AsyncSelect
          {...props}
          id={`wrapper_${props.id}`}
          inputId={props.id}
          instanceId={`instanceId_${props.id}`}
          menuPosition={props.menuPosition ?? 'fixed'}
          menuPlacement={props.menuPlacement ?? 'top'}
          menuPortalTarget={props.menuPortalTarget ?? document.body}
          styles={stylesConfig}
          components={componentsConfig}
          ref={ref}
          classNamePrefix={props.className}
          inputValue={searchKey}
          onInputChange={(input, actionMeta) => {
            props.onInputChange?.(input, actionMeta);
            handleInputChanged(input, actionMeta);
          }}
          onChange={(newValue, actionMeta) => {
            props.onChange(newValue, actionMeta);
            setSearchKey(newValue?.[props.keyCode]);
          }}
        />
      );
    }
  )
);

export default InputSuggestion;
