import React, { memo, ReactElement, useMemo, useState } from 'react';

import {
  BodyTextM,
  Button,
  Flex,
  Intent,
  Svg,
} from '@tutum/design-system/components';
import InfoIcon from '@tutum/mvz/public/images/alert-circle.svg';

export type BannerActionTypes = {
  name: string;
  handleClick?: () => void;
};

const CloseIcon = '/images/close.svg';

export interface IBannerProps {
  className?: string;
  intent: Intent;
  children: string | ReactElement;
  action?: BannerActionTypes;
  canDismiss?: boolean;
  algin?: 'left' | 'center';
}

const Banner = (props: IBannerProps) => {
  const {
    intent,
    children,
    className,
    action,
    canDismiss = false,
    algin = 'center',
  } = props;
  const [isDismissible, setIsDismissible] = useState<boolean>(false);

  const classes = useMemo<string[]>(() => {
    const classesArr = [className!];
    if (intent === Intent.PRIMARY) {
      classesArr.push('sl-banner__info');
    }

    if (intent === Intent.WARNING) {
      classesArr.push('sl-banner__warning');
    }

    if (algin === 'left') {
      classesArr.push('sl-banner__align-left');
    }

    return classesArr;
  }, [className, intent, algin]);

  return !isDismissible ? (
    <Flex className={classes.join(' ')}>
      <InfoIcon />
      <BodyTextM className="sl-banner__title">{children}</BodyTextM>
      {action && (
        <span className="sl-banner__action" onClick={action?.handleClick}>
          {action.name}
        </span>
      )}
      {canDismiss && (
        <Button
          minimal
          iconOnly
          className="sl-banner__dismiss"
          icon={<Svg src={CloseIcon} />}
          onClick={() => setIsDismissible((isDismissible) => !isDismissible)}
        />
      )}
    </Flex>
  ) : (
    <></>
  );
};

export default memo(Banner);
