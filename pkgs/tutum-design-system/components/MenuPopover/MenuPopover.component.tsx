import React, { PropsWithChildren } from 'react';
import { Popover, PopoverProps } from '@tutum/design-system/components/Core';
import {
  StyledMenuPopover,
  StyledMenuListContainer,
  StyledMenuOption,
  StyledMenuOptionIcon,
  StyledMenuOptionLabel,
  PopoverGlobalStyles,
} from './MenuPopover.styled';

export interface MenuOptions {
  label: string | JSX.Element;
  icon?: string | JSX.Element;
  hide?: boolean;
  className?: string;
  onClick?: () => void;
}

interface MenuPopoverProps {
  isOpen?: boolean;
  options: MenuOptions[];
  popoverProps?: PopoverProps;
}

export const MenuPopover: React.FC<PropsWithChildren<MenuPopoverProps>> = ({
  isOpen,
  options,
  popoverProps,
  children,
}) => {
  return (
    <StyledMenuPopover>
      <PopoverGlobalStyles />
      <Popover
        {...popoverProps}
        isOpen={isOpen}
        content={
          <StyledMenuListContainer>
            {options?.map(({ icon, label, onClick, className, hide }, idx) => (
              <StyledMenuOption
                key={label?.toString() || idx}
                onClick={onClick}
                className={className}
                style={
                  hide
                    ? {
                      display: 'none',
                    }
                    : undefined
                }
              >
                {typeof icon === 'string' ? (
                  <StyledMenuOptionIcon src={icon} />
                ) : (
                  icon
                )}
                {typeof label === 'string' ? (
                  <StyledMenuOptionLabel>{label}</StyledMenuOptionLabel>
                ) : (
                  label
                )}
              </StyledMenuOption>
            ))}
          </StyledMenuListContainer>
        }
      >
        {children}
      </Popover>
    </StyledMenuPopover>
  );
};
