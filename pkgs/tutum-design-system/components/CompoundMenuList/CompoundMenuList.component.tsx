import React, { CSSProperties, PropsWithChildren } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import { BodyTextS } from '@tutum/design-system/components';
import StyledCompoundMenuList, {
  StyledMenuItem,
} from './CompoundMenuList.styled';
import { COLOR } from '@tutum/design-system/themes/styles';

function MenuListContainer(props: PropsWithChildren<{ minWidth?: number }>) {
  return (
    <StyledCompoundMenuList style={{ minWidth: props.minWidth }}>
      {props.children}
    </StyledCompoundMenuList>
  );
}

interface IMenuListHeaderProps {
  instructionText?: string;
}

const Header: React.FC<IMenuListHeaderProps> = (props) => {
  const { t } = I18n.useTranslation({
    namespace: 'Common',
  });
  const { instructionText } = props;
  return (
    <BodyTextS className="sl-header-instruction">
      {instructionText || t('NavigationSuggest.listNavigation')}
    </BodyTextS>
  );
};

interface IMenuListProps<T> {
  items: T[];
  selectedIndex?: number;
  getItemId(item: T): string;
  renderItem(item: T, isSelected: boolean, index: number): JSX.Element;
  onItemClick?(item: T): void;
  itemHeight?: string;
}

function MenuList<T = any>(props: IMenuListProps<T>) {
  const {
    items,
    selectedIndex,
    renderItem,
    getItemId,
    onItemClick,
    itemHeight = '60px',
  } = props;
  return (
    <>
      {items.map((m, midx) => {
        const firstDefaultSelected = midx === 0 && selectedIndex === -1;
        const isSelected = midx === selectedIndex || firstDefaultSelected;
        const autoScrollIntoViewWhenSelected: React.LegacyRef<HTMLLIElement> = (
          el
        ) => {
          if (!isSelected || !el) return;
          el.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        };

        return (
          <StyledMenuItem
            key={getItemId(m)}
            style={
              {
                '--sl-compound-menu-item-height': itemHeight,
                '--sl-compound-menu-item-bg': isSelected
                  ? COLOR.BACKGROUND_HOVER
                  : 'initial',
              } as CSSProperties
            }
            onClick={() => onItemClick?.(m)}
            ref={autoScrollIntoViewWhenSelected}
          >
            {renderItem(m, isSelected, midx)}
          </StyledMenuItem>
        );
      })}
    </>
  );
}

MenuListContainer.Header = Header;

MenuListContainer.MenuList = MenuList;

export default MenuListContainer;
