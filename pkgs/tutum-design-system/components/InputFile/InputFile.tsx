import React, { memo, useState, useMemo, useEffect, ReactElement } from 'react';
import { Flex } from '@tutum/design-system/components';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

export interface IInputFileProps {
  t: IFixedNamespaceTFunction<string>;
  className?: string;
  multiple?: boolean;
  accept?: string;
  fileNames: string | string[];
  onChange: (files: FileList) => void;
  children?: ReactElement;
}

const InputFile = ({
  t,
  className,
  multiple,
  accept,
  fileNames,
  children,
  onChange,
}: IInputFileProps) => {
  const [files, setFiles] = useState<FileList | { name: string }[] | null>(null);

  useEffect(() => {
    let cloneFiles = null;
    if (Array.isArray(fileNames)) {
      cloneFiles = fileNames
        ?.filter((item) => Boolean(item))
        ?.map((item) => ({
          name: item,
        }));
    } else {
      cloneFiles = fileNames ? [{ name: fileNames }] : [];
    }
    setFiles(cloneFiles);
  }, []);

  const onChangeFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.files!);
    setFiles(e.target.files);
  };

  const placeholder = useMemo(() => {
    if ((files as { length?: number })?.length! > 1) return `${(files as { length?: number })?.length} files`;
    if ((files as { length?: number })?.length === 1) return `${(files as { name: string }[])?.[0]?.name}`;
    return t('browserPlaceHoler');
  }, [files]);

  return (
    <Flex className={className}>
      {children ? (
        children
      ) : (
        <>
          <span className="sl-browse">{t('browse')}</span>
          <span
            className={`sl-placeholder ${(files as { length?: number })?.length ? 'sl-has-file' : ''}`}
          >
            {placeholder}
          </span>
        </>
      )}
      <input
        className="sl-custom-file-input"
        type="file"
        onChange={onChangeFile}
        multiple={multiple ?? false}
        accept={accept}
      />
    </Flex>
  );
};

export default memo(InputFile);
