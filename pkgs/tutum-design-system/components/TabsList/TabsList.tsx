import React from 'react';

import { getCssClass } from '../../infrastructure/utils';
import { Flex } from '../Flexbox/Flex.styled';
import { BodyTextL } from '../Typography/Typography.styled';
import { Svg } from '../Svg';
import Badge from '@tutum/design-system/components/Badge/Badge.styled';

export interface TabItem {
  label: string;
  value: string;
  icon?: {
    src: string;
    height?: number | 21;
    width?: number | 21;
  };
  badgeContent?: number | string;
}

export interface TabsProps {
  className?: string;
  selectedTab: string;
  tabsList: TabItem[];
  size?: 'small' | 'medium';
  setSelectedTab: (tab: TabItem) => void;
}

const Tabs: React.FC<TabsProps> = ({
  className,
  tabsList,
  selectedTab,
  size = 'medium',
  setSelectedTab,
}) => {
  return (
    <Flex
      className={getCssClass(className, {
        small: size === 'small',
      })}
      align="center"
    >
      {tabsList.map((tab) => (
        <Flex
          className={getCssClass(
            'sl-tabs-list__item',
            {
              'sl-tabs-list__item--active': selectedTab === tab.value,
            },
          )}
          align="center"
          key={tab.value}
          onClick={() => setSelectedTab(tab)}
        >
          <Badge content={tab?.badgeContent}>
            <BodyTextL fontWeight={600}>{tab.label}</BodyTextL>
          </Badge>
          {tab?.icon && (
            <Svg
              src={tab.icon.src}
              className="sl-tab-icon"
              height={tab.icon.height}
              width={tab.icon.width}
            />
          )}
        </Flex>
      ))}
    </Flex>
  );
};

export default Tabs;
