
import { withRouter } from 'next/router';
import React from 'react';

import { Button } from '@tutum/design-system/components/Button';
import { createGlobalStyle } from '@tutum/design-system/models';
import _ from 'lodash';
import { getSendMailLink } from '../../shared/feedback';
import { alertError } from '../Toaster';
import {
  IErrorBoundaryProps,
  IErrorBoundaryState,
  IErrorData,
} from './ErrorBoundaryModel';

export const GlobalStyle = createGlobalStyle`
  .sl-error-boundaries-dialog {
    .bp5-dialog-header {
      box-shadow: none;
    }
    .bp5-overlay-open {
      z-index: 999;
    }
  }
`;

const styles = {
  dialog: {
    marginTop: 10,
  },
};

const ErrorCode_Somethinngs_Went_Wrong = 'ErrorCode_Somethinngs_Went_Wrong';
const ErrorCode_Network_Offline = 'ErrorCode_Network_Offline';
const ResizeLoopErrorMsg =
  'ResizeObserver loop completed with undelivered notifications.';

class ErrorBoundary extends React.PureComponent<
  IErrorBoundaryProps,
  IErrorBoundaryState
> {
  private toaster: any;
  private refHandlers = {
    toaster: (ref: any) => (this.toaster = ref),
  };

  constructor(props: IErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      uiError: false,
      message: '',
      traceId: '',
      statusCode: 0,
      serverError: '',
    };
  }

  componentDidMount() {
    global['addEventListener'](
      'unhandledrejection',
      this._unhandledRejectionEventHandler
    );

    global['addEventListener']('error', this._errorEventHandler);

    global['addEventListener']('offline', this._offlineEventHandler);
  }

  static getDerivedStateFromError() {
    return { hasError: true, uiError: true };
  }

  componentWillUnmount() {
    global['removeEventListener'](
      'unhandledrejection',
      this._unhandledRejectionEventHandler
    );

    global['removeEventListener']('error', this._errorEventHandler);

    global['removeEventListener']('offline', this._offlineEventHandler);
  }
  render() {
    const { Toaster } = this.props;
    return (
      <>
        <Toaster position="top" ref={this.refHandlers.toaster} />
        {this.renderErrorDialog()}
        {!this.state.uiError && this.props.children}
      </>
    );
  }

  renderErrorDialog = () => {
    const { Dialog } = this.props;
    const { hasError, traceId, uiError, statusCode, serverError } = this.state;
    const renderErrorMsg = () => {
      if (statusCode === 500) {
        return <p>{this.props.t?.(ErrorCode_Somethinngs_Went_Wrong)}</p>;
      }
      return (
        <>
          <p>
            error_code:
            <strong>{serverError}</strong>
          </p>
          <p>
            error_message:
            <strong>
              {this.state.message ||
                this.props.t?.(ErrorCode_Somethinngs_Went_Wrong)}
            </strong>
          </p>
        </>
      );
    };
    return (
      <>
        {hasError && <GlobalStyle />}
        <Dialog
          icon="error"
          title="OOPS!"
          onClose={this.handleCloseDialog}
          isCloseButtonShown={false}
          canEscapeKeyClose={!uiError}
          isOpen={hasError}
          canOutsideClickClose={true}
          portalClassName="sl-error-boundaries-dialog"
        >
          <div className="bp5-dialog-body">
            {renderErrorMsg()}
            {traceId && (
              <p style={styles.dialog}>
                Trace Id: <strong>{traceId}</strong>
              </p>
            )}
            <div
              style={{
                marginTop: 18,
                display: 'flex',
                gap: 10,
                justifyContent: 'space-between',
              }}
            >
              <Button
                intent="primary"
                style={{ width: '100%' }}
                outlined
                onClick={this.handleCloseDialog}
              >
                Close
              </Button>

              <a
                className="tet"
                style={{
                  display: 'block',
                  width: '100%',
                }}
                href={getSendMailLink({
                  traceId,
                  message: this.state.message,
                  statusCode: this.state.statusCode.toString(),
                  serverError: this.state.serverError,
                } as any)}
              >
                <Button style={{ width: '100%' }} intent="primary">
                  Send feedback
                </Button>
              </a>
            </div>
          </div>
        </Dialog>
      </>
    );
  };

  renderToasterMessage = (message: string) => (
    <>
      <div>{message}</div>
    </>
  );

  handleCloseDialog = () => {
    this.setState({
      hasError: false,
      uiError: false,
      statusCode: 0,
      message: '',
      traceId: '',
      serverError: '',
    });
  };

  handlePromiseResponseError = async (response, statusCode: number) => {
    const traceId = response.data ? response.data.traceId : undefined;
    const { apiErrorRedirect, router, redirectCallBack } = this.props;

    if (response.status === 500) {
      this.setState({
        hasError: true,
        traceId,
        message: response.data?.message,
      });
      return;
    }
    const redirectPath = apiErrorRedirect?.[statusCode];

    if (redirectPath && router.pathname === redirectPath) {
      return;
    }
    if (redirectPath) {
      if (redirectCallBack) {
        redirectCallBack();
      }
      // this.handleCloseDialog();
      await router.push(redirectPath);
      return;
    }
    this.processExpectedServerErrorHandler(response.data, statusCode);
  };

  processUnhandledError = (event: any, statusCode: number) => {
    console.error(event);
    // TODO: investigate why this error only happened on a few computer
    if (event.message === ResizeLoopErrorMsg) {
      return;
    }
    if (navigator && !navigator.onLine) {
      event.message = this.props.t?.(ErrorCode_Network_Offline) || '';
    }
    if (event.error) {
      const { message, serverError, statusCode } = event.error;
      this.setState({
        hasError: true,
        message: message,
        statusCode: statusCode,
        serverError: serverError,
      });
      return;
    }

    const { reason } = event;
    const errMsg =
      reason?.message ||
      this.props.t?.(reason?.serverError || ErrorCode_Somethinngs_Went_Wrong) || '';

    if (!event.message || !this.toaster) {
      this.setState({
        hasError: true,
        message: errMsg,
        statusCode: statusCode,
        serverError: reason?.serverError || statusCode,
      });
      return;
    }

    this.cachedAlertError(errMsg);
  };

  // avoid same message output multiple time
  cachedAlertError = _.wrap(
    _.memoize((_input: string) =>
      _.throttle((msg: string) => {
        alertError(this.renderToasterMessage(msg), {
          toaster: this.toaster,
        });
      }, 10_000)
    ),
    (debounceFunc, input: string) => debounceFunc(input)(input)
  );

  processExpectedServerErrorHandler = (errorData: IErrorData, statusCode: number) => {
    const { processExpectedServerError } = this.props;
    let errorDataTmp = errorData;
    if (!errorDataTmp) {
      return;
    }

    // Firstly, handle specific error codes at pkgs/app_mvz/components/app-skeleton/AppSkeleton.tsx@handleErrorCode
    // return errorData if it is not handled
    if (processExpectedServerError) {
      errorDataTmp = processExpectedServerError(errorData, statusCode)!;
      if (!errorDataTmp) {
        return;
      }
    }

    // handle unexpected error
    // try to build message detail
    // check if errorDataTmp has no serverError then use message else use serverError and serverErrorParam
    let messageDetail = "";
    const errorDataServerErrorParam = errorDataTmp?.serverErrorParam; // serverErrorParam may be a object or an array or undefined, build the message accordingly
    if (errorDataServerErrorParam && errorDataServerErrorParam instanceof Array) {
      messageDetail = errorDataServerErrorParam.map((err) => err?.message || err).join('\n');
    } else if (errorDataServerErrorParam && typeof errorDataServerErrorParam === 'object') {
      // get field names and values from the object
      const fieldNames = Object.keys(errorDataServerErrorParam);
      const fieldValues = Object.values(errorDataServerErrorParam);
      messageDetail = fieldNames
        .filter((fieldName, index) => {
          const fieldValue = fieldValues[index];
          if (fieldValue === undefined) return false;
          if (typeof fieldValue === 'object' || Array.isArray(fieldValue)) return false;
          return true;
        })
        .map((fieldName, index) => `${fieldName}: ${fieldValues[index]}`)
        .join(', ');
    }

    // main error title from serverError or message
    let mainErrorTitle = errorDataTmp.message || errorDataTmp.serverError;
    if (!mainErrorTitle) {
      mainErrorTitle = this.props.t?.(ErrorCode_Somethinngs_Went_Wrong)!;
    }
    // if messageDetail is not empty, append it to the main error title
    if (messageDetail) {
      mainErrorTitle += `\n${messageDetail}`;
    }

    if (!this.toaster) {
      this.setState({
        hasError: true,
        message: mainErrorTitle,
        statusCode,
      });
      return;
    }
    this.cachedAlertError(mainErrorTitle);
  };

  private _unhandledRejectionEventHandler = (
    event: PromiseRejectionEvent | CustomEvent
  ) => {
    let _shouldHandleError: boolean;
    let _response: any;
    let _statusCode: number;
    if (event instanceof PromiseRejectionEvent) {
      _shouldHandleError = event.reason && event.reason.response;
      _response = event.reason ? event.reason.response : undefined;
      _statusCode = event.reason?.statusCode;
    } else if (event instanceof CustomEvent) {
      _shouldHandleError =
        event.detail && event.detail.reason && event.detail.reason.response;
      _response =
        event.detail && event.detail.reason
          ? event.detail.reason.response
          : undefined;
    }

    if (_shouldHandleError!) {
      this.handlePromiseResponseError(_response, _statusCode!);
      // preventing it from bubbling up to be handled by the runtime's logging code
      event.preventDefault();
      return;
    }
    this.processUnhandledError(event, _statusCode!);
  };

  private _errorEventHandler = (event: ErrorEvent) => {
    this.processUnhandledError(event, undefined!);
  };

  private _offlineEventHandler = () => {
    alertError(
      this.renderToasterMessage(this.props.t?.(ErrorCode_Network_Offline)!)
    );
  };
}

export const ErrorBoundaryWithRouter = withRouter(ErrorBoundary);
