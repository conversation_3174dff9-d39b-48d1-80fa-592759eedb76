import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { NextRouter } from 'next/router';

export interface IErrorBoundaryProps {
  user?: any;
  Dialog: any;
  Toaster: any;
  apiErrorRedirect?: { [key: number]: string };
  t?: IFixedNamespaceTFunction<any>;
  errorCodes?: { [key: string]: string };
  router: NextRouter;
  children?: React.ReactNode;
  redirectCallBack?: () => void;
  processExpectedServerError?: (
    errorData: IErrorData,
    statusCode: number
  ) => void;
}

export interface IErrorBoundaryState {
  hasError: boolean;
  message: string;
  traceId: string;
  uiError: boolean;
  statusCode: number;
  serverError: string;
}
export interface IErrorParam {
  code: string;
  message: string;
}
export interface IErrorData {
  statusCode: number;
  serverError?: string;
  serverErrorParam?: any; // may be array of strings or objects or array of IErrorParam
  validationErrors?: object;
  message?: string;
}

export interface HpmError {
  serverError: string;
  serverErrorParam: HpmErrorMessage[];
}
export interface HpmErrorMessage {
  message: string;
  code: string;
}
