import { scaleSpace } from '@tutum/design-system/styles';
import { COLOR } from '@tutum/design-system/themes/styles';
import I18n from '@tutum/infrastructure/i18n';
import React, { useEffect, useState } from 'react';
import { GroupBase, StylesConfig } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import ISelect from 'react-select/dist/declarations/src/Select';
import { useTheme } from '@tutum/design-system/themes';

import {
  DropdownIndicator,
  IMenuItem,
  ISelectCreatableProps,
} from '../ReactSelect/index';

const handleValue = (
  items: IMenuItem[],
  selectedValue?: number | string | Array<string | number>
) => {
  if (Array.isArray(selectedValue)) {
    const listItem = selectedValue?.map((value) =>
      items?.find((item) => item?.value === value)
    );
    return listItem?.length ? listItem : null;
  }
  const itemMenu = items?.find((item) => item?.value === selectedValue);
  if (itemMenu) return itemMenu;
  return selectedValue
    ? { value: selectedValue, label: `${selectedValue}` }
    : null;
};

const getStyles = (styles: StylesConfig<any>) => ({
  ...styles,
  menuPortal: (base) => ({ ...base, zIndex: 999 }),
  option: (base, state) => ({
    ...base,
    backgroundColor: state.isSelected
      ? COLOR.INFO_SECONDARY_PRESSED
      : state.isFocused
        ? COLOR.BACKGROUND_HOVER
        : 'initial',
    color: state.isSelected ? COLOR.TEXT_PRIMARY_BLACK : 'initial',
    ...(styles?.option ? styles?.option(base, state) : {}),
  }),
  indicatorsContainer: (base) => ({
    ...base,
    paddingRight: scaleSpace(2),
  }),
});

const SelectCreatable = React.forwardRef(
  <T extends IMenuItem>(
    {
      selectedValue,
      items,
      noResults,
      placeholder,
      onItemSelect,
      onCreateOption,
      ...props
    }: ISelectCreatableProps<T>,
    ref: React.Ref<ISelect<unknown, boolean, GroupBase<unknown>>>
  ) => {
    const { t } = I18n.useTranslation<any>({
      namespace: 'Common',
      nestedTrans: 'Select',
    });
    const [target, setTarget] = useState<HTMLElement | undefined>(undefined);
    useEffect(() => {
      if (typeof window !== 'undefined') {
        setTarget(document.body);
      }
    }, []);

    return (
      <CreatableSelect
        {...props}
        ref={ref}
        value={handleValue(items, selectedValue)}
        classNamePrefix={props.classNamePrefix ?? 'react-select'}
        options={items}
        components={{
          DropdownIndicator,
          IndicatorSeparator: null,
          ClearIndicator: null!,
          ...props.components,
        }}
        placeholder={placeholder || t('select')}
        noOptionsMessage={() => noResults || <div>{t('noResults')}</div>}
        onChange={(item) => {
          onItemSelect?.((item || { value: '' }) as T);
        }}
        onCreateOption={(item) => {
          onCreateOption?.({ value: item } as T);
        }}
        menuPortalTarget={target}
        isClearable={props.isClearable || true}
        backspaceRemovesValue={props.backspaceRemovesValue || true}
        styles={getStyles(props?.styles ?? {})}
      />
    );
  }
);

export default SelectCreatable;
