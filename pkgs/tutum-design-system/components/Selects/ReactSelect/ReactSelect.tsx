import React, { useEffect, useState } from 'react';
import ReactSelect, { <PERSON><PERSON>, GroupBase, StylesConfig } from 'react-select';

import { Icon } from '@tutum/design-system/components/Core';
import { COLOR } from '@tutum/design-system/themes/styles';
import { scaleSpace } from '@tutum/design-system/styles';
import ISelect from 'react-select/dist/declarations/src/Select';
import I18n from '@tutum/infrastructure/i18n';

type NumberOrString = number | string;
export interface IMenuItem<T = NumberOrString, K = any> {
  value: T;
  label: string;
  id?: string | number;
  isDisabled?: boolean;
  data?: K;
}

export interface IServiceCodeItem {
  value: string;
  label: string;
  id?: string;
  isDisabled?: boolean;
}

export interface IMenuItemWithData<T> {
  id: NumberOrString;
  value: NumberOrString;
  label: string;
  data?: T;
  isDisabled?: boolean;
}

export interface ISelectProps<T extends IMenuItem<NumberOrString, K>, K = any>
  extends Props {
  selectedValue?: NumberOrString | Array<string | number>;
  items: (T | GroupBase<T>)[];
  noResults?: React.ReactNode;
  className?: string;
  isDocumentTarget?: boolean;
  onItemSelect?: (item: T) => void;
}

export interface ISelectCreatableProps<T extends IMenuItem> extends Props {
  selectedValue?: NumberOrString | Array<string | number>;
  items: T[];
  noResults?: React.ReactNode;
  className?: string;
  placeholder?: string;
  isOpenMenu?: boolean;
  onItemSelect?: (item: T) => void;
  onCreateOption?: (item: T) => void;
}

export const DropdownIndicator = ({ innerProps }: any) => {
  return (
    <Icon
      {...innerProps}
      icon="caret-down"
      color={COLOR.TEXT_TERTIARY_SILVER}
    />
  );
};

const handleValue = (
  items: (IMenuItem | GroupBase<IMenuItem>)[],
  selectedValue: NumberOrString | Array<string | number> | null
): IMenuItem | (IMenuItem | undefined)[] | null => {
  if (Array.isArray(selectedValue)) {
    const listItem = selectedValue?.map((value) =>
      items
        ?.flatMap((item) => ('options' in item ? item.options : item))
        ?.find((item) => item?.value === value)
    );
    return listItem?.length ? listItem : null;
  }
  const itemMenu = items
    ?.flatMap((item) => ('options' in item ? item.options : item))
    ?.find((item) => item?.value === selectedValue);
  if (itemMenu) return itemMenu;
  return selectedValue
    ? { value: selectedValue, label: `${selectedValue}` }
    : null;
};

export const getStyles = (styles: StylesConfig<any>) => ({
  ...styles,
  menuPortal: (base) => ({ ...base, zIndex: 999 }),
  option: (base, state) => ({
    ...base,
    backgroundColor: state.isSelected
      ? COLOR.INFO_SECONDARY_PRESSED
      : state.isFocused
        ? COLOR.BACKGROUND_HOVER
        : 'initial',
    color: state.isSelected ? COLOR.TEXT_PRIMARY_BLACK : 'initial',
    ...(styles?.option ? styles?.option(base, state) : {}),
  }),
  indicatorsContainer: (base) => ({
    ...base,
    paddingRight: scaleSpace(2),
  }),
});

const Select = React.forwardRef(
  <T extends IMenuItem<NumberOrString, K>, K = any>(
    {
      selectedValue,
      items,
      noResults,
      placeholder,
      isDocumentTarget = true,
      onItemSelect,
      ...props
    }: ISelectProps<T, K>,
    ref: React.Ref<ISelect<T, boolean, GroupBase<T>>>
  ) => {
    const { t } = I18n.useTranslation<any>({
      namespace: 'Common',
      nestedTrans: 'Select',
    });
    const [target, setTarget] = useState<HTMLElement | undefined>(undefined);
    useEffect(() => {
      if (isDocumentTarget && typeof window !== 'undefined') {
        setTarget(document.body);
      }
    }, [isDocumentTarget]);

    // todo <ReactSelect<T>
    return (
      <ReactSelect
        {...props}
        ref={ref}
        value={handleValue(items, selectedValue || null)}
        classNamePrefix={props.classNamePrefix ?? 'react-select'}
        options={items}
        components={{
          DropdownIndicator,
          IndicatorSeparator: null,
          ClearIndicator: undefined,
          ...props.components,
        }}
        placeholder={placeholder || t('select')}
        noOptionsMessage={() =>
          noResults || (
            <div data-test-id="no-results-found">{t('noResults')}</div>
          )
        }
        onChange={(item) => {
          onItemSelect?.((item || { value: '' }) as T);
        }}
        menuPortalTarget={target}
        isClearable={props.isClearable || true}
        backspaceRemovesValue={props.backspaceRemovesValue || true}
        styles={getStyles(props?.styles ?? {})}
      />
    );
  }
);

export default Select;
