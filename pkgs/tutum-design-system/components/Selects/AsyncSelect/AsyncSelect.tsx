
import React, { useMemo } from 'react';
import ReactAsyncSelect, { AsyncProps, useAsync } from 'react-select/async';

import type { GroupType, OptionType } from './AsyncSelect.type';

import { DropdownIndicator, getStyles } from '../ReactSelect';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IAsyncSelect<T>
  extends AsyncProps<OptionType<T>, false, GroupType<T>> {
  className?: string;
}

export { useAsync };

const AsyncSelect = <T extends object>(props: IAsyncSelect<T>) => {
  const { styles, components, ...restProps } = props;

  const customStyles = useMemo(
    () => ({
      ...getStyles(styles),
      control: (base, props) => ({
        ...base,
        ...styles?.control?.(base, props),
        borderColor: COLOR.BACKGROUND_TERTIARY_DIM,
        '&:hover': {
          borderColor: COLOR.BACKGROUND_SELECTED_STRONG,
        },
      }),
      option: (base, props) => ({
        ...base,
        ...styles?.option?.(base, props),
        padding: 0,
        li: {
          listStyle: 'none',
        },
      }),
      placeholder: (base) => ({
        ...base,
        color: COLOR.TEXT_PLACEHOLDER,
      }),
    }),
    [styles]
  );

  return (
    <ReactAsyncSelect<OptionType<T>>
      menuPlacement="auto"
      isSearchable
      isClearable
      cacheOptions
      styles={customStyles}
      components={{
        DropdownIndicator,
        IndicatorSeparator: null,
        ClearIndicator: null,
        ...components,
      }}
      {...restProps}
    />
  );
};

export default AsyncSelect;
