
import React, { memo, useCallback, useMemo } from 'react';
import { Popover } from '@tutum/design-system/components/Core';
import {
  BodyTextM,
  Button,
  MultiSelect,
  IMenuItem,
  Flex,
} from '@tutum/design-system/components';
import { StyledContentWrapper } from './MultiSelectFilter.styled';
import { COLOR } from '@tutum/design-system/themes/styles';
import I18n from '@tutum/infrastructure/i18n';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import {
  GroupBase,
  MenuListProps,
  StylesConfig,
  components,
} from 'react-select';

export interface IMultiSelectFilterProps<T extends IMenuItem> {
  className?: string;
  value: Array<string | number>;
  items: T[];
  onClear: () => void;
  onSelectAll: () => void;
  onChange: (newValue: Array<string | number>) => void;
}

const customStyles: StylesConfig = {
  menuPortal: (base) => ({
    ...base,
    left: 0,
    top: 0,
    bottom: 0,
  }),
  menu: (base) => ({
    ...base,
    top: 'calc(100% - 1px)',
    margin: 0,
    left: 0,
    borderRadius: 'unset',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    clipPath: 'inset(0 -1px -1px -1px)',
  }),
  menuList: (base) => ({
    ...base,
    padding: 0,
  }),
  control: (base) => ({
    ...base,
    maxHeight: 184,
    overflowY: 'scroll',
  }),
};

const MultiSelectFilter = <T extends IMenuItem>({
  className,
  value,
  items,
  onChange,
  onSelectAll,
  onClear,
}: IMultiSelectFilterProps<T>) => {
  const { t } = I18n.useTranslation<keyof typeof CommonI18n.MultiSelect>({
    namespace: 'Common',
    nestedTrans: 'MultiSelect',
  });

  const text = useMemo(() => {
    if (!value?.length) return t('select');
    if (value?.length === items?.length) return t('all');
    return t('itemsSelected', { count: value?.length });
  }, [value?.length]);

  const mapValue = useMemo(() => {
    return value?.map((value) => items?.find((item) => item.value === value));
  }, [value]);

  const handleOnChange = (newValue: T[]) => {
    onChange(newValue?.map((item) => item.value));
  };

  const isSelectAll = value && value.length === items.length;

  const CustomMenuListComponent = <
    Option,
    IsMulti extends boolean,
    Group extends GroupBase<Option>
  >(
    props: MenuListProps<Option, IsMulti, Group>
  ) => {
    return (
      <>
        <Button
          style={{ marginBottom: 10 }}
          minimal
          onClick={isSelectAll ? onClear : onSelectAll}
        >
          <BodyTextM fontWeight="SemiBold" color={COLOR.TEXT_INFO}>
            {t(isSelectAll ? 'deselectAll' : 'selectAll')}
          </BodyTextM>
        </Button>
        <components.MenuList {...props}></components.MenuList>
      </>
    );
  };

  return (
    <Popover
      className={className}
      minimal
      position="bottom"
      fill
      popoverClassName="sl-popover-content"
      content={
        <StyledContentWrapper>
          <MultiSelect
            autoFocus
            menuIsOpen
            value={mapValue as any}
            options={items}
            onChange={handleOnChange}
            menuPosition="absolute"
            styles={customStyles}
            components={{
              MenuList: CustomMenuListComponent,
            }}
          />
        </StyledContentWrapper>
      }
    >
      <Button className="sl-target-btn" minimal rightIcon="caret-down">
        <BodyTextM color={!value?.length ? COLOR.TEXT_PLACEHOLDER : null}>
          {text}
        </BodyTextM>
      </Button>
    </Popover>
  );
};

export default memo(MultiSelectFilter);
