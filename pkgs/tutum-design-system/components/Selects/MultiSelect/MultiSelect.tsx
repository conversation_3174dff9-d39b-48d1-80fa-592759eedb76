import React, { memo, useState } from 'react';

import type CommonI18n from '@tutum/mvz/locales/en/Common.json';

import ReactSelect, {
  Props,
  OptionProps,
  components,
  StylesConfig,
  GroupBase,
  MultiValueProps,
  SelectInstance,
} from 'react-select';
import CreatableSelect from 'react-select/creatable';
import I18n from '@tutum/infrastructure/i18n';
import { Flex, BodyTextM, Tag as SLTag } from '@tutum/design-system/components';
import { Checkbox } from '@tutum/design-system/components/Core';
import { Svg } from '@tutum/design-system/components/Svg';
import { COLOR } from '@tutum/design-system/themes/styles';
import { IMenuItem } from '../ReactSelect';

export interface IMultiSelectProps<TMenuItem extends IMenuItem = IMenuItem>
  extends Props<TMenuItem, boolean, GroupBase<TMenuItem>> {
  className?: string;
  isCreatable?: boolean;
  renderContentCustom?: (data) => React.ReactElement;
  ref?: (el: SelectInstance<any, boolean, GroupBase<any>> | null) => void;
  onCreate?: (_: string) => void;
}

export const customOptionStyles: Partial<
  StylesConfig<any, true, GroupBase<any>>
> = {
  placeholder: (base) => ({ ...base, color: COLOR.TEXT_TERTIARY_SILVER }),
  menuPortal: (base) => {
    return {
      ...base,
      zIndex: 99,
    };
  },
  option: (base, state) => {
    return {
      ...base,
      padding: '8px 16px',
      fontSize: 13,
      fontWeight: 400,
      backgroundColor: state.isFocused
        ? COLOR.BACKGROUND_HOVER
        : state.isSelected
          ? COLOR.BACKGROUND_SELECTED
          : 'transparent',
      color: state.isDisabled
        ? COLOR.TEXT_TERTIARY_SILVER
        : COLOR.TEXT_PRIMARY_BLACK,
      '&:hover': {
        cursor: 'pointer',
      },
    };
  },
};

export const CustomOptionComponent = (
  props: OptionProps & { renderContentCustom?: (data) => React.ReactElement }
) => {
  const isNew = (props?.data as any)?.__isNew__ as boolean;

  return (
    <components.Option {...props}>
      <Flex align="flex-start">
        {!isNew && (
          <Checkbox
            className="sl-checkbox"
            checked={props.isSelected}
            disabled={props.isDisabled}
          />
        )}
        {props?.renderContentCustom ? (
          props.renderContentCustom(props?.data)
        ) : (
          <BodyTextM
            aria-disabled={props.isDisabled}
            color={isNew ? COLOR.BACKGROUND_SELECTED_STRONG : undefined}
            fontWeight={isNew ? 'SemiBold' : 'Normal'}
          >
            {props.children}
          </BodyTextM>
        )}
      </Flex>
    </components.Option>
  );
};

// NOTE: replace MultiValue component with the Garrio design system's one
export function MultiValueComponent(
  props: MultiValueProps<any, true, GroupBase<any>>
) {
  return (
    <SLTag
      slState="neutral"
      slSize="regular"
      slStyle="fill"
      style={{ marginRight: 8 }}
      slIcon={
        <Svg
          height={16}
          width={16}
          style={{ cursor: 'pointer' }}
          src="/images/close.svg"
          onClick={props.removeProps.onClick}
        />
      }
    >
      {props.data.label}
    </SLTag>
  );
}

export const MultiValueContainerComponent = ({
  children,
  ...props
}: MultiValueProps<IMenuItem, boolean, GroupBase<IMenuItem>>) => {
  const { getValue, hasValue } = props;
  const { t } = I18n.useTranslation<keyof typeof CommonI18n.MultiSelect>({
    namespace: 'Common',
    nestedTrans: 'MultiSelect',
  });
  const selectedItems = getValue().length;

  return (
    <components.ValueContainer {...(props as any)}>
      {hasValue
        ? t('itemsSelected', {
          count: selectedItems,
        })
        : children}
    </components.ValueContainer>
  );
};

const createOption = (inputValue: string) => ({
  label: inputValue,
  value: inputValue,
});

const MultiSelect = (props: IMultiSelectProps) => {
  const { t } = I18n.useTranslation({
    namespace: 'Common',
    nestedTrans: 'MultiSelect',
  });
  const [creatableOptions, setCreatableOptions] = useState(props.options);

  const handleCreate = (inputValue: string) => {
    props?.onCreate?.(inputValue);
    const newOption = createOption(inputValue);
    setCreatableOptions((prev) => [...prev!, newOption]);
    props.onChange?.([...((props.value ?? []) as any[]), newOption], null!);
  };

  if (props.isCreatable) {
    return (
      <CreatableSelect
        {...props}
        options={creatableOptions}
        isMulti
        ref={(el) => {
          props?.ref?.(el);
        }}
        onCreateOption={handleCreate}
        closeMenuOnSelect={false}
        hideSelectedOptions={props.hideSelectedOptions ?? false}
        menuPosition={props.menuPosition ?? 'fixed'}
        className={props.className}
        classNamePrefix="sl-multi-select"
        components={{
          DropdownIndicator: null,
          Option: (optionProps) => (
            <CustomOptionComponent
              {...optionProps}
              renderContentCustom={props?.renderContentCustom}
            />
          ),
          MultiValue: MultiValueComponent,
          ...props.components,
        }}
        styles={{
          ...props.styles,
          ...customOptionStyles,
        }}
        placeholder={props?.placeholder ?? t('placeholder')}
      />
    );
  }

  return (
    <ReactSelect
      {...props}
      isMulti
      ref={(el) => {
        props?.ref?.(el);
      }}
      closeMenuOnSelect={false}
      hideSelectedOptions={props.hideSelectedOptions ?? false}
      menuPosition={props.menuPosition ?? 'fixed'}
      className={props.className}
      classNamePrefix="sl-multi-select"
      components={{
        DropdownIndicator: null,
        Option: (optionProps) => (
          <CustomOptionComponent
            {...optionProps}
            renderContentCustom={props?.renderContentCustom}
          />
        ),
        MultiValue: MultiValueComponent,
        ...props.components,
      }}
      styles={{
        ...props.styles,
        ...customOptionStyles,
      }}
      placeholder={props?.placeholder ?? t('placeholder')}
    />
  );
};

export default memo(MultiSelect);
