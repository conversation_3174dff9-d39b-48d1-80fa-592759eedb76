import React, { memo, useLayoutEffect, useState } from 'react';

import { Flex } from '../Flexbox';
import { BodyTextM } from '../Typography';

export interface ISectionNavigateProps {
  className?: string;
  sections: string[];
}

/**
 * To use scroll to active section navigate. You must follow this SECTION_ID_PREFIX + index.
 * Ex: SECTION_ID_PREFIX_0
 */
export const SECTION_ID_PREFIX = 'section';

const SectionNavigate = (props: ISectionNavigateProps) => {
  const { className, sections } = props;

  const [currentSection, setCurrentSection] = useState<number>(0);

  useLayoutEffect(() => {
    sections.forEach((_, index) => {
      if (index === currentSection) {
        document
          .getElementById(`${SECTION_ID_PREFIX}_${index}`)
          ?.scrollIntoView({
            behavior: 'auto',
          });
      }
    });
  }, [currentSection, sections]);

  const onChangeSection = (sectionIndex: number) => {
    setCurrentSection(sectionIndex);
  };

  return (
    <Flex className={className}>
      <Flex column>
        {sections.map((title, index) => (
          <BodyTextM
            className={`sl-section-navigate__title${index === currentSection
              ? ' sl-section-navigate__title--active'
              : ''
              }`}
            key={index}
            onClick={() => onChangeSection(index)}
          >
            {title}
          </BodyTextM>
        ))}
      </Flex>
    </Flex>
  );
};

export default memo(SectionNavigate);
