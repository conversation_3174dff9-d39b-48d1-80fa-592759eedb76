import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { styled } from '@tutum/design-system/models';
import { scaleSpacePx } from '@tutum/design-system/styles';
import { COLOR } from '@tutum/design-system/themes/styles';
import OriginalMessageBar from './MessageBar';

const MessageBar = styled(OriginalMessageBar).attrs(
  ({ className, paddingTop, paddingBottom }) => ({
    className: getCssClass('sl-MessageBar', className),
    paddingTop,
    paddingBottom,
  })
)`
  flex: 1;
  padding: ${(props) =>
    `${props.paddingTop ?? props.theme.space.xs} ${props.theme.space.s} ${
      props.paddingBottom ?? props.theme.space.xs
    } ${props.theme.space.s}`};
  align-items: center;

  .sl-message-container {
    align-items: unset;
  }

  .sl-message-content {
    align-items: center;
    .sl-BodyText-M {
      white-space: initial;
    }
  }

  &.error {
    background: ${(props) => props.theme.background.error.lighter};
    span.bullet {
      color: ${COLOR.TAG_BACKGROUND_RED};
    }
    .sl-BodyText-M {
      font-size: 13px;
      font-weight: 600;
      color: ${COLOR.TAG_BACKGROUND_RED};
    }
  }

  &.warning {
    background: ${COLOR.WARNING_LIGHT};
    span.bullet {
      color: ${COLOR.TAG_BACKGROUND_YELLOW};
    }
    .sl-BodyText-M {
      font-size: 13px;
      font-weight: 600;
      color: ${COLOR.TAG_BACKGROUND_YELLOW};
    }
  }

  .description {
    color: ${COLOR.TEXT_PRIMARY_BLACK} !important;
    font-weight: 400 !important;
    margin-left: 24px;
    &.has-bullet {
      margin-left: 36px;
    }
  }

  &.info {
    background: ${COLOR.INFO_SECONDARY_PRESSED};
    padding: 8px 16px;
    .sl-BodyText-M {
      font-size: 13px;
      font-weight: 600;
      color: ${COLOR.BACKGROUND_SELECTED_STRONG};
    }
  }

  &.no-background {
    background-color: transparent;
  }

  .sl-Svg,
  div.none {
    min-width: ${scaleSpacePx(4)};
    height: ${scaleSpacePx(4)};
    margin-right: ${scaleSpacePx(2)};
  }

  .action-button {
    flex: 1;
    justify-content: flex-end;

    .bp5-button {
      min-width: ${scaleSpacePx(30)};
      .bp5-button-text {
        line-height: ${scaleSpacePx(4)};
        white-space: nowrap;
      }
    }
  }
`;

export default MessageBar;
