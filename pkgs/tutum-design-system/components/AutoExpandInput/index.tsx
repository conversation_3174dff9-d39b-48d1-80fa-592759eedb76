import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { styled } from '@tutum/design-system/models';
import { mixTypography } from '@tutum/design-system/themes/styles';
import I18n from '@tutum/infrastructure/i18n';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import { DATE_FORMAT_WITHOUT_DOTS } from '@tutum/infrastructure/shared/date-format';
import { isFormatValid } from '@tutum/infrastructure/utils/datetime.util';
import { LanrBsnrSuggestion } from '@tutum/mvz/components/lanr-bsnr-suggest/LanrBsnrSuggestion';
import { handleGetList } from '@tutum/mvz/components/lanr-bsnr-suggest/LanrBsnrSuggestion.helper';
import {
  AUTO_FOCUS_ATTRIBUTE,
  FOCUSABLE_ATTRIBUTE,
} from '@tutum/mvz/constant/custom-attribute';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import ScheinLocales from '@tutum/mvz/locales/en/Schein.json';
import React, { KeyboardEventHandler } from 'react';
import AutosizeInput from 'react-input-autosize';

export const StyledAutosizeInput = styled(AutosizeInput)`
  .sl-auto-resize__input {
    border: none;
    background-color: transparent;
    // 567 is equal total to width of the remaining elements
    max-width: calc(100vw - 567px);
    ${mixTypography('composer')}
  }
`;

interface IAutoExpandInputProps {
  tabIndex?: number;
  inputId?: string;
  inputType?: React.HTMLInputTypeAttribute;
  value?: string;
  onChange: (newValue: string) => void;
  autoFocus?: boolean;
  withSearchList?: boolean;
  innerInputRef?: (inputRef: HTMLInputElement) => void;
  onCustomKeyDown?: (lowercaseKey: string, event: KeyboardEvent) => void;
  onFocus?: React.FocusEventHandler<HTMLInputElement>;
  shouldStopPropagationOnEnter?: boolean;
  onChangeNative?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  inputClassName?: string;
  isFloat?: boolean;
  isAllery?: boolean;
}

const AutoExpandInput: React.FC<IAutoExpandInputProps> = (props) => {
  const { t } = I18n.useTranslation<
    Paths<typeof ScheinLocales['referral/consulting']>
  >({
    namespace: 'Schein',
  });

  const SPECIAL_KEYS = [
    'backspace',
    'enter',
    'escape',
    'tab',
    'arrowright',
    'arrowleft',
    'arrowdown',
    'arrowup',
  ];
  const changeHandler: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    props.onChangeNative?.(e);
    const newValue = e.target.value;

    if (props.inputType === 'number') {
      if (/^[0-9]*$/.test(newValue) === false) {
        return;
      }
    }
    props.onChange(newValue);
  };

  const handleKeydown: KeyboardEventHandler<HTMLInputElement> = (event) => {
    const lowercaseKey = event.key.toLowerCase();
    const value = event.currentTarget?.value;
    const indexOfDecimalKey = value.indexOf(',');
    if (props.isFloat) {
      const regex = /^[0-9,]*$/;
      if (!regex.test(lowercaseKey) && !SPECIAL_KEYS.includes(lowercaseKey)) {
        event.preventDefault();
        return;
      }
      if ([','].includes(lowercaseKey) && value.includes(',')) {
        event.preventDefault();
        return false;
      }

      if (
        indexOfDecimalKey !== -1 &&
        value.length > indexOfDecimalKey + 2 &&
        !SPECIAL_KEYS.includes(lowercaseKey)
      ) {
        event.preventDefault();
        return false;
      }
    }

    switch (lowercaseKey) {
      case 'enter':
        // if (shouldStopPropagationOnEnter) {
        //   event.stopPropagation();
        // }
        break;
      default:
        break;
    }

    props.onCustomKeyDown?.(lowercaseKey, event as any);
  };

  function setupCustomHTMLAttributes(input: HTMLInputElement) {
    if (props.autoFocus) {
      input?.setAttribute(AUTO_FOCUS_ATTRIBUTE, 'true');
    }
    input?.setAttribute(FOCUSABLE_ATTRIBUTE, 'true');
    props.innerInputRef?.(input);
  }

  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });

  const placeholderContent =
    props.inputType === 'date'
      ? tCommon('DateTimeFormatWithoutDot')
      : undefined;

  const blurHandler: React.FocusEventHandler<HTMLInputElement> = (event) => {
    if (props.inputType !== 'date') {
      return null;
    }

    const target = event?.target as HTMLInputElement;

    if (!target) {
      return;
    }

    const value = target?.value || '';

    const isValid = isFormatValid(value, DATE_FORMAT_WITHOUT_DOTS);

    if (!isValid) {
      props.onChange('');
    }
  };

  return props.withSearchList ? (
    <LanrBsnrSuggestion
      onSelected={(data: any) => {
        props.onChange(data?.generalInfo.bsnr);
      }}
      isComposerMode={true}
      handleGetList={handleGetList(null!, true, t)}
    />
  ) : (
    <StyledAutosizeInput
      type="text"
      inputClassName={getCssClass(
        'sl-auto-resize__input',
        props.inputClassName
      )}
      autoComplete="off"
      value={props.value ?? ''}
      id={props.inputId}
      autoFocus={props.autoFocus}
      tabIndex={props.tabIndex}
      onFocus={props.onFocus}
      onBlur={blurHandler}
      onChange={changeHandler}
      onKeyDown={handleKeydown}
      placeholder={placeholderContent}
      inputRef={setupCustomHTMLAttributes}
    />
  );
};

export default AutoExpandInput;
