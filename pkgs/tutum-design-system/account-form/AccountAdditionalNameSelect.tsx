
import {
  ReactSelect,
  IMenuItem,
  ISelectProps,
} from '@tutum/design-system/components';

import React from 'react';
import { AdditionalName } from '@tutum/hermes/bff/patient_profile_common';

export const AccountAdditionalNameSelect: React.FC<
  Omit<ISelectProps<IMenuItem<string>, any>, 'onItemSelect' | 'items'> & {
    onItemSelect: (item: IMenuItem<string>) => void;
  }
> = (props) => (
  <ReactSelect
    isSearchable={false}
    {...props}
    theme={null}
    items={Object.values(AdditionalName).map(
      (item): IMenuItem => ({
        value: item,
        label: item,
      })
    )}
  />
);
