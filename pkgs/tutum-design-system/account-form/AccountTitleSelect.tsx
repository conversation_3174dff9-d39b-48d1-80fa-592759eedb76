import React, { useEffect, useState } from 'react';
import { styled } from '@tutum/design-system/themes';

import {
  ReactSelect,
  IMenuItem,
  ISelectProps,
} from '@tutum/design-system/components';
import { TITLE } from '@tutum/design-system/consts/employee';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

type I18nToken =
  | 'titleDefault'
  | 'titleV1'
  | 'titleV2'
  | 'titleV3'
  | 'titleV4'
  | 'titleV5'
  | 'titleV6'
  | 'titleV7'
  | 'titleV8'
  | 'titleV9';

const OriginalAccountTitleSelect: React.FC<
  Omit<ISelectProps<IMenuItem<string>, any>, 'onItemSelect' | 'items'> & {
    onItemSelect: (item: IMenuItem<string>) => void;
    onInputChange: (inputValue: string) => void;
  } & {
    t: IFixedNamespaceTFunction<
      | 'titleDefault'
      | 'titleV1'
      | 'titleV2'
      | 'titleV3'
      | 'titleV4'
      | 'titleV5'
      | 'titleV6'
      | 'titleV7'
      | 'titleV8'
      | 'titleV9'
      | 'noResults'
    >;
  }
> = ({ t, ...props }) => {
  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    setInputValue((props.selectedValue as string) || '');
  }, [props.selectedValue]);

  return (
    <ReactSelect
      {...props}
      theme={undefined}
      isSearchable
      items={(TITLE as I18nToken[]).map((item): IMenuItem<string> => ({
        value: item === 'titleDefault' ? '' : t(item),
        label: t(item),
      }))}
      inputValue={inputValue}
      noResults={<div>{t('noResults')}</div>}
      onKeyDown={(event) => {
        if (event.code === 'Enter') {
          event.preventDefault();

          setTimeout(() => {
            (event.target as any).blur();
          });
        }
      }}
      onInputChange={(value, { action, prevInputValue }) => {
        switch (action) {
          case 'input-change':
            setInputValue(value);
            return inputValue;
          case 'input-blur':
            props.onInputChange(inputValue);
            return inputValue;
          default:
            return prevInputValue;
        }
      }}
    />
  );
};

export const AccountTitleSelect = styled(OriginalAccountTitleSelect)`
  .react-select {
    &__input {
      opacity: 1 !important;
    }
  }
`;
