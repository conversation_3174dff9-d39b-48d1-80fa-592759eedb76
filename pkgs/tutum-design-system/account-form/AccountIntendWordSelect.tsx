
import React from 'react';
import {
  ReactSelect,
  IMenuItem,
  ISelectProps,
} from '@tutum/design-system/components';
import { IntendWord } from '@tutum/hermes/bff/patient_profile_common';

export const AccountIntendWordSelect: React.FC<
  Omit<ISelectProps<IMenuItem<string>, any>, 'onItemSelect' | 'items'> & {
    onItemSelect: (item: IMenuItem<string>) => void;
  }
> = (props) => (
  <ReactSelect
    isSearchable={false}
    {...props}
    theme={null}
    items={Object.values(IntendWord).map(
      (item): IMenuItem => ({
        value: item,
        label: item,
      })
    )}
  />
);
