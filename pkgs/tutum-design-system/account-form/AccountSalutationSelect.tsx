import React from 'react';
import {
  ReactSelect,
  IMenuItem,
  ISelectProps,
} from '@tutum/design-system/components';
import { Salutation } from '@tutum/hermes/bff/patient_profile_common';

export const AccountSalutationSelect: React.FC<
  Omit<
    ISelectProps<IMenuItem<string>, any>,
    'onItemSelect' | 'items' | 'formatOptionLabel'
  > & {
    onItemSelect: (item: IMenuItem<string>) => void;
    formatOptionLabel: (item: IMenuItem<string>) => string;
  }
> = ({ formatOptionLabel, ...props }) => (
  <ReactSelect
    isSearchable={false}
    {...props}
    theme={undefined}
    formatOptionLabel={formatOptionLabel}
    items={Object.values(Salutation).map(
      (item): IMenuItem<string> => ({
        value: item,
        label: item,
      })
    )}
  />
);
