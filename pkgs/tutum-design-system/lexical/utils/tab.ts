import {
  $$extractFocusNodeFromSelection,
  $$getCaretPosition,
  $$selectNextNode,
  $$selectNextNodeRecursively,
} from '@tutum/design-system/lexical/utils/selection';
import type { LexicalNode } from 'lexical';
import {
  $createNodeSelection,
  $getRoot,
  $getSelection,
  $isDecoratorNode,
  $isElementNode,
  $isNodeSelection,
  $isParagraphNode,
  $isTextNode,
  $setSelection,
} from 'lexical';
import { LexicalSelection } from '../types';

function _selectNode(
  nodeToSelect: LexicalNode | null,
  selection: LexicalSelection
): boolean {
  // NOTE: handle select node
  if ($isTextNode(nodeToSelect)) {
    nodeToSelect.select(0, nodeToSelect.getTextContentSize());
    return true;
  } else if ($isDecoratorNode(nodeToSelect)) {
    if ($isNodeSelection(selection)) {
      selection.clear();
      selection.add(nodeToSelect.getKey());
    } else {
      const nodeSelection = $createNodeSelection();
      nodeSelection.add(nodeToSelect.getKey());
      $setSelection(nodeSelection);
    }
    return true;
  }
  return false;
}

// NOTE: keep this
export function $$handleTabPress(
  _event: KeyboardEvent,
  _predicateFn: (n: LexicalNode) => boolean
): boolean {
  const selection = $getSelection();

  // NOTE: get focus node
  const focusNode = $$extractFocusNodeFromSelection(selection);

  if (!focusNode) return false;
  if (!_predicateFn(focusNode)) {
    return false;
  }

  // NOTE: custom deselect function for decorators!
  if ($isDecoratorNode(focusNode)) {
    const selection = $getSelection();
    if (selection) {
      $setSelection(null);
    }
  }

  // NOTE: get select node filter by predicate fn
  // const nodeToSelect = $$selectNextNodeRecursively(
  //   focusNode,
  //   _predicateFn,
  //   _event.shiftKey
  // );

  const nodeToSelect = $$selectNextNode(
    focusNode,
    _predicateFn,
    _event.shiftKey
  );

  // NOTE: stop propagation tab event when no more placeholder
  // if (!nodeToSelect) {
  //   _event.stopImmediatePropagation();
  //   _event.preventDefault();
  //   return true;
  // }

  const parent = focusNode.getParent();

  const parentOrRoot = parent ?? $getRoot();

  if ($isParagraphNode(parent) && nodeToSelect === null) {
    const isFirstChildren = parent.getFirstChild()?.is(focusNode);
    if (isFirstChildren) {
      parentOrRoot.selectEnd();
      return true;
    }
  }

  const caretPosition = $$getCaretPosition(focusNode);
  const firstChild = parentOrRoot.getFirstChild();

  if (focusNode.is(firstChild) && caretPosition === 'end') {
    if ($isElementNode(firstChild) && _event.shiftKey) {
      const _nodeToSelect = firstChild.getLastDescendant();
      if (_selectNode(_nodeToSelect, selection)) {
        _event.preventDefault();
        return false;
      }
    }

    return false;
  }

  const rootLastDescendant = parentOrRoot.getLastDescendant();

  if (
    caretPosition === 'end' &&
    focusNode.is(rootLastDescendant) &&
    !_event.shiftKey
  ) {
    parentOrRoot.selectEnd();
    return true;
  }

  const shouldPreventEventDefault = _event.shiftKey
    ? caretPosition !== 'start'
    : caretPosition !== 'end';

  if (!shouldPreventEventDefault || caretPosition === 'start-end') {
    return false;
  }

  _event.preventDefault();

  // NOTE: handle select node
  if ($isTextNode(nodeToSelect)) {
    nodeToSelect.select(0, nodeToSelect.getTextContentSize());
    return true;
  } else if ($isDecoratorNode(nodeToSelect)) {
    if ($isNodeSelection(selection)) {
      selection.clear();
      selection.add(nodeToSelect.getKey());
    } else {
      const nodeSelection = $createNodeSelection();
      nodeSelection.add(nodeToSelect.getKey());
      $setSelection(nodeSelection);
    }
    return true;
  }

  _event.shiftKey ? parentOrRoot?.selectStart() : parentOrRoot?.selectEnd();

  return true;
}

export function $$tabNavigationNode(
  event: KeyboardEvent,
  predicateFn: (n: LexicalNode | null) => boolean
) {
  const selection = $getSelection();
  if (!$isNodeSelection(selection)) {
    return false;
  }
  const focusNode = $$extractFocusNodeFromSelection(selection);
  if (!predicateFn(focusNode)) {
    return false;
  }
  const nodeToSelect = $$selectNextNodeRecursively(
    focusNode,
    predicateFn,
    event.shiftKey
  );
  if (!nodeToSelect) {
    return false;
  }
  event.preventDefault();

  const nodeKey = nodeToSelect.getKey();
  selection.clear();
  selection.add(nodeKey);
  $setSelection(selection);
  return true;
}

export const handleTabNode = (children) => {
  children.forEach((child) => {
    if (child.type === 'tab') {
      child.mode = '';
    }

    if (child.children) {
      handleTabNode(child.children);
    }
  });
}