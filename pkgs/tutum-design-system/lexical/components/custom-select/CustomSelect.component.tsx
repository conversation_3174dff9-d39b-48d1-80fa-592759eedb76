import React, { memo, useEffect, useRef, useState } from 'react';
import { isNil } from 'lodash';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';

import { ReactSelect, IMenuItem } from '@tutum/design-system/components';
import CustomSelectMenuList from '../custom-select-menu-list';
import { ON_ITEM_SELECTED_COMMAND } from '../select-commands';
import { $focusNextInput } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-suggestion-block/AdditionalInfoSuggestionBlock.component';
import useRtlSelectMode from '@tutum/design-system/lexical/hooks/useRtlSelectMode';

type IMenuItemType = IMenuItem;
export interface ICustomSelectProps {
  placeholder?: string;
  className?: string;
  nodeKey?: string;
  preventMenuOpenDefault?: boolean;
  preventOnKeyDownEvent?: boolean;
  items: any;
  selectedValue?: IMenuItemType;
  tabSelectsValue?: boolean;
  onItemSelect?: (item: any) => void;
  renderOptions?(item: IMenuItemType): React.ReactNode;
  getOptionValue?(item: IMenuItemType): string;
  autoFocus?: boolean;
  onCustomKeyDown?: (
    lowercaseKey: string,
    event: KeyboardEvent,
    currentQuery?: string
  ) => void;
  onFocus?: React.FocusEventHandler<HTMLInputElement>;
  innerInputRef?: (inputId: string, input: HTMLInputElement) => void;
  inputId?: string;
}

const MENU_WIDTH = 480;

const CustomSelect: React.FC<ICustomSelectProps> = (props) => {
  const {
    nodeKey,
    items,
    className,
    getOptionValue,
    onItemSelect,
    selectedValue,
    tabSelectsValue,
    preventMenuOpenDefault,
    autoFocus,
    innerInputRef,
    inputId,
    onFocus,
  } = props;

  const [editor] = useLexicalComposerContext();
  const [internalValue, setInternalValue] = useState('');
  const { selectRef, isRtlMode, handleFocus } = useRtlSelectMode({
    menuWidth: MENU_WIDTH,
  });

  const handleEnterForSelect = (event) => {
    const keyEvent = event.key.toLowerCase();

    if (keyEvent === 'enter') {
      event.stopPropagation();
    }

    props.onCustomKeyDown?.(keyEvent, event, internalValue);
  };

  useEffect(() => {
    if (
      innerInputRef &&
      typeof props.inputId === 'string' &&
      selectRef.current &&
      selectRef.current.inputRef
    ) {
      innerInputRef(
        props.inputId,
        selectRef.current.inputRef as HTMLInputElement
      );
    }
  }, [innerInputRef, props.inputId]);

  const onItemSelected = (newValue: IMenuItemType) => {
    if (nodeKey && newValue) {
      editor.dispatchCommand(ON_ITEM_SELECTED_COMMAND, {
        nodeKey,
        selectedValue: newValue.value as string,
      });
    }
    onItemSelect && onItemSelect(newValue);
    $focusNextInput(editor);
  };

  return (
    <ReactSelect
      isSearchable
      className={className}
      items={items ?? []}
      selectedValue={selectedValue?.value}
      tabSelectsValue={isNil(tabSelectsValue) ? true : tabSelectsValue}
      onItemSelect={onItemSelected}
      menuPosition="fixed"
      menuPlacement="top"
      defaultMenuIsOpen={!preventMenuOpenDefault}
      isRtl={isRtlMode}
      isDocumentTarget={false}
      autoFocus={autoFocus}
      maxMenuHeight={300}
      minMenuHeight={300}
      onKeyDown={handleEnterForSelect}
      onFocus={(event) => {
        handleFocus();
        onFocus?.(event);
      }}
      inputValue={internalValue}
      inputId={inputId}
      onInputChange={(newValue) => setInternalValue(newValue)}
      openMenuOnFocus
      ref={(el) => {
        selectRef.current = el as any;
      }}
      components={{
        DropdownIndicator: null,
        MenuList: CustomSelectMenuList as any,
      }}
      getOptionValue={getOptionValue}
      styles={{
        menu: (provided) => ({
          ...provided,
          minWidth: '240px',
          width: `${MENU_WIDTH}px`,
        }),
      }}
      placeholder={props.placeholder || ' '}
      formatOptionLabel={props.renderOptions}
    />
  );
};

export default memo(CustomSelect);
