import React, { useRef, useEffect, memo } from 'react';
import type { LexicalEditor, Node<PERSON><PERSON> } from 'lexical';
import { $getSelection, TextNode } from 'lexical';
import type {
  LetterTemplate,
  DoctorLetter as DoctorLetterModel,
} from '@tutum/hermes/bff/doctor_letter_common';
import {
  CategoryItemName,
  Variable,
} from '@tutum/hermes/bff/doctor_letter_common';
import {
  $getNodeByKey,
  $nodesOfType,
  createCommand,
  COMMAND_PRIORITY_EDITOR,
  $getRoot,
} from 'lexical';
import { mergeRegister } from '@lexical/utils';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getPerformantUpdateFunc } from '@tutum/design-system/lexical/utils/editorUpdate';
import { VariableNode } from '../Variable/Variable.node';
import { GoaServiceTable } from '@tutum/mvz/module_doctor-letter/DoctorLetterTimeline.store';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import { $getNonEditableNodes, NODES_TYPE_TOKEN } from './Onchange.util';
import { TemplateType } from '@tutum/hermes/bff/legacy/doctor_letter_common';
import { NON_EDITABLE_FOR_EAB } from '../DoctorLetter.constant';

export const FLUSH_VARIABLES_COMMAND = createCommand<Variable[]>();
export const INSERT_SERVICE_CODE_TABLE_COMMAND = createCommand<any>();
export const INSERT_UV_SERVICE_CODE_TABLE_COMMAND = createCommand<any>();

const DIAGNOSIS_TYPES: string[] = [
  CategoryItemName.MedicalDocumentation_AnamnesticDiagnosis,
  CategoryItemName.MedicalDocumentation_AcuteDiagnosis,
  CategoryItemName.MedicalDocumentation_PermanentDiagnosis,
];
const DOCTORINFORMATION_TYPES: string[] = [
  CategoryItemName.DoctorInformation_BankAccount,
  CategoryItemName.DoctorInformation_Bank,
  CategoryItemName.DoctorInformation_IBAN,
  CategoryItemName.DoctorInformation_BIC,
];
const SYMBOLS = [',', '•'];
const opacity0 = 'opacity:0;';
const opacity1 = 'opacity:1;';
const show = 'display: ""';
const hide = 'display:none';

const removeLineIfEmpty = (
  editorVariablesMap: Map<CategoryItemName | string, Variable>,
  variableNodes: VariableNode[],
  types: string[]
) => {
  const matchedNodes: VariableNode[] = variableNodes.filter((node) => {
    return types.includes(node.__variableKey);
  });
  for (const node of matchedNodes) {
    const nodeValue = editorVariablesMap.get(node.__variableKey);
    if (!nodeValue) {
      return;
    }
    if (
      (nodeValue.value || '').trim() === '' ||
      nodeValue.value === `{${node.__variableKey}}`
    ) {
      node.setStyle(hide);
    } else {
      node.setStyle(show);
    }
  }

  // handle symbol
  for (let i = 1; i < matchedNodes.length; i++) {
    const symbolNode: TextNode | null = matchedNodes[i].getPreviousSibling();
    if (!symbolNode || !SYMBOLS.includes(symbolNode.getTextContent().trim())) {
      continue;
    }
    symbolNode.setStyle(matchedNodes[i].getStyle());
  }
  // remove first symbol
  if (matchedNodes.length) {
    const nodes: VariableNode[] = [
      matchedNodes[0],
      ...(matchedNodes[0].getNextSiblings() as VariableNode[]),
    ];
    for (let i = 0; i < nodes.length; i++) {
      if (SYMBOLS.includes(nodes[i].getTextContent().trim())) {
        if (nodes.slice(0, i).every((e) => e.getStyle() === hide)) {
          nodes[i].setStyle(hide);
        }
      }
    }
  }
};

function $shouldHideVariableNode(node: VariableNode) {
  const nodeKey = node.getVariableKey();
  const nodeValue = node.getTextContent();
  // Note: to compare type while we have customize variable
  const compareArr: string[] = [
    CategoryItemName.MedicalDocumentation_GoaTable,
    CategoryItemName.MedicalDocumentation_UvGoaTable,
  ];
  if (compareArr.includes(nodeKey)) {
    return true;
  }
  if (nodeValue === '' || nodeValue === ' ') {
    return true;
  }
  return false;
}

function registerOnVariablesChange(
  templateType: string = '',
  editor: LexicalEditor,
  editorVariablesMap: Map<CategoryItemName | string, Variable>,
  isInitFromAdmin: boolean,
  onChange: (newVariablesMap: Map<CategoryItemName | string, Variable>) => void
) {
  const availableNodeVariableMap = new Map<
    NodeKey,
    CategoryItemName | string
  >();

  const updateAvailableNodeVariableMap = (
    nodeKey: NodeKey,
    variableKey: CategoryItemName | string
  ) => {
    availableNodeVariableMap.set(nodeKey, variableKey);
  };

  const performantUpdate = $getPerformantUpdateFunc(editor);

  const $shouldConvertToNormalTextMode = (variableNode: VariableNode) => {
    if (NODES_TYPE_TOKEN.has(variableNode.__variableKey)) {
      return false;
    }
    const varKey = variableNode.getVariableKey();
    const varValue = variableNode.getTextContent().trim();
    if (varValue === `{${varKey}}`) {
      return false;
    }
    if (!isInitFromAdmin && variableNode.__version === 1) {
      return true;
    }
    return false;
  };

  const $shouldUpdateVariableNode = (node: VariableNode) =>
    editor.getEditorState().read(() => {
      if (!editor.isEditable()) {
        return false;
      }
      // Updated by doctor before
      if (node.__version > 1) {
        return false;
      }
      const nodeVarKey = node.getVariableKey();
      const nodeVarValue = node.getTextContent();
      const currentValueInMap = editorVariablesMap.get(nodeVarKey)?.value;

      if (currentValueInMap != null && nodeVarValue === currentValueInMap) {
        return false;
      }

      return true;
    });

  const $extractVariableFromNode = (node: VariableNode) =>
    editor.getEditorState().read(() => {
      const nodeVarKey = node.getVariableKey();
      const varCategory = node.getVariableCategory();
      const nodeVarValue = node.getTextContent();
      return {
        category: varCategory,
        categoryItemName: nodeVarKey as CategoryItemName,
        value: nodeVarValue,
      };
      // if (
      //   Object.values(CategoryItemName).includes(nodeVarKey as CategoryItemName)
      // ) {
      //   return {
      //     category: varCategory,
      //     categoryItemName: nodeVarKey as CategoryItemName,
      //     value: nodeVarValue,
      //     customizeItemName: undefined,
      //   };
      // }
      // return {
      //   category: varCategory,
      //   categoryItemName: '' as CategoryItemName,
      //   value: nodeVarValue,
      //   customizeItemName: nodeVarKey,
      // };
    });

  const $getAllAvailableVariableNodes = (
    predicateFunc?: (node: VariableNode) => boolean
  ) =>
    editor.getEditorState().read(() => {
      if (typeof predicateFunc !== 'undefined') {
        const targetNode = $nodesOfType(VariableNode)?.find(predicateFunc);
        return targetNode ? [targetNode] : [];
      }
      return $nodesOfType(VariableNode);
    });

  const onCreateVariableNode = (node: VariableNode) => {
    const variable = $extractVariableFromNode(node);
    const varKey = variable.categoryItemName;
    if (varKey) {
      updateAvailableNodeVariableMap(node.getKey(), varKey);
    }
    const currentValue = editorVariablesMap.get(varKey)?.value;
    if (!currentValue) {
      editorVariablesMap.set(varKey, { ...variable });
      return;
    }
    if (!$shouldUpdateVariableNode(node)) return;
    performantUpdate(() => {
      node.setTextContent(currentValue);
    });
  };

  const onDeleteVariableNode = (nodeKey: NodeKey) => {
    const deleteNodeVariableKey = availableNodeVariableMap.get(nodeKey);
    if (!deleteNodeVariableKey) {
      console.warn('Node key has no variable key, this is likely a bug');
      return;
    }
    const isVariableKeyStillExist = $getAllAvailableVariableNodes(
      (node) => node.getVariableKey() === deleteNodeVariableKey
    );
    if (isVariableKeyStillExist.length) {
      // NOTE: if variable key still be used by other variable nodes, skip deletion
      return;
    }
    // NOTE: delete variable key in the map since it's stale
    editorVariablesMap.delete(deleteNodeVariableKey);
  };

  let disableUpdated = false;
  return mergeRegister(
    editor.registerCommand(
      FLUSH_VARIABLES_COMMAND,
      (variables) => {
        if (editor.isComposing()) {
          return false;
        }
        const nonEditableNodes = $getNonEditableNodes(
          $getRoot(),
          NON_EDITABLE_FOR_EAB
        );

        // NOTE: Update the editor variable map
        variables.forEach((v) => {
          editorVariablesMap.set(v.categoryItemName, {
            category: v.category,
            categoryItemName: v.categoryItemName,
            value: v.value,
            customizeItemName: v.customizeItemName,
          });
        });
        const variableNodes = $nodesOfType(VariableNode);
        for (const variableNode of variableNodes) {
          if ($shouldConvertToNormalTextMode(variableNode)) {
            variableNode.setMode('normal');
          }
          const prevStyle = variableNode
            .getStyle()
            .replace(opacity0, '')
            .replace(opacity1, '');

          if (
            $shouldUpdateVariableNode(variableNode) &&
            variableNode.isAttached() &&
            !variableNode.isComposing()
          ) {
            const variableKey = variableNode.getVariableKey();
            const variable = editorVariablesMap.get(variableKey);

            if (!isNil(variableNode)) {
              variableNode.setTextContent(variable?.value || '');
            }
          }

          if ($shouldHideVariableNode(variableNode)) {
            variableNode.setStyle(
              `${prevStyle ? `${prevStyle};` : ''}${opacity0}`
            );
          } else {
            variableNode.setStyle(
              `${prevStyle ? `${prevStyle};` : ''}${opacity1}`
            );
          }
        }
        if (TemplateType.TemplateType_Eab === templateType && !disableUpdated) {
          for (const node of nonEditableNodes) {
            const domElement = editor.getElementByKey(node.getKey());
            if (domElement) {
              disableUpdated = true;
              domElement.setAttribute('contenteditable', 'false');
              domElement.setAttribute(
                'style',
                domElement.getAttribute('style') +
                ';cursor:not-allowed;user-select:none;pointer-events:none'
              );
            }
          }
        }

        // ERASE LINE IF EMPTY
        removeLineIfEmpty(editorVariablesMap, variableNodes, DIAGNOSIS_TYPES);
        removeLineIfEmpty(
          editorVariablesMap,
          variableNodes,
          DOCTORINFORMATION_TYPES
        );

        return true;
      },
      COMMAND_PRIORITY_EDITOR
    ),
    editor.registerMutationListener(VariableNode, (nodesMap) => {
      Array.from(nodesMap.entries()).forEach(([nodeKey, mutationType]) => {
        const variableNode = editor
          .getEditorState()
          .read(() => $getNodeByKey<VariableNode>(nodeKey)) as VariableNode;

        switch (mutationType) {
          case 'created': {
            onCreateVariableNode(variableNode);
            break;
          }
          case 'destroyed': {
            const allAvailableVarNodes = $getAllAvailableVariableNodes();
            if (!allAvailableVarNodes?.length) {
              editorVariablesMap.clear();
              break;
            }
            onDeleteVariableNode(nodeKey);
            break;
          }
          case 'updated': {
            editor.update(() => {
              const selection = $getSelection();
              if (selection === null) {
                return false;
              }
              if ($shouldConvertToNormalTextMode(variableNode)) {
                const nodes = selection.getNodes();
                const node0 = nodes[0];
                if (node0 && node0.getKey() === variableNode.getKey()) {
                  variableNode.setMode('normal');
                  variableNode.setVersion(2);
                }
              }
            });
          }
        }

        onChange(editorVariablesMap);
      });
    })
  );
}

export const DoctorLetterOnChangePlugin = memo(
  ({
    defaultValue,
    onChange,
    goaServiceTable,
    initFromAdmin,
  }: {
    initFromAdmin: boolean;
    goaServiceTable: GoaServiceTable;
    defaultValue?: LetterTemplate | DoctorLetterModel;
    onChange: (
      stringifiedEditorState: string,
      template: Pick<LetterTemplate, 'variables'>
    ) => void;
  }): JSX.Element | null => {
    const [editor] = useLexicalComposerContext();
    const stringifiedState = useRef<string | null>(null);
    // NOTE: this map holds all availabe variables inside an editor
    const editorGlobalVariablesMap = useRef<
      Map<CategoryItemName | string, Variable>
    >(new Map<CategoryItemName, Variable>());

    useEffect(() => {
      if (!editor.hasNodes([VariableNode])) {
        throw new Error(
          'DoctorLetterOnChangePlugin: VariableNode is not registered on editor'
        );
      }
    }, [editor]);

    useEffect(() => {
      if (!defaultValue) return;

      console.log('debug defaultValue', defaultValue);

      // NOTE: load default editor state
      if (defaultValue.body) {
        editor.setEditorState(editor.parseEditorState(defaultValue.body));
      }

      // NOTE: load variables into global map
      if ((defaultValue as LetterTemplate).variables?.length) {
        const variables = (defaultValue as LetterTemplate).variables;
        variables.forEach((v) => {
          editorGlobalVariablesMap.current?.set(v.categoryItemName, v);
        });
      }
    }, [editor, defaultValue]);

    useEffect(() => {
      if (!defaultValue) return;
      return mergeRegister(
        registerOnVariablesChange(
          (defaultValue as DoctorLetterModel).type,
          editor,
          editorGlobalVariablesMap.current,
          initFromAdmin,
          (newVariablesMap) => {
            editorGlobalVariablesMap.current = newVariablesMap;
          }
        )
      );
    }, [editor, goaServiceTable, defaultValue]);

    return (
      <OnChangePlugin
        ignoreSelectionChange
        onChange={(state) => {
          const stateAsString = JSON.stringify(state.toJSON());
          stringifiedState.current = stateAsString;
          onChange(stateAsString, {
            variables:
              Array.from(editorGlobalVariablesMap.current?.values()) ?? [],
          });
        }}
      />
    );
  }
);
