import {
  $getRoot,
  $isParagraphNode,
  $isTextNode,
  LexicalNode,
  RootNode,
} from 'lexical';
import { CategoryItemName } from '@tutum/hermes/bff/legacy/doctor_letter_common';
import { PageSheetNode } from '../PaperSheet/PageSheet.node';

export const $getNonEditableNodes = (root: RootNode, targetText: string) => {
  let foundNode: LexicalNode | null = null;
  const nonEditableNodes: LexicalNode[] = [];
  const rootNode = root ?? $getRoot();

  const searchNodes = (rootNode: RootNode | LexicalNode) => {
    if ($isTextNode(rootNode) || $isParagraphNode(rootNode)) {
      const textContent = rootNode.getTextContent();
      if (textContent.includes(targetText)) {
        foundNode = rootNode;
        return true;
      }
    } else {
      console.log('debug rootNode', rootNode);
      const children = (rootNode as RootNode).getChildren();
      console.log('debug children', children);
      for (const childNode of children) {
        if (searchNodes(childNode)) {
          return true;
        }
      }
    }
    return false;
  };

  const targetNode = searchNodes(rootNode);

  if (targetNode) {
    const EabHeader = foundNode as unknown as LexicalNode;
    if (!EabHeader) return [];

    const nodes = rootNode
      .getChildren()
      .map((pagesheet: PageSheetNode) => pagesheet.getChildren())
      .flat();

    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.getKey() !== EabHeader.getKey()) {
        nonEditableNodes.push(node);
      } else {
        break;
      }
    }

    return nonEditableNodes;
  }

  return [];
};

export const NODES_TYPE_TOKEN = new Set<string>([
  CategoryItemName.BSNR_BSNR,
  CategoryItemName.BSNR_PracticeName,
  CategoryItemName.BSNR_PostalCodeCity,
  CategoryItemName.BSNR_StreetNumber,
  CategoryItemName.BSNR_Phone,
  CategoryItemName.BSNR_Fax,
  CategoryItemName.BSNR_Email,
  CategoryItemName.BSNR_BankName,
  CategoryItemName.BSNR_Stamp,
  CategoryItemName.General_SenderName,
  CategoryItemName.General_Sender_BSNR_LANR,
  CategoryItemName.General_ReceiverName,
  CategoryItemName.General_ReceiverStreetNo,
  CategoryItemName.General_ReceiverPostalCodeCity,
  CategoryItemName.General_ReceiverPhone,
  CategoryItemName.General_ReceiverMail,
  CategoryItemName.General_ReceiverSalutation,
]);
