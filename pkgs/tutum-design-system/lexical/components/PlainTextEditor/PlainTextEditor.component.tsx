import React, { useId } from 'react';
// import isFunction from 'lodash/isFunction';
import { isNil } from 'lodash';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { PlainTextEditorProps } from './PlainTextEditor.type';
import {
  defaultLexicalComposerConfig,
  TEST_ID,
} from './PlainTextEditor.constant';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import PlainTextEditorStyleWrapper from './PlainTextEditor.styled';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';

const PlainTextEditor: React.FC<PlainTextEditorProps> = (props) => {
  const {
    className,
    placeholder,
    onChange,
    children,
    editorConfig,
    nodes = [],
    editable = true,
    initEditorState,
  } = props;

  const plainTextPluginKey = useId();

  const onChangePluginKey = useId();

  const initialEditorState = isNil(initEditorState)
    ? undefined
    : initEditorState;

  return (
    <LexicalComposer
      initialConfig={{
        ...defaultLexicalComposerConfig,
        nodes: [...defaultLexicalComposerConfig.nodes!, ...nodes],
        ...editorConfig,
        editable,
        editorState: initialEditorState,
      }}
    >
      <PlainTextEditorStyleWrapper className="sl-lexical-plaintext-editor-wrapper">
        <PlainTextPlugin
          key={plainTextPluginKey}
          ErrorBoundary={LexicalErrorBoundary}
          contentEditable={
            <ContentEditable
              data-test-id={TEST_ID}
              className={getCssClass(
                'sl-lexical-plaintext-contenteditable',
                className
              )}
            />
          }
          placeholder={
            <span className="sl-lexical-plaintext-placeholder">
              {placeholder}
            </span>
          }
        />
        <OnChangePlugin
          key={onChangePluginKey}
          onChange={onChange}
          ignoreSelectionChange
          ignoreHistoryMergeTagChange
        />
        {children}
      </PlainTextEditorStyleWrapper>
    </LexicalComposer>
  );
};

export default PlainTextEditor;
