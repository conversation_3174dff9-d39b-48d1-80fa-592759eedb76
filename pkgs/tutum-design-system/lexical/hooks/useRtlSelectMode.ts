import { useRef, useState } from 'react';
import { SelectInstance } from 'react-select';

interface useRtlSelectModeProps {
  menuWidth?: number;
}

const useRtlSelectMode = ({ menuWidth = 480 }: useRtlSelectModeProps) => {
  const selectRef = useRef<SelectInstance<any> | null>(null);
  const [isRtlMode, setIsRtlMode] = useState(false);

  const handleFocus = () => {
    if (selectRef.current) {
      const selectElement = selectRef.current.controlRef;
      if (selectElement) {
        const rect = selectElement.getBoundingClientRect();
        const rightEdge = rect.left + menuWidth;
        const windowWidth = window.innerWidth;

        setIsRtlMode(rightEdge > windowWidth);
      }
    }
  };

  return {
    selectRef,
    isRtlMode,
    handleFocus,
  };
};

export default useRtlSelectMode;
