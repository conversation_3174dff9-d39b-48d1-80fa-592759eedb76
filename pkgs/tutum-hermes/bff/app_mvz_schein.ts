/* eslint-disable */
// This code was autogenerated from app/mvz/schein.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as catalog_sdkt_common from "./catalog_sdkt_common"
import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"
import * as private_schein_common from "./private_schein_common"
import * as schein_common from "./schein_common"
import * as common1 from "./timeline_common"


// Type definitions
		export interface ScheinChangedResponse {
				patientId: string
				scheinId: string
		}
	

		export interface EventScheinChanged {
				data: ScheinChangedResponse
				eventName: EventName
		}
	

		export interface EventScheinChangedResponse {
				data: EventScheinChanged
		}
	

		export interface EventCreateRemoveSchein {
				data: ScheinChangedResponse
		}
	

		export interface IsValidRequest {
				createScheinRequest: CreateScheinRequest
				insurances: Array<patient_profile_common.InsuranceInfo>
		}
	

		export interface IsValidResponse {
				error?: {[key:string]:common.FieldError}
		}
	

		export interface GetKTABsRequest {
				vKNR: string
				specialGroup: patient_profile_common.SpecialGroupDescription
				patientId: string
				quarter: number
				year: number
				bsnr: string
		}
	

		export interface GetKTABsResponse {
				kTABValue: Array<catalog_sdkt_common.KTABValue>
		}
	

		export interface MarkBillRequest {
				contractId?: string
				mainGroup: schein_common.MainGroup
				excludeScheinIds: Array<string>
				validScheinsIds: Array<string>
		}
	

		export interface TakeOverDiagnoseInfo {
				id: string
				isTreatmentRelevant: boolean
		}
	

		export interface CreateScheinRequest {
				scheinId?: string
				patientId: string
				doctorId?: string
				scheinMainGroup: schein_common.MainGroup
				kvTreatmentCase: schein_common.TreatmentCaseNames
				kvScheinSubGroup?: string
				g4101Year: number
				g4101Quarter: number
				tariffType?: string
				bgType?: string
				bgAccidentDate?: number
				bgAccidentTime?: string
				bgWorkingTimeFrom?: string
				bgWorkingTimeTo?: string
				bgEmployerName?: string
				bgEmployerStreet?: string
				bgEmployerHousenumber?: string
				bgEmployerPostcode?: string
				bgEmployerCity?: string
				bgEmployerCountry?: string
				hzvContractId?: string
				scheinDetails?: schein_common.ScheinDetail
				insuranceId: string
				excludeFromBilling: boolean
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				skipFields?: boolean
				g4122?: string
				g4101: string
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
				assignedToBsnrId?: string
		}
	

		export interface CreateScheinResponse {
				scheinItem?: schein_common.ScheinItem
				fieldErrors: {[key:string]:common.FieldError}
		}
	

		export interface GetSubGroupFromMasterDataRequest {
				bsnr: string
		}
	

		export interface GetSubGroupFromMasterDataResponse {
				keys: Array<string>
		}
	

		export interface GetBillingAreaFromMasterDataRequest {
				bsnr: string
				subgroup: string
		}
	

		export interface GetBillingAreaFromMasterDataResponse {
				keys: Array<string>
		}
	

		export interface Rezidiv {
				code: string
				content: string
		}
	

		export interface GetRezidivListResponse {
				data: Array<Rezidiv>
		}
	

		export interface GetPsychotherapyByIdRequest {
				psychotherapyId: string
		}
	

		export interface GetPsychotherapyByIdResponse {
				data: schein_common.Psychotherapy
		}
	

		export interface GetScheinByInsuranceIdRequest {
				insuranceId: string
		}
	

		export interface GetScheinByInsuranceIdResponse {
				scheinItem: schein_common.ScheinItem
		}
	

		export interface GetScheinsByInsuranceIdResponse {
				scheinItems: Array<schein_common.ScheinItem>
		}
	

		export interface RevertTechnicalScheinRequest {
				scheinId: string
		}
	

		export interface CreatePrivateScheinRequest {
				schein: private_schein_common.PrivateScheinItem
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface UpdatePrivateScheinRequest {
				schein: private_schein_common.PrivateScheinItem
		}
	

		export interface DeletePrivateScheinRequest {
				scheinId: string
				patientId: string
		}
	

		export interface IsValidPrivateScheinRequest {
				schein: private_schein_common.PrivateScheinItem
		}
	

		export interface GetPrivateScheinByIdRequest {
				scheinId: string
		}
	

		export interface GetGoaFactorValueRequest {
				scheinId: string
				goaNumber: string
		}
	

		export interface GetGoaFactorValueResponse {
				value: number
		}
	

		export interface TakeOverScheinDiagnisToRequest {
				scheinId: string
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface TakeOverDiagnosisByScheinIdRequest {
				scheinId: string
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface GetScheinItemByIdRequest {
				scheinId: string
		}
	

		export interface GetScheinItemByIdResponse {
				scheinItems: schein_common.ScheinItem
		}
	

		export interface GetScheinItemByIdsRequest {
				scheinIds: Array<string>
		}
	

		export interface GetScheinItemByIdsResponse {
				scheinItems: Array<schein_common.ScheinItem>
		}
	

		export interface CreateBgScheinRequest {
				schein: schein_common.BgScheinItem
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface UpdateBgScheinRequest {
				schein: schein_common.BgScheinItem
		}
	

		export interface DeleteBgScheinRequest {
				scheinId: string
				patientId: string
		}
	

		export interface IsValidBgScheinRequest {
				schein: schein_common.BgScheinItem
				insurances: patient_profile_common.InsuranceInfo
		}
	

		export interface GetBgScheinByIdRequest {
				scheinId: string
		}
	

		export interface CheckDummyVknrRequest {
				scheinId: string
		}
	

		export interface CheckDummyVknrResponse {
				isDummy: boolean
		}
	

		export interface GetTotalScheinsRequest {
				patientId: string
		}
	

		export interface GetTotalScheinsResponse {
				totalScheins: number
		}
	


// enum definitions
    export enum EventName {
        EventName_UpdateSchein = "EventName_UpdateSchein",
	}


// event definition constant ----------------------------------------
       	const EVENT_ScheinChanged = "api.app.mvz.AppMvzSchein.ScheinChanged";
       	const EVENT_ScheinChangedResponse = "api.app.mvz.AppMvzSchein.ScheinChangedResponse";
       	const EVENT_CreateRemoveSchein = "api.app.mvz.AppMvzSchein.CreateRemoveSchein";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenScheinChanged(handler: (data: EventScheinChanged) => void): void {
			const [response, setResponse] = useState<EventScheinChanged>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_ScheinChanged, _listener);
				return () => window.removeEventListener(EVENT_ScheinChanged, _listener);
			}, []);
        }
        export function useListenScheinChangedResponse(handler: (data: EventScheinChangedResponse) => void): void {
			const [response, setResponse] = useState<EventScheinChangedResponse>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_ScheinChangedResponse, _listener);
				return () => window.removeEventListener(EVENT_ScheinChangedResponse, _listener);
			}, []);
        }
        export function useListenCreateRemoveSchein(handler: (data: EventCreateRemoveSchein) => void): void {
			const [response, setResponse] = useState<EventCreateRemoveSchein>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_CreateRemoveSchein, _listener);
				return () => window.removeEventListener(EVENT_CreateRemoveSchein, _listener);
			}, []);
        }

