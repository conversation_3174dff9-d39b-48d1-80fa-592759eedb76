/* eslint-disable */
// This code was autogenerated from service/domains/sdicd_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"


// Type definitions
		export interface Age {
				value: string
				unit: string
		}
	

		export interface AgeLimit {
				ageLow?: Age
				ageHigh?: Age
				reject: string
		}
	

		export interface Gender {
				value: string
				reject: string
		}
	

		export interface Meta {
				gender?: Gender
				age?: AgeLimit
		}
	

		export interface ThesaurusWithSpecialists {
				value: string
				specialists?: Array<string>
				sdvaRefs?: Array<string>
		}
	

		export interface IcdItem {
				id: string
				code: string
				description: string
				billable: boolean
				rareDiseaseEu: boolean
				schlusselnummer: boolean
				ifSG: boolean
				noPermanent: boolean
				group: boolean
				notionMark: string
				thesaurus?: Array<ThesaurusWithSpecialists>
				meta?: Meta
				sdvaRefs?: Array<string>
				type: TagType
		}
	

		export interface IcdSeachItem {
				id: string
				group: boolean
				code: string
				description: string
				meta?: Meta
				sdvaRefs?: Array<string>
				type: TagType
				noPermanent: boolean
				highlight: {[key:string]:common.Highlight}
		}
	


// enum definitions
    export enum TagType {
        Bezeichnung = "bezeichnung",
        Thesaurus = "thesaurus",
	}

    export enum IcdSearchCatalog {
        SystematicAndAlphabetical = "1",
        Systematic = "2",
        GeneralPractitioner = "3",
        SpecialistGroup = "4",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

