/* eslint-disable */
// This code was autogenerated from app/mvz/medicine.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as timeline from "./app_mvz_timeline"
import * as common from "./common"
import * as common1 from "./form_common"
import * as common2 from "./qes_common"
import * as common3 from "./repo_medicine_common"
import * as bmp from "./service_domains_bmp"


// Type definitions
		export interface DrugInformation {
				aTC: string
				aTCA: string
				aTCName: string
				effectiveness: string
				noOfSubstances: number
				components: Array<Component>
		}
	

		export interface Component {
				substances: Array<Substance>
				componentNumber: number
				componentName: string
				physicalForm: number
				absoluteUnit: string
				absoluteAmount: string
				relativeUnit: string
				relativeAmount: string
				relativeForm: string
				ethanolPercentage: string
				releaseBehavior: number
				galenicBasicForm: number
		}
	

		export interface Substance {
				substanceType: number
				rank: number
				unit: string
				amount: number
				equivalentSubstance: number
				suffix: string
				name: string
				gbaUrl: string
				id: number
		}
	

		export interface HintsAndWarning {
				standardIndex: string
				text: string
		}
	

		export interface LegalNote {
				type: number
				text: string
		}
	

		export interface Medicine {
				id: string
				pzn: string
				search: string
				productInformation?: ProductInformation
				hintsAndWarnings?: Array<HintsAndWarning>
				drugInformation?: DrugInformation
				packagingInformation?: PackagingInformation
				priceInformation?: PriceInformation
				textInformation?: TextInformation
				colorCategory?: ColorCategory
				medicationPlanInformation?: MedicationPlanInformation
				packageExtend?: common3.PackageExtend
		}
	

		export interface ColorCategory {
				sorting: number
				drugCategory: string
				isInPriscusList?: boolean
		}
	

		export interface PackageSize {
				packingComponent: number
				classification: number
				nop: string
		}
	

		export interface PackagingInformation {
				quantity: string
				unit: string
				packageSize: PackageSize
				amountText: string
				quantityNumber?: number
				nameRecipe: string
		}
	

		export interface PriceList {
				priceType?: string
				value?: number
		}
	

		export interface PriceInformation {
				pharmacySalePrice: number
				discount: {[key:string]:DiscountDetail}
				copaymentSorting: number
				copayment: number
				totalPayment: number
				totalCoPayment?: number
				additionalPayment: number
				additionalCost?: number
				priceComparisonGroup1: number
				priceComparisonGroup2: number
				isFreeCopayment?: boolean
				pricelist: Array<PriceList>
				fixedAmount?: number
		}
	

		export interface DiscountDetail {
				additionalPaymentIndicator: number
				discountFactor: number
				preferAutIdem: number
		}
	

		export interface Divisible {
				dIVISIBILITYTYPECODE: string
				dIVISIBLE2_FLAG: number
				dIVISIBLE3_FLAG: number
				dIVISIBLE4_FLAG: number
				dIVISIBLE_FLAG: number
		}
	

		export interface BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST {
				iD: string
				dOCUMENT_ID: number
				dATUM_BE_VOM: string
				dATUM_BE_BIS: string
				nAME_PAT_GR: string
		}
	

		export interface GPA {
				aWG: string
				iD: number
				iD_BE_AKZ: string
				qS_ATMP: number
				rEG_NB: string
				sOND_ZUL_ATMP: number
				sOND_ZUL_AUSN: number
				sOND_ZUL_BESOND: number
				sOND_ZUL_ORPHAN: number
				uES_BE: string
				uRL: string
				uRL_QS_ATMP: string
				uRL_QS_ATMP_TEXT: string
				uRL_TEXT: string
				documentIds: Array<number>
				bENIFIT_ASSESSMENT_PATIENT_GROUP_LIST: Array<BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST>
		}
	

		export interface ARMDocument {
				fileName: string
				amrTypeCode: string
		}
	

		export interface ProductInformation {
				shortName: string
				name: string
				provider: string
				dosageForm: string
				tReceipt: number
				negativeList: number
				importReimport: boolean
				documentRequired: number
				lifeStyle: number
				prescribable: number
				isOTC: boolean
				conditionalReimbursement: number
				isExcludedFromAppendixIII: boolean
				isExistingInGBA: boolean
				pharmaciesAvailability: number
				productGroup: number
				copaymentExemption: number
				copaymentExemptionForBloodAndUrineTest: number
				legalNotes: Array<LegalNote>
				formType: FormType
				activeIngredients: Array<string>
				oTXFlag: number
				orderComponents: Array<string>
				transfusionlawFlag: number
				medicineProductFlag: number
				kReceipt: boolean
				amrMessagesCount: number
				importProductFlag: number
				sampleProductFlag: number
				pharmacyRequired: boolean
				gPAs: Array<GPA>
				divisible: Divisible
				techInformationId: number
				id: number
				providerID: number
				dosageFormCode: string
				pharmFormCodeIFA: string
				regulationTypeCodes: Array<string>
				hasAmr3ConstraintFlag: number
				hasAmr3ExclusionFlag: number
				dispensingTypeCode: string
				medicineProductExceptionFlag: number
				praxisbesonderheitDocument?: ARMDocument
				hasAmr1Flag: number
				isDigaFlag: boolean
				isNegativeList: boolean
				isLifeStyle: boolean
				isBandage: boolean
		}
	

		export interface Substances {
				substanceType: string
				rank: string
				unit: string
				amount: number
				equivalentSubstance: string
				suffix: string
				name: string
		}
	

		export interface TextInfomationItem {
				codeName: string
				name: string
				content: string
				order: number
		}
	

		export interface TextInformation {
				updatedDate: string
				items: Array<TextInfomationItem>
		}
	

		export interface Medicines {
				medicines: Array<Medicine>
		}
	

		export interface Sort {
				field: SortField
				order: common.Order
		}
	

		export interface MedicationPlanInformation {
				medicationPlanUnitCode: string
		}
	

		export interface AddToShoppingBagRequest {
				patientId?: string
				doctorId?: string
				contractId?: string
				ikNumber?: number
				medicine: MedicineShoppingBagInfo
				bsnr?: string
				assignedToBsnrId?: string
		}
	

		export interface MedicineShoppingBagInfo {
				id?: string
				type: MedicineType
				pzn?: string
				name: string
				quantity: number
				packagingInformation?: PackagingInformation
				productInformation?: ProductInformation
				patientId?: string
				doctorId?: string
				currentFormType: FormType
				intakeInterval?: IntakeInterval
				formSetting?: string
				autIdem?: boolean
				asNeeded?: boolean
				furtherInformation?: string
				availableSizes?: Array<AvailableSize>
				drugInformation?: DrugInformation
				textInformation?: TextInformation
				priceInformation?: PriceInformation
				colorCategory?: ColorCategory
				substitutionPrescription?: boolean
				specialExceedings?: Array<SpecialExceeding>
				kBVMedicineId?: number
				isEPrescription?: boolean
				medicationPlanInformation?: MedicationPlanInformation
				isArtificialInsemination?: boolean
				bsnr?: string
				vaccinate?: boolean
				drugFormInformation?: string
		}
	

		export interface AvailableSize {
				pzn: string
				size: string
				unit: string
				quantity: string
				packingComponent: number
				classification: number
		}
	

		export interface IntakeInterval {
				morning?: number
				evening?: number
				afternoon?: number
				night?: number
				freetext?: string
				dJ?: boolean
		}
	

		export interface ShoppingBagResponse {
				medicine: MedicineShoppingBagInfo
		}
	

		export interface UpdateShoppingBagQuantityRequest {
				patientId?: string
				doctorId?: string
				medicineId: string
				contractId?: string
				quantity: number
				bsnr?: string
		}
	

		export interface RemoveFromShoppingBagRequest {
				patientId?: string
				doctorId?: string
				medicineId?: string
				contractId?: string
				shoppingBagId?: string
				bsnr?: string
		}
	

		export interface GetShoppingBagRequest {
				patientId?: string
				doctorId?: string
				contractId?: string
				bsnr?: string
				assignedToBsnrId?: string
		}
	

		export interface GetShoppingBagRequestResponse {
				shoppingBagId: string
				patientId: string
				doctorId: string
				treatmentDoctorId: string
				medicines: Array<MedicineShoppingBagInfo>
				bsnr: string
				assignedToBsnrId?: string
		}
	

		export interface GetSubsitutionResponse {
				subsitutions: Array<SubsitutionResponse>
		}
	

		export interface SubsitutionResponse {
				key: string
				total: number
				aTCA: string
				aTCName: string
				medicines: Array<Medicine>
		}
	

		export interface EventShoppingBagRequest {
				type: ShoppingBagEventType
				payload: MedicineShoppingBagInfo
				treatmentDoctorId?: string
				contractId?: string
				medicineDeletedIds?: Array<string>
				assignedToBsnrId?: string
		}
	

		export interface CheckMissingDiagnoseResponse {
				showWarning: boolean
		}
	

		export interface AvailableSizeRequest {
				shortName: string
				provider: string
		}
	

		export interface UpdateFormRequest {
				patientId: string
				doctorId: string
				medicineIDs: Array<string>
				currentFormType: FormType
				contractId?: string
		}
	

		export interface PrescribeRequest {
				patientId?: string
				doctorId?: string
				formInfos: Array<FormInfo>
				contractId?: string
				treatmentDoctorId?: string
				encounterCase?: string
				medicineAutIdemIds: Array<string>
				scheinId?: string
				bsnr?: string
				hasSupportForm907?: boolean
				assignedToBsnrId?: string
				preventGetPatientProfile?: boolean
		}
	

		export interface DateRange {
				startDate: number
				endDate?: number
		}
	

		export interface FormInfo {
				medicineIDs: Array<string>
				formSetting: string
				currentFormType: FormType
				prescribeDate?: number
				isShowFavHint: boolean
				printDate?: number
				ePrescription?: common3.EPrescription
				printOption?: common1.PrintOption
				bundleUrl?: string
				pdfUrl?: string
				hasChangedSprechsundenbedarf?: boolean
		}
	

		export interface FormInfoResponse {
				id: string
				formInfoResponse: Array<MedicineShoppingBagInfo>
				formSetting: string
				currentFormType: FormType
				prescribeDate?: number
				isNotPicked: boolean
				isShowFavHint: boolean
				ePrescription?: common3.EPrescription
				bundleUrl?: string
				hasChangedSprechsundenbedarf?: boolean
				eRezeptStatus?: common2.DocumentStatus
		}
	

		export interface PrintResult {
				currentFormType: FormType
				formUrl: string
		}
	

		export interface PrescribeResponse {
				id: string
				patientId: string
				doctorId: string
				formInfoResponses: Array<FormInfoResponse>
				printDate: number
				contractId: string
				treatmentDoctorId: string
				createdDate: number
				encounterId: string
				printResults: Array<PrintResult>
		}
	

		export interface UpdateShoppingBagInformationRequest {
				patientId?: string
				doctorId?: string
				contractId?: string
				medicineId?: string
				furtherInformation?: string
				intakeInterval?: IntakeInterval
				packageSizeRequest?: PackageSizeRequest
				treatmentDoctorId?: string
				asNeeded?: boolean
				freeText?: string
				substitutionPrescription?: boolean
				specialExceedings?: Array<SpecialExceeding>
				isEPrescription?: boolean
				isArtificialInsemination?: boolean
				bsnr?: string
				vaccinate?: boolean
				drugFormInformation?: string
				assignedToBsnrId?: string
		}
	

		export interface PackageSizeRequest {
				pzn: string
		}
	

		export interface GetMedicationPrescribeRequest {
				patientId?: string
				bsnr?: string
				sortField: SortFieldConsultation
				order: common.Order
		}
	

		export interface MedicationPrescribeResponse {
				id: string
				type: MedicineType
				pzn?: string
				name: string
				packagingInformation?: PackagingInformation
				productInformation?: ProductInformation
				intakeInterval: IntakeInterval
				furtherInformation: string
				drugInformation?: DrugInformation
				prescribeDate: number
				treatmentDoctorId: string
				textInformation?: TextInformation
				medicationPlanId?: string
				asNeeded?: boolean
				contractId?: string
				priceInformation?: PriceInformation
				colorCategory?: ColorCategory
				autIdem: boolean
				quantity: number
				currentFormType: FormType
				formInfoId: string
				substitutionPrescription?: boolean
				specialExceedings?: Array<SpecialExceeding>
				kBVMedicineId?: number
				medicationPlanInformation?: MedicationPlanInformation
				isEPrescription: boolean
				printDate?: number
				fixedAmount?: number
				isFavourite: boolean
				formInfo: FormInfoResponse
				assignedToBsnrId?: string
				isArtificialInsemination: boolean
				vaccinate: boolean
		}
	

		export interface GetMedicationPrescribeResponse {
				patientId: string
				bsnr: string
				medicationPrescribeResponses: Array<MedicationPrescribeResponse>
		}
	

		export interface EventMedicationPrescribe {
				eventType: EventMedicationPrescribeType
				payload: MedicationPrescribeResponse
				patientId: string
				doctorId: string
				contractId?: string
				bsnr?: string
		}
	

		export interface DeleteMedicationPrescribeRequest {
				id: string
				patientId: string
		}
	

		export interface DeleteMedicationPrescribeResponse {
				id: string
				patientId: string
		}
	

		export interface CreateMedicationPlanRequest {
				prescribedMedicationId: string
				hint: string
		}
	

		export interface EventMedicationPlanChanged {
				eventType: EventMedicationPlanChangedType
				payload: bmp.EntryResponse
				encounterCase: string
				contractType?: common.ContractType
				patientId: string
		}
	

		export interface DeleteMedicationPlanRequest {
				medicationPlanId: string
				patientId: string
				doctorId: string
				contractId?: string
				encounterCase: string
		}
	

		export interface DeleteMedicationPlanResponse {
				id: string
				patientId: string
		}
	

		export interface CheckMissingDiagnosesRequest {
				patientId: string
				doctorId: string
				contractId?: string
				ikNumber?: number
				pzns: Array<string>
		}
	

		export interface CheckMissingDiagnosesResponse {
				pzns: Array<string>
		}
	

		export interface EventRefillMedicine {
				patientId: string
				medicineInfos: Array<MedicineShoppingBagInfo>
		}
	

		export interface ViewMedicationForm {
				treatmentDoctorId: string
				formInfo: FormInfoResponse
				assignedToBsnrId: string
		}
	

		export interface EventViewMedicationForm {
				patientId: string
				viewMedicationForm: ViewMedicationForm
				eventType: ViewMedicationFormType
		}
	

		export interface UpdateTreatmentDoctorMedicationFormRequest {
				medicationFormId: string
				treatmentDoctorId: string
				patientId: string
				assignedToBsnrId?: string
		}
	

		export interface PrintFormRequest {
				formId: string
				printOption: common1.PrintOption
				hasSupportForm907?: boolean
				patientId: string
				preventGetPatientProfile?: boolean
		}
	

		export interface PrintFormResponse {
				printResults: Array<PrintResult>
		}
	


// enum definitions
    export enum SortField {
        Size = "Size",
        Price = "Price",
        TotalPayment = "TotalPayment",
	}

    export enum SpecialExceeding {
        A = "A",
        N = "N",
        S = "S",
        SZ = "SZ",
        ST = "ST",
	}

    export enum FormType {
        KREZ = "KREZ",
        GREZ = "GREZ",
        BTM = "BTM",
        TPrescription = "TPrescription",
        Private = "Private",
        AOKNordwet = "AOKNordwet",
        AOKBremen = "AOKBremen",
        Muster16aBay = "Muster16aBay",
	}

    export enum MedicineType {
        FreeText = "FreeText",
        HPM = "HPM",
        KBV = "KBV",
	}

    export enum ShoppingBagEventType {
        Add = "Add",
        Update = "Update",
        Delete = "Delete",
        UpdateForm = "UpdateForm",
        UpdateDoctorId = "UpdateDoctorId",
        DeleteShoppingBag = "DeleteShoppingBag",
	}

    export enum SpecialIdentifier {
        SpecialIdentifierA = "A - Überschreitung der Höchstverschreibungsmenge innerhalb von 30 Tagen",
        SpecialIdentifierN = "N - Notfall Verschreibung",
        SpecialIdentifierS = "S - Substitutionsmittel Verschreibung",
        SpecialIdentifierSZ = "SZ - § 5 Absatz 8 BtMVV",
        SpecialIdentifierST = "ST - § 5 Absatz 9 BtMVV",
	}

    export enum SortFieldConsultation {
        PrescribeDate = "PrescribeDate",
        Tradename = "Tradename",
        SizeMed = "SizeMed",
        PrescribedBy = "PrescribedBy",
        Status = "Status",
	}

    export enum EventMedicationPrescribeType {
        Remove = "Remove",
        PrescribeSuccess = "PrescribeSuccess",
        RemovePrescribeMedicationPlan = "RemovePrescribeMedicationPlan",
        CreateMedicationPlanSuccess = "CreateMedicationPlanSuccess",
	}

    export enum EventMedicationPlanChangedType {
        RemoveMedicationPlan = "RemoveMedicationPlan",
        CreateMedicationPlan = "CreateMedicationPlan",
        UpdateMedicationPlan = "UpdateMedicationPlan",
	}

    export enum ViewMedicationFormType {
        ViewForm = "ViewForm",
        ChangeTreatmentDoctor = "ChangeTreatmentDoctor",
	}


// event definition constant ----------------------------------------
       	const EVENT_ShoppingBagRequest = "api.app.mvz.AppMvzMedicine.ShoppingBagRequest";
       	const EVENT_MedicationPrescribe = "api.app.mvz.AppMvzMedicine.MedicationPrescribe";
       	const EVENT_MedicationPlanChanged = "api.app.mvz.AppMvzMedicine.MedicationPlanChanged";
       	const EVENT_RefillMedicine = "api.app.mvz.AppMvzMedicine.RefillMedicine";
       	const EVENT_ViewMedicationForm = "api.app.mvz.AppMvzMedicine.ViewMedicationForm";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenShoppingBagRequest(handler: (data: EventShoppingBagRequest) => void): void {
			const [response, setResponse] = useState<EventShoppingBagRequest>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_ShoppingBagRequest, _listener);
				return () => window.removeEventListener(EVENT_ShoppingBagRequest, _listener);
			}, []);
        }
        export function useListenMedicationPrescribe(handler: (data: EventMedicationPrescribe) => void): void {
			const [response, setResponse] = useState<EventMedicationPrescribe>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_MedicationPrescribe, _listener);
				return () => window.removeEventListener(EVENT_MedicationPrescribe, _listener);
			}, []);
        }
        export function useListenMedicationPlanChanged(handler: (data: EventMedicationPlanChanged) => void): void {
			const [response, setResponse] = useState<EventMedicationPlanChanged>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_MedicationPlanChanged, _listener);
				return () => window.removeEventListener(EVENT_MedicationPlanChanged, _listener);
			}, []);
        }
        export function useListenRefillMedicine(handler: (data: EventRefillMedicine) => void): void {
			const [response, setResponse] = useState<EventRefillMedicine>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_RefillMedicine, _listener);
				return () => window.removeEventListener(EVENT_RefillMedicine, _listener);
			}, []);
        }
        export function useListenViewMedicationForm(handler: (data: EventViewMedicationForm) => void): void {
			const [response, setResponse] = useState<EventViewMedicationForm>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_ViewMedicationForm, _listener);
				return () => window.removeEventListener(EVENT_ViewMedicationForm, _listener);
			}, []);
        }

