/* eslint-disable */
// This code was autogenerated from service/domains/edmp_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as sdda_common from "./edmp_sdda_common"
import * as patient_profile_common from "./patient_profile_common"
import * as schein_common from "./schein_common"


// Type definitions
		export interface DMPLabeling {
				name: string
				germanName: string
				participationLetter: string
				value: string
				iCDCode: string
		}
	

		export interface ParticipationForm {
				dMPLabelingValue: string
				participationFormsStatus: ParticipationFormsStatus
				treatmentDoctorId: string
				enrollStatus?: EnrollStatus
				formSetting?: string
		}
	

		export interface XPMErrorContent {
				errorNo: string
				message: string
				lineNumberError: number
				headerName: string
				fieldName: string
				errorType: ErrorType
				billingFileName: string
		}
	

		export interface XPMErrorResponse {
				dPMCaseNumber: string
				xPMErrorContents: Array<XPMErrorContent>
				xPMFileType: XPMFileType
		}
	

		export interface XPMResult {
				status: string
				statisticFile: DMPBillingFile
				protocolFile: DMPBillingFile
				xPMErrorResponse: Array<XPMErrorResponse>
		}
	

		export interface PHQ9 {
				fileName: string
				scores: Array<number>
				totals: Array<number>
				previousScore?: number
		}
	

		export interface DocumentationOverview {
				enrollmentId: string
				documentType: DocumentType
				scheinId: string
				dMPLabelingValue: string
				treatmentDoctorId: string
				doctorId: string
				documentStatus?: DocumentStatus
				enrollStatus?: EnrollStatus
				patientId?: string
				fields?: Array<Field>
				doctorRelationType: DoctorRelationType
				documentationOverviewId?: string
				documentDate: number
				dMPBillingFile: DMPBillingFile
				pHQ9?: PHQ9
				dMPCaseNumber: string
				bsnrCode?: string
				bsnrId?: string
				additionalContracts?: AdditionalContractsEnum
		}
	

		export interface PatientFrequency {
				isEveryQuarter: boolean
				isEveryTwoQuarter: boolean
		}
	

		export interface InsuranceProgrammes {
				smokingCessation: boolean
				nutritionalCounseling: boolean
				physicalTraining: boolean
		}
	

		export interface PatientMedicalData {
				height: number
				weight: number
				bloodPressureDiastolisch: string
				bloodPressureSystolisch: string
				smoker: boolean
		}
	

		export interface ActivatedTime {
				startDate?: number
				endDate?: number
		}
	

		export interface EnrollmentInfo {
				doctorId: string
				patientId: string
				dMPCaseNumber: string
				participationForm: ParticipationForm
				patientFrequency?: PatientFrequency
				insuranceProgrammes?: InsuranceProgrammes
				patientMedicalData: PatientMedicalData
				treatmentDoctorId: string
				activatedTime?: ActivatedTime
				insuranceInfo?: patient_profile_common.InsuranceInfo
		}
	

		export interface EnrollmentInfoRequest {
				doctorId: string
				patientId: string
				dMPCaseNumber: string
				participationForms: Array<ParticipationForm>
				patientFrequency?: PatientFrequency
				insuranceProgrammes?: InsuranceProgrammes
				patientMedicalData: PatientMedicalData
				treatmentDoctorId: string
		}
	

		export interface EnrollmentWithDocumentModel {
				enrollmentInfoModel: EnrollmentInfoModel
				enrollmentDocumentInfoModel: Array<EnrollmentDocumentInfoModel>
		}
	

		export interface EnrollmentInfoModel {
				id: string
				enrollmentInfo: EnrollmentInfo
		}
	

		export interface EnrollmentDocumentInfoModel {
				id: string
				documentationOverview: DocumentationOverview
				patient: Patient
				doctor: Doctor
				schein: schein_common.Schein
				createdAt?: number
		}
	

		export interface Patient {
				patientId: string
				firstName: string
				lastName: string
				dateOfBirth: patient_profile_common.DateOfBirth
				patientNumber: number
				fullName: string
				activeInsurance?: patient_profile_common.InsuranceInfo
		}
	

		export interface Doctor {
				doctorId: string
				firstName: string
				lastName: string
				initial: string
				title: string
				fullName: string
		}
	

		export interface DmpProgram {
				dMPLabelingValue: string
				enrollStatus: EnrollStatus
				startDate?: number
				doctor?: Doctor
				totalED: Total
				totalFD: Total
				totalPED?: Total
				totalPHQ9_ED?: Total
				totalPHQ9_FD?: Total
		}
	

		export interface Total {
				complete: number
				inComplete: number
				submitted: number
		}
	

		export interface DmpPatientInfo {
				patient: Patient
				insuranceNumber?: string
				dmpProgram: Array<DmpProgram>
		}
	

		export interface EdokuPatientOverview {
				id: string
				patient: Patient
				doctor: Doctor
				document: DocumentationOverview
				createdAt?: number
		}
	

		export interface Option {
				name: string
				label: string
				placeholder: string
				fieldType?: FieldType
				unit?: string
				isFloat?: boolean
				decimalDigits?: number
				minLength?: number
				maxLength?: number
				hiddenFieldNames: Array<string>
				hiddenHeaderNames: Array<string>
		}
	

		export interface FieldValue {
				value: string
				fieldType: FieldType
				valueUnit?: string
				name: string
		}
	

		export interface Position {
				p: number
				e: number
		}
	

		export interface Field {
				name: string
				label: string
				isRequire: boolean
				values?: Array<FieldValue>
				fieldType: FieldType
				options: Array<Option>
				isVertical: boolean
				documentType: DocumentType
				fields: Array<Field>
				positionNoNumber: number
				positionValue: string
				header: string
				placeholder: string
				unit?: string
				headerStatus?: HeaderStatus
				nestedFieldNames?: Array<string>
				position: Position
				minLength?: number
				maxLength?: number
				isFloat?: boolean
				decimalDigits?: number
				readOnly?: boolean
				emptyDateFormat?: boolean
				displayHeader?: string
				liveCheck?: boolean
		}
	

		export interface DMP {
				dMPLabelingValue: string
				fields: Array<Field>
				headerStatus?: HeaderStatus
				headerName: string
				position: Position
				displayHeader?: string
		}
	

		export interface Header {
				name: string
				dMPs: Array<DMP>
		}
	

		export interface DMPDocument {
				headersED: Array<Header>
				headersFD: Array<Header>
				headersPD: Array<Header>
				dMPValue: DMPValueEnum
		}
	

		export interface EDOKUDocument {
				headers: Array<Header>
				dMPValue: DMPValueEnum
		}
	

		export interface QuarterToDateRange {
				quarter: number
				year: number
		}
	

		export interface DMPBillingFile {
				fileName: string
				filePath: string
				displayName?: string
				fileType: DMPBillingHistoryFileType
				sddaIkNumber?: string
		}
	

		export interface DmpBillingError {
				code: string
				message: string
		}
	

		export interface BillingData {
				enrollmentId: string
				dataCenter: sdda_common.DataCenter
				billingFiles: Array<DMPBillingFile>
				messageId?: string
				dmpBillingStatus?: DmpBillingStatus
		}
	

		export interface DMPBillingHistoryInfo {
				quarter: number
				year: number
				doctorId: string
				submittedBy: string
				submittedTime?: number
				typeOfBilling: TypeOfBilling
				markAsCompletedBilling: boolean
				billingsData: Array<BillingData>
				dmpBillingStatus: DmpBillingStatus
				dmpBillingError?: DmpBillingError
				kvConnectId: string
				bsnrCode: string
				transferLetters: Array<DMPBillingFile>
		}
	

		export interface DMPDocumentationType {
				documentationIds: Array<string>
				documentationStatus: DMPDocumentationStatus
				total: number
				documentType: DocumentType
		}
	

		export interface EnrollmentWithDataCenter {
				enrollmentId: string
				dataCenter: sdda_common.DataCenter
		}
	

		export interface Script {
				version: string
				name: string
				content: string
		}
	

		export interface FieldValidationResult {
				fieldName: string
				errorCode: string
				errorMessage: string
				headerName?: string
				fieldValidationResultType: FieldValidationResultType
				script?: Script
				errorType: ErrorType
				billingFileName: string
		}
	

		export interface DMPBillingFieldsValidationResult {
				documentId: string
				fieldValidationResults: Array<FieldValidationResult>
				isPlausibility: boolean
				xPMResultFile: DMPBillingFile
		}
	


// enum definitions
    export enum ParticipationFormsStatus {
        ParticipationFormsStatus_Print = "ParticipationFormsStatus_Print",
        ParticipationFormsStatus_Save = "ParticipationFormsStatus_Save",
        ParticipationFormsStatus_Created = "ParticipationFormsStatus_Created",
	}

    export enum DocumentType {
        DocumentType_ED = "DocumentType_ED",
        DocumentType_FD = "DocumentType_FD",
        DocumentType_PD = "DocumentType_PD",
        DocumentType_PHQ9_ED = "DocumentType_PHQ9_ED",
        DocumentType_PHQ9_FD = "DocumentType_PHQ9_FD",
        DocumentType_EHKS_D = "DocumentType_EHKS_D",
        DocumentType_EHKS_ND = "DocumentType_EHKS_ND",
        DocumentType_EHKS_D_EV = "DocumentType_EHKS_D_EV",
        DocumentType_EHKS_ND_EV = "DocumentType_EHKS_ND_EV",
	}

    export enum DocumentStatus {
        DocumentStatus_Saved = "DocumentStatus_Saved",
        DocumentStatus_Finished = "DocumentStatus_Finished",
        DocumentStatus_Billed = "DocumentStatus_Billed",
        DocumentStatus_Printed = "DocumentStatus_Printed",
	}

    export enum EnrollStatus {
        StatusActivated = "Activated",
        StatusPotential = "Potential",
        StatusTerminated = "Terminated",
	}

    export enum DoctorRelationType {
        DoctorRelationType_Treatment = "DoctorRelationType_Treatment",
        DoctorRelationType_Deputy = "DoctorRelationType_Deputy",
        DoctorRelationType_DoctorCharge = "DoctorRelationType_DoctorCharge",
	}

    export enum XPMFileType {
        XPMFileType_Protocol = "XPMFileType_Protocol",
        XPMFileType_Statistic = "XPMFileType_Statistic",
	}

    export enum AdditionalContractsEnum {
        AdditionalContractsEnum_Yes = "yes",
        AdditionalContractsEnum_No = "no",
	}

    export enum EventEnrollType {
        EventEnrollType_Print_Save_Form = "EventEnrollType_Print_Save_Form",
        EventEnrollType_Terminated = "EventEnrollType_Terminated",
        EventEnrollType_CreateDocument = "EventEnrollType_CreateDocument",
        EventEnrollType_Enroll = "EventEnrollType_Enroll",
        EventEnrollType_SaveDocument = "EventEnrollType_SaveDocument",
        EventEnrollType_FinishDocument = "EventEnrollType_FinishDocument",
        EventEnrollType_PrintDocument = "EventEnrollType_PrintDocument",
        EventEnrollType_BilledDocument = "EventEnrollType_BilledDocument",
        EventEnrollType_FetchEnroll = "EventEnrollType_FetchEnroll",
	}

    export enum FieldType {
        FieldType_Text = "FieldType_Text",
        FieldType_Number = "FieldType_Number",
        FieldType_Date = "FieldType_Date",
        FieldType_Checkbox = "FieldType_Checkbox",
        FieldType_Radio = "FieldType_Radio",
        FieldType_Nested = "FieldType_Nested",
        FieldType_Unknown = "FieldType_Unknown",
	}

    export enum HeaderStatus {
        HeaderStatus_NotFilled = "HeaderStatus_NotFilled",
        HeaderStatus_Completed = "HeaderStatus_Completed",
        HeaderStatus_Incomplete = "HeaderStatus_Incomplete",
	}

    export enum DMPValueEnum {
        DMPValueEnum_MellitusType1 = "04",
        DMPValueEnum_MellitusType2 = "01",
        DMPValueEnum_Brustkrebs = "02",
        DMPValueEnum_CoronaryArteryDisease = "03",
        DMPValueEnum_AsthmaBronchiale = "05",
        DMPValueEnum_COPD = "06",
        DMPValueEnum_ChronicHeartFailure = "07",
        DMPValueEnum_Depression = "08",
        DMPValueEnum_BackPain = "09",
        DMPValueEnum_EDO_SkinCancer = "10",
	}

    export enum FileTypeEnum {
        FileTypeEnum_XML = "FileTypeEnum_XML",
        FileTypeEnum_PDF = "FileTypeEnum_PDF",
	}

    export enum EdmpTestModuleResultEnum {
        EdmpTestModuleResultEnum_Correct = "EdmpTestModuleResultEnum_Correct",
        EdmpTestModuleResultEnum_Incorrect = "EdmpTestModuleResultEnum_Incorrect",
	}

    export enum DMPBillingHistoryFileType {
        DMPBillingHistoryFileType_Protocol = "DMPBillingHistoryFileType_Protocol",
        DMPBillingHistoryFileType_Statistic = "DMPBillingHistoryFileType_Statistic",
        DMPBillingHistoryFileType_Companion = "DMPBillingHistoryFileType_Companion",
        DMPBillingHistoryFileType_Billing = "DMPBillingHistoryFileType_Billing",
        DMPBillingHistoryFileType_TransferLetter = "DMPBillingHistoryFileType_TransferLetter",
	}

    export enum DmpBillingStatus {
        DmpBillingStatus_Sent = "Sent",
        DmpBillingStatus_Confirmed = "Confirmed",
        DmpBillingStatus_Feedback_Available = "Feedback Available",
        DmpBillingStatus_Empty = "Empty",
        DmpBillingStatus_Sending = "Sending",
        DmpBillingStatus_Sending_Failed = "Sending Failed",
        DmpBillingStatus_Timeout_ReceiveEmail = "Timeout Receive Email",
        DmpBillingStatus_ReceiveEmail_Failed = "Timeout Receive Email",
	}

    export enum TypeOfBilling {
        TypeOfBilling_Send_As_Real_Billing = "TypeOfBilling_Send_As_Real_Billing",
        TypeOfBilling_Send_As_Real_Test_Billing = "TypeOfBilling_Send_As_Real_Test_Billing",
	}

    export enum DMPDocumentationStatus {
        DMPDocumentationStatus_Complete = "DMPDocumentationStatus_Complete",
        DMPDocumentationStatus_Incomplete = "DMPDocumentationStatus_Incomplete",
	}

    export enum FieldValidationResultType {
        FieldValidationResultType_XPMCheck = "FieldValidationResultType_XPMCheck",
        FieldValidationResultType_SelfCheck = "FieldValidationResultType_SelfCheck",
	}

    export enum ErrorType {
        ErrorType_Error = "ErrorType_Error",
        ErrorType_Warning = "ErrorType_Warning",
        ErrorType_Schema = "ErrorType_Schema",
	}

    export enum SequenceType {
        SequenceType_Billing = "SequenceType_Billing",
        SequenceType_Conpanion = "SequenceType_Conpanion",
        SequenceType_XKM = "SequenceType_XKM",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

