/* eslint-disable */
// This code was autogenerated from app/admin/card_operation.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as card_common from "./card_common"


// Type definitions
		export interface Terminal {
				mandant: card_common.Mandant
				terminalId: string
				terminalName: string
		}
	

		export interface GetTerminalsResponse {
				terminals: Array<Terminal>
		}
	

		export interface GetTerminalsAndCardsResponse {
				terminals: Array<card_common.TerminalWithCards>
		}
	

		export interface GetCardsRequest {
				connectorId: string
		}
	

		export interface GetCardsResponse {
				cards: Array<card_common.Card>
		}
	

		export interface VerifyPinRequest {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface ChangePinRequest {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface UnblockPinRequest {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				setNewPin: boolean
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface PinResponse {
				cardHandle: string
				pinResult: PinResult
				leftTries?: number
		}
	

		export interface AssignBSNRRequest {
				isscn: string
				bSNRCode: string
		}
	

		export interface UnassignRequest {
				isscn: string
		}
	

		export interface AssignDoctorRequest {
				isscn: string
				doctorId: string
		}
	

		export interface AssignedCard {
				iccsn: string
				bsnrCode?: string
				doctorId?: string
		}
	

		export interface GetCardsWithAssignedDoctorOrPracticeResponse {
				assignedCards: Array<AssignedCard>
		}
	

		export interface GetPinStatus {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface GetPinStatusRequest {
				getPinStatus: Array<GetPinStatus>
		}
	

		export interface GetPinStatusResponse {
				cardPins: Array<card_common.CardPin>
		}
	


// enum definitions
    export enum PinResult {
        PinResultEnumERROR = "ERROR",
        PinResultEnumOK = "OK",
        PinResultEnumREJECTED = "REJECTED",
        PinResultEnumWASBLOCKED = "WASBLOCKED",
        PinResultEnumNOWBLOCKED = "NOWBLOCKED",
        PinResultEnumTRANSPORT_PIN = "TRANSPORT_PIN",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

