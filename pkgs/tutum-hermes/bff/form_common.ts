/* eslint-disable */
// This code was autogenerated from service/domains/form_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"
import * as common1 from "./eau_common"
import * as common2 from "./qes_common"


// Type definitions
		export interface Form {
				id: string
				formTab: boolean
				size: FormSize
				orientation: FormOrientation
				additionalDocs?: Array<string>
				actions: Array<FormAction>
				formType?: FormType
				title: string
				fileNameWithVersion: string
				isDuplex: boolean
				printTypes: Array<PrintType>
				hasRefill: boolean
				isHzvFav: boolean
		}
	

		export interface Prescribe {
				id?: string
				doctorId: string
				treatmentDoctorId: string
				patientId: string
				createdDate: number
				updatedDate?: number
				printedDate?: number
				payload: string
				formName: FormName
				encounterCase: string
				contractType: common.ContractType
				contractId?: string
				scheinId?: string
				prescribeDate: number
				updatedBy?: string
				pdfUrl?: Array<string>
				eAUStatus?: common2.DocumentStatus
				eAUSetting?: common1.EAUSetting
				formTitle?: string
				eAUBundleUrl?: string
				isImported?: boolean
				isTerminated?: boolean
				eAUCancellationBundleUrl?: string
				assignedToBsnrId?: string
		}
	

		export interface PrintOption {
				dateOfPrint: number
				pdfWithBackground: boolean
				formAction: FormAction
				preventAddToTimeline?: boolean
		}
	

		export interface PrintResult {
				formName: FormName
				formUrl: string
		}
	


// enum definitions
    export enum FormCategory {
        FormCategory_A4_portrait = "A4_portrait",
        FormCategory_A4_landscape = "A4_landscape",
        FormCategory_A5_portrait = "A5_portrait",
        FormCategory_A5_landscape = "A5_landscape",
        FormCategory_A6_portrait = "A6_portrait",
        FormCategory_A6_landscape = "A6_landscape",
	}

    export enum FormType {
        FormType_medication = "medication",
        FormType_heimi = "heimi",
        FormType_himi = "himi",
        FormType_lab = "lab",
        FormType_generic_form = "generic_form",
        FormType_public_document = "public_document",
        FormType_public_contract_text = "public_contract_text",
        FormType_contract_hint = "contract_hint",
        FormType_diga = "diga",
	}

    export enum FormSize {
        FormSize_A4 = "A4",
        FormSize_A5 = "A5",
        FormSize_A6 = "A6",
	}

    export enum FormOrientation {
        PrintOrientation_portrait = "portrait",
        PrintOrientation_landscape = "landscape",
	}

    export enum PrintType {
        PrintType_formPrint = "formPrint",
        PrintType_fullPrint = "fullPrint",
	}

    export enum FormAction {
        FormAction_PrintFull = "FormAction_PrintFull",
        FormAction_PrintHeader = "FormAction_PrintHeader",
        FormAction_PrintWithoutContent = "FormAction_PrintWithoutContent",
        FormAction_OpenNewTab = "FormAction_OpenNewTab",
        FormAction_PrintOnly = "FormAction_PrintOnly",
        FormAction_PrintWithBSNRAndLANR = "FormAction_PrintWithBSNRAndLANR",
	}

    export enum EAUFormType {
        FormType_V = "v",
        FormType_KK = "kk",
        FormType_AG = "ag",
	}

    export enum FormName {
        Muster_8 = "Muster_8",
        Muster_8A = "Muster_8A",
        Muster_16 = "Muster_16",
        Muster_15 = "Muster_15",
        Muster_1 = "Muster_1",
        Muster_6 = "Muster_6",
        Muster_10 = "Muster_10",
        Muster_10A = "Muster_10A",
        Muster_13 = "Muster_13",
        Muster_39A = "Muster_39A",
        Muster_2B = "Muster_2B",
        Muster_4 = "Muster_4",
        Muster_2A = "Muster_2A",
        KREZ = "Muster_16",
        GREZ = "Gruenes_Rezept",
        BTM = "Btm_Rezept_Print",
        TPrescription = "T-Rezept-Muster",
        Private = "Blaues_Rezept",
        Muster_6_cover_letter = "Muster_6_cover_letter",
        Muster_2C = "Muster_2C",
        Muster_3A = "Muster_3A",
        Muster_3B = "Muster_3B",
        Muster_5 = "Muster_5",
        Muster_9 = "Muster_9",
        Muster_21 = "Muster_21",
        Muster_19A = "Muster_19A",
        Muster_36_E_2017_07 = "Muster_36_E_2017_07",
        Muster_12A = "Muster_12A",
        Muster_28A = "Muster_28A",
        Muster_28B = "Muster_28B",
        Muster_28C = "Muster_28C",
        Muster_52_0_V2 = "Muster_52_0_V2",
        Muster_12B = "Muster_12B",
        Muster_12C = "Muster_12C",
        Muster_55 = "Muster_55",
        Muster_10C = "Muster_10C",
        Muster_20A = "Muster_20A",
        Muster_20B = "Muster_20B",
        Muster_20C = "Muster_20C",
        Muster_20D = "Muster_20D",
        Muster_65A = "Muster_65A",
        Muster_65B = "Muster_65B",
        Muster_70 = "Muster_70",
        Muster_70_B = "Muster_70_B",
        Muster_70A = "Muster_70A",
        Muster_70A_B = "Muster_70A_B",
        Muster_N63A = "Muster_N63A",
        Muster_N63B = "Muster_N63B",
        Muster_N63C = "Muster_N63C",
        Muster_N63D = "Muster_N63D",
        Muster_61A = "Muster_61A",
        Muster_61B = "Muster_61B",
        Muster_64 = "Muster_64",
        Muster_64B = "Muster_64B",
        Muster_19B = "Muster_19B",
        Muster_19C = "Muster_19C",
        Muster_56 = "Muster_56",
        Muster_61 = "Muster_61",
        Muster_PTV_11A = "Muster_PTV_11A",
        Muster_PTV_11B = "Muster_PTV_11B",
        Muster_PTV_3 = "Muster_PTV_3",
        Muster_PTV_10 = "Muster_PTV_10",
        G81_EHIC_Bulgarisch = "G81_EHIC_Bulgarisch",
        G81_EHIC_Danisch = "G81_EHIC_Danisch",
        G81_EHIC_Englisch = "G81_EHIC_Englisch",
        G81_EHIC_Franzosisch = "G81_EHIC_Franzosisch",
        G81_EHIC_Griechisch = "G81_EHIC_Griechisch",
        G81_EHIC_Italienisch = "G81_EHIC_Italienisch",
        G81_EHIC_Kroatisch = "G81_EHIC_Kroatisch",
        G81_EHIC_Niederlandisch = "G81_EHIC_Niederlandisch",
        G81_EHIC_Polnisch = "G81_EHIC_Polnisch",
        G81_EHIC_Rumanisch = "G81_EHIC_Rumanisch",
        G81_EHIC_Spanisch = "G81_EHIC_Spanisch",
        G81_EHIC_Tschechisch = "G81_EHIC_Tschechisch",
        G81_EHIC_Ungarisch = "G81_EHIC_Ungarisch",
        Muster_16A = "Muster_16A",
        Muster_16A_Bay = "Muster_16a_bay",
        G81_EHIC_All = "G81_EHIC_All",
        DMP_Enrollment_Form = "DMP_Enrollment_Form",
        PHQ_9_Q3_2023 = "PHQ_9_Q3_2023",
        Muster_7 = "Muster_7",
        Muster_11 = "Muster_11",
        Muster_50 = "Muster_50",
        Muster_51 = "Muster_51",
        G81_EHIC_Finnisch = "G81_EHIC_Finnisch",
        G81_EHIC_Estnisch = "G81_EHIC_Estnisch",
        G81_EHIC_Slowenisch = "G81_EHIC_Slowenisch",
        G81_EHIC_Slowakisch = "G81_EHIC_Slowakisch",
        G81_EHIC_Schwedisch = "G81_EHIC_Schwedisch",
        G81_EHIC_Portugiesisch = "G81_EHIC_Portugiesisch",
        G81_EHIC_Litauisch = "G81_EHIC_Litauisch",
        G81_EHIC_Lettisch = "G81_EHIC_Lettisch",
        Muster_22A = "Muster_22A",
        Muster_22B = "Muster_22B",
        Muster_22C = "Muster_22C",
        Muster_22D = "Muster_22D",
        Muster_26A = "Muster_26A",
        Muster_26B = "Muster_26B",
        Muster_26C = "Muster_26C",
        Muster_27A = "Muster_27A",
        Muster_27B = "Muster_27B",
        Muster_27C = "Muster_27C",
        Muster_PTV_1A = "Muster_PTV_1A",
        Muster_PTV_1B = "Muster_PTV_1B",
        Muster_PTV_1C = "Muster_PTV_1C",
        Muster_PTV_12A = "Muster_PTV_12A",
        Muster_PTV_12B = "Muster_PTV_12B",
        Muster_PTV_2A = "Muster_PTV_2A",
        Muster_PTV_2B = "Muster_PTV_2B",
        Muster_PTV_2C = "Muster_PTV_2C",
        Muster_39B = "Muster_39B",
        Muster_61C = "Muster_61C",
        Muster_61D = "Muster_61D",
        AOK_FA_NPPP_BW = "AOK_FA_NPPP_BW",
        BKK_BOSCH_FA_BW = "BKK_BOSCH_FA_BW",
        MEDI_FA_PT_BW = "MEDI_FA_PT_BW",
        HIMI_QUESTION_NAME = "HIMI_QUESTION_NAME",
        BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4 = "BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4",
        BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5 = "BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5",
        Muster_52_2_V3 = "Muster_52_2_V3",
        HIMIFB0399051_V3 = "HIMIFB0399051_V3",
        HIMIFB0399054_V3 = "HIMIFB0399054_V3",
        HIMIFB039906_V3 = "HIMIFB039906_V3",
        HIMIFB0440_V3 = "HIMIFB0440_V3",
        HIMIFB1129_V3 = "HIMIFB1129_V3",
        HIMIFB1424_V3 = "HIMIFB1424_V3",
        HIMIFB1525192_V3 = "HIMIFB1525192_V3",
        HIMIFB1846ER_V3 = "HIMIFB1846ER_V3",
        HIMIFB1865_V3 = "HIMIFB1865_V3",
        HIMIFB1940_V3 = "HIMIFB1940_V3",
        HIMIFB213401_V4 = "HIMIFB213401_V4",
        HIMIFB31_V3 = "HIMIFB31_V3",
        BKK_VAG_FA_BW_TE_HepCModul_V2 = "BKK_VAG_FA_BW_TE_HepCModul_V2",
        TK_HZV_Versichertenteilnahmeerklaerung_V9 = "TK_HZV_Versichertenteilnahmeerklaerung_V9",
        HZV_Beleg_Muster_V3 = "HZV_Beleg_Muster_V3",
        SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7 = "SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7",
        RV_KBS_HZV_Versichertenteilnahmeerklaerung_V4 = "RV_KBS_HZV_Versichertenteilnahmeerklaerung_V4",
        Ueberleitungsbogen_AOK_KBS_NO_WL_V2 = "Ueberleitungsbogen_AOK_KBS_NO_WL_V2",
        RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3 = "RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3",
        RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5 = "RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5",
        BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17 = "BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17",
        BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3 = "BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3",
        BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2 = "BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2",
        BKK_VAG_FA_PT_BW_Ausschreibeformular_V5 = "BKK_VAG_FA_PT_BW_Ausschreibeformular_V5",
        BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2 = "BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2",
        BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2 = "BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2",
        BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2 = "BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2",
        BKK_GWQ_FA_PT_BW_Versichertenteilnahmeerklaerung_V2 = "BKK_GWQ_FA_PT_BW_Versichertenteilnahmeerklaerung_V2",
        LKK_Teilnahme_und_Einwilligungserklaerung_V7 = "LKK_Teilnahme-und Einwilligungserklaerung_V7",
        LKK_BY_HZV_Muster_Versicherteneinschreibebeleg_V3 = "LKK_BY_HZV_Muster_Versicherteneinschreibebeleg_V3",
        Praxisuebergabe_V1 = "Praxisuebergabe_V1",
        LKK_BY_Teilnahme_und_Einwilligungserklaerung_V4 = "LKK_BY_Teilnahme-und Einwilligungserklaerung_V4",
        LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15 = "LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15",
        IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12 = "IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12",
        HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4 = "HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4",
        EK_WL_HZV_Versichertenteilnahmeerklaerung_V2 = "EK_WL_HZV_Versichertenteilnahmeerklaerung_V2",
        Ueberleitungsbogen_EK_BKK_NO_WL_V1 = "Ueberleitungsbogen_EK_BKK_NO_WL_V1",
        EK_SN_HZV_Versichertenteilnahmeerklaerung_V3 = "EK_SN_HZV_Versichertenteilnahmeerklaerung_V3",
        EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3 = "EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3",
        EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5 = "EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5",
        EK_NO_HZV_Versichertenteilnahmeerklaerung_V2 = "EK_NO_HZV_Versichertenteilnahmeerklaerung_V2",
        EK_FA_DIA_BW_Versichertenteilnahmeerklaerung_V1 = "EK_FA_DIA_BW_Versichertenteilnahmeerklaerung_V1",
        EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7 = "EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7",
        Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5 = "Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5",
        EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4 = "EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4",
        DAK_HZV_VersichertenTeilnahmeerklaerung_V4 = "DAK_HZV_VersichertenTeilnahmeerklaerung_V4",
        Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8 = "Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8",
        BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4 = "BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4",
        BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1 = "BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1",
        BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1 = "BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1",
        Begleitschreiben_FaV_V4 = "Begleitschreiben_FaV_V4",
        Versichertenteilnahmeerklaerung_Online_Variante_A_V11 = "Versichertenteilnahmeerklaerung_Online_Variante_A_V11",
        Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5 = "Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5",
        Ambulantes_Operieren_V1 = "Ambulantes_Operieren_V1",
        AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4 = "AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4",
        BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11 = "BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11",
        BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6 = "BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6",
        AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3 = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3",
        AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3 = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3",
        AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3 = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3",
        AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3 = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3",
        AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3 = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3",
        AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5 = "AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5",
        BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10 = "BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10",
        BKK_BOSCH_FA_TE_HepCModul_V4 = "BKK_BOSCH_FA_TE_HepCModul_V4",
        BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3 = "BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3",
        BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2 = "BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2",
        Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6 = "Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6",
        BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1 = "BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1",
        BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6 = "BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6",
        BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9 = "BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9",
        BKK_BOSCH_FA_BW_GDK_Antragsformular_V4 = "BKK_BOSCH_FA_BW_GDK_Antragsformular_V4",
        AWH_01_Checkliste_Somatik_V1 = "AWH_01_Checkliste_Somatik_V1",
        AWH_01_Checkliste_Psychosomatik_V1 = "AWH_01_Checkliste_Psychosomatik_V1",
        AWH_01_Kurzantrag_HZV_KinderReha_V1 = "AWH_01_Kurzantrag_HZV-KinderReha_V1",
        AWH_01_BVKJ_Anlage_7b_Osteopathie_V2 = "AWH_01_BVKJ_Anlage_7b_Osteopathie_V2",
        AWH_01_Patientenfragebogen_AOK_Check_18_V2 = "AWH_01_Patientenfragebogen_AOK-Check 18+_V2",
        AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12 = "AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12",
        AOK_BW_Beratungsbogen_Einbindung_SD_V7 = "AOK_BW_Beratungsbogen_Einbindung_SD_V7",
        AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9 = "AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9",
        AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3 = "AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3",
        AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8 = "AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8",
        AOK_SH_HZV_Ueberleitungsmanagement_V3 = "AOK_SH_HZV_Ueberleitungsmanagement_V3",
        AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7 = "AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7",
        AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4 = "AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4",
        AOK_PLUS_Versichertenteilnahmeerklaerung_V6 = "AOK_PLUS_Versichertenteilnahmeerklaerung_V6",
        AOK_NO_HH_Versichertenteilnahmeerklaerung_V5 = "AOK_NO_HH_Versichertenteilnahmeerklaerung_V5",
        AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11 = "AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11",
        AOK_IKK_BLN_HZV_Muster_Versicherteneinschreibebeleg_V2 = "AOK_IKK_BLN_HZV_Muster_Versicherteneinschreibebeleg_V2",
        AOK_HH_HZV_Ueberleitungsbogen_V2 = "AOK_HH_HZV_Ueberleitungsbogen_V2",
        AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12 = "AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12",
        AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3 = "AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3",
        AOK_FA_NPPP_BW_GDK_Antragsformular_V6 = "AOK_FA_NPPP_BW_GDK_Antragsformular_V6",
        AOK_FA_BW_GDK_Antragsformular_DF_V4 = "AOK_FA_BW_GDK_Antragsformular_DF_V4",
        AOK_FA_BW_TE_HepCModul_V3 = "AOK_FA_BW_TE_HepCModul_V3",
        AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2 = "AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2",
        AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2 = "AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2",
        AOK_SL_HZV_Versichertenteilnahmeerklaerung_V2 = "AOK_SL_HZV_Versichertenteilnahmeerklaerung_V2",
        BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4 = "BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4",
        AOKNordwet = "AOK_Nordwet",
        AOKBremen = "AOK_Bremen_impfstoff",
        Muster16aBay = "Muster_16a_bay",
        Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V8 = "Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V8",
        F1050 = "F1050",
        F9990 = "F9990",
        F2100 = "F2100",
        F1000 = "F1000",
        BKK_BY_HZV_Einwilligungserklaerung_Telemedizinisches_Facharztkonsil = "BKK_BY_HZV_Einwilligungserklaerung_Telemedizinisches_Facharztkonsil",
        Muster_eRezept = "Muster_eRezept",
        AOK_FA_OC_BW_Antrag_AOK_Sports_V3 = "AOK_FA_OC_BW_Antrag_AOK_Sports_V3",
        AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2 = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2",
        Muster_4_A5 = "Muster_4_print_preview_n",
        EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3 = "EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3",
        AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3 = "AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3",
        LabResults_Landscape = "LabResults_Landscape",
        LabResults_Portrait = "LabResults_Portrait",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

