/* eslint-disable */
// This code was autogenerated from service/domains/error_code.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';



// Type definitions
		export interface CommonException {
				status: number
				message: string
				serverError: string
				serverErrorParam: Array<string>
		}
	


// enum definitions
    export enum ErrorCode {
        ErrorCode_ValidationError_NotActive_InsuranceInfo = "ErrorCode_ValidationError_NotActive_InsuranceInfo",
        ErrorCode_CardReader_NotAvailable = "ErrorCode_CardReader_NotAvailable",
        ErrorCode_CardReader_Error = "ErrorCode_CardReader_Error",
        ErrorCode_Setting_TI_Not_Found = "ErrorCode_Setting_TI_Not_Found",
        ErrorCode_Patient_Not_Found = "ErrorCode_Patient_Not_Found",
        ErrorCode_Cost_Unit_Not_Found = "ErrorCode_Cost_Unit_Not_Found",
        ErrorCode_Doctor_Not_Found = "ErrorCode_Doctor_Not_Found",
        ErrorCode_Schein_Not_Found = "ErrorCode_Schein_Not_Found",
        ErrorCode_SDEBM_Rule_Not_Found = "ErrorCode_SDEBM_Rule_Not_Found",
        ErrorCode_Employee_Not_Found = "ErrorCode_Employee_Not_Found",
        ErrorCode_Prescribe_Not_Found = "ErrorCode_Prescribe_Not_Found",
        ErrorCode_Form_Not_Found = "ErrorCode_Form_Not_Found",
        ErrorCode_PrinterProfile_Not_Found = "ErrorCode_PrinterProfile_Not_Found",
        ErrorCode_Login_Invalid_Verify_Password = "ErrorCode_Login_Invalid_Verify_Password",
        ErrorCode_Login_Invalid_New_Password = "ErrorCode_Login_Invalid_New_Password",
        ErrorCode_Login_Invalid_Current_Password = "ErrorCode_Login_Invalid_Current_Password",
        ErrorCode_SDEBM_Service_Code_Existed = "ErrorCode_SDEBM_Service_Code_Existed",
        ErrorCode_SDKT_Duplicated_VKNR = "ErrorCode_SDKT_Duplicated_VKNR",
        ErrorCode_SDKT_Required_CatalogId = "ErrorCode_SDKT_Required_CatalogId",
        ErrorCode_SDKT_Create_Failed = "ErrorCode_SDKT_Create_Failed",
        ErrorCode_SDKT_Not_Found = "ErrorCode_SDKT_Not_Found",
        ErrorCode_SDEBM_Cannot_Deleted = "ErrorCode_SDEBM_Cannot_Deleted",
        ErrorCode_BSNR_NOT_FOUND = "ErrorCode_BSNR_NOT_FOUND",
        ErrorCode_NO_SCHEIN_BILLING_FOUND = "ErrorCode_NO_SCHEIN_BILLING_FOUND",
        ErrorCode_NO_SCHEIN_BILLING_IN_QUARTER_FOUND = "ErrorCode_NO_SCHEIN_BILLING_IN_QUARTER_FOUND",
        ErrorCode_NO_DIAGNOSIS_BILLING_FOUND = "ErrorCode_NO_DIAGNOSIS_BILLING_FOUND",
        ErrorCode_NO_SERVICES_BILLING_FOUND = "ErrorCode_NO_SERVICES_BILLING_FOUND",
        ErrorCode_CAN_NOT_CREATE_ERROR_HISTORY = "ErrorCode_CAN_NOT_CREATE_ERROR_HISTORY",
        ErrorCode_WAITING_ROOM_NOT_FOUND = "ErrorCode_WAITING_ROOM_NOT_FOUND",
        ErrorCode_SERVICE_CODE_USED_IN_TIMELINE = "ErrorCode_SERVICE_CODE_USED_IN_TIMELINE",
        ErrorCode_Value_Is_Required = "ErrorCode_Value_Is_Required",
        ErrorCode_Waiting_Room_Have_Patient = "ErrorCode_Waiting_Room_Have_Patient",
        ErrorCode_Exist_Patient_In_A_Waiting_Room = "ErrorCode_Exist_Patient_In_A_Waiting_Room",
        ErrorCode_Contract_Not_Found = "ErrorCode_Contract_Not_Found",
        ErrorCode_Record_Not_Found = "ErrorCode_Record_Not_Found",
        ErrorCode_Data_Invalid = "ErrorCode_Data_Invalid",
        ErrorCode_Validation_MessageABRD613 = "ErrorCode_Validation_MessageABRD613",
        ErrorCode_Validation_MessageABRD969 = "ErrorCode_Validation_MessageABRD969",
        ErrorCode_Validation_MessageABRD514 = "ErrorCode_Validation_MessageABRD514",
        ErrorCode_Validation_MessageABRD612 = "ErrorCode_Validation_MessageABRD612",
        ErrorCode_Validation_MessageABRD786 = "ErrorCode_Validation_MessageABRD786",
        ErrorCode_Validation_MessageP10470MM = "ErrorCode_Validation_MessageP10470MM",
        ErrorCode_Validation_MessageP10470MF = "ErrorCode_Validation_MessageP10470MF",
        ErrorCode_Validation_MessageP10470KM = "ErrorCode_Validation_MessageP10470KM",
        ErrorCode_Validation_MessageP10470KF = "ErrorCode_Validation_MessageP10470KF",
        ErrorCode_Validation_MessageP10480MM = "ErrorCode_Validation_MessageP10480MM",
        ErrorCode_Validation_MessageP10480MR = "ErrorCode_Validation_MessageP10480MR",
        ErrorCode_Validation_MessageP10480MZ = "ErrorCode_Validation_MessageP10480MZ",
        ErrorCode_Validation_MessageP10480KM = "ErrorCode_Validation_MessageP10480KM",
        ErrorCode_Validation_MessageP10480KR = "ErrorCode_Validation_MessageP10480KR",
        ErrorCode_Validation_MessageABRD456 = "ErrorCode_Validation_MessageABRD456",
        ErrorCode_Validation_MessageABRD887 = "ErrorCode_Validation_MessageABRD887",
        ErrorCode_Validation_MessageABRD970 = "ErrorCode_Validation_MessageABRD970",
        ErrorCode_Validation_MessageFieldIsRequiredFormat = "ErrorCode_Validation_MessageFieldIsRequiredFormat",
        ErrorCode_Validation_MessageMedicineTypeRequireFormat = "ErrorCode_Validation_MessageMedicineTypeRequireFormat",
        ErrorCode_Validation_MessageMappingError = "ErrorCode_Validation_MessageMappingError",
        ErrorCode_Validation_MessageNotBillingAble = "ErrorCode_Validation_MessageNotBillingAble",
        ErrorCode_Validation_MessageNotFillingForBilling = "ErrorCode_Validation_MessageNotFillingForBilling",
        ErrorCode_Validation_MessageRareDiseaseEu = "ErrorCode_Validation_MessageRareDiseaseEu",
        ErrorCode_Validation_MessageIfSG = "ErrorCode_Validation_MessageIfSG",
        ErrorCode_Validation_MessageNotSuitablePermanent = "ErrorCode_Validation_MessageNotSuitablePermanent",
        ErrorCode_Validation_MessageNeedPrimaryCode = "ErrorCode_Validation_MessageNeedPrimaryCode",
        ErrorCode_CardReader_Validity_Card = "ErrorCode_CardReader_Validity_Card",
        ErrorCode_CardReader_UnknownType_Card = "ErrorCode_CardReader_UnknownType_Card",
        ErrorCode_CardReader_Error_VSDService = "ErrorCode_CardReader_Error_VSDService",
        ErrorCode_TIConnector_Error_Version_Not_Support = "ErrorCode_TIConnector_Error_Version_Not_Support",
        ErrorCode_TIConnector_Error_Service_Not_Found = "ErrorCode_TIConnector_Error_Service_Not_Found",
        ErrorCode_CardReader_Confirm_PN_Code_No_Successful = "ErrorCode_CardReader_Confirm_PN_Code_No_Successful",
        ErrorCode_CardReader_Confirm_PN_Code_Successful = "ErrorCode_CardReader_Confirm_PN_Code_Successful",
        ErrorCode_CardReader_Insurance_EndDate_Before_Current_Date = "ErrorCode_CardReader_Insurance_EndDate_Before_Current_Date",
        ErrorCode_CardReader_Insurance_StartDate_After_Current_Date = "ErrorCode_CardReader_Insurance_StartDate_After_Current_Date",
        ErrorCode_CardReader_SDKT_Vknr_Invalid = "ErrorCode_CardReader_SDKT_Vknr_Invalid",
        ErrorCode_CardReader_Proof_Of_Insurance_Invalid = "ErrorCode_CardReader_Proof_Of_Insurance_Invalid",
        Warning_CardReader_PnResultCode_1 = "Warning_CardReader_PnResultCode_1",
        Warning_CardReader_PnResultCode_2 = "Warning_CardReader_PnResultCode_2",
        Warning_CardReader_PnResultCode_3 = "Warning_CardReader_PnResultCode_3",
        Warning_CardReader_PnResultCode_4 = "Warning_CardReader_PnResultCode_4",
        Warning_CardReader_PnResultCode_5 = "Warning_CardReader_PnResultCode_5",
        Warning_CardReader_PnResultCode_6 = "Warning_CardReader_PnResultCode_6",
        Warning_CardReader_PnErrorCode_114 = "Warning_CardReader_PnErrorCode_114",
        Warning_CardReader_PnErrorCode_106 = "Warning_CardReader_PnErrorCode_106",
        Warning_CardReader_PnErrorCode_107 = "Warning_CardReader_PnErrorCode_107",
        Warning_CardReader_PnErrorCode_113 = "Warning_CardReader_PnErrorCode_113",
        Warning_CardReader_PnErrorCode_4192 = "Warning_CardReader_PnErrorCode_4192",
        Warning_CardReader_PnErrorCode_102 = "Warning_CardReader_PnErrorCode_102",
        Warning_CardReader_PnErrorCode_103 = "Warning_CardReader_PnErrorCode_103",
        Warning_CardReader_PnErrorCode_104 = "Warning_CardReader_PnErrorCode_104",
        Warning_CardReader_PnErrorCode_109 = "Warning_CardReader_PnErrorCode_109",
        Warning_CardReader_PnErrorCode_110 = "Warning_CardReader_PnErrorCode_110",
        Warning_CardReader_PnErrorCode_112 = "Warning_CardReader_PnErrorCode_112",
        Warning_CardReader_PnErrorCode_4147 = "Warning_CardReader_PnErrorCode_4147",
        Warning_CardReader_PnErrorCode_12999 = "Warning_CardReader_PnErrorCode_12999",
        Warning_CardReader_PnErrorCode_101 = "Warning_CardReader_PnErrorCode_101",
        Warning_CardReader_PnErrorCode_111 = "Warning_CardReader_PnErrorCode_111",
        Warning_CardReader_PnErrorCode_4093 = "Warning_CardReader_PnErrorCode_4093",
        Warning_CardReader_PnErrorCode_3001 = "Warning_CardReader_PnErrorCode_3001",
        Warning_CardReader_PnErrorCode_12105 = "Warning_CardReader_PnErrorCode_12105",
        ErrorCode_CardReader_ErrorPn35WithErrorCode12103 = "ErrorCode_CardReader_ErrorPn35WithErrorCode12103",
        ErrorCode_CardReader_ErrorPn6 = "ErrorCode_CardReader_ErrorPn6",
        ErrorCode_CardReader_ErrorPnNotEqual4AndListErrorCode = "ErrorCode_CardReader_ErrorPnNotEqual4AndListErrorCode",
        ErrorCode_CardReader_PN4WithErrorCode = "ErrorCode_CardReader_PN4WithErrorCode",
        ErrorCode_CardReader_ErrorCode300112105 = "ErrorCode_CardReader_ErrorCode300112105",
        ErrorCode_CardReader_ErrorCode30403039 = "ErrorCode_CardReader_ErrorCode30403039",
        ErrorCode_Patient_Not_Found_In_Con_File_Result = "ErrorCode_Patient_Not_Found_In_Con_File_Result",
        ErrorCode_CardReader_Confirm_PN_Code_Invalid = "ErrorCode_CardReader_Confirm_PN_Code_Invalid",
        ErrorCode_SDAV_Cannot_Modified = "ErrorCode_SDAV_Cannot_Modified",
        ErrorCode_Template_Is_Exist = "ErrorCode_Template_Is_Exist",
        ErrorCode_Validation_Document_88130 = "ErrorCode_Validation_Document_88130",
        ErrorCode_KV_Connect_Account_Not_Found = "ErrorCode_KV_Connect_Account_Not_Found",
        ErrorCode_Validation_EDMPSuggestion = "EDMPSuggestion",
        ErrorCode_TIConnector_Not_Found = "ErrorCode_TIConnector_Not_Found",
        ErrorCode_TI_Status_Not_Found = "ErrorCode_TI_Status_Not_Found",
        ErrorCode_HeaderFooter_Is_Exist = "ErrorCode_HeaderFooter_Is_Exist",
        ErrorCode_TI_Can_Not_Check_Certificate_Expire = "ErrorCode_TI_Can_Not_Check_Certificate_Expire",
        ErrorCode_TI_Certificate_Expire_Not_Found = "ErrorCode_TI_Certificate_Expire_Not_Found",
        ErrorCode_Erezept_Invalid_Request = "ErrorCode_Erezept_Invalid_Request",
        ErrorCode_Device_Not_Found = "ErrorCode_Device_Not_Found",
        ErrorCode_EDMP_Case_Number_Invalid = "ErrorCode_EDMP_Case_Number_Invalid",
        ErrorCode_EDMP_Enrollment_Not_Found = "ErrorCode_EDMP_Enrollment_Not_Found",
        ErrorCode_EDMP_Not_Allowed = "ErrorCode_EDMP_Not_Allowed",
        ErrorCode_EREZEPT_TRAINING_DOCTOR_AUTHORIZATION = "ErrorCode_EREZEPT_TRAINING_DOCTOR_AUTHORIZATION",
        ErrorCode_Validation_ICD_Code_Not_In_Master_Data = "ErrorCode_Validation_ICD_Code_Not_In_Master_Data",
        ErrorCode_CANNOT_SEARCH_SERVICE_CODE = "ErrorCode_CANNOT_SEARCH_SERVICE_CODE",
        ErrorCode_CERTAINTY_IS_REQUIRED = "ErrorCode_CERTAINTY_IS_REQUIRED",
        ErrorCode_Email_Not_Found = "ErrorCode_Email_Not_Found",
        ErrorCode_EDMP_ED_Exist = "ErrorCode_EDMP_ED_Exist",
        ErrorCode_Validation_Missing_Treatment_Time = "ErrorCode_Validation_Missing_Treatment_Time",
        ErrorCode_Missing_Referral_Doctor = "ErrorCode_Missing_Referral_Doctor",
        ErrorCode_EDMP_ED_Not_Exist = "ErrorCode_EDMP_ED_Not_Exist",
        ErrorCode_Validation_Missing_Pseudo_GNR = "ErrorCode_Validation_Missing_Pseudo_GNR",
        ErrorCode_kvConnect_CertificateOlderThanOnKvServer = "ErrorCode_kvConnect_CertificateOlderThanOnKvServer",
        ErrorCode_empty = "ErrorCode_empty",
        ErrorCode_CardReader_InsHasBeenTerminated = "ErrorCode_CardReader_InsHasBeenTerminated",
        ErrorCode_CardReader_InsHasBeenDeactive = "ErrorCode_CardReader_InsHasBeenDeactive",
        ErrorCode_CardReader_InsHasBeenRestrictArea = "ErrorCode_CardReader_InsHasBeenRestrictArea",
        ErrorCode_CardReader_IK_Invalid_Expired = "ErrorCode_CardReader_IK_Invalid_Expired",
        ErrorCode_Common_DataExisting = "ErrorCode_Common_DataExisting",
        ErrorCode_EDMP_Get_Document_Not_Found = "ErrorCode_EDMP_Get_Document_Not_Found",
        ErrorCode_ValidationError_ScheinHasTimeline = "ErrorCode_ValidationError_ScheinHasTimeline",
        ErrorCode_Real_Billing_Already_Sent = "ErrorCode_Real_Billing_Already_Sent",
        ErrorCode_Cannot_Correct_Billing_Before_Real_Billing = "ErrorCode_Cannot_Correct_Billing_Before_Real_Billing",
        ErrorCode_IK_NotFound = "ErrorCode_IK_NotFound",
        ErrorCode_MissICDCodeToBilling = "ErrorCode_MissICDCodeToBilling",
        ErrorCode_InvalidServiceCode = "ErrorCode_InvalidServiceCode",
        ErrorCode_ServiceValidationError = "ErrorCode_ServiceValidationError",
        ErrorCode_Service_Timeline_Not_Found = "ErrorCode_Service_Timeline_Not_Found",
        ErrorCode_SearchSdkt = "ErrorCode_SearchSdkt",
        ErrorCode_Validation_Missing_ScheinId = "ErrorCode_Validation_Missing_ScheinId",
        ErrorCode_kvconnect_invalidSignature = "ErrorCode_kvconnect_invalidSignature",
        ErrorCode_kvconnect_tss_fail = "ErrorCode_kvconnect_tss_fail",
        ErrorCode_kvconnect_serverCert_invalid = "ErrorCode_kvconnect_serverCert_invalid",
        ErrorCode_kvconnect_login = "ErrorCode_kvconnect_login",
        ErrorCode_kvconnect_SendCSR = "ErrorCode_kvconnect_SendCSR",
        ErrorCode_Validation_Invalid_Pseudo_GNR = "ErrorCode_Validation_Invalid_Pseudo_GNR",
        ErrorCode_Validation_RangeAge = "ErrorCode_Validation_RangeAge",
        ErrorCode_kvconnect_serverCert_expired = "ErrorCode_kvconnect_serverCert_expired",
        ErrorCode_kvconnect_tss_timeout = "ErrorCode_kvconnect_tss_timeout",
        ErrorCode_Empty_InsuranceInfo = "ErrorCode_Empty_InsuranceInfo",
        ErrorCode_Validation_ReplacedWithServiceCodeWhenBilling = "ReplacedWithServiceCodeWhenBilling",
        ErrorCode_Cannot_Delete_Schein = "ErrorCode_Cannot_Delete_Schein",
        ErrorCode_ServerError_KvBilling = "ErrorCode_ServerError_KvBilling",
        ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter = "ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter",
        ErrorCode_ValidationError_ScheinHasActiveDocuments = "ErrorCode_ValidationError_ScheinHasActiveDocuments",
        ErrorCode_ValidationError_OpsMustInList = "ErrorCode_ValidationError_OpsMustInList",
        ErrorCode_ValidationError_GnrMustInList = "ErrorCode_ValidationError_GnrMustInList",
        ErrorCode_Validation_Must_Not_Present_Treatment_Time = "ErrorCode_Validation_Must_Not_Present_Treatment_Time",
        ErrorCode_ValidationError_TSS_Surcharge_Acute = "tss_surcharge_acute_error",
        ErrorCode_ValidationError_TSS_Surcharge_Routine = "tss_surcharge_routine_error",
        ErrorCode_ValidationError_TSS_Surcharge_Common = "tss_surcharge_error_common",
        ErrorCode_ValidationError_TSS_Suggestion = "tss_surcharge_suggestion",
        ErrorCode_ValidationError_VknrIsRequired = "ErrorCode_ValidationError_VknrIsRequired",
        ErrorCode_ValidationError_BSNR_NOT_VALID_IN_QUARTER = "ErrorCode_ValidationError_BSNR_NOT_VALID_IN_QUARTER",
        ErrorCode_ValidationError_MultipleActive_InsuranceInfo = "ErrorCode_ValidationError_MultipleActive_InsuranceInfo",
        ErrorCode_ValidationError_Form_IcdIsRequired = "ErrorCode_ValidationError_Form_IcdIsRequired",
        ErrorCode_CardReader_Not_Found_Patient_Card = "ErrorCode_CardReader_Not_Found_Patient_Card",
        ErrorCode_GOA_Existed = "ErrorCode_GOA_Existed",
        ErrorCode_Create_Goa_Catalog_Failed = "ErrorCode_Create_Goa_Catalog_Failed",
        ErrorCode_ValidationError_EDMP_Not_Supported_Version = "ErrorCode_ValidationError_EDMP_Not_Supported_Version",
        ErrorCode_CardReader_UnknownType_Card_Status = "ErrorCode_CardReader_UnknownType_Card_Status",
        ErrorCode_Sdik_Existed = "ErrorCode_Sdik_Existed",
        ErrorCode_Patient_Missing_Gender = "ErrorCode_Patient_Missing_Gender",
        ErrorCode_Insurance_InValid_For_PrivateSchein = "ErrorCode_Insurance_InValid_For_PrivateSchein",
        ErrorCode_PrivateContractGroup_NotFound = "ErrorCode_PrivateContractGroup_NotFound",
        ErrorCode_CardReader_ErrorCode105 = "ErrorCode_CardReader_ErrorCode105",
        ErrorCode_LDAP_AddressBookNotFound = "ErrorCode_LDAP_AddressBookNotFound",
        ErrorCode_LDAP_MailNotFound = "ErrorCode_LDAP_MailNotFound",
        ErrorCode_CardReader_Not_Found_Doctor_Card = "ErrorCode_CardReader_Not_Found_Doctor_Card",
        ErrorCode_Validation_Ad4125_InValid = "ErrorCode_Validation_Ad4125_InValid",
        ErrorCode_CardReader_Invalid_Vsd_TimeStamp = "ErrorCode_CardReader_Invalid_Vsd_TimeStamp",
        ErrorCode_CardReader_Invalid_Insurance = "ErrorCode_CardReader_Invalid_Insurance",
        ErrorCode_CardReader_Cannot_Delete_Insurance = "ErrorCode_CardReader_Cannot_Delete_Insurance",
        ErrorCode_GoaService_ExcludedCode = "ErrorCode_GoaService_ExcludedCode",
        ErrorCode_Companion_NotReady = "ErrorCode_Companion_NotReady",
        ErrorCode_EDMP_Gender_Not_Allowed = "ErrorCode_EDMP_Gender_Not_Allowed",
        ErrorCode_Sdkt_Invalid_Validity = "ErrorCode_Sdkt_Invalid_Validity",
        ErrorCode_Sdkt_Invalid_Restrict_Region = "ErrorCode_Sdkt_Invalid_Restrict_Region",
        ErrorCode_ValidationError_MustHaveRVSA = "ErrorCode_ValidationError_MustHaveRVSA",
        ErrorCode_ValidationError_RequireLanr = "InVertretungFuer_LANR",
        ErrorCode_ValidationError_RequireBsnr = "InVertretungFuer_BSNR",
        ErrorCode_Schein_Validation_Patient_Insurance_Not_Found = "ErrorCode_Schein_Validation_Patient_Insurance_Not_Found",
        ErrorCode_Sdik_Assigned_To_Schein = "ErrorCode_Sdik_Assigned_To_Schein",
        ErrorCode_Validation_MessageVERT647 = "ErrorCode_Validation_MessageVERT647",
        ErrorCode_TI_Card_Not_Found = "ErrorCode_TI_Card_Not_Found",
        ErrorCode_SMCB_Card_Not_Found = "ErrorCode_SMCB_Card_Not_Found",
        ErrorCode_CardReader_ReadCardDate_Not_Found = "ErrorCode_CardReader_ReadCardDate_Not_Found",
        ErrorCode_EAU_NotTransmitted = "ErrorCode_EAU_NotTransmitted",
        ErrorCode_Record_Exist = "ErrorCode_Record_Exist",
        ErrorCode_HpmFunction_Not_Available = "ErrorCode_HpmFunction_Not_Available",
        ErrorCode_Zitadel_Resource_Already_Exits = "Resource_Already_Exits",
        ErrorCode_Zitadel_Resource_Not_Found = "Resource_Not_Found",
        ErrorCode_Zitadel_Resource_Not_Changed = "Resource_Not_Changed",
        ErrorCode_CardReader_Invalid_CardType = "ErrorCode_CardReader_Invalid_CardType",
        ErrorCode_GoaService_GoaNumber_Not_Found = "ErrorCode_GoaService_GoaNumber_Not_Found",
        ErrorCode_GoaService_Invalid_GOA = "ErrorCode_GoaService_Invalid_GOA",
        ErrorCode_DoctorLetter_Timeline_Not_Found = "ErrorCode_DoctorLetter_Timeline_Not_Found",
        ErrorCode_ValidationError_Attachment = "ErrorCode_ValidationError_Attachment",
        ErrorCode_CardReader_Invalid_Vknr = "ErrorCode_CardReader_Invalid_Vknr",
        ErrorCode_CardReader_Invalid_Sdik = "ErrorCode_CardReader_Invalid_Sdik",
        ErrorCode_EAU_SendPrintOutToInsurance = "ErrorCode_EAU_SendPrintOutToInsurance",
        ErrorCode_EAB_RecordNotFound = "ErrorCode_EAB_RecordNotFound",
        ErrorCode_EAB_SettingNotFound = "ErrorCode_EAB_SettingNotFound",
        ErrorCode_ServiceCodeInMasterDataNotFound = "ErrorCode_ServiceCodeInMasterDataNotFound",
        ErrorCode_Validation_MessageABRG669 = "ErrorCode_Validation_MessageABRG669",
        ErrorCode_Diga_PznAlreadyExists = "ErrorCode_Diga_PznAlreadyExists",
        ErrorCode_Diga_PznExpired = "ErrorCode_Diga_PznExpired",
        ErrorCode_EAB_Mail_Invalid = "ErrorCode_EAB_Mail_Invalid",
        ErrorCode_EAB_History_RecordNotFound = "ErrorCode_EAB_History_RecordNotFound",
        ErrorCode_Cannot_Delete_Timeline_Entry = "ErrorCode_Cannot_Delete_Timeline_Entry",
        ErrorCode_Add_Organization_Failed = "ErrorCode_Add_Organization_Failed",
        ErrorCode_Organization_Exists = "ErrorCode_Organization_Exists",
        ErrorCode_GrantPRO_Failed = "ErrorCode_GrantPRO_Failed",
        ErrorCode_GrantCAL_Failed = "ErrorCode_GrantCAL_Failed",
        ErrorCode_Organization_Deactivated = "ErrorCode_Organization_Deactivated",
        ErrorCode_Invalid_IK_Number = "ErrorCode_Invalid_IK_Number",
        ErrorCode_MobileCard_SettingNotFound = "ErrorCode_MobileCard_SettingNotFound",
        ErrorCode_Validation_Abrd1564 = "ErrorCode_Validation_Abrd1564",
        ErrorCode_WarningForGroupDoctorValidate = "ErrorCode_WarningForGroupDoctorValidate",
        Hint_ABRD1062 = "Hint_ABRD1062",
        ErrorCode_Arriba_Not_Eligible = "ErrorCode_Arriba_Not_Eligible",
        ErrorCode_PrinterProfileGroup_Not_Found = "ErrorCode_PrinterProfileGroup_Not_Found",
        ErrorCode_LDAPConnection_Failed = "ErrorCode_LDAPConnection_Failed",
        ErrorCode_Arriba_Session_Not_Finished = "ErrorCode_Arriba_Session_Not_Finished",
        ErrorCode_Device_Wrong_Format = "ErrorCode_Device_Wrong_Format",
        ErrorCode_KIM_Account_Invalid = "ErrorCode_KIM_Account_Invalid",
        ErrorCode_TI_Setting_Not_Found = "ErrorCode_TI_Setting_Not_Found",
        ErrorCode_UniqueFileAndFolder_GdtImport = "ErrorCode_UniqueFileAndFolder_GdtImport",
        ErrorCode_Forbidden = "ErrorCode_Forbidden",
        ErrorCode_Unauthorized = "ErrorCode_Unauthorized",
        ErrorCode_Insurance_Has_Assign_Schein = "ErrorCode_Insurance_Has_Assign_Schein",
        ErrorCode_Duplicate_LHMItem = "ErrorCode_Duplicate_LHMItem",
        ErrorCode_Cost_Unit_Has_Expired = "ErrorCode_Cost_Unit_Has_Expired",
        ErrorCode_Cost_Unit_IsNot_Available_In_KvRegion = "ErrorCode_Cost_Unit_IsNot_Available_In_KvRegion",
        ErrorCode_CanNot_Change_InsuranceType = "ErrorCode_CanNot_Change_InsuranceType",
        ErrorCode_Missmatch5005And5050 = "ErrorCode_Missmatch5005And5050",
        ErrorCode_UV_GOA_Existed = "ErrorCode_UV_GOA_Existed",
        ErrorCode_DocumentType_NameExisted = "ErrorCode_DocumentType_NameExisted",
        ErrorCode_DocumentType_AbbrExisted = "ErrorCode_DocumentType_AbbrExisted",
        ErrorCode_CardReader_Invalid_KVK = "ErrorCode_CardReader_Invalid_KVK",
        ErrorCode_ExportReportFailed = "ErrorCode_ExportReportFailed",
        ErrorCode_Insurance_InValid_For_BgSchein = "ErrorCode_Insurance_InValid_For_BgSchein",
        ErrorCode_PrinterHost_Not_Found = "ErrorCode_PrinterHost_Not_Found",
        ErrorCode_PrinterSetting_IsInvalid = "ErrorCode_PrinterSetting_IsInvalid",
        ErrorCode_Printer_Connection_Failed = "ErrorCode_Printer_Connection_Failed",
        ErrorCode_Validation_Abrg1565 = "ErrorCode_Validation_Abrg1565",
        ErrorCode_Goa_Not_Found = "ErrorCode_Goa_Not_Found",
        ErrorCode_Sdebm_Catalog_Existed = "ErrorCode_Sdebm_Catalog_Existed",
        ErrorCode_Sdebm_Catalog_Not_Found = "ErrorCode_Sdebm_Catalog_Not_Found",
        ErrorCode_Sdik_NotFound = "ErrorCode_Sdik_NotFound",
        ErrorCode_UV_GOA_Not_Found = "ErrorCode_UV_GOA_Not_Found",
        ErrorCode_Masterdata_GenerateApiKey_Failed = "ErrorCode_Masterdata_GenerateApiKey_Failed",
        ErrorCode_PTV_Import_Older_Than_Latest = "ErrorCode_PTV_Import_Older_Than_Latest",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

