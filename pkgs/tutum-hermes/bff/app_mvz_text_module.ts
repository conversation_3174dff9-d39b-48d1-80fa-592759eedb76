/* eslint-disable */
// This code was autogenerated from app/mvz/text_module.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as text_module_common from "./text_module_common"


// Type definitions
		export interface GetInvalidOmimGChainItem {
				id: string
				textShortcut: string
		}
	

		export interface GetInvalidOmimGChainResponse {
				data: Array<GetInvalidOmimGChainItem>
		}
	

		export interface GetTextModulesRequest {
				query: string
				page?: number
				pageSize?: number
				useFors: Array<text_module_common.TextModuleUseFor>
		}
	

		export interface GetInvalidOmimGChainRequest {
				selectedDate: number
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

