/* eslint-disable */
// This code was autogenerated from app/mvz/patient_encounter.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as catalog_material_cost_common from "./catalog_material_cost_common"
import * as catalog_sdebm_common from "./catalog_sdebm_common"
import * as common5 from "./hgnc_common"
import * as common1 from "./sdicd_common"
import * as common3 from "./sdomim_common"
import * as common2 from "./sdops_common"
import * as common4 from "./sdva_common"
import * as doctor_participate from "./service_domains_doctor_participate"
import * as common from "./version_info_common"


// Type definitions
		export interface GetEbmsRequest {
				selectedDate: number
		}
	

		export interface GetEbmsResponse {
				items: Array<catalog_sdebm_common.EbmSearchItem>
		}
	

		export interface GetMaterialCostsResponse {
				items: Array<catalog_material_cost_common.MaterialCostCatalog>
		}
	

		export interface GetOpsResponse {
				items: Array<catalog_sdebm_common.OpsCatalog>
		}
	

		export interface GetAdditionalInfoFieldsKvRequest {
				treatmentCase: string
				selectedDate: number
		}
	

		export interface GetAdditionalInfoFieldsSelectiveContractRequest {
				contractId: string
		}
	

		export interface GetAdditionalInfoFieldsResponse {
				fields: Array<catalog_sdebm_common.Field>
		}
	

		export interface GetPseudoGnrRequest {
				bsnr: string
		}
	

		export interface PseudoGNR {
				key: string
				value: string
		}
	

		export interface GetPseudoGnrResponse {
				pseudoGNR: Array<PseudoGNR>
		}
	

		export interface GetOmimLicenseRequest {
				selectedDate: number
				patientId: string
		}
	

		export interface GetOmimLicenseResponse {
				data: common.VersionInfo
		}
	

		export interface SearchEbmsRequest {
				selectedDate: number
				query: string
				organizationId?: string
		}
	

		export interface SearchEbmsResponse {
				items: Array<catalog_sdebm_common.EbmSearchItem>
		}
	

		export interface SearchDiagnosisRequest {
				query: string
				selectedDate: number
				catalog?: common1.IcdSearchCatalog
				doctorSpecialistType?: string
		}
	

		export interface SearchDiagnosisResponse {
				items: Array<common1.IcdSeachItem>
		}
	

		export interface SearchOpsRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchOpsResponse {
				items: Array<common2.OpsItem>
		}
	

		export interface SearchOmimGRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchOmimPRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchOmimGResponse {
				items: Array<common3.OmimG>
		}
	

		export interface SearchOmimPResponse {
				items: Array<common3.OmimP>
		}
	

		export interface SearchSdvaRequest {
				query: Array<string>
				selectedDate: number
		}
	

		export interface SearchSdvaResponse {
				items: Array<common4.Chapter>
		}
	

		export interface SearchHgncRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchHgncResponse {
				items: Array<common5.HgncItem>
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

