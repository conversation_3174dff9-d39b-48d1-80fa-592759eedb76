/* eslint-disable */
// This code was autogenerated from app/sync_cal/sync_cal.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';



// Type definitions
		export interface SyncCalRequest {
				syncCalLogId: string
		}
	

		export interface GetStatusSyncCalResponse {
				status: string
				syncPatientDate?: Date
				startAt?: Date
				endAt?: Date
				createdAt?: Date
				updatedAt?: Date
				errorLog?: string
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

