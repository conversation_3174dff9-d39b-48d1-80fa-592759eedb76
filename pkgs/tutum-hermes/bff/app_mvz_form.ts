/* eslint-disable */
// This code was autogenerated from app/mvz/form.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common1 from "./eau_common"
import * as common from "./form_common"
import * as schein_common from "./schein_common"


// Type definitions
		export interface GetFormRequest {
				id?: string
				oKV?: string
				ikNumber?: number
				contractId?: string
		}
	

		export interface GetFormResponse {
				form: common.Form
		}
	

		export interface PrescribeRequest {
				prescribe: common.Prescribe
				printOption?: common.PrintOption
				eAUSetting?: common1.EAUSetting
		}
	

		export interface PrescribeResponse {
				prescribe: common.Prescribe
				pdfBase64: string
				pdfBase64List?: Array<string>
		}
	

		export interface BuildBundleAndValidationRequest {
				prescribe: common.Prescribe
				printOption?: common.PrintOption
		}
	

		export interface BuildBundleAndValidationResponse {
				eAUValidation?: common1.EAUValidation
		}
	

		export interface GetPrescribeRequest {
				prescribeId: string
		}
	

		export interface GetPrescribeResponse {
				prescribe: common.Prescribe
		}
	

		export interface GetFormsResponse {
				forms: Array<common.Form>
		}
	

		export interface PrintRequest {
				prescribeId: string
				printOption: common.PrintOption
				eAUSetting?: common1.EAUSetting
		}
	

		export interface PrintData {
				formUrls: Array<string>
		}
	

		export interface PrintResponse {
				printData: Array<PrintData>
		}
	

		export interface GetFormsRequest {
				oKV: string
				ikNumber: number
				contractId: string
				chargeSystemId: string
				moduleChargeSystemId?: string
				scheinMainGroup?: schein_common.MainGroup
		}
	

		export interface PrintPlainPdfRequest {
				formSetting: string
				formName: string
				treatmentDoctorId?: string
				isRemoveBackground?: boolean
				contractId?: string
		}
	

		export interface PrintPlainPdfResponse {
				formUrl: string
		}
	

		export interface GetFileUrlRequest {
				fileName: string
		}
	

		export interface GetFileUrlResponse {
				fileUrl: string
		}
	

		export interface PrescribeResponseV2 {
				printInfos: Array<common.PrintResult>
				timelineId: string
		}
	

		export interface GetIndicatorActiveIngredientsRequest {
				contractId: string
		}
	

		export interface AtcDiagnoseCode {
				atcCode?: string
				diagnoseCode?: string
		}
	

		export interface GetIndicatorActiveIngredientsResponse {
				data: Array<AtcDiagnoseCode>
		}
	

		export interface GetIcdFormRequest {
				contractId: string
		}
	

		export interface GetIcdFormResponse {
				icdCodes: Array<string>
		}
	


// enum definitions
    export enum KBV_PRF_NR {
        KBV_PRF_LABEL = "label_prf_nr",
        KBV_PRF_VALUE = "Y/9/2407/36/001",
	}

    export enum EHIC_PRF_NR {
        EHIC_PRF_LABEL = "label_ehic_prf_nr",
        EHIC_PRF_VALUE = "Y/1/2404/36/701",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

