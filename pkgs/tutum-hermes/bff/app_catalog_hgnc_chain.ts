/* eslint-disable */
// This code was autogenerated from app/mvz/catalog_hgnc_chain.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"
import * as common1 from "./hgnc_common"


// Type definitions
		export interface HgncChain {
				id: string
				name: string
				hgncItems: Array<common1.HgncItem>
		}
	

		export interface SearchHgncChainRequest {
				name: string
				paginationRequest: common.PaginationRequest
		}
	

		export interface SearchHgncChainResponse {
				hgncChains: Array<HgncChain>
				pagination: common.PaginationResponse
		}
	

		export interface CreateHgncChainRequest {
				name: string
				hgncItems: Array<common1.HgncItem>
		}
	

		export interface CreateHgncChainResponse {
				chain: HgncChain
		}
	

		export interface UpdateHgncChainRequest {
				id: string
				name: string
				hgncItems: Array<common1.HgncItem>
		}
	

		export interface UpdateHgncChainResponse {
				chain: Hgnc<PERSON>hain
		}
	

		export interface DeleteHgncChainRequest {
				id: string
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

