/* eslint-disable */
// This code was autogenerated from service/domains/patient_participation.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"
import * as profile from "./service_domains_profile"


// Type definitions
		export interface GetPatientParticipationByIdRequest {
				patientId: string
		}
	

		export interface GetPatientParticipationRequest {
				patientId: string
				checkDate: number
		}
	

		export interface GetPatientParticipationWithoutCheckDateRequest {
				patientId: string
		}
	

		export interface CheckPatientParticipationRequest {
				patientId: string
				checkDate: Date
				contractId: string
				doctorId: string
		}
	

		export interface GetPatientParticipationResponse {
				participations: Array<PatientParticipation>
		}
	

		export interface CheckPatientParticipationResponse {
				isAvailable: boolean
		}
	

		export interface GetPatientParticipationForPTVImportRequest {
				doctorId: string
				contractId: string
		}
	

		export interface UpdatePatientParticipationForPTVImportRequest {
				doctorId: string
				contractId: string
				patientId: string
				ikNumber: number
				insuranceNumber: string
				startDate?: number
				endDate?: number
				status: PatientParticipationStatus
				reason?: string
				ppId?: string
				treatmentType: string
		}
	

		export interface GetPatientParticipationForPTVImportResponse {
				participationPTVImports: Array<PatientParticipationPTVImport>
		}
	

		export interface EventPatientParticipationChange {
				patientId: string
				contractIds: Array<string>
				insuranceNumber: string
		}
	

		export interface GetDoctorsCanTreatAsDeputyRequest {
				doctorId: string
				contractId: string
		}
	

		export interface GetDoctorsCanTreatAsDeputyResponse {
				profiles: Array<profile.EmployeeProfileResponse>
		}
	

		export interface CreatePatientParticipationForPTVImportResponse {
				participations: Array<PatientParticipation>
		}
	

		export interface PatientParticipationPTVImport {
				patientId?: string
				startDate?: number
				endDate?: number
				status: PatientParticipationStatus
				insuranceNumber: string
				ikNumber: number
				ppId?: string
				treatmentType: string
		}
	

		export interface PatientParticipation {
				id?: string
				startDate?: number
				endDate?: number
				doctorId?: string
				contractId: string
				ikNumber: number
				status: PatientParticipationStatus
				createdDate: number
				updatedDate?: number
				updatedBy?: string
				doctorFunctionType: DoctorFunctionType
				contractType: common.ContractType
				isTransmittedHpm: boolean
				isChangingDoctor: boolean
				chargeSystemId: string
				teId: string
				patientId: string
		}
	


// enum definitions
    export enum DoctorFunctionType {
        DoctorFunctionTypeCustodian = "Custodian",
        DoctorFunctionTypeDeputy = "Deputy",
	}

    export enum PatientParticipationStatus {
        PatientParticipation_Requested = "REQUESTED",
        PatientParticipation_Rejected = "REJECTED",
        PatientParticipation_Active = "ACTIVE",
        PatientParticipation_Cancelled = "CANCELLED",
        PatientParticipation_Terminated = "TERMINATED",
	}


// event definition constant ----------------------------------------
       	const EVENT_PatientParticipationChange = "api.service.domains.ServiceDomainsPatientParticipation.PatientParticipationChange";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenPatientParticipationChange(handler: (data: EventPatientParticipationChange) => void): void {
			const [response, setResponse] = useState<EventPatientParticipationChange>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_PatientParticipationChange, _listener);
				return () => window.removeEventListener(EVENT_PatientParticipationChange, _listener);
			}, []);
        }

