/* eslint-disable */
// This code was autogenerated from app/admin/bdt.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./bdt_log_common"


// Type definitions
		export interface ImportBDTRequest {
				fileName: string
		}
	

		export interface ImportBdtResponse {
				data: common.BdtLogModel
		}
	

		export interface GetProcessingStatusResponse {
				data: common.BdtLogModel
		}
	

		export interface UploadDocumentRequest {
				fileName: string
		}
	

		export interface UploadDocumentResponse {
				success: boolean
		}
	

		export interface GetProcessUploadDocumentResponse {
				data: common.UploadDocumentLogModel
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

