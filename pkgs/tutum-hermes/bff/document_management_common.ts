/* eslint-disable */
// This code was autogenerated from service/domains/document_management_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as catalog_sdav_common from "./catalog_sdav_common"
import * as common from "./document_type_common"
import * as patient_profile_common from "./patient_profile_common"


// Type definitions
		export interface Patient {
				id: string
				firstName: string
				lastName: string
				dateOfBirth: patient_profile_common.DateOfBirth
				patientNumber: number
				gender: string
				fullName: string
		}
	

		export interface DocumentManagementSender {
				bsnr: string
				lanr: string
		}
	

		export interface DocumentManagementItem {
				id: string
				companionFileId?: number
				companionFilePath?: string
				patient?: Patient
				sender?: catalog_sdav_common.SdavCatalog
				documentName: string
				documentType?: common.DocumentType
				description: string
				status: DocumentManagementStatus
				importedDate: number
				documentDirPath: string
				gdtSenderName: string
				metaData?: {[key:string]:string}
				documentUrl?: string
		}
	

		export interface DocumentManagementModel {
				companionFileId?: number
				companionFilePath?: string
				patientId?: string
				senderId?: string
				documentType?: common.DocumentType
				documentName: string
				description: string
				status: DocumentManagementStatus
				importedDate: number
				documentSettingId?: string
				gdtImportModTime: number
				metaData?: {[key:string]:string}
		}
	

		export interface ReadBy {
				id: string
				name: string
				readAt: number
		}
	

		export interface FolderState {
				currentState: {[key:string]:number}
				dirPath: string
		}
	


// enum definitions
    export enum DocumentManagementStatus {
        DocumentManagementStatus_New = "NEW",
        DocumentManagementStatus_ReImport = "RE_IMPORT",
        DocumentManagementStatus_InProgress = "IN_PROGRESS",
        DocumentManagementStatus_Completed = "COMPLETED",
        DocumentManagementStatus_Failed = "FAILED",
	}

    export enum DocumentNotificationType {
        DocumentNotificationType_LDT = "LDT",
        DocumentNotificationType_GDT = "GDT",
	}

    export enum MetaDataKey {
        MetaDataKey_PatientLabOrder = "patient_lab_order",
        MetaDataKey_PatientLabResult = "patient_lab_result",
        MetaDataKey_FileNameUploaded = "file_name_uploaded",
        MetaDataKey_LabResultChunkFileName = "lab_result_chunk_file_name",
        MetaDataKey_GDTFileStatus = "gdt_file_status",
	}

    export enum LDTStatusCode {
        LDTStatusCode_Final = "E",
        LDTStatusCode_Partial = "T",
        LDTStatusCode_Preliminary = "V",
        LDTStatusCode_Archived = "A",
        LDTStatusCode_Supplementary = "N",
        LDTStatusCode_Unknown = "",
	}

    export enum LDTStatus {
        LDTStatus_Final = "Endbefund",
        LDTStatus_Partial = "Teilbefund",
        LDTStatus_Preliminary = "Vorläufiger Befund",
        LDTStatus_Archived = "Archiv-Befund",
        LDTStatus_Supplementary = "Nachforderung",
        LDTStatus_Unknown = "Unknown",
	}

    export enum LabTitleType {
        LabTitleType_LabOrder = "Laboratory Order",
        LabTitleType_LabResult = "Laboratory Result",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

