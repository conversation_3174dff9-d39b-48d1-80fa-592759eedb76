/* eslint-disable */
// This code was autogenerated from app/mvz/patient_combination.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface CombinePatientsRequest {
				targetPatientId: string
				duplicatedPatientIds: Array<string>
		}
	


// enum definitions

// method name convention const
		export const EVENT_CombinePatients = "api.app.mvz.PatientCombinationApp.CombinePatients";
		export const EVENT_CombinePatients_Response = "api.app.mvz.PatientCombinationApp.CombinePatients.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_CombinePatients = "/api/app/mvz/patient/combination/combinePatients";


// Define action methods and their listener -----------------------------------------------------------------
			export async function combinePatients(request: CombinePatientsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_CombinePatients, { init , request})
			}

			export function useQueryCombinePatients<TransformedType =any>(payload: CombinePatientsRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CombinePatients, payload],
					queryFn: async ({ signal }) => await combinePatients(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCombinePatients(opts?: UseMutationOptions<ResponseType<any>, ErrorType,CombinePatientsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await combinePatients(request),
						retry: false,
						...opts
                });
            }
    

