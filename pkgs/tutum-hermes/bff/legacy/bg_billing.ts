/* eslint-disable */
// This code was autogenerated from app/mvz/bg_billing.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common from "./bg_billing_common"
import * as common1 from "./common"
import * as patient_profile_common from "./patient_profile_common"
import * as common2 from "./timeline_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface CreateBgBillingRequest {
				item: common.BillingRecord
		}
	

		export interface GetBgBillingsRequest {
				search?: string
				pagination: common1.PaginationRequest
				filter?: common.BgBillingFilter
		}
	

		export interface GetBgBillingsResponse {
				items: Array<common.BgBillingItem>
				total: number
		}
	

		export interface GetBgBillingByScheinIdRequest {
				scheinId: string
		}
	

		export interface GetBgBillingByScheinIdResponse {
				item: common.BgBillingItem
		}
	

		export interface GetBgBillingByIdRequest {
				id: string
		}
	

		export interface GetBgBillingByIdResponse {
				item: common.BgBillingItem
		}
	

		export interface GetUvGoaServiceCodeRequest {
				id: string
		}
	

		export interface GetUvGoaServiceCodeByIdsRequest {
				ids: Array<string>
		}
	

		export interface UvGoaServiceTimelineData {
				billingId: string
				timelineModels: Array<common2.TimelineModel>
				invoiceInfo: common.InvoiceInfo
		}
	

		export interface GetUvGoaServiceCodeResponse {
				data: UvGoaServiceTimelineData
		}
	

		export interface GetUvGoaServiceCodeByIdsResponse {
				data: Array<UvGoaServiceTimelineData>
		}
	

		export interface GetPrintedInvoicesRequest {
				patientId: string
				billingId: string
		}
	

		export interface GetPrintedInvoicesResponse {
				timelineModels: Array<common2.TimelineModel>
		}
	

		export interface MarkBgBillingPaidRequest {
				patientId: string
				billingId: string
				invoiceNumber: string
		}
	

		export interface MarkBgBillingPaidResponse {
				timelineModel: common2.TimelineModel
		}
	

		export interface MarkBgBillingUnpaidRequest {
				patientId: string
				billingId: string
		}
	

		export interface MarkBgBillingUnpaidResponse {
				item: common.BgBillingItem
		}
	

		export interface MarkBgBillingCancelledRequest {
				patientId: string
				billingId: string
		}
	

		export interface MarkBgBillingCancelledResponse {
				item: common.BgBillingItem
		}
	

		export interface GetListDoctorResponse {
				doctors: Array<common.Doctor>
		}
	

		export interface GetListInsuranceResponse {
				insurances: Array<patient_profile_common.InsuranceInfo>
		}
	

		export interface GetListStatusResponse {
				status: Array<common.BillingStatus>
		}
	

		export interface GetRangeAmountResponse {
				minPrice: number
				maxPrice: number
		}
	


// enum definitions

// method name convention const
		export const EVENT_GetBgBillingByScheinId = "api.app.mvz.BgBillingApp.GetBgBillingByScheinId";
		export const EVENT_GetBgBillingByScheinId_Response = "api.app.mvz.BgBillingApp.GetBgBillingByScheinId.Response";
		export const EVENT_GetBgBilling = "api.app.mvz.BgBillingApp.GetBgBilling";
		export const EVENT_GetBgBilling_Response = "api.app.mvz.BgBillingApp.GetBgBilling.Response";
		export const EVENT_getUvGoaServiceCode = "api.app.mvz.BgBillingApp.GetUvGoaServiceCode";
		export const EVENT_getUvGoaServiceCode_Response = "api.app.mvz.BgBillingApp.GetUvGoaServiceCode.Response";
		export const EVENT_getUvGoaServiceCodeByIds = "api.app.mvz.BgBillingApp.GetUvGoaServiceCodeByIds";
		export const EVENT_getUvGoaServiceCodeByIds_Response = "api.app.mvz.BgBillingApp.GetUvGoaServiceCodeByIds.Response";
		export const EVENT_GetBgBillingById = "api.app.mvz.BgBillingApp.GetBgBillingById";
		export const EVENT_GetBgBillingById_Response = "api.app.mvz.BgBillingApp.GetBgBillingById.Response";
		export const EVENT_GetPrintedInvoices = "api.app.mvz.BgBillingApp.GetPrintedInvoices";
		export const EVENT_GetPrintedInvoices_Response = "api.app.mvz.BgBillingApp.GetPrintedInvoices.Response";
		export const EVENT_MarkBgBillingPaid = "api.app.mvz.BgBillingApp.MarkBgBillingPaid";
		export const EVENT_MarkBgBillingPaid_Response = "api.app.mvz.BgBillingApp.MarkBgBillingPaid.Response";
		export const EVENT_MarkBgBillingUnpaid = "api.app.mvz.BgBillingApp.MarkBgBillingUnpaid";
		export const EVENT_MarkBgBillingUnpaid_Response = "api.app.mvz.BgBillingApp.MarkBgBillingUnpaid.Response";
		export const EVENT_MarkBgBillingCancelled = "api.app.mvz.BgBillingApp.MarkBgBillingCancelled";
		export const EVENT_MarkBgBillingCancelled_Response = "api.app.mvz.BgBillingApp.MarkBgBillingCancelled.Response";
		export const EVENT_GetListDoctor = "api.app.mvz.BgBillingApp.GetListDoctor";
		export const EVENT_GetListDoctor_Response = "api.app.mvz.BgBillingApp.GetListDoctor.Response";
		export const EVENT_GetListInsurance = "api.app.mvz.BgBillingApp.GetListInsurance";
		export const EVENT_GetListInsurance_Response = "api.app.mvz.BgBillingApp.GetListInsurance.Response";
		export const EVENT_GetListStatus = "api.app.mvz.BgBillingApp.GetListStatus";
		export const EVENT_GetListStatus_Response = "api.app.mvz.BgBillingApp.GetListStatus.Response";
		export const EVENT_GetRangeAmount = "api.app.mvz.BgBillingApp.GetRangeAmount";
		export const EVENT_GetRangeAmount_Response = "api.app.mvz.BgBillingApp.GetRangeAmount.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetBgBillingByScheinId = "/api/app/mvz/bg/billing/getBgBillingByScheinId";
        export const LEGACY_TOPIC_GetBgBilling = "/api/app/mvz/bg/billing/getBgBilling";
        export const LEGACY_TOPIC_GetUvGoaServiceCode = "/api/app/mvz/bg/billing/getUvGoaServiceCode";
        export const LEGACY_TOPIC_GetUvGoaServiceCodeByIds = "/api/app/mvz/bg/billing/getUvGoaServiceCodeByIds";
        export const LEGACY_TOPIC_GetBgBillingById = "/api/app/mvz/bg/billing/getBgBillingById";
        export const LEGACY_TOPIC_GetPrintedInvoices = "/api/app/mvz/bg/billing/getPrintedInvoices";
        export const LEGACY_TOPIC_MarkBgBillingPaid = "/api/app/mvz/bg/billing/markBgBillingPaid";
        export const LEGACY_TOPIC_MarkBgBillingUnpaid = "/api/app/mvz/bg/billing/markBgBillingUnpaid";
        export const LEGACY_TOPIC_MarkBgBillingCancelled = "/api/app/mvz/bg/billing/markBgBillingCancelled";
        export const LEGACY_TOPIC_GetListDoctor = "/api/app/mvz/bg/billing/getListDoctor";
        export const LEGACY_TOPIC_GetListInsurance = "/api/app/mvz/bg/billing/getListInsurance";
        export const LEGACY_TOPIC_GetListStatus = "/api/app/mvz/bg/billing/getListStatus";
        export const LEGACY_TOPIC_GetRangeAmount = "/api/app/mvz/bg/billing/getRangeAmount";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getBgBillingByScheinId(request: GetBgBillingByScheinIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetBgBillingByScheinIdResponse>("POST", LEGACY_TOPIC_GetBgBillingByScheinId, { init , request})
			}

			export function useQueryGetBgBillingByScheinId<TransformedType =GetBgBillingByScheinIdResponse>(payload: GetBgBillingByScheinIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetBgBillingByScheinIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetBgBillingByScheinIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetBgBillingByScheinId, payload],
					queryFn: async ({ signal }) => await getBgBillingByScheinId(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetBgBillingByScheinId(opts?: UseMutationOptions<ResponseType<GetBgBillingByScheinIdResponse>, ErrorType,GetBgBillingByScheinIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getBgBillingByScheinId(request),
						retry: false,
						...opts
                });
            }
    
			export async function getBgBilling(request: GetBgBillingsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetBgBillingsResponse>("POST", LEGACY_TOPIC_GetBgBilling, { init , request})
			}

			export function useQueryGetBgBilling<TransformedType =GetBgBillingsResponse>(payload: GetBgBillingsRequest,ops?: CustomUseQueryOptions<ResponseType<GetBgBillingsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetBgBillingsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetBgBilling, payload],
					queryFn: async ({ signal }) => await getBgBilling(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetBgBilling(opts?: UseMutationOptions<ResponseType<GetBgBillingsResponse>, ErrorType,GetBgBillingsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getBgBilling(request),
						retry: false,
						...opts
                });
            }
    
			export async function getUvGoaServiceCode(request: GetUvGoaServiceCodeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetUvGoaServiceCodeResponse>("POST", LEGACY_TOPIC_GetUvGoaServiceCode, { init , request})
			}

			export function useQueryGetUvGoaServiceCode<TransformedType =GetUvGoaServiceCodeResponse>(payload: GetUvGoaServiceCodeRequest,ops?: CustomUseQueryOptions<ResponseType<GetUvGoaServiceCodeResponse>, TransformedType>) {
                return useQuery<ResponseType<GetUvGoaServiceCodeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getUvGoaServiceCode, payload],
					queryFn: async ({ signal }) => await getUvGoaServiceCode(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetUvGoaServiceCode(opts?: UseMutationOptions<ResponseType<GetUvGoaServiceCodeResponse>, ErrorType,GetUvGoaServiceCodeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getUvGoaServiceCode(request),
						retry: false,
						...opts
                });
            }
    
			export async function getUvGoaServiceCodeByIds(request: GetUvGoaServiceCodeByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetUvGoaServiceCodeByIdsResponse>("POST", LEGACY_TOPIC_GetUvGoaServiceCodeByIds, { init , request})
			}

			export function useQueryGetUvGoaServiceCodeByIds<TransformedType =GetUvGoaServiceCodeByIdsResponse>(payload: GetUvGoaServiceCodeByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<GetUvGoaServiceCodeByIdsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetUvGoaServiceCodeByIdsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getUvGoaServiceCodeByIds, payload],
					queryFn: async ({ signal }) => await getUvGoaServiceCodeByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetUvGoaServiceCodeByIds(opts?: UseMutationOptions<ResponseType<GetUvGoaServiceCodeByIdsResponse>, ErrorType,GetUvGoaServiceCodeByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getUvGoaServiceCodeByIds(request),
						retry: false,
						...opts
                });
            }
    
			export async function getBgBillingById(request: GetBgBillingByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetBgBillingByIdResponse>("POST", LEGACY_TOPIC_GetBgBillingById, { init , request})
			}

			export function useQueryGetBgBillingById<TransformedType =GetBgBillingByIdResponse>(payload: GetBgBillingByIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetBgBillingByIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetBgBillingByIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetBgBillingById, payload],
					queryFn: async ({ signal }) => await getBgBillingById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetBgBillingById(opts?: UseMutationOptions<ResponseType<GetBgBillingByIdResponse>, ErrorType,GetBgBillingByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getBgBillingById(request),
						retry: false,
						...opts
                });
            }
    
			export async function getPrintedInvoices(request: GetPrintedInvoicesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetPrintedInvoicesResponse>("POST", LEGACY_TOPIC_GetPrintedInvoices, { init , request})
			}

			export function useQueryGetPrintedInvoices<TransformedType =GetPrintedInvoicesResponse>(payload: GetPrintedInvoicesRequest,ops?: CustomUseQueryOptions<ResponseType<GetPrintedInvoicesResponse>, TransformedType>) {
                return useQuery<ResponseType<GetPrintedInvoicesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPrintedInvoices, payload],
					queryFn: async ({ signal }) => await getPrintedInvoices(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPrintedInvoices(opts?: UseMutationOptions<ResponseType<GetPrintedInvoicesResponse>, ErrorType,GetPrintedInvoicesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPrintedInvoices(request),
						retry: false,
						...opts
                });
            }
    
			export async function markBgBillingPaid(request: MarkBgBillingPaidRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<MarkBgBillingPaidResponse>("POST", LEGACY_TOPIC_MarkBgBillingPaid, { init , request})
			}

			export function useQueryMarkBgBillingPaid<TransformedType =MarkBgBillingPaidResponse>(payload: MarkBgBillingPaidRequest,ops?: CustomUseQueryOptions<ResponseType<MarkBgBillingPaidResponse>, TransformedType>) {
                return useQuery<ResponseType<MarkBgBillingPaidResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkBgBillingPaid, payload],
					queryFn: async ({ signal }) => await markBgBillingPaid(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkBgBillingPaid(opts?: UseMutationOptions<ResponseType<MarkBgBillingPaidResponse>, ErrorType,MarkBgBillingPaidRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markBgBillingPaid(request),
						retry: false,
						...opts
                });
            }
    
			export async function markBgBillingUnpaid(request: MarkBgBillingUnpaidRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<MarkBgBillingUnpaidResponse>("POST", LEGACY_TOPIC_MarkBgBillingUnpaid, { init , request})
			}

			export function useQueryMarkBgBillingUnpaid<TransformedType =MarkBgBillingUnpaidResponse>(payload: MarkBgBillingUnpaidRequest,ops?: CustomUseQueryOptions<ResponseType<MarkBgBillingUnpaidResponse>, TransformedType>) {
                return useQuery<ResponseType<MarkBgBillingUnpaidResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkBgBillingUnpaid, payload],
					queryFn: async ({ signal }) => await markBgBillingUnpaid(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkBgBillingUnpaid(opts?: UseMutationOptions<ResponseType<MarkBgBillingUnpaidResponse>, ErrorType,MarkBgBillingUnpaidRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markBgBillingUnpaid(request),
						retry: false,
						...opts
                });
            }
    
			export async function markBgBillingCancelled(request: MarkBgBillingCancelledRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<MarkBgBillingCancelledResponse>("POST", LEGACY_TOPIC_MarkBgBillingCancelled, { init , request})
			}

			export function useQueryMarkBgBillingCancelled<TransformedType =MarkBgBillingCancelledResponse>(payload: MarkBgBillingCancelledRequest,ops?: CustomUseQueryOptions<ResponseType<MarkBgBillingCancelledResponse>, TransformedType>) {
                return useQuery<ResponseType<MarkBgBillingCancelledResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkBgBillingCancelled, payload],
					queryFn: async ({ signal }) => await markBgBillingCancelled(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkBgBillingCancelled(opts?: UseMutationOptions<ResponseType<MarkBgBillingCancelledResponse>, ErrorType,MarkBgBillingCancelledRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markBgBillingCancelled(request),
						retry: false,
						...opts
                });
            }
    
			export async function getListDoctor(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetListDoctorResponse>("POST", LEGACY_TOPIC_GetListDoctor, { init })
			}

			export function useQueryGetListDoctor<TransformedType =GetListDoctorResponse>(ops?: CustomUseQueryOptions<ResponseType<GetListDoctorResponse>, TransformedType>) {
                return useQuery<ResponseType<GetListDoctorResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetListDoctor],
					queryFn: async ({ signal }) => await getListDoctor({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetListDoctor(opts?: UseMutationOptions<ResponseType<GetListDoctorResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getListDoctor(),
						retry: false,
						...opts
                });
            }
    
			export async function getListInsurance(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetListInsuranceResponse>("POST", LEGACY_TOPIC_GetListInsurance, { init })
			}

			export function useQueryGetListInsurance<TransformedType =GetListInsuranceResponse>(ops?: CustomUseQueryOptions<ResponseType<GetListInsuranceResponse>, TransformedType>) {
                return useQuery<ResponseType<GetListInsuranceResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetListInsurance],
					queryFn: async ({ signal }) => await getListInsurance({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetListInsurance(opts?: UseMutationOptions<ResponseType<GetListInsuranceResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getListInsurance(),
						retry: false,
						...opts
                });
            }
    
			export async function getListStatus(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetListStatusResponse>("POST", LEGACY_TOPIC_GetListStatus, { init })
			}

			export function useQueryGetListStatus<TransformedType =GetListStatusResponse>(ops?: CustomUseQueryOptions<ResponseType<GetListStatusResponse>, TransformedType>) {
                return useQuery<ResponseType<GetListStatusResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetListStatus],
					queryFn: async ({ signal }) => await getListStatus({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetListStatus(opts?: UseMutationOptions<ResponseType<GetListStatusResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getListStatus(),
						retry: false,
						...opts
                });
            }
    
			export async function getRangeAmount(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetRangeAmountResponse>("POST", LEGACY_TOPIC_GetRangeAmount, { init })
			}

			export function useQueryGetRangeAmount<TransformedType =GetRangeAmountResponse>(ops?: CustomUseQueryOptions<ResponseType<GetRangeAmountResponse>, TransformedType>) {
                return useQuery<ResponseType<GetRangeAmountResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetRangeAmount],
					queryFn: async ({ signal }) => await getRangeAmount({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetRangeAmount(opts?: UseMutationOptions<ResponseType<GetRangeAmountResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getRangeAmount(),
						retry: false,
						...opts
                });
            }
    

