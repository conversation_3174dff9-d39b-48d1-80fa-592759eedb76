/* eslint-disable */
// This code was autogenerated from app/admin/bdt.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common from "./bdt_log_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface ImportBDTRequest {
				fileName: string
		}
	

		export interface ImportBdtResponse {
				data: common.BdtLogModel
		}
	

		export interface GetProcessingStatusResponse {
				data: common.BdtLogModel
		}
	

		export interface UploadDocumentRequest {
				fileName: string
		}
	

		export interface UploadDocumentResponse {
				success: boolean
		}
	

		export interface GetProcessUploadDocumentResponse {
				data: common.UploadDocumentLogModel
		}
	


// enum definitions

// method name convention const
		export const EVENT_ImportBdt = "api.app.admin.BdtApp.ImportBdt";
		export const EVENT_ImportBdt_Response = "api.app.admin.BdtApp.ImportBdt.Response";
		export const EVENT_Continue = "api.app.admin.BdtApp.Continue";
		export const EVENT_Continue_Response = "api.app.admin.BdtApp.Continue.Response";
		export const EVENT_DeleteCollectionOfCareProvider = "api.app.admin.BdtApp.DeleteCollectionOfCareProvider";
		export const EVENT_DeleteCollectionOfCareProvider_Response = "api.app.admin.BdtApp.DeleteCollectionOfCareProvider.Response";
		export const EVENT_GetProcessingStatus = "api.app.admin.BdtApp.GetProcessingStatus";
		export const EVENT_GetProcessingStatus_Response = "api.app.admin.BdtApp.GetProcessingStatus.Response";
		export const EVENT_Remove = "api.app.admin.BdtApp.Remove";
		export const EVENT_Remove_Response = "api.app.admin.BdtApp.Remove.Response";
		export const EVENT_UploadDocument = "api.app.admin.BdtApp.UploadDocument";
		export const EVENT_UploadDocument_Response = "api.app.admin.BdtApp.UploadDocument.Response";
		export const EVENT_GetProcessUploadDocument = "api.app.admin.BdtApp.GetProcessUploadDocument";
		export const EVENT_GetProcessUploadDocument_Response = "api.app.admin.BdtApp.GetProcessUploadDocument.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_ImportBdt = "/api/app/admin/bdt/importBdt";
        export const LEGACY_TOPIC_Continue = "/api/app/admin/bdt/continue";
        export const LEGACY_TOPIC_DeleteCollectionOfCareProvider = "/api/app/admin/bdt/deleteCollectionOfCareProvider";
        export const LEGACY_TOPIC_GetProcessingStatus = "/api/app/admin/bdt/getProcessingStatus";
        export const LEGACY_TOPIC_Remove = "/api/app/admin/bdt/remove";
        export const LEGACY_TOPIC_UploadDocument = "/api/app/admin/bdt/uploadDocument";
        export const LEGACY_TOPIC_GetProcessUploadDocument = "/api/app/admin/bdt/getProcessUploadDocument";


// Define action methods and their listener -----------------------------------------------------------------
			export async function importBdt(request: ImportBDTRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ImportBdtResponse>("POST", LEGACY_TOPIC_ImportBdt, { init , request})
			}

			export function useQueryImportBdt<TransformedType =ImportBdtResponse>(payload: ImportBDTRequest,ops?: CustomUseQueryOptions<ResponseType<ImportBdtResponse>, TransformedType>) {
                return useQuery<ResponseType<ImportBdtResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ImportBdt, payload],
					queryFn: async ({ signal }) => await importBdt(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationImportBdt(opts?: UseMutationOptions<ResponseType<ImportBdtResponse>, ErrorType,ImportBDTRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await importBdt(request),
						retry: false,
						...opts
                });
            }
    
			export async function continue(init?: CustomRequestInit) {
				return await fetchWithHeaders<ImportBdtResponse>("POST", LEGACY_TOPIC_Continue, { init })
			}

			export function useQueryContinue<TransformedType =ImportBdtResponse>(ops?: CustomUseQueryOptions<ResponseType<ImportBdtResponse>, TransformedType>) {
                return useQuery<ResponseType<ImportBdtResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Continue],
					queryFn: async ({ signal }) => await continue({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationContinue(opts?: UseMutationOptions<ResponseType<ImportBdtResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await continue(),
						retry: false,
						...opts
                });
            }
    
			export async function deleteCollectionOfCareProvider(init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteCollectionOfCareProvider, { init })
			}

			export function useQueryDeleteCollectionOfCareProvider<TransformedType =any>(ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteCollectionOfCareProvider],
					queryFn: async ({ signal }) => await deleteCollectionOfCareProvider({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteCollectionOfCareProvider(opts?: UseMutationOptions<ResponseType<any>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await deleteCollectionOfCareProvider(),
						retry: false,
						...opts
                });
            }
    
			export async function getProcessingStatus(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetProcessingStatusResponse>("POST", LEGACY_TOPIC_GetProcessingStatus, { init })
			}

			export function useQueryGetProcessingStatus<TransformedType =GetProcessingStatusResponse>(ops?: CustomUseQueryOptions<ResponseType<GetProcessingStatusResponse>, TransformedType>) {
                return useQuery<ResponseType<GetProcessingStatusResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetProcessingStatus],
					queryFn: async ({ signal }) => await getProcessingStatus({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetProcessingStatus(opts?: UseMutationOptions<ResponseType<GetProcessingStatusResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getProcessingStatus(),
						retry: false,
						...opts
                });
            }
    
			export async function remove(init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_Remove, { init })
			}

			export function useQueryRemove<TransformedType =any>(ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Remove],
					queryFn: async ({ signal }) => await remove({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRemove(opts?: UseMutationOptions<ResponseType<any>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await remove(),
						retry: false,
						...opts
                });
            }
    
			export async function uploadDocument(request: UploadDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<UploadDocumentResponse>("POST", LEGACY_TOPIC_UploadDocument, { init , request})
			}

			export function useQueryUploadDocument<TransformedType =UploadDocumentResponse>(payload: UploadDocumentRequest,ops?: CustomUseQueryOptions<ResponseType<UploadDocumentResponse>, TransformedType>) {
                return useQuery<ResponseType<UploadDocumentResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UploadDocument, payload],
					queryFn: async ({ signal }) => await uploadDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUploadDocument(opts?: UseMutationOptions<ResponseType<UploadDocumentResponse>, ErrorType,UploadDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await uploadDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function getProcessUploadDocument(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetProcessUploadDocumentResponse>("POST", LEGACY_TOPIC_GetProcessUploadDocument, { init })
			}

			export function useQueryGetProcessUploadDocument<TransformedType =GetProcessUploadDocumentResponse>(ops?: CustomUseQueryOptions<ResponseType<GetProcessUploadDocumentResponse>, TransformedType>) {
                return useQuery<ResponseType<GetProcessUploadDocumentResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetProcessUploadDocument],
					queryFn: async ({ signal }) => await getProcessUploadDocument({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetProcessUploadDocument(opts?: UseMutationOptions<ResponseType<GetProcessUploadDocumentResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getProcessUploadDocument(),
						retry: false,
						...opts
                });
            }
    

