/* eslint-disable */
// This code was autogenerated from app/mvz/medicine.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as timeline from "./app_mvz_timeline"
import * as common from "./common"
import * as common1 from "./form_common"
import * as common2 from "./qes_common"
import * as common3 from "./repo_medicine_common"
import * as bmp from "./service_domains_bmp"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface DrugInformation {
				aTC: string
				aTCA: string
				aTCName: string
				effectiveness: string
				noOfSubstances: number
				components: Array<Component>
		}
	

		export interface Component {
				substances: Array<Substance>
				componentNumber: number
				componentName: string
				physicalForm: number
				absoluteUnit: string
				absoluteAmount: string
				relativeUnit: string
				relativeAmount: string
				relativeForm: string
				ethanolPercentage: string
				releaseBehavior: number
				galenicBasicForm: number
		}
	

		export interface Substance {
				substanceType: number
				rank: number
				unit: string
				amount: number
				equivalentSubstance: number
				suffix: string
				name: string
				gbaUrl: string
				id: number
		}
	

		export interface HintsAndWarning {
				standardIndex: string
				text: string
		}
	

		export interface LegalNote {
				type: number
				text: string
		}
	

		export interface Medicine {
				id: string
				pzn: string
				search: string
				productInformation?: ProductInformation
				hintsAndWarnings?: Array<HintsAndWarning>
				drugInformation?: DrugInformation
				packagingInformation?: PackagingInformation
				priceInformation?: PriceInformation
				textInformation?: TextInformation
				colorCategory?: ColorCategory
				medicationPlanInformation?: MedicationPlanInformation
				packageExtend?: common3.PackageExtend
		}
	

		export interface ColorCategory {
				sorting: number
				drugCategory: string
				isInPriscusList?: boolean
		}
	

		export interface PackageSize {
				packingComponent: number
				classification: number
				nop: string
		}
	

		export interface PackagingInformation {
				quantity: string
				unit: string
				packageSize: PackageSize
				amountText: string
				quantityNumber?: number
				nameRecipe: string
		}
	

		export interface PriceList {
				priceType?: string
				value?: number
		}
	

		export interface PriceInformation {
				pharmacySalePrice: number
				discount: {[key:string]:DiscountDetail}
				copaymentSorting: number
				copayment: number
				totalPayment: number
				totalCoPayment?: number
				additionalPayment: number
				additionalCost?: number
				priceComparisonGroup1: number
				priceComparisonGroup2: number
				isFreeCopayment?: boolean
				pricelist: Array<PriceList>
				fixedAmount?: number
		}
	

		export interface DiscountDetail {
				additionalPaymentIndicator: number
				discountFactor: number
				preferAutIdem: number
		}
	

		export interface Divisible {
				dIVISIBILITYTYPECODE: string
				dIVISIBLE2_FLAG: number
				dIVISIBLE3_FLAG: number
				dIVISIBLE4_FLAG: number
				dIVISIBLE_FLAG: number
		}
	

		export interface BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST {
				iD: string
				dOCUMENT_ID: number
				dATUM_BE_VOM: string
				dATUM_BE_BIS: string
				nAME_PAT_GR: string
		}
	

		export interface GPA {
				aWG: string
				iD: number
				iD_BE_AKZ: string
				qS_ATMP: number
				rEG_NB: string
				sOND_ZUL_ATMP: number
				sOND_ZUL_AUSN: number
				sOND_ZUL_BESOND: number
				sOND_ZUL_ORPHAN: number
				uES_BE: string
				uRL: string
				uRL_QS_ATMP: string
				uRL_QS_ATMP_TEXT: string
				uRL_TEXT: string
				documentIds: Array<number>
				bENIFIT_ASSESSMENT_PATIENT_GROUP_LIST: Array<BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST>
		}
	

		export interface ARMDocument {
				fileName: string
				amrTypeCode: string
		}
	

		export interface ProductInformation {
				shortName: string
				name: string
				provider: string
				dosageForm: string
				tReceipt: number
				negativeList: number
				importReimport: boolean
				documentRequired: number
				lifeStyle: number
				prescribable: number
				isOTC: boolean
				conditionalReimbursement: number
				isExcludedFromAppendixIII: boolean
				isExistingInGBA: boolean
				pharmaciesAvailability: number
				productGroup: number
				copaymentExemption: number
				copaymentExemptionForBloodAndUrineTest: number
				legalNotes: Array<LegalNote>
				formType: FormType
				activeIngredients: Array<string>
				oTXFlag: number
				orderComponents: Array<string>
				transfusionlawFlag: number
				medicineProductFlag: number
				kReceipt: boolean
				amrMessagesCount: number
				importProductFlag: number
				sampleProductFlag: number
				pharmacyRequired: boolean
				gPAs: Array<GPA>
				divisible: Divisible
				techInformationId: number
				id: number
				providerID: number
				dosageFormCode: string
				pharmFormCodeIFA: string
				regulationTypeCodes: Array<string>
				hasAmr3ConstraintFlag: number
				hasAmr3ExclusionFlag: number
				dispensingTypeCode: string
				medicineProductExceptionFlag: number
				praxisbesonderheitDocument?: ARMDocument
				hasAmr1Flag: number
				isDigaFlag: boolean
				isNegativeList: boolean
				isLifeStyle: boolean
				isBandage: boolean
		}
	

		export interface Substances {
				substanceType: string
				rank: string
				unit: string
				amount: number
				equivalentSubstance: string
				suffix: string
				name: string
		}
	

		export interface TextInfomationItem {
				codeName: string
				name: string
				content: string
				order: number
		}
	

		export interface TextInformation {
				updatedDate: string
				items: Array<TextInfomationItem>
		}
	

		export interface Medicines {
				medicines: Array<Medicine>
		}
	

		export interface Sort {
				field: SortField
				order: common.Order
		}
	

		export interface MedicationPlanInformation {
				medicationPlanUnitCode: string
		}
	

		export interface AddToShoppingBagRequest {
				patientId?: string
				doctorId?: string
				contractId?: string
				ikNumber?: number
				medicine: MedicineShoppingBagInfo
				bsnr?: string
				assignedToBsnrId?: string
		}
	

		export interface MedicineShoppingBagInfo {
				id?: string
				type: MedicineType
				pzn?: string
				name: string
				quantity: number
				packagingInformation?: PackagingInformation
				productInformation?: ProductInformation
				patientId?: string
				doctorId?: string
				currentFormType: FormType
				intakeInterval?: IntakeInterval
				formSetting?: string
				autIdem?: boolean
				asNeeded?: boolean
				furtherInformation?: string
				availableSizes?: Array<AvailableSize>
				drugInformation?: DrugInformation
				textInformation?: TextInformation
				priceInformation?: PriceInformation
				colorCategory?: ColorCategory
				substitutionPrescription?: boolean
				specialExceedings?: Array<SpecialExceeding>
				kBVMedicineId?: number
				isEPrescription?: boolean
				medicationPlanInformation?: MedicationPlanInformation
				isArtificialInsemination?: boolean
				bsnr?: string
				vaccinate?: boolean
				drugFormInformation?: string
		}
	

		export interface AvailableSize {
				pzn: string
				size: string
				unit: string
				quantity: string
				packingComponent: number
				classification: number
		}
	

		export interface IntakeInterval {
				morning?: number
				evening?: number
				afternoon?: number
				night?: number
				freetext?: string
				dJ?: boolean
		}
	

		export interface ShoppingBagResponse {
				medicine: MedicineShoppingBagInfo
		}
	

		export interface UpdateShoppingBagQuantityRequest {
				patientId?: string
				doctorId?: string
				medicineId: string
				contractId?: string
				quantity: number
				bsnr?: string
		}
	

		export interface RemoveFromShoppingBagRequest {
				patientId?: string
				doctorId?: string
				medicineId?: string
				contractId?: string
				shoppingBagId?: string
				bsnr?: string
		}
	

		export interface GetShoppingBagRequest {
				patientId?: string
				doctorId?: string
				contractId?: string
				bsnr?: string
				assignedToBsnrId?: string
		}
	

		export interface GetShoppingBagRequestResponse {
				shoppingBagId: string
				patientId: string
				doctorId: string
				treatmentDoctorId: string
				medicines: Array<MedicineShoppingBagInfo>
				bsnr: string
				assignedToBsnrId?: string
		}
	

		export interface GetSubsitutionResponse {
				subsitutions: Array<SubsitutionResponse>
		}
	

		export interface SubsitutionResponse {
				key: string
				total: number
				aTCA: string
				aTCName: string
				medicines: Array<Medicine>
		}
	

		export interface EventShoppingBagRequest {
				type: ShoppingBagEventType
				payload: MedicineShoppingBagInfo
				treatmentDoctorId?: string
				contractId?: string
				medicineDeletedIds?: Array<string>
				assignedToBsnrId?: string
		}
	

		export interface CheckMissingDiagnoseResponse {
				showWarning: boolean
		}
	

		export interface AvailableSizeRequest {
				shortName: string
				provider: string
		}
	

		export interface UpdateFormRequest {
				patientId: string
				doctorId: string
				medicineIDs: Array<string>
				currentFormType: FormType
				contractId?: string
		}
	

		export interface PrescribeRequest {
				patientId?: string
				doctorId?: string
				formInfos: Array<FormInfo>
				contractId?: string
				treatmentDoctorId?: string
				encounterCase?: string
				medicineAutIdemIds: Array<string>
				scheinId?: string
				bsnr?: string
				hasSupportForm907?: boolean
				assignedToBsnrId?: string
				preventGetPatientProfile?: boolean
		}
	

		export interface DateRange {
				startDate: number
				endDate?: number
		}
	

		export interface FormInfo {
				medicineIDs: Array<string>
				formSetting: string
				currentFormType: FormType
				prescribeDate?: number
				isShowFavHint: boolean
				printDate?: number
				ePrescription?: common3.EPrescription
				printOption?: common1.PrintOption
				bundleUrl?: string
				pdfUrl?: string
				hasChangedSprechsundenbedarf?: boolean
		}
	

		export interface FormInfoResponse {
				id: string
				formInfoResponse: Array<MedicineShoppingBagInfo>
				formSetting: string
				currentFormType: FormType
				prescribeDate?: number
				isNotPicked: boolean
				isShowFavHint: boolean
				ePrescription?: common3.EPrescription
				bundleUrl?: string
				hasChangedSprechsundenbedarf?: boolean
				eRezeptStatus?: common2.DocumentStatus
		}
	

		export interface PrintResult {
				currentFormType: FormType
				formUrl: string
		}
	

		export interface PrescribeResponse {
				id: string
				patientId: string
				doctorId: string
				formInfoResponses: Array<FormInfoResponse>
				printDate: number
				contractId: string
				treatmentDoctorId: string
				createdDate: number
				encounterId: string
				printResults: Array<PrintResult>
		}
	

		export interface UpdateShoppingBagInformationRequest {
				patientId?: string
				doctorId?: string
				contractId?: string
				medicineId?: string
				furtherInformation?: string
				intakeInterval?: IntakeInterval
				packageSizeRequest?: PackageSizeRequest
				treatmentDoctorId?: string
				asNeeded?: boolean
				freeText?: string
				substitutionPrescription?: boolean
				specialExceedings?: Array<SpecialExceeding>
				isEPrescription?: boolean
				isArtificialInsemination?: boolean
				bsnr?: string
				vaccinate?: boolean
				drugFormInformation?: string
				assignedToBsnrId?: string
		}
	

		export interface PackageSizeRequest {
				pzn: string
		}
	

		export interface GetMedicationPrescribeRequest {
				patientId?: string
				bsnr?: string
				sortField: SortFieldConsultation
				order: common.Order
		}
	

		export interface MedicationPrescribeResponse {
				id: string
				type: MedicineType
				pzn?: string
				name: string
				packagingInformation?: PackagingInformation
				productInformation?: ProductInformation
				intakeInterval: IntakeInterval
				furtherInformation: string
				drugInformation?: DrugInformation
				prescribeDate: number
				treatmentDoctorId: string
				textInformation?: TextInformation
				medicationPlanId?: string
				asNeeded?: boolean
				contractId?: string
				priceInformation?: PriceInformation
				colorCategory?: ColorCategory
				autIdem: boolean
				quantity: number
				currentFormType: FormType
				formInfoId: string
				substitutionPrescription?: boolean
				specialExceedings?: Array<SpecialExceeding>
				kBVMedicineId?: number
				medicationPlanInformation?: MedicationPlanInformation
				isEPrescription: boolean
				printDate?: number
				fixedAmount?: number
				isFavourite: boolean
				formInfo: FormInfoResponse
				assignedToBsnrId?: string
				isArtificialInsemination: boolean
				vaccinate: boolean
		}
	

		export interface GetMedicationPrescribeResponse {
				patientId: string
				bsnr: string
				medicationPrescribeResponses: Array<MedicationPrescribeResponse>
		}
	

		export interface EventMedicationPrescribe {
				eventType: EventMedicationPrescribeType
				payload: MedicationPrescribeResponse
				patientId: string
				doctorId: string
				contractId?: string
				bsnr?: string
		}
	

		export interface DeleteMedicationPrescribeRequest {
				id: string
				patientId: string
		}
	

		export interface DeleteMedicationPrescribeResponse {
				id: string
				patientId: string
		}
	

		export interface CreateMedicationPlanRequest {
				prescribedMedicationId: string
				hint: string
		}
	

		export interface EventMedicationPlanChanged {
				eventType: EventMedicationPlanChangedType
				payload: bmp.EntryResponse
				encounterCase: string
				contractType?: common.ContractType
				patientId: string
		}
	

		export interface DeleteMedicationPlanRequest {
				medicationPlanId: string
				patientId: string
				doctorId: string
				contractId?: string
				encounterCase: string
		}
	

		export interface DeleteMedicationPlanResponse {
				id: string
				patientId: string
		}
	

		export interface CheckMissingDiagnosesRequest {
				patientId: string
				doctorId: string
				contractId?: string
				ikNumber?: number
				pzns: Array<string>
		}
	

		export interface CheckMissingDiagnosesResponse {
				pzns: Array<string>
		}
	

		export interface EventRefillMedicine {
				patientId: string
				medicineInfos: Array<MedicineShoppingBagInfo>
		}
	

		export interface ViewMedicationForm {
				treatmentDoctorId: string
				formInfo: FormInfoResponse
				assignedToBsnrId: string
		}
	

		export interface EventViewMedicationForm {
				patientId: string
				viewMedicationForm: ViewMedicationForm
				eventType: ViewMedicationFormType
		}
	

		export interface UpdateTreatmentDoctorMedicationFormRequest {
				medicationFormId: string
				treatmentDoctorId: string
				patientId: string
				assignedToBsnrId?: string
		}
	

		export interface PrintFormRequest {
				formId: string
				printOption: common1.PrintOption
				hasSupportForm907?: boolean
				patientId: string
				preventGetPatientProfile?: boolean
		}
	

		export interface PrintFormResponse {
				printResults: Array<PrintResult>
		}
	


// enum definitions
    export enum SortField {
        Size = "Size",
        Price = "Price",
        TotalPayment = "TotalPayment",
    }

    export enum SpecialExceeding {
        A = "A",
        N = "N",
        S = "S",
        SZ = "SZ",
        ST = "ST",
    }

    export enum FormType {
        KREZ = "KREZ",
        GREZ = "GREZ",
        BTM = "BTM",
        TPrescription = "TPrescription",
        Private = "Private",
        AOKNordwet = "AOKNordwet",
        AOKBremen = "AOKBremen",
        Muster16aBay = "Muster16aBay",
    }

    export enum MedicineType {
        FreeText = "FreeText",
        HPM = "HPM",
        KBV = "KBV",
    }

    export enum ShoppingBagEventType {
        Add = "Add",
        Update = "Update",
        Delete = "Delete",
        UpdateForm = "UpdateForm",
        UpdateDoctorId = "UpdateDoctorId",
        DeleteShoppingBag = "DeleteShoppingBag",
    }

    export enum SpecialIdentifier {
        SpecialIdentifierA = "A - Überschreitung der Höchstverschreibungsmenge innerhalb von 30 Tagen",
        SpecialIdentifierN = "N - Notfall Verschreibung",
        SpecialIdentifierS = "S - Substitutionsmittel Verschreibung",
        SpecialIdentifierSZ = "SZ - § 5 Absatz 8 BtMVV",
        SpecialIdentifierST = "ST - § 5 Absatz 9 BtMVV",
    }

    export enum SortFieldConsultation {
        PrescribeDate = "PrescribeDate",
        Tradename = "Tradename",
        SizeMed = "SizeMed",
        PrescribedBy = "PrescribedBy",
        Status = "Status",
    }

    export enum EventMedicationPrescribeType {
        Remove = "Remove",
        PrescribeSuccess = "PrescribeSuccess",
        RemovePrescribeMedicationPlan = "RemovePrescribeMedicationPlan",
        CreateMedicationPlanSuccess = "CreateMedicationPlanSuccess",
    }

    export enum EventMedicationPlanChangedType {
        RemoveMedicationPlan = "RemoveMedicationPlan",
        CreateMedicationPlan = "CreateMedicationPlan",
        UpdateMedicationPlan = "UpdateMedicationPlan",
    }

    export enum ViewMedicationFormType {
        ViewForm = "ViewForm",
        ChangeTreatmentDoctor = "ChangeTreatmentDoctor",
    }


// method name convention const
		export const EVENT_HandleEventShoppingBagChanged = "api.app.mvz.MedicineApp.HandleEventShoppingBagChanged";
		export const EVENT_HandleEventShoppingBagChanged_Response = "api.app.mvz.MedicineApp.HandleEventShoppingBagChanged.Response";
		export const EVENT_HandleEventRefillMedicine = "api.app.mvz.MedicineApp.HandleEventRefillMedicine";
		export const EVENT_HandleEventRefillMedicine_Response = "api.app.mvz.MedicineApp.HandleEventRefillMedicine.Response";
		export const EVENT_GetShoppingBag = "api.app.mvz.MedicineApp.GetShoppingBag";
		export const EVENT_GetShoppingBag_Response = "api.app.mvz.MedicineApp.GetShoppingBag.Response";
		export const EVENT_AddToShoppingBag = "api.app.mvz.MedicineApp.AddToShoppingBag";
		export const EVENT_AddToShoppingBag_Response = "api.app.mvz.MedicineApp.AddToShoppingBag.Response";
		export const EVENT_CheckMissingDiagnose = "api.app.mvz.MedicineApp.CheckMissingDiagnose";
		export const EVENT_CheckMissingDiagnose_Response = "api.app.mvz.MedicineApp.CheckMissingDiagnose.Response";
		export const EVENT_UpdateShoppingBagQuantity = "api.app.mvz.MedicineApp.UpdateShoppingBagQuantity";
		export const EVENT_UpdateShoppingBagQuantity_Response = "api.app.mvz.MedicineApp.UpdateShoppingBagQuantity.Response";
		export const EVENT_UpdateShoppingBagInformation = "api.app.mvz.MedicineApp.UpdateShoppingBagInformation";
		export const EVENT_UpdateShoppingBagInformation_Response = "api.app.mvz.MedicineApp.UpdateShoppingBagInformation.Response";
		export const EVENT_RemoveFromShoppingBag = "api.app.mvz.MedicineApp.RemoveFromShoppingBag";
		export const EVENT_RemoveFromShoppingBag_Response = "api.app.mvz.MedicineApp.RemoveFromShoppingBag.Response";
		export const EVENT_UpdateForm = "api.app.mvz.MedicineApp.UpdateForm";
		export const EVENT_UpdateForm_Response = "api.app.mvz.MedicineApp.UpdateForm.Response";
		export const EVENT_Prescribe = "api.app.mvz.MedicineApp.Prescribe";
		export const EVENT_Prescribe_Response = "api.app.mvz.MedicineApp.Prescribe.Response";
		export const EVENT_GetMedicationPrescribe = "api.app.mvz.MedicineApp.GetMedicationPrescribe";
		export const EVENT_GetMedicationPrescribe_Response = "api.app.mvz.MedicineApp.GetMedicationPrescribe.Response";
		export const EVENT_DeleteMedicationPrescribe = "api.app.mvz.MedicineApp.DeleteMedicationPrescribe";
		export const EVENT_DeleteMedicationPrescribe_Response = "api.app.mvz.MedicineApp.DeleteMedicationPrescribe.Response";
		export const EVENT_CheckMissingDiagnoses = "api.app.mvz.MedicineApp.CheckMissingDiagnoses";
		export const EVENT_CheckMissingDiagnoses_Response = "api.app.mvz.MedicineApp.CheckMissingDiagnoses.Response";
		export const EVENT_HandleEventMedicationPrescribeChanged = "api.app.mvz.MedicineApp.HandleEventMedicationPrescribeChanged";
		export const EVENT_HandleEventMedicationPrescribeChanged_Response = "api.app.mvz.MedicineApp.HandleEventMedicationPrescribeChanged.Response";
		export const EVENT_CreateMedicationPlan = "api.app.mvz.MedicineApp.CreateMedicationPlan";
		export const EVENT_CreateMedicationPlan_Response = "api.app.mvz.MedicineApp.CreateMedicationPlan.Response";
		export const EVENT_HandleEventMedicationPlanChanged = "api.app.mvz.MedicineApp.HandleEventMedicationPlanChanged";
		export const EVENT_HandleEventMedicationPlanChanged_Response = "api.app.mvz.MedicineApp.HandleEventMedicationPlanChanged.Response";
		export const EVENT_DeleteMedicationPlan = "api.app.mvz.MedicineApp.DeleteMedicationPlan";
		export const EVENT_DeleteMedicationPlan_Response = "api.app.mvz.MedicineApp.DeleteMedicationPlan.Response";
		export const EVENT_HandleEventViewMedicationForm = "api.app.mvz.MedicineApp.HandleEventViewMedicationForm";
		export const EVENT_HandleEventViewMedicationForm_Response = "api.app.mvz.MedicineApp.HandleEventViewMedicationForm.Response";
		export const EVENT_UpdateTreatmentDoctorMedicationForm = "api.app.mvz.MedicineApp.UpdateTreatmentDoctorMedicationForm";
		export const EVENT_UpdateTreatmentDoctorMedicationForm_Response = "api.app.mvz.MedicineApp.UpdateTreatmentDoctorMedicationForm.Response";
		export const EVENT_PrintForm = "api.app.mvz.MedicineApp.PrintForm";
		export const EVENT_PrintForm_Response = "api.app.mvz.MedicineApp.PrintForm.Response";
		export const EVENT_OnTimelineHardDelete = "api.app.mvz.MedicineApp.OnTimelineHardDelete";
		export const EVENT_OnTimelineHardDelete_Response = "api.app.mvz.MedicineApp.OnTimelineHardDelete.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_HandleEventShoppingBagChanged = "/api/app/mvz/medicine/handleEventShoppingBagChanged";
        export const LEGACY_TOPIC_HandleEventRefillMedicine = "/api/app/mvz/medicine/handleEventRefillMedicine";
        export const LEGACY_TOPIC_GetShoppingBag = "/api/app/mvz/medicine/getShoppingBag";
        export const LEGACY_TOPIC_AddToShoppingBag = "/api/app/mvz/medicine/addToShoppingBag";
        export const LEGACY_TOPIC_CheckMissingDiagnose = "/api/app/mvz/medicine/checkMissingDiagnose";
        export const LEGACY_TOPIC_UpdateShoppingBagQuantity = "/api/app/mvz/medicine/updateShoppingBagQuantity";
        export const LEGACY_TOPIC_UpdateShoppingBagInformation = "/api/app/mvz/medicine/updateShoppingBagInformation";
        export const LEGACY_TOPIC_RemoveFromShoppingBag = "/api/app/mvz/medicine/removeFromShoppingBag";
        export const LEGACY_TOPIC_UpdateForm = "/api/app/mvz/medicine/updateForm";
        export const LEGACY_TOPIC_Prescribe = "/api/app/mvz/medicine/prescribe";
        export const LEGACY_TOPIC_GetMedicationPrescribe = "/api/app/mvz/medicine/getMedicationPrescribe";
        export const LEGACY_TOPIC_DeleteMedicationPrescribe = "/api/app/mvz/medicine/deleteMedicationPrescribe";
        export const LEGACY_TOPIC_CheckMissingDiagnoses = "/api/app/mvz/medicine/checkMissingDiagnoses";
        export const LEGACY_TOPIC_HandleEventMedicationPrescribeChanged = "/api/app/mvz/medicine/handleEventMedicationPrescribeChanged";
        export const LEGACY_TOPIC_CreateMedicationPlan = "/api/app/mvz/medicine/createMedicationPlan";
        export const LEGACY_TOPIC_HandleEventMedicationPlanChanged = "/api/app/mvz/medicine/handleEventMedicationPlanChanged";
        export const LEGACY_TOPIC_DeleteMedicationPlan = "/api/app/mvz/medicine/deleteMedicationPlan";
        export const LEGACY_TOPIC_HandleEventViewMedicationForm = "/api/app/mvz/medicine/handleEventViewMedicationForm";
        export const LEGACY_TOPIC_UpdateTreatmentDoctorMedicationForm = "/api/app/mvz/medicine/updateTreatmentDoctorMedicationForm";
        export const LEGACY_TOPIC_PrintForm = "/api/app/mvz/medicine/printForm";
        export const LEGACY_TOPIC_OnTimelineHardDelete = "/api/app/mvz/medicine/onTimelineHardDelete";


// Define action methods and their listener -----------------------------------------------------------------
    
    
			export async function getShoppingBag(request: GetShoppingBagRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetShoppingBagRequestResponse>("POST", LEGACY_TOPIC_GetShoppingBag, { init , request})
			}

			export function useQueryGetShoppingBag<TransformedType =GetShoppingBagRequestResponse>(payload: GetShoppingBagRequest,ops?: CustomUseQueryOptions<ResponseType<GetShoppingBagRequestResponse>, TransformedType>) {
                return useQuery<ResponseType<GetShoppingBagRequestResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetShoppingBag, payload],
					queryFn: async ({ signal }) => await getShoppingBag(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetShoppingBag(opts?: UseMutationOptions<ResponseType<GetShoppingBagRequestResponse>, ErrorType,GetShoppingBagRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getShoppingBag(request),
						retry: false,
						...opts
                });
            }
    
			export async function addToShoppingBag(request: AddToShoppingBagRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ShoppingBagResponse>("POST", LEGACY_TOPIC_AddToShoppingBag, { init , request})
			}

			export function useQueryAddToShoppingBag<TransformedType =ShoppingBagResponse>(payload: AddToShoppingBagRequest,ops?: CustomUseQueryOptions<ResponseType<ShoppingBagResponse>, TransformedType>) {
                return useQuery<ResponseType<ShoppingBagResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_AddToShoppingBag, payload],
					queryFn: async ({ signal }) => await addToShoppingBag(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationAddToShoppingBag(opts?: UseMutationOptions<ResponseType<ShoppingBagResponse>, ErrorType,AddToShoppingBagRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await addToShoppingBag(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkMissingDiagnose(request: AddToShoppingBagRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckMissingDiagnoseResponse>("POST", LEGACY_TOPIC_CheckMissingDiagnose, { init , request})
			}

			export function useQueryCheckMissingDiagnose<TransformedType =CheckMissingDiagnoseResponse>(payload: AddToShoppingBagRequest,ops?: CustomUseQueryOptions<ResponseType<CheckMissingDiagnoseResponse>, TransformedType>) {
                return useQuery<ResponseType<CheckMissingDiagnoseResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckMissingDiagnose, payload],
					queryFn: async ({ signal }) => await checkMissingDiagnose(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckMissingDiagnose(opts?: UseMutationOptions<ResponseType<CheckMissingDiagnoseResponse>, ErrorType,AddToShoppingBagRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkMissingDiagnose(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateShoppingBagQuantity(request: UpdateShoppingBagQuantityRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ShoppingBagResponse>("POST", LEGACY_TOPIC_UpdateShoppingBagQuantity, { init , request})
			}

			export function useQueryUpdateShoppingBagQuantity<TransformedType =ShoppingBagResponse>(payload: UpdateShoppingBagQuantityRequest,ops?: CustomUseQueryOptions<ResponseType<ShoppingBagResponse>, TransformedType>) {
                return useQuery<ResponseType<ShoppingBagResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateShoppingBagQuantity, payload],
					queryFn: async ({ signal }) => await updateShoppingBagQuantity(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateShoppingBagQuantity(opts?: UseMutationOptions<ResponseType<ShoppingBagResponse>, ErrorType,UpdateShoppingBagQuantityRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateShoppingBagQuantity(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateShoppingBagInformation(request: UpdateShoppingBagInformationRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ShoppingBagResponse>("POST", LEGACY_TOPIC_UpdateShoppingBagInformation, { init , request})
			}

			export function useQueryUpdateShoppingBagInformation<TransformedType =ShoppingBagResponse>(payload: UpdateShoppingBagInformationRequest,ops?: CustomUseQueryOptions<ResponseType<ShoppingBagResponse>, TransformedType>) {
                return useQuery<ResponseType<ShoppingBagResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateShoppingBagInformation, payload],
					queryFn: async ({ signal }) => await updateShoppingBagInformation(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateShoppingBagInformation(opts?: UseMutationOptions<ResponseType<ShoppingBagResponse>, ErrorType,UpdateShoppingBagInformationRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateShoppingBagInformation(request),
						retry: false,
						...opts
                });
            }
    
			export async function removeFromShoppingBag(request: RemoveFromShoppingBagRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ShoppingBagResponse>("POST", LEGACY_TOPIC_RemoveFromShoppingBag, { init , request})
			}

			export function useQueryRemoveFromShoppingBag<TransformedType =ShoppingBagResponse>(payload: RemoveFromShoppingBagRequest,ops?: CustomUseQueryOptions<ResponseType<ShoppingBagResponse>, TransformedType>) {
                return useQuery<ResponseType<ShoppingBagResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_RemoveFromShoppingBag, payload],
					queryFn: async ({ signal }) => await removeFromShoppingBag(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRemoveFromShoppingBag(opts?: UseMutationOptions<ResponseType<ShoppingBagResponse>, ErrorType,RemoveFromShoppingBagRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await removeFromShoppingBag(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateForm(request: UpdateFormRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UpdateForm, { init , request})
			}

			export function useQueryUpdateForm<TransformedType =any>(payload: UpdateFormRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateForm, payload],
					queryFn: async ({ signal }) => await updateForm(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateForm(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UpdateFormRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateForm(request),
						retry: false,
						...opts
                });
            }
    
			export async function prescribe(request: PrescribeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PrescribeResponse>("POST", LEGACY_TOPIC_Prescribe, { init , request})
			}

			export function useQueryPrescribe<TransformedType =PrescribeResponse>(payload: PrescribeRequest,ops?: CustomUseQueryOptions<ResponseType<PrescribeResponse>, TransformedType>) {
                return useQuery<ResponseType<PrescribeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Prescribe, payload],
					queryFn: async ({ signal }) => await prescribe(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationPrescribe(opts?: UseMutationOptions<ResponseType<PrescribeResponse>, ErrorType,PrescribeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await prescribe(request),
						retry: false,
						...opts
                });
            }
    
			export async function getMedicationPrescribe(request: GetMedicationPrescribeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetMedicationPrescribeResponse>("POST", LEGACY_TOPIC_GetMedicationPrescribe, { init , request})
			}

			export function useQueryGetMedicationPrescribe<TransformedType =GetMedicationPrescribeResponse>(payload: GetMedicationPrescribeRequest,ops?: CustomUseQueryOptions<ResponseType<GetMedicationPrescribeResponse>, TransformedType>) {
                return useQuery<ResponseType<GetMedicationPrescribeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetMedicationPrescribe, payload],
					queryFn: async ({ signal }) => await getMedicationPrescribe(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetMedicationPrescribe(opts?: UseMutationOptions<ResponseType<GetMedicationPrescribeResponse>, ErrorType,GetMedicationPrescribeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getMedicationPrescribe(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteMedicationPrescribe(request: DeleteMedicationPrescribeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<DeleteMedicationPrescribeResponse>("POST", LEGACY_TOPIC_DeleteMedicationPrescribe, { init , request})
			}

			export function useQueryDeleteMedicationPrescribe<TransformedType =DeleteMedicationPrescribeResponse>(payload: DeleteMedicationPrescribeRequest,ops?: CustomUseQueryOptions<ResponseType<DeleteMedicationPrescribeResponse>, TransformedType>) {
                return useQuery<ResponseType<DeleteMedicationPrescribeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteMedicationPrescribe, payload],
					queryFn: async ({ signal }) => await deleteMedicationPrescribe(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteMedicationPrescribe(opts?: UseMutationOptions<ResponseType<DeleteMedicationPrescribeResponse>, ErrorType,DeleteMedicationPrescribeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteMedicationPrescribe(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkMissingDiagnoses(request: CheckMissingDiagnosesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckMissingDiagnosesResponse>("POST", LEGACY_TOPIC_CheckMissingDiagnoses, { init , request})
			}

			export function useQueryCheckMissingDiagnoses<TransformedType =CheckMissingDiagnosesResponse>(payload: CheckMissingDiagnosesRequest,ops?: CustomUseQueryOptions<ResponseType<CheckMissingDiagnosesResponse>, TransformedType>) {
                return useQuery<ResponseType<CheckMissingDiagnosesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckMissingDiagnoses, payload],
					queryFn: async ({ signal }) => await checkMissingDiagnoses(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckMissingDiagnoses(opts?: UseMutationOptions<ResponseType<CheckMissingDiagnosesResponse>, ErrorType,CheckMissingDiagnosesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkMissingDiagnoses(request),
						retry: false,
						...opts
                });
            }
    
    
			export async function createMedicationPlan(request: CreateMedicationPlanRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_CreateMedicationPlan, { init , request})
			}

			export function useQueryCreateMedicationPlan<TransformedType =any>(payload: CreateMedicationPlanRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateMedicationPlan, payload],
					queryFn: async ({ signal }) => await createMedicationPlan(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateMedicationPlan(opts?: UseMutationOptions<ResponseType<any>, ErrorType,CreateMedicationPlanRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createMedicationPlan(request),
						retry: false,
						...opts
                });
            }
    
    
			export async function deleteMedicationPlan(request: DeleteMedicationPlanRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<DeleteMedicationPlanResponse>("POST", LEGACY_TOPIC_DeleteMedicationPlan, { init , request})
			}

			export function useQueryDeleteMedicationPlan<TransformedType =DeleteMedicationPlanResponse>(payload: DeleteMedicationPlanRequest,ops?: CustomUseQueryOptions<ResponseType<DeleteMedicationPlanResponse>, TransformedType>) {
                return useQuery<ResponseType<DeleteMedicationPlanResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteMedicationPlan, payload],
					queryFn: async ({ signal }) => await deleteMedicationPlan(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteMedicationPlan(opts?: UseMutationOptions<ResponseType<DeleteMedicationPlanResponse>, ErrorType,DeleteMedicationPlanRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteMedicationPlan(request),
						retry: false,
						...opts
                });
            }
    
    
			export async function updateTreatmentDoctorMedicationForm(request: UpdateTreatmentDoctorMedicationFormRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UpdateTreatmentDoctorMedicationForm, { init , request})
			}

			export function useQueryUpdateTreatmentDoctorMedicationForm<TransformedType =any>(payload: UpdateTreatmentDoctorMedicationFormRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateTreatmentDoctorMedicationForm, payload],
					queryFn: async ({ signal }) => await updateTreatmentDoctorMedicationForm(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateTreatmentDoctorMedicationForm(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UpdateTreatmentDoctorMedicationFormRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateTreatmentDoctorMedicationForm(request),
						retry: false,
						...opts
                });
            }
    
			export async function printForm(request: PrintFormRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PrintFormResponse>("POST", LEGACY_TOPIC_PrintForm, { init , request})
			}

			export function useQueryPrintForm<TransformedType =PrintFormResponse>(payload: PrintFormRequest,ops?: CustomUseQueryOptions<ResponseType<PrintFormResponse>, TransformedType>) {
                return useQuery<ResponseType<PrintFormResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_PrintForm, payload],
					queryFn: async ({ signal }) => await printForm(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationPrintForm(opts?: UseMutationOptions<ResponseType<PrintFormResponse>, ErrorType,PrintFormRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await printForm(request),
						retry: false,
						...opts
                });
            }
    
    

