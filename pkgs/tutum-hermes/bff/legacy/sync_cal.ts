/* eslint-disable */
// This code was autogenerated from app/sync_cal/sync_cal.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface SyncCalRequest {
				syncCalLogId: string
		}
	

		export interface GetStatusSyncCalResponse {
				status: string
				syncPatientDate?: Date
				startAt?: Date
				endAt?: Date
				createdAt?: Date
				updatedAt?: Date
				errorLog?: string
		}
	


// enum definitions

// method name convention const
		export const EVENT_SyncCal = "api.app.sync_cal.SyncCalApp.SyncCal";
		export const EVENT_SyncCal_Response = "api.app.sync_cal.SyncCalApp.SyncCal.Response";
		export const EVENT_GetStatusSyncCal = "api.app.sync_cal.SyncCalApp.GetStatusSyncCal";
		export const EVENT_GetStatusSyncCal_Response = "api.app.sync_cal.SyncCalApp.GetStatusSyncCal.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_SyncCal = "/api/app/sync_cal/sync/cal/syncCal";
        export const LEGACY_TOPIC_GetStatusSyncCal = "/api/app/sync_cal/sync/cal/getStatusSyncCal";


// Define action methods and their listener -----------------------------------------------------------------
			export async function syncCal(request: SyncCalRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetStatusSyncCalResponse>("POST", LEGACY_TOPIC_SyncCal, { init , request})
			}

			export function useQuerySyncCal<TransformedType =GetStatusSyncCalResponse>(payload: SyncCalRequest,ops?: CustomUseQueryOptions<ResponseType<GetStatusSyncCalResponse>, TransformedType>) {
                return useQuery<ResponseType<GetStatusSyncCalResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SyncCal, payload],
					queryFn: async ({ signal }) => await syncCal(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSyncCal(opts?: UseMutationOptions<ResponseType<GetStatusSyncCalResponse>, ErrorType,SyncCalRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await syncCal(request),
						retry: false,
						...opts
                });
            }
    
			export async function getStatusSyncCal(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetStatusSyncCalResponse>("POST", LEGACY_TOPIC_GetStatusSyncCal, { init })
			}

			export function useQueryGetStatusSyncCal<TransformedType =GetStatusSyncCalResponse>(ops?: CustomUseQueryOptions<ResponseType<GetStatusSyncCalResponse>, TransformedType>) {
                return useQuery<ResponseType<GetStatusSyncCalResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetStatusSyncCal],
					queryFn: async ({ signal }) => await getStatusSyncCal({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetStatusSyncCal(opts?: UseMutationOptions<ResponseType<GetStatusSyncCalResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getStatusSyncCal(),
						retry: false,
						...opts
                });
            }
    

