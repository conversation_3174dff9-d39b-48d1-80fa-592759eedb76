/* eslint-disable */
// This code was autogenerated from service/domains/patient_profile_common.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as catalog_sdkt_common from "./catalog_sdkt_common"
import * as catalog_utils_common from "./catalog_utils_common"
import * as common from "./common"
import * as error_code from "./error_code"
import * as insurance from "./insurance_common"



// Type definitions
		export interface AddressInsurance {
				street?: string
				number?: string
				postCode?: string
				city?: string
				country?: string
		}
	

		export interface InsuranceInfo {
				id: string
				insuranceCompanyId: string
				insuranceCompanyName: string
				locationNames: Array<string>
				validity: catalog_utils_common.Validity
				ikNumber: number
				insuranceNumber?: string
				insuranceStatus: InsuranceStatus
				startDate?: number
				endDate?: number
				specialGroup: SpecialGroupDescription
				dMPLabeling: string
				haveCoPaymentExemptionTill: boolean
				havePatientReceipt: boolean
				haveHeimiLongTermApproval: boolean
				copaymentExemptionTillDate?: number
				wop: string
				lHMs?: Array<LHM>
				feeSchedule: number
				acquiredInsuranceInfo?: InsuranceInfo
				feeCatalogue: catalog_sdkt_common.FeeCatalogue
				insuranceType: TypeOfInsurance
				validUntil?: MMYYYY
				readCardDatas?: Array<ReadCardModel>
				isActive: boolean
				address?: AddressInsurance
				tel?: string
				fax?: string
				isTerminated?: boolean
		}
	

		export interface VSDStatus {
				status: string
				timestamp: number
				version: string
		}
	

		export interface ReadCardModel {
				fromCardType: FromCardType
				proofOfInsurance?: ProofOfInsurance
				registrationNumber?: string
				readCardDate: number
				cdmVersion: string
				vSDStatus?: VSDStatus
		}
	

		export interface ProofOfInsurance {
				onlineCheckDate: number
				resultCode: number
				checkDigit?: string
				errorCode?: number
		}
	

		export interface InsuranceRequestCreate {
				insuranceCompanyId: string
				insuranceCompanyName: string
				locationNames: Array<string>
				validity: catalog_utils_common.Validity
				ikNumber: number
				insuranceNumber?: string
				insuranceStatus: InsuranceStatus
				startDate?: number
				endDate?: number
				specialGroup: SpecialGroupDescription
				dMPLabeling: string
				haveCoPaymentExemptionTill: boolean
				havePatientReceipt: boolean
				haveHeimiLongTermApproval: boolean
				copaymentExemptionTillDate?: number
				wop: string
				lHMs?: Array<LHM>
				isActive: boolean
				feeSchedule: number
				fromCardType?: insurance.FromCardType
				acquiredInsuranceInfo?: insurance.InsuranceInfo
				feeCatalogue: catalog_sdkt_common.FeeCatalogue
				insuranceType: TypeOfInsurance
				validUntil?: MMYYYY
		}
	

		export interface MMYYYY {
				year?: number
				month?: number
		}
	

		export interface GenericInfo {
				patientType: PatientType
				lastManualEntryDate?: number
				lastCardReadinDate?: number
		}
	

		export interface DateOfBirth {
				year?: number
				month?: number
				date?: number
				isValidDOB: boolean
		}
	

		export interface PersonalInfo {
				firstName: string
				lastName: string
				title?: string
				additionalNames?: Array<AdditionalName>
				intendWord?: IntendWord
				gender: Gender
				dOB: number
				dateOfBirth: DateOfBirth
				dateOfDeath?: number
				birthName?: string
				status?: string
		}
	

		export interface Address {
				street?: string
				number?: string
				postCode: string
				city?: string
				countryCode?: string
				additionalAddressInfo?: string
				distance?: number
		}
	

		export interface PostalAddress {
				postCode: string
				officeBox: string
				cityBox: string
				countryCode: string
		}
	

		export interface BillingAddress {
				street?: string
				number?: string
				postCode: string
				city?: string
				countryCode?: string
				firstName: string
				lastName: string
				title?: string
				additionalNames?: Array<AdditionalName>
				intendWord?: IntendWord
		}
	

		export interface CompanyAddress {
				street?: string
				number?: string
				postCode?: string
				city?: string
				countryCode?: string
				employer?: string
		}
	

		export interface AddressInfo {
				address: Address
				billingAddress: BillingAddress
				postalAddress?: PostalAddress
		}
	

		export interface ContactPerson {
				name?: string
				phoneNumber?: string
				email?: string
				relationship?: string
		}
	

		export interface ContactInfo {
				primaryContactNumber?: string
				furtherContactNumber?: Array<string>
				emailAddress?: string
				haveDeclarationOfAgreementToContact: boolean
				contactPersons?: Array<ContactPerson>
				contactAgreementFile?: FileInfo
				address?: string
		}
	

		export interface DoctorInfo {
				generalPractitionerDoctorId: Array<string>
				specialistDoctorId: Array<string>
				treatmentDoctorId?: string
		}
	

		export interface FileInfo {
				fileName: string
				fileUrl: string
		}
	

		export interface OtherInfo {
				cave: string
				patientSinceDate?: number
				lastTreatmentDate?: number
				haveMedicalHistoryFormCompleted: boolean
				isLivingWillAvailable: boolean
				isAgreeWithBillingViaPVS: boolean
				isPrivacyPolicySigned: boolean
				medicalHistoryFileUrl?: FileInfo
				livingWillFileUrl?: FileInfo
				billingFileUrl?: FileInfo
				privacyPolicyFileUrl?: FileInfo
		}
	

		export interface VisitInfo {
				treatmentDoctorId?: string
				additionalVisitInfo?: string
		}
	

		export interface PostOfficeBox {
				postCode: string
				placeOfResidence: string
				officeBox: string
				countryCode: string
		}
	

		export interface EuropeanHealthInsurance {
				hasEuropeanHealthInsuranceCard: boolean
				formSetting?: string
				language?: string
				status?: common.EuropeanHealthInsuranceStatus
				treatmentDoctorId: string
		}
	

		export interface PatientInfo {
				patientNumber: number
				genericInfo: GenericInfo
				personalInfo: PersonalInfo
				addressInfo: AddressInfo
				contactInfo: ContactInfo
				insuranceInfos: Array<InsuranceInfo>
				doctorInfo: DoctorInfo
				otherInfo: OtherInfo
				visitInfo: VisitInfo
				employmentInfo: EmploymentInfo
				postOfficeBox: PostOfficeBox
				europeanHealthInsurance: EuropeanHealthInsurance
				patientId?: string
				cardRawId?: string
		}
	

		export interface EmploymentInfo {
				jobStatus?: JobStatus
				occupation: string
				specialProblemAtWork: string
				workingHourInWeek: number
				workActivity1: WorkActivity1
				workActivity2: WorkActivity2
				companyAddress: CompanyAddress
				isEmployed: boolean
		}
	

		export interface Allergy {
				allergy: string
				isPrescriptionRelated: boolean
		}
	

		export interface PatientMedicalData {
				weight?: number
				height?: number
				bloodPressure?: string
				heartFrequency?: number
				allergies?: Array<Allergy>
				creatinine?: number
				amountOfChildren?: number
				isPregnant?: boolean
				dateOfPlannedBirth?: number
				amountOfBirth?: number
				amountOfPregnancies?: number
				isBreastfeeding?: boolean
				careLevel?: number
				additionalNote?: string
				isSmoker?: boolean
				cigarettesPerDay?: number
				allergiesFor4205?: Array<Allergy>
				bodyTemperature?: number
				pulseOxiMetric?: number
				pulse?: Pulse
		}
	

		export interface LHM {
				id?: string
				firstICDCode: string
				secondICDCode?: string
				diagnosisGroupCode: string
				isStandardCombination: boolean
				primaryRemediesPosition?: Array<string>
				complementaryRemediesPosition?: Array<string>
				validUntilDate?: number
				note?: string
		}
	

		export interface PatientPrepared {
				patientInfo: PatientInfo
				compareStatus: CompareStatus
				validationError?: error_code.ErrorCode
				isCreateShown: boolean
		}
	

		export interface PatientDeleteRequest {
				patientId: string
		}
	

		export interface EventPatientProfileChange {
				eventName: EventName
				patientId: string
				patientMedicalData?: PatientMedicalData
				patientInfo?: PatientInfo
				medicalDataUpdatedAt?: number
				employmentInfoUpdatedAt?: number
				shouldCreateNewEHIC: boolean
				cardInsurance?: InsuranceInfo
				readingDate: number
				shouldRunTimelineValidation: boolean
		}
	

		export interface GetInsurancesByPatientIdRequest {
				patientId: string
		}
	

		export interface GetInsurancesByPatientIdResponse {
				insuranceInfos: Array<InsuranceInfo>
		}
	


// enum definitions
    export enum TypeOfInsurance {
        Public = "Public",
        Private = "Private",
        BG = "BG",
    }

    export enum Gender {
        M = "M",
        W = "W",
        X = "X",
        D = "D",
        U = "U",
    }

    export enum Salutation {
        Salutation_Herr = "Herr",
        Salutation_Frau = "Frau",
        Salutation_Keine = "Keine",
    }

    export enum CreatePatientProfileErrorCode {
        DuplicatedInsuranceNumber = "DuplicatedInsuranceNumber",
        MultipleActiveCostUnits = "MultipleActiveCostUnits",
        EndDateOutOfDate = "EndDateOutOfDate",
        LHMUnique = "LHMUnique",
        CostUnitIsNotAvailableInKvRegion = "CostUnitIsNotAvailableInKvRegion",
        CostUnitHasExpired = "CostUnitHasExpired",
        DuplicateLHMItem = "DuplicateLHMItem",
        IKNumberHasExpired = "IKNumberHasExpired",
        CanNotChangeInsuranceType = "CanNotChangeInsuranceType",
        NotFoundInsurance = "NotFoundInsurance",
        InvalidInsuranceDate = "InvalidInsuranceDate",
        NotFoundActiveInsurance = "NotFoundActiveInsurance",
    }

    export enum SpecialGroupDescription {
        SpecialGroup_00 = "00",
        SpecialGroup_04 = "04",
        SpecialGroup_06 = "06",
        SpecialGroup_07 = "07",
        SpecialGroup_08 = "08",
        SpecialGroup_09 = "09",
    }

    export enum AdditionalName {
        AdditionalName_Bar = "Bar",
        AdditionalName_Baron = "Baron",
        AdditionalName_Baroness = "Baroness",
        AdditionalName_Baronesse = "Baronesse",
        AdditionalName_Baronin = "Baronin",
        AdditionalName_Brand = "Brand",
        AdditionalName_Burggraf = "Burggraf",
        AdditionalName_Burggrafin = "Burggräfin",
        AdditionalName_Condesa = "Condesa",
        AdditionalName_Earl = "Earl",
        AdditionalName_Edle = "Edle",
        AdditionalName_Edler = "Edler",
        AdditionalName_Erbgraf = "Erbgraf",
        AdditionalName_Erbgrafin = "Erbgräfin",
        AdditionalName_Erbprinz = "Erbprinz",
        AdditionalName_Erbprinzessin = "Erbprinzessin",
        AdditionalName_Ffr = "Ffr",
        AdditionalName_Freifr = "Freifr",
        AdditionalName_Freifraulein = "Freifräulein",
        AdditionalName_Freifrau = "Freifrau",
        AdditionalName_Freih = "Freih",
        AdditionalName_Freiherr = "Freiherr",
        AdditionalName_Freiin = "Freiin",
        AdditionalName_Frf = "Frf",
        AdditionalName_FrfDot = "Frf.",
        AdditionalName_Frfr = "Frfr",
        AdditionalName_FrfrDot = "Frfr.",
        AdditionalName_Frh = "Frh",
        AdditionalName_FrhDot = "Frh.",
        AdditionalName_Frhr = "Frhr",
        AdditionalName_FrhrDot = "Frhr.",
        AdditionalName_Fst = "Fst",
        AdditionalName_FstDot = "Fst.",
        AdditionalName_Fstn = "Fstn",
        AdditionalName_FstnDot = "Fstn.",
        AdditionalName_Furst = "Fürst",
        AdditionalName_Furstin = "Fürstin",
        AdditionalName_Gr = "Gr",
        AdditionalName_Graf = "Graf",
        AdditionalName_Grafin = "Gräfin",
        AdditionalName_Grf = "Grf",
        AdditionalName_Grfn = "Grfn",
        AdditionalName_Grossherzog = "Grossherzog",
        AdditionalName_GroBherzog = "Großherzog",
        AdditionalName_Grossherzogin = "Grossherzogin",
        AdditionalName_GroBherzogin = "Großherzogin",
        AdditionalName_Herzog = "Herzog",
        AdditionalName_Herzogin = "Herzogin",
        AdditionalName_Jhr = "Jhr",
        AdditionalName_JhrDot = "Jhr.",
        AdditionalName_Jonkheer = "Jonkheer",
        AdditionalName_Junker = "Junker",
        AdditionalName_Landgraf = "Landgraf",
        AdditionalName_Landgrafin = "Landgräfin",
        AdditionalName_Markgraf = "Markgraf",
        AdditionalName_Markgrafin = "Markgräfin",
        AdditionalName_Marques = "Marques",
        AdditionalName_Marquis = "Marquis",
        AdditionalName_Marschall = "Marschall",
        AdditionalName_Ostoja = "Ostoja",
        AdditionalName_Prinz = "Prinz",
        AdditionalName_Prinzessin = "Prinzessin",
        AdditionalName_Przin = "Przin",
        AdditionalName_Rabe = "Rabe",
        AdditionalName_Reichsgraf = "Reichsgraf",
        AdditionalName_Reichsgrafin = "Reichsgräfin",
        AdditionalName_Ritter = "Ritter",
        AdditionalName_Rr = "Rr",
        AdditionalName_Truchsess = "Truchsess",
        AdditionalName_TruchseB = "Truchseß",
    }

    export enum IntendWord {
        IntendWord_A = "a",
        IntendWord_Aande = "aan de",
        IntendWord_Aanden = "aan den",
        IntendWord_Al = "al",
        IntendWord_Am = "am",
        IntendWord_An = "an",
        IntendWord_Ander = "an der",
        IntendWord_Auf = "auf",
        IntendWord_Aufdem = "auf dem",
        IntendWord_Aufder = "auf der",
        IntendWord_Aufmspace = "auf m",
        IntendWord_Aufm = "aufm",
        IntendWord_Auffm = "auff m",
        IntendWord_Aus = "aus",
        IntendWord_Ausdem = "aus dem",
        IntendWord_Ausden = "aus den",
        IntendWord_Ausder = "aus der",
        IntendWord_B = "b",
        IntendWord_Be = "be",
        IntendWord_Bei = "bei",
        IntendWord_Beider = "bei der",
        IntendWord_Beim = "beim",
        IntendWord_Ben = "ben",
        IntendWord_Bey = "bey",
        IntendWord_Beyder = "bey der",
        IntendWord_Che = "che",
        IntendWord_Cid = "cid",
        IntendWord_D = "d",
        IntendWord_DDot = "d.",
        IntendWord_DQuote = "d'",
        IntendWord_Da = "da",
        IntendWord_Dacosta = "da costa",
        IntendWord_Dalas = "da las",
        IntendWord_Dasilva = "da silva",
        IntendWord_Dal = "dal",
        IntendWord_Dall = "dall",
        IntendWord_Dallquote = "dall'",
        IntendWord_Dalla = "dalla",
        IntendWord_Dalle = "dalle",
        IntendWord_Dallo = "dallo",
        IntendWord_Das = "das",
        IntendWord_De = "de",
        IntendWord_Degli = "degli",
        IntendWord_Dei = "dei",
        IntendWord_Den = "den",
        IntendWord_Delquote = "de l '",
        IntendWord_Dela = "de la",
        IntendWord_Delas = "de las",
        IntendWord_Dele = "de le",
        IntendWord_Delos = "de los",
        IntendWord_Del = "del",
        IntendWord_Delcoz = "del coz",
        IntendWord_Deli = "deli",
        IntendWord_Dell = "dell",
        IntendWord_Dellquote = "dell'",
        IntendWord_Della = "della",
        IntendWord_Delle = "delle",
        IntendWord_Delli = "delli",
        IntendWord_Dello = "dello",
        IntendWord_Der = "der",
        IntendWord_Des = "des",
        IntendWord_Di = "di",
        IntendWord_Dit = "dit",
        IntendWord_Do = "do",
        IntendWord_Doceu = "do ceu",
        IntendWord_Don = "don",
        IntendWord_Donle = "don le",
        IntendWord_Dos = "dos",
        IntendWord_Dossantos = "dos santos",
        IntendWord_Du = "du",
        IntendWord_Dy = "dy",
        IntendWord_El = "el",
        IntendWord_G = "g",
        IntendWord_Gen = "gen",
        IntendWord_Gil = "gil",
        IntendWord_Gli = "gli",
        IntendWord_Grosse = "grosse",
        IntendWord_GroBe = "große",
        IntendWord_I = "i",
        IntendWord_Im = "im",
        IntendWord_In = "in",
        IntendWord_Inde = "in de",
        IntendWord_Inden = "in den",
        IntendWord_Inder = "in der",
        IntendWord_Inhet = "in het",
        IntendWord_Intquote = "in't",
        IntendWord_Kl = "kl",
        IntendWord_Kleine = "kleine",
        IntendWord_L = "l",
        IntendWord_Ldot = "l.",
        IntendWord_Lquote = "l'",
        IntendWord_La = "la",
        IntendWord_Le = "le",
        IntendWord_Lee = "lee",
        IntendWord_Li = "li",
        IntendWord_Lo = "lo",
        IntendWord_M = "m",
        IntendWord_Mc = "mc",
        IntendWord_Mac = "mac",
        IntendWord_N = "n",
        IntendWord_O = "o",
        IntendWord_Oquote = "o'",
        IntendWord_Op = "op",
        IntendWord_Opde = "op de",
        IntendWord_Opden = "op den",
        IntendWord_Opgen = "op gen",
        IntendWord_Ophet = "op het",
        IntendWord_Opte = "op te",
        IntendWord_Opten = "op ten",
        IntendWord_Oude = "oude",
        IntendWord_Pla = "pla",
        IntendWord_Pro = "pro",
        IntendWord_S = "s",
        IntendWord_Stdot = "st.",
        IntendWord_T = "t",
        IntendWord_Te = "te",
        IntendWord_Ten = "ten",
        IntendWord_Ter = "ter",
        IntendWord_Thi = "thi",
        IntendWord_Tho = "tho",
        IntendWord_Thom = "thom",
        IntendWord_Thor = "thor",
        IntendWord_Thum = "thum",
        IntendWord_To = "to",
        IntendWord_Tom = "tom",
        IntendWord_Tor = "tor",
        IntendWord_Tu = "tu",
        IntendWord_Tum = "tum",
        IntendWord_Unten = "unten",
        IntendWord_Unter = "unter",
        IntendWord_Unterm = "unterm",
        IntendWord_Vdot = "v.",
        IntendWord_Vddot = "v. d.",
        IntendWord_Vdem = "v. dem",
        IntendWord_Vden = "v. den",
        IntendWord_Vder = "v. der",
        IntendWord_Vd = "v.d.",
        IntendWord_Vdemdot = "v.dem",
        IntendWord_Vdendot = "v.den",
        IntendWord_Vderdot = "v.der",
        IntendWord_Van = "van",
        IntendWord_Vandespace = "van de",
        IntendWord_Vandemspace = "van dem",
        IntendWord_Vandenspace = "van den",
        IntendWord_Vanderspace = "van der",
        IntendWord_Vande = "vande",
        IntendWord_Vandem = "vandem",
        IntendWord_Vanden = "vanden",
        IntendWord_Vander = "vander",
        IntendWord_Vangen = "van gen",
        IntendWord_Vanhet = "van het",
        IntendWord_Vant = "van t",
        IntendWord_Ven = "ven",
        IntendWord_Vender = "ven der",
        IntendWord_Ver = "ver",
        IntendWord_Vo = "vo",
        IntendWord_Vom = "vom",
        IntendWord_Vomundzu = "vom und zu",
        IntendWord_Von = "von",
        IntendWord_Vonundzu = "von und zu",
        IntendWord_Vonundzuder = "von und zu der",
        IntendWord_Vonundzur = "von und zur",
        IntendWord_Vondespace = "von de",
        IntendWord_Vondemspace = "von dem",
        IntendWord_Vondenspace = "von den",
        IntendWord_Vonderspace = "von der",
        IntendWord_Vonla = "von la",
        IntendWord_Vonzu = "von zu",
        IntendWord_Vonzum = "von zum",
        IntendWord_Vonzur = "von zur",
        IntendWord_Vonde = "vonde",
        IntendWord_Vonden = "vonden",
        IntendWord_Vondem = "vondem",
        IntendWord_Vonder = "vonder",
        IntendWord_Voneinem = "von einem",
        IntendWord_Vonmast = "von mast",
        IntendWord_Vor = "vor",
        IntendWord_Vordem = "vor dem",
        IntendWord_Vorden = "vor den",
        IntendWord_Vorder = "vor der",
        IntendWord_Vorm = "vorm",
        IntendWord_Vorn = "vorn",
        IntendWord_Y = "y",
        IntendWord_Ydel = "y del",
        IntendWord_Zu = "zu",
        IntendWord_Zum = "zum",
        IntendWord_Zur = "zur",
    }

    export enum InsuranceStatus {
        Mitglied = "1",
        Familienmitglied = "3",
        Rentner = "5",
    }

    export enum WorkActivity1 {
        Physical = "Physical",
        Mental = "Mental",
    }

    export enum WorkActivity2 {
        Standing = "Standing",
        Sitting = "Sitting",
    }

    export enum FromCardType {
        FromCardType_EGK = "EGK",
        FromCardType_KVK = "KVK",
        FromCardType_Mobile = "Mobile",
    }

    export enum CardReadInStatus {
        CardReadInStatus_CardReadin = "CardReadin",
        CardReadInStatus_ManualEntry = "ManualEntry",
        CardReadInStatus_NotReadin = "NotReadin",
        CardReadInStatus_OnlineCheckFail = "OnlineCheckFail",
    }

    export enum PatientType {
        PatientType_Private = "Private",
        PatientType_Public = "Public",
    }

    export enum JobStatus {
        JobStatus_Employees = "Angestellte (erwerbstätig=Y)",
        JobStatus_Executives = "Leitende Angestellte (erwerbstätig=Y)",
        JobStatus_ManagingDirector = "Geschäftsfühhrter (erwerbstätig=Y)",
        JobStatus_CivilServant = "Zivilbediensteter/Diener (erwerbstätig=Y)",
        JobStatus_JobSeeker = "Arbeitssuchend (erwerbstätig=N)",
    }

    export enum CompareStatus {
        NotExist = "NotExist",
        PartialMatch = "PartialMatch",
        ExactMatch = "ExactMatch",
        ExactInsuranceNumber = "ExactInsuranceNumber",
    }

    export enum Pulse {
        Pulse_Rhythmic = "Pulse_Rhythmic",
        Pulse_Irregular = "Pulse_Irregular",
    }

    export enum EventName {
        EventName_UpdateMedicalData = "EventName_UpdateMedicalData",
        EventName_UpdatePatientProfile = "EventName_UpdatePatientProfile",
        EventName_UpdateLHM = "EventName_UpdateLHM",
        EventName_CreatedPatient = "EventName_CreatedPatient",
        EventName_DeletedPatient = "EventName_DeletedPatient",
    }


// method name convention const

// Define constants
// method name convention const
