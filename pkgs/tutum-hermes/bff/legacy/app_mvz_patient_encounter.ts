/* eslint-disable */
// This code was autogenerated from app/mvz/patient_encounter.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as catalog_material_cost_common from "./catalog_material_cost_common"
import * as catalog_sdebm_common from "./catalog_sdebm_common"
import * as common5 from "./hgnc_common"
import * as common1 from "./sdicd_common"
import * as common3 from "./sdomim_common"
import * as common2 from "./sdops_common"
import * as common4 from "./sdva_common"
import * as doctor_participate from "./service_domains_doctor_participate"
import * as common from "./version_info_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface GetEbmsRequest {
				selectedDate: number
		}
	

		export interface GetEbmsResponse {
				items: Array<catalog_sdebm_common.EbmSearchItem>
		}
	

		export interface GetMaterialCostsResponse {
				items: Array<catalog_material_cost_common.MaterialCostCatalog>
		}
	

		export interface GetOpsResponse {
				items: Array<catalog_sdebm_common.OpsCatalog>
		}
	

		export interface GetAdditionalInfoFieldsKvRequest {
				treatmentCase: string
				selectedDate: number
		}
	

		export interface GetAdditionalInfoFieldsSelectiveContractRequest {
				contractId: string
		}
	

		export interface GetAdditionalInfoFieldsResponse {
				fields: Array<catalog_sdebm_common.Field>
		}
	

		export interface GetPseudoGnrRequest {
				bsnr: string
		}
	

		export interface PseudoGNR {
				key: string
				value: string
		}
	

		export interface GetPseudoGnrResponse {
				pseudoGNR: Array<PseudoGNR>
		}
	

		export interface GetOmimLicenseRequest {
				selectedDate: number
				patientId: string
		}
	

		export interface GetOmimLicenseResponse {
				data: common.VersionInfo
		}
	

		export interface SearchEbmsRequest {
				selectedDate: number
				query: string
				organizationId?: string
		}
	

		export interface SearchEbmsResponse {
				items: Array<catalog_sdebm_common.EbmSearchItem>
		}
	

		export interface SearchDiagnosisRequest {
				query: string
				selectedDate: number
				catalog?: common1.IcdSearchCatalog
				doctorSpecialistType?: string
		}
	

		export interface SearchDiagnosisResponse {
				items: Array<common1.IcdSeachItem>
		}
	

		export interface SearchOpsRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchOpsResponse {
				items: Array<common2.OpsItem>
		}
	

		export interface SearchOmimGRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchOmimPRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchOmimGResponse {
				items: Array<common3.OmimG>
		}
	

		export interface SearchOmimPResponse {
				items: Array<common3.OmimP>
		}
	

		export interface SearchSdvaRequest {
				query: Array<string>
				selectedDate: number
		}
	

		export interface SearchSdvaResponse {
				items: Array<common4.Chapter>
		}
	

		export interface SearchHgncRequest {
				query: string
				selectedDate: number
		}
	

		export interface SearchHgncResponse {
				items: Array<common5.HgncItem>
		}
	


// enum definitions

// method name convention const
		export const EVENT_GetContractDoctorGroup = "api.app.mvz.PatientEncounterApp.GetContractDoctorGroup";
		export const EVENT_GetContractDoctorGroup_Response = "api.app.mvz.PatientEncounterApp.GetContractDoctorGroup.Response";
		export const EVENT_GetEbms = "api.app.mvz.PatientEncounterApp.GetEbms";
		export const EVENT_GetEbms_Response = "api.app.mvz.PatientEncounterApp.GetEbms.Response";
		export const EVENT_SearchEbmsComposer = "api.app.mvz.PatientEncounterApp.SearchEbmsComposer";
		export const EVENT_SearchEbmsComposer_Response = "api.app.mvz.PatientEncounterApp.SearchEbmsComposer.Response";
		export const EVENT_GetMaterialCosts = "api.app.mvz.PatientEncounterApp.GetMaterialCosts";
		export const EVENT_GetMaterialCosts_Response = "api.app.mvz.PatientEncounterApp.GetMaterialCosts.Response";
		export const EVENT_GetAdditionalInfoFieldsKv = "api.app.mvz.PatientEncounterApp.GetAdditionalInfoFieldsKv";
		export const EVENT_GetAdditionalInfoFieldsKv_Response = "api.app.mvz.PatientEncounterApp.GetAdditionalInfoFieldsKv.Response";
		export const EVENT_GetAdditionalInfoFieldsSelectiveContract = "api.app.mvz.PatientEncounterApp.GetAdditionalInfoFieldsSelectiveContract";
		export const EVENT_GetAdditionalInfoFieldsSelectiveContract_Response = "api.app.mvz.PatientEncounterApp.GetAdditionalInfoFieldsSelectiveContract.Response";
		export const EVENT_GetPseudoGnr = "api.app.mvz.PatientEncounterApp.GetPseudoGnr";
		export const EVENT_GetPseudoGnr_Response = "api.app.mvz.PatientEncounterApp.GetPseudoGnr.Response";
		export const EVENT_GetOmimLicense = "api.app.mvz.PatientEncounterApp.GetOmimLicense";
		export const EVENT_GetOmimLicense_Response = "api.app.mvz.PatientEncounterApp.GetOmimLicense.Response";
		export const EVENT_GetAdditionalInfoFields = "api.app.mvz.PatientEncounterApp.GetAdditionalInfoFields";
		export const EVENT_GetAdditionalInfoFields_Response = "api.app.mvz.PatientEncounterApp.GetAdditionalInfoFields.Response";
		export const EVENT_SearchDiagnosis = "api.app.mvz.PatientEncounterApp.SearchDiagnosis";
		export const EVENT_SearchDiagnosis_Response = "api.app.mvz.PatientEncounterApp.SearchDiagnosis.Response";
		export const EVENT_SearchOps = "api.app.mvz.PatientEncounterApp.SearchOps";
		export const EVENT_SearchOps_Response = "api.app.mvz.PatientEncounterApp.SearchOps.Response";
		export const EVENT_SearchOmimG = "api.app.mvz.PatientEncounterApp.SearchOmimG";
		export const EVENT_SearchOmimG_Response = "api.app.mvz.PatientEncounterApp.SearchOmimG.Response";
		export const EVENT_SearchOmimP = "api.app.mvz.PatientEncounterApp.SearchOmimP";
		export const EVENT_SearchOmimP_Response = "api.app.mvz.PatientEncounterApp.SearchOmimP.Response";
		export const EVENT_SearchSdva = "api.app.mvz.PatientEncounterApp.SearchSdva";
		export const EVENT_SearchSdva_Response = "api.app.mvz.PatientEncounterApp.SearchSdva.Response";
		export const EVENT_SearchHgnc = "api.app.mvz.PatientEncounterApp.SearchHgnc";
		export const EVENT_SearchHgnc_Response = "api.app.mvz.PatientEncounterApp.SearchHgnc.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetContractDoctorGroup = "/api/app/mvz/patient/encounter/getContractDoctorGroup";
        export const LEGACY_TOPIC_GetEbms = "/api/app/mvz/patient/encounter/getEbms";
        export const LEGACY_TOPIC_SearchEbmsComposer = "/api/app/mvz/patient/encounter/searchEbmsComposer";
        export const LEGACY_TOPIC_GetMaterialCosts = "/api/app/mvz/patient/encounter/getMaterialCosts";
        export const LEGACY_TOPIC_GetAdditionalInfoFieldsKv = "/api/app/mvz/patient/encounter/getAdditionalInfoFieldsKv";
        export const LEGACY_TOPIC_GetAdditionalInfoFieldsSelectiveContract = "/api/app/mvz/patient/encounter/getAdditionalInfoFieldsSelectiveContract";
        export const LEGACY_TOPIC_GetPseudoGnr = "/api/app/mvz/patient/encounter/getPseudoGnr";
        export const LEGACY_TOPIC_GetOmimLicense = "/api/app/mvz/patient/encounter/getOmimLicense";
        export const LEGACY_TOPIC_GetAdditionalInfoFields = "/api/app/mvz/patient/encounter/getAdditionalInfoFields";
        export const LEGACY_TOPIC_SearchDiagnosis = "/api/app/mvz/patient/encounter/searchDiagnosis";
        export const LEGACY_TOPIC_SearchOps = "/api/app/mvz/patient/encounter/searchOps";
        export const LEGACY_TOPIC_SearchOmimG = "/api/app/mvz/patient/encounter/searchOmimG";
        export const LEGACY_TOPIC_SearchOmimP = "/api/app/mvz/patient/encounter/searchOmimP";
        export const LEGACY_TOPIC_SearchSdva = "/api/app/mvz/patient/encounter/searchSdva";
        export const LEGACY_TOPIC_SearchHgnc = "/api/app/mvz/patient/encounter/searchHgnc";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getContractDoctorGroup(request: doctor_participate.GetGroupByContractRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<doctor_participate.GetGroupByContractResponse>("POST", LEGACY_TOPIC_GetContractDoctorGroup, { init , request})
			}

			export function useQueryGetContractDoctorGroup<TransformedType =doctor_participate.GetGroupByContractResponse>(payload: doctor_participate.GetGroupByContractRequest,ops?: CustomUseQueryOptions<ResponseType<doctor_participate.GetGroupByContractResponse>, TransformedType>) {
                return useQuery<ResponseType<doctor_participate.GetGroupByContractResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetContractDoctorGroup, payload],
					queryFn: async ({ signal }) => await getContractDoctorGroup(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetContractDoctorGroup(opts?: UseMutationOptions<ResponseType<doctor_participate.GetGroupByContractResponse>, ErrorType,doctor_participate.GetGroupByContractRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getContractDoctorGroup(request),
						retry: false,
						...opts
                });
            }
    
			export async function getEbms(request: GetEbmsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetEbmsResponse>("POST", LEGACY_TOPIC_GetEbms, { init , request})
			}

			export function useQueryGetEbms<TransformedType =GetEbmsResponse>(payload: GetEbmsRequest,ops?: CustomUseQueryOptions<ResponseType<GetEbmsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetEbmsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetEbms, payload],
					queryFn: async ({ signal }) => await getEbms(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetEbms(opts?: UseMutationOptions<ResponseType<GetEbmsResponse>, ErrorType,GetEbmsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getEbms(request),
						retry: false,
						...opts
                });
            }
    
			export async function searchEbmsComposer(request: SearchEbmsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchEbmsResponse>("POST", LEGACY_TOPIC_SearchEbmsComposer, { init , request})
			}

			export function useQuerySearchEbmsComposer<TransformedType =SearchEbmsResponse>(payload: SearchEbmsRequest,ops?: CustomUseQueryOptions<ResponseType<SearchEbmsResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchEbmsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchEbmsComposer, payload],
					queryFn: async ({ signal }) => await searchEbmsComposer(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchEbmsComposer(opts?: UseMutationOptions<ResponseType<SearchEbmsResponse>, ErrorType,SearchEbmsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchEbmsComposer(request),
						retry: false,
						...opts
                });
            }
    
			export async function getMaterialCosts(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetMaterialCostsResponse>("POST", LEGACY_TOPIC_GetMaterialCosts, { init })
			}

			export function useQueryGetMaterialCosts<TransformedType =GetMaterialCostsResponse>(ops?: CustomUseQueryOptions<ResponseType<GetMaterialCostsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetMaterialCostsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetMaterialCosts],
					queryFn: async ({ signal }) => await getMaterialCosts({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetMaterialCosts(opts?: UseMutationOptions<ResponseType<GetMaterialCostsResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getMaterialCosts(),
						retry: false,
						...opts
                });
            }
    
			export async function getAdditionalInfoFieldsKv(request: GetAdditionalInfoFieldsKvRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetAdditionalInfoFieldsResponse>("POST", LEGACY_TOPIC_GetAdditionalInfoFieldsKv, { init , request})
			}

			export function useQueryGetAdditionalInfoFieldsKv<TransformedType =GetAdditionalInfoFieldsResponse>(payload: GetAdditionalInfoFieldsKvRequest,ops?: CustomUseQueryOptions<ResponseType<GetAdditionalInfoFieldsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetAdditionalInfoFieldsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetAdditionalInfoFieldsKv, payload],
					queryFn: async ({ signal }) => await getAdditionalInfoFieldsKv(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetAdditionalInfoFieldsKv(opts?: UseMutationOptions<ResponseType<GetAdditionalInfoFieldsResponse>, ErrorType,GetAdditionalInfoFieldsKvRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getAdditionalInfoFieldsKv(request),
						retry: false,
						...opts
                });
            }
    
			export async function getAdditionalInfoFieldsSelectiveContract(request: GetAdditionalInfoFieldsSelectiveContractRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetAdditionalInfoFieldsResponse>("POST", LEGACY_TOPIC_GetAdditionalInfoFieldsSelectiveContract, { init , request})
			}

			export function useQueryGetAdditionalInfoFieldsSelectiveContract<TransformedType =GetAdditionalInfoFieldsResponse>(payload: GetAdditionalInfoFieldsSelectiveContractRequest,ops?: CustomUseQueryOptions<ResponseType<GetAdditionalInfoFieldsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetAdditionalInfoFieldsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetAdditionalInfoFieldsSelectiveContract, payload],
					queryFn: async ({ signal }) => await getAdditionalInfoFieldsSelectiveContract(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetAdditionalInfoFieldsSelectiveContract(opts?: UseMutationOptions<ResponseType<GetAdditionalInfoFieldsResponse>, ErrorType,GetAdditionalInfoFieldsSelectiveContractRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getAdditionalInfoFieldsSelectiveContract(request),
						retry: false,
						...opts
                });
            }
    
			export async function getPseudoGnr(request: GetPseudoGnrRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetPseudoGnrResponse>("POST", LEGACY_TOPIC_GetPseudoGnr, { init , request})
			}

			export function useQueryGetPseudoGnr<TransformedType =GetPseudoGnrResponse>(payload: GetPseudoGnrRequest,ops?: CustomUseQueryOptions<ResponseType<GetPseudoGnrResponse>, TransformedType>) {
                return useQuery<ResponseType<GetPseudoGnrResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPseudoGnr, payload],
					queryFn: async ({ signal }) => await getPseudoGnr(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPseudoGnr(opts?: UseMutationOptions<ResponseType<GetPseudoGnrResponse>, ErrorType,GetPseudoGnrRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPseudoGnr(request),
						retry: false,
						...opts
                });
            }
    
			export async function getOmimLicense(request: GetOmimLicenseRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetOmimLicenseResponse>("POST", LEGACY_TOPIC_GetOmimLicense, { init , request})
			}

			export function useQueryGetOmimLicense<TransformedType =GetOmimLicenseResponse>(payload: GetOmimLicenseRequest,ops?: CustomUseQueryOptions<ResponseType<GetOmimLicenseResponse>, TransformedType>) {
                return useQuery<ResponseType<GetOmimLicenseResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetOmimLicense, payload],
					queryFn: async ({ signal }) => await getOmimLicense(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetOmimLicense(opts?: UseMutationOptions<ResponseType<GetOmimLicenseResponse>, ErrorType,GetOmimLicenseRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getOmimLicense(request),
						retry: false,
						...opts
                });
            }
    
			export async function getAdditionalInfoFields(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetAdditionalInfoFieldsResponse>("POST", LEGACY_TOPIC_GetAdditionalInfoFields, { init })
			}

			export function useQueryGetAdditionalInfoFields<TransformedType =GetAdditionalInfoFieldsResponse>(ops?: CustomUseQueryOptions<ResponseType<GetAdditionalInfoFieldsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetAdditionalInfoFieldsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetAdditionalInfoFields],
					queryFn: async ({ signal }) => await getAdditionalInfoFields({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetAdditionalInfoFields(opts?: UseMutationOptions<ResponseType<GetAdditionalInfoFieldsResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getAdditionalInfoFields(),
						retry: false,
						...opts
                });
            }
    
			export async function searchDiagnosis(request: SearchDiagnosisRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchDiagnosisResponse>("POST", LEGACY_TOPIC_SearchDiagnosis, { init , request})
			}

			export function useQuerySearchDiagnosis<TransformedType =SearchDiagnosisResponse>(payload: SearchDiagnosisRequest,ops?: CustomUseQueryOptions<ResponseType<SearchDiagnosisResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchDiagnosisResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchDiagnosis, payload],
					queryFn: async ({ signal }) => await searchDiagnosis(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchDiagnosis(opts?: UseMutationOptions<ResponseType<SearchDiagnosisResponse>, ErrorType,SearchDiagnosisRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchDiagnosis(request),
						retry: false,
						...opts
                });
            }
    
			export async function searchOps(request: SearchOpsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchOpsResponse>("POST", LEGACY_TOPIC_SearchOps, { init , request})
			}

			export function useQuerySearchOps<TransformedType =SearchOpsResponse>(payload: SearchOpsRequest,ops?: CustomUseQueryOptions<ResponseType<SearchOpsResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchOpsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchOps, payload],
					queryFn: async ({ signal }) => await searchOps(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchOps(opts?: UseMutationOptions<ResponseType<SearchOpsResponse>, ErrorType,SearchOpsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchOps(request),
						retry: false,
						...opts
                });
            }
    
			export async function searchOmimG(request: SearchOmimGRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchOmimGResponse>("POST", LEGACY_TOPIC_SearchOmimG, { init , request})
			}

			export function useQuerySearchOmimG<TransformedType =SearchOmimGResponse>(payload: SearchOmimGRequest,ops?: CustomUseQueryOptions<ResponseType<SearchOmimGResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchOmimGResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchOmimG, payload],
					queryFn: async ({ signal }) => await searchOmimG(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchOmimG(opts?: UseMutationOptions<ResponseType<SearchOmimGResponse>, ErrorType,SearchOmimGRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchOmimG(request),
						retry: false,
						...opts
                });
            }
    
			export async function searchOmimP(request: SearchOmimPRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchOmimPResponse>("POST", LEGACY_TOPIC_SearchOmimP, { init , request})
			}

			export function useQuerySearchOmimP<TransformedType =SearchOmimPResponse>(payload: SearchOmimPRequest,ops?: CustomUseQueryOptions<ResponseType<SearchOmimPResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchOmimPResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchOmimP, payload],
					queryFn: async ({ signal }) => await searchOmimP(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchOmimP(opts?: UseMutationOptions<ResponseType<SearchOmimPResponse>, ErrorType,SearchOmimPRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchOmimP(request),
						retry: false,
						...opts
                });
            }
    
			export async function searchSdva(request: SearchSdvaRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchSdvaResponse>("POST", LEGACY_TOPIC_SearchSdva, { init , request})
			}

			export function useQuerySearchSdva<TransformedType =SearchSdvaResponse>(payload: SearchSdvaRequest,ops?: CustomUseQueryOptions<ResponseType<SearchSdvaResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchSdvaResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchSdva, payload],
					queryFn: async ({ signal }) => await searchSdva(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchSdva(opts?: UseMutationOptions<ResponseType<SearchSdvaResponse>, ErrorType,SearchSdvaRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchSdva(request),
						retry: false,
						...opts
                });
            }
    
			export async function searchHgnc(request: SearchHgncRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchHgncResponse>("POST", LEGACY_TOPIC_SearchHgnc, { init , request})
			}

			export function useQuerySearchHgnc<TransformedType =SearchHgncResponse>(payload: SearchHgncRequest,ops?: CustomUseQueryOptions<ResponseType<SearchHgncResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchHgncResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchHgnc, payload],
					queryFn: async ({ signal }) => await searchHgnc(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchHgnc(opts?: UseMutationOptions<ResponseType<SearchHgncResponse>, ErrorType,SearchHgncRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchHgnc(request),
						retry: false,
						...opts
                });
            }
    

