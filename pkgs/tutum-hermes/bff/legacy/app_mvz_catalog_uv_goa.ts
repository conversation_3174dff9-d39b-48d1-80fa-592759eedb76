/* eslint-disable */
// This code was autogenerated from app/mvz/catalog_uv_goa.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as catalog_uv_goa_common from "./catalog_uv_goa_common"
import * as common from "./common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface CreateUvGoaCatalogRequest {
				uvGoa: catalog_uv_goa_common.UvGoaCatalog
		}
	

		export interface UvGoaCatalogItem {
				uvGoa: catalog_uv_goa_common.UvGoaCatalog
		}
	

		export interface GetUvGoaCatalogsRequest {
				value: string
				isOnlySelfCreated: boolean
				pagination: common.PaginationRequest
		}
	

		export interface SearchUvGoaRequest {
				value: string
				selectedDate?: number
				isGeneral: boolean
		}
	

		export interface SearchUvGoaResponse {
				items: Array<catalog_uv_goa_common.UvGoaItem>
		}
	

		export interface GetUvGoaCatalogsResponse {
				items: Array<catalog_uv_goa_common.UvGoaCatalog>
				total: number
		}
	

		export interface UpdateUvGoaCatalogRequest {
				uvGoa: catalog_uv_goa_common.UvGoaCatalog
		}
	

		export interface IsValidUpdateUvGoaResponse {
				errors: {[key:string]:common.FieldError}
		}
	

		export interface DeleteUvGoaCatalogRequest {
				id: string
		}
	

		export interface GetUvGoaCatalogByCodeRequest {
				number: string
		}
	

		export interface GetUvGoaCatalogByCodesRequest {
				numbers: Array<string>
		}
	

		export interface GetUvGoaCatalogByCodesResponse {
				items: Array<catalog_uv_goa_common.UvGoaCatalog>
		}
	


// enum definitions

// method name convention const
		export const EVENT_CreateUvGoaCatalog = "api.app.mvz.UvGoaCatalogApp.CreateUvGoaCatalog";
		export const EVENT_CreateUvGoaCatalog_Response = "api.app.mvz.UvGoaCatalogApp.CreateUvGoaCatalog.Response";
		export const EVENT_GetUvGoaCatalogs = "api.app.mvz.UvGoaCatalogApp.GetUvGoaCatalogs";
		export const EVENT_GetUvGoaCatalogs_Response = "api.app.mvz.UvGoaCatalogApp.GetUvGoaCatalogs.Response";
		export const EVENT_UpdateUvGoaCatalog = "api.app.mvz.UvGoaCatalogApp.UpdateUvGoaCatalog";
		export const EVENT_UpdateUvGoaCatalog_Response = "api.app.mvz.UvGoaCatalogApp.UpdateUvGoaCatalog.Response";
		export const EVENT_IsValidUpdateUvGoa = "api.app.mvz.UvGoaCatalogApp.IsValidUpdateUvGoa";
		export const EVENT_IsValidUpdateUvGoa_Response = "api.app.mvz.UvGoaCatalogApp.IsValidUpdateUvGoa.Response";
		export const EVENT_DeleteUvGoaCatalog = "api.app.mvz.UvGoaCatalogApp.DeleteUvGoaCatalog";
		export const EVENT_DeleteUvGoaCatalog_Response = "api.app.mvz.UvGoaCatalogApp.DeleteUvGoaCatalog.Response";
		export const EVENT_SearchUvGoaItems = "api.app.mvz.UvGoaCatalogApp.SearchUvGoaItems";
		export const EVENT_SearchUvGoaItems_Response = "api.app.mvz.UvGoaCatalogApp.SearchUvGoaItems.Response";
		export const EVENT_GetUvGoaCatalogByCode = "api.app.mvz.UvGoaCatalogApp.GetUvGoaCatalogByCode";
		export const EVENT_GetUvGoaCatalogByCode_Response = "api.app.mvz.UvGoaCatalogApp.GetUvGoaCatalogByCode.Response";
		export const EVENT_GetUvGoaCatalogByCodes = "api.app.mvz.UvGoaCatalogApp.GetUvGoaCatalogByCodes";
		export const EVENT_GetUvGoaCatalogByCodes_Response = "api.app.mvz.UvGoaCatalogApp.GetUvGoaCatalogByCodes.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_CreateUvGoaCatalog = "/api/app/mvz/uv/goa/catalog/createUvGoaCatalog";
        export const LEGACY_TOPIC_GetUvGoaCatalogs = "/api/app/mvz/uv/goa/catalog/getUvGoaCatalogs";
        export const LEGACY_TOPIC_UpdateUvGoaCatalog = "/api/app/mvz/uv/goa/catalog/updateUvGoaCatalog";
        export const LEGACY_TOPIC_IsValidUpdateUvGoa = "/api/app/mvz/uv/goa/catalog/isValidUpdateUvGoa";
        export const LEGACY_TOPIC_DeleteUvGoaCatalog = "/api/app/mvz/uv/goa/catalog/deleteUvGoaCatalog";
        export const LEGACY_TOPIC_SearchUvGoaItems = "/api/app/mvz/uv/goa/catalog/searchUvGoaItems";
        export const LEGACY_TOPIC_GetUvGoaCatalogByCode = "/api/app/mvz/uv/goa/catalog/getUvGoaCatalogByCode";
        export const LEGACY_TOPIC_GetUvGoaCatalogByCodes = "/api/app/mvz/uv/goa/catalog/getUvGoaCatalogByCodes";


// Define action methods and their listener -----------------------------------------------------------------
			export async function createUvGoaCatalog(request: CreateUvGoaCatalogRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<UvGoaCatalogItem>("POST", LEGACY_TOPIC_CreateUvGoaCatalog, { init , request})
			}

			export function useQueryCreateUvGoaCatalog<TransformedType =UvGoaCatalogItem>(payload: CreateUvGoaCatalogRequest,ops?: CustomUseQueryOptions<ResponseType<UvGoaCatalogItem>, TransformedType>) {
                return useQuery<ResponseType<UvGoaCatalogItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateUvGoaCatalog, payload],
					queryFn: async ({ signal }) => await createUvGoaCatalog(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateUvGoaCatalog(opts?: UseMutationOptions<ResponseType<UvGoaCatalogItem>, ErrorType,CreateUvGoaCatalogRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createUvGoaCatalog(request),
						retry: false,
						...opts
                });
            }
    
			export async function getUvGoaCatalogs(request: GetUvGoaCatalogsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetUvGoaCatalogsResponse>("POST", LEGACY_TOPIC_GetUvGoaCatalogs, { init , request})
			}

			export function useQueryGetUvGoaCatalogs<TransformedType =GetUvGoaCatalogsResponse>(payload: GetUvGoaCatalogsRequest,ops?: CustomUseQueryOptions<ResponseType<GetUvGoaCatalogsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetUvGoaCatalogsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetUvGoaCatalogs, payload],
					queryFn: async ({ signal }) => await getUvGoaCatalogs(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetUvGoaCatalogs(opts?: UseMutationOptions<ResponseType<GetUvGoaCatalogsResponse>, ErrorType,GetUvGoaCatalogsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getUvGoaCatalogs(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateUvGoaCatalog(request: UpdateUvGoaCatalogRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<UvGoaCatalogItem>("POST", LEGACY_TOPIC_UpdateUvGoaCatalog, { init , request})
			}

			export function useQueryUpdateUvGoaCatalog<TransformedType =UvGoaCatalogItem>(payload: UpdateUvGoaCatalogRequest,ops?: CustomUseQueryOptions<ResponseType<UvGoaCatalogItem>, TransformedType>) {
                return useQuery<ResponseType<UvGoaCatalogItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateUvGoaCatalog, payload],
					queryFn: async ({ signal }) => await updateUvGoaCatalog(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateUvGoaCatalog(opts?: UseMutationOptions<ResponseType<UvGoaCatalogItem>, ErrorType,UpdateUvGoaCatalogRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateUvGoaCatalog(request),
						retry: false,
						...opts
                });
            }
    
			export async function isValidUpdateUvGoa(request: UpdateUvGoaCatalogRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<IsValidUpdateUvGoaResponse>("POST", LEGACY_TOPIC_IsValidUpdateUvGoa, { init , request})
			}

			export function useQueryIsValidUpdateUvGoa<TransformedType =IsValidUpdateUvGoaResponse>(payload: UpdateUvGoaCatalogRequest,ops?: CustomUseQueryOptions<ResponseType<IsValidUpdateUvGoaResponse>, TransformedType>) {
                return useQuery<ResponseType<IsValidUpdateUvGoaResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_IsValidUpdateUvGoa, payload],
					queryFn: async ({ signal }) => await isValidUpdateUvGoa(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationIsValidUpdateUvGoa(opts?: UseMutationOptions<ResponseType<IsValidUpdateUvGoaResponse>, ErrorType,UpdateUvGoaCatalogRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await isValidUpdateUvGoa(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteUvGoaCatalog(request: DeleteUvGoaCatalogRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteUvGoaCatalog, { init , request})
			}

			export function useQueryDeleteUvGoaCatalog<TransformedType =any>(payload: DeleteUvGoaCatalogRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteUvGoaCatalog, payload],
					queryFn: async ({ signal }) => await deleteUvGoaCatalog(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteUvGoaCatalog(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeleteUvGoaCatalogRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteUvGoaCatalog(request),
						retry: false,
						...opts
                });
            }
    
			export async function searchUvGoaItems(request: SearchUvGoaRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchUvGoaResponse>("POST", LEGACY_TOPIC_SearchUvGoaItems, { init , request})
			}

			export function useQuerySearchUvGoaItems<TransformedType =SearchUvGoaResponse>(payload: SearchUvGoaRequest,ops?: CustomUseQueryOptions<ResponseType<SearchUvGoaResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchUvGoaResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchUvGoaItems, payload],
					queryFn: async ({ signal }) => await searchUvGoaItems(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchUvGoaItems(opts?: UseMutationOptions<ResponseType<SearchUvGoaResponse>, ErrorType,SearchUvGoaRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchUvGoaItems(request),
						retry: false,
						...opts
                });
            }
    
			export async function getUvGoaCatalogByCode(request: GetUvGoaCatalogByCodeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<UvGoaCatalogItem>("POST", LEGACY_TOPIC_GetUvGoaCatalogByCode, { init , request})
			}

			export function useQueryGetUvGoaCatalogByCode<TransformedType =UvGoaCatalogItem>(payload: GetUvGoaCatalogByCodeRequest,ops?: CustomUseQueryOptions<ResponseType<UvGoaCatalogItem>, TransformedType>) {
                return useQuery<ResponseType<UvGoaCatalogItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetUvGoaCatalogByCode, payload],
					queryFn: async ({ signal }) => await getUvGoaCatalogByCode(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetUvGoaCatalogByCode(opts?: UseMutationOptions<ResponseType<UvGoaCatalogItem>, ErrorType,GetUvGoaCatalogByCodeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getUvGoaCatalogByCode(request),
						retry: false,
						...opts
                });
            }
    
			export async function getUvGoaCatalogByCodes(request: GetUvGoaCatalogByCodesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetUvGoaCatalogByCodesResponse>("POST", LEGACY_TOPIC_GetUvGoaCatalogByCodes, { init , request})
			}

			export function useQueryGetUvGoaCatalogByCodes<TransformedType =GetUvGoaCatalogByCodesResponse>(payload: GetUvGoaCatalogByCodesRequest,ops?: CustomUseQueryOptions<ResponseType<GetUvGoaCatalogByCodesResponse>, TransformedType>) {
                return useQuery<ResponseType<GetUvGoaCatalogByCodesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetUvGoaCatalogByCodes, payload],
					queryFn: async ({ signal }) => await getUvGoaCatalogByCodes(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetUvGoaCatalogByCodes(opts?: UseMutationOptions<ResponseType<GetUvGoaCatalogByCodesResponse>, ErrorType,GetUvGoaCatalogByCodesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getUvGoaCatalogByCodes(request),
						retry: false,
						...opts
                });
            }
    

