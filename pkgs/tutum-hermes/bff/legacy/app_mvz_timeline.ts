/* eslint-disable */
// This code was autogenerated from app/mvz/timeline.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common from "./common"
import * as common1 from "./eab_service_history_common"
import * as common2 from "./form_common"
import * as patient_encounter from "./repo_encounter"
import * as schein_common from "./schein_common"
import * as validation_timeline from "./service_domains_validation_timeline"
import * as common3 from "./timeline_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface GetRequest {
				patientId: string
				contractId?: string
				createdDate?: number
				encounterCase?: string
				treatmentDoctorId?: string
				types?: Array<common3.TimelineEntityType>
		}
	

		export interface GetResponse {
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface CreateRequest {
				timelineModel: common3.TimelineModel
		}
	

		export interface CreateResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface RemoveRequest {
				hasHardDelete?: boolean
				timelineId?: string
				isChain?: boolean
		}
	

		export interface RemoveResponse {
		}
	

		export interface EditRequest {
				timelineModel: common3.TimelineModel
		}
	

		export interface EditResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface UpdatePrintDateRequest {
				type: common3.TimelineEntityType
				timelineId?: string
				formId?: string
				printDate?: number
		}
	

		export interface UpdateSuggestionRuleAppliedRequest {
				timelineId?: string
				ruleId: string
		}
	

		export interface UpdatePrintDateResponse {
		}
	

		export interface EventTimelineHardRemove {
				patientId: string
				timelineModel: common3.TimelineModel
				timelineId: string
		}
	

		export interface EventTimelineRemove {
				patientId: string
				timelineModel: common3.TimelineModel
		}
	

		export interface EventTimelineRestore {
				timelineModel: common3.TimelineModel
		}
	

		export interface EventTimelineCreate {
				patientId: string
				timelineModel: common3.TimelineModel
		}
	

		export interface EventTimelineUpdate {
				patientId: string
				timelineModel: common3.TimelineModel
				oldTimelineModel?: common3.TimelineModel
				skipUpdateEndDatePermanentDiagnoses: boolean
				auditLogId?: string
				prescribeFormName?: common2.FormName
		}
	

		export interface EventAutoAction {
				patientId: string
				notificationCode: string
		}
	

		export interface ITimelineEntityType {
				timelineEntityType: common3.TimelineEntityType
				command?: string
		}
	

		export interface GroupByQuarterRequest {
				patientId: string
				fromDate?: number
				toDate?: number
				timelineEntityTypes?: Array<ITimelineEntityType>
				isSortByCategory: boolean
				isHistoryMode: boolean
				scheinId?: string
				keyword?: string
				pagination?: common.PaginationRequest
				year?: number
				quarter?: number
		}
	

		export interface GroupByQuarter {
				year: number
				quarter: number
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface GroupByQuarterResponse {
				groupByQuarters: Array<GroupByQuarter>
				totalPage: number
				matchedTokens: Array<string>
		}
	

		export interface EventTimelineValidation {
				patientId: string
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface GetByIdRequest {
				timelineId: string
		}
	

		export interface GetByIdsRequest {
				timelineIds: Array<string>
		}
	

		export interface GetByIdResponse {
				timelineModel?: common3.TimelineModel
		}
	

		export interface GetDiagnoseRequest {
				patientId: string
				codes?: Array<string>
		}
	

		export interface GetDiagnoseResponse {
				encounterDiagnoseTimeline: Array<patient_encounter.EncounterDiagnoseTimeline>
		}
	

		export interface IgnoreSdkrwRuleRequest {
				patientId: string
				ruleId: string
				encounterDate: number
		}
	

		export interface UpdateErezeptStatusRequest {
				medicineId: string
				status: string
		}
	

		export interface DeleteErezeptRequest {
				medicineId: string
				patientId: string
		}
	

		export interface GetTherapiesRequest {
				patientId: string
				scheinId: string
		}
	

		export interface TherapiesResponse {
				pyschotherapy: schein_common.Psychotherapy
				timelineModel: common3.TimelineModel
		}
	

		export interface GetTherapiesResponse {
				pyschotherapies: Array<TherapiesResponse>
		}
	

		export interface GetAmountServiceCodeRequest {
				patientId: string
				serviceCodes: Array<string>
				year: number
				quarter: number
		}
	

		export interface GetAmountServiceCodeResponse {
				amountServiceCode: {[key:string]:number}
		}
	

		export interface MarkNotApprovedPyschotherapyRequest {
				timelineId: Array<string>
				patientId: string
		}
	

		export interface RestoreEntryHistoryRequest {
				auditLogId: string
		}
	

		export interface RestoreEntryHistoryResponse {
		}
	

		export interface ValidateDiagnoseRequest {
				icdCode: Array<string>
				typeCheck: Array<common3.IcdErrorTypeCheck>
		}
	

		export interface ValidateDiagnoseResponse {
				results: Array<common3.ValidationDiagnoseResult>
		}
	

		export interface ReRunValidateServiceRequest {
				patientId: string
				contractId?: string
		}
	

		export interface GetTimelineEntryByIdsRequest {
				entryIds: Array<string>
		}
	

		export interface GetTimelineEntryIdsResponse {
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface GetPreParticipationServiceCodesRequest {
				patientId: string
		}
	

		export interface Document88130Request {
				timelineModel: common3.TimelineModel
				serviceEntryId: string
		}
	

		export interface Document88130Response {
				takeoverDiagnosis: Array<common3.TimelineModel>
				scheinId?: string
				serviceId: string
		}
	

		export interface GetPsychotherapyTakeOverRequest {
				patientId: string
		}
	

		export interface GetPsychotherapyTakeOverResponse {
				psychotherapyEntry: Array<common3.TimelineModel>
		}
	

		export interface TakeoverServiceTerminalApprovalRequest {
				timelineId: string
				scheinId: string
		}
	

		export interface GetTakeOverDiagnosisRequest {
				patientId: string
				fromDate?: number
				toDate?: number
				query?: string
				scheinId?: string
		}
	

		export interface GetTakeOverDiagnosisResponse {
				takeOverDiagnosisGroup: Array<common3.TakeOverDiagnosisGroup>
		}
	

		export interface GetPsychotherapyBefore2020 {
				timelineModel: common3.TimelineModel
				errorCode: validation_timeline.ServiceErrorCode
				serviceEntry: common3.TimelineModel
		}
	

		export interface GetPsychotherapyBefore2020Request {
				patientId: string
		}
	

		export interface GetPsychotherapyBefore2020Response {
				data: Array<GetPsychotherapyBefore2020>
		}
	

		export interface UpdateManyRequest {
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface TakeOverDiagnosisWithScheinIdRequest {
				scheinId: string
				timelineModelIds: Array<string>
				newDiagnosis: Array<common3.TimelineModel>
				mappingTreatmentRelevent: {[key:string]:boolean}
		}
	

		export interface MarkTreatmentRelevantRequest {
				timelineId: string
		}
	

		export interface UpdateEncounterCaseForServiceEntriesRequest {
				timelineId: string
		}
	

		export interface MarkAcceptedByKVRequest {
				timelineId: string
		}
	

		export interface DocumentSuggestionRequest {
				timelineId: string
				suggestionCode: string
				suggestionData?: {[key:string]:string}
		}
	

		export interface RollbackDocumentTerminateServiceRequest {
				terminateServiceId: string
				tehcnicalScheinId: string
				patientId: string
		}
	

		export interface GetLastDocumentedQuarterRequest {
				patientId: string
				year: number
				quarter: number
				timelineEntityType?: common3.TimelineEntityType
		}
	

		export interface GetLastDocumentedQuarterResponse {
				year: number
				quarter: number
		}
	

		export interface GetTimelineByEnrollmentIdRequest {
				enrollmentId: string
				patientId: string
		}
	

		export interface GetTimelineByEnrollmentIdResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface ReSendEABMailRequest {
				timelineId: string
		}
	

		export interface DocumentEABServiceCodeRequest {
				scheinId: string
				patientId: string
				bsnrCode: string
		}
	

		export interface DocumentEABServiceCodeResponse {
				serviceCodes: Array<common1.EABServiceCode>
		}
	

		export interface FindLatesTimelineEntryRequest {
				patientId: string
				type: common3.TimelineEntityType
				contractId: string
		}
	

		export interface FindLatesTimelineEntryResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface CheckIsVSST785Request {
				contractId: string
				patientId: string
				atcCodes: Array<string>
				ikNumber?: number
		}
	

		export interface CheckIsVSST785Response {
				isVSST785: boolean
		}
	

		export interface GetDoctorLetterByIdRequest {
				doctorLetterId: string
		}
	

		export interface GetDoctorLetterByIdResponse {
				timelineModel?: common3.TimelineModel
		}
	

		export interface GetActionChainDiagnoseByCodesRequest {
				codes: Array<string>
				year: number
		}
	

		export interface GetActionChainDiagnoseByCodesResponse {
				validItems: Array<string>
				inValidItems: Array<string>
		}
	


// enum definitions

// method name convention const
		export const EVENT_DocumentSuggestion = "api.app.mvz.TimelineApp.DocumentSuggestion";
		export const EVENT_DocumentSuggestion_Response = "api.app.mvz.TimelineApp.DocumentSuggestion.Response";
		export const EVENT_MarkTreatmentRelevant = "api.app.mvz.TimelineApp.MarkTreatmentRelevant";
		export const EVENT_MarkTreatmentRelevant_Response = "api.app.mvz.TimelineApp.MarkTreatmentRelevant.Response";
		export const EVENT_UpdateEncounterCaseForServiceEntries = "api.app.mvz.TimelineApp.UpdateEncounterCaseForServiceEntries";
		export const EVENT_UpdateEncounterCaseForServiceEntries_Response = "api.app.mvz.TimelineApp.UpdateEncounterCaseForServiceEntries.Response";
		export const EVENT_GetDiagnose = "api.app.mvz.TimelineApp.GetDiagnose";
		export const EVENT_GetDiagnose_Response = "api.app.mvz.TimelineApp.GetDiagnose.Response";
		export const EVENT_HandleEventTimelineValidation = "api.app.mvz.TimelineApp.HandleEventTimelineValidation";
		export const EVENT_HandleEventTimelineValidation_Response = "api.app.mvz.TimelineApp.HandleEventTimelineValidation.Response";
		export const EVENT_HandleEventTimelineHardRemove = "api.app.mvz.TimelineApp.HandleEventTimelineHardRemove";
		export const EVENT_HandleEventTimelineHardRemove_Response = "api.app.mvz.TimelineApp.HandleEventTimelineHardRemove.Response";
		export const EVENT_HandleEventTimelineRemove = "api.app.mvz.TimelineApp.HandleEventTimelineRemove";
		export const EVENT_HandleEventTimelineRemove_Response = "api.app.mvz.TimelineApp.HandleEventTimelineRemove.Response";
		export const EVENT_HandleEventTimelineCreate = "api.app.mvz.TimelineApp.HandleEventTimelineCreate";
		export const EVENT_HandleEventTimelineCreate_Response = "api.app.mvz.TimelineApp.HandleEventTimelineCreate.Response";
		export const EVENT_HandleEventTimelineUpdate = "api.app.mvz.TimelineApp.HandleEventTimelineUpdate";
		export const EVENT_HandleEventTimelineUpdate_Response = "api.app.mvz.TimelineApp.HandleEventTimelineUpdate.Response";
		export const EVENT_HandleEventAutoAction = "api.app.mvz.TimelineApp.HandleEventAutoAction";
		export const EVENT_HandleEventAutoAction_Response = "api.app.mvz.TimelineApp.HandleEventAutoAction.Response";
		export const EVENT_GetById = "api.app.mvz.TimelineApp.GetById";
		export const EVENT_GetById_Response = "api.app.mvz.TimelineApp.GetById.Response";
		export const EVENT_GetByIds = "api.app.mvz.TimelineApp.GetByIds";
		export const EVENT_GetByIds_Response = "api.app.mvz.TimelineApp.GetByIds.Response";
		export const EVENT_Get = "api.app.mvz.TimelineApp.Get";
		export const EVENT_Get_Response = "api.app.mvz.TimelineApp.Get.Response";
		export const EVENT_Create = "api.app.mvz.TimelineApp.Create";
		export const EVENT_Create_Response = "api.app.mvz.TimelineApp.Create.Response";
		export const EVENT_Edit = "api.app.mvz.TimelineApp.Edit";
		export const EVENT_Edit_Response = "api.app.mvz.TimelineApp.Edit.Response";
		export const EVENT_UpdateMany = "api.app.mvz.TimelineApp.UpdateMany";
		export const EVENT_UpdateMany_Response = "api.app.mvz.TimelineApp.UpdateMany.Response";
		export const EVENT_Remove = "api.app.mvz.TimelineApp.Remove";
		export const EVENT_Remove_Response = "api.app.mvz.TimelineApp.Remove.Response";
		export const EVENT_GroupByQuarter = "api.app.mvz.TimelineApp.GroupByQuarter";
		export const EVENT_GroupByQuarter_Response = "api.app.mvz.TimelineApp.GroupByQuarter.Response";
		export const EVENT_UpdatePrintDate = "api.app.mvz.TimelineApp.UpdatePrintDate";
		export const EVENT_UpdatePrintDate_Response = "api.app.mvz.TimelineApp.UpdatePrintDate.Response";
		export const EVENT_UpdateSuggestionRuleApplied = "api.app.mvz.TimelineApp.UpdateSuggestionRuleApplied";
		export const EVENT_UpdateSuggestionRuleApplied_Response = "api.app.mvz.TimelineApp.UpdateSuggestionRuleApplied.Response";
		export const EVENT_IgnoreSdkrwRule = "api.app.mvz.TimelineApp.IgnoreSdkrwRule";
		export const EVENT_IgnoreSdkrwRule_Response = "api.app.mvz.TimelineApp.IgnoreSdkrwRule.Response";
		export const EVENT_DeleteErezept = "api.app.mvz.TimelineApp.DeleteErezept";
		export const EVENT_DeleteErezept_Response = "api.app.mvz.TimelineApp.DeleteErezept.Response";
		export const EVENT_GetTherapies = "api.app.mvz.TimelineApp.GetTherapies";
		export const EVENT_GetTherapies_Response = "api.app.mvz.TimelineApp.GetTherapies.Response";
		export const EVENT_GetAmountServiceCode = "api.app.mvz.TimelineApp.GetAmountServiceCode";
		export const EVENT_GetAmountServiceCode_Response = "api.app.mvz.TimelineApp.GetAmountServiceCode.Response";
		export const EVENT_MarkNotApprovedPyschotherapy = "api.app.mvz.TimelineApp.MarkNotApprovedPyschotherapy";
		export const EVENT_MarkNotApprovedPyschotherapy_Response = "api.app.mvz.TimelineApp.MarkNotApprovedPyschotherapy.Response";
		export const EVENT_RestoreEntryHistory = "api.app.mvz.TimelineApp.RestoreEntryHistory";
		export const EVENT_RestoreEntryHistory_Response = "api.app.mvz.TimelineApp.RestoreEntryHistory.Response";
		export const EVENT_ValidateDiagnose = "api.app.mvz.TimelineApp.ValidateDiagnose";
		export const EVENT_ValidateDiagnose_Response = "api.app.mvz.TimelineApp.ValidateDiagnose.Response";
		export const EVENT_ReRunValidateService = "api.app.mvz.TimelineApp.ReRunValidateService";
		export const EVENT_ReRunValidateService_Response = "api.app.mvz.TimelineApp.ReRunValidateService.Response";
		export const EVENT_GetTimelineEntryByIds = "api.app.mvz.TimelineApp.GetTimelineEntryByIds";
		export const EVENT_GetTimelineEntryByIds_Response = "api.app.mvz.TimelineApp.GetTimelineEntryByIds.Response";
		export const EVENT_GetPreParticipationServiceCodes = "api.app.mvz.TimelineApp.GetPreParticipationServiceCodes";
		export const EVENT_GetPreParticipationServiceCodes_Response = "api.app.mvz.TimelineApp.GetPreParticipationServiceCodes.Response";
		export const EVENT_Document88130 = "api.app.mvz.TimelineApp.Document88130";
		export const EVENT_Document88130_Response = "api.app.mvz.TimelineApp.Document88130.Response";
		export const EVENT_GetPsychotherapyTakeOver = "api.app.mvz.TimelineApp.GetPsychotherapyTakeOver";
		export const EVENT_GetPsychotherapyTakeOver_Response = "api.app.mvz.TimelineApp.GetPsychotherapyTakeOver.Response";
		export const EVENT_TakeoverServiceTerminalApproval = "api.app.mvz.TimelineApp.TakeoverServiceTerminalApproval";
		export const EVENT_TakeoverServiceTerminalApproval_Response = "api.app.mvz.TimelineApp.TakeoverServiceTerminalApproval.Response";
		export const EVENT_GetTakeOverDiagnosis = "api.app.mvz.TimelineApp.GetTakeOverDiagnosis";
		export const EVENT_GetTakeOverDiagnosis_Response = "api.app.mvz.TimelineApp.GetTakeOverDiagnosis.Response";
		export const EVENT_GetPsychotherapyBefore2020 = "api.app.mvz.TimelineApp.GetPsychotherapyBefore2020";
		export const EVENT_GetPsychotherapyBefore2020_Response = "api.app.mvz.TimelineApp.GetPsychotherapyBefore2020.Response";
		export const EVENT_TakeOverDiagnosisWithScheinId = "api.app.mvz.TimelineApp.TakeOverDiagnosisWithScheinId";
		export const EVENT_TakeOverDiagnosisWithScheinId_Response = "api.app.mvz.TimelineApp.TakeOverDiagnosisWithScheinId.Response";
		export const EVENT_MarkAcceptedByKV = "api.app.mvz.TimelineApp.MarkAcceptedByKV";
		export const EVENT_MarkAcceptedByKV_Response = "api.app.mvz.TimelineApp.MarkAcceptedByKV.Response";
		export const EVENT_RollbackDocumentTerminateService = "api.app.mvz.TimelineApp.RollbackDocumentTerminateService";
		export const EVENT_RollbackDocumentTerminateService_Response = "api.app.mvz.TimelineApp.RollbackDocumentTerminateService.Response";
		export const EVENT_GetLastDocumentedQuarter = "api.app.mvz.TimelineApp.GetLastDocumentedQuarter";
		export const EVENT_GetLastDocumentedQuarter_Response = "api.app.mvz.TimelineApp.GetLastDocumentedQuarter.Response";
		export const EVENT_GetTimelineByEnrollmentId = "api.app.mvz.TimelineApp.GetTimelineByEnrollmentId";
		export const EVENT_GetTimelineByEnrollmentId_Response = "api.app.mvz.TimelineApp.GetTimelineByEnrollmentId.Response";
		export const EVENT_ReSendEABMail = "api.app.mvz.TimelineApp.ReSendEABMail";
		export const EVENT_ReSendEABMail_Response = "api.app.mvz.TimelineApp.ReSendEABMail.Response";
		export const EVENT_DocumentEABServiceCode = "api.app.mvz.TimelineApp.DocumentEABServiceCode";
		export const EVENT_DocumentEABServiceCode_Response = "api.app.mvz.TimelineApp.DocumentEABServiceCode.Response";
		export const EVENT_FindLatestTimelineEntry = "api.app.mvz.TimelineApp.FindLatestTimelineEntry";
		export const EVENT_FindLatestTimelineEntry_Response = "api.app.mvz.TimelineApp.FindLatestTimelineEntry.Response";
		export const EVENT_CheckIsVSST785 = "api.app.mvz.TimelineApp.CheckIsVSST785";
		export const EVENT_CheckIsVSST785_Response = "api.app.mvz.TimelineApp.CheckIsVSST785.Response";
		export const EVENT_GetDoctorLetterById = "api.app.mvz.TimelineApp.GetDoctorLetterById";
		export const EVENT_GetDoctorLetterById_Response = "api.app.mvz.TimelineApp.GetDoctorLetterById.Response";
		export const EVENT_GetActionChainDiagnoseByCodes = "api.app.mvz.TimelineApp.GetActionChainDiagnoseByCodes";
		export const EVENT_GetActionChainDiagnoseByCodes_Response = "api.app.mvz.TimelineApp.GetActionChainDiagnoseByCodes.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_DocumentSuggestion = "/api/app/mvz/timeline/documentSuggestion";
        export const LEGACY_TOPIC_MarkTreatmentRelevant = "/api/app/mvz/timeline/markTreatmentRelevant";
        export const LEGACY_TOPIC_UpdateEncounterCaseForServiceEntries = "/api/app/mvz/timeline/updateEncounterCaseForServiceEntries";
        export const LEGACY_TOPIC_GetDiagnose = "/api/app/mvz/timeline/getDiagnose";
        export const LEGACY_TOPIC_HandleEventTimelineValidation = "/api/app/mvz/timeline/handleEventTimelineValidation";
        export const LEGACY_TOPIC_HandleEventTimelineHardRemove = "/api/app/mvz/timeline/handleEventTimelineHardRemove";
        export const LEGACY_TOPIC_HandleEventTimelineRemove = "/api/app/mvz/timeline/handleEventTimelineRemove";
        export const LEGACY_TOPIC_HandleEventTimelineCreate = "/api/app/mvz/timeline/handleEventTimelineCreate";
        export const LEGACY_TOPIC_HandleEventTimelineUpdate = "/api/app/mvz/timeline/handleEventTimelineUpdate";
        export const LEGACY_TOPIC_HandleEventAutoAction = "/api/app/mvz/timeline/handleEventAutoAction";
        export const LEGACY_TOPIC_GetById = "/api/app/mvz/timeline/getById";
        export const LEGACY_TOPIC_GetByIds = "/api/app/mvz/timeline/getByIds";
        export const LEGACY_TOPIC_Get = "/api/app/mvz/timeline/get";
        export const LEGACY_TOPIC_Create = "/api/app/mvz/timeline/create";
        export const LEGACY_TOPIC_Edit = "/api/app/mvz/timeline/edit";
        export const LEGACY_TOPIC_UpdateMany = "/api/app/mvz/timeline/updateMany";
        export const LEGACY_TOPIC_Remove = "/api/app/mvz/timeline/remove";
        export const LEGACY_TOPIC_GroupByQuarter = "/api/app/mvz/timeline/groupByQuarter";
        export const LEGACY_TOPIC_UpdatePrintDate = "/api/app/mvz/timeline/updatePrintDate";
        export const LEGACY_TOPIC_UpdateSuggestionRuleApplied = "/api/app/mvz/timeline/updateSuggestionRuleApplied";
        export const LEGACY_TOPIC_IgnoreSdkrwRule = "/api/app/mvz/timeline/ignoreSdkrwRule";
        export const LEGACY_TOPIC_DeleteErezept = "/api/app/mvz/timeline/deleteErezept";
        export const LEGACY_TOPIC_GetTherapies = "/api/app/mvz/timeline/getTherapies";
        export const LEGACY_TOPIC_GetAmountServiceCode = "/api/app/mvz/timeline/getAmountServiceCode";
        export const LEGACY_TOPIC_MarkNotApprovedPyschotherapy = "/api/app/mvz/timeline/markNotApprovedPyschotherapy";
        export const LEGACY_TOPIC_RestoreEntryHistory = "/api/app/mvz/timeline/restoreEntryHistory";
        export const LEGACY_TOPIC_ValidateDiagnose = "/api/app/mvz/timeline/validateDiagnose";
        export const LEGACY_TOPIC_ReRunValidateService = "/api/app/mvz/timeline/reRunValidateService";
        export const LEGACY_TOPIC_GetTimelineEntryByIds = "/api/app/mvz/timeline/getTimelineEntryByIds";
        export const LEGACY_TOPIC_GetPreParticipationServiceCodes = "/api/app/mvz/timeline/getPreParticipationServiceCodes";
        export const LEGACY_TOPIC_Document88130 = "/api/app/mvz/timeline/document88130";
        export const LEGACY_TOPIC_GetPsychotherapyTakeOver = "/api/app/mvz/timeline/getPsychotherapyTakeOver";
        export const LEGACY_TOPIC_TakeoverServiceTerminalApproval = "/api/app/mvz/timeline/takeoverServiceTerminalApproval";
        export const LEGACY_TOPIC_GetTakeOverDiagnosis = "/api/app/mvz/timeline/getTakeOverDiagnosis";
        export const LEGACY_TOPIC_GetPsychotherapyBefore2020 = "/api/app/mvz/timeline/getPsychotherapyBefore2020";
        export const LEGACY_TOPIC_TakeOverDiagnosisWithScheinId = "/api/app/mvz/timeline/takeOverDiagnosisWithScheinId";
        export const LEGACY_TOPIC_MarkAcceptedByKV = "/api/app/mvz/timeline/markAcceptedByKV";
        export const LEGACY_TOPIC_RollbackDocumentTerminateService = "/api/app/mvz/timeline/rollbackDocumentTerminateService";
        export const LEGACY_TOPIC_GetLastDocumentedQuarter = "/api/app/mvz/timeline/getLastDocumentedQuarter";
        export const LEGACY_TOPIC_GetTimelineByEnrollmentId = "/api/app/mvz/timeline/getTimelineByEnrollmentId";
        export const LEGACY_TOPIC_ReSendEABMail = "/api/app/mvz/timeline/reSendEABMail";
        export const LEGACY_TOPIC_DocumentEABServiceCode = "/api/app/mvz/timeline/documentEABServiceCode";
        export const LEGACY_TOPIC_FindLatestTimelineEntry = "/api/app/mvz/timeline/findLatestTimelineEntry";
        export const LEGACY_TOPIC_CheckIsVSST785 = "/api/app/mvz/timeline/checkIsVSST785";
        export const LEGACY_TOPIC_GetDoctorLetterById = "/api/app/mvz/timeline/getDoctorLetterById";
        export const LEGACY_TOPIC_GetActionChainDiagnoseByCodes = "/api/app/mvz/timeline/getActionChainDiagnoseByCodes";


// Define action methods and their listener -----------------------------------------------------------------
			export async function documentSuggestion(request: DocumentSuggestionRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DocumentSuggestion, { init , request})
			}

			export function useQueryDocumentSuggestion<TransformedType =any>(payload: DocumentSuggestionRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DocumentSuggestion, payload],
					queryFn: async ({ signal }) => await documentSuggestion(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDocumentSuggestion(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DocumentSuggestionRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await documentSuggestion(request),
						retry: false,
						...opts
                });
            }
    
			export async function markTreatmentRelevant(request: MarkTreatmentRelevantRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_MarkTreatmentRelevant, { init , request})
			}

			export function useQueryMarkTreatmentRelevant<TransformedType =any>(payload: MarkTreatmentRelevantRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkTreatmentRelevant, payload],
					queryFn: async ({ signal }) => await markTreatmentRelevant(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkTreatmentRelevant(opts?: UseMutationOptions<ResponseType<any>, ErrorType,MarkTreatmentRelevantRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markTreatmentRelevant(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateEncounterCaseForServiceEntries(request: UpdateEncounterCaseForServiceEntriesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UpdateEncounterCaseForServiceEntries, { init , request})
			}

			export function useQueryUpdateEncounterCaseForServiceEntries<TransformedType =any>(payload: UpdateEncounterCaseForServiceEntriesRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateEncounterCaseForServiceEntries, payload],
					queryFn: async ({ signal }) => await updateEncounterCaseForServiceEntries(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateEncounterCaseForServiceEntries(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UpdateEncounterCaseForServiceEntriesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateEncounterCaseForServiceEntries(request),
						retry: false,
						...opts
                });
            }
    
			export async function getDiagnose(request: GetDiagnoseRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetDiagnoseResponse>("POST", LEGACY_TOPIC_GetDiagnose, { init , request})
			}

			export function useQueryGetDiagnose<TransformedType =GetDiagnoseResponse>(payload: GetDiagnoseRequest,ops?: CustomUseQueryOptions<ResponseType<GetDiagnoseResponse>, TransformedType>) {
                return useQuery<ResponseType<GetDiagnoseResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetDiagnose, payload],
					queryFn: async ({ signal }) => await getDiagnose(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetDiagnose(opts?: UseMutationOptions<ResponseType<GetDiagnoseResponse>, ErrorType,GetDiagnoseRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getDiagnose(request),
						retry: false,
						...opts
                });
            }
    
    
    
    
    
    
    
			export async function getById(request: GetByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetByIdResponse>("POST", LEGACY_TOPIC_GetById, { init , request})
			}

			export function useQueryGetById<TransformedType =GetByIdResponse>(payload: GetByIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetByIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetByIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetById, payload],
					queryFn: async ({ signal }) => await getById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetById(opts?: UseMutationOptions<ResponseType<GetByIdResponse>, ErrorType,GetByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getById(request),
						retry: false,
						...opts
                });
            }
    
			export async function getByIds(request: GetByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetResponse>("POST", LEGACY_TOPIC_GetByIds, { init , request})
			}

			export function useQueryGetByIds<TransformedType =GetResponse>(payload: GetByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<GetResponse>, TransformedType>) {
                return useQuery<ResponseType<GetResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetByIds, payload],
					queryFn: async ({ signal }) => await getByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetByIds(opts?: UseMutationOptions<ResponseType<GetResponse>, ErrorType,GetByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getByIds(request),
						retry: false,
						...opts
                });
            }
    
			export async function get(request: GetRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetResponse>("POST", LEGACY_TOPIC_Get, { init , request})
			}

			export function useQueryGet<TransformedType =GetResponse>(payload: GetRequest,ops?: CustomUseQueryOptions<ResponseType<GetResponse>, TransformedType>) {
                return useQuery<ResponseType<GetResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Get, payload],
					queryFn: async ({ signal }) => await get(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGet(opts?: UseMutationOptions<ResponseType<GetResponse>, ErrorType,GetRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await get(request),
						retry: false,
						...opts
                });
            }
    
			export async function create(request: CreateRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CreateResponse>("POST", LEGACY_TOPIC_Create, { init , request})
			}

			export function useQueryCreate<TransformedType =CreateResponse>(payload: CreateRequest,ops?: CustomUseQueryOptions<ResponseType<CreateResponse>, TransformedType>) {
                return useQuery<ResponseType<CreateResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Create, payload],
					queryFn: async ({ signal }) => await create(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreate(opts?: UseMutationOptions<ResponseType<CreateResponse>, ErrorType,CreateRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await create(request),
						retry: false,
						...opts
                });
            }
    
			export async function edit(request: EditRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<EditResponse>("POST", LEGACY_TOPIC_Edit, { init , request})
			}

			export function useQueryEdit<TransformedType =EditResponse>(payload: EditRequest,ops?: CustomUseQueryOptions<ResponseType<EditResponse>, TransformedType>) {
                return useQuery<ResponseType<EditResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Edit, payload],
					queryFn: async ({ signal }) => await edit(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationEdit(opts?: UseMutationOptions<ResponseType<EditResponse>, ErrorType,EditRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await edit(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateMany(request: UpdateManyRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UpdateMany, { init , request})
			}

			export function useQueryUpdateMany<TransformedType =any>(payload: UpdateManyRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateMany, payload],
					queryFn: async ({ signal }) => await updateMany(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateMany(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UpdateManyRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateMany(request),
						retry: false,
						...opts
                });
            }
    
			export async function remove(request: RemoveRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<RemoveResponse>("POST", LEGACY_TOPIC_Remove, { init , request})
			}

			export function useQueryRemove<TransformedType =RemoveResponse>(payload: RemoveRequest,ops?: CustomUseQueryOptions<ResponseType<RemoveResponse>, TransformedType>) {
                return useQuery<ResponseType<RemoveResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Remove, payload],
					queryFn: async ({ signal }) => await remove(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRemove(opts?: UseMutationOptions<ResponseType<RemoveResponse>, ErrorType,RemoveRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await remove(request),
						retry: false,
						...opts
                });
            }
    
			export async function groupByQuarter(request: GroupByQuarterRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GroupByQuarterResponse>("POST", LEGACY_TOPIC_GroupByQuarter, { init , request})
			}

			export function useQueryGroupByQuarter<TransformedType =GroupByQuarterResponse>(payload: GroupByQuarterRequest,ops?: CustomUseQueryOptions<ResponseType<GroupByQuarterResponse>, TransformedType>) {
                return useQuery<ResponseType<GroupByQuarterResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GroupByQuarter, payload],
					queryFn: async ({ signal }) => await groupByQuarter(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGroupByQuarter(opts?: UseMutationOptions<ResponseType<GroupByQuarterResponse>, ErrorType,GroupByQuarterRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await groupByQuarter(request),
						retry: false,
						...opts
                });
            }
    
			export async function updatePrintDate(request: UpdatePrintDateRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<UpdatePrintDateResponse>("POST", LEGACY_TOPIC_UpdatePrintDate, { init , request})
			}

			export function useQueryUpdatePrintDate<TransformedType =UpdatePrintDateResponse>(payload: UpdatePrintDateRequest,ops?: CustomUseQueryOptions<ResponseType<UpdatePrintDateResponse>, TransformedType>) {
                return useQuery<ResponseType<UpdatePrintDateResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdatePrintDate, payload],
					queryFn: async ({ signal }) => await updatePrintDate(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdatePrintDate(opts?: UseMutationOptions<ResponseType<UpdatePrintDateResponse>, ErrorType,UpdatePrintDateRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updatePrintDate(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateSuggestionRuleApplied(request: UpdateSuggestionRuleAppliedRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UpdateSuggestionRuleApplied, { init , request})
			}

			export function useQueryUpdateSuggestionRuleApplied<TransformedType =any>(payload: UpdateSuggestionRuleAppliedRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateSuggestionRuleApplied, payload],
					queryFn: async ({ signal }) => await updateSuggestionRuleApplied(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateSuggestionRuleApplied(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UpdateSuggestionRuleAppliedRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateSuggestionRuleApplied(request),
						retry: false,
						...opts
                });
            }
    
			export async function ignoreSdkrwRule(request: IgnoreSdkrwRuleRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_IgnoreSdkrwRule, { init , request})
			}

			export function useQueryIgnoreSdkrwRule<TransformedType =any>(payload: IgnoreSdkrwRuleRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_IgnoreSdkrwRule, payload],
					queryFn: async ({ signal }) => await ignoreSdkrwRule(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationIgnoreSdkrwRule(opts?: UseMutationOptions<ResponseType<any>, ErrorType,IgnoreSdkrwRuleRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await ignoreSdkrwRule(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteErezept(request: DeleteErezeptRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteErezept, { init , request})
			}

			export function useQueryDeleteErezept<TransformedType =any>(payload: DeleteErezeptRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteErezept, payload],
					queryFn: async ({ signal }) => await deleteErezept(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteErezept(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeleteErezeptRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteErezept(request),
						retry: false,
						...opts
                });
            }
    
			export async function getTherapies(request: GetTherapiesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTherapiesResponse>("POST", LEGACY_TOPIC_GetTherapies, { init , request})
			}

			export function useQueryGetTherapies<TransformedType =GetTherapiesResponse>(payload: GetTherapiesRequest,ops?: CustomUseQueryOptions<ResponseType<GetTherapiesResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTherapiesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTherapies, payload],
					queryFn: async ({ signal }) => await getTherapies(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTherapies(opts?: UseMutationOptions<ResponseType<GetTherapiesResponse>, ErrorType,GetTherapiesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getTherapies(request),
						retry: false,
						...opts
                });
            }
    
			export async function getAmountServiceCode(request: GetAmountServiceCodeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetAmountServiceCodeResponse>("POST", LEGACY_TOPIC_GetAmountServiceCode, { init , request})
			}

			export function useQueryGetAmountServiceCode<TransformedType =GetAmountServiceCodeResponse>(payload: GetAmountServiceCodeRequest,ops?: CustomUseQueryOptions<ResponseType<GetAmountServiceCodeResponse>, TransformedType>) {
                return useQuery<ResponseType<GetAmountServiceCodeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetAmountServiceCode, payload],
					queryFn: async ({ signal }) => await getAmountServiceCode(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetAmountServiceCode(opts?: UseMutationOptions<ResponseType<GetAmountServiceCodeResponse>, ErrorType,GetAmountServiceCodeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getAmountServiceCode(request),
						retry: false,
						...opts
                });
            }
    
			export async function markNotApprovedPyschotherapy(request: MarkNotApprovedPyschotherapyRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_MarkNotApprovedPyschotherapy, { init , request})
			}

			export function useQueryMarkNotApprovedPyschotherapy<TransformedType =any>(payload: MarkNotApprovedPyschotherapyRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkNotApprovedPyschotherapy, payload],
					queryFn: async ({ signal }) => await markNotApprovedPyschotherapy(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkNotApprovedPyschotherapy(opts?: UseMutationOptions<ResponseType<any>, ErrorType,MarkNotApprovedPyschotherapyRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markNotApprovedPyschotherapy(request),
						retry: false,
						...opts
                });
            }
    
			export async function restoreEntryHistory(request: RestoreEntryHistoryRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<RestoreEntryHistoryResponse>("POST", LEGACY_TOPIC_RestoreEntryHistory, { init , request})
			}

			export function useQueryRestoreEntryHistory<TransformedType =RestoreEntryHistoryResponse>(payload: RestoreEntryHistoryRequest,ops?: CustomUseQueryOptions<ResponseType<RestoreEntryHistoryResponse>, TransformedType>) {
                return useQuery<ResponseType<RestoreEntryHistoryResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_RestoreEntryHistory, payload],
					queryFn: async ({ signal }) => await restoreEntryHistory(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRestoreEntryHistory(opts?: UseMutationOptions<ResponseType<RestoreEntryHistoryResponse>, ErrorType,RestoreEntryHistoryRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await restoreEntryHistory(request),
						retry: false,
						...opts
                });
            }
    
			export async function validateDiagnose(request: ValidateDiagnoseRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ValidateDiagnoseResponse>("POST", LEGACY_TOPIC_ValidateDiagnose, { init , request})
			}

			export function useQueryValidateDiagnose<TransformedType =ValidateDiagnoseResponse>(payload: ValidateDiagnoseRequest,ops?: CustomUseQueryOptions<ResponseType<ValidateDiagnoseResponse>, TransformedType>) {
                return useQuery<ResponseType<ValidateDiagnoseResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ValidateDiagnose, payload],
					queryFn: async ({ signal }) => await validateDiagnose(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationValidateDiagnose(opts?: UseMutationOptions<ResponseType<ValidateDiagnoseResponse>, ErrorType,ValidateDiagnoseRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await validateDiagnose(request),
						retry: false,
						...opts
                });
            }
    
			export async function reRunValidateService(request: ReRunValidateServiceRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_ReRunValidateService, { init , request})
			}

			export function useQueryReRunValidateService<TransformedType =any>(payload: ReRunValidateServiceRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ReRunValidateService, payload],
					queryFn: async ({ signal }) => await reRunValidateService(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationReRunValidateService(opts?: UseMutationOptions<ResponseType<any>, ErrorType,ReRunValidateServiceRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await reRunValidateService(request),
						retry: false,
						...opts
                });
            }
    
			export async function getTimelineEntryByIds(request: GetTimelineEntryByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTimelineEntryIdsResponse>("POST", LEGACY_TOPIC_GetTimelineEntryByIds, { init , request})
			}

			export function useQueryGetTimelineEntryByIds<TransformedType =GetTimelineEntryIdsResponse>(payload: GetTimelineEntryByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<GetTimelineEntryIdsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTimelineEntryIdsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTimelineEntryByIds, payload],
					queryFn: async ({ signal }) => await getTimelineEntryByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTimelineEntryByIds(opts?: UseMutationOptions<ResponseType<GetTimelineEntryIdsResponse>, ErrorType,GetTimelineEntryByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getTimelineEntryByIds(request),
						retry: false,
						...opts
                });
            }
    
			export async function getPreParticipationServiceCodes(request: GetPreParticipationServiceCodesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTimelineEntryIdsResponse>("POST", LEGACY_TOPIC_GetPreParticipationServiceCodes, { init , request})
			}

			export function useQueryGetPreParticipationServiceCodes<TransformedType =GetTimelineEntryIdsResponse>(payload: GetPreParticipationServiceCodesRequest,ops?: CustomUseQueryOptions<ResponseType<GetTimelineEntryIdsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTimelineEntryIdsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPreParticipationServiceCodes, payload],
					queryFn: async ({ signal }) => await getPreParticipationServiceCodes(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPreParticipationServiceCodes(opts?: UseMutationOptions<ResponseType<GetTimelineEntryIdsResponse>, ErrorType,GetPreParticipationServiceCodesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPreParticipationServiceCodes(request),
						retry: false,
						...opts
                });
            }
    
			export async function document88130(request: Document88130Request,init?: CustomRequestInit) {
				return await fetchWithHeaders<Document88130Response>("POST", LEGACY_TOPIC_Document88130, { init , request})
			}

			export function useQueryDocument88130<TransformedType =Document88130Response>(payload: Document88130Request,ops?: CustomUseQueryOptions<ResponseType<Document88130Response>, TransformedType>) {
                return useQuery<ResponseType<Document88130Response>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Document88130, payload],
					queryFn: async ({ signal }) => await document88130(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDocument88130(opts?: UseMutationOptions<ResponseType<Document88130Response>, ErrorType,Document88130Request, any>) {
                return useMutation({
						mutationFn: async(request) => await document88130(request),
						retry: false,
						...opts
                });
            }
    
			export async function getPsychotherapyTakeOver(request: GetPsychotherapyTakeOverRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetPsychotherapyTakeOverResponse>("POST", LEGACY_TOPIC_GetPsychotherapyTakeOver, { init , request})
			}

			export function useQueryGetPsychotherapyTakeOver<TransformedType =GetPsychotherapyTakeOverResponse>(payload: GetPsychotherapyTakeOverRequest,ops?: CustomUseQueryOptions<ResponseType<GetPsychotherapyTakeOverResponse>, TransformedType>) {
                return useQuery<ResponseType<GetPsychotherapyTakeOverResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPsychotherapyTakeOver, payload],
					queryFn: async ({ signal }) => await getPsychotherapyTakeOver(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPsychotherapyTakeOver(opts?: UseMutationOptions<ResponseType<GetPsychotherapyTakeOverResponse>, ErrorType,GetPsychotherapyTakeOverRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPsychotherapyTakeOver(request),
						retry: false,
						...opts
                });
            }
    
			export async function takeoverServiceTerminalApproval(request: TakeoverServiceTerminalApprovalRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_TakeoverServiceTerminalApproval, { init , request})
			}

			export function useQueryTakeoverServiceTerminalApproval<TransformedType =any>(payload: TakeoverServiceTerminalApprovalRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_TakeoverServiceTerminalApproval, payload],
					queryFn: async ({ signal }) => await takeoverServiceTerminalApproval(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationTakeoverServiceTerminalApproval(opts?: UseMutationOptions<ResponseType<any>, ErrorType,TakeoverServiceTerminalApprovalRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await takeoverServiceTerminalApproval(request),
						retry: false,
						...opts
                });
            }
    
			export async function getTakeOverDiagnosis(request: GetTakeOverDiagnosisRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTakeOverDiagnosisResponse>("POST", LEGACY_TOPIC_GetTakeOverDiagnosis, { init , request})
			}

			export function useQueryGetTakeOverDiagnosis<TransformedType =GetTakeOverDiagnosisResponse>(payload: GetTakeOverDiagnosisRequest,ops?: CustomUseQueryOptions<ResponseType<GetTakeOverDiagnosisResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTakeOverDiagnosisResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTakeOverDiagnosis, payload],
					queryFn: async ({ signal }) => await getTakeOverDiagnosis(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTakeOverDiagnosis(opts?: UseMutationOptions<ResponseType<GetTakeOverDiagnosisResponse>, ErrorType,GetTakeOverDiagnosisRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getTakeOverDiagnosis(request),
						retry: false,
						...opts
                });
            }
    
			export async function getPsychotherapyBefore2020(request: GetPsychotherapyBefore2020Request,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetPsychotherapyBefore2020Response>("POST", LEGACY_TOPIC_GetPsychotherapyBefore2020, { init , request})
			}

			export function useQueryGetPsychotherapyBefore2020<TransformedType =GetPsychotherapyBefore2020Response>(payload: GetPsychotherapyBefore2020Request,ops?: CustomUseQueryOptions<ResponseType<GetPsychotherapyBefore2020Response>, TransformedType>) {
                return useQuery<ResponseType<GetPsychotherapyBefore2020Response>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPsychotherapyBefore2020, payload],
					queryFn: async ({ signal }) => await getPsychotherapyBefore2020(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPsychotherapyBefore2020(opts?: UseMutationOptions<ResponseType<GetPsychotherapyBefore2020Response>, ErrorType,GetPsychotherapyBefore2020Request, any>) {
                return useMutation({
						mutationFn: async(request) => await getPsychotherapyBefore2020(request),
						retry: false,
						...opts
                });
            }
    
			export async function takeOverDiagnosisWithScheinId(request: TakeOverDiagnosisWithScheinIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_TakeOverDiagnosisWithScheinId, { init , request})
			}

			export function useQueryTakeOverDiagnosisWithScheinId<TransformedType =any>(payload: TakeOverDiagnosisWithScheinIdRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_TakeOverDiagnosisWithScheinId, payload],
					queryFn: async ({ signal }) => await takeOverDiagnosisWithScheinId(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationTakeOverDiagnosisWithScheinId(opts?: UseMutationOptions<ResponseType<any>, ErrorType,TakeOverDiagnosisWithScheinIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await takeOverDiagnosisWithScheinId(request),
						retry: false,
						...opts
                });
            }
    
			export async function markAcceptedByKV(request: MarkAcceptedByKVRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_MarkAcceptedByKV, { init , request})
			}

			export function useQueryMarkAcceptedByKV<TransformedType =any>(payload: MarkAcceptedByKVRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkAcceptedByKV, payload],
					queryFn: async ({ signal }) => await markAcceptedByKV(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkAcceptedByKV(opts?: UseMutationOptions<ResponseType<any>, ErrorType,MarkAcceptedByKVRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markAcceptedByKV(request),
						retry: false,
						...opts
                });
            }
    
			export async function rollbackDocumentTerminateService(request: RollbackDocumentTerminateServiceRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_RollbackDocumentTerminateService, { init , request})
			}

			export function useQueryRollbackDocumentTerminateService<TransformedType =any>(payload: RollbackDocumentTerminateServiceRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_RollbackDocumentTerminateService, payload],
					queryFn: async ({ signal }) => await rollbackDocumentTerminateService(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRollbackDocumentTerminateService(opts?: UseMutationOptions<ResponseType<any>, ErrorType,RollbackDocumentTerminateServiceRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await rollbackDocumentTerminateService(request),
						retry: false,
						...opts
                });
            }
    
			export async function getLastDocumentedQuarter(request: GetLastDocumentedQuarterRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetLastDocumentedQuarterResponse>("POST", LEGACY_TOPIC_GetLastDocumentedQuarter, { init , request})
			}

			export function useQueryGetLastDocumentedQuarter<TransformedType =GetLastDocumentedQuarterResponse>(payload: GetLastDocumentedQuarterRequest,ops?: CustomUseQueryOptions<ResponseType<GetLastDocumentedQuarterResponse>, TransformedType>) {
                return useQuery<ResponseType<GetLastDocumentedQuarterResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetLastDocumentedQuarter, payload],
					queryFn: async ({ signal }) => await getLastDocumentedQuarter(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetLastDocumentedQuarter(opts?: UseMutationOptions<ResponseType<GetLastDocumentedQuarterResponse>, ErrorType,GetLastDocumentedQuarterRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getLastDocumentedQuarter(request),
						retry: false,
						...opts
                });
            }
    
			export async function getTimelineByEnrollmentId(request: GetTimelineByEnrollmentIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTimelineByEnrollmentIdResponse>("POST", LEGACY_TOPIC_GetTimelineByEnrollmentId, { init , request})
			}

			export function useQueryGetTimelineByEnrollmentId<TransformedType =GetTimelineByEnrollmentIdResponse>(payload: GetTimelineByEnrollmentIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetTimelineByEnrollmentIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTimelineByEnrollmentIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTimelineByEnrollmentId, payload],
					queryFn: async ({ signal }) => await getTimelineByEnrollmentId(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTimelineByEnrollmentId(opts?: UseMutationOptions<ResponseType<GetTimelineByEnrollmentIdResponse>, ErrorType,GetTimelineByEnrollmentIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getTimelineByEnrollmentId(request),
						retry: false,
						...opts
                });
            }
    
			export async function reSendEABMail(request: ReSendEABMailRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_ReSendEABMail, { init , request})
			}

			export function useQueryReSendEABMail<TransformedType =any>(payload: ReSendEABMailRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ReSendEABMail, payload],
					queryFn: async ({ signal }) => await reSendEABMail(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationReSendEABMail(opts?: UseMutationOptions<ResponseType<any>, ErrorType,ReSendEABMailRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await reSendEABMail(request),
						retry: false,
						...opts
                });
            }
    
			export async function documentEABServiceCode(request: DocumentEABServiceCodeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<DocumentEABServiceCodeResponse>("POST", LEGACY_TOPIC_DocumentEABServiceCode, { init , request})
			}

			export function useQueryDocumentEABServiceCode<TransformedType =DocumentEABServiceCodeResponse>(payload: DocumentEABServiceCodeRequest,ops?: CustomUseQueryOptions<ResponseType<DocumentEABServiceCodeResponse>, TransformedType>) {
                return useQuery<ResponseType<DocumentEABServiceCodeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DocumentEABServiceCode, payload],
					queryFn: async ({ signal }) => await documentEABServiceCode(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDocumentEABServiceCode(opts?: UseMutationOptions<ResponseType<DocumentEABServiceCodeResponse>, ErrorType,DocumentEABServiceCodeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await documentEABServiceCode(request),
						retry: false,
						...opts
                });
            }
    
			export async function findLatestTimelineEntry(request: FindLatesTimelineEntryRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<FindLatesTimelineEntryResponse>("POST", LEGACY_TOPIC_FindLatestTimelineEntry, { init , request})
			}

			export function useQueryFindLatestTimelineEntry<TransformedType =FindLatesTimelineEntryResponse>(payload: FindLatesTimelineEntryRequest,ops?: CustomUseQueryOptions<ResponseType<FindLatesTimelineEntryResponse>, TransformedType>) {
                return useQuery<ResponseType<FindLatesTimelineEntryResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_FindLatestTimelineEntry, payload],
					queryFn: async ({ signal }) => await findLatestTimelineEntry(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationFindLatestTimelineEntry(opts?: UseMutationOptions<ResponseType<FindLatesTimelineEntryResponse>, ErrorType,FindLatesTimelineEntryRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await findLatestTimelineEntry(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkIsVSST785(request: CheckIsVSST785Request,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckIsVSST785Response>("POST", LEGACY_TOPIC_CheckIsVSST785, { init , request})
			}

			export function useQueryCheckIsVSST785<TransformedType =CheckIsVSST785Response>(payload: CheckIsVSST785Request,ops?: CustomUseQueryOptions<ResponseType<CheckIsVSST785Response>, TransformedType>) {
                return useQuery<ResponseType<CheckIsVSST785Response>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckIsVSST785, payload],
					queryFn: async ({ signal }) => await checkIsVSST785(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckIsVSST785(opts?: UseMutationOptions<ResponseType<CheckIsVSST785Response>, ErrorType,CheckIsVSST785Request, any>) {
                return useMutation({
						mutationFn: async(request) => await checkIsVSST785(request),
						retry: false,
						...opts
                });
            }
    
			export async function getDoctorLetterById(request: GetDoctorLetterByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetDoctorLetterByIdResponse>("POST", LEGACY_TOPIC_GetDoctorLetterById, { init , request})
			}

			export function useQueryGetDoctorLetterById<TransformedType =GetDoctorLetterByIdResponse>(payload: GetDoctorLetterByIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetDoctorLetterByIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetDoctorLetterByIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetDoctorLetterById, payload],
					queryFn: async ({ signal }) => await getDoctorLetterById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetDoctorLetterById(opts?: UseMutationOptions<ResponseType<GetDoctorLetterByIdResponse>, ErrorType,GetDoctorLetterByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getDoctorLetterById(request),
						retry: false,
						...opts
                });
            }
    
			export async function getActionChainDiagnoseByCodes(request: GetActionChainDiagnoseByCodesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetActionChainDiagnoseByCodesResponse>("POST", LEGACY_TOPIC_GetActionChainDiagnoseByCodes, { init , request})
			}

			export function useQueryGetActionChainDiagnoseByCodes<TransformedType =GetActionChainDiagnoseByCodesResponse>(payload: GetActionChainDiagnoseByCodesRequest,ops?: CustomUseQueryOptions<ResponseType<GetActionChainDiagnoseByCodesResponse>, TransformedType>) {
                return useQuery<ResponseType<GetActionChainDiagnoseByCodesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetActionChainDiagnoseByCodes, payload],
					queryFn: async ({ signal }) => await getActionChainDiagnoseByCodes(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetActionChainDiagnoseByCodes(opts?: UseMutationOptions<ResponseType<GetActionChainDiagnoseByCodesResponse>, ErrorType,GetActionChainDiagnoseByCodesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getActionChainDiagnoseByCodes(request),
						retry: false,
						...opts
                });
            }
    

