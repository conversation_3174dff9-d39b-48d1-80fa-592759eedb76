/* eslint-disable */
// This code was autogenerated from service/domains/common.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as error_code from "./error_code"



// Type definitions
		export interface Pagination {
				offset: number
				max: number
				sortBy: string
				order: string
		}
	

		export interface PaginationRequest {
				page: number
				pageSize: number
				sortBy: string
				order: Order
		}
	

		export interface PaginationResponse {
				total: number
				totalPage: number
		}
	

		export interface YearQuarter {
				year: number
				quarter: number
		}
	

		export interface Sentry {
				dns: string
		}
	

		export interface Configs {
				sentry: Sentry
		}
	

		export interface AuditLog {
				addedUserId?: string
				addedDate?: number
				lastUpdatedUserId?: string
				lastUpdatedDate?: number
		}
	

		export interface BillingInfo {
				billingStatus?: BillingStatus
				submittedDate?: number
				lastUpdatedUserId?: string
		}
	

		export interface ScheinWithMainGroup {
				scheinId: string
				group: MainGroup
		}
	

		export interface FieldError {
				field: string
				fieldName: string
				message: string
				validationType: ValidationType
				errorCode: string
				metaData: {[key:string]:string}
				displayType?: DisplayType
		}
	

		export interface BankInformation {
				bankName?: string
				bankAccount?: string
				iban?: string
				bic?: string
		}
	

		export interface DateRange {
				startDate: number
				endDate?: number
		}
	

		export interface FileInfo {
				fileName: string
				bucketName: string
		}
	

		export interface EventDownload {
				fileInfo: FileInfo
				commonException?: error_code.CommonException
		}
	

		export interface Highlight {
				matchedTokens: Array<string>
				snippet?: string
		}
	


// enum definitions
    export enum Order {
        ASC = "ASC",
        DESC = "DESC",
    }

    export enum SortBy {
        SortBy_MedicalData_CreatedAt = "SortBy_MedicalData_CreatedAt",
        SortBy_MedicalData_UpdatedAt = "SortBy_MedicalData_UpdatedAt",
        SortBy_MedicalData_CreatedBy = "SortBy_MedicalData_CreatedBy",
        SortBy_MedicalData_UpdatedBy = "SortBy_MedicalData_UpdatedBy",
    }

    export enum UserType {
        DOCTOR = "DOCTOR",
        MFA = "MFA",
        MANAGER = "MANAGER",
        CEO = "CEO",
    }

    export enum ContractType {
        ContractType_HouseDoctorCare = "HausarztzentrierteVersorgung",
        ContractType_SpecialistCare = "FachaerztlicheVersorgung",
        ContractType_KvContract = "KvVertrag",
        ContractType_IntegratedCareHouseDoctor = "IntegrierteVersorgungHausarzt",
        ContractType_IntegratedCareSpecialist = "IntegrierteVersorgungFacharzt",
    }

    export enum BillingStatus {
        UnBilled = "UnBilled",
        Billed = "Billed",
        PreParticipationBilled = "PreParticipationBilled",
        BilledDiagnose = "BilledDiagnose",
        BilledPrescription = "BilledPrescription",
    }

    export enum PatientParticipationStatus {
        PatientParticipation_Requested = "REQUESTED",
        PatientParticipation_Rejected = "REJECTED",
        PatientParticipation_Active = "ACTIVE",
        PatientParticipation_Cancelled = "CANCELLED",
        PatientParticipation_Terminated = "TERMINATED",
        PatientParticipation_Faulty = "FAULTY",
    }

    export enum InsuranceType {
        PrivatVersichert = "PrivatVersichert",
        GesetzlichVersichert = "GesetzlichVersichert",
    }

    export enum MainGroup {
        HZV = "HZV",
        FAV = "FAV",
        KV = "KV",
        BG = "BG",
        PRIVATE = "PRIVATE",
        IGEL = "IGEL",
    }

    export enum FieldErrorCode {
        FieldErrorCode_Required = "Required",
        FieldErrorCode_MaxLength = "MaxLength",
        FieldErrorCode_MinLength = "MinLength",
        FieldErrorCode_IsExist = "IsExist",
        FieldErrorCode_RequiredLength = "RequiredLength",
        FieldErrorCode_MustNilOrEmpty = "MustNilOrEmpty",
        FieldErrorCode_OnlyOneFieldAllowed = "OnlyOneFieldAllowed",
        FieldErrorCode_InvalidKTAB = "InvalidKtab",
        FieldErrorCode_SubGroup28NotAllowESS = "SubGroup28NotAllowESS",
        FieldErrorCode_InsuranceStatusNotFoundIn9407 = "InsuranceStatusNotFoundIn9407",
        FieldErrorCode_SubGroupNotFoundIn9406 = "SubGroupNotFoundIn9406",
    }

    export enum ValidationType {
        ValidationType_Error = "Error",
        ValidationType_Warning = "Warning",
        ValidationType_Notice = "Notice",
    }

    export enum DisplayType {
        DisplayType_Inline = "Inline",
        DisplayType_Toast = "Toast",
        DisplayType_Group = "Group",
    }

    export enum EuropeanHealthInsuranceStatus {
        EuropeanHealthInsuranceStatus_Saved = "Saved",
        EuropeanHealthInsuranceStatus_Printed = "Printed",
    }

    export enum EHKSType {
        EHKSType_Dermatologist = "dermatologist",
        EHKSType_NonDermatologist = "nonDermatologist",
    }

    export enum EventType {
        EventType_Created = "EventType_Created",
        EventType_Update = "EventType_Update",
        EventType_Delete = "EventType_Delete",
    }

    export enum EmployeeStatus {
        EmployeeStatus_UNSPECIFIED = "USER_STATE_UNSPECIFIED",
        EmployeeStatus_ACTIVE = "USER_STATE_ACTIVE",
        EmployeeStatus_INACTIVE = "USER_STATE_INACTIVE",
        EmployeeStatus_DELETED = "USER_STATE_DELETED",
        EmployeeStatus_LOCKED = "USER_STATE_LOCKED",
        EmployeeStatus_SUSPEND = "USER_STATE_SUSPEND",
        EmployeeStatus_INITIAL = "USER_STATE_INITIAL",
    }


// method name convention const

// Define constants
// method name convention const
