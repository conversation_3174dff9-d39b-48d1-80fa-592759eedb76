/* eslint-disable */
// This code was autogenerated from app/admin/ti_connector.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as ti_connector_common from "./ti_connector_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface ConnectionStatus {
				severity: ConnectionStatusEnum
				validFrom: number
				type: string
				eventStatus: string
		}
	

		export interface GetTIConnectorsResponse {
				tIConnectors: Array<ti_connector_common.TIConnectorListItem>
		}
	

		export interface SaveTIConnectorRequest {
				iD?: string
				tIConnector: ti_connector_common.TIConnector
		}
	

		export interface TIConnectorRequest {
				iD: string
		}
	

		export interface ViewConnectorStatusResponse {
				tIConnector: ti_connector_common.TIConnector
				manufacturer: string
				connectorTypeVersion: string
				hardWareVersion: string
				firmWareVersion: string
				tiConnection: string
				sisConnection: string
				connectionStatuses: Array<ConnectionStatus>
				unsupportedVersionServices: Array<string>
				productName: string
		}
	

		export interface CheckTIConnectorStatusRequest {
				ids: Array<string>
		}
	

		export interface ConnectorStatus {
				id: string
				status: string
				expiredDate: number
		}
	

		export interface CheckTIConnectorStatusResponse {
				connectorStatuses: Array<ConnectorStatus>
		}
	


// enum definitions
    export enum ConnectionStatusEnum {
        ConnectionStatusEnum_Info = "Info",
        ConnectionStatusEnum_Error = "Error",
        ConnectionStatusEnum_Warning = "Warning",
        ConnectionStatusEnum_Fatal = "Fatal",
    }


// method name convention const
		export const EVENT_GetTIConnectors = "api.app.admin.TIConnectorApp.GetTIConnectors";
		export const EVENT_GetTIConnectors_Response = "api.app.admin.TIConnectorApp.GetTIConnectors.Response";
		export const EVENT_SaveTIConnector = "api.app.admin.TIConnectorApp.SaveTIConnector";
		export const EVENT_SaveTIConnector_Response = "api.app.admin.TIConnectorApp.SaveTIConnector.Response";
		export const EVENT_RemoveTIConnector = "api.app.admin.TIConnectorApp.RemoveTIConnector";
		export const EVENT_RemoveTIConnector_Response = "api.app.admin.TIConnectorApp.RemoveTIConnector.Response";
		export const EVENT_ViewConnectorStatus = "api.app.admin.TIConnectorApp.ViewConnectorStatus";
		export const EVENT_ViewConnectorStatus_Response = "api.app.admin.TIConnectorApp.ViewConnectorStatus.Response";
		export const EVENT_CheckTIConnectorStatus = "api.app.admin.TIConnectorApp.CheckTIConnectorStatus";
		export const EVENT_CheckTIConnectorStatus_Response = "api.app.admin.TIConnectorApp.CheckTIConnectorStatus.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetTIConnectors = "/api/app/admin/ti/connector/getTIConnectors";
        export const LEGACY_TOPIC_SaveTIConnector = "/api/app/admin/ti/connector/saveTIConnector";
        export const LEGACY_TOPIC_RemoveTIConnector = "/api/app/admin/ti/connector/removeTIConnector";
        export const LEGACY_TOPIC_ViewConnectorStatus = "/api/app/admin/ti/connector/viewConnectorStatus";
        export const LEGACY_TOPIC_CheckTIConnectorStatus = "/api/app/admin/ti/connector/checkTIConnectorStatus";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getTIConnectors(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTIConnectorsResponse>("POST", LEGACY_TOPIC_GetTIConnectors, { init })
			}

			export function useQueryGetTIConnectors<TransformedType =GetTIConnectorsResponse>(ops?: CustomUseQueryOptions<ResponseType<GetTIConnectorsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTIConnectorsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTIConnectors],
					queryFn: async ({ signal }) => await getTIConnectors({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTIConnectors(opts?: UseMutationOptions<ResponseType<GetTIConnectorsResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getTIConnectors(),
						retry: false,
						...opts
                });
            }
    
			export async function saveTIConnector(request: SaveTIConnectorRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_SaveTIConnector, { init , request})
			}

			export function useQuerySaveTIConnector<TransformedType =any>(payload: SaveTIConnectorRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SaveTIConnector, payload],
					queryFn: async ({ signal }) => await saveTIConnector(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSaveTIConnector(opts?: UseMutationOptions<ResponseType<any>, ErrorType,SaveTIConnectorRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await saveTIConnector(request),
						retry: false,
						...opts
                });
            }
    
			export async function removeTIConnector(request: TIConnectorRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_RemoveTIConnector, { init , request})
			}

			export function useQueryRemoveTIConnector<TransformedType =any>(payload: TIConnectorRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_RemoveTIConnector, payload],
					queryFn: async ({ signal }) => await removeTIConnector(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRemoveTIConnector(opts?: UseMutationOptions<ResponseType<any>, ErrorType,TIConnectorRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await removeTIConnector(request),
						retry: false,
						...opts
                });
            }
    
			export async function viewConnectorStatus(request: TIConnectorRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ViewConnectorStatusResponse>("POST", LEGACY_TOPIC_ViewConnectorStatus, { init , request})
			}

			export function useQueryViewConnectorStatus<TransformedType =ViewConnectorStatusResponse>(payload: TIConnectorRequest,ops?: CustomUseQueryOptions<ResponseType<ViewConnectorStatusResponse>, TransformedType>) {
                return useQuery<ResponseType<ViewConnectorStatusResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ViewConnectorStatus, payload],
					queryFn: async ({ signal }) => await viewConnectorStatus(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationViewConnectorStatus(opts?: UseMutationOptions<ResponseType<ViewConnectorStatusResponse>, ErrorType,TIConnectorRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await viewConnectorStatus(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkTIConnectorStatus(request: CheckTIConnectorStatusRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckTIConnectorStatusResponse>("POST", LEGACY_TOPIC_CheckTIConnectorStatus, { init , request})
			}

			export function useQueryCheckTIConnectorStatus<TransformedType =CheckTIConnectorStatusResponse>(payload: CheckTIConnectorStatusRequest,ops?: CustomUseQueryOptions<ResponseType<CheckTIConnectorStatusResponse>, TransformedType>) {
                return useQuery<ResponseType<CheckTIConnectorStatusResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckTIConnectorStatus, payload],
					queryFn: async ({ signal }) => await checkTIConnectorStatus(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckTIConnectorStatus(opts?: UseMutationOptions<ResponseType<CheckTIConnectorStatusResponse>, ErrorType,CheckTIConnectorStatusRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkTIConnectorStatus(request),
						retry: false,
						...opts
                });
            }
    

