/* eslint-disable */
// This code was autogenerated from app/mvz/billing_edoku.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common from "./billing_edoku_common"
import * as billing_history_common from "./billing_history_common"
import * as common1 from "./common"
import * as common2 from "./edmp_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface GetValidationListRequest {
				quarter: common1.YearQuarter
				bsnrId: string
				documentType: string
				openPreviousQuarter: boolean
		}
	

		export interface GetValidationListResponse {
				billingValidationList: Array<common.BillingValidationListModel>
				totalPatient: number
		}
	

		export interface TroubleshootRequest {
		}
	

		export interface TroubleshootResponse {
		}
	

		export interface CreateBillingRequest {
				documentIds: Array<string>
				quarter: common1.YearQuarter
				bsnr: string
				dMPValue: common2.DMPValueEnum
				isOpenPreviousQuarter: boolean
		}
	

		export interface CreateBillingResponse {
				status: boolean
				dMPBillingHistoryId: string
				dMPBillingFieldsValidationResults: Array<common2.DMPBillingFieldsValidationResult>
		}
	

		export interface GetBillingSummaryRequest {
		}
	

		export interface GetBillingSummaryResponse {
		}
	

		export interface PrepareForShippingRequest {
				billingHistoryId: string
		}
	

		export interface SendMailRequest {
				billingHistoryId: string
				senderMailSettingId: string
				testMailTo?: string
		}
	

		export interface DownloadBillingFileRequest {
		}
	

		export interface DownloadBillingFileResponse {
		}
	

		export interface UndoSubmissionRequest {
				billingHistoryId: string
		}
	

		export interface GetDispatchListRequest {
				query?: string
		}
	

		export interface GetDispatchListResponse {
				billingHistories: Array<common.BillingHistoryModel>
		}
	

		export interface GetEdokuBillingSelectionResponse {
				quarters: Array<common1.YearQuarter>
				bsnrs: Array<billing_history_common.BSNR>
				documentTypes: Array<string>
		}
	

		export interface CheckForValidationRequest {
				documentIds: Array<string>
				quarter: common1.YearQuarter
				bsnr: string
		}
	

		export interface CheckForValidationResponse {
				status: boolean
				dMPBillingFieldsValidationResults: Array<common2.DMPBillingFieldsValidationResult>
		}
	

		export interface GetBillingHistoryRequest {
				billingHistoryId: string
		}
	

		export interface GetBillingHistoryResponse {
				billingHistory: common.BillingHistoryModel
		}
	

		export interface EventBillingEDokuStatusChanged {
				billingHistoryId: string
				status: common.BillingStatus
		}
	

		export interface GetEdokuDocumentByIdsRequest {
				documentIds: Array<string>
		}
	

		export interface GetEdokuDocumentByIdsResponse {
				documents: Array<common2.DocumentationOverview>
		}
	


// enum definitions

// method name convention const
		export const EVENT_Troubleshoot = "api.app.mvz.BillingEDokuApp.Troubleshoot";
		export const EVENT_Troubleshoot_Response = "api.app.mvz.BillingEDokuApp.Troubleshoot.Response";
		export const EVENT_GetValidationList = "api.app.mvz.BillingEDokuApp.GetValidationList";
		export const EVENT_GetValidationList_Response = "api.app.mvz.BillingEDokuApp.GetValidationList.Response";
		export const EVENT_CreateBilling = "api.app.mvz.BillingEDokuApp.CreateBilling";
		export const EVENT_CreateBilling_Response = "api.app.mvz.BillingEDokuApp.CreateBilling.Response";
		export const EVENT_GetBillingSummary = "api.app.mvz.BillingEDokuApp.GetBillingSummary";
		export const EVENT_GetBillingSummary_Response = "api.app.mvz.BillingEDokuApp.GetBillingSummary.Response";
		export const EVENT_PrepareForShipping = "api.app.mvz.BillingEDokuApp.PrepareForShipping";
		export const EVENT_PrepareForShipping_Response = "api.app.mvz.BillingEDokuApp.PrepareForShipping.Response";
		export const EVENT_SendMail = "api.app.mvz.BillingEDokuApp.SendMail";
		export const EVENT_SendMail_Response = "api.app.mvz.BillingEDokuApp.SendMail.Response";
		export const EVENT_DownloadBillingFile = "api.app.mvz.BillingEDokuApp.DownloadBillingFile";
		export const EVENT_DownloadBillingFile_Response = "api.app.mvz.BillingEDokuApp.DownloadBillingFile.Response";
		export const EVENT_UndoSubmission = "api.app.mvz.BillingEDokuApp.UndoSubmission";
		export const EVENT_UndoSubmission_Response = "api.app.mvz.BillingEDokuApp.UndoSubmission.Response";
		export const EVENT_GetDispatchList = "api.app.mvz.BillingEDokuApp.GetDispatchList";
		export const EVENT_GetDispatchList_Response = "api.app.mvz.BillingEDokuApp.GetDispatchList.Response";
		export const EVENT_GetEdokuBillingSelection = "api.app.mvz.BillingEDokuApp.GetEdokuBillingSelection";
		export const EVENT_GetEdokuBillingSelection_Response = "api.app.mvz.BillingEDokuApp.GetEdokuBillingSelection.Response";
		export const EVENT_CheckForValidation = "api.app.mvz.BillingEDokuApp.CheckForValidation";
		export const EVENT_CheckForValidation_Response = "api.app.mvz.BillingEDokuApp.CheckForValidation.Response";
		export const EVENT_GetBillingHistory = "api.app.mvz.BillingEDokuApp.GetBillingHistory";
		export const EVENT_GetBillingHistory_Response = "api.app.mvz.BillingEDokuApp.GetBillingHistory.Response";
		export const EVENT_GetEdokuDocumentByIds = "api.app.mvz.BillingEDokuApp.GetEdokuDocumentByIds";
		export const EVENT_GetEdokuDocumentByIds_Response = "api.app.mvz.BillingEDokuApp.GetEdokuDocumentByIds.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_Troubleshoot = "/api/app/mvz/billing/e/doku/troubleshoot";
        export const LEGACY_TOPIC_GetValidationList = "/api/app/mvz/billing/e/doku/getValidationList";
        export const LEGACY_TOPIC_CreateBilling = "/api/app/mvz/billing/e/doku/createBilling";
        export const LEGACY_TOPIC_GetBillingSummary = "/api/app/mvz/billing/e/doku/getBillingSummary";
        export const LEGACY_TOPIC_PrepareForShipping = "/api/app/mvz/billing/e/doku/prepareForShipping";
        export const LEGACY_TOPIC_SendMail = "/api/app/mvz/billing/e/doku/sendMail";
        export const LEGACY_TOPIC_DownloadBillingFile = "/api/app/mvz/billing/e/doku/downloadBillingFile";
        export const LEGACY_TOPIC_UndoSubmission = "/api/app/mvz/billing/e/doku/undoSubmission";
        export const LEGACY_TOPIC_GetDispatchList = "/api/app/mvz/billing/e/doku/getDispatchList";
        export const LEGACY_TOPIC_GetEdokuBillingSelection = "/api/app/mvz/billing/e/doku/getEdokuBillingSelection";
        export const LEGACY_TOPIC_CheckForValidation = "/api/app/mvz/billing/e/doku/checkForValidation";
        export const LEGACY_TOPIC_GetBillingHistory = "/api/app/mvz/billing/e/doku/getBillingHistory";
        export const LEGACY_TOPIC_GetEdokuDocumentByIds = "/api/app/mvz/billing/e/doku/getEdokuDocumentByIds";


// Define action methods and their listener -----------------------------------------------------------------
			export async function troubleshoot(request: TroubleshootRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<TroubleshootResponse>("POST", LEGACY_TOPIC_Troubleshoot, { init , request})
			}

			export function useQueryTroubleshoot<TransformedType =TroubleshootResponse>(payload: TroubleshootRequest,ops?: CustomUseQueryOptions<ResponseType<TroubleshootResponse>, TransformedType>) {
                return useQuery<ResponseType<TroubleshootResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Troubleshoot, payload],
					queryFn: async ({ signal }) => await troubleshoot(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationTroubleshoot(opts?: UseMutationOptions<ResponseType<TroubleshootResponse>, ErrorType,TroubleshootRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await troubleshoot(request),
						retry: false,
						...opts
                });
            }
    
			export async function getValidationList(request: GetValidationListRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetValidationListResponse>("POST", LEGACY_TOPIC_GetValidationList, { init , request})
			}

			export function useQueryGetValidationList<TransformedType =GetValidationListResponse>(payload: GetValidationListRequest,ops?: CustomUseQueryOptions<ResponseType<GetValidationListResponse>, TransformedType>) {
                return useQuery<ResponseType<GetValidationListResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetValidationList, payload],
					queryFn: async ({ signal }) => await getValidationList(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetValidationList(opts?: UseMutationOptions<ResponseType<GetValidationListResponse>, ErrorType,GetValidationListRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getValidationList(request),
						retry: false,
						...opts
                });
            }
    
			export async function createBilling(request: CreateBillingRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CreateBillingResponse>("POST", LEGACY_TOPIC_CreateBilling, { init , request})
			}

			export function useQueryCreateBilling<TransformedType =CreateBillingResponse>(payload: CreateBillingRequest,ops?: CustomUseQueryOptions<ResponseType<CreateBillingResponse>, TransformedType>) {
                return useQuery<ResponseType<CreateBillingResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateBilling, payload],
					queryFn: async ({ signal }) => await createBilling(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateBilling(opts?: UseMutationOptions<ResponseType<CreateBillingResponse>, ErrorType,CreateBillingRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createBilling(request),
						retry: false,
						...opts
                });
            }
    
			export async function getBillingSummary(request: GetBillingSummaryRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetBillingSummaryResponse>("POST", LEGACY_TOPIC_GetBillingSummary, { init , request})
			}

			export function useQueryGetBillingSummary<TransformedType =GetBillingSummaryResponse>(payload: GetBillingSummaryRequest,ops?: CustomUseQueryOptions<ResponseType<GetBillingSummaryResponse>, TransformedType>) {
                return useQuery<ResponseType<GetBillingSummaryResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetBillingSummary, payload],
					queryFn: async ({ signal }) => await getBillingSummary(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetBillingSummary(opts?: UseMutationOptions<ResponseType<GetBillingSummaryResponse>, ErrorType,GetBillingSummaryRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getBillingSummary(request),
						retry: false,
						...opts
                });
            }
    
			export async function prepareForShipping(request: PrepareForShippingRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_PrepareForShipping, { init , request})
			}

			export function useQueryPrepareForShipping<TransformedType =any>(payload: PrepareForShippingRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_PrepareForShipping, payload],
					queryFn: async ({ signal }) => await prepareForShipping(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationPrepareForShipping(opts?: UseMutationOptions<ResponseType<any>, ErrorType,PrepareForShippingRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await prepareForShipping(request),
						retry: false,
						...opts
                });
            }
    
			export async function sendMail(request: SendMailRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_SendMail, { init , request})
			}

			export function useQuerySendMail<TransformedType =any>(payload: SendMailRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SendMail, payload],
					queryFn: async ({ signal }) => await sendMail(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSendMail(opts?: UseMutationOptions<ResponseType<any>, ErrorType,SendMailRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await sendMail(request),
						retry: false,
						...opts
                });
            }
    
			export async function downloadBillingFile(request: DownloadBillingFileRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<DownloadBillingFileResponse>("POST", LEGACY_TOPIC_DownloadBillingFile, { init , request})
			}

			export function useQueryDownloadBillingFile<TransformedType =DownloadBillingFileResponse>(payload: DownloadBillingFileRequest,ops?: CustomUseQueryOptions<ResponseType<DownloadBillingFileResponse>, TransformedType>) {
                return useQuery<ResponseType<DownloadBillingFileResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DownloadBillingFile, payload],
					queryFn: async ({ signal }) => await downloadBillingFile(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDownloadBillingFile(opts?: UseMutationOptions<ResponseType<DownloadBillingFileResponse>, ErrorType,DownloadBillingFileRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await downloadBillingFile(request),
						retry: false,
						...opts
                });
            }
    
			export async function undoSubmission(request: UndoSubmissionRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UndoSubmission, { init , request})
			}

			export function useQueryUndoSubmission<TransformedType =any>(payload: UndoSubmissionRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UndoSubmission, payload],
					queryFn: async ({ signal }) => await undoSubmission(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUndoSubmission(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UndoSubmissionRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await undoSubmission(request),
						retry: false,
						...opts
                });
            }
    
			export async function getDispatchList(request: GetDispatchListRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetDispatchListResponse>("POST", LEGACY_TOPIC_GetDispatchList, { init , request})
			}

			export function useQueryGetDispatchList<TransformedType =GetDispatchListResponse>(payload: GetDispatchListRequest,ops?: CustomUseQueryOptions<ResponseType<GetDispatchListResponse>, TransformedType>) {
                return useQuery<ResponseType<GetDispatchListResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetDispatchList, payload],
					queryFn: async ({ signal }) => await getDispatchList(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetDispatchList(opts?: UseMutationOptions<ResponseType<GetDispatchListResponse>, ErrorType,GetDispatchListRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getDispatchList(request),
						retry: false,
						...opts
                });
            }
    
			export async function getEdokuBillingSelection(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetEdokuBillingSelectionResponse>("POST", LEGACY_TOPIC_GetEdokuBillingSelection, { init })
			}

			export function useQueryGetEdokuBillingSelection<TransformedType =GetEdokuBillingSelectionResponse>(ops?: CustomUseQueryOptions<ResponseType<GetEdokuBillingSelectionResponse>, TransformedType>) {
                return useQuery<ResponseType<GetEdokuBillingSelectionResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetEdokuBillingSelection],
					queryFn: async ({ signal }) => await getEdokuBillingSelection({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetEdokuBillingSelection(opts?: UseMutationOptions<ResponseType<GetEdokuBillingSelectionResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getEdokuBillingSelection(),
						retry: false,
						...opts
                });
            }
    
			export async function checkForValidation(request: CheckForValidationRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckForValidationResponse>("POST", LEGACY_TOPIC_CheckForValidation, { init , request})
			}

			export function useQueryCheckForValidation<TransformedType =CheckForValidationResponse>(payload: CheckForValidationRequest,ops?: CustomUseQueryOptions<ResponseType<CheckForValidationResponse>, TransformedType>) {
                return useQuery<ResponseType<CheckForValidationResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckForValidation, payload],
					queryFn: async ({ signal }) => await checkForValidation(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckForValidation(opts?: UseMutationOptions<ResponseType<CheckForValidationResponse>, ErrorType,CheckForValidationRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkForValidation(request),
						retry: false,
						...opts
                });
            }
    
			export async function getBillingHistory(request: GetBillingHistoryRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetBillingHistoryResponse>("POST", LEGACY_TOPIC_GetBillingHistory, { init , request})
			}

			export function useQueryGetBillingHistory<TransformedType =GetBillingHistoryResponse>(payload: GetBillingHistoryRequest,ops?: CustomUseQueryOptions<ResponseType<GetBillingHistoryResponse>, TransformedType>) {
                return useQuery<ResponseType<GetBillingHistoryResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetBillingHistory, payload],
					queryFn: async ({ signal }) => await getBillingHistory(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetBillingHistory(opts?: UseMutationOptions<ResponseType<GetBillingHistoryResponse>, ErrorType,GetBillingHistoryRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getBillingHistory(request),
						retry: false,
						...opts
                });
            }
    
			export async function getEdokuDocumentByIds(request: GetEdokuDocumentByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetEdokuDocumentByIdsResponse>("POST", LEGACY_TOPIC_GetEdokuDocumentByIds, { init , request})
			}

			export function useQueryGetEdokuDocumentByIds<TransformedType =GetEdokuDocumentByIdsResponse>(payload: GetEdokuDocumentByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<GetEdokuDocumentByIdsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetEdokuDocumentByIdsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetEdokuDocumentByIds, payload],
					queryFn: async ({ signal }) => await getEdokuDocumentByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetEdokuDocumentByIds(opts?: UseMutationOptions<ResponseType<GetEdokuDocumentByIdsResponse>, ErrorType,GetEdokuDocumentByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getEdokuDocumentByIds(request),
						retry: false,
						...opts
                });
            }
    

