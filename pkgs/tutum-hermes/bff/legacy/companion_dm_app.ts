/* eslint-disable */
// This code was autogenerated from app/companion/companion_dm.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface GetFolderStateRequest {
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				settingId: string
				recursive: boolean
		}
	

		export interface GetFolderStateResponse {
				folderState: {[key:string]:number}
		}
	

		export interface FileUploadInfo {
				filePath: string
				presignedUrl: string
		}
	

		export interface UploadFilesRequest {
				listFileUploadInfo: Array<FileUploadInfo>
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				deleteAfterUpload: boolean
		}
	

		export interface WatchFolderRequest {
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				settingId: string
				recursive: boolean
		}
	

		export interface ExportGdtDocumentRequest {
				presignUrl: string
				fileName: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				externalAppFilePath?: string
		}
	

		export interface CheckGdtImportDocumentRequest {
				filename: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				lastModifyTime: number
				settingId: string
				characterEncoding: CharacterEncoding
		}
	

		export interface RemoveGdtDocumentRequest {
				filename: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				pdfFileName?: string
		}
	

		export interface CheckGdtImportDocumentResponse {
				modifyTime: number
				isChanged: boolean
				gdtDocumentContent: string
		}
	

		export interface StopWatchFolderRequest {
				sourceType: SourceTypeEnum
				settingId: string
		}
	

		export interface HandleDocumentRequest {
				filename: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				lastModifyTime: number
				settingId: string
				characterEncoding: CharacterEncoding
				fileExt: string
		}
	

		export interface CheckExistedDocumentResponse {
				isExisted: boolean
		}
	


// enum definitions
    export enum SourceTypeEnum {
        SourceTypeEnum_LOCAL = "local",
        SourceTypeEnum_FTP = "ftp",
        SourceTypeEnum_SMB = "smb",
    }

    export enum CharacterEncoding {
        CharacterEncoding_IBM437 = "IBM437",
        CharacterEncoding_7Bit = "7-bit",
        CharacterEncoding_ISO8859 = "ISO8859-1 ANSI CP 1252",
    }


// method name convention const
		export const EVENT_GetFolderState = "api.app.companion.DmApp.GetFolderState";
		export const EVENT_GetFolderState_Response = "api.app.companion.DmApp.GetFolderState.Response";
		export const EVENT_UploadFiles = "api.app.companion.DmApp.UploadFiles";
		export const EVENT_UploadFiles_Response = "api.app.companion.DmApp.UploadFiles.Response";
		export const EVENT_WatchFolder = "api.app.companion.DmApp.WatchFolder";
		export const EVENT_WatchFolder_Response = "api.app.companion.DmApp.WatchFolder.Response";
		export const EVENT_ExportGdtDocument = "api.app.companion.DmApp.ExportGdtDocument";
		export const EVENT_ExportGdtDocument_Response = "api.app.companion.DmApp.ExportGdtDocument.Response";
		export const EVENT_CheckGdtImportDocument = "api.app.companion.DmApp.CheckGdtImportDocument";
		export const EVENT_CheckGdtImportDocument_Response = "api.app.companion.DmApp.CheckGdtImportDocument.Response";
		export const EVENT_StopWatchFolder = "api.app.companion.DmApp.StopWatchFolder";
		export const EVENT_StopWatchFolder_Response = "api.app.companion.DmApp.StopWatchFolder.Response";
		export const EVENT_RemoveGdtDocument = "api.app.companion.DmApp.RemoveGdtDocument";
		export const EVENT_RemoveGdtDocument_Response = "api.app.companion.DmApp.RemoveGdtDocument.Response";
		export const EVENT_CheckExistedDocument = "api.app.companion.DmApp.CheckExistedDocument";
		export const EVENT_CheckExistedDocument_Response = "api.app.companion.DmApp.CheckExistedDocument.Response";
		export const EVENT_HandleGdtImportDocument = "api.app.companion.DmApp.HandleGdtImportDocument";
		export const EVENT_HandleGdtImportDocument_Response = "api.app.companion.DmApp.HandleGdtImportDocument.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetFolderState = "/api/app/companion/dm/getFolderState";
        export const LEGACY_TOPIC_UploadFiles = "/api/app/companion/dm/uploadFiles";
        export const LEGACY_TOPIC_WatchFolder = "/api/app/companion/dm/watchFolder";
        export const LEGACY_TOPIC_ExportGdtDocument = "/api/app/companion/dm/exportGdtDocument";
        export const LEGACY_TOPIC_CheckGdtImportDocument = "/api/app/companion/dm/checkGdtImportDocument";
        export const LEGACY_TOPIC_StopWatchFolder = "/api/app/companion/dm/stopWatchFolder";
        export const LEGACY_TOPIC_RemoveGdtDocument = "/api/app/companion/dm/removeGdtDocument";
        export const LEGACY_TOPIC_CheckExistedDocument = "/api/app/companion/dm/checkExistedDocument";
        export const LEGACY_TOPIC_HandleGdtImportDocument = "/api/app/companion/dm/handleGdtImportDocument";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getFolderState(request: GetFolderStateRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetFolderStateResponse>("POST", LEGACY_TOPIC_GetFolderState, { init , request})
			}

			export function useQueryGetFolderState<TransformedType =GetFolderStateResponse>(payload: GetFolderStateRequest,ops?: CustomUseQueryOptions<ResponseType<GetFolderStateResponse>, TransformedType>) {
                return useQuery<ResponseType<GetFolderStateResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetFolderState, payload],
					queryFn: async ({ signal }) => await getFolderState(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetFolderState(opts?: UseMutationOptions<ResponseType<GetFolderStateResponse>, ErrorType,GetFolderStateRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getFolderState(request),
						retry: false,
						...opts
                });
            }
    
			export async function uploadFiles(request: UploadFilesRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UploadFiles, { init , request})
			}

			export function useQueryUploadFiles<TransformedType =any>(payload: UploadFilesRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UploadFiles, payload],
					queryFn: async ({ signal }) => await uploadFiles(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUploadFiles(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UploadFilesRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await uploadFiles(request),
						retry: false,
						...opts
                });
            }
    
			export async function watchFolder(request: WatchFolderRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_WatchFolder, { init , request})
			}

			export function useQueryWatchFolder<TransformedType =any>(payload: WatchFolderRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_WatchFolder, payload],
					queryFn: async ({ signal }) => await watchFolder(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationWatchFolder(opts?: UseMutationOptions<ResponseType<any>, ErrorType,WatchFolderRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await watchFolder(request),
						retry: false,
						...opts
                });
            }
    
			export async function exportGdtDocument(request: ExportGdtDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_ExportGdtDocument, { init , request})
			}

			export function useQueryExportGdtDocument<TransformedType =any>(payload: ExportGdtDocumentRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ExportGdtDocument, payload],
					queryFn: async ({ signal }) => await exportGdtDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationExportGdtDocument(opts?: UseMutationOptions<ResponseType<any>, ErrorType,ExportGdtDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await exportGdtDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkGdtImportDocument(request: CheckGdtImportDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckGdtImportDocumentResponse>("POST", LEGACY_TOPIC_CheckGdtImportDocument, { init , request})
			}

			export function useQueryCheckGdtImportDocument<TransformedType =CheckGdtImportDocumentResponse>(payload: CheckGdtImportDocumentRequest,ops?: CustomUseQueryOptions<ResponseType<CheckGdtImportDocumentResponse>, TransformedType>) {
                return useQuery<ResponseType<CheckGdtImportDocumentResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckGdtImportDocument, payload],
					queryFn: async ({ signal }) => await checkGdtImportDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckGdtImportDocument(opts?: UseMutationOptions<ResponseType<CheckGdtImportDocumentResponse>, ErrorType,CheckGdtImportDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkGdtImportDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function stopWatchFolder(request: StopWatchFolderRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_StopWatchFolder, { init , request})
			}

			export function useQueryStopWatchFolder<TransformedType =any>(payload: StopWatchFolderRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_StopWatchFolder, payload],
					queryFn: async ({ signal }) => await stopWatchFolder(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationStopWatchFolder(opts?: UseMutationOptions<ResponseType<any>, ErrorType,StopWatchFolderRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await stopWatchFolder(request),
						retry: false,
						...opts
                });
            }
    
			export async function removeGdtDocument(request: RemoveGdtDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_RemoveGdtDocument, { init , request})
			}

			export function useQueryRemoveGdtDocument<TransformedType =any>(payload: RemoveGdtDocumentRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_RemoveGdtDocument, payload],
					queryFn: async ({ signal }) => await removeGdtDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRemoveGdtDocument(opts?: UseMutationOptions<ResponseType<any>, ErrorType,RemoveGdtDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await removeGdtDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkExistedDocument(request: HandleDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckExistedDocumentResponse>("POST", LEGACY_TOPIC_CheckExistedDocument, { init , request})
			}

			export function useQueryCheckExistedDocument<TransformedType =CheckExistedDocumentResponse>(payload: HandleDocumentRequest,ops?: CustomUseQueryOptions<ResponseType<CheckExistedDocumentResponse>, TransformedType>) {
                return useQuery<ResponseType<CheckExistedDocumentResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckExistedDocument, payload],
					queryFn: async ({ signal }) => await checkExistedDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckExistedDocument(opts?: UseMutationOptions<ResponseType<CheckExistedDocumentResponse>, ErrorType,HandleDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkExistedDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function handleGdtImportDocument(request: HandleDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_HandleGdtImportDocument, { init , request})
			}

			export function useQueryHandleGdtImportDocument<TransformedType =any>(payload: HandleDocumentRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_HandleGdtImportDocument, payload],
					queryFn: async ({ signal }) => await handleGdtImportDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationHandleGdtImportDocument(opts?: UseMutationOptions<ResponseType<any>, ErrorType,HandleDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await handleGdtImportDocument(request),
						retry: false,
						...opts
                });
            }
    

