/* eslint-disable */
// This code was autogenerated from app/admin/card_operation.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as card_common from "./card_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface Terminal {
				mandant: card_common.Mandant
				terminalId: string
				terminalName: string
		}
	

		export interface GetTerminalsResponse {
				terminals: Array<Terminal>
		}
	

		export interface GetTerminalsAndCardsResponse {
				terminals: Array<card_common.TerminalWithCards>
		}
	

		export interface GetCardsRequest {
				connectorId: string
		}
	

		export interface GetCardsResponse {
				cards: Array<card_common.Card>
		}
	

		export interface VerifyPinRequest {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface ChangePinRequest {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface UnblockPinRequest {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				setNewPin: boolean
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface PinResponse {
				cardHandle: string
				pinResult: PinResult
				leftTries?: number
		}
	

		export interface AssignBSNRRequest {
				isscn: string
				bSNRCode: string
		}
	

		export interface UnassignRequest {
				isscn: string
		}
	

		export interface AssignDoctorRequest {
				isscn: string
				doctorId: string
		}
	

		export interface AssignedCard {
				iccsn: string
				bsnrCode?: string
				doctorId?: string
		}
	

		export interface GetCardsWithAssignedDoctorOrPracticeResponse {
				assignedCards: Array<AssignedCard>
		}
	

		export interface GetPinStatus {
				connectorId: string
				cardHandle: string
				pinType: card_common.PinType
				cardType: card_common.CardTypeType
				iccsn: string
		}
	

		export interface GetPinStatusRequest {
				getPinStatus: Array<GetPinStatus>
		}
	

		export interface GetPinStatusResponse {
				cardPins: Array<card_common.CardPin>
		}
	


// enum definitions
    export enum PinResult {
        PinResultEnumERROR = "ERROR",
        PinResultEnumOK = "OK",
        PinResultEnumREJECTED = "REJECTED",
        PinResultEnumWASBLOCKED = "WASBLOCKED",
        PinResultEnumNOWBLOCKED = "NOWBLOCKED",
        PinResultEnumTRANSPORT_PIN = "TRANSPORT_PIN",
    }


// method name convention const
		export const EVENT_GetTerminals = "api.app.admin.CardOperationApp.GetTerminals";
		export const EVENT_GetTerminals_Response = "api.app.admin.CardOperationApp.GetTerminals.Response";
		export const EVENT_GetPinStatus = "api.app.admin.CardOperationApp.GetPinStatus";
		export const EVENT_GetPinStatus_Response = "api.app.admin.CardOperationApp.GetPinStatus.Response";
		export const EVENT_VerifyPin = "api.app.admin.CardOperationApp.VerifyPin";
		export const EVENT_VerifyPin_Response = "api.app.admin.CardOperationApp.VerifyPin.Response";
		export const EVENT_ChangePin = "api.app.admin.CardOperationApp.ChangePin";
		export const EVENT_ChangePin_Response = "api.app.admin.CardOperationApp.ChangePin.Response";
		export const EVENT_UnblockPin = "api.app.admin.CardOperationApp.UnblockPin";
		export const EVENT_UnblockPin_Response = "api.app.admin.CardOperationApp.UnblockPin.Response";
		export const EVENT_GetCards = "api.app.admin.CardOperationApp.GetCards";
		export const EVENT_GetCards_Response = "api.app.admin.CardOperationApp.GetCards.Response";
		export const EVENT_AssignBSNR = "api.app.admin.CardOperationApp.AssignBSNR";
		export const EVENT_AssignBSNR_Response = "api.app.admin.CardOperationApp.AssignBSNR.Response";
		export const EVENT_UnassignBSNR = "api.app.admin.CardOperationApp.UnassignBSNR";
		export const EVENT_UnassignBSNR_Response = "api.app.admin.CardOperationApp.UnassignBSNR.Response";
		export const EVENT_AssignDoctor = "api.app.admin.CardOperationApp.AssignDoctor";
		export const EVENT_AssignDoctor_Response = "api.app.admin.CardOperationApp.AssignDoctor.Response";
		export const EVENT_UnassignDoctor = "api.app.admin.CardOperationApp.UnassignDoctor";
		export const EVENT_UnassignDoctor_Response = "api.app.admin.CardOperationApp.UnassignDoctor.Response";
		export const EVENT_GetCardsWithAssignedDoctorOrPractice = "api.app.admin.CardOperationApp.GetCardsWithAssignedDoctorOrPractice";
		export const EVENT_GetCardsWithAssignedDoctorOrPractice_Response = "api.app.admin.CardOperationApp.GetCardsWithAssignedDoctorOrPractice.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetTerminals = "/api/app/admin/card/operation/getTerminals";
        export const LEGACY_TOPIC_GetPinStatus = "/api/app/admin/card/operation/getPinStatus";
        export const LEGACY_TOPIC_VerifyPin = "/api/app/admin/card/operation/verifyPin";
        export const LEGACY_TOPIC_ChangePin = "/api/app/admin/card/operation/changePin";
        export const LEGACY_TOPIC_UnblockPin = "/api/app/admin/card/operation/unblockPin";
        export const LEGACY_TOPIC_GetCards = "/api/app/admin/card/operation/getCards";
        export const LEGACY_TOPIC_AssignBSNR = "/api/app/admin/card/operation/assignBSNR";
        export const LEGACY_TOPIC_UnassignBSNR = "/api/app/admin/card/operation/unassignBSNR";
        export const LEGACY_TOPIC_AssignDoctor = "/api/app/admin/card/operation/assignDoctor";
        export const LEGACY_TOPIC_UnassignDoctor = "/api/app/admin/card/operation/unassignDoctor";
        export const LEGACY_TOPIC_GetCardsWithAssignedDoctorOrPractice = "/api/app/admin/card/operation/getCardsWithAssignedDoctorOrPractice";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getTerminals(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTerminalsResponse>("POST", LEGACY_TOPIC_GetTerminals, { init })
			}

			export function useQueryGetTerminals<TransformedType =GetTerminalsResponse>(ops?: CustomUseQueryOptions<ResponseType<GetTerminalsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTerminalsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTerminals],
					queryFn: async ({ signal }) => await getTerminals({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTerminals(opts?: UseMutationOptions<ResponseType<GetTerminalsResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getTerminals(),
						retry: false,
						...opts
                });
            }
    
			export async function getPinStatus(request: GetPinStatusRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetPinStatusResponse>("POST", LEGACY_TOPIC_GetPinStatus, { init , request})
			}

			export function useQueryGetPinStatus<TransformedType =GetPinStatusResponse>(payload: GetPinStatusRequest,ops?: CustomUseQueryOptions<ResponseType<GetPinStatusResponse>, TransformedType>) {
                return useQuery<ResponseType<GetPinStatusResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPinStatus, payload],
					queryFn: async ({ signal }) => await getPinStatus(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPinStatus(opts?: UseMutationOptions<ResponseType<GetPinStatusResponse>, ErrorType,GetPinStatusRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPinStatus(request),
						retry: false,
						...opts
                });
            }
    
			export async function verifyPin(request: VerifyPinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PinResponse>("POST", LEGACY_TOPIC_VerifyPin, { init , request})
			}

			export function useQueryVerifyPin<TransformedType =PinResponse>(payload: VerifyPinRequest,ops?: CustomUseQueryOptions<ResponseType<PinResponse>, TransformedType>) {
                return useQuery<ResponseType<PinResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_VerifyPin, payload],
					queryFn: async ({ signal }) => await verifyPin(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationVerifyPin(opts?: UseMutationOptions<ResponseType<PinResponse>, ErrorType,VerifyPinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await verifyPin(request),
						retry: false,
						...opts
                });
            }
    
			export async function changePin(request: ChangePinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PinResponse>("POST", LEGACY_TOPIC_ChangePin, { init , request})
			}

			export function useQueryChangePin<TransformedType =PinResponse>(payload: ChangePinRequest,ops?: CustomUseQueryOptions<ResponseType<PinResponse>, TransformedType>) {
                return useQuery<ResponseType<PinResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ChangePin, payload],
					queryFn: async ({ signal }) => await changePin(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationChangePin(opts?: UseMutationOptions<ResponseType<PinResponse>, ErrorType,ChangePinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await changePin(request),
						retry: false,
						...opts
                });
            }
    
			export async function unblockPin(request: UnblockPinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PinResponse>("POST", LEGACY_TOPIC_UnblockPin, { init , request})
			}

			export function useQueryUnblockPin<TransformedType =PinResponse>(payload: UnblockPinRequest,ops?: CustomUseQueryOptions<ResponseType<PinResponse>, TransformedType>) {
                return useQuery<ResponseType<PinResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UnblockPin, payload],
					queryFn: async ({ signal }) => await unblockPin(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUnblockPin(opts?: UseMutationOptions<ResponseType<PinResponse>, ErrorType,UnblockPinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await unblockPin(request),
						retry: false,
						...opts
                });
            }
    
			export async function getCards(request: GetCardsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetCardsResponse>("POST", LEGACY_TOPIC_GetCards, { init , request})
			}

			export function useQueryGetCards<TransformedType =GetCardsResponse>(payload: GetCardsRequest,ops?: CustomUseQueryOptions<ResponseType<GetCardsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetCardsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetCards, payload],
					queryFn: async ({ signal }) => await getCards(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetCards(opts?: UseMutationOptions<ResponseType<GetCardsResponse>, ErrorType,GetCardsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getCards(request),
						retry: false,
						...opts
                });
            }
    
			export async function assignBSNR(request: AssignBSNRRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_AssignBSNR, { init , request})
			}

			export function useQueryAssignBSNR<TransformedType =any>(payload: AssignBSNRRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_AssignBSNR, payload],
					queryFn: async ({ signal }) => await assignBSNR(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationAssignBSNR(opts?: UseMutationOptions<ResponseType<any>, ErrorType,AssignBSNRRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await assignBSNR(request),
						retry: false,
						...opts
                });
            }
    
			export async function unassignBSNR(request: UnassignRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UnassignBSNR, { init , request})
			}

			export function useQueryUnassignBSNR<TransformedType =any>(payload: UnassignRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UnassignBSNR, payload],
					queryFn: async ({ signal }) => await unassignBSNR(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUnassignBSNR(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UnassignRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await unassignBSNR(request),
						retry: false,
						...opts
                });
            }
    
			export async function assignDoctor(request: AssignDoctorRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_AssignDoctor, { init , request})
			}

			export function useQueryAssignDoctor<TransformedType =any>(payload: AssignDoctorRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_AssignDoctor, payload],
					queryFn: async ({ signal }) => await assignDoctor(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationAssignDoctor(opts?: UseMutationOptions<ResponseType<any>, ErrorType,AssignDoctorRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await assignDoctor(request),
						retry: false,
						...opts
                });
            }
    
			export async function unassignDoctor(request: UnassignRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UnassignDoctor, { init , request})
			}

			export function useQueryUnassignDoctor<TransformedType =any>(payload: UnassignRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UnassignDoctor, payload],
					queryFn: async ({ signal }) => await unassignDoctor(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUnassignDoctor(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UnassignRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await unassignDoctor(request),
						retry: false,
						...opts
                });
            }
    
			export async function getCardsWithAssignedDoctorOrPractice(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetCardsWithAssignedDoctorOrPracticeResponse>("POST", LEGACY_TOPIC_GetCardsWithAssignedDoctorOrPractice, { init })
			}

			export function useQueryGetCardsWithAssignedDoctorOrPractice<TransformedType =GetCardsWithAssignedDoctorOrPracticeResponse>(ops?: CustomUseQueryOptions<ResponseType<GetCardsWithAssignedDoctorOrPracticeResponse>, TransformedType>) {
                return useQuery<ResponseType<GetCardsWithAssignedDoctorOrPracticeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetCardsWithAssignedDoctorOrPractice],
					queryFn: async ({ signal }) => await getCardsWithAssignedDoctorOrPractice({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetCardsWithAssignedDoctorOrPractice(opts?: UseMutationOptions<ResponseType<GetCardsWithAssignedDoctorOrPracticeResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getCardsWithAssignedDoctorOrPractice(),
						retry: false,
						...opts
                });
            }
    

