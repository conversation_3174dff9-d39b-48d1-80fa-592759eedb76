/* eslint-disable */
// This code was autogenerated from app/mvz/text_module.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as text_module_common from "./text_module_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface GetInvalidOmimGChainItem {
				id: string
				textShortcut: string
		}
	

		export interface GetInvalidOmimGChainResponse {
				data: Array<GetInvalidOmimGChainItem>
		}
	

		export interface GetTextModulesRequest {
				query: string
				page?: number
				pageSize?: number
				useFors: Array<text_module_common.TextModuleUseFor>
		}
	

		export interface GetInvalidOmimGChainRequest {
				selectedDate: number
		}
	


// enum definitions

// method name convention const
		export const EVENT_GetInvalidOmimGChain = "api.app.mvz.TextModuleApp.GetInvalidOmimGChain";
		export const EVENT_GetInvalidOmimGChain_Response = "api.app.mvz.TextModuleApp.GetInvalidOmimGChain.Response";
		export const EVENT_GetTextModules = "api.app.mvz.TextModuleApp.GetTextModules";
		export const EVENT_GetTextModules_Response = "api.app.mvz.TextModuleApp.GetTextModules.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetInvalidOmimGChain = "/api/app/mvz/text/module/getInvalidOmimGChain";
        export const LEGACY_TOPIC_GetTextModules = "/api/app/mvz/text/module/getTextModules";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getInvalidOmimGChain(request: GetInvalidOmimGChainRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetInvalidOmimGChainResponse>("POST", LEGACY_TOPIC_GetInvalidOmimGChain, { init , request})
			}

			export function useQueryGetInvalidOmimGChain<TransformedType =GetInvalidOmimGChainResponse>(payload: GetInvalidOmimGChainRequest,ops?: CustomUseQueryOptions<ResponseType<GetInvalidOmimGChainResponse>, TransformedType>) {
                return useQuery<ResponseType<GetInvalidOmimGChainResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetInvalidOmimGChain, payload],
					queryFn: async ({ signal }) => await getInvalidOmimGChain(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetInvalidOmimGChain(opts?: UseMutationOptions<ResponseType<GetInvalidOmimGChainResponse>, ErrorType,GetInvalidOmimGChainRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getInvalidOmimGChain(request),
						retry: false,
						...opts
                });
            }
    
			export async function getTextModules(request: text_module_common.TextModulePaginationRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<text_module_common.TextModulePaginationResponse>("POST", LEGACY_TOPIC_GetTextModules, { init , request})
			}

			export function useQueryGetTextModules<TransformedType =text_module_common.TextModulePaginationResponse>(payload: text_module_common.TextModulePaginationRequest,ops?: CustomUseQueryOptions<ResponseType<text_module_common.TextModulePaginationResponse>, TransformedType>) {
                return useQuery<ResponseType<text_module_common.TextModulePaginationResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTextModules, payload],
					queryFn: async ({ signal }) => await getTextModules(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTextModules(opts?: UseMutationOptions<ResponseType<text_module_common.TextModulePaginationResponse>, ErrorType,text_module_common.TextModulePaginationRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getTextModules(request),
						retry: false,
						...opts
                });
            }
    

