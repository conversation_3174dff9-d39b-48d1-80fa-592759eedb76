/* eslint-disable */
// This code was autogenerated from service/domains/bsnr_common.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as admin_bff from "./app_admin"
import * as common from "./common"



// Type definitions
		export interface RVSACertificate {
				id: string
				labParameter: string
				gnr: string
				certificateStatus: RVSACertificateStatus
				isValidity: boolean
				material: string
				validFrom: string
				validTil: string
				isDocumented: boolean
		}
	

		export interface DeviceType {
				deviceType: string
				manufacturer: string
		}
	

		export interface TiComponent {
				value: boolean
				readonly: boolean
				checkedAt?: number
		}
	

		export interface BSNR {
				id?: string
				code: string
				name: string
				billingDoctors: Array<string>
				uv: string
				street: string
				number: string
				postCode: string
				city: string
				country: string
				phoneNumber: string
				fax: string
				email: string
				hasRVSA: boolean
				rvsaCertificateReagents: RVSACertificateReagents
				rVSACertificate: Array<RVSACertificate>
				deviceTypes?: Array<DeviceType>
				bsnrId?: string
				createdAt?: number
				facilityType: FacilityType
				practiceStamp: string
				bankInformations: Array<common.BankInformation>
				postCodePrevious: string
				rvsaCertificateStatus: RVSACertificateStatus
				ePAStufe1: TiComponent
				ePAStufe2: TiComponent
				eRezept: TiComponent
				nFDM: TiComponent
				eMP: TiComponent
				kIM: TiComponent
				eAU: TiComponent
				eArztbrief: TiComponent
				kartenterminal: TiComponent
				sMCB: TiComponent
				eHBA: TiComponent
				ePAStufe3: TiComponent
				eVDGA: TiComponent
				hpmConfig: HpmConfig
		}
	

		export interface BSNRWithEmployee {
				id?: string
				code: string
				name: string
				billingDoctors: Array<admin_bff.EmployeeDetailResponse>
				uv: string
				street: string
				number: string
				postCode: string
				city: string
				country: string
				phoneNumber: string
				fax: string
				email: string
				hasRVSA: boolean
				rvsaCertificateReagents: RVSACertificateReagents
				rVSACertificate: Array<RVSACertificate>
				hasEPAStufe1: boolean
				hasERezept: boolean
				deviceTypes?: Array<DeviceType>
				bsnrId?: string
				createdAt?: number
		}
	

		export interface BSNRName {
				id?: string
				code: string
				name: string
				billingDoctors: Array<string>
				bsnrId?: string
				bSNRNames: Array<BSNRName>
				street: string
				number: string
				postCode: string
				city: string
				phoneNumber: string
				fax: string
				facilityType: FacilityType
				hasEPAStufe1: boolean
				hasEPAStufe2: boolean
				nFDM: boolean
				eMP: boolean
				kIM: boolean
				eAU: boolean
				eArztbrief: boolean
				kartenterminal: boolean
				sMCB: boolean
				eHBA: boolean
				hasERezept: boolean
				hasEPAStufe3: boolean
				practiceStamp: string
				email: string
		}
	

		export interface HpmConfig {
				endpoint: string
		}
	


// enum definitions
    export enum FacilityType {
        FacilityType_Practice = "FacilityType_Practice",
        FacilityType_Hospital = "FacilityType_Hospital",
    }

    export enum StatusResponse {
        Success = "Sucess",
        Failure = "Failure",
    }

    export enum RVSACertificateReagents {
        No = "0",
        YES_EXCLUSIVELY = "1",
        YES_PARTIALLY = "2",
    }

    export enum TiApplication {
        TiApplication_eRezept = "0",
        TiApplication_ePAStufe = "1",
    }

    export enum RVSACertificateStatus {
        NO = "0",
        YES = "1",
        PNSD_UU_ANALYSE = "2",
    }


// method name convention const

// Define constants
// method name convention const
