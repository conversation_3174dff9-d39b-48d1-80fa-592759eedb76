/* eslint-disable */
// This code was autogenerated from app/mvz/sidebar.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface GetDefaultInformationResponse {
				hasNotSubmittedEnrollment: boolean
				hasPendingPtvImport: boolean
		}
	

		export interface EventSidebarRepsonse {
				userId: string
				hasNotSubmittedEnrollment: boolean
				hasPendingPtvImport: boolean
		}
	


// enum definitions

// method name convention const
		export const EVENT_GetDefaultInformation = "api.app.mvz.SidebarApp.GetDefaultInformation";
		export const EVENT_GetDefaultInformation_Response = "api.app.mvz.SidebarApp.GetDefaultInformation.Response";
		export const EVENT_HandleEventSidebar = "api.app.mvz.SidebarApp.HandleEventSidebar";
		export const EVENT_HandleEventSidebar_Response = "api.app.mvz.SidebarApp.HandleEventSidebar.Response";
		export const EVENT_HandleEventForPatientOverview = "api.app.mvz.SidebarApp.HandleEventForPatientOverview";
		export const EVENT_HandleEventForPatientOverview_Response = "api.app.mvz.SidebarApp.HandleEventForPatientOverview.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetDefaultInformation = "/api/app/mvz/sidebar/getDefaultInformation";
        export const LEGACY_TOPIC_HandleEventSidebar = "/api/app/mvz/sidebar/handleEventSidebar";
        export const LEGACY_TOPIC_HandleEventForPatientOverview = "/api/app/mvz/sidebar/handleEventForPatientOverview";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getDefaultInformation(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetDefaultInformationResponse>("POST", LEGACY_TOPIC_GetDefaultInformation, { init })
			}

			export function useQueryGetDefaultInformation<TransformedType =GetDefaultInformationResponse>(ops?: CustomUseQueryOptions<ResponseType<GetDefaultInformationResponse>, TransformedType>) {
                return useQuery<ResponseType<GetDefaultInformationResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetDefaultInformation],
					queryFn: async ({ signal }) => await getDefaultInformation({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetDefaultInformation(opts?: UseMutationOptions<ResponseType<GetDefaultInformationResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getDefaultInformation(),
						retry: false,
						...opts
                });
            }
    
    
    

