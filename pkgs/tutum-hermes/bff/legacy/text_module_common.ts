/* eslint-disable */
// This code was autogenerated from service/domains/text_module_common.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as patient_encounter from "./repo_encounter"



// Type definitions
		export interface TextModuleContent {
				text: string
				data: Array<TextModuleNode>
		}
	

		export interface TextModuleNode {
				type: TextModuleNodeType
				children: Array<TextModuleNode>
				text?: TextModuleTextNode
				placeholder?: TextModulePlaceholderNode
				questionnaire?: TextModuleQuestionnaireNode
				variable?: TextModuleVariableNode
				omimG?: patient_encounter.AdditionalInfoParent
		}
	

		export interface TextModuleTextNode {
				value: string
		}
	

		export interface TextModulePlaceholderNode {
				label: string
				value: string
		}
	

		export interface TextModuleQuestionnaireNode {
				label: string
				questionType: QuestionnaireQuestionType
				answers: Array<Answer>
				value?: string
		}
	

		export interface Answer {
				label: string
				answerType: AnswerType
				value: string
		}
	

		export interface TextModuleVariableNode {
		}
	

		export interface TextModule {
				id?: string
				useFor: Array<TextModuleUseFor>
				textShortcut: string
				moduleType: ModuleType
				content: TextModuleContent
				status: TextModuleStatus
				bsnrId?: string
		}
	

		export interface TextModulePaginationRequest {
				query: string
				page?: number
				pageSize?: number
				useFors: Array<TextModuleUseFor>
				bsnrId?: string
		}
	

		export interface TextModulePaginationResponse {
				textModules: Array<TextModule>
				page: number
				total: number
		}
	


// enum definitions
    export enum TextModuleUseFor {
        TextModuleUseFor_Anamnesis = "TextModuleUseFor_Anamnesis",
        TextModuleUseFor_Cave = "TextModuleUseFor_Cave",
        TextModuleUseFor_Findings = "TextModuleUseFor_Findings",
        TextModuleUseFor_Note = "TextModuleUseFor_Note",
        TextModuleUseFor_Therapy = "TextModuleUseFor_Therapy",
        TextModuleUseFor_OmimGChain = "TextModuleUseFor_OmimGChain",
        TextModuleUseFor_Form = "TextModuleUseFor_Form",
        TextModuleUseFor_BMP = "TextModuleUseFor_BMP",
        TextModuleUseFor_Doctorletter = "TextModuleUseFor_Doctorletter",
        TextModuleUseFor_HGNC = "TextModuleUseFor_HGNC",
    }

    export enum ModuleType {
        ModuleType_FreeText = "ModuleType_FreeText",
        ModuleType_Questionnaire = "ModuleType_Questionnaire",
    }

    export enum TextModuleStatus {
        TextModuleStatus_Active = "TextModuleStatus_Active",
        TextModuleNodeType_Deactive = "TextModuleNodeType_Deactive",
    }

    export enum TextModuleNodeType {
        TextModuleNodeType_Text = "TextModuleNodeType_Text",
        TextModuleNodeType_Placeholder = "TextModuleNodeType_Placeholder",
        TextModuleNodeType_Questionnaire = "TextModuleNodeType_Questionnaire",
        TextModuleNodeType_Variable = "TextModuleNodeType_Variable",
        TextModuleNodeType_AdditionalInfo = "TextModuleNodeType_AdditionalInfo",
        TextModuleNodeType_LineBreak = "TextModuleNodeType_LineBreak",
    }

    export enum QuestionnaireQuestionType {
        QuestionnaireQuestionType_SingleSelection = "QuestionnaireQuestionType_SingleSelection",
        QuestionnaireQuestionType_MultipleSelection = "QuestionnaireQuestionType_MultipleSelection",
        QuestionnaireQuestionType_Freetext = "QuestionnaireQuestionType_Freetext",
    }

    export enum AnswerType {
        AnswerType_Select = "AnswerType_Select",
        AnswerType_Others = "AnswerType_Others",
    }


// method name convention const

// Define constants
// method name convention const
