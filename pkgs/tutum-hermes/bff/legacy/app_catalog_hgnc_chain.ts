/* eslint-disable */
// This code was autogenerated from app/mvz/catalog_hgnc_chain.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common from "./common"
import * as common1 from "./hgnc_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface HgncChain {
				id: string
				name: string
				hgncItems: Array<common1.HgncItem>
		}
	

		export interface SearchHgncChainRequest {
				name: string
				paginationRequest: common.PaginationRequest
		}
	

		export interface SearchHgncChainResponse {
				hgncChains: Array<HgncChain>
				pagination: common.PaginationResponse
		}
	

		export interface CreateHgncChainRequest {
				name: string
				hgncItems: Array<common1.HgncItem>
		}
	

		export interface CreateHgncChainResponse {
				chain: Hgnc<PERSON>hain
		}
	

		export interface UpdateHgncChainRequest {
				id: string
				name: string
				hgncItems: Array<common1.HgncItem>
		}
	

		export interface UpdateHgncChainResponse {
				chain: HgncChain
		}
	

		export interface DeleteHgncChainRequest {
				id: string
		}
	


// enum definitions

// method name convention const
		export const EVENT_SearchHgncChain = "api.app.mvz.HgncChainApp.SearchHgncChain";
		export const EVENT_SearchHgncChain_Response = "api.app.mvz.HgncChainApp.SearchHgncChain.Response";
		export const EVENT_CreateHgncChain = "api.app.mvz.HgncChainApp.CreateHgncChain";
		export const EVENT_CreateHgncChain_Response = "api.app.mvz.HgncChainApp.CreateHgncChain.Response";
		export const EVENT_UpdateHgncChain = "api.app.mvz.HgncChainApp.UpdateHgncChain";
		export const EVENT_UpdateHgncChain_Response = "api.app.mvz.HgncChainApp.UpdateHgncChain.Response";
		export const EVENT_DeleteHgncChain = "api.app.mvz.HgncChainApp.DeleteHgncChain";
		export const EVENT_DeleteHgncChain_Response = "api.app.mvz.HgncChainApp.DeleteHgncChain.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_SearchHgncChain = "/api/app/mvz/hgnc/chain/searchHgncChain";
        export const LEGACY_TOPIC_CreateHgncChain = "/api/app/mvz/hgnc/chain/createHgncChain";
        export const LEGACY_TOPIC_UpdateHgncChain = "/api/app/mvz/hgnc/chain/updateHgncChain";
        export const LEGACY_TOPIC_DeleteHgncChain = "/api/app/mvz/hgnc/chain/deleteHgncChain";


// Define action methods and their listener -----------------------------------------------------------------
			export async function searchHgncChain(request: SearchHgncChainRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<SearchHgncChainResponse>("POST", LEGACY_TOPIC_SearchHgncChain, { init , request})
			}

			export function useQuerySearchHgncChain<TransformedType =SearchHgncChainResponse>(payload: SearchHgncChainRequest,ops?: CustomUseQueryOptions<ResponseType<SearchHgncChainResponse>, TransformedType>) {
                return useQuery<ResponseType<SearchHgncChainResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_SearchHgncChain, payload],
					queryFn: async ({ signal }) => await searchHgncChain(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSearchHgncChain(opts?: UseMutationOptions<ResponseType<SearchHgncChainResponse>, ErrorType,SearchHgncChainRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await searchHgncChain(request),
						retry: false,
						...opts
                });
            }
    
			export async function createHgncChain(request: CreateHgncChainRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CreateHgncChainResponse>("POST", LEGACY_TOPIC_CreateHgncChain, { init , request})
			}

			export function useQueryCreateHgncChain<TransformedType =CreateHgncChainResponse>(payload: CreateHgncChainRequest,ops?: CustomUseQueryOptions<ResponseType<CreateHgncChainResponse>, TransformedType>) {
                return useQuery<ResponseType<CreateHgncChainResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateHgncChain, payload],
					queryFn: async ({ signal }) => await createHgncChain(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateHgncChain(opts?: UseMutationOptions<ResponseType<CreateHgncChainResponse>, ErrorType,CreateHgncChainRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createHgncChain(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateHgncChain(request: UpdateHgncChainRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<UpdateHgncChainResponse>("POST", LEGACY_TOPIC_UpdateHgncChain, { init , request})
			}

			export function useQueryUpdateHgncChain<TransformedType =UpdateHgncChainResponse>(payload: UpdateHgncChainRequest,ops?: CustomUseQueryOptions<ResponseType<UpdateHgncChainResponse>, TransformedType>) {
                return useQuery<ResponseType<UpdateHgncChainResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateHgncChain, payload],
					queryFn: async ({ signal }) => await updateHgncChain(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateHgncChain(opts?: UseMutationOptions<ResponseType<UpdateHgncChainResponse>, ErrorType,UpdateHgncChainRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateHgncChain(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteHgncChain(request: DeleteHgncChainRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteHgncChain, { init , request})
			}

			export function useQueryDeleteHgncChain<TransformedType =any>(payload: DeleteHgncChainRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteHgncChain, payload],
					queryFn: async ({ signal }) => await deleteHgncChain(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteHgncChain(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeleteHgncChainRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteHgncChain(request),
						retry: false,
						...opts
                });
            }
    

