/* eslint-disable */
// This code was autogenerated from service/domains/patient_participation.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common from "./common"
import * as profile from "./service_domains_profile"



// Type definitions
		export interface GetPatientParticipationByIdRequest {
				patientId: string
		}
	

		export interface GetPatientParticipationRequest {
				patientId: string
				checkDate: number
		}
	

		export interface GetPatientParticipationWithoutCheckDateRequest {
				patientId: string
		}
	

		export interface CheckPatientParticipationRequest {
				patientId: string
				checkDate: Date
				contractId: string
				doctorId: string
		}
	

		export interface GetPatientParticipationResponse {
				participations: Array<PatientParticipation>
		}
	

		export interface CheckPatientParticipationResponse {
				isAvailable: boolean
		}
	

		export interface GetPatientParticipationForPTVImportRequest {
				doctorId: string
				contractId: string
		}
	

		export interface UpdatePatientParticipationForPTVImportRequest {
				doctorId: string
				contractId: string
				patientId: string
				ikNumber: number
				insuranceNumber: string
				startDate?: number
				endDate?: number
				status: PatientParticipationStatus
				reason?: string
				ppId?: string
				treatmentType: string
		}
	

		export interface GetPatientParticipationForPTVImportResponse {
				participationPTVImports: Array<PatientParticipationPTVImport>
		}
	

		export interface EventPatientParticipationChange {
				patientId: string
				contractIds: Array<string>
				insuranceNumber: string
		}
	

		export interface GetDoctorsCanTreatAsDeputyRequest {
				doctorId: string
				contractId: string
		}
	

		export interface GetDoctorsCanTreatAsDeputyResponse {
				profiles: Array<profile.EmployeeProfileResponse>
		}
	

		export interface CreatePatientParticipationForPTVImportResponse {
				participations: Array<PatientParticipation>
		}
	

		export interface PatientParticipationPTVImport {
				patientId?: string
				startDate?: number
				endDate?: number
				status: PatientParticipationStatus
				insuranceNumber: string
				ikNumber: number
				ppId?: string
				treatmentType: string
		}
	

		export interface PatientParticipation {
				id?: string
				startDate?: number
				endDate?: number
				doctorId?: string
				contractId: string
				ikNumber: number
				status: PatientParticipationStatus
				createdDate: number
				updatedDate?: number
				updatedBy?: string
				doctorFunctionType: DoctorFunctionType
				contractType: common.ContractType
				isTransmittedHpm: boolean
				isChangingDoctor: boolean
				chargeSystemId: string
				teId: string
				patientId: string
		}
	


// enum definitions
    export enum DoctorFunctionType {
        DoctorFunctionTypeCustodian = "Custodian",
        DoctorFunctionTypeDeputy = "Deputy",
    }

    export enum PatientParticipationStatus {
        PatientParticipation_Requested = "REQUESTED",
        PatientParticipation_Rejected = "REJECTED",
        PatientParticipation_Active = "ACTIVE",
        PatientParticipation_Cancelled = "CANCELLED",
        PatientParticipation_Terminated = "TERMINATED",
    }


// method name convention const
		export const EVENT_GetPatientParticipation = "api.service.domains.PatientParticipationService.GetPatientParticipation";
		export const EVENT_GetPatientParticipation_Response = "api.service.domains.PatientParticipationService.GetPatientParticipation.Response";
		export const EVENT_CheckPatientParticipation = "api.service.domains.PatientParticipationService.CheckPatientParticipation";
		export const EVENT_CheckPatientParticipation_Response = "api.service.domains.PatientParticipationService.CheckPatientParticipation.Response";
		export const EVENT_HandleEventInsuranceInformationChange = "api.service.domains.PatientParticipationService.HandleEventInsuranceInformationChange";
		export const EVENT_HandleEventInsuranceInformationChange_Response = "api.service.domains.PatientParticipationService.HandleEventInsuranceInformationChange.Response";
		export const EVENT_GetPatientParticipationForPTVImport = "api.service.domains.PatientParticipationService.GetPatientParticipationForPTVImport";
		export const EVENT_GetPatientParticipationForPTVImport_Response = "api.service.domains.PatientParticipationService.GetPatientParticipationForPTVImport.Response";
		export const EVENT_UpdatePatientParticipationForPTVImport = "api.service.domains.PatientParticipationService.UpdatePatientParticipationForPTVImport";
		export const EVENT_UpdatePatientParticipationForPTVImport_Response = "api.service.domains.PatientParticipationService.UpdatePatientParticipationForPTVImport.Response";
		export const EVENT_GetDoctorsCanTreatAsDeputy = "api.service.domains.PatientParticipationService.GetDoctorsCanTreatAsDeputy";
		export const EVENT_GetDoctorsCanTreatAsDeputy_Response = "api.service.domains.PatientParticipationService.GetDoctorsCanTreatAsDeputy.Response";
		export const EVENT_GetActivePatientParticipation = "api.service.domains.PatientParticipationService.GetActivePatientParticipation";
		export const EVENT_GetActivePatientParticipation_Response = "api.service.domains.PatientParticipationService.GetActivePatientParticipation.Response";
		export const EVENT_GetActivePatientParticipationWithoutCheckDate = "api.service.domains.PatientParticipationService.GetActivePatientParticipationWithoutCheckDate";
		export const EVENT_GetActivePatientParticipationWithoutCheckDate_Response = "api.service.domains.PatientParticipationService.GetActivePatientParticipationWithoutCheckDate.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetPatientParticipation = "/api/service/domains/patient/participation/getPatientParticipation";
        export const LEGACY_TOPIC_CheckPatientParticipation = "/api/service/domains/patient/participation/checkPatientParticipation";
        export const LEGACY_TOPIC_HandleEventInsuranceInformationChange = "/api/service/domains/patient/participation/handleEventInsuranceInformationChange";
        export const LEGACY_TOPIC_GetPatientParticipationForPTVImport = "/api/service/domains/patient/participation/getPatientParticipationForPTVImport";
        export const LEGACY_TOPIC_UpdatePatientParticipationForPTVImport = "/api/service/domains/patient/participation/updatePatientParticipationForPTVImport";
        export const LEGACY_TOPIC_GetDoctorsCanTreatAsDeputy = "/api/service/domains/patient/participation/getDoctorsCanTreatAsDeputy";
        export const LEGACY_TOPIC_GetActivePatientParticipation = "/api/service/domains/patient/participation/getActivePatientParticipation";
        export const LEGACY_TOPIC_GetActivePatientParticipationWithoutCheckDate = "/api/service/domains/patient/participation/getActivePatientParticipationWithoutCheckDate";

