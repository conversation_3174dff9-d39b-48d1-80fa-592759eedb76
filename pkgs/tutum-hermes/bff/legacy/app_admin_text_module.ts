/* eslint-disable */
// This code was autogenerated from app/admin/text_module.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common from "./common"
import * as text_module_common from "./text_module_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface CreateTextModuleRequest {
				textModule: text_module_common.TextModule
		}
	

		export interface UpdateTextModuleRequest {
				textModule: text_module_common.TextModule
		}
	

		export interface IsValidResponse {
				errors: {[key:string]:common.FieldError}
		}
	

		export interface DeactivateTextModuleRequest {
				id: string
		}
	

		export interface GetOmimGRequest {
				search: string
		}
	

		export interface OmimG {
				id: string
				code: string
				genName: string
		}
	

		export interface GetOmimGResponse {
				omimGs: Array<OmimG>
		}
	

		export interface EventTextModuleUpdated {
				id: string
				textModule: text_module_common.TextModule
		}
	


// enum definitions

// method name convention const
		export const EVENT_GetTextModules = "api.app.admin.TextModuleApp.GetTextModules";
		export const EVENT_GetTextModules_Response = "api.app.admin.TextModuleApp.GetTextModules.Response";
		export const EVENT_CreateTextModule = "api.app.admin.TextModuleApp.CreateTextModule";
		export const EVENT_CreateTextModule_Response = "api.app.admin.TextModuleApp.CreateTextModule.Response";
		export const EVENT_UpdateTextModule = "api.app.admin.TextModuleApp.UpdateTextModule";
		export const EVENT_UpdateTextModule_Response = "api.app.admin.TextModuleApp.UpdateTextModule.Response";
		export const EVENT_IsValid = "api.app.admin.TextModuleApp.IsValid";
		export const EVENT_IsValid_Response = "api.app.admin.TextModuleApp.IsValid.Response";
		export const EVENT_DeactivateTextModule = "api.app.admin.TextModuleApp.DeactivateTextModule";
		export const EVENT_DeactivateTextModule_Response = "api.app.admin.TextModuleApp.DeactivateTextModule.Response";
		export const EVENT_getOmimG = "api.app.admin.TextModuleApp.GetOmimG";
		export const EVENT_getOmimG_Response = "api.app.admin.TextModuleApp.GetOmimG.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetTextModules = "/api/app/admin/text/module/getTextModules";
        export const LEGACY_TOPIC_CreateTextModule = "/api/app/admin/text/module/createTextModule";
        export const LEGACY_TOPIC_UpdateTextModule = "/api/app/admin/text/module/updateTextModule";
        export const LEGACY_TOPIC_IsValid = "/api/app/admin/text/module/isValid";
        export const LEGACY_TOPIC_DeactivateTextModule = "/api/app/admin/text/module/deactivateTextModule";
        export const LEGACY_TOPIC_GetOmimG = "/api/app/admin/text/module/getOmimG";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getTextModules(request: text_module_common.TextModulePaginationRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<text_module_common.TextModulePaginationResponse>("POST", LEGACY_TOPIC_GetTextModules, { init , request})
			}

			export function useQueryGetTextModules<TransformedType =text_module_common.TextModulePaginationResponse>(payload: text_module_common.TextModulePaginationRequest,ops?: CustomUseQueryOptions<ResponseType<text_module_common.TextModulePaginationResponse>, TransformedType>) {
                return useQuery<ResponseType<text_module_common.TextModulePaginationResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTextModules, payload],
					queryFn: async ({ signal }) => await getTextModules(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTextModules(opts?: UseMutationOptions<ResponseType<text_module_common.TextModulePaginationResponse>, ErrorType,text_module_common.TextModulePaginationRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getTextModules(request),
						retry: false,
						...opts
                });
            }
    
			export async function createTextModule(request: CreateTextModuleRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_CreateTextModule, { init , request})
			}

			export function useQueryCreateTextModule<TransformedType =any>(payload: CreateTextModuleRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateTextModule, payload],
					queryFn: async ({ signal }) => await createTextModule(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateTextModule(opts?: UseMutationOptions<ResponseType<any>, ErrorType,CreateTextModuleRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createTextModule(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateTextModule(request: UpdateTextModuleRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UpdateTextModule, { init , request})
			}

			export function useQueryUpdateTextModule<TransformedType =any>(payload: UpdateTextModuleRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateTextModule, payload],
					queryFn: async ({ signal }) => await updateTextModule(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateTextModule(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UpdateTextModuleRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateTextModule(request),
						retry: false,
						...opts
                });
            }
    
			export async function isValid(request: CreateTextModuleRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<IsValidResponse>("POST", LEGACY_TOPIC_IsValid, { init , request})
			}

			export function useQueryIsValid<TransformedType =IsValidResponse>(payload: CreateTextModuleRequest,ops?: CustomUseQueryOptions<ResponseType<IsValidResponse>, TransformedType>) {
                return useQuery<ResponseType<IsValidResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_IsValid, payload],
					queryFn: async ({ signal }) => await isValid(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationIsValid(opts?: UseMutationOptions<ResponseType<IsValidResponse>, ErrorType,CreateTextModuleRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await isValid(request),
						retry: false,
						...opts
                });
            }
    
			export async function deactivateTextModule(request: DeactivateTextModuleRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeactivateTextModule, { init , request})
			}

			export function useQueryDeactivateTextModule<TransformedType =any>(payload: DeactivateTextModuleRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeactivateTextModule, payload],
					queryFn: async ({ signal }) => await deactivateTextModule(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeactivateTextModule(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeactivateTextModuleRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deactivateTextModule(request),
						retry: false,
						...opts
                });
            }
    
			export async function getOmimG(request: GetOmimGRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetOmimGResponse>("POST", LEGACY_TOPIC_GetOmimG, { init , request})
			}

			export function useQueryGetOmimG<TransformedType =GetOmimGResponse>(payload: GetOmimGRequest,ops?: CustomUseQueryOptions<ResponseType<GetOmimGResponse>, TransformedType>) {
                return useQuery<ResponseType<GetOmimGResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getOmimG, payload],
					queryFn: async ({ signal }) => await getOmimG(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetOmimG(opts?: UseMutationOptions<ResponseType<GetOmimGResponse>, ErrorType,GetOmimGRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getOmimG(request),
						retry: false,
						...opts
                });
            }
    

