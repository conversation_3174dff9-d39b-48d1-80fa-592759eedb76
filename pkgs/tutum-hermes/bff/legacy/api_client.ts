import { UseQueryOptions } from '@tanstack/react-query';

let baseUrl = '';
const headers: HeadersInit = {};

function setHeader(ops: HeadersInit) {
  for (const key in ops) {
    headers[key] = ops[key];
  }
}

function setBaseUrl(url: string) {
  baseUrl = url;
}

function getBaseUrl() {
  return baseUrl;
}

type ResponseType<T> = {
  status: number;
  data: T;
  headers: Headers;
};

type JsonErrorType = {
  message: string;
  logref?: string;
  path?: string;
  _links?: { [key: string]: string[] };
  _embedded?: { [key: string]: any[] };
  traceId?: string;
  validationErrors?: ValidationError[];
  serverError?: string;
  serverErrorParam?: any;
  statusCode: number;
};

type ValidationError = {
  namespace: string;
  field: string;
  rule: string;
  value: any;
  param: string;
};

type FetchPromise<T = any> = Promise<ResponseType<T>>;

function NewError(errData: JsonErrorType, statusCode: number): ErrorType {
  return {
    name: errData.serverError ?? '',
    message: errData.message,
    response: {
      data: errData,
    },
    statusCode,
  };
}

type ResponseFormat = 'json' | 'text' | 'blob';

interface CustomRequestInit extends RequestInit {
  responseType?: ResponseFormat;
}

type Options = {
  request?: any;
  init?: CustomRequestInit;
};

function isJson(str: string) {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

async function fetchWithHeaders<T = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  ops: Options = { init: {} }
): FetchPromise<T> {
  try {
    const { init } = ops;
    const fetchOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      credentials: 'include',
      ...init,
    };

    if (ops.request) {
      fetchOptions.body = JSON.stringify(ops.request);
    }

    const response = await fetch(`${baseUrl}${url}`, fetchOptions);
    const responseType = init?.responseType ?? 'json';
    if (responseType === 'blob') {
      return {
        status: response.status,
        data: (await response.blob()) as T,
        headers: response.headers,
      };
    }

    const text = await response.text();
    if (!response.ok) {
      if (isJson(text)) {
        const errData = JSON.parse(text);
        throw NewError(errData, response.status);
      }

      throw NewError(
        {
          message: response.statusText,
          statusCode: response.status,
        },
        response.status
      );
    }

    if (!text) {
      return { data: null, status: response.status, headers: response.headers };
    }

    let data: T;
    switch (responseType) {
      case 'text':
        data = text as T;
        break;
      case 'json':
      default:
        data = JSON.parse(text) as T;
        break;
    }

    return {
      status: response.status,
      data,
      headers: response.headers,
    };
  } catch (error) {
    return Promise.reject(error);
  }
}

type CustomUseQueryOptions<T, K> = Pick<
  UseQueryOptions<T, ErrorType, K, any>,
  'select' | 'enabled' | 'throwOnError' | 'staleTime' | 'gcTime'
>;

interface ErrorType extends Error {
  response: {
    data: JsonErrorType;
  };
  statusCode: number;
}

export { fetchWithHeaders, setBaseUrl, getBaseUrl, setHeader };
export type {
  CustomUseQueryOptions,
  ErrorType,
  ResponseType,
  FetchPromise,
  CustomRequestInit,
};
