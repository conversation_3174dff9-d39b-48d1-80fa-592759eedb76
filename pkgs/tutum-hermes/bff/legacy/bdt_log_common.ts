/* eslint-disable */
// This code was autogenerated from service/domains/bdt_log_common.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';



// Type definitions
		export interface BdtLogModel {
				fileName: string
				status: BdtLogStatus
				readRaws: number
				totalRaws: number
				percent?: number
		}
	

		export interface UploadDocumentLogModel {
				fileName: string
				processedFile: number
				totalFile: number
				status: BdtLogStatus
		}
	


// enum definitions
    export enum BdtLogStatus {
        Status_Processing = "Status_Processing",
        Status_Success = "Status_Success",
        Status_Failed = "Status_Failed",
        Status_Extracting = "Status_Extracting",
    }


// method name convention const

// Define constants
// method name convention const
