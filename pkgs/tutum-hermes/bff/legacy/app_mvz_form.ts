/* eslint-disable */
// This code was autogenerated from app/mvz/form.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common1 from "./eau_common"
import * as common from "./form_common"
import * as schein_common from "./schein_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface GetFormRequest {
				id?: string
				oKV?: string
				ikNumber?: number
				contractId?: string
		}
	

		export interface GetFormResponse {
				form: common.Form
		}
	

		export interface PrescribeRequest {
				prescribe: common.Prescribe
				printOption?: common.PrintOption
				eAUSetting?: common1.EAUSetting
		}
	

		export interface PrescribeResponse {
				prescribe: common.Prescribe
				pdfBase64: string
				pdfBase64List?: Array<string>
		}
	

		export interface BuildBundleAndValidationRequest {
				prescribe: common.Prescribe
				printOption?: common.PrintOption
		}
	

		export interface BuildBundleAndValidationResponse {
				eAUValidation?: common1.EAUValidation
		}
	

		export interface GetPrescribeRequest {
				prescribeId: string
		}
	

		export interface GetPrescribeResponse {
				prescribe: common.Prescribe
		}
	

		export interface GetFormsResponse {
				forms: Array<common.Form>
		}
	

		export interface PrintRequest {
				prescribeId: string
				printOption: common.PrintOption
				eAUSetting?: common1.EAUSetting
		}
	

		export interface PrintData {
				formUrls: Array<string>
		}
	

		export interface PrintResponse {
				printData: Array<PrintData>
		}
	

		export interface GetFormsRequest {
				oKV: string
				ikNumber: number
				contractId: string
				chargeSystemId: string
				moduleChargeSystemId?: string
				scheinMainGroup?: schein_common.MainGroup
		}
	

		export interface PrintPlainPdfRequest {
				formSetting: string
				formName: string
				treatmentDoctorId?: string
				isRemoveBackground?: boolean
				contractId?: string
		}
	

		export interface PrintPlainPdfResponse {
				formUrl: string
		}
	

		export interface GetFileUrlRequest {
				fileName: string
		}
	

		export interface GetFileUrlResponse {
				fileUrl: string
		}
	

		export interface PrescribeResponseV2 {
				printInfos: Array<common.PrintResult>
				timelineId: string
		}
	

		export interface GetIndicatorActiveIngredientsRequest {
				contractId: string
		}
	

		export interface AtcDiagnoseCode {
				atcCode?: string
				diagnoseCode?: string
		}
	

		export interface GetIndicatorActiveIngredientsResponse {
				data: Array<AtcDiagnoseCode>
		}
	

		export interface GetIcdFormRequest {
				contractId: string
		}
	

		export interface GetIcdFormResponse {
				icdCodes: Array<string>
		}
	


// enum definitions
    export enum KBV_PRF_NR {
        KBV_PRF_LABEL = "label_prf_nr",
        KBV_PRF_VALUE = "Y/9/2407/36/001",
    }

    export enum EHIC_PRF_NR {
        EHIC_PRF_LABEL = "label_ehic_prf_nr",
        EHIC_PRF_VALUE = "Y/1/2404/36/701",
    }


// method name convention const
		export const EVENT_GetForm = "api.app.mvz.FormAPP.GetForm";
		export const EVENT_GetForm_Response = "api.app.mvz.FormAPP.GetForm.Response";
		export const EVENT_GetForms = "api.app.mvz.FormAPP.GetForms";
		export const EVENT_GetForms_Response = "api.app.mvz.FormAPP.GetForms.Response";
		export const EVENT_GetIcdForm = "api.app.mvz.FormAPP.GetIcdForm";
		export const EVENT_GetIcdForm_Response = "api.app.mvz.FormAPP.GetIcdForm.Response";
		export const EVENT_GetPrescribe = "api.app.mvz.FormAPP.GetPrescribe";
		export const EVENT_GetPrescribe_Response = "api.app.mvz.FormAPP.GetPrescribe.Response";
		export const EVENT_Print = "api.app.mvz.FormAPP.Print";
		export const EVENT_Print_Response = "api.app.mvz.FormAPP.Print.Response";
		export const EVENT_PrintPlainPdf = "api.app.mvz.FormAPP.PrintPlainPdf";
		export const EVENT_PrintPlainPdf_Response = "api.app.mvz.FormAPP.PrintPlainPdf.Response";
		export const EVENT_GetAllForms = "api.app.mvz.FormAPP.GetAllForms";
		export const EVENT_GetAllForms_Response = "api.app.mvz.FormAPP.GetAllForms.Response";
		export const EVENT_GetFileUrl = "api.app.mvz.FormAPP.GetFileUrl";
		export const EVENT_GetFileUrl_Response = "api.app.mvz.FormAPP.GetFileUrl.Response";
		export const EVENT_BuildBundleAndValidation = "api.app.mvz.FormAPP.BuildBundleAndValidation";
		export const EVENT_BuildBundleAndValidation_Response = "api.app.mvz.FormAPP.BuildBundleAndValidation.Response";
		export const EVENT_PrescribeV2 = "api.app.mvz.FormAPP.PrescribeV2";
		export const EVENT_PrescribeV2_Response = "api.app.mvz.FormAPP.PrescribeV2.Response";
		export const EVENT_GetIndicatorActiveIngredients = "api.app.mvz.FormAPP.GetIndicatorActiveIngredients";
		export const EVENT_GetIndicatorActiveIngredients_Response = "api.app.mvz.FormAPP.GetIndicatorActiveIngredients.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetForm = "/api/app/mvz/form/app/getForm";
        export const LEGACY_TOPIC_GetForms = "/api/app/mvz/form/app/getForms";
        export const LEGACY_TOPIC_GetIcdForm = "/api/app/mvz/form/app/getIcdForm";
        export const LEGACY_TOPIC_GetPrescribe = "/api/app/mvz/form/app/getPrescribe";
        export const LEGACY_TOPIC_Print = "/api/app/mvz/form/app/print";
        export const LEGACY_TOPIC_PrintPlainPdf = "/api/app/mvz/form/app/printPlainPdf";
        export const LEGACY_TOPIC_GetAllForms = "/api/app/mvz/form/app/getAllForms";
        export const LEGACY_TOPIC_GetFileUrl = "/api/app/mvz/form/app/getFileUrl";
        export const LEGACY_TOPIC_BuildBundleAndValidation = "/api/app/mvz/form/app/buildBundleAndValidation";
        export const LEGACY_TOPIC_PrescribeV2 = "/api/app/mvz/form/app/prescribeV2";
        export const LEGACY_TOPIC_GetIndicatorActiveIngredients = "/api/app/mvz/form/app/getIndicatorActiveIngredients";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getForm(request: GetFormRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetFormResponse>("POST", LEGACY_TOPIC_GetForm, { init , request})
			}

			export function useQueryGetForm<TransformedType =GetFormResponse>(payload: GetFormRequest,ops?: CustomUseQueryOptions<ResponseType<GetFormResponse>, TransformedType>) {
                return useQuery<ResponseType<GetFormResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetForm, payload],
					queryFn: async ({ signal }) => await getForm(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetForm(opts?: UseMutationOptions<ResponseType<GetFormResponse>, ErrorType,GetFormRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getForm(request),
						retry: false,
						...opts
                });
            }
    
			export async function getForms(request: GetFormsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetFormsResponse>("POST", LEGACY_TOPIC_GetForms, { init , request})
			}

			export function useQueryGetForms<TransformedType =GetFormsResponse>(payload: GetFormsRequest,ops?: CustomUseQueryOptions<ResponseType<GetFormsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetFormsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetForms, payload],
					queryFn: async ({ signal }) => await getForms(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetForms(opts?: UseMutationOptions<ResponseType<GetFormsResponse>, ErrorType,GetFormsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getForms(request),
						retry: false,
						...opts
                });
            }
    
			export async function getIcdForm(request: GetIcdFormRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetIcdFormResponse>("POST", LEGACY_TOPIC_GetIcdForm, { init , request})
			}

			export function useQueryGetIcdForm<TransformedType =GetIcdFormResponse>(payload: GetIcdFormRequest,ops?: CustomUseQueryOptions<ResponseType<GetIcdFormResponse>, TransformedType>) {
                return useQuery<ResponseType<GetIcdFormResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetIcdForm, payload],
					queryFn: async ({ signal }) => await getIcdForm(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetIcdForm(opts?: UseMutationOptions<ResponseType<GetIcdFormResponse>, ErrorType,GetIcdFormRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getIcdForm(request),
						retry: false,
						...opts
                });
            }
    
			export async function getPrescribe(request: GetPrescribeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetPrescribeResponse>("POST", LEGACY_TOPIC_GetPrescribe, { init , request})
			}

			export function useQueryGetPrescribe<TransformedType =GetPrescribeResponse>(payload: GetPrescribeRequest,ops?: CustomUseQueryOptions<ResponseType<GetPrescribeResponse>, TransformedType>) {
                return useQuery<ResponseType<GetPrescribeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPrescribe, payload],
					queryFn: async ({ signal }) => await getPrescribe(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPrescribe(opts?: UseMutationOptions<ResponseType<GetPrescribeResponse>, ErrorType,GetPrescribeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPrescribe(request),
						retry: false,
						...opts
                });
            }
    
			export async function print(request: PrintRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PrintResponse>("POST", LEGACY_TOPIC_Print, { init , request})
			}

			export function useQueryPrint<TransformedType =PrintResponse>(payload: PrintRequest,ops?: CustomUseQueryOptions<ResponseType<PrintResponse>, TransformedType>) {
                return useQuery<ResponseType<PrintResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_Print, payload],
					queryFn: async ({ signal }) => await print(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationPrint(opts?: UseMutationOptions<ResponseType<PrintResponse>, ErrorType,PrintRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await print(request),
						retry: false,
						...opts
                });
            }
    
			export async function printPlainPdf(request: PrintPlainPdfRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PrintPlainPdfResponse>("POST", LEGACY_TOPIC_PrintPlainPdf, { init , request})
			}

			export function useQueryPrintPlainPdf<TransformedType =PrintPlainPdfResponse>(payload: PrintPlainPdfRequest,ops?: CustomUseQueryOptions<ResponseType<PrintPlainPdfResponse>, TransformedType>) {
                return useQuery<ResponseType<PrintPlainPdfResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_PrintPlainPdf, payload],
					queryFn: async ({ signal }) => await printPlainPdf(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationPrintPlainPdf(opts?: UseMutationOptions<ResponseType<PrintPlainPdfResponse>, ErrorType,PrintPlainPdfRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await printPlainPdf(request),
						retry: false,
						...opts
                });
            }
    
			export async function getAllForms(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetFormsResponse>("POST", LEGACY_TOPIC_GetAllForms, { init })
			}

			export function useQueryGetAllForms<TransformedType =GetFormsResponse>(ops?: CustomUseQueryOptions<ResponseType<GetFormsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetFormsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetAllForms],
					queryFn: async ({ signal }) => await getAllForms({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetAllForms(opts?: UseMutationOptions<ResponseType<GetFormsResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getAllForms(),
						retry: false,
						...opts
                });
            }
    
			export async function getFileUrl(request: GetFileUrlRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetFileUrlResponse>("POST", LEGACY_TOPIC_GetFileUrl, { init , request})
			}

			export function useQueryGetFileUrl<TransformedType =GetFileUrlResponse>(payload: GetFileUrlRequest,ops?: CustomUseQueryOptions<ResponseType<GetFileUrlResponse>, TransformedType>) {
                return useQuery<ResponseType<GetFileUrlResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetFileUrl, payload],
					queryFn: async ({ signal }) => await getFileUrl(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetFileUrl(opts?: UseMutationOptions<ResponseType<GetFileUrlResponse>, ErrorType,GetFileUrlRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getFileUrl(request),
						retry: false,
						...opts
                });
            }
    
			export async function buildBundleAndValidation(request: BuildBundleAndValidationRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<BuildBundleAndValidationResponse>("POST", LEGACY_TOPIC_BuildBundleAndValidation, { init , request})
			}

			export function useQueryBuildBundleAndValidation<TransformedType =BuildBundleAndValidationResponse>(payload: BuildBundleAndValidationRequest,ops?: CustomUseQueryOptions<ResponseType<BuildBundleAndValidationResponse>, TransformedType>) {
                return useQuery<ResponseType<BuildBundleAndValidationResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_BuildBundleAndValidation, payload],
					queryFn: async ({ signal }) => await buildBundleAndValidation(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationBuildBundleAndValidation(opts?: UseMutationOptions<ResponseType<BuildBundleAndValidationResponse>, ErrorType,BuildBundleAndValidationRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await buildBundleAndValidation(request),
						retry: false,
						...opts
                });
            }
    
			export async function prescribeV2(request: PrescribeRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<PrescribeResponseV2>("POST", LEGACY_TOPIC_PrescribeV2, { init , request})
			}

			export function useQueryPrescribeV2<TransformedType =PrescribeResponseV2>(payload: PrescribeRequest,ops?: CustomUseQueryOptions<ResponseType<PrescribeResponseV2>, TransformedType>) {
                return useQuery<ResponseType<PrescribeResponseV2>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_PrescribeV2, payload],
					queryFn: async ({ signal }) => await prescribeV2(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationPrescribeV2(opts?: UseMutationOptions<ResponseType<PrescribeResponseV2>, ErrorType,PrescribeRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await prescribeV2(request),
						retry: false,
						...opts
                });
            }
    
			export async function getIndicatorActiveIngredients(request: GetIndicatorActiveIngredientsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetIndicatorActiveIngredientsResponse>("POST", LEGACY_TOPIC_GetIndicatorActiveIngredients, { init , request})
			}

			export function useQueryGetIndicatorActiveIngredients<TransformedType =GetIndicatorActiveIngredientsResponse>(payload: GetIndicatorActiveIngredientsRequest,ops?: CustomUseQueryOptions<ResponseType<GetIndicatorActiveIngredientsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetIndicatorActiveIngredientsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetIndicatorActiveIngredients, payload],
					queryFn: async ({ signal }) => await getIndicatorActiveIngredients(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetIndicatorActiveIngredients(opts?: UseMutationOptions<ResponseType<GetIndicatorActiveIngredientsResponse>, ErrorType,GetIndicatorActiveIngredientsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getIndicatorActiveIngredients(request),
						retry: false,
						...opts
                });
            }
    

