/* eslint-disable */
// This code was autogenerated from app/mvz/document_management.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as common1 from "./common"
import * as common2 from "./document_management_common"
import * as common from "./document_type_common"
import * as patient_profile_common from "./patient_profile_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface EventDocumentManagementChange {
		}
	

		export interface EventGdtExportResult {
				result: boolean
				error: string
		}
	

		export interface EventGdtImportResult {
				result: boolean
				patientName: string
				error: string
				type: common2.DocumentNotificationType
		}
	

		export interface CreateDocumentManagementRequest {
				documentName: string
				companionFileId: number
				companionFilePath: string
				status: common2.DocumentManagementStatus
		}
	

		export interface CreateDocumentManagementResponse {
				id: string
				documentManagementModel: common2.DocumentManagementModel
				presignedUrl: string
		}
	

		export interface UpdateDocumentManagementStatusRequest {
				id: string
				status: common2.DocumentManagementStatus
				importedDate: number
		}
	

		export interface UpdateDocumentManagementStatusResponse {
				id: string
				documentManagementModel: common2.DocumentManagementModel
		}
	

		export interface ListDocumentManagementRequest {
				value?: string
				isNotAssigned: boolean
				pagination?: common1.PaginationRequest
				fromDate?: number
				toDate?: number
				status?: common2.DocumentManagementStatus
				patientIds?: Array<string>
				senderIds?: Array<string>
		}
	

		export interface ListDocumentManagementResponse {
				data: Array<common2.DocumentManagementItem>
				pagination?: common1.PaginationResponse
		}
	

		export interface AssignPatientDocumentRequest {
				id: string
				patientId?: string
				sender?: common2.DocumentManagementSender
				documentType?: common.DocumentType
				description?: string
		}
	

		export interface GetDocumentManagementRequest {
				id: string
		}
	

		export interface GetDocumentManagementResponse {
				data: common2.DocumentManagementItem
		}
	

		export interface DeleteDocumentManagementRequest {
				id: string
				patientId?: string
		}
	

		export interface DeleteFailDocumentManagementRequest {
				ids: Array<string>
				isAll: boolean
		}
	

		export interface MarkReadDocumentManagementRequest {
				id: string
				patientId?: string
		}
	

		export interface UploadFile {
				fileName: string
				objectId: string
		}
	

		export interface UploadDocumentManagementRequest {
				files: Array<UploadFile>
		}
	

		export interface GetDocumentBadgeResponse {
				unassignedCount: number
				failCount: number
				totalCount: number
		}
	

		export interface ReImportFailDocumentRequest {
				ids: Array<string>
				isAll: boolean
		}
	

		export interface ExportGdtDocumentRequest {
				gdtExportSettingId: string
				treatmentDate?: number
				readingDate?: number
				patientId: string
				scheinId?: string
				treatmentTime?: number
				readingTime?: number
		}
	

		export interface LabParameter {
				name: string
				min: string
				max: string
				unit: string
		}
	

		export interface LabResultItem {
				name: string
				value: string
				icon: string
				testNote: string
				testResultText: string
		}
	

		export interface LabResultOverview {
				date: number
				labOrderId: string
				items: Array<LabResultItem>
		}
	

		export interface GetLabResultsRequest {
				patientId: string
				results?: number
				fromDate?: number
				toDate?: number
				fieldNames?: Array<string>
				isOnlyPathologicalResults?: boolean
		}
	

		export interface GetLabResultsResponse {
				labParameters: Array<LabParameter>
				availableLabParameters: Array<LabParameter>
				labResults: Array<LabResultOverview>
		}
	

		export interface GetLabResultsPDFResponse {
				pdf: Int8Array
				mode: LabResultsPDFMode
		}
	

		export interface ExportGdtDocumentResponse {
		}
	


// enum definitions
    export enum LabResultsPDFMode {
        PORTRAIT = "portrait",
        LANDSCAPE = "landscape",
    }


// method name convention const
		export const EVENT_CreateDocumentManagement = "api.app.mvz.DocumentManagementApp.CreateDocumentManagement";
		export const EVENT_CreateDocumentManagement_Response = "api.app.mvz.DocumentManagementApp.CreateDocumentManagement.Response";
		export const EVENT_UpdateDocumentManagementStatus = "api.app.mvz.DocumentManagementApp.UpdateDocumentManagementStatus";
		export const EVENT_UpdateDocumentManagementStatus_Response = "api.app.mvz.DocumentManagementApp.UpdateDocumentManagementStatus.Response";
		export const EVENT_ListDocumentManagement = "api.app.mvz.DocumentManagementApp.ListDocumentManagement";
		export const EVENT_ListDocumentManagement_Response = "api.app.mvz.DocumentManagementApp.ListDocumentManagement.Response";
		export const EVENT_AssignPatientDocument = "api.app.mvz.DocumentManagementApp.AssignPatientDocument";
		export const EVENT_AssignPatientDocument_Response = "api.app.mvz.DocumentManagementApp.AssignPatientDocument.Response";
		export const EVENT_GetDocumentManagement = "api.app.mvz.DocumentManagementApp.GetDocumentManagement";
		export const EVENT_GetDocumentManagement_Response = "api.app.mvz.DocumentManagementApp.GetDocumentManagement.Response";
		export const EVENT_MarkReadDocumentManagement = "api.app.mvz.DocumentManagementApp.MarkReadDocumentManagement";
		export const EVENT_MarkReadDocumentManagement_Response = "api.app.mvz.DocumentManagementApp.MarkReadDocumentManagement.Response";
		export const EVENT_DeleteDocumentManagement = "api.app.mvz.DocumentManagementApp.DeleteDocumentManagement";
		export const EVENT_DeleteDocumentManagement_Response = "api.app.mvz.DocumentManagementApp.DeleteDocumentManagement.Response";
		export const EVENT_DeleteFailDocumentManagement = "api.app.mvz.DocumentManagementApp.DeleteFailDocumentManagement";
		export const EVENT_DeleteFailDocumentManagement_Response = "api.app.mvz.DocumentManagementApp.DeleteFailDocumentManagement.Response";
		export const EVENT_HandleEventDocumentManagementChange = "api.app.mvz.DocumentManagementApp.HandleEventDocumentManagementChange";
		export const EVENT_HandleEventDocumentManagementChange_Response = "api.app.mvz.DocumentManagementApp.HandleEventDocumentManagementChange.Response";
		export const EVENT_UploadDocumentManagement = "api.app.mvz.DocumentManagementApp.UploadDocumentManagement";
		export const EVENT_UploadDocumentManagement_Response = "api.app.mvz.DocumentManagementApp.UploadDocumentManagement.Response";
		export const EVENT_GetDocumentBadge = "api.app.mvz.DocumentManagementApp.GetDocumentBadge";
		export const EVENT_GetDocumentBadge_Response = "api.app.mvz.DocumentManagementApp.GetDocumentBadge.Response";
		export const EVENT_ReImportFailDocument = "api.app.mvz.DocumentManagementApp.ReImportFailDocument";
		export const EVENT_ReImportFailDocument_Response = "api.app.mvz.DocumentManagementApp.ReImportFailDocument.Response";
		export const EVENT_ProcessDocumentUpload = "api.app.mvz.DocumentManagementApp.ProcessDocumentUpload";
		export const EVENT_ProcessDocumentUpload_Response = "api.app.mvz.DocumentManagementApp.ProcessDocumentUpload.Response";
		export const EVENT_ExportGdtDocument = "api.app.mvz.DocumentManagementApp.ExportGdtDocument";
		export const EVENT_ExportGdtDocument_Response = "api.app.mvz.DocumentManagementApp.ExportGdtDocument.Response";
		export const EVENT_ProcessImportGdtDocuments = "api.app.mvz.DocumentManagementApp.ProcessImportGdtDocuments";
		export const EVENT_ProcessImportGdtDocuments_Response = "api.app.mvz.DocumentManagementApp.ProcessImportGdtDocuments.Response";
		export const EVENT_ProcessImportLdtDocuments = "api.app.mvz.DocumentManagementApp.ProcessImportLdtDocuments";
		export const EVENT_ProcessImportLdtDocuments_Response = "api.app.mvz.DocumentManagementApp.ProcessImportLdtDocuments.Response";
		export const EVENT_OnPatientUpdate = "api.app.mvz.DocumentManagementApp.OnPatientUpdate";
		export const EVENT_OnPatientUpdate_Response = "api.app.mvz.DocumentManagementApp.OnPatientUpdate.Response";
		export const EVENT_GetLabResults = "api.app.mvz.DocumentManagementApp.GetLabResults";
		export const EVENT_GetLabResults_Response = "api.app.mvz.DocumentManagementApp.GetLabResults.Response";
		export const EVENT_GetLabResultsPDF = "api.app.mvz.DocumentManagementApp.GetLabResultsPDF";
		export const EVENT_GetLabResultsPDF_Response = "api.app.mvz.DocumentManagementApp.GetLabResultsPDF.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_CreateDocumentManagement = "/api/app/mvz/document/management/createDocumentManagement";
        export const LEGACY_TOPIC_UpdateDocumentManagementStatus = "/api/app/mvz/document/management/updateDocumentManagementStatus";
        export const LEGACY_TOPIC_ListDocumentManagement = "/api/app/mvz/document/management/listDocumentManagement";
        export const LEGACY_TOPIC_AssignPatientDocument = "/api/app/mvz/document/management/assignPatientDocument";
        export const LEGACY_TOPIC_GetDocumentManagement = "/api/app/mvz/document/management/getDocumentManagement";
        export const LEGACY_TOPIC_MarkReadDocumentManagement = "/api/app/mvz/document/management/markReadDocumentManagement";
        export const LEGACY_TOPIC_DeleteDocumentManagement = "/api/app/mvz/document/management/deleteDocumentManagement";
        export const LEGACY_TOPIC_DeleteFailDocumentManagement = "/api/app/mvz/document/management/deleteFailDocumentManagement";
        export const LEGACY_TOPIC_HandleEventDocumentManagementChange = "/api/app/mvz/document/management/handleEventDocumentManagementChange";
        export const LEGACY_TOPIC_UploadDocumentManagement = "/api/app/mvz/document/management/uploadDocumentManagement";
        export const LEGACY_TOPIC_GetDocumentBadge = "/api/app/mvz/document/management/getDocumentBadge";
        export const LEGACY_TOPIC_ReImportFailDocument = "/api/app/mvz/document/management/reImportFailDocument";
        export const LEGACY_TOPIC_ProcessDocumentUpload = "/api/app/mvz/document/management/processDocumentUpload";
        export const LEGACY_TOPIC_ExportGdtDocument = "/api/app/mvz/document/management/exportGdtDocument";
        export const LEGACY_TOPIC_ProcessImportGdtDocuments = "/api/app/mvz/document/management/processImportGdtDocuments";
        export const LEGACY_TOPIC_ProcessImportLdtDocuments = "/api/app/mvz/document/management/processImportLdtDocuments";
        export const LEGACY_TOPIC_OnPatientUpdate = "/api/app/mvz/document/management/onPatientUpdate";
        export const LEGACY_TOPIC_GetLabResults = "/api/app/mvz/document/management/getLabResults";
        export const LEGACY_TOPIC_GetLabResultsPDF = "/api/app/mvz/document/management/getLabResultsPDF";


// Define action methods and their listener -----------------------------------------------------------------
			export async function createDocumentManagement(request: CreateDocumentManagementRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CreateDocumentManagementResponse>("POST", LEGACY_TOPIC_CreateDocumentManagement, { init , request})
			}

			export function useQueryCreateDocumentManagement<TransformedType =CreateDocumentManagementResponse>(payload: CreateDocumentManagementRequest,ops?: CustomUseQueryOptions<ResponseType<CreateDocumentManagementResponse>, TransformedType>) {
                return useQuery<ResponseType<CreateDocumentManagementResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateDocumentManagement, payload],
					queryFn: async ({ signal }) => await createDocumentManagement(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateDocumentManagement(opts?: UseMutationOptions<ResponseType<CreateDocumentManagementResponse>, ErrorType,CreateDocumentManagementRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createDocumentManagement(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateDocumentManagementStatus(request: UpdateDocumentManagementStatusRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<UpdateDocumentManagementStatusResponse>("POST", LEGACY_TOPIC_UpdateDocumentManagementStatus, { init , request})
			}

			export function useQueryUpdateDocumentManagementStatus<TransformedType =UpdateDocumentManagementStatusResponse>(payload: UpdateDocumentManagementStatusRequest,ops?: CustomUseQueryOptions<ResponseType<UpdateDocumentManagementStatusResponse>, TransformedType>) {
                return useQuery<ResponseType<UpdateDocumentManagementStatusResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateDocumentManagementStatus, payload],
					queryFn: async ({ signal }) => await updateDocumentManagementStatus(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateDocumentManagementStatus(opts?: UseMutationOptions<ResponseType<UpdateDocumentManagementStatusResponse>, ErrorType,UpdateDocumentManagementStatusRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateDocumentManagementStatus(request),
						retry: false,
						...opts
                });
            }
    
			export async function listDocumentManagement(request: ListDocumentManagementRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ListDocumentManagementResponse>("POST", LEGACY_TOPIC_ListDocumentManagement, { init , request})
			}

			export function useQueryListDocumentManagement<TransformedType =ListDocumentManagementResponse>(payload: ListDocumentManagementRequest,ops?: CustomUseQueryOptions<ResponseType<ListDocumentManagementResponse>, TransformedType>) {
                return useQuery<ResponseType<ListDocumentManagementResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ListDocumentManagement, payload],
					queryFn: async ({ signal }) => await listDocumentManagement(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationListDocumentManagement(opts?: UseMutationOptions<ResponseType<ListDocumentManagementResponse>, ErrorType,ListDocumentManagementRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await listDocumentManagement(request),
						retry: false,
						...opts
                });
            }
    
			export async function assignPatientDocument(request: AssignPatientDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_AssignPatientDocument, { init , request})
			}

			export function useQueryAssignPatientDocument<TransformedType =any>(payload: AssignPatientDocumentRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_AssignPatientDocument, payload],
					queryFn: async ({ signal }) => await assignPatientDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationAssignPatientDocument(opts?: UseMutationOptions<ResponseType<any>, ErrorType,AssignPatientDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await assignPatientDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function getDocumentManagement(request: GetDocumentManagementRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetDocumentManagementResponse>("POST", LEGACY_TOPIC_GetDocumentManagement, { init , request})
			}

			export function useQueryGetDocumentManagement<TransformedType =GetDocumentManagementResponse>(payload: GetDocumentManagementRequest,ops?: CustomUseQueryOptions<ResponseType<GetDocumentManagementResponse>, TransformedType>) {
                return useQuery<ResponseType<GetDocumentManagementResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetDocumentManagement, payload],
					queryFn: async ({ signal }) => await getDocumentManagement(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetDocumentManagement(opts?: UseMutationOptions<ResponseType<GetDocumentManagementResponse>, ErrorType,GetDocumentManagementRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getDocumentManagement(request),
						retry: false,
						...opts
                });
            }
    
			export async function markReadDocumentManagement(request: MarkReadDocumentManagementRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_MarkReadDocumentManagement, { init , request})
			}

			export function useQueryMarkReadDocumentManagement<TransformedType =any>(payload: MarkReadDocumentManagementRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkReadDocumentManagement, payload],
					queryFn: async ({ signal }) => await markReadDocumentManagement(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkReadDocumentManagement(opts?: UseMutationOptions<ResponseType<any>, ErrorType,MarkReadDocumentManagementRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markReadDocumentManagement(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteDocumentManagement(request: DeleteDocumentManagementRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteDocumentManagement, { init , request})
			}

			export function useQueryDeleteDocumentManagement<TransformedType =any>(payload: DeleteDocumentManagementRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteDocumentManagement, payload],
					queryFn: async ({ signal }) => await deleteDocumentManagement(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteDocumentManagement(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeleteDocumentManagementRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteDocumentManagement(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteFailDocumentManagement(request: DeleteFailDocumentManagementRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteFailDocumentManagement, { init , request})
			}

			export function useQueryDeleteFailDocumentManagement<TransformedType =any>(payload: DeleteFailDocumentManagementRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteFailDocumentManagement, payload],
					queryFn: async ({ signal }) => await deleteFailDocumentManagement(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteFailDocumentManagement(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeleteFailDocumentManagementRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteFailDocumentManagement(request),
						retry: false,
						...opts
                });
            }
    
    
			export async function uploadDocumentManagement(request: UploadDocumentManagementRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UploadDocumentManagement, { init , request})
			}

			export function useQueryUploadDocumentManagement<TransformedType =any>(payload: UploadDocumentManagementRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UploadDocumentManagement, payload],
					queryFn: async ({ signal }) => await uploadDocumentManagement(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUploadDocumentManagement(opts?: UseMutationOptions<ResponseType<any>, ErrorType,UploadDocumentManagementRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await uploadDocumentManagement(request),
						retry: false,
						...opts
                });
            }
    
			export async function getDocumentBadge(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetDocumentBadgeResponse>("POST", LEGACY_TOPIC_GetDocumentBadge, { init })
			}

			export function useQueryGetDocumentBadge<TransformedType =GetDocumentBadgeResponse>(ops?: CustomUseQueryOptions<ResponseType<GetDocumentBadgeResponse>, TransformedType>) {
                return useQuery<ResponseType<GetDocumentBadgeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetDocumentBadge],
					queryFn: async ({ signal }) => await getDocumentBadge({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetDocumentBadge(opts?: UseMutationOptions<ResponseType<GetDocumentBadgeResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getDocumentBadge(),
						retry: false,
						...opts
                });
            }
    
			export async function reImportFailDocument(request: ReImportFailDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_ReImportFailDocument, { init , request})
			}

			export function useQueryReImportFailDocument<TransformedType =any>(payload: ReImportFailDocumentRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ReImportFailDocument, payload],
					queryFn: async ({ signal }) => await reImportFailDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationReImportFailDocument(opts?: UseMutationOptions<ResponseType<any>, ErrorType,ReImportFailDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await reImportFailDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function processDocumentUpload(init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_ProcessDocumentUpload, { init })
			}

			export function useQueryProcessDocumentUpload<TransformedType =any>(ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ProcessDocumentUpload],
					queryFn: async ({ signal }) => await processDocumentUpload({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationProcessDocumentUpload(opts?: UseMutationOptions<ResponseType<any>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await processDocumentUpload(),
						retry: false,
						...opts
                });
            }
    
			export async function exportGdtDocument(request: ExportGdtDocumentRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<ExportGdtDocumentResponse>("POST", LEGACY_TOPIC_ExportGdtDocument, { init , request})
			}

			export function useQueryExportGdtDocument<TransformedType =ExportGdtDocumentResponse>(payload: ExportGdtDocumentRequest,ops?: CustomUseQueryOptions<ResponseType<ExportGdtDocumentResponse>, TransformedType>) {
                return useQuery<ResponseType<ExportGdtDocumentResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ExportGdtDocument, payload],
					queryFn: async ({ signal }) => await exportGdtDocument(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationExportGdtDocument(opts?: UseMutationOptions<ResponseType<ExportGdtDocumentResponse>, ErrorType,ExportGdtDocumentRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await exportGdtDocument(request),
						retry: false,
						...opts
                });
            }
    
			export async function processImportGdtDocuments(init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_ProcessImportGdtDocuments, { init })
			}

			export function useQueryProcessImportGdtDocuments<TransformedType =any>(ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ProcessImportGdtDocuments],
					queryFn: async ({ signal }) => await processImportGdtDocuments({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationProcessImportGdtDocuments(opts?: UseMutationOptions<ResponseType<any>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await processImportGdtDocuments(),
						retry: false,
						...opts
                });
            }
    
			export async function processImportLdtDocuments(init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_ProcessImportLdtDocuments, { init })
			}

			export function useQueryProcessImportLdtDocuments<TransformedType =any>(ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_ProcessImportLdtDocuments],
					queryFn: async ({ signal }) => await processImportLdtDocuments({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationProcessImportLdtDocuments(opts?: UseMutationOptions<ResponseType<any>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await processImportLdtDocuments(),
						retry: false,
						...opts
                });
            }
    
    
			export async function getLabResults(request: GetLabResultsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetLabResultsResponse>("POST", LEGACY_TOPIC_GetLabResults, { init , request})
			}

			export function useQueryGetLabResults<TransformedType =GetLabResultsResponse>(payload: GetLabResultsRequest,ops?: CustomUseQueryOptions<ResponseType<GetLabResultsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetLabResultsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetLabResults, payload],
					queryFn: async ({ signal }) => await getLabResults(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetLabResults(opts?: UseMutationOptions<ResponseType<GetLabResultsResponse>, ErrorType,GetLabResultsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getLabResults(request),
						retry: false,
						...opts
                });
            }
    
			export async function getLabResultsPDF(request: GetLabResultsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetLabResultsPDFResponse>("POST", LEGACY_TOPIC_GetLabResultsPDF, { init , request})
			}

			export function useQueryGetLabResultsPDF<TransformedType =GetLabResultsPDFResponse>(payload: GetLabResultsRequest,ops?: CustomUseQueryOptions<ResponseType<GetLabResultsPDFResponse>, TransformedType>) {
                return useQuery<ResponseType<GetLabResultsPDFResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetLabResultsPDF, payload],
					queryFn: async ({ signal }) => await getLabResultsPDF(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetLabResultsPDF(opts?: UseMutationOptions<ResponseType<GetLabResultsPDFResponse>, ErrorType,GetLabResultsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getLabResultsPDF(request),
						retry: false,
						...opts
                });
            }
    

