/* eslint-disable */
// This code was autogenerated from app/mvz/schein.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as catalog_sdkt_common from "./catalog_sdkt_common"
import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"
import * as private_schein_common from "./private_schein_common"
import * as schein_common from "./schein_common"
import * as common1 from "./timeline_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface ScheinChangedResponse {
				patientId: string
				scheinId: string
		}
	

		export interface EventScheinChanged {
				data: ScheinChangedResponse
				eventName: EventName
		}
	

		export interface EventScheinChangedResponse {
				data: EventScheinChanged
		}
	

		export interface EventCreateRemoveSchein {
				data: ScheinChangedResponse
		}
	

		export interface IsValidRequest {
				createScheinRequest: CreateScheinRequest
				insurances: Array<patient_profile_common.InsuranceInfo>
		}
	

		export interface IsValidResponse {
				error?: {[key:string]:common.FieldError}
		}
	

		export interface GetKTABsRequest {
				vKNR: string
				specialGroup: patient_profile_common.SpecialGroupDescription
				patientId: string
				quarter: number
				year: number
				bsnr: string
		}
	

		export interface GetKTABsResponse {
				kTABValue: Array<catalog_sdkt_common.KTABValue>
		}
	

		export interface MarkBillRequest {
				contractId?: string
				mainGroup: schein_common.MainGroup
				excludeScheinIds: Array<string>
				validScheinsIds: Array<string>
		}
	

		export interface TakeOverDiagnoseInfo {
				id: string
				isTreatmentRelevant: boolean
		}
	

		export interface CreateScheinRequest {
				scheinId?: string
				patientId: string
				doctorId?: string
				scheinMainGroup: schein_common.MainGroup
				kvTreatmentCase: schein_common.TreatmentCaseNames
				kvScheinSubGroup?: string
				g4101Year: number
				g4101Quarter: number
				tariffType?: string
				bgType?: string
				bgAccidentDate?: number
				bgAccidentTime?: string
				bgWorkingTimeFrom?: string
				bgWorkingTimeTo?: string
				bgEmployerName?: string
				bgEmployerStreet?: string
				bgEmployerHousenumber?: string
				bgEmployerPostcode?: string
				bgEmployerCity?: string
				bgEmployerCountry?: string
				hzvContractId?: string
				scheinDetails?: schein_common.ScheinDetail
				insuranceId: string
				excludeFromBilling: boolean
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				skipFields?: boolean
				g4122?: string
				g4101: string
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
				assignedToBsnrId?: string
		}
	

		export interface CreateScheinResponse {
				scheinItem?: schein_common.ScheinItem
				fieldErrors: {[key:string]:common.FieldError}
		}
	

		export interface GetSubGroupFromMasterDataRequest {
				bsnr: string
		}
	

		export interface GetSubGroupFromMasterDataResponse {
				keys: Array<string>
		}
	

		export interface GetBillingAreaFromMasterDataRequest {
				bsnr: string
				subgroup: string
		}
	

		export interface GetBillingAreaFromMasterDataResponse {
				keys: Array<string>
		}
	

		export interface Rezidiv {
				code: string
				content: string
		}
	

		export interface GetRezidivListResponse {
				data: Array<Rezidiv>
		}
	

		export interface GetPsychotherapyByIdRequest {
				psychotherapyId: string
		}
	

		export interface GetPsychotherapyByIdResponse {
				data: schein_common.Psychotherapy
		}
	

		export interface GetScheinByInsuranceIdRequest {
				insuranceId: string
		}
	

		export interface GetScheinByInsuranceIdResponse {
				scheinItem: schein_common.ScheinItem
		}
	

		export interface GetScheinsByInsuranceIdResponse {
				scheinItems: Array<schein_common.ScheinItem>
		}
	

		export interface RevertTechnicalScheinRequest {
				scheinId: string
		}
	

		export interface CreatePrivateScheinRequest {
				schein: private_schein_common.PrivateScheinItem
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface UpdatePrivateScheinRequest {
				schein: private_schein_common.PrivateScheinItem
		}
	

		export interface DeletePrivateScheinRequest {
				scheinId: string
				patientId: string
		}
	

		export interface IsValidPrivateScheinRequest {
				schein: private_schein_common.PrivateScheinItem
		}
	

		export interface GetPrivateScheinByIdRequest {
				scheinId: string
		}
	

		export interface GetGoaFactorValueRequest {
				scheinId: string
				goaNumber: string
		}
	

		export interface GetGoaFactorValueResponse {
				value: number
		}
	

		export interface TakeOverScheinDiagnisToRequest {
				scheinId: string
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface TakeOverDiagnosisByScheinIdRequest {
				scheinId: string
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface GetScheinItemByIdRequest {
				scheinId: string
		}
	

		export interface GetScheinItemByIdResponse {
				scheinItems: schein_common.ScheinItem
		}
	

		export interface GetScheinItemByIdsRequest {
				scheinIds: Array<string>
		}
	

		export interface GetScheinItemByIdsResponse {
				scheinItems: Array<schein_common.ScheinItem>
		}
	

		export interface CreateBgScheinRequest {
				schein: schein_common.BgScheinItem
				takeOverDiagnoseInfos: Array<TakeOverDiagnoseInfo>
				newTakeOverDiagnosis?: Array<common1.TimelineModel>
		}
	

		export interface UpdateBgScheinRequest {
				schein: schein_common.BgScheinItem
		}
	

		export interface DeleteBgScheinRequest {
				scheinId: string
				patientId: string
		}
	

		export interface IsValidBgScheinRequest {
				schein: schein_common.BgScheinItem
				insurances: patient_profile_common.InsuranceInfo
		}
	

		export interface GetBgScheinByIdRequest {
				scheinId: string
		}
	

		export interface CheckDummyVknrRequest {
				scheinId: string
		}
	

		export interface CheckDummyVknrResponse {
				isDummy: boolean
		}
	

		export interface GetTotalScheinsRequest {
				patientId: string
		}
	

		export interface GetTotalScheinsResponse {
				totalScheins: number
		}
	


// enum definitions
    export enum EventName {
        EventName_UpdateSchein = "EventName_UpdateSchein",
    }


// method name convention const
		export const EVENT_CreateSvScheins = "api.app.mvz.ScheinApp.CreateSvScheins";
		export const EVENT_CreateSvScheins_Response = "api.app.mvz.ScheinApp.CreateSvScheins.Response";
		export const EVENT_IsValid = "api.app.mvz.ScheinApp.IsValid";
		export const EVENT_IsValid_Response = "api.app.mvz.ScheinApp.IsValid.Response";
		export const EVENT_createSchein = "api.app.mvz.ScheinApp.CreateSchein";
		export const EVENT_createSchein_Response = "api.app.mvz.ScheinApp.CreateSchein.Response";
		export const EVENT_takeOverScheinDiagnosis = "api.app.mvz.ScheinApp.TakeOverScheinDiagnosis";
		export const EVENT_takeOverScheinDiagnosis_Response = "api.app.mvz.ScheinApp.TakeOverScheinDiagnosis.Response";
		export const EVENT_getScheinDetailById = "api.app.mvz.ScheinApp.GetScheinDetailById";
		export const EVENT_getScheinDetailById_Response = "api.app.mvz.ScheinApp.GetScheinDetailById.Response";
		export const EVENT_getScheinDetailByIds = "api.app.mvz.ScheinApp.GetScheinDetailByIds";
		export const EVENT_getScheinDetailByIds_Response = "api.app.mvz.ScheinApp.GetScheinDetailByIds.Response";
		export const EVENT_updateSchein = "api.app.mvz.ScheinApp.UpdateSchein";
		export const EVENT_updateSchein_Response = "api.app.mvz.ScheinApp.UpdateSchein.Response";
		export const EVENT_markNotBilled = "api.app.mvz.ScheinApp.MarkNotBilled";
		export const EVENT_markNotBilled_Response = "api.app.mvz.ScheinApp.MarkNotBilled.Response";
		export const EVENT_checkExistKVScheinCurrentQuarter = "api.app.mvz.ScheinApp.CheckExistKVScheinCurrentQuarter";
		export const EVENT_checkExistKVScheinCurrentQuarter_Response = "api.app.mvz.ScheinApp.CheckExistKVScheinCurrentQuarter.Response";
		export const EVENT_deleteSchein = "api.app.mvz.ScheinApp.DeleteSchein";
		export const EVENT_deleteSchein_Response = "api.app.mvz.ScheinApp.DeleteSchein.Response";
		export const EVENT_getScheinDetail = "api.app.mvz.ScheinApp.GetScheinDetail";
		export const EVENT_getScheinDetail_Response = "api.app.mvz.ScheinApp.GetScheinDetail.Response";
		export const EVENT_getFields = "api.app.mvz.ScheinApp.GetFields";
		export const EVENT_getFields_Response = "api.app.mvz.ScheinApp.GetFields.Response";
		export const EVENT_getScheinsOverview = "api.app.mvz.ScheinApp.GetScheinsOverview";
		export const EVENT_getScheinsOverview_Response = "api.app.mvz.ScheinApp.GetScheinsOverview.Response";
		export const EVENT_handleEventScheinChanged = "api.app.mvz.ScheinApp.HandleEventScheinChanged";
		export const EVENT_handleEventScheinChanged_Response = "api.app.mvz.ScheinApp.HandleEventScheinChanged.Response";
		export const EVENT_handleEventCreateRemoveSchein = "api.app.mvz.ScheinApp.HandleEventCreateRemoveSchein";
		export const EVENT_handleEventCreateRemoveSchein_Response = "api.app.mvz.ScheinApp.HandleEventCreateRemoveSchein.Response";
		export const EVENT_getSetting = "api.app.mvz.ScheinApp.GetSetting";
		export const EVENT_getSetting_Response = "api.app.mvz.ScheinApp.GetSetting.Response";
		export const EVENT_saveSetting = "api.app.mvz.ScheinApp.SaveSetting";
		export const EVENT_saveSetting_Response = "api.app.mvz.ScheinApp.SaveSetting.Response";
		export const EVENT_getSelectedTreatmentCaseSubgroup = "api.app.mvz.ScheinApp.GetSelectedTreatmentCaseSubgroup";
		export const EVENT_getSelectedTreatmentCaseSubgroup_Response = "api.app.mvz.ScheinApp.GetSelectedTreatmentCaseSubgroup.Response";
		export const EVENT_getOrderList = "api.app.mvz.ScheinApp.GetOrderList";
		export const EVENT_getOrderList_Response = "api.app.mvz.ScheinApp.GetOrderList.Response";
		export const EVENT_saveOrderList = "api.app.mvz.ScheinApp.SaveOrderList";
		export const EVENT_saveOrderList_Response = "api.app.mvz.ScheinApp.SaveOrderList.Response";
		export const EVENT_MarkBill = "api.app.mvz.ScheinApp.MarkBill";
		export const EVENT_MarkBill_Response = "api.app.mvz.ScheinApp.MarkBill.Response";
		export const EVENT_GetKTABs = "api.app.mvz.ScheinApp.GetKTABs";
		export const EVENT_GetKTABs_Response = "api.app.mvz.ScheinApp.GetKTABs.Response";
		export const EVENT_GetSubGroupFromMasterData = "api.app.mvz.ScheinApp.GetSubGroupFromMasterData";
		export const EVENT_GetSubGroupFromMasterData_Response = "api.app.mvz.ScheinApp.GetSubGroupFromMasterData.Response";
		export const EVENT_GetBillingAreaFromMasterData = "api.app.mvz.ScheinApp.GetBillingAreaFromMasterData";
		export const EVENT_GetBillingAreaFromMasterData_Response = "api.app.mvz.ScheinApp.GetBillingAreaFromMasterData.Response";
		export const EVENT_GetRezidivList = "api.app.mvz.ScheinApp.GetRezidivList";
		export const EVENT_GetRezidivList_Response = "api.app.mvz.ScheinApp.GetRezidivList.Response";
		export const EVENT_GetPsychotherapyById = "api.app.mvz.ScheinApp.GetPsychotherapyById";
		export const EVENT_GetPsychotherapyById_Response = "api.app.mvz.ScheinApp.GetPsychotherapyById.Response";
		export const EVENT_GetScheinByInsuranceId = "api.app.mvz.ScheinApp.GetScheinByInsuranceId";
		export const EVENT_GetScheinByInsuranceId_Response = "api.app.mvz.ScheinApp.GetScheinByInsuranceId.Response";
		export const EVENT_GetScheinsByInsuranceId = "api.app.mvz.ScheinApp.GetScheinsByInsuranceId";
		export const EVENT_GetScheinsByInsuranceId_Response = "api.app.mvz.ScheinApp.GetScheinsByInsuranceId.Response";
		export const EVENT_RevertTechnicalSchein = "api.app.mvz.ScheinApp.RevertTechnicalSchein";
		export const EVENT_RevertTechnicalSchein_Response = "api.app.mvz.ScheinApp.RevertTechnicalSchein.Response";
		export const EVENT_CreatePrivateSchein = "api.app.mvz.ScheinApp.CreatePrivateSchein";
		export const EVENT_CreatePrivateSchein_Response = "api.app.mvz.ScheinApp.CreatePrivateSchein.Response";
		export const EVENT_UpdatePrivateSchein = "api.app.mvz.ScheinApp.UpdatePrivateSchein";
		export const EVENT_UpdatePrivateSchein_Response = "api.app.mvz.ScheinApp.UpdatePrivateSchein.Response";
		export const EVENT_DeletePrivateSchein = "api.app.mvz.ScheinApp.DeletePrivateSchein";
		export const EVENT_DeletePrivateSchein_Response = "api.app.mvz.ScheinApp.DeletePrivateSchein.Response";
		export const EVENT_IsValidPrivateSchein = "api.app.mvz.ScheinApp.IsValidPrivateSchein";
		export const EVENT_IsValidPrivateSchein_Response = "api.app.mvz.ScheinApp.IsValidPrivateSchein.Response";
		export const EVENT_GetPrivateScheinById = "api.app.mvz.ScheinApp.GetPrivateScheinById";
		export const EVENT_GetPrivateScheinById_Response = "api.app.mvz.ScheinApp.GetPrivateScheinById.Response";
		export const EVENT_GetGoaFactorValue = "api.app.mvz.ScheinApp.GetGoaFactorValue";
		export const EVENT_GetGoaFactorValue_Response = "api.app.mvz.ScheinApp.GetGoaFactorValue.Response";
		export const EVENT_TakeOverDiagnosisByScheinId = "api.app.mvz.ScheinApp.TakeOverDiagnosisByScheinId";
		export const EVENT_TakeOverDiagnosisByScheinId_Response = "api.app.mvz.ScheinApp.TakeOverDiagnosisByScheinId.Response";
		export const EVENT_CreateSvScheinAutomaticly = "api.app.mvz.ScheinApp.CreateSvScheinAutomaticly";
		export const EVENT_CreateSvScheinAutomaticly_Response = "api.app.mvz.ScheinApp.CreateSvScheinAutomaticly.Response";
		export const EVENT_CreateSvScheinFromReference = "api.app.mvz.ScheinApp.CreateSvScheinFromReference";
		export const EVENT_CreateSvScheinFromReference_Response = "api.app.mvz.ScheinApp.CreateSvScheinFromReference.Response";
		export const EVENT_CreateSvScheinManually = "api.app.mvz.ScheinApp.CreateSvScheinManually";
		export const EVENT_CreateSvScheinManually_Response = "api.app.mvz.ScheinApp.CreateSvScheinManually.Response";
		export const EVENT_UpdateSvSchein = "api.app.mvz.ScheinApp.UpdateSvSchein";
		export const EVENT_UpdateSvSchein_Response = "api.app.mvz.ScheinApp.UpdateSvSchein.Response";
		export const EVENT_MarkAsReferral = "api.app.mvz.ScheinApp.MarkAsReferral";
		export const EVENT_MarkAsReferral_Response = "api.app.mvz.ScheinApp.MarkAsReferral.Response";
		export const EVENT_RemoveReferral = "api.app.mvz.ScheinApp.RemoveReferral";
		export const EVENT_RemoveReferral_Response = "api.app.mvz.ScheinApp.RemoveReferral.Response";
		export const EVENT_GetScheinItemById = "api.app.mvz.ScheinApp.GetScheinItemById";
		export const EVENT_GetScheinItemById_Response = "api.app.mvz.ScheinApp.GetScheinItemById.Response";
		export const EVENT_GetScheinItemByIds = "api.app.mvz.ScheinApp.GetScheinItemByIds";
		export const EVENT_GetScheinItemByIds_Response = "api.app.mvz.ScheinApp.GetScheinItemByIds.Response";
		export const EVENT_CreateBgSchein = "api.app.mvz.ScheinApp.CreateBgSchein";
		export const EVENT_CreateBgSchein_Response = "api.app.mvz.ScheinApp.CreateBgSchein.Response";
		export const EVENT_UpdateBgSchein = "api.app.mvz.ScheinApp.UpdateBgSchein";
		export const EVENT_UpdateBgSchein_Response = "api.app.mvz.ScheinApp.UpdateBgSchein.Response";
		export const EVENT_DeleteBgSchein = "api.app.mvz.ScheinApp.DeleteBgSchein";
		export const EVENT_DeleteBgSchein_Response = "api.app.mvz.ScheinApp.DeleteBgSchein.Response";
		export const EVENT_GetBgScheinById = "api.app.mvz.ScheinApp.GetBgScheinById";
		export const EVENT_GetBgScheinById_Response = "api.app.mvz.ScheinApp.GetBgScheinById.Response";
		export const EVENT_IsValidBgSchein = "api.app.mvz.ScheinApp.IsValidBgSchein";
		export const EVENT_IsValidBgSchein_Response = "api.app.mvz.ScheinApp.IsValidBgSchein.Response";
		export const EVENT_CheckDummyVknr = "api.app.mvz.ScheinApp.CheckDummyVknr";
		export const EVENT_CheckDummyVknr_Response = "api.app.mvz.ScheinApp.CheckDummyVknr.Response";
		export const EVENT_GetTotalScheins = "api.app.mvz.ScheinApp.GetTotalScheins";
		export const EVENT_GetTotalScheins_Response = "api.app.mvz.ScheinApp.GetTotalScheins.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_CreateSvScheins = "/api/app/mvz/schein/createSvScheins";
        export const LEGACY_TOPIC_IsValid = "/api/app/mvz/schein/isValid";
        export const LEGACY_TOPIC_CreateSchein = "/api/app/mvz/schein/createSchein";
        export const LEGACY_TOPIC_TakeOverScheinDiagnosis = "/api/app/mvz/schein/takeOverScheinDiagnosis";
        export const LEGACY_TOPIC_GetScheinDetailById = "/api/app/mvz/schein/getScheinDetailById";
        export const LEGACY_TOPIC_GetScheinDetailByIds = "/api/app/mvz/schein/getScheinDetailByIds";
        export const LEGACY_TOPIC_UpdateSchein = "/api/app/mvz/schein/updateSchein";
        export const LEGACY_TOPIC_MarkNotBilled = "/api/app/mvz/schein/markNotBilled";
        export const LEGACY_TOPIC_CheckExistKVScheinCurrentQuarter = "/api/app/mvz/schein/checkExistKVScheinCurrentQuarter";
        export const LEGACY_TOPIC_DeleteSchein = "/api/app/mvz/schein/deleteSchein";
        export const LEGACY_TOPIC_GetScheinDetail = "/api/app/mvz/schein/getScheinDetail";
        export const LEGACY_TOPIC_GetFields = "/api/app/mvz/schein/getFields";
        export const LEGACY_TOPIC_GetScheinsOverview = "/api/app/mvz/schein/getScheinsOverview";
        export const LEGACY_TOPIC_HandleEventScheinChanged = "/api/app/mvz/schein/handleEventScheinChanged";
        export const LEGACY_TOPIC_HandleEventCreateRemoveSchein = "/api/app/mvz/schein/handleEventCreateRemoveSchein";
        export const LEGACY_TOPIC_GetSetting = "/api/app/mvz/schein/getSetting";
        export const LEGACY_TOPIC_SaveSetting = "/api/app/mvz/schein/saveSetting";
        export const LEGACY_TOPIC_GetSelectedTreatmentCaseSubgroup = "/api/app/mvz/schein/getSelectedTreatmentCaseSubgroup";
        export const LEGACY_TOPIC_GetOrderList = "/api/app/mvz/schein/getOrderList";
        export const LEGACY_TOPIC_SaveOrderList = "/api/app/mvz/schein/saveOrderList";
        export const LEGACY_TOPIC_MarkBill = "/api/app/mvz/schein/markBill";
        export const LEGACY_TOPIC_GetKTABs = "/api/app/mvz/schein/getKTABs";
        export const LEGACY_TOPIC_GetSubGroupFromMasterData = "/api/app/mvz/schein/getSubGroupFromMasterData";
        export const LEGACY_TOPIC_GetBillingAreaFromMasterData = "/api/app/mvz/schein/getBillingAreaFromMasterData";
        export const LEGACY_TOPIC_GetRezidivList = "/api/app/mvz/schein/getRezidivList";
        export const LEGACY_TOPIC_GetPsychotherapyById = "/api/app/mvz/schein/getPsychotherapyById";
        export const LEGACY_TOPIC_GetScheinByInsuranceId = "/api/app/mvz/schein/getScheinByInsuranceId";
        export const LEGACY_TOPIC_GetScheinsByInsuranceId = "/api/app/mvz/schein/getScheinsByInsuranceId";
        export const LEGACY_TOPIC_RevertTechnicalSchein = "/api/app/mvz/schein/revertTechnicalSchein";
        export const LEGACY_TOPIC_CreatePrivateSchein = "/api/app/mvz/schein/createPrivateSchein";
        export const LEGACY_TOPIC_UpdatePrivateSchein = "/api/app/mvz/schein/updatePrivateSchein";
        export const LEGACY_TOPIC_DeletePrivateSchein = "/api/app/mvz/schein/deletePrivateSchein";
        export const LEGACY_TOPIC_IsValidPrivateSchein = "/api/app/mvz/schein/isValidPrivateSchein";
        export const LEGACY_TOPIC_GetPrivateScheinById = "/api/app/mvz/schein/getPrivateScheinById";
        export const LEGACY_TOPIC_GetGoaFactorValue = "/api/app/mvz/schein/getGoaFactorValue";
        export const LEGACY_TOPIC_TakeOverDiagnosisByScheinId = "/api/app/mvz/schein/takeOverDiagnosisByScheinId";
        export const LEGACY_TOPIC_CreateSvScheinAutomaticly = "/api/app/mvz/schein/createSvScheinAutomaticly";
        export const LEGACY_TOPIC_CreateSvScheinFromReference = "/api/app/mvz/schein/createSvScheinFromReference";
        export const LEGACY_TOPIC_CreateSvScheinManually = "/api/app/mvz/schein/createSvScheinManually";
        export const LEGACY_TOPIC_UpdateSvSchein = "/api/app/mvz/schein/updateSvSchein";
        export const LEGACY_TOPIC_MarkAsReferral = "/api/app/mvz/schein/markAsReferral";
        export const LEGACY_TOPIC_RemoveReferral = "/api/app/mvz/schein/removeReferral";
        export const LEGACY_TOPIC_GetScheinItemById = "/api/app/mvz/schein/getScheinItemById";
        export const LEGACY_TOPIC_GetScheinItemByIds = "/api/app/mvz/schein/getScheinItemByIds";
        export const LEGACY_TOPIC_CreateBgSchein = "/api/app/mvz/schein/createBgSchein";
        export const LEGACY_TOPIC_UpdateBgSchein = "/api/app/mvz/schein/updateBgSchein";
        export const LEGACY_TOPIC_DeleteBgSchein = "/api/app/mvz/schein/deleteBgSchein";
        export const LEGACY_TOPIC_GetBgScheinById = "/api/app/mvz/schein/getBgScheinById";
        export const LEGACY_TOPIC_IsValidBgSchein = "/api/app/mvz/schein/isValidBgSchein";
        export const LEGACY_TOPIC_CheckDummyVknr = "/api/app/mvz/schein/checkDummyVknr";
        export const LEGACY_TOPIC_GetTotalScheins = "/api/app/mvz/schein/getTotalScheins";


// Define action methods and their listener -----------------------------------------------------------------
			export async function createSvScheins(request: schein_common.CreateSvScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_CreateSvScheins, { init , request})
			}

			export function useQueryCreateSvScheins<TransformedType =any>(payload: schein_common.CreateSvScheinRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateSvScheins, payload],
					queryFn: async ({ signal }) => await createSvScheins(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateSvScheins(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.CreateSvScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createSvScheins(request),
						retry: false,
						...opts
                });
            }
    
			export async function isValid(request: IsValidRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<IsValidResponse>("POST", LEGACY_TOPIC_IsValid, { init , request})
			}

			export function useQueryIsValid<TransformedType =IsValidResponse>(payload: IsValidRequest,ops?: CustomUseQueryOptions<ResponseType<IsValidResponse>, TransformedType>) {
                return useQuery<ResponseType<IsValidResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_IsValid, payload],
					queryFn: async ({ signal }) => await isValid(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationIsValid(opts?: UseMutationOptions<ResponseType<IsValidResponse>, ErrorType,IsValidRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await isValid(request),
						retry: false,
						...opts
                });
            }
    
			export async function createSchein(request: CreateScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CreateScheinResponse>("POST", LEGACY_TOPIC_CreateSchein, { init , request})
			}

			export function useQueryCreateSchein<TransformedType =CreateScheinResponse>(payload: CreateScheinRequest,ops?: CustomUseQueryOptions<ResponseType<CreateScheinResponse>, TransformedType>) {
                return useQuery<ResponseType<CreateScheinResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_createSchein, payload],
					queryFn: async ({ signal }) => await createSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateSchein(opts?: UseMutationOptions<ResponseType<CreateScheinResponse>, ErrorType,CreateScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function takeOverScheinDiagnosis(request: TakeOverScheinDiagnisToRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_TakeOverScheinDiagnosis, { init , request})
			}

			export function useQueryTakeOverScheinDiagnosis<TransformedType =any>(payload: TakeOverScheinDiagnisToRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_takeOverScheinDiagnosis, payload],
					queryFn: async ({ signal }) => await takeOverScheinDiagnosis(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationTakeOverScheinDiagnosis(opts?: UseMutationOptions<ResponseType<any>, ErrorType,TakeOverScheinDiagnisToRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await takeOverScheinDiagnosis(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinDetailById(request: schein_common.GetScheinDetailByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetScheinDetailByIdResponse>("POST", LEGACY_TOPIC_GetScheinDetailById, { init , request})
			}

			export function useQueryGetScheinDetailById<TransformedType =schein_common.GetScheinDetailByIdResponse>(payload: schein_common.GetScheinDetailByIdRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.GetScheinDetailByIdResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetScheinDetailByIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getScheinDetailById, payload],
					queryFn: async ({ signal }) => await getScheinDetailById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinDetailById(opts?: UseMutationOptions<ResponseType<schein_common.GetScheinDetailByIdResponse>, ErrorType,schein_common.GetScheinDetailByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinDetailById(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinDetailByIds(request: schein_common.GetScheinDetailByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetScheinDetailByIdsResponse>("POST", LEGACY_TOPIC_GetScheinDetailByIds, { init , request})
			}

			export function useQueryGetScheinDetailByIds<TransformedType =schein_common.GetScheinDetailByIdsResponse>(payload: schein_common.GetScheinDetailByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.GetScheinDetailByIdsResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetScheinDetailByIdsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getScheinDetailByIds, payload],
					queryFn: async ({ signal }) => await getScheinDetailByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinDetailByIds(opts?: UseMutationOptions<ResponseType<schein_common.GetScheinDetailByIdsResponse>, ErrorType,schein_common.GetScheinDetailByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinDetailByIds(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateSchein(request: schein_common.UpdateScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_UpdateSchein, { init , request})
			}

			export function useQueryUpdateSchein<TransformedType =any>(payload: schein_common.UpdateScheinRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_updateSchein, payload],
					queryFn: async ({ signal }) => await updateSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateSchein(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.UpdateScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function markNotBilled(request: schein_common.MarkNotBilledRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_MarkNotBilled, { init , request})
			}

			export function useQueryMarkNotBilled<TransformedType =any>(payload: schein_common.MarkNotBilledRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_markNotBilled, payload],
					queryFn: async ({ signal }) => await markNotBilled(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkNotBilled(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.MarkNotBilledRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markNotBilled(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkExistKVScheinCurrentQuarter(request: schein_common.CheckExistKVScheinCurrentQuarterRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.CheckExistKVScheinCurrentQuarterResponse>("POST", LEGACY_TOPIC_CheckExistKVScheinCurrentQuarter, { init , request})
			}

			export function useQueryCheckExistKVScheinCurrentQuarter<TransformedType =schein_common.CheckExistKVScheinCurrentQuarterResponse>(payload: schein_common.CheckExistKVScheinCurrentQuarterRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.CheckExistKVScheinCurrentQuarterResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.CheckExistKVScheinCurrentQuarterResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_checkExistKVScheinCurrentQuarter, payload],
					queryFn: async ({ signal }) => await checkExistKVScheinCurrentQuarter(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckExistKVScheinCurrentQuarter(opts?: UseMutationOptions<ResponseType<schein_common.CheckExistKVScheinCurrentQuarterResponse>, ErrorType,schein_common.CheckExistKVScheinCurrentQuarterRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkExistKVScheinCurrentQuarter(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteSchein(request: schein_common.DeleteScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteSchein, { init , request})
			}

			export function useQueryDeleteSchein<TransformedType =any>(payload: schein_common.DeleteScheinRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_deleteSchein, payload],
					queryFn: async ({ signal }) => await deleteSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteSchein(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.DeleteScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinDetail(request: schein_common.GetScheinDetailRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetScheinDetailResponse>("POST", LEGACY_TOPIC_GetScheinDetail, { init , request})
			}

			export function useQueryGetScheinDetail<TransformedType =schein_common.GetScheinDetailResponse>(payload: schein_common.GetScheinDetailRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.GetScheinDetailResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetScheinDetailResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getScheinDetail, payload],
					queryFn: async ({ signal }) => await getScheinDetail(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinDetail(opts?: UseMutationOptions<ResponseType<schein_common.GetScheinDetailResponse>, ErrorType,schein_common.GetScheinDetailRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinDetail(request),
						retry: false,
						...opts
                });
            }
    
			export async function getFields(request: schein_common.GetFieldsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetFieldsResponse>("POST", LEGACY_TOPIC_GetFields, { init , request})
			}

			export function useQueryGetFields<TransformedType =schein_common.GetFieldsResponse>(payload: schein_common.GetFieldsRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.GetFieldsResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetFieldsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getFields, payload],
					queryFn: async ({ signal }) => await getFields(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetFields(opts?: UseMutationOptions<ResponseType<schein_common.GetFieldsResponse>, ErrorType,schein_common.GetFieldsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getFields(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinsOverview(request: schein_common.GetScheinsOverviewRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetScheinsOverviewResponse>("POST", LEGACY_TOPIC_GetScheinsOverview, { init , request})
			}

			export function useQueryGetScheinsOverview<TransformedType =schein_common.GetScheinsOverviewResponse>(payload: schein_common.GetScheinsOverviewRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.GetScheinsOverviewResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetScheinsOverviewResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getScheinsOverview, payload],
					queryFn: async ({ signal }) => await getScheinsOverview(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinsOverview(opts?: UseMutationOptions<ResponseType<schein_common.GetScheinsOverviewResponse>, ErrorType,schein_common.GetScheinsOverviewRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinsOverview(request),
						retry: false,
						...opts
                });
            }
    
    
    
			export async function getSetting(init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetSettingResponse>("POST", LEGACY_TOPIC_GetSetting, { init })
			}

			export function useQueryGetSetting<TransformedType =schein_common.GetSettingResponse>(ops?: CustomUseQueryOptions<ResponseType<schein_common.GetSettingResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetSettingResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getSetting],
					queryFn: async ({ signal }) => await getSetting({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetSetting(opts?: UseMutationOptions<ResponseType<schein_common.GetSettingResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getSetting(),
						retry: false,
						...opts
                });
            }
    
			export async function saveSetting(request: schein_common.SaveSettingRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_SaveSetting, { init , request})
			}

			export function useQuerySaveSetting<TransformedType =any>(payload: schein_common.SaveSettingRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_saveSetting, payload],
					queryFn: async ({ signal }) => await saveSetting(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSaveSetting(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.SaveSettingRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await saveSetting(request),
						retry: false,
						...opts
                });
            }
    
			export async function getSelectedTreatmentCaseSubgroup(request: schein_common.GetSelectedTreatmentCaseSubgroupRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetSelectedTreatmentCaseSubgroupResponse>("POST", LEGACY_TOPIC_GetSelectedTreatmentCaseSubgroup, { init , request})
			}

			export function useQueryGetSelectedTreatmentCaseSubgroup<TransformedType =schein_common.GetSelectedTreatmentCaseSubgroupResponse>(payload: schein_common.GetSelectedTreatmentCaseSubgroupRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.GetSelectedTreatmentCaseSubgroupResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetSelectedTreatmentCaseSubgroupResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getSelectedTreatmentCaseSubgroup, payload],
					queryFn: async ({ signal }) => await getSelectedTreatmentCaseSubgroup(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetSelectedTreatmentCaseSubgroup(opts?: UseMutationOptions<ResponseType<schein_common.GetSelectedTreatmentCaseSubgroupResponse>, ErrorType,schein_common.GetSelectedTreatmentCaseSubgroupRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getSelectedTreatmentCaseSubgroup(request),
						retry: false,
						...opts
                });
            }
    
			export async function getOrderList(init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.GetOrderListResponse>("POST", LEGACY_TOPIC_GetOrderList, { init })
			}

			export function useQueryGetOrderList<TransformedType =schein_common.GetOrderListResponse>(ops?: CustomUseQueryOptions<ResponseType<schein_common.GetOrderListResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.GetOrderListResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getOrderList],
					queryFn: async ({ signal }) => await getOrderList({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetOrderList(opts?: UseMutationOptions<ResponseType<schein_common.GetOrderListResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getOrderList(),
						retry: false,
						...opts
                });
            }
    
			export async function saveOrderList(request: schein_common.SaveOrderListRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_SaveOrderList, { init , request})
			}

			export function useQuerySaveOrderList<TransformedType =any>(payload: schein_common.SaveOrderListRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_saveOrderList, payload],
					queryFn: async ({ signal }) => await saveOrderList(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationSaveOrderList(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.SaveOrderListRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await saveOrderList(request),
						retry: false,
						...opts
                });
            }
    
			export async function markBill(request: MarkBillRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_MarkBill, { init , request})
			}

			export function useQueryMarkBill<TransformedType =any>(payload: MarkBillRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkBill, payload],
					queryFn: async ({ signal }) => await markBill(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkBill(opts?: UseMutationOptions<ResponseType<any>, ErrorType,MarkBillRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markBill(request),
						retry: false,
						...opts
                });
            }
    
			export async function getKTABs(request: GetKTABsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetKTABsResponse>("POST", LEGACY_TOPIC_GetKTABs, { init , request})
			}

			export function useQueryGetKTABs<TransformedType =GetKTABsResponse>(payload: GetKTABsRequest,ops?: CustomUseQueryOptions<ResponseType<GetKTABsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetKTABsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetKTABs, payload],
					queryFn: async ({ signal }) => await getKTABs(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetKTABs(opts?: UseMutationOptions<ResponseType<GetKTABsResponse>, ErrorType,GetKTABsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getKTABs(request),
						retry: false,
						...opts
                });
            }
    
			export async function getSubGroupFromMasterData(request: GetSubGroupFromMasterDataRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetSubGroupFromMasterDataResponse>("POST", LEGACY_TOPIC_GetSubGroupFromMasterData, { init , request})
			}

			export function useQueryGetSubGroupFromMasterData<TransformedType =GetSubGroupFromMasterDataResponse>(payload: GetSubGroupFromMasterDataRequest,ops?: CustomUseQueryOptions<ResponseType<GetSubGroupFromMasterDataResponse>, TransformedType>) {
                return useQuery<ResponseType<GetSubGroupFromMasterDataResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetSubGroupFromMasterData, payload],
					queryFn: async ({ signal }) => await getSubGroupFromMasterData(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetSubGroupFromMasterData(opts?: UseMutationOptions<ResponseType<GetSubGroupFromMasterDataResponse>, ErrorType,GetSubGroupFromMasterDataRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getSubGroupFromMasterData(request),
						retry: false,
						...opts
                });
            }
    
			export async function getBillingAreaFromMasterData(request: GetBillingAreaFromMasterDataRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetBillingAreaFromMasterDataResponse>("POST", LEGACY_TOPIC_GetBillingAreaFromMasterData, { init , request})
			}

			export function useQueryGetBillingAreaFromMasterData<TransformedType =GetBillingAreaFromMasterDataResponse>(payload: GetBillingAreaFromMasterDataRequest,ops?: CustomUseQueryOptions<ResponseType<GetBillingAreaFromMasterDataResponse>, TransformedType>) {
                return useQuery<ResponseType<GetBillingAreaFromMasterDataResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetBillingAreaFromMasterData, payload],
					queryFn: async ({ signal }) => await getBillingAreaFromMasterData(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetBillingAreaFromMasterData(opts?: UseMutationOptions<ResponseType<GetBillingAreaFromMasterDataResponse>, ErrorType,GetBillingAreaFromMasterDataRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getBillingAreaFromMasterData(request),
						retry: false,
						...opts
                });
            }
    
			export async function getRezidivList(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetRezidivListResponse>("POST", LEGACY_TOPIC_GetRezidivList, { init })
			}

			export function useQueryGetRezidivList<TransformedType =GetRezidivListResponse>(ops?: CustomUseQueryOptions<ResponseType<GetRezidivListResponse>, TransformedType>) {
                return useQuery<ResponseType<GetRezidivListResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetRezidivList],
					queryFn: async ({ signal }) => await getRezidivList({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetRezidivList(opts?: UseMutationOptions<ResponseType<GetRezidivListResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getRezidivList(),
						retry: false,
						...opts
                });
            }
    
			export async function getPsychotherapyById(request: GetPsychotherapyByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetPsychotherapyByIdResponse>("POST", LEGACY_TOPIC_GetPsychotherapyById, { init , request})
			}

			export function useQueryGetPsychotherapyById<TransformedType =GetPsychotherapyByIdResponse>(payload: GetPsychotherapyByIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetPsychotherapyByIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetPsychotherapyByIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPsychotherapyById, payload],
					queryFn: async ({ signal }) => await getPsychotherapyById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPsychotherapyById(opts?: UseMutationOptions<ResponseType<GetPsychotherapyByIdResponse>, ErrorType,GetPsychotherapyByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPsychotherapyById(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinByInsuranceId(request: GetScheinByInsuranceIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetScheinByInsuranceIdResponse>("POST", LEGACY_TOPIC_GetScheinByInsuranceId, { init , request})
			}

			export function useQueryGetScheinByInsuranceId<TransformedType =GetScheinByInsuranceIdResponse>(payload: GetScheinByInsuranceIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetScheinByInsuranceIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetScheinByInsuranceIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetScheinByInsuranceId, payload],
					queryFn: async ({ signal }) => await getScheinByInsuranceId(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinByInsuranceId(opts?: UseMutationOptions<ResponseType<GetScheinByInsuranceIdResponse>, ErrorType,GetScheinByInsuranceIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinByInsuranceId(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinsByInsuranceId(request: GetScheinByInsuranceIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetScheinsByInsuranceIdResponse>("POST", LEGACY_TOPIC_GetScheinsByInsuranceId, { init , request})
			}

			export function useQueryGetScheinsByInsuranceId<TransformedType =GetScheinsByInsuranceIdResponse>(payload: GetScheinByInsuranceIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetScheinsByInsuranceIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetScheinsByInsuranceIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetScheinsByInsuranceId, payload],
					queryFn: async ({ signal }) => await getScheinsByInsuranceId(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinsByInsuranceId(opts?: UseMutationOptions<ResponseType<GetScheinsByInsuranceIdResponse>, ErrorType,GetScheinByInsuranceIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinsByInsuranceId(request),
						retry: false,
						...opts
                });
            }
    
			export async function revertTechnicalSchein(request: RevertTechnicalScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_RevertTechnicalSchein, { init , request})
			}

			export function useQueryRevertTechnicalSchein<TransformedType =any>(payload: RevertTechnicalScheinRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_RevertTechnicalSchein, payload],
					queryFn: async ({ signal }) => await revertTechnicalSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRevertTechnicalSchein(opts?: UseMutationOptions<ResponseType<any>, ErrorType,RevertTechnicalScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await revertTechnicalSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function createPrivateSchein(request: CreatePrivateScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<private_schein_common.PrivateScheinItem>("POST", LEGACY_TOPIC_CreatePrivateSchein, { init , request})
			}

			export function useQueryCreatePrivateSchein<TransformedType =private_schein_common.PrivateScheinItem>(payload: CreatePrivateScheinRequest,ops?: CustomUseQueryOptions<ResponseType<private_schein_common.PrivateScheinItem>, TransformedType>) {
                return useQuery<ResponseType<private_schein_common.PrivateScheinItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreatePrivateSchein, payload],
					queryFn: async ({ signal }) => await createPrivateSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreatePrivateSchein(opts?: UseMutationOptions<ResponseType<private_schein_common.PrivateScheinItem>, ErrorType,CreatePrivateScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createPrivateSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function updatePrivateSchein(request: UpdatePrivateScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<private_schein_common.PrivateScheinItem>("POST", LEGACY_TOPIC_UpdatePrivateSchein, { init , request})
			}

			export function useQueryUpdatePrivateSchein<TransformedType =private_schein_common.PrivateScheinItem>(payload: UpdatePrivateScheinRequest,ops?: CustomUseQueryOptions<ResponseType<private_schein_common.PrivateScheinItem>, TransformedType>) {
                return useQuery<ResponseType<private_schein_common.PrivateScheinItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdatePrivateSchein, payload],
					queryFn: async ({ signal }) => await updatePrivateSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdatePrivateSchein(opts?: UseMutationOptions<ResponseType<private_schein_common.PrivateScheinItem>, ErrorType,UpdatePrivateScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updatePrivateSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function deletePrivateSchein(request: DeletePrivateScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeletePrivateSchein, { init , request})
			}

			export function useQueryDeletePrivateSchein<TransformedType =any>(payload: DeletePrivateScheinRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeletePrivateSchein, payload],
					queryFn: async ({ signal }) => await deletePrivateSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeletePrivateSchein(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeletePrivateScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deletePrivateSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function isValidPrivateSchein(request: IsValidPrivateScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<IsValidResponse>("POST", LEGACY_TOPIC_IsValidPrivateSchein, { init , request})
			}

			export function useQueryIsValidPrivateSchein<TransformedType =IsValidResponse>(payload: IsValidPrivateScheinRequest,ops?: CustomUseQueryOptions<ResponseType<IsValidResponse>, TransformedType>) {
                return useQuery<ResponseType<IsValidResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_IsValidPrivateSchein, payload],
					queryFn: async ({ signal }) => await isValidPrivateSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationIsValidPrivateSchein(opts?: UseMutationOptions<ResponseType<IsValidResponse>, ErrorType,IsValidPrivateScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await isValidPrivateSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function getPrivateScheinById(request: GetPrivateScheinByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<private_schein_common.PrivateScheinItem>("POST", LEGACY_TOPIC_GetPrivateScheinById, { init , request})
			}

			export function useQueryGetPrivateScheinById<TransformedType =private_schein_common.PrivateScheinItem>(payload: GetPrivateScheinByIdRequest,ops?: CustomUseQueryOptions<ResponseType<private_schein_common.PrivateScheinItem>, TransformedType>) {
                return useQuery<ResponseType<private_schein_common.PrivateScheinItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetPrivateScheinById, payload],
					queryFn: async ({ signal }) => await getPrivateScheinById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetPrivateScheinById(opts?: UseMutationOptions<ResponseType<private_schein_common.PrivateScheinItem>, ErrorType,GetPrivateScheinByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getPrivateScheinById(request),
						retry: false,
						...opts
                });
            }
    
			export async function getGoaFactorValue(request: GetGoaFactorValueRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetGoaFactorValueResponse>("POST", LEGACY_TOPIC_GetGoaFactorValue, { init , request})
			}

			export function useQueryGetGoaFactorValue<TransformedType =GetGoaFactorValueResponse>(payload: GetGoaFactorValueRequest,ops?: CustomUseQueryOptions<ResponseType<GetGoaFactorValueResponse>, TransformedType>) {
                return useQuery<ResponseType<GetGoaFactorValueResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetGoaFactorValue, payload],
					queryFn: async ({ signal }) => await getGoaFactorValue(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetGoaFactorValue(opts?: UseMutationOptions<ResponseType<GetGoaFactorValueResponse>, ErrorType,GetGoaFactorValueRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getGoaFactorValue(request),
						retry: false,
						...opts
                });
            }
    
			export async function takeOverDiagnosisByScheinId(request: TakeOverDiagnosisByScheinIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_TakeOverDiagnosisByScheinId, { init , request})
			}

			export function useQueryTakeOverDiagnosisByScheinId<TransformedType =any>(payload: TakeOverDiagnosisByScheinIdRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_TakeOverDiagnosisByScheinId, payload],
					queryFn: async ({ signal }) => await takeOverDiagnosisByScheinId(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationTakeOverDiagnosisByScheinId(opts?: UseMutationOptions<ResponseType<any>, ErrorType,TakeOverDiagnosisByScheinIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await takeOverDiagnosisByScheinId(request),
						retry: false,
						...opts
                });
            }
    
			export async function createSvScheinAutomaticly(request: schein_common.CreateSvScheinAutomaticlyRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.CreateSvScheinAutomaticlyResponse>("POST", LEGACY_TOPIC_CreateSvScheinAutomaticly, { init , request})
			}

			export function useQueryCreateSvScheinAutomaticly<TransformedType =schein_common.CreateSvScheinAutomaticlyResponse>(payload: schein_common.CreateSvScheinAutomaticlyRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.CreateSvScheinAutomaticlyResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.CreateSvScheinAutomaticlyResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateSvScheinAutomaticly, payload],
					queryFn: async ({ signal }) => await createSvScheinAutomaticly(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateSvScheinAutomaticly(opts?: UseMutationOptions<ResponseType<schein_common.CreateSvScheinAutomaticlyResponse>, ErrorType,schein_common.CreateSvScheinAutomaticlyRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createSvScheinAutomaticly(request),
						retry: false,
						...opts
                });
            }
    
			export async function createSvScheinFromReference(request: schein_common.CreateSvScheinFromReferenceRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.CreateSvScheinFromReferenceResponse>("POST", LEGACY_TOPIC_CreateSvScheinFromReference, { init , request})
			}

			export function useQueryCreateSvScheinFromReference<TransformedType =schein_common.CreateSvScheinFromReferenceResponse>(payload: schein_common.CreateSvScheinFromReferenceRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.CreateSvScheinFromReferenceResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.CreateSvScheinFromReferenceResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateSvScheinFromReference, payload],
					queryFn: async ({ signal }) => await createSvScheinFromReference(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateSvScheinFromReference(opts?: UseMutationOptions<ResponseType<schein_common.CreateSvScheinFromReferenceResponse>, ErrorType,schein_common.CreateSvScheinFromReferenceRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createSvScheinFromReference(request),
						retry: false,
						...opts
                });
            }
    
			export async function createSvScheinManually(request: schein_common.CreateSvScheinManuallyRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.CreateSvScheinManuallyResponse>("POST", LEGACY_TOPIC_CreateSvScheinManually, { init , request})
			}

			export function useQueryCreateSvScheinManually<TransformedType =schein_common.CreateSvScheinManuallyResponse>(payload: schein_common.CreateSvScheinManuallyRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.CreateSvScheinManuallyResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.CreateSvScheinManuallyResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateSvScheinManually, payload],
					queryFn: async ({ signal }) => await createSvScheinManually(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateSvScheinManually(opts?: UseMutationOptions<ResponseType<schein_common.CreateSvScheinManuallyResponse>, ErrorType,schein_common.CreateSvScheinManuallyRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createSvScheinManually(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateSvSchein(request: schein_common.UpdateSvScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.UpdateSvScheinResponse>("POST", LEGACY_TOPIC_UpdateSvSchein, { init , request})
			}

			export function useQueryUpdateSvSchein<TransformedType =schein_common.UpdateSvScheinResponse>(payload: schein_common.UpdateSvScheinRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.UpdateSvScheinResponse>, TransformedType>) {
                return useQuery<ResponseType<schein_common.UpdateSvScheinResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateSvSchein, payload],
					queryFn: async ({ signal }) => await updateSvSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateSvSchein(opts?: UseMutationOptions<ResponseType<schein_common.UpdateSvScheinResponse>, ErrorType,schein_common.UpdateSvScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateSvSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function markAsReferral(request: schein_common.MarkAsReferralRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_MarkAsReferral, { init , request})
			}

			export function useQueryMarkAsReferral<TransformedType =any>(payload: schein_common.MarkAsReferralRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_MarkAsReferral, payload],
					queryFn: async ({ signal }) => await markAsReferral(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationMarkAsReferral(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.MarkAsReferralRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await markAsReferral(request),
						retry: false,
						...opts
                });
            }
    
			export async function removeReferral(request: schein_common.RemoveReferralRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_RemoveReferral, { init , request})
			}

			export function useQueryRemoveReferral<TransformedType =any>(payload: schein_common.RemoveReferralRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_RemoveReferral, payload],
					queryFn: async ({ signal }) => await removeReferral(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationRemoveReferral(opts?: UseMutationOptions<ResponseType<any>, ErrorType,schein_common.RemoveReferralRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await removeReferral(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinItemById(request: GetScheinItemByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetScheinItemByIdResponse>("POST", LEGACY_TOPIC_GetScheinItemById, { init , request})
			}

			export function useQueryGetScheinItemById<TransformedType =GetScheinItemByIdResponse>(payload: GetScheinItemByIdRequest,ops?: CustomUseQueryOptions<ResponseType<GetScheinItemByIdResponse>, TransformedType>) {
                return useQuery<ResponseType<GetScheinItemByIdResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetScheinItemById, payload],
					queryFn: async ({ signal }) => await getScheinItemById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinItemById(opts?: UseMutationOptions<ResponseType<GetScheinItemByIdResponse>, ErrorType,GetScheinItemByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinItemById(request),
						retry: false,
						...opts
                });
            }
    
			export async function getScheinItemByIds(request: GetScheinItemByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetScheinItemByIdsResponse>("POST", LEGACY_TOPIC_GetScheinItemByIds, { init , request})
			}

			export function useQueryGetScheinItemByIds<TransformedType =GetScheinItemByIdsResponse>(payload: GetScheinItemByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<GetScheinItemByIdsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetScheinItemByIdsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetScheinItemByIds, payload],
					queryFn: async ({ signal }) => await getScheinItemByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetScheinItemByIds(opts?: UseMutationOptions<ResponseType<GetScheinItemByIdsResponse>, ErrorType,GetScheinItemByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getScheinItemByIds(request),
						retry: false,
						...opts
                });
            }
    
			export async function createBgSchein(request: CreateBgScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.BgScheinItem>("POST", LEGACY_TOPIC_CreateBgSchein, { init , request})
			}

			export function useQueryCreateBgSchein<TransformedType =schein_common.BgScheinItem>(payload: CreateBgScheinRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.BgScheinItem>, TransformedType>) {
                return useQuery<ResponseType<schein_common.BgScheinItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CreateBgSchein, payload],
					queryFn: async ({ signal }) => await createBgSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCreateBgSchein(opts?: UseMutationOptions<ResponseType<schein_common.BgScheinItem>, ErrorType,CreateBgScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await createBgSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function updateBgSchein(request: UpdateBgScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.BgScheinItem>("POST", LEGACY_TOPIC_UpdateBgSchein, { init , request})
			}

			export function useQueryUpdateBgSchein<TransformedType =schein_common.BgScheinItem>(payload: UpdateBgScheinRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.BgScheinItem>, TransformedType>) {
                return useQuery<ResponseType<schein_common.BgScheinItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_UpdateBgSchein, payload],
					queryFn: async ({ signal }) => await updateBgSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationUpdateBgSchein(opts?: UseMutationOptions<ResponseType<schein_common.BgScheinItem>, ErrorType,UpdateBgScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await updateBgSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function deleteBgSchein(request: DeleteBgScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<any>("POST", LEGACY_TOPIC_DeleteBgSchein, { init , request})
			}

			export function useQueryDeleteBgSchein<TransformedType =any>(payload: DeleteBgScheinRequest,ops?: CustomUseQueryOptions<any, TransformedType>) {
                return useQuery<ResponseType<any>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_DeleteBgSchein, payload],
					queryFn: async ({ signal }) => await deleteBgSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationDeleteBgSchein(opts?: UseMutationOptions<ResponseType<any>, ErrorType,DeleteBgScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await deleteBgSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function getBgScheinById(request: GetBgScheinByIdRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<schein_common.BgScheinItem>("POST", LEGACY_TOPIC_GetBgScheinById, { init , request})
			}

			export function useQueryGetBgScheinById<TransformedType =schein_common.BgScheinItem>(payload: GetBgScheinByIdRequest,ops?: CustomUseQueryOptions<ResponseType<schein_common.BgScheinItem>, TransformedType>) {
                return useQuery<ResponseType<schein_common.BgScheinItem>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetBgScheinById, payload],
					queryFn: async ({ signal }) => await getBgScheinById(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetBgScheinById(opts?: UseMutationOptions<ResponseType<schein_common.BgScheinItem>, ErrorType,GetBgScheinByIdRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getBgScheinById(request),
						retry: false,
						...opts
                });
            }
    
			export async function isValidBgSchein(request: IsValidBgScheinRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<IsValidResponse>("POST", LEGACY_TOPIC_IsValidBgSchein, { init , request})
			}

			export function useQueryIsValidBgSchein<TransformedType =IsValidResponse>(payload: IsValidBgScheinRequest,ops?: CustomUseQueryOptions<ResponseType<IsValidResponse>, TransformedType>) {
                return useQuery<ResponseType<IsValidResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_IsValidBgSchein, payload],
					queryFn: async ({ signal }) => await isValidBgSchein(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationIsValidBgSchein(opts?: UseMutationOptions<ResponseType<IsValidResponse>, ErrorType,IsValidBgScheinRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await isValidBgSchein(request),
						retry: false,
						...opts
                });
            }
    
			export async function checkDummyVknr(request: CheckDummyVknrRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<CheckDummyVknrResponse>("POST", LEGACY_TOPIC_CheckDummyVknr, { init , request})
			}

			export function useQueryCheckDummyVknr<TransformedType =CheckDummyVknrResponse>(payload: CheckDummyVknrRequest,ops?: CustomUseQueryOptions<ResponseType<CheckDummyVknrResponse>, TransformedType>) {
                return useQuery<ResponseType<CheckDummyVknrResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_CheckDummyVknr, payload],
					queryFn: async ({ signal }) => await checkDummyVknr(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationCheckDummyVknr(opts?: UseMutationOptions<ResponseType<CheckDummyVknrResponse>, ErrorType,CheckDummyVknrRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await checkDummyVknr(request),
						retry: false,
						...opts
                });
            }
    
			export async function getTotalScheins(request: GetTotalScheinsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<GetTotalScheinsResponse>("POST", LEGACY_TOPIC_GetTotalScheins, { init , request})
			}

			export function useQueryGetTotalScheins<TransformedType =GetTotalScheinsResponse>(payload: GetTotalScheinsRequest,ops?: CustomUseQueryOptions<ResponseType<GetTotalScheinsResponse>, TransformedType>) {
                return useQuery<ResponseType<GetTotalScheinsResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetTotalScheins, payload],
					queryFn: async ({ signal }) => await getTotalScheins(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetTotalScheins(opts?: UseMutationOptions<ResponseType<GetTotalScheinsResponse>, ErrorType,GetTotalScheinsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getTotalScheins(request),
						retry: false,
						...opts
                });
            }
    

