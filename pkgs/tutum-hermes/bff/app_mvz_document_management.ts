/* eslint-disable */
// This code was autogenerated from app/mvz/document_management.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common1 from "./common"
import * as common2 from "./document_management_common"
import * as common from "./document_type_common"
import * as patient_profile_common from "./patient_profile_common"


// Type definitions
		export interface EventDocumentManagementChange {
		}
	

		export interface EventGdtExportResult {
				result: boolean
				error: string
		}
	

		export interface EventGdtImportResult {
				result: boolean
				patientName: string
				error: string
				type: common2.DocumentNotificationType
		}
	

		export interface CreateDocumentManagementRequest {
				documentName: string
				companionFileId: number
				companionFilePath: string
				status: common2.DocumentManagementStatus
		}
	

		export interface CreateDocumentManagementResponse {
				id: string
				documentManagementModel: common2.DocumentManagementModel
				presignedUrl: string
		}
	

		export interface UpdateDocumentManagementStatusRequest {
				id: string
				status: common2.DocumentManagementStatus
				importedDate: number
		}
	

		export interface UpdateDocumentManagementStatusResponse {
				id: string
				documentManagementModel: common2.DocumentManagementModel
		}
	

		export interface ListDocumentManagementRequest {
				value?: string
				isNotAssigned: boolean
				pagination?: common1.PaginationRequest
				fromDate?: number
				toDate?: number
				status?: common2.DocumentManagementStatus
				patientIds?: Array<string>
				senderIds?: Array<string>
		}
	

		export interface ListDocumentManagementResponse {
				data: Array<common2.DocumentManagementItem>
				pagination?: common1.PaginationResponse
		}
	

		export interface AssignPatientDocumentRequest {
				id: string
				patientId?: string
				sender?: common2.DocumentManagementSender
				documentType?: common.DocumentType
				description?: string
		}
	

		export interface GetDocumentManagementRequest {
				id: string
		}
	

		export interface GetDocumentManagementResponse {
				data: common2.DocumentManagementItem
		}
	

		export interface DeleteDocumentManagementRequest {
				id: string
				patientId?: string
		}
	

		export interface DeleteFailDocumentManagementRequest {
				ids: Array<string>
				isAll: boolean
		}
	

		export interface MarkReadDocumentManagementRequest {
				id: string
				patientId?: string
		}
	

		export interface UploadFile {
				fileName: string
				objectId: string
		}
	

		export interface UploadDocumentManagementRequest {
				files: Array<UploadFile>
		}
	

		export interface GetDocumentBadgeResponse {
				unassignedCount: number
				failCount: number
				totalCount: number
		}
	

		export interface ReImportFailDocumentRequest {
				ids: Array<string>
				isAll: boolean
		}
	

		export interface ExportGdtDocumentRequest {
				gdtExportSettingId: string
				treatmentDate?: number
				readingDate?: number
				patientId: string
				scheinId?: string
				treatmentTime?: number
				readingTime?: number
		}
	

		export interface LabParameter {
				name: string
				min: string
				max: string
				unit: string
		}
	

		export interface LabResultItem {
				name: string
				value: string
				icon: string
				testNote: string
				testResultText: string
		}
	

		export interface LabResultOverview {
				date: number
				labOrderId: string
				items: Array<LabResultItem>
		}
	

		export interface GetLabResultsRequest {
				patientId: string
				results?: number
				fromDate?: number
				toDate?: number
				fieldNames?: Array<string>
				isOnlyPathologicalResults?: boolean
		}
	

		export interface GetLabResultsResponse {
				labParameters: Array<LabParameter>
				availableLabParameters: Array<LabParameter>
				labResults: Array<LabResultOverview>
		}
	

		export interface GetLabResultsPDFResponse {
				pdf: Int8Array
				mode: LabResultsPDFMode
		}
	

		export interface ExportGdtDocumentResponse {
		}
	


// enum definitions
    export enum LabResultsPDFMode {
        PORTRAIT = "portrait",
        LANDSCAPE = "landscape",
	}


// event definition constant ----------------------------------------
       	const EVENT_DocumentManagementChange = "api.app.mvz.AppMvzDocumentManagement.DocumentManagementChange";
       	const EVENT_GdtExportResult = "api.app.mvz.AppMvzDocumentManagement.GdtExportResult";
       	const EVENT_GdtImportResult = "api.app.mvz.AppMvzDocumentManagement.GdtImportResult";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenDocumentManagementChange(handler: (data: EventDocumentManagementChange) => void): void {
			const [response, setResponse] = useState<EventDocumentManagementChange>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_DocumentManagementChange, _listener);
				return () => window.removeEventListener(EVENT_DocumentManagementChange, _listener);
			}, []);
        }
        export function useListenGdtExportResult(handler: (data: EventGdtExportResult) => void): void {
			const [response, setResponse] = useState<EventGdtExportResult>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_GdtExportResult, _listener);
				return () => window.removeEventListener(EVENT_GdtExportResult, _listener);
			}, []);
        }
        export function useListenGdtImportResult(handler: (data: EventGdtImportResult) => void): void {
			const [response, setResponse] = useState<EventGdtImportResult>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_GdtImportResult, _listener);
				return () => window.removeEventListener(EVENT_GdtImportResult, _listener);
			}, []);
        }

