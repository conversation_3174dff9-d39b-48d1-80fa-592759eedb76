/* eslint-disable */
// This code was autogenerated from service/domains/catalog_goa_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as catalog_utils_common from "./catalog_utils_common"
import * as common from "./common"


// Type definitions
		export interface ChapterRange {
				from: string
				to: string
		}
	

		export interface Chapter {
				chapterNumber: string
				chapterName: string
				subChapterNumber: string
				chapterRange: ChapterRange
		}
	

		export interface GoaCatalog {
				goaId: string
				goaNumber: string
				description: string
				excludedCode: Array<string>
				validity: catalog_utils_common.Validity
				factor: Factor
				evaluation: number
				unit: Unit
				maxEvaluation: string
				source: catalog_utils_common.SourceType
				chapter: Chapter
				price: number
				remark?: string
		}
	

		export interface GoaItem {
				id: string
				goaNumber: string
				description: string
				evaluation: number
				factor: Factor
				price: number
				isSelfCreated: boolean
				highlight: {[key:string]:common.Highlight}
		}
	


// enum definitions
    export enum Unit {
        Unit_Unknow = "Unit_Unknow",
        Unit_Points = "Unit_Points",
        Unit_Euros = "Unit_Euros",
	}

    export enum Factor {
        Factor_Medical = "Ärztlich",
        Factor_Technical = "Technisch",
        Factor_Laboratory = "Labor",
        Factor_Additional = "Zusatzleistungen",
        Factor_ReimbursementOfCost = "Kostenerstattung",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

