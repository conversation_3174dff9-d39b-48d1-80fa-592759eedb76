/* eslint-disable */
// This code was autogenerated from repo/mvz/encounter_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common7 from "./appointment_common"
import * as common10 from "./arriba_common"
import * as catalog_sdav_common from "./catalog_sdav_common"
import * as common3 from "./common"
import * as common6 from "./doctor_letter_common"
import * as common8 from "./document_management_common"
import * as common9 from "./document_type_common"
import * as common4 from "./form_common"
import * as common2 from "./himi_model_common"
import * as lab_common from "./lab_common"
import * as master_data_common from "./masterdata_common"
import * as patient_profile_common from "./patient_profile_common"
import * as common1 from "./repo_bmp_common"
import * as common from "./repo_medicine_common"
import * as common5 from "./repo_prescribed_heimi_common"
import * as waiting_room_common from "./waiting_room_common"


// Type definitions
		export interface DoctorLetterTimeline {
				id?: string
				sortOrder: number
				body: string
				templateId: string
				sender: common6.Sender
				receiver: common6.Receiver
				variables: Array<common6.Variable>
		}
	

		export interface EncounterNoteTimeline {
				id?: string
				sortOrder: number
				fromUnknownRow?: boolean
				note: string
				command: string
				sources?: Sources
				type?: EncounterNoteType
				auditLog?: common3.AuditLog
				schienIds?: Array<string>
		}
	

		export interface EncounterCalendarTimeline {
				waitingRoomPatient?: waiting_room_common.WaitingRoomPatient
				waitingRoomName?: string
				acceptableWaitingTimeInMinutes?: number
				activeTimeMeasurement?: boolean
		}
	

		export interface EncounterServiceTimeline {
				code: string
				description: string
				referralDoctorInfo?: ReferralDoctorInfo
				materialCosts?: MaterialCosts
				careFacility?: CareFacility
				freeText: string
				errors?: Array<EncounterItemError>
				command: string
				patientId?: string
				sources?: Sources
				isPreParticipate: boolean
				auditLog?: common3.AuditLog
				scheins?: Array<common3.ScheinWithMainGroup>
				serviceMainGroup?: common3.MainGroup
				additionalInfos?: Array<AdditionalInfoParent>
				additionalInfosRaw?: string
				chargeSystemId?: string
				approvalStatus?: EncounterServiceTimelinePsychotherapyStatus
				timelineServiceSuggestionId?: string
				eabId?: string
				preParticipateType?: PreParticipateType
				isSubmitPreParticipateSucsess?: boolean
				participateId?: string
				serviceId?: string
				price?: number
		}
	

		export interface Diagnose {
				code: string
				description: string
				certainty: Certainty
				timelineId?: string
		}
	

		export interface DiagnoseSuggestion {
				suggestionType: SuggestionType
				proposal: string
				diagnoses: Array<Diagnose>
				ruleId: string
				applied: boolean
				hint: string
				checkTime: master_data_common.CheckTime
		}
	

		export interface EncounterDiagnoseTimeline {
				code: string
				description: string
				type: DiagnoseType
				startDate?: number
				validUntil?: number
				certainty?: Certainty
				laterality?: Laterality
				hib: boolean
				mrsa: boolean
				imported: boolean
				freeText: string
				errors?: Array<EncounterItemError>
				command: string
				group: boolean
				sources?: Sources
				morbiRsa?: MorbiRsa
				auditLog?: common3.AuditLog
				scheins?: Array<common3.ScheinWithMainGroup>
				diagnoseSuggestions?: Array<DiagnoseSuggestion>
				runSdkrw: RunSdkrwEnum
				exception: string
				explanation: string
				sdvaRefs: Array<string>
				markedTreatmentRelevant: boolean
		}
	

		export interface EncounterItemError {
				type: EncounterItemErrorType
				message: string
				akaFunction: string
				errorCode: string
				metaData: {[key:string]:string}
				validationLevel: ValidationLevel
				errorParams: Array<string>
		}
	

		export interface QuarterEncounterItemError {
				type: EncounterItemErrorType
				message: string
				code: string
		}
	

		export interface MorbiRsa {
				chronic: boolean
				krhNumber: boolean
				krhLabel: boolean
		}
	

		export interface PrescriptionDates {
				createdDate: number
				changedDate?: number
				printedDate?: number
				deletedDate?: number
		}
	

		export interface Drug {
				drugId: string
				autIdem: boolean
				amount: number
		}
	

		export interface ReceiptKvDetails {
				documentRequired: boolean
				himi: boolean
				vaccine: boolean
		}
	

		export interface Delegate {
				havgNumber: string
				mediId: string
				lanr: string
		}
	

		export interface AdditionalInfoParent {
				fK: string
				value: string
				children: Array<AdditionalInfoChild>
		}
	

		export interface AdditionalInfoChild {
				fK: string
				value: string
		}
	

		export interface EncounterService {
				code: string
				description: string
				referralDoctorInfo?: ReferralDoctorInfo
				materialCosts?: MaterialCosts
				careFacility?: CareFacility
				freeText: string
				errors?: Array<EncounterItemError>
				command: string
				patientId?: string
				sources?: Sources
				isPreParticipate: boolean
				scheins?: Array<common3.ScheinWithMainGroup>
				serviceMainGroup?: common3.MainGroup
				additionalInfos?: Array<AdditionalInfoParent>
				additionalInfosRaw?: string
		}
	

		export interface ReferralDoctorInfo {
				bsnr?: string
				lanr?: string
				requiredLanr?: boolean
				requiredBsnr?: boolean
		}
	

		export interface MaterialCosts {
				required?: boolean
				materialCostsItemList?: Array<MaterialCostsItemList>
		}
	

		export interface MaterialCostsItemList {
				id?: string
				amount: string
				description: string
				required?: boolean
		}
	

		export interface CareFacility {
				name: string
				ort: string
				required?: boolean
		}
	

		export interface EncounterDiagnose {
				code: string
				description: string
				type: DiagnoseType
				validUntil?: number
				certainty?: Certainty
				laterality?: Laterality
				hib: boolean
				mrsa: boolean
				imported: boolean
				freeText: string
				errors?: Array<EncounterItemError>
				command: string
				group: boolean
				sources?: Sources
				morbiRsa?: MorbiRsa
				scheins?: Array<common3.ScheinWithMainGroup>
				exception: string
				explanation: string
				sdvaRefs: Array<string>
		}
	

		export interface EncounterNote {
				id?: string
				sortOrder: number
				fromUnknownRow?: boolean
				note: string
				command: string
				sources?: Sources
				type?: EncounterNoteType
				scheins?: Array<common3.ScheinWithMainGroup>
		}
	

		export interface EncounterMedicinePrescription {
				id: string
				medicinePrescriptionId: string
				formInfos: Array<common.FormInfo>
				billingInfo: common3.BillingInfo
				auditLog?: common3.AuditLog
				sortOrder: number
				patientId: string
				encounterId: string
		}
	

		export interface EncounterHimiPrescription {
				id: string
				himiPrescriptionId: string
				formInfo: common2.FormInfo
				additionalForm: common2.AdditionalForm
				billingInfo: common3.BillingInfo
				auditLog?: common3.AuditLog
				patientId: string
				encounterId: string
				printDate?: number
				prescribeDate?: number
				createdDate: number
				diagnoseCode: string
				secondaryDiagnore?: string
		}
	

		export interface HeimiForm {
				heimiPrescriptionId: string
				prescription: common5.Prescription
		}
	

		export interface EncounterHeimiPrescription {
				id: string
				heimiForm: HeimiForm
				patientId: string
				encounterId: string
				billingInfo: common3.BillingInfo
				sortOrder: number
				auditLog?: common3.AuditLog
		}
	

		export interface EncounterForm {
				id: string
				prescribe: common4.Prescribe
				patientId: string
				encounterId: string
				billingInfo: common3.BillingInfo
				sortOrder: number
				auditLog?: common3.AuditLog
				isEnrollmentForm: boolean
				enrollmentId?: string
				enrollmentFormType?: string
				enrollmentPrintFormStatus?: EnrollmentPrintFormStatus
				bgInvoice?: common6.BgInvoice
		}
	

		export interface EncounterLab {
				id: string
				labForm: lab_common.LabForm
				patientId: string
				encounterId: string
				billingInfo: common3.BillingInfo
				sortOrder: number
				auditLog?: common3.AuditLog
				labResultStatus?: lab_common.LabResultStatus
		}
	

		export interface EncounterPatientMedicalData {
				id: string
				sortOrder: number
				old: patient_profile_common.PatientMedicalData
				new?: patient_profile_common.PatientMedicalData
				patientId: string
				encounterId: string
				auditLog?: common3.AuditLog
		}
	

		export interface EncounterMedicinePlanHistory {
				id: string
				medicationPlanId: string
				actionType: string
				medicineInfo: common1.MedicationInformation
				beforeMedicineInfo?: common1.MedicationInformation
				auditLog?: common3.AuditLog
		}
	

		export interface EncounterEHIC {
				formSetting?: string
				language?: string
				status?: common3.EuropeanHealthInsuranceStatus
		}
	

		export interface ServiceCodeApproval {
				entryIds: Array<string>
				amountBilled: number
				amountApproval?: number
				terminalId?: string
		}
	

		export interface EncounterPsychotherapy {
				requestDate?: number
				approvalDate: number
				amountApproval: number
				amountBilled: number
				serviceCodes: Array<string>
				scheinId: string
				status: EncounterPsychotherapyStatus
				terminateServiceId?: string
				takeOverId?: string
				entries: {[key:string]:ServiceCodeApproval}
				referenceServiceCodes: Array<string>
				referenceAmountApproval: number
		}
	

		export interface EncounterGoaService {
				code: string
				description: string
				freeText: string
				factor: number
				quantity: number
				materialCosts?: MaterialCosts
				errors?: Array<EncounterItemError>
				price?: number
				additionalInfos?: Array<AdditionalInfoParent>
				additionalInfosRaw?: string
				command: string
				scheins?: Array<common3.ScheinWithMainGroup>
				isChangeDefault: boolean
				serviceId?: string
				originalPrice?: number
		}
	

		export interface EncounterUvGoaService {
				code: string
				description: string
				freeText: string
				materialCosts?: MaterialCosts
				errors?: Array<EncounterItemError>
				price?: number
				additionalInfos?: Array<AdditionalInfoParent>
				additionalInfosRaw?: string
				command: string
				scheins?: Array<common3.ScheinWithMainGroup>
				isGeneral: boolean
				serviceId?: string
		}
	

		export interface EncounterAppointmentTimeline {
				appointmentId: string
				appointmentContent: TimelineAppointmentContent
				auditLog?: common3.AuditLog
				updatedAppointmentDateTime?: common7.UpdatedAppointmentDateTime
				updatedTreatingDoctor?: common7.UpdatedTreatingPerson
				updatedTreatingMFA?: common7.UpdatedTreatingPerson
				updatedTodoTypeName?: common7.UpdatedStringValue
				updatedMedicalDeviceName?: common7.UpdatedStringValue
				appointmentAction: AppointmentAction
		}
	

		export interface TimelineAppointmentContent {
				treatingPerson: common7.TreatingPerson
				note: string
				startTime: number
				endTime: number
				todoTypeName: string
				medicalDeviceName: string
		}
	

		export interface EncounterDocumentManagement {
				id: string
				companionFileId: number
				companionFilePath: string
				patient: common8.Patient
				sender?: catalog_sdav_common.SdavCatalog
				documentName: string
				documentType?: common9.DocumentType
				description?: string
				status: common8.DocumentManagementStatus
				importedDate: number
				readBy?: common8.ReadBy
				documentUrl?: string
		}
	

		export interface EncounterArriba {
				sessionId: string
				companionFilePaths: Array<string>
				status: common10.ArribaStatus
				arribaId: string
		}
	

		export interface EncounterCustomize {
				documentTypeId: string
				command: string
				description: string
				sortOrder: number
		}
	

		export interface EncounterGDT {
				filePath: string
				fileName: string
				bucketName: string
				note: string
				gdtImportSettingId: string
				gdtImportSettingName: string
				documentManagementId: string
				command: string
				archiveFiles: Array<ArchiveFile>
				labOrderId: string
		}
	

		export interface EncounterLDT {
				filePath: string
				fileName: string
				bucketName: string
				note: string
				ldtImportSettingId: string
				ldtImportSettingName: string
				documentManagementId: string
				command: string
				type: EncounterLDTType
				labOrderId: string
		}
	

		export interface ArchiveFile {
				fileName: string
				objectName: string
		}
	


// enum definitions
    export enum EncounterServiceTimelinePsychotherapyStatus {
        IsApproval = "IsApproval",
        IsCompleted = "IsCompleted",
        HasBeenRemoveApproval = "HasBeenRemoveApproval",
        NotAcceptedByKV = "NotAcceptedByKV",
	}

    export enum PreParticipateType {
        UHU35 = "UHU35",
        KJP4a = "KJP4a",
	}

    export enum Laterality {
        U = "U",
        L = "L",
        R = "R",
        B = "B",
	}

    export enum RunSdkrwEnum {
        RUNSDKRWENUM_DEFAULT = "DEFAULT",
        RUNSDKRWENUM_RUNNING = "RUNNING",
        RUNSDKRWENUM_CANCELLED = "CANCELLED",
        RUNSDKRWENUM_DONE = "DONE",
	}

    export enum Certainty {
        G = "G",
        V = "V",
        Z = "Z",
        A = "A",
	}

    export enum EncounterNoteType {
        ANAMNESE = "ANAMNESE",
        FINDING = "FINDING",
        THERAPY = "THERAPY",
        NOTE = "NOTE",
	}

    export enum SuggestionType {
        SUGGESTIONTYPE_ADD = "SUGGESTIONTYPE_ADD",
        SUGGESTIONTYPE_DELETE = "SUGGESTIONTYPE_DELETE",
        SUGGESTIONTYPE_REPLACE = "SUGGESTIONTYPE_REPLACE",
	}

    export enum DiagnoseType {
        DIAGNOSETYPE_PERMANENT = "PERMANENT",
        DIAGNOSETYPE_ACUTE = "ACUTE",
        DIAGNOSETYPE_ANAMNESTIC = "ANAMNESTIC",
	}

    export enum DiagnoseCommand {
        PERMANENT_DIAGNOSE_COMMAND_DD = "DD",
        PERMANENT_DIAGNOSE_COMMAND_DA = "DA",
        ACUTE_DIAGNOSE_COMMAND = "D",
        ANAMNESTIC_DIAGNOSE_COMMAND = "AD",
	}

    export enum ValidationLevel {
        ValidationLevelEntry = "ValidationLevelEntry",
        ValidationLevelEncounter = "ValidationLevelEncounter",
        ValidationLevelQuarter = "ValidationLevelQuarter",
        ValidationLevelTimeline = "ValidationLevelTimeline",
	}

    export enum EncounterItemErrorType {
        EncounterItemErrorType_error = "error",
        EncounterItemErrorType_warning = "warning",
        EncounterItemErrorType_info = "info",
	}

    export enum Sources {
        Imported = "Imported",
        Composer = "Composer",
        Timeline = "Timeline",
        EAB = "EAB",
	}

    export enum EncounterCase {
        AB = "AB",
        PB = "PB",
        NOT = "NOT",
        PRE_ENROLLMENT = "PRE_ENROLLMENT",
	}

    export enum TreatmentCase {
        TreatmentCaseCustodian = "TreatmentCaseCustodian",
        TreatmentCaseDelegate = "TreatmentCaseDelegate",
        TreatmentCaseDeputy = "TreatmentCaseDeputy",
        TreatmentCasePreParticipate = "TreatmentCasePreParticipate",
	}

    export enum EnrollmentPrintFormStatus {
        EnrollmentPrintFormStatus_Generated = "Generated",
        EnrollmentPrintFormStatus_Printed_Created = "PrintedCreated",
        EnrollmentPrintFormStatus_Printed_Succesfully = "PrintedSuccesfully",
        EnrollmentPrintFormStatus_Printed_Failed = "PrintedFailed",
        EnrollmentPrintFormStatus_Incorrect = "Incorrect",
        EnrollmentPrintFormStatus_Handed_Over = "HandedOver",
        EnrollmentPrintFormStatus_Received = "Received",
	}

    export enum EncounterPsychotherapyStatus {
        INPROGRESS = "INPROGRESS",
        HAS_BEEN_TAKE_OVER = "HAS_BEEN_TAKE_OVER",
        COMPLETED = "COMPLETED",
        BILLED = "BILLED",
        READY_TO_BILL = "READY_TO_BILL",
	}

    export enum AppointmentAction {
        CREATE = "CREATE",
        RESCHEDULE = "RESCHEDULE",
        CANCEL = "CANCEL",
        REMOVE = "REMOVE",
	}

    export enum EncounterLDTType {
        LDT_RESULT = "LDT_RESULT",
        LDT_ORDER = "LDT_ORDER",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

