/* eslint-disable */
// This code was autogenerated from app/companion/companion_dm.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';



// Type definitions
		export interface GetFolderStateRequest {
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				settingId: string
				recursive: boolean
		}
	

		export interface GetFolderStateResponse {
				folderState: {[key:string]:number}
		}
	

		export interface FileUploadInfo {
				filePath: string
				presignedUrl: string
		}
	

		export interface UploadFilesRequest {
				listFileUploadInfo: Array<FileUploadInfo>
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				deleteAfterUpload: boolean
		}
	

		export interface WatchFolderRequest {
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				settingId: string
				recursive: boolean
		}
	

		export interface ExportGdtDocumentRequest {
				presignUrl: string
				fileName: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				externalAppFilePath?: string
		}
	

		export interface CheckGdtImportDocumentRequest {
				filename: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				lastModifyTime: number
				settingId: string
				characterEncoding: CharacterEncoding
		}
	

		export interface RemoveGdtDocumentRequest {
				filename: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				pdfFileName?: string
		}
	

		export interface CheckGdtImportDocumentResponse {
				modifyTime: number
				isChanged: boolean
				gdtDocumentContent: string
		}
	

		export interface StopWatchFolderRequest {
				sourceType: SourceTypeEnum
				settingId: string
		}
	

		export interface HandleDocumentRequest {
				filename: string
				folder: string
				host?: string
				username?: string
				password?: string
				sourceType: SourceTypeEnum
				lastModifyTime: number
				settingId: string
				characterEncoding: CharacterEncoding
				fileExt: string
		}
	

		export interface CheckExistedDocumentResponse {
				isExisted: boolean
		}
	


// enum definitions
    export enum SourceTypeEnum {
        SourceTypeEnum_LOCAL = "local",
        SourceTypeEnum_FTP = "ftp",
        SourceTypeEnum_SMB = "smb",
	}

    export enum CharacterEncoding {
        CharacterEncoding_IBM437 = "IBM437",
        CharacterEncoding_7Bit = "7-bit",
        CharacterEncoding_ISO8859 = "ISO8859-1 ANSI CP 1252",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

