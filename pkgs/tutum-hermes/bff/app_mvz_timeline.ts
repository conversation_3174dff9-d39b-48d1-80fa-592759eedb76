/* eslint-disable */
// This code was autogenerated from app/mvz/timeline.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"
import * as common1 from "./eab_service_history_common"
import * as common2 from "./form_common"
import * as patient_encounter from "./repo_encounter"
import * as schein_common from "./schein_common"
import * as validation_timeline from "./service_domains_validation_timeline"
import * as common3 from "./timeline_common"


// Type definitions
		export interface GetRequest {
				patientId: string
				contractId?: string
				createdDate?: number
				encounterCase?: string
				treatmentDoctorId?: string
				types?: Array<common3.TimelineEntityType>
		}
	

		export interface GetResponse {
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface CreateRequest {
				timelineModel: common3.TimelineModel
		}
	

		export interface CreateResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface RemoveRequest {
				hasHardDelete?: boolean
				timelineId?: string
				isChain?: boolean
		}
	

		export interface RemoveResponse {
		}
	

		export interface EditRequest {
				timelineModel: common3.TimelineModel
		}
	

		export interface EditResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface UpdatePrintDateRequest {
				type: common3.TimelineEntityType
				timelineId?: string
				formId?: string
				printDate?: number
		}
	

		export interface UpdateSuggestionRuleAppliedRequest {
				timelineId?: string
				ruleId: string
		}
	

		export interface UpdatePrintDateResponse {
		}
	

		export interface EventTimelineHardRemove {
				patientId: string
				timelineModel: common3.TimelineModel
				timelineId: string
		}
	

		export interface EventTimelineRemove {
				patientId: string
				timelineModel: common3.TimelineModel
		}
	

		export interface EventTimelineRestore {
				timelineModel: common3.TimelineModel
		}
	

		export interface EventTimelineCreate {
				patientId: string
				timelineModel: common3.TimelineModel
		}
	

		export interface EventTimelineUpdate {
				patientId: string
				timelineModel: common3.TimelineModel
				oldTimelineModel?: common3.TimelineModel
				skipUpdateEndDatePermanentDiagnoses: boolean
				auditLogId?: string
				prescribeFormName?: common2.FormName
		}
	

		export interface EventAutoAction {
				patientId: string
				notificationCode: string
		}
	

		export interface ITimelineEntityType {
				timelineEntityType: common3.TimelineEntityType
				command?: string
		}
	

		export interface GroupByQuarterRequest {
				patientId: string
				fromDate?: number
				toDate?: number
				timelineEntityTypes?: Array<ITimelineEntityType>
				isSortByCategory: boolean
				isHistoryMode: boolean
				scheinId?: string
				keyword?: string
				pagination?: common.PaginationRequest
				year?: number
				quarter?: number
		}
	

		export interface GroupByQuarter {
				year: number
				quarter: number
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface GroupByQuarterResponse {
				groupByQuarters: Array<GroupByQuarter>
				totalPage: number
				matchedTokens: Array<string>
		}
	

		export interface EventTimelineValidation {
				patientId: string
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface GetByIdRequest {
				timelineId: string
		}
	

		export interface GetByIdsRequest {
				timelineIds: Array<string>
		}
	

		export interface GetByIdResponse {
				timelineModel?: common3.TimelineModel
		}
	

		export interface GetDiagnoseRequest {
				patientId: string
				codes?: Array<string>
		}
	

		export interface GetDiagnoseResponse {
				encounterDiagnoseTimeline: Array<patient_encounter.EncounterDiagnoseTimeline>
		}
	

		export interface IgnoreSdkrwRuleRequest {
				patientId: string
				ruleId: string
				encounterDate: number
		}
	

		export interface UpdateErezeptStatusRequest {
				medicineId: string
				status: string
		}
	

		export interface DeleteErezeptRequest {
				medicineId: string
				patientId: string
		}
	

		export interface GetTherapiesRequest {
				patientId: string
				scheinId: string
		}
	

		export interface TherapiesResponse {
				pyschotherapy: schein_common.Psychotherapy
				timelineModel: common3.TimelineModel
		}
	

		export interface GetTherapiesResponse {
				pyschotherapies: Array<TherapiesResponse>
		}
	

		export interface GetAmountServiceCodeRequest {
				patientId: string
				serviceCodes: Array<string>
				year: number
				quarter: number
		}
	

		export interface GetAmountServiceCodeResponse {
				amountServiceCode: {[key:string]:number}
		}
	

		export interface MarkNotApprovedPyschotherapyRequest {
				timelineId: Array<string>
				patientId: string
		}
	

		export interface RestoreEntryHistoryRequest {
				auditLogId: string
		}
	

		export interface RestoreEntryHistoryResponse {
		}
	

		export interface ValidateDiagnoseRequest {
				icdCode: Array<string>
				typeCheck: Array<common3.IcdErrorTypeCheck>
		}
	

		export interface ValidateDiagnoseResponse {
				results: Array<common3.ValidationDiagnoseResult>
		}
	

		export interface ReRunValidateServiceRequest {
				patientId: string
				contractId?: string
		}
	

		export interface GetTimelineEntryByIdsRequest {
				entryIds: Array<string>
		}
	

		export interface GetTimelineEntryIdsResponse {
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface GetPreParticipationServiceCodesRequest {
				patientId: string
		}
	

		export interface Document88130Request {
				timelineModel: common3.TimelineModel
				serviceEntryId: string
		}
	

		export interface Document88130Response {
				takeoverDiagnosis: Array<common3.TimelineModel>
				scheinId?: string
				serviceId: string
		}
	

		export interface GetPsychotherapyTakeOverRequest {
				patientId: string
		}
	

		export interface GetPsychotherapyTakeOverResponse {
				psychotherapyEntry: Array<common3.TimelineModel>
		}
	

		export interface TakeoverServiceTerminalApprovalRequest {
				timelineId: string
				scheinId: string
		}
	

		export interface GetTakeOverDiagnosisRequest {
				patientId: string
				fromDate?: number
				toDate?: number
				query?: string
				scheinId?: string
		}
	

		export interface GetTakeOverDiagnosisResponse {
				takeOverDiagnosisGroup: Array<common3.TakeOverDiagnosisGroup>
		}
	

		export interface GetPsychotherapyBefore2020 {
				timelineModel: common3.TimelineModel
				errorCode: validation_timeline.ServiceErrorCode
				serviceEntry: common3.TimelineModel
		}
	

		export interface GetPsychotherapyBefore2020Request {
				patientId: string
		}
	

		export interface GetPsychotherapyBefore2020Response {
				data: Array<GetPsychotherapyBefore2020>
		}
	

		export interface UpdateManyRequest {
				timelineModels: Array<common3.TimelineModel>
		}
	

		export interface TakeOverDiagnosisWithScheinIdRequest {
				scheinId: string
				timelineModelIds: Array<string>
				newDiagnosis: Array<common3.TimelineModel>
				mappingTreatmentRelevent: {[key:string]:boolean}
		}
	

		export interface MarkTreatmentRelevantRequest {
				timelineId: string
		}
	

		export interface UpdateEncounterCaseForServiceEntriesRequest {
				timelineId: string
		}
	

		export interface MarkAcceptedByKVRequest {
				timelineId: string
		}
	

		export interface DocumentSuggestionRequest {
				timelineId: string
				suggestionCode: string
				suggestionData?: {[key:string]:string}
		}
	

		export interface RollbackDocumentTerminateServiceRequest {
				terminateServiceId: string
				tehcnicalScheinId: string
				patientId: string
		}
	

		export interface GetLastDocumentedQuarterRequest {
				patientId: string
				year: number
				quarter: number
				timelineEntityType?: common3.TimelineEntityType
		}
	

		export interface GetLastDocumentedQuarterResponse {
				year: number
				quarter: number
		}
	

		export interface GetTimelineByEnrollmentIdRequest {
				enrollmentId: string
				patientId: string
		}
	

		export interface GetTimelineByEnrollmentIdResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface ReSendEABMailRequest {
				timelineId: string
		}
	

		export interface DocumentEABServiceCodeRequest {
				scheinId: string
				patientId: string
				bsnrCode: string
		}
	

		export interface DocumentEABServiceCodeResponse {
				serviceCodes: Array<common1.EABServiceCode>
		}
	

		export interface FindLatesTimelineEntryRequest {
				patientId: string
				type: common3.TimelineEntityType
				contractId: string
		}
	

		export interface FindLatesTimelineEntryResponse {
				timelineModel: common3.TimelineModel
		}
	

		export interface CheckIsVSST785Request {
				contractId: string
				patientId: string
				atcCodes: Array<string>
				ikNumber?: number
		}
	

		export interface CheckIsVSST785Response {
				isVSST785: boolean
		}
	

		export interface GetDoctorLetterByIdRequest {
				doctorLetterId: string
		}
	

		export interface GetDoctorLetterByIdResponse {
				timelineModel?: common3.TimelineModel
		}
	

		export interface GetActionChainDiagnoseByCodesRequest {
				codes: Array<string>
				year: number
		}
	

		export interface GetActionChainDiagnoseByCodesResponse {
				validItems: Array<string>
				inValidItems: Array<string>
		}
	


// enum definitions

// event definition constant ----------------------------------------
       	const EVENT_TimelineHardRemove = "api.app.mvz.AppMvzTimeline.TimelineHardRemove";
       	const EVENT_TimelineRemove = "api.app.mvz.AppMvzTimeline.TimelineRemove";
       	const EVENT_TimelineRestore = "api.app.mvz.AppMvzTimeline.TimelineRestore";
       	const EVENT_TimelineCreate = "api.app.mvz.AppMvzTimeline.TimelineCreate";
       	const EVENT_TimelineUpdate = "api.app.mvz.AppMvzTimeline.TimelineUpdate";
       	const EVENT_AutoAction = "api.app.mvz.AppMvzTimeline.AutoAction";
       	const EVENT_TimelineValidation = "api.app.mvz.AppMvzTimeline.TimelineValidation";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenTimelineHardRemove(handler: (data: EventTimelineHardRemove) => void): void {
			const [response, setResponse] = useState<EventTimelineHardRemove>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_TimelineHardRemove, _listener);
				return () => window.removeEventListener(EVENT_TimelineHardRemove, _listener);
			}, []);
        }
        export function useListenTimelineRemove(handler: (data: EventTimelineRemove) => void): void {
			const [response, setResponse] = useState<EventTimelineRemove>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_TimelineRemove, _listener);
				return () => window.removeEventListener(EVENT_TimelineRemove, _listener);
			}, []);
        }
        export function useListenTimelineRestore(handler: (data: EventTimelineRestore) => void): void {
			const [response, setResponse] = useState<EventTimelineRestore>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_TimelineRestore, _listener);
				return () => window.removeEventListener(EVENT_TimelineRestore, _listener);
			}, []);
        }
        export function useListenTimelineCreate(handler: (data: EventTimelineCreate) => void): void {
			const [response, setResponse] = useState<EventTimelineCreate>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_TimelineCreate, _listener);
				return () => window.removeEventListener(EVENT_TimelineCreate, _listener);
			}, []);
        }
        export function useListenTimelineUpdate(handler: (data: EventTimelineUpdate) => void): void {
			const [response, setResponse] = useState<EventTimelineUpdate>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_TimelineUpdate, _listener);
				return () => window.removeEventListener(EVENT_TimelineUpdate, _listener);
			}, []);
        }
        export function useListenAutoAction(handler: (data: EventAutoAction) => void): void {
			const [response, setResponse] = useState<EventAutoAction>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_AutoAction, _listener);
				return () => window.removeEventListener(EVENT_AutoAction, _listener);
			}, []);
        }
        export function useListenTimelineValidation(handler: (data: EventTimelineValidation) => void): void {
			const [response, setResponse] = useState<EventTimelineValidation>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_TimelineValidation, _listener);
				return () => window.removeEventListener(EVENT_TimelineValidation, _listener);
			}, []);
        }

