/* eslint-disable */
// This code was autogenerated from app/admin/text_module.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"
import * as text_module_common from "./text_module_common"


// Type definitions
		export interface CreateTextModuleRequest {
				textModule: text_module_common.TextModule
		}
	

		export interface UpdateTextModuleRequest {
				textModule: text_module_common.TextModule
		}
	

		export interface IsValidResponse {
				errors: {[key:string]:common.FieldError}
		}
	

		export interface DeactivateTextModuleRequest {
				id: string
		}
	

		export interface GetOmimGRequest {
				search: string
		}
	

		export interface OmimG {
				id: string
				code: string
				genName: string
		}
	

		export interface GetOmimGResponse {
				omimGs: Array<OmimG>
		}
	

		export interface EventTextModuleUpdated {
				id: string
				textModule: text_module_common.TextModule
		}
	


// enum definitions

// event definition constant ----------------------------------------
       	const EVENT_TextModuleUpdated = "api.app.admin.AppAdminTextModule.TextModuleUpdated";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenTextModuleUpdated(handler: (data: EventTextModuleUpdated) => void): void {
			const [response, setResponse] = useState<EventTextModuleUpdated>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_TextModuleUpdated, _listener);
				return () => window.removeEventListener(EVENT_TextModuleUpdated, _listener);
			}, []);
        }

