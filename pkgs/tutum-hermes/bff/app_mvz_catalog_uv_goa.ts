/* eslint-disable */
// This code was autogenerated from app/mvz/catalog_uv_goa.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as catalog_uv_goa_common from "./catalog_uv_goa_common"
import * as common from "./common"


// Type definitions
		export interface CreateUvGoaCatalogRequest {
				uvGoa: catalog_uv_goa_common.UvGoaCatalog
		}
	

		export interface UvGoaCatalogItem {
				uvGoa: catalog_uv_goa_common.UvGoaCatalog
		}
	

		export interface GetUvGoaCatalogsRequest {
				value: string
				isOnlySelfCreated: boolean
				pagination: common.PaginationRequest
		}
	

		export interface SearchUvGoaRequest {
				value: string
				selectedDate?: number
				isGeneral: boolean
		}
	

		export interface SearchUvGoaResponse {
				items: Array<catalog_uv_goa_common.UvGoaItem>
		}
	

		export interface GetUvGoaCatalogsResponse {
				items: Array<catalog_uv_goa_common.UvGoaCatalog>
				total: number
		}
	

		export interface UpdateUvGoaCatalogRequest {
				uvGoa: catalog_uv_goa_common.UvGoaCatalog
		}
	

		export interface IsValidUpdateUvGoaResponse {
				errors: {[key:string]:common.FieldError}
		}
	

		export interface DeleteUvGoaCatalogRequest {
				id: string
		}
	

		export interface GetUvGoaCatalogByCodeRequest {
				number: string
		}
	

		export interface GetUvGoaCatalogByCodesRequest {
				numbers: Array<string>
		}
	

		export interface GetUvGoaCatalogByCodesResponse {
				items: Array<catalog_uv_goa_common.UvGoaCatalog>
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

