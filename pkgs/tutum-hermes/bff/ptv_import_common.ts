/* eslint-disable */
// This code was autogenerated from service/domains/ptv_import_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"


// Type definitions
		export interface GetCodePtvImportByDoctorRequest {
				doctorId: string
				year: number
				quarter: number
		}
	

		export interface GetCodePtvImportByDoctorResponse {
				code: string
		}
	

		export interface GetPtvContractByDoctorRequest {
				doctorId: string
				year: number
				quarter: number
				code: string
		}
	

		export interface GetPtvContractByDoctorResponse {
				contracts: Array<ImportContract>
		}
	

		export interface ImportContract {
				contractId: string
				documentId: string
				version: number
				status: ImportContractStatus
				fileInfo?: FileInfo
				year: number
				quarter: number
				ptvImportId: string
		}
	

		export interface PtvImport {
				doctorId: string
				retrievalCode: string
		}
	

		export interface GetParticipantsByDoctorRequest {
				doctorId: string
				code: string
				documentId: string
				year: number
				quarter: number
				version: number
				contractId: string
				newSession: boolean
				ptvImportId: string
		}
	

		export interface GetParticipantsByDoctorResponse {
				id: string
				doctorId: string
				contractId: string
				documentId: string
				autoImportParticipants: Array<ParticipantDecision>
				conflictParticipants: Array<ParticipantDecision>
				missingParticipants: Array<ParticipantDecision>
				beforeParticipantCount: number
				afterParticipantCount: number
				year: number
				quarter: number
				ptvImportId: string
		}
	

		export interface ParticipantDecision {
				id: string
				patientId: string
				ikNumber: IkNumber
				insuranceNumber: InsuranceNumber
				status: Status
				treatmentType: TreatmentType
				firstName: FirstName
				lastName: LastName
				reason: Reason
				gender: Gender
				dob: Dob
				contractBeginDate: ContractBeginDate
				contractEndDate: ContractEndDate
				markAsDone: boolean
				typeGroupDecision: TypeGroupDecision
				isProccessing: boolean
				ppId?: string
				conflictResolved: boolean
				hints?: Array<string>
		}
	

		export interface IkNumber {
				localIkNumber: NumberSelection
				hpmIkNumber: NumberSelection
		}
	

		export interface InsuranceNumber {
				localInsuranceNumber: StringSelection
				hpmInsuranceNumber: StringSelection
		}
	

		export interface Status {
				localStatus: StatusSelection
				hpmStatus: StatusSelection
		}
	

		export interface FirstName {
				localFirstName: StringSelection
				hpmFirstName: StringSelection
		}
	

		export interface TreatmentType {
				localTreatmentType: StringSelection
				hpmTreatmentType: StringSelection
		}
	

		export interface LastName {
				localLastName: StringSelection
				hpmLastName: StringSelection
		}
	

		export interface Reason {
				localReason: StringSelection
				hpmReason: StringSelection
		}
	

		export interface Gender {
				localGender: GenderSelection
				hpmGender: GenderSelection
		}
	

		export interface Dob {
				localDOB: NumberSelection
				hpmDOB: NumberSelection
		}
	

		export interface ContractBeginDate {
				localContractBeginDate: NumberSelection
				hpmContractBeginDate: NumberSelection
		}
	

		export interface ContractEndDate {
				localContractEndDate: NumberSelection
				hpmContractEndDate: NumberSelection
		}
	

		export interface ImportParticipantsRequest {
				id: string
				doctorId: string
				contractId: string
				documentId: string
				autoImportParticipants: Array<ParticipantDecision>
				conflictParticipants: Array<ParticipantDecision>
				missingParticipants: Array<ParticipantDecision>
				year: number
				quarter: number
				importType: PTVImportType
				ptvImportId: string
		}
	

		export interface GetListPtvImportHistoryRequest {
				pagination: common.Pagination
		}
	

		export interface GetListPtvImportHistoryResponse {
				data: Array<PtvImportHistory>
				total: number
		}
	

		export interface PtvImportHistory {
				id: string
				doctorId: string
				contractId: string
				documentId: string
				autoImportParticipants: Array<ParticipantDecision>
				conflictParticipants: Array<ParticipantDecision>
				missingParticipants: Array<ParticipantDecision>
				importerId: string
				createTime: number
				updateTime: number
				year: number
				quarter: number
				status: ImportContractStatus
				beforeParticipantCount: number
				afterParticipantCount: number
				ptvImportId: string
				code: string
				version: number
		}
	

		export interface ImportTestDataRequest {
				doctorId: string
				xmlData: string
		}
	

		export interface StringSelection {
				value: string
				selected: boolean
		}
	

		export interface NumberSelection {
				value?: number
				selected: boolean
		}
	

		export interface GenderSelection {
				value: patient_profile_common.Gender
				selected: boolean
		}
	

		export interface StatusSelection {
				value: PatientParticipationStatus
				selected: boolean
		}
	

		export interface FileInfo {
				bucketName: string
				objectName: string
				hash: string
				createdDate: number
		}
	


// enum definitions
    export enum ImportParticipantsType {
        ImportType_Auto = "autoImportParticipants",
        ImportType_Conflict = "conflictParticipants",
        ImportType_Missing = "missingParticipants",
	}

    export enum ImportContractStatus {
        ImportContractStatus_New = "NEW",
        ImportContractStatus_InProgress = "INPROGRESS",
        ImportContractStatus_Done = "DONE",
        ImportContractStatus_Pending = "PENDING",
	}

    export enum PTVImportType {
        ImportType_Basic = "basic",
        ImportType_Full = "full",
	}

    export enum TypeGroupDecision {
        GeneralGroupUnchanged = "Unchanged",
        GeneralGroupNew = "New",
        GeneralGroupTerminated = "Terminated",
        GeneralGroupRequested = "Requested",
        GeneralGroupRejected = "Rejected",
        SpecialGroup = "SpecialGroup",
        MissingGroupPTV = "MissingGroupPTV",
        MissingGroupIV = "MissingGroupIV",
	}

    export enum PatientParticipationStatus {
        PatientParticipation_Requested = "REQUESTED",
        PatientParticipation_Rejected = "REJECTED",
        PatientParticipation_Active = "ACTIVE",
        PatientParticipation_Cancelled = "CANCELLED",
        PatientParticipation_Terminated = "TERMINATED",
        PatientParticipation_Faulty = "FAULTY",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

