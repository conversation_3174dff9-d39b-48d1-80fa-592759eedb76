/* eslint-disable */
// This code was autogenerated from app/mvz/bg_billing.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./bg_billing_common"
import * as common1 from "./common"
import * as patient_profile_common from "./patient_profile_common"
import * as common2 from "./timeline_common"


// Type definitions
		export interface CreateBgBillingRequest {
				item: common.BillingRecord
		}
	

		export interface GetBgBillingsRequest {
				search?: string
				pagination: common1.PaginationRequest
				filter?: common.BgBillingFilter
		}
	

		export interface GetBgBillingsResponse {
				items: Array<common.BgBillingItem>
				total: number
		}
	

		export interface GetBgBillingByScheinIdRequest {
				scheinId: string
		}
	

		export interface GetBgBillingByScheinIdResponse {
				item: common.BgBillingItem
		}
	

		export interface GetBgBillingByIdRequest {
				id: string
		}
	

		export interface GetBgBillingByIdResponse {
				item: common.BgBillingItem
		}
	

		export interface GetUvGoaServiceCodeRequest {
				id: string
		}
	

		export interface GetUvGoaServiceCodeByIdsRequest {
				ids: Array<string>
		}
	

		export interface UvGoaServiceTimelineData {
				billingId: string
				timelineModels: Array<common2.TimelineModel>
				invoiceInfo: common.InvoiceInfo
		}
	

		export interface GetUvGoaServiceCodeResponse {
				data: UvGoaServiceTimelineData
		}
	

		export interface GetUvGoaServiceCodeByIdsResponse {
				data: Array<UvGoaServiceTimelineData>
		}
	

		export interface GetPrintedInvoicesRequest {
				patientId: string
				billingId: string
		}
	

		export interface GetPrintedInvoicesResponse {
				timelineModels: Array<common2.TimelineModel>
		}
	

		export interface MarkBgBillingPaidRequest {
				patientId: string
				billingId: string
				invoiceNumber: string
		}
	

		export interface MarkBgBillingPaidResponse {
				timelineModel: common2.TimelineModel
		}
	

		export interface MarkBgBillingUnpaidRequest {
				patientId: string
				billingId: string
		}
	

		export interface MarkBgBillingUnpaidResponse {
				item: common.BgBillingItem
		}
	

		export interface MarkBgBillingCancelledRequest {
				patientId: string
				billingId: string
		}
	

		export interface MarkBgBillingCancelledResponse {
				item: common.BgBillingItem
		}
	

		export interface GetListDoctorResponse {
				doctors: Array<common.Doctor>
		}
	

		export interface GetListInsuranceResponse {
				insurances: Array<patient_profile_common.InsuranceInfo>
		}
	

		export interface GetListStatusResponse {
				status: Array<common.BillingStatus>
		}
	

		export interface GetRangeAmountResponse {
				minPrice: number
				maxPrice: number
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

