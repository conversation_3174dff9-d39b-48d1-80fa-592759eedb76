/* eslint-disable */
// This code was autogenerated from app/mvz/billing_edoku.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./billing_edoku_common"
import * as billing_history_common from "./billing_history_common"
import * as common1 from "./common"
import * as common2 from "./edmp_common"


// Type definitions
		export interface GetValidationListRequest {
				quarter: common1.YearQuarter
				bsnrId: string
				documentType: string
				openPreviousQuarter: boolean
		}
	

		export interface GetValidationListResponse {
				billingValidationList: Array<common.BillingValidationListModel>
				totalPatient: number
		}
	

		export interface TroubleshootRequest {
		}
	

		export interface TroubleshootResponse {
		}
	

		export interface CreateBillingRequest {
				documentIds: Array<string>
				quarter: common1.YearQuarter
				bsnr: string
				dMPValue: common2.DMPValueEnum
				isOpenPreviousQuarter: boolean
		}
	

		export interface CreateBillingResponse {
				status: boolean
				dMPBillingHistoryId: string
				dMPBillingFieldsValidationResults: Array<common2.DMPBillingFieldsValidationResult>
		}
	

		export interface GetBillingSummaryRequest {
		}
	

		export interface GetBillingSummaryResponse {
		}
	

		export interface PrepareForShippingRequest {
				billingHistoryId: string
		}
	

		export interface SendMailRequest {
				billingHistoryId: string
				senderMailSettingId: string
				testMailTo?: string
		}
	

		export interface DownloadBillingFileRequest {
		}
	

		export interface DownloadBillingFileResponse {
		}
	

		export interface UndoSubmissionRequest {
				billingHistoryId: string
		}
	

		export interface GetDispatchListRequest {
				query?: string
		}
	

		export interface GetDispatchListResponse {
				billingHistories: Array<common.BillingHistoryModel>
		}
	

		export interface GetEdokuBillingSelectionResponse {
				quarters: Array<common1.YearQuarter>
				bsnrs: Array<billing_history_common.BSNR>
				documentTypes: Array<string>
		}
	

		export interface CheckForValidationRequest {
				documentIds: Array<string>
				quarter: common1.YearQuarter
				bsnr: string
		}
	

		export interface CheckForValidationResponse {
				status: boolean
				dMPBillingFieldsValidationResults: Array<common2.DMPBillingFieldsValidationResult>
		}
	

		export interface GetBillingHistoryRequest {
				billingHistoryId: string
		}
	

		export interface GetBillingHistoryResponse {
				billingHistory: common.BillingHistoryModel
		}
	

		export interface EventBillingEDokuStatusChanged {
				billingHistoryId: string
				status: common.BillingStatus
		}
	

		export interface GetEdokuDocumentByIdsRequest {
				documentIds: Array<string>
		}
	

		export interface GetEdokuDocumentByIdsResponse {
				documents: Array<common2.DocumentationOverview>
		}
	


// enum definitions

// event definition constant ----------------------------------------
       	const EVENT_BillingEDokuStatusChanged = "api.app.mvz.AppMvzBillingEdoku.BillingEDokuStatusChanged";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenBillingEDokuStatusChanged(handler: (data: EventBillingEDokuStatusChanged) => void): void {
			const [response, setResponse] = useState<EventBillingEDokuStatusChanged>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_BillingEDokuStatusChanged, _listener);
				return () => window.removeEventListener(EVENT_BillingEDokuStatusChanged, _listener);
			}, []);
        }

