/* eslint-disable */
// This code was autogenerated from app/admin/ti_connector.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as ti_connector_common from "./ti_connector_common"


// Type definitions
		export interface ConnectionStatus {
				severity: ConnectionStatusEnum
				validFrom: number
				type: string
				eventStatus: string
		}
	

		export interface GetTIConnectorsResponse {
				tIConnectors: Array<ti_connector_common.TIConnectorListItem>
		}
	

		export interface SaveTIConnectorRequest {
				iD?: string
				tIConnector: ti_connector_common.TIConnector
		}
	

		export interface TIConnectorRequest {
				iD: string
		}
	

		export interface ViewConnectorStatusResponse {
				tIConnector: ti_connector_common.TIConnector
				manufacturer: string
				connectorTypeVersion: string
				hardWareVersion: string
				firmWareVersion: string
				tiConnection: string
				sisConnection: string
				connectionStatuses: Array<ConnectionStatus>
				unsupportedVersionServices: Array<string>
				productName: string
		}
	

		export interface CheckTIConnectorStatusRequest {
				ids: Array<string>
		}
	

		export interface ConnectorStatus {
				id: string
				status: string
				expiredDate: number
		}
	

		export interface CheckTIConnectorStatusResponse {
				connectorStatuses: Array<ConnectorStatus>
		}
	


// enum definitions
    export enum ConnectionStatusEnum {
        ConnectionStatusEnum_Info = "Info",
        ConnectionStatusEnum_Error = "Error",
        ConnectionStatusEnum_Warning = "Warning",
        ConnectionStatusEnum_Fatal = "Fatal",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

