/* eslint-disable */
// This code was autogenerated from app/mvz/patient_combination.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';



// Type definitions
		export interface CombinePatientsRequest {
				targetPatientId: string
				duplicatedPatientIds: Array<string>
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

