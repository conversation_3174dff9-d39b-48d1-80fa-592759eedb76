/* eslint-disable */
// This code was autogenerated from service/domains/catalog_uv_goa_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as catalog_utils_common from "./catalog_utils_common"
import * as common from "./common"


// Type definitions
		export interface UvGoaCatalog {
				uvGoaId: string
				code: string
				description: string
				additionalCode?: string
				validity: catalog_utils_common.Validity
				generalTreatmentEvaluation?: number
				generalCost?: number
				hospitalTreatmentEvaluation?: string
				longDescription: string
				source: catalog_utils_common.SourceType
				materialCost?: number
				residentialTreatmentEvaluation?: string
				specificTreatmentEvaluation?: number
				isNotBillable?: boolean
		}
	

		export interface UvGoaItem {
				uvGoaId: string
				code: string
				description: string
				evaluation: number
				isSelfCreated: boolean
				highlight: {[key:string]:common.Highlight}
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

