/* eslint-disable */
// This code was autogenerated from service/domains/catalog_sdebm_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as catalog_sdkt_common from "./catalog_sdkt_common"
import * as catalog_utils_common from "./catalog_utils_common"
import * as common from "./common"


// Type definitions
		export interface Time {
				value: string
				unit: string
		}
	

		export interface SdebmCatalog {
				sdebmId: string
				code: string
				description: string
				longDescription: string
				validity: catalog_utils_common.Validity
				startBillingFrom?: number
				evaluation: number
				unit: UnitStatistiks
				duration: number
				source: catalog_utils_common.SourceType
				receiptDescription: string
				chapterAssignment?: string
				notes: string
				timeTesting?: Time
				timeRequirement?: Array<Time>
				profileTime?: string
				performanceGroup?: string
				isUpdated: boolean
		}
	

		export interface DataSource {
				name: string
				value: string
		}
	

		export interface TreatmentCaseWithRule {
				treatmentCase: string
				ruleNames: Array<string>
		}
	

		export interface Field {
				fK: string
				label: string
				minLength?: number
				maxLength?: number
				isMany: boolean
				dataType: DataType
				inputType: InputType
				dataSource: string
				dataSourceValues: Array<string>
				isRequired: boolean
				dataId?: string
				treatmentCaseWithRule: Array<TreatmentCaseWithRule>
				additionalInformations: Array<Field>
		}
	

		export interface FieldValue {
				fK: string
				value: string
				additionalInformations: Array<FieldValue>
		}
	

		export interface RuleInfo {
				currentField: FieldValue
				currentFieldRule: Field
				previousField: Array<FieldValue>
				ruleName: RuleName
				extendedData: ExtendedData
		}
	

		export interface ExtendedData {
				kvScheinSubGroup?: string
				treatmentCase: string
				feeCatalogue: catalog_sdkt_common.FeeCatalogue
				serviceCode: string
		}
	

		export interface OpsCatalog {
				code: string
				description: string
				isLaterality: boolean
		}
	

		export interface EbmSearchItem {
				code: string
				description: string
				evaluation: number
				unit: UnitStatistiks
				isSelfCreated: boolean
				highlight: {[key:string]:common.Highlight}
		}
	


// enum definitions
    export enum UnitStatistiks {
        UnitStatistiks_Unknown = "0",
        UnitStatistiks_Points = "1",
        UnitStatistiks_Euros = "2",
        UnitStatistiks_Unrated = "5",
	}

    export enum RuleName {
        RuleName_046F = "046F",
        RuleName_049F = "049F",
        RuleName_050F = "050F",
        RuleName_056W = "056W",
        RuleName_061F = "061F",
        RuleName_062F = "062F",
        RuleName_106F = "106F",
        RuleName_111F = "111F",
        RuleName_147F = "147F",
        RuleName_162F = "162F",
        RuleName_700W = "700W",
        RuleName_701W = "701W",
        RuleName_702W = "702W",
        RuleName_703W = "703W",
        RuleName_704W = "704W",
        RuleName_705W = "705W",
        RuleName_707W = "707W",
        RuleName_710W = "710W",
        RuleName_725Format = "725FORMAT",
        RuleName_762F = "762F",
        RuleName_770F = "770F",
        RuleName_772F = "772F",
        RuleName_773F = "773F",
        RuleName_816F = "816F",
        RuleName_823Format = "823FORMAT",
        RuleName_828F = "828F",
        RuleName_829F = "829F",
        RuleName_830F = "830F",
        RuleName_834F = "834F",
        RuleName_837F = "837F",
        RuleName_838I = "838I",
        RuleName_843F = "843F",
        RuleName_847I = "847I",
        RuleName_848I = "848I",
        RuleName_854F = "854F",
        RuleName_859F = "859F",
        RuleName_868W = "868W",
        RuleName_888888800 = "888888800",
        RuleName_TimeHHMM = "TIMEHHMM",
        RuleName_TimeDDMMYYYY = "DDMMYYYY",
        RuleName_888W = "888W",
        RuleName_889W = "889W",
        RuleName_890W = "890W",
	}

    export enum DataType {
        DataTypeString = "string",
        DataTypeInt = "int",
        DataTypeFloat = "float",
        DataTypeDate = "DateEpoc",
        DataTypeReferralBlock = "ReferralBlock",
	}

    export enum InputType {
        InputTypeTextInput = "TextInput",
        InputTypeNumberInput = "NumberInput",
        InputTypeDropDownList = "DropDownList",
        InputTypeDropDownListApi = "DropDownListApi",
        InputTypeDate = "DateInput",
        InputTypeDecimal = "DecimalInput",
        InputTypeDropDownCreateList = "DropDownCreateList",
        InputTypeReferralBlock = "ReferralBlock",
	}

    export enum CreateEbmErrorCode {
        TheServiceCodeAlreadyExisted = "TheServiceCodeAlreadyExisted",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

