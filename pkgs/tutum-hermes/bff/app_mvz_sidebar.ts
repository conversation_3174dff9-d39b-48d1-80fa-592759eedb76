/* eslint-disable */
// This code was autogenerated from app/mvz/sidebar.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';



// Type definitions
		export interface GetDefaultInformationResponse {
				hasNotSubmittedEnrollment: boolean
				hasPendingPtvImport: boolean
		}
	

		export interface EventSidebarRepsonse {
				userId: string
				hasNotSubmittedEnrollment: boolean
				hasPendingPtvImport: boolean
		}
	


// enum definitions

// event definition constant ----------------------------------------
       	const EVENT_SidebarRepsonse = "api.app.mvz.AppMvzSidebar.SidebarRepsonse";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenSidebarRepsonse(handler: (data: EventSidebarRepsonse) => void): void {
			const [response, setResponse] = useState<EventSidebarRepsonse>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_SidebarRepsonse, _listener);
				return () => window.removeEventListener(EVENT_SidebarRepsonse, _listener);
			}, []);
        }

