/* eslint-disable */
// This code was autogenerated from service/domains/schein_common.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"
import * as patient_participation from "./service_domains_patient_participation"


// Type definitions
		export interface PatientSnapshot {
				patientId: string
				patientInfo: patient_profile_common.PatientInfo
				updatedAt: number
		}
	

		export interface ReferralDoctor {
				bsnr: string
				lanr: string
		}
	

		export interface MarkAsReferralRequest {
				scheinId: string
				referralDoctor: ReferralDoctor
		}
	

		export interface RemoveReferralRequest {
				scheinId: string
		}
	

		export interface Schein {
				scheinMainGroup: string
				kvScheinSubGroup?: string
				kvTreatmentCase: string
				g4101Quarter?: number
				g4101Year?: number
				tariffType?: string
				bgType?: string
				bgAccidentDate?: number
				bgAccidentTime?: string
				bgWorkingTimeFrom?: string
				bgWorkingTimeTo?: string
				bgEmployerName?: string
				bgEmployerStreet?: string
				bgEmployerHousenumber?: string
				bgEmployerPostcode?: string
				bgEmployerCity?: string
				bgEmployerCountry?: string
				insuranceId: string
				ikNumber?: number
				patientSnapshot?: PatientSnapshot
		}
	

		export interface ScheinDetail {
				g4122?: string
				g4106?: string
				g4102?: number
				ad4206?: number
				ad4125From?: number
				ad4125To?: number
				ad4126?: string
				ad4124?: string
				ad4204?: boolean
				ad4202?: boolean
				re4241?: string
				re4248?: string
				re4249?: string
				re4242?: string
				re4225?: string
				re4233?: Array<DateTimeFromTo>
				re4226?: string
				re4221?: string
				re4219?: string
				re4218?: string
				re4217?: string
				re4209?: string
				re4208?: string
				re4207?: string
				re4205?: string
				re4220?: string
				re4229?: string
				ps4257?: number
				ps4299?: number
				ps4256: Array<string>
				ps4253: Array<string>
				ps4254?: number
				ps4255?: number
				ps4252?: number
				ps4251?: number
				ps4247?: number
				ps4244: Array<string>
				ps4245?: string
				ps4246?: string
				ps4235?: number
				ps4236?: boolean
				ps4234?: boolean
				ps4250?: boolean
				re4243?: string
				g4104?: number
				tsvgContactType?: string
				tsvgInfor?: string
				tsvgTranferCode?: string
				tsvgContactDate?: number
				pausingStartDate?: number
				pausingEndDate?: number
				isInsuranceInformedTherapy?: boolean
				ad4123?: string
				re4214?: number
				psychotherapy: Array<Psychotherapy>
		}
	

		export interface DateTimeFromTo {
				from: number
				to: number
		}
	

		export interface GroupServicesCode {
				serviceCode: string
				amountBilled: number
		}
	

		export interface GroupServicesCodeBefore2017 {
				serviceCode: string
				amountApproval: number
				amountBilled: number
		}
	

		export interface Psychotherapy {
				ps4235?: number
				ps4247?: number
				ps4245?: number
				ps4246?: string
				pausingStartDate?: number
				pausingEndDate?: number
				isInsuranceInformedTherapy?: boolean
				ps4251?: number
				ps4250?: boolean
				ps4299?: number
				ps4254?: number
				ps4257?: number
				ps4255?: number
				ps4252?: number
				groupServicesCode: Array<GroupServicesCode>
				groupCareGiver: Array<GroupServicesCode>
				isReason: boolean
				id?: string
				takeOverId?: string
				groupServicesCodeBefore2017: Array<GroupServicesCodeBefore2017>
		}
	

		export interface UpdateScheinRequest {
				scheinId: string
				patientId: string
				doctorId: string
				scheinMainGroup: MainGroup
				kvTreatmentCase: TreatmentCaseNames
				kvScheinSubGroup?: string
				g4101Year?: number
				g4101Quarter?: number
				tariffType?: string
				bgType?: string
				bgAccidentDate?: number
				bgAccidentTime?: string
				bgWorkingTimeFrom?: string
				bgWorkingTimeTo?: string
				bgEmployerName?: string
				bgEmployerStreet?: string
				bgEmployerHousenumber?: string
				bgEmployerPostcode?: string
				bgEmployerCity?: string
				bgEmployerCountry?: string
				hzvContractId?: string
				scheinDetails?: ScheinDetail
				insuranceId: string
				excludeFromBilling: boolean
				assignedToBsnrId?: string
		}
	

		export interface CheckEmptyScheinRequest {
				scheinId: string
		}
	

		export interface CheckExistKVScheinCurrentQuarterRequest {
				patientId: string
				quarter?: number
				year?: number
		}
	

		export interface CheckExistKVScheinCurrentQuarterResponse {
				isExist: boolean
		}
	

		export interface CheckEmptyScheinResponse {
				isEmpty: boolean
		}
	

		export interface DeleteScheinRequest {
				scheinId: string
				patientId: string
		}
	

		export interface GetScheinDetailRequest {
				patientId: string
		}
	

		export interface GetScheinDetailByIdRequest {
				scheinId: string
		}
	

		export interface GetScheinDetailByIdsRequest {
				scheinIds: Array<string>
		}
	

		export interface GetScheinDetailByIdResponse {
				patientId: string
				doctorId: string
				scheinMainGroup: MainGroup
				kvTreatmentCase: TreatmentCaseNames
				kvScheinSubGroup?: string
				g4101Year?: number
				g4101Quarter?: number
				tariffType?: string
				bgType?: string
				bgAccidentDate?: number
				bgAccidentTime?: string
				bgWorkingTimeFrom?: string
				bgWorkingTimeTo?: string
				bgEmployerName?: string
				bgEmployerStreet?: string
				bgEmployerHousenumber?: string
				bgEmployerPostcode?: string
				bgEmployerCity?: string
				bgEmployerCountry?: string
				hzvContractId?: string
				scheinDetails?: ScheinDetail
				scheinId: string
				insuranceId: string
				excludeFromBilling: boolean
				markedAsBilled: boolean
				chargeSystemId?: string
				svScheinDetail?: SvScheinDetail
				assignedToBsnrId?: string
		}
	

		export interface GetScheinDetailByIdsResponse {
				data: Array<GetScheinDetailByIdResponse>
		}
	

		export interface GetScheinDetailResponse {
				scheinIds: Array<string>
		}
	

		export interface ScheinItem {
				scheinId: string
				scheinMainGroup: MainGroup
				kvTreatmentCase: TreatmentCaseNames
				kvScheinSubGroup?: string
				g4101Year?: number
				g4101Quarter?: number
				hzvContractId?: string
				createdTime: number
				markedAsBilled: boolean
				g4110?: number
				insuranceId: string
				excludeFromBilling: boolean
				updatedBy?: string
				updatedAt?: number
				chargeSystemId?: string
				tsvgContactType?: string
				tsvgInfor?: string
				tsvgTranferCode?: string
				tsvgContactDate?: number
				scheinDetail: ScheinDetail
				isTechnicalSchein?: boolean
				patientSnapshot?: PatientSnapshot
				doctorId: string
				issueDate?: number
				scheinStatus?: ScheinStatus
				invoiceNumber?: string
				referralDoctor?: ReferralDoctor
				isGeneral?: boolean
				accidentDate?: number
				jobOccupation?: string
				arrivalDate?: number
				workingTimeStart: number
				workingTimeEnd: number
				companyAddress: patient_profile_common.CompanyAddress
				bgScheinDetail?: BgScheinDetail
		}
	

		export interface GetScheinsOverviewRequest {
				patientId: string
				quarter?: common.YearQuarter
				mainGroup?: MainGroup
		}
	

		export interface GetScheinsOverviewResponse {
				scheinItems: Array<ScheinItem>
				insuranceInfo: Array<patient_profile_common.InsuranceInfo>
		}
	

		export interface MarkNotBilledRequest {
				scheinId: string
		}
	

		export interface ScheinFields {
				scheinId: string
				hzvEncounterCase?: EncounterCase
				hzvTreatmentCase?: TreatmentCase
		}
	

		export interface PatientDiagnose {
				id: string
				code: string
				diagnoseDescription: string
				chronicDiagnose: boolean
				validUntil?: number
				certainty?: Certainty
				laterality?: Laterality
				patientId: string
				freeText: string
				command: string
				source: Sources
				treatmentDoctorId: string
				createdTime: number
				createdUser: string
				updatedTime: number
				updatedUser: string
				encounterDate: number
		}
	

		export interface ScheinDiagnose {
				id: string
				code: string
				diagnoseDescription: string
				chronicDiagnose: boolean
				validUntil?: number
				certainty?: Certainty
				laterality?: Laterality
				patientId: string
				freeText: string
				command: string
				source: Sources
				treatmentDoctorId: string
				createdTime: number
				createdUser: string
				updatedTime: number
				updatedUser: string
				encounterDate: number
				patientDiagnoseId: string
				scheinId: string
				hzvEncounterCase: EncounterCase
				hzvTreatmentCase: TreatmentCase
		}
	

		export interface CreateDiagnoseRequest {
				code: string
				diagnoseDescription: string
				chronicDiagnose: boolean
				validUntil?: number
				certainty?: Certainty
				laterality?: Laterality
				patientId: string
				freeText: string
				command: string
				source: Sources
				treatmentDoctorId: string
				scheinDiagnoseFields?: Array<ScheinFields>
				id: string
		}
	

		export interface CreateDiagnoseResponse {
				diagnoseId: string
		}
	

		export interface UpdateDiagnoseRequest {
				code: string
				diagnoseDescription: string
				chronicDiagnose: boolean
				validUntil?: number
				certainty?: Certainty
				laterality?: Laterality
				patientId: string
				freeText: string
				command: string
				source: Sources
				diagnoseId: string
				treatmentDoctorId: string
				scheinDiagnoseFields?: Array<ScheinFields>
		}
	

		export interface DeleteDiagnoseRequest {
				diagnoseId: string
		}
	

		export interface GetDiagnoseByIdRequest {
				diagnoseId: string
		}
	

		export interface GetDiagnoseByIdResponse {
				diagnose: PatientDiagnose
		}
	

		export interface GetScheinDiagnoseByDiagnoseIdRequest {
				diagnoseId: string
		}
	

		export interface GetScheinDiagnoseByDiagnoseIdResponse {
				scheinDiagnoses: Array<ScheinDiagnose>
		}
	

		export interface PatientService {
				id: string
				code: string
				serviceDescription: string
				isPreParticipate?: boolean
				hzvReferralDoctorBsnr?: string
				hzvReferralDoctorLanr?: string
				hzvCareDoctorFacilityName?: string
				hzvCareDoctorFacilityOrt?: string
				patientId: string
				freeText: string
				command: string
				source: Sources
				treatmentDoctorId: string
				createdUser: string
				createdTime: number
				updatedUser: string
				updatedTime: number
				scheinServiceFields?: Array<ScheinFields>
		}
	

		export interface CreateServiceRequest {
				code: string
				serviceDescription: string
				isPreParticipate?: boolean
				hzvReferralDoctorBsnr?: string
				hzvReferralDoctorLanr?: string
				hzvCareDoctorFacilityName?: string
				hzvCareDoctorFacilityOrt?: string
				patientId: string
				freeText: string
				command: string
				source: Sources
				treatmentDoctorId: string
				scheinServiceFields?: Array<ScheinFields>
				id: string
		}
	

		export interface CreateServiceResponse {
				serviceId: string
		}
	

		export interface UpdateServiceRequest {
				id: string
				code: string
				serviceDescription: string
				isPreParticipate?: boolean
				hzvReferralDoctorBsnr?: string
				hzvReferralDoctorLanr?: string
				hzvCareDoctorFacilityName?: string
				hzvCareDoctorFacilityOrt?: string
				freeText: string
				command: string
				source: Sources
				treatmentDoctorId: string
				scheinServiceFields?: Array<ScheinFields>
		}
	

		export interface DeleteServiceRequest {
				serviceId: string
		}
	

		export interface GetPatientServiceByIdRequest {
				patientServiceId: string
		}
	

		export interface GetPatientServiceByIdResponse {
				patientService: PatientService
		}
	

		export interface RunValidationForScheinsRequest {
				scheinIds?: Array<string>
				serviceId?: string
				diagnoseId?: string
				type: ValidationType
		}
	

		export interface SaveSettingRequest {
				assignDiagnosisCreatedschein: Assigndiagnosis
				previouslySelected: boolean
				defaultTreatmentcase?: string
				defaultSubgroup?: string
				autoCreatekvscheinHzvfav: boolean
				composerAutoSelectscheinDiagnosis: boolean
				showWhenPatientCardIsRead: string
				includeCurrentPatient: boolean
				showHintSpecialGroup09: boolean
				hideHintForTfSG: boolean
		}
	

		export interface GetSettingResponse {
				assignDiagnosisCreatedschein: Assigndiagnosis
				previouslySelected: boolean
				defaultTreatmentcase?: string
				defaultSubgroup?: string
				autoCreatekvscheinHzvfav: boolean
				composerAutoSelectscheinDiagnosis: boolean
				showWhenPatientCardIsRead: string
				includeCurrentPatient: boolean
				showHintSpecialGroup09: boolean
				hideHintForTfSG: boolean
		}
	

		export interface OrderValue {
				name: string
				value: string
		}
	

		export interface GetOrderListResponse {
				orderValues: Array<OrderValue>
		}
	

		export interface SaveOrderListRequest {
				orderValues: Array<OrderValue>
		}
	

		export interface GetSelectedTreatmentCaseSubgroupRequest {
				patientId: string
		}
	

		export interface GetSelectedTreatmentCaseSubgroupResponse {
				kvTreatmentCase: TreatmentCaseNames
				kvScheinSubGroup?: string
		}
	

		export interface GetFieldsRequest {
				treatmentCase: TreatmentCaseNames
				patientId?: string
		}
	

		export interface FieldValidation {
				name: string
				validationType: FieldValidationType
		}
	

		export interface SubGroupRules {
				code: string
				rules: Array<string>
		}
	

		export interface CaseFields {
				code: string
				fields: Array<FieldValidation>
				subGroups: Array<SubGroupRules>
				specialRules: Array<string>
		}
	

		export interface GetFieldsResponse {
				caseFields: CaseFields
		}
	

		export interface CreateSvScheinRequest {
				patientId: string
				doctorId: string
				patientParticipations: Array<patient_participation.PatientParticipation>
				insuranceInfo: patient_profile_common.InsuranceInfo
				startDate?: number
				endDate?: number
		}
	

		export interface OmimG {
				id: string
				code: string
				genName: string
		}
	

		export interface GetOmimGResponse {
				omimGs: Array<OmimG>
		}
	

		export interface OmimP {
				id: string
				code: string
				typeOfIllness: string
				pmk: number
		}
	

		export interface GetOmimPResponse {
				omimPs: Array<OmimP>
		}
	

		export interface OmimGChain {
				id: string
				name: string
				omimGIds: Array<string>
		}
	

		export interface GetOmimGChainResponse {
				omimGChains: Array<OmimGChain>
		}
	

		export interface InsertOmimGChainRequest {
				name: string
				omimGIds: Array<string>
		}
	

		export interface UpdateOmimGChainRequest {
				id: string
				name: string
				omimGIds: Array<string>
		}
	

		export interface DeleteOmimGChainRequest {
				id: string
		}
	

		export interface InsertOmimPChainRequest {
				name: string
				omimPIds: Array<string>
		}
	

		export interface UpdateOmimPChainRequest {
				id: string
				name: string
				omimPIds: Array<string>
		}
	

		export interface DeleteOmimPChainRequest {
				id: string
		}
	

		export interface GetOmimPChainResponse {
				omimPChains: Array<OmimPChain>
		}
	

		export interface OmimPChain {
				id: string
				name: string
				omimPIds: Array<string>
		}
	

		export interface GroupTherapy {
				serviceCode: Array<string>
				amountApprovalTherapy: number
				amountDocumentedTherapy: number
		}
	

		export interface PyschoTherapy {
				isCheckingSomaticSymptom: boolean
				isApproval: boolean
				deadlineApproval?: number
				lanr?: string
				dateApproval?: number
				serviceCodeApproval: Array<string>
				amountApprovedTherapy?: number
				amountBilledTherapy?: number
				pausingStartDate?: number
				pausingEndDate?: number
				isInsuranceInformedTherapy?: boolean
				isCombinatedTreatment?: boolean
				typeOfCombinatedTreatment?: number
				patient?: GroupTherapy
				otherPerson?: GroupTherapy
		}
	

		export interface CreateSvScheinAutomaticlyRequest {
				referenceScheinIds: Array<string>
				selectedDate: number
		}
	

		export interface CreateSvScheinAutomaticlyResponse {
				scheinIds: Array<string>
		}
	

		export interface CreateSvScheinFromReferenceRequest {
				referenceScheinId: string
				doctorId: string
				chargeSystemId: string
				selectedDate: number
				startDate?: number
				endDate?: number
		}
	

		export interface CreateSvScheinFromReferenceResponse {
				scheinId: string
		}
	

		export interface CreateSvScheinManuallyRequest {
				patientId: string
				doctorId: string
				insuranceId: string
				selectedDate: number
				contractId: string
				chargeSystemId: string
				startDate?: number
				endDate?: number
				scheinMainGroup: MainGroup
				assignedToBsnrId?: string
		}
	

		export interface CreateSvScheinManuallyResponse {
				scheinId: string
		}
	

		export interface UpdateSvScheinRequest {
				scheinId: string
				doctorId: string
				chargeSystemId: string
				selectedDate: number
				startDate?: number
				endDate?: number
				assignedToBsnrId?: string
		}
	

		export interface UpdateSvScheinResponse {
				scheinId: string
		}
	

		export interface BgScheinItem {
				scheinId: string
				insuranceId?: string
				doctorId: string
				patientId: string
				createdOn: number
				endDate?: number
				personalAccident: boolean
				accidentDate: number
				arrivalDate?: number
				fileNumber?: number
				excludeFromBilling: boolean
				markedAsBilled: boolean
				scheinMainGroup: MainGroup
				workingTimeStart: number
				workingTimeEnd: number
				scheinStatus?: ScheinStatus
				bGType: BGType
				employmentInfo: patient_profile_common.EmploymentInfo
				assignedToBsnrId?: string
				employmentInfoUpdatedAt?: number
				fileNumberStr?: string
				ikNumber?: number
		}
	

		export interface BgScheinDetail {
				createdOn: number
				endDate?: number
				personalAccident: boolean
				accidentDate: number
				arrivalDate?: number
				fileNumber?: number
				workingTimeStart: number
				workingTimeEnd: number
				bGType: BGType
				invoiceNumber?: string
				employmentInfo: patient_profile_common.EmploymentInfo
				employmentInfoUpdatedAt?: number
				fileNumberStr?: string
		}
	

		export interface SvScheinDetail {
				startDate?: number
				endDate?: number
				onlineParticipatedCheckDate?: number
		}
	


// enum definitions
    export enum MainGroup {
        HZV = "HZV",
        FAV = "FAV",
        KV = "KV",
        BG = "BG",
        PRIVATE = "PRIVATE",
        IGEL = "IGEL",
	}

    export enum EncounterCase {
        AB = "AB",
        PB = "PB",
        NOT = "NOT",
        PRE_ENROLLMENT = "PRE_ENROLLMENT",
	}

    export enum TreatmentCase {
        TreatmentCaseCustodian = "TreatmentCaseCustodian",
        TreatmentCaseDelegate = "TreatmentCaseDelegate",
        TreatmentCaseDeputy = "TreatmentCaseDeputy",
        TreatmentCasePreParticipate = "TreatmentCasePreParticipate",
	}

    export enum Laterality {
        U = "U",
        L = "L",
        R = "R",
        B = "B",
	}

    export enum Certainty {
        G = "G",
        V = "V",
        Z = "Z",
        A = "A",
	}

    export enum Sources {
        Imported = "Imported",
        Composer = "Composer",
        Timeline = "Timeline",
	}

    export enum TreatmentCaseNames {
        TCKvOutpatient = "0101",
        TCKvReferral = "0102",
        TCKvHospital = "0103",
        TCKvEmergency = "0104",
        TCKvSpaMedical = "0109",
        TCKvSadtOutpatient = "sadt1",
        TCKvSadtReferral = "sadt2",
        TCKvSadtHospital = "sadt3",
        TCBgCase = "BG",
        TCPrivateCase = "PRIVATE",
	}

    export enum ValidationType {
        ScheinValidation = "ScheinValidation",
        ServiceValidation = "ServiceValidation",
        DiagnoseValidation = "DiagnoseValidation",
	}

    export enum CreateScheinErrorCode {
        CostUnitIsNotAvailableInKvRegion = "CostUnitIsNotAvailableInKvRegion",
        CostUnitHasExpired = "CostUnitHasExpired",
        CostUnitIsTerminated = "CostUnitIsTerminated",
        TheBillingAreaIsOutOfValidityDateRange = "TheBillingAreaIsOutOfValidityDateRange",
        WarningKvx3SKTAddtional = "WarningKvx3SKTAddtional",
        TheBillingAreaIsInvalid = "TheBillingAreaIsInvalid",
	}

    export enum Assigndiagnosis {
        AssigndiagnosisAll = "all",
        AssigndiagnosisPermanent = "permanent",
        AssigndiagnosisManually = "manually",
	}

    export enum FieldValidationType {
        FieldValidationType_Required = "required",
        FieldValidationType_Optional = "optional",
	}

    export enum ScheinStatus {
        ScheinStatus_Normal = "ScheinStatus_Normal",
        ScheinStatus_Printed = "ScheinStatus_Printed",
        ScheinStatus_Billed = "ScheinStatus_Billed",
        ScheinStatus_Canceled = "ScheinStatus_Canceled",
	}

    export enum BGType {
        GereralAction = "GereralAction",
        SpecialAction = "SpecialAction",
	}


// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

