import { useEffect } from 'react';
import { proxy, useSnapshot } from 'valtio';
import {
  GetPrescriptionRequest,
  GetPrescriptionResponse,
  Selector,
  Diagnose,
  GetDiagnoseFilterRequest,
  ProductDetail,
  getPrescription,
  getHeimiArea,
  getDiagnoseFilter,
  getDiagnoseGroupFilter,
  getRemedyFilter,
  exportPrescription,
} from '@tutum/hermes/bff/legacy/app_mvz_heimi';

interface IHeimiStatisticsStore {
  heimiStatistics: GetPrescriptionResponse | undefined;
  isLoadingHeimiStatistics: boolean;
  heimiAreas: Selector[];
  diagnosis: Diagnose[];
  diagnoseGroupFilter: Diagnose[];
  remedies: ProductDetail[];
  complementaryRemedies: ProductDetail[];
  isLoadingExport: boolean;
}

const initStore: IHeimiStatisticsStore = {
  heimiStatistics: undefined,
  isLoadingHeimiStatistics: false,
  heimiAreas: [],
  diagnosis: [],
  diagnoseGroupFilter: [],
  remedies: [],
  complementaryRemedies: [],
  isLoadingExport: false,
};

export let heimiStatisticsStore = proxy<IHeimiStatisticsStore>(initStore);

export const heimiStatisticsActions = {
  loadHeimiStatistics: (payload: GetPrescriptionRequest) => {
    heimiStatisticsStore.isLoadingHeimiStatistics = true;
    getPrescription(payload)
      .then((res) => {
        heimiStatisticsStore.heimiStatistics = res.data || undefined;
      })
      .catch((error) => {
        throw error;
      })
      .finally(() => {
        heimiStatisticsStore.isLoadingHeimiStatistics = false;
      });
  },
  loadHeimiAreas: () => {
    getHeimiArea()
      .then((res) => {
        heimiStatisticsStore.heimiAreas = res.data?.selectors || [];
      })
      .catch((error) => {
        throw error;
      });
  },
  loadDiagnosisFilter: (payload: GetDiagnoseFilterRequest) => {
    getDiagnoseFilter(payload)
      .then((res) => {
        heimiStatisticsStore.diagnosis = res.data?.diagnoses || [];
      })
      .catch((error) => {
        throw error;
      });
  },
  loadDiagnoseGroupFilter: () => {
    getDiagnoseGroupFilter()
      .then((res) => {
        heimiStatisticsStore.diagnoseGroupFilter = res.data?.diagnoses || [];
      })
      .catch((error) => {
        throw error;
      });
  },
  loadRemediesFilter: (doctorId: string) => {
    getRemedyFilter({ doctorId })
      .then((res) => {
        heimiStatisticsStore.remedies = res.data?.remedies || [];
        heimiStatisticsStore.complementaryRemedies =
          res.data?.complementaryRemedies || [];
      })
      .catch((error) => {
        throw error;
      });
  },
  exportPrescription: (payload: GetPrescriptionRequest) => {
    heimiStatisticsStore.isLoadingExport = true;
    exportPrescription(payload, { responseType: 'blob' })
      .then((response) => {
        const url = global['URL'].createObjectURL(
          new Blob([response.data as any], { type: 'text/csv;charset=utf-8;' })
        );
        const contentDisposition = response.headers.get('content-disposition');
        const fileName = contentDisposition?.split('=')?.[1] || 'unknow.csv';
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        link.remove();
      })
      .catch((error) => {
        throw error;
      })
      .finally(() => {
        heimiStatisticsStore.isLoadingExport = false;
      });
  },
};

export function useHeimiStatisticsStore(payload: GetPrescriptionRequest) {
  useEffect(() => {
    heimiStatisticsActions.loadHeimiStatistics(payload);
    heimiStatisticsActions.loadHeimiAreas();
    heimiStatisticsActions.loadDiagnosisFilter({ doctorId: payload.doctorId });
    heimiStatisticsActions.loadDiagnoseGroupFilter();
    heimiStatisticsActions.loadRemediesFilter(payload.doctorId);

    return () => {
      heimiStatisticsStore = proxy<IHeimiStatisticsStore>(initStore);
    };
  }, []);
  return useSnapshot(heimiStatisticsStore);
}
