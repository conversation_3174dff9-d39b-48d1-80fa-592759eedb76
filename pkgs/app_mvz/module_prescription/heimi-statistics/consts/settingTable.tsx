import React from 'react';
import { NextRouter } from 'next/router';

import {
  PrescriptionStatistics,
  ProductDetail,
} from '@tutum/hermes/bff/app_mvz_heimi';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import {
  Flex,
  BodyTextS,
  BodyTextM,
  Svg,
} from '@tutum/design-system/components';
import { Tooltip } from '@tutum/design-system/components/Core';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import PatientManagementUtil from '@tutum/infrastructure/utils/form.util';
import TableAction from '@tutum/design-system/components/Table/TableAction/TableAction.styled';
import { COLOR } from '@tutum/design-system/themes/styles';

const handleConnectString = (firstString: string, lastString: string) => {
  return `${firstString || ''} ${firstString && lastString ? '-' : ''} ${lastString || ''
    }`;
};

const handleMapStandardCase = (complementaryRemedies: ProductDetail[]) => {
  const listString = complementaryRemedies
    ?.map((compRemedy) => compRemedy.name)
    .join(';');
  return listString
    ? `Standardisierte Heilmittelkombination (${listString})`
    : 'Standardisierte Heilmittelkombination';
};
const formatter = new Intl.NumberFormat('de-DE');
export const genColumns = (
  t: IFixedNamespaceTFunction<any>,
  router: NextRouter
) => ({
  prescribeDate: {
    id: 'prescribeDate',
    name: 'Datum',
    selector: (row) => row.prescribeDate,
    width: '121px',
    format: (row: PrescriptionStatistics) => {
      return row.prescribeDate
        ? toDateFormat(new Date(row?.prescribeDate), {
          dateFormat: 'dd.MM.yyyy',
        })
        : '';
    },
  },
  patient: (openHeimiPrescription: (row: PrescriptionStatistics) => void) => ({
    id: 'patient',
    name: 'Patient',
    selector: (row) => row.patient,
    width: '246px',
    sortable: true,
    cell: (row: PrescriptionStatistics) => (
      <Flex column>
        <BodyTextM
          fontSize={13}
          fontWeight={600}
          color={COLOR.TEXT_INFO}
          style={{ cursor: 'pointer' }}
          onClick={() => openHeimiPrescription(row)}
        >
          {PatientManagementUtil.getFullName(
            row?.patientProfile?.patientInfo?.personalInfo?.lastName,
            row?.patientProfile?.patientInfo?.personalInfo?.title,
            row?.patientProfile?.patientInfo?.personalInfo?.intendWord!,
            row?.patientProfile?.patientInfo?.personalInfo?.firstName
          )}
        </BodyTextM>
        <BodyTextS fontSize={11} color={COLOR.TEXT_SECONDARY_NAVAL}>
          {
            getDateOfBirth(
              row?.patientProfile?.patientInfo?.personalInfo?.dateOfBirth
            ).value
          }
        </BodyTextS>
      </Flex>
    ),
  }),
  insuranceNo: {
    id: 'insuranceNo',
    name: 'Insurance no.',
    selector: (row) => row.insuranceNo,
    width: '152px',
  },
  heimiArea: {
    id: 'heimiArea',
    name: 'Heilmittelbereich',
    selector: (row) => row.heimiArea,
    width: '224px',
    cell: (row: PrescriptionStatistics) => {
      return (
        <Flex column w="100%">
          <Tooltip
            // targetClassName="sl-target-content"
            content={<span>{row?.area?.code}</span>}
          >
            <BodyTextM fontSize={13} className="sl-text-ellipsis">
              {row?.area?.code || ''}
            </BodyTextM>
          </Tooltip>
        </Flex>
      );
    },
  },
  remedies: {
    id: 'remedies',
    name: 'Remedy',
    selector: (row) => row.remedies,
    width: '400px',
    cell: (row: PrescriptionStatistics) => (
      <Flex column w="100%">
        {row?.isStandardCombination ? (
          <Tooltip
            // targetClassName="sl-target-content"
            content={
              <span>{handleMapStandardCase(row?.complementaryRemedies)}</span>
            }
          >
            <BodyTextM fontSize={13} className="sl-text-ellipsis">
              {handleMapStandardCase(row?.complementaryRemedies)}
            </BodyTextM>
          </Tooltip>
        ) : (
          <>
            {row?.remedies?.map((remedy, index) => (
              <Tooltip
                key={remedy.name}
                // targetClassName="sl-target-content"
                content={
                  <span>
                    {handleConnectString(
                      `${t('MainRemedy')} ${index + 1}`,
                      remedy.name
                    )}
                  </span>
                }
              >
                <BodyTextM fontSize={13} className="sl-text-ellipsis">
                  {handleConnectString(
                    `${t('MainRemedy')} ${index + 1}`,
                    remedy.name
                  )}
                </BodyTextM>
              </Tooltip>
            ))}
            {row?.complementaryRemedies?.map((compRemedy, index) => (
              <Tooltip
                key={`${index}`}
                // targetClassName="sl-target-content"
                content={
                  <span>
                    {handleConnectString(
                      t('ComplementaryRemedy'),
                      compRemedy.name
                    )}
                  </span>
                }
              >
                <BodyTextS
                  key={`${index}`}
                  fontSize={11}
                  color={COLOR.TEXT_SECONDARY_NAVAL}
                  className="sl-text-ellipsis"
                >
                  {handleConnectString(
                    t('ComplementaryRemedy'),
                    compRemedy.name
                  )}
                </BodyTextS>
              </Tooltip>
            ))}
          </>
        )}
      </Flex>
    ),
  },
  diagnose: {
    id: 'diagnose',
    name: 'VERORDnung/Diagnose',
    selector: (row) => row.diagnose,
    width: '362px',
    cell: (row: PrescriptionStatistics) => (
      <Flex column w="100%" style={{ padding: '5px' }}>
        <Tooltip
          // targetClassName="sl-target-content"
          content={
            <span>
              {handleConnectString(row?.diagnose?.code, row?.diagnose?.name)}
            </span>
          }
        >
          <BodyTextM fontSize={13} className="sl-text-ellipsis">
            {handleConnectString(row?.diagnose?.code, row?.diagnose?.name)}
          </BodyTextM>
        </Tooltip>

        {row?.secondaryDiagnose?.code && (
          <Tooltip
            // targetClassName="sl-target-content"
            content={
              <span>
                {handleConnectString(
                  row?.secondaryDiagnose?.code,
                  row?.secondaryDiagnose?.name
                )}
              </span>
            }
          >
            <BodyTextM fontSize={13} className="sl-text-ellipsis">
              {handleConnectString(
                row?.secondaryDiagnose?.code,
                row?.secondaryDiagnose?.name
              )}
            </BodyTextM>
          </Tooltip>
        )}

        <Tooltip
          // targetClassName="sl-target-content"
          content={
            <span>
              {handleConnectString(
                row?.diagnoseGroup?.code,
                row?.diagnoseGroup?.name
              )}
            </span>
          }
        >
          <BodyTextS
            fontSize={11}
            color={COLOR.TEXT_SECONDARY_NAVAL}
            className="sl-text-ellipsis"
          >
            {handleConnectString(
              row?.diagnoseGroup?.code,
              row?.diagnoseGroup?.name
            )}
          </BodyTextS>
        </Tooltip>
      </Flex>
    ),
  },
  indicator: {
    id: 'indicator',
    name: 'BVB/LHM',
    selector: (row) => row.indicator,
    width: '102px',
    center: true,
    cell: (row: PrescriptionStatistics) => {
      return row?.indicator?.label ? (
        <span className="sl-indicator-cell">{row?.indicator?.label}</span>
      ) : null;
    },
  },
  quantity: {
    id: 'quantity',
    name: 'MENGE',
    selector: (row) => row.quantity,
    width: '120px',
    right: true,
    format: (row: PrescriptionStatistics) => {
      if (row?.isStandardCombination) {
        return row.totalPriorityRemedy;
      }
      if (row?.totalComplementaryRemedy && !row.totalPriorityRemedy) {
        return row?.totalComplementaryRemedy;
      }
      return row.totalPriorityRemedy;
    },
  },
  blankForm: {
    id: 'blankForm',
    name: 'Blanco',
    selector: (row) => row.blankForm,
    width: '102px',
    center: true,
    format: (row: PrescriptionStatistics) => {
      return row?.remedies[0]?.isBlankForm ? (
        <span>
          <Svg src={'/images/check-circle.svg'} />
        </span>
      ) : (
        ''
      );
    },
  },
  totalPrice: {
    id: 'totalPrice',
    name: 'Total Price',
    selector: (row) => row.totalPrice,
    width: '150px',
    right: true,
    format: (row: PrescriptionStatistics) => {
      return row?.totalPrice ? `${formatter.format(row.totalPrice)}€` : '';
    },
  },
  actions: {
    id: 'action',
    name: 'Action',
    width: '40px',
    cell: (row) => {
      return (
        <TableAction
          actions={[
            {
              id: 'edit',
              label: t('gotoPatientProfile'),
              icon: <Svg src={'/images/user-gray.svg'} />,
              onClick: () => {
                router.push('/patients/' + row.patientProfile.id);
              },
            },
          ]}
        />
      );
    },
  },
});
