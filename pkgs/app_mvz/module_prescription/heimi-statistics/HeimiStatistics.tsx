import React, { useState } from 'react';
import { IMvzTheme } from '@tutum/mvz/theme';
import { omitBy, isNil } from 'lodash';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PrescriptionI18n from '@tutum/mvz/locales/en/Prescription.json';
import { GetPrescriptionRequest, Sort } from '@tutum/hermes/bff/app_mvz_heimi';
import {
  useHeimiStatisticsStore,
  heimiStatisticsActions,
} from '@tutum/mvz/module_prescription/heimi-statistics/HeimiStatistics.store';
import { CONSTS, MAP_RADIO_VALUES } from './consts';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import HeimiStatisticsTable from './HeimiStatisticsTable/HeimiStatisticsTable.styled';
import FilterForm from './FilterForm/FilterForm.styled';
import { Button, Intent } from '@tutum/design-system/components/Core';
import {
  Flex,
  BodyTextM,
  IMenuItem,
  Svg,
} from '@tutum/design-system/components';
import Spinner from '@tutum/mvz/components/spinner/Spinner.styled';
import { DateOfBirth } from '@tutum/hermes/bff/patient_profile_common';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IHeimiStatisticsProps {
  className?: string;
  theme?: IMvzTheme;
}

export interface IFilterValues {
  fromDate: Date;
  toDate: Date;
  patientName: string;
  patientDOB: DateOfBirth | undefined;
  insuranceNumber: string;
  area: IMenuItem | undefined;
  diagnosis: IMenuItem[];
  group: IMenuItem | undefined;
  remedy: IMenuItem[];
  complementaryRemedy: IMenuItem[];
  standardizedCombination: string | null;
  homeVisit: string | null;
  therapyReport: string | null;
  isBVB: string | null;
  isLHM: string | undefined;
  isBlankForm: string | undefined;
}

const FilterIcon = '/images/filter.svg';
const FilterActiveIcon = '/images/filter-active.svg';
const DownloadIcon = '/images/download.svg';
const InfoIcon = '/images/information-circle.svg';

export const initialValues: IFilterValues = {
  fromDate: DatetimeUtil.getStartOfQuarter(DatetimeUtil.now()).toDate(),
  toDate: DatetimeUtil.getEndOfQuarter(DatetimeUtil.now()).toDate(),
  patientName: '',
  patientDOB: undefined,
  insuranceNumber: '',
  area: undefined,
  diagnosis: [],
  group: undefined,
  remedy: [],
  complementaryRemedy: [],
  standardizedCombination: null,
  homeVisit: null,
  therapyReport: null,
  isBVB: null,
  isLHM: undefined,
  isBlankForm: undefined,
};

const HeimiStatistics = React.memo(
  ({
    t,
    className,
  }: IHeimiStatisticsProps &
    II18nFixedNamespace<keyof typeof PrescriptionI18n.HeimiStatistics>) => {
    const [isOpenDialog, setIsOpenDialog] = useState(false);
    const [filterValues, setFilterValues] =
      useState<IFilterValues>(initialValues);
    const [page, setPage] = useState(CONSTS.DEFAULT_CURRENT_PAGE);
    const [rowsPerPage, setRowsPerPage] = useState(CONSTS.DEFAULT_PAGE_SIZE);
    const [sortDirection, setSortDirection] = useState<Sort>(Sort.ASC);

    const { globalData } = GlobalContext.useContext();

    const store = useHeimiStatisticsStore(
      mapValues(filterValues, page, rowsPerPage, sortDirection)
    );

    function mapValues(
      values: IFilterValues,
      page?: number,
      totalInPage?: number,
      sortDirection?: Sort
    ): GetPrescriptionRequest {
      const newMap: GetPrescriptionRequest = {
        ...values,
        doctorId: globalData?.userProfile?.id!,
        page: page!,
        totalInPage: totalInPage!,
        sortByPatientName: sortDirection,
        fromDate: values?.fromDate ? values.fromDate.getTime() : undefined,
        toDate: values?.toDate ? values.toDate.getTime() : undefined,
        patientName: values?.patientName || undefined,
        patientDOB: {
          date: values?.patientDOB?.date && +values?.patientDOB?.date || undefined,
          month: values?.patientDOB?.month && +values?.patientDOB?.month || undefined,
          year: values?.patientDOB?.year && +values?.patientDOB?.year || undefined,
          isValidDOB: false,
        },
        area: values?.area ? (values.area.value as string) : undefined,
        insuranceNumber: values?.insuranceNumber || undefined,
        diagnosis: values?.diagnosis?.length
          ? values.diagnosis.map((item) => item.value as string)
          : undefined,
        group: values?.group ? (values.group.value as string) : undefined,
        remedy: values?.standardizedCombination
          ? undefined
          : values?.remedy?.length
            ? values.remedy.map((item) => store?.remedies?.[item.value])
            : undefined,
        complementaryRemedy: values?.complementaryRemedy?.length
          ? values.complementaryRemedy.map(
            (item) => store?.complementaryRemedies?.[item.value]
          )
          : undefined,
        standardizedCombination: values?.standardizedCombination
          ? MAP_RADIO_VALUES[values?.standardizedCombination]
          : null,
        homeVisit: values?.homeVisit
          ? MAP_RADIO_VALUES[values?.homeVisit]
          : null,
        therapyReport: values?.therapyReport
          ? MAP_RADIO_VALUES[values?.therapyReport]
          : null,
        isBVB: values?.isBVB ? MAP_RADIO_VALUES[values?.isBVB] : null,
        isLHM: values?.isLHM ? MAP_RADIO_VALUES[values?.isLHM] : null,
        isBlankForm: values?.isBlankForm
          ? MAP_RADIO_VALUES[values?.isBlankForm]
          : null,
      };
      return omitBy(newMap, isNil) as GetPrescriptionRequest;
    }

    const handleLoadHeimiStatistics = (
      page: number,
      totalInPage: number,
      sortDirection: Sort
    ) => {
      const payload: GetPrescriptionRequest = mapValues(
        filterValues,
        page,
        totalInPage,
        sortDirection
      );
      heimiStatisticsActions.loadHeimiStatistics(payload);
    };

    const openFilterForm = () => {
      setIsOpenDialog(true);
    };

    const closeFilterForm = () => {
      setIsOpenDialog(false);
    };

    const handleSubmitFilter = (values) => {
      heimiStatisticsActions.loadHeimiStatistics(
        mapValues(
          values,
          CONSTS.DEFAULT_CURRENT_PAGE,
          rowsPerPage,
          sortDirection
        )
      );
      setFilterValues(values);
    };

    const handleExport = () => {
      heimiStatisticsActions.exportPrescription(
        mapValues(filterValues, CONSTS.DEFAULT_CURRENT_PAGE, 100, sortDirection)
      );
    };

    const handleMapMenuItems = (
      list: any[],
      keyValue: string,
      keyText: string,
      identifyValue?: boolean
    ): IMenuItem[] => {
      return list.map((item) => ({
        value: item[keyValue],
        label: identifyValue
          ? `${item[keyValue]} - ${item[keyText]}`
          : item[keyText],
      }));
    };

    const countFilterValues = Object.keys(mapValues(filterValues)).length - 1;

    return (
      <div className={className}>
        <Flex justify="flex-end" className="sl-block-actions">
          {store?.isLoadingExport && <Spinner />}
          <Button
            className="sl-btn"
            intent={Intent.NONE}
            onClick={openFilterForm}
          >
            <Svg src={countFilterValues ? FilterActiveIcon : FilterIcon} />
            <BodyTextM
              fontWeight={600}
              color={countFilterValues ? COLOR.TEXT_INFO : 'initial'}
            >
              {t('filterBtn')}{' '}
              {countFilterValues ? `(${countFilterValues})` : null}
            </BodyTextM>
          </Button>
          <Button
            className="sl-btn"
            intent={Intent.NONE}
            onClick={handleExport}
            disabled={
              store?.isLoadingExport ||
              !store?.heimiStatistics?.prescriptions?.length
            }
          >
            <Svg src={DownloadIcon} />
            <BodyTextM fontWeight={600}>{t('exportBtn')}</BodyTextM>
          </Button>
        </Flex>
        <Flex className="sl-information">
          <Svg src={InfoIcon} />
          <BodyTextM fontWeight={600} color={COLOR.TEXT_INFO}>
            {CONSTS.INFO_TEXT}
          </BodyTextM>
        </Flex>
        <HeimiStatisticsTable
          heimiStatistics={store?.heimiStatistics!}
          isLoadingHeimiStatistics={store?.isLoadingHeimiStatistics}
          page={page}
          rowsPerPage={rowsPerPage}
          sortDirection={sortDirection}
          setPage={setPage}
          setRowsPerPage={setRowsPerPage}
          setSortDirection={setSortDirection}
          handleLoadHeimiStatistics={handleLoadHeimiStatistics}
        />
        <FilterForm
          filterValues={filterValues}
          isOpen={isOpenDialog}
          closeFilterForm={closeFilterForm}
          handleSubmitFilter={handleSubmitFilter}
          heimiAreas={handleMapMenuItems(store.heimiAreas, 'value', 'name')}
          diagnosis={handleMapMenuItems(store?.diagnosis, 'code', 'name', true)}
          diagnoseGroup={handleMapMenuItems(
            store.diagnoseGroupFilter,
            'code',
            'name',
            true
          )}
          remedies={store?.remedies.map((remedy, index) => ({
            value: index,
            label: remedy.name,
          }))}
          complementaryRemedies={store?.complementaryRemedies.map(
            (remedy, index) => ({
              value: index,
              label: remedy.name,
            })
          )}
        />
      </div>
    );
  }
);

export default I18n.withTranslation(HeimiStatistics, {
  namespace: 'Prescription',
  nestedTrans: 'HeimiStatistics',
});
