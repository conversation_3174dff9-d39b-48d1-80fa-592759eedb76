import { useEffect, useState } from 'react';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  alertError,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';

function useFetch<Response = any, Param = any>(
  promiser: (parameter: Param) => Promise<Response>,
  parameter?: Param,
  initialData?: Nullable<Response>
): {
  data: Nullable<Response>;
  loading: boolean;
  error: Error | null;
  refetch: (_parameter: Param) => Promise<Response | undefined>;
} {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Nullable<Response>>(initialData);
  const [error, setError] = useState<Error | null>(null);
  const toaster = useToaster();

  useEffect(() => {
    if (!error) return;

    alertError(`${error?.message}`, {
      timeout: TOASTER_TIMEOUT_CUSTOM,
      toaster,
    });
  }, [error]);

  async function fetchSomeData(_parameter: Param) {
    setLoading(true);
    try {
      const res = await promiser(_parameter);
      setError(null!);
      setData(res);
      return res;
    } catch (e) {
      setError(e);
    } finally {
      setLoading(false);
    }
  }

  return {
    data,
    loading,
    error,
    refetch: fetchSomeData,
  };
}

export default useFetch;
