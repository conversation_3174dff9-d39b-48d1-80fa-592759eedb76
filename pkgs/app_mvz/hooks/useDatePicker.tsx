import { Flex } from '@tutum/design-system/components';
import { getUTCMilliseconds } from '@tutum/design-system/infrastructure/utils';
import DatePickerForm from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/date-picker-form/DatePickerForm.styled';
import { useState } from 'react';

interface IProps {
  dateShortcut?: any;
  title?: any;
  cancelLabel?: any;
  submitLabel?: any;
  defaultValue?: any;
  minDate?: any;
  maxDate?: any;
  className?: string;
  showFavGroupCheckBox?: boolean;
  isSubmitting?: any;
  intentSubmit?: any;
}
interface IAskDateResult {
  startDate: number;
  endDate: number;
}
const useDatePicker = ({
  dateShortcut,
  title,
  cancelLabel,
  submitLabel,
  minDate,
  maxDate,
  className,
  defaultValue,
  showFavGroupCheckBox,
  isSubmitting,
  intentSubmit,
}: IProps) => {
  const [promise, setPromise] = useState<any>(null);
  const [props, setProps] = useState<IProps>({
    dateShortcut,
    title,
    cancelLabel,
    submitLabel,
    minDate,
    maxDate,
    className,
    defaultValue,
    showFavGroupCheckBox,
    isSubmitting,
    intentSubmit,
  });

  const askDate = ({ ...newProps }: IProps): Promise<IAskDateResult> => {
    setProps({ ...props, ...newProps });
    return new Promise((resolve) => {
      setPromise({ resolve });
      setTimeout(() => {
        resolve(null!);
      }, 30000);
    });
  };

  const handleClose = () => {
    setPromise(null);
  };
  const handleConfirm = (date) => {
    promise?.resolve({
      startDate: getUTCMilliseconds(date),
      endDate: getUTCMilliseconds(date),
    });
    handleClose();
  };

  const handleCancel = () => {
    promise?.resolve(null);
    handleClose();
  };

  const DatePickerPopover = () => (
    <>
      <Flex column auto className={'popover-form-container'}>
        <DatePickerForm
          dateShortcut={dateShortcut}
          title={title}
          cancelLabel={cancelLabel}
          submitLabel={submitLabel}
          defaultValue={defaultValue}
          minDate={minDate}
          maxDate={maxDate}
          className={'date-picker-form'}
          showFavGroupCheckBox={showFavGroupCheckBox} // this action need to set all fav contracts to active, there is no need to show the check box
          onCancel={handleCancel}
          onSubmit={handleConfirm}
          isSubmitting={isSubmitting}
          intentSubmit={intentSubmit}
        />
      </Flex>
    </>
  );

  return { DatePickerPopover, askDate };
};

export default useDatePicker;
