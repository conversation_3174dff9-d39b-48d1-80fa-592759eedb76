import { useState, useEffect } from 'react';
import { isEqual } from 'lodash';
import { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import { getCopaymentExemptionTillDateUnder18y } from '../module_patient-management/create-patient-v2/CreatePatient.helper';
import {
  formatValueDateOfBirth,
  getAge,
} from '@tutum/design-system/infrastructure/utils';

export interface ICopaymentRequest {
  patientInfo: PatientInfo;
  prevPatientInfo?: PatientInfo;
  isCreatePatient?: boolean;
}

export interface ICopaymentData {
  isUnder18: boolean;
  copaymentExemptionTillDate: number | undefined;
}

const useCopaymentData = (props: ICopaymentRequest) => {
  const { patientInfo, prevPatientInfo, isCreatePatient } = props;
  const [copaymentData, setCopaymentData] = useState<ICopaymentData | undefined>({
    isUnder18: false,
    copaymentExemptionTillDate: undefined,
  });

  const calculateCopaymentData = (dateOfBirth) => {
    const patientAge = getAge(formatValueDateOfBirth(dateOfBirth));
    const isUnder18 = patientAge !== -1 && patientAge < 18;
    const copaymentExemptionTillDate = isUnder18
      ? getCopaymentExemptionTillDateUnder18y(dateOfBirth)
      : undefined;

    return { isUnder18, copaymentExemptionTillDate };
  };

  useEffect(() => {
    const { dateOfBirth } = patientInfo.personalInfo;
    if (isCreatePatient) {
      if (dateOfBirth.date && dateOfBirth.month && dateOfBirth.year) {
        setCopaymentData(calculateCopaymentData(dateOfBirth));
        return;
      }
    }
    if (prevPatientInfo) {
      const { dateOfBirth: prevDateOfBirth } = prevPatientInfo.personalInfo;
      if (
        !isEqual(dateOfBirth, prevDateOfBirth) &&
        dateOfBirth.date &&
        dateOfBirth.month &&
        dateOfBirth.year
      ) {
        setCopaymentData(calculateCopaymentData(dateOfBirth));
        return;
      }
    }
    setCopaymentData(undefined);
  }, [
    JSON.stringify(patientInfo.personalInfo.dateOfBirth),
    JSON.stringify(prevPatientInfo),
  ]);

  return copaymentData;
};

export { useCopaymentData };
