import { FOCUSABLE_ATTRIBUTE } from '@tutum/mvz/constant/custom-attribute';

const TABBABLE_NODES = /input|select|textarea|button|object/;
export const FOCUS_SELECTOR = `a, input, select, textarea, button, object, [tabindex], [${FOCUSABLE_ATTRIBUTE}]`;

function hidden(element: HTMLElement) {
  return element.style.display === 'none';
}

function visible(element: HTMLElement) {
  let parentElement: HTMLElement = element;
  while (parentElement) {
    if (parentElement === document.body) {
      break;
    }

    if (hidden(parentElement)) {
      return false;
    }

    parentElement = parentElement.parentNode as HTMLElement;
  }

  return true;
}

function getElementTabIndex(element: HTMLElement) {
  let tabIndex = element.getAttribute('tabindex')!;

  if (tabIndex === null) {
    tabIndex = undefined!;
  }

  return parseInt(tabIndex, 10);
}

export function focusable(element) {
  const nodeName = element.nodeName.toLowerCase();

  const isTabIndexNotNaN = !Number.isNaN(getElementTabIndex(element));

  const isNotDisabledAndTabbable =
    TABBABLE_NODES.test(nodeName) && !element.disabled;

  const isCustomFocusable = element.getAttribute(FOCUSABLE_ATTRIBUTE);

  const isAnchorEl = element instanceof HTMLAnchorElement;

  const hasTabindex = isAnchorEl
    ? element.href || isTabIndexNotNaN
    : isTabIndexNotNaN;

  const isFocusable =
    isNotDisabledAndTabbable || hasTabindex || isCustomFocusable === 'true';

  return isFocusable && visible(element);
}

export function tabbable(element: HTMLElement) {
  const tabIndex = getElementTabIndex(element);
  const isTabIndexNaN = Number.isNaN(tabIndex);
  return (isTabIndexNaN || tabIndex >= 0) && focusable(element);
}

export function findTabbableDescendants(element: HTMLElement): HTMLElement[] {
  if (!element) return [];
  return Array.from(
    element.querySelectorAll<HTMLElement>(FOCUS_SELECTOR)
  ).filter(tabbable);
}

export function findFocusableDescendants(element: HTMLElement): HTMLElement[] {
  return Array.from(element.querySelectorAll<HTMLElement>(FOCUS_SELECTOR));
}
