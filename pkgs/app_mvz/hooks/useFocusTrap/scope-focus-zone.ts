import { FOCUSABLE_ATTRIBUTE } from '@tutum/mvz/constant/custom-attribute';
import { findTabbableDescendants } from './tabbable';

function $extractAnnotationCoordinateAttribute(
  el: HTMLElement
): [number, number] | null {
  const rect = el.getBoundingClientRect();
  return [rect.right, rect.top];
}

function sortElementTopLeftCoordinates(els: HTMLElement[]) {
  return [...els].sort((a, b) => {
    // sort by top, secondary by left
    const ac = $extractAnnotationCoordinateAttribute(a);
    if (!ac?.length) return 0;
    const bc = $extractAnnotationCoordinateAttribute(b);
    if (!bc?.length) return 0;
    const [ax, ay] = ac;
    const [bx, by] = bc;
    return Math.abs(ay - by) <= 3 ? ax - bx : ay - by;
  });
}

export function tabindexing(tabbableNodes: HTMLElement[]): HTMLElement[] {
  const sortedTabbable = sortElementTopLeftCoordinates(tabbableNodes);

  let index = 1;

  // re-tabindex
  const tabbable = sortedTabbable.reduce<HTMLElement[]>((cummulated, el) => {
    const customFocusAttribute = el.getAttribute(FOCUSABLE_ATTRIBUTE);
    if (customFocusAttribute === 'false') {
      return cummulated;
    }
    el.setAttribute('tabindex', `${index++}`);
    cummulated.push(el);
    return cummulated;
  }, []);

  return tabbable;
}

export function scopeFocusZone(
  node: HTMLElement,
  event: KeyboardEvent,
  onLastTabbale?: (event: KeyboardEvent) => void,
  onTabbing?: (
    event: KeyboardEvent,
    firstTabbable?: HTMLElement,
    containerNode?: HTMLElement,
    tabbables?: HTMLElement[]
  ) => void
) {
  const tabbableDescendants = findTabbableDescendants(node);

  if (!tabbableDescendants.length) {
    return;
  }

  // re-tabindex
  const tabbable = tabindexing(tabbableDescendants);

  if (tabbable?.length <= 1) {
    if (onLastTabbale) {
      onLastTabbale(event);
    } else {
      event.preventDefault();
      const target = tabbable[event.shiftKey ? tabbable.length - 1 : 0];
      target?.focus();
    }
    return;
  }

  const finalTabbable = tabbable[event.shiftKey ? 0 : tabbable.length - 1];

  const shouldRestart =
    document.activeElement === finalTabbable || document.activeElement === node;

  if (!shouldRestart) {
    if (onTabbing) {
      onTabbing(
        event,
        tabbable[event.shiftKey ? tabbable.length - 1 : 0],
        node,
        tabbable
      );
    }
    return;
  }

  if (onLastTabbale) {
    onLastTabbale(event);
  } else {
    event.preventDefault();
    const target = tabbable[event.shiftKey ? tabbable.length - 1 : 0];
    target?.focus();
  }
}
