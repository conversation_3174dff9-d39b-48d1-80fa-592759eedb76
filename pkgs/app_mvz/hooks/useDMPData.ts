import { useMemo, useState, useEffect, useCallback } from 'react';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import {
  EnrollmentWithDocumentModel,
  EventEnrollType,
  EnrollStatus,
  ParticipationForm,
} from '@tutum/hermes/bff/edmp_common';
import DMP_PROGRAMS from '@tutum/design-system/consts/DMP-programs';
import {
  EventEnrollEDMP,
  getEnrollment,
} from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import { useListenEnrollEDMP } from '@tutum/hermes/bff/app_mvz_edmp';
import { isEmpty } from 'lodash';
import { useTimeLineStore } from '../module_patient-management/patient-file/timeline/Timeline.store';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';

interface useDMPDataProps {
  patient?: IPatientProfile;
  doctorList: IEmployeeProfile[];
  currentLoggedInUser: IEmployeeProfile;
}

export const MATCH_COMMAND_DIAGNOSE = 'DD';

const useDMPData = ({
  patient,
  doctorList,
  currentLoggedInUser,
}: useDMPDataProps) => {
  const timelineStore = useTimeLineStore();

  const [isLoadingEnrollment, setLoadingEnrollment] = useState<boolean>(false);
  const [enrollmentList, setEnrollmentList] = useState<
    EnrollmentWithDocumentModel[]
  >([]);

  const selectedContractDoctorInfo = useMemo(() => {
    return doctorList?.find((doctor) => doctor?.id === currentLoggedInUser.id);
  }, [doctorList, currentLoggedInUser.id]);

  const DMPList = useMemo(() => {
    return doctorList.reduce<string[]>((dMPList, doctor) => {
      doctor.dmpPrograms?.forEach((dmp) => {
        if (!dMPList.includes(dmp)) {
          dMPList.push(dmp);
        }
      });

      return dMPList;
    }, []);
  }, [doctorList]);

  const convertedEnrollmentList = useMemo(() => {
    return enrollmentList.reduce(
      (convertedEnrollmentList: ParticipationForm[], datum) => {
        const { participationForm, activatedTime } =
          datum.enrollmentInfoModel.enrollmentInfo;
        const dmpLabel =
          DMP_PROGRAMS.getMatchedDMP(participationForm.dMPLabelingValue)
            ?.name || '';

        if (
          DMPList.includes(dmpLabel) &&
          DMP_PROGRAMS.getEnrollStatus(activatedTime) ===
            EnrollStatus.StatusActivated
        ) {
          const matchedIndex = convertedEnrollmentList.findIndex(
            (i) => i.dMPLabelingValue === participationForm.dMPLabelingValue
          );
          const newData = {
            ...participationForm,
            enrollStatus: EnrollStatus.StatusActivated,
          };

          if (matchedIndex !== -1) {
            convertedEnrollmentList[matchedIndex] = newData;
          } else {
            convertedEnrollmentList.push(newData);
          }
        }

        return convertedEnrollmentList;
      },
      []
    );
  }, [enrollmentList, DMPList]);

  const DMPListWithoutEnrolled = useMemo(() => {
    return DMPList.filter((dmp) =>
      convertedEnrollmentList.every(
        (item) =>
          DMP_PROGRAMS.getMatchedDMP(item.dMPLabelingValue)?.name !== dmp
      )
    );
  }, [DMPList, convertedEnrollmentList]);

  const DMPListEnrolled = useMemo(() => {
    return DMPList.filter((dmp) =>
      convertedEnrollmentList.some(
        (item) =>
          DMP_PROGRAMS.getMatchedDMP(item.dMPLabelingValue)?.name === dmp
      )
    );
  }, [DMPList, convertedEnrollmentList]);

  const hasDMPButton = useMemo(() => {
    const isPrivatePatient =
      patient?.patientInfo.genericInfo.patientType ===
      PatientType.PatientType_Private;

    return !isPrivatePatient && !!DMPList.length;
  }, [patient, DMPList]);

  const numberEnrolled = useMemo(() => {
    return convertedEnrollmentList.length;
  }, [convertedEnrollmentList]);

  const fetchEnrollmentList = useCallback(async () => {
    try {
      if (isEmpty(patient) || !DMPList[0]) {
        return;
      }

      setLoadingEnrollment(true);

      const resp = await getEnrollment({
        patientId: patient.id,
      });

      const enrollmentWithDocumentModels =
        resp.data.enrollmentWithDocumentModels || [];
      const lastedEnrollmentList: {
        [keyMapping: string]: EnrollmentWithDocumentModel;
      } = enrollmentWithDocumentModels
        .reverse()
        .reduce((data, enrollmentDatum) => {
          const dMPLabelingValue =
            enrollmentDatum.enrollmentInfoModel.enrollmentInfo.participationForm
              .dMPLabelingValue;
          const dmpLabel =
            DMP_PROGRAMS.getMatchedDMP(dMPLabelingValue)?.name || '';

          if (DMPList.includes(dmpLabel) && !data[dMPLabelingValue]) {
            data[dMPLabelingValue] = enrollmentDatum;
          }

          return data;
        }, {});

      setEnrollmentList(Object.values(lastedEnrollmentList));
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setLoadingEnrollment(false);
    }
  }, [patient, DMPList]);

  useEffect(() => {
    fetchEnrollmentList();
  }, [fetchEnrollmentList]);

  useListenEnrollEDMP((response: EventEnrollEDMP) => {
    if (
      [
        EventEnrollType.EventEnrollType_Enroll,
        EventEnrollType.EventEnrollType_CreateDocument,
        EventEnrollType.EventEnrollType_SaveDocument,
        EventEnrollType.EventEnrollType_FinishDocument,
        EventEnrollType.EventEnrollType_FetchEnroll,
      ].includes(response.eventEnrollType)
    ) {
      fetchEnrollmentList();
    }
  });

  return {
    timelineStateLoading: timelineStore.isLoadingTimeline,
    isLoadingEnrollment,
    hasDMPButton,
    selectedContractDoctorInfo,
    DMPList,
    DMPListWithoutEnrolled,
    DMPListEnrolled,
    convertedEnrollmentList,
    numberEnrolled,
    enrollmentList,
    fetchEnrollmentList,
  };
};

export default useDMPData;
