import { BSNRName } from '@tutum/hermes/bff/legacy/bsnr_common';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { useMemo } from 'react';

export const updateEmployeeProfileWithBsnr = (
  employee: IEmployeeProfile,
  bsnr: BSNRName
): IEmployeeProfile => {
  return {
    ...employee,
    bsnrId: bsnr.id,
    bsnr: bsnr.code,
    bsnrName: bsnr.name,
    bsnrStreet: bsnr.street,
    bsnrNumber: bsnr.number,
    bsnrPostCode: bsnr.postCode,
    bsnrPhoneNumber: bsnr.phoneNumber,
    bsnrFaxNumber: bsnr.fax,
    bsnrEmail: bsnr.email,
    bsnrPracticeStamp: bsnr.practiceStamp,
  };
};

export const useTreatmentDoctorWithBsnr = (
  doctorId: string | undefined,
  bsnrId: string | undefined
) => {
  const globalContext = GlobalContext.useContext();
  const doctorList = globalContext.useGetDoctorList();
  const { listBsnr } = globalContext.globalData;

  return useMemo(() => {
    const currentDoctor = (doctorList || []).find(
      (doctor) => doctor.id === doctorId
    );

    if (!currentDoctor) {
      return undefined;
    }

    const currentBsnr = (listBsnr || []).find((bsnr) => bsnr.id === bsnrId);

    if (!currentBsnr) {
      return currentDoctor;
    }

    return updateEmployeeProfileWithBsnr(currentDoctor, currentBsnr);
  }, [doctorId, bsnrId, doctorList, listBsnr]);
};

export const useGetTreatmentDoctorWithBsnr: () => (
  doctorId: string,
  bsnrId: string
) => IEmployeeProfile | undefined = () => {
  const globalContext = GlobalContext.useContext();
  const doctorList = globalContext.useGetDoctorList();
  const { listBsnr } = globalContext.globalData;

  return (doctorId: string, bsnrId: string) => {
    const currentDoctor = doctorList?.find((doctor) => doctor.id === doctorId);
    if (!currentDoctor) {
      return undefined;
    }

    const currentBsnr = (listBsnr || []).find((bsnr) => bsnr.id === bsnrId);
    if (!currentBsnr) {
      return currentDoctor;
    }

    return updateEmployeeProfileWithBsnr(currentDoctor, currentBsnr);
  };
};
