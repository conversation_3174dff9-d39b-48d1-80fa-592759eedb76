import { proxy, useSnapshot } from 'valtio';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import FormLocales from '@tutum/mvz/locales/en/Form.json';

interface IFormTranslation {
  translate: IFixedNamespaceTFunction<keyof typeof FormLocales> | null;
}

interface IFormTranslationActions {
  setTranslate: (t: IFixedNamespaceTFunction<keyof typeof FormLocales>) => void;
}

const initStore: IFormTranslation = {
  translate: null,
};

export const storeFormTranslation = proxy<IFormTranslation>(initStore);

export const formTranslationActions: IFormTranslationActions = {
  setTranslate: (t) => {
    storeFormTranslation.translate = t;
  },
};

export function useFormTranslationStore() {
  return useSnapshot(storeFormTranslation);
}
