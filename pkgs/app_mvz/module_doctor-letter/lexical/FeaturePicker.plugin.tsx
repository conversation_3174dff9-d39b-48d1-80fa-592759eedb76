import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import ReactDOM from 'react-dom';
import { TextNode } from 'lexical';
import type DoctorLetterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import CompoundMenuList from '@tutum/design-system/components/CompoundMenuList';
import { BodyTextM } from '@tutum/design-system/components/Typography';
import { Svg } from '@tutum/design-system/components/Svg';
import { Flex } from '@tutum/design-system/components/Flexbox';
import I18n from '@tutum/infrastructure/i18n';

import {
  TypeaheadPlugin,
  TypeaheadOption,
  useBasicTypeaheadTriggerMatch,
} from '@tutum/design-system/lexical/plugins/TypeaheadPlugin';
// import { INSERT_HORIZONTAL_RULE_COMMAND } from '@lexical/react/LexicalHorizontalRuleNode';
import { INSERT_TABLE_COMMAND } from '@lexical/table';
import { TOGGLE_TIMELINE_ENTRIES_DIALOG_COMMAND } from '../lexical/plugins';

class FeaturePickerOption extends TypeaheadOption {
  title: string;
  icon?: JSX.Element;
  disable?: boolean;
  keywords: string[];
  keyboardShortcut?: string;
  onSelect: (queryString: string) => void;

  constructor(
    title: string,
    options: {
      icon?: JSX.Element;
      disable?: boolean;
      keywords?: string[];
      keyboardShortcut?: string;
      onSelect: (queryString: string) => void;
    }
  ) {
    super(title);
    this.title = title;
    this.keywords = options.keywords || [];
    this.icon = options.icon;
    this.disable = options.disable || false;
    this.keyboardShortcut = options.keyboardShortcut;
    this.onSelect = options.onSelect.bind(this);
  }
}

interface FeaturePickerMenuPluginProps {
  onSelectTextmodule: () => void;
}

export function FeaturePickerMenuPlugin({
  onSelectTextmodule,
}: FeaturePickerMenuPluginProps): JSX.Element {
  const [editor] = useLexicalComposerContext();

  const { t } = I18n.useTranslation<keyof typeof DoctorLetterI18n>({
    namespace: 'DoctorLetter',
  });

  const [queryString, setQueryString] = useState<string | null>(null);

  const checkForTriggerMatch = useBasicTypeaheadTriggerMatch('/', {
    minLength: 0,
  });

  const options = useMemo(() => {
    // TODO: translate name
    const baseOptions = [
      new FeaturePickerOption(t('timelineEntries'), {
        icon: <Svg src="/images/git-commit.svg" />,
        keywords: ['timeline', 'entry', 'entries', 'diagnose', 'befund'],
        onSelect: () => {
          editor.dispatchCommand(TOGGLE_TIMELINE_ENTRIES_DIALOG_COMMAND, true);
        },
      }),

      new FeaturePickerOption(t('LabResults'), {
        disable: true,
        icon: <Svg src="/images/lab.svg" />,
        keywords: ['lab', 'results'],
        onSelect: () => {
          // TODO: dispatch command
        },
      }),

      new FeaturePickerOption(t('Stamp'), {
        disable: true,
        icon: <Svg src="/images/stamp.svg" />,
        keywords: ['stamp', 'doctor'],
        onSelect: () => {
          // TODO: dispatch command
        },
      }),

      new FeaturePickerOption(t('TextModule'), {
        icon: <Svg src="/images/message-circle.svg" />,
        keywords: ['text', '@tutum/mvz/module', 'txt', 'tm'],
        onSelect: onSelectTextmodule,
      }),

      new FeaturePickerOption(t('HorizontalRule'), {
        disable: true,
        icon: <Svg src="/images/horizontal-rule.svg" />,
        keywords: ['rule', 'horizontal', 'hr'],
        onSelect: () => {
          // TODO: dispatch command
        },
      }),
      // HIDE IMAGE OPTION BASE ON ISSUE REQUIREMENT: PRO-13923
      // new FeaturePickerOption(t('Image'), {
      //   disable: true,
      //   icon: <Svg src="/images/media-image.svg" />,
      //   keywords: ['image', 'picture', 'img'],
      //   onSelect: () => {
      //     // TODO: dispatch command
      //   },
      // }),

      new FeaturePickerOption(t('Table3x3'), {
        disable: false,
        icon: <Svg src="/images/table.svg" />,
        keywords: ['table', 'data'],
        onSelect: () => {
          editor.dispatchCommand(INSERT_TABLE_COMMAND, {
            columns: '3',
            rows: '3',
            includeHeaders: false,
          });
        },
      }),
    ];

    return queryString
      ? baseOptions.filter((option) => {
          return new RegExp(queryString, 'gi').exec(option.title) ||
            option.keywords != null
            ? option.keywords.some((keyword) =>
                new RegExp(queryString, 'gi').exec(keyword)
              )
            : false;
        })
      : baseOptions;
  }, [editor, queryString]);

  const onSelectOption = useCallback(
    (
      selectedOption: FeaturePickerOption,
      nodeToRemove: TextNode | null,
      closeMenu: () => void,
      matchingString: string
    ) => {
      if (selectedOption.disable) return;
      editor.update(() => {
        if (nodeToRemove) {
          nodeToRemove.remove();
        }
        selectedOption.onSelect(matchingString);
        closeMenu();
      });
    },
    [editor]
  );

  return (
    <TypeaheadPlugin<FeaturePickerOption>
      position="bottom"
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      triggerFn={checkForTriggerMatch}
      options={options}
      renderFn={({ selectedIndex, selectOptionAndCleanUp }, anchorElement) =>
        anchorElement && options.length
          ? ReactDOM.createPortal(
              <CompoundMenuList>
                <CompoundMenuList.Header />
                <CompoundMenuList.MenuList<FeaturePickerOption>
                  itemHeight="initial"
                  items={options}
                  getItemId={(option) => option.key}
                  onItemClick={(option) => {
                    if (option.disable) {
                      return;
                    }
                    selectOptionAndCleanUp(option);
                  }}
                  selectedIndex={selectedIndex}
                  renderItem={(option) => (
                    <Flex
                      align="center"
                      gap={8}
                      style={
                        option.disable
                          ? {
                              cursor: 'not-allowed',
                              opacity: 0.5,
                            }
                          : undefined
                      }
                    >
                      {option.icon}
                      <BodyTextM limitLines={1}>{option.title}</BodyTextM>
                    </Flex>
                  )}
                />
              </CompoundMenuList>,
              anchorElement
            )
          : <></>
      }
    />
  );
}
