import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import type { LetterTemplate } from '@tutum/hermes/bff/doctor_letter_common';
import { $setSelection, CLEAR_HISTORY_COMMAND } from 'lexical';
import React, { useEffect } from 'react';
import { handleTabNode } from '@tutum/mvz/module_doctor-letter/util';

export const SwitchTemplatePlugin: React.FC<{
  value: LetterTemplate | undefined;
}> = ({ value }) => {
  const [editor] = useLexicalComposerContext();
  useEffect(() => {
    if (!value) return;
    try {
      const parseBody = JSON.parse(value.body);

      handleTabNode(parseBody.root.children);

      const validState = editor.parseEditorState(JSON.stringify(parseBody));
      if (!validState) return;
      editor.setEditorState(validState);
      editor.dispatchCommand(CLEAR_HISTORY_COMMAND, undefined);
      // NOTE: force blur
      editor.update(() => {
        $setSelection(null);
        editor.blur();
      });
    } catch (error) {
      console.error(error);
      throw error;
    }
  }, [editor, value]);
  return null;
};
