import { memo } from 'react';
import { useField } from 'formik';
import { Flex } from '@tutum/design-system/components';
import type DoctorLetterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';
import { Spinner } from '@tutum/design-system/components/Core';
import DateRangeSingleInput from '@tutum/mvz/components/date-range-single-input';
import MomentLocaleUtils from 'react-day-picker/moment';
import { getDateWithTime } from '@tutum/design-system/infrastructure/utils';
import type { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type { ScheinItem } from '@tutum/hermes/bff/legacy/schein_common';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';

interface TimelineDateRangeProps {
  activeSchein: ScheinItem;
  t: IFixedNamespaceTFunction<keyof typeof DoctorLetterI18n>;
  setIsChanged: (changed: boolean) => void;
  dateRangeForPrivateSchein: (
    fieldStartDate: number,
    fieldEndDate: number
  ) => Record<string, number>;
  onHandleChangeDateRange: (startDate: Date, endDate: Date) => void;
  loading: boolean;
}

const TimelineDateRange = (props: TimelineDateRangeProps) => {
  const {
    t,
    activeSchein,
    setIsChanged,
    dateRangeForPrivateSchein,
    onHandleChangeDateRange,
    loading,
  } = props;

  const [startDateField] = useField('timelineStartDate');
  const [endDateField] = useField('timelineEndDate');

  const isCurrentScheinPrivate = checkIsPrivateSchein(activeSchein);

  const {
    startOfQuarterMs: defaultStartOfQuarter,
    endOfQuarterMs: defaultEndOfQuarter,
  } = isCurrentScheinPrivate
    ? dateRangeForPrivateSchein(null!, null!)
    : {
        startOfQuarterMs: startDateField?.value
          ? getDateWithTime(startDateField.value)
          : null,
        endOfQuarterMs: endDateField?.value
          ? getDateWithTime(endDateField.value)
          : null,
      };

  const defaultStartDate = startDateField?.value
    ? getDateWithTime(startDateField.value)
    : activeSchein?.issueDate
      ? getDateWithTime(activeSchein.issueDate)
      : getDateWithTime(defaultStartOfQuarter!);

  const defaultEndDate = endDateField?.value
    ? getDateWithTime(endDateField.value)
    : getDateWithTime(defaultEndOfQuarter!);

  if (loading) {
    return (
      <Flex w="100%" h={38}>
        <Spinner size={22} />
      </Flex>
    );
  }

  return (
    <Flex w="100%" gap={16}>
      <DateRangeSingleInput
        key={activeSchein.scheinId}
        locale="de"
        placeholder={t('select')}
        localeUtils={MomentLocaleUtils}
        defaultStartDate={defaultStartDate}
        defaultEndDate={defaultEndDate}
        onChange={(startDate, endDate) => {
          setIsChanged(true);
          onHandleChangeDateRange(startDate!, endDate!);
        }}
      />
    </Flex>
  );
};

export default memo(TimelineDateRange);
