import type { StylesConfig, GroupBase } from 'react-select';
import { SL_SELECT_STYLE_CONFIG } from '@tutum/design-system/consts/react-select-config';
import { COLOR } from '@tutum/design-system/themes/styles';

export const SELECT_STYLE = {
  ...SL_SELECT_STYLE_CONFIG(),
  container(base) {
    return { ...base, flexGrow: 1 };
  },
  indicatorsContainer(base) {
    return {
      ...base,
      paddingRight: 8,
      flex: 0,
    };
  },
  group(base) {
    return { ...base, padding: 0 };
  },
  groupHeading(base) {
    return { ...base, padding: '8px 16px', marginBottom: 0 };
  },
  menu(base) {
    return { ...base, zIndex: 2 };
  },
  singleValue(base, props) {
    return {
      ...base,
      color: props.isDisabled
        ? COLOR.TEXT_SECONDARY_NAVAL
        : COLOR.TEXT_PRIMARY_BLACK,
    };
  },
} as any;
