import type { Receiver } from '@tutum/hermes/bff/doctor_letter_common';
import type { DoctorInfo } from '@tutum/hermes/bff/catalog_sdav_common';
import type DoctorLetterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';
import type { MenuListProps } from 'react-select';

import React, {
  createContext,
  memo,
  useContext,
  useMemo,
  useState,
} from 'react';
import _groupBy from 'lodash/groupBy';
import Select, { GroupBase, SelectComponentsConfig } from 'react-select';

import i18n from '@tutum/infrastructure/i18n';
import { ReceiverType } from '@tutum/hermes/bff/doctor_letter_common';
import { SL_SELECT_COMPONENT_CONFIG } from '@tutum/design-system/consts/react-select-config';
import {
  getReceiverGroupLabel,
  getReceiverId,
  getReceiverName,
} from '@tutum/mvz/module_doctor-letter/util';
import {
  BodyTextM,
  coreComponents,
  Svg,
} from '@tutum/design-system/components';
import HospitalsPracticesDialog from '@tutum/mvz/module_kv_hzv_schein/hospitals-practices-dialog';

import { SELECT_STYLE } from './select-styles.const';
import { SelectGroupLabel } from './SelectGroupLabel.component';
import {
  SELECT_PREFIX_CLASS_NAME,
  StyledReceiverSelectFooter,
} from './Sidebar.styled';
import { GeneralInfo, SdavCatalog } from '@tutum/hermes/bff/legacy/catalog_sdav_common';
import { IntendWord, Salutation } from '@tutum/hermes/bff/legacy/patient_profile_common';

const ReceiverSelectCtx = createContext<{
  setOpenDoctorSelect: (isOpen: boolean) => void;
}>({
  setOpenDoctorSelect: () => {},
});

const MenuListWithDoctorSelect = (props: MenuListProps<any>) => {
  const { t: tDoctorLetter } = i18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({
    namespace: 'DoctorLetter',
  });

  const { setOpenDoctorSelect } = useContext(ReceiverSelectCtx);

  return (
    <coreComponents.MenuList {...props}>
      {props.children}
      {/* FOOTER HERE */}
      <StyledReceiverSelectFooter onClick={() => setOpenDoctorSelect(true)}>
        <BodyTextM
          className="sl-receiver-select-footer-label"
          fontWeight="SemiBold"
        >
          {tDoctorLetter('selectDoctorLabel')}
        </BodyTextM>
        <span className="sl-receiver-select-footer-icon">
          <Svg src="/images/arrow-right.svg" />
        </span>
      </StyledReceiverSelectFooter>
    </coreComponents.MenuList>
  );
};

const RECEIVER_COMPONENT_SELECT_CONFIG: SelectComponentsConfig<
  Receiver,
  false,
  GroupBase<Receiver>
> = {
  ...SL_SELECT_COMPONENT_CONFIG,
  MenuList: MenuListWithDoctorSelect,
};

// NOTE: RECEIVER
export const ReceiverSelect: React.FC<{
  disabled?: boolean;
  receivers?: Receiver[];
  className?: string;
  value?: Receiver;
  setIsChanged?: (isChanged: boolean) => void;
  onChange: (opt?: Receiver) => void;
  isLoading?: boolean;
}> = memo((props) => {
  const {
    className,
    disabled,
    receivers,
    onChange,
    value,
    setIsChanged,
    isLoading,
  } = props;

  const { t: tDoctorLetter } = i18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({
    namespace: 'DoctorLetter',
  });

  const [isOpenDoctorSelect, setOpenDoctorSelect] = useState(false);

  const groupedReceivers = useMemo<GroupBase<Receiver>[]>(() => {
    const groupedRawReceivers = _groupBy(receivers, 'receiverType');
    return Object.entries(groupedRawReceivers).map(
      ([type, options]: [ReceiverType, Receiver[]]) => {
        return {
          label: tDoctorLetter(getReceiverGroupLabel(type)),
          options,
        };
      }
    );
  }, [receivers]);

  return (
    <ReceiverSelectCtx.Provider
      value={{
        setOpenDoctorSelect,
      }}
    >
      <Select<Receiver>
        isLoading={isLoading}
        classNamePrefix={SELECT_PREFIX_CLASS_NAME}
        styles={SELECT_STYLE}
        components={RECEIVER_COMPONENT_SELECT_CONFIG}
        isDisabled={disabled} // NOTE: disabled if user selected it when create?
        className={className}
        value={value}
        options={groupedReceivers}
        onChange={async (opt, metadata) => {
          setIsChanged?.(true);
          if (metadata.action === 'select-option') {
            onChange(opt || undefined);
          }
        }}
        getOptionLabel={getReceiverName}
        getOptionValue={getReceiverId}
        filterOption={(opt) => {
          if (opt?.data?.receiverType === ReceiverType.ReceiverType_Employee) {
            return getReceiverId(opt.data) != '';
          }
          return true;
        }}
        formatGroupLabel={(data: GroupBase<Receiver>) => (
          <SelectGroupLabel label={data.label} />
        )}
      />

      {isOpenDoctorSelect && (
        <HospitalsPracticesDialog
          needDoctorInfo
          needAddressInfo
          isOpen
          onClose={() => setOpenDoctorSelect(false)}
          onSelect={(sdav: SdavCatalog) => {
            setOpenDoctorSelect(false);
            if (!sdav || !sdav.sdavId) {
              return;
            }
            setIsChanged?.(true);
            onChange({
              ...value,
              receiverType: ReceiverType.ReceiverType_Sdav_Doctor,
              sdavDoctorPayload: {
                id: sdav.sdavId,
                lastName: sdav.doctorInfo.lastName || '',
                firstName: sdav.doctorInfo.firstName || '',
                title: sdav.doctorInfo.title || '',
                intendWord: sdav.doctorInfo.intendWord as IntendWord,
                salutation: sdav.doctorInfo.salutation as Salutation,
                companyAddress: {
                  city: sdav.generalInfo?.city,
                  street: sdav.generalInfo?.street,
                  postCode: sdav.generalInfo?.postCode,
                  number: sdav.generalInfo?.number,
                },
                lanr: sdav.doctorInfo.lanr,
                bsnr: sdav.generalInfo.bsnr,
              },
            });
          }}
        />
      )}
    </ReceiverSelectCtx.Provider>
  );
});
