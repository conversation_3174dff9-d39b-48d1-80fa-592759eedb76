import React, { useEffect } from 'react';
import Select, { GroupBase } from 'react-select';

import type DoctorLetterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';

import { SL_SELECT_COMPONENT_CONFIG } from '@tutum/design-system/consts/react-select-config';
import { SenderType } from '@tutum/hermes/bff/doctor_letter_common';
import i18n from '@tutum/infrastructure/i18n';
import { SelectGroupLabel } from './SelectGroupLabel.component';
import { SELECT_PREFIX_CLASS_NAME } from './Sidebar.styled';
import { SELECT_STYLE } from './select-styles.const';
import { CategoryItemName } from '@tutum/hermes/bff/legacy/doctor_letter_common';
import { SHOW_VARIABLE_COMMAND } from '@tutum/design-system/lexical/components/doctor-letter/Variable';
import { useEditors } from '@tutum/mvz/module_doctor-letter/lexical/EditorProvider';

export const SenderSelect: React.FC<{
  disabled?: boolean;
  className?: string;
  value: SenderType;
  bsnrName: string;
  onChange: (opt?: SenderType) => void;
}> = ({ className, disabled, onChange, value, bsnrName }) => {
  const { editors } = useEditors();

  const { t: tDoctorLetter } = i18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({
    namespace: 'DoctorLetter',
  });

  const options = [
    {
      label: tDoctorLetter('bsnr'),
      options: [{ value: SenderType.SenderType_BSNR }],
    },
    {
      label: '',
      options: [{ value: SenderType.SenderType_Doctor }],
    },
  ];

  const handleChange = (opt: { value: SenderType } | null, metadata: any) => {
    if (!editors) {
      throw new Error('Editors is undefined in handleChange');
    }
    if (opt?.value === SenderType.SenderType_BSNR) {
      editors.forEach((editor) => {
        if (!editor) {
          throw new Error('Editor is undefined in handleChange');
        }
        editor.dispatchCommand(SHOW_VARIABLE_COMMAND, {
          categoryItemName: CategoryItemName.DoctorInformation_Name,
          isShow: false,
          index: 0,
        });
      });
    } else {
      editors.forEach((editor) => {
        if (!editor) {
          throw new Error('Editor is undefined in handleChange');
        }
        editor.dispatchCommand(SHOW_VARIABLE_COMMAND, {
          categoryItemName: CategoryItemName.DoctorInformation_Name,
          isShow: true,
          index: 0,
        });
      });
    }

    if (metadata.action === 'select-option') {
      onChange(opt?.value);
    }
  };

  useEffect(() => {
    handleChange({ value }, { action: 'select-option' });
  }, [editors.length, value]);

  return (
    <Select<{ value: SenderType }>
      classNamePrefix={SELECT_PREFIX_CLASS_NAME}
      isDisabled={disabled}
      styles={SELECT_STYLE}
      components={SL_SELECT_COMPONENT_CONFIG}
      className={className}
      value={{ value }}
      options={options}
      onChange={handleChange}
      getOptionLabel={({ value }) =>
        value === SenderType.SenderType_BSNR
          ? bsnrName
          : tDoctorLetter('doctor')
      }
      getOptionValue={(opt) => opt.value}
      formatGroupLabel={(data: GroupBase<{ value: SenderType }>) => (
        <SelectGroupLabel label={data.label} />
      )}
    />
  );
};
