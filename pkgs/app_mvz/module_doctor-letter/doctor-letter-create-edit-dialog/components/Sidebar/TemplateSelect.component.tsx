import React from 'react';
import Select from 'react-select';

import type { LetterTemplate } from '@tutum/hermes/bff/doctor_letter_common';

import { SL_SELECT_COMPONENT_CONFIG } from '@tutum/design-system/consts/react-select-config';
import { SELECT_STYLE } from './select-styles.const';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

export const TemplateSelect: React.FC<{
  t: IFixedNamespaceTFunction<any>;
  templates: LetterTemplate[];
  className?: string;
  value?: LetterTemplate;
  defaultValue?: any;
  onChange: (opt?: LetterTemplate) => void;
  isDisabled?: boolean;
}> = (props) => {
  const {
    t,
    className,
    templates,
    value,
    onChange,
    defaultValue,
    isDisabled = false,
  } = props;

  return (
    <Select<LetterTemplate>
      isDisabled={isDisabled}
      placeholder={t('Select.search')}
      styles={SELECT_STYLE}
      components={SL_SELECT_COMPONENT_CONFIG}
      className={className}
      value={value || undefined}
      options={templates}
      defaultValue={defaultValue}
      noOptionsMessage={() => t('Select.noResults')}
      getOptionLabel={(opt: LetterTemplate) => opt.name}
      getOptionValue={(opt: LetterTemplate) => opt.id ?? ''}
      onChange={(opt: LetterTemplate, metadata) => {
        if (metadata.action === 'select-option') {
          onChange(opt);
        }
      }}
    />
  );
};
