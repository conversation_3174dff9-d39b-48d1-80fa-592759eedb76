import type { Sender, Receiver } from '@tutum/hermes/bff/doctor_letter_common';
import type Doctor<PERSON>etterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';
import { isEmpty } from 'lodash';

import {
  SenderType,
  ReceiverType,
} from '@tutum/hermes/bff/doctor_letter_common';
import Utils from '@tutum/infrastructure/utils/form.util';

export function getSenderName(sender: Sender) {
  switch (sender?.senderType) {
    case SenderType.SenderType_BSNR: {
      return sender.bSNRPayload?.name || '';
    }
    case SenderType.SenderType_Doctor: {
      return (
        sender.doctorPayload?.firstName + ' ' + sender.doctorPayload?.lastName
      );
    }
    default:
      return '';
  }
}

export function getSenderId(sender: Sender) {
  switch (sender?.senderType) {
    case SenderType.SenderType_BSNR: {
      return sender.bSNRPayload?.id;
    }
    case SenderType.SenderType_Doctor: {
      return sender.doctorPayload?.id;
    }
    default:
      return '';
  }
}

export function getReceiverName(receiver: Receiver) {
  switch (receiver?.receiverType) {
    case ReceiverType.ReceiverType_Employee: {
      return receiver.employerPayload?.employeeName || '';
    }
    case ReceiverType.ReceiverType_Patient: {
      return `${receiver.patientPayload?.personalInfo?.firstName} ${receiver.patientPayload?.personalInfo?.lastName}`;
    }
    case ReceiverType.ReceiverType_Insurance: {
      return receiver.insurancePayload?.name?.toString() || '';
    }
    case ReceiverType.ReceiverType_Sdav_Doctor: {
      if (!receiver.sdavDoctorPayload) {
        return '';
      }

      const { firstName, lastName, title, intendWord, id } =
        receiver.sdavDoctorPayload;
      return id && lastName
        ? Utils.getFullName(title, intendWord, lastName, firstName)
        : '';
    }
    default:
      return '';
  }
}

export function getReceiverId(receiver: Receiver) {
  switch (receiver?.receiverType) {
    case ReceiverType.ReceiverType_Employee: {
      return receiver.employerPayload?.companyAddress?.street || '';
    }
    case ReceiverType.ReceiverType_Patient: {
      return receiver.patientPayload?.id as string;
    }
    case ReceiverType.ReceiverType_Insurance: {
      return receiver.insurancePayload?.id as string;
    }
    default:
      return '';
  }
}

export function getReceiverGroupLabel(
  receiverType: ReceiverType
): Partial<keyof typeof DoctorLetterI18n> {
  switch (receiverType) {
    case ReceiverType.ReceiverType_Employee: {
      return 'employee';
    }
    case ReceiverType.ReceiverType_Patient: {
      return 'patient';
    }
    default:
      return 'insurance';
  }
}

export function getReceiverByType(
  receivers: Receiver[],
  receiverType: ReceiverType
): Receiver | undefined {
  switch (receiverType) {
    case ReceiverType.ReceiverType_Employee: {
      return receivers.find((r) => !isEmpty(r.employerPayload));
    }
    case ReceiverType.ReceiverType_Patient: {
      return receivers.find((r) => !isEmpty(r.patientPayload));
    }
    case ReceiverType.ReceiverType_Insurance: {
      return receivers.find((r) => !isEmpty(r.insurancePayload));
    }
    default:
      return undefined;
  }
}
export const handleTabNode = (children) => {
  children.forEach((child) => {
    if (child.type === 'tab') {
      child.mode = '';
    }

    if (child.children) {
      handleTabNode(child.children);
    }
  });
}