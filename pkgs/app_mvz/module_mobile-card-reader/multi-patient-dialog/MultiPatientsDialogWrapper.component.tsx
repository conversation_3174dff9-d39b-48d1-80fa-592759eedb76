import React from 'react';

import type { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import { BodyTextL, Flex, Svg } from '@tutum/design-system/components';
import { Divider } from '@tutum/design-system/components/Core';
import {
  MobileCardReaderOverviewAction,
  useMobileCardReaderStore,
} from '../overview/MobileCardReaderOverview.store';
import {
  StyledContainer,
  StyledDialogComponent,
} from './MultiPatientsDialogWrapper.styled';
import {
  reviewPatientStoreActions,
  useReviewPatientStore,
} from '../review-patient-dialog/useReviewPatientStore';
import NavigationPatients from './NavigationPatients';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';

interface MultiPatientsPaginationPanelProps {
  title: string;
  description?: React.ReactNode;
  activePatientIdx: number;
  totalReviewingPatients?: number;
  isNeedUpdateProfile?: boolean;
  onClose: () => void;
  onNextPatient: () => void;
  onPrevPatient: () => void;
}

const MultiPatientsPaginationPanel: React.FC<MultiPatientsPaginationPanelProps> =
  ({
    onClose,
    onNextPatient,
    onPrevPatient,
    title,
    description = '',
    activePatientIdx,
    totalReviewingPatients = 0,
    isNeedUpdateProfile = false,
  }) => {
    return (
      <StyledContainer className="sl-review-patient-header-container">
        <Flex className="sl-review-patient-header">
          <Flex column gap={4}>
            <BodyTextL
              className={`sl-review-patient-header__title ${
                isNeedUpdateProfile ? 'm-right' : ''
              }`}
              fontWeight="Bold"
            >
              {title}
            </BodyTextL>
            <BodyTextL className="sl-review-patient-header__desc">
              {description}
            </BodyTextL>
          </Flex>

          <Flex>
            <Flex>
              <NavigationPatients
                activePatientIdx={activePatientIdx}
                totalReviewingPatients={totalReviewingPatients}
                onPrevPatient={onPrevPatient}
                onNextPatient={onNextPatient}
              />

              <div>
                <Divider className="sl-review-patient-header__divider" />
              </div>
              <Svg
                onClick={onClose}
                style={{ height: '24px', cursor: 'pointer' }}
                src="/images/close.svg"
              />
            </Flex>
          </Flex>
        </Flex>
      </StyledContainer>
    );
  };

interface MultiPatientsDialogWrapperProps {
  className?: string;
  isOpen: boolean;
  numberSelected: number;
  onClose: () => void;
  title: (activePatient?: PatientInfo | null) => string;
  description?: (activePatient?: PatientInfo | null) => React.ReactNode;
  footer: (activePatient?: PatientInfo | null) => React.ReactNode;
  children: (activePatient?: PatientInfo | null) => React.ReactNode;
  patients?: PatientInfo;
  activePatientIndex: number;
  setActivePatientIndex: (activePatientIndex: number) => void;
}

export function MultiPatientsDialogWrapper({
  isOpen,
  onClose,
  numberSelected,
  title,
  description,
  footer,
  children,
  patients,
  activePatientIndex,
  className,
  setActivePatientIndex,
}: MultiPatientsDialogWrapperProps) {
  const mobileCardReaderOverviewStore = useMobileCardReaderStore();
  const [reviewPatientStore, _] = useReviewPatientStore();
  const { patientOriginal } = reviewPatientStore;

  const isNeedUpdateProfile =
    patientOriginal &&
    patientOriginal.errorCode === ErrorCode.ErrorCode_Patient_Missing_Gender;

  return (
    <StyledDialogComponent
      size="full"
      isOpen={isOpen}
      usePortal={false}
      className={getCssClass('bp5-dialog-content-scrollable', className)}
      onClose={onClose}
      isCloseButtonShown={false}
      style={{
        '--sl-dialog-header-padding-inline': '16px',
        '--sl-dialog-header-padding-top': '4px',
      }}
      title={
        <MultiPatientsPaginationPanel
          activePatientIdx={mobileCardReaderOverviewStore.activePatient}
          title={title(
            mobileCardReaderOverviewStore.patientManagement?.patient
              ?.patientInfo || patients
          )}
          description={description?.(
            mobileCardReaderOverviewStore.patientManagement?.patient
              ?.patientInfo || patients
          )}
          totalReviewingPatients={numberSelected}
          onClose={onClose}
          onNextPatient={() => {
            if (mobileCardReaderOverviewStore.isCreateSchein) {
              MobileCardReaderOverviewAction.getInformationSchein(
                mobileCardReaderOverviewStore.rowSelectedFilter[
                  activePatientIndex + 1
                ]
              );
            } else {
              reviewPatientStoreActions.addPatientToReviewList(
                mobileCardReaderOverviewStore.rowSelectedFilter[
                  activePatientIndex + 1
                ]
              );
            }
            setActivePatientIndex(activePatientIndex + 1);
            MobileCardReaderOverviewAction.setActivePatient(
              mobileCardReaderOverviewStore.activePatient + 1
            );
          }}
          onPrevPatient={() => {
            if (mobileCardReaderOverviewStore.isCreateSchein) {
              MobileCardReaderOverviewAction.getInformationSchein(
                mobileCardReaderOverviewStore.rowSelectedFilter[
                  activePatientIndex - 1
                ]
              );
            } else {
              reviewPatientStoreActions.addPatientToReviewList(
                mobileCardReaderOverviewStore.rowSelectedFilter[
                  activePatientIndex - 1
                ]
              );
            }
            MobileCardReaderOverviewAction.setActivePatient(
              mobileCardReaderOverviewStore.activePatient - 1
            );
            setActivePatientIndex(activePatientIndex - 1);
          }}
          isNeedUpdateProfile={isNeedUpdateProfile}
        />
      }
      actions={
        !!footer(
          mobileCardReaderOverviewStore.patientManagement?.patient
            ?.patientInfo || patients
        ) && (
          <StyledContainer>
            <Flex className="sl-review-patient-footer">
              {footer(
                mobileCardReaderOverviewStore.patientManagement?.patient
                  ?.patientInfo || patients
              )}
            </Flex>
          </StyledContainer>
        )
      }
    >
      <StyledContainer>
        <Flex className="sl-review-patient-body" justify="center">
          {children(
            mobileCardReaderOverviewStore.patientManagement?.patient
              ?.patientInfo || patients
          )}
        </Flex>
      </StyledContainer>
    </StyledDialogComponent>
  );
}
