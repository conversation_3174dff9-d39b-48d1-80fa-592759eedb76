import { proxy, useSnapshot } from 'valtio';

import { reviewRawResult } from '@tutum/hermes/bff/legacy/app_mvz_card_raw';
import { GetCardsResponse } from '@tutum/hermes/bff/app_mvz_cardservice';
import { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import { MobileCardInfoModel } from '@tutum/hermes/bff/card_raw_common';

interface ReviewPatientStore {
  currentPatients: PatientInfo | undefined;
  cardReview: GetCardsResponse | undefined;
  patientOriginal: MobileCardInfoModel;
}

interface ReviewPatientStoreAction {
  addPatientToReviewList(row: MobileCardInfoModel): void;
  clearPatientToReviewList(): void;
}

const initRowPatient: MobileCardInfoModel = {
  id: '',
  mobileCardInfo: null!,
  errorCode: '',
};

export const reviewPatientStore = proxy<ReviewPatientStore>({
  currentPatients: undefined,
  cardReview: undefined,
  patientOriginal: initRowPatient,
});

export const reviewPatientStoreActions: ReviewPatientStoreAction = {
  addPatientToReviewList(row: MobileCardInfoModel) {
    reviewRawResult({ id: row?.id }).then((data) => {
      reviewPatientStore.cardReview = data.data;
      reviewPatientStore.currentPatients =
        data.data?.patientCompareData.patientFromCard;
    });
    reviewPatientStore.patientOriginal = row;
  },
  clearPatientToReviewList() {
    reviewPatientStore.currentPatients = undefined;
    reviewPatientStore.patientOriginal = initRowPatient;
    reviewPatientStore.cardReview = undefined;
  },
};

export const useReviewPatientStore = (): [
  ReviewPatientStore,
  ReviewPatientStoreAction
] => {
  const snapshot = useSnapshot(reviewPatientStore);
  return [snapshot, reviewPatientStoreActions];
};
