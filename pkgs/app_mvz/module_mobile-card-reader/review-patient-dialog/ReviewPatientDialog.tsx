import { ReactNode, useState, memo, useMemo, useRef } from 'react';
import dynamic from 'next/dynamic';
import isEmpty from 'lodash/isEmpty';

import type MobileCardReaderI18n from '@tutum/mvz/locales/en/MobileCardReader.json';
import type patientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  PatientProfileResponse,
  UpdatePatientProfileV2Request,
  updatePatientProfileV2,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  PatientInfo,
  CompareStatus,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import {
  alertError,
  alertSuccessfully,
  BodyTextM,
  Button,
  Flex,
  LeaveConfirmModal,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import i18n from '@tutum/infrastructure/i18n';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { Radio } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import {
  formatUnixToDateString,
  getUUID,
} from '@tutum/design-system/infrastructure/utils';
import PatientDataComparison from '@tutum/mvz/module_patient-overview/patient-overview-synchronise/PatientDataComparison';
import { reviewPatientStore } from '../review-patient-dialog/useReviewPatientStore';
import { MobileCardReaderOverviewStore } from '../overview/MobileCardReaderOverview.store';
import {
  StyledReviewPatientNotMatch,
  WrapperCreatePatientDialog,
  Container,
} from './ReviewPatientDialog.styled';
import { MultiPatientsDialogWrapper } from '../multi-patient-dialog/MultiPatientsDialogWrapper.component';
import {
  ICustomInsuranceInfo,
  getActiveInsurance,
  stageSelectedInsurance,
} from '@tutum/mvz/_utils/checkInsurance';
import {
  FromCardType,
  PatientType,
} from '@tutum/hermes/bff/legacy/patient_profile_common';
import { statusErrorCreatePatient } from '@tutum/mvz/_utils/statusErrorCreatePatient';
import { PatientCompareData } from '@tutum/hermes/bff/legacy/app_mvz_cardservice';
import { useMutationUpdateStatus } from '@tutum/hermes/bff/legacy/app_mvz_card_raw';
import { ImportStatus } from '@tutum/hermes/bff/card_raw_common';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { getInsuranceReadTICardByQuarter } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import CreatePatientBody from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatientContent';
import { getCardInsurance } from '@tutum/mvz/_utils/cardReader';
import { COLOR } from '@tutum/design-system/themes/styles';
import { InsuranceActionEnum } from '@tutum/mvz/module_insurance/ListInsurance.type';
import { IFormikRef } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';

const ListInsurancesModal = dynamic(
  () => import('@tutum/mvz/module_insurance/ListInsurance.styled'),
  { ssr: false }
);

interface DataTablePatientCard {
  id?: number;
  insuranceNumber?: string;
  patientName: string;
  dateOfBirth: number;
}

interface DataTablePatientDB {
  id?: string;
  insuranceNumber?: string;
  patientName: string;
  dateOfBirth: number;
}

const ReviewPatientsDialogInsuranceNOTMatchBody: React.FC<{
  activeGPRowId: string | null;
  onChange: (activeGPRowId: string) => void;
}> = ({ onChange, activeGPRowId }) => {
  const { t } = i18n.useTranslation<keyof typeof MobileCardReaderI18n>({
    namespace: 'MobileCardReader',
  });

  const createDataTablePatientCard = () => {
    const dataTable: DataTablePatientCard[] = [];

    const item =
      reviewPatientStore.cardReview?.patientCompareData.patientFromCard;
    dataTable.push({
      id: item?.patientNumber,
      insuranceNumber: getActiveInsurance(item?.insuranceInfos)
        ?.insuranceNumber,
      patientName: `${item?.personalInfo?.lastName}, ${item?.personalInfo?.firstName}`,
      dateOfBirth: item?.personalInfo?.dOB!,
    });
    return dataTable;
  };

  const createDataTablePatientDb = () => {
    const dataTable: DataTablePatientDB[] = [];
    reviewPatientStore.cardReview?.patientCompareData.patientsMatched?.forEach(
      (item: PatientInfo) => {
        dataTable.push({
          id: item.patientId,
          insuranceNumber: getActiveInsurance(item?.insuranceInfos)
            ?.insuranceNumber,
          patientName: `${item?.personalInfo?.lastName}, ${item?.personalInfo?.firstName}`,
          dateOfBirth: item?.personalInfo?.dOB,
        });
      }
    );

    return dataTable;
  };

  return (
    <StyledReviewPatientNotMatch
      column
      gap={16}
      className="sl-review-patient-body-not-match"
    >
      <BodyTextM>{t('patientProfileMayExist')}</BodyTextM>
      <Flex column gap={8} className="sl-review-patient-body-not-match-table">
        <BodyTextM className="sl-review-patient-body-not-match-table__name">
          {reviewPatientStore.cardReview?.cardType || 'EGK'}
        </BodyTextM>
        <Table
          data={createDataTablePatientCard()}
          columns={[
            {
              selector: (row: DataTablePatientCard) => row.id!,
              width: '48px',
              style: {
                borderRight: 'none',
              },
              format() {
                return '';
              },
            },
            {
              name: t('insuranceNumber'),
              selector: (row: DataTablePatientCard) => row.insuranceNumber!,
              format(row: DataTablePatientCard) {
                return row.insuranceNumber;
              },
            },
            {
              name: t('patientName'),
              selector: (row: DataTablePatientCard) => row.patientName,
              format(row: DataTablePatientCard) {
                return row.patientName;
              },
            },
            {
              name: t('dateOfBirth'),
              selector: (row: DataTablePatientCard) => row.dateOfBirth,
              format(row: DataTablePatientCard) {
                return formatUnixToDateString(row.dateOfBirth);
              },
            },
          ]}
          noHeader
          persistTableHead
        />
      </Flex>

      <Flex column gap={8} className="sl-review-patient-body-not-match-table">
        <BodyTextM className="sl-review-patient-body-not-match-table__name">
          {t('tableTitle')}
        </BodyTextM>
        <Table
          data={createDataTablePatientDb()}
          columns={[
            {
              name: t('insuranceNumber'),
              selector: (row: DataTablePatientDB) => row.insuranceNumber!,
              format(row: DataTablePatientDB) {
                return row.insuranceNumber;
              },
            },
            {
              name: t('patientName'),
              selector: (row: DataTablePatientDB) => row.patientName,
              format(row: DataTablePatientDB) {
                return row.patientName;
              },
            },
            {
              name: t('dateOfBirth'),
              selector: (row: DataTablePatientDB) => row.dateOfBirth,
              format(row: DataTablePatientDB) {
                return formatUnixToDateString(row.dateOfBirth);
              },
            },
          ]}
          noHeader
          persistTableHead
          selectableRows
          selectableRowsNoSelectAll
          selectableRowSelected={(row: DataTablePatientDB) => row.id === activeGPRowId}
          selectableRowsComponent={Radio as unknown as ReactNode}
          onSelectedRowsChange={({ selectedRows }) => {
            onChange((selectedRows?.[0] as any)?.id ?? null);
          }}
          conditionalRowStyles={[
            {
              when(row: DataTablePatientDB) {
                return row.id === activeGPRowId;
              },
              style: {
                backgroundColor: COLOR.INFO_SECONDARY_PRESSED,
              },
            },
          ]}
          isCustom
        />
      </Flex>
    </StyledReviewPatientNotMatch>
  );
};

const ReviewPatientsDialogInsuranceMatchBody: React.FC<{
  onChange: (data: UpdatePatientProfileV2Request) => void;
  patientId: string;
  patientCompareData?: PatientCompareData;
}> = ({ onChange, patientId, patientCompareData }) => {
  const { t } = i18n.useTranslation<keyof typeof MobileCardReaderI18n>({
    namespace: 'MobileCardReader',
  });

  if (
    !patientCompareData?.patientsMatched ||
    !patientCompareData?.patientFromCard
  )
    return <></>;

  const patientInfo = patientCompareData.patientsMatched?.find(
    (p) => p.patientId === patientId
  );

  const patientPrepared = patientCompareData.patientsPrepared?.find(
    (p) => p.patientInfo.patientId === patientId
  );

  return (
    <PatientDataComparison
      fromCardType={FromCardType.FromCardType_Mobile}
      conflictWarningLabel={t('moreUpdateDataAvailable')}
      conflictWarningContent={t('newDataMightNeedToUpdate')}
      patientId={patientId}
      egkProfile={patientCompareData.patientFromCard}
      patientInfo={patientInfo}
      patientPrepared={patientPrepared}
      onPatientProfileChange={onChange}
      compareStatus={patientCompareData.compareResult}
    />
  );
};

interface ReviewPatientsDialogProps {
  onClose: () => void;
  onAction: () => void;
  patients?: PatientInfo;
  activePatientIndex: number;
  setActivePatientIndex(index: number): void;
}

const ReviewPatientsDialog = ({
  onClose,
  onAction,
  patients,
  setActivePatientIndex,
}: ReviewPatientsDialogProps) => {
  const formikRef = useRef<IFormikRef>(null);

  const { t } = i18n.useTranslation<keyof typeof MobileCardReaderI18n>({
    namespace: 'MobileCardReader',
  });

  const { t: tCreatePatient } = i18n.useTranslation<
    keyof typeof patientManagementI18n.CreatePatient
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'CreatePatient',
  });

  const [initInsurances, setInitInsurances] = useState<ICustomInsuranceInfo[]>(
    []
  );
  const [insuranceState, setInsuranceState] = useState<{
    insurance: ICustomInsuranceInfo[];
    insuranceDeleted: ICustomInsuranceInfo[];
  }>({
    insurance: [],
    insuranceDeleted: [],
  });
  const [isChangeInsurance, setIsChangeInsurance] = useState(false);
  const [openInsuranceList, setOpenInsuranceList] = useState(false);
  const [isOpenCreatePatient, setIsOpenCreatePatient] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const isNeedUpdateProfile =
    reviewPatientStore.patientOriginal.errorCode ===
    ErrorCode.ErrorCode_Patient_Missing_Gender;

  const stageSelectInsurance = useMemo(() => {
    switch (reviewPatientStore.currentPatients?.genericInfo?.patientType) {
      case PatientType.PatientType_Public:
        return stageSelectedInsurance.KV_createSchein;
      case PatientType.PatientType_Private:
        return stageSelectedInsurance.PRIVATE_createSchein;
      default:
        return undefined;
    }
  }, [reviewPatientStore.currentPatients?.genericInfo?.patientType]);

  const onToggleInsuranceList = () => {
    setOpenInsuranceList((prev) => !prev);
  };

  const onSaveNewInsurances = (items: ICustomInsuranceInfo[]) => {
    setInitInsurances(items);
    setInsuranceState((prev) => ({
      ...prev,
      insurance: items.map((i) => ({
        ...i,
        id: i.id || getUUID(),
      })),
    }));
    setIsChangeInsurance(true);
  };

  const toaster = useToaster();

  const toastPrintSuccessUpdated = () => {
    alertSuccessfully(
      isNeedUpdateProfile ? t('patientCreated') : t('toastUpdated'),
      { timeout: TOASTER_TIMEOUT_CUSTOM, toaster }
    );
  };

  const updateStatusCard = useMutationUpdateStatus({
    onSuccess: () => {
      toastPrintSuccessUpdated();
    },
  });

  // NOTE: insurance NOT match
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [openSynchronise, setOpenSynchonise] = useState<boolean>(false);

  // NOTE: insurance matched
  const [newPatientPayload, setNewPatientPayload] =
    useState<UpdatePatientProfileV2Request | null>(null);

  const [isConfirmLeaveOpen, setIsConfirmLeaveOpen] = useState(false);

  const [selectedPatient, setSelectedPatient] = useState<PatientInfo | undefined>(
    undefined
  );

  async function handleUpdatePatientProfile() {
    if (!newPatientPayload) {
      return;
    }
    updatePatientProfileV2(newPatientPayload).then((res) => {
      if (res.data?.updateErrorStatus) {
        const errorMes = statusErrorCreatePatient(
          res.data?.updateErrorStatus,
          tCreatePatient
        );
        alertError(errorMes, {
          timeout: TOASTER_TIMEOUT_CUSTOM,
          toaster,
        });
      }
    });
    updateStatusCard
      .mutateAsync({
        id: reviewPatientStore.patientOriginal.id,
        cardStatus: ImportStatus.ImportStatus_PatientUpdated,
        isCreate: false,
      })
      .then(() => {
        setSelectedPatient(undefined);
        if (openSynchronise) {
          setOpenSynchonise(false);
        }
        onAction();
      });
  }

  async function handleAssignPatientToInsurance() {
    if (!selectedPatientId) {
      return;
    }

    const isExactMatch =
      reviewPatientStore.cardReview?.patientCompareData.patientsPrepared?.find(
        (p) => p.patientInfo.patientId === selectedPatientId
      )?.compareStatus === CompareStatus.ExactMatch;

    if (isExactMatch) {
      updateStatusCard
        .mutateAsync({
          patientId: selectedPatientId,
          id: reviewPatientStore.patientOriginal?.id,
          cardStatus: ImportStatus.ImportStatus_PatientUpdated,
          isCreate: false,
        })
        .then(() => {
          setIsOpenCreatePatient(false);
          onAction();
          setIsLoading(false);
          setOpenSynchonise(false);
        });

      return;
    }
    setOpenSynchonise(true);
  }

  function handleCancel() {
    onClose();
    setIsOpenCreatePatient(false);
  }

  function handleSelectPatient(patient: PatientInfo) {
    setSelectedPatient(patient);
  }

  const handlerCreatePatientV2 = async () => {
    const payload = formikRef.current?.getValue();
    const valid = await formikRef.current?.onValidateRef();
    if (isEmpty(valid)) {
      await formikRef.current?.onSaveFormRef(payload);
      setIsLoading(true);
    }
  };

  const handlerUpdatePatientCard = async (patientId: string) => {
    updateStatusCard
      .mutateAsync({
        patientId: patientId,
        id: reviewPatientStore.patientOriginal?.id,
        cardStatus: ImportStatus.ImportStatus_PatientCreated, // when isCreate = true -> create new patient.
        isCreate: true,
      })
      .then(() => {
        setIsOpenCreatePatient(false);
        onAction();
        setIsLoading(false);
      });
  };

  const handlerOpenCreatePatient = (patient: PatientInfo) => {
    handleSelectPatient(patient);
    setIsOpenCreatePatient(true);
  };

  const markAsActive = (id: string) => {
    setInitInsurances((prev) =>
      prev.map((item) => ({ ...item, isActive: item.id === id }))
    );
  };

  return (
    <Container>
      <MultiPatientsDialogWrapper
        isOpen
        onClose={onClose}
        numberSelected={MobileCardReaderOverviewStore.rowSelectedFilter.length}
        patients={patients}
        activePatientIndex={MobileCardReaderOverviewStore.activePatient}
        setActivePatientIndex={setActivePatientIndex}
        title={(activePatient) => {
          if (isNeedUpdateProfile) return t('createPatient');
          if (activePatient)
            return [
              reviewPatientStore.currentPatients?.personalInfo.lastName,
              reviewPatientStore.currentPatients?.personalInfo.firstName,
            ].join(', ');
          return '';
        }}
        footer={(activePatient: PatientInfo) => {
          const isPartialMatch =
            reviewPatientStore.cardReview?.patientCompareData.compareResult ===
            CompareStatus.PartialMatch && !openSynchronise;

          if (selectedPatient) {
            return (
              <>
                <Button
                  intent="primary"
                  outlined
                  minimal
                  onClick={handleCancel}
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={() => handlerCreatePatientV2()}
                  intent="primary"
                >
                  {t('create')}
                </Button>
              </>
            );
          }

          const patientId = reviewPatientStore.patientOriginal.mobileCardInfo.cardInfo
            .patientId;

          const isCreateShown =
            reviewPatientStore.cardReview?.patientCompareData.patientsPrepared?.find(
              (p) => p.patientInfo.patientId === patientId
            )?.isCreateShown;

          return (
            <>
              <Button
                intent="primary"
                outlined
                minimal
                loading={isLoading}
                disabled={!isNeedUpdateProfile && isPartialMatch && !selectedPatientId}
                onClick={
                  isNeedUpdateProfile
                    ? handleCancel
                    : isPartialMatch
                      ? handleAssignPatientToInsurance
                      : handleCancel
                }
              >
                {isNeedUpdateProfile
                  ? t('cancel')
                  : isPartialMatch
                    ? t('assignToThisPatient')
                    : t('cancel')}
              </Button>
              {!isNeedUpdateProfile && !isPartialMatch && isCreateShown && (
                <Button
                  intent="primary"
                  outlined
                  minimal
                  loading={isLoading}
                  onClick={() => handlerOpenCreatePatient(activePatient)}
                >
                  {t('createPatient')}
                </Button>
              )}
              <Button
                loading={isLoading}
                onClick={
                  isNeedUpdateProfile
                    ? handlerCreatePatientV2
                    : isPartialMatch && !openSynchronise
                      ? () => handlerOpenCreatePatient(activePatient)
                      : handleUpdatePatientProfile
                }
                intent="primary"
              >
                {isNeedUpdateProfile
                  ? t('create')
                  : isPartialMatch && !openSynchronise
                    ? t('createPatient')
                    : t('updatePatientProfile')}
              </Button>
            </>
          );
        }}
      >
        {(activePatient?: PatientInfo) => {
          if (!activePatient) return null;

          const isPartialMatch =
            reviewPatientStore.cardReview?.patientCompareData.compareResult ===
            CompareStatus.PartialMatch;

          if (isNeedUpdateProfile || (selectedPatient && isOpenCreatePatient)) {
            return (
              <WrapperCreatePatientDialog>
                <CreatePatientBody
                  isOpen
                  onClose={() => {
                    setIsConfirmLeaveOpen(true);
                  }}
                  isLoadingPatient={false}
                  onToggleInsuranceList={onToggleInsuranceList}
                  initInsurances={initInsurances}
                  setInitInsurances={(value) => setInitInsurances(value)}
                  insuranceState={insuranceState}
                  createPatientDefaultValue={
                    reviewPatientStore.cardReview?.patientCompareData
                      ?.patientFromCard || reviewPatientStore.currentPatients
                  }
                  handlerUpdatePatientCard={handlerUpdatePatientCard}
                  isMobileCard={true}
                  ref={formikRef}
                  isChangeInsurance={isChangeInsurance}
                  isApplyForEab={false}
                />
              </WrapperCreatePatientDialog>
            );
          }
          if (isPartialMatch && !openSynchronise) {
            return (
              <ReviewPatientsDialogInsuranceNOTMatchBody
                activeGPRowId={selectedPatientId}
                onChange={(selectedPatient) =>
                  setSelectedPatientId(selectedPatient)
                }
              />
            );
          }

          if (
            reviewPatientStore.cardReview?.patientCompareData.compareResult !==
            CompareStatus.NotExist
          ) {
            const patientCompareData = reviewPatientStore.cardReview?.patientCompareData;
            // with insurance matched status, only 1 patient be matched
            const patientId = patientCompareData?.patientsMatched[0]?.patientId;

            return (
              <ReviewPatientsDialogInsuranceMatchBody
                patientId={openSynchronise ? selectedPatientId! : patientId!}
                patientCompareData={patientCompareData}
                onChange={(updatePatientPayload) =>
                  setNewPatientPayload(updatePatientPayload)
                }
              />
            );
          }
        }}
      </MultiPatientsDialogWrapper>

      {isConfirmLeaveOpen && (
        <LeaveConfirmModal
          isOpen
          onConfirm={() => {
            setOpenSynchonise(false);
            setSelectedPatient(undefined);
            setIsConfirmLeaveOpen(false);
          }}
          onClose={() => {
            setOpenSynchonise(false);
            setIsConfirmLeaveOpen(false);
          }}
        />
      )}

      {openInsuranceList && (
        <ListInsurancesModal
          scenario={{ action: InsuranceActionEnum.Create }}
          patient={formikRef.current?.getValue() as unknown as PatientProfileResponse}
          show={openInsuranceList}
          className="sl-ListInsurancesModal"
          onClose={onToggleInsuranceList}
          initInsurance={initInsurances}
          stateSelectInsurance={stageSelectInsurance!}
          onSubmit={onSaveNewInsurances}
        />
      )}
    </Container>
  );
};

export default memo(ReviewPatientsDialog);
