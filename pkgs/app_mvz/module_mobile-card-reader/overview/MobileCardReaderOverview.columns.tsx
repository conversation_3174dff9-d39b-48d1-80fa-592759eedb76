import React from 'react';

import type ErrorI18n from '@tutum/mvz/locales/en/ErrorCode.json';
import type MobileCardReaderI18n from '@tutum/mvz/locales/en/MobileCardReader.json';

import { BodyTextM, Flex, Svg, Tag } from '@tutum/design-system/components';
import { formatUnixToDateString } from '@tutum/design-system/infrastructure/utils';
import {
  MobileCardInfoModel,
  ImportStatus,
  SortField,
  TypeOfInsurance,
  CardInfo,
} from '@tutum/hermes/bff/card_raw_common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import { formatBirthday } from '@tutum/mvz/_utils/formatBirthday';
import {
  statusImportCanProcess,
  renderContent,
} from './MobileCardReader.helper';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { MainGroup, Order } from '@tutum/hermes/bff/common';
import {
  backgroundMainGroup,
  colorMainGroup,
} from '@tutum/mvz/_utils/scheinFormat';
import { COLOR } from '@tutum/design-system/themes/styles';

const MoreVerticalIconSvgURL = '/images/more-vertical.svg';
const UserIcon = '/images/user-icon.svg';
export const EditIconSvgURL = '/images/edit-value.svg';
export const TrashBinRedIcon = '/images/trash-bin-red.svg';
export const PlusGrayIcon = '/images/plus-gray.svg';
export const OpenListSchein = '/images/chevron-down.svg';
const ProcessIcon = '/images/refresh.svg';
const CopyIcon = '/images/copy-icon.svg';
const SortableIcon = '/images/sortable-icon.svg';
const SortAscIcon = '/images/sort-ascending.svg';
const SortDescIcon = '/images/sort-descending.svg';

const InsuranceExpiredError = ErrorCode.ErrorCode_CardReader_IK_Invalid_Expired;
const KVKInvalid = ErrorCode.ErrorCode_CardReader_SDKT_Vknr_Invalid;
const InsuranceStartDateAfterNow =
  ErrorCode.ErrorCode_CardReader_Insurance_StartDate_After_Current_Date;
const InsuranceEndDateBeforeNow =
  ErrorCode.ErrorCode_CardReader_Insurance_EndDate_Before_Current_Date;

const isShowCopyForm = [
  KVKInvalid as string,
  InsuranceEndDateBeforeNow as string,
  InsuranceStartDateAfterNow as string,
];

export const renderSortIcon = (
  sortType: {
    field: SortField;
    order: '' | Order;
  },
  sortField: SortField
) => {
  if (sortType?.field !== sortField) return SortableIcon;
  if (sortType?.order === Order.ASC) return SortAscIcon;
  return SortDescIcon;
};

const renderInformationPatient = (t, row: CardInfo) => {
  if (!row) return '';

  return (
    <Flex>
      <strong>
        {row.insuranceType === TypeOfInsurance.Public ? (
          <span className="sl-MobileCardReader-table-cell-patient__public">
            {renderContent('K')}
          </span>
        ) : (
          <span className="sl-MobileCardReader-table-cell-patient__private">
            {renderContent('P')}
          </span>
        )}
        {renderContent(' ')}
      </strong>
      &nbsp;{renderContent('•')}&nbsp;
      <span className="sl-MobileCardReader-table-cell-patient__dob">
        {formatBirthday(t, row.patient?.dateOfBirth)}
      </span>
      {row?.patient?.gender ? (
        <span className="sl-MobileCardReader-table-cell-patient__dob">
          &nbsp;{renderContent('•')}&nbsp;
          {row?.patient?.gender}
        </span>
      ) : null}
    </Flex>
  );
};

export default function genColumnDefinitions(
  t: IFixedNamespaceTFunction<keyof typeof MobileCardReaderI18n>,
  tError: IFixedNamespaceTFunction<keyof typeof ErrorI18n>,
  {
    onCreateSchein,
    onRemovePatient,
    onGoToPatientProfile,
    onReviewPatient,
    onOpenPageCardInformation,
    onProcess,
    handleSort,
    sortType,
  }: {
    onCreateSchein: (row: MobileCardInfoModel, typeSchein: string) => void;
    onRemovePatient: (record: string) => void;
    onGoToPatientProfile: (id: string) => void;
    onReviewPatient: (id: MobileCardInfoModel) => void;
    onOpenPageCardInformation: (row: MobileCardInfoModel) => void;
    onProcess: (id: string) => void;
    handleSort: (field: SortField) => void;
    sortType: { field: SortField; order: '' | Order };
  }
): Array<IDataTableColumn<MobileCardInfoModel>> {
  return [
    {
      selector: (row) => row.mobileCardInfo?.cardInfo?.readingDate,
      name: (
        <Flex justify="space-between">
          <span style={{ paddingTop: 3 }}>{t('readingDate')}</span>
          <Svg
            src={renderSortIcon(sortType, SortField.ReadingDate)}
            style={{ cursor: 'pointer', marginLeft: 6, width: 18 }}
            onClick={() => handleSort(SortField.ReadingDate)}
          />
        </Flex>
      ),
      format(row) {
        return row.mobileCardInfo?.cardInfo?.readingDate
          ? formatUnixToDateString(row.mobileCardInfo.cardInfo.readingDate)
          : '';
      },
    },
    {
      name: (
        <Flex justify="space-between">
          <span style={{ paddingTop: 3 }}>{t('patient')}</span>
          <Svg
            src={renderSortIcon(sortType, SortField.LastName)}
            style={{ cursor: 'pointer', marginLeft: 6, width: 18 }}
            onClick={() => handleSort(SortField.LastName)}
          />
        </Flex>
      ),
      cell(row) {
        return (
          <div className="sl-MobileCardReader-table-cell-patient">
            <span className="sl-MobileCardReader-table-cell-patient__name">
              {renderContent(
                `${row?.mobileCardInfo.cardInfo.patient?.lastName}, ${row?.mobileCardInfo.cardInfo.patient?.firstName}`
              )}
            </span>
            {renderInformationPatient(t, row.mobileCardInfo.cardInfo)}
          </div>
        );
      },
    },
    {
      selector: (row) => row.mobileCardInfo?.cardInfo?.insuranceName,
      width: '400px',
      style: {
        minHeight: 'fit-content',
        whiteSpace: 'normal',
      },
      conditionalCellStyles: [
        {
          when: () => true,
          style: {
            minHeight: 'fit-content',
            whiteSpace: 'normal',
          },
        },
      ],
      name: t('insuranceName'),
    },
    {
      name: t('ikNumber'),
      cell(row) {
        return (
          <div className="sl-MobileCardReader-table-cell-patient">
            <span className="sl-MobileCardReader-table-cell-patient__name">
              {row.mobileCardInfo?.cardInfo?.iKNumber !== 0
                ? row.mobileCardInfo?.cardInfo?.iKNumber
                : ''}
            </span>
          </div>
        );
      },
    },
    {
      name: t('regNumber'),
      cell(row) {
        return (
          <div className="sl-MobileCardReader-table-cell-patient">
            <span className="sl-MobileCardReader-table-cell-patient__name">
              {row.mobileCardInfo?.registrationNumber
                ? row.mobileCardInfo?.registrationNumber
                : ''}
            </span>
          </div>
        );
      },
    },
    {
      name: (
        <Flex justify="space-between">
          <span style={{ paddingTop: 3 }}>{t('status')}</span>
          <Svg
            src={renderSortIcon(sortType, SortField.Status)}
            style={{ cursor: 'pointer', marginLeft: 6, width: 18 }}
            onClick={() => handleSort(SortField.Status)}
          />
        </Flex>
      ),
      cell(row) {
        if (
          row.mobileCardInfo.cardInfo.importStatus ===
          ImportStatus.ImportStatus_ReviewRequired
        ) {
          return (
            <Tag
              className="sl-MobileCardReader-table-cell-status__tag"
              slState="error"
              slSize="regular"
              slStyle="fill"
            >
              {t('statusReviewRequired')}
            </Tag>
          );
        }

        if (
          row.mobileCardInfo.cardInfo.importStatus ===
          ImportStatus.ImportStatus_PatientCreated
        ) {
          return (
            <Tag
              className="sl-MobileCardReader-table-cell-status__tag"
              slState="positive"
              slSize="regular"
              slStyle="fill"
            >
              {t('statusPatientCreated')}
            </Tag>
          );
        }

        if (
          row.mobileCardInfo.cardInfo.importStatus ===
          ImportStatus.ImportStatus_PatientUpdated
        ) {
          return (
            <Tag
              className="sl-MobileCardReader-table-cell-status__tag"
              slState="positive"
              slSize="regular"
              slStyle="fill"
            >
              {t('statusPatientUpdated')}
            </Tag>
          );
        }

        if (
          row.mobileCardInfo.cardInfo.importStatus ===
          ImportStatus.ImportStatus_Processed
        ) {
          return (
            <Tag
              className="sl-MobileCardReader-table-cell-status__tag"
              slState="positive"
              slSize="regular"
              slStyle="fill"
            >
              {t('statusPatientProcessed')}
            </Tag>
          );
        }

        if (
          row.mobileCardInfo.cardInfo.importStatus ===
          ImportStatus.ImportStatus_NewData
        ) {
          return (
            <Tag
              className="sl-MobileCardReader-table-cell-status__tag"
              slState="info"
              slSize="regular"
              slStyle="fill"
            >
              {t('statusNewData')}
            </Tag>
          );
        }

        // special case for warning
        if (row.errorCode === InsuranceExpiredError) {
          return (
            <div className="sl-MobileCardReader-table-cell-status--warning">
              <Svg
                className="sl-MobileCardReader-table-cell-status--warning__icon"
                src="/images/info-solid-warn.svg"
              />
              <span>{t('insuranceExpired')}</span>
            </div>
          );
        }

        // special case for error need to show
        if (row.errorCode !== '') {
          return (
            <div className="sl-MobileCardReader-table-cell-status--error">
              <Svg
                className="sl-MobileCardReader-table-cell-status--error__icon"
                src="/images/alert-triangle-24.svg"
              />
              <span>{tError(row.errorCode as any)}</span>
            </div>
          );
        }

        return (
          <div className="sl-MobileCardReader-table-cell-status--error">
            <Svg
              className="sl-MobileCardReader-table-cell-status--error__icon"
              src="/images/alert-triangle-24.svg"
            />
            <span>{t('statusFailedToProcess')}</span>
          </div>
        );
      },
    },
    {
      name: '',
      width: '32px',
      style: { paddingRight: 0 },
      cell(row) {
        if (
          (row.mobileCardInfo.cardInfo.importStatus ===
            ImportStatus.ImportStatus_ImportedFailure ||
            row.mobileCardInfo.cardInfo.importStatus ===
            ImportStatus.ImportStatus_NewData) &&
          !isShowCopyForm.includes(row.errorCode) &&
          row.errorCode !== InsuranceExpiredError
        ) {
          return (
            <Flex w="100%" align="center" justify="center">
              <Svg
                className="sl-icon"
                src="/images/trash-bin.svg"
                style={{ cursor: 'pointer' }}
                onClick={() => onRemovePatient(row.id)}
              />
            </Flex>
          );
        }

        const menu = (
          <Menu className="action-menu">
            <MenuItem
              text={t('actionReviewLabel')}
              hidden={
                row.mobileCardInfo.cardInfo.importStatus !==
                ImportStatus.ImportStatus_ReviewRequired
              }
              icon={<Svg src={EditIconSvgURL} />}
              onClick={() => {
                onReviewPatient(row);
              }}
            />
            <MenuItem
              text={t('actionGoToPatientProfileLabel')}
              hidden={
                !statusImportCanProcess.includes(
                  row.mobileCardInfo.cardInfo.importStatus
                )
              }
              icon={<Svg src={UserIcon} />}
              onClick={() => {
                onGoToPatientProfile(
                  row?.mobileCardInfo.cardInfo?.patientId || ''
                );
              }}
            />
            <MenuItem
              text={t('actionCreateScheinLabel')}
              icon={<Svg src={OpenListSchein} />}
              hidden={
                !statusImportCanProcess.includes(
                  row.mobileCardInfo.cardInfo.importStatus
                )
              }
            >
              <MenuItem
                onClick={() => onCreateSchein(row, MainGroup.KV)}
                text={
                  <Flex
                    gap={8}
                    pb={8}
                    style={{ cursor: 'pointer' }}
                    data-test-id="btn-create-schein-kv"
                  >
                    <Flex
                      p={2}
                      className="main-group"
                      style={{
                        justifyContent: 'center',
                        borderRadius: '4px',
                        alignItems: 'center',
                        width: '24px',
                        fontSize: '11px',
                        lineHeight: '16px',
                        backgroundColor: backgroundMainGroup(MainGroup.KV),
                        color: colorMainGroup(MainGroup.KV),
                      }}
                    >
                      {renderContent('KV')}
                    </Flex>
                    <div className="text-lbl">{t('lblNewKvSchein')}</div>
                  </Flex>
                }
                hidden={
                  row.mobileCardInfo.cardInfo.insuranceType ===
                  TypeOfInsurance.Private
                }
              />
              <MenuItem
                onClick={() => onCreateSchein(row, MainGroup.PRIVATE)}
                text={
                  <Flex
                    gap={8}
                    pb={8}
                    style={{ cursor: 'pointer' }}
                    data-test-id="btn-create-schein-private"
                  >
                    <Flex
                      p={2}
                      className="main-group"
                      style={{
                        justifyContent: 'center',
                        borderRadius: '4px',
                        alignItems: 'center',
                        width: '24px',
                        fontSize: '11px',
                        lineHeight: '16px',
                        backgroundColor: backgroundMainGroup(MainGroup.PRIVATE),
                        color: colorMainGroup(MainGroup.PRIVATE),
                      }}
                    >
                      {renderContent('P')}
                    </Flex>
                    <div className="text-lbl">{t('lblNewPrivateSchein')}</div>
                  </Flex>
                }
              />
              <MenuItem
                onClick={() => onCreateSchein(row, MainGroup.IGEL)}
                text={
                  <Flex
                    gap={8}
                    pb={8}
                    style={{ cursor: 'pointer' }}
                    data-test-id="btn-create-schein-igel"
                  >
                    <Flex
                      p={2}
                      className="main-group"
                      style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: '4px',
                        width: '24px',
                        fontSize: '11px',
                        lineHeight: '16px',
                        backgroundColor: backgroundMainGroup(MainGroup.IGEL),
                        color: colorMainGroup(MainGroup.IGEL),
                      }}
                    >
                      {renderContent('IG')}
                    </Flex>
                    <div className="text-lbl">{t('lblNewIgelSchein')}</div>
                  </Flex>
                }
              />
            </MenuItem>
            <MenuItem
              text={t('process')}
              hidden={row.errorCode !== InsuranceExpiredError}
              icon={<Svg src={ProcessIcon} />}
              onClick={() => {
                onProcess(row.id);
              }}
            />
            <MenuItem
              text={t('copyInformation')}
              hidden={!isShowCopyForm.includes(row.errorCode)}
              icon={<Svg src={CopyIcon} />}
              onClick={() => {
                onOpenPageCardInformation(row);
              }}
            />
            <MenuItem
              text={
                <BodyTextM color={COLOR.TEXT_NEGATIVE}>
                  {t('actionRemoveLabel')}
                </BodyTextM>
              }
              icon={<Svg src={TrashBinRedIcon} />}
              onClick={() => {
                onRemovePatient(row.id);
              }}
            />
          </Menu>
        );

        return (
          <Popover content={menu} placement="right-end">
            <Svg style={{ cursor: 'pointer' }} src={MoreVerticalIconSvgURL} />
          </Popover>
        );
      },
    },
  ];
}
