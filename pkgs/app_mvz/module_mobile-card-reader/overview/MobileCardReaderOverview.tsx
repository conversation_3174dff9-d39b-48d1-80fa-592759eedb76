import Router from 'next/router';
import { useState, useMemo, useEffect } from 'react';
import isEmpty from 'lodash/isEmpty';

import type MobileCardReaderI18n from '@tutum/mvz/locales/en/MobileCardReader.json';
import {
  MobileCardInfoModel,
  ImportStatus,
  SortField,
} from '@tutum/hermes/bff/legacy/card_raw_common';
import i18n from '@tutum/infrastructure/i18n';
import {
  Switch,
  Spinner,
  SpinnerSize,
  Popover,
} from '@tutum/design-system/components/Core';
import Table, { IDataTableStyles } from '@tutum/design-system/components/Table';
import {
  BodyTextL,
  BodyTextM,
  Button,
  Flex,
  H1,
  Svg,
} from '@tutum/design-system/components';
import { TransferDialog } from '@tutum/mvz/module_mobile-card-reader/transfer-dialog';
import CreateScheinDialog from '@tutum/mvz/module_mobile-card-reader/create-schein-dialog/CreateSchein';
import {
  removeAllRawResult,
  useQueryGetRawResult,
  useMutationImportRawResult,
  useMutationRemoveRawResult,
  importRawResult,
} from '@tutum/hermes/bff/legacy/app_mvz_card_raw';
import { StyledContainer } from './MobileCardReaderOverview.styled';
import genColumnDefinitions, {
  EditIconSvgURL,
  TrashBinRedIcon,
  OpenListSchein,
} from './MobileCardReaderOverview.columns';
import { useReviewPatientStore } from '../review-patient-dialog/useReviewPatientStore';
import ReviewPatientsDialog from '../review-patient-dialog/ReviewPatientDialog';
import {
  useMobileCardReaderStore,
  MobileCardReaderOverviewAction,
  MobileCardReaderOverviewStore,
} from './MobileCardReaderOverview.store';
import { ROUTING } from '../../types/route.type';
import {
  PAGINATION_DEFAULT
} from '@tutum/design-system/consts/table';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import {
  alertError,
  alertSuccessfully,
  alertWarning,
} from '@tutum/design-system/components/Toaster';
import {
  statusImportCanProcess,
  renderContent,
} from './MobileCardReader.helper';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { useErrorCodeI18n } from '@tutum/mvz/hooks/useErrorCode';
import { MainGroup, Order } from '@tutum/hermes/bff/common';
import {
  backgroundMainGroup,
  colorMainGroup,
} from '@tutum/mvz/_utils/scheinFormat';
import { TypeOfInsurance } from '@tutum/hermes/bff/patient_profile_common';
import { useMutationStartReadingMobileCardReader } from '@tutum/hermes/bff/legacy/app_mvz_card_raw';
import {
  useListenMobileCardChanged,
  useListenImportRawMobileCardChanged,
  EventImportRawMobileCardChanged,
} from '@tutum/hermes/bff/app_mvz_card_raw';
import { BASE_PATH_MVZ } from '@tutum/infrastructure/utils/string.util';
import { COLOR } from '@tutum/design-system/themes/styles';

const TABLE_STYLES: IDataTableStyles = {
  headCells: {
    style: {
      padding: '8px',
      width: 'max-content',
    },
  },
  headRow: {
    style: {
      padding: '0px 8px',
    },
  },
  rows: {
    style: {
      padding: '0px 8px',
      minHeight: 'fit-content',
    },
  },
  cells: {
    style: {
      padding: '8px',
    },
  },
  pagination: {
    style: {
      position: 'fixed',
      bottom: 0,
      height: 36,
      fontSize: 11,
      justifyContent: 'flex-start',
      minHeight: 'unset',
    },
  },
};

export default function MobileCardReaderOverview() {
  const { t } = i18n.useTranslation<keyof typeof MobileCardReaderI18n>({
    namespace: 'MobileCardReader',
  });
  const { t: tTransfer } = i18n.useTranslation<
    keyof typeof MobileCardReaderI18n.dialogTransfer
  >({
    namespace: 'MobileCardReader',
    nestedTrans: 'dialogTransfer',
  });

  const tError = useErrorCodeI18n();

  const {
    isOpenTransfer,
    isOpenCreateSchein,
    isFilterReviewOnly,
    selectedNumber,
    selectedRows,
  } = useMobileCardReaderStore();
  const [reviewPatientStore, reviewPatientStoreActions] =
    useReviewPatientStore();

  const [openReviewDialog, setOpenReviewDialog] = useState(false);
  const [pagination, setPagination] = useState(PAGINATION_DEFAULT);
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [isRemoveAllProcess, setIsRemoveAllProcess] = useState(false);
  const [toggleCleared, setToggleCleared] = useState(false);
  const [patientActiveIndex, setPatientActiveIndex] = useState(0);
  const [idSelectDelete, setIdSelectDelete] = useState<string | null>(null);
  const [sortType, setSortType] = useState<{ field: SortField; order: Order }>({
    field: SortField.ReadingDate,
    order: Order.ASC,
  });
  const [scheinSelect, setScheinSelect] = useState<MainGroup | null>(null);
  const [openListSchein, setOpenListSchein] = useState(false);
  const [isPending, setIsPending] = useState(false);

  // handle error or import one patient
  useListenMobileCardChanged((response) => {
    const { errorCode, rawIds } = response;
    const hasData = rawIds && rawIds.length > 0;

    if (errorCode) {
      const errorMessage = {
        title: tTransfer(errorCode === '6200' ? 'noData' : 'titleFailed'),
        errorCode: tTransfer(errorCode as any),
      };
      MobileCardReaderOverviewAction.setFinishReadData(true, errorMessage);
    }

    if (hasData) {
      refetch();
      doImportRawResult(rawIds);
      if (!isPending) {
        MobileCardReaderOverviewAction.setOpenTransfer(false);
      }
    }
  });

  useListenImportRawMobileCardChanged(
    (response: EventImportRawMobileCardChanged) => {
      const { errorCode } = response;
      if (errorCode) {
        alertError(tError(errorCode as any));
      }

      setIsPending(false);
      MobileCardReaderOverviewAction.reset();
      setToggleCleared(!toggleCleared);
      refetch();
    }
  );

  const { isFetching, data, refetch } = useQueryGetRawResult({
    pagination: {
      page: pagination.page,
      pageSize: pagination.pageSize,
      order: sortType.order,
      sortBy: sortType.field,
    },
  });

  const { mutateAsync: readMobileDeviceFunc } =
    useMutationStartReadingMobileCardReader({
      onError: (err) => {
        MobileCardReaderOverviewAction.setOpenTransfer(false);
        alertError(tError(`${err.response.data.serverError}` as any));
        console.error(err);
      },
      throwOnError: false,
    });

  const { mutateAsync: removeRawResult } = useMutationRemoveRawResult({
    onSuccess: () => {
      alertSuccessfully(
        t('toastRemoveSuccess', {
          record:
            MobileCardReaderOverviewStore.selectedNumber > 0
              ? MobileCardReaderOverviewStore.selectedNumber
              : 1,
        })
      );
      refetch();
    },
    onError: () => {
      alertWarning(
        t('toastRemoveFailure', {
          record:
            MobileCardReaderOverviewStore.selectedNumber > 0
              ? MobileCardReaderOverviewStore.selectedNumber
              : 1,
        })
      );
      refetch();
    },
  });

  const doImportRawResult = (ids: string[]) => {
    setIsPending(true);
    importRawResult({ ids });
  };

  const onProcessRow = (id: string) => {
    doImportRawResult([id]);
  };

  const onCreateScheinOnMenu = (
    row: MobileCardInfoModel,
    typeSchein: MainGroup
  ) => {
    setScheinSelect(typeSchein);
    MobileCardReaderOverviewAction.setSelectedRows([row]);
    MobileCardReaderOverviewAction.setSelectedRow([row].length);
    MobileCardReaderOverviewAction.setRowSelectedFilter([row]);
    MobileCardReaderOverviewAction.getInformationSchein(row);
    MobileCardReaderOverviewAction.setOpenCreateSchein(true);
    MobileCardReaderOverviewAction.createSchein(true);
  };

  const onReviewPatientOnMenu = (row: MobileCardInfoModel) => {
    reviewPatientStoreActions.addPatientToReviewList(row);
    MobileCardReaderOverviewAction.setSelectedRows([row]);
    MobileCardReaderOverviewAction.setRowSelectedFilter([row]);
    MobileCardReaderOverviewAction.setSelectedRow([row].length);
    setOpenReviewDialog(true);
  };

  const onGotoPatientProfileOnMenu = (id: string) => {
    Router.push(`${ROUTING.PATIENTS}/${id}`);
  };

  const onRemovePatientByRow = async (id: string) => {
    await removeRawResult({ ids: [id] });
    setIdSelectDelete(null);
    setConfirmDialog(false);
  };

  const onRemovePatientOnMenu = (id: string) => {
    setIdSelectDelete(id);
    setConfirmDialog(true);
  };

  const onOpenPageCardInformationOnMenu = (row: MobileCardInfoModel) => {
    window.open(
      `${BASE_PATH_MVZ}/${ROUTING.CARD_INFORMATION}/${row.id}`,
      '_blank',
      'noopener,noreferrer'
    );
  };

  const onSortOnMenu = (field: SortField) => {
    const sort = {
      field,
      order:
        sortType?.field !== field
          ? Order.DESC
          : sortType?.order === Order.ASC
            ? Order.DESC
            : Order.ASC,
    };
    setSortType(sort);
  };

  const onChangePage = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination({
      ...pagination,
      pageSize: currentRowsPerPage,
    });
  };

  const onStopTransfer = () => {
    MobileCardReaderOverviewAction.setOpenTransfer(false);
  };

  const handlerCloseCreateSchein = () => {
    setPatientActiveIndex(0);
    setOpenListSchein(false);
    MobileCardReaderOverviewAction.clear();
    MobileCardReaderOverviewAction.setOpenCreateSchein(false);
  };

  const handlerCreateSchein = () => {
    // choose one raw select create
    if (MobileCardReaderOverviewStore.rowSelectedFilter.length === 1) {
      MobileCardReaderOverviewAction.reset();
      setOpenListSchein(false);
      MobileCardReaderOverviewAction.setOpenCreateSchein(false);
      setPatientActiveIndex(0);
      setToggleCleared(!toggleCleared);
    } else {
      // logic calculator active index patient in header
      let nextPatient = patientActiveIndex;
      if (
        MobileCardReaderOverviewStore.rowSelectedFilter.length ===
        patientActiveIndex + 1
      ) {
        nextPatient = patientActiveIndex - 1;
      }
      const patientActiveCreateSchein =
        MobileCardReaderOverviewStore.rowSelectedFilter[patientActiveIndex];
      const listPatientRemain =
        MobileCardReaderOverviewStore.rowSelectedFilter.filter(
          (patient) => patient.id !== patientActiveCreateSchein.id
        );
      MobileCardReaderOverviewAction.getInformationSchein(
        listPatientRemain[nextPatient]
      );
      MobileCardReaderOverviewAction.setRowSelectedFilter(listPatientRemain);
      setPatientActiveIndex(nextPatient);
      const activeNumber =
        MobileCardReaderOverviewStore.activePatient ===
          MobileCardReaderOverviewStore.rowSelectedFilter.length
          ? MobileCardReaderOverviewStore.activePatient - 1
          : MobileCardReaderOverviewStore.activePatient;
      MobileCardReaderOverviewAction.setActivePatient(activeNumber);
    }
  };

  const removeAllRawResults = () => {
    removeAllRawResult().then(() => {
      setConfirmDialog(false);
      MobileCardReaderOverviewAction.reset();
      setToggleCleared(!toggleCleared);
      refetch();
    });
    alertSuccessfully(
      t('toastRemoveSuccess', {
        record: data?.mobileCardInfoModels.filter((item) =>
          statusImportCanProcess.includes(
            item.mobileCardInfo.cardInfo.importStatus
          )
        ).length,
      })
    );
  };

  const removeBySelectingRowsAction = async () => {
    const arrayIds =
      MobileCardReaderOverviewStore.selectedRows.reduce<string[]>((arrId, current) => {
        arrId.push(current.id);
        return arrId;
      }, []);
    await removeRawResult({ ids: arrayIds });
    MobileCardReaderOverviewAction.reset();
    setToggleCleared(!toggleCleared);
    setConfirmDialog(false);
  };

  const onConfirmRemoveRecords = () => {
    if (isRemoveAllProcess) {
      setIsRemoveAllProcess(false);
      removeAllRawResults();
    } else if (idSelectDelete) {
      onRemovePatientByRow(idSelectDelete);
    } else {
      removeBySelectingRowsAction();
    }
  };

  const hanlderNavigateCreatePatient = () => {
    if (MobileCardReaderOverviewStore.rowSelectedFilter.length === 1) {
      MobileCardReaderOverviewAction.reset();
      refetch();
      setOpenReviewDialog(false);
      setPatientActiveIndex(0);
      setToggleCleared(!toggleCleared);
    } else {
      // logic calculator active index patient in header
      let nextPatient = patientActiveIndex;
      if (
        MobileCardReaderOverviewStore.rowSelectedFilter.length ===
        patientActiveIndex + 1
      ) {
        nextPatient = patientActiveIndex - 1;
      }
      const patientActive =
        MobileCardReaderOverviewStore.rowSelectedFilter[patientActiveIndex];
      const listPatientRemain =
        MobileCardReaderOverviewStore.rowSelectedFilter.filter(
          (patient) => patient.id !== patientActive.id
        );

      MobileCardReaderOverviewAction.setRowSelectedFilter(listPatientRemain);
      setPatientActiveIndex(nextPatient);
      reviewPatientStoreActions.addPatientToReviewList(
        listPatientRemain[nextPatient]
      );
      const activeNumber =
        MobileCardReaderOverviewStore.activePatient ===
          MobileCardReaderOverviewStore.rowSelectedFilter.length
          ? MobileCardReaderOverviewStore.activePatient - 1
          : MobileCardReaderOverviewStore.activePatient;
      MobileCardReaderOverviewAction.setActivePatient(activeNumber);
    }
  };

  const EmptyState = () => (
    <BodyTextM className="sl-MobileCardReader-table__empty-text">
      {t('noRecord')}
    </BodyTextM>
  );

  const onClickCreateScheinOutMenu = (typeSchein: MainGroup) => {
    setScheinSelect(typeSchein);
    const firstPatientCreateSchein = selectedRows.find((patient) =>
      statusImportCanProcess.includes(
        patient.mobileCardInfo.cardInfo?.importStatus
      )
    );
    const patientHasCreateSchein = selectedRows.filter((patient) =>
      statusImportCanProcess.includes(
        patient.mobileCardInfo.cardInfo?.importStatus
      )
    );
    MobileCardReaderOverviewAction.setRowSelectedFilter(patientHasCreateSchein);
    MobileCardReaderOverviewAction.getInformationSchein(
      firstPatientCreateSchein!
    );
    MobileCardReaderOverviewAction.setOpenCreateSchein(true);
    setOpenListSchein(false);
    MobileCardReaderOverviewAction.createSchein(true);
  };

  const LabelCreateSchein = useMemo(() => {
    const isAllowReview = selectedRows.some(
      (item) =>
        item.mobileCardInfo.cardInfo?.importStatus ===
        ImportStatus.ImportStatus_ReviewRequired
    );
    const isAllowCreateSchein = selectedRows?.some((item) =>
      statusImportCanProcess.includes(
        item.mobileCardInfo.cardInfo?.importStatus
      )
    );

    const isAllowProcess = selectedRows?.some(
      (item) =>
        item.errorCode === ErrorCode.ErrorCode_CardReader_IK_Invalid_Expired
    );
    return (
      <>
        {selectedRows.length != 0 ? (
          <Flex className="sl-MobileCardReader-panel__btn-group">
            <>
              {isAllowProcess && (
                <Button
                  disabled={
                    !data?.mobileCardInfoModels?.length
                  }
                  outlined={
                    !!data?.mobileCardInfoModels?.length
                  }
                  onClick={() =>
                    doImportRawResult(selectedRows.map((row) => row.id))
                  }
                  icon={
                    isPending ? null : (
                      <Svg width={16} height={16} src="/images/refresh.svg" />
                    )
                  }
                >
                  {isPending ? (
                    <Spinner size={SpinnerSize.SMALL} />
                  ) : (
                    t('process')
                  )}
                </Button>
              )}
              {isAllowReview && (
                <Button
                  outlined
                  icon={<Svg width={16} height={16} src={EditIconSvgURL} />}
                  onClick={() => {
                    const firstPatientNeedReview = selectedRows.find(
                      (patient) =>
                        patient.mobileCardInfo.cardInfo.importStatus ===
                        ImportStatus.ImportStatus_ReviewRequired
                    );

                    reviewPatientStoreActions.addPatientToReviewList(
                      firstPatientNeedReview!
                    );
                    const patientHasStatusReview = selectedRows.filter(
                      (patient) =>
                        patient.mobileCardInfo.cardInfo.importStatus ===
                        ImportStatus.ImportStatus_ReviewRequired
                    );
                    MobileCardReaderOverviewAction.setRowSelectedFilter(
                      patientHasStatusReview
                    );
                    setOpenReviewDialog(true);
                  }}
                >
                  {t('actionReviewLabel')}
                </Button>
              )}
            </>
            <>
              {isAllowCreateSchein && (
                <Popover
                  popoverClassName="list-schein-popover"
                  placement="bottom"
                  data-test-id="pop-up-list-schein"
                  isOpen={openListSchein}
                  content={
                    <Flex column p="8px 16px" gap={8}>
                      {selectedRows &&
                        !selectedRows.some(
                          (row) =>
                            row.mobileCardInfo.cardInfo.insuranceType ===
                            TypeOfInsurance.Private
                        ) && (
                          <Flex
                            gap={8}
                            pb={8}
                            style={{ cursor: 'pointer' }}
                            onClick={() =>
                              onClickCreateScheinOutMenu(MainGroup.KV)
                            }
                            data-test-id="btn-create-schein-kv"
                          >
                            <Flex
                              p={2}
                              className="main-group"
                              style={{
                                justifyContent: 'center',
                                borderRadius: '4px',
                                alignItems: 'center',
                                width: '24px',
                                fontSize: '11px',
                                lineHeight: '16px',
                                backgroundColor: backgroundMainGroup(
                                  MainGroup.KV
                                ),
                                color: colorMainGroup(MainGroup.KV),
                              }}
                            >
                              {renderContent('KV')}
                            </Flex>
                            <div className="text-lbl">
                              {t('lblNewKvSchein')}
                            </div>
                          </Flex>
                        )}
                      <Flex
                        gap={8}
                        pb={8}
                        style={{ cursor: 'pointer' }}
                        onClick={() =>
                          onClickCreateScheinOutMenu(MainGroup.PRIVATE)
                        }
                        data-test-id="btn-create-schein-private"
                      >
                        <Flex
                          p={2}
                          className="main-group"
                          style={{
                            justifyContent: 'center',
                            borderRadius: '4px',
                            alignItems: 'center',
                            width: '24px',
                            fontSize: '11px',
                            lineHeight: '16px',
                            backgroundColor: backgroundMainGroup(
                              MainGroup.PRIVATE
                            ),
                            color: colorMainGroup(MainGroup.PRIVATE),
                          }}
                        >
                          {renderContent('P')}
                        </Flex>
                        <div className="text-lbl">
                          {t('lblNewPrivateSchein')}
                        </div>
                      </Flex>
                      <Flex
                        gap={8}
                        pb={8}
                        style={{ cursor: 'pointer' }}
                        onClick={() =>
                          onClickCreateScheinOutMenu(MainGroup.IGEL)
                        }
                        data-test-id="btn-create-schein-igel"
                      >
                        <Flex
                          p={2}
                          className="main-group"
                          style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderRadius: '4px',
                            width: '24px',
                            fontSize: '11px',
                            lineHeight: '16px',
                            backgroundColor: backgroundMainGroup(
                              MainGroup.IGEL
                            ),
                            color: colorMainGroup(MainGroup.IGEL),
                          }}
                        >
                          {renderContent('IG')}
                        </Flex>
                        <div className="text-lbl">{t('lblNewIgelSchein')}</div>
                      </Flex>
                    </Flex>
                  }
                  enforceFocus={false}
                  onClose={() => setOpenListSchein(false)}
                  canEscapeKeyClose
                >
                  <Button
                    outlined
                    onClick={() => setOpenListSchein(true)}
                    icon={<Svg width={16} height={16} src={OpenListSchein} />}
                  >
                    {t('actionCreateScheinLabel')}
                  </Button>
                </Popover>
              )}
            </>
            <Button
              intent="danger"
              outlined
              icon={
                <Svg
                  width={16}
                  height={16}
                  color="white"
                  src={TrashBinRedIcon}
                />
              }
              onClick={() => setConfirmDialog(true)}
            >
              {t('actionRemoveLabel')}
            </Button>
          </Flex>
        ) : (
          <Switch
            className="sl-MobileCardReader-panel__switch"
            label={t('showReviewDataOnly')}
            checked={isFilterReviewOnly}
            onChange={() => {
              MobileCardReaderOverviewAction.toggleFilterReviewOnly();
            }}
          />
        )}
      </>
    );
  }, [data, isFilterReviewOnly, selectedRows, openListSchein]);

  const onReadMobileDevice = async () => {
    MobileCardReaderOverviewAction.setOpenTransfer(true);
    await readMobileDeviceFunc({});
  };

  const LabelReviewPatient = useMemo(() => {
    const isAllowProcess = data?.mobileCardInfoModels?.some((item) =>
      statusImportCanProcess.includes(item.mobileCardInfo.cardInfo.importStatus)
    );
    return (
      <>
        {selectedRows?.length != 0 ? (
          <div className="item-selected">
            {t('itemSelected', { number: selectedNumber })}
          </div>
        ) : (
          <Flex className="sl-MobileCardReader-panel__btn-group">
            <Button
              outlined={isAllowProcess}
              disabled={!isAllowProcess}
              icon={<Svg width={16} height={16} src="/images/trash-bin.svg" />}
              onClick={() => {
                setIsRemoveAllProcess(true);
                setConfirmDialog(true);
                refetch();
              }}
            >
              {t('removeDataTransfered')}
            </Button>
            <Button
              disabled={!data?.mobileCardInfoModels?.length}
              outlined={!!data?.mobileCardInfoModels?.length}
              onClick={() => doImportRawResult(null!)}
              icon={
                isPending ? null : (
                  <Svg width={16} height={16} src="/images/refresh.svg" />
                )
              }
            >
              {isPending ? (
                <Spinner size={SpinnerSize.SMALL} />
              ) : (
                t('processes')
              )}
            </Button>
            <Button
              intent="primary"
              icon={
                <Svg
                  width={16}
                  height={16}
                  src="/images/device-smartphone.svg"
                />
              }
              onClick={() => onReadMobileDevice()}
            >
              {t('readMobileDevice')}
            </Button>
          </Flex>
        )}
      </>
    );
  }, [selectedRows, data, isPending]);

  const onSelectedRowChange = (selectedRowState: {
    allSelected: boolean;
    selectedCount: number;
    selectedRows: MobileCardInfoModel[];
  }) => {
    MobileCardReaderOverviewAction.setSelectedRow(
      selectedRowState.selectedCount
    );
    MobileCardReaderOverviewAction.setSelectedRows(
      selectedRowState.selectedRows
    );
  };

  return (
    <StyledContainer>
      <Flex
        align="center"
        className="sl-Header_Section"
        justify="space-between"
      >
        <H1>{t('title')}</H1>
      </Flex>
      <Flex className="sl-MobileCardReader-panel">
        {LabelCreateSchein}
        <Flex grow={1} />
        {LabelReviewPatient}
      </Flex>
      <div className="sl-MobileCardReader-table-container">
        <Table
          className="sl-MobileCardReader-table"
          noHeader
          persistTableHead
          pagination
          selectableRows
          sortServer={true}
          selectableRowDisabled={(row: MobileCardInfoModel) =>
            row.mobileCardInfo.cardInfo.importStatus === 'ImportedFailure' &&
            row.errorCode !==
            ErrorCode.ErrorCode_CardReader_IK_Invalid_Expired
          }
          clearSelectedRows={toggleCleared}
          onSelectedRowsChange={onSelectedRowChange}
          conditionalRowStyles={[
            {
              when(row: MobileCardInfoModel) {
                return selectedRows.find((r) => r.id === row.id) != null;
              },
              style: {
                backgroundColor: COLOR.INFO_SECONDARY_PRESSED,
                '&:hover': {
                  backgroundColor: COLOR.INFO_SECONDARY_PRESSED,
                  outline: 'none',
                },
              },
            },
          ]}
          noDataComponent={<EmptyState />}
          customStyles={TABLE_STYLES}
          progressPending={isFetching}
          data={
            data ?
              isFilterReviewOnly
                ? data.mobileCardInfoModels.filter(
                  (record) =>
                    record.mobileCardInfo.cardInfo.importStatus ===
                    ImportStatus.ImportStatus_ReviewRequired
                )
                : data.mobileCardInfoModels
              : []
          }
          isCustom
          columns={genColumnDefinitions(t, tError, {
            onCreateSchein: onCreateScheinOnMenu,
            onRemovePatient: onRemovePatientOnMenu,
            onGoToPatientProfile: onGotoPatientProfileOnMenu,
            onReviewPatient: onReviewPatientOnMenu,
            onOpenPageCardInformation: onOpenPageCardInformationOnMenu,
            onProcess: onProcessRow,
            handleSort: onSortOnMenu,
            sortType: sortType,
          })}
          paginationServer
          paginationDefaultPage={pagination.page}
          paginationResetDefaultPage
          paginationTotalRows={data?.paginationResponse.total}
          paginationPerPage={pagination.pageSize}
          onChangePage={onChangePage}
          onChangeRowsPerPage={onChangeRowsPerPage}
        />
      </div>

      {openReviewDialog && !isEmpty(reviewPatientStore.cardReview) && (
        <ReviewPatientsDialog
          patients={reviewPatientStore?.currentPatients}
          onAction={hanlderNavigateCreatePatient}
          onClose={() => {
            // Update behavior from close modal review but need to recheck with Brwa
            refetch();
            MobileCardReaderOverviewAction.reset();
            reviewPatientStoreActions.clearPatientToReviewList();
            setToggleCleared(!toggleCleared);
            setPatientActiveIndex(0);
            setOpenReviewDialog(false);
          }}
          activePatientIndex={patientActiveIndex}
          setActivePatientIndex={setPatientActiveIndex}
        />
      )}

      <TransferDialog isOpen={isOpenTransfer} onStopTransfer={onStopTransfer} />

      {isOpenCreateSchein && (
        <CreateScheinDialog
          isOpen={isOpenCreateSchein}
          className="create-schein"
          onCreateScheinMobileCard={handlerCreateSchein}
          onClose={handlerCloseCreateSchein}
          setPatientActiveIndex={setPatientActiveIndex}
          activePatientIdx={patientActiveIndex}
          typeSchein={scheinSelect!}
        />
      )}

      <DeleteConfirmDialog
        className="confirm-dialog"
        isOpen={confirmDialog}
        close={() => {
          setIdSelectDelete(null);
          setIsRemoveAllProcess(false);
          setConfirmDialog(false);
        }}
        confirm={onConfirmRemoveRecords}
        text={{
          btnCancel: t('btnNo'),
          btnOk: t('btnRemove'),
          title: isRemoveAllProcess
            ? t('titleConfirmDialog')
            : t('titleConfirmDialogV2'),
          message: t('contentConfirmDialog'),
        }}
      />
    </StyledContainer>
  );
}
