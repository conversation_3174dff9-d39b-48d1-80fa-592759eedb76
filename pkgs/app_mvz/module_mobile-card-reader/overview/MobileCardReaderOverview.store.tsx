import { proxy, useSnapshot } from 'valtio';

import PatientManagementService from '@tutum/mvz/module_patient-management/PatientManagement.service';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import type { IPatientManagement } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { MobileCardInfoModel } from '@tutum/hermes/bff/legacy/card_raw_common';

interface IMCardReaderOverviewAction {
  setOpenTransfer: (isOpen: boolean) => void;
  setOpenCreateSchein: (isOpen: boolean) => void;
  toggleFilterReviewOnly: () => void;
  setFinishReadData: (
    isFinish: boolean,
    errorCode: { title: string; errorCode: string }
  ) => void;
  setSelectedRow: (row: number) => void;
  setSelectedRows: (row: MobileCardInfoModel[]) => void;
  createSchein: (row: boolean) => void;
  getInformationSchein: (row: MobileCardInfoModel) => void;
  clear: () => void;
  setActivePatient: (active: number) => void;
  setRowSelectedFilter: (rows: MobileCardInfoModel[]) => void;
  reset: () => void;
}
interface IMCardReaderOverviewStore {
  mobileCardReaderRecords: MobileCardInfoModel[];
  isOpenTransfer: boolean;
  isFinishReadData: boolean;
  error: { title: string; errorCode: string };
  isOpenCreateSchein: boolean;
  isFilterReviewOnly: boolean;
  isLoading: boolean;
  selectedNumber: number;
  selectedRows: MobileCardInfoModel[];
  isCreateSchein: boolean;
  patientManagement: IPatientManagement;
  activePatient: number;
  rowSelectedFilter: MobileCardInfoModel[];
}

const INIT_PATIENT_MANAGER: IPatientManagement = {
  patient: undefined,
  patientId: { value: '' },
  ikNumber: 0,
  loadingPatient: false,
  availableHzvContracts: [],
  availableFavContracts: [],
  selectedContractDoctor: {
    bsnrId: undefined,
    doctorId: undefined,
    contractId: undefined,
    chargeSystemId: undefined,
    availableDoctor: [],
    encounterCase: undefined,
  },
  readyToUsePatientParticipationData: false,
  getPatientParticipationResponse: {
    participations: [],
  },
  activeParticipations: [],
  isShowHzvButton: false,
  indicatorActiveIngredients: [],
  showHintVSST785: false,
  pznAtcForHighPrescriptions: [],
};

const initStore: IMCardReaderOverviewStore = {
  mobileCardReaderRecords: [],
  isOpenTransfer: false,
  isOpenCreateSchein: false,
  isFilterReviewOnly: false,
  isFinishReadData: false,
  error: { title: '', errorCode: '' },
  isLoading: false,
  selectedNumber: 1,
  selectedRows: [],
  isCreateSchein: false,
  patientManagement: INIT_PATIENT_MANAGER,
  activePatient: 0,
  rowSelectedFilter: [],
};

export const MobileCardReaderOverviewStore =
  proxy<IMCardReaderOverviewStore>(initStore);

export const MobileCardReaderOverviewAction: IMCardReaderOverviewAction = {
  setOpenTransfer: (isOpen: boolean) => {
    MobileCardReaderOverviewStore.isFinishReadData = false;
    MobileCardReaderOverviewStore.isOpenTransfer = isOpen;
  },
  setFinishReadData: (
    isFinish: boolean,
    errorCode: { title: string; errorCode: string }
  ) => {
    MobileCardReaderOverviewStore.isFinishReadData = isFinish;
    MobileCardReaderOverviewStore.error = errorCode;
  },
  setOpenCreateSchein: (isOpen: boolean) => {
    MobileCardReaderOverviewStore.isOpenCreateSchein = isOpen;
  },
  toggleFilterReviewOnly: () => {
    const { isFilterReviewOnly } = MobileCardReaderOverviewStore;
    const nextFilterState = !isFilterReviewOnly;

    MobileCardReaderOverviewStore.isFilterReviewOnly = nextFilterState;
  },
  setSelectedRow: (row: number) => {
    MobileCardReaderOverviewStore.selectedNumber = row;
  },
  setSelectedRows: (rows: MobileCardInfoModel[]) => {
    MobileCardReaderOverviewStore.selectedRows = rows;
  },
  createSchein: (isCreate: boolean) => {
    MobileCardReaderOverviewStore.isCreateSchein = isCreate;
  },
  getInformationSchein: (row: MobileCardInfoModel) => {
    const patientId = row.mobileCardInfo.cardInfo?.patientId || '';
    Promise.all([
      PatientManagementService.getPatientProfileById(patientId),
    ]).then(([patient]) => {
      MobileCardReaderOverviewStore.patientManagement.patient = patient!;
      patientFileActions.patient.setCurrent(patient!);
      MobileCardReaderOverviewStore.patientManagement.ikNumber =
        row.mobileCardInfo.cardInfo.iKNumber;

      if (MobileCardReaderOverviewStore.patientManagement.patientId) {
        MobileCardReaderOverviewStore.patientManagement.patientId.value =
          row.mobileCardInfo.cardInfo.patientId!;
      }
    });
    patientFileActions.schein.getScheinsOverview(
      row?.mobileCardInfo.cardInfo?.patientId || ''
    );
  },
  clear: () => {
    MobileCardReaderOverviewStore.isCreateSchein = false;
    MobileCardReaderOverviewStore.patientManagement = INIT_PATIENT_MANAGER;
    MobileCardReaderOverviewStore.activePatient = 0;
    MobileCardReaderOverviewStore.rowSelectedFilter = [];
    MobileCardReaderOverviewStore.selectedNumber = 0;
  },
  reset: () => {
    MobileCardReaderOverviewStore.isCreateSchein = false;
    MobileCardReaderOverviewStore.patientManagement = INIT_PATIENT_MANAGER;
    MobileCardReaderOverviewStore.activePatient = 0;
    MobileCardReaderOverviewStore.rowSelectedFilter = [];
    MobileCardReaderOverviewStore.selectedNumber = 0;
    MobileCardReaderOverviewStore.selectedRows = [];
  },
  setActivePatient: (active: number) => {
    MobileCardReaderOverviewStore.activePatient = active;
  },
  setRowSelectedFilter: (rows: MobileCardInfoModel[]) => {
    MobileCardReaderOverviewStore.rowSelectedFilter = rows;
  },
};

export function useMobileCardReaderStore() {
  return useSnapshot(MobileCardReaderOverviewStore);
}
