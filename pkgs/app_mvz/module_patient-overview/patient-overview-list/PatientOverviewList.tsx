import moment from 'moment';
import { NextRouter, withRouter } from 'next/router';
import { memo, useEffect, useRef, useState } from 'react';

import {
  Avatar,
  BodyTextM,
  BodyTextS,
  Flex,
  Svg,
  Tag,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Intent,
  Popover,
  PopoverInteractionKind,
  Position,
  Tooltip,
} from '@tutum/design-system/components/Core';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { TableActionItem } from '@tutum/design-system/components/Table/TableAction/TableAction';
import TableAction from '@tutum/design-system/components/Table/TableAction/TableAction.styled';
import {
  getCssClass,
  toDateFormat,
  toShortCase,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  ContactStatus,
  PatientContract,
  PatientEnrollment,
  PatientOverview,
  VerahRelatedIcd,
} from '@tutum/hermes/bff/app_mvz_patient_overview';
import { Pagination } from '@tutum/hermes/bff/common';
import { SubmitUhu35Status } from '@tutum/hermes/bff/legacy/service_domains_patient_overview';
import { PatientEnrollmentStatus } from '@tutum/hermes/bff/service_domains_enrollment';
import { DoctorFunctionType } from '@tutum/hermes/bff/service_domains_patient_participation';
import I18n, {
  IFixedNamespaceTFunction,
  II18nFixedNamespace,
} from '@tutum/infrastructure/i18n';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import {
  default as DatetimeUtil,
  default as datetimeUtil,
} from '@tutum/infrastructure/utils/datetime.util';
import PatientOverviewUtil from '@tutum/infrastructure/utils/form.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import { formatBirthday } from '@tutum/mvz/_utils/formatBirthday';
import { getShortPatientType } from '@tutum/mvz/_utils/patientType';
import type PatientOverviewI18n from '@tutum/mvz/locales/en/PatientOverview.json';
import { handleShowPatientNumber } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import { getPatientPostActionByStatus } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.hook';
import { IMvzTheme, IMvzThemeProps } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { ColumnSetting } from '../PatientOverview.service';

const deathIcon = '/images/death-alt3.svg';
const CopyIcon = '/images/content-editor-copy.svg';
const UserIcon = '/images/user-gray.svg';
const ResubmitIcon = '/images/upload.svg';
const readCardIcon = '/images/patient/selector/read-card-icon.svg';

const heightIcon = 16;
const weightIcon = 16;
const BASE_WIDTH = 1440;

export interface IPatientOverviewListProps {
  className?: string;
  theme?: IMvzTheme;
  mapEmployee: Map<string, IEmployeeProfile>;
  patientOverviews?: PatientOverview[];
  isLoading: boolean;
  pagination: Pagination;
  totalItems: number;
  onSorting: () => void;
  mapShowColumn: ColumnSetting;
  totalWidthColumn: number;
  warningMessage?: string;
  onResubmitEnrollment: (selectedPatient: PatientOverview) => void;
  onChangePage: (page: number) => void;
  onChangeRowsPerPage: (currentRowsPerPage: number) => void;
}

const timeNowMillisecond = DatetimeUtil.now();
const beginQuarterMillisecond =
  DatetimeUtil.getStartOfQuarter(timeNowMillisecond).unix();

const AlertCircle = '/images/alert-circle-solid.svg';
const CheckCircle = '/images/check-circle.svg';
const AlertTriangle = '/images/alert-triangle.svg';
const FormSigned1 = '/images/1-form.svg';
const FormSignedHalf = '/images/1:2-form.svg';
const FormSigned2 = '/images/2-forms.svg';

const parseContractStatus = (
  contract: PatientContract,
  t: IFixedNamespaceTFunction<string>
) => {
  const statusCheck = contract.status;

  switch (statusCheck) {
    case ContactStatus.ContactStatus_Active:
      return (
        <Flex className="contractStatusWrap">
          <Flex className="contractStatus contractActive">
            <span>{t('contractActive')}</span>
            <Svg src={'/images/arrow-up-right.svg'} size={10} />
          </Flex>
          <BodyTextS>
            {datetimeUtil.dateTimeFormat(contract.startDate, DATE_FORMAT)}
            {contract.endDate
              ? ` - ${datetimeUtil.dateTimeFormat(contract.endDate, DATE_FORMAT)}`
              : ''}
          </BodyTextS>
        </Flex>
      );
    case ContactStatus.ContactStatus_Requested:
      return (
        <Flex className="contractStatusWrap">
          <Flex className="contractStatus contractPending">
            <span>{t('contractPending')}</span>
            <Svg src={'/images/arrow-up-right.svg'} size={10} />
          </Flex>
          <BodyTextS>
            {/* {datetimeUtil.dateTimeFormat(contract.updatedDate, DATE_FORMAT)} */}
            {datetimeUtil.dateTimeFormat(contract.startDate, DATE_FORMAT)}
            {contract.endDate
              ? ` - ${datetimeUtil.dateTimeFormat(contract.endDate, DATE_FORMAT)}`
              : ''}
          </BodyTextS>
        </Flex>
      );
    case ContactStatus.ContactStatus_Terminated:
      return (
        <Flex className="contractStatusWrap">
          <Flex className="contractStatus contractTerminated">
            <span>{t('contractTerminated')}</span>
            <Svg src={'/images/arrow-up-right.svg'} size={10} />
          </Flex>
          <BodyTextS>
            {datetimeUtil.dateTimeFormat(contract.startDate, DATE_FORMAT)}
            {contract.endDate
              ? ` - ${datetimeUtil.dateTimeFormat(contract.endDate, DATE_FORMAT)}`
              : ''}
          </BodyTextS>
        </Flex>
      );
    default:
      return <div>--</div>;
  }
};

const parseEnrollmentStatus = (
  enrollment: PatientEnrollment | undefined,
  contract: PatientContract | undefined,
  t: IFixedNamespaceTFunction<string>
) => {
  const contractStatus = contract?.status;
  let statusCheck = enrollment?.status;
  const errMessage = enrollment?.message;

  const faultyErrorMessage = (
    <Flex className="tooltipContent">
      {errMessage?.map((message, index) => {
        return (
          <BodyTextS key={`error_content_showing_${index}`}>
            {message}
          </BodyTextS>
        );
      })}
    </Flex>
  );

  // Override enrollment status if contract is active
  switch (contractStatus) {
    case ContactStatus.ContactStatus_Active:
      statusCheck = PatientEnrollmentStatus.PatientEnrollmentStatus_Active;
      break;
    case ContactStatus.ContactStatus_Requested:
      statusCheck = PatientEnrollmentStatus.PatientEnrollmentStatus_Requested;
      break;
    case ContactStatus.ContactStatus_Terminated:
      statusCheck = PatientEnrollmentStatus.PatientEnrollmentStatus_Terminated;
      break;
    case ContactStatus.ContactStatus_Cancelled:
      statusCheck = PatientEnrollmentStatus.PatientEnrollmentStatus_Cancelled;
      break;
    default:
  }

  switch (statusCheck) {
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Active:
      return (
        <Flex className="enrollmentStatus">
          <span>{t('enrollmentActive')}</span>
          <Svg src={'/images/arrow-up-right.svg'} size={10} />
        </Flex>
      );
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Cancelled:
      return null;
      // return (
      //   <Flex className="enrollmentStatus">
      //     <span>{t('enrollmentCancelled')}</span>
      //     <Svg src={'/images/arrow-up-right.svg'} size={10} />
      //   </Flex>
      // );
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Requested:
      return (
        <Flex className="enrollmentStatus">
          <span>{t('enrollmentSubmitted')}</span>
          <Svg src={'/images/arrow-up-right.svg'} size={10} />
        </Flex>
      );
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Created:
      return (
        <Flex className="enrollmentStatus">
          <span>{t('enrollmentCreated')}</span>
          <Svg src={'/images/arrow-up-right.svg'} size={10} />
        </Flex>
      );
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Faulty:
      return (
        <Flex className="contractStatusWrap">
          <Flex className="enrollmentStatus enrollmentFaulty">
            <Svg src={AlertTriangle} />
            <span>{t('enrollmentFaulty')}</span>
          </Flex>
          {faultyErrorMessage}
        </Flex>
      );
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Printed:
      return (
        <Flex className="enrollmentStatus">
          <span>{t('enrollmentPrinted')}</span>
          <Svg src={'/images/arrow-up-right.svg'} size={10} />
        </Flex>
      );
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Terminated:
      return null;
      // return (
      //   <Flex className="enrollmentStatus">
      //     <span>{t('enrollmentTerminated')}</span>
      //     <Svg src={'/images/arrow-up-right.svg'} size={10} />
      //   </Flex>
      // );
    default:
      return <div>--</div>;
  }
};

const parseSubmitPreEnrollmentStatus = (
  status: SubmitUhu35Status,
  t: IFixedNamespaceTFunction<string>
) => {
  switch (status) {
    case SubmitUhu35Status.SubmitUhu35Status_Success:
      return (
        <Tag
          slState={'positive'}
          intent={Intent.SUCCESS}
          slIcon={<Svg src={'/images/check-circle-solid-1.svg'} size={10} />}
        >
          {t('success')}
        </Tag>
      );
    case SubmitUhu35Status.SubmitUhu35Status_Failed:
      return (
        <Tag
          slState={'warning'}
          intent={Intent.DANGER}
          slIcon={<Svg src={'/images/alert-triangle-solid.svg'} size={10} />}
        >
          {t('fail')}
        </Tag>
      );
    case SubmitUhu35Status.SubmitUhu35Status_NotSubmit:
      return (
        <Tag
          slState="neutral"
          intent={Intent.NONE}
          slIcon={<Svg src={'/images/clock.svg'} size={10} />}
        >
          {t('fail')}
        </Tag>
      );
    default:
      return undefined;
  }
};

const parseMenuICDs = (
  icds: VerahRelatedIcd[] | undefined,
  t: IFixedNamespaceTFunction<string>
) => {
  if (!icds || !icds.length) {
    return null;
  }

  return (
    <Flex column>
      {icds.map((icd) => {
        return (
          <Flex key={'menu-' + icd.id} className="icd-row">
            <Flex className="icd-code">{icd.code}</Flex>
            <Flex column className="icd-detail">
              <Flex className="icd-description">{icd.description}</Flex>
              <Flex className="icd-date">
                {`${toDateFormat(new Date(icd.createdDate), {
                  dateFormat: 'dd.MM.yyyy',
                })} • ${
                  icd.chronic ? t('permanentDiagnose') : t('acuteDiagnose')
                }`}
              </Flex>
            </Flex>
          </Flex>
        );
      })}
    </Flex>
  );
};

const parseICDs = (icds?: VerahRelatedIcd[]) => {
  if (!icds || !icds.length) {
    return '-';
  }

  const [firstData, secondData, ...others] = icds.filter((icd) => {
    return (
      (!icd.chronic && icd.createdDate / 1000 > beginQuarterMillisecond) ||
      (icd.chronic && icd.validUntil / 1000 > timeNowMillisecond) ||
      (icd.chronic && !icd.validUntil)
    );
  });

  return (
    <Flex>
      {[firstData, secondData].map((icd) => {
        return (
          <Flex
            key={icd?.id}
            className={icd?.chronic ? 'icd-code-bold' : 'icd-code'}
          >
            {icd?.code}
          </Flex>
        );
      })}
      <Flex className="icd-sum">+{others.length}</Flex>
    </Flex>
  );
};

function PatientOverviewListMemo(
  props: IPatientOverviewListProps &
    IMvzThemeProps & { router: NextRouter } & II18nFixedNamespace<
      keyof typeof PatientOverviewI18n.PatientOverviewList
    >
) {
  const patientOverviewListRef = useRef<HTMLDivElement>(null);
  const {
    className,
    t,
    patientOverviews,
    isLoading,
    mapEmployee,
    mapShowColumn,
    warningMessage,
    pagination,
    totalItems,
    onResubmitEnrollment,
    onChangePage,
    onChangeRowsPerPage,
  } = props;
  const NoResultPatientOverview = '/images/search-patient-overview.svg';
  const [mapWidth, setMapWidth] = useState<Map<string, string>>(new Map());
  const onCopyData = (teId: string) => {
    if (typeof window !== 'undefined') {
      navigator.clipboard.writeText(teId);
    }
    alertSuccessfully(t('copySuccess'));
  };

  const genColumn = (key: string, column: ColumnSetting) => {
    switch (key) {
      case 'patientId':
        return {
          id: 'patientId',
          minWidth: mapWidth.get('widthID'),
          maxWidth: column.maxWidth || mapWidth.get('widthID'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('patientNumber')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              className={getCssClass('attribute-col sl-patient-overview-row')}
              gap={8}
            >
              {handleShowPatientNumber(patientOverview.patientNumber)}
            </Flex>
          ),
        };
      case 'patientName':
        return {
          id: 'patientName',
          minWidth: mapWidth.get('widthPatient'),
          maxWidth: column.maxWidth || mapWidth.get('widthPatient'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('patientName')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => {
            const dobText = formatBirthday(t, patientOverview.dateOfBirth);
            const typeOfInsuranceText = toShortCase(
              getShortPatientType(patientOverview.patientType)
            );
            const gender = patientOverview.gender;

            return (
              <Flex
                className={getCssClass('attribute-col sl-patient-overview-row')}
                gap={8}
                w="100%"
              >
                {!!patientOverview.dateOfDeath && (
                  <>
                    <div className="sl-death-icon">
                      <Svg src={deathIcon} width={20} />
                    </div>
                  </>
                )}
                <Flex column w="100%">
                  <Flex className="fullNamePatient">
                    <Flex
                      onClick={() => {
                        props.router.push(
                          `/patients/${patientOverview.patientId}`
                        );
                      }}
                    >
                      {PatientOverviewUtil.getFullName(
                        patientOverview.title || '',
                        patientOverview.intendWord || '',
                        patientOverview.lastName,
                        patientOverview.firstName
                      )}
                    </Flex>
                    {isEVCase(
                      patientOverview.insuranceInfo,
                      DatetimeUtil.now(),
                      undefined
                    ) && (
                      <Tooltip content={t('notreadcard')} position="bottom">
                        <Svg
                          className="icon-card"
                          src={readCardIcon}
                          height={heightIcon}
                          width={weightIcon}
                        />
                      </Tooltip>
                    )}
                  </Flex>
                  <Flex className="dobPatient">
                    <BodyTextM>
                      <span style={{ fontWeight: '600' }}>
                        {typeOfInsuranceText}&nbsp;
                      </span>
                      •&nbsp;
                      {dobText} • {gender}
                    </BodyTextM>
                  </Flex>
                </Flex>
              </Flex>
            );
          },
        };
      case 'insuranceName':
        return {
          id: 'insuranceName',
          minWidth: mapWidth.get('widthInsuranceName'),
          maxWidth: column.maxWidth || mapWidth.get('widthInsuranceName'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('insuranceName')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              className={getCssClass('attribute-col sl-patient-overview-row')}
              column
            >
              {patientOverview.insureName}
              {patientOverview.ikNumber !== 0 && (
                <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                  {t('ikText', {
                    value: patientOverview.ikNumber,
                  })}{' '}
                </BodyTextS>
              )}
            </Flex>
          ),
        };
      case 'insuranceNumber':
        return {
          id: 'insuranceNumber',
          minWidth: mapWidth.get('widthInsuranceNo'),
          maxWidth: column.maxWidth || mapWidth.get('widthInsuranceNo'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('insuranceNo')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              className={getCssClass('attribute-col sl-patient-overview-row')}
              gap={8}
            >
              {patientOverview.insureNumber}
            </Flex>
          ),
        };
      case 'ikNumber':
        return {
          id: 'ikNumber',
          minWidth: mapWidth.get('widthIkNumber'),
          maxWidth: column.maxWidth || mapWidth.get('widthIkNumber'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('ikNumber')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              className={getCssClass('attribute-col sl-patient-overview-row')}
              gap={8}
            >
              {patientOverview.ikNumber}
            </Flex>
          ),
        };
      case 'phoneNumber':
        return {
          id: 'phoneNumber',
          minWidth: mapWidth.get('widthPhoneNumber'),
          maxWidth: column.maxWidth || mapWidth.get('widthPhoneNumber'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('phoneNumber')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              className={getCssClass('attribute-col sl-patient-overview-row')}
              gap={8}
            >
              {patientOverview.phoneNumber}
            </Flex>
          ),
        };
      case 'address':
        return {
          id: 'address',
          minWidth: mapWidth.get('widthAddress'),
          maxWidth: column.maxWidth || mapWidth.get('widthAddress'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('address')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              className={getCssClass('attribute-col sl-patient-overview-row')}
              gap={8}
            >
              {patientOverview.address}
            </Flex>
          ),
        };
      case 'preEnrollmentStatus':
        return {
          id: 'preEnrollmentStatus',
          minWidth: mapWidth.get('widthUHUSubmissionDate'),
          maxWidth: column.maxWidth || mapWidth.get('widthUHUSubmissionDate'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('preEnrollmentStatus')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              column
              className={getCssClass('attribute-col sl-patient-overview-row')}
            >
              {patientOverview?.patientContracts.map((contract) =>
                parseSubmitPreEnrollmentStatus(contract.submitUhu35Status, t)
              )}
            </Flex>
          ),
        };
      case 'uhu35SubmissionDate':
        return {
          id: 'uhu35SubmissionDate',
          minWidth: mapWidth.get('widthUHUSubmissionDate'),
          maxWidth: column.maxWidth || mapWidth.get('widthUHUSubmissionDate'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('uhu35SubmissionDate')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              column
              className={getCssClass('attribute-col sl-patient-overview-row')}
            >
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  moment(contract.uhu35SubmissionDate).isValid() &&
                  moment(contract.uhu35SubmissionDate).format(DATE_FORMAT)
                );
              })}
            </Flex>
          ),
        };
      case 'contract':
        return {
          id: 'contract',
          minWidth: mapWidth.get('widthContract'),
          maxWidth: column.maxWidth || mapWidth.get('widthContract'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('contract')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              column
              className={getCssClass('attribute-col sl-patient-overview-row')}
            >
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex
                    key={contract.contractId}
                    align="center"
                    style={{
                      minHeight: '40px',
                    }}
                  >
                    {contract?.contractName || '-'}
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'status':
        return {
          id: 'status',
          minWidth: mapWidth.get('widthStatus'),
          maxWidth: column.maxWidth || mapWidth.get('widthStatus'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('status')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              column
              className={getCssClass('attribute-col sl-patient-overview-row')}
            >
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex
                    key={`${contract.contractId}_${contract.status}`}
                    className="attribute-col sl-patient-overview-row sl-patient-overview-row-contract-info"
                    w="100%"
                    column
                  >
                    <Flex
                      align="center"
                      style={{
                        minHeight: '40px',
                        cursor: 'pointer',
                        width: '100%',
                      }}
                      onClick={() => {
                        const { contractId, status } = contract;
                        if (!status) {
                          return;
                        }
                        const action = getPatientPostActionByStatus(status);
                        const pathname = `/patients/${patientOverview.patientId}`;
                        props.router.push(
                          { pathname, query: { contractId, action } },
                          pathname
                        );
                      }}
                    >
                      {parseContractStatus(contract, t)}
                      {/* {contract.isBilledFaulty && <Svg src={triangleIcon} />} */}
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'enrollmentStatus':
        return {
          id: 'enrollmentStatus',
          minWidth: mapWidth.get('widthEnrollmentStatus'),
          maxWidth: column.maxWidth || mapWidth.get('widthEnrollmentStatus'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('enrollmentStatus')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              column
              className={getCssClass('attribute-col sl-patient-overview-row')}
            >
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex
                    key={`${contract.contractId}_${contract.status}`}
                    className="attribute-col sl-patient-overview-row sl-patient-overview-row-contract-info"
                    w="100%"
                  >
                    <Flex
                      style={{
                        minHeight: '40px',
                        cursor: 'pointer',
                        justifyContent: 'center',
                      }}
                      column
                      onClick={() => {
                        const { contractId, status } = contract;
                        if (!status) {
                          return;
                        }
                        const action = getPatientPostActionByStatus(status);
                        const pathname = `/patients/${patientOverview.patientId}`;
                        props.router.push(
                          { pathname, query: { contractId, action } },
                          pathname
                        );
                      }}
                    >
                      {parseEnrollmentStatus(
                        contract?.patientEnrollment || undefined,
                        contract || undefined,
                        t
                      )}
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'signed':
        return {
          id: 'signed',
          minWidth: mapWidth.get('widthSigned'),
          maxWidth: column.maxWidth || mapWidth.get('widthSigned'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('signed')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => {
            const signed = patientOverview?.patientContracts?.filter(
              (contract) => contract.isSignaturesDone
            );

            if (!signed?.[0]) {
              return null;
            }

            let src = FormSigned1;
            let content = t('formSigned1');

            if (patientOverview?.patientContracts?.length > 1) {
              src =
                signed.length === 1
                  ? FormSignedHalf
                  : signed.length > 1
                    ? FormSigned2
                    : FormSigned1;
              content =
                signed.length === 1
                  ? t('formSignedHalf')
                  : signed.length > 1
                    ? t('formSigned2')
                    : t('formSigned1');
            }

            return (
              <Flex
                className={getCssClass('attribute-col sl-patient-overview-row')}
                gap={8}
              >
                <Tooltip
                  usePortal={false}
                  content={<Flex w="max-content">{content}</Flex>}
                  position={Position.BOTTOM}
                >
                  <Svg src={src} />
                </Tooltip>
              </Flex>
            );
          },
        };
      case 'startDate':
        return {
          id: 'startDate',
          minWidth: mapWidth.get('widthStartDate'),
          maxWidth: column.maxWidth || mapWidth.get('widthStartDate'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('contractStartDate')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex column>
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex
                    key={`${contract.contractId}_${contract.startDate}`}
                    className="attribute-col sl-patient-overview-row"
                  >
                    <Flex
                      style={{
                        minHeight: '40px',
                      }}
                    >
                      {contract.startDate && contract.startDate > 0
                        ? toDateFormat(new Date(contract.startDate), {
                            dateFormat: 'dd.MM.yyyy',
                          })
                        : '-'}
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'endDate':
        return {
          id: 'endDate',
          minWidth: mapWidth.get('widthEndDate'),
          maxWidth: column.maxWidth || mapWidth.get('widthEndDate'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('contractEndDate')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex column>
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex
                    key={`${contract.contractId}_${contract.endDate}`}
                    className="attribute-col sl-patient-overview-row"
                  >
                    <Flex
                      style={{
                        minHeight: '40px',
                      }}
                    >
                      {contract.endDate && contract.endDate > 0
                        ? toDateFormat(new Date(contract.endDate), {
                            dateFormat: 'dd.MM.yyyy',
                          })
                        : '-'}
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'doctor':
        return {
          id: 'doctor',
          minWidth: mapWidth.get('widthDoctor'),
          maxWidth: column.maxWidth || mapWidth.get('widthDoctor'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('doctor')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex column className="attribute-col sl-patient-overview-row">
              {patientOverview?.patientContracts?.map((contract) => {
                const doctorProfile = contract.doctorId
                  ? mapEmployee?.get(contract.doctorId)
                  : undefined;

                return (
                  <Flex key={contract.contractId}>
                    {doctorProfile ? (
                      <Tooltip
                        usePortal={false}
                        content={
                          <Flex column>
                            <Flex className="tooltipDoctorName">
                              {contract.doctorFunctionType ==
                              DoctorFunctionType.DoctorFunctionTypeCustodian
                                ? t('custodianDoctor')
                                : t('deputyDoctor')}
                            </Flex>
                            {doctorProfile.mediId && (
                              <Flex className="tooltipDoctorId">
                                {t('mediId')} : {doctorProfile.mediId}
                              </Flex>
                            )}
                            {doctorProfile.havgId && (
                              <Flex className="tooltipDoctorId">
                                {t('havgID')} : {doctorProfile.havgId}
                              </Flex>
                            )}
                          </Flex>
                        }
                        position={Position.BOTTOM}
                      >
                        <Flex>
                          <Avatar
                            size="small"
                            initial={doctorProfile.initial}
                            className="avatar"
                          />
                          <Flex column>
                            <Flex>
                              {nameUtils.getDoctorName(doctorProfile)}
                            </Flex>
                            <Flex>
                              {contract.doctorFunctionType &&
                                t(
                                  `${contract.doctorFunctionType.toLowerCase()}Doctor` as any
                                )}
                            </Flex>
                          </Flex>
                        </Flex>
                      </Tooltip>
                    ) : (
                      '-'
                    )}
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'teId':
        return {
          id: 'teId',
          minWidth: mapWidth.get('widthTeID'),
          maxWidth: column.maxWidth || mapWidth.get('widthTeID'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('teId')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex column className="attribute-col sl-patient-overview-row">
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex
                    key={`${contract.contractId}_${contract.teId}`}
                    align="center"
                    style={{
                      minHeight: '40px',
                    }}
                  >
                    {contract.teId && (
                      <>
                        <BodyTextM className="copy-text">
                          {contract.teId}
                        </BodyTextM>
                        <Svg
                          className="copy-icon"
                          src={CopyIcon}
                          onClick={() => onCopyData(contract.teId)}
                        />
                      </>
                    )}
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'enrolledBy':
        return {
          id: 'enrolledBy',
          minWidth: mapWidth.get('widthEnrollBy'),
          maxWidth: column.maxWidth || mapWidth.get('widthEnrollBy'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('enrolledBy')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex column className="attribute-col sl-patient-overview-row">
              {patientOverview?.patientContracts?.map((contract) => {
                const enrolledProfile = contract.enrolledBy
                  ? mapEmployee?.get(contract.enrolledBy)
                  : undefined;

                const shouldRender = contract.teId && enrolledProfile;
                return (
                  <Flex
                    key={`${contract.contractId}_${contract.enrolledBy}`}
                    style={{
                      minHeight: '40px',
                    }}
                  >
                    {shouldRender ? (
                      <Flex column>
                        <Flex>
                          {PatientOverviewUtil.getFullName(
                            enrolledProfile.title == '-'
                              ? ''
                              : enrolledProfile.title,
                            '',
                            enrolledProfile.lastName,
                            enrolledProfile.firstName,
                            true
                          )}
                        </Flex>
                        <Flex>
                          {toDateFormat(
                            contract.enrolledDate
                              ? new Date(contract.enrolledDate)
                              : new Date(),
                            {
                              dateFormat: 'dd.MM.yyyy',
                              timeFormat: 'hh:mm',
                            }
                          )}
                        </Flex>
                      </Flex>
                    ) : (
                      ''
                    )}
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'verah':
        return {
          id: 'verah',
          minWidth: mapWidth.get('widthVerah'),
          maxWidth: column.maxWidth || mapWidth.get('widthVerah'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('verah')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex column className="attribute-col sl-patient-overview-row">
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex key={contract.contractId}>
                    {contract.hasVerahService &&
                    contract.verahUpdatedDate &&
                    contract.verahUpdatedDate / 1000 >
                      beginQuarterMillisecond ? (
                      <Tooltip
                        content={t('verahHint')}
                        usePortal={false}
                        className="tooltipVerah"
                        position={Position.TOP}
                      >
                        <Svg src={CheckCircle} />
                      </Tooltip>
                    ) : (
                      '-'
                    )}
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'icdCodes':
        return {
          id: 'icdCodes',
          minWidth: mapWidth.get('widthICDs'),
          maxWidth: column.maxWidth || mapWidth.get('widthICDs'),
          sortable: false,
          name: (
            <Flex justify="space-between" align="center">
              {t('icdCodes')}
            </Flex>
          ),
          cell: (patientOverview: PatientOverview) => (
            <Flex
              column
              className="attribute-col sl-patient-overview-row sl-patient-overview-row-contract-info"
            >
              {patientOverview?.patientContracts?.map((contract) => {
                return (
                  <Flex key={contract.contractId} className="icd-group">
                    <Popover
                      usePortal={false}
                      position={Position.BOTTOM_RIGHT}
                      interactionKind={PopoverInteractionKind.HOVER}
                      content={
                        parseMenuICDs(contract.verahRelatedIcds, t) || undefined
                      }
                    >
                      {parseICDs(contract.verahRelatedIcds)}
                    </Popover>
                  </Flex>
                );
              })}
            </Flex>
          ),
        };
      case 'actionRow':
        return {
          id: 'actionRow',
          minWidth: mapWidth.get('widthActionRow'),
          maxWidth: column.maxWidth || mapWidth.get('widthActionRow'),
          sortable: false,
          cell: (patientOverview: PatientOverview) => {
            const onlyCreatedContractStatus =
              column.patientOnly ||
              patientOverview?.patientContracts?.some(
                (contract) =>
                  contract.status === ContactStatus.ContactStatus_Created
              );

            const actions: TableActionItem[] = [];

            actions.push({
              id: 'gotoPatientProfile',
              icon: <Svg src={UserIcon} />,
              label: t('goToPatientProfile'),
              onClick: () => {
                props.router.push(`/patients/${patientOverview.patientId}`);
              },
            });
            if (!onlyCreatedContractStatus) {
              actions.push({
                id: 'resubmitEnrollment',
                icon: <Svg src={ResubmitIcon} />,
                label: t('resubmitEnrollment'),
                onClick: () => onResubmitEnrollment(patientOverview),
              });
            }

            return <TableAction actions={actions} />;
          },
        };
      default:
        return {};
    }
  };

  const genColumns = (): IDataTableColumn<PatientOverview>[] => {
    return Object.entries(mapShowColumn)
      .sort((a, b) => a[1].index - b[1].index)
      .reduce((columns, datum) => {
        const [key, value] = datum;
        if (value.visible) {
          columns.push(
            genColumn(key, value as unknown as ColumnSetting) as any
          );
        }
        return columns;
      }, [] as IDataTableColumn<PatientOverview>[]);
  };

  useEffect(() => {
    const handleCalculateWidthColumns = () => {
      const eleWidth = patientOverviewListRef?.current?.offsetWidth || 0 + 48;
      const percent = eleWidth / BASE_WIDTH;
      const widthID = `${Math.floor(
        mapShowColumn['patientId'].fixWidth * percent
      )}px`;
      const widthPatient = `${Math.floor(
        mapShowColumn['patientName'].fixWidth * percent
      )}px`;
      const widthInsuranceNo = `${Math.floor(
        mapShowColumn['insuranceNumber'].fixWidth * percent
      )}px`;
      const widthIkNumber = `${Math.floor(
        mapShowColumn['ikNumber']?.fixWidth * percent
      )}px`;
      const widthInsuranceName = `${Math.floor(
        mapShowColumn['insuranceName']?.fixWidth * percent
      )}px`;
      const widthContract = `${Math.floor(
        mapShowColumn['contract']?.fixWidth * percent
      )}px`;
      const widthStatus = `${Math.floor(
        mapShowColumn['status']?.fixWidth * percent
      )}px`;
      const widthStartDate = `${Math.floor(
        mapShowColumn['contractStartDate']?.fixWidth * percent
      )}px`;
      const widthEndDate = `${Math.floor(
        mapShowColumn['contractEndDate']?.fixWidth * percent
      )}px`;
      const widthDoctor = `${Math.floor(
        mapShowColumn['doctor']?.fixWidth * percent
      )}px`;
      const widthEnrollBy = `${Math.floor(
        mapShowColumn['enrolledBy']?.fixWidth * percent
      )}px`;
      const widthVerah = `${Math.floor(
        mapShowColumn['verah']?.fixWidth * percent
      )}px`;
      const widthICDs = `${Math.floor(
        mapShowColumn['icdCodes']?.fixWidth * percent
      )}px`;
      const widthUHUSubmissionDate = `${Math.floor(
        mapShowColumn['uhu35SubmissionDate']?.fixWidth * percent
      )}px`;
      const widthPreEnrollmentStatus = `${Math.floor(
        mapShowColumn['preEnrollmentStatus']?.fixWidth * percent
      )}px`;
      const widthTeID = `${Math.floor(
        mapShowColumn['teId']?.fixWidth * percent
      )}px`;
      const widthSigned = `${Math.floor(
        mapShowColumn['signed']?.fixWidth * percent
      )}px`;
      const widthPhoneNumber = `${Math.floor(
        mapShowColumn['phoneNumber']?.fixWidth * percent
      )}px`;
      const widthAddress = `${Math.floor(
        mapShowColumn['address']?.fixWidth * percent
      )}px`;
      const widthActionRow = `${Math.floor(
        mapShowColumn['actionRow']?.fixWidth * percent
      )}px`;
      const widthEnrollmentStatus = `${Math.floor(
        mapShowColumn['enrollmentStatus']?.fixWidth * percent
      )}px`;
      const mapTempWidth: Map<string, string> = new Map();
      mapTempWidth.set('widthID', widthID);
      mapTempWidth.set('widthPatient', widthPatient);
      // mapTempWidth.set('widthMoreOption', widthMoreOption);
      mapTempWidth.set('widthICDs', widthICDs);
      mapTempWidth.set('widthVerah', widthVerah);
      mapTempWidth.set('widthEnrollBy', widthEnrollBy);
      mapTempWidth.set('widthDoctor', widthDoctor);
      mapTempWidth.set('widthEndDate', widthEndDate);
      mapTempWidth.set('widthStartDate', widthStartDate);
      mapTempWidth.set('widthStatus', widthStatus);
      mapTempWidth.set('widthContract', widthContract);
      mapTempWidth.set('widthInsuranceNo', widthInsuranceNo);
      mapTempWidth.set('widthIkNumber', widthIkNumber);
      mapTempWidth.set('widthInsuranceName', widthInsuranceName);
      mapTempWidth.set('widthUHUSubmissionDate', widthUHUSubmissionDate);
      mapTempWidth.set('widthPreEnrollmentStatus', widthPreEnrollmentStatus);
      mapTempWidth.set('widthTeID', widthTeID);
      mapTempWidth.set('widthSigned', widthSigned);
      mapTempWidth.set('widthPhoneNumber', widthPhoneNumber);
      mapTempWidth.set('widthAddress', widthAddress);
      mapTempWidth.set('widthActionRow', widthActionRow);
      mapTempWidth.set('widthEnrollmentStatus', widthEnrollmentStatus);
      setMapWidth(mapTempWidth);
    };

    const resizeObserver = new ResizeObserver((entries) => {
      for (const _entry of entries) {
        handleCalculateWidthColumns();
      }
    });

    handleCalculateWidthColumns();

    if (patientOverviewListRef?.current) {
      resizeObserver.observe(patientOverviewListRef?.current);
    }
  }, [mapShowColumn]);

  return (
    <Flex
      ref={patientOverviewListRef}
      auto
      className={getCssClass(className, {
        'has-pagination': totalItems > pagination.max,
        'has-warning-message': !!warningMessage,
      })}
      column
    >
      {warningMessage && (
        <Flex className="sl-patient-overview-message">
          <Svg src={AlertCircle} />
          {warningMessage}
        </Flex>
      )}
      <Table
        columns={genColumns()}
        data={patientOverviews || []}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        responsive={false}
        noDataComponent={
          <Flex justify="center" align="center" column h="100%">
            <Svg src={NoResultPatientOverview} />
            <Flex className="title-default-result">{t('noData')}</Flex>
            <Flex className="content-default-result">
              {t('defaultMessageNoResult')}
            </Flex>
          </Flex>
        }
        progressPending={isLoading}
        pagination={totalItems > pagination.max}
        paginationServer
        paginationDefaultPage={pagination.offset / pagination.max + 1}
        paginationResetDefaultPage
        paginationPerPage={pagination.max}
        paginationTotalRows={totalItems}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
      />
    </Flex>
  );
}

export default memo(
  withRouter(
    I18n.withTranslation(PatientOverviewListMemo, {
      namespace: 'PatientOverview',
      nestedTrans: 'PatientOverviewList',
    })
  )
);
