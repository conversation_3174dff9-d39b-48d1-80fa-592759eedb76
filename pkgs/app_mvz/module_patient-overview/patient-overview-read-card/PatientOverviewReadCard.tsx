import React, { useState, memo, ChangeEvent } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';

import { IMvzTheme, IMvzThemeProps } from '@tutum/mvz/theme';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientOverviewI18n from '@tutum/mvz/locales/en/PatientOverview.json';
import type CardReaderI18n from '@tutum/mvz/locales/en/CardReader.json';
import {
  Dialog,
  Radio,
  Button,
  Classes,
  Divider,
  H4,
} from '@tutum/design-system/components/Core';
import {
  getCssClass,
  formatUnixToDateString,
} from '@tutum/design-system/infrastructure/utils';
import { BodyTextM, Box, Flex } from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import { customStyles } from './PatientOverviewReadCard.styled';
import { isActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { PatientInfo } from '@tutum/hermes/bff/legacy/patient_profile_common';

export interface IPatientOverViewReadCardProps {
  className?: string;
  theme?: IMvzTheme;
}
export interface IPatientOverviewReadCard {
  isOpen: boolean;
  onCloseDialog: () => void;
  patients: PatientInfo[];
  onSelect: (cardHolder: PatientInfo) => void;
  onCreatePatient: () => void;
  patientFromCard?: PatientInfo;
}

type ByName = {
  type: 'name';
  lastName: string;
  firstName: string;
};

type ByDOB = {
  type: 'dob';
  dOB: number;
};

type INotMatching = {
  patientId: string;
  isAllow: boolean;
} & (ByName | ByDOB);

const PatientOverviewReadCard = (
  props: IPatientOverviewReadCard &
    IPatientOverViewReadCardProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof PatientOverviewI18n.ReadCard>
) => {
  const {
    t,
    className,
    isOpen,
    onCloseDialog,
    patients,
    patientFromCard,
    onSelect,
    onCreatePatient,
  } = props;
  const [selectCardHolder, setSelectCardHolder] =
    useState<PatientInfo | undefined>(undefined);

  const { t: tCard } = I18n.useTranslation<
    keyof typeof CardReaderI18n.CardReader.SelectPatientDialog
  >({
    namespace: 'CardReader',
    nestedTrans: 'CardReader.SelectPatientDialog',
  });

  useHotkeys('esc', (event) => {
    event.preventDefault();
    onCloseDialog();
  });

  const isNotMatch = (value: INotMatching) => {
    if (value.isAllow) return false;
    if (value.type === 'name') {
      return (
        patientFromCard?.personalInfo.lastName !== value.lastName ||
        patientFromCard?.personalInfo.firstName !== value.firstName
      );
    }
    if (value.type === 'dob') {
      return patientFromCard?.personalInfo.dOB !== value.dOB;
    }
    return false;
  };

  const genColumns = (isAllowSelect: boolean) => {
    return [
      {
        name: '',
        width: '10%',
        cell: (row: PatientInfo) => (
          <>
            {isAllowSelect && (
              <Flex justify="center" align="center" w={'100%'}>
                <Radio
                  checked={row.patientId === selectCardHolder?.patientId}
                  value={row.patientId}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => {
                    setSelectCardHolder(
                      patients.find(
                        (c: PatientInfo) => c.patientId === e.target.value
                      )
                    );
                  }}
                />
              </Flex>
            )}
          </>
        ),
      },
      {
        name: 'Insurance number',
        width: '20%',
        cell: (row: PatientInfo) => {
          return (
            <p onClick={() => setSelectCardHolder(row)}>
              {
                row?.insuranceInfos?.find((i) => isActiveInsurance(i))
                  ?.insuranceNumber
              }
            </p>
          );
        },
      },
      {
        name: 'Patient Name',
        width: '40%',
        cell: (row: PatientInfo) => (
          <p
            onClick={() => setSelectCardHolder(row)}
            className={`${isNotMatch({
              isAllow: !isAllowSelect,
              patientId: row.patientId!,
              type: 'name',
              lastName: row.personalInfo.lastName,
              firstName: row.personalInfo.firstName,
            })
              ? 'sl-match'
              : ''
              }`}
          >
            {`${row.personalInfo.lastName}, ${row.personalInfo.firstName}`}
          </p>
        ),
      },

      {
        name: 'Date of Birth',
        width: '20%',
        cell: (row: PatientInfo) => {
          return (
            <p
              onClick={() => setSelectCardHolder(row)}
              className={`${isNotMatch({
                isAllow: !isAllowSelect,
                patientId: row.patientId!,
                type: 'dob',
                dOB: row.personalInfo.dOB,
              })
                ? 'sl-match'
                : ''
                }`}
            >
              {formatUnixToDateString(row.personalInfo.dOB)}
            </p>
          );
        },
      },
    ];
  };

  return (
    <Dialog
      className={getCssClass(
        className,
        'bp5-dialog-fullscreen',
        'bp5-dialog-content-scrollable',
        'bp5-dialog-createpatient',
        'create-patient-modal'
      )}
      isOpen={isOpen}
      canEscapeKeyClose={false}
      canOutsideClickClose={false}
      onClose={onCloseDialog}
      title={tCard('selectPatient')}
      isCloseButtonShown
    >
      <Divider />
      <div className="wrapper">
        <Box className="content">
          <BodyTextM padding={'16px 0px'}>
            {tCard('patientExistedDescription')}
          </BodyTextM>
          <Table
            className="sl-table"
            columns={genColumns(false)}
            data={[patientFromCard]}
            customStyles={customStyles}
            title={<H4>{tCard('card')}</H4>}
            onRowClicked={(row: PatientInfo) => {
              setSelectCardHolder(row);
            }}
          />
          <Divider
            className={Classes.FILL}
            style={{ margin: 0, marginBottom: 16 }}
          />

          <Table
            className="sl-table"
            columns={genColumns(true)}
            data={patients}
            customStyles={customStyles}
            title={<H4>{tCard('garrioPro')}</H4>}
            onRowClicked={(row: PatientInfo) => {
              setSelectCardHolder(row);
            }}
          />
          <Divider className={Classes.FILL} style={{ margin: 0 }} />
        </Box>
      </div>
      <Flex justify="flex-end" className={`${Classes.DIALOG_FOOTER} footer`}>
        <Flex justify="flex-end" className="footer__content">
          <Box w="25%" mr="16px">
            <Button
              fill
              minimal
              intent="primary"
              className="footer__content--button-create"
              onClick={onCreatePatient}
            >
              {t('createPatient')}
            </Button>
          </Box>
          <Box>
            <Button
              fill
              intent="primary"
              className="footer__content--button-select"
              disabled={!selectCardHolder?.patientId}
              onClick={() => {
                onSelect(selectCardHolder!);
              }}
            >
              {t('selectPatient')}
            </Button>
          </Box>
        </Flex>
      </Flex>
    </Dialog>
  );
};

export default memo(
  I18n.withTranslation(PatientOverviewReadCard, {
    namespace: 'PatientOverview',
    nestedTrans: 'ReadCard',
  })
);
