import type PatientOverviewI18n from '@tutum/mvz/locales/en/PatientOverview.json';
import type { InsuranceInformation } from '@tutum/mvz/module_patient-management/types/insurance.type';
import type { UpdatePatientProfileV2Request } from '@tutum/hermes/bff/app_mvz_patient_profile';
import type {
  PatientInfo,
  PatientMedicalData,
} from '@tutum/hermes/bff/patient_profile_common';

import React, { useEffect, useMemo, useState } from 'react';
import moment from 'moment';

import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import WarnInfoCircleIcon from '@tutum/mvz/public/images/info-solid-warn.svg';
import { medicalDataTransform } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import { Box, Flex, H4 } from '@tutum/design-system/components';
import { ReactSelect } from '@tutum/design-system/components';
import { Gender } from '@tutum/hermes/bff/patient_profile_common';
import { useQueryGetPatientProfileById } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { IMenuItem } from '@tutum/design-system/components';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';

import { StyledTable, customStyles, customStylesHeader } from './index.styled';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { CardTypeType } from '@tutum/hermes/bff/card_common';
import {
  CompareStatus,
  FromCardType,
  PatientPrepared,
  TypeOfInsurance,
} from '@tutum/hermes/bff/legacy/patient_profile_common';
import { InsuranceInfo } from '@tutum/hermes/bff/patient_profile_common';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { COLOR } from '@tutum/design-system/themes/styles';

interface IRowData {
  field: string;
  value: any;
}

type IEGKTUTUM = IRowData[] | number | string | null | undefined;

interface IMapData {
  key: string;
  section: string;
  egk: IEGKTUTUM;
  garrioPro: IEGKTUTUM;
  field?: string;
  parent?: string;
}

const IGNORE_SECTION = ['patientNumber'];
const ACCEPT_FIELDS = [
  'dOB',
  'firstName',
  'lastName',
  'gender',
  'title',
  'additionalNames',
  'intendWord',

  'address',
  'postalAddress',
  // 'billingAddress',
  'insuranceCompanyName',
  'ikNumber',
  'insuranceNumber',
  'insuranceStatus',
  'startDate',
  'endDate',
  'specialGroup',
  'dMPLabeling',

  // 'typeOfInsurance',
];
const DATE_FIELDS = ['startDate', 'endDate', 'dOB'];
const ignoreFields = ['distance'];

const handleObj = (
  obj: any
): IRowData[] | number | string | null | undefined => {
  if (!obj) return;

  if (typeof obj === 'object') {
    return Object.keys(obj).map((o: string) => ({
      field: o,
      value: obj[o],
    })) as IRowData[];
  }
  return obj;
};

const EditableGender = React.memo(
  ({
    gender,
    onChangeGender,
    t,
  }: {
    gender: Gender;
    onChangeGender: (data: Gender) => void;
    t: IFixedNamespaceTFunction<keyof typeof PatientOverviewI18n.Synchronise>;
  }) => {
    const [isEdit, setIsEdit] = useState(false);

    if (isEdit) {
      return (
        <div style={{ width: '100%' }}>
          <ReactSelect
            styles={{}}
            id="gender"
            instanceId={'gender'}
            selectedValue={gender}
            items={[
              { label: t('genderM'), value: Gender.M },
              { label: t('genderW'), value: Gender.W },
              { label: t('genderX'), value: Gender.X },
              { label: t('genderD'), value: Gender.D },
              { label: t('genderU'), value: Gender.U },
            ]}
            onItemSelect={(item: IMenuItem) => {
              onChangeGender(item.value as Gender);
              setIsEdit(false);
            }}
          />
        </div>
      );
    }
    return (
      <div
        onClick={() => setIsEdit(true)}
        style={{
          width: '100%',
          height: 24,
          display: 'flex',
          alignItems: 'center',
        }}
      >
        {gender}
      </div>
    );
  }
);

interface PatientDataComparisonProps {
  fromCardType?: FromCardType;
  conflictWarningLabel?: string;
  conflictWarningContent?: string;
  patientId?: string;
  egkProfile?: PatientInfo;
  patientInfo?: PatientInfo;
  onPatientProfileChange?: (data: UpdatePatientProfileV2Request) => void;
  cardType?: CardTypeType;
  compareStatus?: CompareStatus;
  patientPrepared?: PatientPrepared;
}

type InsurancesComparison = {
  cardInsurance: InsuranceInfo;
  activeInsurance: InsuranceInfo;
};

export default function PatientDataComparison({
  conflictWarningLabel = '',
  conflictWarningContent = '',
  patientId,
  egkProfile,
  patientInfo,
  onPatientProfileChange,
  cardType,
  patientPrepared,
}: PatientDataComparisonProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PatientOverviewI18n.Synchronise
  >({
    namespace: 'PatientOverview',
    nestedTrans: 'Synchronise',
  });
  const [medicalData, setMedicalData] =
    useState<Nullable<PatientMedicalData>>(undefined);
  const [gender, setGender] = useState<Gender | undefined>(
    patientPrepared?.patientInfo?.personalInfo?.gender
  );

  const patientPreparedInfo = patientPrepared?.patientInfo;

  const { data, isSuccess } = useQueryGetPatientProfileById(
    {
      id: patientId!,
    },
    {
      throwOnError: false,
    }
  );

  const handleCompareData = (
    egk: IRowData[],
    garrioPro: IRowData[],
    parent
  ) => {
    const temp = egk.map((e: IRowData, index: number) => ({
      parent: parent,
      field: t(e.field as any),
      key: e.field,
      id: e.field,
      egk:
        Array.isArray(e.value) || typeof e.value === 'object'
          ? e.value
            ? Object.keys(e.value)
              .filter((v: string) => !ignoreFields.includes(v))
              .map((v: string) => e.value[v] || '')
              .join(' ') === '  0  '
              ? ''
              : Object.keys(e.value)
                .filter((v: string) => !ignoreFields.includes(v))
                .map((v: string) => e.value[v] || '')
                .join(' ')
            : ''
          : DATE_FIELDS.includes(e.field)
            ? moment.utc(e.value).format(DATE_FORMAT)
            : e.value,
      garrioPro:
        Array.isArray(garrioPro[index].value) ||
          typeof garrioPro[index].value === 'object'
          ? garrioPro[index].value
            ? Object.keys(garrioPro[index].value)
              .filter((v: string) => !ignoreFields.includes(v))
              .map((v: string) => garrioPro[index].value[v] || '')
              .join(' ') === '  0  '
              ? ''
              : Object.keys(garrioPro[index].value)
                .filter((v: string) => !ignoreFields.includes(v))
                .map((v: string) => garrioPro[index].value[v] || '')
                .join(' ')
            : ''
          : DATE_FIELDS.includes(garrioPro[index].field)
            ? moment(new Date(garrioPro[index].value)).format(DATE_FORMAT)
            : garrioPro[index].value,
    }));
    let accept_fields = ACCEPT_FIELDS;
    if (cardType === CardTypeType.CardTypeTypeKVK) {
      accept_fields = ACCEPT_FIELDS.filter(
        (f) => f !== 'startDate' && f != 'gender'
      );
    }
    const result = temp.filter((t) => accept_fields.includes(t.key));
    return result;
  };

  const handleGenerateColumns = () => {
    return [
      {
        id: 1,
        name: '',
        selector: (row) => row.field,
        width: '180px',
      },
      {
        id: 2,
        name: t('headerOriginalData'),
        selector: (row) => row.garrioPro,
        style: {
          width: '100%',
        },
        minWidth: '282px',
        maxWidth: `${760 * 0.4}px`,
        conditionalCellStyles: [
          {
            when: (row: any) => Boolean(row.egk !== row.garrioPro),
            style: {
              backgroundColor: COLOR.WARNING_LIGHT,
            },
          },
        ],
      },
      {
        id: 3,
        name: t('headerCardReader'),
        style: {
          width: '100%',
        },
        minWidth: '282px',
        maxWidth: `${760 * 0.4}px`,
        cell: (row: any) => {
          if (row.key === 'gender') {
            return (
              <EditableGender
                t={t}
                gender={row.egk}
                onChangeGender={(gen) => setGender(gen)}
              />
            );
          }
          return <div>{row.egk}</div>;
        },
        conditionalCellStyles: [
          {
            when: (row: any) => Boolean(row.egk !== row.garrioPro),
            style: {
              backgroundColor: COLOR.WARNING_LIGHT,
            },
          },
        ],
      },
    ];
  };

  const getInsurancesComparison = () => {
    const cardInsurance = egkProfile?.insuranceInfos?.find(
      (i) => i?.readCardDatas?.length
    );
    if (!cardInsurance) return;

    const activeInsurance = getActiveInsurance(patientInfo?.insuranceInfos);
    if (!activeInsurance) {
      const isPrivate = cardInsurance.insuranceType == TypeOfInsurance.Private;
      if (!isPrivate) return;

      return {
        cardInsurance,
        activeInsurance: patientInfo?.insuranceInfos[0],
      };
    }

    return {
      cardInsurance,
      activeInsurance,
    };
  };

  const removeInsuranceField = (key: keyof InsuranceInfo) => {
    const insuranceData = getInsurancesComparison();

    if (!insuranceData) {
      return;
    }

    const { cardInsurance, activeInsurance } = insuranceData;
    if (!cardInsurance || !activeInsurance) return;

    delete cardInsurance[key];
    delete activeInsurance[key];
  };

  /*
   *when compare patient info and insurance info
   *there is just only one insurance info compared
   * if patient already include the insurance from card -> show it
   * if patient not include the card insurance -> get active insurance to show
   */
  const insuranceInfosFiltered = () => {
    const cardInsurance = egkProfile?.insuranceInfos?.find(
      (i) => i.readCardDatas?.length
    );

    const insuranceCompared = patientInfo?.insuranceInfos?.find(
      (i) => i.id == cardInsurance?.id
    );
    if (insuranceCompared) return insuranceCompared;

    const activeInsurance = getActiveInsurance(patientInfo?.insuranceInfos);
    return activeInsurance;
  };

  const mappingData = useMemo((): IMapData[] => {
    removeInsuranceField('address');

    const patientInfoFiltered = {
      ...patientInfo,
      insuranceInfos: [insuranceInfosFiltered()],
    };

    return Object.keys(patientInfoFiltered)
      .map((e: string) => {
        if (
          Array.isArray(egkProfile?.[e]) &&
          Array.isArray(patientInfoFiltered[e])
        ) {
          if (egkProfile?.[e].length > patientInfo?.[e].length) {
            return egkProfile?.[e].map((ins: InsuranceInformation, i: number) => {
              const keySection = `${t(e as any)} - ${egkProfile?.[e][i].insuranceCompanyName
                }`;
              return {
                section: keySection,
                key: e,
                egk: handleObj(ins),
                garrioPro: handleObj(patientInfo?.[e][i]),
              };
            });
          } else {
            return patientInfoFiltered[e].map(
              (ins: InsuranceInformation, i: number) => {
                const keySection = `${t(e as any)} - ${patientInfoFiltered[e][i].insuranceCompanyName
                  }`;
                return {
                  section: keySection,
                  key: e,
                  egk: handleObj(egkProfile?.[e]?.[i]),
                  garrioPro: handleObj(ins),
                };
              }
            );
          }
        } else {
          return {
            section: t(e as any),
            key: e,
            egk: handleObj(egkProfile?.[e]),
            garrioPro: handleObj(patientInfoFiltered[e]),
          };
        }
      })
      .reduce(
        (prev: IMapData[], next: any) =>
          Array.isArray(next) ? [...prev, ...next] : [...prev, next],
        []
      )
      .filter((m: IMapData) => !IGNORE_SECTION.includes(m.key)) as IMapData[];
  }, [patientInfo, egkProfile]);

  const isVknrChanged = (() => {
    const insuranceData = getInsurancesComparison();
    if (!insuranceData?.cardInsurance) return false;

    return !patientInfo?.insuranceInfos?.some(
      (i) =>
        i?.readCardDatas?.length &&
        insuranceData.cardInsurance?.insuranceCompanyId === i?.insuranceCompanyId
    );
  })();

  const isInsuranceConflict = (() => {
    const conflictFields = [
      'startDate',
      'endDate',
      'specialGroup',
      'dMPLabeling',
    ];

    const insuranceData = getInsurancesComparison();
    if (!insuranceData?.cardInsurance) return false;

    const insuranceCompared = insuranceInfosFiltered();

    for (const key of conflictFields) {
      if (insuranceCompared?.[key] !== insuranceData.cardInsurance[key]) return true;
    }

    return false;
  })();

  const isPersonalInfoConflict = (() => {
    const conflictFields = [
      'dOB',
      'firstName',
      'lastName',
      'gender',
      'title',
      'intendWord',
    ];

    const personalInfo = patientInfo?.personalInfo;

    if (!personalInfo?.additionalNames) {
      return false;
    }

    for (let i = 0; i < personalInfo.additionalNames.length; i++) {
      if (egkProfile?.personalInfo?.additionalNames?.[i] !== personalInfo.additionalNames[i])
        return true;
    }

    return conflictFields.some(
      (key) => patientInfo?.personalInfo[key] !== egkProfile?.personalInfo[key]
    );
  })();

  const isConflict = isPersonalInfoConflict || isInsuranceConflict;

  useEffect(() => {
    const insuranceData = getInsurancesComparison();

    if (!insuranceData?.cardInsurance) return;

    const payload: UpdatePatientProfileV2Request = {
      id: patientId!,
      patientInfo: {
        ...patientPreparedInfo!,
        personalInfo: {
          ...patientPreparedInfo?.personalInfo!,
          gender: gender!,
        },
        insuranceInfos: patientPreparedInfo?.insuranceInfos!,
        patientNumber: patientInfo?.patientNumber!,
      },
      patientMedicalData: medicalDataTransform(
        medicalData,
        patientPreparedInfo?.personalInfo.gender === Gender.W
      ),
      cardInsurance: insuranceData.cardInsurance!,
    };
    onPatientProfileChange?.(payload);
  }, [patientId, patientPreparedInfo, patientInfo, medicalData]);

  useEffect(() => {
    if (isSuccess) {
      setMedicalData(data.patientMedicalData);
    }
  }, [isSuccess]);

  return (
    <StyledTable>
      <Box className="content">
        {isConflict && (
          <Flex className="hint" id="hint-data-conflict">
            <WarnInfoCircleIcon className="sl-hint-icon" />
            <Flex className="sl-Flex-msg" column gap={8}>
              <H4>{conflictWarningLabel}</H4>
              <p>{conflictWarningContent}</p>
            </Flex>
          </Flex>
        )}
        {isVknrChanged && (
          <Flex className="hint" id="hint-data-conflict">
            <WarnInfoCircleIcon className="sl-hint-icon" />
            <Flex className="sl-Flex-msg" column gap={8}>
              <H4>{t('vknrChangedTitle')}</H4>
              <p>{t('vknrChangedDescription')}</p>
            </Flex>
          </Flex>
        )}

        <Table
          className="table-header"
          columns={[
            {
              id: 1,
              name: '',
              selector: (row: IMapData) => row.field,
              width: '180px',
              maxWidth: `${760 * 0.2}px`,
              style: {
                marginLeft: '8px',
                backgroundColor: COLOR.BACKGROUND_SECONDARY_SHINE,
              },
            },
            {
              id: 3,
              name: 'garrioPro',
              selector: (row: IMapData) => row.garrioPro,
              style: {
                width: '100%',
                backgroundColor: COLOR.BACKGROUND_SECONDARY_SHINE,
                fontWeight: 600,
                color: COLOR.TEXT_SECONDARY_NAVAL,
              },
              minWidth: '282px',
              maxWidth: `${760 * 0.4}px`,
            },
            {
              id: 2,
              name: 'EGK',
              selector: (row: IMapData) => row.egk,
              style: {
                width: '100%',
                backgroundColor: COLOR.BACKGROUND_SECONDARY_SHINE,
                fontWeight: 600,
                color: COLOR.TEXT_SECONDARY_NAVAL,
                marginRight: '8px',
              },
              minWidth: '282px',
              maxWidth: `${760 * 0.4}px`,
            },
          ] as IDataTableColumn<IMapData>[]}
          data={[
            {
              parent: 'header',
              field: '',
              key: 'header',
              id: 'header',
              egk: t('headerCardReader'),
              garrioPro: t('headerOriginalData'),
            },
          ]}
          customStyles={customStylesHeader}
          highlightOnHover
          noTableHead
          noHeader
        />
        <Box
          className={`table ${isConflict ? '' : 'table-no_conflict'}`}
          id="table-compare-data"
        >
          {mappingData.map((data: IMapData) => {
            if (!(Array.isArray(data.egk) && Array.isArray(data.garrioPro))) {
              return null;
            }
            const parseRows = handleCompareData(
              data.egk,
              data.garrioPro,
              data.key
            );
            return (
              parseRows.length > 0 && (
                <div key={data.key}>
                  <H4>{data.section}</H4>
                  {Array.isArray(data.egk) && Array.isArray(data.garrioPro) && (
                    <Table
                      columns={handleGenerateColumns()}
                      data={parseRows}
                      customStyles={customStyles}
                      highlightOnHover
                      noTableHead
                    />
                  )}
                </div>
              )
            );
          })}
        </Box>
      </Box>
    </StyledTable>
  );
}
