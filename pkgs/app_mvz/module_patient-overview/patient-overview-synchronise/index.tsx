import React, { useState, memo } from 'react';
import { useRouter } from 'next/router';

import { IMvzTheme, IMvzThemeProps } from '@tutum/mvz/theme';
import { Dialog, Button, Classes } from '@tutum/design-system/components/Core';
import {
  alertError,
  alertSuccessfully,
  Box,
  Flex,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientOverviewI18n from '@tutum/mvz/locales/en/PatientOverview.json';
import type ErrorI18n from '@tutum/mvz/locales/en/ErrorCode.json';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';

import {
  updatePatientProfileV2,
  UpdatePatientProfileV2Request,
  createPatientProfileV2,
  useMutationCreatePatientProfileV2,
  useMutationUpdatePatientProfileV2,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  PatientInfo,
  PatientType,
  ProofOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { CardTypeType } from '@tutum/hermes/bff/card_common';
import PatientDataComparison from './PatientDataComparison';
import {
  CompareStatus,
  FromCardType,
  PatientPrepared,
} from '@tutum/hermes/bff/legacy/patient_profile_common';
import { getScheinsOverview } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { getValidScheinItemsInQuarter } from '@tutum/mvz/module_kv_hzv_schein/utils';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import Hoppla from '@tutum/mvz/components/hoppla-v2/styled';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';

export interface IPatientOverViewSynchroniseStyled {
  className?: string;
  theme?: IMvzTheme;
}

export interface IPatientOverViewSynchroniseProps {
  patientId?: string;
  egkProfile?: PatientInfo;
  originalPatientInfo?: PatientInfo;
  isOpen: boolean;
  onCloseDialog: () => void;
  proofOfInsurance?: ProofOfInsurance;
  cardType?: CardTypeType;
  fromCardType?: FromCardType;
  compareStatus?: CompareStatus;
  patientPrepared?: PatientPrepared;
}

const PatientOverViewSynchronise = (
  props: IPatientOverViewSynchroniseProps &
    IPatientOverViewSynchroniseStyled &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof PatientOverviewI18n.Synchronise>
) => {
  const {
    className,
    isOpen = false,
    onCloseDialog,
    t,
    originalPatientInfo,
    egkProfile,
    patientId,
    cardType,
    fromCardType,
    compareStatus,
    patientPrepared,
  } = props;
  const { push, reload, asPath } = useRouter();

  const [updatePatientPayload, setUpdatePatientPayload] =
    useState<UpdatePatientProfileV2Request | undefined>(undefined);

  const toaster = useToaster();
  const { t: tError } = I18n.useTranslation<keyof typeof ErrorI18n>({
    namespace: 'ErrorCode',
  });

  const { mutate: updatePatientProfile, isPending: isLoadingUpdate } =
    useMutationUpdatePatientProfileV2({
      onSuccess: async (data) => {
        if (data.data.updateErrorStatus) {
          return alertError(tError(data.data.updateErrorStatus as any));
        }
        alertSuccessfully(t('updateSuccess'));
        const patientType = data.data.patientInfo?.genericInfo?.patientType;
        await route(patientId!, patientType);
        onCloseDialog();
      },
      onError: () => {
        alertError(t('updateFail'));
      },
    });

  const { mutate: createPatientProfile, isPending: isLoadingCreate } =
    useMutationCreatePatientProfileV2({
      onSuccess: async (data) => {
        if (data.data.createErrorStatus) {
          return alertError(tError(data.data.createErrorStatus as any));
        }
        alertSuccessfully(t('createSuccess'));
        const patientType = data.data.patientInfo?.genericInfo?.patientType;
        await route(data.data.id!, patientType!);
        onCloseDialog();
      },
      onError: () => {
        alertError(t('createFail'));
      },
    });

  /*
  when patient re-read card without schein in this quarter -> show create schein
  when patient re-read card without schein created by card insurance in this quarter -> show create schein
  */
  const isOpenCreateSchein = async () => {
    const response = await getScheinsOverview({ patientId: patientId! });
    const scheinItems = response.data.scheinItems;

    const validScheinItems = getValidScheinItemsInQuarter(
      scheinItems,
      datetimeUtil.now()
    );
    if (!validScheinItems.length) return true;

    const cardInsurance = egkProfile?.insuranceInfos?.find(
      (i) => !!i.readCardDatas?.length
    );
    if (!cardInsurance) return true;

    return !validScheinItems.some(
      (item) => item.insuranceId === cardInsurance.id
    );
  };

  const route = async (patientId: string, patientType: PatientType) => {
    const URL = `/patients/${patientId}`;

    const isOpen = await isOpenCreateSchein();
    if (isOpen) {
      if (patientType === PatientType.PatientType_Private) {
        await push(`${URL}#private`);
      } else {
        await push(`${URL}#schein`);
      }
      return reload();
    }

    if (asPath.includes(URL)) return reload();

    await push(URL);
  };

  const onCreatePatient = () => {
    createPatientProfile({
      patientInfo: egkProfile!,
    });
  };

  const onUpdatePatient = async () => {
    if (!updatePatientPayload) {
      alertError(t('missingGender'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
        toaster,
      });
      return;
    }
    updatePatientProfile(updatePatientPayload);
  };

  const isValidVsdTimestamp =
    patientPrepared?.validationError !==
    ErrorCode.ErrorCode_CardReader_Invalid_Vsd_TimeStamp;

  return (
    <div>
      {isValidVsdTimestamp ? (
        <Dialog
          className={getCssClass(
            className,
            'bp5-dialog-fullscreen',
            'bp5-dialog-content-scrollable',
            'bp5-dialog-createpatient',
            'create-patient-modal'
          )}
          canEscapeKeyClose={false}
          canOutsideClickClose={false}
          isOpen={isOpen}
          onClose={onCloseDialog}
          title={t('titleDialog')}
        >
          <PatientDataComparison
            fromCardType={fromCardType}
            conflictWarningLabel={t('dataConflict')}
            conflictWarningContent={t('descriptDataConflict')}
            patientId={patientId}
            patientInfo={originalPatientInfo}
            egkProfile={egkProfile}
            onPatientProfileChange={(newPatientInfoPayload) =>
              setUpdatePatientPayload(newPatientInfoPayload)
            }
            cardType={cardType}
            compareStatus={compareStatus}
            patientPrepared={patientPrepared}
          />
          <Flex
            justify="flex-end"
            className={`${Classes.DIALOG_FOOTER} footer_btns`}
          >
            <Box className="cancel">
              <Button fill onClick={onCloseDialog} intent="none">
                {t('cancel')}
              </Button>
            </Box>
            {!!patientPrepared?.isCreateShown && (
              <Box className="create">
                <Button
                  fill
                  onClick={onCreatePatient}
                  intent="none"
                  loading={isLoadingUpdate || isLoadingCreate}
                >
                  {t('create')}
                </Button>
              </Box>
            )}
            <Box className="submit">
              <Button
                fill
                onClick={onUpdatePatient}
                intent="primary"
                loading={isLoadingUpdate || isLoadingCreate}
              >
                {t('updatePatientdetail')}
              </Button>
            </Box>
          </Flex>
        </Dialog>
      ) : (
        <Hoppla
          titleError={'VSD TimeStamp Invalid'}
          messageError={t('ErrorCode_CardReader_Invalid_Vsd_TimeStamp')}
        />
      )}
    </div>
  );
};

export default memo(
  I18n.withTranslation(PatientOverViewSynchronise, {
    namespace: 'PatientOverview',
    nestedTrans: 'Synchronise',
  })
);
