import { FormType } from '@tutum/hermes/bff/app_mvz_medicine';
import { FormSize, PrintType } from '@tutum/hermes/bff/form_common';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { FormName } from '@tutum/hermes/bff/form_common';
import {
  Printer,
  PrinterProfile,
  Size,
  PrintUnits,
} from '@tutum/hermes/bff/printer_common';
import * as qz from 'qz-tray';
import { SettingsFeatures } from '@tutum/hermes/bff/legacy/settings_common';
import { getSetting } from '@tutum/hermes/bff/legacy/app_mvz_settings';
import { PrintData } from '@tutum/hermes/bff/app_mvz_form';
import {
  SignQzRequest,
  findPrinterProfiles,
  signQz,
} from '@tutum/hermes/bff/legacy/app_mvz_printer';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';
import store from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import { newServerError } from '@tutum/infrastructure/utils/error.util';
import { isNil } from 'lodash';

let printerProfile: PrinterProfile | null = null;

function setPrinterProfile(req: PrinterProfile) {
  printerProfile = req;
}

async function getPrinterProfile(formId: string) {
  if (printerProfile) {
    return printerProfile;
  }

  const { data } = await findPrinterProfiles({ id: formId });
  if (data && data.printers.length > 0) {
    return data.printers[0];
  }

  return null;
}

function resetPrinterProfile() {
  printerProfile = null;
}

// Get trusted certificate file
async function getPrivateKey(): Promise<any> {
  const cert = 'digital-certificate.txt';
  return fetchWithHeaders('GET', `/data/certificates/${cert}`, {
    init: {
      responseType: 'text',
    },
  });
}

async function initQZ(host: string) {
  try {
    // Get private key
    const privateKey = await getPrivateKey();

    // Set certificate use privateKey
    qz.security.setCertificatePromise((resolve) => {
      resolve(privateKey.data);
    });

    // Set signature
    qz.security.setSignaturePromise((toSign) => {
      return (resolve, reject) => {
        const request: SignQzRequest = {
          toSign: toSign,
        };
        signQz(request)
          .then((response) => resolve(response.data.signature))
          .catch((error) => {
            reject(error);
          });
      };
    });

    const isActive = qz.websocket.isActive();
    if (!isActive) {
      await qz.websocket.connect({ host: host });
    }
  } catch (error) {
    console.error(error);
    const err = newServerError({
      errorCode: ErrorCode.ErrorCode_Printer_Connection_Failed,
    });
    throw err;
  }
}

function getPrinters() {
  return qz.printers.find();
}

async function detailPrinters() {
  return (await qz.printers.details()) as Printer[];
}

const getDefaultPaperSize = (
  unit: PrintUnits,
  formSize: FormSize | undefined
): Size | undefined => {
  if (unit === PrintUnits.PrintUnits_in) {
    switch (formSize) {
      case FormSize.FormSize_A4:
        return {
          width: 8.3,
          height: 11.7,
        };
      case FormSize.FormSize_A5:
        return {
          width: 5.8,
          height: 8.3,
        };
      case FormSize.FormSize_A6:
        return {
          width: 4.1,
          height: 5.8,
        };
      default:
        return undefined;
    }
  }
  let val = 1;
  if (unit === PrintUnits.PrintUnits_mm) {
    val = 10;
  }
  switch (formSize) {
    case FormSize.FormSize_A4:
      return {
        width: 21 * val,
        height: 29.7 * val,
      };
    case FormSize.FormSize_A5:
      return {
        width: 14.85 * val,
        height: 21 * val,
      };
    case FormSize.FormSize_A6:
      return {
        width: 10.5 * val,
        height: 14.85 * val,
      };
    default:
      return undefined;
  }
};

async function checkQzConnection() {
  try {
    await qz.printers.find();
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}

async function printDataByPrinterProfile(
  formId: string,
  data: string, // can be base64 or pdf url
  callbackError?: (error) => void
) {
  try {
    const isConnected = await checkQzConnection();

    if (!isConnected) {
      const err = newServerError({
        errorCode: ErrorCode.ErrorCode_Printer_Connection_Failed,
      });

      if (callbackError) {
        callbackError(err);
        return;
      }

      throw err;
    }

    const printerProfile = await getPrinterProfile(formId);
    if (!printerProfile) {
      const err = newServerError({
        errorCode: ErrorCode.ErrorCode_PrinterProfile_Not_Found,
      });

      if (callbackError) {
        callbackError(err);
        return;
      }

      throw err;
    }

    const printingData: qz.PrintData[] = [
      {
        data,
        type: 'pixel',
        format: 'pdf',
        flavor: 'base64',
      },
    ];

    // check if data parameter is address of pdf file
    if (data.includes('.pdf')) {
      printingData[0].flavor = 'file';
    }

    const options: qz.PrinterOptions = {
      printerTray: printerProfile.tray,
      copies: printerProfile.copies,
      duplex: printerProfile.duplex,
      colorType: printerProfile.colorType,
      units: printerProfile.units,
      density: printerProfile.density ? [printerProfile.density] : undefined,
      rasterize: printerProfile.rasterize,
      rotation: printerProfile.rotation,
      size: printerProfile.size,
      scaleContent: printerProfile.scaleContent,
      margins: printerProfile.margins,
      bounds: printerProfile.bounds,
      interpolation: printerProfile.interpolation,
    };

    const units = printerProfile.units || PrintUnits.PrintUnits_in;

    if (!printerProfile.size?.height && !printerProfile.size?.width) {
      const size = getDefaultPaperSize(units, printerProfile.formSize);
      options.units = units;
      options.size = size;
    }

    const config = qz.configs.create(printerProfile.printerName, options);
    await qz.print(config, printingData);
    resetPrinterProfile();
  } catch (e) {
    console.error(e);
    resetPrinterProfile();

    if (callbackError) {
      callbackError(e);
      return;
    }

    throw e;
  }
}

function endConnection() {
  if (qz.websocket.isActive()) {
    qz.websocket.disconnect();
  }
}

async function getPrinterHost() {
  const res = await getSetting({
    feature: SettingsFeatures.SettingsFeatures_Printer,
    settings: [],
  });
  const appConfig = res?.data?.settings;

  if (!appConfig?.printerHost) {
    const error = newServerError({
      errorCode: ErrorCode.ErrorCode_PrinterHost_Not_Found,
    });
    throw error;
  }

  return appConfig?.printerHost;
}

function checkNeedRemoveBackground(formName: FormName) {
  return [FormName.LKK_BY_HZV_Muster_Versicherteneinschreibebeleg_V3].includes(
    formName
  );
}

async function initAndPrint(
  formId: string,
  prescribeCallback: (
    isPdfWithBackground: boolean
  ) => Promise<string | string[] | PrintData[]>,
  printCallback?: {
    printSuccess: () => Promise<void>;
    printFailure: (err?: any) => void;
    onClose?: () => Promise<void>;
  } | null,
  onEditTimeline?: () => Promise<void>,
  callbackError?: (error) => void
) {
  let data: string | string[] | PrintData[] = '';

  const printerHost = await getPrinterHost();
  await initQZ(printerHost);
  const matchedForm = (store.listForms || []).find(
    (form) => form.id === formId
  );
  const shouldUseSVForms =
    !formId &&
    (!matchedForm ||
      [FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5].includes(
        formId as FormName
      ) ||
      matchedForm?.isHzvFav);
  let id = shouldUseSVForms ? 'SV_FORMS' : formId;
  const isPdfWithBackground =
    printerProfile?.printType === PrintType.PrintType_fullPrint &&
    !checkNeedRemoveBackground(id as FormName);

  if (formId === FormName.Muster_4 && !isPdfWithBackground) {
    id = FormName.Muster_4_A5;
  }

  if (!printerProfile || [FormName.Muster_4_A5].includes(id as FormName)) {
    const {
      data: { printers: printerProfiles },
    } = await findPrinterProfiles({ id });
    printerProfile = printerProfiles?.[0];
  }
  if (!printerProfile || !printerProfile.printerName) {
    const error = newServerError({
      errorCode: ErrorCode.ErrorCode_PrinterProfile_Not_Found,
    });
    printCallback?.printFailure?.(error);
    throw error;
  }

  await onEditTimeline?.();
  data = await prescribeCallback(isPdfWithBackground);
  if (data && data.length > 0) {
    await printCallback?.printSuccess?.();
  }

  if (!data && printCallback?.onClose) {
    printCallback?.onClose();
    return;
  }

  if (data instanceof Array) {
    data.forEach(async (item: string | PrintData) => {
      if (typeof item === 'string') {
        await printDataByPrinterProfile(formId, item, callbackError);
      } else {
        item.formUrls.forEach(async (pdf: string) => {
          await printDataByPrinterProfile(formId, pdf, callbackError);
        });
      }
    });
  } else {
    await printDataByPrinterProfile(formId, data, callbackError);
  }
}

async function getPrinterSetting(formId: string) {
  const {
    data: { printers: printerProfiles },
  } = await findPrinterProfiles({ id: formId });
  const firstPrinterProfile = printerProfiles?.[0];
  if (isNil(firstPrinterProfile)) {
    return;
  }
  const isPdfWithBackground =
    firstPrinterProfile?.printType === PrintType.PrintType_fullPrint &&
    !checkNeedRemoveBackground(formId as FormName);
  return isPdfWithBackground;
}

function getMedicationFormFileName(medicationFormType: string) {
  let mapFormType;
  switch (medicationFormType) {
    case FormType.KREZ:
      mapFormType = FormName.Muster_16;
      break;
    case FormType.BTM:
      mapFormType = 'Btm_Rezept';
      break;
    case FormType.GREZ:
      mapFormType = 'Gruenes_Rezept';
      break;
    case FormType.TPrescription:
      mapFormType = 'T-Rezept-Muster';
      break;
    case FormType.Private:
      mapFormType = 'Blaues_Rezept';
      break;
    case FormType.Muster16aBay:
      mapFormType = FormName.Muster_16A_Bay;
      break;
    default:
      mapFormType = medicationFormType;
  }
  return mapFormType;
}

export default {
  checkNeedRemoveBackground,
  initQZ,
  getPrinters,
  endConnection,
  detailPrinters,
  getPrinterHost,
  initAndPrint,
  getPrinterSetting,
  getMedicationFormFileName,
  printDataByPrinterProfile,
  getPrinterProfile,
  setPrinterProfile,
  resetPrinterProfile,
};
