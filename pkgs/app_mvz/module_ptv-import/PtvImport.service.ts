import * as PtvImportApi from '@tutum/hermes/bff/legacy/app_mvz_ptv_import';
import {
  GetParticipantsByDoctorRequest,
  ParticipantDecision,
} from '@tutum/hermes/bff/ptv_import_common';

export type Participants = {
  id: string;
  doctorId: string;
  contractId: string;
  documentId: string;
  autoImportParticipants: ParticipantDecision[];
  conflictParticipants: ParticipantDecision[];
  missingParticipants: ParticipantDecision[];
  beforeParticipantCount: number;
  afterParticipantCount: number;
  updateTime?: number;
  year: number;
  quarter: number;
  ptvImportId: string;
};

export type EmpType = { Id: string; Name: string };

export interface PTVImportTable {
  description: string;
  patient: ParticipantDecision[];
}

export function getPatientParticipantsByDoctor(
  request: GetParticipantsByDoctorRequest
): Promise<Participants> {
  return PtvImportApi.getParticipantsByDoctor(request).then((res) => {
    return {
      id: res.data.id,
      doctorId: res.data.doctorId,
      documentId: res.data.documentId,
      contractId: res.data.contractId,
      autoImportParticipants: res.data.autoImportParticipants,
      conflictParticipants: res.data.conflictParticipants,
      missingParticipants: res.data.missingParticipants,
      beforeParticipantCount: res.data.beforeParticipantCount,
      afterParticipantCount: res.data.afterParticipantCount,
      year: res.data.year ?? 0,
      quarter: res.data.quarter ?? 0,
      ptvImportId: res.data.ptvImportId,
    };
  });
}
