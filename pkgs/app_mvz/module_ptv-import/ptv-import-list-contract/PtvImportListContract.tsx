import {
  BodyTextL,
  BodyTextM,
  Flex,
  ReactSelect,
  Svg,
  Tag,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Button,
  Classes,
  Dialog,
  InputGroup,
  Intent,
} from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { useMutationImportTestData } from '@tutum/hermes/bff/legacy/app_mvz_ptv_import';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';
import {
  ImportContract,
  ImportContractStatus,
} from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import {
  ComplianceWrapper,
  useIsPracticeSupportCompliance,
} from '@tutum/mvz/hooks/useCompliance';
import useConfirm from '@tutum/mvz/hooks/useConfirm';
import { FFWrapper, useFeatureFlag } from '@tutum/mvz/hooks/useFeatureFlag';
import type PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { checkContractSupport } from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import {
  Participants,
  getPatientParticipantsByDoctor,
} from '@tutum/mvz/module_ptv-import/PtvImport.service';
import { IEmployeeProfile, UserType } from '@tutum/mvz/types/profile.type';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { SLTagState } from '@tutum/design-system/components/Tag';

const InfoSolid = '/images/info-solid-warn.svg';

export interface IPtvImportListContractProps {
  className?: string;
  onCloseImportContractList: () => void;
  mapEmployee: Map<string, IEmployeeProfile>;
  isOpenImportListContract: boolean;
  onGetRetrievalCodeByDoctor: (selectedDoctor: IEmployeeProfile | null) => void;
  importContracts: ImportContract[];
  setSelectedParticipants: React.Dispatch<React.SetStateAction<Participants>>;
  setIsOpenDryRun: React.Dispatch<React.SetStateAction<boolean>>;
  onGetContractByDoctor: (doctorId: string, code: string) => void;
  onResetContractDoctor: () => void;
  selectedDoctor: IEmployeeProfile | null;
  setSelectedDoctor: React.Dispatch<
    React.SetStateAction<IEmployeeProfile | null>
  >;
  iCode: string;
  setICode: React.Dispatch<React.SetStateAction<string>>;

  isImported: boolean;
  setImported: (isImported: boolean) => void;
  isLoading: boolean;
  selectedContract: ImportContract | null;
  setSelectedContract: React.Dispatch<
    React.SetStateAction<ImportContract | null>
  >;
  handleRequest: (selectedDoctor: IEmployeeProfile | null, icode: string) => void;
}

function PtvImportListContract({
  mapEmployee,
  isOpenImportListContract,
  onGetContractByDoctor,
  onGetRetrievalCodeByDoctor,
  onResetContractDoctor,
  onCloseImportContractList,
  className,
  importContracts,
  setSelectedParticipants,
  setIsOpenDryRun,
  selectedDoctor,
  setSelectedDoctor,
  iCode,
  setICode,
  isImported,
  setImported,
  isLoading,
  selectedContract,
  setSelectedContract,
  handleRequest,
}: IPtvImportListContractProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportListContract
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportListContract',
  });
  const { t: tConfirmDialog } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportListContract.ConfirmDialog
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportListContract.ConfirmDialog',
  });

  const uploadRef = useRef(null);

  const [doctors, setDoctors] = useState<IEmployeeProfile[]>([]);
  const [openImportedDialog, setOpenImportedDialog] = useState<boolean>(false);

  const { ConfirmationDialog, askConfirmation } = useConfirm();

  const importTestData = useMutationImportTestData({
    throwOnError: false,
    onSuccess: (res) => {
      alertSuccessfully(t('uploadSuccess'));
    },
    onError: (error) => {
      if (error.name === ErrorCode.ErrorCode_PTV_Import_Older_Than_Latest) {
        askConfirmation({
          title: t('uploadOlderErrorTitle'),
          content: t('uploadOlderErrorDescription'),
          intent: Intent.DANGER,
          confirmButton: undefined,
          cancelButton: t('closeBtn'),
          isShowIconTitle: true,
          headerIcon: (
            <Svg
              className="sl-type-header-icon"
              src={InfoSolid}
              style={{ width: 32, height: 32, marginRight: 8 }}
            />
          ),
        });
        return;
      }

      alertError(t('uploadFailed'));
    },
  });

  const isEnablePTV = useFeatureFlag({
    ffKey: FeatureFlagKey.FeatureFlagKey_HZV_PTV_TESTMODE,
  });
  const isPracticeSupportPTV = useIsPracticeSupportCompliance({
    complianceIds: ['VERT1481'],
  });

  const isShowUploadBtn = isEnablePTV && isPracticeSupportPTV;

  const handleUpload = async (event) => {
    const file = event.target.files[0];

    if (!file) {
      return;
    }

    const reader = new FileReader();

    reader.onload = function (e) {
      const xmlContent = e.target?.result as string;
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');

      importTestData.mutate({
        doctorId: selectedDoctor?.id || '',
        xmlData: new XMLSerializer().serializeToString(xmlDoc),
      });
    };

    reader.readAsText(file);
  };

  const handleSetDoctors = async () => {
    const doctors: IEmployeeProfile[] = [];

    if (mapEmployee.size > 0) {
      for (const [_, value] of mapEmployee.entries()) {
        if (value.types.includes(UserType.DOCTOR)) {
          const contracts = [...value.hzvContracts || [], ...value.favContracts || []];
          let isSupportVERT1481 = await checkContractSupport(
            ['VERT1481'],
            contracts.map(contract => contract.contractId),
          );

          if (isSupportVERT1481) {
            doctors.push(value);
          }
        }
      }
      setDoctors(doctors);

      if (selectedDoctor) {
        return;
      }
      setSelectedDoctor(doctors[0]);
      onGetRetrievalCodeByDoctor(doctors[0]);
    }
  }

  useEffect(() => {
    handleSetDoctors();
  }, [selectedDoctor, mapEmployee]);

  useEffect(() => {
    if (selectedDoctor && selectedContract && !openImportedDialog) {
      getPatientParticipantsByDoctor({
        doctorId: selectedDoctor.id || '',
        code: iCode,
        documentId: selectedContract.documentId,
        year: selectedContract.year,
        quarter: selectedContract.quarter,
        version: selectedContract.version,
        contractId: selectedContract.contractId,
        ptvImportId: selectedContract.ptvImportId,
        newSession: false,
      }).then((res) => {
        setSelectedParticipants(res);
        setIsOpenDryRun(true);
        onResetContractDoctor();
        onCloseImportContractList();
      });
    }
  }, [selectedDoctor, selectedContract, openImportedDialog]);

  const columnsData = useMemo(() => {
    return [
      {
        width: '20%',
        name: t('contractId'),
        cell: (contract: ImportContract) => {
          return <span>{contract.contractId}</span>;
        },
      },
      {
        width: '20%',
        name: t('quarterTime'),
        cell: (contract: ImportContract) => {
          return <span>{`Q${contract.quarter} ${contract.year}`}</span>;
        },
      },
      {
        width: '20%',
        name: t('versionDocument'),
        cell: (contract: ImportContract) => {
          return <span>{contract.version}</span>;
        },
      },
      {
        width: '20%',
        name: t('status'),
        cell: (contract: ImportContract) => {

          let label = '';
          let slState: SLTagState = 'info';
          switch (contract.status) {
            case ImportContractStatus.ImportContractStatus_Done:
              label = t('importedLabel');
              slState = 'positive';
              break;
            case ImportContractStatus.ImportContractStatus_InProgress:
              label = t('importingLabel');
              slState = 'info';
              break;
            case ImportContractStatus.ImportContractStatus_New:
              label = t('notYetImportLabel');
              slState = 'info';
              break;
            case ImportContractStatus.ImportContractStatus_Pending:
              label = t('pendingLabel');
              slState = 'warning';
              break;
            default:
          }

          return (
            <Tag slStyle="fill" slState={slState as SLTagState}>
              {label}
            </Tag>
          );
        },
      },
      {
        width: '20%',
        name: '',
        cell: (contract: ImportContract) => {
          return (
            <Button
              intent={Intent.PRIMARY}
              data-test-id="import-contract"
              onClick={() => {
                const isImported =
                  contract.status ===
                  ImportContractStatus.ImportContractStatus_Done;

                if (isImported) {
                  setOpenImportedDialog(true);
                }

                setSelectedContract(contract);
              }}
            >
              {t('importBtn')}
            </Button>
          );
        },
      },
    ];
  }, []);

  return (
    <Dialog
      className={getCssClass(
        'bp5-dialog-fullscreen',
        'bp5-dialog-content-scrollable'
      )}
      title={t('ptvImportContractHeader')}
      isOpen={isOpenImportListContract}
      onClose={() => {
        onResetContractDoctor();
        onCloseImportContractList();
      }}
      canOutsideClickClose={false}
    >
      <Flex
        column
        className={getCssClass(className, Classes.DIALOG_BODY)}
        align="center"
        gap={24}
      >
        <Flex w={'50%'} column mt={8}>
          <BodyTextL fontWeight={600} color={COLOR.TEXT_PRIMARY_BLACK}>
            {t('ptvImportContractDescriptionRemark')}:
          </BodyTextL>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
            {t('ptvImportContractDescriptionDetail1')}
            <a href="http://arztportal.net" target="target">
              {t('ptvImportContractDescriptionDetailLink')}
            </a>{' '}
            {t('ptvImportContractDescriptionDetail2')}
          </BodyTextM>
        </Flex>
        <Flex w={'50%'} className="ptv-group" column gap={16}>
          <Flex gap={16}>
            <BodyTextM className="full-circle-text actived">1</BodyTextM>
            <BodyTextL
              fontSize={18}
              fontWeight={700}
              color={COLOR.TEXT_PRIMARY_BLACK}
            >
              {t('step1')}
            </BodyTextL>
          </Flex>
          <Flex ml={40} gap={16}>
            <Flex
              column
              className="ptv-contract-list-doctor"
              w={isShowUploadBtn ? '40%' : '60%'}
            >
              <Flex mb={8}>{t('doctor')}</Flex>
              <Flex w="100%">
                <ReactSelect
                  id="select-doctor"
                  instanceId="select-doctor"
                  selectedValue={selectedDoctor?.id}
                  items={(doctors || []).map((doctor) => ({
                    label: nameUtils.getDoctorName(doctor).trim(),
                    value: doctor.id || '',
                  }))}
                  onItemSelect={(item) => {
                    const selectedDoctor =
                      doctors.find((doctor) => doctor.id === item.value) ||
                      null;

                    setImported(false);
                    setSelectedDoctor(selectedDoctor);
                    onGetRetrievalCodeByDoctor(selectedDoctor);
                    onResetContractDoctor();
                  }}
                />
              </Flex>
            </Flex>
            <Flex
              column
              className="ptv-contract-list-icode"
              w={isShowUploadBtn ? '60%' : '40%'}
            >
              <Flex mb={8}>{t('icode')}</Flex>
              <Flex gap={16}>
                <InputGroup
                  className="flex-1"
                  minLength={10}
                  maxLength={10}
                  value={iCode}
                  placeholder={t('icode')}
                  onChange={(event) => {
                    setImported(false);
                    setICode(event.target.value);
                  }}
                />
                <Button
                  disabled={!iCode}
                  intent={Intent.PRIMARY}
                  data-test-id="request-doctor-contract"
                  onClick={() => handleRequest(selectedDoctor, iCode)}
                >
                  {t('verifyDoctor')}
                </Button>
                <FFWrapper
                  ffKey={FeatureFlagKey.FeatureFlagKey_HZV_PTV_TESTMODE}
                >
                  <ComplianceWrapper complianceIds={['VERT1481']}>
                    <input
                      ref={uploadRef}
                      type="file"
                      multiple
                      hidden
                      accept=".xml"
                      onInput={handleUpload}
                    />
                    <Button
                      intent={Intent.PRIMARY}
                      minimal
                      outlined
                      loading={importTestData.isPending}
                      disabled={importTestData.isPending}
                      data-test-id="button-upload"
                      onClick={() => {
                        if (uploadRef.current) {
                          const button = uploadRef.current as HTMLButtonElement;
                          button.click();
                        }
                      }}
                    >
                      {t('upload')}
                    </Button>
                  </ComplianceWrapper>
                </FFWrapper>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
        <Flex w={'50%'} column gap={16}>
          <Flex gap={16}>
            <BodyTextM
              className={getCssClass('full-circle-text', {
                actived: isImported,
              })}
            >
              2
            </BodyTextM>
            <BodyTextL
              fontSize={18}
              fontWeight={700}
              color={
                isImported
                  ? COLOR.TEXT_PRIMARY_BLACK
                  : COLOR.TEXT_TERTIARY_SILVER
              }
            >
              {t('step2')}
            </BodyTextL>
          </Flex>
          {isImported && (
            <Flex ml={40}>
              <Table
                columns={columnsData}
                highlightOnHover
                noHeader
                persistTableHead
                striped
                data={importContracts}
                responsive={false}
                progressPending={isLoading}
                noDataComponent={
                  <Flex my={16} column>
                    <BodyTextL
                      margin="24px 0 0"
                      fontWeight={600}
                      color={COLOR.TEXT_TERTIARY_SILVER}
                    >
                      {t('noResultFound')}
                    </BodyTextL>
                  </Flex>
                }
              />
            </Flex>
          )}
        </Flex>
      </Flex>
      <ConfirmationDialog />
      <ConfirmDialog
        isOpen={openImportedDialog}
        text={{
          btnCancel: tConfirmDialog('cancelBtn'),
          btnOk: tConfirmDialog('confirmBtn'),
          title: tConfirmDialog('title'),
          message: tConfirmDialog('description'),
        }}
        intent={Intent.PRIMARY}
        hasIcon={false}
        close={() => {
          setSelectedContract(null);
          setOpenImportedDialog(false);
        }}
        confirm={() => {
          setOpenImportedDialog(false);
        }}
      />
    </Dialog>
  );
}

export default PtvImportListContract;
