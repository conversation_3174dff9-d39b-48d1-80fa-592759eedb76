import { ParticipantDecision, PatientParticipationStatus } from "@tutum/hermes/bff/legacy/ptv_import_common";
import { cloneDeep } from "lodash";

export const SORT_ORDER_STATUS = {
  [PatientParticipationStatus.PatientParticipation_Active]: 1,
  [PatientParticipationStatus.PatientParticipation_Terminated]: 2,
  [PatientParticipationStatus.PatientParticipation_Requested]: 3,
  [PatientParticipationStatus.PatientParticipation_Rejected]: 4,
  [PatientParticipationStatus.PatientParticipation_Cancelled]: 5,
}


export const getSortedDataByStatus = (data: ParticipantDecision[]) => {
  const cloneData = cloneDeep(data);

  cloneData.sort((a, b) => {
    const statusA = a.status.localStatus.value;
    const statusB = b.status.localStatus.value;

    if (statusA !== statusB) {
      return SORT_ORDER_STATUS[statusA] - SORT_ORDER_STATUS[statusB];
    }

    // If statuses are the same, sort by last name
    const lastNameA = a.lastName.localLastName.value;
    const lastNameB = b.lastName.localLastName.value;

    return lastNameA.localeCompare(lastNameB);
  });

  return cloneData;
};
