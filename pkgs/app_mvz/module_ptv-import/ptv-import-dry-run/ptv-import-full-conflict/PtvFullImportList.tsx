import {
  BodyTextM,
  BodyTextS,
  Dialog,
  Flex,
  FormGroup2,
  InputSuggestion,
  Svg,
  Tooltip,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { Button, Classes, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import {
  DEFAULT_INPUT_SUGGESTION_STYLE_CONFIG,
  DEFAULT_SELECT_COMPONENT_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
import { getAge } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { PatientParticipationStatus } from '@tutum/hermes/bff/common';
import {
  PatientSearchingResponse,
  SearchingType,
  useMutationSearchPatients,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_search';
import { PatientInfo } from '@tutum/hermes/bff/legacy/patient_profile_common';
import { TypeOfInsurance } from '@tutum/hermes/bff/patient_profile_common';
import {
  ContractBeginDate,
  Dob,
  FirstName,
  Gender,
  IkNumber,
  InsuranceNumber,
  LastName,
  NumberSelection,
  ParticipantDecision,
  Reason,
  Status,
  TypeGroupDecision,
} from '@tutum/hermes/bff/ptv_import_common';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import PatientOverviewI18n from '@tutum/mvz/locales/en/PatientOverview.json';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import PtvImportResolveParticipant from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-conflict-import-list/ptv-import-resolve-participant/PtvImportResolveParticipant.styled';
import {
  PTVImportDate,
  PTVImportStatus,
  PTVTreatmentType,
} from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-status/PTVImportStatus';
import { handleCreateNewPatientByKeyCode } from '@tutum/mvz/utils';

import { Field, Form, Formik, FormikProps } from 'formik';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { components } from 'react-select';

const readCardIcon = '/images/patient/selector/read-card-icon.svg';

// Extended type to include hasConflict flag
export type ParticipantDecisionWithConflict = ParticipantDecision & {
  hasConflict?: boolean;
};

export interface IPtvFullImportListProps {
  className?: string;
  allParticipants: ParticipantDecisionWithConflict[];
  participationDecision: {
    [key: string]: ParticipantDecision;
  };
  setParticipationDecision: React.Dispatch<
    React.SetStateAction<{
      [key: string]: ParticipantDecision;
    }>
  >;
}

interface AssignedPatientForm {
  selectPatient: PatientSearchingResponse | null;
}

const CustomInput = (props) => <components.Input {...props} maxLength={20} />;

const CustomOption = (
  props,
  t: IFixedNamespaceTFunction<
    keyof typeof PatientOverviewI18n.PatientOverviewList
  >
) => {
  const patient = props.data;
  const birthdateString = getDateOfBirth(patient.dateOfBirth).value;

  return (
    <components.Option {...props}>
      <Flex column auto>
        <Flex w="100%" justify="space-between">
          <BodyTextM whiteSpace="normal">
            {nameUtils.getPatientName(patient)} -{' '}
            <span>{birthdateString} </span>
            <span>
              (
              {patient.dateOfBirth?.isValidDOB
                ? getAge(new Date(patient.dOB)) + ' J.'
                : '?'}
              )
            </span>
          </BodyTextM>
          <div>
            {isEVCase(patient.insuranceInfo, datetimeUtil.now(), undefined) && (
              <Tooltip content={t('notreadcard')} position="bottom">
                <Svg
                  className="icon-card"
                  src={readCardIcon}
                  height={16}
                  width={16}
                />
              </Tooltip>
            )}
          </div>
        </Flex>
        <div className="searchLightResult">
          <span>
            {patient.typeOfInsurance === TypeOfInsurance.Public ? (
              <span className="public">{'K'}</span>
            ) : (
              <span className="private">{'P'}</span>
            )}{' '}
          </span>

          {patient.insuranceNumber && (
            <>• {<span>{patient.insuranceNumber}</span>}</>
          )}
        </div>
      </Flex>
    </components.Option>
  );
};

function PtvFullImportList({
  className,
  allParticipants,
  participationDecision,
  setParticipationDecision,
}: IPtvFullImportListProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });

  const { t: tAssignPatientDialog } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol.assignPatientDialog
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol.assignPatientDialog',
  });

  const { t: tPatientOverviewList } = I18n.useTranslation<
    keyof typeof PatientOverviewI18n.PatientOverviewList
  >({ namespace: 'PatientOverview', nestedTrans: 'PatientOverviewList' });

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const patientFileStore = usePatientFileStore();

  const inputSuggestionRef = useRef<any>(null);
  const formikRef = useRef<FormikProps<AssignedPatientForm>>(null);
  const searchPatients = useMutationSearchPatients();

  const [participantDecisionSelectedId, setParticipantDecisionSelectedId] =
    useState<string>('');

  const [isOpenResolveConflict, setOpenResolveConflict] =
    useState<boolean>(false);

  const [assignPatient, setAssignPatient] =
    useState<ParticipantDecision | null>(null);

  const [defaultOptions, setDefaultOptions] = useState<
    PatientSearchingResponse[]
  >([]);

  const handleGetList = async (query, callback) => {
    const resp = await searchPatients.mutateAsync({
      searchingKeyword: query,
      searchingType: SearchingType.PatientName,
    });

    const mapData = (resp.data.patients || []).map((item) => ({
      ...item,
      data: item,
      label: nameUtils.getPatientName(item),
      value: nameUtils.getPatientName(item),
    }));

    callback(mapData);
  };

  const handleOnFocus = (event, form, field) => {
    form.setTouched({
      ...form.touched,
      [field.name]: true,
    });
    handleGetList(event.target.value, (data: PatientSearchingResponse[]) => {
      setDefaultOptions(data);
    });
  };

  const handleCreatePatient = () => {
    patientFileActions.setPreventRedirectCreatePatient(true);
    handleCreateNewPatientByKeyCode();
    inputSuggestionRef.current?.blur();
  };

  const handleAssignNewPatient = () => {
    const createdPatient = patientFileStore.createPatient;
    if (createdPatient) {
      handleGetList(
        nameUtils.getPatientName(createdPatient.personalInfo),
        (data: PatientSearchingResponse[]) => {
          const matchedData = data.find(
            (datum) => datum.id === createdPatient.patientId
          );

          setDefaultOptions(data);
          formikRef.current?.setFieldValue('selectPatient', matchedData);
        }
      );
    }
  };

  const onCloseDialog = () => {
    setAssignPatient(null);
    patientFileActions.setPreventRedirectCreatePatient(false);
    patientFileActions.setCreatePatient(undefined as unknown as PatientInfo);
  };

  const updateValueField = <T,>(
    key: string,
    type: 'current' | 'hpm',
    data: T,
    newValue: string | number
  ): T => {
    const isCurrent = type === 'current';
    const assignKey = isCurrent ? `local${key}` : `hpm${key}`;

    return {
      ...data,
      [assignKey]: {
        ...data[assignKey],
        value: newValue,
      },
    } as T;
  };

  const initialValues = useMemo((): AssignedPatientForm => {
    return {
      selectPatient: null,
    };
  }, []);

  const checkValidation = async (values: AssignedPatientForm) => {
    const errors = {};

    if (!values.selectPatient) {
      errors['selectPatient'] = tAssignPatientDialog('selectPatientRequired');
    }

    return errors;
  };

  const handleSubmit = (values: AssignedPatientForm) => {
    const { selectPatient } = values;
    if (!assignPatient || !selectPatient) {
      return;
    }
    setParticipationDecision((prevValues) => ({
      ...prevValues,
      [assignPatient.id]: {
        ...assignPatient,
        patientId: selectPatient.id,
        firstName: updateValueField(
          'FirstName',
          'current',
          assignPatient.firstName,
          selectPatient.firstName
        ) as FirstName,
        lastName: updateValueField(
          'LastName',
          'current',
          assignPatient.lastName,
          selectPatient.lastName
        ) as LastName,
        gender: updateValueField(
          'Gender',
          'current',
          assignPatient.gender,
          selectPatient.gender
        ) as Gender,
        dob: updateValueField(
          'DOB',
          'current',
          assignPatient.dob,
          selectPatient.dOB
        ) as Dob,
        status: updateValueField(
          'Status',
          'current',
          assignPatient.status,
          PatientParticipationStatus.PatientParticipation_Active
        ) as Status,
        contractBeginDate: updateValueField(
          'ContractBeginDate',
          'current',
          assignPatient.contractBeginDate,
          selectPatient.insuranceInfo?.startDate || 0
        ) as ContractBeginDate,
        insuranceNumber: updateValueField(
          'InsuranceNumber',
          'current',
          assignPatient.insuranceNumber,
          selectPatient.insuranceInfo?.insuranceNumber || 0
        ) as InsuranceNumber,
        ikNumber: updateValueField(
          'IkNumber',
          'current',
          assignPatient.ikNumber,
          selectPatient.insuranceInfo?.ikNumber || 0
        ) as IkNumber,
        reason: updateValueField(
          'Reason',
          'current',
          assignPatient.reason,
          ''
        ) as Reason,
      },
    }));
    alertSuccessfully(tAssignPatientDialog('patientAssigned'));
    onCloseDialog();
  };

  const columnsData = useMemo(() => {
    return [
      {
        width: '250px',
        name: t('firstName'),
        cell: (participantDecisionItem: ParticipantDecisionWithConflict) => {
          // Handle different data structures for different participant groups
          let lastName = 'N/A';
          let firstName = 'N/A';
          let dob: NumberSelection | undefined = undefined;

          // Try different data access patterns
          if (participantDecisionItem?.lastName?.localLastName?.value) {
            lastName = participantDecisionItem.lastName.localLastName.value;
          } else if (participantDecisionItem?.lastName?.hpmLastName?.value) {
            lastName = participantDecisionItem.lastName.hpmLastName.value;
          } else if (participantDecisionItem?.lastName?.localLastName?.value) {
            lastName = participantDecisionItem.lastName.localLastName.value;
          } else if (participantDecisionItem?.lastName?.hpmLastName?.value) {
            lastName = participantDecisionItem.lastName.hpmLastName.value;
          } else if (typeof participantDecisionItem?.lastName === 'string') {
            lastName = participantDecisionItem.lastName;
          }

          if (participantDecisionItem?.firstName?.localFirstName?.value) {
            firstName = participantDecisionItem.firstName.localFirstName.value;
          } else if (participantDecisionItem?.firstName?.hpmFirstName?.value) {
            firstName = participantDecisionItem.firstName.hpmFirstName.value;
          } else if (
            participantDecisionItem?.firstName?.localFirstName?.value
          ) {
            firstName = participantDecisionItem.firstName.localFirstName.value;
          } else if (participantDecisionItem?.firstName?.hpmFirstName?.value) {
            firstName = participantDecisionItem.firstName.hpmFirstName.value;
          } else if (typeof participantDecisionItem?.firstName === 'string') {
            firstName = participantDecisionItem.firstName;
          }

          if (participantDecisionItem?.dob?.hpmDOB) {
            dob = participantDecisionItem.dob.hpmDOB;
          } else if (participantDecisionItem?.dob?.localDOB) {
            dob = participantDecisionItem.dob.localDOB;
          }

          return (
            <Flex column gap={4}>
              <div>
                {lastName}, {firstName}
              </div>
              <div>
                <PTVImportDate date={dob} />
              </div>
            </Flex>
          );
        },
      },
      {
        width: '140px',
        name: t('insuranceNumber'),
        cell: (participantDecisionItem: ParticipantDecisionWithConflict) => {
          let insuranceNumber = 'N/A';

          if (
            participantDecisionItem?.insuranceNumber?.localInsuranceNumber
              ?.value
          ) {
            insuranceNumber =
              participantDecisionItem.insuranceNumber.localInsuranceNumber
                .value;
          } else if (
            participantDecisionItem?.insuranceNumber?.hpmInsuranceNumber?.value
          ) {
            insuranceNumber =
              participantDecisionItem.insuranceNumber.hpmInsuranceNumber.value;
          } else if (
            typeof participantDecisionItem?.insuranceNumber === 'string'
          ) {
            insuranceNumber = participantDecisionItem.insuranceNumber;
          }

          return (
            <Flex column gap={4}>
              {insuranceNumber}
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('status'),
        cell: (participantDecisionItem: ParticipantDecisionWithConflict) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem?.status?.localStatus?.value}
              />
              <PTVImportDate
                prefix={t('beginDate')}
                date={
                  participantDecisionItem?.contractBeginDate
                    ?.localContractBeginDate
                }
              />
              <PTVImportDate
                prefix={t('endDate')}
                date={
                  participantDecisionItem?.contractEndDate?.localContractEndDate
                }
              />
              <PTVTreatmentType
                status={participantDecisionItem?.status?.localStatus?.value}
                treatmentType={
                  participantDecisionItem?.treatmentType?.localTreatmentType
                    ?.value
                }
              />
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('statusPtv'),
        cell: (participantDecisionItem: ParticipantDecisionWithConflict) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem?.status?.hpmStatus?.value}
              />
              <PTVImportDate
                prefix={t('beginDate')}
                date={
                  participantDecisionItem?.contractBeginDate
                    ?.hpmContractBeginDate
                }
              />
              <PTVImportDate
                prefix={t('endDate')}
                date={
                  participantDecisionItem?.contractEndDate?.hpmContractEndDate
                }
              />
              <PTVTreatmentType
                status={participantDecisionItem?.status?.hpmStatus?.value}
                treatmentType={
                  participantDecisionItem?.treatmentType?.hpmTreatmentType
                    ?.value
                }
              />
            </Flex>
          );
        },
      },
      {
        name: t('reason'),
        cell: (participantDecisionItem: ParticipantDecisionWithConflict) => {
          return (
            <span>
              {participantDecisionItem?.reason?.hpmReason?.value || ''}
            </span>
          );
        },
      },
      {
        name: t('hint'),
        cell: (participantDecisionItem: ParticipantDecisionWithConflict) => {
          return <span>{participantDecisionItem.hints?.join(', ')}</span>;
        },
      },
      {
        id: 'action',
        name: t('actions'),
        cell: (participantDecisionItem: ParticipantDecisionWithConflict) => {
          const hasConflict = participantDecisionItem.hasConflict || false;
          const isResolved =
            participationDecision[participantDecisionItem.id]?.conflictResolved;
          const typeGroup = participantDecisionItem.typeGroupDecision;
          // For missing in PTV (exists in system but not in PTV), just show data
          if (typeGroup === TypeGroupDecision.MissingGroupIV) {
            return null;
          }

          // Check if this is a missing IV patient (exists in PTV but not in system)
          // These patients typically have hpm values but might not have local values properly set
          const isMissingIVPatient =
            typeGroup === TypeGroupDecision.MissingGroupPTV;

          // For missing in system (exists in PTV but not in system), show assign patient button

          if (isMissingIVPatient) {
            const isAssigned =
              participationDecision[participantDecisionItem.id]?.patientId &&
              participationDecision[participantDecisionItem.id]?.patientId !==
              '00000000-0000-0000-0000-000000000000';

            return (
              <Flex
                justify="flex-end"
                align="center"
                gap={8}
                style={{ width: '100%' }}
              >
                {isAssigned && (
                  <BodyTextS color={COLOR.TEXT_PRIMARY_BLACK}>
                    {t('assignTo', {
                      patientName: nameUtils.getPatientName({
                        firstName:
                          participationDecision[participantDecisionItem.id]
                            .firstName.localFirstName.value,
                        lastName:
                          participationDecision[participantDecisionItem.id]
                            .lastName.localLastName.value,
                      }),
                    })}
                  </BodyTextS>
                )}
                <Button
                  className="assign-patient-btn"
                  intent={!isAssigned ? Intent.PRIMARY : Intent.NONE}
                  outlined
                  minimal
                  onClick={() => {
                    setParticipantDecisionSelectedId(
                      participantDecisionItem.id
                    );

                    if (!participationDecision[participantDecisionItem.id]) {
                      setParticipationDecision((prevValues) => ({
                        ...prevValues,
                        [participantDecisionItem.id]: {
                          ...participantDecisionItem,
                          patientId: '00000000-0000-0000-0000-000000000000', // Ensure patientId is not set until form submission
                        },
                      }));
                    }

                    setAssignPatient(participantDecisionItem);
                  }}
                >
                  {t('assignPatient')}
                </Button>
                {isAssigned && (
                  <Svg
                    src="/images/check-circle-solid.svg"
                    width={16}
                    height={16}
                  />
                )}
              </Flex>
            );
          }

          // For conflict participants, show resolve conflict button
          if (hasConflict) {
            return (
              <Flex
                justify="flex-end"
                align="center"
                gap={8}
                style={{ width: '100%' }}
              >
                <Button
                  className="resolve-conflict-btn"
                  intent={!isResolved ? Intent.PRIMARY : Intent.NONE}
                  outlined
                  minimal
                  onClick={() => {
                    setParticipantDecisionSelectedId(
                      participantDecisionItem.id
                    );

                    if (!participationDecision[participantDecisionItem.id]) {
                      setParticipationDecision((prevValues) => ({
                        ...prevValues,
                        [participantDecisionItem.id]: {
                          ...participantDecisionItem,
                          patientId: '00000000-0000-0000-0000-000000000000', // Ensure patientId is not set until proper assignment
                        },
                      }));
                    }

                    setOpenResolveConflict(true);
                  }}
                >
                  {t(isResolved ? 'conflictResolved' : 'resolveConflictBtn')}
                </Button>
                <Svg
                  src={
                    isResolved
                      ? '/images/check-circle-solid.svg'
                      : '/images/x-circle-solid.svg'
                  }
                  width={16}
                  height={16}
                />
              </Flex>
            );
          }

          // Default: no action for other participant types
          return null;
        },
      },
    ];
  }, [participationDecision, allParticipants]);

  const onSaveResolveParticipant = (
    participantDecisionItem: ParticipantDecision
  ) => {
    setParticipationDecision((prevValues) => ({
      ...prevValues,
      [participantDecisionItem.id]: {
        ...participantDecisionItem,
        conflictResolved: true,
      },
    }));
    setOpenResolveConflict(false);
  };

  useEffect(() => {
    if (patientFileStore.createPatient) {
      handleAssignNewPatient();
    }
  }, [patientFileStore.createPatient]);

  return (
    <Flex column className={className} gap={8}>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        data={allParticipants}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      {isOpenResolveConflict &&
        !!participationDecision[participantDecisionSelectedId] && (
          <PtvImportResolveParticipant
            participantDecision={
              participationDecision[participantDecisionSelectedId]
            }
            openResolveParticipant
            onCloseResolveParticipant={() => setOpenResolveConflict(false)}
            onSaveResolveParticipant={onSaveResolveParticipant}
          />
        )}
      {assignPatient && (
        <Dialog
          className={className}
          isOpen
          title={tAssignPatientDialog('title')}
          onClose={onCloseDialog}
        >
          <Formik<AssignedPatientForm>
            innerRef={formikRef}
            initialValues={initialValues}
            enableReinitialize
            validate={checkValidation}
            onSubmit={handleSubmit}
          >
            {({ errors, submitCount, touched, dirty, values }) => (
              <Form>
                <div className={Classes.DIALOG_BODY}>
                  <Flex my={24} mx={16} gap={4} column>
                    <FormGroup2
                      label={tAssignPatientDialog('selectPatient')}
                      name="selectPatient"
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name="selectPatient">
                        {({ field, form }) => (
                          <InputSuggestion
                            ref={inputSuggestionRef}
                            defaultInputValue={nameUtils.getPatientName(
                              field.value
                            )}
                            isClearable
                            defaultValue={field.value?.id}
                            defaultOptions={defaultOptions}
                            cacheOptions
                            isLoading={searchPatients.isPending}
                            loadOptions={(query, callback) => {
                              handleGetList(query, callback);
                            }}
                            keyCode="value"
                            menuPlacement="auto"
                            noOptionsMessage={() => (
                              <Flex gap={8} justify="center">
                                <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER}>
                                  {tAssignPatientDialog('noResultFound')}
                                </BodyTextM>
                                <BodyTextM
                                  className="cursor-pointer"
                                  color={COLOR.TEXT_INFO}
                                  fontWeight={600}
                                  onClick={handleCreatePatient}
                                >
                                  {tAssignPatientDialog('createNewPatient')}
                                </BodyTextM>
                              </Flex>
                            )}
                            styles={{
                              ...DEFAULT_INPUT_SUGGESTION_STYLE_CONFIG(),
                            }}
                            components={{
                              ...DEFAULT_SELECT_COMPONENT_CONFIG,
                              Option: (props) =>
                                CustomOption(props, tPatientOverviewList),
                              Input: CustomInput,
                            }}
                            onChange={(data) => {
                              if (
                                !data ||
                                !field.value ||
                                data.id !== field.value.id
                              ) {
                                form.setFieldValue(field.name, data);
                              }
                            }}
                            onFocus={(event) =>
                              handleOnFocus(event, form, field)
                            }
                          />
                        )}
                      </Field>
                    </FormGroup2>
                    {!!values.selectPatient && (
                      <Flex
                        p="8px 16px"
                        gap={4}
                        column
                        style={{
                          backgroundColor: COLOR.BACKGROUND_ZEBRA,
                        }}
                      >
                        <Flex align="center" gap={8}>
                          <BodyTextS
                            className="flex-1"
                            color={COLOR.TEXT_SECONDARY_NAVAL2}
                            fontWeight={500}
                            textTransform="uppercase"
                          >
                            {tAssignPatientDialog('dob')}
                          </BodyTextS>
                          <BodyTextM
                            color={COLOR.TEXT_PRIMARY_BLACK}
                            style={{ flex: 2 }}
                          >
                            {
                              getDateOfBirth(values.selectPatient.dateOfBirth)
                                .value
                            }{' '}
                            (
                            {values.selectPatient.dateOfBirth?.isValidDOB
                              ? getAge(new Date(values.selectPatient.dOB)) +
                              ' J.'
                              : '?'}
                            )
                          </BodyTextM>
                        </Flex>
                        <Flex align="center" gap={8}>
                          <BodyTextS
                            className="flex-1"
                            color={COLOR.TEXT_SECONDARY_NAVAL2}
                            fontWeight={500}
                            textTransform="uppercase"
                          >
                            {tAssignPatientDialog('insuranceNumber')}
                          </BodyTextS>
                          <BodyTextM
                            color={COLOR.TEXT_PRIMARY_BLACK}
                            style={{ flex: 2 }}
                          >
                            {values.selectPatient.insuranceNumber}
                          </BodyTextM>
                        </Flex>
                        <Flex gap={8}>
                          <BodyTextS
                            className="flex-1"
                            color={COLOR.TEXT_SECONDARY_NAVAL2}
                            fontWeight={500}
                            textTransform="uppercase"
                          >
                            {tAssignPatientDialog('insuranceInfo')}
                          </BodyTextS>
                          <Flex style={{ flex: 2 }} column gap={4}>
                            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                              {
                                values.selectPatient.insuranceInfo
                                  ?.insuranceCompanyName
                              }
                            </BodyTextM>
                            <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL2}>
                              {tAssignPatientDialog('ik')}{' '}
                              {values.selectPatient.insuranceInfo?.ikNumber}
                            </BodyTextS>
                          </Flex>
                        </Flex>
                      </Flex>
                    )}
                  </Flex>
                </div>
                <div className={Classes.DIALOG_FOOTER}>
                  <Flex className={`${Classes.DIALOG_FOOTER_ACTIONS}`} gap={16}>
                    <Button
                      intent={Intent.PRIMARY}
                      outlined
                      minimal
                      onClick={onCloseDialog}
                    >
                      {tButtonActions('cancelText')}
                    </Button>
                    <Button
                      intent={Intent.PRIMARY}
                      type="submit"
                      disabled={!dirty}
                    >
                      {tButtonActions('assignText')}
                    </Button>
                  </Flex>
                </div>
              </Form>
            )}
          </Formik>
        </Dialog>
      )}
    </Flex>
  );
}

export default PtvFullImportList;
