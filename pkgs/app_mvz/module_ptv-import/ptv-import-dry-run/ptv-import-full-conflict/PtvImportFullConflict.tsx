import {
  BodyTextM,
  BodyTextS,
  Button,
  Flex,
  H2,
  Intent,
} from '@tutum/design-system/components';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  ParticipantDecision,
  PatientParticipationStatus,
} from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { generatePDFBlobFromHTMLExcludeContent } from '@tutum/mvz/_utils/downloadPdfFile';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { Participants } from '@tutum/mvz/module_ptv-import/PtvImport.service';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import React, { useMemo, useRef } from 'react';
import type { ParticipantDecisionWithConflict } from './PtvFullImportList';
import PtvFullImportList from './PtvFullImportList.styled';

export interface IPtvImportFullConflictProps {
  className?: string;
  data: Participants | null;
  doctorImported: IEmployeeProfile | null;
  currentEmployee: IEmployeeProfile | null;
  participationDecision: {
    [key: string]: ParticipantDecision;
  };
  setParticipationDecision: React.Dispatch<
    React.SetStateAction<{
      [key: string]: ParticipantDecision;
    }>
  >;
  summaryDataDryRun: {
    specialGroup: ParticipantDecision[];
    unchangedGroup: ParticipantDecision[];
    newGroup: ParticipantDecision[];
    terminatedGroup: ParticipantDecision[];
    requestedGroup: ParticipantDecision[];
    rejectGroup: ParticipantDecision[];
    missingPtvGroup: ParticipantDecision[];
    missingIvGroup: ParticipantDecision[];
    beforeParticipantCount: number;
    afterParticipantCount: number;
  } | null;
}

function PtvImportFullConflict({
  className,
  data,
  doctorImported,
  currentEmployee,
  participationDecision,
  setParticipationDecision,
  summaryDataDryRun,
}: IPtvImportFullConflictProps) {
  const { t } = I18n.useTranslation<keyof typeof PtvImportI18n.PtvImportDryRun>(
    {
      namespace: 'PtvImport',
      nestedTrans: 'PtvImportDryRun',
    }
  );

  const contentRef = useRef<HTMLDivElement | null>(null);

  const nowDate = datetimeUtil.date();
  const updateTime = data?.updateTime
    ? datetimeUtil.date(data.updateTime)
    : null;
  const quarter = data?.quarter || 0;
  const year = data?.year || 0;

  // Merge all participants into a single array
  const allParticipants = useMemo(() => {
    if (!summaryDataDryRun) return [];

    const merged: ParticipantDecisionWithConflict[] = [];

    // Add all participant groups with hasConflict flag for special group
    summaryDataDryRun.specialGroup.forEach((participant) => {
      // const isConflictTreatmentType =
      //   participant.status.localStatus.value ===
      //   PatientParticipationStatus.PatientParticipation_Active &&
      //   participant.status.hpmStatus.value ===
      //   PatientParticipationStatus.PatientParticipation_Active &&
      //   participant.treatmentType.localTreatmentType.value !==
      //   participant.treatmentType.hpmTreatmentType.value;

      // // `conflict treatment type` is resolved by PTV automatically
      // if (isConflictTreatmentType) {
        const cloneParticipant = { ...participant };
        // TODO: util set selected value
        cloneParticipant.ikNumber.localIkNumber.selected = false;
        cloneParticipant.insuranceNumber.localInsuranceNumber.selected = false;
        cloneParticipant.status.localStatus.selected = false;
        cloneParticipant.treatmentType.localTreatmentType.selected = false;
        cloneParticipant.firstName.localFirstName.selected = false;
        cloneParticipant.lastName.localLastName.selected = false;
        cloneParticipant.reason.localReason.selected = false;
        cloneParticipant.gender.localGender.selected = false;
        cloneParticipant.dob.localDOB.selected = false;
        cloneParticipant.contractBeginDate.localContractBeginDate.selected = false;
        cloneParticipant.contractEndDate.localContractEndDate.selected = false;

        cloneParticipant.ikNumber.hpmIkNumber.selected = true;
        cloneParticipant.insuranceNumber.hpmInsuranceNumber.selected = true;
        cloneParticipant.status.hpmStatus.selected = true;
        cloneParticipant.treatmentType.hpmTreatmentType.selected = true;
        cloneParticipant.firstName.hpmFirstName.selected = true;
        cloneParticipant.lastName.hpmLastName.selected = true;
        cloneParticipant.reason.hpmReason.selected = true;
        cloneParticipant.gender.hpmGender.selected = true;
        cloneParticipant.dob.hpmDOB.selected = true;
        cloneParticipant.contractBeginDate.hpmContractBeginDate.selected = true;
        cloneParticipant.contractEndDate.hpmContractEndDate.selected = true;

        cloneParticipant.conflictResolved = true;
        // merged.push({ ...cloneParticipant, hasConflict: false });
        setParticipationDecision((prev) => ({
          ...prev,
          [cloneParticipant.id]: {
            ...cloneParticipant,
          },
        }));
      //   return;
      // }

      // merged.push({ ...participant, hasConflict: true });
    });

    // summaryDataDryRun.unchangedGroup.forEach((participant) => {
    //   merged.push({ ...participant, hasConflict: false });
    // });

    // summaryDataDryRun.newGroup.forEach((participant) => {
    //   merged.push({ ...participant, hasConflict: false });
    // });

    // summaryDataDryRun.terminatedGroup.forEach((participant) => {
    //   merged.push({ ...participant, hasConflict: false });
    // });

    // summaryDataDryRun.requestedGroup.forEach((participant) => {
    //   merged.push({ ...participant, hasConflict: false });
    // });

    // summaryDataDryRun.rejectGroup.forEach((participant) => {
    //   merged.push({ ...participant, hasConflict: false });
    // });

    summaryDataDryRun.missingPtvGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    summaryDataDryRun.missingIvGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    const sortedParticipants = merged.sort((a, b) => {
      // Define status priority order
      const statusPriority = {
        [PatientParticipationStatus.PatientParticipation_Active]: 1,
        [PatientParticipationStatus.PatientParticipation_Terminated]: 2,
        [PatientParticipationStatus.PatientParticipation_Requested]: 3, // PENDING is REQUESTED
        [PatientParticipationStatus.PatientParticipation_Cancelled]: 4,
        [PatientParticipationStatus.PatientParticipation_Rejected]: 5,
        [PatientParticipationStatus.PatientParticipation_Faulty]: 6, // Fallback for any faulty status
      };

      // Get the selected status value for each participant
      const getSelectedStatus = (
        participant: ParticipantDecisionWithConflict
      ) => {
        return participant.status.hpmStatus.value;
      };

      // Get the selected last name for each participant
      const getSelectedLastName = (
        participant: ParticipantDecisionWithConflict
      ) => {
        return participant.lastName.hpmLastName.value || '';
      };

      const statusA = getSelectedStatus(a);
      const statusB = getSelectedStatus(b);

      const priorityA = statusPriority[statusA] || 999;
      const priorityB = statusPriority[statusB] || 999;

      // First sort by status priority
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // If status is the same, sort alphabetically by last name
      const lastNameA = getSelectedLastName(a);
      const lastNameB = getSelectedLastName(b);

      return lastNameA.localeCompare(lastNameB);
    });

    return sortedParticipants;
  }, [summaryDataDryRun]);

  const handleDownloadPdf = async () => {
    const contentEle = contentRef.current;

    if (!contentEle) {
      return;
    }

    const elementsToRemove = [
      '[data-test-id="test-run-report-download-pdf"]',
      '[data-column-id="action"]',
    ];

    generatePDFBlobFromHTMLExcludeContent(
      contentEle,
      elementsToRemove,
      'full-import.pdf',
      {
        orientation: 'l',
        unit: 'px',
        format: 'a3',
        width: 1500,
        windowWidth: 1500,
        scale: 0.55,
      }
    );
  };

  return (
    <Flex column gap={24} w="100%" className={className} ref={contentRef}>
      <Flex column gap={8}>
        <Flex gap={16} align="center" justify="space-between">
          <H2 margin="0 0 8px">{t('titleFullImport')}</H2>
          <Button
            intent={Intent.PRIMARY}
            outlined
            minimal
            data-test-id="test-run-report-download-pdf"
            onClick={handleDownloadPdf}
          >
            {t('downloadPdf')}
          </Button>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {t('fullImportDescription')}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('doctor')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {t('doctorInfo', {
              doctorName: nameUtils.getDoctorName(doctorImported),
              doctorLanr: doctorImported?.lanr,
              doctorHavgVpId: doctorImported?.havgVpId,
            })}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('contract')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {data?.contractId}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('quarterTime')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            Q{quarter} - {year}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('importInfo')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {nameUtils.getDoctorName(currentEmployee)}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('importDate')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {toDateFormat(updateTime || nowDate, {
              dateFormat: 'dd.MM.yyyy',
              timeFormat: 'hh:mm:ss',
            })}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {t('totalAmountContractAfterImport', {
              contract: data?.contractId,
              count: summaryDataDryRun?.afterParticipantCount,
            })}
          </BodyTextM>
        </Flex>
      </Flex>
      <PtvFullImportList
        allParticipants={allParticipants}
        participationDecision={participationDecision}
        setParticipationDecision={setParticipationDecision}
      />
    </Flex>
  );
}

export default PtvImportFullConflict;
