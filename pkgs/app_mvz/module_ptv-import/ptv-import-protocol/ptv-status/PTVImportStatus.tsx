import { BodyTextS } from '@tutum/design-system/components';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  NumberSelection,
  PatientParticipationStatus,
} from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';

export const PTVImportStatus = (props: {
  status: PatientParticipationStatus;
}) => {
  const { status } = props;
  const statusText = {
    [PatientParticipationStatus.PatientParticipation_Active]: 'Aktiv',
    [PatientParticipationStatus.PatientParticipation_Requested]: 'Beantragt',
    [PatientParticipationStatus.PatientParticipation_Rejected]: 'Abgelehnt',
    [PatientParticipationStatus.PatientParticipation_Terminated]: 'Beendet',
    [PatientParticipationStatus.PatientParticipation_Cancelled]: 'Storniert',
  };

  const commonStyle = {
    fontWeight: 'bold',
  };
  const style = {
    [PatientParticipationStatus.PatientParticipation_Active]: {
      color: COLOR.TEXT_POSITIVE,
    },
    [PatientParticipationStatus.PatientParticipation_Requested]: {
      color: COLOR.TEXT_WARNING,
    },
    [PatientParticipationStatus.PatientParticipation_Rejected]: {
      color: COLOR.TEXT_NEGATIVE,
    },
    [PatientParticipationStatus.PatientParticipation_Terminated]: {
      color: COLOR.TEXT_NEGATIVE,
    },
    [PatientParticipationStatus.PatientParticipation_Cancelled]: {
      color: COLOR.TEXT_NEGATIVE,
    },
  };

  if (!status) return null;

  return (
    <div style={{ ...commonStyle, ...style[status] }}>{statusText[status]}</div>
  );
};

export const PTVImportDate = (props: {
  prefix?: string;
  date?: NumberSelection;
  fallback?: string;
  isHpm?: boolean;
}) => {
  const { prefix, date, fallback = '--', isHpm = false } = props;
  const dateValue = date?.value || 0;
  if (!date?.value) return null;
  return (
    <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
      {prefix && `${prefix}: `}
      {date?.value
        ? toDateFormat(new Date(dateValue), {
          dateFormat: 'dd.MM.yyyy',
          utc: isHpm,
        })
        : fallback}
    </BodyTextS>
  );
};

export const PTVTreatmentType = ({ treatmentType, status }: {
  treatmentType: string;
  status: PatientParticipationStatus;
}) => {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });

  const { t: tPtvImportResolve } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportResolve
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportResolve',
  });

  const mapTreatmentTypeTrans = (value: string) => {
    if (!value) {
      return '';
    }

    return tPtvImportResolve(
      `${value.toLowerCase()}Type` as keyof typeof PtvImportI18n.PtvImportResolve
    );
  };

  if (
    !treatmentType ||
    status !== PatientParticipationStatus.PatientParticipation_Active
  )
    return null;

  return (
    <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
      {mapTreatmentTypeTrans(treatmentType)}
    </BodyTextS>
  );
};
