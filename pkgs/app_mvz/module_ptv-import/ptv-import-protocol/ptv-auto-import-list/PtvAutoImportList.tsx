import {
  Body<PERSON>extM,
  Button,
  Flex,
  H3,
  Intent,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  ParticipantDecision,
  PatientParticipationStatus,
} from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import { generatePDFBlobFromHTMLExcludeContent } from '@tutum/mvz/_utils/downloadPdfFile';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import {
  PTVImportTable,
  Participants,
} from '@tutum/mvz/module_ptv-import/PtvImport.service';
import {
  PTVImportDate,
  PTVImportStatus,
} from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-status/PTVImportStatus';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';

import { useMemo, useRef } from 'react';
import RenderHeader from './RenderHeaderInfo';

export interface IPtvAutoImportListProps {
  className?: string;
  autoImport: PTVImportTable[];
  data: Participants | null;
  doctorImported: IEmployeeProfile | null;
  currentEmployee: IEmployeeProfile | null;
}

function PtvAutoImportList({
  className,
  autoImport,
  data,
  doctorImported,
  currentEmployee,
}: IPtvAutoImportListProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });
  const contentRef = useRef<HTMLDivElement | null>(null);

  const columnsData = useMemo(() => {
    return [
      {
        width: '250px',
        name: t('firstName'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <div>
                {participantDecisionItem.lastName.localLastName.value},{' '}
                {participantDecisionItem.firstName.localFirstName.value}
              </div>
              <div>
                <PTVImportDate date={participantDecisionItem.dob.localDOB} />
              </div>
            </Flex>
          );
        },
      },
      {
        width: '160px',
        name: t('insuranceNumber'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              {
                participantDecisionItem.insuranceNumber.localInsuranceNumber
                  .value
              }
            </Flex>
          );
        },
      },
      {
        width: '170px',
        name: t('status'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          const isHidePrefixStartDate = [
            PatientParticipationStatus.PatientParticipation_Requested,
            PatientParticipationStatus.PatientParticipation_Rejected,
            PatientParticipationStatus.PatientParticipation_Cancelled,
          ].includes(participantDecisionItem.status.hpmStatus.value);

          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.localStatus.value}
              />
              <PTVImportDate
                prefix={isHidePrefixStartDate ? '' : t('beginDate')}
                date={
                  participantDecisionItem.contractBeginDate
                    .localContractBeginDate
                }
              />
              <PTVImportDate
                prefix={t('endDate')}
                date={
                  participantDecisionItem.contractEndDate.localContractEndDate
                }
              />
            </Flex>
          );
        },
      },
      {
        width: '170px',
        name: t('statusPtv'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          const hasShowDate = [
            PatientParticipationStatus.PatientParticipation_Active,
            PatientParticipationStatus.PatientParticipation_Requested,
          ].includes(participantDecisionItem.status.hpmStatus.value);
          const isHidePrefixStartDate = [
            PatientParticipationStatus.PatientParticipation_Requested,
            PatientParticipationStatus.PatientParticipation_Rejected,
            PatientParticipationStatus.PatientParticipation_Cancelled,
          ].includes(participantDecisionItem.status.hpmStatus.value);

          const isTerminated = [
            PatientParticipationStatus.PatientParticipation_Terminated,
          ].includes(participantDecisionItem.status.hpmStatus.value);

          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.hpmStatus.value}
              />
              {!isTerminated && hasShowDate && (
                <>
                  <PTVImportDate
                    prefix={isHidePrefixStartDate ? '' : t('beginDate')}
                    date={
                      participantDecisionItem.contractBeginDate
                        .hpmContractBeginDate
                    }
                  />
                  <PTVImportDate
                    prefix={t('endDate')}
                    date={
                      participantDecisionItem.contractEndDate.hpmContractEndDate
                    }
                  />
                </>
              )}
              {isTerminated && (
                <PTVImportDate
                  prefix={t('endDate')}
                  date={
                    participantDecisionItem.contractEndDate.hpmContractEndDate
                  }
                />
              )}
            </Flex>
          );
        },
      },
      // {
      //   width: '160px',
      //   name: t('requestDate'),
      //   cell: (participantDecisionItem: ParticipantDecision) => {
      //     const hasShowDate = [
      //       PatientParticipationStatus.PatientParticipation_Requested,
      //     ].includes(participantDecisionItem.status.hpmStatus.value);

      //     if (!hasShowDate) {
      //       return;
      //     }

      //     return (
      //       <Flex column gap={4}>
      //         <PTVImportDate
      //           prefix="Request"
      //           isHpm
      //           date={
      //             participantDecisionItem.contractEndDate.hpmContractEndDate
      //           }
      //         />
      //       </Flex>
      //     );
      //   },
      // },
      {
        name: t('reason'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              {participantDecisionItem.reason.hpmReason.value || '--'}
            </Flex>
          );
        },
      },
      {
        width: '170px',
        name: t('rejectDate'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          const hasShowDate = [
            PatientParticipationStatus.PatientParticipation_Rejected,
          ].includes(participantDecisionItem.status.hpmStatus.value);

          if (!hasShowDate) {
            return;
          }

          return (
            <Flex column gap={4}>
              <PTVImportDate
                date={
                  participantDecisionItem.contractBeginDate.hpmContractBeginDate
                }
              />
            </Flex>
          );
        },
      },
    ];
  }, []);

  const total = useMemo(() => {
    return autoImport.reduce((total, datum) => {
      const activePatient = datum.patient.filter(
        patient => [
          PatientParticipationStatus.PatientParticipation_Active,
          PatientParticipationStatus.PatientParticipation_Requested
        ].includes(patient.status.localStatus.value)
      );

      return total + activePatient.length;
    }, 0);
  }, [autoImport]);

  const patientAmount = (index: number) => {
    const total = autoImport[index].patient.length;
    return total === 0 ? t('zeroPatient') : t('patients', { total });
  };

  const handleDownloadPdf = async () => {
    const contentEle = contentRef.current;

    if (!contentEle) {
      return;
    }

    const elementsToRemove = ['[data-test-id="test-run-report-download-pdf"]'];

    generatePDFBlobFromHTMLExcludeContent(
      contentEle,
      elementsToRemove,
      'auto-import.pdf',
      {
        orientation: 'l',
        unit: 'px',
        format: 'a3',
        width: 1500,
        windowWidth: 1500,
        scale: 0.55,
      }
    );
  };

  return (
    <Flex column className={className} ref={contentRef}>
      <Flex gap={24} mb={16}>
        <Flex column gap={16} className="flex-1">
          <H3 color={COLOR.TEXT_PRIMARY_BLACK}>{t('autoImportTag')}</H3>
          <RenderHeader
            doctorImported={doctorImported}
            currentEmployee={currentEmployee}
            data={data}
            total={total}
          />
        </Flex>
        <Flex>
          <Button
            intent={Intent.PRIMARY}
            outlined
            minimal
            data-test-id="test-run-report-download-pdf"
            onClick={handleDownloadPdf}
          >
            {t('downloadPdf')}
          </Button>
        </Flex>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        data={[]}
        noDataComponent={<></>}
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[0].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(0)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[0].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[1].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(1)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[1].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[2].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(2)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[2].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[3].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(3)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[3].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[4].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(4)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[4].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
    </Flex>
  );
}

export default PtvAutoImportList;
