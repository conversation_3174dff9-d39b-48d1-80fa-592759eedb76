import React from 'react';
import { BodyTextS, Flex } from '@tutum/design-system/components';
import { BodyTextM } from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import I18n from '@tutum/infrastructure/i18n';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { Participants } from '@tutum/mvz/module_ptv-import/PtvImport.service';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

interface RenderHeaderProps {
  doctorImported: IEmployeeProfile | null;
  currentEmployee: IEmployeeProfile | null;
  data: Participants | null;
  total: number;
}

const RenderHeader = ({ doctorImported, currentEmployee, data, total }: RenderHeaderProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });

  const nowDate = datetimeUtil.date();
  const updateTime = data?.updateTime
    ? datetimeUtil.date(data.updateTime)
    : null;
  const quarter = data?.quarter || 0;
  const year = data?.year || 0;

  return (
    <>
      <Flex align="center" gap={8}>
        <BodyTextS
          className="flex-1"
          color={COLOR.TEXT_SECONDARY_NAVAL2}
          fontWeight={500}
          textTransform="uppercase"
        >
          {t('doctor')}
        </BodyTextS>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
          {t('doctorInfo', {
            doctorName: nameUtils.getDoctorName(doctorImported),
            doctorLanr: doctorImported?.lanr,
            doctorHavgVpId: doctorImported?.havgVpId,
          })}
        </BodyTextM>
      </Flex>
      <Flex align="center" gap={8}>
        <BodyTextS
          className="flex-1"
          color={COLOR.TEXT_SECONDARY_NAVAL2}
          fontWeight={500}
          textTransform="uppercase"
        >
          {t('contract')}
        </BodyTextS>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
          {data?.contractId}
        </BodyTextM>
      </Flex>
      <Flex align="center" gap={8}>
        <BodyTextS
          className="flex-1"
          color={COLOR.TEXT_SECONDARY_NAVAL2}
          fontWeight={500}
          textTransform="uppercase"
        >
          {t('quarterTime')}
        </BodyTextS>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
          Q{quarter} - {year}
        </BodyTextM>
      </Flex>
      <Flex align="center" gap={8}>
        <BodyTextS
          className="flex-1"
          color={COLOR.TEXT_SECONDARY_NAVAL2}
          fontWeight={500}
          textTransform="uppercase"
        >
          {t('importInfo')}
        </BodyTextS>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
          {nameUtils.getDoctorName(currentEmployee)}
        </BodyTextM>
      </Flex>
      <Flex align="center" gap={8}>
        <BodyTextS
          className="flex-1"
          color={COLOR.TEXT_SECONDARY_NAVAL2}
          fontWeight={500}
          textTransform="uppercase"
        >
          {t('importDate')}
        </BodyTextS>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
          {toDateFormat(updateTime || nowDate, {
            dateFormat: 'dd.MM.yyyy',
            timeFormat: 'hh:mm:ss',
          })}
        </BodyTextM>
      </Flex>
      <Flex align="center" gap={8}>
        <BodyTextS
          className="flex-1"
          color={COLOR.TEXT_SECONDARY_NAVAL2}
          fontWeight={500}
          textTransform="uppercase"
        >
          {t('total')}
        </BodyTextS>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
          {t('patients', {
            total,
          })}
        </BodyTextM>
      </Flex>
    </>
  )
}

export default RenderHeader;