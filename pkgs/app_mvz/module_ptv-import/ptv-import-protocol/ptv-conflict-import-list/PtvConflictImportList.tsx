import {
  BodyTextM,
  BodyTextS,
  Flex,
  H3,
  Svg,
} from '@tutum/design-system/components';
import { But<PERSON>, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import { COLOR } from '@tutum/design-system/themes/styles';
import { ParticipantDecision } from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import { generatePDFBlobFromHTMLExcludeContent } from '@tutum/mvz/_utils/downloadPdfFile';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { Participants, PTVImportTable } from '@tutum/mvz/module_ptv-import/PtvImport.service';
import PtvImportResolveParticipant from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-conflict-import-list/ptv-import-resolve-participant/PtvImportResolveParticipant.styled';
import {
  PTVImportDate,
  PTVImportStatus,
  PTVTreatmentType,
} from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-status/PTVImportStatus';

import React, { useMemo, useRef, useState } from 'react';
import RenderHeader from '../ptv-auto-import-list/RenderHeaderInfo';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
export interface IPtvConflictImportListProps {
  className?: string;
  conflictParticipant: PTVImportTable[];
  participationDecision: {
    [key: string]: ParticipantDecision;
  };
  setParticipationDecision: React.Dispatch<
    React.SetStateAction<{
      [key: string]: ParticipantDecision;
    }>
  >;
  showHeader?: boolean;

  doctorImported: IEmployeeProfile | null;
  currentEmployee: IEmployeeProfile | null;
  data: Participants | null;
}

function PtvConflictImportList({
  className,
  conflictParticipant,
  participationDecision,
  setParticipationDecision,
  showHeader = true,
  doctorImported,
  currentEmployee,
  data,
}: IPtvConflictImportListProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });

  const contentRef = useRef<HTMLDivElement | null>(null);

  const [participantDecisionSelectedId, setParticipantDecisionSelectedId] =
    useState<string>('');

  const [isOpenResolveConflict, setOpenResolveConflict] =
    useState<boolean>(false);

  const columnsData = useMemo(() => {
    return [
      {
        width: '250px',
        name: t('firstName'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <div>
                {participantDecisionItem.lastName.localLastName.value},{' '}
                {participantDecisionItem.firstName.localFirstName.value}
              </div>
              <div>
                <PTVImportDate date={participantDecisionItem.dob.hpmDOB} />
              </div>
            </Flex>
          );
        },
      },
      {
        width: '140px',
        name: t('insuranceNumber'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              {
                participantDecisionItem.insuranceNumber.localInsuranceNumber
                  .value
              }
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('status'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.localStatus.value}
              />
              <PTVImportDate
                prefix={t('beginDate')}
                date={
                  participantDecisionItem.contractBeginDate
                    .localContractBeginDate
                }
              />
              <PTVImportDate
                prefix={t('endDate')}
                date={
                  participantDecisionItem.contractEndDate.localContractEndDate
                }
              />
              <PTVTreatmentType
                status={participantDecisionItem.status.localStatus.value}
                treatmentType={
                  participantDecisionItem.treatmentType.localTreatmentType
                    ?.value
                }
              />
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('statusPtv'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.hpmStatus.value}
              />
              <PTVImportDate
                prefix={t('beginDate')}
                date={
                  participantDecisionItem.contractBeginDate.hpmContractBeginDate
                }
              />
              <PTVImportDate
                prefix={t('endDate')}
                date={
                  participantDecisionItem.contractEndDate.hpmContractEndDate
                }
              />
              <PTVTreatmentType
                status={participantDecisionItem.status.hpmStatus.value}
                treatmentType={
                  participantDecisionItem.treatmentType.hpmTreatmentType
                    ?.value
                }
              />
            </Flex>
          );
        },
      },
      {
        name: t('reason'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <span>{participantDecisionItem.reason.hpmReason.value}</span>
          );
        },
      },
      {
        id: 'action',
        width: '160px',
        name: '',
        cell: (participantDecisionItem: ParticipantDecision) => {
          const isResolved =
            participationDecision[participantDecisionItem.id]?.conflictResolved;
          return (
            <Flex justify="space-between" align="center" gap={8}>
              <Button
                className="resolve-conflict-btn"
                intent={!isResolved ? Intent.PRIMARY : Intent.NONE}
                outlined
                minimal
                // disabled={isResolved}
                onClick={() => {
                  setParticipantDecisionSelectedId(participantDecisionItem.id);

                  if (!participationDecision[participantDecisionItem.id]) {
                    setParticipationDecision((prevValues) => ({
                      ...prevValues,
                      [participantDecisionItem.id]: participantDecisionItem,
                    }));
                  }

                  setOpenResolveConflict(true);
                }}
              >
                {t(isResolved ? 'conflictResolved' : 'resolveConflictBtn')}
              </Button>
              <Svg
                src={
                  isResolved
                    ? '/images/check-circle-solid.svg'
                    : '/images/x-circle-solid.svg'
                }
                width={16}
                height={16}
              />
            </Flex>
          );
        },
      },
    ];
  }, [participationDecision]);

  const sortedData = useMemo(() => {
    return conflictParticipant[0].patient.sort((a, b) => {
      const lastNameA = a.lastName.localLastName.value;
      const lastNameB = b.lastName.localLastName.value;

      return lastNameA.localeCompare(lastNameB);
    });
  }, [])

  const onSaveResolveParticipant = (participantDecisionItem) => {
    setParticipationDecision((prevValues) => ({
      ...prevValues,
      [participantDecisionItem.id]: {
        ...participantDecisionItem,
        conflictResolved: true,
      },
    }));
    setOpenResolveConflict(false);
  };

  const handleDownloadPdf = async () => {
    const contentEle = contentRef.current;

    if (!contentEle) {
      return;
    }

    const elementsToRemove = [
      '[data-test-id="test-run-report-download-pdf"]',
      '[data-column-id="action"]',
    ];

    generatePDFBlobFromHTMLExcludeContent(contentEle, elementsToRemove, 'conflict-import.pdf', {
      orientation: 'l',
      unit: 'px',
      format: 'a3',
      width: 1500,
      windowWidth: 1500,
      scale: 0.55,
    });
  };

  return (
    <Flex column className={className} gap={8} ref={contentRef}>
      {showHeader && (
        <>
          <Flex column gap={16} mb={16}>
            <Flex gap={16} align="center" justify="space-between">
              <H3 color={COLOR.TEXT_PRIMARY_BLACK}>{t('conflictImportTag')}</H3>
              <Button
                intent={Intent.PRIMARY}
                outlined
                minimal
                data-test-id="test-run-report-download-pdf"
                onClick={handleDownloadPdf}
              >
                {t('downloadPdf')}
              </Button>
            </Flex>
            <Flex>
              <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                {t('conflictDescription')}
              </BodyTextM>
            </Flex>
            <RenderHeader doctorImported={doctorImported} currentEmployee={currentEmployee} data={data} total={conflictParticipant[0].patient.length} />
          </Flex>
        </>
      )}

      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        data={sortedData}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      {isOpenResolveConflict &&
        !!participationDecision[participantDecisionSelectedId] && (
          <PtvImportResolveParticipant
            participantDecision={
              participationDecision[participantDecisionSelectedId]
            }
            openResolveParticipant
            onCloseResolveParticipant={() => setOpenResolveConflict(false)}
            onSaveResolveParticipant={onSaveResolveParticipant}
          />
        )}
    </Flex>
  );
}

export default PtvConflictImportList;
