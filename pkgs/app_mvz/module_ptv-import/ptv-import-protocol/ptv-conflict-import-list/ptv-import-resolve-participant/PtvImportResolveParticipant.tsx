import {
  BodyTextM,
  BodyTextS,
  Dialog,
  Flex,
  MessageBar,
} from '@tutum/design-system/components';
import { Button, Intent, Radio } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import {
  getCssClass,
  toDateFormat,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { ParticipantDecision } from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { useEffect, useMemo, useState } from 'react';

export interface IPtvImportResolveParticipantProps {
  className?: string;
  participantDecision: ParticipantDecision;
  openResolveParticipant: boolean;
  onCloseResolveParticipant: () => void;
  onSaveResolveParticipant: (participantDecision: ParticipantDecision) => void;
}

interface TableRow {
  key: string;
  label: string;
  value;
  current: string;
  currentSelected: boolean;
  hpm: string;
  hpmSelected: boolean;
  isConflict: boolean;
}

function PtvImportResolveParticipant({
  className,
  onCloseResolveParticipant,
  openResolveParticipant,
  participantDecision,
  onSaveResolveParticipant,
}: IPtvImportResolveParticipantProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportResolve
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportResolve',
  });

  const [currentParticipationDecision, setCurrentParticipationDecision] =
    useState<ParticipantDecision>(participantDecision);

  const onSelectRow = (type: 'current' | 'hpm', row: TableRow) => {
    const capitalizeKey =
      row.key === 'dob' ? 'DOB' : row.key[0].toUpperCase() + row.key.slice(1);

    setCurrentParticipationDecision((prevValue) => {
      return {
        ...prevValue,
        [row.key]: updateSelectedField(capitalizeKey, type, prevValue[row.key]),
      };
    });
  };

  const updateSelectedField = <T,>(
    key: string,
    type: 'current' | 'hpm',
    data: T
  ): T => {
    const isCurrent = type === 'current';

    return {
      [`local${key}`]: {
        value: data[`local${key}`].value,
        selected: isCurrent,
      },
      [`hpm${key}`]: {
        value: data[`hpm${key}`].value,
        selected: !isCurrent,
      },
    } as T;
  };

  const onSelectAll = (type: 'current' | 'hpm') => {
    setCurrentParticipationDecision((prevValue) => {
      return {
        ...prevValue,
        firstName: updateSelectedField('FirstName', type, prevValue.firstName),
        lastName: updateSelectedField('LastName', type, prevValue.lastName),
        gender: updateSelectedField('Gender', type, prevValue.gender),
        dob: updateSelectedField('DOB', type, prevValue.dob),
        status: updateSelectedField('Status', type, prevValue.status),
        contractBeginDate: updateSelectedField(
          'ContractBeginDate',
          type,
          prevValue.contractBeginDate
        ),
        contractEndDate: updateSelectedField(
          'ContractEndDate',
          type,
          prevValue.contractEndDate
        ),
        insuranceNumber: updateSelectedField(
          'InsuranceNumber',
          type,
          prevValue.insuranceNumber
        ),
        ikNumber: updateSelectedField('IkNumber', type, prevValue.ikNumber),
        reason: updateSelectedField('Reason', type, prevValue.reason),
        treatmentType: updateSelectedField(
          'TreatmentType',
          type,
          prevValue.treatmentType
        ),
      };
    });
  };

  const columnsData = useMemo(() => {
    return [
      {
        width: '180px',
        name: 'Field',
        cell: (row: TableRow) => {
          return <span>{row.label}</span>;
        },
      },
      {
        width: '260px',
        name: (
          <Flex justify="space-between" gap={8}>
            <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL} fontWeight={600}>
              {t('keepCurrent')}
            </BodyTextS>
            <BodyTextM
              className="cursor-pointer"
              textTransform="capitalize"
              color={COLOR.TEXT_INFO}
              fontWeight={600}
              onClick={() => onSelectAll('current')}
            >
              {t('selectAll')}
            </BodyTextM>
          </Flex>
        ),
        cell: (row: TableRow) => {
          return (
            <Flex
              w="100%"
              h="100%"
              align="center"
              justify="space-between"
              p="6px"
              gap={12}
              style={{
                backgroundColor: row.isConflict ? COLOR.WARNING_LIGHT : '',
              }}
            >
              {row.current || '--'}
              {row.isConflict && (
                <Radio
                  checked={row.currentSelected}
                  onChange={() => onSelectRow('current', row)}
                />
              )}
            </Flex>
          );
        },
      },
      {
        width: '260px',
        name: (
          <Flex justify="space-between" gap={8}>
            <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL} fontWeight={600}>
              {t('updateTo')}
            </BodyTextS>
            <BodyTextM
              className="cursor-pointer"
              textTransform="capitalize"
              color={COLOR.TEXT_INFO}
              fontWeight={600}
              onClick={() => onSelectAll('hpm')}
            >
              {t('selectAll')}
            </BodyTextM>
          </Flex>
        ),
        cell: (row: TableRow) => {
          return (
            <Flex
              w="100%"
              h="100%"
              align="center"
              justify="space-between"
              p="6px"
              gap={12}
              style={{
                backgroundColor: row.isConflict ? COLOR.WARNING_LIGHT : '',
              }}
            >
              {row.hpm}
              {row.isConflict && (
                <Radio
                  checked={row.hpmSelected}
                  onChange={() => onSelectRow('hpm', row)}
                />
              )}
            </Flex>
          );
        },
      },
    ];
  }, []);

  const mapStatusTrans = (value: string) => {
    if (!value) {
      return '';
    }

    return t(
      `${value.toLowerCase()}Status` as keyof typeof PtvImportI18n.PtvImportResolve
    );
  };

  const mapTreatmentTypeTrans = (value: string) => {
    if (!value) {
      return '';
    }

    return t(
      `${value.toLowerCase()}Type` as keyof typeof PtvImportI18n.PtvImportResolve
    );
  };

  const conflictData = useMemo(() => {
    if (!currentParticipationDecision) {
      return [];
    }

    return [
      {
        key: 'firstName',
        label: t('firstName'),
        value: currentParticipationDecision.firstName,
        current: currentParticipationDecision.firstName.localFirstName.value,
        currentSelected:
          currentParticipationDecision.firstName.localFirstName.selected,
        hpm: currentParticipationDecision.firstName.hpmFirstName.value,
        hpmSelected:
          currentParticipationDecision.firstName.hpmFirstName.selected,
        isConflict:
          currentParticipationDecision.firstName.localFirstName.value !==
          currentParticipationDecision.firstName.hpmFirstName.value,
      },
      {
        key: 'lastName',
        label: t('lastName'),
        value: currentParticipationDecision.lastName,
        current: currentParticipationDecision.lastName.localLastName.value,
        currentSelected:
          currentParticipationDecision.lastName.localLastName.selected,
        hpm: currentParticipationDecision.lastName.hpmLastName.value,
        hpmSelected: currentParticipationDecision.lastName.hpmLastName.selected,
        isConflict:
          currentParticipationDecision.lastName.localLastName.value !==
          currentParticipationDecision.lastName.hpmLastName.value,
      },
      {
        key: 'gender',
        label: t('gender'),
        value: currentParticipationDecision.gender,
        current: currentParticipationDecision.gender.localGender.value,
        currentSelected:
          currentParticipationDecision.gender.localGender.selected,
        hpm: currentParticipationDecision.gender.hpmGender.value,
        hpmSelected: currentParticipationDecision.gender.hpmGender.selected,
        isConflict:
          currentParticipationDecision.gender.localGender.value !==
          currentParticipationDecision.gender.hpmGender.value,
      },
      {
        key: 'dob',
        label: t('dob'),
        value: currentParticipationDecision.dob,
        current: currentParticipationDecision.dob.localDOB.value
          ? toDateFormat(
            new Date(currentParticipationDecision.dob.localDOB.value),
            {
              dateFormat: 'dd.MM.yyyy',
            }
          )
          : '--',
        currentSelected: currentParticipationDecision.dob.localDOB.selected,
        hpm: currentParticipationDecision.dob.hpmDOB.value
          ? toDateFormat(
            new Date(currentParticipationDecision.dob.hpmDOB.value),
            {
              dateFormat: 'dd.MM.yyyy',
            }
          )
          : '--',
        hpmSelected: currentParticipationDecision.dob.hpmDOB.selected,
        isConflict:
          currentParticipationDecision.dob.localDOB.value !==
          currentParticipationDecision.dob.hpmDOB.value,
      },
      {
        key: 'status',
        label: t('status'),
        value: currentParticipationDecision.status,
        current: mapStatusTrans(
          currentParticipationDecision.status.localStatus.value
        ),
        currentSelected:
          currentParticipationDecision.status.localStatus.selected,
        hpm: mapStatusTrans(
          currentParticipationDecision.status.hpmStatus.value
        ),
        hpmSelected: currentParticipationDecision.status.hpmStatus.selected,
        isConflict:
          currentParticipationDecision.status.localStatus.value !==
          currentParticipationDecision.status.hpmStatus.value,
      },
      {
        key: 'treatmentType',
        label: t('treatmentType'),
        value: currentParticipationDecision.treatmentType,
        current:
          mapTreatmentTypeTrans(currentParticipationDecision.treatmentType.localTreatmentType.value),
        currentSelected:
          currentParticipationDecision.treatmentType.localTreatmentType
            .selected,
        hpm: mapTreatmentTypeTrans(currentParticipationDecision.treatmentType.hpmTreatmentType.value),
        hpmSelected:
          currentParticipationDecision.treatmentType.hpmTreatmentType.selected,
        isConflict:
          currentParticipationDecision.treatmentType.localTreatmentType.value !==
          currentParticipationDecision.treatmentType.hpmTreatmentType.value,
      },
      {
        key: 'contractBeginDate',
        label: t('beginDateContract'),
        value: currentParticipationDecision.contractBeginDate,
        current:
          typeof currentParticipationDecision.contractBeginDate
            ?.localContractBeginDate?.value === 'number' &&
            currentParticipationDecision.contractBeginDate.localContractBeginDate
              .value > 0
            ? toDateFormat(
              new Date(
                currentParticipationDecision.contractBeginDate
                  .localContractBeginDate.value
              ),
              {
                dateFormat: 'dd.MM.yyyy',
              }
            )
            : '--',
        currentSelected:
          currentParticipationDecision.contractBeginDate.localContractBeginDate
            .selected,
        hpm:
          typeof currentParticipationDecision.contractBeginDate
            ?.hpmContractBeginDate?.value === 'number' &&
            currentParticipationDecision.contractBeginDate.hpmContractBeginDate
              .value > 0
            ? toDateFormat(
              new Date(
                currentParticipationDecision.contractBeginDate
                  .hpmContractBeginDate.value
              ),
              {
                dateFormat: 'dd.MM.yyyy',
              }
            )
            : '--',
        hpmSelected:
          currentParticipationDecision.contractBeginDate.hpmContractBeginDate
            .selected,
        isConflict:
          currentParticipationDecision.contractBeginDate.localContractBeginDate
            ?.value !==
          currentParticipationDecision.contractBeginDate.hpmContractBeginDate
            ?.value,
      },
      {
        key: 'contractEndDate',
        label: t('contractEndDate'),
        value: currentParticipationDecision.contractEndDate,
        current:
          typeof currentParticipationDecision.contractEndDate
            ?.localContractEndDate?.value === 'number' &&
            currentParticipationDecision.contractEndDate.localContractEndDate
              .value > 0
            ? toDateFormat(
              new Date(
                currentParticipationDecision.contractEndDate
                  .localContractEndDate.value
              ),
              {
                dateFormat: 'dd.MM.yyyy',
              }
            )
            : '--',
        currentSelected:
          currentParticipationDecision.contractEndDate.localContractEndDate
            .selected,
        hpm:
          typeof currentParticipationDecision.contractEndDate
            ?.hpmContractEndDate?.value === 'number' &&
            currentParticipationDecision.contractEndDate.hpmContractEndDate
              .value > 0
            ? toDateFormat(
              new Date(
                currentParticipationDecision.contractEndDate
                  .hpmContractEndDate.value
              ),
              {
                dateFormat: 'dd.MM.yyyy',
              }
            )
            : '--',
        hpmSelected:
          currentParticipationDecision.contractEndDate.hpmContractEndDate
            .selected,
        isConflict:
          currentParticipationDecision.contractEndDate.localContractEndDate
            ?.value !==
          currentParticipationDecision.contractEndDate.hpmContractEndDate
            ?.value,
      },
      {
        key: 'insuranceNumber',
        label: t('insuranceNumber'),
        value: currentParticipationDecision.insuranceNumber,
        current:
          currentParticipationDecision.insuranceNumber.localInsuranceNumber
            .value,
        currentSelected:
          currentParticipationDecision.insuranceNumber.localInsuranceNumber
            .selected,
        hpm: currentParticipationDecision.insuranceNumber.hpmInsuranceNumber
          .value,
        hpmSelected:
          currentParticipationDecision.insuranceNumber.hpmInsuranceNumber
            .selected,
        isConflict:
          currentParticipationDecision.insuranceNumber.localInsuranceNumber
            .value !==
          currentParticipationDecision.insuranceNumber.hpmInsuranceNumber.value,
      },
      {
        key: 'ikNumber',
        label: t('insurance'),
        value: currentParticipationDecision.ikNumber,
        current: currentParticipationDecision.ikNumber.localIkNumber.value
          ? t('ikNumber', {
            ikNumber:
              currentParticipationDecision.ikNumber.localIkNumber.value,
          })
          : '--',
        currentSelected:
          currentParticipationDecision.ikNumber.localIkNumber.selected,
        hpm: currentParticipationDecision.ikNumber.hpmIkNumber.value
          ? t('ikNumber', {
            ikNumber: currentParticipationDecision.ikNumber.hpmIkNumber.value,
          })
          : '--',
        hpmSelected: currentParticipationDecision.ikNumber.hpmIkNumber.selected,
        isConflict:
          currentParticipationDecision.ikNumber.localIkNumber.value !==
          currentParticipationDecision.ikNumber.hpmIkNumber.value,
      },
      {
        key: 'reason',
        label: t('reason'),
        value: currentParticipationDecision.reason,
        current: currentParticipationDecision.reason.localReason.value,
        currentSelected:
          currentParticipationDecision.reason.localReason.selected,
        hpm: currentParticipationDecision.reason.hpmReason.value,
        hpmSelected: currentParticipationDecision.reason.hpmReason.selected,
        isConflict:
          currentParticipationDecision.reason.localReason.value !==
          currentParticipationDecision.reason.hpmReason.value,
      },
    ];
  }, [currentParticipationDecision]);

  useEffect(() => {
    if (participantDecision) {
      onSelectAll('hpm');
    }
  }, [participantDecision]);

  if (!participantDecision) {
    return null;
  }

  return (
    <Dialog
      className={getCssClass('bp5-dialog-fullscreen', className)}
      isOpen={openResolveParticipant}
      canOutsideClickClose={false}
      title={t('title', {
        patientName: nameUtils.getPatientName({
          firstName: participantDecision.firstName.localFirstName.value,
          lastName: participantDecision.lastName.localLastName.value,
        }),
      })}
      actions={
        <>
          <Button
            intent={Intent.PRIMARY}
            data-test-id="btn-save"
            onClick={() =>
              onSaveResolveParticipant(currentParticipationDecision)
            }
          >
            {t('save')}
          </Button>
        </>
      }
      onClose={onCloseResolveParticipant}
    >
      <Flex column gap={16} className="conflict-content">
        <Flex mt={32}>
          <MessageBar
            hasBullet={false}
            type="warning"
            content={t('note')}
            subDescription={t('description')}
          />
        </Flex>
        <Table
          columns={columnsData}
          highlightOnHover
          noHeader
          persistTableHead
          striped
          data={conflictData}
          responsive={false}
          progressPending={false}
        />
      </Flex>
    </Dialog>
  );
}

export default PtvImportResolveParticipant;
