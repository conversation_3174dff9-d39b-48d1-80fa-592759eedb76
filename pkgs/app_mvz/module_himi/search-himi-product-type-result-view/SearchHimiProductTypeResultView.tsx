import { BodyTextM, Flex, LoadingState } from '@tutum/design-system/components';
import { But<PERSON> } from '@tutum/design-system/components';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { Art, Base } from '@tutum/hermes/bff/himi_common';
import I18n, {
  IFixedNamespaceTFunction,
  II18nFixedNamespace,
} from '@tutum/infrastructure/i18n';
import { KEYCODE } from '@tutum/mvz/constant/keycode';
import type HimiI18n from '@tutum/mvz/locales/en/Himi.json';
import {
  musterFormDialogActions,
  useMusterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import React, { memo, useState, useMemo } from 'react';
import { ISelectedContractDoctor } from '../../module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { IPatientProfile } from '../../module_patient-management/types/profile.type';
import { IMvzThemeProps } from '../../theme';
import HimiMuster8ModalConfirm from '../himi-muster-8-modal-confirm/HimiMuster8ModalConfirm';
import HimiProductModalList from '../himi-product-modal-list/HimiProductModalList.styled';
import { himiProductTypeResultActions } from './himiProductTypeResult.store';
import {
  printSettingStore as printSettingsStore,
  FormTypeSetting,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import { COLOR } from '@tutum/design-system/themes/styles';
import { FormName } from '@tutum/hermes/bff/form_common';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import { useQuerySearchArt } from '@tutum/hermes/bff/legacy/app_mvz_himi';
import { useDebounce } from '@tutum/infrastructure/hook';
import { SearchValue } from '../himi/Himi';
import { SearchArtType } from '@tutum/hermes/bff/legacy/app_mvz_himi';
export interface ISearchHimiProductTypeResultViewMemoProps {
  className?: string;
  searchValue?: SearchValue;
  selectedContractDoctor: ISelectedContractDoctor;
  patient: IPatientProfile;
}

const genBaseCode = (base: Base): string => {
  return `${base.gruppe.toString().padStart(2, '0')}.${base.ort
    .toString()
    .padStart(2, '0')}.${base.unter.toString().padStart(2, '0')}`;
};

const SearchHimiProductTypeResultViewMemo = (
  props: ISearchHimiProductTypeResultViewMemoProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof HimiI18n.SearchProductTypeViewResult>
) => {
  const { t, className, searchValue } = props;
  const [selectedProduct, setSelectedProduct] = useState<Art | null>(null);

  const musterFormStore = useMusterFormDialogStore();
  const currentSchein = useCurrentSchein();

  const debouncedSearchValue = useDebounce(searchValue, 1000);
  const {
    data: arts,
    isLoading,
    isSuccess,
  } = useQuerySearchArt(
    {
      searchValue: debouncedSearchValue?.Value ?? '',
      searchArtType:
        debouncedSearchValue?.Category ??
        SearchArtType.SearchArtType_SearchArtType_Unknown,
    },
    {
      enabled: !!debouncedSearchValue?.Value,
      select: ({ data }) => data.arts || [],
    }
  );

  const { isContractSupport } = useCheckContractSupport(
    ['VSST628', 'VSST626', 'VSST629', 'VSST630'],
    [currentSchein?.hzvContractId]
  );

  const getColumns = (t: IFixedNamespaceTFunction) => {
    const columns: IDataTableColumn<Art>[] = [
      {
        name: 'ID',
        selector: (row) => row['id'],
        grow: 2,
        wrap: true,
        format: (art: Art) => {
          return (
            <span>{`${genBaseCode(art.base)}.${art.artId.toString()} - ${
              art.bezeichnung
            }`}</span>
          );
        },
      },
      {
        name: 'action',
        cell: (art: Art) => (
          <div
            style={{ width: '100%' }}
            className="row middle-md end-md sl-hover"
          >
            <div className="col-md-8">
              <div
                onClick={() => {
                  setSelectedProduct(art);
                }}
                className="sl-btn-show-product-list"
              >
                <b>{t('ShowSingleProductList')}</b>
              </div>
            </div>
            <div className="col-md-4">
              <Button
                onClick={() => {
                  musterFormDialogActions.setProductType(
                    art,
                    isContractSupport
                  );
                }}
                intent="primary"
                className="sl-btn-prescribe-himi"
              >
                {t('Prescribe')}
              </Button>
            </div>
          </div>
        ),
      },
    ];

    return columns;
  };

  const formSetting = useMemo(() => {
    return printSettingsStore.formsSetting.find(
      (form) => form.formId === musterFormStore.currentFormName
    );
  }, [musterFormStore.currentFormName, printSettingsStore.formsSetting]);

  return (
    <div
      className={className + ' data-list'}
      tabIndex={isSuccess && arts?.length > 0 ? 0 : undefined}
      onKeyDown={(e) => {
        const nextDocumentBody = document.getElementById('__next');
        if (!nextDocumentBody) {
          return;
        }
        const isFromPortal = !nextDocumentBody.contains(
          e.target as HTMLElement
        );

        if (e.code === KEYCODE.Tab && !isFromPortal) {
          e.preventDefault();
          return false;
        }
      }}
    >
      <HimiMuster8ModalConfirm
        isOpen={
          musterFormStore?.himiDeviceGroupNumber === 25 &&
          !musterFormStore?.currentFormName
        }
        onClose={(value) => {
          if (value) {
            musterFormDialogActions.setCurrentFormName(value);
          } else {
            musterFormDialogActions.clear();
          }
        }}
      />
      {selectedProduct && (
        <HimiProductModalList
          onClose={() => {
            setSelectedProduct(null);
          }}
          selectedProduct={selectedProduct}
        />
      )}
      {formSetting?.type === FormTypeSetting.HIMI_FORM && (
        <MusterFormDialog
          patient={props.patient}
          selectedContractDoctor={props.selectedContractDoctor}
          isOpen={!!musterFormStore?.currentFormName}
          onClose={() => {
            musterFormDialogActions.setProduct(undefined, undefined);
            musterFormDialogActions.setProductType(undefined, undefined);
          }}
          componentActions={himiProductTypeResultActions}
        />
      )}

      <Table
        noTableHead
        highlightOnHover
        customStyles={{
          table: {
            style: {
              maxHeight: 'calc(100vh - 113px)',
            },
          },
        }}
        columns={getColumns(t)}
        data={arts || []}
        progressPending={isLoading}
        progressComponent={
          <div style={{ height: '65px', width: '100%' }}>
            <LoadingState />
          </div>
        }
        noDataComponent={
          <Flex gap={16} column align="center">
            <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER} fontSize="20">
              {t('NoResult')}
            </BodyTextM>
            {!!searchValue && (
              <Button
                onClick={() => {
                  musterFormDialogActions.setHIMIFreetextPrescribe(
                    searchValue?.Value ?? ''
                  );

                  musterFormDialogActions.setCurrentFormName(
                    checkIsPrivateSchein(currentSchein)
                      ? FormName.Private
                      : FormName.Muster_16
                  );
                }}
                intent="primary"
                data-test-id="prescribe-himi-freetext"
                className="sl-btn-prescribe-himi"
              >
                {t('PrescribeFreeText')}
              </Button>
            )}
          </Flex>
        }
      ></Table>
    </div>
  );
};

export default memo(
  I18n.withTranslation(SearchHimiProductTypeResultViewMemo, {
    namespace: 'Himi',
    nestedTrans: 'SearchProductTypeViewResult',
  })
);
