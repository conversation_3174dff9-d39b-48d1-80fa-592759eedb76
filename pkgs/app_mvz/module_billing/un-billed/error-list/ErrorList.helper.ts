import { GroupResponseBilling, GroupResponseBillingPatient } from '@tutum/hermes/bff/legacy/service_domains_billing_common';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';

export const mapFETableStructure = (
  groupErrors: GroupResponseBilling[]
): GroupResponseBilling[] => {
  return groupErrors?.map((groupError) => {
    const mergedPatients = mergePatients(groupError.patients);
    return {
      ...groupError,
      patients: mergedPatients,
    };
  });
};

function mergePatients(patients: GroupResponseBillingPatient[]) {
  const mergedPatients: GroupResponseBillingPatient[] = [];
  patients.forEach((gPatient) => {
    let patient = mergedPatients.find((patient) => {
      return patient?.patient?.id === gPatient?.patient?.id;
    });
    if (!patient) {
      patient = { ...gPatient };
      mergedPatients.push(patient);
    } else {
      patient.contracts = mergeContracts(patient.contracts, gPatient.contracts);
    }
  });

  return mergedPatients.sort((a, b) => {
    if (!a?.patient?.patientNumber) return 1;
    if (!b?.patient?.patientNumber) return -1;
    return b.patient.patientNumber - a.patient?.patientNumber;
  });
}

function mergeContracts(existingContracts, newContracts) {
  const mergedContracts = [...existingContracts];
  newContracts.forEach((gContract) => {
    let contract = mergedContracts.find(
      (contract) => contract.contract === gContract.contract
    );
    if (!contract) {
      contract = { ...gContract };
      mergedContracts.push(contract);
    } else {
      contract.doctors = mergeDoctors(contract.doctors, gContract.doctors);
      contract.errors = mergeErrors(contract.errors, gContract.errors);
    }
  });
  return mergedContracts.sort((a, b) => b.contract?.localeCompare(a.contract));
}
function mergeDoctors(existingDoctors, newDoctors) {
  const doctorMap = new Map();
  [...existingDoctors, ...newDoctors].forEach((doctor) => {
    doctorMap.set(doctor.id, doctor);
  });
  return Array.from(doctorMap.values());
}

function mergeErrors(existingErrors, newErrors) {
  const errorMap = new Map();
  [...existingErrors, ...newErrors].forEach((error) => {
    errorMap.set(error.errorCode, error);
  });
  return Array.from(errorMap.values()).sort((a, b) =>
    b.timelineId?.localeCompare(a.timelineId)
  );
}

const getEncounterServiceInfo = (serviceTimeline) => {
  const { command, code, description } = serviceTimeline;
  const fullCode = [code].filter(Boolean).join(' ');
  return { command, description, fullCode };
};

const getEncounterDiagnoseInfo = (diagnoseTimeline) => {
  const { command, code, certainty, laterality, description } =
    diagnoseTimeline;
  const fullCode = [code, certainty, laterality].filter(Boolean).join(' ');
  return { command, description, fullCode };
};

export const getDocumentInfo = (
  timeline: TimelineModel
): { command; description; fullCode } | null => {
  const type = timeline?.type;

  if (type === 'SERVICE') {
    return getEncounterServiceInfo(timeline?.encounterServiceTimeline);
  }

  if (type === 'DIAGNOSE') {
    return getEncounterDiagnoseInfo(timeline?.encounterDiagnoseTimeline);
  }

  return null;
};
