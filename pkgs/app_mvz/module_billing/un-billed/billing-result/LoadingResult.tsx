import React, { memo } from 'react';
import { ProgressBar, H4, Classes } from '@tutum/design-system/components/Core';
import { Flex, BodyTextM } from '@tutum/design-system/components';
import { scaleSpacePx } from '@tutum/design-system/styles';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { IMvzTheme } from '@tutum/mvz/theme';

export interface ILoadingResultProps {
  className?: string;
  theme?: IMvzTheme;
  totalPackage: number;
  receivedPackage: number;
}

function LoadingResult(
  props: ILoadingResultProps &
    II18nFixedNamespace<keyof typeof BillingI18n.UnBilled>
) {
  const { className, t, totalPackage, receivedPackage } = props;

  function CellLoading() {
    return (
      <td style={{ padding: scaleSpacePx(1) }}>
        <div className="loading-cell bp5-skeleton" />
      </td>
    );
  }

  function loadingRows(numerOfRow: number) {
    const UI: React.JSX.Element[] = [];
    for (let i = 0; i < numerOfRow; i++) {
      UI.push(
        <tr key={i}>
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <CellLoading />
          <td />
        </tr>
      );
    }
    return UI || null;
  }
  const progessNo = Math.round(
    ((receivedPackage || 0) * 100) / (totalPackage || 1) - 1
  );
  return (
    <div className={className}>
      <Flex
        className="sl-header-content"
        column
        justify="center"
        align="center"
      >
        <ProgressBar
          className="sl-progress"
          value={receivedPackage / totalPackage}
        />
        <H4 className="sl-header-h3">{t('submittingBilling')}</H4>
        <BodyTextM textTransform="lowercase">
          {`${progessNo > 1 ? progessNo - 1 : progessNo}% ${t('submitted')}`}
        </BodyTextM>
      </Flex>
      <Flex className="sl-scollable">
        <table className={Classes.HTML_TABLE}>
          <thead>
            <tr>
              <th>{t('timePeriod')}</th>
              <th>{t('group')}</th>
              <th>{t('contractName')}</th>
              <th>{t('doctor')}</th>
              <td>{t('encounter')}</td>
              <th>{t('diagnose')}</th>
              <th>{t('service')}</th>
              <th>{t('prescription')}</th>
              <td>{t('billedPatient')}</td>
              <th>{t('status')}</th>
              <th style={{ width: '15%' }} />
            </tr>
          </thead>
          <tbody>{loadingRows(totalPackage)}</tbody>
        </table>
      </Flex>
    </div>
  );
}

export default memo(
  I18n.withTranslation(LoadingResult, {
    namespace: 'Billing',
    nestedTrans: 'UnBilled',
  })
);
