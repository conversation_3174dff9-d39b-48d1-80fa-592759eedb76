import '@blueprintjs/table/lib/css/table.css';
import { groupBy, isEqual, uniq, uniqWith } from 'lodash';
import {
  Dispatch,
  SetStateAction,
  memo,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';

import { Tooltip } from '@tutum/design-system/components/Core';
import { Cell, Column, SelectionModes, Table2 } from '@blueprintjs/table';
import { Avatar, Flex, Svg } from '@tutum/design-system/components';
import {
  Button,
  Dialog,
  Menu,
  MenuItem,
  Popover,
  PopoverPosition,
} from '@tutum/design-system/components/Core';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  EventBillingResponse,
  PatientProfileResponse,
  useListenBillingResponse,
} from '@tutum/hermes/bff/app_mvz_billing';
import { submitBillingToHpm } from '@tutum/hermes/bff/legacy/app_mvz_billing';
import {
  EmployeeProfileResponse,
  getEmployeeProfileByIds,
} from '@tutum/hermes/bff/legacy/app_profile';
import { YearQuarter } from '@tutum/hermes/bff/legacy/service_domains_billing_common';
import {
  CalculateBillingSummaryResponse,
  DiseaseGroup,
  MapGroupCalculateByDoctor,
  P4DiseaseAnalysisResponse,
  SubmitBillingToHpmResponse,
} from '@tutum/hermes/bff/service_domains_billing';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import BillingType from '../../Billing.type';
import { HintErrorListResponse, ISelectedFilter } from '../UnBilled.type';
import ConfirmSubmitBilling from '../confirm-submit-billing/ConfirmSubmitBilling.styled';
import MultiMorbidPatient from '../multi-morbid-patient/MultiMorbidPatient.styled';
import StepHeader from '../step-header/StepHeader.styled';
import { IBillingSummaryTable } from './BillingSummary.type';
const ErrorIcon = '/images/alert-triangle.svg';

export interface IBillingSummaryProps {
  className?: string;
  theme?: IMvzTheme;
  step: number;
  doctorInfo: { [doctorId: string]: IEmployeeProfile };
  setStep: Function;
  canViewDirectly: boolean;
  contractList: { [key: string]: string };
  patientInfo: {
    [id: string]: PatientProfileResponse;
  };
  analyzeForP4Patient: IP4DiseaseAnalysisPatient;
  billingSelectionHeight: number;
  billingSummariesWithError: Nullable<CalculateBillingSummaryResponse>;
  hintError: HintErrorListResponse;
  submitPrescription: boolean;
  billingSummaries: Nullable<CalculateBillingSummaryResponse>;
  selectFilter: ISelectedFilter;
  // ignoreBilling: IgnoreBilling[];
  setBilledResponse: Dispatch<
    SetStateAction<Nullable<SubmitBillingToHpmResponse>>
  >;
  referenceId: string;
  filterP4Service: boolean;
}

export interface IBillingSummaryState {
  isSubmitting: boolean;
  isShowConfirm: boolean;
  isPrescriptionOnly: boolean;
}
export interface IMorbiPatientList {
  [year: string]: {
    [quarter: string]: {
      [patientId: string]: {
        specialServiceCount?: number;
        diseaseGroups: DiseaseGroup[];
      };
    };
  };
}
export interface IP4DiseaseAnalysisPatient {
  [year: string]: {
    [quarter: string]: {
      [patientId: string]: Array<{
        contractId: string;
        patientId: string;
        specialServiceCount?: number;
        diseaseGroups: DiseaseGroup[];
      }>;
    };
  };
}

const SendIcon = '/images/send.svg';
const ChevronDownIcon = '/images/chevron-down-white.svg';

function BillingSumary(
  props: IBillingSummaryProps &
    II18nFixedNamespace<keyof typeof BillingI18n.UnBilled>
) {
  const [state, setState] = useState<IBillingSummaryState>({
    isShowConfirm: false,
    isSubmitting: false,
    isPrescriptionOnly: false,
  });
  const [morbiPatientList, setMorbiPatientList] = useState<IMorbiPatientList>(
    {} as IMorbiPatientList
  );
  const [data, setData] = useState<IBillingSummaryTable[]>([]);
  const [showMorbiPatientDialog, setShowMorbiPatientDialog] =
    useState<boolean>(false);
  const table2 = useRef<HTMLDivElement>();
  const tableRef = useRef<Table2>();
  const [doctorProfile, setDoctorProfile] = useState<IEmployeeProfile>(
    {} as IEmployeeProfile
  );

  const setting = useSettingStore();
  const isOfflineBilling =
    setting.selectiveContract.SelectiveContracts_BillingSubmission ===
    'offline';

  const [columnWidths, setColumnWidths] = useState<number[]>([
    300, 150, 100, 150, 100, 100, 100, 120, 100,
  ]);

  const {
    className,
    step,
    t,
    setStep,
    submitPrescription,
    billingSummaries,
    billingSummariesWithError,
    patientInfo,
    // ignoreBilling,
    setBilledResponse,
    referenceId,
    hintError,
    doctorInfo,
  } = props;
  const { isSubmitting } = state;

  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();
  const { markAsBillingDoctor } = currentLoggedInUser;

  const mappingBillingSummaries = async (
    summaries:
      | {
          [key: string]: MapGroupCalculateByDoctor;
        }
      | undefined,
    errorSummaries:
      | {
          [key: string]: MapGroupCalculateByDoctor;
        }
      | undefined
  ) => {
    if (!summaries && !errorSummaries) {
      return;
    }
    // Get doctor ids from summaries and error summaries
    let doctorIds: string[] = [];
    doctorIds.push(...Object.keys(summaries || {}));
    doctorIds.push(...Object.keys(errorSummaries || {}));
    doctorIds = uniq(doctorIds);

    if (!doctorIds.length) {
      return;
    }
    const doctors: EmployeeProfileResponse[] =
      (
        await getEmployeeProfileByIds({
          originalIds: doctorIds,
        })
      )?.data.profiles || [];

    const result: IBillingSummaryTable[] = [];
    doctorIds.map((doctorId) => {
      const doctor = doctors.find((d) => d.id == doctorId);
      const invalidPatientIds: string[] =
        errorSummaries?.[doctorId] && errorSummaries[doctorId]
          ? errorSummaries[doctorId].groupCalculateByDoctors?.flatMap(
              (group) => group.billedPatients
            )
          : [];
      result.push({
        doctor: doctor,
        isDoctor: true,
        isTotal: false,
      });
      const total = {
        billedPatients: 0,
        encounters: 0,
        diagnoses: 0,
        services: 0,
        ops: 0,
        prescriptions: 0,
      };

      if (
        summaries &&
        Object.keys(summaries).length > 0 &&
        summaries[doctorId]
      ) {
        summaries[doctorId].groupCalculateByDoctors?.forEach((group) => {
          total.billedPatients += group.billedPatients.length;
          total.encounters +=
            group.encounters.length + group.countPreventiveCase;
          total.diagnoses += group.diagnoses.length;
          total.services += group.services.length + group.countPreventiveCase;
          total.prescriptions += group.prescriptions.length;
          result.push({
            doctor: doctor,
            quarter: String(group.yearQuarter.quarter),
            year: String(group.yearQuarter.year),
            group: group.mainGroup,
            contractName: group.contractId,
            billedPatients: group.billedPatients.length,
            encounters: group.encounters.length + group.countPreventiveCase,
            services: group.services.length + group.countPreventiveCase,
            diagnoses: group.diagnoses.length,
            prescriptions: group.prescriptions.length,
            isDoctor: false,
            isTotal: false,
            morbiPatientList: {
              ...group.p4DiseaseAnalysis,
              patients: group.p4DiseaseAnalysis.patients?.filter(
                (patient) => !invalidPatientIds.includes(patient.patientId)
              ),
            },
            isErrorRow: false,
          });
        });
      }
      // Handle error summaries
      if (
        errorSummaries &&
        Object.keys(errorSummaries).length > 0 &&
        errorSummaries[doctorId]
      ) {
        errorSummaries[doctorId].groupCalculateByDoctors?.forEach((group) => {
          result.push({
            doctor: doctor,
            quarter: String(group.yearQuarter.quarter),
            year: String(group.yearQuarter.year),
            group: group.mainGroup,
            contractName: group.contractId,
            billedPatients: group.billedPatients.length,
            encounters: group.encounters.length + group.countPreventiveCase,
            services: group.services.length + group.countPreventiveCase,
            diagnoses: group.diagnoses.length,
            prescriptions: group.prescriptions.length,
            isDoctor: false,
            isTotal: false,
            morbiPatientList: group.p4DiseaseAnalysis,
            isErrorRow: true,
          });
        });
      }
      // Add total row for each doctor
      result.push({
        doctor: doctor,
        billedPatients: total.billedPatients,
        encounters: total.encounters,
        diagnoses: total.diagnoses,
        services: total.services,
        ops: total.ops,
        prescriptions: total.prescriptions,
        isDoctor: false,
        isTotal: true,
      });
    });
    setData(result);
  };

  const groupMorbiPatientList = (
    p4DiseaseAnalysises: P4DiseaseAnalysisResponse[]
  ) => {
    const groupByYear = groupBy(p4DiseaseAnalysises, (item) =>
      item.year.toString()
    );
    const groupByYearQuarter = {};
    const finalResponseGroupByYearQuarterPatientId: IMorbiPatientList =
      {} as IMorbiPatientList;

    Object.keys(groupByYear).map((year) => {
      if (!groupByYearQuarter[year]) groupByYearQuarter[year] = {};

      const groupByQuarter = groupBy(groupByYear[year], (item) =>
        item.quarter.toString()
      );
      groupByYearQuarter[year] = groupByQuarter;
    });

    Object.keys(groupByYearQuarter).map((year) =>
      Object.keys(groupByYearQuarter[year]).map((quarter) => {
        const analyzeData = groupByYearQuarter[year][quarter];
        const groupByPatient: {
          [key: string]: {
            diseaseGroups: DiseaseGroup[];
            specialServiceCount?: number;
          };
        } = {};
        analyzeData.map((analyzedP4Item) =>
          analyzedP4Item.patients.map((patient) => {
            if (!groupByPatient[patient.patientId]) {
              groupByPatient[patient.patientId] = {
                diseaseGroups: [],
              };
            }
            groupByPatient[patient.patientId] = {
              diseaseGroups: patient.diseaseGroups,
              specialServiceCount: patient.specialServiceCount,
            };
          })
        );
        if (!finalResponseGroupByYearQuarterPatientId[year]) {
          finalResponseGroupByYearQuarterPatientId[year] = {};
        }
        if (!finalResponseGroupByYearQuarterPatientId[year][quarter]) {
          finalResponseGroupByYearQuarterPatientId[year][quarter] = {};
        }
        finalResponseGroupByYearQuarterPatientId[year][quarter] =
          groupByPatient;
      })
    );
    setMorbiPatientList(finalResponseGroupByYearQuarterPatientId);
  };

  useEffect(() => {
    mappingBillingSummaries(
      billingSummaries?.response,
      billingSummariesWithError?.response
    );
  }, [billingSummaries, billingSummariesWithError]);

  const onBackStep = () => {
    setStep(BillingType.BILLING_STEP_NUMBER['ERROR_LIST']);
  };

  const buildBillingSubmitData = (
    isCancel = false,
    isPrescriptionOnly = false
  ) => {
    if (isCancel) {
      setState({
        ...state,
        isShowConfirm: false,
        isSubmitting: false,
      });
      return;
    }

    setState({
      ...state,
      isShowConfirm: false,
      isSubmitting: true,
    });

    if (billingSummaries?.response) {
      const summaries = billingSummaries?.response;
      const yearQuarters: YearQuarter[] = [];
      const doctorIds: string[] = [];
      const contractIds: string[] = [];
      // debugger;
      Object.keys(summaries).forEach((d) => {
        doctorIds.push(d);
        summaries[d].groupCalculateByDoctors.forEach((g) => {
          contractIds.push(g.contractId);
          yearQuarters.push(g.yearQuarter);
        });
      });

      return submitBillingToHpm({
        times: uniqWith(yearQuarters, isEqual),
        doctorIds: uniq(doctorIds),
        contractIds: uniq(contractIds),
        testRun: false,
        isSubmitPresciption: submitPrescription,
        ignoreDocumentBilling: hintError.ignoreDocumentedBillingList,
        ignoreBilling: [],
        referenceId,
        isPrescriptionOnly,
        isGetErrorTimeline: false,
      }).catch((_) => {
        setState((currentState) => ({
          ...currentState,
          isSubmitting: false,
          isShowConfirm: false,
          isPrescriptionOnly: false,
        }));
      });
    }
  };

  useListenBillingResponse((res: EventBillingResponse) => {
    if (
      res.referenceId == referenceId &&
      step === BillingType.BILLING_STEP_NUMBER['BILLING_SUMMARY']
    ) {
      if (!res.data.isHasErrors) {
        window.open(`${window.location.href}#${res.referenceId}`);
      }
      if (!!res.data) {
        setBilledResponse(res.data);
        setState((currentState) => ({
          ...currentState,
          isSubmitting: false,
          isShowConfirm: false,
        }));
      } else {
        throw new Error(res.systemError);
      }
    }
  });

  const onCancelConfirm = () => {
    setState({
      ...state,
      isShowConfirm: false,
    });
  };

  const onShowConfrim = (isPrescriptionOnly: boolean) => {
    setState({
      ...state,
      isShowConfirm: true,
      isPrescriptionOnly,
    });
  };

  return (
    <div className={className}>
      <Flex column>
        <StepHeader
          active={Boolean(
            step && step >= BillingType.BILLING_STEP_NUMBER['BILLING_SUMMARY']
          )}
          stepNumber={BillingType.BILLING_STEP_NUMBER['BILLING_SUMMARY']}
          title={t('billingSummary')}
          detail={
            step &&
            step >= BillingType.BILLING_STEP_NUMBER['BILLING_SUMMARY'] ? (
              <Flex align="center" className="sl-warning-err">
                {t('summaryWarningExcludedErr')}
              </Flex>
            ) : (
              ''
            )
          }
          onClick={() =>
            props.setStep(BillingType.BILLING_STEP_NUMBER['BILLING_SUMMARY'])
          }
          canClick={props.canViewDirectly}
        />
      </Flex>
      {step && step === BillingType.BILLING_STEP_NUMBER['BILLING_SUMMARY'] && (
        <div
          ref={(r) => {
            if (!!r) {
              table2.current = r;
            }
          }}
          className={`wrap-table ${data.length ? '' : 'noContent'}`}
        >
          <Flex className="sl-main-billing-summary-view" column>
            <Table2
              ref={(r) => {
                if (!!r) {
                  tableRef.current = r;
                }
              }}
              numRows={data.length || 0}
              loadingOptions={[]}
              enableRowHeader={false}
              selectionModes={SelectionModes.NONE}
              columnWidths={columnWidths}
              defaultRowHeight={50}
              minRowHeight={40}
              maxRowHeight={200}
              rowHeights={data.map((d) => (d.isDoctor ? 58 : 48))}
              onCompleteRender={() => {
                const widths = Array(9)
                  .fill(0)
                  .map((_, index) =>
                    Number(
                      tableRef?.current?.locator?.getWidestVisibleCellInColumn(
                        index
                      )
                    )
                  );
                const totalWidth = Number(widths.reduce((a, b) => a + b, 0));
                const width = Number(table2?.current?.offsetWidth);
                setColumnWidths(widths.map((w) => width * (w / totalWidth)));
                tableRef?.current?.forceUpdate();
              }}
            >
              <Column
                className="border"
                name={t('Quarter')}
                nameRenderer={(name: string) => (
                  <p className="header">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    <Flex>
                      {data[rowIndex]?.isDoctor
                        ? data[rowIndex]?.doctor && (
                            <Flex align="center">
                              <Avatar
                                size="medium"
                                className="avatar"
                                initial={data[rowIndex].doctor!.initial}
                              />
                              <b>
                                {nameUtils.getDoctorName(
                                  data[rowIndex].doctor!
                                )}
                              </b>
                            </Flex>
                          )
                        : !data[rowIndex]?.isTotal &&
                          `Q${data[rowIndex]?.quarter} ${data[rowIndex]?.year}`}
                      {data[rowIndex]?.isErrorRow && (
                        <Flex
                          align="center"
                          className="sl-warning-err-icon-tooltip"
                        >
                          <Tooltip content={t('summaryWarningExcludedErr')}>
                            <Svg src={ErrorIcon} width={16} height={16} />
                          </Tooltip>
                        </Flex>
                      )}
                    </Flex>
                  </Cell>
                )}
              />
              <Column
                className="border"
                name={t('ContractName')}
                nameRenderer={(name: string) => (
                  <p className="header">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>{data[rowIndex]?.contractName}</Cell>
                )}
              />
              <Column
                className="border"
                name={t('Group')}
                nameRenderer={(name: string) => (
                  <p className="header">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    {data[rowIndex]?.isTotal ? (
                      <b>{t('total')}</b>
                    ) : (
                      data[rowIndex]?.group
                    )}
                  </Cell>
                )}
              />
              <Column
                className="border"
                name={t('BilledPatient')}
                nameRenderer={(name: string) => (
                  <p className="header numberColumn">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    <div
                      className={`${
                        data[rowIndex]?.isTotal ? 'total' : ''
                      } numberColumn`}
                    >
                      {data[rowIndex]?.billedPatients}
                    </div>
                  </Cell>
                )}
              />
              <Column
                className="border"
                name={t('Encounter')}
                nameRenderer={(name: string) => (
                  <p className="header numberColumn">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    <div
                      className={`${
                        data[rowIndex]?.isTotal ? 'total' : ''
                      } numberColumn`}
                    >
                      {data[rowIndex]?.encounters}
                    </div>
                  </Cell>
                )}
              />
              <Column
                className="border"
                name={t('Diagnose')}
                nameRenderer={(name: string) => (
                  <p className="header numberColumn">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    <div
                      className={`${
                        data[rowIndex]?.isTotal ? 'total' : ''
                      } numberColumn`}
                    >
                      {data[rowIndex]?.diagnoses}
                    </div>
                  </Cell>
                )}
              />
              <Column
                className="border"
                name={t('Service')}
                nameRenderer={(name: string) => (
                  <p className="header numberColumn">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    <div
                      className={`${
                        data[rowIndex]?.isTotal ? 'total' : ''
                      } numberColumn`}
                    >
                      {data[rowIndex]?.services}
                    </div>
                  </Cell>
                )}
              />
              <Column
                className="border"
                name={t('Prescription')}
                nameRenderer={(name: string) => (
                  <p className="header numberColumn">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    <div
                      className={`${
                        data[rowIndex]?.isTotal ? 'total' : ''
                      } numberColumn`}
                    >
                      {data[rowIndex]?.prescriptions}
                    </div>
                  </Cell>
                )}
              />
              <Column
                className="border"
                name={''}
                nameRenderer={(name: string) => (
                  <p className="header numberColumn">{name}</p>
                )}
                cellRenderer={(rowIndex: number) => (
                  <Cell wrapText={true}>
                    <div
                      className={`${
                        data[rowIndex]?.isTotal ? 'total' : ''
                      } numberColumn`}
                    >
                      {props.filterP4Service &&
                        !data[rowIndex]?.isTotal &&
                        !data[rowIndex]?.isDoctor &&
                        !!data[rowIndex]?.morbiPatientList &&
                        !!data[rowIndex].morbiPatientList?.patients?.reduce(
                          (curr, next) => {
                            if (curr) {
                              return true;
                            }
                            if (next.diseaseGroups.length > 0) {
                              return true;
                            }
                            return curr;
                          },
                          false
                        ) && (
                          <a
                            onClick={() => {
                              if (
                                typeof data[rowIndex]?.morbiPatientList !=
                                'undefined'
                              ) {
                                groupMorbiPatientList([
                                  data[rowIndex]
                                    .morbiPatientList as P4DiseaseAnalysisResponse,
                                ]);
                                const doctor =
                                  doctorInfo[
                                    String(data[rowIndex]?.doctor?.id)
                                  ];
                                if (!!doctor) {
                                  setDoctorProfile(doctor);
                                }
                                setShowMorbiPatientDialog(true);
                              }
                            }}
                          >
                            {t('p4Title')}
                          </a>
                        )}
                    </div>
                  </Cell>
                )}
              />
            </Table2>
            {data.length === 0 && <div>{t('noData')}</div>}
          </Flex>
          <Flex className="sl-button" gap={24}>
            <Button
              intent="primary"
              text={t('editHintProtocol')}
              onClick={onBackStep}
            />
            <Flex>
              <Popover
                usePortal
                position={PopoverPosition.TOP_LEFT}
                content={
                  <>
                    <Menu className="sl-menu-action" key="menu-more">
                      <MenuItem
                        icon={<Svg src={SendIcon} width={16} height={16} />}
                        text={t('submitAllBilling')}
                        onClick={() => {
                          if (isOfflineBilling) {
                            buildBillingSubmitData(false, false);
                            return;
                          }

                          onShowConfrim(false);
                        }}
                      />
                      <MenuItem
                        icon={<Svg src={SendIcon} width={16} height={16} />}
                        text={t('submitPrescriptionOnly')}
                        onClick={() => {
                          if (isOfflineBilling) {
                            buildBillingSubmitData(false, true);
                            return;
                          }
                          onShowConfrim(true);
                        }}
                      />
                    </Menu>
                  </>
                }
              >
                <Button
                  intent="primary"
                  text={t('submitBilling')}
                  rightIcon={<Svg src={ChevronDownIcon} />}
                  loading={isSubmitting}
                  disabled={
                    !markAsBillingDoctor || isSubmitting || data.length === 0
                  }
                  onClick={() => {}}
                />
              </Popover>
            </Flex>
          </Flex>
          <Dialog
            title={t('confirmation')}
            isOpen={state.isShowConfirm}
            onClose={onCancelConfirm}
            canEscapeKeyClose={true}
            canOutsideClickClose={false}
            isCloseButtonShown={false}
          >
            <ConfirmSubmitBilling
              callBack={(isCancel: boolean) =>
                buildBillingSubmitData(isCancel, state.isPrescriptionOnly)
              }
            />
          </Dialog>
          <MultiMorbidPatient
            isOpen={showMorbiPatientDialog}
            patientInfo={patientInfo}
            morbiPatientList={morbiPatientList}
            onCloseDialog={() => setShowMorbiPatientDialog(false)}
            doctorProfile={doctorProfile}
          />
        </div>
      )}
    </div>
  );
}

export default memo(
  I18n.withTranslation(BillingSumary, {
    namespace: 'Billing',
    nestedTrans: 'UnBilled',
  })
);
