import { useQuery } from '@tanstack/react-query';
import { isNil } from 'lodash';
import { NextRouter, withRouter } from 'next/router';
import { memo, useCallback, useEffect, useState } from 'react';

import {
  BodyTextL,
  Flex,
  H2,
  LeaveConfirmModal,
  LoadingState,
} from '@tutum/design-system/components';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { getUUID, unique } from '@tutum/design-system/infrastructure/utils';
import {
  useListenBillingResponse,
  useListenBillingSubmissionResponse,
} from '@tutum/hermes/bff/app_mvz_billing';
import { ContractType } from '@tutum/hermes/bff/common';
import {
  EventBillingResponse,
  EventBillingSubmissionResponse,
  getBillableEncounters,
  getContractTypeByIds,
  getPatientProfileByIds,
  reSubmitBillings,
} from '@tutum/hermes/bff/legacy/app_mvz_billing';
import { YearQuarter } from '@tutum/hermes/bff/legacy/service_domains_billing_common';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import {
  BillingSubmissionResponseStatus,
  CalculateBillingSummaryResponse,
  IgnoreBilling,
  PreConditionSvBillingResponse,
  SubmitBillingToHpmResponse,
} from '@tutum/hermes/bff/service_domains_billing';
import { ErrorType } from '@tutum/hermes/bff/service_domains_billing_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { SV_BILLING_KEY_LOCAL_STORAGE } from '@tutum/mvz/constant/general';
import { GET_ALL_EMPLOYEE_PROFILE } from '@tutum/mvz/constant/queryKey';
import useStateWithCallback from '@tutum/mvz/hooks/useStateWithCallback';
import {
  useValidationBillingStore,
  validationBillingActions,
} from '@tutum/mvz/hooks/useValidationBilling';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { mapFETableStructure } from '@tutum/mvz/module_billing/un-billed/error-list/ErrorList.helper';
import { contractActions } from '@tutum/mvz/module_contract/contract.store';
import PatientValidation from '@tutum/mvz/module_kv_billing/patient-validation';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '../../types/profile.type';
import BillingType, { BILLING_TABS } from '../Billing.type';
import {
  HintErrorListResponse,
  IDialogConfirmLeavingState,
  IDoctorInfo,
  IGroupByYearQuarter,
  IP4DiseaseAnalysisPatient,
  IPatientInfo,
  ISelectedFilter,
} from './UnBilled.type';
import BillingResult from './billing-result/BillingResult.styled';
import BillingSummary from './billing-summary/BillingSummary.styled';
import ErrorList from './error-list/ErrorList.styled';
import HpmConnectionStatus from './hpm-connection-status/HpmConnectionStatus.styled';
import SelectBilling from './select-billing/SelectBilling.styled';
import BillingService from './services/billing.service';
import ErrorHintMessageService from './services/error-hint-message.service';

interface ITempYearQuater {
  year: string;
  quarter: string;
}

export interface INested {
  [key: number]: {
    [key: number]: {
      [key: string]: string[];
    };
  };
}

export interface IUnBilledProps {
  className?: string;
  theme?: IMvzTheme;
  gotoTab: (tabId: BILLING_TABS) => void;
}

export interface IIgnoreBilling {
  doctorId: string;
  contractId: string;
  yearQuarter: YearQuarter;
  patientId: string;
}

export interface IUnBilledState {
  step: number;
  isLoading: boolean;
}

function UnBilled(
  props: IUnBilledProps & { router: NextRouter } & II18nFixedNamespace<
      keyof typeof BillingI18n.UnBilled
    >
) {
  const { fixValidationPatientId } = useValidationBillingStore();
  const { setValidationList, setErrorListByQuarter } = validationBillingActions;

  const [billingSocketId] = useState<string>(getUUID());
  const [state, setState] = useState<IUnBilledState>({
    step: 1,
    isLoading: true,
  });

  const [isPreparingData, setIsPreparingData] = useState<boolean>(false);
  const [groupEncounterByQuarter, setGroupEncounterByQuarter] =
    useState<IGroupByYearQuarter>({});
  const [doctorInfo, setDoctorInfo] = useState<IDoctorInfo>({});
  const [patientInfo, setPatientInfo] = useState<IPatientInfo>({});
  const [billAbleEncounters, setBillAbleEncounters] =
    useState<TimelineModel[]>();
  const [selectedBillingEncounter, setSelectedBillingEncounter] =
    useState<IGroupByYearQuarter>({});
  const [contractList, setContractList] = useState<{
    [key: string]: ContractType;
  }>({});

  const [referenceId, _] = useState<string>(getUUID());

  const [confirmDialogState, setConfirmDialogState] =
    useState<IDialogConfirmLeavingState>({
      showConfirmLeaving: false,
      lastLocation: undefined,
    });
  const [confirmLeaving, setConfirmLeaving] = useStateWithCallback<boolean>(
    false,
    handleCallBackWhenConfirmLeave
  );
  const [detectChanged, setDetectChanged] = useState<boolean>(true);
  const [hintError, setHintError] = useState<HintErrorListResponse>(
    {} as HintErrorListResponse
  );
  const [morbidPatientByQuarter, setMorbidPatientByQuarter] =
    useState<IP4DiseaseAnalysisPatient>({} as IP4DiseaseAnalysisPatient);
  const [selectedFilter, setSelectedFilter] = useState<ISelectedFilter>(
    {} as ISelectedFilter
  );
  const [totalPackage, setTotalPackage] = useState<number>(0);
  const [billingResponse, setBillingResponse] = useState<
    EventBillingSubmissionResponse[]
  >([]);
  const [billingSelectionHeight, setBillingSelectionHeight] =
    useState<number>(52);
  const [submitPrescription, setSubmitPrescription] = useState(true);
  const [filterP4Service, setFilterP4Service] = useState(true);
  // const [ignoreBilling, setIgnoreBilling] = useState<IgnoreBilling[]>([]);
  const [nested, setNested] = useState<INested>({});

  const { step } = state;
  const { className, t, router, gotoTab } = props;
  const [errorsListByQuarterPatient, setErrorsListByQuarterPatient] =
    useState<Nullable<SubmitBillingToHpmResponse>>();
  const [billingSummaries, setBillingSummaries] =
    useState<Nullable<CalculateBillingSummaryResponse>>();
  const [billingSummariesWithError, setBillingSummariesWithError] =
    useState<Nullable<CalculateBillingSummaryResponse>>();
  const [billedResponse, setBilledResponse] =
    useState<Nullable<SubmitBillingToHpmResponse>>();
  const [selectedQuarter, setSelectedQuarter] = useState<ITempYearQuater[]>([]);
  const [selectedDoctorId, setSelectedDoctorId] = useState<string[]>([]);
  const [selectedContractId, setSelectedContractId] = useState<string[]>([]);
  const [precondition, setPreCondition] =
    useState<Nullable<PreConditionSvBillingResponse>>();

  useListenBillingSubmissionResponse((response) => {
    if (!response.data || response.data.referenceId !== billingSocketId) {
      return;
    }

    setBillingResponse((prevBillingResponse) => {
      return [...prevBillingResponse, response];
    });
  });

  useEffect(() => {
    contractActions.loadContracts();
  }, []);

  const { isSuccess, data: employeeProfiles } = useQuery<IEmployeeProfile[]>({
    queryKey: [GET_ALL_EMPLOYEE_PROFILE],
    enabled: false,
  });

  // TODO: reimplement as new timeline service
  const getBillingData = useCallback(async () => {
    if (isSuccess) {
      const { data: billableEncountersData } = await getBillableEncounters();
      setSelectedBillingEncounter({});
      setBillAbleEncounters(
        billableEncountersData?.timelineModels as TimelineModel[]
      );

      // set quarter data
      const { timelineModels = [] } = billableEncountersData;
      if (timelineModels.length > 0) {
        const groupByQuarter =
          BillingService.groupEncounterByQuarter(timelineModels);
        setGroupEncounterByQuarter(groupByQuarter);

        const uniqeContractIds = unique(
          timelineModels.map((item) => String(item.contractId))
        );
        const getConctractType = getContractTypeByIds({
          contractIds: uniqeContractIds.filter((c) => Boolean(c)),
        });
        const patientIds = unique(
          timelineModels.map((item) => String(item.patientId))
        );
        const getPatientProfile = getPatientProfileByIds({ ids: patientIds });
        const [patientProfile, contractIdsWithType] = await Promise.all([
          getPatientProfile,
          getConctractType,
        ]);
        const { contractIds } = contractIdsWithType.data;
        setContractList(contractIds);

        const employeeProfileMap = {};
        employeeProfiles.forEach((item) => {
          if (item.id) {
            employeeProfileMap[item.id] = item;
          }
        });
        const patientProfileMap = {};

        const { profiles } = patientProfile.data;
        profiles.map((item) => (patientProfileMap[item.id] = item));
        setPatientInfo(patientProfileMap);
        setDoctorInfo(employeeProfileMap);
        const currentState = state;
        setState({ ...currentState, isLoading: false });
      } else {
        const currentState = state;
        setState({ ...currentState, isLoading: false });
      }
    }
  }, [employeeProfiles, isSuccess]);

  useEffect(() => {
    getBillingData();
  }, [employeeProfiles, isSuccess]);

  const resetComponentToDefaultState = () => {
    setState({ isLoading: true, step: 1 });
    setTotalPackage(0);
    setBillingResponse([]);
    return getBillingData();
  };

  const setStep = (step: number) => {
    const currentState = state;
    setState({
      ...currentState,
      step: step,
    });
  };

  const closeConfirmModal = (isLeaving = false) => {
    setConfirmDialogState({
      ...confirmDialogState,
      showConfirmLeaving: false,
    });
    if (isLeaving) {
      setConfirmLeaving(true);
    }
  };

  function handleCallBackWhenConfirmLeave() {
    if (confirmLeaving) {
      const { lastLocation } = confirmDialogState;
      if (lastLocation?.pathname) {
        router.push(lastLocation?.pathname);
      }
    }
  }

  useListenBillingResponse((data: EventBillingResponse) => {
    if (data.referenceId !== referenceId) {
      return;
    }
    if (
      [
        BillingType.BILLING_STEP_NUMBER['SELECT_BILLING'],
        BillingType.BILLING_STEP_NUMBER['ERROR_LIST'],
      ].includes(step)
    ) {
      if (!isNil(data.data)) {
        data.data.groupErrors = mapFETableStructure(data.data?.groupErrors);
        const groupErrors = data.data.groupErrors;

        let invalidPatientIds: string[] = [];
        let validPatientIds: string[] = [];
        groupErrors?.forEach((item) => {
          item?.patients?.forEach((patientObj) => {
            const hasError = patientObj?.contracts?.some((contract) =>
              contract?.errors?.some(
                (error) => error?.errorType === ErrorType.ErrorType_Error
              )
            );
            const patientId = patientObj.patient?.id;
            if (hasError && patientId) invalidPatientIds.push(patientId);
            else if (patientId) validPatientIds.push(patientId);
          });
        });
        invalidPatientIds = unique(invalidPatientIds);
        validPatientIds = unique(validPatientIds);
        validPatientIds = validPatientIds.filter(
          (id) => !invalidPatientIds.includes(id)
        );

        const errors = groupErrors
          .map((item) =>
            item.patients
              .map((p) =>
                p.contracts.map((c) => c.errors.map((e) => e.errorType)).flat(2)
              )
              .flat(2)
          )
          .flat(2);
        const countError = errors.filter(
          (e) => e === ErrorType.ErrorType_Error
        ).length;
        const countHint = errors.filter(
          (e) => e === ErrorType.ErrorType_Warning
        ).length;

        const responseData = data.data?.responseData || [];

        const ignoreDocumentBillingPayload: IgnoreBilling[] = [];

        groupErrors.forEach((group) => {
          const {
            yearQuarter: { year, quarter },
          } = group;

          group.patients.forEach((patientWrapper) => {
            if (!patientWrapper.patient) return;
            const patientId = patientWrapper.patient.id;

            patientWrapper.contracts.forEach((contractWrapper) => {
              const contractId = contractWrapper.contract;

              const isError = contractWrapper.errors.filter(
                (e) => e.errorType === ErrorType.ErrorType_Error
              );

              if (isError.length > 0) {
                ignoreDocumentBillingPayload.push({
                  patientId,
                  contractId,
                  yearQuarter: { year, quarter },
                  doctorId: isError[0].doctorId,
                });
              }
            });
          });
        });

        const lstPatientIds = Object.keys(patientInfo);
        const referenceDoctorId = {};
        const lstdoctorIds = Object.keys(doctorInfo);
        referenceDoctorId[lstPatientIds[0]] = lstdoctorIds[0];
        const hintErrorList: HintErrorListResponse = {
          countError,
          countHint,
          ignoreDocumentedBillingList: ignoreDocumentBillingPayload,
          patientErrorByQuarter: {},
          patientHintErrorList: responseData as any,
          patientIdsNoError: validPatientIds,
          patientIdsHasError: invalidPatientIds,
          patientIdsHasHint: [],
          referenceDoctorId: referenceDoctorId,
        };
        setHintError(hintErrorList);
        setErrorsListByQuarterPatient(data.data);
        setErrorListByQuarter(groupErrors);
        setBillingResponse([]);
        setDetectChanged(false);
        setStep(2);
        setIsPreparingData(false);
        setValidationList(
          lstPatientIds?.map((patientId) => ({
            isChild: false,
            patientId,
            validation: '',
          })) ?? []
        );
      } else {
        throw new Error(data.systemError);
      }
    }
  });

  const onStartTroubleShooting = async (
    selectBillingFilter: ISelectedFilter,
    ignoreIds: IgnoreBilling[]
  ) => {
    localStorage.removeItem(SV_BILLING_KEY_LOCAL_STORAGE);
    setIsPreparingData(true);
    try {
      ErrorHintMessageService.getHintErrorListViaHpm(
        selectBillingFilter,
        submitPrescription,
        ignoreIds,
        referenceId
      );
      setSelectedFilter(selectBillingFilter);

      const { selectedContractId, selectedQuarter, selectedDoctorId } =
        selectBillingFilter;
      const filteredEncounterByQuarter = {};
      selectedQuarter.map(({ year, quarter }) => {
        const timelines =
          groupEncounterByQuarter?.[year]?.[quarter]?.filter(
            (encounter) =>
              selectedDoctorId?.includes(encounter?.billingDoctorId || '') &&
              selectedContractId?.includes(encounter?.contractId || '')
          ) ?? [];
        if (timelines?.length) {
          if (filteredEncounterByQuarter[year] === undefined) {
            filteredEncounterByQuarter[year] = {};
            if (filteredEncounterByQuarter[year][quarter] === undefined) {
              filteredEncounterByQuarter[year][quarter] = {};
            }
          }
          filteredEncounterByQuarter[year][quarter] = timelines;
        }
      });

      setSelectedBillingEncounter(filteredEncounterByQuarter);
    } catch (err) {
      setIsPreparingData(false);
      throw err;
    }
  };

  function onResubmitFailedBillingPackages() {
    const failtedPackages = billingResponse
      .filter(
        (item) =>
          item.data &&
          item.data.status ===
            BillingSubmissionResponseStatus.BillingSubmissionResponseStatus_Error
      )
      .map((item) => item.data.errorBillingSubmission.billingSubmission);
    const successPackages = billingResponse.filter(
      (item) =>
        item.data &&
        item.data.status ===
          BillingSubmissionResponseStatus.BillingSubmissionResponseStatus_Ok
    );
    setBillingResponse(successPackages);
    return reSubmitBillings({
      billingSubmissions: failtedPackages,
      referenceId: billingSocketId,
      submitPrescription: submitPrescription,
    });
  }

  if (state.isLoading) {
    return (
      <Flex className={className}>
        <LoadingState />
      </Flex>
    );
  }

  if (!billAbleEncounters || Object.keys(billAbleEncounters)?.length === 0) {
    return (
      <Flex justify="center" className={className}>
        <Flex justify="center" column>
          <H2 textAlign="center">{t('noThingToBillYet')}</H2>
          <BodyTextL>{t('yourBillingWillAppearHere')}</BodyTextL>
        </Flex>
      </Flex>
    );
  }

  if (billedResponse) {
    return (
      <BillingResult
        contractInfo={contractList}
        doctorInfo={doctorInfo}
        totalPackage={totalPackage}
        receivedPackage={billingResponse}
        gotoTab={gotoTab}
        resetComponentToDefaultState={resetComponentToDefaultState}
        onResubmit={onResubmitFailedBillingPackages}
        submitPrescription={submitPrescription}
        billedResponse={billedResponse}
      />
    );
  }

  if (fixValidationPatientId) {
    return <PatientValidation isSvBilling />;
  }

  return (
    <Flex className={className} column>
      {isPreparingData && <LoadingState shadow />}
      <HpmConnectionStatus />
      <LeaveConfirmModal
        isOpen={false}
        onConfirm={() => closeConfirmModal(true)}
        onClose={() => closeConfirmModal(false)}
      />
      <SelectBilling
        doctorInfo={doctorInfo}
        contractList={contractList}
        quarters={groupEncounterByQuarter}
        setStep={setStep}
        onStartTroubleShooting={onStartTroubleShooting}
        step={step}
        setDetectChanged={setDetectChanged}
        setBillingSelectionHeight={setBillingSelectionHeight}
        submitPrescription={submitPrescription}
        setSubmitPrescription={setSubmitPrescription}
        filterP4Service={filterP4Service}
        setFilterP4Service={setFilterP4Service}
        setNested={setNested}
        selectedQuarter={selectedQuarter}
        setSelectedQuarter={setSelectedQuarter}
        selectedDoctorId={selectedDoctorId}
        setSelectedDoctorId={setSelectedDoctorId}
        selectedContractId={selectedContractId}
        setSelectedContractId={setSelectedContractId}
        precondition={precondition}
        setPreCondition={setPreCondition}
      />

      <ErrorList
        billingEncounter={selectedBillingEncounter}
        doctorInfo={doctorInfo}
        patientInfo={patientInfo}
        step={step}
        hintError={hintError}
        setStep={setStep}
        selectFilter={selectedFilter}
        canViewDirectly={!detectChanged}
        billingSelectionHeight={billingSelectionHeight}
        setBillingSummariesWithError={setBillingSummariesWithError}
        onStartTroubleShooting={onStartTroubleShooting}
        restartTroubleShooting={() => {}}
        errors={errorsListByQuarterPatient}
        // setIgnoreBilling={setIgnoreBilling}
        // ignoreBilling={ignoreBilling}
        setBillingSummaries={setBillingSummaries}
        referenceId={referenceId}
        setPatientInfo={setPatientInfo}
        nested={nested}
      />

      {/* <ErrorFree onClickHeader={setStep} step={step} /> */}

      <BillingSummary
        contractList={contractList}
        doctorInfo={doctorInfo}
        step={step}
        analyzeForP4Patient={morbidPatientByQuarter}
        setStep={setStep}
        canViewDirectly={!detectChanged}
        patientInfo={patientInfo}
        billingSelectionHeight={billingSelectionHeight}
        billingSummariesWithError={billingSummariesWithError}
        submitPrescription={submitPrescription}
        filterP4Service={filterP4Service}
        billingSummaries={billingSummaries}
        selectFilter={selectedFilter}
        hintError={hintError}
        // ignoreBilling={ignoreBilling}
        setBilledResponse={setBilledResponse}
        referenceId={referenceId}
      />
    </Flex>
  );
}

export default memo(
  withRouter(
    I18n.withTranslation(UnBilled, {
      namespace: 'Billing',
      nestedTrans: 'UnBilled',
    })
  )
);
