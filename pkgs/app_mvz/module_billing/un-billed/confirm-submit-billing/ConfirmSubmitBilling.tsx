import React from 'react';
import { Classes, Button } from '@tutum/design-system/components/Core';
import { Flex } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';

export interface IConfirmSubmitBillingProps {
  className?: string;
  callBack: (isCancel: boolean) => void;
}

const ConfirmSubmitBilling = ({
  className,
  callBack,
}: IConfirmSubmitBillingProps) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.UnBilled>({
    namespace: 'Billing',
    nestedTrans: 'UnBilled',
  });

  return (
    <div className={className}>
      <div key={Classes.DIALOG_BODY} className={Classes.DIALOG_BODY}>
        <Flex p={5} mb={10}>
          {t('confirmationDes')}
        </Flex>
      </div>
      <div key={Classes.DIALOG_FOOTER} className={Classes.DIALOG_FOOTER}>
        <div className="sl-acction">
          <Button onClick={() => callBack(true)} className="sl-w-45">
            {t('cancel')}
          </Button>
          <Button
            onClick={() => callBack(false)}
            className="sl-w-45"
            intent="primary"
          >
            {t('submit')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmSubmitBilling;
