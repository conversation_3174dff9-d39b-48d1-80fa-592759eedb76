import {
  BodyTextL,
  BodyTextM,
  Box,
  Button,
  Flex,
  IMenuItem,
  Label,
  Svg,
} from '@tutum/design-system/components';
import {
  Divider,
  Intent,
  MenuItem,
} from '@tutum/design-system/components/Core';
import { Select } from '@tutum/design-system/components/Select';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  FormInfoResponse,
  FormType,
  removeFromShoppingBag,
  SpecialIdentifier,
  updateShoppingBagInformation,
  updateTreatmentDoctorMedicationForm,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { FormName } from '@tutum/hermes/bff/form_common';
import { NormSizeCodeListItem } from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { Medicine } from '@tutum/hermes/bff/legacy/medicine_common';
import { EPrescription } from '@tutum/hermes/bff/legacy/repo_medicine_common';
import { DocumentStatus } from '@tutum/hermes/bff/qes_common';
import { MedicineInfo } from '@tutum/hermes/bff/repo_medicine_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import Cave from '@tutum/mvz/components/cave';
import { FormError, FormWarning } from '@tutum/mvz/components/form-common';
import {
  FORM_SETTING_OBJECT,
  RULE_KP7_90_FORMS,
} from '@tutum/mvz/constant/form';
import GlobalContext, {
  IGlobalContext,
} from '@tutum/mvz/contexts/Global.context';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import ErezeptSetting from '@tutum/mvz/module_medication_kbv/medication-print-preview/e-rezept-setting/ErezeptSetting';
import {
  IPrintPreviewStore,
  printPreviewActions,
  usePrintPreviewStore,
} from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.store';
import VosButton from '@tutum/mvz/module_medication_kbv/medication-print-preview/print-preview-setting/VosButton';
import IconsCell, {
  checkHasAnyIconInPrintPreview,
} from '@tutum/mvz/module_medication_kbv/medication/components/icons-cell';
import { medicationActions } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import {
  FormMedicine,
  IPrintPreviewForm,
} from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import forOwn from 'lodash/forOwn';
import { memo, useContext, useMemo, useState } from 'react';
import { useMedicationShoppingBagStore } from '../../shopping-bag/MedicationShoppingBag.store';
import { PreviewERP } from '@tutum/mvz/module_e-documents';
import {
  FormElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import { isEmpty } from 'lodash';
import { PrinterInformation } from '@tutum/mvz/components/printer-information';
import { SPACE_NUMBER } from '@tutum/design-system/styles';
import SelectDoctorBsnr from '@tutum/mvz/components/select-doctor-bsnr';
import { useQueryCheckDummyVknr } from '@tutum/hermes/bff/legacy/app_mvz_schein';

const WarningIcon = '/images/alert-circle-solid.svg';

export interface IPrintPreviewSettingProps {
  selectedContractDoctor: ISelectedContractDoctor;
  groupMedicineByForm: {
    [key: string]: IPrintPreviewForm[];
  };
  patient?: IPatientProfile;
  treatmentDoctorId?: string;
  onRequestPrescribe: (printDate?) => unknown;
  isViewForm: boolean;
  viewFormId?: string;
  onPrescribeSingleMedicationForm: (printDate?) => unknown;
  currentFormType?: FormType;
  warningN1MedicineSizeCodes?: NormSizeCodeListItem[];
  formInfo?: FormInfoResponse;
  bsnr?: string;
  clearSearchBox?: () => void;
  printPreviewStore?: IPrintPreviewStore;
  formAnnotationSetting: Record<string, FORM_SETTING_OBJECT>;
  assignedToBsnrId?: string;
}

function PrintPreviewSettingMemo(
  props: IPrintPreviewSettingProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.PrintPreviewSetting> &
    IGlobalContext
) {
  const {
    className,
    t,
    selectedContractDoctor,
    groupMedicineByForm,
    patient,
    treatmentDoctorId,
    onRequestPrescribe,
    isViewForm,
    viewFormId,
    onPrescribeSingleMedicationForm,
    warningN1MedicineSizeCodes,
    formInfo,
    bsnr,
    clearSearchBox,
    formAnnotationSetting,
    assignedToBsnrId,
  } = props;
  const medicationContext = useContext(MedicationContext);
  const { t: tCommon } = I18n.useTranslation<any>({
    namespace: 'Common',
    nestedTrans: 'Select',
  });
  const [isConnectorError, setIsConnectorError] = useState(false);
  const [isInvalidPartialPrescription, setIsInvalidPartialPrescription] =
    useState(false);

  const { patientManagement } = useContext(PatientManagementContext.instance);
  const { showHintVSST785 } = patientManagement;

  const currentSchein = useCurrentSchein();
  const { shoppingBag, diga, onPrescribe, onPrint } =
    useMedicationShoppingBagStore();
  const printPreviewStore = usePrintPreviewStore();
  const formInfoResponse = formInfo?.formInfoResponse?.[0];

  const { data, isSuccess } = useQueryCheckDummyVknr(
    {
      scheinId: currentSchein?.scheinId as string,
    },
    {
      enabled: !!currentSchein,
    }
  );

  const { indexPrescriptionFormSelected, currentFormType, medicines } =
    useMemo<{
      indexPrescriptionFormSelected?: number;
      currentFormType?: string;
      medicines: FormMedicine[] | undefined;
    }>(() => {
      const flattenGroupMedicineByForm =
        printPreviewActions.flattenGroupByForm(groupMedicineByForm);
      const currentMedicines = flattenGroupMedicineByForm?.find(
        (item) => item.id === printPreviewStore.formViewId
      )?.medicines;
      const selectedPrescriptionForm = flattenGroupMedicineByForm.find(
        (item) =>
          item.id === printPreviewStore.formViewId &&
          !!item.medicines[0]?.isEPrescription
      );
      if (selectedPrescriptionForm) {
        const indexPrescriptionFormSelected = groupMedicineByForm[
          selectedPrescriptionForm.medicines[0].currentFormType
        ].findIndex(
          (prescriptionForm) =>
            prescriptionForm.id === selectedPrescriptionForm.id
        );
        return {
          indexPrescriptionFormSelected,
          currentFormType:
            selectedPrescriptionForm.medicines?.[0]?.currentFormType,
          medicines: currentMedicines,
        };
      }

      return {
        medicines: currentMedicines,
      };
    }, [groupMedicineByForm, printPreviewStore.formViewId]);

  const updateEPrescriptionFormData = (ePrescription: EPrescription) => {
    const groupMedicineByFormCloned = [
      ...groupMedicineByForm[currentFormType || ''],
    ];
    groupMedicineByFormCloned[indexPrescriptionFormSelected || ''] = {
      ...groupMedicineByFormCloned[indexPrescriptionFormSelected || ''],
      ePrescription,
    };

    printPreviewActions.setGroupForms({
      ...groupMedicineByForm,
      [currentFormType || '']: groupMedicineByFormCloned,
    });
  };

  const isERezept = Boolean(
    formInfoResponse?.['erezeptItemStatus'] ||
    formInfoResponse?.['isEPrescription'] ||
    (medicines?.[0] as unknown as MedicineInfo)?.isEPrescription
  );
  const medicinesHaveIcons = useMemo(() => {
    return medicines?.filter((medicine) =>
      checkHasAnyIconInPrintPreview(medicine as unknown as Medicine)
    );
  }, [medicines]);

  const selectItemKeyValueRender = (item, { handleClick }) => {
    return (
      <MenuItem
        key={item.value}
        text={item.name}
        onClick={handleClick}
        shouldDismissPopover={true}
      />
    );
  };

  const updateTreatMentDoctor = (
    selectedItem: IMenuItem<string, IEmployeeProfile>
  ) => {
    if (!selectedItem.data) {
      return;
    }

    if (isViewForm) {
      return updateTreatmentDoctorMedicationForm({
        medicationFormId: viewFormId as string,
        treatmentDoctorId: selectedItem?.data?.id!,
        assignedToBsnrId: selectedItem?.data?.bsnrId!,
        patientId: patient?.id as string,
      });
    } else {
      return updateShoppingBagInformation({
        doctorId: selectedContractDoctor?.doctorId,
        patientId: patient?.id,
        contractId: selectedContractDoctor?.contractId,
        treatmentDoctorId: selectedItem?.data?.id,
        assignedToBsnrId: selectedItem?.data?.bsnrId,
        bsnr,
      });
    }
  };

  const renderSetting = () => {
    if (isERezept) {
      const formData: {
        ePrescription: EPrescription | undefined;
        medicines: FormMedicine[];
      } = {
        ePrescription: {
          isPrescribePartialPrescription: false,
        },
        medicines: [],
      };

      if (isViewForm && formInfo) {
        formData.ePrescription = formInfo.ePrescription;
      }

      if (
        indexPrescriptionFormSelected &&
        indexPrescriptionFormSelected !== -1 &&
        groupMedicineByForm[currentFormType || '']
      ) {
        const selectedPrescriptionForm =
          groupMedicineByForm[currentFormType || ''][
          indexPrescriptionFormSelected
          ];
        formData.ePrescription = selectedPrescriptionForm.ePrescription;
        formData.medicines = selectedPrescriptionForm.medicines;
      }

      return (
        <>
          <ErezeptSetting
            ePrescription={formData.ePrescription}
            medicines={formData.medicines}
            isViewForm={isViewForm}
            setIsConnectorError={setIsConnectorError}
            updateEPrescriptionFormData={updateEPrescriptionFormData}
            setIsInvalidPartialPrescription={setIsInvalidPartialPrescription}
            formInfo={formInfo}
            groupMedicineByForm={groupMedicineByForm}
            currentFormType={currentFormType}
            indexPrescriptionFormSelected={indexPrescriptionFormSelected}
          />
          <PrinterInformation formId={FormName.Muster_eRezept} />
        </>
      );
    }

    let formId = '';
    if (diga && diga.formName) {
      formId = FormName[diga.formName];
    }

    if (printPreviewStore.currentFormName) {
      formId = FormName[printPreviewStore.currentFormName];
    }

    if (!formId) {
      return null;
    }

    return (
      <Flex column pt={SPACE_NUMBER.SPACE_S}>
        <PrinterInformation formId={formId} />
      </Flex>
    );
  };

  const isEV = useMemo(() => {
    const currentInsurance = patient?.patientInfo?.insuranceInfos.find(
      (info) => info.id === currentSchein?.insuranceId
    );

    if (!currentInsurance) {
      return false;
    }

    return isEVCase(currentInsurance, datetimeUtil.now(), currentSchein);
  }, [patient, currentSchein]);

  const isWarningEV = useMemo(() => {
    return (
      isEV &&
      RULE_KP7_90_FORMS.includes(printPreviewStore.currentFormName as FormName)
    );
  }, [isEV, printPreviewStore.currentFormName]);

  const hasDummyVknr = isSuccess && data.isDummy;

  const renderButton = () => {
    let currentForm;
    if (isViewForm) {
      currentForm = formInfo;
    } else {
      forOwn(groupMedicineByForm, (groups) => {
        groups.map((item) => {
          if (item.id === printPreviewStore.formViewId) {
            currentForm = item;
          }
        });
      });
    }

    const isDisabled =
      hasDummyVknr ||
      isInvalidPartialPrescription ||
      (isConnectorError && isERezept);
    const isSaveButtonDisabled = hasDummyVknr || isInvalidPartialPrescription;
    const flattenGroupMedicineByForm =
      printPreviewActions.flattenGroupByForm(groupMedicineByForm);
    const hasSaveOrPrescribe = Boolean(
      currentForm?.hasSaveOrPrescribe || currentForm?.prescribeDate
    );
    const showPrescribeButton = isERezept
      ? currentForm?.eRezeptStatus !== DocumentStatus.Status_Sent
      : hasSaveOrPrescribe;

    return (
      <Flex w="100%" gap={16} column>
        <Divider className="group-button-divider" />
        <Flex
          className="row sl-btn-actions"
          justify="flex-end"
          gap={10}
          mx={16}
        >
          {!isViewForm ? (
            <>
              {patient && currentSchein && !isERezept && (
                <VosButton
                  treatmentDoctorId={treatmentDoctorId}
                  patientId={patient.id}
                  scheinId={currentSchein.scheinId}
                  groupForms={groupMedicineByForm}
                  formAnnotationSetting={formAnnotationSetting}
                />
              )}
              {!currentForm?.hasSaveOrPrescribe && (
                <div>
                  <Button
                    disabled={isSaveButtonDisabled}
                    large
                    outlined
                    intent={Intent.PRIMARY}
                    loading={printPreviewStore?.isLoadingPrescribe}
                    {...registerActionChainElementId(
                      FormElementId.DIALOG_SAVE_BUTTON
                    )}
                    onClick={async () => {
                      await onPrescribeSingleMedicationForm();
                      clearSearchBox?.();
                      medicationActions?.resetPackageSizeStore();
                      medicationContext.setInRefillProcess(false);
                    }}
                  >
                    {t('save')}
                  </Button>
                </div>
              )}
              <div>
                <Button
                  disabled={
                    isDisabled ||
                    flattenGroupMedicineByForm.length < 2 ||
                    !!diga // Disabled when only one form can prescribe
                  }
                  large
                  outlined
                  intent={Intent.PRIMARY}
                  className="sl-btn-print sl-default-button"
                  loading={printPreviewStore?.isLoadingPrescribe}
                  onClick={async () => {
                    const isSuccess = await onRequestPrescribe(
                      datetimeUtil.now()
                    );
                    if (isSuccess) {
                      clearSearchBox?.();
                      medicationActions?.resetPackageSizeStore();
                      medicationContext.setInRefillProcess(false);
                      removeFromShoppingBag({
                        shoppingBagId: shoppingBag?.shoppingBagId,
                        doctorId: selectedContractDoctor?.doctorId,
                        patientId: patient?.id,
                        bsnr,
                      });
                      printPreviewActions.setFormViewId(null);
                      printPreviewActions.setCurrentFormName('');
                    }
                  }}
                >
                  {t('prescribeAll')}
                </Button>
              </div>
              {!currentForm?.hasSaveOrPrescribe && (
                <>
                  <div>
                    <Button
                      disabled={isDisabled}
                      large
                      intent={Intent.PRIMARY}
                      loading={printPreviewStore?.isLoadingPrescribe}
                      {...registerActionChainElementId(
                        FormElementId.DIALOG_PRINT_BUTTON
                      )}
                      onClick={async () => {
                        await onPrescribeSingleMedicationForm(
                          datetimeUtil.now()
                        );
                        clearSearchBox?.();
                        medicationActions?.resetPackageSizeStore();
                        medicationContext.setInRefillProcess(false);
                        onPrescribe?.();
                      }}
                    >
                      {t('prescribe')}
                    </Button>
                  </div>
                </>
              )}
            </>
          ) : (
            showPrescribeButton && (
              <div>
                <Button
                  disabled={isDisabled}
                  large
                  intent={Intent.PRIMARY}
                  loading={printPreviewStore?.isLoadingPrescribe}
                  onClick={async () => {
                    await onPrescribeSingleMedicationForm(datetimeUtil.now());
                    medicationActions?.resetPackageSizeStore();
                    medicationContext.setInRefillProcess(false);
                    if (isERezept) {
                      printPreviewActions.setFormViewId(null);
                      printPreviewActions.setCurrentFormName('');
                      onPrint?.();
                    }
                  }}
                >
                  {isERezept ? t('prescribe') : t('print')}
                </Button>
              </div>
            )
          )}
        </Flex>
      </Flex>
    );
  };

  const handleSelectBtmSpecialExceeding = (selectedSpecialExceeding) => {
    const newBTMGroup = (groupMedicineByForm[FormType.BTM] || []).map(
      (item) => {
        if (item.id === printPreviewStore.formViewId) {
          const ePrescription = {
            ...item.ePrescription,
            specialIdentifier: selectedSpecialExceeding,
          };
          return { ...item, ePrescription };
        }
        return item;
      }
    ) as IPrintPreviewForm[];
    printPreviewActions.setGroupForms({
      ...groupMedicineByForm,
      [FormType.BTM]: newBTMGroup,
    });
  };

  const btmData = [
    SpecialIdentifier.SpecialIdentifierA,
    SpecialIdentifier.SpecialIdentifierN,
    SpecialIdentifier.SpecialIdentifierS,
    SpecialIdentifier.SpecialIdentifierSZ,
    SpecialIdentifier.SpecialIdentifierST,
  ].map((item) => ({
    name: item,
    value: item,
  }));

  const renderMedicationBtm = () => {
    if (groupMedicineByForm[FormType.BTM]) {
      const currentFormBTM = groupMedicineByForm[FormType.BTM].find(
        (item) => item.id === printPreviewStore.formViewId
      );
      if (currentFormBTM) {
        return (
          <>
            <h1>{t('specialInformation')}</h1>
            <Flex className="sl-btm-select-label">{t('btmSelectLabel')}</Flex>
            <Flex className="sl-select-doctor-container">
              <Select
                items={[
                  {
                    name: tCommon('select'),
                    value: '',
                  },
                  ...btmData,
                ]}
                filterable={false}
                popoverProps={{ usePortal: false }}
                itemRenderer={selectItemKeyValueRender}
                onItemSelect={(selectedItem) => {
                  handleSelectBtmSpecialExceeding(selectedItem.value);
                }}
                disabled={currentFormBTM.hasSaveOrPrescribe || isViewForm}
              >
                <Button
                  text={
                    btmData?.find(
                      (d) =>
                        d.value ===
                        currentFormBTM.ePrescription?.specialIdentifier
                    )?.name || tCommon('select')
                  }
                  fill
                  rightIcon="caret-down"
                />
              </Select>
            </Flex>
          </>
        );
      }
    }
    return null;
  };

  return (
    <Flex auto column className={className}>
      <Flex className="setting-wrapper" column>
        {showHintVSST785 && <FormWarning labels={['indicatedDiagnose']} />}
        {/* refs: PRO-9511 */}
        {!bsnr && isWarningEV && <FormWarning labels={['noInsurance']} />}
        {hasDummyVknr ? <FormError labels={['dummyVknr']} /> : null}
        {warningN1MedicineSizeCodes?.length ? (
          <>
            <Flex
              mx={16}
              p={16}
              align="center"
              style={{
                backgroundColor: COLOR.WARNING_LIGHT,
              }}
            >
              <Svg src={WarningIcon} width={16} />
              &nbsp;
              <b className="sl-warning-text">
                {t('warnMedicines', {
                  warnMedicines: warningN1MedicineSizeCodes
                    ?.map((code) => `"${code.productName}"`)
                    .join(', '),
                })}
              </b>
            </Flex>
            <Flex mb={16} />
          </>
        ) : null}

        {!isEmpty(patient?.patientInfo?.otherInfo?.cave) && (
          <Flex mx={16} w="100%">
            <Cave patient={patient} />
          </Flex>
        )}
        {Boolean(medicinesHaveIcons?.length) && !diga && (
          <Flex column p="0 16px" gap={16} mt={warningN1MedicineSizeCodes?.length ? 0 : 16}>
            <BodyTextL fontWeight="Bold" fontSize={18}>
              {t('medication')}
            </BodyTextL>
            {medicinesHaveIcons?.map((item, index) => (
              <Flex key={index} column gap={4}>
                <BodyTextM>{item?.productInformation?.name}</BodyTextM>
                <IconsCell
                  medicine={item as unknown as Medicine}
                  isFirstView={false}
                />
              </Flex>
            ))}
          </Flex>
        )}
        {/* <Divider className="sl-divider" /> */}
        <Flex className="sl-sign-by" mt={16} mx={16} column>
          <BodyTextL fontWeight="Bold" fontSize={18} margin="0 0 8px 0">
            {t('signBy')}
          </BodyTextL>
          <Flex mb={4}>
            <Label className="sl-label-right-side">{t('doctorLabel')}</Label>
          </Flex>
          <SelectDoctorBsnr<IEmployeeProfile>
            doctors={selectedContractDoctor?.availableDoctor}
            selectedValue={
              treatmentDoctorId &&
              assignedToBsnrId &&
              `${treatmentDoctorId}-${assignedToBsnrId}`
            }
            onItemSelect={updateTreatMentDoctor}
            isDisabled={isERezept || isViewForm}
            getOptionLabel={(item: IMenuItem<string, IEmployeeProfile>) =>
              nameUtils.getDoctorName(item.data)
            }
          />
        </Flex>
        {((isViewForm && isERezept) ||
          (indexPrescriptionFormSelected !== -1 &&
            groupMedicineByForm[currentFormType || '']?.length) ||
          !!groupMedicineByForm[FormType.BTM]) && (
            <Divider className="sl-divider" />
          )}
        <Flex className="sl-main" column mb={24} mx={16}>
          {renderMedicationBtm()}
          {renderSetting()}
        </Flex>
      </Flex>
      {renderButton()}
      <PreviewERP isViewOnly />
    </Flex>
  );
}

export default memo(
  GlobalContext.withContext(
    I18n.withTranslation(PrintPreviewSettingMemo, {
      namespace: 'Medication',
      nestedTrans: 'PrintPreviewSetting',
    })
  )
);
