import { Flex, LoadingState, Svg } from '@tutum/design-system/components';
import { parseNumber } from '@tutum/design-system/infrastructure/utils';
import {
  FormType,
  MedicineShoppingBagInfo,
} from '@tutum/hermes/bff/app_mvz_medicine';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { usePrintPreviewStore } from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.store';
import { IPrintPreviewForm } from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import React, { memo, useCallback, useEffect, useState } from 'react';
import {
  ICustomAnnotationComponent,
  IFormField,
  IFormFieldType,
  IFormInfo,
} from '../../../components/form-annotation/FormAnnotation.type';
import FormAnnotation from '../../../components/form-annotation/RenderAnnotation.styled';
import MedicationInfo from '../medication-info/Medication.styled';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
} from '@tutum/mvz/constant/form';
import { medicationUtil } from '../../utils/medication-util';
import { buildContent } from '../medication-info/MedicationInfo';
import {
  buildDigaContent,
  Blue_FORM_MAX_LENGTH,
  chooseLabelToDisplay,
  getCustomLabelMedicationWhenPrint,
} from '../helper';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { DigaInfo } from '../../shopping-bag/MedicationShoppingBag.store';
import { isEmpty } from 'lodash';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { checkIsKvSchein } from '@tutum/mvz/_utils/scheinFormat';
import { ScheinItem } from '@tutum/hermes/bff/legacy/schein_common';

const formSvg = '/data/form/Blaues_Rezept.svg';

export type IBlueRezeptProps = {
  className?: string;
  medicineFormInfo?: IPrintPreviewForm;
  digaInfo?: DigaInfo;
  patient?: IPatientProfile;
  formInfoMap: FORM_SETTING_OBJECT;
  setEditingMedicine?: (medicine: MedicineShoppingBagInfo) => void;
  onChangeFormSetting?: (
    formId: string,
    formSetting: FORM_SETTING_OBJECT
  ) => void;
  onChangePrescribeDate?: (
    formTyppe: FormType,
    formId: string,
    selectedDate: number
  ) => void;
  initFormSetting?: FORM_SETTING_OBJECT;
  isViewOnly?: boolean;
  doctorStamp?: IEmployeeProfile;
  doctorId?: string;
  isShow?: boolean;
  bsnr?: string;
  currentSchein?: ScheinItem;
} & ({ medicineFormInfo: IPrintPreviewForm } | { digaInfo: DigaInfo });

function BlueRezeptMemo(
  props: IBlueRezeptProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.KkRezept>
) {
  const {
    className,
    medicineFormInfo,
    setEditingMedicine,
    onChangeFormSetting,
    onChangePrescribeDate,
    initFormSetting,
    isViewOnly,
    formInfoMap,
    digaInfo,
    isShow,
    bsnr,
    currentSchein,
    ...restProps
  } = props;

  const { t: tMedicine } = I18n.useTranslation<
    keyof typeof MedicationI18n.RenderAnnotation
  >({
    namespace: 'Medication',
    nestedTrans: 'RenderAnnotation',
  });

  const { t: tDiga } = I18n.useTranslation<keyof typeof DigaListLang.FormPrint>(
    {
      namespace: 'Diga',
      nestedTrans: 'FormPrint',
    }
  );

  const printPreviewStore = usePrintPreviewStore();

  const [formInfo, setFormInfo] = useState<IFormInfo>({} as IFormInfo);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadedImg, setLoadedImg] = useState<boolean>(false);
  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>(
    initFormSetting || {}
  );
  const [customFormInfoMap, setCustomFormInfoMap] =
    useState<FORM_SETTING_OBJECT>({});

  const loadFormMeta = useCallback((): Promise<IFormInfo> => {
    return fetchWithHeaders('GET', '/data/form/Blaues_Rezept.json').then(
      (response) => response.data || []
    );
  }, []);

  //Initial for loading annotation data
  useEffect(() => {
    loadFormMeta()
      .then((formInfo) => {
        setFormInfo(formInfo);
        let formData = medicationUtil.preProcessFormValue(formInfo);

        if (digaInfo) {
          buildDigaContent(digaInfo, tDiga, Blue_FORM_MAX_LENGTH).forEach(
            (item) => {
              const content = item.rawText || '';
              const customData = content.split('\n').reduce(
                (formData, content, index) => {
                  return {
                    ...formData,
                    [`label_medication1-${index + 1}`]: content,
                  };
                },
                {
                  'label_medication1-1': '',
                  'label_medication1-2': '',
                  'label_medication1-3': '',
                  'label_medication1-4': '',
                  'label_medication1-5': '',
                  'label_medication1-6': '',
                }
              );

              formData = {
                ...formData,
                ...customData,
                [item.fieldName]: content,
              };
            }
          );
        } else {
          medicineFormInfo?.medicines.forEach((medicine, index) => {
            const fieldName = chooseLabelToDisplay(
              medicineFormInfo.medicines,
              index,
              Blue_FORM_MAX_LENGTH,
              tMedicine
            );
            const medicineContent = buildContent(medicine, tMedicine);

            formData[fieldName || ''] = medicineContent;
          });

          // for printing must to custom label medication
          const values = getCustomLabelMedicationWhenPrint(
            medicineFormInfo?.medicines,
            Blue_FORM_MAX_LENGTH,
            tMedicine
          );

          formData = {
            ...formData,
            ...values,
          };
        }

        if (!initFormSetting || !Object.keys(initFormSetting).length) {
          setFormValue(formData);
        } else {
          setFormValue({
            ...formData,
            ...initFormSetting,
          });
        }
      })
      .finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    if (onChangeFormSetting) {
      onChangeFormSetting(
        medicineFormInfo?.id || 'customFormSetting',
        formValue
      );
    }
  }, [JSON.stringify(formValue), medicineFormInfo?.id]);

  const onChangeFormEvent = (
    formField: IFormField,
    newVal?: FORM_SETTING_TYPE
  ) => {
    if (
      formField.type === IFormFieldType.DATE_PICKER &&
      formField.name === 'date_prescribe'
    ) {
      if (onChangePrescribeDate) {
        return onChangePrescribeDate(
          FormType.Private,
          medicineFormInfo?.id || 'customFormSetting',
          parseNumber(newVal)
        );
      }
    } else if (formValue[formField.name] !== undefined) {
      medicationUtil.processFormValue(
        formField,
        formValue,
        setFormValue,
        newVal!
      );
    }
  };

  const customComponents = (formField: IFormField) => {
    //copy from generated Component
    const comArray: ICustomAnnotationComponent[] = [];
    if (digaInfo) {
      comArray.push(...buildDigaContent(digaInfo, tDiga, Blue_FORM_MAX_LENGTH));
    } else {
      //max length medicine in each form is 3
      medicineFormInfo?.medicines.forEach((medicine, index) => {
        const medicineComponent: ICustomAnnotationComponent = {
          fieldName: chooseLabelToDisplay(
            medicineFormInfo.medicines,
            index,
            Blue_FORM_MAX_LENGTH,
            tMedicine
          ),
          component: () => (
            <MedicationInfo
              prescribeMedicine={[...[medicine]]}
              isViewOnly={isViewOnly || medicineFormInfo.hasSaveOrPrescribe}
              onEditMedicine={setEditingMedicine}
              onChangeFormEvent={onChangeFormEvent}
              formField={formField}
            />
          ),
        };
        comArray.push(medicineComponent);
      });
    }

    return comArray;
  };

  const onChangeBsnr = (data: Record<string, string>) => {
    setFormValue((prevValues) => ({
      ...prevValues,
      ...data,
    }));
    setCustomFormInfoMap((prevValues) => ({
      ...prevValues,
      ...data,
    }));
  };

  useEffect(() => {
    if (!isEmpty(formInfoMap) && (bsnr || currentSchein)) {
      if (!checkIsKvSchein(currentSchein)) {
        setCustomFormInfoMap(formInfoMap);
        return;
      }

      setCustomFormInfoMap({
        ...formInfoMap,
        label_insurance_name: 'PRIVAT',
        label_ik_number: '',
        label_insurance_number: ''
      })
    }
  }, [formInfoMap, currentSchein, bsnr]);

  if (loading) {
    return <LoadingState />;
  }

  return (
    <>
      {(printPreviewStore.formViewId === medicineFormInfo?.id || isShow) && (
        <Flex auto justify="center" className={className}>
          <div id="medication-form" className="sl-form">
            <Svg
              id="img-form"
              className="sl-img"
              src={formSvg}
              onLoad={() => setLoadedImg(true)}
            />
            {formInfo?.meta?.fields?.length > 0 &&
              loadedImg &&
              formInfo?.meta?.fields.map((formField) => {
                if (
                  formField.name.includes('label_medication') &&
                  !customComponents(formField).find(
                    (field) => field.fieldName === formField.name
                  )
                ) {
                  return null;
                }

                return (
                  <FormAnnotation
                    {...restProps}
                    formField={formField}
                    key={formField.name}
                    labelFontSize="23px"
                    onChangeEvent={onChangeFormEvent}
                    formMetaData={formInfo}
                    componentValue={
                      formField.name === 'date_prescribe'
                        ? medicineFormInfo?.prescribeDate
                        : formValue[formField.name]
                    }
                    prescribeDate={medicineFormInfo?.prescribeDate}
                    isViewOnly={
                      isViewOnly || medicineFormInfo?.hasSaveOrPrescribe
                    }
                    dateRange={
                      formField.name === 'date_prescribe'
                        ? {
                          minDate: undefined,
                          maxDate: datetimeUtil.date(),
                        }
                        : undefined
                    }
                    customComponents={customComponents(formField)}
                    formInfoMap={customFormInfoMap}
                    onChangeBsnr={onChangeBsnr}
                  />
                );
              })}
          </div>
        </Flex>
      )}
    </>
  );
}

export default memo(
  I18n.withTranslation(BlueRezeptMemo, {
    namespace: 'Medication',
    nestedTrans: 'KkRezept',
  })
);
