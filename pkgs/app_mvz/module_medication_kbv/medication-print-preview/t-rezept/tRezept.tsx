import { Flex, LoadingState, Svg } from '@tutum/design-system/components';
import { getAge, parseNumber } from '@tutum/design-system/infrastructure/utils';
import {
  FormType,
  MedicineShoppingBagInfo,
} from '@tutum/hermes/bff/app_mvz_medicine';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { usePrintPreviewStore } from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.store';
import { IPrintPreviewForm } from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import React, { memo, useCallback, useEffect, useState } from 'react';
import {
  ICustomAnnotationComponent,
  IFormField,
  IFormFieldType,
  IFormInfo,
} from '../../../components/form-annotation/FormAnnotation.type';
import FormAnnotation from '../../../components/form-annotation/RenderAnnotation.styled';
import MedicationInfo from '../medication-info/Medication.styled';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
} from '@tutum/mvz/constant/form';
import { medicationUtil } from '../../utils/medication-util';
import {
  CHECKBOX_COPAYMENT_1,
  CHECKBOX_COPAYMENT_2,
  COPAYMENT_SINGLE_CHECKBOX,
} from '../kk-rezept/KkRezept';
import { checkCopaymentExemptionTillDate } from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { buildContent } from '../medication-info/MedicationInfo';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import {
  getCustomLabelMedicationWhenPrint,
  T_FORM_MAX_LENGTH,
} from '../helper';
import { isEmpty } from 'lodash';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

const formSvg = '/data/form/T-Rezept-Muster.svg';

export interface ITRezeptProps {
  medicineFormInfo: IPrintPreviewForm;
  patient?: IPatientProfile;
  formInfoMap: { [key: string]: string };
  setEditingMedicine?: (medicine: MedicineShoppingBagInfo) => void;
  onChangeFormSetting?: (
    formId: string,
    formSetting: FORM_SETTING_OBJECT
  ) => void;
  onChangePrescribeDate?: (
    formTyppe: FormType,
    formId: string,
    selectedDate: number
  ) => void;
  initFormSetting?: FORM_SETTING_OBJECT;
  isViewOnly?: boolean;
  doctorStamp?: IEmployeeProfile;
  doctorId?: string;
}

function TRezeptMemo(
  props: ITRezeptProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.KkRezept>
) {
  const {
    className,
    medicineFormInfo,
    setEditingMedicine,
    onChangeFormSetting,
    onChangePrescribeDate,
    initFormSetting,
    isViewOnly,
    patient,
    formInfoMap,
    ...restProps
  } = props;

  const { t: tMedicine } = I18n.useTranslation<
    keyof typeof MedicationI18n.RenderAnnotation
  >({
    namespace: 'Medication',
    nestedTrans: 'RenderAnnotation',
  });

  const printPreviewStore = usePrintPreviewStore();
  const {
    schein: { activatedSchein },
  } = usePatientFileStore();

  const [formInfo, setFormInfo] = useState<IFormInfo>({} as IFormInfo);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadedImg, setLoadedImg] = useState<boolean>(false);
  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>(
    initFormSetting || {}
  );
  const [customFormInfoMap, setCustomFormInfoMap] =
    useState<FORM_SETTING_OBJECT>({});

  const loadFormMeta = useCallback((): Promise<IFormInfo> => {
    return fetchWithHeaders('GET', '/data/form/T-Rezept-Muster.json').then(
      (response) => response.data || []
    );
  }, []);

  //Initial for loading annotation data
  useEffect(() => {
    loadFormMeta()
      .then((formInfo) => {
        setFormInfo(formInfo);
        let formData = medicationUtil.preProcessFormValue(formInfo);

        const patientAge = patient?.dateOfBirth
          ? getAge(new Date(patient.dateOfBirth))
          : -1;
        if (patientAge != -1 && patientAge < 18) {
          formData[CHECKBOX_COPAYMENT_1] = true;
        } else {
          const insuranceActive = getActiveInsurance(
            patient?.patientInfo?.insuranceInfos
          );

          if (insuranceActive?.haveCoPaymentExemptionTill) {
            const fieldNameActive = checkCopaymentExemptionTillDate(
              insuranceActive?.copaymentExemptionTillDate
            )
              ? CHECKBOX_COPAYMENT_1
              : CHECKBOX_COPAYMENT_2;
            formData[fieldNameActive] = true;
          } else {
            formData[CHECKBOX_COPAYMENT_2] = true;
          }
        }

        medicineFormInfo.medicines.forEach((medicine) => {
          const medicineContent = buildContent(medicine, tMedicine);

          formData['label_medication'] = medicineContent;
        });

        // for printing must to custom label medication
        const values = getCustomLabelMedicationWhenPrint(
          medicineFormInfo.medicines,
          T_FORM_MAX_LENGTH,
          tMedicine
        );

        formData = {
          ...formData,
          ...values,
        };

        if (!initFormSetting || !Object.keys(initFormSetting).length) {
          setFormValue(formData);
        } else {
          setFormValue({
            ...formData,
            ...initFormSetting,
          });
        }
      })
      .finally(() => setLoading(false));
  }, [activatedSchein]);

  useEffect(() => {
    if (onChangeFormSetting && medicineFormInfo.id) {
      onChangeFormSetting(medicineFormInfo.id, formValue);
    }
  }, [JSON.stringify(formValue), medicineFormInfo?.id]);

  const processOptionalCheckBox = (
    optionalCheckBoxNames: string[],
    formField: IFormField,
    cloned: object
  ) => {
    if (cloned[formField.name] && IFormFieldType.CHECK_BOX) {
      optionalCheckBoxNames
        .filter((name) => name !== formField.name)
        .forEach((name) => {
          cloned[name] = !cloned[formField.name];
        });
    }
  };

  const processMusterTValue = (formField: IFormField, cloned: object) => {
    const onlyGeb = ['checkbox_gebuhrfrei', 'checkbox_gebpfl'];

    if (onlyGeb.includes(formField.name)) {
      processOptionalCheckBox(onlyGeb, formField, cloned);
    }
  };

  const onChangeFormEvent = (
    formField: IFormField,
    newVal?: FORM_SETTING_TYPE
  ) => {
    let dependGroup: string[] = [];
    if (COPAYMENT_SINGLE_CHECKBOX.includes(formField.name)) {
      dependGroup = COPAYMENT_SINGLE_CHECKBOX;
    }
    if (
      formField.type === IFormFieldType.DATE_PICKER &&
      formField.name === 'date_prescribe'
    ) {
      if (onChangePrescribeDate) {
        return onChangePrescribeDate(
          FormType.TPrescription,
          medicineFormInfo.id,
          parseNumber(newVal)
        );
      }
    } else if (formValue[formField.name] !== undefined) {
      const cloned = { ...formValue };
      processMusterTValue(formField, cloned);
      medicationUtil.processFormValue(
        formField,
        formValue,
        setFormValue,
        newVal!,
        dependGroup
      );
    }
  };

  const customComponents = (formField: IFormField) => {
    //copy from generated Component
    const medicineComponent: ICustomAnnotationComponent = {
      fieldName: 'label_medication',
      component: () => (
        <MedicationInfo
          prescribeMedicine={medicineFormInfo.medicines}
          isViewOnly={isViewOnly || medicineFormInfo.hasSaveOrPrescribe}
          onEditMedicine={setEditingMedicine}
          onChangeFormEvent={onChangeFormEvent}
          formField={formField}
        />
      ),
    };
    return [medicineComponent];
  };

  const onChangeBsnr = (data: Record<string, string>) => {
    setFormValue((prevValues) => ({
      ...prevValues,
      ...data,
    }));
    setCustomFormInfoMap((prevValues) => ({
      ...prevValues,
      ...data,
    }));
  };

  useEffect(() => {
    if (!isEmpty(formInfoMap)) {
      setCustomFormInfoMap(formInfoMap);
    }
  }, [formInfoMap]);

  if (loading) {
    return <LoadingState />;
  }

  return (
    <>
      {printPreviewStore.formViewId === medicineFormInfo.id && (
        <Flex auto justify="center" className={className}>
          <div id="medication-form" className="sl-form">
            <Svg
              id="img-form"
              className="sl-img"
              src={formSvg}
              onLoad={() => setLoadedImg(true)}
            />
            {formInfo?.meta?.fields?.length > 0 &&
              loadedImg &&
              formInfo?.meta?.fields.map((formField) => (
                <FormAnnotation
                  {...restProps}
                  formField={formField}
                  key={formField.name}
                  labelFontSize="23px"
                  onChangeEvent={(field, newVal) =>
                    onChangeFormEvent(field, newVal)
                  }
                  formMetaData={formInfo}
                  componentValue={
                    formField.name === 'date_prescribe'
                      ? medicineFormInfo.prescribeDate
                      : formValue[formField.name]
                  }
                  prescribeDate={medicineFormInfo.prescribeDate}
                  isViewOnly={isViewOnly || medicineFormInfo.hasSaveOrPrescribe}
                  dateRange={
                    formField.name === 'date_prescribe'
                      ? {
                        minDate: undefined,
                        maxDate: datetimeUtil.date(),
                      }
                      : undefined
                  }
                  customComponents={customComponents(formField)}
                  formInfoMap={customFormInfoMap}
                  onChangeBsnr={onChangeBsnr}
                />
              ))}
          </div>
        </Flex>
      )}
    </>
  );
}

export default memo(
  I18n.withTranslation(TRezeptMemo, {
    namespace: 'Medication',
    nestedTrans: 'KkRezept',
  })
);
