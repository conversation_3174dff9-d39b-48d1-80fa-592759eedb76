import { memo, useContext, useEffect, useMemo, useState } from 'react';

import {
  alertError,
  alertSuccessfully,
  Flex,
  LoadingState,
  Svg,
} from '@tutum/design-system/components';
import { Classes, Dialog, Divider } from '@tutum/design-system/components/Core';
import {
  flatten,
  getCssClass,
  isNullUUID,
} from '@tutum/design-system/infrastructure/utils';
import {
  NormSizeCodeListItem,
  getPackageSizes,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { PatientProfileResponse } from '@tutum/hermes/bff/app_mvz_patient_profile';
import { MainGroup } from '@tutum/hermes/bff/common';
import { FormAction, PrintResult } from '@tutum/hermes/bff/form_common';
import {
  PrescribeType,
  createFreeText,
  prescribe as prescribeDiga,
  FormInfo as FormInfoDiga,
  CreateFreetextRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { useMutationPrescribeERP } from '@tutum/hermes/bff/legacy/app_mvz_erezept';
import {
  FormInfo,
  FormType,
  MedicineShoppingBagInfo,
  PrescribeRequest,
  PrescribeResponse,
  PrintFormRequest,
  PrintFormResponse,
  ViewMedicationForm,
  prescribe,
  printForm,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { PrescribeRequest as PrescribeDigaRequest } from '@tutum/hermes/bff/legacy/app_mvz_diga';
import {
  getPatientFormProfile,
  useQueryGetPatientFormProfile,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { FormName } from '@tutum/hermes/bff/legacy/form_common';
import i18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { default as datetimeUtil } from '@tutum/infrastructure/utils/datetime.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { FORM_SETTING_OBJECT } from '@tutum/mvz/constant/form';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import FormI18n from '@tutum/mvz/locales/en/Form.json';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import {
  FormElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import {
  printPreviewActions,
  usePrintPreviewStore,
} from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.store';
import {
  medicationActions,
  useMedicationStore,
} from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import { IPrintPreviewForm } from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import PrinterService from '@tutum/mvz/services/printer.service';
import chunk from 'lodash/chunk';
import { useHotkeys } from 'react-hotkeys-hook';
import { getInformationByBSNR } from '../medication/helpers';
import {
  medicationShoppingBagActions,
  useMedicationShoppingBagStore,
} from '../shopping-bag/MedicationShoppingBag.store';
import { EREZEPT, medicationUtil } from '../utils/medication-util';
import AOKBremen from './aok-bremen/AOKBremen.styled';
import AOKNordwet from './aok-nordwet/AOKNordwet.styled';
import BlueRezept from './blue-rezept/blueRezept.styled';
import BtmRezept from './btm-rezept/BtmRezept.styled';
import { ERezept, ERezeptStylesheet } from './e-rezept';
import GreenRezept from './green-rezept/GreenRezept.styled';
import KkRezept from './kk-rezept/KkRezept.styled';
import { buildContent } from './medication-info/MedicationInfo';
import Muster16aBay from './muster-16a-bay/Muster16aBay.styled';
import PrintPreviewSetting from './print-preview-setting/PrintPreviewSetting.styled';
import TRezept from './t-rezept/tRezept.styled';
import { EncounterCase } from '@tutum/hermes/bff/repo_encounter';
import {
  BTM_FORM_MAX_LENGTH,
  getContentByLines,
  K_FORM_MAX_LENGTH,
  G_FORM_MAX_LENGTH,
  Blue_FORM_MAX_LENGTH,
  MUSTER_16A_BAY_MAX_LENGTH,
  MAX_MEDICINES_CONSULTATION_PRESCRIPTION_FORM,
  MIN_LINES_MEDICINE,
  MAX_LINES_MEDICINE,
} from './helper';
import { getPatientUpdatedData } from '@tutum/mvz/module_form/module_form.util';
import { ErezeptRequestType } from '@tutum/hermes/bff/legacy/repo_medicine_common';
import { cloneDeep, isEmpty, isNil } from 'lodash';
import { PSEUDOIK_FORM907 } from '@tutum/mvz/module_medication_kbv/utils/medication.const';
import { eDocumentsActions } from '@tutum/mvz/module_e-documents/EDocuments.store';
import { FormMiniMap } from '@tutum/mvz/components/form-mini-map';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';

const closeIcon = '/images/close.svg';

const IAutIdem = {
  autIdem1: 'checkbox_autIdem1',
  autIdem2: 'checkbox_autIdem2',
  autIdem3: 'checkbox_autIdem3',
};

const privateSvg = '/data/form/Blaues_Rezept.svg';
const BtmSvg = '/data/form/Btm_Rezept.svg';
const GSvg = '/data/form/Gruenes_Rezept.svg';
const KSvg = '/data/form/Muster_16.svg';
const TSvg = '/data/form/T-Rezept-Muster.svg';
const ERezeptSvg = '/data/form/eRezept_PZN.svg';
const AOKNordWetSvg = '/data/form/AOK_Nordwet.svg';
const AOKBremenSvg = '/data/form/AOK_Bremen_impfstoff.svg';
const Muster16aBaySvg = '/data/form/Muster_16a_bay.svg';

export const FormMaxLengthMap: Partial<Record<FormType, number>> = {
  [FormType.KREZ]: K_FORM_MAX_LENGTH,
  [FormType.BTM]: BTM_FORM_MAX_LENGTH,
  [FormType.GREZ]: G_FORM_MAX_LENGTH,
  [FormType.Private]: Blue_FORM_MAX_LENGTH,
  [FormType.AOKBremen]: MUSTER_16A_BAY_MAX_LENGTH,
  [FormType.AOKNordwet]: MUSTER_16A_BAY_MAX_LENGTH,
  [FormType.Muster16aBay]: MUSTER_16A_BAY_MAX_LENGTH,
};

export const FORM_HAS_7_MEDICINES = [
  FormType.AOKBremen,
  FormType.AOKNordwet,
  FormType.Muster16aBay,
];

const imgConst = {
  [FormType.Private]: privateSvg,
  [FormType.BTM]: BtmSvg,
  [FormType.GREZ]: GSvg,
  [FormType.KREZ]: KSvg,
  [FormType.TPrescription]: TSvg,
  [EREZEPT]: ERezeptSvg,
  [FormType.AOKNordwet]: AOKNordWetSvg,
  [FormType.AOKBremen]: AOKBremenSvg,
  [FormType.Muster16aBay]: Muster16aBaySvg,
};

const SCHEIN_FOR_EREZEPT = [MainGroup.KV, MainGroup.FAV, MainGroup.HZV];

const processChunkMedicines = (
  medicines: MedicineShoppingBagInfo[],
  formType: FormType,
  chunkSize: number,
  tMedicine: IFixedNamespaceTFunction
): any[][] => {
  const groups: MedicineShoppingBagInfo[][] = [];

  if (Object.keys(FormMaxLengthMap).includes(formType)) {
    const maxLengthCheck = FormMaxLengthMap[formType] || 0;

    const defaultMaxLinesInForm =
      (FORM_HAS_7_MEDICINES.includes(formType)
        ? MAX_MEDICINES_CONSULTATION_PRESCRIPTION_FORM
        : MAX_LINES_MEDICINE) *
      (MAX_LINES_MEDICINE - 1);
    let maxLinesInform = defaultMaxLinesInForm;
    let currentIndex = 0;

    medicines.forEach((medicine) => {
      let maxLine = getContentByLines(
        buildContent(medicine, tMedicine),
        maxLengthCheck
      ).line;

      if (maxLine < MIN_LINES_MEDICINE) {
        maxLine = MIN_LINES_MEDICINE;
      }

      if (maxLine > maxLinesInform) {
        currentIndex += 1;
        maxLinesInform = defaultMaxLinesInForm;
      }

      if (!groups[currentIndex]?.length) {
        groups[currentIndex] = [];
      }

      if (maxLine > MAX_LINES_MEDICINE) {
        groups[currentIndex].push(medicine);
        currentIndex += 1;
        maxLinesInform = defaultMaxLinesInForm;
      } else if (maxLine <= MAX_LINES_MEDICINE && maxLine <= maxLinesInform) {
        groups[currentIndex].push(medicine);
        maxLinesInform -= maxLine;
      }
    });

    return groups;
  }
  return chunk(medicines, chunkSize);
};

export interface IMedicationPrintPreviewProps {
  className?: string;
  patient?: PatientProfileResponse;
  bsnr?: string;
  clearSearchBox?: () => void;
  medicineType: 'diga' | 'kbv';
  formProfile?: { [key: string]: string };
}

function MedicationPrintPreviewMemo(props: IMedicationPrintPreviewProps) {
  const { patientManagement } = useContext(PatientManagementContext.instance);
  const { selectedContractDoctor } = patientManagement;
  const { className, patient: patientProps, clearSearchBox, bsnr } = props;
  const printPreviewStore = usePrintPreviewStore();
  const medicationContext = useContext(MedicationContext);
  const { viewFormInfo } = medicationContext;
  const patientFileStore = usePatientFileStore();
  const isSVPatient = patientFileStore.isSvPatient;
  const activatedSchein = patientFileStore.schein?.activatedSchein;
  const [currentScheinId, setCurrentScheinId] = useState<string>('');
  const { globalData, useGetDoctorList } = GlobalContext.useContext();
  const [patient, setPatient] = useState<PatientProfileResponse | undefined>(
    patientManagement.patient
  );
  const [hasSupportForm907, setSupportForm907] = useState<boolean>(false);
  const doctorList = useGetDoctorList();
  const { t } = i18n.useTranslation<
    keyof typeof MedicationI18n.MedicationPrintPreview
  >({
    namespace: 'Medication',
    nestedTrans: 'MedicationPrintPreview',
  });

  const { t: tMedicine } = i18n.useTranslation<
    keyof typeof MedicationI18n.RenderAnnotation
  >({
    namespace: 'Medication',
    nestedTrans: 'RenderAnnotation',
  });

  const { t: tPrintPreviewSetting } = i18n.useTranslation<
    keyof typeof MedicationI18n.PrintPreviewSetting
  >({
    namespace: 'Medication',
    nestedTrans: 'PrintPreviewSetting',
  });

  const { t: tForm } = i18n.useTranslation<keyof typeof FormI18n>({
    namespace: 'Form',
  });
  const groupByForm = printPreviewStore.groupForms;

  const [formInfoMap, setFormInfoMap] = useState<{
    [key: string]: string;
  }>({});

  const [formAnnotationSetting, setFormAnnotationSetting] = useState<{
    [formId: string]: FORM_SETTING_OBJECT;
  }>({});

  const [warningN1MedicineSizeCodes, setWarningN1MedicineSizeCodes] = useState<
    NormSizeCodeListItem[]
  >([]);

  const [changedSprechsundenbedarf, setChangedSprechsundenbedarf] = useState<{
    [key: string]: boolean;
  }>({});

  const [prescribeDate, setPrescribeDate] = useState<number | null>(null);

  useEffect(() => {
    if (patientProps) {
      setPatient(patientProps);
    }
    return () => {
      printPreviewActions.setPrintQRCode(false);
    };
  }, [patientProps]);

  const [pureProfile, setPureProfile] = useState<{
    [key: string]: string;
  } | null>(null);

  const onChangeFormSetting = (
    formId: string,
    formSetting: FORM_SETTING_OBJECT
  ) => {
    setFormAnnotationSetting((prevValue) => ({
      ...prevValue,
      [formId]: formSetting,
    }));
  };
  const store = useMedicationStore();
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();

  const onChangePrescribeDate = (
    formType: FormType,
    formId: string,
    selectedDate: number
  ) => {
    const index = (groupByForm[formType] || []).findIndex(
      (item) => item.id === formId
    );
    if (index !== -1) {
      const cloned = cloneDeep(groupByForm);
      cloned[formType][index] = {
        ...cloned[formType][index],
        prescribeDate: selectedDate,
      };
      printPreviewActions.setGroupForms(cloned);
    }
  };

  const { shoppingBag, diga } = useMedicationShoppingBagStore();
  const digaInfo = diga?.digaInfo;

  const treatmentDoctorId =
    medicationContext.viewFormInfo?.treatmentDoctorId ||
    shoppingBag?.treatmentDoctorId;
  const assignedToBsnrId =
    medicationContext.viewFormInfo?.assignedToBsnrId ||
    shoppingBag?.assignedToBsnrId ||
    globalContext.getBsnrIdByDoctorId(treatmentDoctorId || '');
  const doctorStamp = (
    (bsnr ? doctorList : selectedContractDoctor?.availableDoctor) || []
  ).find((doctor) => doctor.id === treatmentDoctorId);

  const onSetEditingMedicine = (medicine: MedicineShoppingBagInfo) => {
    medicationContext.setEditingMedication(medicine);
  };

  const scheinId = useMemo(() => {
    if (!!viewFormInfo) {
      return viewFormInfo.scheinId;
    }
    return activatedSchein?.scheinId;
  }, [activatedSchein?.scheinId, viewFormInfo?.scheinId]);

  const { data, isSuccess } = useQueryGetPatientFormProfile(
    {
      patientID: patient?.id as string,
      doctorID: treatmentDoctorId as string,
      scheinId: scheinId,
      bsnrId: assignedToBsnrId,
    },
    {
      enabled: !!(
        selectedContractDoctor.doctorId &&
        patient?.id &&
        treatmentDoctorId &&
        !isNullUUID(treatmentDoctorId)
      ),
    }
  );
  useEffect(() => {
    const flatted = printPreviewActions.flattenGroupByForm(groupByForm);
    if (flatted.length && flatted.every((item) => item.hasSaveOrPrescribe)) {
      return medicationContext.setShowPrintPreviewState(false);
    }
  }, [groupByForm]);

  useEffect(() => {
    setCurrentScheinId(digaInfo?.scheinId || '');
    setPrescribeDate(digaInfo?.formInfo?.prescribeDate || null);
  }, [digaInfo]);

  useEffect(() => {
    if (data && isSuccess) {
      setFormInfoMap((prevState) => ({
        ...prevState,
        ...data.profile,
      }));
      setPureProfile(data.profile);
    }
  }, [isSuccess, data]);

  // pass form profile directly
  useEffect(() => {
    if (props.formProfile) {
      setFormInfoMap((prevState) => ({
        ...prevState,
        ...props.formProfile,
      }));
      setPureProfile(props.formProfile);
    }
  }, [props.formProfile]);

  useEffect(() => {
    if (store.isStatistics) {
      const value = getInformationByBSNR(bsnr || '');
      const prescribeDateValue = datetimeUtil.date();
      const quarter = datetimeUtil.getQuarter(prescribeDateValue);
      const year = datetimeUtil.getYear(prescribeDateValue, false);
      const formInfoMap = {
        label_insurance_name: value.insuranceName,
        label_patientInfo_line1: tPrintPreviewSetting('sprechsundenbedarf'),
        label_patientInfo_line2: tPrintPreviewSetting('quarter', {
          quarter,
          year,
        }),
        label_patientInfo_line3: '',
        label_patientInfo_line4: '',
        label_date_of_birth: '',
        label_insurance_number: '',
        label_insurance_status: '',
        label_ik_number: String(value.ikNumber),
        label_bsnr: bsnr || '',
        label_lanr: currentLoggedinUser?.lanr || '',
      };
      setFormInfoMap(formInfoMap);
    }
  }, [bsnr, currentLoggedinUser, store.isStatistics]);

  useEffect(() => {
    if (!shoppingBag?.medicines?.length) {
      return;
    }
    const scheinMainGroup = activatedSchein?.scheinMainGroup;
    const groupByForm = medicationUtil.groupMedicineByForm(
      shoppingBag.medicines
    );

    const groupByFormWithChunk = {};
    Object.keys(groupByForm).forEach((formType) => {
      const medicines: MedicineShoppingBagInfo[] = [];
      const ePrescriptionMedicines: MedicineShoppingBagInfo[][] = [];
      const asNeedMedicines: MedicineShoppingBagInfo[][] = [];
      const medicinesHasDiga: MedicineShoppingBagInfo[][] = [];
      //Chunk with size 1 when implement T-Rezept or E-prescription or asNeeded or isDiagaFlag checked
      groupByForm[formType].forEach((medicine) => {
        switch (true) {
          case !!medicine.isEPrescription &&
            formType !== FormType.BTM &&
            SCHEIN_FOR_EREZEPT.includes(scheinMainGroup as MainGroup):
            ePrescriptionMedicines.push([medicine]);
            break;
          case !!medicine.asNeeded:
          case !!medicine.substitutionPrescription:
            asNeedMedicines.push([medicine]);
            break;
          case medicine.productInformation?.isDigaFlag:
            medicinesHasDiga.push([medicine]);
            break;
          default:
            medicines.push(medicine);
            break;
        }
      });

      let chunkSize = 3;

      if (formType === FormType.TPrescription) {
        chunkSize = 1;
      }

      const medicinePreChunk = processChunkMedicines(
        medicines,
        FormType[formType],
        chunkSize,
        tMedicine
      );

      const medicineChunk = medicinePreChunk.concat(
        ePrescriptionMedicines,
        asNeedMedicines,
        medicinesHasDiga
      );

      const formInfo = store.refill.formInfo;

      const formChunk = medicineChunk.map((medicinesInForm) => {
        const obj = {
          id: medicinesInForm.map((item) => item.id).join(','),
          prescribeDate: formInfo?.hasChangedSprechsundenbedarf
            ? formInfo.prescribeDate
            : datetimeUtil.now(),
          medicines: medicinesInForm,
          ePrescription: {},
        };

        return obj;
      });

      groupByFormWithChunk[formType] = formChunk;
    });
    printPreviewActions.setGroupForms(groupByFormWithChunk);

    const flattenGroupByForm =
      printPreviewActions.flattenGroupByForm(groupByFormWithChunk);
    if (flattenGroupByForm && flattenGroupByForm.length > 0) {
      const groupMedicines = flattenGroupByForm[0];
      printPreviewActions.setFormViewId(groupMedicines.id);
      printPreviewActions.setCurrentFormName(
        groupMedicines.medicines[0].currentFormType
      );
    }
  }, [
    JSON.stringify(shoppingBag),
    globalData?.userProfile,
    store.refill.formInfo,
  ]);

  useEffect(() => {
    if (medicationContext.viewFormInfo) {
      printPreviewActions.setFormViewId(
        medicationContext.viewFormInfo?.formInfo?.id
      );
      printPreviewActions.setCurrentFormName(
        medicationContext.viewFormInfo?.formInfo?.currentFormType
      );
    }
  }, [medicationContext.viewFormInfo]);

  useEffect(() => {
    const flatted = printPreviewActions.flattenGroupByForm(groupByForm);
    const groupMedicines = flatted?.find(
      (item) => item.id === printPreviewStore.formViewId
    );

    const firstMedicine = groupMedicines?.medicines?.[0];
    const isAsNeeded =
      firstMedicine?.asNeeded || firstMedicine?.substitutionPrescription;
    const isHasTssCode = activatedSchein?.tsvgTranferCode;
    const formType = medicationUtil.getFormName(firstMedicine);
    const isKREZ = [FormType.KREZ].includes(formType as FormType);
    const isERezept = formType === (EREZEPT as FormType);

    const tempLabelInsStatus = pureProfile?.label_insurance_status?.split('');

    if (!tempLabelInsStatus?.[0]) return;

    if (isAsNeeded) {
      tempLabelInsStatus[5] = '1';
    }

    if ((isKREZ || isERezept) && isHasTssCode) {
      tempLabelInsStatus[6] = '7';
    }
    setFormInfoMap((prev) => ({
      ...prev,
      label_insurance_status: tempLabelInsStatus?.join(''),
    }));
  }, [printPreviewStore.formViewId, groupByForm, activatedSchein, pureProfile]);

  const onClosePrintPreview = () => {
    medicationShoppingBagActions.getShoppingBag(bsnr);
    medicationShoppingBagActions.clearDigaInfo();
    printPreviewActions.setFormViewId(null);
    printPreviewActions.setCurrentFormName('');
    medicationActions.resetPackageSizeStore();
    medicationContext.setInRefillProcess(false);
    setChangedSprechsundenbedarf({});
    setPrescribeDate(null);
    return medicationContext.setShowPrintPreviewState(false);
  };

  const getAutIdemMedicineIds = (
    medicines: MedicineShoppingBagInfo[],
    formSetting: FORM_SETTING_OBJECT
  ) => {
    const medicineIds: string[] = [];
    if (formSetting) {
      if (formSetting[IAutIdem.autIdem1] && medicines[0]?.id) {
        medicineIds.push(medicines[0].id);
      }
      if (formSetting[IAutIdem.autIdem2] && medicines[1]?.id) {
        medicineIds.push(medicines[1].id);
      }
      if (formSetting[IAutIdem.autIdem3] && medicines[2]?.id) {
        medicineIds.push(medicines[2].id);
      }
    }
    return medicineIds;
  };

  const handlePrintFormV2 = async (printResult: PrintResult) => {
    if (!printResult) {
      throw new Error('Empty print result');
    }

    const printSuccess = async () => {
      alertSuccessfully(tForm('formPrinted'));
    };
    const printFailure = () => { };

    const prescribeCallback = async () => printResult.formUrl;
    await PrinterService.initAndPrint(printResult.formName, prescribeCallback, {
      printSuccess,
      printFailure,
    });
  };

  const handlePrintForm = async (
    response: PrescribeResponse | PrintFormResponse
  ) => {
    if (response && response.printResults?.length) {
      const printSuccess = async () => {
        alertSuccessfully(tForm('formPrinted'));
      };
      const printFailure = () => { };

      for (const item of response.printResults) {
        const formFileName = PrinterService.getMedicationFormFileName(
          item.currentFormType
        );
        const prescribeCallback = async () => {
          return item.formUrl;
        };
        await PrinterService.initAndPrint(formFileName, prescribeCallback, {
          printSuccess,
          printFailure,
        });
      }
    }
  };

  const { mutateAsync: prescribeERP } = useMutationPrescribeERP();

  const handleERezept = async ({
    formsInfo,
    erezeptRequestType,
    scheinId,
    hasSupportForm907 = false,
  }: {
    formsInfo: FormInfo[];
    erezeptRequestType: ErezeptRequestType;
    scheinId: string;
    hasSupportForm907?: boolean;
  }): Promise<FormInfo[]> => {
    const formsInfoERezept = formsInfo.filter((formInfo) =>
      Boolean(formInfo['isEPrescription'])
    );

    if (formsInfoERezept.length) {
      const response = await prescribeERP({
        doctorId: (selectedContractDoctor.doctorId ||
          globalData.userProfile?.id) as string,
        patientId: patient?.id as string,
        treatmentDoctorId: globalData.userProfile?.id as string,
        formInfos: formsInfoERezept,
        erezeptRequestType,
        hasSupportForm907,
        scheinId,
      });

      if (printPreviewStore.isPrintQRCode) {
        await eDocumentsActions.print({
          formId: FormName.Muster_eRezept,
          files: response.data.eRezeptFormUrl,
        });
      }
    }

    return formsInfo;
  };

  const cleanUpFormSetting = (
    formSetting: FORM_SETTING_OBJECT
  ): FORM_SETTING_OBJECT => {
    const result: FORM_SETTING_OBJECT = { ...formSetting };
    for (const key in formSetting) {
      const value = formSetting[key];
      if (value instanceof String) {
        result[key] = value.trim();
      }
    }

    return result;
  };

  //In case click prescribe all form
  const onPrescribeMedicationForm = async (printDate?: number) => {
    try {
      printPreviewActions.setIsLoadingPrescribe(true);
      if (!store.isStatistics && !activatedSchein?.scheinId) {
        printPreviewActions.setIsLoadingPrescribe(false);
        return;
      }
      let autIdemMedicineIds: string[] = [];
      let isERezept = false;
      const formInfors: FormInfo[] = await Promise.all(
        flatten(
          Object.keys(FormType).map((formType) =>
            (groupByForm[formType] || []).map(async (formInfo) => {
              if (formInfo.hasSaveOrPrescribe) {
                return null;
              }
              let formSettingMapped: FORM_SETTING_OBJECT = {
                ...formAnnotationSetting[formInfo.id],
                ...formInfoMap,
                label_bsnr:
                  formAnnotationSetting[formInfo.id]?.label_bsnr ||
                  formInfoMap?.label_bsnr ||
                  '',
                label_insurance_status:
                  formAnnotationSetting[formInfo.id]?.label_insurance_status ||
                  formInfoMap.label_insurance_status,
              };

              if (
                [FormType.KREZ, FormType.BTM].includes(formType as FormType) &&
                changedSprechsundenbedarf[formType]
              ) {
                formSettingMapped = {
                  ...formAnnotationSetting[formInfo.id],
                  label_doctor_stamp:
                    doctorStamp?.doctorStamp ||
                    doctorStamp?.bsnrPracticeStamp ||
                    '',
                };
              }

              const formSetting = JSON.stringify(
                cleanUpFormSetting(formSettingMapped) || {}
              );
              const medicineIDs = formInfo.medicines.map((med) => med.id);
              const currentFormType = formType;
              if (
                currentFormType === FormType.KREZ ||
                currentFormType === FormType.Private ||
                currentFormType === FormType.BTM
              ) {
                const autIdemIds = getAutIdemMedicineIds(
                  formInfo.medicines,
                  formSettingMapped
                );
                autIdemMedicineIds = [...autIdemMedicineIds, ...autIdemIds];
              }
              const mapFormType =
                PrinterService.getMedicationFormFileName(currentFormType);
              const isEPrescription = formInfo.medicines[0].isEPrescription;
              if (isEPrescription) {
                isERezept = true;
              }
              const pdfWithBackGround =
                !isEPrescription &&
                (await PrinterService.getPrinterSetting(mapFormType));

              return {
                formSetting,
                medicineIDs,
                currentFormType,
                hasChangedSprechsundenbedarf:
                  changedSprechsundenbedarf[currentFormType] || false,
                prescribeDate: formInfo.prescribeDate,
                printDate,
                ePrescription: {
                  ...formInfo.ePrescription,
                  isPrintQRCode: printPreviewStore.isPrintQRCode,
                },
                printOption: isEPrescription
                  ? null
                  : {
                    dateOfPrint: printDate,
                    pdfWithBackground: pdfWithBackGround,
                    formAction: FormAction.FormAction_PrintFull,
                  },
                isEPrescription,
              };
            })
          )
        )
      );
      const noPrinterProfile = formInfors.some(
        (form) =>
          form &&
          form.printDate &&
          form.printOption &&
          form.printOption.pdfWithBackground === undefined
      );
      if (noPrinterProfile) {
        return;
      }

      const isPrintFlow =
        !isERezept && formInfors.some((form) => form && form.printDate);

      if (isPrintFlow) {
        await PrinterService.getPrinterHost();
      }

      // Create bundle
      const formInfosData = await handleERezept({
        formsInfo: formInfors.filter((item) => item),
        erezeptRequestType: ErezeptRequestType.ErezeptRequestType_prescribe_all,
        scheinId: activatedSchein?.scheinId as string,
        hasSupportForm907,
      });

      const payload: PrescribeRequest = {
        doctorId: selectedContractDoctor.doctorId,
        patientId: patient?.id,
        contractId: selectedContractDoctor.contractId,
        treatmentDoctorId: shoppingBag?.treatmentDoctorId,
        formInfos: formInfosData,
        bsnr,
        encounterCase: selectedContractDoctor.encounterCase,
        medicineAutIdemIds: autIdemMedicineIds,
        scheinId: activatedSchein?.scheinId,
        hasSupportForm907,
      };

      const response = await prescribe(payload).then((response) => {
        let message = t('rezeptSaved');
        if (Object.keys(formAnnotationSetting || []).length > 1) {
          message = t('rezeptsSaved');
        }

        if (isERezept) {
          message = t('ERezeptsSent');
        }

        alertSuccessfully(message);

        medicationContext.setShowPrintPreviewState(false);
        return response;
      });
      await handlePrintForm(response?.data);
    } finally {
      printPreviewActions.setIsLoadingPrescribe(false);
    }
  };

  const print = async (viewFormInfo: ViewMedicationForm, printDate: number) => {
    const mapFormType = PrinterService.getMedicationFormFileName(
      viewFormInfo.formInfo.currentFormType
    );

    const pdfWithBackGround =
      await PrinterService.getPrinterSetting(mapFormType);

    if (isNil(pdfWithBackGround)) {
      return false;
    }

    const request: PrintFormRequest = {
      formId: viewFormInfo.formInfo.id,
      printOption: {
        dateOfPrint: printDate,
        pdfWithBackground: pdfWithBackGround,
        formAction: FormAction.FormAction_PrintFull,
      },
      hasSupportForm907,
      patientId: (!bsnr ? patient?.id : null) as string,
    };
    const { data: response } = await printForm(request)
      .then((response) => {
        return response;
      })
      .finally(() => {
        printPreviewActions.setIsLoadingPrescribe(false);
      });

    await handlePrintForm(response);
  };

  // In case click prescribe only one form
  const onPrescribeSingleMedicationForm = async (printDate?: number) => {
    printPreviewActions.setIsLoadingPrescribe(true);
    const { viewFormInfo } = medicationContext;
    try {
      let pdfWithBackGround;
      // handle for diga case
      if (diga && digaInfo) {
        if (!!printDate) {
          await PrinterService.getPrinterHost();
          pdfWithBackGround = await PrinterService.getPrinterSetting(
            diga.formName
          );
          if (pdfWithBackGround === undefined) {
            return;
          }
        }
        const patientUpdatedData =
          activatedSchein?.scheinId !== digaInfo.scheinId
            ? getPatientUpdatedData(formInfoMap)
            : formInfoMap;
        const formSetting = digaInfo.id
          ? {
            ...JSON.parse(digaInfo.formInfo?.formSetting || '{}'),
            ...formAnnotationSetting['customFormSetting'],
            ...patientUpdatedData,
          }
          : {
            ...formAnnotationSetting['customFormSetting'],
            ...(changedSprechsundenbedarf[FormType.KREZ] ? {} : formInfoMap),
            label_bsnr:
              formAnnotationSetting['customFormSetting']?.label_bsnr ||
              formInfoMap?.label_bsnr ||
              '',
            label_insurance_status:
              formAnnotationSetting['customFormSetting']
                .label_insurance_status || formInfoMap.label_insurance_status,
          };
        const formInfo: FormInfoDiga = {
          formSetting: JSON.stringify(formSetting),
          formName: diga.formName,
          prescribeDate: prescribeDate as number,
          isShowFavHint: false,
          printDate: printDate || 0,
          printOption: {
            dateOfPrint: printDate || 0,
            pdfWithBackground: pdfWithBackGround,
            formAction: FormAction.FormAction_PrintFull,
          },
        };
        let printResult: PrintResult;
        const prescribeId = viewFormInfo ? digaInfo.id : null;
        if (diga.type === PrescribeType.PRESCRIBED) {
          const resp = await prescribeDiga({
            id: prescribeId,
            digaId: digaInfo.digaId,
            patientId: digaInfo.patientId || patient?.id,
            doctorId: digaInfo.doctorId || shoppingBag?.treatmentDoctorId, // value of treatment doctor and bsnr select
            assignedToBsnrId:
              digaInfo.assignedToBsnrId || shoppingBag?.assignedToBsnrId, // value of treatment doctor and bsnr select
            note: '',
            formInfo: formInfo,
            scheinId: digaInfo.scheinId || activatedSchein?.scheinId,
            contractId:
              digaInfo.contractId || selectedContractDoctor.contractId,
            encounterCase:
              digaInfo.encounterCase || selectedContractDoctor.encounterCase,
          } as PrescribeDigaRequest);
          printResult = resp.data?.printResult;
        } else {
          const resp = await createFreeText({
            id: prescribeId,
            pzn: diga.digaInfo.digaPzn,
            name: diga.digaInfo.digaName,
            patientId: digaInfo?.patientId ?? patient?.id,
            doctorId: digaInfo?.doctorId ?? selectedContractDoctor.doctorId,
            scheinId: digaInfo?.scheinId ?? activatedSchein?.scheinId,
            encounterCase:
              (digaInfo.encounterCase as EncounterCase) ??
              selectedContractDoctor.encounterCase,
            note: '',
            contractId:
              digaInfo.contractId ?? selectedContractDoctor.contractId,
            formInfo: formInfo,
          } as CreateFreetextRequest);
          printResult = resp.data?.printResult;
        }
        if (!printDate) {
          alertSuccessfully(tForm('Prescrible.FormSaved' as any));
          onClosePrintPreview();
          return patientFileActions.setActiveTabId(ID_TABS.TIMELINE);
        }
        if (!prescribeId) {
          alertSuccessfully(tPrintPreviewSetting('prescribedDiga'));
          handlePrintFormV2(printResult);
        } else {
          // await close print preview when printing successfully
          await handlePrintFormV2(printResult);
        }
        onClosePrintPreview();
        return;
      }

      if (viewFormInfo) {
        if (viewFormInfo.formInfo.formInfoResponse[0].isEPrescription) {
          const medicineInfo = viewFormInfo.formInfo.formInfoResponse[0];
          await handleERezept({
            erezeptRequestType: ErezeptRequestType.ErezeptRequestType_prescribe,
            formsInfo: [
              {
                ...medicineInfo,
                currentFormType: medicineInfo.currentFormType,
                ePrescription: viewFormInfo.formInfo.ePrescription,
                formSetting: viewFormInfo.formInfo.formSetting,
                isShowFavHint: viewFormInfo.formInfo.isShowFavHint,
                medicineIDs: [medicineInfo.id as string],
                prescribeDate: printDate,
              },
            ],
            scheinId: viewFormInfo.scheinId as string,
            hasSupportForm907,
          });
        } else {
          await print(viewFormInfo, printDate as number);
        }
        medicationContext.setShowPrintPreviewState(false);

        return;
      }

      if (!store.isStatistics && !activatedSchein?.scheinId) {
        printPreviewActions.setIsLoadingPrescribe(false);
        return;
      }

      let autIdemMedicineIds: string[] = [];
      let isERezept = false;
      const singleFormInfo: FormInfo[] = await Promise.all(
        flatten(
          Object.keys(FormType).map((formType) =>
            (groupByForm[formType] || []).map(async (formInfo) => {
              if (
                formInfo.id === printPreviewStore.formViewId ||
                !!viewFormInfo
              ) {
                let formSettingMapped: FORM_SETTING_OBJECT = {
                  ...formAnnotationSetting[formInfo.id],
                  ...formInfoMap,
                  // mapping data for case change bsnr
                  label_bsnr:
                    formAnnotationSetting[formInfo.id]?.label_bsnr ||
                    formInfoMap?.label_bsnr ||
                    '',
                  label_insurance_status:
                    formAnnotationSetting[formInfo.id].label_insurance_status ||
                    formInfoMap.label_insurance_status,
                };

                if (
                  [FormType.KREZ, FormType.BTM].includes(
                    formType as FormType
                  ) &&
                  changedSprechsundenbedarf[formType]
                ) {
                  formSettingMapped = {
                    ...formAnnotationSetting[formInfo.id],
                    label_doctor_stamp:
                      doctorStamp?.doctorStamp ||
                      doctorStamp?.bsnrPracticeStamp ||
                      '',
                  };
                }
                const formSetting = JSON.stringify(
                  cleanUpFormSetting(formSettingMapped) || {}
                );
                const medicineIDs = formInfo.medicines.map((med) => med.id);
                const currentFormType = formType;
                if (
                  currentFormType === FormType.KREZ ||
                  currentFormType === FormType.Private ||
                  currentFormType === FormType.BTM
                ) {
                  const autIdemIds = getAutIdemMedicineIds(
                    formInfo.medicines,
                    formSettingMapped
                  );
                  autIdemMedicineIds = [...autIdemMedicineIds, ...autIdemIds];
                }

                const formInfoItem = {
                  formSetting,
                  medicineIDs,
                  currentFormType,
                  hasChangedSprechsundenbedarf:
                    changedSprechsundenbedarf[currentFormType] || false,
                  prescribeDate: formInfo.prescribeDate,
                  printDate: printDate,
                  ePrescription: {
                    ...formInfo.ePrescription,
                    isPrintQRCode: printPreviewStore.isPrintQRCode,
                  },
                } as FormInfo;

                // Create bundle when save or prescribe
                if (formInfo.medicines[0].isEPrescription) {
                  isERezept = true;
                  formInfoItem['isEPrescription'] =
                    formInfo.medicines[0].isEPrescription;
                  await handleERezept({
                    formsInfo: [formInfoItem],
                    erezeptRequestType: printDate
                      ? ErezeptRequestType.ErezeptRequestType_prescribe
                      : ErezeptRequestType.ErezeptRequestType_save,
                    scheinId: activatedSchein?.scheinId as string,
                    hasSupportForm907,
                  });
                } else {
                  if (printDate) {
                    const mapFormType =
                      PrinterService.getMedicationFormFileName(currentFormType);
                    const pdfWithBackGround =
                      await PrinterService.getPrinterSetting(mapFormType);
                    formInfoItem['printOption'] = {
                      dateOfPrint: printDate,
                      pdfWithBackground: !!pdfWithBackGround,
                      formAction: FormAction.FormAction_PrintFull,
                    };
                  }
                }

                return formInfoItem;
              } else {
                return null;
              }
            })
          )
        )
      );

      const singleFormInfoData = singleFormInfo.filter((item) => item);
      const noPrinterProfile =
        !isERezept &&
        singleFormInfoData.some(
          (form) =>
            form.printDate && form.printOption?.pdfWithBackground === undefined
        );

      if (noPrinterProfile) {
        return;
      }

      const isPrintFlow =
        !isERezept && singleFormInfoData.some((form) => form.printDate);

      if (isPrintFlow) {
        await PrinterService.getPrinterHost();
      }

      const request: PrescribeRequest = {
        doctorId: selectedContractDoctor.doctorId,
        patientId: patient?.id,
        contractId: selectedContractDoctor.contractId,
        treatmentDoctorId: shoppingBag?.treatmentDoctorId,
        assignedToBsnrId: shoppingBag?.assignedToBsnrId,
        bsnr,
        formInfos: singleFormInfoData,
        encounterCase: selectedContractDoctor.encounterCase,
        medicineAutIdemIds: autIdemMedicineIds,
        scheinId: activatedSchein?.scheinId,
        hasSupportForm907,
      };

      const response = await prescribe(request).then((response) => {
        if (!printDate) {
          const message = isERezept ? t('ERezeptStore') : t('1rezeptsSaved');

          alertSuccessfully(message);
        }

        printPreviewActions.updateFormSaveOrPrescribe(bsnr);
        return response;
      });

      await handlePrintForm(response?.data);
    } finally {
      printPreviewActions.setIsLoadingPrescribe(false);
    }
  };

  const renderFormMiniMap = () => {
    if (diga) {
      return null;
    }

    const { viewFormInfo } = medicationContext;
    if (!viewFormInfo) {
      const flattenGroupByForm =
        printPreviewActions.flattenGroupByForm(groupByForm);
      return (
        flattenGroupByForm.length > 1 && (
          <div className="form-tree-wrapper">
            {flattenGroupByForm.map((groupMedicines, index) => {
              const formName = medicationUtil.getFormName(
                groupMedicines.medicines[0]
              );
              return (
                <FormMiniMap
                  key={groupMedicines.id}
                  selected={printPreviewStore.formViewId === groupMedicines.id}
                  onClick={() => {
                    printPreviewActions.setFormViewId(groupMedicines.id);
                    printPreviewActions.setCurrentFormName(
                      groupMedicines.medicines[0].currentFormType
                    );
                  }}
                  completed={groupMedicines.hasSaveOrPrescribe}
                  formNumber={index + 1}
                  source={imgConst[formName || '']}
                />
              );
            })}
          </div>
        )
      );
    } else {
      const { currentFormType, formInfoResponse } = viewFormInfo.formInfo;
      const source =
        imgConst[
        formInfoResponse?.[0]?.['erezeptItemStatus'] ||
          formInfoResponse?.[0]?.isEPrescription
          ? EREZEPT
          : currentFormType
        ];
      return (
        <div className="form-tree-wrapper">
          <FormMiniMap
            selected={true}
            completed={true}
            formNumber={1}
            source={source}
            onClick={() => { }}
          />
        </div>
      );
    }
  };

  const checkSupportForm907 = async (selectedSchein) => {
    if (!selectedSchein) {
      setSupportForm907(false);
      return;
    }

    const isSupported = await webWorkerServices.doesContractSupportFunctions(
      ['FORM907', 'FORM513'],
      selectedSchein?.hzvContractId,
      selectedSchein?.chargeSystemId
    );

    setSupportForm907(isSupported);
  };

  const renderPrintPreview = useMemo(() => {
    const commonFormProps = {
      formInfoMap,
      doctorId: treatmentDoctorId,
      doctorStamp,
      patient,
      bsnr,
      currentSchein: activatedSchein,
      isViewOnly:
        !!diga &&
        !!digaInfo &&
        !!digaInfo.id &&
        (!!digaInfo.formInfo?.printDate || !!digaInfo.printedDate),
      onChangePrescribeDate,
      onChangeFormSetting,
      setEditingMedicine: onSetEditingMedicine,
      setFormInfoMap,
    };

    if (diga && digaInfo) {
      const patientUpdatedData =
        activatedSchein?.scheinId !== digaInfo.scheinId
          ? getPatientUpdatedData(formInfoMap)
          : formInfoMap;
      const formInfoMapDiga = digaInfo.id
        ? {
          ...JSON.parse(digaInfo.formInfo?.formSetting || '{}'),
          ...patientUpdatedData,
        }
        : formInfoMap;

      commonFormProps.onChangePrescribeDate = (
        formTyppe: FormType,
        formId: string,
        selectedDate: number
      ) => {
        setPrescribeDate(selectedDate);
      };

      return (
        <Flex auto column className="sl-form-container">
          {(() => {
            switch (diga.formName) {
              case FormName.KREZ:
                return (
                  <KkRezept
                    key={digaInfo.id}
                    className="sl-krezept-form"
                    digaInfo={digaInfo}
                    changedSprechsundenbedarf={changedSprechsundenbedarf}
                    setChangedSprechsundenbedarf={setChangedSprechsundenbedarf}
                    initFormSetting={
                      digaInfo.formInfo?.formSetting
                        ? JSON.parse(digaInfo.formInfo.formSetting)
                        : null
                    }
                    hasSupportForm907={hasSupportForm907}
                    isStatistics={store.isStatistics}
                    isShow
                    medicineFormInfo={
                      {
                        prescribeDate,
                      } as IPrintPreviewForm
                    }
                    {...commonFormProps}
                    formInfoMap={formInfoMapDiga}
                  />
                );
              case FormName.GREZ: {
                return (
                  <GreenRezept
                    key={digaInfo.id}
                    className="sl-grezept-form"
                    digaInfo={digaInfo}
                    isShow
                    initFormSetting={
                      digaInfo.formInfo?.formSetting
                        ? JSON.parse(digaInfo.formInfo.formSetting)
                        : null
                    }
                    medicineFormInfo={
                      {
                        prescribeDate,
                      } as IPrintPreviewForm
                    }
                    {...commonFormProps}
                    formInfoMap={formInfoMapDiga}
                  />
                );
              }
              case FormName.Private: {
                return (
                  <BlueRezept
                    key={digaInfo.id}
                    className="sl-blue-form"
                    digaInfo={digaInfo}
                    isShow
                    initFormSetting={
                      digaInfo.formInfo?.formSetting
                        ? JSON.parse(digaInfo.formInfo.formSetting)
                        : null
                    }
                    medicineFormInfo={
                      {
                        prescribeDate,
                      } as IPrintPreviewForm
                    }
                    {...commonFormProps}
                    formInfoMap={formInfoMapDiga}
                  />
                );
              }
              default:
                return undefined;
            }
          })()}
        </Flex>
      );
    }

    if (medicationContext.viewFormInfo) {
      return null;
    }
    if (isEmpty(formInfoMap)) {
      return <LoadingState />;
    }

    const flattenGroupByForm =
      printPreviewActions.flattenGroupByForm(groupByForm);

    return (
      <Flex auto column className="sl-form-container">
        {flattenGroupByForm.map((groupMedicines) => {
          if (
            groupMedicines.medicines[0]?.isEPrescription ||
            groupMedicines?.medicines?.[0]?.erezeptItemStatus
          ) {
            return printPreviewStore.formViewId === groupMedicines.id ? (
              <ERezept
                key={groupMedicines.id}
                medicineFormInfo={groupMedicines}
                hasSupportForm907={hasSupportForm907}
                {...commonFormProps}
              />
            ) : null;
          }
          switch (groupMedicines.medicines[0].currentFormType) {
            case FormType.KREZ: {
              return (
                <KkRezept
                  key={groupMedicines.id}
                  className="sl-krezept-form"
                  medicineFormInfo={groupMedicines}
                  changedSprechsundenbedarf={changedSprechsundenbedarf}
                  setChangedSprechsundenbedarf={setChangedSprechsundenbedarf}
                  initFormSetting={
                    store.refill.formInfo?.hasChangedSprechsundenbedarf
                      ? formAnnotationSetting[FormType.KREZ]
                      : store.isStatistics
                        ? {
                          ...formInfoMap,
                          toggle_8_impfstoff:
                            !!groupMedicines.medicines[0].vaccinate,
                          toggle_9_bedarf: true,
                        }
                        : undefined
                  }
                  hasSupportForm907={hasSupportForm907}
                  isStatistics={store.isStatistics}
                  {...commonFormProps}
                />
              );
            }
            case FormType.GREZ: {
              return (
                <GreenRezept
                  key={groupMedicines.id}
                  className="sl-grezept-form"
                  medicineFormInfo={groupMedicines}
                  {...commonFormProps}
                />
              );
            }
            case FormType.BTM: {
              return (
                <BtmRezept
                  key={groupMedicines.id}
                  className="sl-btm-form"
                  medicineFormInfo={groupMedicines}
                  changedSprechsundenbedarf={changedSprechsundenbedarf}
                  setChangedSprechsundenbedarf={setChangedSprechsundenbedarf}
                  initFormSetting={
                    store.refill.formInfo?.hasChangedSprechsundenbedarf
                      ? formAnnotationSetting[FormType.BTM]
                      : store.isStatistics
                        ? {
                          ...formInfoMap,
                          toggle_9_bedarf: true,
                        }
                        : undefined
                  }
                  isStatistics={store.isStatistics}
                  hasSupportForm907={hasSupportForm907}
                  {...commonFormProps}
                />
              );
            }
            case FormType.TPrescription: {
              return (
                <TRezept
                  key={groupMedicines.id}
                  medicineFormInfo={groupMedicines}
                  {...commonFormProps}
                />
              );
            }
            case FormType.Private: {
              return (
                <BlueRezept
                  key={groupMedicines.id}
                  className="sl-blue-form"
                  medicineFormInfo={groupMedicines}
                  {...commonFormProps}
                />
              );
            }
            case FormType.AOKNordwet: {
              return (
                <AOKNordwet
                  key={groupMedicines.id}
                  medicineFormInfo={groupMedicines}
                  prescribeDate={groupMedicines.prescribeDate}
                  changedSprechsundenbedarf={changedSprechsundenbedarf}
                  setChangedSprechsundenbedarf={setChangedSprechsundenbedarf}
                  initFormSetting={
                    store.refill.formInfo?.hasChangedSprechsundenbedarf
                      ? formAnnotationSetting[FormType.AOKNordwet]
                      : store.isStatistics
                        ? {
                          ...formInfoMap,
                          toggle_8_impfstoff:
                            !!groupMedicines.medicines[0].vaccinate,
                          toggle_9_bedarf: true,
                        }
                        : undefined
                  }
                  {...commonFormProps}
                />
              );
            }
            case FormType.AOKBremen: {
              return (
                <AOKBremen
                  key={groupMedicines.id}
                  medicineFormInfo={groupMedicines}
                  prescribeDate={groupMedicines.prescribeDate}
                  changedSprechsundenbedarf={changedSprechsundenbedarf}
                  setChangedSprechsundenbedarf={setChangedSprechsundenbedarf}
                  initFormSetting={
                    store.refill.formInfo?.hasChangedSprechsundenbedarf
                      ? formAnnotationSetting[FormType.AOKBremen]
                      : store.isStatistics
                        ? {
                          ...formInfoMap,
                          toggle_8_impfstoff:
                            !!groupMedicines.medicines[0].vaccinate,
                          toggle_9_bedarf: true,
                        }
                        : undefined
                  }
                  {...commonFormProps}
                />
              );
            }
            case FormType.Muster16aBay: {
              return (
                <Muster16aBay
                  key={groupMedicines.id}
                  medicineFormInfo={groupMedicines}
                  prescribeDate={groupMedicines.prescribeDate}
                  changedSprechsundenbedarf={changedSprechsundenbedarf}
                  setChangedSprechsundenbedarf={setChangedSprechsundenbedarf}
                  initFormSetting={
                    store.refill.formInfo?.hasChangedSprechsundenbedarf
                      ? formAnnotationSetting[FormType.Muster16aBay]
                      : store.isStatistics
                        ? {
                          ...formInfoMap,
                          toggle_8_impfstoff:
                            !!groupMedicines.medicines[0].vaccinate,
                        }
                        : undefined
                  }
                  {...commonFormProps}
                />
              );
            }
          }
        })}
      </Flex>
    );
  }, [
    activatedSchein?.scheinId,
    JSON.stringify(groupByForm),
    JSON.stringify(selectedContractDoctor),
    JSON.stringify(shoppingBag),
    medicationContext.viewFormInfo,
    JSON.stringify(formInfoMap),
    printPreviewStore.formViewId,
    printPreviewStore.isPrintQRCode,
    changedSprechsundenbedarf,
    JSON.stringify(digaInfo),
    diga,
    prescribeDate,
    bsnr,
    activatedSchein,
  ]);

  const renderViewForm = useMemo(() => {
    const { viewFormInfo } = medicationContext;
    if (!viewFormInfo || diga) {
      return null;
    }

    const {
      formInfoResponse,
      prescribeDate,
      currentFormType,
      formSetting,
      id,
    } = viewFormInfo.formInfo;

    if (
      formInfoResponse?.[0]?.['erezeptItemStatus'] ||
      formInfoResponse?.[0]?.isEPrescription
    ) {
      return (
        <Flex auto column className="sl-form-container">
          <ERezeptStylesheet bundleUrl={viewFormInfo.formInfo.bundleUrl} />
        </Flex>
      );
    }

    if (isEmpty(formInfoMap)) {
      return <LoadingState />;
    }

    const medicineFormInfo = {
      id: id,
      medicines: formInfoResponse,
      prescribeDate: prescribeDate,
    };

    const viewFormSetting = {
      ...JSON.parse(formSetting),
      ...formInfoMap,
    };

    const currentSchein = patientFileStore.schein?.list.find(
      (schein) => schein.scheinId === viewFormInfo.scheinId
    );
    const formProps = {
      formInfoMap: {
        ...viewFormSetting,
        label_ik_number:
          [FormType.KREZ, FormType.BTM].includes(currentFormType) &&
            hasSupportForm907
            ? PSEUDOIK_FORM907
            : viewFormSetting.label_ik_number,
      },
      isViewOnly: true,
      patient,
      medicineFormInfo,
      doctorStamp,
      currentSchein,
    };

    switch (currentFormType) {
      case FormType.GREZ:
        return (
          <Flex auto column className="sl-form-container">
            <GreenRezept
              {...formProps}
              initFormSetting={viewFormSetting}
              className="sl-grezept-form"
            />
          </Flex>
        );
      case FormType.KREZ:
        return (
          <Flex auto column className="sl-form-container">
            <KkRezept
              {...formProps}
              className="sl-krezept-form"
              initFormSetting={viewFormSetting}
              hasSupportForm907={hasSupportForm907}
            />
          </Flex>
        );
      case FormType.BTM:
        return (
          <Flex auto column className="sl-form-container">
            <BtmRezept
              {...formProps}
              className="sl-btm-form"
              initFormSetting={viewFormSetting}
              medicineFormInfo={{
                ...medicineFormInfo,
                ePrescription: viewFormInfo.formInfo.ePrescription,
              }}
              hasSupportForm907={hasSupportForm907}
            />
          </Flex>
        );

      case FormType.TPrescription:
        return (
          <Flex auto column className="sl-form-container">
            <TRezept {...formProps} initFormSetting={viewFormSetting} />
          </Flex>
        );

      case FormType.Private:
        return (
          <Flex auto column className="sl-form-container">
            <BlueRezept
              {...formProps}
              initFormSetting={viewFormSetting}
              className="sl-blue-form"
            />
          </Flex>
        );

      case FormType.AOKNordwet:
        return (
          <Flex auto column className="sl-form-container">
            <AOKNordwet
              {...formProps}
              initFormSetting={viewFormSetting}
              medicineFormInfo={{
                ...medicineFormInfo,
                ePrescription: viewFormInfo.formInfo.ePrescription,
              }}
            />
          </Flex>
        );

      case FormType.AOKBremen:
        return (
          <Flex auto column className="sl-form-container">
            <AOKBremen
              {...formProps}
              initFormSetting={viewFormSetting}
              medicineFormInfo={{
                ...medicineFormInfo,
                ePrescription: viewFormInfo.formInfo.ePrescription,
              }}
            />
          </Flex>
        );

      case FormType.Muster16aBay:
        return (
          <Flex auto column className="sl-form-container">
            <Muster16aBay
              {...formProps}
              initFormSetting={viewFormSetting}
              setChangedSprechsundenbedarf={setChangedSprechsundenbedarf}
              medicineFormInfo={{
                ...medicineFormInfo,
                ePrescription: viewFormInfo.formInfo.ePrescription,
              }}
            />
          </Flex>
        );

      default:
        return null;
    }
  }, [
    hasSupportForm907,
    medicationContext.viewFormInfo,
    JSON.stringify(formInfoMap),
    activatedSchein?.scheinId,
  ]);

  // const getDisplayCountForm = useMemo(() => {
  //   if (medicationContext.viewFormInfo) {
  //     return '';
  //   }
  //   return `(${getTotalForms()} ${t('forms')})`;
  // }, [getTotalForms]);

  useEffect(() => {
    const currentSchein = patientFileStore.schein?.list.find(
      (schein) => schein.scheinId === currentScheinId
    );

    checkSupportForm907(currentSchein || activatedSchein);
  }, [activatedSchein, currentScheinId]);

  useEffect(() => {
    if (!shoppingBag?.medicines) {
      return;
    }

    const medicineN1ProductIds = shoppingBag?.medicines.reduce(
      (listOfIds, med) => {
        if (
          med.productInformation?.id != null &&
          med.packagingInformation?.packageSize?.nop === 'N1'
        ) {
          listOfIds.push(med.productInformation?.id);
        }
        return listOfIds;
      },
      [] as number[]
    );

    if (!medicineN1ProductIds.length) return;

    getPackageSizes({
      medicineProductIds: medicineN1ProductIds,
      isSvPatient: isSVPatient,
      referenceDate: datetimeUtil.dateTimeFormat(
        datetimeUtil.now(),
        YEAR_MONTH_DAY_FORMAT
      ),
      ikNumber:
        (bsnr
          ? getInformationByBSNR(bsnr).ikNumber
          : patientFileStore.activeInsurance?.ikNumber) || undefined,
      contractId: selectedContractDoctor.contractId,
    }).then(({ data }) => {
      if (
        medicineN1ProductIds.length < data.normSizeCodeList?.length &&
        (('changingPackageSizeMedicine' in store.packageSize
          ? Boolean(store.packageSize?.changingPackageSizeMedicine)
          : false) ||
          medicationContext.isInRefillProcess)
      ) {
        const mapp: Record<number, number> = {};
        const codesToWarn: NormSizeCodeListItem[] = [];
        for (const code of data.normSizeCodeList) {
          if (mapp[code.medicineProductId] == null) {
            mapp[code.medicineProductId] = 1;
            continue;
          }
          const isExist = codesToWarn.find(
            (kode) => kode.medicineProductId === code.medicineProductId
          );
          if (!isExist) {
            codesToWarn.push(code);
          }
        }
        setWarningN1MedicineSizeCodes(codesToWarn ?? []);
      }
    });
  }, [JSON.stringify(shoppingBag?.medicines)]);

  useEffect(() => {
    const formInfo = store.refill.formInfo;

    if (formInfo?.hasChangedSprechsundenbedarf && shoppingBag?.medicines[0]) {
      setChangedSprechsundenbedarf((prevValues) => ({
        ...prevValues,
        [formInfo.currentFormType]: true,
      }));
      setFormAnnotationSetting((prevValue) => ({
        ...prevValue,
        [formInfo.currentFormType]: JSON.parse(formInfo.formSetting),
      }));
    }
  }, [store.refill.formInfo, shoppingBag?.medicines[0]]);

  useHotkeys('esc', (event) => {
    event.preventDefault();
    onClosePrintPreview();
  });

  return (
    <Dialog
      isOpen={medicationContext.showPrintReview}
      enforceFocus={false}
      className={getCssClass('bp5-dialog-fullscreen', className)}
      portalClassName="sl-portal-hidden-scroll-container"
      canOutsideClickClose={false}
    >
      <Flex
        column
        className={Classes.DIALOG_BODY}
        style={{ padding: 0 }}
        {...registerActionChainElementId(FormElementId.FORM)}
      >
        <Flex justify="space-between" align="center" p={16}>
          <Divider />
          <Flex>
            <Flex ml={24} align="center">
              <Svg
                onClick={onClosePrintPreview}
                className="sl-close-icon"
                src={closeIcon}
              />
            </Flex>
          </Flex>
        </Flex>
        <Divider style={{ margin: 0 }} />
        <Flex auto className="sl-main-form-view">
          {renderFormMiniMap()}
          {renderPrintPreview}
          {renderViewForm}
          <Flex className="form-right-panel">
            <PrintPreviewSetting
              isViewForm={!!medicationContext?.viewFormInfo}
              selectedContractDoctor={
                bsnr
                  ? ({
                    doctorId: null,
                    bsnrId: null,
                    contractId: undefined,
                    chargeSystemId: undefined,
                    availableDoctor: doctorList,
                  } as unknown as ISelectedContractDoctor)
                  : selectedContractDoctor
              }
              groupMedicineByForm={groupByForm}
              patient={patient}
              treatmentDoctorId={treatmentDoctorId}
              bsnr={bsnr}
              viewFormId={medicationContext.viewFormInfo?.formInfo?.id}
              onRequestPrescribe={onPrescribeMedicationForm}
              onPrescribeSingleMedicationForm={onPrescribeSingleMedicationForm}
              currentFormType={
                medicationContext?.viewFormInfo?.formInfo?.currentFormType
              }
              warningN1MedicineSizeCodes={warningN1MedicineSizeCodes}
              formInfo={medicationContext?.viewFormInfo?.formInfo}
              clearSearchBox={clearSearchBox}
              printPreviewStore={printPreviewStore}
              formAnnotationSetting={formAnnotationSetting}
              assignedToBsnrId={assignedToBsnrId}
            />
          </Flex>
        </Flex>
      </Flex>
    </Dialog>
  );
}

export default memo(MedicationPrintPreviewMemo);
