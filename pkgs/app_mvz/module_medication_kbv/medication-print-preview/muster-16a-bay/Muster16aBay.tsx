import { usePrintPreviewStore } from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.store';
import { IPrintPreviewForm } from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import React, { memo, useCallback, useEffect, useState } from 'react';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import {
  FormType,
  MedicineShoppingBagInfo,
} from '@tutum/hermes/bff/app_mvz_medicine';
import { LoadingState, Flex, Svg } from '@tutum/design-system/components';
import FormAnnotation from '../../../components/form-annotation/RenderAnnotation.styled';
import MedicationInfo from '../medication-info/Medication.styled';
import {
  ICustomAnnotationComponent,
  IFormField,
  IFormFieldType,
  IFormInfo,
} from '../../../components/form-annotation/FormAnnotation.type';
import { parseNumber } from '@tutum/design-system/infrastructure/utils';
import { IPatientProfile } from '../../../module_patient-management/types/profile.type';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
} from '@tutum/mvz/constant/form';
import { medicationUtil } from '../../utils/medication-util';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  buildContent,
  getMedicineLength,
} from '../medication-info/MedicationInfo';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import i18n from '@tutum/infrastructure/i18n';
import {
  RATIO_CONSULTATION_PRESCRIPTION_FORM,
  MAX_MEDICINES_CONSULTATION_PRESCRIPTION_FORM,
  MUSTER_16A_BAY_MAX_LENGTH,
} from '../helper';
import { isEmpty } from 'lodash';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

const formSvg = '/data/form/Muster_16a_bay.svg';

export interface IMuster16aBayProps {
  className?: string;
  medicineFormInfo: IPrintPreviewForm;
  formInfoMap: { [key: string]: string };
  setEditingMedicine?: (medicine: MedicineShoppingBagInfo) => void;
  onChangeFormSetting?: (
    formId: string,
    formSetting: FORM_SETTING_OBJECT
  ) => void;
  onChangePrescribeDate?: (
    formType: FormType,
    formId: string,
    selectedDate: number
  ) => void;
  initFormSetting?: FORM_SETTING_OBJECT;
  isViewOnly?: boolean;
  patient?: IPatientProfile;
  setFormInfoMap?: Function;
  doctorStamp?: IEmployeeProfile;
  doctorId?: string;
  prescribeDate?: number;
  changedSprechsundenbedarf?: {
    [key: string]: boolean;
  };
  setChangedSprechsundenbedarf?: Function;
}

function Muster16aBayMemo(props: IMuster16aBayProps & IMvzThemeProps) {
  const {
    className,
    medicineFormInfo,
    formInfoMap,
    setEditingMedicine,
    onChangeFormSetting,
    onChangePrescribeDate,
    initFormSetting,
    isViewOnly,
    changedSprechsundenbedarf,
    setChangedSprechsundenbedarf,
    ...restProps
  } = props;

  const printPreviewStore = usePrintPreviewStore();
  const {
    schein: { activatedSchein },
  } = usePatientFileStore();

  const [formInfo, setFormInfo] = useState<IFormInfo>({} as IFormInfo);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadedImg, setLoadedImg] = useState<boolean>(false);
  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>(
    initFormSetting || {}
  );
  const [customFormInfoMap, setCustomFormInfoMap] =
    useState<FORM_SETTING_OBJECT>({});

  const { t: tMedicine } = i18n.useTranslation<
    keyof typeof MedicationI18n.RenderAnnotation
  >({
    namespace: 'Medication',
    nestedTrans: 'RenderAnnotation',
  });

  const chooseLabelToDisplay = (
    medicine: MedicineShoppingBagInfo,
    medicineLength: number,
    index: number
  ) => {
    //only one medicine in form default display in label_medication
    if (medicineLength === 1) {
      return 'label_medication';
    }
    // have 2 medicine in form
    // List 2 cases:
    // case 1: both < MUSTER_16A_BAY_MAX_LENGTH will display in label_medication1 and label_medication2
    // case 2: only one < MUSTER_16A_BAY_MAX_LENGTH while the other >= MUSTER_16A_BAY_MAX_LENGTH then display as label_medication4
    if (medicineLength <= 5) {
      const m1Length = getMedicineLength(medicine, tMedicine);
      if (
        m1Length <
        MUSTER_16A_BAY_MAX_LENGTH * (RATIO_CONSULTATION_PRESCRIPTION_FORM + 1)
      ) {
        return `label_medication${index + 1}`; // case for 2 item in min group
      } else {
        return `label_medication${index + MAX_MEDICINES_CONSULTATION_PRESCRIPTION_FORM
          }`; //case for 2 item in medium group, display at: label_medication4 and label_medication5
      }
    } else {
      return `label_medication${index + 1}`;
    }
  };

  useEffect(() => {
    if (onChangeFormSetting && medicineFormInfo.id) {
      onChangeFormSetting(medicineFormInfo.id, formValue);
    }
  }, [JSON.stringify(formValue), medicineFormInfo?.id]);

  const loadFormMeta = useCallback((): Promise<IFormInfo> => {
    return fetchWithHeaders('GET', '/data/form/Muster_16a_bay.json').then(
      (response) => response.data || []
    );
  }, []);

  const onChangeFormEvent = (
    formField: IFormField,
    newVal?: FORM_SETTING_TYPE
  ) => {
    if (
      formField.type === IFormFieldType.DATE_PICKER &&
      formField.name === 'date_prescribe'
    ) {
      const dateValue = parseNumber(newVal);

      if (onChangePrescribeDate) {
        return onChangePrescribeDate(
          FormType.Muster16aBay,
          medicineFormInfo.id,
          dateValue
        );
      }
    } else {
      medicationUtil.processFormValue(
        formField,
        formValue,
        setFormValue,
        newVal!
      );
    }
  };

  const customComponents = (formField: IFormField) => {
    //copy from generated Component
    const comArray: ICustomAnnotationComponent[] = [];
    //max length medicine in each form is 3
    medicineFormInfo.medicines.forEach((medicine, index) => {
      const medicineComponent: ICustomAnnotationComponent = {
        fieldName: chooseLabelToDisplay(
          medicine,
          medicineFormInfo.medicines.length,
          index
        ),
        component: () => (
          <MedicationInfo
            prescribeMedicine={[...[medicine]]}
            isViewOnly={isViewOnly || medicineFormInfo.hasSaveOrPrescribe}
            onEditMedicine={setEditingMedicine}
            onChangeFormEvent={onChangeFormEvent}
            formField={formField}
          />
        ),
      };
      comArray.push(medicineComponent);
    });

    return comArray;
  };

  const onChangeBsnr = (data: Record<string, string>) => {
    setFormValue((prevValues) => ({
      ...prevValues,
      ...data,
    }));
    setCustomFormInfoMap((prevValues) => ({
      ...prevValues,
      ...data,
    }));
  };

  //Initial for loading annotation data
  useEffect(() => {
    loadFormMeta()
      .then((formInfo) => {
        setFormInfo(formInfo);
        const newValue = {};
        const formData = medicationUtil.preProcessFormValue(formInfo);

        medicineFormInfo.medicines.forEach((medicine, index) => {
          const fieldName = chooseLabelToDisplay(
            medicine,
            medicineFormInfo.medicines.length,
            index
          );
          const medicineContent = buildContent(medicine, tMedicine);

          formData[fieldName] = medicineContent;
        });
        setChangedSprechsundenbedarf?.((prevValues) => ({
          ...prevValues,
          [FormType.Muster16aBay]: true,
        }));
        if (!initFormSetting || !Object.keys(initFormSetting).length) {
          setFormValue({
            ...formData,
            ...newValue,
          });
        } else {
          setFormValue({
            ...formData,
            ...newValue,
            ...initFormSetting,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [activatedSchein]);

  useEffect(() => {
    if (!isEmpty(formInfoMap)) {
      setCustomFormInfoMap(
        changedSprechsundenbedarf && initFormSetting
          ? initFormSetting
          : formInfoMap
      );
    }
  }, [
    JSON.stringify(formInfoMap),
    // changedSprechsundenbedarf,
    JSON.stringify(initFormSetting)
  ]);

  if (loading) {
    return <LoadingState />;
  }

  return (
    <>
      {printPreviewStore.formViewId === medicineFormInfo.id && (
        <Flex auto justify="center" className={className}>
          <div id="medication-form" className="sl-form">
            <Svg
              id="img-form"
              className="sl-img"
              src={formSvg}
              onLoad={() => setLoadedImg(true)}
            />
            {formInfo?.meta?.fields?.length > 0 &&
              loadedImg &&
              formInfo?.meta?.fields.map((formField) => {
                if (
                  formField.name.includes('label_medication') &&
                  !customComponents(formField).find(
                    (field) => field.fieldName === formField.name
                  )
                ) {
                  return null;
                }

                return (
                  <FormAnnotation
                    {...restProps}
                    formField={formField}
                    key={formField.name}
                    labelFontSize="23px"
                    onChangeEvent={(field, newVal) =>
                      onChangeFormEvent(field, newVal)
                    }
                    formMetaData={formInfo}
                    componentValue={
                      formField.name === 'date_prescribe'
                        ? medicineFormInfo.prescribeDate
                        : formValue[formField.name]
                    }
                    prescribeDate={medicineFormInfo.prescribeDate}
                    isViewOnly={
                      isViewOnly ||
                      medicineFormInfo.hasSaveOrPrescribe ||
                      formField.name.includes('toggle_9_bedarf')
                    }
                    dateRange={
                      formField.name === 'date_prescribe'
                        ? {
                          minDate: undefined,
                          maxDate: datetimeUtil.date(),
                        }
                        : undefined
                    }
                    customComponents={customComponents(formField)}
                    formInfoMap={customFormInfoMap}
                    onChangeBsnr={onChangeBsnr}
                  />
                );
              })}
          </div>
        </Flex>
      )}
    </>
  );
}

export default memo(Muster16aBayMemo);
