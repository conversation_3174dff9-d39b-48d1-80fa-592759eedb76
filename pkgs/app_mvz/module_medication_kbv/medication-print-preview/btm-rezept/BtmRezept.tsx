import { usePrintPreviewStore } from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.store';
import { IPrintPreviewForm } from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import {
  FormType,
  MedicineShoppingBagInfo,
} from '@tutum/hermes/bff/app_mvz_medicine';
import { LoadingState, Flex, Svg } from '@tutum/design-system/components';
import FormAnnotation from '@tutum/mvz/components/form-annotation/RenderAnnotation.styled';
import MedicationInfo from '../medication-info/Medication.styled';
import {
  ICustomAnnotationComponent,
  IFormField,
  IFormFieldType,
  IFormInfo,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import { getAge, parseNumber } from '@tutum/design-system/infrastructure/utils';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
} from '@tutum/mvz/constant/form';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import { checkCopaymentExemptionTillDate } from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  CHECKBOX_COPAYMENT_1,
  CHECKBOX_COPAYMENT_2,
  COPAYMENT_SINGLE_CHECKBOX,
} from '../kk-rezept/KkRezept';
import {
  BTM_FORM_MAX_LENGTH,
  chooseLabelToDisplay,
  getCustomLabelMedicationWhenPrint,
} from '../helper';
import { buildContent } from '../medication-info/MedicationInfo';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import i18n from '@tutum/infrastructure/i18n';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { isEmpty } from 'lodash';
import { PSEUDOIK_FORM907 } from '@tutum/mvz/module_medication_kbv/utils/medication.const';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';

const formSvg = '/data/form/Btm_Rezept.svg';

const DEFAULT_VALUES_RULE_9 = {
  region: '01',
  insurance: 'AOK NORDWEST',
  ikNumber: '101317184',
};

export interface IBtmRezeptProps {
  className?: string;
  medicineFormInfo: IPrintPreviewForm;
  formInfoMap: { [key: string]: string };
  setEditingMedicine?: (medicine: MedicineShoppingBagInfo) => void;
  onChangeFormSetting?: (
    formId: string,
    formSetting: FORM_SETTING_OBJECT
  ) => void;
  onChangePrescribeDate?: (
    formType: FormType,
    formId: string,
    selectedDate: number
  ) => void;
  initFormSetting?: FORM_SETTING_OBJECT;
  isViewOnly?: boolean;
  patient?: IPatientProfile;
  setFormInfoMap?: Function;
  doctorStamp?: IEmployeeProfile;
  doctorId?: string;
  changedSprechsundenbedarf?: {
    [key: string]: boolean;
  };
  setChangedSprechsundenbedarf?: Function;
  isStatistics?: boolean;
  hasSupportForm907?: boolean;
}

function BtmRezeptMemo(props: IBtmRezeptProps & IMvzThemeProps) {
  const {
    className,
    medicineFormInfo,
    formInfoMap,
    setEditingMedicine,
    onChangeFormSetting,
    onChangePrescribeDate,
    initFormSetting,
    isViewOnly,
    patient,
    setFormInfoMap,
    changedSprechsundenbedarf,
    setChangedSprechsundenbedarf,
    isStatistics,
    hasSupportForm907,
    ...restProps
  } = props;

  const printPreviewStore = usePrintPreviewStore();
  const {
    schein: { activatedSchein },
  } = usePatientFileStore();

  const [formInfo, setFormInfo] = useState<IFormInfo>({} as IFormInfo);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadedImg, setLoadedImg] = useState<boolean>(false);
  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>(
    initFormSetting || {}
  );
  const [customFormInfoMap, setCustomFormInfoMap] =
    useState<FORM_SETTING_OBJECT>({});

  const { t } = i18n.useTranslation<
    keyof typeof MedicationI18n.PrintPreviewSetting
  >({
    namespace: 'Medication',
    nestedTrans: 'PrintPreviewSetting',
  });

  const { t: tMedicine } = i18n.useTranslation<
    keyof typeof MedicationI18n.RenderAnnotation
  >({
    namespace: 'Medication',
    nestedTrans: 'RenderAnnotation',
  });

  useEffect(() => {
    if (onChangeFormSetting && medicineFormInfo.id) {
      onChangeFormSetting(medicineFormInfo.id, formValue);
    }
  }, [JSON.stringify(formValue), medicineFormInfo?.id]);

  const getBTMSpecialExceedingValue = useMemo(() => {
    if (!medicineFormInfo?.ePrescription?.specialIdentifier) {
      return '';
    }

    return medicineFormInfo?.ePrescription?.specialIdentifier
      .split('-')?.[0]
      .trim();
  }, [medicineFormInfo?.ePrescription?.specialIdentifier]);

  useEffect(() => {
    if (!isEmpty(formInfo)) {
      formInfo?.meta?.fields.map((item) => {
        if (item.name === 'label_special_exceedings') {
          setFormValue((prevState) => ({
            ...prevState,
            label_special_exceedings: getBTMSpecialExceedingValue,
          }));
          setFormInfoMap?.((prevState) => ({
            ...prevState,
            [`label_special_exceedings_${medicineFormInfo.id}`]:
              getBTMSpecialExceedingValue,
          }));
        }
      });
    }
  }, [medicineFormInfo.id, formInfo, getBTMSpecialExceedingValue]);

  const loadFormMeta = useCallback((): Promise<IFormInfo> => {
    return fetchWithHeaders('GET', '/data/form/Btm_Rezept.json').then(
      (response) => response.data || []
    );
  }, []);

  //Initial for loading annotation data
  useEffect(() => {
    loadFormMeta()
      .then((formInfo) => {
        setFormInfo(formInfo);
        let formData = medicationUtil.preProcessFormValue(formInfo);
        formInfo?.meta.fields.map((item) => {
          if (item.name === 'label_special_exceedings' && !isViewOnly) {
            //if not view mode, set special exceeding value from medicineFormInfo with form Id
            formData['label_special_exceedings'] = getBTMSpecialExceedingValue;
            setFormInfoMap?.((prevState) => ({
              ...prevState,
              [`label_special_exceedings_${medicineFormInfo.id}`]:
                getBTMSpecialExceedingValue,
            }));
          }
        });

        if (patient) {
          const patientAge = patient.dateOfBirth
            ? getAge(new Date(patient.dateOfBirth))
            : -1;
          if (patientAge != -1 && patientAge < 18) {
            formData[CHECKBOX_COPAYMENT_1] = true;
          } else {
            const insuranceActive = getActiveInsurance(
              patient?.patientInfo?.insuranceInfos
            );

            if (insuranceActive?.haveCoPaymentExemptionTill) {
              const fieldNameActive = checkCopaymentExemptionTillDate(
                insuranceActive?.copaymentExemptionTillDate
              )
                ? CHECKBOX_COPAYMENT_1
                : CHECKBOX_COPAYMENT_2;
              formData[fieldNameActive] = true;
            } else {
              formData[CHECKBOX_COPAYMENT_2] = true;
            }
          }
        }

        medicineFormInfo.medicines.forEach((medicine, index) => {
          const fieldName = chooseLabelToDisplay(
            medicineFormInfo.medicines,
            index,
            BTM_FORM_MAX_LENGTH,
            tMedicine
          );
          const medicineContent = buildContent(medicine, tMedicine);

          formData[fieldName] = medicineContent;
        });

        // for printing must to custom label medication
        const values = getCustomLabelMedicationWhenPrint(
          medicineFormInfo.medicines,
          BTM_FORM_MAX_LENGTH,
          tMedicine
        );

        formData = {
          ...formData,
          ...values,
        };

        if (!initFormSetting || !Object.keys(initFormSetting).length) {
          setFormValue(formData);
        } else {
          setFormValue({
            ...formData,
            ...initFormSetting,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [activatedSchein]);

  //Revise state for current BTM form by form Id
  const currentFormInfoMapForBTM = useMemo(() => {
    // in view mode, get special_exceeding from medicineFormInfo that we get form api viewMedicationForm
    if (isViewOnly) {
      return {
        ...customFormInfoMap,
        label_special_exceedings: getBTMSpecialExceedingValue,
      };
    } else {
      // not in view mode, get special_exceedings from formInfoMap with form Id
      if (customFormInfoMap) {
        return {
          ...customFormInfoMap,
          label_special_exceedings:
            customFormInfoMap[
            `label_special_exceedings_${medicineFormInfo.id}`
            ] || getBTMSpecialExceedingValue,
        };
      }
    }
  }, [
    JSON.stringify(customFormInfoMap),
    getBTMSpecialExceedingValue,
    medicineFormInfo.id,
    isViewOnly,
  ]);

  const onChangeFormEvent = (
    formField: IFormField,
    newVal?: FORM_SETTING_TYPE
  ) => {
    let dependGroup: string[] = [];
    if (COPAYMENT_SINGLE_CHECKBOX.includes(formField.name)) {
      dependGroup = COPAYMENT_SINGLE_CHECKBOX;
    }
    if (formField.name === 'toggle_9_bedarf' && !formValue[formField.name]) {
      const prescribeDateValue = new Date(
        medicineFormInfo.prescribeDate || +DatetimeUtil.date()
      );
      const quarter = DatetimeUtil.getQuarter(prescribeDateValue);
      const year = DatetimeUtil.getYear(prescribeDateValue, false);
      const newValue: FORM_SETTING_OBJECT = {
        [formField.name]: !formValue[formField.name],
        label_patientInfo_line1: t('sprechsundenbedarf'),
        label_patientInfo_line2: t('quarter', {
          quarter,
          year,
        }),
        label_patientInfo_line3: '',
        label_patientInfo_line4: '',
        label_date_of_birth: '',
        label_insurance_status: '',
      };

      if (
        (customFormInfoMap.label_bsnr as string).startsWith(
          DEFAULT_VALUES_RULE_9.region
        )
      ) {
        newValue.label_insurance_name = DEFAULT_VALUES_RULE_9.insurance;
        newValue.label_ik_number = DEFAULT_VALUES_RULE_9.ikNumber;
      }

      setChangedSprechsundenbedarf?.((prevValues) => ({
        ...prevValues,
        [FormType.BTM]: true,
      }));
      setFormValue((prevValues) => ({
        ...prevValues,
        ...customFormInfoMap,
        ...newValue,
        [CHECKBOX_COPAYMENT_1]: changedSprechsundenbedarf?.[FormType.BTM]
          ? prevValues[CHECKBOX_COPAYMENT_1]
          : false,
        [CHECKBOX_COPAYMENT_2]: changedSprechsundenbedarf?.[FormType.BTM]
          ? prevValues[CHECKBOX_COPAYMENT_2]
          : false,
      }));
      setCustomFormInfoMap((prevValues) => ({
        ...prevValues,
        ...newValue,
      }));

      return;
    }
    if (
      formField.type === IFormFieldType.DATE_PICKER &&
      formField.name === 'date_prescribe'
    ) {
      const dateValue = parseNumber(newVal);
      const quarter = DatetimeUtil.getQuarter(new Date(dateValue));
      const year = DatetimeUtil.getYear(new Date(dateValue), false);

      if (
        changedSprechsundenbedarf?.[FormType.BTM] ||
        formValue['toggle_9_bedarf']
      ) {
        const newValue = {
          label_patientInfo_line2: t('quarter', {
            quarter,
            year,
          }),
        };

        setFormValue((prevValues) => ({
          ...prevValues,
          ...newValue,
        }));
        setCustomFormInfoMap((prevValues) => ({
          ...prevValues,
          ...newValue,
        }));
      }

      if (onChangePrescribeDate) {
        return onChangePrescribeDate(
          FormType.BTM,
          medicineFormInfo.id,
          dateValue
        );
      }
    } else {
      medicationUtil.processFormValue(
        formField,
        formValue,
        setFormValue,
        newVal!,
        dependGroup
      );
    }
  };

  const customComponents = (formField: IFormField) => {
    //copy from generated Component
    const comArray: ICustomAnnotationComponent[] = [];
    //max length medicine in each form is 3
    medicineFormInfo.medicines.forEach((medicine, index) => {
      const medicineComponent: ICustomAnnotationComponent = {
        fieldName: chooseLabelToDisplay(
          medicineFormInfo.medicines,
          index,
          BTM_FORM_MAX_LENGTH,
          tMedicine
        ),
        component: () => (
          <MedicationInfo
            prescribeMedicine={[...[medicine]]}
            isViewOnly={isViewOnly || medicineFormInfo.hasSaveOrPrescribe}
            onEditMedicine={setEditingMedicine}
            onChangeFormEvent={onChangeFormEvent}
            formField={formField}
          />
        ),
      };
      comArray.push(medicineComponent);
    });

    return comArray;
  };

  const onChangeBsnr = (data: Record<string, string>) => {
    setFormValue((prevValues) => ({
      ...prevValues,
      ...data,
    }));
    setCustomFormInfoMap((prevValues) => ({
      ...prevValues,
      ...data,
    }));
  };

  useEffect(() => {
    const checkValue = { ...formInfoMap };

    delete checkValue[`label_special_exceedings_${medicineFormInfo.id}`];

    if (!isEmpty(checkValue)) {
      const data =
        changedSprechsundenbedarf && initFormSetting
          ? initFormSetting
          : formInfoMap;

      setCustomFormInfoMap({
        ...data,
        label_ik_number: hasSupportForm907
          ? PSEUDOIK_FORM907
          : data['label_ik_number'],
      });
    }
  }, [
    formInfoMap,
    changedSprechsundenbedarf,
    initFormSetting,
    hasSupportForm907,
    medicineFormInfo.id,
  ]);

  useEffect(() => {
    if (isStatistics) {
      setChangedSprechsundenbedarf?.((prevValues) => ({
        ...prevValues,
        [FormType.BTM]: true,
      }));
    }
  }, [isStatistics]);

  if (loading) {
    return <LoadingState />;
  }

  return (
    <>
      {printPreviewStore.formViewId === medicineFormInfo.id && (
        <Flex auto justify="center" className={className}>
          <div id="medication-form" className="sl-form">
            <Svg
              id="img-form"
              className="sl-img"
              src={formSvg}
              onLoad={() => setLoadedImg(true)}
            />
            {formInfo?.meta?.fields?.length > 0 &&
              loadedImg &&
              formInfo?.meta?.fields.map((formField) => {
                if (
                  formField.name.includes('label_medication') &&
                  !customComponents(formField).find(
                    (field) => field.fieldName === formField.name
                  )
                ) {
                  return null;
                }

                return (
                  <FormAnnotation
                    {...restProps}
                    formField={formField}
                    key={formField.name}
                    labelFontSize="23px"
                    onChangeEvent={(field, newVal) =>
                      onChangeFormEvent(field, newVal)
                    }
                    formMetaData={formInfo}
                    componentValue={
                      formField.name === 'date_prescribe'
                        ? medicineFormInfo.prescribeDate
                        : formValue[formField.name]
                    }
                    prescribeDate={medicineFormInfo.prescribeDate}
                    isViewOnly={
                      isViewOnly ||
                      medicineFormInfo.hasSaveOrPrescribe ||
                      (isStatistics &&
                        formField.name.includes('toggle_9_bedarf'))
                    }
                    dateRange={
                      formField.name === 'date_prescribe'
                        ? {
                          minDate: undefined,
                          maxDate: DatetimeUtil.date(),
                        }
                        : undefined
                    }
                    customComponents={customComponents(formField)}
                    formInfoMap={currentFormInfoMapForBTM}
                    onChangeBsnr={onChangeBsnr}
                  />
                );
              })}
          </div>
        </Flex>
      )}
    </>
  );
}

export default memo(BtmRezeptMemo);
