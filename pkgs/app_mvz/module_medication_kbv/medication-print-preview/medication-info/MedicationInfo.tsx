import { ReactElement, memo, useContext, useMemo } from 'react';

import { Flex, Svg } from '@tutum/design-system/components';
import { Button, Tooltip } from '@tutum/design-system/components/Core';
import { MedicineShoppingBagInfo } from '@tutum/hermes/bff/app_mvz_medicine';
import I18n, {
  IFixedNamespaceTFunction,
  II18nFixedNamespace,
} from '@tutum/infrastructure/i18n';
import { IFormField } from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import { ON_CHANGE_FORM_EVENT } from '@tutum/mvz/constant/form';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import { medicationUtil as medicineUtil } from '@tutum/mvz/module_medication/utils/medication-util';
import { medicationActions } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { medicationShoppingBagActions } from '../../shopping-bag/MedicationShoppingBag.store';
import { medicationUtil } from '../../utils/medication-util';
import { printPreviewActions } from '../MedicationPrintPreview.store';

const editIcon = '/images/edit-blue.svg';

export interface IMedicationInfo {
  className?: string;
  prescribeMedicine: MedicineShoppingBagInfo[];
  showFavHint?: boolean;
  isViewOnly?: boolean;
  onEditMedicine?: (medicine: MedicineShoppingBagInfo) => void;
  onChangeFormEvent: ON_CHANGE_FORM_EVENT;
  formField: IFormField;
  isERezept?: boolean;
}

// NOTE: PRO-1560 Make PZN changes for ATC-Code N07BC  medication  (paper prescription)
function shouldShowPznForAtcN07bc(medicine: MedicineShoppingBagInfo): boolean {
  if (
    !medicine?.productInformation ||
    !medicine?.drugInformation?.aTC?.startsWith('N07BC')
  ) {
    return true;
  }
  return medicine.name === medicine?.productInformation.name;
}

const isShowComma = (
  medicine: MedicineShoppingBagInfo,
  intakeInterval: string
) => {
  const isAtcN07bcNameNotEdited = shouldShowPznForAtcN07bc(medicine);

  return Boolean(
    medicationUtil.getDosageForm(
      medicine?.productInformation,
      medicine?.name
    ) ||
    medicineUtil.getPackageSize(medicine?.packagingInformation) ||
    medicine?.furtherInformation ||
    (medicine?.pzn && isAtcN07bcNameNotEdited) ||
    intakeInterval ||
    medicine?.asNeeded ||
    medicine?.isArtificialInsemination
  );
};

export const buildContent = (
  medicine: MedicineShoppingBagInfo,
  t: IFixedNamespaceTFunction
) => {
  if (medicine.productInformation?.isDigaFlag) {
    return t('digitalHealth', {
      pzn: medicine.pzn,
      name: medicine.name,
    });
  }

  const intakeInterval =
    medicationUtil.getDisplayIntakeInterval(medicine, t) || '';
  const dosageForm =
    medicationUtil.getDosageForm(
      medicine?.productInformation,
      medicine?.name
    ) || '';
  const furtherInformation = medicine?.furtherInformation || '';
  const isAtcN07bcNameNotEdited = shouldShowPznForAtcN07bc(medicine);
  const pznInfo =
    medicine?.pzn && isAtcN07bcNameNotEdited ? `PZN${medicine.pzn}` : '';
  let medicineQuantity = `${medicine?.packagingInformation?.nameRecipe || medicine?.name
    }${isShowComma(medicine, intakeInterval) ? ' |' : ''}`;
  if (medicine?.quantity > 1) {
    medicineQuantity = `${medicine?.quantity} x ${medicineQuantity}`;
  }
  const medicineContent = [
    dosageForm,
    pznInfo,
    intakeInterval,
    furtherInformation,
  ]
    .filter((item) => item)
    .join(' | ');
  const medicineAsNeeded =
    // TODO review data
    medicine?.asNeeded || medicine?.substitutionPrescription
      ? '\nErsatzverordnung gemäß § 31 Absatz 3 Satz 7 SGB V'
      : '';
  const medicineArtificialInsemination = medicine?.isArtificialInsemination
    ? '| Verordnung nach §27a SGB V'
    : '';
  const finalContent = [
    medicineQuantity,
    medicineContent,
    medicineAsNeeded,
    medicineArtificialInsemination,
  ]
    .filter((item) => item)
    .join(' ');
  return finalContent.trim();
};

export const getMedicineLength = (medicine: MedicineShoppingBagInfo, t) => {
  const finalMedicineContent = buildContent(medicine, t);
  return finalMedicineContent.length;
};

function MedicationInfo(
  props: IMedicationInfo &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.RenderAnnotation>
) {
  const {
    className,
    prescribeMedicine,
    t,
    isViewOnly,
    onEditMedicine,
    onChangeFormEvent,
    formField,
    isERezept = false,
  } = props;

  const medicationContext = useContext(MedicationContext);
  const onClickEditMedicine = () => {
    medicationShoppingBagActions.getShoppingBag();
    medicationContext.setShowPrintPreviewState(false);
    medicationActions.setShowShoppingBag(true);
    printPreviewActions.setFormViewId(null);
    printPreviewActions.setCurrentFormName('');
  };

  const eRezeptContent = (medicine: MedicineShoppingBagInfo): ReactElement => {
    const dosageForm = medicationUtil.getDosageForm(
      medicine?.productInformation,
      medicine?.name
    );
    return (
      <div>
        <div>
          {medicine?.asNeeded
            ? '\nErsatzverordnung gemäß § 31 Absatz 3 Satz 7 SGB V'
            : ''}
        </div>
        <div>{`${medicine?.quantity} x ${medicine?.name}${medicine.drugFormInformation ? ` ${medicine.drugFormInformation}` : ''
          }${dosageForm ? `, ${dosageForm}` : ''}${medicineUtil.getPackageSize(
            medicine?.packagingInformation
          )}${medicine?.pzn ? ` (PZN${medicine?.pzn})` : ''}`}</div>
        <div>{`${medicationUtil.displayERezeptIntakeInterval(medicine)}`}</div>
        <div>{medicine?.furtherInformation}</div>
        <div>
          {medicine?.isArtificialInsemination
            ? 'Zuzahlung von 50% aufgrund Regelung nach § 27a SGB V'
            : ''}
        </div>
      </div>
    );
  };

  const renderLabelMedicineContent = useMemo(() => {
    let labelMedicineArrayContent: string[] = [];
    const content = prescribeMedicine.map((medicine) => {
      const medicineContent = buildContent(medicine, t);
      labelMedicineArrayContent = [
        ...labelMedicineArrayContent,
        medicineContent,
      ];
      const content = isERezept ? eRezeptContent(medicine) : medicineContent;
      return (
        <Flex column key={medicine.id} className="sl-medicine-line">
          <Flex key={`${medicine.id}-name`}>
            <Flex auto className="sl-medicine-break-line">
              {content}
            </Flex>
            {onEditMedicine && !isViewOnly && (
              <Flex align="center">
                <Tooltip content={t('edit')}>
                  <Button
                    className="sl-edit-btn"
                    onClick={() => onClickEditMedicine()}
                    icon={<Svg src={editIcon} />}
                  />
                </Tooltip>
              </Flex>
            )}
          </Flex>
        </Flex>
      );
    });
    onChangeFormEvent(formField, labelMedicineArrayContent.join(''));
    return <>{content}</>;
  }, [prescribeMedicine, isViewOnly]);

  if (!prescribeMedicine || !prescribeMedicine.length) {
    return null;
  }

  return (
    <Flex className={className}>
      <Flex column className="sl-medicine-box">
        {renderLabelMedicineContent}
      </Flex>
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(MedicationInfo, {
    namespace: 'Medication',
    nestedTrans: 'RenderAnnotation',
  })
);
