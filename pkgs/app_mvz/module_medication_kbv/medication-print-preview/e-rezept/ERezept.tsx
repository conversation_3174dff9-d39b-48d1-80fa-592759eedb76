import { LoadingState, Svg } from '@tutum/design-system/components';
import { getAge } from '@tutum/design-system/infrastructure/utils';
import { TypeOfInsurance } from '@tutum/hermes/bff/patient_profile_common';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  ICustomAnnotationComponent,
  IFormField,
  IFormInfo,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import {
  DATE_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
} from '@tutum/mvz/constant/form';
import useFocusTrap from '@tutum/mvz/hooks/useFocusTrap';
import { checkCopaymentExemptionTillDate } from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import type { LexicalEditor } from 'lexical';
import moment from 'moment';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import FormAnnotation from '@tutum/mvz/components/form-annotation/RenderAnnotation.styled';
import {
  EREZEPT,
  medicationUtil,
} from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import { IPrintPreviewForm } from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import MedicationInfo from '@tutum/mvz/module_medication_kbv/medication-print-preview/medication-info/Medication.styled';
import { useMedicationShoppingBagStore } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { getBSNR } from '@tutum/hermes/bff/legacy/app_bsnr';
import { BSNR } from '@tutum/hermes/bff/bsnr_common';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { PSEUDOIK_FORM907 } from '@tutum/mvz/module_medication_kbv/utils/medication.const';
import { MedicineShoppingBagInfo } from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';

export interface IERezeptProps {
  className?: string;
  medicineFormInfo: IPrintPreviewForm;
  patient?: IPatientProfile;
  formInfoMap: FORM_SETTING_OBJECT;
  initFormSetting?: FORM_SETTING_OBJECT;
  isViewOnly?: boolean;
  doctorStamp?: IEmployeeProfile;
  hasSupportForm907?: boolean;
  setEditingMedicine?: (medicine: MedicineShoppingBagInfo) => void;
  onChangeFormSetting?: (
    formId: string,
    formSetting: FORM_SETTING_OBJECT
  ) => void;
}

const formSvg = '/data/form/eRezept_PZN.svg';
const formSvgFreeText = '/data/form/eRezept_Freetext.svg';

const dependGroup1Fields = ['checkbox_gebpfl', 'checkbox_gebuhr_frei'];
const dependGroup2Fields = [
  'checkbox_unfall',
  'checkbox_arbeits_unfall',
  'checkbox_berufs_krankheit',
];

const TypeOfInsuranceMap = {
  [TypeOfInsurance.Public]: 'GKV',
  [TypeOfInsurance.Private]: 'PKV',
};

const CHECKBOX_COPAYMENT_1 = 'checkbox_gebuhr_frei';
const CHECKBOX_COPAYMENT_2 = 'checkbox_gebpfl';
const CHECKBOX_UNFALL = 'checkbox_unfall';
const CHECKBOX_ARBEITS_UNFALL = 'checkbox_arbeits_unfall';
const DATE_UNFALL_TAG = 'date_unfalltag';
const LABEL_DATE_UNFALL_TAG = 'label_date_unfall';
const LABEL_DATE_OF_BIRTH = 'label_date_of_birth';
const LABEL_PATIENT_INFO_LINE2 = 'label_patientInfo_line2';
const LABEL_PATIENT_INFO = 'label_patientInfo';
const KEY_TO_BUILD_DOCTOR_INFO = [
  'name',
  'code',
  ['street', 'number'],
  ['postCode', 'city'],
  'fullName',
];

const ERezept = (props: IERezeptProps) => {
  const {
    className,
    initFormSetting,
    medicineFormInfo,
    isViewOnly,
    formInfoMap, //patient header info
    setEditingMedicine,
    onChangeFormSetting,
    patient,
    doctorStamp,
    hasSupportForm907,
    ...restProps
  } = props;

  const [formInfo, setFormInfo] = useState<IFormInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadedImg, setLoadedImg] = useState<boolean>(false);
  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>(
    initFormSetting || {}
  );
  const [customFormInfoMap, setCustomFormInfoMap] =
    useState<FORM_SETTING_OBJECT>(formInfoMap);
  const [bsnr, setBsnr] = useState<BSNR | null>(null);

  const { bundlesCreated } = useMedicationShoppingBagStore();

  const [setFocusRef] = useFocusTrap();
  const [textModuleInstance, setTextModuleInstance] = useState<
    Record<string, LexicalEditor>
  >({});
  const { globalData } = GlobalContext.useContext();

  useEffect(() => {
    getBSNR().then((res) => {
      const result = res.data.data.find(
        (item) => item.bsnr.code === globalData.userProfile?.bsnr
      );
      if (!result) {
        return doctorStamp;
      }
      const { bsnr } = result;
      setBsnr(bsnr);
    });
  }, [globalData]);

  const erezeptDoctorStamp = useMemo(() => {
    if (!bsnr) {
      return doctorStamp;
    }
    const doctorStampCloned = { ...doctorStamp };
    const doctorInfo: string[] = [];
    KEY_TO_BUILD_DOCTOR_INFO.forEach((key) => {
      if (Array.isArray(key)) {
        const values: string[] = [];
        key.forEach((k) => {
          values.push(bsnr[k]);
        });

        doctorInfo.push(values.join(' '));
      } else {
        doctorInfo.push(bsnr[key]);
      }
    });

    if (bsnr.phoneNumber) {
      doctorInfo.push(`Tel: ${bsnr.phoneNumber}`);
    }

    if (bsnr.fax) {
      doctorInfo.push(`Fax: ${bsnr.fax}`);
    }

    if (bsnr.email) {
      doctorInfo.push(`Email: ${bsnr.email}`);
    }
    const { userProfile } = globalData;
    if (userProfile?.fullName) {
      doctorInfo.push(userProfile.fullName);
    }

    if (userProfile?.jobDescription) {
      doctorInfo.push(userProfile.jobDescription);
    }

    if (userProfile?.lanr) {
      doctorInfo.push(userProfile.lanr);
    }

    return { ...doctorStampCloned, doctorStamp: doctorInfo.join('\n') };
  }, [doctorStamp, bsnr, globalData]);

  const handleLexical = React.useCallback((editor, fieldName) => {
    setTextModuleInstance((prev) => ({ ...prev, [fieldName]: editor }));
  }, []);

  const loadFormMeta = useCallback(async (): Promise<IFormInfo> => {
    return fetchWithHeaders('GET', '/data/form/eRezept.json').then(
      (response) => response.data || []
    );
  }, []);

  useEffect(() => {
    if (initFormSetting) {
      setFormValue(initFormSetting);
      setCustomFormInfoMap({
        ...formInfoMap,
        label_prescribe: initFormSetting?.label_prescribe?.toString() ?? '',
        label_insurance_type:
          TypeOfInsuranceMap[
          patient?.patientInfo.genericInfo.patientType || ''
          ],
        label_ik_number: hasSupportForm907
          ? PSEUDOIK_FORM907
          : formInfoMap['label_ik_number'],
      });
    } else {
      const bundle = bundlesCreated.find(
        (bundle) => bundle.medicineId === medicineFormInfo.id
      );

      const clonedFormInfoMap = {
        ...formInfoMap,
        label_prescribe: DatetimeUtil.dateTimeFormat(
          DatetimeUtil.now(),
          DATE_FORMAT
        ),
        label_insurance_type:
          TypeOfInsuranceMap[
          patient?.patientInfo.genericInfo.patientType || ''
          ],
        label_prfnr: bundle?.aMVCheckNumber || '',
        label_dok_id: bundle?.prescriptionId || '',
        label_ik_number: hasSupportForm907
          ? PSEUDOIK_FORM907
          : formInfoMap['label_ik_number'],
      };
      if (formInfoMap[LABEL_PATIENT_INFO]) {
        const labelPatientInfoLine2 = formInfoMap[LABEL_PATIENT_INFO]
          .toString()
          .substring(0, 34);
        clonedFormInfoMap[LABEL_PATIENT_INFO_LINE2] = labelPatientInfoLine2;
      }

      if (formInfoMap['dob']) {
        const labelDateOfBirth = DatetimeUtil.dateTimeFormat(
          parseInt(formInfoMap['dob'].toString()),
          DATE_FORMAT
        );
        clonedFormInfoMap[LABEL_DATE_OF_BIRTH] = labelDateOfBirth;
      }
      setCustomFormInfoMap(clonedFormInfoMap);
    }
  }, [
    initFormSetting,
    formInfoMap,
    bundlesCreated,
    medicineFormInfo.id,
    hasSupportForm907,
  ]);

  //Initial for loading annotation data
  useEffect(() => {
    loadFormMeta()
      .then((formInfo) => {
        setFormInfo(formInfo);
        const formData = medicationUtil.preProcessFormValue(formInfo);
        const insuranceActive = getActiveInsurance(
          patient?.patientInfo?.insuranceInfos
        );

        const patientAge = patient?.dateOfBirth
          ? getAge(new Date(patient?.dateOfBirth))
          : -1;
        if (patientAge != -1 && patientAge < 18) {
          formData[CHECKBOX_COPAYMENT_1] = true;
        } else {
          if (insuranceActive?.haveCoPaymentExemptionTill) {
            const fieldNameActive = checkCopaymentExemptionTillDate(
              insuranceActive?.copaymentExemptionTillDate
            )
              ? CHECKBOX_COPAYMENT_1
              : CHECKBOX_COPAYMENT_2;
            formData[fieldNameActive] = true;
          } else {
            formData[CHECKBOX_COPAYMENT_2] = true;
          }
        }

        formData['checkbox_aut_idem'] = true; // Default aut idem is true in ERezept
        if (!initFormSetting || !Object.keys(initFormSetting).length) {
          setFormValue(formData);
        }
      })
      .finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    if (onChangeFormSetting && medicineFormInfo.id) {
      onChangeFormSetting(medicineFormInfo.id, formValue);
    }
  }, [JSON.stringify(formValue), medicineFormInfo?.id]);

  const onChangeDateUnfallAdditionalUpdate = (newVal) => {
    const date = new Date(newVal as number);
    const valueUpdate = moment(date).format(YEAR_MONTH_DAY_FORMAT);
    return { fieldName: LABEL_DATE_UNFALL_TAG, fieldValue: valueUpdate };
  };

  const onChangeFormEvent = (
    formField: IFormField,
    newVal?: FORM_SETTING_TYPE
  ) => {
    let dependGroup: string[] = [];
    if (dependGroup1Fields.includes(formField.name)) {
      dependGroup = dependGroup1Fields;
    }

    if (dependGroup2Fields.includes(formField.name)) {
      dependGroup = dependGroup2Fields;
    }

    let additionalUpdate:
      | { fieldName: string; fieldValue: string }
      | undefined = undefined;
    if (formValue[formField.name] !== undefined) {
      if (formField.name === DATE_UNFALL_TAG) {
        additionalUpdate = onChangeDateUnfallAdditionalUpdate(newVal);
      }

      if (
        formField.name === CHECKBOX_UNFALL ||
        formField.name === CHECKBOX_ARBEITS_UNFALL
      ) {
        const currentTime = DatetimeUtil.now();
        formValue[DATE_UNFALL_TAG] = currentTime;
        formValue[LABEL_DATE_UNFALL_TAG] = DatetimeUtil.dateTimeFormat(
          currentTime,
          YEAR_MONTH_DAY_FORMAT
        );
      }

      medicationUtil.processFormValue(
        formField,
        formValue,
        setFormValue,
        newVal!,
        dependGroup,
        additionalUpdate
      );
    }
  };

  const customComponents = (formField: IFormField) => {
    //copy from generated Component
    const medicineComponent: ICustomAnnotationComponent = {
      fieldName: 'label_pzn',
      component: () => (
        <MedicationInfo
          prescribeMedicine={medicineFormInfo.medicines}
          isViewOnly={isViewOnly || medicineFormInfo.hasSaveOrPrescribe}
          onEditMedicine={setEditingMedicine}
          onChangeFormEvent={onChangeFormEvent}
          formField={formField}
          isERezept={true}
        />
      ),
    };
    return [medicineComponent];
  };

  const onChangeBsnr = (data: Record<string, string>) => {
    setFormValue((prevValues) => ({
      ...prevValues,
      ...data,
    }));
    const prevFormInfoMap = { ...customFormInfoMap } as Record<string, string>;
    setCustomFormInfoMap({ ...prevFormInfoMap, ...data });
  };

  const disabledBsnr = useMemo<boolean>(() => {
    const { employeeProfiles, userProfile } = globalData;
    const employeeProfile = employeeProfiles.find(
      (profile) => profile.accountId === userProfile?.accountId
    );
    return !employeeProfile?.teamNumbers?.length;
  }, [globalData]);

  return loading ? (
    <LoadingState />
  ) : (
    <div className={className} ref={setFocusRef}>
      <div id="medication-form" className="sl-form">
        <Svg
          id="img-form"
          className="sl-img"
          src={medicineFormInfo.medicines[0].pzn ? formSvg : formSvgFreeText}
          onLoad={() => setLoadedImg(true)}
        />
        {!!formInfo?.meta?.fields?.length &&
          loadedImg &&
          formInfo?.meta?.fields.map((formField) => {
            if (
              formField.name === DATE_UNFALL_TAG &&
              !(
                formValue['checkbox_arbeits_unfall'] ||
                formValue['checkbox_unfall']
              )
            ) {
              return null;
            }

            if (
              formField.name === 'textbox_unfallbetrieb' &&
              !formValue['checkbox_arbeits_unfall']
            ) {
              return null;
            }

            return (
              <FormAnnotation
                formField={formField}
                key={formField.name}
                onChangeEvent={onChangeFormEvent}
                formMetaData={formInfo}
                componentValue={formValue[formField.name]}
                prescribeDate={medicineFormInfo.prescribeDate}
                isViewOnly={isViewOnly || medicineFormInfo.hasSaveOrPrescribe}
                currentFormName={EREZEPT}
                customComponents={customComponents(formField)}
                handleLexical={handleLexical}
                textModuleInstance={textModuleInstance}
                labelFontSize="25px"
                disabledBsnr={disabledBsnr}
                {...restProps}
                formInfoMap={customFormInfoMap}
                doctorStamp={erezeptDoctorStamp as IEmployeeProfile}
                onChangeBsnr={onChangeBsnr}
                defaultDoctorStampFontSize={20}
              />
            );
          })}
      </div>
    </div>
  );
};

export default memo(ERezept);
