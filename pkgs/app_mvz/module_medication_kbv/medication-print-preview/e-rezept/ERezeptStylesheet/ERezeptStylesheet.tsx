import React, { useEffect, useRef } from 'react';

import XMLHelper from '@tutum/infrastructure/utils/XMLHelper';
import { getPresignedURL } from '@tutum/mvz/module_e-documents/EDocument.helper';
import fileUtil from '@tutum/infrastructure/utils/file.util';
import { BASE_PATH_MVZ } from '@tutum/infrastructure/utils/string.util';

const XSLT = `${BASE_PATH_MVZ}/data/form/e-prescription/eRezept_Stylesheet.xslt`;

export interface IERezeptStylesheetProps {
  className?: string;
  bundleUrl?: string;
}

const ERezeptStylesheet = (props: IERezeptStylesheetProps) => {
  const { className, bundleUrl } = props;
  const erpRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (bundleUrl) {
      getPresignedURL(bundleUrl)
        .then((urlFile) => {
          fileUtil.readUrlFileToText(urlFile).then((bundleXmlString) => {
            if (
              bundleXmlString &&
              document.implementation &&
              document.implementation.createDocument!
            ) {
              const xmlHelper = new XMLHelper();
              const resultDocument = xmlHelper.bindXMLDataToXSLFile(
                bundleXmlString,
                XSLT
              );
              if (!resultDocument) {
                return;
              }

              if (!erpRef || !erpRef.current) {
                return;
              }

              if (erpRef.current.hasChildNodes()) {
                erpRef.current.removeChild(erpRef.current?.firstChild!);
              }

              erpRef.current.appendChild(resultDocument);
            }
          });
        })
        .catch((err) => {
          throw new Error(err);
        });
    }
  }, [bundleUrl]);

  return <div className={className} ref={erpRef} />;
};

export default ERezeptStylesheet;
