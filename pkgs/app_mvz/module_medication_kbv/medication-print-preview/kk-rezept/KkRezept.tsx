import { Flex, LoadingState, Svg } from '@tutum/design-system/components';
import { getAge, parseNumber } from '@tutum/design-system/infrastructure/utils';
import {
  FormType,
  MedicineShoppingBagInfo,
} from '@tutum/hermes/bff/app_mvz_medicine';
import {
  default as I18n,
  II18nFixedNamespace,
  default as i18n,
} from '@tutum/infrastructure/i18n';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { checkIsSvSchein } from '@tutum/mvz/_utils/scheinFormat';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
  KK_AUTIDEM_MEDICINE_CATEGORIES,
} from '@tutum/mvz/constant/form';
import useFocusTrap from '@tutum/mvz/hooks/useFocusTrap';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { checkCopaymentExemptionTillDate } from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import { usePrintPreviewStore } from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.store';
import { IPrintPreviewForm } from '@tutum/mvz/module_medication_kbv/utils/medication.type';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { ADJUSTABILITY_OF_THE_AUT_IDEM_CROSS_ON_THE_RECIPE } from '@tutum/mvz/module_setting/medication/interface';
import { getSetting } from '@tutum/mvz/module_setting/medication/service';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { useCallback, useEffect, useState } from 'react';
import {
  ICustomAnnotationComponent,
  IFormField,
  IFormFieldType,
  IFormInfo,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import FormAnnotation from '@tutum/mvz/components/form-annotation/RenderAnnotation.styled';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import {
  buildDigaContent,
  K_FORM_MAX_LENGTH,
  chooseLabelToDisplay,
  getCustomLabelMedicationWhenPrint,
} from '../helper';
import MedicationInfo from '../medication-info/Medication.styled';
import { buildContent } from '../medication-info/MedicationInfo';
import { DigaInfo } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import { PSEUDOIK_FORM907 } from '@tutum/mvz/module_medication_kbv/utils/medication.const';
import { isEmpty } from 'lodash';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';

const formSvg = '/data/form/Muster_16.svg';

export const CHECKBOX_COPAYMENT_1 = 'checkbox_gebuhrfrei';
export const CHECKBOX_COPAYMENT_2 = 'checkbox_gebpfl';
export const COPAYMENT_SINGLE_CHECKBOX = [
  CHECKBOX_COPAYMENT_1,
  CHECKBOX_COPAYMENT_2,
];

export type IKkRezeptProps = {
  className?: string;
  medicineFormInfo?: IPrintPreviewForm;
  digaInfo?: DigaInfo;
  patient?: IPatientProfile;
  formInfoMap: FORM_SETTING_OBJECT;
  setEditingMedicine?: (medicine: MedicineShoppingBagInfo) => void;
  onChangeFormSetting?: (
    formId: string,
    formSetting: FORM_SETTING_OBJECT
  ) => void;
  onChangePrescribeDate?: (
    formTyppe: FormType,
    formId: string,
    selectedDate: number
  ) => void;
  initFormSetting?: FORM_SETTING_OBJECT;
  isViewOnly?: boolean;
  doctorStamp?: IEmployeeProfile;
  doctorId?: string;
  changedSprechsundenbedarf?: {
    [key: string]: boolean;
  };
  isStatistics?: boolean;
  setChangedSprechsundenbedarf?: Function;
  hasSupportForm907?: boolean;
  isShow?: boolean;
} & ({ medicineFormInfo: IPrintPreviewForm } | { digaInfo: DigaInfo });

function KkRezeptMemo(
  props: IKkRezeptProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.KkRezept>
) {
  const {
    className,
    medicineFormInfo,
    patient,
    setEditingMedicine,
    onChangePrescribeDate,
    initFormSetting,
    isViewOnly,
    onChangeFormSetting,
    formInfoMap,
    changedSprechsundenbedarf,
    setChangedSprechsundenbedarf,
    isStatistics,
    digaInfo,
    hasSupportForm907,
    isShow,
    ...restProps
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof MedicationI18n.PrintPreviewSetting
  >({
    namespace: 'Medication',
    nestedTrans: 'PrintPreviewSetting',
  });
  const { t: tDiga } = I18n.useTranslation<keyof typeof DigaListLang.FormPrint>(
    {
      namespace: 'Diga',
      nestedTrans: 'FormPrint',
    }
  );

  const printPreviewStore = usePrintPreviewStore();

  const [setFocusRef] = useFocusTrap();

  const [formInfo, setFormInfo] = useState<IFormInfo>({} as IFormInfo);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadedImg, setLoadedImg] = useState<boolean>(false);
  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>({});
  const [customFormInfoMap, setCustomFormInfoMap] =
    useState<FORM_SETTING_OBJECT>({});

  const viewOnly = isViewOnly || medicineFormInfo?.hasSaveOrPrescribe;

  const loadFormMeta = useCallback((): Promise<IFormInfo> => {
    return fetchWithHeaders('GET', '/data/form/Muster_16.json').then(
      (response) => response.data || []
    );
  }, []);

  const {
    schein: { activatedSchein },
  } = usePatientFileStore();

  // PRO-3475 [HzV Medication] VSST509 As a MFA/doctor, I want to set up the Aut-idem for medicines in the medicine categories >Green<, >GruenCalculated< and >Blue<.
  const preCheckAutDem = async (formValue, setFormValue) => {
    const formData = { ...formValue };
    if (checkIsSvSchein(activatedSchein)) {
      const { settings } = await getSetting();
      if (settings?.['autIdem'] === 'true') {
        const isSupport =
          await catalogOverviewActions.doesContractSupportFunctions(
            [ADJUSTABILITY_OF_THE_AUT_IDEM_CROSS_ON_THE_RECIPE],
            activatedSchein?.hzvContractId || '',
            activatedSchein?.chargeSystemId
          );
        if (isSupport) {
          medicineFormInfo?.medicines.forEach((item, index) => {
            if (
              KK_AUTIDEM_MEDICINE_CATEGORIES.includes(
                (item.colorCategory?.drugCategory || '').toLowerCase()
              )
            ) {
              formData[`checkbox_autIdem${index + 1}`] = true;
            }
          });
          setFormValue(formData);
        }
      }
    }
  };

  const { t: tMedicine } = i18n.useTranslation<
    keyof typeof MedicationI18n.RenderAnnotation
  >({
    namespace: 'Medication',
    nestedTrans: 'RenderAnnotation',
  });

  //Initial for loading annotation data
  useEffect(() => {
    loadFormMeta()
      .then((formInfo) => {
        setFormInfo(formInfo);
        let formData = medicationUtil.preProcessFormValue(formInfo);
        const insuranceActive = getActiveInsurance(
          patient?.patientInfo?.insuranceInfos
        );

        if (patient) {
          const patientAge = patient?.dateOfBirth
            ? getAge(new Date(patient.dateOfBirth))
            : -1;
          if (patientAge != -1 && patientAge < 18) {
            formData[CHECKBOX_COPAYMENT_1] = true;
          } else {
            if (insuranceActive?.haveCoPaymentExemptionTill) {
              const fieldNameActive = checkCopaymentExemptionTillDate(
                insuranceActive?.copaymentExemptionTillDate
              )
                ? CHECKBOX_COPAYMENT_1
                : CHECKBOX_COPAYMENT_2;
              formData[fieldNameActive] = true;
            } else {
              formData[CHECKBOX_COPAYMENT_2] = true;
            }
          }
        }

        if (digaInfo) {
          formData[CHECKBOX_COPAYMENT_1] = true;
          formData[CHECKBOX_COPAYMENT_2] = false;
          buildDigaContent(digaInfo, tDiga, K_FORM_MAX_LENGTH).forEach(
            (item) => {
              const content = item.rawText || '';
              const customData = content.split('\n').reduce(
                (formData, content, index) => {
                  return {
                    ...formData,
                    [`label_medication1-${index + 1}`]: content,
                  };
                },
                {
                  'label_medication1-1': '',
                  'label_medication1-2': '',
                  'label_medication1-3': '',
                  'label_medication1-4': '',
                  'label_medication1-5': '',
                  'label_medication1-6': '',
                }
              );

              formData = {
                ...formData,
                ...customData,
                [item.fieldName]: content,
              };
            }
          );
        } else {
          medicineFormInfo?.medicines.forEach((medicine, index) => {
            const fieldName = chooseLabelToDisplay(
              medicineFormInfo.medicines,
              index,
              K_FORM_MAX_LENGTH,
              tMedicine
            );
            const medicineContent = buildContent(medicine, tMedicine);

            formData[fieldName] = medicineContent;
          });

          // for printing must to custom label medication
          const values = getCustomLabelMedicationWhenPrint(
            medicineFormInfo?.medicines,
            K_FORM_MAX_LENGTH,
            tMedicine
          );

          formData = {
            ...formData,
            ...values,
          };
        }
        if (!initFormSetting || !Object.keys(initFormSetting).length) {
          setFormValue(formData);
        } else {
          setFormValue({
            ...formData,
            ...initFormSetting,
          });
        }
        preCheckAutDem(formData, setFormValue);
      })
      .finally(() => setLoading(false));
  }, [activatedSchein]);

  useEffect(() => {
    if (onChangeFormSetting) {
      onChangeFormSetting(
        medicineFormInfo?.id || 'customFormSetting',
        formValue
      );
    }
  }, [JSON.stringify(formValue), medicineFormInfo?.id]);

  const onChangeFormEvent = (
    formField: IFormField,
    newVal?: FORM_SETTING_TYPE
  ) => {
    let dependGroup: string[] = [];
    if (COPAYMENT_SINGLE_CHECKBOX.includes(formField.name)) {
      dependGroup = COPAYMENT_SINGLE_CHECKBOX;
    }
    if (formField.name === 'toggle_9_bedarf' && !formValue[formField.name]) {
      const prescribeDateValue = new Date(
        medicineFormInfo?.prescribeDate || +DatetimeUtil.date()
      );
      const quarter = DatetimeUtil.getQuarter(prescribeDateValue);
      const year = DatetimeUtil.getYear(prescribeDateValue, false);
      const newValue = {
        [formField.name]: !formValue[formField.name],
        label_patientInfo_line1: t('sprechsundenbedarf'),
        label_patientInfo_line2: t('quarter', {
          quarter,
          year,
        }),
        label_patientInfo_line3: '',
        label_patientInfo_line4: '',
        label_date_of_birth: '',
        label_insurance_number: '',
        label_insurance_status: '',
      };

      setChangedSprechsundenbedarf?.((prevValues) => ({
        ...prevValues,
        [FormType.KREZ]: true,
      }));
      setFormValue((prevValues) => ({
        ...prevValues,
        ...customFormInfoMap,
        ...newValue,
        [CHECKBOX_COPAYMENT_1]: changedSprechsundenbedarf?.[FormType.KREZ]
          ? prevValues[CHECKBOX_COPAYMENT_1]
          : false,
        [CHECKBOX_COPAYMENT_2]: changedSprechsundenbedarf?.[FormType.KREZ]
          ? prevValues[CHECKBOX_COPAYMENT_2]
          : false,
      }));
      setCustomFormInfoMap((prevValues) => ({
        ...prevValues,
        ...newValue,
      }));

      return;
    }
    if (
      formField.type === IFormFieldType.DATE_PICKER &&
      formField.name === 'date_prescribe'
    ) {
      const dateValue = parseNumber(newVal);
      const quarter = DatetimeUtil.getQuarter(new Date(dateValue));
      const year = DatetimeUtil.getYear(new Date(dateValue), false);

      if (
        changedSprechsundenbedarf?.[FormType.KREZ] ||
        formValue['toggle_9_bedarf']
      ) {
        const newValue = {
          label_patientInfo_line2: t('quarter', {
            quarter,
            year,
          }),
        };

        setFormValue((prevValues) => ({
          ...prevValues,
          ...newValue,
        }));
        setCustomFormInfoMap((prevValues) => ({
          ...prevValues,
          ...newValue,
        }));
      }

      if (onChangePrescribeDate) {
        return onChangePrescribeDate(
          FormType.KREZ,
          medicineFormInfo?.id || 'customFormSetting',
          dateValue
        );
      }
    } else {
      medicationUtil.processFormValue(
        formField,
        formValue,
        setFormValue,
        newVal!,
        dependGroup
      );
    }
  };

  const customComponents = (
    formField: IFormField
  ): ICustomAnnotationComponent[] => {
    //copy from generated Component
    const comArray: ICustomAnnotationComponent[] = [];
    //max length medicine in each form is 3
    if (digaInfo) {
      comArray.push(...buildDigaContent(digaInfo, tDiga, K_FORM_MAX_LENGTH));
    } else {
      medicineFormInfo?.medicines.forEach((medicine, index) => {
        const medicineComponent: ICustomAnnotationComponent = {
          fieldName: chooseLabelToDisplay(
            medicineFormInfo.medicines,
            index,
            K_FORM_MAX_LENGTH,
            tMedicine
          ),
          component: () => (
            <MedicationInfo
              prescribeMedicine={[...[medicine]]}
              isViewOnly={viewOnly || !!digaInfo}
              onEditMedicine={setEditingMedicine}
              onChangeFormEvent={onChangeFormEvent}
              formField={formField}
            />
          ),
        };
        comArray.push(medicineComponent);
      });
    }

    return comArray;
  };

  const onChangeBsnr = (data: Record<string, string>) => {
    setFormValue((prevValues) => ({
      ...prevValues,
      ...data,
    }));
    setCustomFormInfoMap((prevValues) => ({
      ...prevValues,
      ...data,
    }));
  };

  useEffect(() => {
    if (isStatistics) {
      setChangedSprechsundenbedarf?.((prevValues) => ({
        ...prevValues,
        [FormType.KREZ]: true,
      }));
    }
  }, [isStatistics]);

  useEffect(() => {
    if (!isEmpty(formInfoMap)) {
      const data =
        changedSprechsundenbedarf && initFormSetting
          ? initFormSetting
          : formInfoMap;

      setCustomFormInfoMap({
        ...data,
        label_ik_number: hasSupportForm907
          ? PSEUDOIK_FORM907
          : data['label_ik_number'],
      });
    }
  }, [
    JSON.stringify(formInfoMap),
    // changedSprechsundenbedarf,
    JSON.stringify(initFormSetting),
    hasSupportForm907,
  ]);

  if (loading) {
    return <LoadingState />;
  }
  const prescribeDate = medicineFormInfo?.prescribeDate || +DatetimeUtil.date();
  return (
    <>
      {(printPreviewStore.formViewId === medicineFormInfo?.id || isShow) && (
        <Flex auto justify="center" className={className} ref={setFocusRef}>
          <div id="medication-form" className="sl-form">
            <Svg
              id="img-form"
              className="sl-img"
              src={formSvg}
              onLoad={() => setLoadedImg(true)}
            />
            {formInfo?.meta?.fields?.length > 0 &&
              loadedImg &&
              formInfo?.meta?.fields.map((formField) => {
                if (
                  formField.name.includes('label_medication') &&
                  !customComponents(formField).find(
                    (field) => field.fieldName === formField.name
                  )
                ) {
                  return null;
                }

                return (
                  <FormAnnotation
                    {...restProps}
                    formField={formField}
                    key={formField.name}
                    labelFontSize="23px"
                    onChangeEvent={onChangeFormEvent}
                    formMetaData={formInfo}
                    componentValue={
                      formField.name === 'date_prescribe'
                        ? prescribeDate
                        : formValue[formField.name]
                    }
                    prescribeDate={prescribeDate}
                    isViewOnly={
                      viewOnly ||
                      (isStatistics &&
                        formField.name.includes('toggle_9_bedarf'))
                    }
                    dateRange={
                      formField.name === 'date_prescribe'
                        ? {
                          minDate: undefined,
                          maxDate: DatetimeUtil.date(),
                        }
                        : undefined
                    }
                    customComponents={customComponents(formField)}
                    formInfoMap={customFormInfoMap}
                    onChangeBsnr={onChangeBsnr}
                  />
                );
              })}
          </div>
        </Flex>
      )}
    </>
  );
}

export default I18n.withTranslation(KkRezeptMemo, {
  namespace: 'Medication',
  nestedTrans: 'KkRezept',
});
