import { proxy, useSnapshot } from 'valtio';
import {
  FormType,
  getShoppingBag,
  updateShoppingBagInformation,
  GetShoppingBagRequestResponse,
  MedicineShoppingBagInfo,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { BundleCreatedData } from '@tutum/hermes/bff/erezept_common';
import { FormInfo } from '@tutum/hermes/bff/legacy/medicine_common';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { FormName as FormNameCommon } from '@tutum/hermes/bff/form_common';
import {
  Prescribe,
  PrescribeType,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';

export interface CreateBundleTypes {
  formsInfo?: FormInfo[];
  medicines?: MedicineShoppingBagInfo[];
  isPrescribeAll?: boolean;
  onlyPrepareBundle?: boolean;
  hasSupportForm907?: boolean;
}

export interface IMedicationShoppingBagStore {
  isLoading: boolean;
  editingMedicine: MedicineShoppingBagInfo | null;
  editingDrugName?: MedicineShoppingBagInfo;
  patientId: string | undefined;
  doctorId: string | undefined;
  contractId?: string;
  shoppingBag?: GetShoppingBagRequestResponse;
  notPrescribeAbleByQuantity: { id: string; quantity: number }[] | null;
  bundlesCreated: BundleCreatedData[];
  diga: Nullable<{
    digaInfo: DigaInfo;
    formName: FormNameCommon;
    type: PrescribeType;
  }>;
  onPrint: Function | undefined;
  onPrescribe: Function | undefined;
}

export type DigaInfo = Partial<Prescribe> & {
  digaName: string;
  digaPzn: string;
};

const initStore = {
  isLoading: false,
  editingMedicine: null,
  selectedContractDoctor: null,
  patientId: undefined,
  doctorId: undefined,
  notPrescribeAbleByQuantity: null,
  ePrescriptionFormInfos: null,
  bundlesCreated: [],
  diga: undefined,
  onPrint: () => {},
  onPrescribe: () => {},
};

interface IMedicationShoppingBagActions {
  setPatientDoctor: (patient, selectedDoctor) => void;
  getShoppingBag: (bsnr?: string) => void;
  setShoppingBag: (shoppingBag: GetShoppingBagRequestResponse) => void;
  showEditDrugName: (medicine: MedicineShoppingBagInfo) => void;
  closeEditDrugName: () => void;
  saveDrugName: (
    newDrugName: string,
    successCB: Function,
    bsnr?: string
  ) => void;
  updateItemType: (newType: FormType, item: MedicineShoppingBagInfo) => void;
  setMedicineQuantity: (id: string, quantity: number) => void;
  setNotPrescribeAbleByQuantity: (medicine) => void;
  removeForNotPrescribeAbleByQuantity: (medicine) => void;
  setBundlesCreated: (payload: BundleCreatedData[]) => void;
  setLoading: (isLoading: boolean) => void;
  setDigaInfo: (
    digaInfo: DigaInfo,
    formType: FormNameCommon,
    type: PrescribeType
  ) => void;
  onPrint: (cb?: Function) => void;
  onPrescribe: (cb?: Function) => void;
  clearDigaInfo: () => void;
}

export const medicationShoppingBagStore =
  proxy<IMedicationShoppingBagStore>(initStore);

export const medicationShoppingBagActions: IMedicationShoppingBagActions = {
  setPatientDoctor: (patient, selectedDoctor) => {
    medicationShoppingBagStore.patientId = patient?.id;
    medicationShoppingBagStore.doctorId = selectedDoctor?.doctorId;
    medicationShoppingBagStore.contractId = selectedDoctor?.contractId;
  },

  setShoppingBag: (shoppingBag) => {
    medicationShoppingBagStore.shoppingBag = shoppingBag;
    medicationShoppingBagActions.setNotPrescribeAbleByQuantity(shoppingBag);
  },

  getShoppingBag: (bsnr: string) => {
    if (
      (medicationShoppingBagStore.patientId &&
        medicationShoppingBagStore.doctorId) ||
      bsnr
    ) {
      getShoppingBag({
        doctorId: bsnr ? null! : medicationShoppingBagStore.doctorId,
        patientId: bsnr ? null! : medicationShoppingBagStore.patientId,
        contractId: bsnr ? null! : medicationShoppingBagStore.contractId,
        bsnr,
      }).then((data) => {
        medicationShoppingBagActions.setShoppingBag(data.data);
      });
    }
  },

  showEditDrugName: (medicine: MedicineShoppingBagInfo) => {
    medicationShoppingBagStore.editingDrugName = medicine;
  },

  closeEditDrugName: () => {
    medicationShoppingBagStore.editingDrugName = undefined;
  },

  saveDrugName: (newDrugName: string, successCb: Function, bsnr?: string) => {
    updateShoppingBagInformation({
      patientId: medicationShoppingBagStore.patientId,
      doctorId: medicationShoppingBagStore.doctorId,
      contractId: medicationShoppingBagStore.contractId,
      medicineId: medicationShoppingBagStore.editingDrugName?.id,
      bsnr,
      freeText: newDrugName,
    }).then(() => {
      medicationShoppingBagStore.editingDrugName = undefined;
      medicationShoppingBagActions.getShoppingBag(bsnr);
      successCb();
    });
  },
  updateItemType: function (
    newType: FormType,
    item: MedicineShoppingBagInfo
  ): void {
    const target = medicationShoppingBagStore?.shoppingBag?.medicines.find(
      (med) => med.id === item.id
    );
    if (target) {
      target.currentFormType = newType;
    }
  },

  setMedicineQuantity: (id: string, quantity: number) => {
    medicationShoppingBagStore.notPrescribeAbleByQuantity =
      (medicationShoppingBagStore.notPrescribeAbleByQuantity || []).map((medicine) => {
        if (medicine.id === id) {
          return { ...medicine, quantity: quantity };
        }
        return medicine;
      });
  },

  setNotPrescribeAbleByQuantity: (medicine) => {
    medicationShoppingBagStore.notPrescribeAbleByQuantity =
      medicine?.medicines?.map((medicine) => ({
        id: medicine.id,
        quantity: medicine.quantity,
      }));
  },

  removeForNotPrescribeAbleByQuantity: (medicine) => {
    medicationShoppingBagStore.notPrescribeAbleByQuantity =
      (medicationShoppingBagStore.notPrescribeAbleByQuantity || []).filter(
        (item) => item.id !== medicine.id
      );
  },

  setBundlesCreated: (bundleCreateds) => {
    medicationShoppingBagStore.bundlesCreated = bundleCreateds;
  },
  setLoading: (isLoading) => {
    medicationShoppingBagStore.isLoading = isLoading;
  },
  setDigaInfo: (digaInfo: DigaInfo, formName: FormNameCommon, type) => {
    medicationShoppingBagStore.diga = {
      digaInfo,
      formName,
      type,
    };
  },
  onPrint: function (cb?: Function): void {
    medicationShoppingBagStore.onPrint = cb;
  },
  onPrescribe: function (cb?: Function): void {
    medicationShoppingBagStore.onPrescribe = cb;
  },
  clearDigaInfo: () => {
    medicationShoppingBagStore.diga = undefined;
  },
};

export function useMedicationShoppingBagStore() {
  return useSnapshot(medicationShoppingBagStore);
}
