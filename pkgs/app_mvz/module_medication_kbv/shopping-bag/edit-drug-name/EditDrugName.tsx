import React, { memo, useEffect, useState } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import {
  Button,
  Dialog,
  InputGroup,
  Intent,
} from '@tutum/design-system/components/Core';
import { MedicineShoppingBagInfo } from '@tutum/hermes/bff/app_mvz_medicine';
import {
  medicationShoppingBagActions,
  useMedicationShoppingBagStore,
} from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import {
  alertSuccessfully,
  BodyTextM,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IEditDrugNameProps {
  className?: string;
  medicineShoppingBagInfo?: MedicineShoppingBagInfo;
  doctorId?: string;
  patientId?: string;
  bsnr?: string;
  isRequire?: boolean;
}

function EditDrugNameMemo(
  props: IEditDrugNameProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.EditDrugName>
) {
  const { className, t, bsnr } = props;
  const [drugName, setDrugName] = useState('');
  const { editingDrugName } = useMedicationShoppingBagStore();

  useEffect(() => {
    setDrugName(editingDrugName?.name!);
  }, [editingDrugName]);

  return (
    <Dialog
      isCloseButtonShown={false}
      isOpen={!!editingDrugName}
      title={t('editDrugName')}
      onClose={() => medicationShoppingBagActions.closeEditDrugName()}
      className={className}
      canOutsideClickClose={false}
    >
      <div className="edit-drug-wrapper">
        <div className="edit-drug-description">{t('description')}</div>
        <div className="drug-name-input">
          <BodyTextM
            color={COLOR.TEXT_SECONDARY_NAVAL}
            fontFamily="Work Sans"
            fontWeight={500}
            style={{ padding: '2px 0' }}
          >
            {t('drugName')}
          </BodyTextM>
          <InputGroup
            value={drugName}
            onChange={(event) => {
              setDrugName(event.target.value);
            }}
          />
        </div>

        <Flex className="edit-drug-name-footer">
          <Button
            className="btn-drug-cancel"
            onClick={medicationShoppingBagActions.closeEditDrugName}
          >
            {t('cancel')}
          </Button>
          <Button
            intent={Intent.PRIMARY}
            onClick={() => {
              const successCb = () =>
                alertSuccessfully(t('editDrugNameSuccess'));
              medicationShoppingBagActions.saveDrugName(
                drugName,
                successCb,
                bsnr
              );
            }}
          >
            {t('save')}
          </Button>
        </Flex>
      </div>
    </Dialog>
  );
}

export default memo(
  I18n.withTranslation(EditDrugNameMemo, {
    namespace: 'Medication',
    nestedTrans: 'EditDrugName',
  })
);
