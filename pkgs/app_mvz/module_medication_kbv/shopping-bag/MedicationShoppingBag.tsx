import {
  BodyTextM,
  Button,
  Flex,
  H2,
  LoadingState,
  Svg,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Classes,
  Dialog,
  Divider,
  Intent,
} from '@tutum/design-system/components/Core';
import { styled } from '@tutum/design-system/models';
import {
  FormType,
  MedicineType,
  removeFromShoppingBag,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import {
  medicationShoppingBagActions,
  useMedicationShoppingBagStore,
} from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import EditDrugName from '@tutum/mvz/module_medication_kbv/shopping-bag/edit-drug-name/EditDrugName.styled';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import {
  MutableRefObject,
  memo,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useHotkeys } from 'react-hotkeys-hook';

import { isVisible } from '../utils/dom';
import { MedicineDetail } from './medicine-detail/MedicineDetail.styled';

import { useMutationCreateBundles } from '@tutum/hermes/bff/legacy/app_mvz_erezept';
import { checkIsVSST785 } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { FormInfo } from '@tutum/hermes/bff/medicine_common';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  patientFileStore,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  medicationActions,
  useMedicationStore,
} from '../medication/MedicationKBV.store';

const warningIcon = '/images/alert-circle-solid-billing.svg';

const BodyModalConfirm = styled.div`
  padding: 8px;
`;

export interface IMedicationShoppingBagProps {
  className?: string;
  isOpen: boolean;
  bsnr?: string;
  handleCloseDialog: (shouldFocusSearchInput?: boolean) => void;
}

function MedicationShoppingBagMemo(
  props: IMedicationShoppingBagProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.MedicationShoppingBag>
) {
  const { className, t, handleCloseDialog, isOpen: openDialog, bsnr } = props;
  const medicationShoppingBagStore = useMedicationShoppingBagStore();
  const shoppingBag = medicationShoppingBagStore.shoppingBag;
  const medicationContext = useContext(MedicationContext);
  const { setShowHintVSST785 } = useContext(PatientManagementContext.instance);
  const medStore = useMedicationStore();
  const { schein: scheinStore } = usePatientFileStore();

  const isEditing = !!medicationShoppingBagStore.editingMedicine;
  const [showConfirmRemoveAll, setShowConfirmRemoveAll] =
    useState<boolean>(false);

  const btnRemoveAll: MutableRefObject<any> = useRef();
  const btnPrescribe: MutableRefObject<any> = useRef();

  const { mutateAsync: createBundles, isPending: isPendingERP } =
    useMutationCreateBundles({
      onError: ({ response }) => {
        if (
          response.data.serverError ===
          ErrorCode.ErrorCode_EREZEPT_TRAINING_DOCTOR_AUTHORIZATION
        ) {
          alertError(t('erezeptTrainingDoctorAuthorization'));
        } else {
          alertError(t('failedToCreateEPrescription'));
        }
      },
      onSuccess: ({ data }) => {
        medicationShoppingBagActions.setBundlesCreated(data.bundleCreateds);
        goToPrintPreview();
      },
    });

  useHotkeys('ctrl+delete', (event) => {
    event.preventDefault();
    if (!btnRemoveAll?.current || !isVisible(btnRemoveAll.current)) {
      return;
    }
    btnRemoveAll?.current?.click();
  });

  useHotkeys('ctrl+v', (event) => {
    event.preventDefault();
    if (!btnPrescribe?.current || !isVisible(btnPrescribe.current)) {
      return;
    }
    btnPrescribe?.current?.click();
  });

  useHotkeys('esc', (event) => {
    event.preventDefault();
    handleCloseDialog();
  });

  const isNotPrescribeAble = useMemo(() => {
    if (!shoppingBag?.medicines?.length) {
      return true;
    }

    if (medStore.isStatistics) {
      return false;
    }

    const missingIntakeInterMedicines = shoppingBag.medicines
      // PRO-6981 No mandatory intake interval for Green and Private Prescription
      // skip medicine FormType Green and Private
      .filter((medicine) => {
        return (
          medicine.type !== MedicineType.FreeText &&
          medicine.currentFormType !== FormType.GREZ &&
          medicine.currentFormType !== FormType.Private
        );
      })
      // require input interval (input text or check DJ)
      .filter((medicine) => {
        return (
          !medicine.intakeInterval?.dJ &&
          !medicine.intakeInterval?.freetext?.length
        );
      });

    const missingValueFreetext = shoppingBag.medicines.filter(
      (medicine) => medicine.type === MedicineType.FreeText && !medicine.name
    );

    const quantityNotValid = (
      medicationShoppingBagStore.notPrescribeAbleByQuantity || []
    ).filter((medicine) => {
      return medicine.quantity < 1;
    });
    return (
      missingIntakeInterMedicines.length > 0 ||
      quantityNotValid.length > 0 ||
      missingValueFreetext.length > 0
    );
  }, [
    medStore.isStatistics,
    JSON.stringify(shoppingBag?.medicines),
    JSON.stringify(medicationShoppingBagStore.notPrescribeAbleByQuantity),
  ]);

  const onRemoveAll = useCallback(() => {
    setShowConfirmRemoveAll(true);
  }, []);

  const onCancelRemoveAll = useCallback(() => {
    setShowConfirmRemoveAll(false);
  }, []);

  const onConfirmRemoveAll = useCallback(() => {
    removeFromShoppingBag({
      shoppingBagId: shoppingBag?.shoppingBagId,
      doctorId: medicationShoppingBagStore?.doctorId,
      patientId: medicationShoppingBagStore?.patientId,
      bsnr,
    })
      .then(() => {
        alertSuccessfully(t('removedFromRezeptpool'));
        handleCloseDialog();
      })
      .finally(() => setShowConfirmRemoveAll(false));
  }, []);

  const renderModalConfirmRemoveAll = useMemo(() => {
    return (
      <Dialog
        isCloseButtonShown={false}
        isOpen={showConfirmRemoveAll}
        title={null}
        canOutsideClickClose={false}
      >
        <Flex className={`${Classes.DIALOG_BODY} sl-remove-shopping-bag`}>
          <BodyModalConfirm>
            <Flex column>
              <Flex align="center" className="sl-header-diaglog" gap={8}>
                <Svg width={32} height={32} src={warningIcon} />
                <H2>{t('headerRemoveAll')}</H2>
              </Flex>
              <Flex mt={18}>
                <BodyTextM>{t('removeAllDescription')}</BodyTextM>
              </Flex>
              <Flex mt={18} gap={16}>
                <Button
                  data-test-id="remove-all-cancel"
                  large
                  outlined
                  onClick={onCancelRemoveAll}
                  className="flex-1"
                >
                  {t('keepIt')}
                </Button>
                <Button
                  data-test-id="remove-all-confirm"
                  large
                  intent={Intent.DANGER}
                  onClick={onConfirmRemoveAll}
                  className="flex-1"
                >
                  {t('removeAll')}
                </Button>
              </Flex>
            </Flex>
          </BodyModalConfirm>
        </Flex>
      </Dialog>
    );
  }, [showConfirmRemoveAll]);

  const renderMedicineInShoppingBag = useMemo(() => {
    let renderList = shoppingBag?.medicines || [];
    if (isEditing) {
      renderList = renderList.filter(
        (item) => item.id === medicationShoppingBagStore.editingMedicine?.id
      );
    }

    return (
      <Flex column className="sl-collapse-panel">
        {renderList.map((item, index) => (
          <div key={item.id}>
            <MedicineDetail
              item={item}
              doctorId={medicationShoppingBagStore.doctorId!}
              patientId={medicationShoppingBagStore.patientId!}
              medicationShoppingBagStore={medicationShoppingBagStore}
              contractId={medicationShoppingBagStore.contractId!}
              bsnr={bsnr}
              isStatistics={medStore.isStatistics}
              onRemove={() => {
                if (renderList.length > 1) {
                  return;
                }

                handleCloseDialog();
              }}
            />
            {!(index + 1 === renderList.length) && <Divider />}
          </div>
        ))}
      </Flex>
    );
  }, [
    JSON.stringify(shoppingBag?.medicines),
    medicationShoppingBagStore.editingMedicine?.id,
    JSON.stringify(medicationShoppingBagStore),
  ]);

  const renderEditDrugName = useMemo(() => {
    return <EditDrugName bsnr={bsnr} />;
  }, [medicationShoppingBagStore.editingDrugName]);

  const goToPrintPreview = () => {
    handleCloseDialog(false);
    medicationContext.setShowPrintPreviewState(true);
  };

  const onRequestPrescribe = useCallback(async () => {
    const medicines = shoppingBag?.medicines || [];
    const medicineERP = medicines?.filter(
      (medicine) => !!medicine.isEPrescription
    );

    const ikNumber = scheinStore?.insuranceInfos?.find(
      (insurance) => insurance.id === scheinStore?.activatedSchein?.insuranceId
    )?.ikNumber;
    const data = await checkIsVSST785({
      contractId: scheinStore?.activatedSchein?.hzvContractId || '',
      patientId: shoppingBag?.patientId || '',
      atcCodes: medicines
        .filter((m) => Boolean(m.drugInformation))
        .map((m) => m.drugInformation?.aTC || ''),
      ikNumber: ikNumber,
    });
    setShowHintVSST785(Boolean(data?.data?.isVSST785));
    medicationActions.setRefillFormData(undefined);

    if (medicineERP.length) {
      const formInfos = medicineERP.map<FormInfo>((medicine) => ({
        currentFormType: medicine.currentFormType,
        medicineIDs: medicine.id ? [medicine.id] : [],
        formSetting: '',
        isShowFavHint: false,
      }));

      if (shoppingBag && patientFileStore?.schein?.activatedSchein?.scheinId) {
        await createBundles({
          formInfos,
          doctorId: shoppingBag.doctorId,
          patientId: shoppingBag.patientId,
          treatmentDoctorId: shoppingBag.treatmentDoctorId,
          scheinId: patientFileStore.schein.activatedSchein.scheinId,
        });
      }
    }
    goToPrintPreview();
  }, [shoppingBag]);

  const renderShoppingBagContent = useMemo(() => {
    return (
      <>
        <div className="wrapper">
          {shoppingBag?.medicines?.length ? (
            renderMedicineInShoppingBag
          ) : (
            <Flex justify={'center'} mt={16}>
              {t('noItemInRezeptPool')}
            </Flex>
          )}
        </div>

        <div className="sl-second-layer-footer">
          <Flex w="100%" justify="flex-end">
            {/* // TODO: hide feature for launch production */}
            {/* <div className="keyboard-hint">
              <span>{t('keyboardHint')}</span>
            </div> */}
            <div className="actions">
              <Button
                large
                outlined
                className="sl-shopping-bag__btn-remove"
                disabled={!shoppingBag?.medicines?.length}
                data-test-id="remove-all-btn"
                onClick={onRemoveAll}
                intent={Intent.DANGER}
                ref={btnRemoveAll}
              >
                {t('removeAll')}
              </Button>
              <Button
                large
                loading={medicationShoppingBagStore.isLoading || isPendingERP}
                intent={Intent.PRIMARY}
                disabled={isNotPrescribeAble}
                data-test-id="go-to-print-preview"
                onClick={onRequestPrescribe}
                ref={btnPrescribe}
              >
                {t('goToPrintPreview')}
              </Button>
            </div>
          </Flex>
        </div>
      </>
    );
  }, [shoppingBag, JSON.stringify(medicationShoppingBagStore), isPendingERP]);

  return (
    <Dialog
      className={`dialog-right ${className}`}
      title={`${t('title')} ${
        shoppingBag?.medicines?.length
          ? `(${shoppingBag?.medicines.length})`
          : ''
      }`}
      isOpen={openDialog}
      isCloseButtonShown
      canEscapeKeyClose={true}
      // portalClassName={`second-layer-right_wo-backdrop`}
      onClose={() => handleCloseDialog(true)}
      transitionDuration={0}
      canOutsideClickClose={false}
    >
      {shoppingBag ? renderShoppingBagContent : <LoadingState />}
      {renderEditDrugName}
      {renderModalConfirmRemoveAll}
    </Dialog>
  );
}

export default memo(
  I18n.withTranslation(MedicationShoppingBagMemo, {
    namespace: 'Medication',
    nestedTrans: 'MedicationShoppingBag',
  })
);
