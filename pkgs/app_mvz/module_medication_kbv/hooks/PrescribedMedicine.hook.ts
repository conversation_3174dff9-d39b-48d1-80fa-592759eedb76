import usePrescribedMedicineHookBase from '@tutum/mvz/module_medication/hooks/PrescribedMedicine.hook';
export interface IPrescribeMedicineHookRequest {
  patientId: string;
}

function usePrescribedMedicineHook(request: IPrescribeMedicineHookRequest) {
  const [data] = usePrescribedMedicineHookBase.usePrescribedMedicineHook({
    patientId: request.patientId,
    order: null!,
    sortField: null!,
  });

  return data;
}

export default { usePrescribedMedicineHook };
