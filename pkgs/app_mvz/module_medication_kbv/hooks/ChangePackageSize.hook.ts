import type { Medicine } from '@tutum/hermes/bff/legacy/medicine_common';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useMedicationPackageSizeStore } from '../medication/MedicationKBV.store';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  getMedicationByPzn,
  getMedicationsByPackageSizeForPriceComparison,
  Sort,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';

import type { IMenuItem } from '@tutum/design-system/components';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';

interface ISelectedPackageSize extends IMenuItem {
  priceComparisonGroupId: number;
}

async function fetchMedicationByPackageSizeAPI(
  pzn: string,
  ikNumber: number,
  isSvPatient: boolean,
  contractId?: string,
  isPrivateSchein?: boolean
) {
  return getMedicationByPzn({
    pzn,
    ikNumber,
    isSvPatient,
    contractId,
    referenceDate: datetimeUtil.dateTimeFormat(
      datetimeUtil.now(),
      YEAR_MONTH_DAY_FORMAT
    ),
    patientId: patientFileStore?.patient?.current?.id,
    isPrivateSchein,
  });
}

async function fetchMedicationsForPriceComparationAPI(
  currentSelectedPackgeSize: ISelectedPackageSize,
  currentMedicineProductId: number,
  ikNumber: number,
  isSvPatient: boolean,
  contractId?: string,
  _page?: number,
  _pageSize?: number,
  _sort?: Sort,
  isPrivateSchein?: boolean
) {
  if (!currentSelectedPackgeSize) return;

  return await getMedicationsByPackageSizeForPriceComparison({
    priceComparisonGroupId: currentSelectedPackgeSize.priceComparisonGroupId,
    medicineProductId: currentMedicineProductId,
    page: _page,
    pageSize: _pageSize,
    sort: _sort,
    ikNumber,
    isSvPatient,
    contractId,
    referenceDate: datetimeUtil.dateTimeFormat(
      datetimeUtil.now(),
      YEAR_MONTH_DAY_FORMAT
    ),
    patientId: patientFileStore?.patient?.current?.id,
    isPrivateSchein,
  });
}

export function useChangeKBVPackageSize(
  currentMedicineProductId: number,
  ikNumber: number,
  isSvPatient: boolean,
  contractId?: string,
  defaultPzn?: string,
  isPrivateSchein?: boolean
) {
  const packageSizeStore = useMedicationPackageSizeStore();

  const [isFetchingComparableMedications, setFetching] = useState(false);

  const [isFetchingCurrentMedication, setIsFetchingCurrentMedication] =
    useState(false);

  const isFetching = useMemo(
    () => isFetchingCurrentMedication || isFetchingComparableMedications,
    [isFetchingCurrentMedication, isFetchingComparableMedications]
  );

  const [currentMedication, setCurrentMedication] = useState<Medicine | null>(
    null
  );

  const [priceComparableMedications, setPriceComparableMedications] = useState<
    Medicine[]
  >([]);

  const [totalPriceComparable, setTotalPriceComparable] = useState(0);

  const [selectedPackageSize, setSelectedPackageSize] =
    useState<ISelectedPackageSize | null>(null);

  const fetchPriceComparableMedications = useCallback(
    async (_pageNo?: number, _pageSize?: number, _sortType?: Sort) => {
      try {
        setFetching(true);
        const resp = await fetchMedicationsForPriceComparationAPI(
          selectedPackageSize!,
          currentMedicineProductId,
          ikNumber,
          isSvPatient,
          contractId,
          _pageNo,
          _pageSize,
          _sortType,
          isPrivateSchein
        );
        setPriceComparableMedications(resp?.data.medicines!);
        setTotalPriceComparable(resp?.data.total!);
      } catch (error) {
        setPriceComparableMedications([]);
        setTotalPriceComparable(0);
      } finally {
        setFetching(false);
      }
    },
    [selectedPackageSize, currentMedicineProductId, isPrivateSchein]
  );

  const fetchCurrentMedicationOnPackageSizeChange = useCallback(
    async (packageSize: ISelectedPackageSize) => {
      if (!packageSize?.value) return;
      const pzn = packageSize.value.toString();
      try {
        setIsFetchingCurrentMedication(true);
        const {
          data: { medicine },
        } = await fetchMedicationByPackageSizeAPI(
          pzn,
          ikNumber,
          isSvPatient,
          contractId,
          isPrivateSchein
        );
        setCurrentMedication(medicine);
      } catch (error) {
        setCurrentMedication(null);
        throw error;
      } finally {
        setIsFetchingCurrentMedication(false);
      }
    },
    [isPrivateSchein]
  );

  const availablePackageSizes = useMemo(() => {
    if (!packageSizeStore.medicationMapWithSizes || !currentMedicineProductId) {
      return [];
    }
    return (
      packageSizeStore.medicationMapWithSizes[currentMedicineProductId] ?? []
    );
  }, [currentMedicineProductId, packageSizeStore.medicationMapWithSizes]);

  const availableComparableMedicines = useMemo(() => {
    if (!currentMedication) return priceComparableMedications;
    return priceComparableMedications?.filter(
      (med) => med.pzn !== currentMedication?.pzn
    );
  }, [priceComparableMedications, currentMedication]);

  const onPackageSizeSelected = useCallback(
    (menuItem: IMenuItem) => {
      if (!menuItem) return;

      const pzn = menuItem.value?.toString();

      const pkgSize = availablePackageSizes?.find(
        (pkgSize) => pkgSize.pzn === pzn
      );

      if (!pkgSize) return;

      setPriceComparableMedications([]);

      setSelectedPackageSize({
        label: menuItem.label,
        value: pzn,
        priceComparisonGroupId: pkgSize.priceComparisonGroupId,
      });
    },
    [availablePackageSizes]
  );

  // NOTE: set default selected package size
  useEffect(() => {
    if (!availablePackageSizes?.length) return;

    const defaultSelectedPackage =
      availablePackageSizes.find((size) => size.pzn === defaultPzn) ??
      availablePackageSizes[0];

    if (!defaultSelectedPackage) return;

    setSelectedPackageSize({
      label: defaultSelectedPackage.label,
      value: defaultSelectedPackage.pzn,
      priceComparisonGroupId: defaultSelectedPackage.priceComparisonGroupId,
    });

    return () => {
      setSelectedPackageSize(null);
    };
  }, [availablePackageSizes, defaultPzn]);

  // NOTE: fetch medication, price comparable medications for selected package size
  useEffect(() => {
    if (!selectedPackageSize || !currentMedicineProductId) return;
    fetchCurrentMedicationOnPackageSizeChange(selectedPackageSize).then(() => {
      fetchPriceComparableMedications();
    });
  }, [
    selectedPackageSize,
    currentMedicineProductId,
    fetchCurrentMedicationOnPackageSizeChange,
    fetchPriceComparableMedications,
  ]);

  return {
    isFetching,
    currentMedication,
    selectedPackageSize,
    totalPriceComparable,
    availablePackageSizes,
    isFetchingCurrentMedication,
    isFetchingComparableMedications,
    onPackageSizeSelected,
    availableComparableMedicines,
    fetchPriceComparableMedications,
  };
}
