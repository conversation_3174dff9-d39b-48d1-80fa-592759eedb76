import React, {
  memo,
  MutableRefObject,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { isEmpty } from 'lodash';
import moment, { Moment } from 'moment';

import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';

import {
  alertError,
  alertSuccessfully,
  BodyTextM,
  Flex,
  LoadingState,
  Svg,
} from '@tutum/design-system/components';
import {
  Button,
  Dialog,
  Intent,
  PopoverPosition,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { addEntry } from '@tutum/hermes/bff/legacy/app_mvz_bmp';
import {
  GetAMRDocumentLinkRequest,
  GetARVRequest,
  useQueryGetListPznAtcForHighPrescription,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import {
  AMR,
  Divisible,
  FormType,
  HandLetter,
  HintsAndWarning,
  Medicine,
  PriceInformation,
  ProductInformation,
} from '@tutum/hermes/bff/legacy/medicine_common';
import { PackageExtend } from '@tutum/hermes/bff/legacy/repo_medicine_common';
import { MedicationInformation } from '@tutum/hermes/bff/repo_bmp_common';
import {
  AddEntryRequest,
  GroupType,
} from '@tutum/hermes/bff/service_domains_bmp';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import MedicationDialog from '@tutum/mvz/module_medication/medication-plan/medication-dialog/MedicationDialog.styled';
import GBADialog from '@tutum/mvz/module_medication_kbv/medication/gba/GBADialog.styled';
import {
  secondLayerDialogActions,
  secondLayerDialogStore,
  useSecondLayerDialogStore,
} from '@tutum/mvz/module_medication_kbv/medication/second-layer/secondLayerDialog.store';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMvzTheme } from '@tutum/mvz/theme';

import type { Dialog as DialogType } from '@tutum/design-system/components/Core';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { getAge } from '@tutum/design-system/infrastructure/utils';
import { MedicationPrescribeResponse } from '@tutum/hermes/bff/app_mvz_medicine';
import { AmrTypeCode } from '@tutum/hermes/bff/legacy/medicine_common';
import {
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { renderComparePriceIcon } from '@tutum/mvz/module_medication_kbv/medication/medication-table/setting-table';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { createTimelineItem } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.service';
import PrescribedMedicineHook from '../../hooks/PrescribedMedicine.hook';
import { BASE_PATH_MVZ } from '@tutum/infrastructure/utils/string.util';
import { isVisible } from '../../utils/dom';
import { medicationUtil } from '../../utils/medication-util';
import ArmInforCollapsible from '../arm-information-collapsable/ArmInformationCollapsable.syled';
import {
  getOtcOtxTag,
  isDispensingTypeCode2,
  isPatientOver65Age,
  isShowPrescribableMedicalProduct,
} from '../components/icons-cell';
import IWWListeItemCollapsible from '../iww-regulations/IWWListeItemCollapsible.styled';
import { SecondaryViewType } from '../MedicationKBV';
import { medicationActions, useMedicationStore } from '../MedicationKBV.store';
import { MediInfoType } from '../secondary-view/SecondaryViewDialog';
import SecondaryViewDialog from '../secondary-view/SecondaryViewDialog.styled';
import { GlobalStyle2ndLayer } from './SecondLayerDialog.styled';
import { AutoTextSize } from 'auto-text-size';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';

const ThumbsDownIcon = '/images/thumbs-down.svg';
const IconPharmacy = '/images/icons_medicationfbv/pharmacy-require.svg';
const AlertCircleSolidBig = '/images/alert-circle-solid-big.svg';
const FileSignature = '/images/file-signature.svg';
const AlertCircle = '/images/alert-circle-solid-purple.svg';
const RedHandIcon = '/images/red-hand.svg';
const BlueHandIcon = '/images/blue-hand.svg';
const BandageIcon = '/images/bandage.svg';

export interface ISecondLayerDialogProps {
  className?: string;
  theme?: IMvzTheme;
  isOpen: boolean;
  shouldStopClickOutside: boolean;
  handleCloseDialog: () => void;
  pzn: string;
  ikNumber: string;
  isSvPatient: boolean;
  isSubstitution: boolean;
  hintsAndWarnings: HintsAndWarning[] | undefined;
  handlePriceComparison: (input) => void;
  handleAlternatives: (input) => void;
  patient?: IPatientProfile;
  selectedContractDoctor: ISelectedContractDoctor;
  bsnr?: string;
  lanr?: string;
  setShowShoppingBag: Function;
  isMP: boolean;
  doctorId?: string;
  onClickMedicationLink: (atcCode: string) => void;
  closeDialogWhileAddedBmp: () => void;
  isPrivateSchein: boolean;
  parentViewType?: SecondaryViewType;
  handleAddShoppingBagAfter?: () => void;
  handleRedHand?: (handLetters?: HandLetter[]) => void;
  handleBlueHand?: (handLetters?: HandLetter[]) => void;
}

const pricusDoc = `${BASE_PATH_MVZ}/data/kbv-medication/PRISCUS_2011.pdf`;

const SecondLayerDialog = ({
  parentViewType,
  className,
  isOpen,
  shouldStopClickOutside,
  handleCloseDialog,
  pzn,
  isSvPatient,
  isSubstitution,
  ikNumber,
  hintsAndWarnings,
  handlePriceComparison,
  handleAlternatives,
  patient,
  selectedContractDoctor,
  bsnr,
  lanr,
  setShowShoppingBag,
  isMP,
  doctorId,
  onClickMedicationLink,
  closeDialogWhileAddedBmp,
  isPrivateSchein,
  handleAddShoppingBagAfter,
  handleRedHand,
  handleBlueHand,
}: ISecondLayerDialogProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof MedicationI18n.SecondLayerDialog
  >({
    namespace: 'Medication',
    nestedTrans: 'SecondLayerDialog',
  });
  const { t: tMedication } = I18n.useTranslation<
    keyof typeof MedicationI18n.MedicationTable
  >({
    namespace: 'Medication',
    nestedTrans: 'MedicationTable',
  });

  const medicationContext = useContext(MedicationContext);

  const secondLayerDialogRef = useRef<DialogType | null>(null);

  const [secondaryViewType, setSecondaryViewType] =
    useState<MediInfoType | null>(null);
  const [openGBA, setOpenGBA] = useState(false);
  const [openBmpMedicationDialog, setOpenBmpMedicationDialog] = useState(false);
  const [isAlternative, setIsAlternative] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const store = useSecondLayerDialogStore();

  const medStore = useMedicationStore();

  const { medicationInfo } = store;
  const isOver65Age = isPatientOver65Age(patient?.patientInfo);
  const canOutsideClickClose = !(
    !isOpen ||
    secondaryViewType !== null ||
    (secondLayerDialogStore.GBA && openGBA) ||
    openBmpMedicationDialog ||
    store.openPriscusMedicationWarning ||
    shouldStopClickOutside
  );

  const addMPRef: MutableRefObject<any> = useRef();
  const addRPRef: MutableRefObject<any> = useRef();

  const currentSchein = useCurrentSchein();

  const { patientManagement, getActiveParticipation } = useContext(
    PatientManagementContext.instance
  );
  const activeParticipate = getActiveParticipation();

  const isEnablePznAtcForHighPrescription = Boolean(
    activeParticipate?.contractId
  );
  const {
    data,
    isLoading: isLoadingGetListPznAtcForHighPrescription,
    isSuccess: isSuccessGetListPznAtcForHighPrescription,
  } = useQueryGetListPznAtcForHighPrescription(
    {
      contractId: activeParticipate?.contractId!,
    },
    {
      enabled: isEnablePznAtcForHighPrescription,
    }
  );

  const isCheckPznAtcPass =
    !isEnablePznAtcForHighPrescription ||
    !isLoadingGetListPznAtcForHighPrescription;

  const pznAtcCode = data?.data?.find((c) => c.pzn === medicationInfo?.pzn);

  const [isShowPopupConfirm, setIsShowPopupConfirm] = useState(false);
  const [openPopupConfirmPznAtc, setOpenPopupConfirmPznAtc] = useState(false);
  const [endDate, setEndDate] = useState<Nullable<Moment>>(null);

  const prescribedMedicines = PrescribedMedicineHook.usePrescribedMedicineHook({
    patientId: patient?.id!,
  });
  let medicines: MedicationPrescribeResponse[] = [];
  if (isSuccessGetListPznAtcForHighPrescription && pznAtcCode) {
    medicines = prescribedMedicines.filter(
      (m) => m.drugInformation?.aTC === pznAtcCode.atcCode
    );
  }
  useEffect(() => {
    const medicine = medicines.find((m) => {
      if (m.intakeInterval.dJ) {
        return false;
      }
      const {
        morning = 0,
        afternoon = 0,
        evening = 0,
        night = 0,
      } = m.intakeInterval;

      const totalInterval = morning + afternoon + evening + night;
      const totalDays = Math.ceil(
        (m.quantity * (m.packagingInformation?.quantityNumber || 1)) /
          totalInterval
      );
      const endDate = datetimeUtil.startOf(
          moment(m.prescribeDate).add(totalDays, 'days').toDate(),
          'day'
        ),
        now = moment(datetimeUtil.getNowStartDay());
      const diffDays = endDate.diff(now, 'days');
      if (diffDays >= 20) {
        setEndDate(endDate);
        return true;
      }
      return false;
    });
    setIsShowPopupConfirm(Boolean(medicine));
    if (!medicine) {
      setEndDate(null);
    }
  }, [medicines, pznAtcCode]);

  useHotkeys('ctrl+m', (event) => {
    event.preventDefault();
    if (
      !addMPRef?.current?.buttonRef ||
      !isVisible(addMPRef.current?.buttonRef)
    ) {
      return;
    }
    addMPRef?.current?.buttonRef?.click();
  });

  useHotkeys('ctrl+r', (event) => {
    event.preventDefault();
    if (
      !addRPRef?.current?.buttonRef ||
      !isVisible(addRPRef.current?.buttonRef)
    ) {
      return;
    }
    addRPRef?.current?.buttonRef?.click();
  });

  useEffect(() => {
    if (bsnr && lanr && pzn) {
      medicationActions.getAltViewList({
        bsnr,
        lanr,
        pzn,
      });
    }
  }, [bsnr, lanr, pzn]);

  useEffect(() => {
    if (medStore.alternative?.dataARV?.length) {
      setIsAlternative(true);
    } else {
      setIsAlternative(false);
    }
  }, [medStore.alternative?.dataARV]);

  useEffect(() => {
    if (pzn && ikNumber) {
      secondLayerDialogActions.getById(
        pzn,
        ikNumber,
        patient,
        isSvPatient,
        selectedContractDoctor,
        bsnr,
        lanr,
        isPrivateSchein,
        currentSchein
      );
    }
  }, [
    pzn,
    ikNumber,
    patient,
    selectedContractDoctor,
    bsnr,
    lanr,
    isPrivateSchein,
    currentSchein,
  ]);

  useEffect(() => {
    if (!openGBA && pzn) {
      secondLayerDialogActions.getBGAInformation(setOpenGBA, pzn, true);
    }
  }, [pzn, openGBA]);

  const Information = (props) => {
    const { title, content } = props;

    if (isEmpty(content)) {
      return null;
    }

    return (
      <Flex key={'general-info_1_' + title} className="general-info">
        <div className="info-title">{title}</div>
        <div className="info-content">
          {content instanceof Array ? content.join(', ') : content}
        </div>
      </Flex>
    );
  };

  const getDivisibleInfo = (divisible?: Divisible) => {
    if (divisible?.dIVISIBLE_FLAG) {
      return t('yes');
    } else {
      return t('no');
    }
  };

  const renderFormTypeBadge = (productInformation?: ProductInformation) => {
    switch (productInformation?.formType) {
      case FormType.BTM:
        return (
          <Tooltip placement="top" content={tMedication('tooltipBTM')}>
            <div className="info-tag b-tag">BTM</div>
          </Tooltip>
        );
      case FormType.TPrescription:
        return (
          <Tooltip placement="top" content={tMedication('tooltipTRez')}>
            <div className="info-tag t-tag">T-Rez</div>
          </Tooltip>
        );
      case FormType.GREZ:
        return (
          <Tooltip placement="top" content={tMedication('tooltipGRez')}>
            <div className="info-tag g-tag">G-Rez</div>
          </Tooltip>
        );
      default:
        return null;
    }
  };

  const contentLifeStyle = {
    '1': tMedication('tooltipLifestyle'),
    '2': tMedication('conditionalTooltipLifestyle'),
  };

  const labelLifeStyle = {
    '1': tMedication('labelLifeStyle'),
    '2': tMedication('labelConditionalLifestyle'),
  };

  const getAMRDocumentLink = (payload: GetAMRDocumentLinkRequest) => {
    medicationActions.getAMRDocumentLink({
      filename: payload.filename,
      amrTypeCode: payload.amrTypeCode,
    });
  };

  const reducedAMRs = useMemo(() => {
    if (!store.amrInformation) return [];
    return store.amrInformation.reduce((acc: AMR[], curr) => {
      if (
        acc?.find((item) => item.rEGULATIONTYPECODE === curr.rEGULATIONTYPECODE)
      ) {
        return acc;
      }
      return [...acc, curr];
    }, []);
  }, [store.amrInformation]);

  const getANLInfo = useCallback((arm: AMR) => {
    switch (arm?.rEGULATIONTYPECODE) {
      case '1': {
        const version = arm?.tITLE.split(':')[0].split(' ')[1];
        return {
          text: `ANL ${version}`,
          tooltip: `${tMedication('tooltipAMR')} ${version}`,
        };
      }
      case '3':
        return {
          text: 'ANL III',
          tooltip: tMedication('tooltipAMRIII'),
        };
      case '4':
        return {
          text: 'ANL IV',
          tooltip: tMedication('tooltipAMRIV'),
        };
      case '5':
        return {
          text: 'ANL V',
          tooltip: tMedication('tooltipAMRV'),
        };
      default:
        return null;
    }
  }, []);

  const renderImportantInformation = useMemo(() => {
    const { isShowOtcOtx, otcOtxText, tooltipOtcOtxText } = getOtcOtxTag(
      medicationInfo,
      tMedication
    );

    return (
      <Flex flexWrap gap={8}>
        {!!medicationInfo?.redHandLetters?.[0] && (
          <Tooltip
            content={tMedication('viewRedHandLetters')}
            position={PopoverPosition.TOP}
          >
            <Svg
              className="info-tag info-tag--no-border cursor-pointer"
              src={RedHandIcon}
              onClick={() => handleRedHand?.(medicationInfo.redHandLetters)}
            />
          </Tooltip>
        )}
        {!!medicationInfo?.blueHandLetters?.[0] && (
          <Tooltip
            content={tMedication('viewBlueHandLetters')}
            position={PopoverPosition.TOP}
          >
            <Svg
              className="info-tag info-tag--no-border cursor-pointer"
              src={BlueHandIcon}
              onClick={() => handleBlueHand?.(medicationInfo.blueHandLetters)}
            />
          </Tooltip>
        )}
        {medicationInfo?.productInformation?.isBandage && (
          <Tooltip
            content={tMedication('tooltipBandage')}
            position={PopoverPosition.TOP}
          >
            <Svg className="info-tag info-tag--no-border" src={BandageIcon} />
          </Tooltip>
        )}
        {!!medicationInfo?.colorCategory?.isInPriscusList && (
          <Tooltip
            placement="top"
            content={
              isOver65Age
                ? tMedication('tooltipPOver65Age')
                : tMedication('tooltipP')
            }
          >
            <div
              className={`info-tag ${isOver65Age ? 'p-tag-red' : 'p-tag-gray'}`}
            >
              P
            </div>
          </Tooltip>
        )}
        {!!medicationInfo?.productInformation?.pharmacyRequired && (
          <Tooltip placement="top" content={tMedication('tooltipPharmacy')}>
            <div
              className="info-tag info-tag--no-border"
              style={{ borderColor: 'red', paddingTop: 6, paddingBottom: 2 }}
            >
              <Svg src={IconPharmacy} />
            </div>
          </Tooltip>
        )}
        {!!medicationInfo?.productInformation?.negativeList && (
          <Tooltip placement="top" content={tMedication('tooltipThumbdown')}>
            <div className="info-tag">
              <Svg src={ThumbsDownIcon} />
            </div>
          </Tooltip>
        )}
        {!!medicationInfo?.productInformation?.lifeStyle && (
          <Tooltip
            placement="top"
            content={
              contentLifeStyle[medicationInfo.productInformation.lifeStyle]
            }
          >
            <div className="info-tag">
              {labelLifeStyle[medicationInfo.productInformation.lifeStyle]}
            </div>
          </Tooltip>
        )}
        {!!medicationInfo?.productInformation?.isExistingInGBA && (
          <Tooltip placement="top" content={tMedication('tooltipGPA')}>
            <div className="info-tag">G-BA</div>
          </Tooltip>
        )}
        {reducedAMRs.map((item) => {
          const anlInfo = getANLInfo(item);
          if (!anlInfo) return null;
          return (
            <Tooltip
              key={item.tITLE}
              placement="top"
              content={anlInfo?.tooltip}
            >
              <div className="info-tag">{anlInfo?.text}</div>
            </Tooltip>
          );
        })}
        {store.medicationInfo?.productInformation?.hasArv && (
          <Tooltip placement="top" content={tMedication('tooltipARV')}>
            <div className="info-tag">ARV</div>
          </Tooltip>
        )}
        {isShowOtcOtx && (
          <Tooltip placement="top" content={tooltipOtcOtxText}>
            <div className="info-tag">{otcOtxText}</div>
          </Tooltip>
        )}
        {!!medicationInfo?.productInformation?.transfusionlawFlag && (
          <Tooltip placement="top" content={tMedication('tooltipTFG')}>
            <div className="info-tag">TFG</div>
          </Tooltip>
        )}
        {!!medicationInfo?.productInformation?.importProductFlag && (
          <Tooltip
            placement="top"
            content={tMedication('tooltipImportProduct')}
          >
            <div className="info-tag">Importprodukt</div>
          </Tooltip>
        )}
        {isShowPrescribableMedicalProduct(
          medicationInfo?.productInformation
        ) && (
          <div className="info-tag">verordnungsfähiges Medizinprodukt</div> // Prescribable Medical product
        )}
        {!!medicationInfo?.productInformation?.medicineProductFlag &&
          !isShowPrescribableMedicalProduct(
            medicationInfo?.productInformation
          ) && (
            <div className="info-tag">Medizinprodukt</div> // Medicine product
          )}
        {!!medicationInfo?.productInformation?.sampleProductFlag && (
          <Tooltip placement="top" content={tMedication('tooltipAM')}>
            <div className="info-tag">Ärtzmuster</div>
          </Tooltip>
        )}
        {renderFormTypeBadge(medicationInfo?.productInformation)}
        {!!medicationInfo?.productInformation?.pharmaciesAvailability && (
          <Tooltip placement="top" content={tMedication('tooltipAV')}>
            <div className="av-tag info-tag">AV</div>
          </Tooltip>
        )}
        {isDispensingTypeCode2(medicationInfo?.productInformation) && (
          <Tooltip
            placement="top"
            content={tMedication('tooltipPrescriptionRequired')}
          >
            <div className="info-tag info-tag--no-border">
              <Svg src={FileSignature} />
            </div>
          </Tooltip>
        )}
      </Flex>
    );
  }, [medicationInfo, store.amrInformation]);

  const addMedicineToShoppingBag = async () => {
    let defaultFormType: Nullable<FormType> = undefined;
    const success = () => {
      alertSuccessfully(t('addToShoppingBag'));
    };
    const contractId = activeParticipate?.contractId;
    if (contractId) {
      const isSupportVSST1212 =
        await webWorkerServices.doesContractSupportFunctions(
          ['VSST1212'],
          contractId
        );
      const patientAge = patientManagement.patient?.dateOfBirth
        ? getAge(new Date(patientManagement.patient.dateOfBirth))
        : -1;
      if (
        isSupportVSST1212 &&
        patientAge >= 12 &&
        patientAge <= 18 &&
        (medicationInfo?.productInformation?.isNegativeList ||
          medicationInfo?.productInformation?.isOTC ||
          medicationInfo?.colorCategory?.isInPriscusList ||
          !!medicationInfo?.productInformation?.lifeStyle)
      ) {
        defaultFormType = FormType.KREZ;
      }
    }
    const unitName =
      medicationContext.units.find(
        (item) =>
          item.code ===
            medicationInfo?.medicationPlanInformation?.medicationPlanUnitCode ||
          item.name ===
            medicationInfo?.medicationPlanInformation?.medicationPlanUnitCode ||
          item.description ===
            medicationInfo?.medicationPlanInformation?.medicationPlanUnitCode
      )?.name || '';
    secondLayerDialogActions.addMedicineToBag(
      isSvPatient,
      {
        ...medicationInfo,
        medicationPlanInformation: { medicationPlanUnitCode: unitName },
      } as Medicine,
      success,
      setShowShoppingBag,
      handleCloseDialog,
      defaultFormType,
      bsnr,
      doctorId
    );
    handleAddShoppingBagAfter?.();
  };

  const handleAddMedicineToShoppingBag = async () => {
    try {
      setIsLoading(true);
      if (parentViewType !== SecondaryViewType.PriceComparison) {
        const hasSubstitution = await medicationActions.hasSubstitution({
          pzn,
          ikNumber,
          contractId: selectedContractDoctor?.contractId,
          referenceDate: datetimeUtil.dateTimeFormat(
            datetimeUtil.now(),
            YEAR_MONTH_DAY_FORMAT
          ),
        });
        if (hasSubstitution) {
          handlePriceComparison({
            pzn,
            ikNumber,
            priceComparisonGroup:
              medicationInfo?.priceInformation?.priceComparisonGroup2,
          });
          return;
        }
      }

      if (medicationInfo?.colorCategory?.isInPriscusList) {
        if (isOver65Age) {
          secondLayerDialogActions.setOpenPriscusMedicationWarning(true);
          return;
        }
      }
      await addMedicineToShoppingBag();
    } finally {
      setIsLoading(false);
      setOpenPopupConfirmPznAtc(false);
    }
  };

  const handleOpenMedicationDialog = () => {
    setOpenBmpMedicationDialog(true);
  };

  const handleCloseMedicationDialog = () => {
    setOpenBmpMedicationDialog(false);
  };

  const handleSaveMedication = (
    medicationInformation: MedicationInformation
  ) => {
    const requestAddEntry: AddEntryRequest = {
      patientId: patient?.id!,
      contractId: selectedContractDoctor.contractId,
      doctorId: selectedContractDoctor.doctorId!,
      encounterCase: selectedContractDoctor.encounterCase!,
      type: GroupType.MEDICATION,
      medicationInformation: {
        ...medicationInformation,
        isKBVMedication: true,
      },
    };
    addEntry(requestAddEntry)
      .then(() => {
        alertSuccessfully(t('updateSuccess'));
        closeDialogWhileAddedBmp();
      })
      .finally(() => {});
  };

  const handleIgnorePriscusMedication = async () => {
    await addMedicineToShoppingBag();
    secondLayerDialogActions.setOpenPriscusMedicationWarning(false);
  };

  const handleSelectAnotherPriscusMedication = () => {
    secondLayerDialogActions.setOpenPriscusMedicationWarning(false);
  };

  const renderPriscusMedicationWarning = () => {
    return (
      <Dialog
        isOpen={store.openPriscusMedicationWarning}
        className={`${className}`}
        canOutsideClickClose={false}
      >
        <div className="p-warning-dialog">
          <div className="p-warning-dialog-wrapper">
            <Flex>
              <div>
                <Svg src={AlertCircleSolidBig} />
              </div>
              <div className="title">{t('attention')}</div>
            </Flex>
            <div className="content">{t('priscusMedicationWarning')}</div>
            <Flex className="p-warning-dialog-actions">
              <Button
                className="p-warning-dialog-actions-btn"
                style={{ display: 'flex' }}
                onClick={(event) => {
                  event.stopPropagation();
                  handleIgnorePriscusMedication();
                }}
              >
                {t('ignore')}
              </Button>
              <Button
                className="p-warning-dialog-actions-btn select-other"
                intent={Intent.PRIMARY}
                style={{ display: 'flex' }}
                onClick={(event) => {
                  event.stopPropagation();
                  handleSelectAnotherPriscusMedication();
                }}
              >
                {t('selectAnotherOne')}
              </Button>
            </Flex>
          </div>
        </div>
      </Dialog>
    );
  };

  const memoizedPriceInfo:
    | (PriceInformation & {
        isShowCopayment: boolean;
        isShowNoPayment: boolean;
      })
    | undefined = useMemo(() => {
    if (!medicationInfo?.priceInformation) return undefined;

    const { additionalPayment, copayment, svPrices } =
      medicationInfo.priceInformation;

    const isShowNoPayment = !svPrices && copayment === -1;
    const isShowCopayment =
      !svPrices && !isShowNoPayment && copayment - additionalPayment >= 0;

    return {
      ...medicationInfo?.priceInformation,
      isShowCopayment,
      isShowNoPayment,
    };
  }, [medicationInfo?.priceInformation]);

  const addToTimeline = useCallback(async () => {
    try {
      setIsLoading(true);
      const payload = {
        patientId: patient?.id,
        contractId: selectedContractDoctor.contractId,
        treatmentDoctorId: selectedContractDoctor?.doctorId,
        encounterMedicine: medicationInfo,
        type: TimelineEntityType.TimelineEntityType_Medicine,
        auditLogs: [],
        isActive: true,
      };

      await createTimelineItem(payload as unknown as TimelineModel);

      alertSuccessfully(t('doctorSampleAddedToTimeline'));
      handleCloseDialog();
    } catch (error) {
      alertError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [medicationInfo]);

  const hasHeaderAccessOtherView = useMemo(() => {
    return !!(
      medicationInfo?.priceInformation?.priceComparisonGroup2 ||
      (medicationInfo?.productInformation &&
        medicationInfo.textInformation?.items?.length) ||
      medicationInfo?.productInformation?.techInformationId ||
      isAlternative ||
      store.medicationInfo?.productInformation?.isExistingInGBA ||
      medicationInfo?.colorCategory?.isInPriscusList
    );
  }, [
    medicationInfo?.priceInformation?.priceComparisonGroup2,
    medicationInfo?.productInformation,
    medicationInfo?.textInformation?.items,
    medicationInfo?.productInformation?.techInformationId,
    isAlternative,
    store.medicationInfo?.productInformation?.isExistingInGBA,
    medicationInfo?.colorCategory?.isInPriscusList,
  ]);

  function renderPriceInformation(
    priceInfo: PriceInformation | undefined,
    title: string,
    medicationInfo: Medicine | undefined,
    tMedication: IFixedNamespaceTFunction<
      keyof typeof MedicationI18n.MedicationTable
    >
  ) {
    const content = priceInfo?.svPrices
      ? priceInfo?.svPrices.pharmacySalePrice
      : priceInfo?.pharmacySalePrice
        ? `${medicationUtil.transformPrice(priceInfo?.pharmacySalePrice)}`
        : '';

    const comparePriceIcon = priceInfo?.svPrices
      ? null
      : renderComparePriceIcon(medicationInfo?.priceInformation, tMedication);

    return (
      <Flex>
        <Information title={title} content={content} />
        {comparePriceIcon && (
          <div style={{ marginLeft: '5px' }}>{comparePriceIcon}</div>
        )}
      </Flex>
    );
  }

  const renderFixedPriceInformation = (
    priceInfo: PriceInformation | undefined,
    title: string
  ) => {
    const content = priceInfo?.svPrices
      ? priceInfo.svPrices.priceFixed
      : priceInfo?.priceFixedStr
        ? priceInfo.priceFixedStr === '-'
          ? '-'
          : +priceInfo.priceFixedStr
            ? `${medicationUtil.transformPrice(+priceInfo.priceFixedStr)}`
            : ''
        : '';

    return <Information title={title} content={content} />;
  };

  const renderMedicationInfo = useMemo(() => {
    return (
      <>
        <div className="wrapper">
          {hasHeaderAccessOtherView && (
            <div className="information-block">
              <div className="title">{t('headerAccessOtherView')}</div>
              <Flex flexWrap gap={8} mt={8}>
                {!!medicationInfo?.priceInformation?.priceComparisonGroup2 && (
                  <Button
                    data-test-id="price-comparison"
                    intent={Intent.NONE}
                    small
                    outlined
                    minimal
                    onClick={() =>
                      handlePriceComparison({
                        pzn,
                        ikNumber,
                        priceComparisonGroup:
                          medicationInfo?.priceInformation
                            ?.priceComparisonGroup2,
                      })
                    }
                  >
                    {t('btnPriceComparison')}
                  </Button>
                )}
                {!!medicationInfo?.productInformation &&
                  !!medicationInfo.textInformation?.items?.length && (
                    <Button
                      data-test-id="composition"
                      intent={Intent.NONE}
                      small
                      outlined
                      minimal
                      onClick={() =>
                        setSecondaryViewType(MediInfoType.Zusammensetzung)
                      }
                    >
                      {t('btnComposition')}
                    </Button>
                  )}
                {Boolean(
                  medicationInfo?.productInformation?.techInformationId
                ) && (
                  <Button
                    data-test-id="technical-information"
                    intent={Intent.NONE}
                    small
                    outlined
                    minimal
                    onClick={() =>
                      setSecondaryViewType(MediInfoType.Fachinformation)
                    }
                  >
                    {t('btnTechnicalInformation')}
                  </Button>
                )}
                {Boolean(
                  medicationInfo?.productInformation?.therapyHintName
                ) && (
                  <Button
                    data-test-id="therapy-hint"
                    intent={Intent.NONE}
                    small
                    outlined
                    minimal
                    onClick={() => {
                      getAMRDocumentLink({
                        filename:
                          medicationInfo?.productInformation?.therapyHintName!,
                        amrTypeCode: AmrTypeCode.TH,
                      });
                    }}
                  >
                    {t('btnTherapyHint')}
                  </Button>
                )}
                {isAlternative && (
                  <Button
                    data-test-id="alternatives"
                    intent={Intent.NONE}
                    small
                    outlined
                    minimal
                    onClick={() =>
                      handleAlternatives({
                        bsnr,
                        lanr,
                        pzn,
                      } as GetARVRequest)
                    }
                  >
                    {t('btnAlternatives')}
                  </Button>
                )}
                {store.medicationInfo?.productInformation?.isExistingInGBA && (
                  <Button
                    data-test-id="gba-decision"
                    intent={Intent.NONE}
                    small
                    outlined
                    minimal
                    onClick={() => {
                      secondLayerDialogActions.getBGAInformation(setOpenGBA);
                    }}
                  >
                    {t('btnGBADecision')}
                  </Button>
                )}
                {medicationInfo?.colorCategory?.isInPriscusList && (
                  <Button
                    data-test-id="price-list"
                    intent={Intent.NONE}
                    small
                    outlined
                    minimal
                  >
                    <a
                      style={{ all: 'unset' }}
                      href={pricusDoc}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {t('btnPriscusList')}
                    </a>
                  </Button>
                )}
                {store.medicationInfo?.productInformation
                  ?.praxisbesonderheitDocument && (
                  <Button
                    data-test-id="practice-specifics"
                    intent={Intent.NONE}
                    small
                    outlined
                    minimal
                    onClick={() => {
                      const { fileName, amrTypeCode } =
                        store.medicationInfo?.productInformation
                          ?.praxisbesonderheitDocument || {};
                      getAMRDocumentLink({
                        filename: fileName!,
                        amrTypeCode: amrTypeCode as AmrTypeCode,
                      });
                    }}
                  >
                    {t('btnPracticeSpecifics')}
                  </Button>
                )}
              </Flex>
            </div>
          )}

          <div className="information-block">
            <div className="title">{t('headerImportanceInformation')}</div>
            <div>{renderImportantInformation}</div>
            {/*General information section*/}
            <div className="sub-title">{t('headerGeneralInfo')}</div>
            <div>
              <Information title="pzn" content={medicationInfo?.pzn} />
              <Information
                title={t('lblAtcCode')}
                content={medicationInfo?.drugInformation?.aTC}
              />
              <Information
                title={t('lblDosageForm')}
                content={medicationInfo?.productInformation?.dosageForm}
              />
              <Information
                title={t('lblActiveIngredient')}
                content={medicationInfo?.productInformation?.activeIngredients}
              />
              <Information
                title={t('lblOtherIngredients')}
                content={medicationInfo?.productInformation?.orderComponents?.join(
                  ', '
                )}
              />
              <Information
                title={t('lblManufacturer')}
                content={medicationInfo?.productInformation?.provider}
              />
              <Information
                title={t('lblDivisible')}
                content={getDivisibleInfo(
                  medicationInfo?.productInformation?.divisible
                )}
              />
            </div>
            {/*Pricing Section*/}
            <div className="sub-title">{t('headerPricing')}</div>
            <div>
              <Flex>
                {renderPriceInformation(
                  memoizedPriceInfo,
                  t('lblPrice'),
                  medicationInfo,
                  tMedication
                )}
              </Flex>
              {renderFixedPriceInformation(
                memoizedPriceInfo,
                t('lblFixedPrice')
              )}
              {memoizedPriceInfo?.pricelist?.map((item) => (
                <Information
                  key={item?.priceType}
                  title={t('discount130b')}
                  content={`${medicationUtil.transformPrice(
                    (item?.value || 0) / 100
                  )}`}
                />
              ))}
            </div>
            {Boolean(
              memoizedPriceInfo?.svPrices ||
                memoizedPriceInfo?.isShowCopayment ||
                memoizedPriceInfo?.isShowNoPayment
            ) && <div className="sub-title">{t('patientCosts')}</div>}
            {memoizedPriceInfo?.svPrices && (
              <Information
                title={t('lblCoPayment')}
                content={memoizedPriceInfo?.svPrices.copayment}
              />
            )}
            {memoizedPriceInfo?.isShowNoPayment && (
              <Information
                title={t('lblCoPayment')}
                content={t('noCopayment')}
              />
            )}
            {memoizedPriceInfo?.isShowCopayment &&
            medicationInfo?.packageExtend?.isConfidentialReimbursementAmount ? (
              <Information
                title={t('lblTotalCoPayment')}
                content={`${medicationUtil.transformPrice(
                  medicationInfo?.packageExtend?.confidentialReimbursementAmount
                )}`}
              />
            ) : memoizedPriceInfo ? (
              <React.Fragment>
                <Information
                  title={t('lblCoPayment')}
                  content={
                    memoizedPriceInfo?.isFreeCopayment
                      ? t('freeCopayment')
                      : medicationUtil.transformPrice(
                          memoizedPriceInfo?.copayment
                        )
                  }
                />
                <Information
                  title={t('lblAdditionalCosts')}
                  content={`${medicationUtil.transformPrice(
                    memoizedPriceInfo?.additionalCost || 0
                  )}`}
                />
                <Information
                  title={t('lblTotalCoPayment')}
                  content={`${medicationUtil.transformPrice(
                    memoizedPriceInfo?.totalCoPayment || 0
                  )}`}
                />
              </React.Fragment>
            ) : null}
          </div>

          {/*Text information section*/}
          {!!store.amrInformation?.length && (
            <ArmInforCollapsible
              t={t}
              armInformation={store.amrInformation}
              getAMRDocumentLink={getAMRDocumentLink}
            />
          )}
          {medStore.iwwListe?.details?.map((iwwListeItem) => (
            <IWWListeItemCollapsible
              key={iwwListeItem.aRVId}
              item={iwwListeItem}
              onClickMedicationLink={onClickMedicationLink}
            />
          ))}
          {(!isEmpty(medicationInfo?.hintsAndWarnings) || (
            !isEmpty(hintsAndWarnings) && isSubstitution
          )) && (
            <div className="second-layer-hints">
              <div className="second-layer-hints__header">
                <Svg src={AlertCircle} />
                Meldungen
              </div>
              <div
                className={`second-layer-hints__content ${
                  medicationInfo?.hintsAndWarnings?.length === 1 || 
                  (hintsAndWarnings?.length === 1 && isSubstitution)
                    ? 'second-layer-hints__content--only-one-child'
                    : ''
                }`}
              >
                <ul>
                  {[...(medicationInfo?.hintsAndWarnings || []), ...(hintsAndWarnings || [])]
                    .reduce((list: HintsAndWarning[], hint) => {
                      if (!list.find((item) => item.text === hint.text)) {
                        list.push(hint);
                      }
                      return list;
                    }, [])
                    .map((item) => (
                      <li key={item.text}>{item.text}</li>
                    ))}
                </ul>
              </div>
            </div>
          )}
        </div>
        <div className="second-layer-footer">
          {/* // TODO: hide feature for launch production */}
          {/* <Flex w="100%" justify="space-between"> */}
          <Flex w="100%" justify="flex-end">
            {/* // TODO: hide feature for launch production */}
            {/* <div className="keyboard-hint">
              {!medStore.isStatistics && <span>{t('keyboardHint')}</span>}
            </div> */}
            <div className="actions">
              {!isMP && !medStore.isStatistics && (
                <Button
                  data-test-id="add-bmp"
                  intent={Intent.PRIMARY}
                  outlined
                  minimal
                  onClick={handleOpenMedicationDialog}
                  ref={addMPRef}
                  loading={isLoading}
                >
                  + {t('btnMP')}
                </Button>
              )}
              {medicationInfo?.productInformation?.sampleProductFlag ? (
                !medStore.isStatistics && (
                  <Button
                    data-test-id="add-bmp"
                    intent={Intent.PRIMARY}
                    outlined
                    minimal
                    onClick={addToTimeline}
                    loading={isLoading}
                  >
                    + {t('timeline')}
                  </Button>
                )
              ) : (
                <Button
                  data-test-id="add-recipe-pool"
                  intent={Intent.PRIMARY}
                  onClick={() => {
                    if (isShowPopupConfirm) {
                      setOpenPopupConfirmPznAtc(true);
                      return;
                    }
                    handleAddMedicineToShoppingBag();
                  }}
                  ref={addRPRef}
                  loading={isLoading}
                >
                  + {t('btnRecipePool')}
                </Button>
              )}
            </div>
          </Flex>
        </div>
      </>
    );
  }, [
    medicationInfo,
    store.amrInformation,
    medStore.iwwListe?.details,
    isLoading,
    hasHeaderAccessOtherView,
    medStore.isStatistics,
    hintsAndWarnings,
    isSubstitution,
  ]);

  const isLoadingContentSecondLayer = Object.values(
    store.isLoadingSecondLayer
  ).some((v) => v);

  const getTitleName = useMemo(() => {
    if (
      isLoadingContentSecondLayer ||
      !medicationInfo?.productInformation?.name
    ) {
      return null;
    }

    return (
      <AutoTextSize minFontSizePx={17} maxFontSizePx={24}>
        <Tooltip content={medicationInfo?.productInformation?.name || ''}>
          {medicationInfo?.productInformation?.name || ''}
        </Tooltip>
      </AutoTextSize>
    );
  }, [isLoadingContentSecondLayer, medicationInfo]);

  return (
    <>
      <GlobalStyle2ndLayer />
      <Dialog
        ref={secondLayerDialogRef}
        className={`dialog-right ${className} second-layer-right_wo-backdrop`}
        title={getTitleName}
        isOpen={isOpen}
        portalClassName={`second-layer-right_wo-backdrop`}
        transitionDuration={100}
        canOutsideClickClose={canOutsideClickClose}
        onClose={handleCloseDialog}
      >
        {!isLoadingContentSecondLayer && isCheckPznAtcPass ? (
          renderMedicationInfo
        ) : (
          <LoadingState />
        )}
        {renderPriscusMedicationWarning()}
      </Dialog>

      <SecondaryViewDialog
        isOpen={secondaryViewType !== null}
        handleCloseDialog={() => setSecondaryViewType(null)}
        medicine={medicationInfo}
        type={secondaryViewType}
        handleOpenBmpDialog={handleOpenMedicationDialog}
        addShoppingBagHandler={handleAddMedicineToShoppingBag}
      />
      {secondLayerDialogStore.GBA && openGBA && (
        <GBADialog
          isOpen={openGBA}
          handleCloseDialog={() => setOpenGBA(false)}
          gba={secondLayerDialogStore.GBA}
          gpas={
            secondLayerDialogStore.medicationInfo?.productInformation?.gPAs
          }
          icdCodes={secondLayerDialogStore.medicationInfo?.patientICDCodes}
        />
      )}

      {openBmpMedicationDialog && (
        <MedicationDialog
          isBMPHintTextModule
          medicine={medicationInfo}
          isOpen={openBmpMedicationDialog}
          close={handleCloseMedicationDialog}
          saveMedication={handleSaveMedication}
          units={medicationContext.units}
          forms={medicationContext.forms}
          reasons={medicationContext.reasons}
          hints={medicationContext.hints}
          addHint={medicationContext.addHint}
          addReason={medicationContext.addReason}
        />
      )}

      {isShowPopupConfirm && (
        <InfoConfirmDialog
          type="primary"
          isOpen={openPopupConfirmPznAtc}
          title={t('titlePznAtcCode')}
          cancelText={t('buttonNoPznAtcCode')}
          confirmText={t('buttonYesPznAtcCode')}
          isShowIconTitle={false}
          isCloseButtonShown={false}
          canEscapeKeyClose={false} // cuz true will run onClose()
          onClose={() => {
            setOpenPopupConfirmPznAtc(false);
          }}
          isLoading={isLoading}
          onConfirm={handleAddMedicineToShoppingBag}
        >
          <BodyTextM>
            {t('bodyPznAtcCode', {
              endDate: endDate,
              name: medicines.map((m) => m.name).join(', '),
            })}
          </BodyTextM>
        </InfoConfirmDialog>
      )}
    </>
  );
};

export default memo(SecondLayerDialog);
