import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import Theme from '@tutum/mvz/theme';
import { ComponentType } from 'react';
import { COLOR_PRICE_MEDICATION } from '../../utils/medication.const';
import OriginalPriceComparision, {
  IPriceComparisionProps,
} from './PriceComparision';

const styled = Theme.styled;
const StyledPriceComparison: ComponentType<IPriceComparisionProps> = styled(
  OriginalPriceComparision
).attrs(({ className }) => ({
  className: getCssClass('sl-MedicationComparison', className),
}))`
  & {
    .loading-state {
      z-index: 1;
    }
    .sl-table--discounted {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      p {
        text-transform: capitalize;
        border: 1px solid ${COLOR.TEXT_TERTIARY_SILVER};
        border-radius: 25px;
        display: inline-block;
        font-size: 11px;
        line-height: 16px;
        font-weight: 600;
        padding: 3px 8px;
      }
    }

    .sl-target-element {
      width: 24px;
      position: absolute;
      height: calc(100% - 1px);
      top: 0px;
      &.sl-green {
        background: ${COLOR_PRICE_MEDICATION.green};
      }
      &.sl-red {
        background: ${COLOR_PRICE_MEDICATION.red};
      }
      &.sl-rot {
        background: ${COLOR_PRICE_MEDICATION.rot};
      }
      &.sl-orange {
        background: ${COLOR_PRICE_MEDICATION.orange};
      }
      &.sl-blau {
        background: ${COLOR_PRICE_MEDICATION.blau};
      }
      &.sl-gruen {
        background: ${COLOR_PRICE_MEDICATION.gruen};
      }
      &.sl-gruenberechnet {
        background: ${COLOR_PRICE_MEDICATION.gruenberechnet};
      }
    }
    .wrapper {
      position: relative;
    }

    .rdt_TableHeadRow {
      svg {
        width: 18px;
        margin-top: -5px;
      }
    }
    .rdt_Pagination {
      position: fixed;
      bottom: 0;
      /* right: 0; */
    }
    .sl-callout-discounted {
      width: calc(100% - 32px);
      margin: 0 auto;
      margin-bottom: 24px;
      padding-left: 16px;
      padding-right: 16px;
      .sl-Svg {
        margin-right: 8px;
        display: inline;
        vertical-align: middle;
        line-height: 1.1;
      }
    }
    .sl-LoadingState {
      position: absolute;
      top: 0;
      left: 0;
      background-color: ${COLOR.BACKGROUND_PRIMARY_WHITE};
    }
    .cmp-table {
      position: relative;
      .table-name {
        margin: 0;
        padding: 10px 16px;
      }
      &.comparison {
        .hzv-substitution-note {
          margin-left: 16px;
        }
        .sl-table {
          width: calc(100vw - 428px);
          padding-bottom: 0;
          margin-bottom: 0;

          &.sl-statistics {
            width: 100%;
          }
        }
      }
    }

    .rdt_TableBody {
      overflow-y: scroll;
      height: calc(100vh - 330px);

      ::-webkit-scrollbar {
        width: 8px;
      }
      /* Track */
      ::-webkit-scrollbar-track {
        background: transparent;
      }
      /* Handle */
      ::-webkit-scrollbar-thumb {
        background: ${COLOR.NEUTRAL_BASE_LIGHT};
      }
      /* Handle on hover */
      ::-webkit-scrollbar-thumb:hover {
        background: ${COLOR.NEUTRAL_BASE};
      }
    }

    .header-table {
      .rdt_TableBody,
      .rdt_TableHeader {
        display: none;
      }
      .sort-substance .sl-Svg {
        display: none;
      }
    }

    .rdt_TableCell,
    .rdt_TableCol {
      position: relative;
    }

    .rdt_TableRow {
      border-bottom: none;
      min-height: auto;
    }

    .rdt_TableCol_Sortable {
      .sl-Svg {
        position: absolute;
        right: 10px;
      }
    }

    .rdt_Pagination {
      justify-content: flex-start;
    }
    .header {
      padding: 0 20px 16px 20px;
      .bp5-control {
        vertical-align: middle;
        margin-top: 4px;
        margin-left: 0px;
        margin-bottom: 0;
        color: ${COLOR.TEXT_PRIMARY_BLACK};
      }
      .bp5-control:not(.bp5-disabled):hover
        > input:not(checked)
        ~ .bp5-control-indicator {
        background: rgba(115, 134, 148, 0.2);
      }
      a.back {
        line-height: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
        margin-bottom: 18px;
        &:hover {
          text-decoration: none;
        }
        span {
          font-size: 16px;
          align-items: center;
          letter-spacing: -0.2px;
          color: ${COLOR.TEXT_PRIMARY_BLACK};
        }
        div.sl-Svg {
          display: inline-block;
          vertical-align: middle;
        }
      }
    }

    .sl-price-cell {
      padding-right: 34px;
    }

    .sl-price-comparison {
      position: absolute;
      top: 10px;
      right: 22px;
    }

    .sl-price-discounted {
      position: absolute;
      top: 10px;
      right: 4px;
    }
    .sl-zuza-cell {
      width: 100%;
      text-align: right;
    }
  }
`;

export default StyledPriceComparison;
