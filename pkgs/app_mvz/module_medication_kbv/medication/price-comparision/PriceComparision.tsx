import {
  BodyTextM,
  Flex,
  LoadingState,
  Svg,
} from '@tutum/design-system/components';
import {
  Callout,
  Classes,
  Icon,
  Intent,
  Switch,
} from '@tutum/design-system/components/Core';
import { COLOR } from '@tutum/design-system/themes/styles';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import {
  FindByIdRequest,
  Order,
  Sort,
  SortField,
  markFavourite,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { Medicine } from '@tutum/hermes/bff/legacy/medicine_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IMvzTheme } from '@tutum/mvz/theme';
import { memo, useEffect, useState } from 'react';
import { customStyles, genColumns } from '../medication-table/setting-table';
import { medicationActions, useMedicationStore } from '../MedicationKBV.store';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import MessageBar from '@tutum/design-system/components/message-bar/MessageBar.styled';

const IconBack = '/images/back.svg';
const warnInfoCircleIcon = '/images/info-solid-warn.svg';

export interface IPriceComparisionProps {
  theme?: IMvzTheme;
  className?: string;
  ikNumber: string;
  pzn: string;
  isSVPatient: boolean;
  contractId?: string;
  priceComparisonGroup: number;
  inputRowsPerPage: number;
  isComparePrice: boolean;
  handleSetCurrentMedicine: (medicine: Medicine) => void;
  handleClose: () => void;
}

const PriceComparision = ({
  className,
  ikNumber,
  pzn,
  isSVPatient,
  contractId,
  inputRowsPerPage,
  isComparePrice,
  handleSetCurrentMedicine,
  handleClose,
  priceComparisonGroup,
  t,
}: IPriceComparisionProps & II18nFixedNamespace<any>) => {
  const { t: tMedicationTable } = I18n.useTranslation({
    namespace: 'Medication',
    nestedTrans: 'MedicationTable',
  });

  const { patient, schein } = usePatientFileStore();
  const settingStore = useSettingStore();
  const [isDiscount, setIsDiscount] = useState<boolean>(
    settingStore.medication.isOnlyDiscountInSearch
  );
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(inputRowsPerPage);
  const [isEnableFilter, setEnableFilter] = useState(true);
  const [currentPriceComparisionPzn, setCurrentPriceComparisionPzn] = useState<
    string | null
  >(pzn);

  const store = useMedicationStore();

  const currentSchein = useCurrentSchein();
  const { isContractSupport: hasSupportVSST550 } = useCheckContractSupport(
    ['VSST550'],
    [currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportVSST551 } = useCheckContractSupport(
    ['VSST551'],
    [currentSchein?.hzvContractId]
  );

  const [sortType, setSortType] = useState<Sort | undefined>(
    isSVPatient
      ? undefined
      : {
          field: SortField.Price,
          order: Order.Asc,
        }
  );

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPage(1);
    setRowsPerPage(currentRowsPerPage);
  };
  const onChangePage = (page: number) => {
    setPage(page);
  };

  useEffect(() => {
    return () => {
      setCurrentPriceComparisionPzn(null);
    };
  }, []);

  useEffect(() => {
    if (pzn) {
      setCurrentPriceComparisionPzn(pzn);
    }
  }, [pzn]);

  useEffect(() => {
    if (!currentPriceComparisionPzn || !ikNumber) {
      return;
    }
    medicationActions.getPriceComparison({
      pzn: currentPriceComparisionPzn,
      ikNumber,
      pageSize: rowsPerPage,
      page,
      isDiscount,
      priceComparisonGroupId: priceComparisonGroup,
      sort: sortType!,
      isSvPatient: isSVPatient,
      referenceDate: datetimeUtil.dateTimeFormat(
        datetimeUtil.now(),
        YEAR_MONTH_DAY_FORMAT
      ),
      contractId: contractId,
      patientId: patient?.current?.id,
      isPrivateSchein: checkIsPrivateSchein(schein?.activatedSchein),
    });
  }, [
    isDiscount,
    sortType,
    page,
    rowsPerPage,
    ikNumber,
    schein?.activatedSchein,
  ]);
  useEffect(() => {
    if (
      !store.comparison.medicineComparison?.isHaveDiscountProduct &&
      isDiscount &&
      isEnableFilter
    ) {
      setEnableFilter(false);
      setIsDiscount(false);
    }
  }, [store.comparison.medicineComparison]);
  useEffect(() => {
    if (settingStore.medication.isOnlyDiscountInSearch !== isDiscount) {
      setIsDiscount(settingStore.medication.isOnlyDiscountInSearch);
    }
  }, [settingStore.medication.isOnlyDiscountInSearch]);
  useEffect(() => {
    if (!pzn || !isComparePrice || !ikNumber) {
      return;
    }
    medicationActions.getMedication({
      pzn,
      ikNumber,
      contractId,
      isSvPatient: isSVPatient,
      patientId: patient?.current?.id,
      referenceDate: datetimeUtil.dateTimeFormat(
        datetimeUtil.now(),
        YEAR_MONTH_DAY_FORMAT
      ),
      isPrivateSchein: checkIsPrivateSchein(schein?.activatedSchein),
    }, t('notFoundPzn'));
  }, [ikNumber, isComparePrice, patient?.current?.id, schein?.activatedSchein]);

  const handleSort = (field: SortField) => {
    const isSameField = sortType?.field === field;
    let nextOrder = Order.Asc;
    if (isSameField) {
      switch (sortType?.order) {
        case Order.Asc:
          nextOrder = Order.Desc;
          break;
        case Order.Desc:
          nextOrder = null!;
          break;
        default:
          nextOrder = Order.Asc;
      }
    }

    const sort = {
      field,
      order: nextOrder,
    };
    setSortType(nextOrder ? sort : undefined);
  };

  const handleMarkFavourite = async (medicine: Medicine) => {
    await markFavourite({
      pzn: medicine.pzn,
      isFavourite: !medicine.isFavourite,
    });
    medicationActions.updatePriceComparisonMarkFavourite(
      medicine.pzn,
      !medicine.isFavourite
    );
  };

  const genColumnsOverride = (
    t,
    handleSetCurrentMedicine: (medicine: Medicine) => void,
    handleSort: (field: SortField) => void,
  ): IDataTableColumn<Medicine>[] => {
    const config = genColumns(
      t,
      handleSetCurrentMedicine,
      handleSort,
      sortType,
      undefined,
      undefined,
      undefined,
      handleMarkFavourite
    );
    const zuzaColConfig = config.find((col) => col.id === 'zuza');
    if (!zuzaColConfig) {
      return config;
    }
    zuzaColConfig.maxWidth = '120px';
    zuzaColConfig.name = (
      <Flex align="center" justify="space-between" className="sort-copayment">
        <span>{t('colZuza')}</span>
      </Flex>
    );
    // config[4] = zuzaColConfig;
    return config;
  };

  const cssConditional = [
    {
      when: (item) => Boolean(item['borderTop']),
      style: {
        borderTop: `2px solid ${COLOR.BORDER_INPUT}`,
        zIndex: 1,
      },
    },
    {
      when: (item) => Boolean(item['borderBottom']),
      style: {
        borderBottom: `2px solid ${COLOR.BORDER_INPUT} !important`,
        zIndex: 1,
      },
    },
    {
      when: (item) => item.pzn === pzn,
      style: { backgroundColor: COLOR.INFO_SECONDARY_PRESSED },
    },
    {
      when: (item) => item['pzn'] === store.comparison.medicine?.[0].pzn,
      style: { background: COLOR.WARNING_LIGHT },
    },
  ];

  const pharmacySalePrice =
    store.comparison.medicine?.[0].priceInformation?.pharmacySalePrice ??
    +(
      store.comparison.medicine?.[0].priceInformation?.svPrices
        ?.pharmacySalePrice || 0
    );

  return (
    <Flex column className={className}>
      {hasSupportVSST550 && (
        <Flex mb={16}>
          <MessageBar
            type="info"
            content={t('PriceComparision.hint')}
            hasBullet={false}
          />
        </Flex>
      )}
      <Flex column auto className="outer-wrapper">
        <div className="wrapper">
          {!isEnableFilter ? (
            <Callout
              className="sl-callout-discounted"
              intent={Intent.WARNING}
              icon={<Svg src={warnInfoCircleIcon} />}
            >
              {t('PriceComparision.txtNoDiscound')}
            </Callout>
          ) : null}
          <Flex className="header" column>
            <a
              href=""
              className="back"
              onClick={(evt) => {
                evt.preventDefault();
                return handleClose();
              }}
            >
              <Svg src={IconBack} style={{ marginRight: 8 }} />
              <span className="page-title">
                {t('PriceComparision.title', {
                  tradeName:
                    store.comparison.medicine?.[0].productInformation?.name ??
                    '',
                  size: `${
                    store.comparison.medicine?.[0].packagingInformation
                      ?.packageSize?.nop ?? ''
                  } (${
                    store.comparison.medicine?.[0].packagingInformation
                      ?.amountText ?? ''
                  })`,
                  price: medicationUtil.formatPrice(pharmacySalePrice * 100),
                })}
              </span>
            </a>
            <Switch
              className="switch-btn"
              checked={isDiscount}
              onChange={() => setIsDiscount(!isDiscount)}
              label={t('PriceComparision.lblOnlyDiscount')}
              style={{ display: 'inline-block' }}
              disabled={!isEnableFilter}
            />
          </Flex>
          {hasSupportVSST551 && (
            <Flex mb={16}>
              <MessageBar
                type="info"
                content={t('PriceComparision.hintForSpecificMedications')}
                hasBullet={false}
              />
            </Flex>
          )}
          <div className="cmp-table comparison">
            <Table
              className={`sl-table ${
                store.isStatistics ? 'sl-statistics' : ''
              }`}
              columns={genColumnsOverride(
                tMedicationTable,
                handleSetCurrentMedicine,
                handleSort
              )}
              data={(store.comparison.medicineComparison?.medicines || []).map(
                (item, index) => ({
                  ...item,
                  id: `${item.id}_${index}`,
                })
              )}
              onChangePage={onChangePage}
              paginationDefaultPage={page}
              paginationPerPage={rowsPerPage}
              paginationTotalRows={
                store.comparison.medicineComparison?.total || 1
              }
              onChangeRowsPerPage={onChangeRowsPerPage}
              paginationServer
              paginationResetDefaultPage
              customStyles={customStyles}
              noHeader
              conditionalRowStyles={cssConditional}
              fixedHeader
              highlightOnHover
              pagination
            />
          </div>
          {store.comparison.isLoadingMedicine ||
          store.comparison.isLoadingComparison ? (
            <LoadingState className="loading-state" />
          ) : null}
        </div>
      </Flex>
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(PriceComparision, {
    namespace: 'Medication',
  })
);
