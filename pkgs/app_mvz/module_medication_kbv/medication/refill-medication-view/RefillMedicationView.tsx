import { memo, useContext, useEffect, useState } from 'react';

import {
  BodyTextM,
  Flex,
  LoadingState,
  Svg,
} from '@tutum/design-system/components';
import {
  Button,
  Callout,
  Collapse,
  Dialog,
  Intent,
} from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  AddToShoppingBagRequest,
  addToShoppingBag,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { useMutationCreateBundles } from '@tutum/hermes/bff/legacy/app_mvz_erezept';
import {
  Order,
  Sort,
  SortField,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import {
  Medicine,
  MedicineType,
} from '@tutum/hermes/bff/legacy/medicine_common';
import { FormInfo } from '@tutum/hermes/bff/medicine_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMvzTheme } from '@tutum/mvz/theme';
import { medicationShoppingBagActions } from '../../shopping-bag/MedicationShoppingBag.store';
import { medicationUtil } from '../../utils/medication-util';
import { medicationActions, TMedicineIntersection, useMedicationStore } from '../MedicationKBV.store';
import { customStyles, genColumns } from './setting-table';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';

const IconPencil = '/images/edit-hollow.svg';

export interface IRefillMedicationViewProps {
  theme?: IMvzTheme;
  className?: string;
  isOpen: boolean;
  ikNumber: string;
  selectedContractDoctor: ISelectedContractDoctor;
  patient?: IPatientProfile;
  handleClose: () => void;
}

const infoIcon = '/images/information-circle.svg';
enum TableType {
  NEW = 'new',
  PREVIOUS = 'previous',
}

const RefillMedicationView = ({
  className,
  isOpen,
  ikNumber,
  patient,
  selectedContractDoctor,
  handleClose,
  t,
}: IRefillMedicationViewProps & II18nFixedNamespace<any>) => {
  const { t: tMedication } = I18n.useTranslation<Paths<typeof MedicationI18n>>({
    namespace: 'Medication',
  });
  const [step, setStep] = useState(0);
  const [currMedicines, setCurrMedicines] = useState<any>([]);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sigKey, setSigKey] = useState<string>('');

  const [enabledIndex, setEnabledIndex] = useState([true]);
  const [isLoadingPrescribe, setIsLoadingPrescribe] = useState(false);
  // const [isSkipAllRefill, setIsSkipAllRefill] = useState<boolean>(undefined);
  const [selectedTable, setSelectedTable] = useState(TableType.PREVIOUS);

  const globalContext = useContext(GlobalContext.instance);
  const store = useMedicationStore();
  const patientFileStore = usePatientFileStore();
  const medicationContext = useContext(MedicationContext);
  const isSvPatient = patientFileStore.isSvPatient;
  const [sortType, setSortType] = useState<Sort | undefined>(
    isSvPatient
      ? undefined
      : {
        field: SortField.Price,
        order: Order.Asc,
      }
  );

  const { mutateAsync: createBundles } = useMutationCreateBundles({
    onSuccess: ({ data }) => {
      medicationShoppingBagActions.setBundlesCreated(data.bundleCreateds);
    },
  });

  useEffect(() => {
    if (store?.refill.data.length) {
      setCurrMedicines(store?.refill.data);
    }
  }, [store?.refill.data]);

  const altPayload = {
    ikNumber,
    pageSize: rowsPerPage,
    sort: sortType,
    isDiscount: false,
    isSvPatient,
    referenceDate: datetimeUtil.dateTimeFormat(
      datetimeUtil.now(),
      YEAR_MONTH_DAY_FORMAT
    ),
    contractId: selectedContractDoctor.contractId,
  };

  useEffect(() => {
    if (isOpen && store?.refill.data.some((item) => !item.skipRefill)) {
      const fn = async (): Promise<string> => {
        const sigkey = await medicationActions.getRefill(
          step,
          altPayload,
          checkIsPrivateSchein(patientFileStore.schein?.activatedSchein),
          tMedication('notFoundPzn')
        );
        return sigkey;
      };
      const setSigKeyFunc = async () => {
        const getRefill = await fn();
        setSigKey(getRefill);
      };
      setSigKeyFunc();
    }
  }, [
    isOpen,
    sortType,
    rowsPerPage,
    step,
    ikNumber,
    store.refill.cache[step]?.page,
    store?.refill.data,
    patientFileStore.schein?.activatedSchein,
  ]);

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    medicationActions.setRefillPage(step, 1);
    setRowsPerPage(currentRowsPerPage);
  };
  const onChangePage = (page: number) => {
    medicationActions.setRefillPage(step, page);
  };
  const handleOnClick = (step: number, isEditable) => {
    if (!isEditable) {
      return;
    }
    setStep(step);
    enabledIndex[step] = true;
    setEnabledIndex(enabledIndex);
  };
  const handleSort = (field: SortField) => {
    const sort = {
      field,
      order: sortType?.order === Order.Asc ? Order.Desc : Order.Asc,
    };
    setSortType(sort);
  };
  const handleSetMedicine = (tableNo: number, medicine: Medicine) => {
    const clonePznOfTable = [...currMedicines];
    clonePznOfTable[tableNo] = medicine;
    setCurrMedicines(clonePznOfTable);
  };
  const handlePrescribed = async () => {
    setIsLoadingPrescribe(true);
    const mapData = (medicine, original): AddToShoppingBagRequest => ({
      patientId: patient?.id,
      doctorId: store.isStatistics
        ? globalContext.globalData.userProfile?.id
        : selectedContractDoctor.doctorId,
      contractId: selectedContractDoctor.contractId,
      ikNumber: +ikNumber,
      medicine: {
        ...original,
        id: getUUID(),
        type: selectedContractDoctor.contractId
          ? MedicineType.HPM
          : medicine.type || MedicineType.KBV,
        pzn: medicine.pzn,
        name:
          medicine.productInformation?.shortName ||
          medicine.productInformation?.name,
        quantity: medicine?.quantity || 1,
        currentFormType:
          medicine?.currentFormType || medicine?.productInformation?.formType,
        packagingInformation: medicine?.packagingInformation,
        productInformation: medicine?.productInformation,
        drugInformation: medicine?.drugInformation,
        textInformation: medicine?.textInformation,
        priceInformation: medicine?.priceInformation,
        colorCategory: medicine?.colorCategory,
        kBVMedicineId: medicationUtil.extractMedicineKBVId(medicine),
        isEPrescription: !!medicine?.erezeptItemStatus,
      },
    });

    // execute sequentially, not parallels
    try {
      for (const medicineData of currMedicines) {
        const original = store?.refill.originalMedicines.find(
          (item) => item.pzn === medicineData.pzn
        );
        const { data: { medicine } } = await addToShoppingBag(
          mapData(medicineData, original)
        );
        if (mapData(medicineData, original).medicine.isEPrescription) {
          const formInfo: FormInfo = {
            ...medicine,
            medicineIDs: [medicine.id!],
            formSetting: '',
            isShowFavHint: false,
          };
          await createBundles({
            doctorId: selectedContractDoctor.doctorId!,
            patientId: patient?.id!,
            scheinId: patientFileStore.schein.activatedSchein?.scheinId!,
            treatmentDoctorId: selectedContractDoctor.doctorId!,
            formInfos: [formInfo],
          });
        }
      }
      setEnabledIndex([true]);
      setStep(0);
      medicationContext.setShowPrintPreviewState(true);
      handleClose();
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsLoadingPrescribe(false);
    }
  };
  const btnBackHandler = () => {
    enabledIndex[step - 1] = true;
    setStep(step - 1);
  };

  const btnNextHandler = () => {
    if (
      step <
      store?.refill.data?.filter((item) => !item.skipRefill).length - 1
    ) {
      setStep(step + 1);
      enabledIndex[step + 1] = true;
    } else {
      handlePrescribed();
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Dialog
      className={`bp5-dialog-fullscreen ${className}`}
      isOpen={isOpen}
      onClose={() => {
        setStep(0);
        handleClose();
        medicationContext.setInRefillProcess(false);
        medicationActions.resetPackageSizeStore();
      }}
      title={t('title')}
      isCloseButtonShown
      canOutsideClickClose={false}
    >
      <Flex p="24px 16px" column style={{ overflow: 'scroll' }}>
        <Callout
          className="sl-callout-information"
          intent={Intent.PRIMARY}
          icon={<Svg src={infoIcon} />}
        >
          {isSvPatient ? t('lbSVInfoText') : t('lbInfoText')}
        </Callout>
        {store?.refill.data
          ?.filter((item) => !item.skipRefill)
          .map((_data, index) => {
            const isEditable = enabledIndex[step] && step !== index;
            return (
              <Flex key={index} column w="100%">
                <Flex
                  className={`sl-header-collapse${enabledIndex[index] ? '' : ' disabled'
                    }`}
                  onClick={() => handleOnClick(index, !!enabledIndex[index])}
                  align="center"
                  w="100%"
                  mb={12}
                >
                  <div className="sl-number">{index + 1}</div>
                  <BodyTextM fontSize={16} fontWeight={600} margin="0 8px">
                    {currMedicines[index]?.name ||
                      currMedicines[index]?.productInformation?.name || ''}
                    {isEditable ? (
                      <Svg
                        src={IconPencil}
                        style={{ fill: COLOR.ICON_INFO, marginLeft: 10 }}
                      />
                    ) : null}
                  </BodyTextM>
                </Flex>
                <Collapse isOpen={step === index}>
                  <div className="header-table">
                    <Table
                      className="sl-table"
                      columns={genColumns(
                        handleSetMedicine,
                        handleSort,
                        sortType,
                        currMedicines[index],
                        index
                      )}
                      data={[{} as any]}
                      paginationServer
                      customStyles={customStyles}
                    />
                  </div>
                  <div className="cmp-table">
                    <h4 className="table-name">
                      {t('lbPreviouslyPrescribed')}
                    </h4>
                    <Table
                      className="sl-table"
                      columns={genColumns(
                        handleSetMedicine,
                        handleSort,
                        sortType,
                        currMedicines[index],
                        index,
                        selectedTable,
                        setSelectedTable,
                        TableType.PREVIOUS
                      )}
                      data={
                        store.refill?.originalMedicines?.[step]
                          ? [store.refill?.originalMedicines?.[step]]
                          : []
                      }
                      customStyles={customStyles}
                      noHeader
                      fixedHeader
                      highlightOnHover
                    />
                  </div>
                  <div className="cmp-table comparison">
                    <h4 className="table-name">{t('lbCheaperMedications')}</h4>
                    {!store.refill.cache[step] ||
                      store.refill.cache[step]?.isLoading ? (
                      <LoadingState />
                    ) : (
                      <Table
                        className="sl-table"
                        columns={genColumns(
                          handleSetMedicine,
                          handleSort,
                          sortType,
                          currMedicines[index],
                          index,
                          selectedTable,
                          setSelectedTable,
                          TableType.NEW
                        )}
                        data={(
                          store.refill.cache[step]?.tableData?.[sigKey]
                            ?.medicines || []
                        ).map((item) => ({
                          ...item,
                          id: `${item.id}`,
                        }))}
                        onChangePage={onChangePage}
                        paginationDefaultPage={
                          store.refill.cache[step]?.tableData?.[sigKey]?.page
                        }
                        paginationPerPage={rowsPerPage}
                        paginationTotalRows={
                          store.refill.cache[step]?.tableData?.[sigKey]
                            ?.total || 1
                        }
                        onChangeRowsPerPage={onChangeRowsPerPage}
                        paginationServer
                        paginationResetDefaultPage
                        customStyles={customStyles}
                        noHeader
                        fixedHeader
                        highlightOnHover
                        pagination
                      />
                    )}
                    <div className="sl-btn-actions">
                      {step > 0 ? (
                        <Button
                          onClick={btnBackHandler}
                          loading={isLoadingPrescribe}
                        >
                          {t('lblBack')}
                        </Button>
                      ) : null}
                      <Button
                        intent={Intent.PRIMARY}
                        onClick={btnNextHandler}
                        loading={isLoadingPrescribe}
                      >
                        {t(
                          step <
                            store?.refill.data?.filter(
                              (item) => !item.skipRefill
                            ).length -
                            1
                            ? 'lblNext'
                            : 'lblFinish'
                        )}
                      </Button>
                    </div>
                  </div>
                </Collapse>
              </Flex>
            );
          })}
      </Flex>
    </Dialog>
  );
};

export default memo(
  I18n.withTranslation(RefillMedicationView, {
    namespace: 'Medication',
    nestedTrans: 'Refill',
  })
);
