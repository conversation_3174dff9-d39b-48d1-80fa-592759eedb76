import React, {
  memo,
  useState,
  useEffect,
  useContext,
  useCallback,
} from 'react';
import I18n from '@tutum/infrastructure/i18n';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { IMvzTheme } from '@tutum/mvz/theme';
import {
  Flex,
  LoadingState,
  BodyTextS,
  Button,
  ReactSelect,
  IMenuItem,
} from '@tutum/design-system/components';
import { Intent, Dialog, Classes } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import {
  Sort,
  Order,
  SortField,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import {
  addToShoppingBag,
  removeFromShoppingBag,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import useTablePage from '@tutum/mvz/hooks/useTablePage';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import RemoveShoppingBagDialog from '@tutum/mvz/module_medication/remove-shopping-bag-dialog/RemoveShoppingBagDialog.styled';
import {
  medicationActions,
  TMedicineIntersection,
  useMedicationPackageSizeStore,
} from '../MedicationKBV.store';
import { genColumns, customStyles } from './setting-table';
import { medicationUtil } from '../../utils/medication-util';
import { useChangeKBVPackageSize } from '../../hooks/ChangePackageSize.hook';
import { useMedicationShoppingBagStore } from '../../shopping-bag/MedicationShoppingBag.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';

export interface IChangePackageSizeViewProps {
  isOpen: boolean;
  isSVPatient: boolean;
  ikNumber: number;
  contractId?: string;
  theme?: IMvzTheme;
  className?: string;
  isPrivateSchein: boolean;
  handleClose: () => void;
}

const ChangePackageSizeView = ({
  className,
  isOpen,
  handleClose,
  isSVPatient,
  ikNumber,
  contractId,
  isPrivateSchein,
}: IChangePackageSizeViewProps) => {
  const {
    patientManagement: { patient, selectedContractDoctor },
  } = useContext(PatientManagementContext.instance);

  const medicationContext = useContext(MedicationContext);

  const [isLoadingPrescribe, setIsLoadingPrescribe] = useState(false);

  const { shoppingBag } = useMedicationShoppingBagStore();

  const [showRemoveShoppingBag, setShowRemoveShoppingBag] = useState(false);

  const [sortType, setSortType] = useState<Sort>({
    field: SortField.Price,
    order: Order.Asc,
  });

  const packageSizeStore = useMedicationPackageSizeStore();

  const {
    isFetching,
    isFetchingCurrentMedication,
    isFetchingComparableMedications,
    selectedPackageSize,
    availablePackageSizes,
    currentMedication,
    availableComparableMedicines,
    totalPriceComparable,
    fetchPriceComparableMedications,
    onPackageSizeSelected,
  } = useChangeKBVPackageSize(
    packageSizeStore?.changingPackageSizeMedicine?.medicineProductId!,
    ikNumber,
    isSVPatient,
    contractId,
    packageSizeStore?.changingPackageSizeMedicine?.pzn,
    isPrivateSchein
  );

  const [selectedMedicine, setSelectedMedicine] =
    useState<TMedicineIntersection | null>(null);

  const priceComparisonTableHook = useTablePage();

  const { t: tTable } = I18n.useTranslation<
    keyof typeof MedicationI18n.MedicationTable
  >({
    namespace: 'Medication',
    nestedTrans: 'MedicationTable',
  });

  const { t: tRefill } = I18n.useTranslation<
    keyof typeof MedicationI18n.Refill
  >({
    namespace: 'Medication',
    nestedTrans: 'Refill',
  });

  const { t: tPrescribedMedicineRow } = I18n.useTranslation<
    keyof typeof MedicationI18n.PrescribedMedicineRow
  >({
    namespace: 'Medication',
    nestedTrans: 'PrescribedMedicineRow',
  });

  const selectMedicine = (_selectedMedicine: TMedicineIntersection) =>
    setSelectedMedicine(_selectedMedicine);

  const handleSort = (field: SortField) => {
    const sort = {
      field,
      order: sortType?.order === Order.Asc ? Order.Desc : Order.Asc,
    };
    setSortType(sort);
  };

  const handleBeforeClose = () => {
    setSelectedMedicine(null);
    handleClose();
  };

  const handlePrescribe = useCallback(() => {
    const doctorId = selectedContractDoctor?.doctorId;

    const patientId = patient?.id;

    if (!selectedMedicine || ikNumber == null || !doctorId || !patientId) {
      return;
    }

    const contractId = selectedContractDoctor?.contractId;

    setIsLoadingPrescribe(true);

    addToShoppingBag(
      medicationUtil.mappingMedicineToShoppingBagRequest(
        patientId,
        doctorId,
        ikNumber,
        selectedMedicine,
        contractId,
        isSVPatient
      )
    )
      .then(() => {
        medicationContext.setShowPrintPreviewState(true);
        handleBeforeClose();
      })
      .finally(() => {
        setIsLoadingPrescribe(false);
      });
  }, [selectedMedicine, patient, selectedContractDoctor]);

  const onPrescribeButtonHandler = useCallback(() => {
    if (shoppingBag?.medicines?.length) {
      setShowRemoveShoppingBag(true);
      return;
    }
    handlePrescribe();
  }, [handlePrescribe, shoppingBag?.medicines]);

  useEffect(() => {
    fetchPriceComparableMedications(
      priceComparisonTableHook.pageNo,
      priceComparisonTableHook.pageSize,
      sortType
    );
  }, [
    priceComparisonTableHook.pageNo,
    priceComparisonTableHook.pageSize,
    sortType,
  ]);

  useEffect(() => {
    if (!currentMedication) return;
    setSelectedMedicine(currentMedication as any);
  }, [currentMedication]);

  const onRemoveShoppingBag = useCallback(() => {
    return removeFromShoppingBag({
      shoppingBagId: shoppingBag?.shoppingBagId,
      doctorId: selectedContractDoctor.doctorId,
      contractId: selectedContractDoctor.contractId,
      patientId: patient?.id,
    })
      .then(() => {
        medicationContext.setRefillProcessState(true, []);
        handlePrescribe();
      })
      .finally(() => {
        setShowRemoveShoppingBag(false);
      });
  }, [
    shoppingBag?.shoppingBagId,
    patient,
    selectedContractDoctor,
    handlePrescribe,
  ]);

  function isCheckFunc(row: TMedicineIntersection): boolean {
    return row.pzn === selectedMedicine?.pzn;
  }

  if (!isOpen) {
    return null;
  }

  return (
    <>
      <Dialog
        className={`bp5-dialog-fullscreen ${className}`}
        isOpen={isOpen}
        onClose={() => {
          medicationActions.resetPackageSizeStore();
          handleBeforeClose();
          medicationContext.setInRefillProcess(false);
        }}
        title={tPrescribedMedicineRow('changePackageSize')}
        isCloseButtonShown
        canOutsideClickClose={false}
      >
        <Flex column className={`${Classes.DIALOG_BODY} no-padding`}>
          <Flex
            pl={16}
            py={16}
            align="center"
            gap={16}
            className="package-size-toolbar"
          >
            <BodyTextS textTransform="uppercase">Package size</BodyTextS>
            <ReactSelect
              items={
                availablePackageSizes?.map((nop) => ({
                  label: nop.label,
                  value: nop.pzn,
                })) ?? []
              }
              selectedValue={selectedPackageSize?.value ?? undefined}
              onItemSelect={(item: IMenuItem) => onPackageSizeSelected(item)}
              menuPosition="fixed"
              menuPlacement="bottom"
              styles={{
                control: (base) => ({ ...base, minWidth: 160 }),
                menuList: (base) => ({ ...base, minWidth: 160 }),
              }}
              getOptionLabel={(opt: IMenuItem) => opt.label ?? ''}
            />
          </Flex>
          <Flex column w="100%">
            <div className="header-table">
              <Table
                className="sl-table"
                columns={genColumns(
                  selectMedicine,
                  handleSort,
                  isCheckFunc,
                  tTable,
                  sortType
                )}
                data={[{} as any]}
                paginationServer
                customStyles={customStyles}
              />
            </div>
            <div className="cmp-table">
              <h4 className="table-name">
                {tRefill('lbPreviouslyPrescribed')}
              </h4>
              {isFetchingCurrentMedication ? (
                <Flex m={32}>
                  <LoadingState />
                </Flex>
              ) : (
                <Table
                  className="sl-table"
                  columns={genColumns(
                    selectMedicine,
                    handleSort,
                    isCheckFunc,
                    tTable,
                    sortType
                  )}
                  customStyles={customStyles}
                  data={currentMedication ? [currentMedication] : []}
                  noHeader
                  fixedHeader
                  highlightOnHover
                />
              )}
            </div>
            <div className="cmp-table comparison">
              <h4 className="table-name">{tRefill('lbOtherMedications')}</h4>
              {isFetchingComparableMedications ? (
                <Flex m={32}>
                  <LoadingState />
                </Flex>
              ) : (
                <Table
                  className="sl-table"
                  columns={genColumns(
                    selectMedicine,
                    handleSort,
                    isCheckFunc,
                    tTable,
                    sortType
                  )}
                  customStyles={customStyles}
                  data={availableComparableMedicines}
                  onChangePage={priceComparisonTableHook.onPageNoChange}
                  paginationDefaultPage={priceComparisonTableHook.pageNo}
                  paginationPerPage={priceComparisonTableHook.pageSize}
                  paginationTotalRows={totalPriceComparable}
                  onChangeRowsPerPage={(nextPageSize) =>
                    priceComparisonTableHook.onPageSizeChange(nextPageSize)
                  }
                  paginationServer
                  paginationResetDefaultPage
                  noHeader
                  fixedHeader
                  highlightOnHover
                  pagination
                />
              )}
            </div>

            <div className="sl-btn-actions">
              <Button
                intent={Intent.PRIMARY}
                onClick={handleBeforeClose}
                outlined
                minimal
              >
                {tRefill('lblCancel')}
              </Button>
              <Button
                intent={Intent.PRIMARY}
                onClick={onPrescribeButtonHandler}
                disabled={!selectedMedicine}
                loading={isFetching || isLoadingPrescribe}
              >
                {tRefill('lblPrescribe')}
              </Button>
            </div>
          </Flex>
        </Flex>
      </Dialog>

      <RemoveShoppingBagDialog
        show={showRemoveShoppingBag}
        onCancel={() => {
          setShowRemoveShoppingBag(false);
        }}
        onConfirmRemove={onRemoveShoppingBag}
      />
    </>
  );
};

export default memo(ChangePackageSizeView);
