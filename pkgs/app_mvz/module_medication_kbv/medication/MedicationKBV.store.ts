import { useMemo } from 'react';
import { proxy, useSnapshot } from 'valtio';
import {
  GetPriceComparisonResponse,
  search,
  SearchMedicineRequest,
  SearchMedicineResponse,
  GetPriceComparisonRequest,
  getPriceComparison,
  FindByIdRequest,
  findByID,
  GetTechInformationRequest,
  GetTechInformationResponse,
  getTechInformation,
  GetAlternativeDetailsResponse,
  GetARVRequest,
  GetAlternativeDetailsRequest,
  getARV,
  ARVItem,
  checkARVIndicationTreeExist,
  GetARVIndicationTreeRequest,
  ARVIndicationItem,
  getARVIndicationTree,
  ARVMoleculeItem,
  ARVDocument,
  GetDocumentLinkRequest,
  getIndicationDocumentLink,
  getMoleculeDocumentLink,
  IWWListeItem,
  getIWWListeDetails,
  getARVDocumentLink,
  getPackageSizes,
  GetPackageSizesResponse,
  NormSizeCodeListItem,
  getAMRDocumentLink,
  SearchType,
  GetAMRDocumentLinkRequest,
  hasSubstitution,
  HasSubstitutionRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';

import { getAlternativeDetails } from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';

import {
  addToShoppingBag,
  AddToShoppingBagRequest,
  MedicationPrescribeResponse,
  MedicineShoppingBagInfo,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { IParsedQuery } from './search-medication-box';
import { Medicine } from '@tutum/hermes/bff/legacy/medicine_common';
import { BmpMedicationPlanKeytab } from '@tutum/hermes/bff/service_domains_bmp';
import { GetPredefinedDataOfMedicationResponse } from '@tutum/hermes/bff/legacy/app_mvz_bmp';
import { FormInfo, MedicineInfo } from '@tutum/hermes/bff/repo_medicine_common';
import { medicationShoppingBagActions } from '../shopping-bag/MedicationShoppingBag.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { groupByPrice } from './helpers';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { isNull } from 'lodash';
import { createBundles } from '@tutum/hermes/bff/legacy/app_mvz_erezept';
import { clearToaster } from '@tutum/design-system/components';

export type ARVIndicationExtendedItem = {
  indicationId: number;
  description: string;
  level: number;
  molecules?: ARVMoleculeItem[];
  documents?: ARVDocument[];
  children?: ARVIndicationExtendedItem[];
  order: number;

  // for UI only
  isExpanding: boolean;
  isDisplaying: boolean;
};

interface IRefillTableData {
  total?: number;
  medicines?: TMedicineIntersection[];
}

interface IRefillData {
  page?: number;
  originalMedicine?: TMedicineIntersection | null;
  isLoading?: boolean;
  tableData?: IRefillTableData;
}

type _TKBVMedicineId = number;

export type TMedicineIntersection = Medicine &
  MedicationPrescribeResponse &
  MedicineShoppingBagInfo & {
    skipRefill: boolean;
  };

export type TMedicine = MedicineInfo & { skipRefill: boolean };
interface IMedicationStore {
  medicationResults: SearchMedicineResponse | undefined;
  isLoading: boolean;
  isLoadingSorting: boolean;
  isShowShoppingBag: boolean;
  isNoConcentraion: boolean;
  isProcessingRefillMedication: boolean;
  isAddToRefill: boolean;
  isOpenChangeMedicationPackageSizeDialog: boolean;
  isStatistics: boolean;
  isFavourite: boolean;
  isDisableFavourite: boolean;
  packageSize: {
    isNoMoreSizes: boolean;
    changingPackageSizeMedicine?: {
      medicineProductId: number;
      pzn: string;
    };
    medicationMapWithSizes?: Record<_TKBVMedicineId, NormSizeCodeListItem[]>;
  };
  comparison: {
    medicineComparison: GetPriceComparisonResponse | undefined;
    isLoadingMedicine: boolean;
    isLoadingComparison: boolean;
    medicine: Medicine[] | undefined;
  };
  technicalInfo: {
    isLoading: boolean;
    data: GetTechInformationResponse | undefined;
  };
  alternative: {
    isLoadingARV: boolean;
    dataARV: ARVItem[] | undefined;
    isLoading: boolean;
    data: GetAlternativeDetailsResponse | undefined;
    arvDetail: ARVItem | undefined;
    medicine: Medicine | undefined;
  };
  iwwListe: {
    items: ARVItem[] | undefined;
    details: IWWListeItem[] | undefined;
  };
  iwwRegulation: {
    isLoading: boolean;
    isExist: boolean;
    data: ARVIndicationExtendedItem[] | undefined;
    isLoadingGetLink: boolean;
  };
  refill: {
    formInfo: FormInfo | undefined;
    originalMedicines: TMedicineIntersection[];
    data: TMedicineIntersection[];
    cache: IRefillData[];
  };
  isDisplaySearchMedicineResult: boolean;
  page: number;
  rowsPerPage: number;
  selectAllRowsItem: boolean;
  isChangeConcentration: boolean;
  medicineQuery: IParsedQuery;
  units: BmpMedicationPlanKeytab[];
  forms: BmpMedicationPlanKeytab[];
  isOpenRefillDialog: boolean;
  isCheckedMMI: boolean;
  cacheMedicine: { [key: string]: any };
}

interface IMedicationActions {
  getMedicationResults: (
    payload: Omit<SearchMedicineRequest, 'isFavourite'> & {
      isFavourite?: boolean;
    },
    isSort?: boolean
  ) => void;
  resetMedicationResults: () => void;
  getPriceComparison: (payload: GetPriceComparisonRequest) => void;
  getMedication: (payload: FindByIdRequest, tNotFoundPzn: string) => void;
  getMedicineTechInformation: (payload: GetTechInformationRequest) => void;
  getAltViewList: (payload: GetARVRequest) => void;
  getAlternative: (payload: GetAlternativeDetailsRequest) => void;
  clearAlternativeARVInfo: () => void;
  clearIWWListeInfo: () => void;
  clearAlternativeInfo: () => void;
  setArvDetail: (arvDetail: ARVItem | undefined) => void;
  setShowShoppingBag: (showShoppingBag: boolean) => void;
  checkIwwRegulations: (payload: GetARVIndicationTreeRequest) => void;
  getIwwRegulations: (payload: GetARVIndicationTreeRequest) => void;
  setIwwRegulationsExpanding: (ids: number[], isExpanding: boolean) => void;
  searchIwwRegulations: (query: string) => void;
  getIndicationDocumentLink: (payload: GetDocumentLinkRequest) => void;
  getMoleculeDocumentLink: (payload: GetDocumentLinkRequest) => void;
  getARVDocumentLink: (payload: GetDocumentLinkRequest) => void;
  getAMRDocumentLink: (payload: GetAMRDocumentLinkRequest) => void;
  setIsProcessingRefillMedication: (payload: boolean) => void;
  setIsAddToRefill: (isAdd: boolean) => void;
  setRefillFormData: (formInfo: FormInfo | undefined) => void;
  setRefillData: (payload: TMedicineIntersection[]) => void;
  setRefillDataAtStep: (payload: TMedicineIntersection, step: number) => void;
  setRefillComparableDataAtStep: (
    payload: TMedicineIntersection[],
    step: number,
    sigKey: string,
    total?: number
  ) => void;
  getRefill: (
    step: number,
    getRefillParams: Partial<GetPriceComparisonRequest> & {
      ikNumber: string;
      isDiscount: boolean;
    },
    isPrivateSchein: boolean,
    tNotFoundPzn: string
  ) => Promise<string>;
  setRefillPage: (step: number, page: number) => void;

  setIsOpenChangeMedicationPackageSizeDialog(payload: boolean): void;
  startChangePackageSizesForSingleMedication(
    medicineProductId: number,
    ikNumber: number,
    isSvPatient: boolean,
    contractId?: string,
    pzn?: string
  ): Promise<GetPackageSizesResponse | void>;
  setIsOpenNoMorePackageSizeDialog(payload: boolean): void;
  resetPackageSizeStore(): void;

  setMedicineQuery: (medicineQuery: IParsedQuery) => void;
  setMedicines: (medicines: SearchMedicineResponse) => void;
  setIsDisplaySearchMedicineResult: (state: boolean) => void;
  setPage: (page: number) => void;
  setRowsPerPage: (rowsPerPage: number) => void;
  setSelectAllRowsItem: (data: boolean) => void;
  setIsChangeConcentration: (data: boolean) => void;
  setIsNoConcentration: (data: boolean) => void;
  initUnits: (data: GetPredefinedDataOfMedicationResponse) => void;
  preCheckRefill: (
    medicines: TMedicineIntersection[],
    ikNumber: number,
    isSvPatient: boolean,
    contractId: string | undefined,
    isPrivateSchein: boolean,
    tNotFoundPzn: string,
  ) => Promise<boolean>;
  setIsOpenRefillDialog: (isOpen: boolean) => void;
  prescribeShoppingBag: (
    patient: IPatientProfile | undefined,
    selectedContractDoctor: ISelectedContractDoctor,
    ikNumber: number,
    medicines: MedicineShoppingBagInfo[],
    bsnr?: string,
    doctorId?: string
  ) => Promise<void>;
  setIsCheckedMMI: (value: boolean) => void;
  setStatistics: (isStatistics: boolean) => void;
  hasSubstitution: (request: HasSubstitutionRequest) => Promise<boolean>;
  updateMarkFavourite: (pzn: string, isFavourite: boolean) => void;
  updatePriceComparisonMarkFavourite: (
    pzn: string,
    isFavourite: boolean
  ) => void;
  setIsFavouriteToggle: (isFavourite: boolean) => void;
}

let controller: Nullable<AbortController>;

export const RefillToastKey = 'RefillToastKey';

const initStore: IMedicationStore = {
  medicationResults: undefined,
  isLoading: false,
  isLoadingSorting: false,
  isShowShoppingBag: false,
  isNoConcentraion: false,
  isProcessingRefillMedication: false,
  isAddToRefill: false,
  isOpenChangeMedicationPackageSizeDialog: false,
  isStatistics: false,
  isFavourite: true,
  isDisableFavourite: false,
  packageSize: {
    isNoMoreSizes: false,
  },
  comparison: {
    medicineComparison: undefined,
    medicine: undefined,
    isLoadingMedicine: false,
    isLoadingComparison: false,
  },
  technicalInfo: {
    isLoading: false,
    data: undefined,
  },
  alternative: {
    isLoadingARV: false,
    dataARV: undefined,
    isLoading: false,
    data: undefined,
    arvDetail: undefined,
    medicine: undefined,
  },
  iwwListe: {
    items: undefined,
    details: undefined,
  },
  iwwRegulation: {
    isLoading: false,
    isExist: false,
    data: undefined,
    isLoadingGetLink: false,
  },
  refill: {
    formInfo: undefined,
    originalMedicines: [],
    data: [],
    cache: [],
  },
  isDisplaySearchMedicineResult: false,
  page: 1,
  rowsPerPage: 30,
  selectAllRowsItem: false,
  isChangeConcentration: false,
  medicineQuery: {
    keyword: '',
    type: SearchType.All,
  },
  units: [],
  forms: [],
  isOpenRefillDialog: false,
  isCheckedMMI: false,
  cacheMedicine: {},
};

export const medicationStore = proxy<IMedicationStore>(initStore);

const generateCacheKey = (request: FindByIdRequest) => {
  const properties = Object.keys(request);
  properties.sort();
  const values = properties.map((property) => request[property]);
  return JSON.stringify(values);
};
const findByIdWithCache = async (request: FindByIdRequest) => {
  // To prevent multiple requests for the same time
  const TTL = 10000;
  const { cacheMedicine } = medicationStore;
  const cacheKey = generateCacheKey(request);
  if (cacheMedicine[cacheKey]) return cacheMedicine[cacheKey];
  const response = await findByID(request);
  cacheMedicine[cacheKey] = response;
  setTimeout(() => {
    delete cacheMedicine[cacheKey];
  }, TTL);
  return response;
};

export const medicationActions: IMedicationActions = {
  setRefillFormData: (formInfo: FormInfo | undefined) => {
    medicationStore.refill.formInfo = formInfo;
  },
  setRefillData: (payload: TMedicineIntersection[]) => {
    medicationStore.refill.data = payload;
    medicationStore.refill.originalMedicines = payload;
  },
  setRefillDataAtStep: (payload: TMedicineIntersection, step: number) => {
    if (!medicationStore.refill.cache[step]) {
      medicationStore.refill.cache[step] = {
        originalMedicine: payload,
      };
    } else {
      medicationStore.refill.cache[step] = {
        ...medicationStore.refill.cache[step],
        originalMedicine: payload,
      };
    }
  },
  setRefillComparableDataAtStep(
    payload: TMedicineIntersection[],
    step: number,
    sigKey: string,
    _total?: number
  ) {
    const _target = medicationStore.refill.cache[step];
    const _payload = {
      tableData: {
        [sigKey]: {
          medicines: payload,
          total: _total,
        },
      },
    };

    if (!_target) {
      medicationStore.refill.cache[step] = _payload;
    } else {
      medicationStore.refill.cache[step] = {
        ..._target,
        ..._payload,
      };
    }
  },
  setRefillPage: (step: number, page: number) => {
    medicationStore.refill.cache[step] =
      medicationStore.refill.cache[step] || {};
    medicationStore.refill.cache[step].page = page;
  },
  getRefill: async function (
    step: number,
    getRefillParams: Partial<GetPriceComparisonRequest> & {
      ikNumber: string;
      isDiscount: boolean;
    },
    isPrivateSchein: boolean,
    tNotFoundPzn: string
  ): Promise<string> {
    const patientId = patientFileStore.patient?.current?.id;
    const currentMedicine: TMedicineIntersection =
      medicationStore.refill.data[step];
    medicationStore.refill.cache[step] =
      medicationStore.refill.cache[step] || {};
    const requestObj: GetPriceComparisonRequest = {
      pzn: currentMedicine?.pzn,
      priceComparisonGroupId:
        currentMedicine?.priceInformation?.priceComparisonGroup2!,
      page: medicationStore?.refill.cache[step]?.page || 1,
      ...getRefillParams,
      isSvPatient: !!getRefillParams.isSvPatient,
      contractId: getRefillParams.contractId,
      referenceDate: getRefillParams.referenceDate,
      patientId,
      isPrivateSchein,
      excludedPzns: [currentMedicine?.pzn],
    };
    const rqKey = JSON.stringify({ ...requestObj, step });
    if (medicationStore.refill.cache[step]?.originalMedicine?.skipRefill) {
      return rqKey;
    }
    if (medicationStore.refill.data[step]?.skipRefill) {
      return rqKey;
    }
    if (
      (!medicationStore.refill.cache[step]?.originalMedicine ||
        medicationStore.refill.cache[step]?.originalMedicine?.pzn !==
        currentMedicine?.pzn) &&
      currentMedicine?.pzn &&
      getRefillParams.ikNumber &&
      medicationStore.refill.cache[step]?.originalMedicine !== null
    ) {
      // get original medicine for step
      medicationStore.refill.cache[step].originalMedicine = null;
      const request: FindByIdRequest = {
        pzn: currentMedicine?.pzn,
        ikNumber: `${getRefillParams.ikNumber}`,
        isSvPatient: !!getRefillParams.isSvPatient,
        contractId: getRefillParams.contractId,
        referenceDate: getRefillParams.referenceDate,
        patientId: patientId,
        isPrivateSchein,
      };
      await findByIdWithCache(request)
        .then((rsData) => {
          medicationStore.refill.cache[step].originalMedicine =
            rsData.data as any;
        })
        .catch((err) => {
          medicationActions.setIsProcessingRefillMedication(false);
          medicationActions.setRefillData([]);
          clearToaster(RefillToastKey);
          throw {
            ...err,
            response: {
              ...err.response,
              data: {
                ...err.response.data,
                serverError: tNotFoundPzn,
              }
            }
          };
        })
        .finally(() => {
          medicationStore.comparison.isLoadingMedicine = false;
        });
    }

    if (!medicationStore.refill.cache[step].originalMedicine?.skipRefill) {
      if (
        medicationStore.refill.data?.length <= 0 ||
        !currentMedicine ||
        !currentMedicine?.priceInformation?.priceComparisonGroup2
      ) {
        medicationStore.refill.cache[step] = {
          tableData: {},
          originalMedicine:
            medicationStore.refill.cache[step]?.originalMedicine,
          page: 1,
          isLoading: false,
        };
        return rqKey;
      }
      let currentStepItem = medicationStore.refill.cache[step];
      if (!currentStepItem) {
        const newCache: IRefillData = {
          originalMedicine:
            medicationStore.refill.cache[step]?.originalMedicine,
          tableData: {},
          page: 1,
          isLoading: true,
        };
        medicationStore.refill.cache[step] = newCache;
        currentStepItem = newCache;
      }
      if (!currentStepItem?.tableData || !currentStepItem?.tableData[rqKey]) {
        medicationStore.refill.cache[step].isLoading = true;
        getPriceComparison(requestObj)
          .then(({ data: rsData }) => {
            medicationStore.refill.cache[step].tableData = {
              ...(medicationStore.refill.cache[step].tableData || {}),
              [rqKey]: rsData,
            }
          })
          .catch((err) => {
            throw err;
          })
          .finally(() => {
            medicationStore.refill.cache[step].isLoading = false;
          });
      }
    }

    return rqKey;
  },
  getMedicationResults: (payload: SearchMedicineRequest, isSort) => {
    if (controller) {
      controller.abort();
    }
    controller = new AbortController();
    if (isSort) {
      medicationStore.isLoadingSorting = true;
    } else {
      medicationStore.isLoading = true;
    }
    medicationStore.isDisableFavourite = false;
    const req = { ...payload, isFavourite: medicationStore.isFavourite };
    search(req, { signal: controller.signal })
      .then((res) => {
        if (!res) {
          medicationStore.medicationResults = undefined;
        } else {
          medicationStore.medicationResults = {
            ...res.data,
            medicines: groupByPrice(res.data.medicines ?? []),
          };
          if (!isNull(res?.data?.isHaveFavourite)) {
            const isHaveFavourite = res.data.isHaveFavourite;
            medicationStore.isFavourite = Boolean(isHaveFavourite);

            if (req.isFavourite) {
              medicationStore.isDisableFavourite = !isHaveFavourite;
            }
          }
        }
        if (isSort) {
          medicationStore.isLoadingSorting = false;
        } else {
          medicationStore.isLoading = false;
        }
      })
      .catch((err) => {
        medicationStore.medicationResults = undefined;
        if (err.name !== 'AbortError') {
          if (isSort) {
            medicationStore.isLoadingSorting = false;
          } else {
            medicationStore.isLoading = false;
          }
          throw err;
        }
      })
      .finally(() => {
        controller = null;
      });
  },
  resetMedicationResults: () => {
    medicationStore.medicationResults = undefined;
  },
  getPriceComparison: function (payload: GetPriceComparisonRequest): void {
    medicationStore.comparison.isLoadingComparison = true;
    getPriceComparison(payload)
      .then(({ data: rsData }) => {
        medicationStore.comparison.medicineComparison = {
          ...rsData,
          medicines: groupByPrice(rsData.medicines),
        };
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.comparison.isLoadingComparison = false;
      });
  },
  getMedication: function (payload: FindByIdRequest, tNotFoundPzn: string): void {
    medicationStore.comparison.isLoadingMedicine = true;
    findByIdWithCache(payload)
      .then(({ data: rsData }) => {
        medicationStore.comparison.medicine = [rsData];
        medicationStore.alternative.medicine = rsData;
      })
      .catch((err) => {
        medicationActions.setIsProcessingRefillMedication(false);
        medicationActions.setRefillData([]);
        clearToaster(RefillToastKey);
        throw {
          ...err,
          response: {
            ...err.response,
            data: {
              ...err.response.data,
              serverError: tNotFoundPzn,
            }
          }
        };
      })
      .finally(() => {
        medicationStore.comparison.isLoadingMedicine = false;
      });
  },
  getMedicineTechInformation: function (
    payload: GetTechInformationRequest
  ): void {
    medicationStore.technicalInfo.isLoading = true;
    getTechInformation(payload)
      .then(({ data: rsData }) => {
        medicationStore.technicalInfo.data = rsData;
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.technicalInfo.isLoading = false;
      });
  },
  getAltViewList: function (payload: GetARVRequest): void {
    medicationStore.alternative.isLoadingARV = true;
    getARV(payload)
      .then(({ data: rsData }) => {
        const alternativeItems = rsData.items.filter(
          (item) => item.typeName !== 'IWW-Liste'
        );
        medicationStore.alternative.dataARV = alternativeItems;

        // TODO: consider this logic
        // const iwwListeItems = rsData.items.filter(
        //   (item) => item.typeName === 'IWW-Liste'
        // );
        const iwwListeItems = rsData.items;
        medicationStore.iwwListe.items = iwwListeItems;
        if (iwwListeItems.length > 0) {
          getIWWListeDetails({
            ...payload,
            aRVIds: iwwListeItems.map((item) => item.aRVId),
          })
            .then(({ data: res }) => {
              medicationStore.iwwListe.details = res.items;
            })
            .catch((err) => {
              throw err;
            });
        }
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.alternative.isLoadingARV = false;
      });
  },
  getAlternative: function (payload: GetAlternativeDetailsRequest): void {
    medicationStore.alternative.isLoading = true;
    getAlternativeDetails(payload)
      .then((rsData) => {
        medicationStore.alternative.data = rsData.data;
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.alternative.isLoading = false;
      });
  },
  clearAlternativeInfo: function (): void {
    medicationStore.alternative.isLoading = false;
    medicationStore.alternative.isLoadingARV = false;
    medicationStore.alternative.dataARV = undefined;
    medicationStore.alternative.data = undefined;
    medicationStore.alternative.arvDetail = undefined;
  },
  setArvDetail: function (arvDetail: ARVItem | undefined): void {
    medicationStore.alternative.arvDetail = arvDetail;
  },
  clearAlternativeARVInfo: function (): void {
    medicationStore.alternative.dataARV = undefined;
  },
  clearIWWListeInfo: () => {
    medicationStore.iwwListe = {
      details: [],
      items: [],
    };
  },
  setShowShoppingBag: (showShoppingBag: boolean) => {
    medicationStore.isShowShoppingBag = showShoppingBag;
  },
  checkIwwRegulations: function (payload: GetARVIndicationTreeRequest): void {
    checkARVIndicationTreeExist(payload)
      .then(({ data: rsData }) => {
        medicationStore.iwwRegulation.isExist = rsData.isExist;
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.iwwRegulation.isLoading = false;
      });
  },
  getIwwRegulations: function (payload: GetARVIndicationTreeRequest): void {
    medicationStore.iwwRegulation.isLoading = true;
    const transformData = (items?: ARVIndicationItem[]) => {
      if (!items) {
        return [];
      }
      return items.map((item) => {
        return {
          ...item,
          children: transformData(item.children),
          isExpanding: false,
          isDisplaying: true,
        };
      });
    };

    getARVIndicationTree(payload)
      .then(({ data: rsData }) => {
        medicationStore.iwwRegulation.data = transformData(
          rsData.indicationItems
        );
        if (medicationStore.iwwRegulation.data?.length) {
          medicationStore.iwwRegulation.data[0].isExpanding = true;
          if (medicationStore.iwwRegulation.data[0].children) {
            medicationStore.iwwRegulation.data[0].children[0].isExpanding = true;
          }
        }
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.iwwRegulation.isLoading = false;
      });
  },
  setIwwRegulationsExpanding: function (
    ids: number[],
    isExpanding: boolean
  ): void {
    const traverseTree = (
      items: ARVIndicationExtendedItem[],
      ids: number[]
    ) => {
      let itemToFind = items;
      for (let index = 0; index < ids.length; index++) {
        const indicationId = ids[index];
        const found = itemToFind.find(
          (item) => item.indicationId === indicationId
        );

        if (index !== ids.length - 1) {
          itemToFind = found?.children || [];
        } else {
          return found;
        }
      }

      throw 'should have correct data';
    };

    const foundItem = traverseTree(
      medicationStore.iwwRegulation.data || [],
      ids
    );
    foundItem && (foundItem.isExpanding = isExpanding);
  },
  searchIwwRegulations: function (query: string): void {
    const resetTree = (items: ARVIndicationExtendedItem[]) => {
      items.forEach((item) => {
        item.isExpanding = false;
        item.isDisplaying = true;

        if (item.children) {
          resetTree(item.children);
        }
      });
    };

    if (!query) {
      resetTree(medicationStore.iwwRegulation.data || []);
      return;
    }
    query = query.trim().toLowerCase();

    if (query.length <= 2) {
      resetTree(medicationStore.iwwRegulation.data || []);
      return;
    }

    const searchTree = (
      items: ARVIndicationExtendedItem[],
      q: string
    ): boolean => {
      let isMatchParent = false;
      items.forEach((item) => {
        let isMatch = false;
        if (item.description.toLowerCase().indexOf(q) !== -1) {
          isMatch = true;
        }

        if (item.children) {
          if (searchTree(item.children, q)) isMatch = true;
        }
        if (item.molecules) {
          item.molecules.forEach((mole) => {
            if (
              mole.aTCCode === q ||
              mole.name.toLowerCase().indexOf(q) !== -1 ||
              mole.description.toLowerCase().indexOf(q) !== -1
            ) {
              isMatch = true;
            }
          });
        }
        if (item.documents) {
          item.documents.forEach((doc) => {
            if (doc.name.toLowerCase().indexOf(q) !== -1) {
              isMatch = true;
            }
          });
        }

        if (isMatch) {
          item.isExpanding = true;
          isMatchParent = true;
        } else {
          item.isDisplaying = false;
        }
      });

      return isMatchParent;
    };

    resetTree(medicationStore.iwwRegulation.data || []);
    searchTree(medicationStore.iwwRegulation.data || [], query);
  },
  getIndicationDocumentLink: (payload: GetDocumentLinkRequest) => {
    medicationStore.iwwRegulation.isLoadingGetLink = true;
    getIndicationDocumentLink(payload)
      .then((res) => {
        window.open(`${res.data.documentLink}`, '_blank');
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.iwwRegulation.isLoadingGetLink = false;
      });
  },
  getMoleculeDocumentLink: (payload: GetDocumentLinkRequest) => {
    medicationStore.iwwRegulation.isLoadingGetLink = true;
    getMoleculeDocumentLink(payload)
      .then((res) => {
        window.open(res.data.documentLink, '_blank')?.focus();
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStore.iwwRegulation.isLoadingGetLink = false;
      });
  },
  getARVDocumentLink: (payload: GetDocumentLinkRequest) => {
    getARVDocumentLink(payload)
      .then((res) => {
        window.open(res.data.documentLink, '_blank');
      })
      .catch((err) => {
        throw err;
      });
  },
  getAMRDocumentLink: (payload: GetAMRDocumentLinkRequest) => {
    getAMRDocumentLink(payload)
      .then((res) => {
        window.open(res.data.documentLink, '_blank');
      })
      .catch((err) => {
        throw err;
      });
  },
  setIsProcessingRefillMedication: (payload) => {
    medicationStore.isProcessingRefillMedication = payload;
  },

  setIsAddToRefill: (isAdd: boolean) => {
    medicationStore.isAddToRefill = isAdd;
  },

  // NOTE: package size
  setIsOpenChangeMedicationPackageSizeDialog(payload: boolean) {
    medicationStore.isOpenChangeMedicationPackageSizeDialog = payload;
  },
  async startChangePackageSizesForSingleMedication(
    medicineProductId: number,
    ikNumber: number,
    isSvPatient: boolean,
    contractId?: string,
    pzn?: string
  ) {
    try {
      const resp = await getPackageSizes({
        medicineProductIds: [medicineProductId],
        ikNumber,
        isSvPatient,
        contractId,
        referenceDate: datetimeUtil.dateTimeFormat(
          datetimeUtil.now(),
          YEAR_MONTH_DAY_FORMAT
        ),
      });

      medicationStore.packageSize.changingPackageSizeMedicine = {
        medicineProductId: medicineProductId,
        pzn: pzn!,
      };

      const availableSizes = resp.data?.normSizeCodeList ?? [];

      const _isNoMoreSizes = availableSizes?.length <= 1;

      const _isChangingPackageSizeForSingleMedication =
        availableSizes?.length > 1;

      medicationStore.packageSize.medicationMapWithSizes = {
        [medicineProductId]: availableSizes,
      };

      medicationStore.packageSize.isNoMoreSizes = _isNoMoreSizes;

      medicationStore.isOpenChangeMedicationPackageSizeDialog =
        _isChangingPackageSizeForSingleMedication;
      return resp.data;
    } catch (err) {
      medicationStore.isOpenChangeMedicationPackageSizeDialog = false;
      throw err;
    }
  },
  setIsOpenNoMorePackageSizeDialog(isNoMore: boolean) {
    medicationStore.packageSize.isNoMoreSizes = isNoMore;
  },
  resetPackageSizeStore() {
    medicationStore.packageSize = {
      isNoMoreSizes: false,
      changingPackageSizeMedicine: undefined,
      medicationMapWithSizes: undefined,
    };
  },

  setMedicineQuery: (medicineQuery: IParsedQuery) => {
    medicationStore.medicineQuery = medicineQuery;
  },
  setMedicines: (medicines: SearchMedicineResponse) => {
    medicationStore.medicationResults = medicines;
  },
  setIsDisplaySearchMedicineResult: (state: boolean) => {
    medicationStore.isDisplaySearchMedicineResult = state;
  },
  setPage: (page: number) => {
    medicationStore.page = page;
  },
  setRowsPerPage: (rowsPerPage: number) => {
    medicationStore.rowsPerPage = rowsPerPage;
  },
  setSelectAllRowsItem: (data: boolean) => {
    medicationStore.selectAllRowsItem = data;
  },
  setIsChangeConcentration: (data: boolean) => {
    medicationStore.isChangeConcentration = data;
  },
  setIsNoConcentration: (data: boolean) => {
    medicationStore.isNoConcentraion = data;
  },
  initUnits: (data: GetPredefinedDataOfMedicationResponse) => {
    medicationStore.units = data?.units || [];
    medicationStore.forms = data?.forms || [];
  },
  // PRO-6658 Preview when refilling the prescription form with having updates of discount contracts
  // pre-check refill medication form before refill it
  // if has no medicine to refill, not open price comparison dialog, direct open print preview dialog
  preCheckRefill: async (
    refillMedicines: TMedicineIntersection[],
    ikNumber: number,
    isSvPatient: boolean,
    contractId: string | undefined,
    isPrivateSchein: boolean,
    tNotFoundPzn: string
  ): Promise<boolean> => {
    const patientId = patientFileStore.patient?.current?.id;
    const referenceDate = datetimeUtil.dateTimeFormat(
      datetimeUtil.now(),
      YEAR_MONTH_DAY_FORMAT
    );
    const supportVSST863 = await webWorkerServices.doesContractSupportFunctions(
      ['VSST863'],
      contractId || ''
    );
    if (!supportVSST863) true;

    const findIdPromises = refillMedicines.map((medicine) =>
      findByIdWithCache({
        pzn: String(medicine.pzn),
        ikNumber: String(ikNumber),
        isSvPatient,
        patientId,
        contractId,
        referenceDate,
        isPrivateSchein,
      })
    );

    const hasSubstitutionPromises = refillMedicines.map((medicine) =>
      hasSubstitution({
        pzn: String(medicine.pzn),
        ikNumber: String(ikNumber),
        contractId,
        referenceDate,
      }).then((res) => ({
        pzn: res.data.pzn,
        hasSubstitution: res.data.hasSubstitution,
      }))
    );

    const [pznResult, hasSubstitutionResult] = await Promise.all([
      Promise.all(findIdPromises),
      Promise.all(hasSubstitutionPromises),
    ]).catch((err) => {
      medicationActions.setIsProcessingRefillMedication(false);
      medicationActions.setRefillData([]);
      clearToaster(RefillToastKey);
      throw {
        ...err,
        response: {
          ...err.response,
          data: {
            ...err.response.data,
            serverError: tNotFoundPzn,
          }
        }
      };
    });

    const refillMedicinesMap = new Map(
      refillMedicines.map((item) => [item.pzn, item])
    );
    const hasPznResultMap = new Map(
      hasSubstitutionResult.map((item) => [item.pzn, item.hasSubstitution])
    );

    const medicinesToRefill = pznResult.map(({ data: value }) => {
      const findToCompare = refillMedicinesMap.get(value.pzn);
      if (!findToCompare) {
        return value;
      }
      const isSameColor =
        findToCompare.colorCategory?.drugCategory ===
        value.colorCategory.drugCategory;
      const isSameMedicine = isSameColor;

      const hasSubstitution = hasPznResultMap.get(value.pzn);

      return {
        ...value,
        skipRefill: isSameMedicine && !hasSubstitution,
      } as TMedicineIntersection;
    });

    medicationStore.refill.data = medicinesToRefill;
    const isSkipAllRefill = medicinesToRefill.every((item) => item.skipRefill);

    if (isSkipAllRefill) {
      medicationStore.isOpenRefillDialog = false;
    }

    return isSkipAllRefill;
  },
  setIsOpenRefillDialog: (isOpen: boolean) => {
    medicationStore.isOpenRefillDialog = isOpen;
  },
  prescribeShoppingBag: async (
    patient,
    selectedContractDoctor,
    ikNumber,
    medicines: MedicineShoppingBagInfo[],
    bsnr,
    doctorId
  ) => {
    for (const medicine of medicines) {
      const medicineToPrescribe = {
        ...medicine,
        isEPrescription:
          !!medicine['erezeptItemStatus'] || medicine.isEPrescription,
        id: undefined,
      };
      const request: AddToShoppingBagRequest = {
        patientId: patient?.id,
        doctorId: doctorId || selectedContractDoctor.doctorId,
        contractId: selectedContractDoctor.contractId,
        ikNumber: ikNumber,
        medicine: medicineToPrescribe,
        bsnr,
      };
      const { data: medicineRes } = await addToShoppingBag(request);
      if (medicineRes.medicine.isEPrescription) {
        const { data } = await createBundles({
          doctorId: doctorId || selectedContractDoctor.doctorId!,
          patientId: patient?.id!,
          scheinId: patientFileStore.schein.activatedSchein?.scheinId!,
          treatmentDoctorId: selectedContractDoctor.doctorId!,
          formInfos: [
            {
              ...medicineRes.medicine,
              medicineIDs: [medicineRes.medicine.id!],
              formSetting: '',
              isShowFavHint: false,
            },
          ],
        });
        medicationShoppingBagActions.setBundlesCreated(data.bundleCreateds);
      }
    }
  },
  setIsCheckedMMI: (value) => {
    medicationStore.isCheckedMMI = value;
  },
  setStatistics: (isStatistics: boolean) => {
    medicationStore.isStatistics = isStatistics;
  },
  hasSubstitution: async (request) => {
    const res = await hasSubstitution(request);
    return res.data.hasSubstitution;
  },
  updateMarkFavourite: (pzn: string, isFavourite: boolean) => {
    if (isFavourite) {
      medicationStore.isDisableFavourite = false;
    }
    medicationStore.medicationResults &&
      (medicationStore.medicationResults.medicines =
        medicationStore.medicationResults.medicines.map((m) => {
          if (m.pzn === pzn) {
            return { ...m, isFavourite: isFavourite };
          }
          return m;
        }));
  },
  updatePriceComparisonMarkFavourite: (pzn: string, isFavourite: boolean) => {
    if (isFavourite) {
      medicationStore.isDisableFavourite = false;
    }
    medicationStore.comparison.medicineComparison &&
      (medicationStore.comparison.medicineComparison.medicines =
        medicationStore.comparison.medicineComparison.medicines.map((m) => {
          if (m.pzn === pzn) {
            return { ...m, isFavourite: isFavourite };
          }
          return m;
        }));
  },
  setIsFavouriteToggle: (isFavourite: boolean) => {
    medicationStore.isFavourite = isFavourite;
  },
};

export function useMedicationStore() {
  return useSnapshot(medicationStore);
}

export function useMedicationPackageSizeStore() {
  const store = useMedicationStore();
  return useMemo(() => store.packageSize, [store.packageSize]);
}
