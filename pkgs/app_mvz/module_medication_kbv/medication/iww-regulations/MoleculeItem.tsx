import React, { useMemo, memo } from 'react';
import {
  BodyTextM,
  BodyTextS,
  Flex,
  Link,
  Tooltip,
} from '@tutum/design-system/components';
import {
  ARVLink,
  ARVMoleculeItem,
  GetDocumentLinkRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type MedicationLocales from '@tutum/mvz/locales/en/Medication.json';
import split from 'lodash/split';
import { processDescription } from './DescriptionUtils';
import DocumentItem from './DocumentItem.styled';

export type MoleculeItemProps = {
  item: ARVMoleculeItem;
  onClickDocumentLink: (data: GetDocumentLinkRequest) => void;
  onClickMedicationLink: (atcCode: string) => void;
};

const getTooltip = (
  item: ARVMoleculeItem,
  t: IFixedNamespaceTFunction<keyof typeof MedicationLocales.GBADialog>
) => {
  switch (item.categoryName) {
    case '1':
      return t('standardTooltip');
    case '2':
      return t('reserveDrugTooltip');
    case '3':
      return t('notRecommendedTooltip');
    case '4':
      return t('noIndicationTooltip');
    default:
      return '';
  }
};

const MoleculeItem = ({
  item,
  onClickDocumentLink,
  onClickMedicationLink,
}: MoleculeItemProps) => {
  const { t } = I18n.useTranslation<keyof typeof MedicationLocales.GBADialog>({
    namespace: 'Medication',
    nestedTrans: 'GBADialog',
  });

  const sentenceWithPartsMemoized = useMemo(() => {
    const sentences = split(item.description, '\r\n');
    const sentenceWithParts: (string | ARVLink)[][] = [];
    sentences?.forEach((sentence) => {
      if (item.links) {
        sentenceWithParts.push(processDescription(sentence, item.links));
      } else {
        sentenceWithParts.push([sentence]);
      }
    });
    return sentenceWithParts;
  }, [item.description]);

  return (
    <Flex
      key={item.moleculeId}
      className="indication-item--content molecule-item"
    >
      <Tooltip content={getTooltip(item, t)} placement="right">
        <div
          className={`molecule-item--circle molecule-item--circle-in-${item.colorName}`}
          style={{ backgroundColor: '#' + item.colorHexCode }}
        >
          {item.categoryName}
        </div>
      </Tooltip>

      <Flex column>
        <BodyTextM>{item.name}</BodyTextM>
        {item.documents &&
          item.documents.map((doc) => (
            <DocumentItem
              key={doc.documentId}
              item={doc}
              mainId={item.moleculeId}
              handleOpenIndicationDocument={onClickDocumentLink}
            />
          ))}
        {sentenceWithPartsMemoized.map((parts, sentenceIdx) => (
          <BodyTextS key={sentenceIdx} className="molecule-item--description">
            {parts.map((part, idx) =>
              typeof part === 'string' ? (
                part
              ) : (
                <Link
                  key={idx}
                  className="molecule-item--atc-link"
                  onClick={() =>
                    onClickMedicationLink(part.aTCCode)
                  }
                >
                  {part.description}
                </Link>
              )
            )}
          </BodyTextS>
        ))}
      </Flex>
    </Flex>
  );
};

export default memo(MoleculeItem);
