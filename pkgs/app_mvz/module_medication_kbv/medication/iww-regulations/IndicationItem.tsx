import { BodyTextL, BodyTextM, Flex } from '@tutum/design-system/components';
import { GetDocumentLinkRequest } from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import React from 'react';
import DocumentItem from './DocumentItem.styled';
import MoleculeItem from './MoleculeItem';
import { Collapse, Icon } from '@tutum/design-system/components/Core';
import {
  ARVIndicationExtendedItem,
  medicationActions,
} from '../MedicationKBV.store';

export type IncicationItemProps = {
  item: ARVIndicationExtendedItem;
  parentIds: number[];
  onClickMedicationLink: (atcCode: string) => void;
  handleOpenIndicationDocument: (payload: GetDocumentLinkRequest) => void;
  handleOpenMoleculeDocument: (payload: GetDocumentLinkRequest) => void;
};
const IndicationItem = ({
  item,
  parentIds,
  onClickMedicationLink,
  handleOpenIndicationDocument,
  handleOpenMoleculeDocument,
}: IncicationItemProps) => {
  const isDisplaying = item.isDisplaying;
  if (!isDisplaying) {
    return <></>;
  }

  const isExpanding = item.isExpanding;
  const isChildContentOnly =
    item.children && !item.documents && !item.molecules;
  const handleCollapseExpand = () => {
    medicationActions.setIwwRegulationsExpanding(
      [...parentIds, item.indicationId],
      !item.isExpanding
    );
  };
  return (
    <Flex key={item.indicationId} auto column className={'indication-item'}>
      <Flex
        align="center"
        justify="space-between"
        className={'indication-item--header sl-border-bottom'}
        onClick={handleCollapseExpand}
      >
        {!item.level ? (
          <BodyTextL className="indication-item--title" fontWeight={600}>
            {item.description}
          </BodyTextL>
        ) : (
          <BodyTextM className="indication-item--title" fontWeight={600}>
            {item.description}
          </BodyTextM>
        )}
        <Icon icon={isExpanding ? 'chevron-up' : 'chevron-down'} />
      </Flex>
      <Collapse isOpen={isExpanding}>
        {!!isChildContentOnly &&
          (item?.children || []).map((childItem) => (
            <IndicationItem
              key={childItem.indicationId}
              item={childItem}
              parentIds={[...parentIds, item.indicationId]}
              onClickMedicationLink={onClickMedicationLink}
              handleOpenIndicationDocument={handleOpenIndicationDocument}
              handleOpenMoleculeDocument={handleOpenMoleculeDocument}
            />
          ))}
        {!isChildContentOnly && (
          <Flex column className="indication-item--body sl-border-bottom">
            {!!item.documents &&
              item.documents.map((docItem) => (
                <DocumentItem
                  key={docItem.documentId}
                  className="indication-item--content"
                  item={docItem}
                  mainId={item.indicationId}
                  handleOpenIndicationDocument={handleOpenIndicationDocument}
                />
              ))}

            {!!item.children &&
              item.children.map((childItem) => (
                <IndicationItem
                  key={childItem.indicationId}
                  item={childItem}
                  parentIds={[...parentIds, item.indicationId]}
                  onClickMedicationLink={onClickMedicationLink}
                  handleOpenIndicationDocument={handleOpenIndicationDocument}
                  handleOpenMoleculeDocument={handleOpenMoleculeDocument}
                />
              ))}
            {!!item.molecules &&
              item.molecules.map((moleculeItem) => (
                <MoleculeItem
                  key={moleculeItem.moleculeId}
                  item={moleculeItem}
                  onClickMedicationLink={onClickMedicationLink}
                  onClickDocumentLink={handleOpenMoleculeDocument}
                />
              ))}
          </Flex>
        )}
      </Collapse>
    </Flex>
  );
};

export default IndicationItem;
