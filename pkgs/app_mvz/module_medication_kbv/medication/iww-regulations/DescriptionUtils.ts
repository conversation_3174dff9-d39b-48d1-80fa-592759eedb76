import { ARVLink } from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { split } from 'lodash';

export const splitAndJoinStr = (
  d: string,
  separator: string,
  insertor: ARVLink
) => {
  const newResults: (string | ARVLink)[] = [];
  const parts = split(d, separator);
  if (parts.length > 1) {
    for (let index = 0; index < parts.length; index++) {
      const part = parts[index];
      newResults.push(part);
      if (index != parts.length - 1) {
        newResults.push(insertor);
      }
    }
  } else {
    newResults.push(d);
  }
  return newResults;
};

export const processDescription = (desc: string, links: ARVLink[]) => {
  let results: any[] = [desc];

  links.forEach((link) => {
    const newResults: (string | ARVLink)[] = [];
    results.forEach((d) => {
      const separator = `=\u003e${link.description}`;
      const r = splitAndJoinStr(d, separator, link);
      newResults.push(...r);
    });
    results = newResults;
  });

  return results;
};
