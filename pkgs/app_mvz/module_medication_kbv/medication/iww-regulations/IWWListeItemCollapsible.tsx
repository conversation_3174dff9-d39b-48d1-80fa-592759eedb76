import {
  BodyTextL,
  BodyTextM,
  BodyTextS,
  Flex,
  Link,
} from '@tutum/design-system/components';
import {
  ARVLink,
  IWWListeItem,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import React, { useState } from 'react';
import DocumentItem from './DocumentItem.styled';
import { Collapse, Icon } from '@tutum/design-system/components/Core';
import { processDescription } from './DescriptionUtils';
import moment from 'moment';
import { medicationActions } from '../MedicationKBV.store';
import I18n from '@tutum/infrastructure/i18n';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';

const formatDate = (v?: number) => {
  if (!v) return '--';
  return moment(v).format(DATE_FORMAT);
};
export type IWWListeItemCollapsibleProps = {
  className?: string;
  item: IWWListeItem;
  onClickMedicationLink: (atcCode: string) => void;
};
const IWWListeItemCollapsible = ({
  className,
  item,
  onClickMedicationLink,
}: IWWListeItemCollapsibleProps) => {
  const { t } = I18n.useTranslation<any>({
    namespace: 'Medication',
    nestedTrans: 'IWWRegulations',
  });

  const [isExpanding, setExpanding] = useState(true);
  const handleCollapseExpand = () => setExpanding(!isExpanding);
  const title = item.typeName ? item.typeName : t('titleCollapse');
  const sentenceWithParts: (string | ARVLink)[][] = [];

  if (item.links) {
    sentenceWithParts.push(processDescription(item.hint, item.links));
  } else {
    sentenceWithParts.push([item.hint]);
  }

  const handleOpenIndicationDocument = (payload) => {
    medicationActions.getARVDocumentLink(payload);
  };
  const validityText = item.validTo
    ? t('validityFromTo', {
        fromDate: formatDate(item.validFrom),
        toDate: formatDate(item.validTo),
      })
    : t('validityFrom', {
        fromDate: formatDate(item.validFrom),
        toDate: formatDate(item.validTo),
      });

  return (
    <Flex
      key={item.aRVId}
      auto
      column
      className={`iww-liste-item ${className}`}
    >
      <Flex
        align="center"
        justify="space-between"
        className={'iww-liste-item--header'}
        onClick={handleCollapseExpand}
      >
        <Flex column>
          <BodyTextL className="iww-liste-item--title" fontWeight={600}>
            {t('arzneimittelvereinbarung', {
              title,
            })}
          </BodyTextL>
          <BodyTextS className="iww-liste-item--sub-title">
            {validityText}.{' '}
            {t('createdOn', { date: formatDate(item.createdDate) })}
          </BodyTextS>
        </Flex>

        <Icon icon={isExpanding ? 'chevron-up' : 'chevron-down'} />
      </Flex>
      <Collapse isOpen={isExpanding}>
        <Flex column className="iww-liste-item--body">
          {sentenceWithParts.map((parts, sentenceIdx) => (
            <BodyTextM key={sentenceIdx} className="molecule-item--description">
              {parts.map((part, idx) =>
                typeof part === 'string' ? (
                  part
                ) : (
                  <Link
                    key={idx}
                    className="molecule-item--atc-link"
                    onClick={() =>
                      onClickMedicationLink(part.aTCCode)
                    }
                  >
                    {part.description}
                  </Link>
                )
              )}
            </BodyTextM>
          ))}
          {item.documents &&
            item.documents.map((docItem) => (
              <DocumentItem
                key={docItem.documentId}
                item={docItem}
                mainId={item.aRVId}
                handleOpenIndicationDocument={handleOpenIndicationDocument}
              />
            ))}
        </Flex>
      </Collapse>
    </Flex>
  );
};

export default IWWListeItemCollapsible;
