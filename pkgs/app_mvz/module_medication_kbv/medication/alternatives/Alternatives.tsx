import React, { useEffect, useState, memo } from 'react';
import { IMvzTheme } from '@tutum/mvz/theme';
import { Flex, LoadingState, Svg } from '@tutum/design-system/components';
import { Intent } from '@tutum/design-system/components/Core';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import {
  Sort,
  Order,
  SortField,
  GetARVRequest,
  AlternativeDetailsFilter,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { genColumns, customStyles } from '../medication-table/setting-table';
import Table from '@tutum/design-system/components/Table';
import { useMedicationStore, medicationActions } from '../MedicationKBV.store';
import SingleSelect, {
  IMenuItem,
} from '@tutum/mvz/components/select/single-select/SingleSelect.styled';
import { Classes, Button } from '@tutum/design-system/components/Core';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { Dialog } from '@blueprintjs/core';
import FilterDialog, {
  FilterGlobalStyle,
} from './filter-form/FilterDialog.styled';
import { AltFilterGlobalStyle } from './Alternatives.styled';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import { COLOR } from '@tutum/design-system/themes/styles';

const IconBack = '/images/back.svg';
const IconFilter = '/images/filter.svg';

export interface IAlternativesProps {
  theme?: IMvzTheme;
  className?: string;
  arvInfo: GetARVRequest;
  pzn: string;
  ikNumber: string;
  inputRowsPerPage: number;
  contractId?: string;
  isSVPatient: boolean;
  handleSetPzn: (pzn: string) => void;
  close2ndLayer: () => void;
  handleClose: () => void;
}

const Alternatives = ({
  className,
  inputRowsPerPage,
  handleSetPzn,
  arvInfo,
  ikNumber,
  contractId,
  isSVPatient,
  handleClose,
  close2ndLayer,
  pzn,
  t,
}: IAlternativesProps & II18nFixedNamespace<any>) => {
  const { patient, isSvPatient, schein } = usePatientFileStore();
  const { t: tMedicationTable } = I18n.useTranslation({
    namespace: 'Medication',
    nestedTrans: 'MedicationTable',
  });

  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(inputRowsPerPage);

  const store = useMedicationStore();

  const [selectedARVID, setSelectedARVID] = useState(
    '' + store?.alternative?.arvDetail?.aRVId
  );
  const [currentARVID, setCurrentARVID] = useState('');
  const [isShowFilter, setIsShowFilter] = useState(false);
  const [filter, setFilter] = useState<AlternativeDetailsFilter | null>(null);

  const [sortType, setSortType] = useState<Sort>({
    field: SortField.Price,
    order: Order.Asc,
  });

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPage(1);
    setRowsPerPage(currentRowsPerPage);
  };
  const onChangePage = (page: number) => {
    setPage(page);
  };
  useEffect(() => {
    medicationActions.getAltViewList(arvInfo);
  }, [arvInfo]);
  useEffect(() => {
    if (store?.alternative?.dataARV?.length === 1) {
      setSelectedARVID(store?.alternative?.dataARV[0].aRVId + '');
      medicationActions.setArvDetail(store?.alternative?.dataARV[0]);
    }
  }, store?.alternative?.dataARV);
  useEffect(() => {
    if (selectedARVID) {
      close2ndLayer();
      if (
        arvInfo.bsnr &&
        arvInfo.lanr &&
        arvInfo.pzn &&
        store?.alternative?.arvDetail?.aRVId
      ) {
        medicationActions.getAlternative({
          bsnr: arvInfo.bsnr,
          lanr: arvInfo.lanr,
          pzn: arvInfo.pzn,
          aRVId:
            Number.parseInt(selectedARVID) ||
            store?.alternative?.arvDetail?.aRVId,
          ikNumber,
          page,
          pageSize: rowsPerPage,
          sort: sortType,
          filter: filter!,
          contractId,
          isSvPatient: isSvPatient,
          referenceDate: datetimeUtil.dateTimeFormat(
            datetimeUtil.now(),
            YEAR_MONTH_DAY_FORMAT
          ),
          patientId: patient?.current?.id,
          isPrivateSchein: checkIsPrivateSchein(schein?.activatedSchein),
        });
      }
    }
  }, [
    selectedARVID,
    sortType,
    page,
    rowsPerPage,
    filter,
    schein?.activatedSchein,
  ]);

  useEffect(() => {
    if (!pzn || !ikNumber) return;

    medicationActions.getMedication({
      pzn,
      ikNumber,
      contractId,
      isSvPatient: isSVPatient,
      patientId: patient.current?.id,
      referenceDate: datetimeUtil.dateTimeFormat(
        datetimeUtil.now(),
        YEAR_MONTH_DAY_FORMAT
      ),
      isPrivateSchein: checkIsPrivateSchein(schein?.activatedSchein),
    }, t('notFoundPzn'));
  }, [ikNumber, patient.current?.id, schein?.activatedSchein]);

  const handleSort = (field: SortField) => {
    const sort = {
      field,
      order: sortType?.order === Order.Asc ? Order.Desc : Order.Asc,
    };
    setSortType(sort);
  };

  if (!store.alternative.data || !store.alternative.dataARV) {
    return (
      <>
        <Dialog
          isOpen={true}
          title={null}
          onClose={handleClose}
          className="view-alt-dialog"
          usePortal
          canOutsideClickClose={false}
        >
          <Flex column={true}>
            <Flex className={Classes.DIALOG_BODY} column={true}>
              <h3 className="title">{t('Alternatives.modalTitle')}</h3>
              {store.alternative.isLoadingARV || store.alternative.isLoading ? (
                <LoadingState />
              ) : (
                <>
                  <p>{t('Alternatives.modalDescription')}</p>
                  <div className="view">
                    <label>{t('Alternatives.modalLblView')}</label>
                    <SingleSelect
                      className={Classes.FILL}
                      items={(store?.alternative?.dataARV || []).map(
                        (arvItem) => {
                          return {
                            text: arvItem.typeName,
                            value: arvItem.aRVId,
                          } as unknown as IMenuItem;
                        }
                      )}
                      onItemSelect={(item: IMenuItem) => {
                        medicationActions.setArvDetail(
                          (store?.alternative?.dataARV || []).find(
                            (arv) => `${arv.aRVId}`.trim() === `${item.value}`
                          )
                        );
                        setCurrentARVID(item.value);
                      }}
                      popoverProps={{
                        usePortal: true,
                        minimal: false,
                      }}
                      value={currentARVID}
                      placeholder={t('Alternatives.modalLblViewPlaceholder')}
                    />
                  </div>
                  <div className="sl-align-layout bottom">
                    <Button
                      intent={Intent.NONE}
                      className="sl-btn-cancel"
                      onClick={handleClose}
                    >
                      {t('Alternatives.modalCptCancel')}
                    </Button>
                    <Button
                      intent={Intent.PRIMARY}
                      onClick={() => {
                        setSelectedARVID(`${currentARVID}`);
                      }}
                      disabled={!currentARVID}
                    >
                      {t('Alternatives.modalCptContinue')}
                    </Button>
                  </div>
                </>
              )}
            </Flex>
          </Flex>
        </Dialog>
        <AltFilterGlobalStyle />
      </>
    );
  }

  const cssConditional = [
    {
      when: (item) => item['pzn'] === store.comparison.medicine?.[0].pzn,
      style: { background: COLOR.WARNING_LIGHT },
    },
  ];

  const pharmacySalePrice =
    store.alternative.medicine?.priceInformation?.pharmacySalePrice ??
    +(store.alternative.medicine?.priceInformation?.svPrices?.pharmacySalePrice || 0);

  return (
    <>
      <Flex column className={className}>
        <Flex column auto className="outer-wrapper">
          <div className="wrapper">
            <Flex className="header">
              <Flex
                className="back"
                align="center"
                onClick={() => {
                  return handleClose();
                }}
              >
                <Svg src={IconBack} style={{ marginRight: 8 }} />
                <span className="page-title">
                  {t('Alternatives.title', {
                    tradeName:
                      store.alternative.medicine?.productInformation?.name ?? '',
                    size: `${store.alternative.medicine?.packagingInformation
                      ?.packageSize?.nop ?? ''
                      } (${store.alternative.medicine?.packagingInformation
                        ?.amountText ?? ''
                      })`,
                    price: medicationUtil.formatPrice(pharmacySalePrice * 100),
                  })}
                </span>
              </Flex>

              <Button
                icon={<Svg src={IconFilter} />}
                className="filter"
                text={t('Alternatives.lblFilter')}
                onClick={() => setIsShowFilter(true)}
              />
            </Flex>
            <div className="cmp-table comparison">
              <Table
                className={`sl-table ${store.isStatistics ? 'sl-statistics' : ''
                  }`}
                columns={genColumns(
                  tMedicationTable,
                  (medicine) => handleSetPzn(medicine.pzn),
                  handleSort,
                  sortType,
                  true
                )}
                data={(store.alternative.data?.medicines || []).map(
                  (item, index) => ({
                    ...item,
                    id: `${item.id}_${index}`,
                  })
                )}
                customStyles={customStyles}
                noHeader
                fixedHeader
                conditionalRowStyles={cssConditional}
                // overflowY
                pagination
                paginationDefaultPage={page}
                paginationPerPage={rowsPerPage}
                paginationServer
                paginationResetDefaultPage
                paginationTotalRows={store.alternative.data?.total || 1}
                onChangePage={onChangePage}
                onChangeRowsPerPage={onChangeRowsPerPage}
              />
            </div>
            {store.alternative.isLoading ? <LoadingState /> : null}
          </div>
        </Flex>
      </Flex>
      <FilterDialog
        isOpen={isShowFilter}
        sizeList={store?.alternative?.data?.sizeFilters}
        dosaFormList={store?.alternative?.data?.dosaFormFilters}
        data={filter}
        setFilter={setFilter}
        onClose={() => setIsShowFilter(false)}
      />
      <FilterGlobalStyle />
    </>
  );
};

export default memo(
  I18n.withTranslation(Alternatives, {
    namespace: 'Medication',
  })
);
