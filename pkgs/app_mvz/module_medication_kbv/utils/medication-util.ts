import { groupBy } from '@tutum/design-system/infrastructure/utils';
import {
  AddToShoppingBagRequest,
  ColorCategory,
  DrugInformation,
  IntakeInterval,
  MedicineShoppingBagInfo,
  MedicineType,
  PriceInformation,
  ProductInformation,
} from '@tutum/hermes/bff/app_mvz_medicine';
import { GetMMIDBStatusResponse } from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { FormType } from '@tutum/hermes/bff/legacy/medicine_common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { transformPrice } from '@tutum/infrastructure/shared/price-format';
import {
  IFormField,
  IFormFieldType,
  IFormInfo,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
} from '@tutum/mvz/constant/form';
import {
  getSettings,
  saveSettings,
  removeSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import isNil from 'lodash/isNil';
import isEqual from 'lodash/isEqual';
import moment from 'moment';
import { TMedicineIntersection } from '../medication/MedicationKBV.store';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { FormName } from '@tutum/hermes/bff/legacy/form_common';

export type IconType = 'LABEL' | 'ICON';
export const USE_MUSTER_16_FOR_OTC_DRUG_FOR_LESS_18_YEARS_OLD = 'VSST1212';
export const HAVE_TO_SHOW_FAV_HINT_ON_FORM = 'FORM1042';
export type IntakeInterValType = 'MORNING' | 'AFTERNOON' | 'EVENING' | 'NIGHT';
export const EREZEPT = 'E_REZEPT';
export const CHECK_UPDATE_MMI = 'CHECK_UPDATE_MMI';
export const now = datetimeUtil.date();
export enum DrugColorEnum {
  GREEN = 'gruent',
  RED = 'rot',
  BLUE = 'blau',
  ORANGE = 'orange',
  CALCULATED_GREEN = 'gruenberechnet',
  KEINE = 'keine',
}

function getComponentsAndSubstants(
  drugInformation?: DrugInformation
): Array<{ componentName: string; substances: string[] }> {
  if (!drugInformation || !drugInformation.components) {
    return [];
  }
  return drugInformation.components.map((comp) => {
    let finalList = comp.substances?.filter(
      (item) => item.equivalentSubstance === 1 && item.substanceType === 1
    );

    if (finalList?.length === 0) {
      finalList = comp.substances?.filter((item) => item.substanceType === 1);
    }

    const substances = finalList?.map(
      (item) =>
        `${item.name} ${transformFormatNumber(item.amount, 0).replace(
          ',00',
          ''
        )} ${item.unit}`
    );

    return {
      componentName: `${comp.absoluteAmount} ${comp.absoluteUnit} (${comp.componentName})`,
      substances,
    };
  });
}

function shouldShowDiscountPrice(colorCategory?: ColorCategory) {
  if (!colorCategory || !colorCategory.drugCategory) {
    return false;
  }
  switch (colorCategory.drugCategory.trim().toLowerCase()) {
    case DrugColorEnum.BLUE:
    case DrugColorEnum.CALCULATED_GREEN:
      return true;

    default:
      return false;
  }
}

function formatPrice(price: number): string {
  const finalPrice = price / 100;
  const formatted = Intl.NumberFormat('de', {
    style: 'currency',
    currency: 'EUR',
  }).format(finalPrice);
  return formatted;
}

function getPrice(
  t: IFixedNamespaceTFunction,
  priceInfo?: PriceInformation,
  colorCategory?: ColorCategory
): { discount: boolean; display: string } {
  if (shouldShowDiscountPrice(colorCategory)) {
    return {
      discount: true,
      display: t('discount'),
    };
  }

  const price = (priceInfo && priceInfo.pharmacySalePrice) || 0;
  return {
    display: formatPrice(price),
    discount: false,
  };
}

function getDrugColor(colorCategory: ColorCategory) {
  if (
    !colorCategory ||
    !colorCategory.drugCategory ||
    !colorCategory.drugCategory.trim()
  ) {
    return '#FFFFFF';
  }

  switch (colorCategory.drugCategory.trim().toLowerCase()) {
    case DrugColorEnum.RED:
      return '#F55E5E';
    case DrugColorEnum.ORANGE:
      return '#E59700';
    case DrugColorEnum.BLUE:
      return '#356BF5';
    case DrugColorEnum.GREEN:
      return '#00A55E';
    case DrugColorEnum.CALCULATED_GREEN:
      return '#D5F3E6';

    default:
      return '#FFFFFF';
  }
}

function getCoPaymentColorAndRelationalOperator(
  priceInformation: PriceInformation
): { colorCode: string; operation: string } {
  if (!priceInformation) {
    return { colorCode: 'unset', operation: '' };
  }
  const { pharmacySalePrice, fixedAmount = 0 } = priceInformation;
  if (pharmacySalePrice === fixedAmount) {
    return { colorCode: '#028A4B', operation: '(=)' };
  }
  if (pharmacySalePrice > fixedAmount) {
    return { colorCode: '#028A4B', operation: '(>)' };
  }
  return { colorCode: '#D84B4B', operation: '(<)' };
}

function groupMedicineByForm(medicines: MedicineShoppingBagInfo[]) {
  if (!medicines) {
    return {};
  }

  const groupByFormType = groupBy(
    medicines,
    (item: MedicineShoppingBagInfo) => item.currentFormType
  );
  return groupByFormType;
}

function parseIntakeInterval(str: string): IntakeInterval {
  const regex = /^(\d+|\d+(\/|,)\d+)+((-(\d+|\d+(\/|,)\d+)){0,3})?$/;
  let parsedIntakeInterval: {
    morning?: number;
    afternoon?: number;
    evening?: number;
    night?: number;
  } = {
    morning: undefined,
    afternoon: undefined,
    evening: undefined,
    night: undefined,
  };
  const freeText = str;
  if (regex.test(str)) {
    const arrInterval = str.split('-').map((item) => item.trim());
    parsedIntakeInterval = {
      morning: (arrInterval[0] && parseFloat(arrInterval[0])) || 0,
      afternoon: (arrInterval[1] && parseFloat(arrInterval[1])) || 0,
      evening: (arrInterval[2] && parseFloat(arrInterval[2])) || 0,
      night: (arrInterval[3] && parseFloat(arrInterval[3])) || 0,
    };
  }
  return {
    ...parsedIntakeInterval,
    freetext: freeText,
  };
}

function getFormLabel(
  formType: string,
  t: IFixedNamespaceTFunction<keyof typeof MedicationI18n.MedicineDetail>
) {
  switch (formType) {
    case FormType.GREZ:
      return {
        label: t('gRez'),
        className: 'sl-gRez',
      };
    case FormType.BTM:
      return {
        label: t('btm'),
        className: 'sl-btm',
      };
    case FormType.KREZ:
      return {
        className: 'sl-kRez',
        label: t('kRez'),
      };
    case FormType.TPrescription:
      return {
        className: 'sl-tRez',
        label: t('tRez'),
      };
    case FormType.Private:
      return {
        className: 'sl-private',
        label: t('private'),
      };
    case FormType.Muster16aBay:
      return {
        className: 'sl-kRez',
        label: t('muster16aBay'),
      };
    case FormName.KREZ:
      return {
        className: 'sl-kRez',
        label: t(formType),
      };
    case FormName.GREZ:
      return {
        label: t('gRez'),
        className: 'sl-gRez',
      };
    case FormName.Private:
      return {
        className: 'sl-private',
        label: t('private'),
      };
    default:
      return {
        label: '',
        className: '',
      };
  }
}

function getDosageForm(
  productInformation: ProductInformation | undefined,
  name: string
): string {
  if (!productInformation || !productInformation.dosageForm || name) {
    return '';
  }
  return productInformation.dosageForm;
}

function getDisplayIntakeInterval(
  medicine: MedicineShoppingBagInfo,
  t: IFixedNamespaceTFunction
) {
  if (!medicine || !medicine.intakeInterval) {
    return '';
  }
  const { morning, evening, afternoon, night, freetext, dJ } =
    medicine.intakeInterval;
  if (dJ) {
    if (medicine.currentFormType === FormType.BTM) {
      return `>>${t('gema')}<<`;
    } else {
      return `>>Dj<<`;
    }
  }
  if (
    morning != undefined &&
    afternoon != undefined &&
    evening != undefined &&
    night != undefined
  ) {
    return `>>${morning}-${afternoon}-${evening}-${night}<<`;
  }

  if (freetext) {
    return `>>${freetext}<<`;
  }

  return '';
}

function displayERezeptIntakeInterval(medicine: MedicineShoppingBagInfo) {
  if (!medicine || !medicine.intakeInterval) {
    return '';
  }
  const { morning, evening, afternoon, night, freetext, dJ } =
    medicine.intakeInterval;
  if (dJ) {
    return `Dosieranweisung / Medikationsplan mitgegeben`;
  }
  if (
    morning != undefined &&
    afternoon != undefined &&
    evening != undefined &&
    night != undefined
  ) {
    return `Dosierung: ${morning}-${afternoon}-${evening}-${night}`;
  }

  if (freetext) {
    return `Dosierung: ${freetext}`;
  }

  return '';
}

function getIntakeInterValue(
  intakeInterval: IntakeInterval,
  type: IntakeInterValType
) {
  if (!intakeInterval) {
    return '';
  }
  const { morning, night, afternoon, evening } = intakeInterval;

  switch (type) {
    case 'MORNING':
      return morning || '0';
    case 'AFTERNOON':
      return afternoon || '0';
    case 'NIGHT':
      return night || '0';
    case 'EVENING':
      return evening || '0';

    default:
      return '';
  }
}

function processFormValue(
  formField: IFormField,
  formValue: FORM_SETTING_OBJECT,
  setFormValue: Function,
  newVal: FORM_SETTING_TYPE,
  dependGroup: string[] = [],
  additionalUpdate?: { fieldName: string; fieldValue: string }
) {
  const cloned = { ...formValue };
  if (
    formField.name.startsWith('checkbox') ||
    formField.name.startsWith('toggle')
  ) {
    cloned[formField.name] = newVal ?? !formValue[formField.name];
    if (dependGroup.length) {
      dependGroup.forEach((name) => {
        if (name !== formField.name) {
          cloned[name] = false;
        }
      });
    }
  } else {
    cloned[formField.name] = newVal;
  }

  if (additionalUpdate) {
    const { fieldName, fieldValue } = additionalUpdate;
    cloned[fieldName] = fieldValue;
  }
  if (!isEqual(cloned, formValue)) {
    setFormValue(cloned);
  }
}

function preProcessFormValue(formInfo: IFormInfo) {
  const formInfoData = {};
  formInfo?.meta?.fields.map((item: IFormField) => {
    switch (item.type) {
      case IFormFieldType.CHECK_BOX:
      case IFormFieldType.TOGGLE_NUMBER:
        formInfoData[item.name] = false;
        break;
      default:
        formInfoData[item.name] = '';
    }
  });
  return formInfoData;
}

const mappingMedicineToShoppingBagRequest = (
  patientId: string,
  doctorId: string,
  ikNumber: number,
  medicine: TMedicineIntersection,
  contractId?: string,
  isSVPatient?: boolean
): AddToShoppingBagRequest => ({
  patientId,
  doctorId,
  ikNumber,
  contractId,
  medicine: {
    type: medicine.type ?? (isSVPatient ? MedicineType.HPM : MedicineType.KBV),
    pzn: medicine.pzn,
    name:
      medicine.productInformation?.['shortName'] ||
      medicine.productInformation?.['name'] ||
      '',
    quantity: 1,
    packagingInformation: medicine?.packagingInformation,
    productInformation: medicine?.productInformation,
    currentFormType: medicine.productInformation?.['formType'] ?? FormType.KREZ,
    drugInformation: medicine?.drugInformation,
    textInformation: medicine?.textInformation,
    priceInformation: medicine?.priceInformation,
    colorCategory: medicine?.colorCategory,
    kBVMedicineId: extractMedicineKBVId(medicine),
  },
});

function extractMedicineKBVId(medicine: TMedicineIntersection) {
  return medicine.kBVMedicineId != null
    ? medicine.kBVMedicineId
    : !isNaN(parseInt(medicine.id, 10))
      ? parseInt(medicine.id, 10)
      : undefined;
}

function transformFormatNumber(
  numb: number | string,
  minimumFractionDigits = 3,
  maximumFractionDigits = 3,
  option = {}
): string {
  if (isNil(numb)) return '';
  const formatted = Intl.NumberFormat('de', {
    ...option,
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(+numb);
  return formatted;
}

const getFormName = (medicine?: MedicineShoppingBagInfo) => {
  return medicine?.isEPrescription ? EREZEPT : medicine?.currentFormType;
};

// FE have handled this logic
const isMMIUpdated = (data: GetMMIDBStatusResponse) => {
  if (!data?.priceDate || !data?.drugDate) return false;

  const priceDate = moment(data?.priceDate, DATE_FORMAT).toDate();
  const drugDate = moment(data?.drugDate, DATE_FORMAT).toDate();

  if (priceDate.getDate() === 1 && drugDate.getDate() === 1) {
    if (
      moment.duration(moment(now).endOf('D').diff(priceDate)).asDays() < 22 &&
      moment.duration(moment(now).endOf('D').diff(drugDate)).asDays() < 22
    ) {
      return true;
    }
    return false;
  }
  if (
    priceDate.getDate() === 15 &&
    drugDate.getDate() === 15 &&
    priceDate.getMonth() === drugDate.getMonth() &&
    moment.duration(moment(now).endOf('D').diff(priceDate)).asMonths() <= 1
  ) {
    const month = priceDate.getMonth();
    if (month === now.getMonth()) return true;
    if (month === now.getMonth() - 1 && now.getDate() < 9) return true;
    if (month === 11 && now.getMonth() === 0 && now.getDate() < 9) return true;
  }
  return false;
};

const validToShowPopupMMI = async (res: GetMMIDBStatusResponse) => {
  const {
    data: { settings },
  } = await getSettings({ settings: [CHECK_UPDATE_MMI] });
  const savedTime = settings?.[CHECK_UPDATE_MMI]
    ? +settings[CHECK_UPDATE_MMI]
    : null;
  const isUpdated = isMMIUpdated(res);

  if (isUpdated) {
    savedTime && removeSettings({ settings: [CHECK_UPDATE_MMI] });
    return false;
  }
  if (!isUpdated && (now.getDate() === 1 || now.getDate() === 15)) {
    if (moment.duration(moment(now).endOf('D').diff(savedTime)).asDays() < 2) {
      return false;
    }
    saveSettings({
      settings: {
        [CHECK_UPDATE_MMI]: `${now.getTime()}`,
      },
    });
    return true;
  }
  if (savedTime) {
    return false;
  }
  saveSettings({
    settings: {
      [CHECK_UPDATE_MMI]: `${now.getTime()}`,
    },
  });
  return true;
};

// getCoPaymentValue use for 1st view only!
// second layer still show no copayment but first view show 0 value
const getCoPaymentValue = (pricePatientPayment: number): number => {
  if (pricePatientPayment == -1) return 0;
  return pricePatientPayment;
};

export const medicationUtil = {
  transformFormatNumber,
  extractMedicineKBVId,
  formatPrice,
  getPrice,
  getDrugColor,
  getCoPaymentColorAndRelationalOperator,
  groupMedicineByForm,
  getComponentsAndSubstants,
  parseIntakeInterval,
  getFormLabel,
  getDosageForm,
  getDisplayIntakeInterval,
  getIntakeInterValue,
  processFormValue,
  preProcessFormValue,
  mappingMedicineToShoppingBagRequest,
  transformPrice,
  getFormName,
  // isMMIUpdated,
  validToShowPopupMMI,
  getCoPaymentValue,
  displayERezeptIntakeInterval,
};
