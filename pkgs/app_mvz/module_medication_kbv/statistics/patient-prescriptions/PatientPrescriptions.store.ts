import { proxy, useSnapshot } from 'valtio';
import {
  GetMedicationPrescriptionRequest,
  getMedicationPrescription,
  exportMedicationPrescription,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_statistic';
import * as medicine from '@tutum/hermes/bff/app_mvz_medicine';
import * as patient_overview from '@tutum/hermes/bff/app_mvz_patient_overview';
import * as profile_bff from '@tutum/hermes/bff/app_profile';

export interface IMedicationStatResults {
  id: string;
  pzn?: string;
  name: string;
  drugInformation?: medicine.DrugInformation;
  prescribeDate: number;
  doctor: profile_bff.EmployeeProfileResponse;
  patient: patient_overview.PatientOverview;
  priceInformation?: medicine.PriceInformation;
  quantity: number;
}

interface IMedicationStatStore {
  medicationStatResults: {
    list: IMedicationStatResults[];
    page: number;
    total: number;
    totalRecords: number;
  };
  isLoading: boolean;
  isLoadingSorting: boolean;
  isLoadingExport: boolean;
}

interface IMedicationStatActions {
  search: (payload: GetMedicationPrescriptionRequest, isSort?: boolean) => void;
  export: (payload: GetMedicationPrescriptionRequest) => void;
}

const initStore: IMedicationStatStore = {
  medicationStatResults: {
    list: [],
    page: 0,
    total: 0,
    totalRecords: 0,
  },
  isLoading: false,
  isLoadingSorting: false,
  isLoadingExport: false,
};

export const medicationStatStore = proxy<IMedicationStatStore>(initStore);

export const medicationActions: IMedicationStatActions = {
  search: (payload, isSort) => {
    if (isSort) {
      medicationStatStore.isLoadingSorting = true;
    } else {
      medicationStatStore.isLoading = true;
    }
    getMedicationPrescription(payload)
      .then((res) => {
        const { medicationPrescribeResponses, doctors, patients } = res.data;
        medicationStatStore.medicationStatResults.list =
          medicationPrescribeResponses?.map((item) => ({
            id: item.id,
            pzn: item.pzn,
            name: item.name,
            drugInformation: item.drugInformation,
            prescribeDate: item.prescribeDate,
            doctor:
              doctors?.find((doctorItem) => doctorItem.id === item.doctorId) ||
              null!,
            patient:
              patients?.find(
                (patientItem) => patientItem.patientId === item.patientId
              ) || null!,
            priceInformation: item.priceInformation,
            quantity: item.quantity,
          }));
        medicationStatStore.medicationStatResults.totalRecords =
          res.data.totalRecords;
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        if (isSort) {
          medicationStatStore.isLoadingSorting = false;
        } else {
          medicationStatStore.isLoading = false;
        }
      });
  },
  export: (payload) => {
    medicationStatStore.isLoadingExport = true;
    exportMedicationPrescription(payload)
      .then((res) => {
        window.open(`${res.data.documentLink}`, '_blank');
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        medicationStatStore.isLoadingExport = false;
      });
  },
};

export function useMedicationStatStore() {
  return useSnapshot(medicationStatStore);
}
