import React, { memo, useState } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import { IMvzTheme } from '@tutum/mvz/theme';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import {
  Flex,
  BodyTextS,
  Button,
  FormGroup2,
} from '@tutum/design-system/components';
import DateRangeInput from '@tutum/mvz/components/customize/date-range-input/DateRangeInput.styled';
import { Checkbox, MenuItem } from '@tutum/design-system/components/Core';
import { MultiSelect } from '@tutum/design-system/components/Select';
import { IFilterValues } from '../PatientPrescriptions';
import { SearchingType } from '@tutum/hermes/bff/legacy/app_mvz_patient_search';
import { PatientSearchApi } from '@tutum/infrastructure/resource/PatientSearchResource';
import { debounce } from '@tutum/design-system/infrastructure/utils';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import { MainGroup } from '@tutum/hermes/bff/common';
import { SCHEIN_MAIN_GROUPS } from '@tutum/mvz/_utils/scheinFormat';

export interface IFiltersProps {
  className?: string;
  theme?: IMvzTheme;
  filterValues: IFilterValues;
  handleSetFilters: (filterValues: IFilterValues) => void;
  handleResetFilters: () => void;
  handleApplySearch: () => void;
}

export interface IMenuItem {
  value: string | number | undefined;
  name: string;
  dOB: string;
}

const renderMultipleOptions =
  (selectedItems: IMenuItem[]) =>
    (item: IMenuItem, { handleClick, modifiers }) => {
      if (!modifiers.matchesPredicate) {
        return null;
      }
      const isChecked = !!selectedItems?.find(
        (selectedItem) => selectedItem.value === item.value
      );
      return (
        <MenuItem
          multiline
          className={`sl-multi-options ${isChecked ? 'sl-checked' : ''}`}
          key={item.value}
          text={
            <>
              <Flex align="center">
                <Checkbox
                  checked={isChecked}
                  onClick={(e) => {
                    e.stopPropagation();
                    return false;
                  }}
                />
                <Flex column>
                  <BodyTextS className="sl-text-item">{item.name}</BodyTextS>
                  <BodyTextS className="sl-text-item">{item.dOB}</BodyTextS>
                </Flex>
              </Flex>
            </>
          }
          onClick={handleClick}
          shouldDismissPopover={false}
        />
      );
    };

const Filters = ({
  className,
  filterValues,
  handleSetFilters,
  handleResetFilters,
  handleApplySearch,
}: IFiltersProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof MedicationI18n.PatientPrescriptions
  >({
    namespace: 'Medication',
    nestedTrans: 'PatientPrescriptions',
  });
  const { t: tCommon } = I18n.useTranslation<any>({
    namespace: 'Common',
  });
  const { selectedMainGroup = [] } = filterValues;

  const [isOpenListPatients, setIsOpenListPatients] = useState(false);
  const [listPatients, setListPatients] = useState<IMenuItem[]>([]);
  const [groupMap, setGroupMap] = useState({
    KV: [MainGroup.KV].every((el) => selectedMainGroup.includes(el)),
    SV: [MainGroup.HZV, MainGroup.FAV].every((el) =>
      selectedMainGroup.includes(el)
    ),
    PRIVATE: [MainGroup.PRIVATE, MainGroup.IGEL].every((el) =>
      selectedMainGroup.includes(el)
    ),
  });

  const handleSearchPatient = debounce(500, (query: string) => {
    if (!query?.length) {
      setListPatients([]);
      setIsOpenListPatients(false);
    } else {
      PatientSearchApi.searchPatients({
        searchingKeyword: query.toLowerCase().split(' ').join(', '),
        searchingType: SearchingType.PatientName,
      })
        .then((response) => {
          const mapData: IMenuItem[] = (response.data.patients || []).map(
            (patient) => ({
              name: `${patient.lastName}, ${patient.firstName}`,
              value: patient.id,
              dOB: getDateOfBirth(patient.dateOfBirth).value,
            })
          );
          setListPatients(mapData);
          setIsOpenListPatients(true);
        })
        .catch((err) => {
          console.error(err);
          setListPatients([]);
          setIsOpenListPatients(true);
        });
    }
  });

  const handleRemoveItem = (removedItem) => {
    handleSetFilters({
      ...filterValues,
      selectedPatients:
        filterValues?.selectedPatients?.filter(
          (item: IMenuItem) =>
            item.value !== removedItem.value && item.name !== removedItem
        ) || [],
    });
  };

  const handleSelectItem = (selectedItem: IMenuItem) => {
    if (
      !filterValues?.selectedPatients?.find(
        (item) => item.value === selectedItem.value
      )
    ) {
      handleSetFilters({
        ...filterValues,
        selectedPatients: [...(filterValues?.selectedPatients || []), selectedItem],
      });
    } else {
      handleRemoveItem(selectedItem);
    }
  };

  const onSelectSchein = (mainGroup: 'KV' | 'SV' | 'PRIVATE') => {
    const updatedGroupMap = {
      ...groupMap,
      [mainGroup]: !groupMap[mainGroup],
    };
    setGroupMap(updatedGroupMap);
    const selectedMainGroup = Object.entries(updatedGroupMap)
      .filter(([_, isSelected]) => isSelected)
      .flatMap(([group]) => SCHEIN_MAIN_GROUPS[group]);
    handleSetFilters({
      ...filterValues,
      selectedMainGroup,
    });
  };

  return (
    <Flex className={className} column p={24} w={416} gap={16}>
      <FormGroup2 label={t('dateOfPrescription')}>
        <DateRangeInput
          className="sl-date-range-input"
          onChange={(fromDate, toDate) => {
            handleSetFilters({
              ...filterValues,
              dateRangevalue: [fromDate, toDate],
            });
          }}
          locale="de"
          dateRangevalue={filterValues?.dateRangevalue}
          placeholder="DD.MM.YYYY - DD.MM.YYYY"
          minimal
        />
      </FormGroup2>
      <FormGroup2 label={t('patients')}>
        <MultiSelect
          tagRenderer={(item: IMenuItem) => {
            return item.name;
          }}
          placeholder={tCommon('Input.search')}
          className="sl-patients"
          items={listPatients}
          selectedItems={filterValues?.selectedPatients || []}
          popoverProps={{
            isOpen: isOpenListPatients,
            position: 'bottom',
            hasBackdrop: true,
            backdropProps: {
              onClick: () => setIsOpenListPatients(false),
            },
          }}
          itemRenderer={renderMultipleOptions(filterValues?.selectedPatients || [])}
          onItemSelect={handleSelectItem}
          onQueryChange={(val) => {
            handleSearchPatient(val);
          }}
          tagInputProps={{
            onRemove: handleRemoveItem,
          }}
          noResults={
            <MenuItem
              text={tCommon('MultiSelect.noResult')}
              shouldDismissPopover={false}
              style={{
                background: '#ccc',
                cursor: 'not-allowed',
              }}
            />
          }
          resetOnSelect
        />
      </FormGroup2>
      <FormGroup2 label={t('scheinType')}>
        <Flex align="center" gap={20}>
          <Checkbox
            checked={groupMap.KV}
            onClick={(e) => {
              e.stopPropagation();
              return onSelectSchein('KV');
            }}
          >
            {t('kv')}
          </Checkbox>
          <Checkbox
            checked={groupMap.SV}
            onClick={(e) => {
              e.stopPropagation();
              return onSelectSchein('SV');
            }}
          >
            {t('hzvOrFav')}
          </Checkbox>
          <Checkbox
            checked={groupMap.PRIVATE}
            onClick={(e) => {
              e.stopPropagation();
              return onSelectSchein('PRIVATE');
            }}
          >
            {t('privateOrIgel')}
          </Checkbox>
        </Flex>
      </FormGroup2>
      <Flex justify="flex-end" gap={8}>
        <Button intent="primary" outlined onClick={handleResetFilters}>
          {tCommon('ButtonActions.reset')}
        </Button>
        <Button intent="primary" onClick={handleApplySearch}>
          {tCommon('ButtonActions.apply')}
        </Button>
      </Flex>
    </Flex>
  );
};

export default memo(Filters);
