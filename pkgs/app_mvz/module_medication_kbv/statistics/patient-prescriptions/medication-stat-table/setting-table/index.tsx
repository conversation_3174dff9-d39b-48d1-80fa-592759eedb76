import React from 'react';
import {
  IDataTableStyles,
  IDataTableColumn,
} from '@tutum/design-system/components/Table';
import moment from 'moment';

import { Flex, BodyTextM, Svg, Avatar } from '@tutum/design-system/components';
import {
  Sort,
  SortField,
  Order,
} from '@tutum/hermes/bff/app_mvz_medicine_statistic';
import { IMedicationStatResults } from '../../PatientPrescriptions.store';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import TableAction from '@tutum/design-system/components/Table/TableAction/TableAction.styled';
import { TableActionItem } from '@tutum/design-system/components/Table/TableAction/TableAction';
import { COLOR } from '@tutum/design-system/themes/styles';

const SortableIcon = '/images/sortable-icon.svg';
const SortAscIcon = '/images/sort-ascending.svg';
const SortDescIcon = '/images/sort-descending.svg';
const UserIcon = '/images/user-gray.svg';

const renderSortIcon = (sortType: Sort, sortField: SortField) => {
  if (sortType?.field !== sortField) return SortableIcon;
  if (sortType?.order === Order.Asc) return SortAscIcon;
  return SortDescIcon;
};

export const genColumns = (
  t: IFixedNamespaceTFunction,
  handleSort,
  sortType: Sort,
  gotoPatientPage
): IDataTableColumn<IMedicationStatResults>[] => [
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('dateOfPrescription')}</span>
          <Svg
            src={renderSortIcon(sortType, SortField.PrescribeDate)}
            style={{ cursor: 'pointer' }}
            onClick={() => handleSort(SortField.PrescribeDate)}
          />
        </Flex>
      ),
      selector: (row) => row['date_of_prescription'],
      maxWidth: '174px',
      format: (row: IMedicationStatResults) =>
        row.prescribeDate ? moment(row.prescribeDate).format(DATE_FORMAT) : '',
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('prescribingDoctor')}</span>
        </Flex>
      ),
      cell: (row: IMedicationStatResults) => (
        <Flex>
          {row.doctor && (
            <Flex>
              <Avatar initial={row.doctor.initial} className="sl-avatar" />
              <BodyTextM>{nameUtils.getDoctorName(row.doctor)}</BodyTextM>
            </Flex>
          )}
        </Flex>
      ),
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('patientReference')}</span>
        </Flex>
      ),
      cell: (row: IMedicationStatResults) => (
        <Flex column>
          <BodyTextM
            color={COLOR.TEXT_INFO}
            fontWeight="Bold"
            style={{ cursor: 'pointer' }}
            onClick={() => gotoPatientPage(row.patient.patientId)}
          >
            {nameUtils.getPatientNameFromPersonalInfo(row.patient)}
          </BodyTextM>
          <BodyTextM fontSize={12} color={COLOR.TEXT_SECONDARY_NAVAL}>
            {getDateOfBirth(row.patient.dateOfBirth).value}
          </BodyTextM>
        </Flex>
      ),
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('insuranceNo')}</span>
        </Flex>
      ),
      maxWidth: '128px',
      cell: (row: IMedicationStatResults) => (
        <Flex>
          <BodyTextM>{row.patient.insureNumber}</BodyTextM>
        </Flex>
      ),
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('ATCCode')}</span>
        </Flex>
      ),
      maxWidth: '88px',
      cell: (row: IMedicationStatResults) => (
        <Flex>
          <BodyTextM>{row.drugInformation?.aTC || ''}</BodyTextM>
        </Flex>
      ),
    },
    {
      name: (
        <Flex align="center" justify="flex-end">
          <span>{t('amount')}</span>
        </Flex>
      ),
      style: {
        justifyContent: 'flex-end',
      },
      maxWidth: '90px',
      cell: (row: IMedicationStatResults) => (
        <Flex>
          <BodyTextM>{row.quantity}</BodyTextM>
        </Flex>
      ),
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('tradeName')}</span>
        </Flex>
      ),
      cell: (row: IMedicationStatResults) => (
        <Flex>
          <BodyTextM>{row.name}</BodyTextM>
        </Flex>
      ),
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('activeSubstance')}</span>
        </Flex>
      ),
      width: '304px',
      cell: (row: IMedicationStatResults) => {
        const activeSubstances = (
          row.drugInformation?.components?.[0]?.substances || []
        )
          .filter((datum) => datum.substanceType === 1)
          .slice(0, 3);

        return (
          <Flex column>
            {activeSubstances.map((substance, index) => {
              let text = substance.name;

              if (substance.amount && substance.unit) {
                text += ` ${substance.amount}${substance.unit}`;
              }

              if (index !== activeSubstances.length - 1) {
                text += ',';
              }

              return <BodyTextM key={index}>{text}</BodyTextM>;
            }) || ''}
          </Flex>
        );
      },
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('PZN')}</span>
        </Flex>
      ),
      maxWidth: '80px',
      selector: (row) => row.pzn || '',
    },
    {
      name: (
        <Flex align="center" justify="flex-end">
          <span>{t('price')}</span>
        </Flex>
      ),
      maxWidth: '88px',
      style: {
        justifyContent: 'flex-end',
      },
      selector: (row) => row['price'],
      format: (row: IMedicationStatResults) =>
        row.priceInformation // dont' show for case freeText
          ? medicationUtil.formatPrice(
            row.priceInformation.pharmacySalePrice * 100
          )
          : null,
    },
    {
      minWidth: '40px',
      maxWidth: '40px',
      cell: (row) => {
        const actions: TableActionItem[] = [
          {
            id: 'gotoPatientProfile',
            label: t('gotoPatientProfile'),
            icon: <Svg src={UserIcon} />,
            onClick: () => gotoPatientPage(row.patient.patientId),
          },
        ];
        return <TableAction actions={actions} />;
      },
    },
  ];

export const customStyles: IDataTableStyles = {
  rows: {
    style: {
      paddingLeft: '8px',
      minHeight: '40px',
    },
  },
  headRow: {
    style: {
      paddingLeft: '8px',
      textTransform: 'uppercase',
      border: 'none',
    },
  },
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
    },
  },
  cells: {
    style: {
      padding: '8px',
      alignItems: 'flex-start',
    },
  },
};
