import React, { memo, useState, useEffect } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import { IMvzTheme } from '@tutum/mvz/theme';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { BodyTextL, Flex, Svg } from '@tutum/design-system/components';
import { <PERSON><PERSON>, Drawer } from '@tutum/design-system/components/Core';
import {
  medicationActions,
  useMedicationStatStore,
} from './PatientPrescriptions.store';
import {
  Sort,
  Order,
  SortField,
} from '@tutum/hermes/bff/app_mvz_medicine_statistic';
import { IMenuItem } from './filters/Filters';
import Filters from './filters/Filters.styled';
import MedicationStatTable from './medication-stat-table';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { MainGroup } from '@tutum/hermes/bff/common';

const filter = '/images/filter.svg';
const download = '/images/download.svg';

export interface IPatientPrescriptionsProps {
  className?: string;
  theme?: IMvzTheme;
}

export interface IFilterValues {
  dateRangevalue: [Date, Date];
  selectedPatients?: IMenuItem[];
  selectedMainGroup?: MainGroup[];
}

const quarterAdjustment = (datetimeUtil.date().getMonth() % 3) + 1;
const lastQuarterEndDate = datetimeUtil
  .subtract({ months: quarterAdjustment })
  .endOf('month');
const lastQuarterStartDate = lastQuarterEndDate
  .clone()
  .subtract({ months: 3 })
  .startOf('month')
  .toDate();

const initialFilterValues: IFilterValues = {
  dateRangevalue: [lastQuarterStartDate, datetimeUtil.date()],
  selectedPatients: [],
  selectedMainGroup: [],
};

const PatientPrescriptions = ({ className }: IPatientPrescriptionsProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof MedicationI18n.PatientPrescriptions
  >({
    namespace: 'Medication',
    nestedTrans: 'PatientPrescriptions',
  });

  const [isOpenFilter, setOpenFilter] = useState(false);
  const [filterValues, setFilterValues] = useState(initialFilterValues);
  const [filterValuesTemp, setFilterValuesTemp] = useState(initialFilterValues);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortType, setSortType] = useState<Sort>({
    field: SortField.PrescribeDate,
    order: Order.Desc,
  });
  const store = useMedicationStatStore();

  const handleSearchValuesApi = (
    values: IFilterValues,
    page,
    pageSize,
    sort,
    isSort?: boolean
  ) => {
    medicationActions.search(
      {
        page,
        pageSize,
        prescribeFrom:
          datetimeUtil.startOf(values?.dateRangevalue?.[0], 'day').unix() *
          1000 || undefined,
        prescribeTo:
          datetimeUtil.endOf(values?.dateRangevalue?.[1], 'day').unix() *
          1000 || undefined,
        patientIds: values.selectedPatients?.map(
          (patient) => patient.value
        ) as string[],
        sort,
        scheinMainGroups: values.selectedMainGroup!,
      },
      isSort
    );
  };

  useEffect(() => {
    handleSearchValuesApi(filterValues, page, rowsPerPage, sortType);
  }, [page, rowsPerPage]);

  const handleSetFilters = (filterValues: IFilterValues) => {
    setFilterValuesTemp(filterValues);
  };

  const handleApplySearch = () => {
    setFilterValues(filterValuesTemp);
    setPage(1);
    handleSearchValuesApi(filterValuesTemp, 1, rowsPerPage, sortType);
    setOpenFilter(false);
  };

  const handleResetFilters = () => {
    setFilterValues(initialFilterValues);
    setFilterValuesTemp(initialFilterValues);
    handleSearchValuesApi(initialFilterValues, page, rowsPerPage, sortType);
    setOpenFilter(false);
  };

  const handleSort = (field: SortField) => {
    const sort = {
      field,
      order:
        sortType?.field !== field
          ? Order.Desc
          : sortType?.order === Order.Asc
            ? Order.Desc
            : Order.Asc,
    };
    setSortType(sort);
    handleSearchValuesApi(filterValues, page, rowsPerPage, sort, true);
  };

  const handleExport = () => {
    medicationActions.export({
      prescribeFrom:
        datetimeUtil.startOf(filterValues?.dateRangevalue?.[0], 'day').unix() *
        1000 || undefined,
      prescribeTo:
        datetimeUtil.endOf(filterValues?.dateRangevalue?.[1], 'day').unix() *
        1000 || undefined,
      patientIds: filterValues?.selectedPatients?.map(
        (patient) => patient.value
      ) as string[],
      sort: sortType,
      scheinMainGroups: [],
    });
  };

  return (
    <div className={className}>
      <Flex p="24px 16px" justify="space-between">
        <Button
          onClick={() => setOpenFilter(true)}
          minimal
          outlined
          icon={<Svg src={filter} />}
        >
          {t('filters')}
        </Button>
        <Button
          minimal
          outlined
          icon={<Svg src={download} />}
          onClick={handleExport}
          loading={store?.isLoadingExport}
        >
          {t('export')}
        </Button>
      </Flex>
      <MedicationStatTable
        medicationStatResults={store?.medicationStatResults}
        isLoading={store?.isLoading}
        isLoadingSorting={store?.isLoadingSorting}
        page={page}
        rowsPerPage={rowsPerPage}
        setPage={setPage}
        setRowsPerPage={setRowsPerPage}
        sortType={sortType}
        handleSort={handleSort}
      />

      <Drawer
        size="40%"
        title={
          <BodyTextL padding="10px 0" fontWeight="ExtraBold">
            Filter
          </BodyTextL>
        }
        isOpen={isOpenFilter}
        canEscapeKeyClose={false}
        onClose={() => setOpenFilter(false)}
      >
        <Filters
          filterValues={filterValuesTemp}
          handleSetFilters={handleSetFilters}
          handleResetFilters={handleResetFilters}
          handleApplySearch={handleApplySearch}
        />
      </Drawer>
    </div>
  );
};

export default memo(PatientPrescriptions);
