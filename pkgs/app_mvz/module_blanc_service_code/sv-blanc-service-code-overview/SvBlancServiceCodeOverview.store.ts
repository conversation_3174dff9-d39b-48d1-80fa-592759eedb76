import {
  activeBlankService,
  ActiveBlankServiceRequest,
  BlankService,
  deactiveBlankService,
  DeactiveBlankServiceRequest,
  getBlankServices,
} from '@tutum/hermes/bff/legacy/app_mvz_blank_service';
import {
  BlankService as BlankServiceContract,
  Contract,
  ContractMetaData,
  getContracts,
  getContractsHasFunctions,
  GetContractsHasFunctionsRequest,
  GetContractsRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_contract';
import { getContractDoctorGroup } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import {
  DoctorParticipateContract,
  DoctorParticipateStatus,
} from '@tutum/hermes/bff/service_domains_doctor_participate';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { IEmployeeProfile, UserType } from '@tutum/mvz/types/profile.type';
import uniqBy from 'lodash/uniqBy';
import { proxy, useSnapshot } from 'valtio';

export interface ISvBlancServiceCodeActions {
  getContracts: () => void;
  getServiceCode: (contractId: string) => void;
  getContractsHasFunctions: (currentLoggedInUser: IEmployeeProfile) => void;
  getGetBlankServices: () => void;
  activeBlancService: (values: BlankService) => void;
  deActiveBlankService: () => void;
  setCurrentSelectedServiceCode: (selectedServiceCode: BlankService) => void;
  reset: () => void;
}

type BlankServiceContractExtended = BlankServiceContract & {
  chargeSystemId: string;
};

export interface ISvBlancServiceCodeOverviewStore {
  isLoading: boolean;
  contracts?: ContractMetaData[];
  blankServiceCodes?: BlankServiceContractExtended[];
  contractHasFunctions?: Contract[];
  blancServices?: BlankService[];
  currentSelectedServiceCode?: BlankService;
  contractDoctorGroups: DoctorParticipateContract[];
}

const initStore: ISvBlancServiceCodeOverviewStore = {
  isLoading: false,
  contractDoctorGroups: [],
};

export const svBlancServiceCodeOverviewStore =
  proxy<ISvBlancServiceCodeOverviewStore>(initStore);

export const svBlancServiceCodeActions: ISvBlancServiceCodeActions = {
  getContracts: async () => {
    const request: GetContractsRequest = {
      selectedDate: datetimeUtil.date().getUTCMilliseconds(),
    };
    const response = await getContracts(request);
    svBlancServiceCodeOverviewStore.contracts = response.data.contracts;
  },
  getServiceCode: async (contractId: string) => {
    const contract = await webWorkerServices.getContractById(contractId);
    const doctors = svBlancServiceCodeOverviewStore.contractDoctorGroups.find(
      (conDoc) => conDoc.contractID === contractId
    )?.doctors;

    if (!doctors) {
      return;
    }

    const chargeSystemIds = doctors.map((doc) => doc.chargeSystemId);

    const blankServiceCodes: BlankServiceContractExtended[] = [];
    contract.chargeSystems.forEach((chargeSystem) => {
      if (chargeSystemIds.indexOf(chargeSystem.id) !== -1) {
        blankServiceCodes.push(
          ...(chargeSystem.blankServices || []).map((s) => ({
            ...s,
            chargeSystemId: chargeSystem.id,
          }))
        );
      }
    });
    contract.moduleChargeSystems.forEach((chargeSystem) => {
      if (chargeSystemIds.indexOf(chargeSystem.id) !== -1) {
        blankServiceCodes.push(
          ...(chargeSystem.blankServices || []).map((s) => ({
            ...s,
            chargeSystemId: chargeSystem.id,
          }))
        );
      }
    });
    svBlancServiceCodeOverviewStore.blankServiceCodes = blankServiceCodes;
  },
  getContractsHasFunctions: async (currentLoggedInUser) => {
    const request: GetContractsHasFunctionsRequest = {
      functionIds: ['ABRD936', 'ABRD939'],
    };
    const response = await getContractsHasFunctions(request);
    const resp = await getContractDoctorGroup({
      contractIds: [],
      doctorIds: [],
      statuses: [DoctorParticipateStatus.Active],
    });
    const contracts = [
      ...((currentLoggedInUser.hasHzvContracts &&
        currentLoggedInUser.hzvContracts) ||
        []),
      ...((currentLoggedInUser.hasFavContracts &&
        currentLoggedInUser.favContracts) ||
        []),
    ];
    const contractDoctorGroups = (
      resp.data.doctorParticipateContracts || []
    ).filter(
      (participateContract) =>
        currentLoggedInUser.types.includes(UserType.MFA) ||
        contracts.some(
          (contract) => contract.contractId === participateContract.contractID
        )
    );
    svBlancServiceCodeOverviewStore.contractDoctorGroups = contractDoctorGroups;
    const availableContractIds = contractDoctorGroups.map(
      (con) => con.contractID
    );
    svBlancServiceCodeOverviewStore.contractHasFunctions = uniqBy(
      response.data.contracts,
      'contractId'
    ).filter((con) => availableContractIds.indexOf(con.contractId) !== -1);
  },
  getGetBlankServices: async () => {
    svBlancServiceCodeOverviewStore.blancServices = [];
    getBlankServices({}).then((response) => {
      svBlancServiceCodeOverviewStore.blancServices =
        response.data.blankServices;
    });
  },

  activeBlancService: async (values: BlankService) => {
    const request: ActiveBlankServiceRequest = {
      contractId: values.contractId,
      chargeSystemId: values.chargeSystemId,
      code: values.code,
      description: values.description,
      price: Number(values.price),
    };
    const response = await activeBlankService(request);
    return response;
  },

  deActiveBlankService: async () => {
    const request: DeactiveBlankServiceRequest = {
      contractId:
        svBlancServiceCodeOverviewStore.currentSelectedServiceCode?.contractId!,
      chargeSystemId:
        svBlancServiceCodeOverviewStore.currentSelectedServiceCode
          ?.chargeSystemId!,
      code: svBlancServiceCodeOverviewStore.currentSelectedServiceCode?.code!,
    };
    const response = await deactiveBlankService(request);
    svBlancServiceCodeActions.reset();
    return response;
  },
  setCurrentSelectedServiceCode: (selectedServiceCode: BlankService) => {
    svBlancServiceCodeOverviewStore.currentSelectedServiceCode =
      selectedServiceCode;
  },

  reset: () => {
    svBlancServiceCodeOverviewStore.currentSelectedServiceCode = undefined;
  },
};

export function useSvBlancServiceCodeOverviewStore() {
  return useSnapshot(svBlancServiceCodeOverviewStore);
}
