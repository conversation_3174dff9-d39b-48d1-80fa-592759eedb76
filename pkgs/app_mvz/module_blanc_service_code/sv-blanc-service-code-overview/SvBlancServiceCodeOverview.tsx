import { withRouter, useRouter } from 'next/router';
import React, { memo, useEffect, useState } from 'react';

import {
  alertSuccessfully,
  Box,
  Button,
  Flex,
  H1,
  Svg,
} from '@tutum/design-system/components';
import { Alignment, Classes } from '@tutum/design-system/components/Core';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import SvBlancServiceCodeDialog from '../sv-blanc-service-code-dialog/SvBlancServiceCodeDialog.styled';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { BlankService } from '@tutum/hermes/bff/legacy/app_mvz_blank_service';
import { Dialog } from '@tutum/design-system/components/Core';
import {
  svBlancServiceCodeActions,
  useSvBlancServiceCodeOverviewStore,
} from './SvBlancServiceCodeOverview.store';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import {
  TableAction,
  TableActionItem,
} from '@tutum/design-system/components/Table/TableAction/TableAction';
import { COLOR } from '@tutum/design-system/themes/styles';
// import type svBlancServiceCodeI18n from '@tutum/mvz/locales/en/SvBlancServiceCode.json';
// import { useListenBlankServiceCode } from '../../../tutum-hermes/bff/app_mvz_blank_service';

const MoreIcon = '/images/more-vertical.svg';
const EditIcon = '/images/edit-2.svg';
const TrashBinRedSVG = '/images/trash-bin-red.svg';
const PlusIcon = '/images/plus-white.svg';

interface BlankServiceData extends BlankService {
  index?: number;
  id?: string;
}

const genColumns = (
  t,
  handleOnEdit,
  handleDeactivate
): IDataTableColumn<BlankServiceData>[] => [
  {
    name: (
      <Flex className="column-title">
        {t('SvBlancServiceCodeOverview.serviceCode')}
      </Flex>
    ),
    maxWidth: '160px',
    style: { position: 'relative' },
    cell: (row) => {
      return <Flex>{row.code}</Flex>;
    },
  },
  {
    name: (
      <Flex className="column-title">
        {t('SvBlancServiceCodeOverview.description')}
      </Flex>
    ),
    minWidth: '130px',
    style: { position: 'relative' },
    cell: (row) => {
      return <Flex>{row.description}</Flex>;
    },
  },
  {
    name: (
      <Flex className="column-title">
        {t('SvBlancServiceCodeOverview.contract')}
      </Flex>
    ),
    maxWidth: '200px',
    style: { position: 'relative' },
    cell: (row: BlankServiceData) => {
      return <Flex>{row.contractId}</Flex>;
    },
  },
  {
    name: (
      <Flex className="column-title">
        {t('SvBlancServiceCodeOverview.price')}
      </Flex>
    ),
    maxWidth: '160px',
    style: { position: 'relative' },
    cell: (row) => {
      return (
        <Flex w="100%" className="serviceCode-price" justify="flex-end">
          {medicationUtil.transformPrice(row.price)}
        </Flex>
      );
    },
  },
  {
    minWidth: '40px',
    maxWidth: '40px',
    style: { position: 'relative' },
    cell: (row) => {
      const actions: TableActionItem[] = [
        {
          id: 'edit',
          label: t('SvBlancServiceCodeOverview.edit'),
          icon: <Svg src={EditIcon} />,
          onClick: () => handleOnEdit(row),
        },
        {
          id: 'remove',
          hoverColor: 'danger',
          label: t('SvBlancServiceCodeOverview.deactivate'),
          icon: <Svg src={TrashBinRedSVG} />,
          onClick: () => handleDeactivate(row),
        },
      ];

      return <TableAction actions={actions} />;
    },
  },
];

export interface ISvBlancServiceCodeOverviewProps {}

const SvBlancServiceCodeOverview = ({
  t,
  className,
}: any & II18nFixedNamespace<any>) => {
  useEffect(() => {
    svBlancServiceCodeActions.getGetBlankServices();
  }, []);

  const router = useRouter();

  const toasterRef = useToaster();

  const store = useSvBlancServiceCodeOverviewStore();

  const [openDialog, setOpenDialog] = useState<boolean>(false);

  useEffect(() => {
    const query = router.query;
    if (query.activate === 'true') {
      setOpenDialog(true);
    }
  }, []);

  const [confirmDeactivateOpen, setConfirmDeactivateOpen] =
    useState<boolean>(false);

  const handleOnEdit = (row: BlankService) => {
    svBlancServiceCodeActions.setCurrentSelectedServiceCode(row);
    setOpenDialog(true);
  };

  const handleDeactivate = (row: BlankService) => {
    svBlancServiceCodeActions.setCurrentSelectedServiceCode(row);
    setConfirmDeactivateOpen(true);
  };

  const confirmDeactivate = async () => {
    await svBlancServiceCodeActions.deActiveBlankService();
    setConfirmDeactivateOpen(false);
    alertSuccessfully(t('SvBlancServiceCodeOverview.deactivateSuccess'), {
      toaster: toasterRef,
    });
    svBlancServiceCodeActions.getGetBlankServices();
  };

  return (
    <Box className={getCssClass(className)}>
      <Flex align="center" className="sl-Header_Section">
        <H1>{t('SvBlancServiceCodeOverview.title')}</H1>
      </Flex>
      <Flex className="header-section">
        <Button
          large
          icon={<Svg src={PlusIcon} />}
          alignText={Alignment.LEFT}
          text={t('SvBlancServiceCodeOverview.active')}
          intent="primary"
          style={{
            paddingTop: '10px',
            paddingBottom: '10px',
            marginRight: '24px',
          }}
          onClick={() => setOpenDialog(true)}
          className="active-button"
        />
      </Flex>
      <Flex className="sl-table">
        <Table
          columns={genColumns(t, handleOnEdit, handleDeactivate)}
          data={(store.blancServices || []).map((item, index) => ({
            ...item,
            id: `${item.code}_${index}`,
            index,
          }))}
          persistTableHead
          progressPending={store.isLoading}
          noHeader
          fixedHeader
          pagination
          paginationResetDefaultPage
          conditionalRowStyles={[
            {
              when: (row: BlankServiceData) => row.index! % 2 !== 0,
              style: {
                backgroundColor: COLOR.BACKGROUND_HOVER,
              },
            },
          ]}
        />
      </Flex>
      {openDialog && (
        <SvBlancServiceCodeDialog
          isCreate={!store.currentSelectedServiceCode}
          isOpen={true}
          onClose={() => {
            setOpenDialog(false);
            svBlancServiceCodeActions.reset();
          }}
          currentSelectedServiceCode={store.currentSelectedServiceCode}
        ></SvBlancServiceCodeDialog>
      )}
      {confirmDeactivateOpen && (
        <Dialog
          title={t('SvBlancServiceCodeConfirmDialog.title')}
          isOpen={confirmDeactivateOpen}
          onClose={() => setConfirmDeactivateOpen(false)}
          canOutsideClickClose={false}
        >
          <Flex className={Classes.DIALOG_BODY}>
            {t('SvBlancServiceCodeConfirmDialog.content')}
          </Flex>
          <Flex justify="space-between" className={Classes.DIALOG_FOOTER}>
            <Box w="45%">
              <Button
                fill
                onClick={() => setConfirmDeactivateOpen(false)}
                style={{ backgroundColor: 'white', color: 'black' }}
              >
                {t('SvBlancServiceCodeConfirmDialog.cancel')}
              </Button>
            </Box>
            <Box w="45%">
              <Button
                fill
                onClick={confirmDeactivate}
                style={{
                  backgroundColor: COLOR.TAG_BACKGROUND_RED,
                  color: 'white',
                }}
              >
                {t('SvBlancServiceCodeConfirmDialog.yes')}
              </Button>
            </Box>
          </Flex>
        </Dialog>
      )}
    </Box>
  );
};

export default memo(
  withRouter(
    I18n.withTranslation(SvBlancServiceCodeOverview, {
      namespace: 'SvBlancServiceCode',
    })
  )
);
