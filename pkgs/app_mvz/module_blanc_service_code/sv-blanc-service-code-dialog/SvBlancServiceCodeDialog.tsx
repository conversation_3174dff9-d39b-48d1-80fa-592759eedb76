import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  Box,
  Button,
  Flex,
  Modal,
  ModalSize,
  coreComponents,
  ReactSelect,
  IMenuItem,
  FormGroup2,
  alertSuccessfully,
  LeaveConfirmModal,
} from '@tutum/design-system/components';
import {
  Classes,
  InputGroup,
  Intent,
} from '@tutum/design-system/components/Core';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { Contract } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { BlankService } from '@tutum/hermes/bff/legacy/app_mvz_blank_service';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { Field, Form, Formik, FormikProps } from 'formik';
import {
  svBlancServiceCodeActions,
  useSvBlancServiceCodeOverviewStore,
} from '../sv-blanc-service-code-overview/SvBlancServiceCodeOverview.store';
import { scaleSpace, scaleSpacePx } from '@tutum/design-system/styles';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { isEmpty } from 'lodash';

const CustomSingleValue = (props) => {
  return (
    <coreComponents.SingleValue {...props}>
      {props.data?.value}
    </coreComponents.SingleValue>
  );
};

const CustomOption = (props) => {
  return (
    <coreComponents.Option {...props}>
      <Flex>
        <Box
          style={{ minWidth: scaleSpacePx(20) }}
          className={Classes.MENU_ITEM_LABEL}
        >
          {props.data?.value}
        </Box>
        <Box
          auto
          className={`${Classes.TEXT_OVERFLOW_ELLIPSIS} ${Classes.FILL}`}
        >
          {props.data?.label}
        </Box>
      </Flex>
    </coreComponents.Option>
  );
};

export interface ISvBlancServiceCodeDialogProps {
  className?: string;
  isCreate: boolean;
  isOpen: boolean;
  onClose: () => void;
  currentSelectedServiceCode: BlankService | undefined;
}

const SvBlancServiceCodeDialog = ({
  className,
  isOpen,
  currentSelectedServiceCode,
  t,
  isCreate,
  onClose,
}: ISvBlancServiceCodeDialogProps & II18nFixedNamespace<any>) => {
  const store = useSvBlancServiceCodeOverviewStore();
  const toasterRef = useToaster();
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();

  const initialValues = {
    ...currentSelectedServiceCode,
    code: currentSelectedServiceCode?.code!,
    description: currentSelectedServiceCode?.description!,
    contractId: currentSelectedServiceCode?.contractId!,
    price: currentSelectedServiceCode?.price!,
  };

  useEffect(() => {
    if (!isEmpty(currentLoggedInUser)) {
      svBlancServiceCodeActions.getContractsHasFunctions(currentLoggedInUser);
    }
  }, [currentLoggedInUser]);

  const onValidateForm = () => {};

  const onSaveForm = async (values: BlankService) => {
    await svBlancServiceCodeActions.activeBlancService(values);
    onClose();
    svBlancServiceCodeActions.getGetBlankServices();
    alertSuccessfully(t('SvBlancServiceCodeDialog.createSuccess'), {
      toaster: toasterRef,
    });
  };

  const onSaveEditForm = async (values: BlankService) => {
    await svBlancServiceCodeActions.activeBlancService(values);
    onClose();
    svBlancServiceCodeActions.getGetBlankServices();
    alertSuccessfully(t('SvBlancServiceCodeDialog.updateSuccess'), {
      toaster: toasterRef,
    });
  };

  const formikRef = useRef<FormikProps<typeof initialValues>>(null);
  const [isOpenConfirm, setIsOpenConfirm] = useState(false);

  function closeConfirmDialog() {
    if (formikRef.current?.dirty) {
      return setIsOpenConfirm(true);
    }
    onClose();
  }

  const renderForm = (formikProps: FormikProps<BlankService>) => {
    const { isSubmitting, errors, touched, submitCount } = formikProps;
    return (
      <Form>
        <Flex column gap={scaleSpace(4)} p={scaleSpace(4)}>
          <Flex gap={16} style={{ padding: '0 1px' }}>
            <FormGroup2
              label={t('SvBlancServiceCodeDialog.contract')}
              name="contractId"
              submitCount={submitCount}
              errors={errors}
              touched={touched}
              className="sv-input"
            >
              <Field name="contractId">
                {({ field, form }) => (
                  <ReactSelect
                    id="contractId"
                    instanceId="contractId"
                    isDisabled={!isCreate}
                    isSearchable={true}
                    selectedValue={field.value}
                    items={(store.contractHasFunctions || [])
                      .map((item) => ({
                        label: item.contractName,
                        value: item.contractId,
                        ...item,
                      }))
                      .sort((a, b) => a.label.localeCompare(b.label))}
                    onItemSelect={(item: IMenuItem & Contract) => {
                      form.setFieldValue(field.name, item.value);
                      form.setFieldValue('code', '');
                      form.setFieldValue('description', '');
                      form.setFieldValue('price', '');
                      svBlancServiceCodeActions.getServiceCode(
                        String(item.value)
                      );
                    }}
                  />
                )}
              </Field>
            </FormGroup2>

            <FormGroup2
              label={t('SvBlancServiceCodeDialog.serviceCode')}
              name="code"
              submitCount={submitCount}
              errors={errors}
              touched={touched}
              className="sv-input"
            >
              <Field name="code">
                {({ field, form }) => (
                  <ReactSelect
                    id="code"
                    instanceId="code"
                    isDisabled={!isCreate}
                    isSearchable={true}
                    selectedValue={field.value}
                    items={(store.blankServiceCodes || []).map((item) => ({
                      value: item.code,
                      label: item.description,
                    }))}
                    onItemSelect={(item: IMenuItem) => {
                      form.setFieldValue(field.name, item.value);
                      const serviceCode = store.blankServiceCodes?.find(
                        (blankServiceItem) =>
                          blankServiceItem.code === item.value
                      );
                      if (!serviceCode) return;
                      form.setFieldValue(
                        'description',
                        serviceCode.description
                      );
                      form.setFieldValue(
                        'chargeSystemId',
                        serviceCode.chargeSystemId
                      );
                    }}
                    components={{
                      Option: CustomOption,
                      SingleValue: CustomSingleValue,
                    }}
                  />
                )}
              </Field>
            </FormGroup2>
          </Flex>
          <Flex>
            <FormGroup2
              label={t('SvBlancServiceCodeDialog.description')}
              name="description"
              submitCount={submitCount}
              errors={errors}
              touched={touched}
              className="sv-input"
            >
              <Field name="description">
                {({ field, form }) => (
                  <InputGroup
                    {...field}
                    onChange={(e) => {
                      form.setFieldValue(field.name, e.target.value);
                    }}
                    data-tab-id={field.name}
                  />
                )}
              </Field>
            </FormGroup2>
          </Flex>
          <Flex>
            <FormGroup2
              label={t('SvBlancServiceCodeDialog.price')}
              name="price"
              submitCount={submitCount}
              errors={errors}
              touched={touched}
            >
              <Field name="price">
                {({ field, form }) => (
                  <NumberInput
                    isFloat
                    defaultValue={field.value || ''}
                    onValueChange={({ value }) =>
                      form.setFieldValue(field.name, +value || null)
                    }
                  />
                )}
              </Field>
            </FormGroup2>
          </Flex>
        </Flex>
        <Flex className={Classes.DIALOG_FOOTER} justify="flex-end">
          <Button
            large
            outlined
            intent={Intent.PRIMARY}
            loading={isSubmitting}
            onClick={closeConfirmDialog}
          >
            {t('SvBlancServiceCodeDialog.cancel')}
          </Button>
          <Button
            large
            type="submit"
            intent={Intent.PRIMARY}
            loading={isSubmitting}
          >
            {isCreate
              ? t('SvBlancServiceCodeDialog.active')
              : t('SvBlancServiceCodeDialog.save')}
          </Button>
        </Flex>
      </Form>
    );
  };

  return (
    <>
      <Modal
        className={getCssClass('bp5-dialog-content-scrollable', className)}
        title={
          isCreate
            ? t('SvBlancServiceCodeDialog.activeSvBlancServiceCode')
            : t('SvBlancServiceCodeDialog.editSvBlancServiceCode')
        }
        isOpen={!!isOpen}
        canOutsideClickClose={false}
        onClose={closeConfirmDialog}
        size={ModalSize.AUTO}
        isDisableModalBodyScroll
      >
        {/* <Box className={`${Classes.DIALOG_BODY} box`}> */}
        <Flex
          auto
          column
          className={`${Classes.DIALOG_BODY} sv-blanc-service-container`}
        >
          <Formik
            innerRef={formikRef}
            initialValues={initialValues}
            validate={onValidateForm}
            onSubmit={isCreate ? onSaveForm : onSaveEditForm}
            render={renderForm}
          />
        </Flex>
        {/* </Box> */}
      </Modal>
      <LeaveConfirmModal
        isOpen={isOpenConfirm}
        onConfirm={() => {
          setIsOpenConfirm(false);
          onClose();
        }}
        onClose={() => setIsOpenConfirm(false)}
      />
    </>
  );
};

export default I18n.withTranslation(SvBlancServiceCodeDialog, {
  namespace: 'SvBlancServiceCode',
});
