import { useQuery } from '@tanstack/react-query';
import { OverlayToaster, Toaster } from '@tutum/design-system/components/Core';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { isArray, isNullUUID } from '@tutum/design-system/infrastructure/utils';
import { useListenHandleUpdateEmployee } from '@tutum/hermes/bff/app_admin';
import { useListenFeatureFlagUpdated } from '@tutum/hermes/bff/app_mvz_feature_flag';
import {
  EventCardEVT,
  useListenCardEVT,
} from '@tutum/hermes/bff/app_mvz_cardservice';
import { useQueryGetFeatureFlag } from '@tutum/hermes/bff/legacy/app_mvz_feature_flag';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { GET_ALL_EMPLOYEE_PROFILE } from '@tutum/mvz/constant/queryKey';
import ProfileService from '@tutum/mvz/services/profile.service';
import { IEmployeeProfile, UserType } from '@tutum/mvz/types/profile.type';
import { isEmpty } from 'lodash';
import { QuickFilter } from '@tutum/hermes/bff/legacy/app_mvz_patient_overview';
import React, {
  ComponentType,
  FunctionComponent,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useListenGdtImportResult } from '@tutum/hermes/bff/app_mvz_document_management';
import { alertError, alertSuccessfully } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import type DocumentManagement from '@tutum/mvz/locales/en/DocumentManagement.json';
import { useQueryGetListBSNRName } from '@tutum/hermes/bff/legacy/app_bsnr';
import { BSNRName } from '@tutum/hermes/bff/legacy/bsnr_common';
import { DocumentNotificationType } from '@tutum/hermes/bff/legacy/document_management_common';

export interface GlobalData {
  userProfile: IEmployeeProfile | undefined;
  isForbidden: boolean;
  employeeProfiles: IEmployeeProfile[];
  employeeProfilesMap: Map<string, IEmployeeProfile>;
  ffConfig: { [key: string]: boolean } | undefined;
  isExistSVDoctor: boolean;
  isExistHZVDoctor: boolean;
  isExistFAVDoctor: boolean;
  patientOverviewActiveFilter?: QuickFilter;
  listBsnr?: BSNRName[];
}

export interface IGlobalContext {
  globalData: GlobalData;
  reloadUserProfile: () => Promise<IEmployeeProfile | undefined>;
  getEmployeeProfiles: () => Promise<IEmployeeProfile[]>;
  useGetDoctorList: () => IEmployeeProfile[];
  useGetDoctorMap: () => Map<string, IEmployeeProfile>;
  useGetLoggedInUserProfile: () => IEmployeeProfile;
  getDoctorById: (doctorId: string) => Nullable<IEmployeeProfile>;
  getBsnrIdByDoctorId: (doctorId?: string) => string; // get default bsnrId of doctor
  getDoctorName: (doctorId: string) => string;
  toasterRef: React.RefObject<Toaster> | null;
  setGlobalLoading: React.Dispatch<React.SetStateAction<boolean>>;
  getDoctorInitial: (doctorId: string) => string;
  logoutUser: () => void;
  setIsForbidden: (value: boolean) => void;
  setPatientOverviewActiveFilter: (value?: QuickFilter) => void;
}

export interface IGlobalProviderProps {
  userProfile?: IEmployeeProfile;
  isForbidden: boolean;
  reloadUserProfile: () => Promise<IEmployeeProfile | undefined>;
  children: any;
  toasterRef: React.RefObject<OverlayToaster>;
  setGlobalLoading: React.Dispatch<React.SetStateAction<boolean>>;
  logoutUser: () => void;
  setIsForbidden: (value: boolean) => void;
}

const GlobalContext = React.createContext<IGlobalContext>({
  globalData: {} as GlobalData,
  reloadUserProfile: () => Promise.resolve(undefined),
  useGetLoggedInUserProfile: (): IEmployeeProfile => {
    return {} as IEmployeeProfile;
  },
  getEmployeeProfiles: (): Promise<IEmployeeProfile[]> => {
    return {} as any;
  },
  useGetDoctorList: (): IEmployeeProfile[] => {
    return [];
  },
  useGetDoctorMap: (): Map<string, IEmployeeProfile> => {
    return new Map<string, IEmployeeProfile>();
  },
  getDoctorById: (): Nullable<IEmployeeProfile> => {
    return;
  },
  getDoctorName: (): string => {
    return '';
  },
  getDoctorInitial: (): string => {
    return '';
  },
  getBsnrIdByDoctorId: (): string => {
    return '';
  },
  toasterRef: null,
  setGlobalLoading: () => { },
  logoutUser: () => { },
  setIsForbidden: () => { },
  setPatientOverviewActiveFilter: () => { },
});

const GlobalContextProvider = (props: IGlobalProviderProps) => {
  const [globalData, setGlobalData] = useState<GlobalData>({
    userProfile: props.userProfile,
    isForbidden: props.isForbidden,
    employeeProfiles: [],
    employeeProfilesMap: new Map<string, IEmployeeProfile>(),
    ffConfig: undefined,
    isExistSVDoctor: false,
    isExistHZVDoctor: false,
    isExistFAVDoctor: false,
    listBsnr: [],
  });

  let _allEmployeeProfilesPromise: Promise<IEmployeeProfile[]>;

  const { data: ffConfig, refetch: ffRefetch } = useQueryGetFeatureFlag({
    select: (data) => data.data.fFConfig,
    enabled: !!globalData.userProfile?.id,
  });

  const { t } = I18n.useTranslation<keyof typeof DocumentManagement>({
    namespace: 'DocumentManagement',
  });

  useListenGdtImportResult((data) => {
    if (data.result) {
      let message: keyof typeof DocumentManagement = 'gdtImportSuccessMessage';
      switch (data.type) {
        case DocumentNotificationType.DocumentNotificationType_LDT:
          message = 'ldtImportSuccessMessage';
          if (!data.patientName) {
            message = 'ldtImportSuccessMessageNoPatient';
          }
          break;
        case DocumentNotificationType.DocumentNotificationType_GDT:
          message = 'gdtImportSuccessMessage';
          if (!data.patientName) {
            message = 'gdtImportSuccessMessageNoPatient';
          }
      }
      alertSuccessfully(
        t(message, {
          patientName: data.patientName,
        })
      );
    } else {
      alertError(data.error);
    }
  });

  useListenFeatureFlagUpdated(() => ffRefetch());
  // TODO: Handle show toast
  useListenCardEVT((data: EventCardEVT) => {
    // Data example:
    // <Message>
    //     <Parameter>
    //         <Key>ICCSN</Key>
    //         <Value>80276881025544419820</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>SlotID</Key>
    //         <Value>1</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>Type</Key>
    //         <Value>EGK</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>CardVersion</Key>
    //         <Value>{COSVERSION=4.3.0; OBJECTSYSTEMVERSION=4.3.2}</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>CardHandle</Key>
    //         <Value>ce41b635-b5c0-4304-90ee-10ef665e0d6c</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>CtID</Key>
    //         <Value>T1</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>KVNR</Key>
    //         <Value>X110481189</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>InsertTime</Key>
    //         <Value>2025-01-06T08:40:05Z</Value>
    //     </Parameter>
    //     <Parameter>
    //         <Key>CardHolderName</Key>
    //         <Value>Sieglinde Blücher</Value>
    //     </Parameter>
    // </Message>
    // get card type and card holder name
    let cardType = data.parameters.find((item) => item.key === 'Type')?.value;
    const cardType1 = data.parameters.find(
      (item) => item.key === 'CardType'
    )?.value;
    cardType = cardType || cardType1;

    const cardHolderName = data.parameters.find(
      (item) => item.key === 'CardHolderName'
    )?.value;
    props.toasterRef.current?.show({
      message: `${data.topic} - ${cardType}, ${cardHolderName}`,
      intent: 'primary',
    });
  });

  useEffect(() => {
    setGlobalData((prev) => ({
      ...prev,
      ffConfig,
    }));
  }, [ffConfig]);

  const { refetch } = useQuery({
    queryKey: [GET_ALL_EMPLOYEE_PROFILE],
    queryFn: async () => {
      const result = await ProfileService.getAllEmployeeProfiles();
      const resultMap = new Map<string, IEmployeeProfile>();
      let isExistHZVDoctor = false;
      let isExistFAVDoctor = false;
      result.forEach((item) => {
        if (!item?.id) {
          return;
        }
        isExistHZVDoctor = isExistHZVDoctor || item.hasHzvContracts || false;
        isExistFAVDoctor = isExistFAVDoctor || item.hasFavContracts || false;
        resultMap.set(item.id, item);
      });
      setGlobalData((prev) => ({
        ...prev,
        employeeProfiles: result,
        employeeProfilesMap: resultMap,
        isExistHZVDoctor,
        isExistFAVDoctor,
        isExistSVDoctor: isExistHZVDoctor || isExistFAVDoctor,
      }));
      return result;
    },
    enabled: !isEmpty(props.userProfile),
  });

  const { data, isSuccess } = useQueryGetListBSNRName({
    enabled: !!props.userProfile,
  });

  useEffect(() => {
    if (isSuccess) {
      // flat array nbnsr in bsnr
      const tmp = [...data.data];
      for (let i = 0; i < tmp.length; i++) {
        const t = tmp[i];
        if (isArray(t.bSNRNames)) {
          tmp.push(...t.bSNRNames);
        }
      }
      setGlobalData((prev) => ({
        ...prev,
        listBsnr: tmp,
      }));
    }
  }, [isSuccess]);

  useListenHandleUpdateEmployee(() => {
    refetch();
  });

  useEffect(() => {
    setGlobalData((globalData) => ({
      ...globalData,
      userProfile: props.userProfile,
      isForbidden: props.isForbidden,
    }));
  }, [props.userProfile, props.isForbidden]);

  const setPatientOverviewActiveFilter = (value?: QuickFilter) => {
    setGlobalData((prev) => ({
      ...prev,
      patientOverviewActiveFilter: value,
    }));
  };

  const useGetLoggedInUserProfile = (): IEmployeeProfile => {
    const [loggedInUser, setLoggedInUser] = useState<IEmployeeProfile>(
      {} as IEmployeeProfile
    );
    useEffect(() => {
      if (globalData.userProfile) {
        setLoggedInUser(globalData.userProfile);
      }
    }, [globalData.userProfile]);
    return loggedInUser;
  };

  const getDoctorById = (doctorId: string): Nullable<IEmployeeProfile> => {
    if (!doctorId) {
      return null;
    }
    return globalData.employeeProfiles?.find((item) => item.id === doctorId);
  };

  const getBsnrIdByDoctorId = (doctorId?: string): string => {
    if (!doctorId) {
      return '';
    }
    const doc = getDoctorById(doctorId);
    if (!doc) {
      return '';
    }
    if (!doc.bsnrId) {
      return doc.bsnrIds?.[0] || '';
    }
    return doc.bsnrId;
  };

  const getDoctorName = (doctorId: string): string => {
    if (isNullUUID(doctorId)) {
      return 'External System';
    }
    const profile = globalData.employeeProfiles
      ? globalData?.employeeProfiles?.find((item) => item.id === doctorId)
      : null;
    if (!profile) {
      return '';
    }
    return nameUtils.getDoctorName(profile);
  };

  const getDoctorInitial = (doctorId: string): string => {
    const profile = globalData.employeeProfiles
      ? globalData?.employeeProfiles?.find((item) => item.id === doctorId)
      : null;
    if (!profile) {
      return '';
    }
    return profile.initial;
  };

  const useGetDoctorList = (): IEmployeeProfile[] => {
    const [listEmployees, setlistEmployees] = useState<IEmployeeProfile[]>([]);
    useEffect(() => {
      if (!globalData.employeeProfiles) {
        return;
      }
      const doctorList = globalData.employeeProfiles
        ? globalData.employeeProfiles.filter((item) =>
          item.types.includes(UserType.DOCTOR)
        )
        : [];
      const myUserId = globalData.userProfile?.id;
      setlistEmployees(
        doctorList.sort((a, b) => {
          return a.id == myUserId ? -1 : b.id == myUserId ? 1 : 0;
        })
      );
    }, [globalData.employeeProfiles]);
    return listEmployees;
  };

  const useGetDoctorMap = (): Map<string, IEmployeeProfile> => {
    return globalData.employeeProfilesMap;
  };

  const getEmployeeProfiles = (): Promise<IEmployeeProfile[]> => {
    if (!_allEmployeeProfilesPromise) {
      _allEmployeeProfilesPromise = ProfileService.getAllEmployeeProfiles();
    }
    return _allEmployeeProfilesPromise;
  };

  const globalContext = useMemo<IGlobalContext>(
    () => ({
      globalData: globalData,
      getDoctorById: getDoctorById,
      getDoctorName: getDoctorName,
      getEmployeeProfiles: getEmployeeProfiles,
      useGetLoggedInUserProfile: useGetLoggedInUserProfile,
      reloadUserProfile: props.reloadUserProfile,
      useGetDoctorList: useGetDoctorList,
      useGetDoctorMap: useGetDoctorMap,
      toasterRef: props.toasterRef,
      setGlobalLoading: props.setGlobalLoading,
      getDoctorInitial: getDoctorInitial,
      logoutUser: props.logoutUser,
      setIsForbidden: props.setIsForbidden,
      setPatientOverviewActiveFilter: setPatientOverviewActiveFilter,
      getBsnrIdByDoctorId: getBsnrIdByDoctorId,
    }),
    [globalData, props.reloadUserProfile]
  );

  return (
    <GlobalContext.Provider value={globalContext}>
      {props.children}
    </GlobalContext.Provider>
  );
};

function withGlobalContext<TProps>(
  Component: ComponentType<TProps & IGlobalContext>
): FunctionComponent<TProps> {
  return (props: TProps) => {
    return (
      <GlobalContext.Consumer>
        {(contexts) => <Component {...props} {...contexts} />}
      </GlobalContext.Consumer>
    );
  };
}

function useGlobalContext() {
  const context = useContext(GlobalContext);
  return context;
}

export default {
  Consumer: GlobalContext.Consumer,
  Provider: GlobalContextProvider,
  instance: GlobalContext,
  withContext: withGlobalContext,
  useContext: useGlobalContext,
};
