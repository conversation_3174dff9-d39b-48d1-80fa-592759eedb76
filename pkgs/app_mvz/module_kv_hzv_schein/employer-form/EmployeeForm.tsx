import React, {
  createRef,
  memo,
  MutableRefObject,
  useEffect,
  useState,
} from 'react';
import ReactDOM from 'react-dom';

import { IMvzTheme } from '@tutum/mvz/theme';
import { ScheinInfo } from '../CreateSchein.service';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { FormikProps } from 'formik';
import { Flex, H3 } from '@tutum/design-system/components';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { InputValue } from '@tutum/mvz/module_kv_hzv_schein/ScheinComponents';
import { scrollToElem } from '../utils';
import { FieldValidationType } from '@tutum/hermes/bff/schein_common';
import { Icon } from '@tutum/design-system/components/Core';

export interface IEmployeeFormProps {
  className?: string;
  theme?: IMvzTheme;
  mapFields: Map<string, string>;
  navRef: HTMLElement | null;
  validationList: {
    [key: string]: FieldValidationType;
  };
  onChangeField: (values: ScheinInfo) => void;
}
const namespace = 'Schein';
const nestedTrans = 'employer';
const EMPLOYEE_FIELDS = [
  'bgEmployerName',
  'bgEmployerStreet',
  'bgEmployerHousenumber',
  'bgEmployerHousenumber',
  'bgEmployerPostcode',
  'bgEmployerCity',
  'bgEmployerCountry',
];
function EmployeeForm(
  props: IEmployeeFormProps & II18nFixedNamespace & FormikProps<ScheinInfo>
): React.FunctionComponentElement<IEmployeeFormProps> {
  const {
    className,
    t,
    submitCount,
    errors,
    touched,
    navRef,
    isValidating,
    onChangeField,
  } = props;
  const [employeeRef, setEmployeeRef] = useState<Element | null>(null);
  const [isExpaned, setExpand] = useState<boolean>(true);
  useEffect(() => {
    setEmployeeRef(navRef?.querySelector('.employee') || null);
  }, [navRef]);
  const contentRef: MutableRefObject<HTMLDivElement | null> = createRef();

  const menuClick = () => {
    if (!contentRef.current) {
      return;
    }
    scrollToElem(contentRef.current);
    return false;
  };

  useEffect(() => {
    const hasErrorInSection = EMPLOYEE_FIELDS.some((field) => !!errors[field]);

    if (hasErrorInSection) {
      setExpand(true);
    }
  }, [errors, isValidating]);

  return (
    <Flex column className={getCssClass(className, 'form-info')}>
      {employeeRef
        ? ReactDOM.createPortal(
          <H3 className="label-section employee-form" onClick={menuClick}>
            {t('title')}
          </H3>,
          employeeRef
        )
        : null}
      <H3
        className="label-section"
        onClick={() => setExpand(!isExpaned)}
        ref={contentRef}
      >
        {t('title')}
        <Icon
          className="expand-icon"
          icon={isExpaned ? 'chevron-up' : 'chevron-down'}
        />
      </H3>
      <Flex column className="form-body">
        <Flex className="row">
          <InputValue
            namespace={namespace}
            nestedTrans={nestedTrans}
            idField="bgEmployerName"
            titleField="bgEmployerName"
            validationType="required"
            submitCount={submitCount}
            touched={touched}
            errors={errors}
            onChange={onChangeField}
          />
        </Flex>
        <Flex className="row">
          <InputValue
            namespace={namespace}
            nestedTrans={nestedTrans}
            idField="bgEmployerStreet"
            titleField="bgEmployerStreet"
            validationType="required"
            submitCount={submitCount}
            touched={touched}
            errors={errors}
            maxLength={150}
            onChange={onChangeField}
          />
        </Flex>
        <Flex auto justify="space-between">
          <Flex className="half-row">
            <InputValue
              namespace={namespace}
              nestedTrans={nestedTrans}
              idField="bgEmployerHousenumber"
              titleField="bgEmployerHousenumber"
              validationType="required"
              submitCount={submitCount}
              touched={touched}
              errors={errors}
              maxLength={50}
              onChange={onChangeField}
            />
          </Flex>
          <Flex className="half-row">
            <Flex className="half-row">
              <InputValue
                namespace={namespace}
                nestedTrans={nestedTrans}
                idField="bgEmployerPostcode"
                titleField="bgEmployerPostcode"
                validationType="required"
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                maxLength={50}
                onChange={onChangeField}
              />
            </Flex>
          </Flex>
        </Flex>
        <Flex auto justify="space-between">
          <Flex className="half-row">
            <InputValue
              namespace={namespace}
              nestedTrans={nestedTrans}
              idField="bgEmployerCity"
              titleField="bgEmployerCity"
              validationType="required"
              submitCount={submitCount}
              touched={touched}
              errors={errors}
              maxLength={100}
              onChange={onChangeField}
            />
          </Flex>
          <Flex className="half-row">
            <InputValue
              namespace={namespace}
              nestedTrans={nestedTrans}
              idField="bgEmployerCountry"
              titleField="bgEmployerCountry"
              validationType="required"
              submitCount={submitCount}
              touched={touched}
              errors={errors}
              maxLength={100}
              onChange={onChangeField}
            />
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(EmployeeForm, {
    namespace: namespace,
    nestedTrans: nestedTrans,
  })
);
