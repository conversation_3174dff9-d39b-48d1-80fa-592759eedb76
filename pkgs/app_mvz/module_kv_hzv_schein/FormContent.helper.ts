import { isNil } from 'lodash';
import moment from 'moment';

import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  isEmpty,
  isValidFormat,
  isNumber,
  isExactLength,
} from '@tutum/design-system/infrastructure/utils';
import { regexForCheckingPseudoLanr } from '@tutum/infrastructure/utils/match';
import { DATE_TIME_TRANSFER_UTC } from '@tutum/infrastructure/shared/date-format';
import { ScheinInfo } from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';
import { Psychotherapy } from '@tutum/hermes/bff/schein_common';
import { PatientParticipationStatus } from '@tutum/hermes/bff/legacy/common';

export const RESTRICT_VALUE = ['888888800'];
export const SCROLL_SHIFTING = 20;
export enum FORM_SECTION {
  PSYCHOTHERAPY = 'PSYCHOTHERAPY',
}

export const validateRequired = (fieldName: string, { values, errors, t }) => {
  if (!isEmpty(values[fieldName])) return;
  errors[fieldName] = t('required');
};

export const validateG4101 = ({ values, errors, t }) => {
  if (!isEmpty(values.g4101)) {
    if (!values?.g4101?.includes('.')) {
      errors['g4101'] = t('invalidQuarter');
    } else {
      const inputSplitted = values?.g4101?.split('.');

      if (inputSplitted) {
        const quarter = Number(inputSplitted?.[0] || 0);
        const year = Number(inputSplitted?.[1] || 0);
        const currentDate = DatetimeUtil.date();
        const currentQuarter = DatetimeUtil.getQuarter(currentDate);
        const currentYear = currentDate.getFullYear();

        if (!quarter || quarter > 4 || !year) {
          errors['g4101'] = t('invalidQuarter');
        } else if (
          year > currentYear ||
          (quarter > currentQuarter && year === currentYear)
        ) {
          errors['g4101'] = t('cannotSetQuarterToAFutureQuarter');
        }
      }
    }
  } else {
    errors['g4101'] = t('required');
  }
};

export const validateAd4125 = ({ values, errors, t, mapFields }) => {
  if (
    values['g4101'] &&
    mapFields.get('ad4125') &&
    values['ad4125From'] &&
    values['ad4125To']
  ) {
    const groupValues = values['g4101'].split('.');
    const selectedQuarter = +groupValues[0];
    const selectedYear = +groupValues[1];
    const startDateQuarter = moment(`${selectedYear}`).quarter(selectedQuarter);
    const endDateQuarter = DatetimeUtil.getEndOfSelectedQuarter(
      selectedQuarter,
      selectedYear
    );
    const isValidDateInRange =
      values['ad4125From'] >= +startDateQuarter &&
      values['ad4125To'] <= +endDateQuarter;

    if (!isValidDateInRange) {
      errors['ad4125'] = t('errorOutDateRange');
    }
  }
};

/**
 * Bundeswehr case
 * @param values
 * @param errors
 * @param t
 * @param patient
 */
export const validateArmedForceInsurance = ({
  values,
  errors,
  t,
  patient,
  mapFields,
}) => {
  const insurance = patient.patientInfo.insuranceInfos.find(
    (info) => info.id === values['insuranceId']
  );

  // NOTE: Bundeswehr case
  const hasArmedForceInsurance =
    !isNil(insurance) &&
    (insurance.insuranceCompanyId === '79868' ||
      insurance.insuranceCompanyId === '79869');

  if (hasArmedForceInsurance) {
    if (
      mapFields.get('ad4125') &&
      (!values['ad4125From'] || !values['ad4125To'])
    ) {
      errors['ad4125'] = t('required');
    }
  }
};

export const validateLANR = ({ values, errors, t }) => {
  ['re4241', 're4242', 'ps4299'].forEach((fieldName) => {
    const fieldValue = `${values[fieldName]}`;

    if (fieldValue && RESTRICT_VALUE.includes(fieldValue)) {
      errors[fieldName] = t('restrictLANRValue', {
        fieldValue,
      });
    }
  });
};

export const validateLANRDigitalRule = ({ values, errors, t }) => {
  if (values?.re4242 && !isValidFormat('nnnnnnmff', values?.re4242)) {
    errors.re4242 = t('errLanrInvalidDigitRule');
  }
};

export const validateRe4229 = ({ values, errors, t }) => {
  if (values['re4229'] && values?.re4229?.length !== 5) {
    errors.re4229 = t('errorRe4229');
  }
};

export const validateG4101AndG4102 = ({ values, errors, t }) => {
  if (values['g4102'] && values['g4101']) {
    const quarter = +DatetimeUtil.getQuarter(values['g4102']);
    const year = +DatetimeUtil.getYear(values['g4102']);

    if (quarter !== values['g4101Quarter'] || year !== values['g4101Year']) {
      errors['g4102'] = t('invalidDateOfIssue');
    }
  }
};

export const validateTsvgContractType = ({ values, errors, t, mapFields }) => {
  if (
    mapFields.get('tsvgContactType') &&
    values['tsvgContactType'] &&
    values['tsvgTranferCode']
  ) {
    if (values['tsvgTranferCode'].length < 12) {
      errors['tsvgTranferCode'] = t('codeFormat');
    }
  }
};

const checkRequired4299FieldBy4251Field = (values: ScheinInfo) => {
  return values['ps4250'] && [3, 4].includes(Number(values['ps4251']));
};

export const validatePsycotherapy = ({ values, errors, t, tPsychotherapy }) => {
  // Validate pyschotherapy
  if (values?.ps4234) {
    values.psychotherapy.forEach((v: Psychotherapy, i) => {
      const requestDate = moment(new Date(v.ps4247!));
      if (!v?.ps4235) {
        errors[`psychotherapy.${i}.ps4235`] = t('required');
      }

      if (v.ps4250) {
        if (!v?.ps4251) {
          errors[`psychotherapy.${i}.ps4251`] = t('required');
        }
        if ([3, 4].includes(v?.ps4251!)) {
          const lanr = String(v?.ps4299);
          if (!lanr || !isNumber(lanr) || !isExactLength(lanr, 9)) {
            errors[`psychotherapy.${i}.ps4299`] = t('9digitLanr');
          }
          if (!isValidFormat('nnnnnnmff', lanr)) {
            errors[`psychotherapy.${i}.ps4299`] = t('errLanrInvalidDigitRule');
          }
          if (regexForCheckingPseudoLanr.test(lanr)) {
            errors[`psychotherapy.${i}.ps4299`] = t(
              'errLanrIsPseudoLanrFormat'
            );
          }
          if (RESTRICT_VALUE.includes(lanr)) {
            errors[`psychotherapy.${i}.ps4299`] = t('restrictLANRValue', {
              fieldValue: lanr,
            });
          }
        }
      }
      if (
        requestDate.isValid() &&
        moment
          .utc(moment(v.ps4247).format(DATE_TIME_TRANSFER_UTC))
          .toDate()
          .getTime() <
        moment
          .utc(moment('2017-04-01').format(DATE_TIME_TRANSFER_UTC))
          .toDate()
          .getTime()
      ) {
        v.groupServicesCodeBefore2017.forEach((g, ig) => {
          if (!g.serviceCode) {
            if (typeof g.amountBilled == 'undefined') {
              errors[
                `psychotherapy.${i}.groupServicesCodeBefore2017.${ig}.amountBilled`
              ] = t('required');
            }
          } else {
            if (!g.amountApproval) {
              errors[
                `psychotherapy.${i}.groupServicesCodeBefore2017.${ig}.amountApproval`
              ] = t('required');
            }
            if (g.amountBilled > g.amountApproval) {
              errors[
                `psychotherapy.${i}.groupServicesCodeBefore2017.${ig}.amountBilled`
              ] = t('cannotHigherField', {
                amount: g.amountApproval,
                field: tPsychotherapy('amountApproval'),
              });
            }
          }
        });
      } else {
        const totalGroupServicesCode = v.groupServicesCode?.reduce(
          (total, item) => (total += +item.amountBilled),
          0
        );

        if (checkRequired4299FieldBy4251Field(values) && !v?.ps4299) {
          errors[`psychotherapy.${i}.ps4299`] = t('required');
        }
        v.groupServicesCode.forEach((s, idx) => {
          if (!s.serviceCode) {
            // For amount approval
            if (
              (typeof s.amountBilled === 'undefined' || s.amountBilled === 0) &&
              (typeof v?.ps4245 === 'undefined' || v.ps4245 === null)
            ) {
              return;
            }
            if (
              typeof v?.ps4245 !== 'undefined' &&
              +v.ps4245 < totalGroupServicesCode
            ) {
              errors[`psychotherapy.${i}.ps4245`] = t('totalAmountServiceCode');
            }
            if (Number(v?.ps4245) < Number(v?.ps4246)) {
              errors[`psychotherapy.${i}.ps4246`] = t('cannotHigherField', {
                amount: v?.ps4245,
                field: tPsychotherapy('ps4245'),
              });
            }

            // For group service code
            if (typeof s.amountBilled == 'undefined') {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('required');
            }
            if (Number(v?.ps4245) < Number(s.amountBilled)) {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('cannotHigherField', {
                amount: v?.ps4245,
                field: tPsychotherapy('ps4245'),
              });
            }
          } else {
            if (typeof v?.ps4245 == 'undefined') {
              errors[`psychotherapy.${i}.ps4245`] = t('amountApprovedRequired');
            }
            if (typeof s.amountBilled == 'undefined') {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('required');
            }
            if (Number(v?.ps4245) < Number(s.amountBilled)) {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('cannotHigherField', {
                amount: v?.ps4245,
                field: tPsychotherapy('ps4245'),
              });
            }
          }
          // totalGroupServiceCode += Number(s.amountBilled ?? '0') ?? 0;
        });

        v.groupCareGiver.forEach((s, idx) => {
          if (!s.serviceCode) {
            // For amount approval
            if (
              (typeof s.amountBilled === 'undefined' || s.amountBilled === 0) &&
              (typeof v?.ps4245 === 'undefined' || v.ps4245 === null)
            ) {
              return;
            }
            if (
              typeof v?.ps4245 !== 'undefined' &&
              +v.ps4245 < totalGroupServicesCode
            ) {
              errors[`psychotherapy.${i}.ps4245`] = t('totalAmountServiceCode');
            }
            if (Number(v?.ps4245) < Number(v?.ps4246)) {
              errors[`psychotherapy.${i}.ps4246`] = t('cannotHigherField', {
                amount: v?.ps4245,
                field: tPsychotherapy('ps4245'),
              });
            }

            // For group service code
            if (typeof s.amountBilled == 'undefined') {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('required');
            }
            if (Number(v?.ps4245) < Number(s.amountBilled)) {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('cannotHigherField', {
                amount: v?.ps4245,
                field: tPsychotherapy('ps4245'),
              });
            }
          } else {
            if (typeof v?.ps4245 == 'undefined') {
              errors[`psychotherapy.${i}.ps4245`] = t('amountApprovedRequired');
            }
            if (typeof s.amountBilled == 'undefined') {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('required');
            }
            if (Number(v?.ps4245) < Number(s.amountBilled)) {
              errors[
                `psychotherapy.${i}.groupServicesCode.${idx}.amountBilled`
              ] = t('cannotHigherField', {
                amount: v?.ps4245,
                field: tPsychotherapy('ps4245'),
              });
            }
          }
          // totalGroupServiceCode += Number(s.amountBilled ?? '0') ?? 0;
        });

        if (!!v?.ps4252 && Number(v?.ps4252) > 3) {
          errors[`psychotherapy.${i}.ps4252`] = t('maximumValue', {
            amount: 3,
          });
        }
        if (v?.ps4254) {
          if (Number(v?.ps4254) > 3) {
            errors[`psychotherapy.${i}.ps4254`] = t('maximumValue', {
              amount: 3,
            });
          }
          if (Number(v?.ps4254) > Number(v?.ps4252)) {
            errors[`psychotherapy.${i}.ps4254`] = t('cannotHigherField', {
              amount: v?.ps4252,
              field: tPsychotherapy('versicherten.ps4252'),
            });
          }
        }
      }
    });
  }
};

export const ACTIVATED_STATUS: PatientParticipationStatus[] = [
  PatientParticipationStatus.PatientParticipation_Active,
  PatientParticipationStatus.PatientParticipation_Requested,
];

export enum ScheinHintForCompliance {
  ABRG1006 = 'ABRG1006',
  ABRG829 = 'ABRG829',
}
