import React, { memo, useMemo, useEffect } from 'react';
import { Flex, Button } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import { Dialog } from '@blueprintjs/core';
import { Classes } from '@tutum/design-system/components/Core';
import { Form, Formik, FormikProps } from 'formik';
import { IAutoFill4205ModalProps } from './AutoFill4205.type';
import AllergiesInline, {
  onValidateForm,
} from '@tutum/mvz/module_patient-management/patient-file/allergies/allergies-inline/';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import { Allergy } from '@tutum/hermes/bff/patient_profile_common';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import { createPatientMedicalData } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  AllergyElementId,
  actionChainActions,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import { ScheinInfo } from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';

interface IInitValues {
  allergies: Allergy[];
}

const initAllergy = {
  allergy: '',
  isPrescriptionRelated: false,
};

function AutoFill4205Modal({
  className,
  patient,
  targetFormik,
  onClose,
}: IAutoFill4205ModalProps): React.FunctionComponentElement<IAutoFill4205ModalProps> {
  const { patientMedicalData } = patient || {};
  const allergies = patientMedicalData?.allergiesFor4205 || [];
  const id = patient?.id!;

  const { t } = I18n.useTranslation<keyof typeof ScheinI18n.AutoFill4205Modal>({
    namespace: 'Schein',
    nestedTrans: 'AutoFill4205Modal',
  });
  const { t: tFormValidation } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const initialValues = useMemo(() => {
    return {
      allergies: allergies?.length ? allergies : [initAllergy],
    };
  }, []);

  const onSaveAllergiesInline = async (values: IInitValues) => {
    const filteredAllergies = values?.allergies?.filter((item) => item.allergy);
    const medicalData = {
      ...patientMedicalData,
      allergiesFor4205: filteredAllergies || [],
    };
    await createPatientMedicalData({
      patientId: id,
      patientMedicalData: medicalData,
    });
    (targetFormik as FormikProps<ScheinInfo>).setFieldValue(
      're4205',
      values.allergies.map((item) => item.allergy).join(', ')
    );
    onClose();
  };

  useEffect(() => {
    // NOTE: notify to all action chain subscribers that allergy-sidebar is mounted - ready
    actionChainActions.setMountedSectionsStatus({
      'allergy-sidebar': true,
    });
    return () => {
      // NOTE: notify to all action chain subscribers that allergy-sidebar is unmounted - not ready
      actionChainActions.setMountedSectionsStatus({
        'allergy-sidebar': false,
      });
    };
  }, []);

  return (
    <Dialog
      className={className}
      isOpen={true}
      title={t('title')}
      onClose={onClose}
      canOutsideClickClose={false}
    >
      <Formik
        initialValues={initialValues}
        onSubmit={onSaveAllergiesInline}
        validate={onValidateForm(tFormValidation)}
        enableReinitialize
        validateOnChange={false}
        validateOnBlur
      >
        {({ values, errors, touched, submitCount, dirty, isSubmitting }) => (
          <Form>
            <Flex column>
              <Flex className="sl-popover-body" p={16}>
                <AllergiesInline
                  patient={patient}
                  allergies={values?.allergies}
                  is4205
                  errors={errors}
                  touched={touched}
                  submitCount={submitCount}
                />
              </Flex>
              <div className={Classes.DIALOG_FOOTER}>
                <div className={`${Classes.DIALOG_FOOTER_ACTIONS}`}>
                  <Button
                    intent="primary"
                    outlined
                    minimal
                    fill
                    onClick={onClose}
                    loading={isSubmitting}
                    {...registerActionChainElementId(
                      AllergyElementId.DIALOG_CANCEL_BUTTON
                    )}
                  >
                    {tButtonActions('cancelText')}
                  </Button>
                  <Button
                    type="submit"
                    intent="primary"
                    disabled={!dirty}
                    fill
                    loading={isSubmitting}
                    {...registerActionChainElementId(
                      AllergyElementId.DIALOG_SAVE_BUTTON
                    )}
                  >
                    {tButtonActions('saveText')}
                  </Button>
                </div>
              </div>
            </Flex>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
}

export default memo(AutoFill4205Modal);
