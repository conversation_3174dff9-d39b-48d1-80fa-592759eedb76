import { Form, Formik, FormikErrors, FormikProps } from 'formik';
import {
  cloneDeep,
  debounce,
  get,
  isEqual,
  isNil,
  isEmpty as isempty,
  pickBy,
  sortBy,
} from 'lodash';
import {
  Ref,
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';

import {
  BodyTextM,
  Box,
  Flex,
  LoadingState,
  MessageBar,
  TOASTER_TIMEOUT_CUSTOM,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { Button } from '@tutum/design-system/components/Button';
import { Classes, Intent } from '@tutum/design-system/components/Core';
import { Modal } from '@tutum/design-system/components/Modal';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  getCssClass,
  isEmpty,
} from '@tutum/design-system/infrastructure/utils';
import {
  ContractType,
  DisplayType,
  FieldError,
  FieldErrorCode,
  PatientParticipationStatus,
  ValidationType,
} from '@tutum/hermes/bff/common';
import { useQueryGetListBSNRName } from '@tutum/hermes/bff/legacy/app_bsnr';
import {
  updateInsurances,
  useQueryGetPatientProfileById,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  CreateScheinResponse,
  useMutationGetFields,
  useQueryGetSetting,
  useQueryGetSubGroupFromMasterData,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  reRunValidateService,
  takeoverServiceTerminalApproval,
  useQueryGetTherapies,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import {
  GetSelectedTreatmentCaseSubgroupResponse,
  TreatmentCaseNames,
} from '@tutum/hermes/bff/legacy/schein_common';
import { TakeoverDiagnosisType } from '@tutum/hermes/bff/legacy/timeline_common';
import {
  DoctorInfo,
  InsuranceInfo,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import {
  Assigndiagnosis,
  FieldValidationType,
  MainGroup,
  ScheinItem,
} from '@tutum/hermes/bff/schein_common';
import {
  default as I18n,
  II18nFixedNamespace,
  default as i18n,
} from '@tutum/infrastructure/i18n';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  ICustomInsuranceInfo,
  getActiveInsurance,
} from '@tutum/mvz/_utils/checkInsurance';
import { useLocationHash } from '@tutum/mvz/hooks/useLocationHash';
import useToaster from '@tutum/mvz/hooks/useToaster';
import {
  CreateOrUpdateSchein,
  INITIAL_PSYCHOTHERAPY,
  IdAndText,
  QueryIf,
  QueryOperator,
  QueryValue,
  ScheinInfo,
  additionFormPrefix,
  compareDay,
  createSchein,
  dataTransform,
  employeeFormPrefix,
  generalFormPrefix,
  getLastSelectedTreatmentCaseSubgroupByPatientId,
  parseCatalogsData,
  parsingErrorObject,
  parsingStatement,
  psychotherapyFormPrefix,
  referralFormPrefix,
  serverValidate,
  updateSchein,
} from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';
import {
  ACTIVATED_STATUS,
  FORM_SECTION,
  SCROLL_SHIFTING,
  ScheinHintForCompliance,
  validateAd4125,
  validateArmedForceInsurance,
  validateG4101,
  validateLANR,
  validateLANRDigitalRule,
  validatePsycotherapy,
  validateRe4229,
  validateRequired,
  validateTsvgContractType,
} from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import AdditionForm from '@tutum/mvz/module_kv_hzv_schein/addition-information-form/AdditionForm.styled';
import EmployeeForm from '@tutum/mvz/module_kv_hzv_schein/employer-form/EmployeeForm.styled';
import GeneralForm from '@tutum/mvz/module_kv_hzv_schein/general-form/GeneralForm.styled';
import PsychotherapieForm from '@tutum/mvz/module_kv_hzv_schein/psychotherapy-form/PsychotherapieForm.styled';
import ReferralForm from '@tutum/mvz/module_kv_hzv_schein/referral-form/ReferralForm.styled';
import { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import SelectDiagnosisDialog from '../components/select-diagnosis-dialog/SelectDiagnosisDialog.styled';
import { ComposerDiagnosisType } from '../components/select-diagnosis-dialog/helpers';
import GlobalContext, { IGlobalContext } from '../contexts/Global.context';
import type { IPatientManagement } from '../module_patient-management/contexts/patient-management/PatientManagementContext.type';
import CheckInformColonoscopyInfo from '../module_patient-management/create-patient-v2/colonoscopy-info/CheckInformColonoscopyInfo';
import useCheckContractSupport from '../module_patient-management/hooks/useCheckContractSupport';
import {
  patientFileActions,
  usePatientFileStore,
} from '../module_patient-management/patient-file/PatientFile.store';
import { useCurrentSchein } from '../module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import reloadSchein from '../module_patient-management/utils/reloadSchein';
import { getTreatmentCaseFromSubgroup } from '../module_setting/schein/tmpapi';
import { FlexFooter } from './CreateSchein.styled';
import AutoFill21 from './general-form/AutoFill21';
import { ICustomWarning } from './types';

const COMMON_MAIN_GROUP = [
  '00',
  '20',
  '21',
  '23',
  '24',
  '26',
  '27',
  '28',
  '30',
  '31',
  '32',
  '41',
  '42',
  '43',
  '44',
  '45',
  '46',
];
export interface IFormContentProps {
  className?: string;
  t?: any;
  isOpen: boolean;
  id?: string;
  isCard?: boolean;
  onCancel: () => void;
  onClose: () => void;
  patientId: string;
  isBillingValidation?: boolean;
  patientManagement: IPatientManagement;
  globalContext: IGlobalContext;
  isCreateReadCard: boolean;
  setShowDMPEnrollDialog: (isShowDMPEnrollDialog: boolean) => void;
  reloadQuarters?: ReloadQuarterFunc;
  onCreateScheinMobileCard?: () => void;
  onCreateScheinMailBox?: () => void;
  scrollToSection?: FORM_SECTION;
}

type FormRef = {
  nav: HTMLElement | null;
  body: HTMLElement | null;
};

let activeIndexRev = -2;

function FormContent(
  props: IFormContentProps & II18nFixedNamespace<any>,
  ref: Ref<any>
) {
  const formikRef = useRef<FormikProps<ScheinInfo>>(null);
  useImperativeHandle(ref, () => ({
    get formik() {
      return formikRef.current;
    },
  }));
  const scrollToFirstError = (firstInvalidElement: Element) => {
    if (!firstInvalidElement) {
      return;
    }
    firstInvalidElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest',
    });
  };

  const newFieldError = (
    fieldName: string,
    validationType: FieldValidationType
  ): FieldError => {
    return {
      field: fieldName,
      fieldName: fieldName,
      validationType: ValidationType.ValidationType_Error,
      errorCode: validationType,
      metaData: {},
      message: '',
    };
  };

  const [isScroll, setIsScroll] = useState<boolean>(false);
  const [validations, setValidations] = useState<{
    [key: string]: FieldError;
  }>({});
  const [disabledFields, setDisabledFields] = useState({});
  const [mapFields, setMapField] = useState<Map<string, string>>(
    new Map<string, string>()
  );
  const [mapFieldsWithSubGroup, setMapFieldWithSubGroup] = useState<
    Nullable<Map<string, string>>
  >(new Map());
  const [mapForm, setMapForm] = useState<Map<string, boolean>>(
    new Map<string, boolean>()
  );
  const [mapFormWithSubGroup, setMapFormWithSubGroup] = useState<
    Nullable<Map<string, boolean>>
  >(new Map());
  const [confirmChangeTreatmentOpen, setConfirmChangeTreatmentOpen] =
    useState<boolean>(false);
  const [errMap, setErrMap] = useState<Map<string, string>>(
    new Map<string, string>()
  );
  const [assignDiagnosis, setAssignDiagnosis] =
    useState<Nullable<Assigndiagnosis>>(null);

  const [previousTreatmentCase, setPreviousTreatmentCase] =
    useState<string>('');
  const [actionOvertakeDiagnosis, setActionOvertakeDiagnosis] = useState<
    (
      takeOverDiagnoseIds: string[],
      newDiagnosis?: ComposerDiagnosisType[],
      mappingTreatmentRelevent?: {
        [key: string]: boolean;
      }
    ) => void
  >(() => {});
  const [isOpenTakeOverModal, setIsOpenTakeOverModal] =
    useState<boolean>(false);
  const [skipNotice, setSkipNotice] = useState<boolean>(false);

  const {
    isOpen,
    id,
    patientId,
    onCancel,
    isCard,
    className,
    patientManagement,
    globalContext,
    setShowDMPEnrollDialog,
    reloadQuarters,
    onCreateScheinMobileCard,
    onCreateScheinMailBox,
    scrollToSection,
  } = props;
  const tError = i18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'additionInfoWarning',
  }).t;
  const doctors = globalContext.useGetDoctorList();
  const employeeMap = globalContext.useGetDoctorMap();
  const [autoFillModalM39, setAutoFillModalM39] = useState<boolean>(false);
  const [, setAutoFillField4205] = useState<boolean>(false);
  const [customWarnings, setCustomWarnings] = useState<ICustomWarning>(
    {} as ICustomWarning
  );
  const [catalogsDataKeyText, setCatalogsDataKeyText] = useState<{
    [key in string]: IdAndText[];
  }>({});
  const toasterRef = useToaster();
  const [initialValues, setInitialValues] = useState<ScheinInfo>({
    patientId: props.patientId,
    markedAsBilled: false,
  } as ScheinInfo);
  const currentFormikProps = formikRef.current;
  const patientFileStore = usePatientFileStore();
  const { data: bSNRData, isSuccess: isSuccessGetBsnr } =
    useQueryGetListBSNRName();

  const formRef = useRef<FormRef>({ nav: null, body: null });

  const { t: tPsychotherapy } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'psychotherapy',
  });

  const tCreateError = i18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein',
  }).t;

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { t } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein',
  });

  const { hash, setHash } = useLocationHash();
  const isReadCard = useMemo(() => {
    if (hash === '#schein') {
      return true;
    }
    return false;
  }, [hash]);

  const [isOpenWarningConfirm, setIsOpenWarningConfirm] = useState(false);
  const [noticeErrors, setNoticeErrors] = useState<object>({});
  const currentSchein = useCurrentSchein();
  const currentUser = globalContext.useGetLoggedInUserProfile();
  const [loading, setLoading] = useState<'loading' | 'idle'>('loading');
  const [mainGroups, setMainGroup] = useState<string[]>(COMMON_MAIN_GROUP);

  const { refetch } = useQueryGetPatientProfileById(
    {
      id: props.patientId,
    },
    {
      enabled: false,
    }
  );
  const doctor = useMemo(() => {
    if (!currentFormikProps || !isSuccessGetBsnr) {
      return null;
    }
    const doc = doctors.find(
      (d) =>
        d.id === currentFormikProps.values.doctorId ||
        d.id === initialValues.doctorId
    );
    if (!doc) {
      return null;
    }
    const selectedBsnr = bSNRData?.data?.find(
      (b) => b.id === currentFormikProps?.values?.assignedToBsnrId
    );
    return {
      ...doc,
      bsnr: selectedBsnr?.code,
      bsnrId: selectedBsnr?.id,
    };
  }, [
    doctors,
    currentFormikProps?.values?.doctorId,
    initialValues?.doctorId,
    isSuccessGetBsnr,
  ]);

  const warningTreatmentCategory = useMemo(() => {
    const cloneData = cloneDeep(noticeErrors);

    delete cloneData['re4214'];

    return cloneData;
  }, [noticeErrors]);

  const [insuranceState, setInsuranceState] = useState<{
    insurance: ICustomInsuranceInfo[];
    insuranceDeleted: ICustomInsuranceInfo[];
  }>({
    insurance: [],
    insuranceDeleted: [],
  });

  const { refetch: refetchGetTherapies } = useQueryGetTherapies(
    {
      patientId: patientId,
      scheinId: currentSchein?.scheinId || '',
    },
    { enabled: false }
  );

  const {
    data: settings,
    isSuccess: isSuccessGetSetting,
    isFetching: isLoadingGetSetting,
  } = useQueryGetSetting({
    enabled: isOpen,
  });

  const {
    data: subGroups,
    isSuccess,
    isFetching: isLoadingGetSubGroup,
  } = useQueryGetSubGroupFromMasterData(
    {
      bsnr: doctor?.bsnr || '',
    },
    { enabled: !!doctor?.bsnr }
  );

  const getParticipationsByType = (type: ContractType) => {
    const participations =
      patientManagement.getPatientParticipationResponse?.participations || [];
    return participations.filter(
      (p) => p.contractType === type && ACTIVATED_STATUS.includes(p.status)
    );
  };
  const hzvParticipations = getParticipationsByType(
    ContractType.ContractType_HouseDoctorCare
  );
  const favParticipations = getParticipationsByType(
    ContractType.ContractType_SpecialistCare
  );
  const { isContractSupport: isSupportABRG1006 } = useCheckContractSupport(
    [ScheinHintForCompliance.ABRG1006],
    hzvParticipations.map((p) => p.contractId)
  );
  const { isContractSupport: isSupportABRG829 } = useCheckContractSupport(
    [ScheinHintForCompliance.ABRG829],
    hzvParticipations.map((p) => p.contractId)
  );
  const { isContractSupport: isSupportVERT484 } = useCheckContractSupport(
    ['VERT484'],
    hzvParticipations.map((p) => p.contractId)
  );
  const { getBsnrIdByDoctorId } = useContext(GlobalContext.instance);
  const { mutateAsync, isPending: isLoadingGetFields } = useMutationGetFields();

  useEffect(() => {
    if (loading === 'idle') {
      return;
    }

    if (!isLoadingGetFields && !isLoadingGetSetting && !isLoadingGetSubGroup) {
      setLoading('idle');
    }
  }, [loading, isLoadingGetFields, isLoadingGetSetting, isLoadingGetSubGroup]);

  useEffect(() => {
    if (isSuccess && subGroups?.keys.length > 0) {
      setMainGroup(subGroups.keys);
    }
  }, [isSuccess, subGroups]);

  const clearServerError = () => {
    setCustomWarnings({});
    setValidations({});
    setDisabledFields({});
  };

  const setErrMapMessage = (fieldName: string, value: string) => {
    setErrMap((preState) => {
      const cloneData = { ...preState };

      if (!value) {
        delete cloneData[fieldName];

        return cloneData;
      }
      return { ...cloneData, [fieldName]: value };
    });
  };

  const getFieldValidateOnService = (
    kvTreatmentCase: TreatmentCaseNames,
    kvScheinSubGroup?: string,
    values?: ScheinInfo,
    preventUpdateInsuranceByReadCard = false
  ) => {
    if (!kvTreatmentCase) {
      return;
    }
    // todo query API should also provide subgroup so that we can simplify resp
    return mutateAsync(
      {
        treatmentCase: kvTreatmentCase,
        patientId: patientId,
      },
      {
        onSuccess: ({ data: res }) => {
          const tmpMapField = new Map<string, string>();
          const tmpMapForm = new Map<string, boolean>();
          let subGroupRules: Array<QueryIf | QueryValue | QueryOperator> = [];
          const fieldValidation = {} as {
            [key: string]: FieldError;
          };
          res?.caseFields?.fields?.forEach((fieldValue) => {
            if (
              fieldValue.validationType ===
              FieldValidationType.FieldValidationType_Required
            ) {
              fieldValidation[fieldValue.name] = newFieldError(
                fieldValue.name,
                fieldValue.validationType
              );
            }

            if (fieldValue.name.includes('g4101')) {
              // there are 2 fields in BE: g4101Quarter, g4101Year
              tmpMapField.set('g4101', fieldValue.validationType);
              tmpMapForm.set(fieldValue.name[0], true);
              if (
                fieldValue.validationType ===
                FieldValidationType.FieldValidationType_Required
              ) {
                fieldValidation['g4101'] = newFieldError(
                  fieldValue.name,
                  fieldValue.validationType
                );
              }
            } else if (fieldValue.name.includes('ad4125')) {
              tmpMapField.set('ad4125', fieldValue.validationType);
              tmpMapForm.set(fieldValue.name[0], true);
            } else if (fieldValue.name.includes(employeeFormPrefix, 0)) {
              tmpMapForm.set(employeeFormPrefix, true);
              tmpMapField.set(fieldValue.name, fieldValue.validationType);
              tmpMapForm.set(fieldValue.name[0], true);
            } else {
              tmpMapField.set(fieldValue.name, fieldValue.validationType);
              tmpMapForm.set(fieldValue.name[0], true);
            }
          });

          const subGroupVal = res?.caseFields?.subGroups?.find(
            (sg) => sg.code === kvScheinSubGroup
          );
          if (subGroupVal) {
            if (subGroupVal.rules.length > 0) {
              const arrValue: Array<QueryIf | QueryValue | QueryOperator> = [];
              subGroupVal.rules.forEach((val) => {
                const rule = JSON.parse(val);
                parsingStatement(rule, arrValue);
              });
              subGroupRules = arrValue;
              subGroupRules.forEach((subGroupRule) => {
                if (subGroupRule['key']) {
                  const subGroupKeyValue = subGroupRule as QueryValue;
                  let fName = subGroupKeyValue.key;

                  if (fName.includes('g4101')) {
                    // quarter
                    fName = 'g4101';
                  }

                  const fValidationType =
                    subGroupKeyValue.value as FieldValidationType;

                  // this case cover hidden by default, but required in certain subgroup
                  if (
                    fValidationType ===
                      FieldValidationType.FieldValidationType_Required &&
                    !fieldValidation[fName]
                  ) {
                    tmpMapField.set(fName, fValidationType);
                    tmpMapForm.set(fName, true);
                    fieldValidation[fName] = newFieldError(
                      fName,
                      fValidationType
                    );
                  }
                  // this case cover hidden by default, but optional in certain subgroup, does not update validation
                  if (
                    fValidationType ===
                      FieldValidationType.FieldValidationType_Optional &&
                    !fieldValidation[fName]
                  ) {
                    tmpMapField.set(fName, fValidationType);
                    tmpMapForm.set(fName, true);
                  }

                  // this is the case where config return the value, assume it will need to display
                  if (
                    fValidationType !==
                      FieldValidationType.FieldValidationType_Required &&
                    fValidationType !==
                      FieldValidationType.FieldValidationType_Optional
                  ) {
                    if (!fieldValidation[fName]) {
                      tmpMapField.set(fName, fValidationType);
                      tmpMapForm.set(fName, true);
                    }
                    currentFormikProps?.setFieldValue(fName, fValidationType);
                  }
                }
              });
            }
          }
          const activeInsurance = getActiveInsurance(
            patientManagement?.patient?.patientInfo?.insuranceInfos ?? []
          );

          setMapField(cloneDeep(tmpMapField));
          setMapForm(tmpMapForm);
          setPreviousTreatmentCase(kvTreatmentCase);
          setMapFormWithSubGroup(undefined);
          setMapFieldWithSubGroup(undefined);

          const formValues = currentFormikProps?.values;
          const req = {
            ...formValues,
            patientId: patientId,
            doctorId: doctor?.id,
            kvTreatmentCase: kvTreatmentCase,
            kvScheinSubGroup: kvScheinSubGroup,
          } as ScheinInfo;
          if (values?.insuranceId) {
            req.insuranceId = values.insuranceId;
          }
          if (kvScheinSubGroup) {
            req.kvScheinSubGroup = kvScheinSubGroup;
          }
          if (
            req.insuranceId &&
            formValues?.g4101Quarter &&
            formValues?.g4101Year &&
            formValues?.g4106
          ) {
            handleServerValidate(req);
          }

          if (!preventUpdateInsuranceByReadCard && isReadCard) {
            if (activeInsurance) {
              if (activeInsurance.insuranceType !== TypeOfInsurance.Private) {
                currentFormikProps?.setFieldValue(
                  'insuranceId',
                  activeInsurance?.id
                );
              }
              currentFormikProps?.setFieldValue(
                'g4104',
                activeInsurance?.insuranceCompanyId
              );
              if (tmpMapField.get('g3116')) {
                currentFormikProps?.setFieldValue(
                  'g3116',
                  activeInsurance?.wop ? Number(activeInsurance?.wop) : null
                );
              }
            }
          }
        },
      }
    );
  };

  useEffect(() => {
    if (!formRef.current.body) {
      return;
    }
    const onScrollFnc = () => {
      const targetElem = formRef.current.body?.querySelector('.main-content');
      const formInfors = targetElem?.querySelectorAll('.form-info');
      if (!targetElem || !formInfors || formInfors.length <= 0) {
        return;
      }
      const currentScrollTop = formRef.current.body!.scrollTop;
      const viewportScrollHeight = formRef.current.body!.scrollHeight;
      let maxScrollableHeight =
        viewportScrollHeight - formRef.current.body!.clientHeight;
      const visibleStatus: Array<{
        startRatio: number;
        elem: Element;
        rect: DOMRect;
      }> = [];
      let tmp = 0;
      let lstItemHeight = 0;
      formInfors.forEach((domItem, index) => {
        const rect = domItem.getClientRects()[0];
        visibleStatus[index] = {
          startRatio: tmp / viewportScrollHeight,
          elem: domItem,
          rect,
        };
        tmp += rect.height + SCROLL_SHIFTING;
        lstItemHeight = rect.height + SCROLL_SHIFTING;
      });
      maxScrollableHeight += lstItemHeight;
      const scrollRatio = currentScrollTop / maxScrollableHeight;
      activeIndexRev = visibleStatus.reverse().findIndex((item) => {
        return item.startRatio < scrollRatio;
      });
      const menuLst = formRef.current.nav?.querySelectorAll('.label-section');

      if (!menuLst) {
        return;
      }
      menuLst?.forEach(
        (domElem) =>
          (domElem.className = domElem.className.replaceAll(/ active/gi, ''))
      );
      if (activeIndexRev !== -1) {
        const selectedItem = menuLst[visibleStatus.length - 1 - activeIndexRev];
        selectedItem.className = selectedItem?.className + ' active';
      } else {
        menuLst[0].className = menuLst[0].className + ' active';
      }
    };
    formRef.current.body.addEventListener('scroll', onScrollFnc);
    formRef.current.body.scroll();

    return () => {
      if (formRef.current.body) {
        formRef.current.body.removeEventListener('scroll', onScrollFnc);
      }
    };
  }, [formRef.current.body]);

  // useHotkeys('alt+up', (evt) => {
  //   evt.preventDefault();
  //   if (document) {
  //     const targetElem = document?.querySelector('.create-schein_main-content');
  //     const formInfors = targetElem?.querySelectorAll('.form-info');
  //     activeIndexRev = activeIndexRev !== -2 ? activeIndexRev : 0;
  //     const nextActiveIndex = formInfors.length - activeIndexRev - 2;
  //     if (formInfors.length <= 0 || nextActiveIndex < 0) {
  //       return;
  //     }
  //     activeIndexRev = formInfors.length - 1 - nextActiveIndex;
  //     formInfors[nextActiveIndex].scrollIntoView({
  //       behavior: 'smooth',
  //       block: 'start',
  //       inline: 'nearest',
  //     });
  //   }
  // });

  // useHotkeys('alt+down', (evt) => {
  //   evt.preventDefault();
  //   if (document) {
  //     const targetElem = document?.querySelector('.create-schein_main-content');
  //     const formInfors = targetElem?.querySelectorAll('.form-info');
  //     activeIndexRev =
  //       activeIndexRev !== -2 ? activeIndexRev : formInfors.length - 1;
  //     const nextActiveIndex = formInfors.length - activeIndexRev;
  //     if (formInfors.length <= 0 || nextActiveIndex > formInfors.length - 1) {
  //       return;
  //     }
  //     activeIndexRev = formInfors.length - 1 - nextActiveIndex;
  //     formInfors[nextActiveIndex].scrollIntoView({
  //       behavior: 'smooth',
  //       block: 'start',
  //       inline: 'nearest',
  //     });
  //   }
  // });

  const getDefaultDoctor = (doctorInfo?: DoctorInfo): string => {
    if (doctorInfo?.treatmentDoctorId) {
      return doctorInfo.treatmentDoctorId;
    }
    if (currentUser && currentUser.markAsBillingDoctor) {
      return currentUser.id || '';
    }
    if (
      bSNRData?.data?.length === 1 &&
      doctors.length === 1 &&
      doctors[0].markAsBillingDoctor
    ) {
      return doctors[0].id || '';
    }

    const filtered = sortBy(
      patientFileStore.schein.list.filter(
        (s) => s.scheinMainGroup == MainGroup.KV
      ),
      (o) => o.createdTime
    );
    const findLastSchein = filtered.at(-1);
    return findLastSchein?.doctorId || '';
  };

  const setInitValue = async (
    res: GetSelectedTreatmentCaseSubgroupResponse
  ) => {
    const tmpInitValue = {
      patientId: props.patientId,
      psychotherapy: [],
      // default value of FieldArray
      re4233: [{}],
      markedAsBilled: false,
    } as unknown as ScheinInfo;
    if (res.kvTreatmentCase) {
      tmpInitValue.kvTreatmentCase = res.kvTreatmentCase;
      getFieldValidateOnService(res.kvTreatmentCase);
    }
    if (res.kvScheinSubGroup) {
      tmpInitValue.kvScheinSubGroup = res.kvScheinSubGroup;
    }
    const defaultDoctor = getDefaultDoctor(
      patientManagement?.patient?.patientInfo?.doctorInfo
    );
    if (
      defaultDoctor &&
      !/[0]{8}-[0]{4}-[0]{4}-[0]{4}-[0]{12}/g.test(defaultDoctor)
    ) {
      tmpInitValue.doctorId = defaultDoctor;
      tmpInitValue.assignedToBsnrId = getBsnrIdByDoctorId(defaultDoctor);
    }
    setInitialValues(tmpInitValue);
  };

  useEffect(() => {
    if (!isSuccessGetSetting || isNil(settings)) {
      return;
    }

    setAssignDiagnosis(settings.assignDiagnosisCreatedschein);
    if (settings.previouslySelected) {
      getLastSelectedTreatmentCaseSubgroupByPatientId(props.patientId).then(
        (res) => {
          setInitValue(res);
        }
      );
    } else {
      setInitValue({
        kvScheinSubGroup: settings.defaultSubgroup,
        kvTreatmentCase: settings.defaultTreatmentcase as TreatmentCaseNames,
      });
    }
  }, [
    settings?.previouslySelected,
    settings?.defaultSubgroup,
    settings?.defaultTreatmentcase,
    settings?.assignDiagnosisCreatedschein,
    JSON.stringify(currentUser),
  ]);

  useEffect(() => {
    // For assign default value in form in each cases
    // Don't assign in update case
    if (id || !currentFormikProps) {
      return;
    }

    if (mapFields.get('g4101')) {
      const defaultDate = patientFileStore.defaultDocumentDate;
      const currentDate = DatetimeUtil.date(defaultDate ?? undefined);
      const currentQuarter = DatetimeUtil.getQuarter(currentDate);
      const currentYear = currentDate.getFullYear();
      currentFormikProps.setFieldValue(
        'g4101',
        `${currentQuarter}.${currentYear}`
      );
      currentFormikProps.setFieldValue('g4101Quarter', currentQuarter);
      currentFormikProps.setFieldValue('g4101Year', currentYear);
    }
  }, [
    mapFields,
    mapFields.get('g4101'),
    Boolean(currentFormikProps),
    patientFileStore.defaultDocumentDate,
  ]);

  useEffect(() => {
    // For assign default value in form in each cases
    // Don't assign in update case
    if (id || !currentFormikProps) {
      return;
    }

    if (
      patientManagement.patient?.patientInfo?.insuranceInfos &&
      patientManagement.patient.patientInfo.insuranceInfos.length > 0
    ) {
      const publicInsuranceInfos =
        patientManagement.patient.patientInfo.insuranceInfos.filter(
          (i) => i.insuranceType == TypeOfInsurance.Public
        );
      const lastInsurance =
        publicInsuranceInfos[publicInsuranceInfos.length - 1];

      if (mapFields.get('insuranceId')) {
        currentFormikProps.setFieldValue(
          'insuranceId',
          lastInsurance?.id || null
        );
        currentFormikProps.setFieldValue(
          'g4104',
          lastInsurance?.insuranceCompanyId || null
        );
        getFieldValidateOnService(
          currentFormikProps.values?.kvTreatmentCase,
          currentFormikProps.values?.kvScheinSubGroup,
          {
            insuranceId: lastInsurance?.id || null,
          } as ScheinInfo
        );
        currentFormikProps.setFieldValue(
          'g4104',
          lastInsurance?.insuranceCompanyId || null
        );
      }
      if (mapFields.get('g3116')) {
        currentFormikProps.setFieldValue('g3116', Number(lastInsurance?.wop));
      }
    }
  }, [
    mapFields.get('insuranceId'),
    mapFields.get('g3116'),
    patientId,
    Boolean(currentFormikProps),
  ]);

  const cleanStates = () => {
    setMapField(new Map<string, string>());
    setMapForm(new Map<string, boolean>());
    setMapFieldWithSubGroup(new Map<string, string>());
    setMapFormWithSubGroup(new Map<string, boolean>());
    setPreviousTreatmentCase('');
    setErrMap(new Map<string, string>());
  };

  const convertServerErrorsToFormikErrors = (
    validations: ICustomWarning,
    values
  ) => {
    const errors = {} as FormikErrors<ScheinInfo>;
    Object.keys(validations)?.forEach((vK) => {
      const validation = validations[vK];
      // NOTE: skip if display type is toast
      if (validation.displayType === DisplayType.DisplayType_Toast) return;

      // NOTE: handle or condition response
      if (vK.indexOf(',') !== -1) {
        const splittedFields = vK.split(',');
        const anyFilled = splittedFields.find((af) => values[af]);

        // handle xor condition response
        if (
          validation.errorCode ===
          FieldErrorCode.FieldErrorCode_OnlyOneFieldAllowed
        ) {
          splittedFields.forEach((k) => {
            errors[k] = t(validation.errorCode);
          });
        } else if (!anyFilled) {
          splittedFields.forEach((k) => {
            errors[k] = t(`${validation.errorCode}`.toLowerCase());
          });
        }
      } else if (validation.errorCode.toLowerCase() === 'required') {
        if (isEmpty(values[vK])) {
          // only show required validation when there is no data
          errors[vK] = t(`${validation.errorCode}`.toLowerCase());
        } else if (
          vK === 'tsvgContactDate' &&
          !compareDay(values['tsvgContactDate']) &&
          values['tsvgContactType'] !== ''
        ) {
          errors[vK] = t('timeNotValid');
        }
      } else {
        errors[vK] = t(`${validation.errorCode}`);
      }
    });
    return errors;
  };

  const validateFields = (validationContext) => {
    validateRequired('kvTreatmentCase', validationContext);
    validateRequired('doctorId', validationContext);
    validateG4101(validationContext);
    validateAd4125(validationContext);
    validateArmedForceInsurance(validationContext);
    validateLANR(validationContext);
    validateLANRDigitalRule(validationContext);
    validateRe4229(validationContext);
    // validateG4101AndG4102(validationContext);
    validateTsvgContractType(validationContext);
    validatePsycotherapy(validationContext);
  };

  const getHzvFavWarning = () => {
    let hintList: string[] = [];
    const hasActiveHzv = hzvParticipations.some(
      (p) => p.status === PatientParticipationStatus.PatientParticipation_Active
    );
    const hasFav = favParticipations.length > 0;

    if (hasFav) {
      hintList = [t('warningFavContent')];
    } else if (hasActiveHzv) {
      // hintList = isSupportABRG1006
      //   ? [t('warningHzvContent_ABRG1006')]
      //   : isSupportABRG829
      //     ? [t('warningHzvContent_ABRG829')]
      //     : [];

      if (isSupportVERT484) {
        hintList.push(t('warningHzvContent_VERT484'));
      }
    }
    if (hintList[0]) {
      return hintList.map((hint) => (
        <Flex mb={24} key={hint}>
          <MessageBar type="info" content={hint} hasBullet={false} />
        </Flex>
      ));
    }
    return null;
  };

  useEffect(() => {
    setCatalogsDataKeyText(parseCatalogsData());
  }, []);

  const handleData = (values: ScheinInfo): ScheinInfo => {
    const data: ScheinInfo = dataTransform(values) as ScheinInfo;
    if (id) {
      //change to update function
      data.scheinId = id;
      data.takeOverDiagnoseIds = [];
    }
    if (data.psychotherapy.length == 1) {
      const a = pickBy(data.psychotherapy[0], (v) => typeof v != 'undefined'),
        b = pickBy(INITIAL_PSYCHOTHERAPY, (v) => typeof v != 'undefined');
      b.id = a.id;
      if (isEqual(a, b)) {
        data.psychotherapy = [];
      }
    }
    return data;
  };

  const resetDisabledFields = (disabledFields: any) => {
    const resetValues = Object.keys(disabledFields || {}).reduce(
      (resetValues: { [fieldName: string]: string }, fieldName) => ({
        ...resetValues,
        [fieldName]: '',
      }),
      {}
    );

    currentFormikProps?.setValues((prevValues) => ({
      ...prevValues,
      ...resetValues,
    }));
  };

  const handleErrors = (serverLst: any, values: ScheinInfo) => {
    const errorsAsync = convertServerErrorsToFormikErrors(
      { ...serverLst?.errors },
      values
    );
    currentFormikProps?.setErrors(errorsAsync);
    return errorsAsync;
  };

  const handleInsuranceIdError = (error: any) => {
    const insuranceIdError = get(error, 'insuranceId');
    const typeInsuranceIdError = get(insuranceIdError, 'validationType');

    return !!insuranceIdError && typeInsuranceIdError !== 'Warning';
  };

  // NOTE: this function is used to validate form fields (Frontend)
  const onValidateForm = async (values: ScheinInfo) => {
    const errors = {} as FormikErrors<ScheinInfo>;

    const validationContext = {
      values,
      errors,
      t,
      mapFields,
      tPsychotherapy,
      patient: patientManagement.patient,
    };

    validateFields(validationContext);
    const serverErrors = convertServerErrorsToFormikErrors(validations, values);
    return { ...errors, ...errMap, ...serverErrors };
  };

  // NOTE: validate formik after server run validation.
  useEffect(() => {
    currentFormikProps?.validateForm();
  }, [validations]);

  // NOTE: only one place for handling error
  const handleServerValidate = async (values: ScheinInfo) => {
    const data = handleData(values);
    const rs = await serverValidate(data, [...insuranceState.insurance]);
    if (!rs?.error) {
      clearServerError();
      return { isError: false, serverLst: null };
    }

    const serverLst = parsingErrorObject(rs.error);

    const isEmptyServerLst =
      isempty(serverLst?.errors) &&
      isempty(serverLst?.warnings) &&
      isempty(serverLst?.disabledFields);

    if (isEmptyServerLst) {
      clearServerError();
      return { isError: false, serverLst };
    }

    setValidations({ ...serverLst?.errors });
    setCustomWarnings({ ...serverLst?.warnings });
    setDisabledFields({ ...serverLst?.disabledFields });

    if (!isempty(serverLst?.disabledFields)) {
      resetDisabledFields(serverLst?.disabledFields);
    }

    const errorsAsync = handleErrors(serverLst, values);

    if (!isempty(errorsAsync) || handleInsuranceIdError(rs.error)) {
      return { isError: true, serverLst };
    }

    return { isError: false, serverLst };
  };

  const onChangeField = debounce(handleServerValidate, 500);

  const renderForm = (formikProps: FormikProps<ScheinInfo>) => {
    const { values, isSubmitting, resetForm, setValues, setFieldValue } =
      formikProps;

    if (isSubmitting) {
      if (!isScroll) setIsScroll(true);
    } else {
      if (isScroll) {
        const invalidElements =
          document.getElementsByClassName('bp5-intent-danger');
        if (invalidElements && invalidElements.length > 0) {
          scrollToFirstError(invalidElements[0]);
          setIsScroll(false);
        }
      }
    }

    const onInsuranceChange = (insurance: InsuranceInfo) => {
      setValidations({});
      if (formikRef.current?.values?.insuranceId === insurance?.id) return;

      getFieldValidateOnService(
        formikProps.values.kvTreatmentCase,
        formikProps.values.kvScheinSubGroup,
        { ...formikProps.values, insuranceId: insurance?.id },
        true
      );
      setFieldValue('insuranceId', insurance?.id);
      setFieldValue('g4104', insurance?.insuranceCompanyId);
      setFieldValue('g4106', null);

      if (mapFields.get('g3116')) {
        currentFormikProps?.setFieldValue('g3116', Number(insurance?.wop));
      }
    };

    const autoSelectWithReadCard = () => {
      if (isReadCard) {
        const activeInsurance = getActiveInsurance(
          patientManagement?.patient?.patientInfo?.insuranceInfos ?? []
        );
        if (activeInsurance?.insuranceType !== TypeOfInsurance.Private) {
          formikProps.setFieldValue('insuranceId', activeInsurance?.id || null);
        }
        if (activeInsurance) {
          onInsuranceChange(activeInsurance);
        }
      }
    };

    const onSubGroupChange = (newSubgroup: string) => {
      const prevFormValues = { ...values };
      const kvTreatmentCase = getTreatmentCaseFromSubgroup(
        newSubgroup
      ) as TreatmentCaseNames;
      if (kvTreatmentCase !== '0102' && Number(values.tsvgContactType) === 3) {
        delete prevFormValues?.tsvgContactType;
        delete prevFormValues?.tsvgTranferCode;
        delete prevFormValues?.tsvgContactDate;
      }
      resetForm();
      setValues(prevFormValues);
      setFieldValue('kvTreatmentCase', kvTreatmentCase);
      getFieldValidateOnService(kvTreatmentCase, newSubgroup);
      setFieldValue('kvScheinSubGroup', newSubgroup);
      autoSelectWithReadCard();
    };

    const onSelectTsvgContactType = async (tsvgContactType: string) => {
      await handleServerValidate({
        ...values,
        tsvgContactType: tsvgContactType || undefined,
      });

      if (!tsvgContactType) {
        const cloneValues = { ...values };
        delete cloneValues.tsvgContactType;
        delete cloneValues.tsvgTranferCode;
        delete cloneValues.tsvgContactDate;

        setValues(cloneValues);
        // waiting for update new value before checking validation
        await new Promise((resolve) => setTimeout(resolve));
        formikProps.validateForm();
      }
    };

    const onSelectDoctor = async (doctorId: string) => {
      await handleServerValidate({
        ...values,
        doctorId: doctorId || '',
      });
      currentFormikProps?.setFieldValue('doctorId', doctorId);
      formikProps.validateForm();
    };

    const onCloseConfirmChangeTreatment = () => {
      setConfirmChangeTreatmentOpen(false);
    };

    const dontChangeTreatmentCase = () => {
      const nextValue = {
        doctorId: initialValues.doctorId,
        patientId: props.patientId,
        kvTreatmentCase: previousTreatmentCase,
      } as ScheinInfo;
      resetForm();
      setValues(nextValue);
      onCloseConfirmChangeTreatment();
    };

    const changeTreatmentCaseByConfirm = async () => {
      const nextValue = {
        patientId: props.patientId,
        kvTreatmentCase: values.kvTreatmentCase,
        doctorId: values.doctorId,
      } as ScheinInfo;
      getFieldValidateOnService(values.kvTreatmentCase);
      resetForm();
      setValues(nextValue);
      onCloseConfirmChangeTreatment();
      autoSelectWithReadCard();
      if (!nextValue.kvTreatmentCase) {
        cleanStates();
      }
    };

    const onAutoFillField4205 = () => {
      if (
        formikProps.values.kvScheinSubGroup == '21' ||
        formikProps.values.kvScheinSubGroup == '27'
      ) {
        setAutoFillField4205(true);
      }
    };

    const strValidations = Object.fromEntries(mapFields) as {
      [key: string]: FieldValidationType;
    };
    Object.keys(validations).forEach((v) => {
      if (v.indexOf(',')) {
        strValidations[v] = `${
          validations[v]?.errorCode || ''
        }`.toLowerCase() as FieldValidationType;
      }
    });

    return loading === 'loading' ? (
      <LoadingState />
    ) : (
      <>
        <Form>
          <Flex auto column>
            <Box
              className={getCssClass(
                Classes.DIALOG_BODY,
                'sl-schein-form-content-dialog-body'
              )}
              ref={(ref) => (formRef.current.body = ref)}
            >
              <Box>
                <Flex className="form-wrapper">
                  <Flex
                    className="nav"
                    ref={(ref) => (formRef.current.nav = ref)}
                  >
                    <div className="wrapper">
                      <div className="item-container general"></div>
                      <div className="item-container referral"></div>
                      <div className="item-container additional"></div>
                      <div className="item-container psychotherapie"></div>
                      <div className="item-container employee"></div>
                    </div>
                  </Flex>
                  <Flex
                    className="main-content create-schein_main-content"
                    auto
                    column
                    justify="space-between"
                    align="center"
                  >
                    {getHzvFavWarning()}
                    <GeneralForm
                      {...formikProps}
                      mainGroups={mainGroups}
                      doctors={doctors}
                      mapDoctor={employeeMap}
                      onSubGroupChange={onSubGroupChange}
                      onInsuranceChange={onInsuranceChange}
                      onChangeField={onChangeField}
                      mapFields={
                        mapFieldsWithSubGroup
                          ? mapFieldsWithSubGroup
                          : mapFields
                      }
                      setErrMapMessage={setErrMapMessage}
                      catalogsData={catalogsDataKeyText}
                      navRef={formRef.current.nav}
                      validationList={strValidations}
                      id={id || ''}
                      formFieldAltActions={{
                        getFieldValidateOnService,
                        dialogCloseAction: () => onCancel,
                      }}
                      customWarnings={customWarnings}
                      patientId={patientId}
                      currentUser={currentUser}
                      isCheckOpenM39={autoFillModalM39}
                      onOpenModalM39={setAutoFillModalM39}
                      onDoctorChange={onSelectDoctor}
                      setInsuranceState={setInsuranceState}
                    />
                    {(mapForm.get(referralFormPrefix) ||
                      mapFormWithSubGroup?.get(referralFormPrefix)) && (
                      <ReferralForm
                        {...formikProps}
                        mapFields={
                          mapFieldsWithSubGroup
                            ? mapFieldsWithSubGroup
                            : mapFields
                        }
                        onAutoFillField4205={onAutoFillField4205}
                        setErrMapMessage={setErrMapMessage}
                        catalogsData={catalogsDataKeyText}
                        navRef={formRef.current.nav}
                        validationList={strValidations}
                        disabledFields={disabledFields}
                        onChangeField={onChangeField}
                      />
                    )}
                    {(mapForm.get(additionFormPrefix) ||
                      mapForm.get(generalFormPrefix) || // for field g4102
                      mapFormWithSubGroup?.get(additionFormPrefix)) && (
                      <AdditionForm
                        {...formikProps}
                        mapFields={
                          mapFieldsWithSubGroup
                            ? mapFieldsWithSubGroup
                            : mapFields
                        }
                        isEdit={!!id}
                        updateInsurances={insuranceState.insurance}
                        setErrMapMessage={setErrMapMessage}
                        catalogsData={catalogsDataKeyText}
                        navRef={formRef.current.nav}
                        validationList={strValidations}
                        validations={validations}
                        customWarnings={customWarnings}
                        setValidations={setValidations}
                        onContactTypeChange={onSelectTsvgContactType}
                        onChangeField={onChangeField}
                      />
                    )}
                    {(mapForm.get(psychotherapyFormPrefix) ||
                      mapFormWithSubGroup?.get(psychotherapyFormPrefix)) &&
                      ['0101', '0102'].includes(values.kvTreatmentCase) && (
                        <PsychotherapieForm
                          {...formikProps}
                          mapFields={
                            mapFieldsWithSubGroup
                              ? mapFieldsWithSubGroup
                              : mapFields
                          }
                          setErrMapMessage={setErrMapMessage}
                          catalogsData={catalogsDataKeyText}
                          navRef={formRef.current.nav}
                          validationList={strValidations}
                          patientId={patientId}
                          doctors={doctors}
                          values={values}
                          onChangeField={onChangeField}
                          patient={patientManagement.patient}
                          scrollToTop={
                            scrollToSection !== FORM_SECTION.PSYCHOTHERAPY
                          }
                        />
                      )}
                    {mapForm.get(employeeFormPrefix) && (
                      <EmployeeForm
                        {...formikProps}
                        mapFields={
                          mapFieldsWithSubGroup
                            ? mapFieldsWithSubGroup
                            : mapFields
                        }
                        navRef={formRef.current.nav}
                        validationList={strValidations}
                        onChangeField={onChangeField}
                      />
                    )}
                  </Flex>
                </Flex>
              </Box>
            </Box>

            <Flex
              auto
              align="center"
              h={80}
              className="footer sl-schein-form-content-dialog-footer"
            >
              <Box className="inner-wrapper">
                <Box className="shortcut-keys">
                  {t('keyboardCommandNavigateBetweenSections')}
                </Box>
                <Box className="btns-group">
                  <Button
                    intent={Intent.PRIMARY}
                    className="btn-submit"
                    type="submit"
                    loading={isSubmitting}
                    disabled={isSubmitting}
                  >
                    {id ? t('save') : t('create')}
                  </Button>
                  <Button
                    className="btn-cancel"
                    minimal
                    outlined
                    loading={isSubmitting}
                    disabled={isSubmitting}
                    onClick={onCancel}
                  >
                    {t('cancel')}
                  </Button>
                </Box>
              </Box>
            </Flex>
          </Flex>
          <Modal
            title={t('changeTreatmentCaseTitle')}
            isCloseButtonShown={false}
            isOpen={confirmChangeTreatmentOpen}
            onClose={dontChangeTreatmentCase}
            className="sl-CreateScheinDialog_confirm-modal"
            autoFocus={true}
            tabIndex={-1}
          >
            <Flex className={Classes.DIALOG_BODY}>
              {t('changeTreatmentCaseContent')}
            </Flex>
            <FlexFooter
              justify="space-between"
              className={Classes.DIALOG_FOOTER}
            >
              <Box w="45%">
                <Button
                  fill
                  intent="primary"
                  onClick={() => {
                    changeTreatmentCaseByConfirm();
                  }}
                >
                  {t('yesChange')}
                </Button>
              </Box>
              <Box w="45%">
                <Button fill onClick={dontChangeTreatmentCase}>
                  {t('noChange')}
                </Button>
              </Box>
            </FlexFooter>
          </Modal>
        </Form>
        {autoFillModalM39 && currentFormikProps && (
          <>
            <AutoFill21
              targetFormik={currentFormikProps}
              onClose={() => setAutoFillModalM39(false)}
            />
          </>
        )}
        {isOpenTakeOverModal && (
          <SelectDiagnosisDialog
            patientId={patientId}
            takeoverDiagnosisType={
              TakeoverDiagnosisType.TakeoverDiagnosisSchein
            }
            quarter={values.g4101Quarter}
            year={values.g4101Year}
            isOpen={isOpen}
            onCancel={(continueWithoutTakeOver: boolean) => {
              if (!continueWithoutTakeOver) {
                setSkipNotice(false);
                setIsOpenTakeOverModal(false);
                return;
              }
              actionOvertakeDiagnosis([]);
              setIsOpenTakeOverModal(false);
            }}
            onTakeover={(
              existedDiagnosis,
              newDiagnosis,
              mappingTreatmentRelevent
            ) => {
              const takeOverDiagnoseIds = existedDiagnosis
                .map((item) => item.id)
                .filter((id): id is string => id !== undefined);
              actionOvertakeDiagnosis(
                takeOverDiagnoseIds,
                newDiagnosis,
                mappingTreatmentRelevent
              );
              setIsOpenTakeOverModal(false);
            }}
          />
        )}
      </>
    );
  };

  const submit = async (values: ScheinInfo) => {
    formikRef.current?.setSubmitting(true);

    let createOrUpdateScheinFunc: CreateOrUpdateSchein = createSchein;
    const data = handleData(values);
    const newTakeOverDiagnosis =
      data.newTakeOverDiagnosis?.map((item) => {
        return {
          ...item,
          treatmentDoctorId: data.doctorId,
          billingDoctorId: data.doctorId,
        };
      }) || [];
    let payload = {
      markedAsBilled: data.markedAsBilled,
      hzvContractId: data.hzvContractId,
      excludeFromBilling: data.excludeFromBilling,
      doctorId: data.doctorId,
      kvScheinSubGroup: data.kvScheinSubGroup,
      kvTreatmentCase: data.kvTreatmentCase,
      psychotherapy: data.psychotherapy,
      takeOverDiagnoseInfos: (data.takeOverDiagnoseIds || []).map((item) => ({
        id: item,
        isTreatmentRelevant: data.mappingTreatmentRelevent?.[item] || false,
      })),
      newTakeOverDiagnosis: newTakeOverDiagnosis,
      patientId,
      isInsuranceInformedTherapy: data.isInsuranceInformedTherapy,
      pausingStartDate: data.pausingStartDate,
      pausingEndDate: data.pausingEndDate,
      g4104: data.g4104,
      assignedToBsnrId: data.assignedToBsnrId,
    } as ScheinInfo;

    if (mapForm.get(employeeFormPrefix)) {
      payload = {
        ...payload,
        bgEmployerName: data.bgEmployerName,
        bgEmployerStreet: data.bgEmployerStreet,
        bgEmployerHousenumber: data.bgEmployerHousenumber,
        bgEmployerPostcode: data.bgEmployerPostcode,
        bgEmployerCity: data.bgEmployerCity,
        bgEmployerCountry: data.bgEmployerCountry,
      };
    }

    mapFields.forEach((_, key) => {
      payload[key] = data[key];
      if (key === 're4233') {
        payload = {
          ...payload,
          re4233: data.re4233?.filter((item) => item.from && item.to),
        };
      } else if (key === 'ad4125') {
        payload = {
          ...payload,
          ad4125From: data.ad4125From,
          ad4125To: data.ad4125To,
        };
      } else if (key === 're4218') {
        payload = {
          ...payload,
          re4242: data.re4242,
        };
      }
    });

    if (id) {
      payload.scheinId = data.scheinId;
      payload.scheinMainGroup = data.scheinMainGroup;
      createOrUpdateScheinFunc = updateSchein;
    }

    if (payload.g4101) {
      const yearQuarter = payload.g4101.split('.');
      if (yearQuarter.length > 0) {
        payload.g4101Quarter = parseInt(yearQuarter[0]);
        payload.g4101Year = parseInt(yearQuarter[1]);
      }
    }

    try {
      const res: void | CreateScheinResponse =
        await createOrUpdateScheinFunc(payload);
      patientFileActions.schein.getScheinsOverview(patientId);

      reloadSchein(patientManagement).then(({ scheins }) => {
        const foundScheinIndex = scheins.findIndex((schein: ScheinItem) => {
          if (res instanceof Object && res.scheinItem) {
            return schein.scheinId === res.scheinItem.scheinId;
          }
          if (id) {
            return schein.scheinId === data.scheinId;
          }
          return false;
        });
        const foundSchein =
          foundScheinIndex === -1 ? undefined : scheins[foundScheinIndex];
        patientFileActions.schein.setActivatedSchein(
          foundSchein,
          undefined,
          props.patientId
        );
      });

      if (toasterRef) {
        alertSuccessfully(!id ? t('created') : t('updated'), {
          timeout: TOASTER_TIMEOUT_CUSTOM,
          toaster: toasterRef,
        });
      }
      if (
        res instanceof Object &&
        patientFileStore.terminalId &&
        res.scheinItem
      ) {
        await takeoverServiceTerminalApproval({
          timelineId: patientFileStore.terminalId,
          scheinId: res.scheinItem.scheinId,
        });
      }
      formikRef.current?.resetForm();
      if (isCard) {
        onCreateScheinMobileCard?.();
        return;
      }
      cleanStates();
      reloadQuarters?.({ year: data.g4101Year, quarter: data.g4101Quarter });
      onCreateScheinMailBox?.();
      props.onClose();
      !id && setShowDMPEnrollDialog(patientFileStore.patient.isReadingCard);
      if (data?.ps4234) {
        reRunValidateService({ patientId });
      }
      refetchGetTherapies();
      if (!isCard) {
        setMapField(new Map<string, string>());
        setMapForm(new Map<string, boolean>());
        setMapFieldWithSubGroup(new Map<string, string>());
        setMapFormWithSubGroup(new Map<string, boolean>());
        setPreviousTreatmentCase('');
        setErrMap(new Map<string, string>());
      }
    } catch (err) {
      console.error(err);
      throw err;
    } finally {
      formikRef.current?.setSubmitting(false);
    }
  };

  const onSaveForm = async (
    values: ScheinInfo,
    serverLst: { errors: object; warnings: object; notices: object } | null
  ) => {
    const hasNoNotice = isempty(serverLst?.notices) || skipNotice;
    if (hasNoNotice && isempty(serverLst?.errors)) {
      submit(values);
      return;
    }
  };

  const onSubmitForm = async (values: ScheinInfo, { setSubmitting }) => {
    setSubmitting(true);
    try {
      await updateInsurances({
        patientId,
        insuranceInfos: insuranceState.insurance,
        insuranceInfosDeleted: insuranceState.insuranceDeleted,
        currentScheinId: values.scheinId,
      });
      const { isError, serverLst } = await handleServerValidate(values);
      if (isError) {
        const toastErrors = Object.values(serverLst?.errors || {}).filter(
          (error) => error.displayType === DisplayType.DisplayType_Toast
        );
        toastErrors.forEach((toastError) => {
          const msg = tCreateError(`Error.${toastError.errorCode}`);
          alertError(msg);
        });
        return;
      }

      // show warning dialog when have notices data
      serverLst?.notices && setNoticeErrors(serverLst?.notices);
      if (!isempty(serverLst?.notices) && !skipNotice) {
        setIsOpenWarningConfirm(true);
        return;
      }

      if (assignDiagnosis === 'manually' && !values.scheinId) {
        setIsOpenTakeOverModal(true);
        setActionOvertakeDiagnosis(
          () =>
            (
              takeOverDiagnoseIds: string[],
              newTakeOverDiagnosis: ComposerDiagnosisType[],
              mappingTreatmentRelevent: {
                [key: string]: boolean;
              }
            ) => {
              return onSaveForm(
                {
                  ...values,
                  takeOverDiagnoseIds,
                  newTakeOverDiagnosis,
                  mappingTreatmentRelevent,
                },
                serverLst
              );
            }
        );
        return;
      }

      return onSaveForm(values, serverLst);
    } catch (err) {
      console.error(err);
      throw err;
    } finally {
      setHash('');
      setSubmitting(false);
      refetch();
    }
  };

  return (
    <Flex auto column className={className}>
      <Formik<ScheinInfo>
        innerRef={formikRef}
        initialValues={initialValues}
        enableReinitialize
        validate={onValidateForm}
        onSubmit={onSubmitForm}
        render={renderForm}
      />
      <InfoConfirmDialog
        isOpen={isOpenWarningConfirm}
        title={t(`warningValidate`)}
        isShowIconTitle={true}
        isCloseButtonShown={false}
        confirmText={tButtonActions('no')}
        cancelText={tButtonActions('yes')}
        onClose={() => {
          setIsOpenWarningConfirm(false);
        }}
        onConfirm={async () => {
          setSkipNotice(true);
          setIsOpenWarningConfirm(false);
          currentFormikProps?.submitForm();
        }}
      >
        <Flex column>
          {!!Object.keys(warningTreatmentCategory)[0] && (
            <>
              <BodyTextM>{t('warningTreatmentCategory')}</BodyTextM>
              <Flex column>
                {Object.keys(warningTreatmentCategory).map((e, index) => (
                  <BodyTextM key={index}>
                    <li>
                      {warningTreatmentCategory[e]?.metaData
                        ?.warningKeyAdditional
                        ? `${tError(
                            `noticeWarning.${noticeErrors[e]?.metaData?.warningKeyAdditional}`
                          )} in `
                        : ''}
                      {tError(e)}
                    </li>
                  </BodyTextM>
                ))}
                {noticeErrors['re4214'] && (
                  <BodyTextM>
                    <li>{t('warningMissingRe4214')}</li>
                  </BodyTextM>
                )}
              </Flex>
            </>
          )}
          {Object.keys(warningTreatmentCategory).length === 0 &&
            noticeErrors['re4214'] && (
              <BodyTextM>{t('warningMissingRe4214Only')}</BodyTextM>
            )}
          <BodyTextM>{t('addMoreInformation')}</BodyTextM>
        </Flex>
      </InfoConfirmDialog>
      {patientManagement.patient && (
        <CheckInformColonoscopyInfo
          patient={patientManagement.patient}
          contracts={patientManagement.availableHzvContracts}
        />
      )}
    </Flex>
  );
}

export default forwardRef(FormContent);
