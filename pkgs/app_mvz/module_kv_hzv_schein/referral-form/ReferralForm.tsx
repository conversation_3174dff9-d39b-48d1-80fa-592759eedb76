import React, { createRef, memo, useEffect, useState, useMemo } from 'react';
import ReactDOM from 'react-dom';
import { Field, FormikProps, FieldArray } from 'formik';

import { IMvzTheme } from '@tutum/mvz/theme';
import { IdAndText, ScheinInfo } from '../CreateSchein.service';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import {
  Flex,
  H3,
  FormGroup2,
  BodyTextM,
  Box,
  Button,
  Svg,
} from '@tutum/design-system/components';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import {
  FormGroup,
  Label,
  Icon,
  Divider,
} from '@tutum/design-system/components/Core';
import {
  InputValue,
  InputTimeRangeValue,
  InputDateTimeValue,
  ReactSelectValue,
} from '@tutum/mvz/module_kv_hzv_schein/ScheinComponents';
import { scrollToElem } from '../utils';
import { FieldValidationType } from '@tutum/hermes/bff/schein_common';
import { LanrBsnrSuggestion } from '@tutum/mvz/components/lanr-bsnr-suggest/LanrBsnrSuggestion';
import { handleGetList } from '@tutum/mvz/components/lanr-bsnr-suggest/LanrBsnrSuggestion.helper';
import AllerySuggestor from '@tutum/mvz/components/select/allery';
import { LanrBsnrSuggestionProvider } from '@tutum/mvz/components/lanr-bsnr-suggest/LanrBsnrSuggestion.context';

const minusCircle = '/images/minus-circle.svg';
const plusIcon = '/images/plus.svg';

export interface IReferralFormProps {
  className?: string;
  theme?: IMvzTheme;
  onAutoFillField4205: () => void;
  mapFields: Map<string, string>;
  setErrMapMessage: (fieldName: string, value: string) => void;
  catalogsData: { [key: string]: IdAndText[] };
  navRef: HTMLElement | null;
  validationList: {
    [key: string]: FieldValidationType;
  };
  disabledFields: {
    [key: string]: object;
  };
  onChangeField: (values: ScheinInfo) => void;
}

const namespace = 'Schein';
const nestedTrans = 'referral/consulting';
const REFERRAL_FIELDS = [
  're4221',
  're4241',
  're4248',
  're4225',
  're4217',
  're4242',
  're4249',
  're4226',
  're4218',
  're4214',
  're4220',
  're4243',
  're4219',
  're4208',
  're4207',
  're4205',
  're4209',
  're4229',
  're4233',
];
const LAB_SUB_GROUP = ['21', '23', '24', '26', '27', '28', '30', '31'];

function ReferralForm(
  props: IReferralFormProps & II18nFixedNamespace & FormikProps<ScheinInfo>
): React.FunctionComponentElement<IReferralFormProps> {
  const {
    className,
    t,
    mapFields,
    errors,
    validationList,
    disabledFields,
    touched,
    submitCount,
    setErrMapMessage,
    catalogsData,
    navRef,
    values,
    isValidating,
    setFieldTouched,
    onChangeField,
  } = props;
  const { t: tReferralConsulting } = I18n.useTranslation({
    namespace,
    nestedTrans,
  });

  const { t: tRe4214Tooltip } = I18n.useTranslation({
    namespace,
    nestedTrans: `${nestedTrans}.re4214Tooltip`,
  });

  const [referralRefSlot, setReferralRefSlot] = useState<Element | null>(null);
  const [isExpanded, setExpand] = useState(true);

  useEffect(() => {
    setReferralRefSlot(navRef?.querySelector('.referral') || null);
  }, [navRef]);
  const contentRef: React.RefObject<HTMLDivElement> = createRef();

  const menuClick = () => {
    if (!contentRef.current) {
      return;
    }
    scrollToElem(contentRef.current);
    return false;
  };

  const isLabSubGroup = useMemo(() => {
    return LAB_SUB_GROUP.includes(values.kvScheinSubGroup!);
  }, [values.kvScheinSubGroup]);

  useEffect(() => {
    const hasErrorInSection = REFERRAL_FIELDS.some((field) => !!errors[field]);

    if (hasErrorInSection && submitCount > 0) {
      setExpand(true);
    }
  }, [errors, isValidating]);

  return (
    <LanrBsnrSuggestionProvider>
      <Flex
        column
        className={getCssClass(className, 'form-info')}
        id="general-form"
        mb={16}
      >
        {referralRefSlot
          ? ReactDOM.createPortal(
            <H3 className="label-section referral-form" onClick={menuClick}>
              {t('title')}
            </H3>,
            referralRefSlot
          )
          : null}
        <H3
          className="label-section"
          onClick={() => setExpand(!isExpanded)}
          ref={contentRef}
        >
          {t('title')}
          <Icon
            className="expand-icon"
            icon={isExpanded ? 'chevron-up' : 'chevron-down'}
          />
        </H3>
        <Flex
          column
          className={getCssClass('form-body', isExpanded ? 'expanded' : '')}
        >
          {mapFields.get('re4221') && (
            <Flex className="row">
              <ReactSelectValue
                idField="re4221"
                namespace={namespace}
                nestedTrans={nestedTrans}
                touched={touched}
                onSelected={(item) => {
                  onChangeField({
                    ...values,
                    ['re4221']: item.value.toString(),
                  });
                }}
                setFieldTouched={setFieldTouched}
                isShowValue={true}
                listValues={catalogsData['re4221']}
                validationType={validationList.re4221}
                submitCount={submitCount}
                errors={errors}
                selectedValue={values['re4221']}
              />
            </Flex>
          )}
          {(mapFields.get('re4241') ||
            mapFields.get('re4248') ||
            mapFields.get('re4225') ||
            mapFields.get('re4217')) && (
              <FormGroup>
                <Flex className="group-field" column gap={8}>
                  <Label name="erstveranlassers" className="label-group">
                    {t('erstveranlassers.title')}
                  </Label>
                  <Flex auto justify="space-between">
                    <Flex className="half-row">
                      {isLabSubGroup ? (
                        <FormGroup2
                          label={tReferralConsulting('erstveranlassers.re4241')}
                          name="re4241"
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                        >
                          <Field name="re4241">
                            {({ field, form }) => {
                              return (
                                <LanrBsnrSuggestion
                                  form={form}
                                  field={field}
                                  fieldDepend={'re4217'}
                                  onChangeField={onChangeField}
                                  hasError={!!errors['re4241']}
                                  handleGetList={handleGetList(
                                    're4241',
                                    false,
                                    t
                                  )}
                                />
                              );
                            }}
                          </Field>
                        </FormGroup2>
                      ) : (
                        <InputValue
                          namespace={namespace}
                          idField="re4241"
                          nestedTrans={nestedTrans}
                          titleField="erstveranlassers.re4241"
                          placeholder="xxxxxxxxx"
                          validationType={validationList.re4241}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          maxLength={9}
                          minLength={9}
                          onChange={onChangeField}
                        />
                      )}
                    </Flex>
                    <Flex className="half-row">
                      <InputValue
                        namespace={namespace}
                        idField="re4248"
                        nestedTrans={nestedTrans}
                        titleField="erstveranlassers.re4248"
                        placeholder="xxxxxxxxx"
                        validationType={validationList.re4248}
                        submitCount={submitCount}
                        touched={touched}
                        errors={errors}
                        maxLength={9}
                        minLength={9}
                        onChange={onChangeField}
                      />
                    </Flex>
                  </Flex>
                  <Flex auto justify="space-between">
                    <Flex className="half-row">
                      <InputValue
                        namespace={namespace}
                        idField="re4225"
                        nestedTrans={nestedTrans}
                        titleField="erstveranlassers.re4225"
                        placeholder="xxxxxxxxx"
                        validationType={validationList.re4225}
                        submitCount={submitCount}
                        touched={touched}
                        errors={errors}
                        maxLength={9}
                        minLength={9}
                        onChange={onChangeField}
                      />
                    </Flex>
                    <Flex className="half-row">
                      {isLabSubGroup ? (
                        <FormGroup2
                          label={tReferralConsulting('erstveranlassers.re4217')}
                          name="re4217"
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                        >
                          <Field name="re4217">
                            {({ field, form }) => {
                              return (
                                <LanrBsnrSuggestion
                                  form={form}
                                  field={field}
                                  fieldDepend={'re4241'}
                                  isBsnr
                                  onChangeField={onChangeField}
                                  hasError={!!errors['re4217']}
                                  handleGetList={handleGetList('re4241', true, t)}
                                />
                              );
                            }}
                          </Field>
                        </FormGroup2>
                      ) : (
                        <InputValue
                          namespace={namespace}
                          idField="re4217"
                          nestedTrans={nestedTrans}
                          titleField="erstveranlassers.re4217"
                          placeholder="xxxxxxxxx"
                          validationType={validationList.re4217}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          maxLength={9}
                          minLength={9}
                          onChange={onChangeField}
                        />
                      )}
                    </Flex>
                  </Flex>
                </Flex>
              </FormGroup>
            )}
          {(mapFields.get('re4242') ||
            mapFields.get('re4249') ||
            mapFields.get('re4226') ||
            mapFields.get('re4218')) && (
              <FormGroup>
                <Flex className="group-field" column gap={8}>
                  <Label name="uberweisers" className="label-group">
                    {t('uberweisers.title')}
                  </Label>
                  <Flex auto justify="space-between">
                    <Flex
                      className={getCssClass({
                        'half-row':
                          !!mapFields.get('re4242') && !!mapFields.get('re4249'),
                      })}
                      w={
                        !mapFields.get('re4242') || !mapFields.get('re4249')
                          ? '100%'
                          : ''
                      }
                    >
                      {isLabSubGroup ? (
                        <FormGroup2
                          label={tReferralConsulting('uberweisers.re4242')}
                          name="re4242"
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                        >
                          <Field name="re4242">
                            {({ field, form }) => {
                              return (
                                <LanrBsnrSuggestion
                                  form={form}
                                  field={field}
                                  fieldDepend={'re4218'}
                                  onChangeField={onChangeField}
                                  hasError={!!errors['re4242']}
                                  handleGetList={handleGetList(
                                    're4242',
                                    false,
                                    t
                                  )}
                                />
                              );
                            }}
                          </Field>
                        </FormGroup2>
                      ) : (
                        <InputValue
                          namespace={namespace}
                          idField="re4242"
                          nestedTrans={nestedTrans}
                          titleField="uberweisers.re4242"
                          placeholder="xxxxxxxxx"
                          validationType={validationList.re4242}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          maxLength={9}
                          minLength={9}
                          onChange={onChangeField}
                        />
                      )}
                    </Flex>
                    {mapFields.get('re4249') && (
                      <Flex
                        className={getCssClass({
                          'half-row':
                            !!mapFields.get('re4242') &&
                            !!mapFields.get('re4249'),
                        })}
                        w={
                          !mapFields.get('re4242') || !mapFields.get('re4249')
                            ? '100%'
                            : ''
                        }
                      >
                        <InputValue
                          namespace={namespace}
                          idField="re4249"
                          nestedTrans={nestedTrans}
                          titleField="uberweisers.re4249"
                          placeholder="xxxxxxxxx"
                          validationType={validationList.re4249}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          maxLength={9}
                          minLength={9}
                          disable={!!disabledFields['re4249']}
                          onChange={onChangeField}
                        />
                      </Flex>
                    )}
                  </Flex>
                  <Flex auto justify="space-between">
                    {mapFields.get('re4226') && (
                      <Flex
                        className={getCssClass({
                          'half-row':
                            !!mapFields.get('re4226') &&
                            !!mapFields.get('re4218'),
                        })}
                        w={
                          !mapFields.get('re4226') || !mapFields.get('re4218')
                            ? '100%'
                            : ''
                        }
                      >
                        <InputValue
                          namespace={namespace}
                          idField="re4226"
                          nestedTrans={nestedTrans}
                          titleField="uberweisers.re4226"
                          placeholder="xxxxxxxxx"
                          validationType={validationList.re4226}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          maxLength={9}
                          minLength={9}
                          onChange={onChangeField}
                          formGroupProps={{
                            helperText: undefined,
                          }}
                        />
                      </Flex>
                    )}
                    <Flex
                      className={getCssClass({
                        'half-row':
                          !!mapFields.get('re4226') && !!mapFields.get('re4218'),
                      })}
                      w={
                        !mapFields.get('re4226') || !mapFields.get('re4218')
                          ? '100%'
                          : ''
                      }
                    >
                      {isLabSubGroup ? (
                        <FormGroup2
                          label={tReferralConsulting('uberweisers.re4218')}
                          name="re4218"
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                        >
                          <Field name="re4218">
                            {({ field, form }) => {
                              return (
                                <LanrBsnrSuggestion
                                  form={form}
                                  field={field}
                                  fieldDepend={'re4242'}
                                  isBsnr
                                  onChangeField={onChangeField}
                                  hasError={!!errors['re4218']}
                                  handleGetList={handleGetList('re4218', true, t)}
                                />
                              );
                            }}
                          </Field>
                        </FormGroup2>
                      ) : (
                        <InputValue
                          namespace={namespace}
                          idField="re4218"
                          nestedTrans={nestedTrans}
                          titleField="uberweisers.re4218"
                          placeholder="xxxxxxxxx"
                          validationType={validationList.re4218}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          maxLength={9}
                          minLength={9}
                          onChange={onChangeField}
                        />
                      )}
                    </Flex>
                  </Flex>
                </Flex>
              </FormGroup>
            )}
          {mapFields.get('re4219') && (
            <Flex className="row">
              <InputValue
                namespace={namespace}
                idField="re4219"
                nestedTrans={nestedTrans}
                titleField="re4219"
                validationType={validationList.re4219}
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                maxLength={60}
                onChange={onChangeField}
              />
            </Flex>
          )}
          {mapFields.get('re4214') && (
            <Flex className="half-row">
              <InputDateTimeValue
                nestedTrans={nestedTrans}
                namespace={namespace}
                idField="re4214"
                titleField="re4214"
                setFieldTouched={setFieldTouched}
                validationType={validationList.re4214}
                submitCount={submitCount}
                tooltipContent={
                  <Flex column>
                    <BodyTextM margin="0 0 16px">
                      {tRe4214Tooltip('title')}
                    </BodyTextM>
                    <BodyTextM>{tRe4214Tooltip('description')}</BodyTextM>
                  </Flex>
                }
                tooltipForLongLabel
                touched={touched}
                errors={errors}
                setErrMapMessage={setErrMapMessage}
              />
            </Flex>
          )}
          {mapFields.get('re4220') && (
            <Flex className="row">
              <InputValue
                namespace={namespace}
                idField="re4220"
                nestedTrans={nestedTrans}
                titleField="re4220"
                validationType={validationList.re4220}
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                maxLength={60}
                onChange={onChangeField}
              />
            </Flex>
          )}
          {mapFields.get('re4243') && (
            <Flex className="row">
              <InputValue
                namespace={namespace}
                idField="re4243"
                nestedTrans={nestedTrans}
                titleField="re4243"
                validationType={validationList.re4243}
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                maxLength={60}
                onChange={onChangeField}
              />
            </Flex>
          )}

          {mapFields.get('re4208') && (
            <Flex className="row">
              <InputValue
                namespace={namespace}
                idField="re4208"
                nestedTrans={nestedTrans}
                titleField="re4208"
                validationType={validationList.re4208}
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                // maxLength={60}
                onChange={onChangeField}
              />
            </Flex>
          )}
          {mapFields.get('re4207') && (
            <Flex className="row">
              <InputValue
                namespace={namespace}
                idField="re4207"
                nestedTrans={nestedTrans}
                titleField="re4207"
                validationType={validationList.re4207}
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                // maxLength={60}
                onChange={onChangeField}
              />
            </Flex>
          )}
          {mapFields.get('re4205') && (
            <Flex mb={15}>
              <FormGroup2
                name="re4205"
                label={tReferralConsulting('re4205')}
                isRequired={
                  validationList.re4205 ===
                  FieldValidationType.FieldValidationType_Required
                }
                errors={errors}
                touched={touched}
                submitCount={submitCount}
              >
                <Field name="re4205">
                  {({ field, form }) => {
                    return (
                      <AllerySuggestor
                        {...field}
                        data-tab-id={field.name}
                        is4205
                        exceptList={field.value ? [field.value] : undefined}
                        onValueChange={(_, valueAsString: string) => {
                          form.setFieldValue(field.name, valueAsString);
                        }}
                      />
                    );
                  }}
                </Field>
              </FormGroup2>
            </Flex>
          )}
          {mapFields.get('re4209') && (
            <Flex className="row">
              <InputValue
                namespace={namespace}
                idField="re4209"
                nestedTrans={nestedTrans}
                titleField="re4209"
                validationType={validationList.re4209}
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                // maxLength={60}
                onChange={onChangeField}
              />
            </Flex>
          )}
          {mapFields.get('re4229') && (
            <Flex className="row">
              <InputValue
                namespace={namespace}
                idField="re4229"
                nestedTrans={nestedTrans}
                titleField="re4229"
                validationType={validationList.re4229}
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                maxLength={5}
                isNumberFormat
                onChange={onChangeField}
              />
            </Flex>
          )}
          {mapFields.get('re4233') && (
            <Flex className="sl-treatment-duration" column>
              <Divider />
              <BodyTextM fontWeight={600}>
                {tReferralConsulting('re4233')}
              </BodyTextM>
              <FieldArray
                name="re4233"
                render={(arrayHelpers) => (
                  <>
                    <Flex column w="100%">
                      {(values.re4233 || []).map((_, index) => (
                        <Flex key={index} gap={12} align="center">
                          <InputTimeRangeValue
                            namespace={namespace}
                            idField={`re4233.${index}`}
                            nestedTrans={nestedTrans}
                            titleField="fromTo"
                            validationType={
                              !index ? validationList.re4233 : undefined!
                            }
                            submitCount={submitCount}
                            touched={touched}
                            errors={errors}
                            onChange={onChangeField}
                            setFieldTouched={setFieldTouched}
                            dateFromKeyName={`re4233.${index}.from`}
                            dateToKeyName={`re4233.${index}.to`}
                          />
                          <Box style={{ width: 16 }}>
                            {!!index && (
                              <Svg
                                className="sl-icon-remove"
                                src={minusCircle}
                                alt="minus-circle-icon"
                                onClick={() => {
                                  arrayHelpers.remove(index);
                                }}
                                width={16}
                                height={16}
                              />
                            )}
                          </Box>
                        </Flex>
                      ))}
                    </Flex>
                    <Flex w="fit-content">
                      <Button
                        outlined
                        intent="primary"
                        minimal
                        onClick={() => {
                          arrayHelpers.push({});
                        }}
                        icon={<Svg src={plusIcon} />}
                      >
                        <span style={{ fontWeight: 600 }}>
                          {tReferralConsulting('addTreatmentDuration')}
                        </span>
                      </Button>
                    </Flex>
                  </>
                )}
              />
            </Flex>
          )}
        </Flex>
      </Flex>
    </LanrBsnrSuggestionProvider>
  );
}

export default memo(
  I18n.withTranslation(ReferralForm, {
    namespace: namespace,
    nestedTrans: nestedTrans,
  })
);
