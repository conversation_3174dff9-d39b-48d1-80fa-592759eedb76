import React, { memo, useState, useContext, useRef } from 'react';
import isEmpty from 'lodash/isEmpty';

import {
  Flex,
  LeaveConfirmModal,
  LoadingState,
} from '@tutum/design-system/components';
import { useLocationHash } from '@tutum/mvz/hooks/useLocationHash';
import { IMvzTheme } from '@tutum/mvz/theme';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { ScheinInfo } from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';
import { AltStyle } from './Alternative.styled';
import { Modal, ModalSize } from '@tutum/design-system/components/Modal';
import FormContent from '@tutum/mvz/module_kv_hzv_schein/FormContent.styled';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { patientFileActions } from '../module_patient-management/patient-file/PatientFile.store';
import { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import { FORM_SECTION } from './FormContent.helper';
import { FormikProps } from 'formik';

export interface ICreateScheinDialogProps {
  className?: string;
  theme?: IMvzTheme;
  isOpen: boolean;
  id?: string;
  onClose: () => void;
  patientId: string;
  isBillingValidation?: boolean;
  isCreateReadCard: boolean;
  setShowDMPEnrollDialog: (isShowDMPEnrollDialog: boolean) => void;
  reloadQuarters?: ReloadQuarterFunc;
  scrollToSection?: FORM_SECTION;
}

export const RESTRICT_VALUE = ['888888800'];

export const SCROLL_SHIFTING = 20;

export const checkRequired4299FieldBy4251Field = (values: ScheinInfo) => {
  return values['ps4250'] && [3, 4].includes(Number(values['ps4251']));
};

function CreateScheinDialog(
  props: ICreateScheinDialogProps & II18nFixedNamespace<any>
): React.FunctionComponentElement<ICreateScheinDialogProps> {
  const [confirmOpen, setConfirmOpen] = useState<boolean>(false);

  const {
    isOpen,
    onClose,
    className,
    t,
    id,
    patientId,
    isCreateReadCard,
    setShowDMPEnrollDialog,
    reloadQuarters,
    scrollToSection,
  } = props;
  const globalContext = useContext(GlobalContext.instance);
  const { patientManagement } = useContext(PatientManagementContext.instance);
  const { setHash } = useLocationHash();
  const ref = useRef<{ formik: FormikProps<ScheinInfo> | null }>(null);

  const onClosePopoverConfirm = () => {
    setConfirmOpen(false);
  };

  const handleConfirmClose = () => {
    // TODO: we should use formik.dirty, but since FormContent component set formvalue directly, dirty is always = true
    setHash('');
    if (!isEmpty(ref.current?.formik?.touched)) {
      setConfirmOpen(true);
    } else {
      onClose();
    }
  };

  return (
    <Flex className={props.className}>
      <AltStyle />
      <Modal
        className={getCssClass(
          'bp5-dialog-fullscreen',
          'bp5-dialog-content-scrollable',
          className
        )}
        size={ModalSize.FULLSCREEN}
        title={t(!id ? 'title' : 'updateTitle')}
        isOpen={isOpen}
        canOutsideClickClose={false}
        isDisableModalBodyScroll
        onClose={handleConfirmClose}
        autoFocus
        enforceFocus={false}
        tabIndex={-1}
        usePortal={!props.isBillingValidation}
      >
        {patientManagement && patientManagement.patient ? (
          <FormContent
            t={t}
            ref={ref}
            patientId={patientId}
            isOpen={isOpen}
            id={id}
            onClose={onClose}
            onCancel={handleConfirmClose}
            patientManagement={patientManagement}
            globalContext={globalContext}
            isCreateReadCard={isCreateReadCard}
            setShowDMPEnrollDialog={setShowDMPEnrollDialog}
            reloadQuarters={reloadQuarters}
            scrollToSection={scrollToSection}
          />
        ) : (
          <LoadingState />
        )}
      </Modal>
      <LeaveConfirmModal
        isOpen={confirmOpen}
        onConfirm={() => {
          onClose();
          onClosePopoverConfirm();
          patientFileActions.setTerminalId(undefined);
        }}
        onClose={onClosePopoverConfirm}
      />
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(CreateScheinDialog, {
    namespace: 'Schein',
    nestedTrans: 'createSchein',
  })
);
