import { Field, FieldProps, FormikProps } from 'formik';
import { debounce, get } from 'lodash';
import moment from 'moment';
import React, {
  MutableRefObject,
  createRef,
  memo,
  useCallback,
  useEffect,
  useState,
} from 'react';
import ReactDOM from 'react-dom';

import {
  Box,
  Flex,
  FormGroup2,
  H3,
  IMenuItem,
  MessageBar,
  Svg,
  Tag,
} from '@tutum/design-system/components';
import { Button } from '@tutum/design-system/components/Button';
import {
  Checkbox,
  Collapse,
  FormGroup,
  H5,
  Icon,
  Intent,
  Label,
} from '@tutum/design-system/components/Core';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  getCssClass,
  getUUID,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { getPsychotherapyById } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { useQueryGetPsychotherapyTakeOver } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import {
  FieldValidationType,
  GroupServicesCode,
  GroupServicesCodeBefore2017,
  Psychotherapy,
} from '@tutum/hermes/bff/schein_common';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import {
  DATE_FORMAT,
  DATE_TIME_TRANSFER_UTC,
} from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import FormUtils from '@tutum/infrastructure/utils/form.util';
import { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import {
  InputDateTimeValue,
  InputNumberValue,
  ReactSelectValue,
  ReactSelectValueOption,
} from '@tutum/mvz/module_kv_hzv_schein/ScheinComponents';
import { IMvzTheme } from '@tutum/mvz/theme';
import type { FilterOptionOption } from 'react-select/dist/declarations/src/filters';
import ServiceBlockService from '../../module_patient-management/patient-file/encounter-v2/composer/service-block/services/service-block.service';
import { IPatientProfile } from '../../module_patient-management/types/profile.type';
import { IEmployeeProfile } from '../../types/profile.type';
import { checkRequired4299FieldBy4251Field } from '../CreateSchein';
import {
  INITIAL_GROUP_SERVICECODE,
  INITIAL_GROUP_SERVICECODE_BEFORE_2017,
  INITIAL_PSYCHOTHERAPY,
  IdAndText,
  ScheinInfo,
} from '../CreateSchein.service';
import Wrapper from '../components/Pyschotherapy/Wrapper/styled';
import {
  checkIfObjHasKey,
  checkIfObjectHasValue,
  scrollToElem,
  debouncedScroll,
} from '../utils';
import {
  Group3550X,
  Group3551X,
  Group3552X,
  Group3553X,
  Group3554X,
  Group3555X,
  ListServiceCodeGroup3550X,
  ListServiceCodeGroup3551X,
  ListServiceCodeGroup3552X,
  ListServiceCodeGroup3553X,
  ListServiceCodeGroup3554X,
  ListServiceCodeGroup3555X,
} from './Psychotherapie.const';
``;

export interface IPsychotherapieFormProps {
  className?: string;
  theme?: IMvzTheme;
  mapFields: Map<string, string>;
  setErrMapMessage: (fieldName: string, value: string) => void;
  catalogsData: { [key: string]: IdAndText[] };
  navRef?: HTMLElement | null;
  validationList: {
    [key: string]: FieldValidationType;
  };
  patientId: string;
  doctors: IEmployeeProfile[];
  values: ScheinInfo;
  onChangeField: (values: ScheinInfo) => void;
  patient?: IPatientProfile;
  scrollToTop?: boolean;
}

const PlusSVG = '/images/plus.svg';
const MinusIcon = '/images/minus-circle.svg';
const AlertCircle = '/images/alert-circle-solid-error.svg';
const namespace = 'Schein';
const nestedTrans = 'psychotherapy';
const PSYCHOTHERAPIE_FIELDS = [
  'ps4236',
  'ps4234',
  'ps4235',
  'ps4247',
  'ps4244',
  'ps4245',
  'ps4246',
  'pausingStartDate',
  'pausingEndDate',
  'isInsuranceInformedTherapy',
  'ps4251',
  'ps4250',
  'ps4299',
  'ps4253',
  'ps4254',
  'ps4256',
  'ps4257',
  'ps4255',
];

function PsychotherapieForm(
  props: IPsychotherapieFormProps &
    II18nFixedNamespace &
    FormikProps<ScheinInfo>
): React.FunctionComponentElement<IPsychotherapieFormProps> {
  const {
    className,
    t,
    mapFields,
    submitCount,
    errors,
    validationList,
    touched,
    values,
    setErrMapMessage,
    catalogsData,
    navRef,
    setFieldValue,
    patientId,
    isValidating,
    doctors,
    scrollToTop = false,
  } = props;
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const [isPausingDate, setIsPausingDate] = useState<{
    [key: string]: boolean;
  }>({});
  if (
    Object.keys(isPausingDate).length == 0 &&
    values.psychotherapy.length > 0
  ) {
    setIsPausingDate(
      values.psychotherapy.reduce(
        (prev, next, index) => {
          const result = { ...prev };
          result[index] = !!next.pausingStartDate && !!next.pausingEndDate;
          return result;
        }, {} as { [key: string]: boolean })
    );
  }
  const [isExpanded, setExpand] = useState<boolean | null>(null);
  if (isExpanded === null && checkIfObjHasKey(PSYCHOTHERAPIE_FIELDS, values)) {
    setExpand(checkIfObjectHasValue(PSYCHOTHERAPIE_FIELDS, values));
  }

  const currentForm: any = null;
  const currentField: any = null;
  const [psychotherapieRef, setPsychotherapieRef] =
    useState<Nullable<Element>>(null);
  const [serviceCodes, setServiceCodes] = useState<IContractData[]>([]);
  const [isResetPsychotherapy, setIsResetPsychotherapy] =
    useState<boolean>(false);
  const [selectedRemoveIndex, setSelectedRemoveIndex] = useState<number>(0);
  const [isOpen, setIsOpen] = useState(true);
  const [isCollapse, setIsCollapse] = useState(true);
  const [mappedIsOpenGroupServiceCode, setMappedIsOpenGroupServiceCode] =
    useState<{ [key: number]: boolean }>(
      values?.psychotherapy?.reduce((curr, _, i) => {
        curr[i] = true;
        return curr;
      }, {})
    );
  const { isSuccess, data } = useQueryGetPsychotherapyTakeOver({
    patientId,
  });
  const takeOver =
    data?.psychotherapyEntry.filter(
      (p) =>
        !!p.encounterPsychotherapy &&
        !values?.psychotherapy?.find((v) => v.id == p.id)
    ) ?? [];
  const searchServiceCodes = useCallback(
    debounce(
      async (query: string, selectedDate?: Date, isBefore2017?: boolean) => {
        const doctorProfile = doctors.find((d) => d.id == values.doctorId);
        if (doctorProfile) {
          let timeSearch = datetimeUtil.now();
          if (selectedDate) {
            const momentDate = moment(selectedDate);
            // TODO:PSYCHOTHERAPY Hard code to check avaialable master data
            if (momentDate.quarter() + momentDate.year() * 4 >= 4 + 2022 * 4) {
              timeSearch = moment
                .utc(momentDate.format(DATE_TIME_TRANSFER_UTC))
                .toDate()
                .getTime();
            }
          }
          const res = await ServiceBlockService.searchService(
            timeSearch,
            query,
            doctorProfile,
            [],
            undefined,
            undefined
          );
          const groupPsychetherapies = [
            Group3550X,
            Group3551X,
            Group3552X,
            Group3553X,
            Group3554X,
            Group3555X,
          ];
          setServiceCodes(
            (isBefore2017 ? [] : groupPsychetherapies)
              .filter(
                (g) =>
                  g?.description
                    ?.toLowerCase()
                    ?.includes(query.toLowerCase()) ||
                  g?.code?.toLowerCase()?.includes(query.toLowerCase())
              )
              .concat(res)
          );
        }
      },
      500
    ),
    [values?.doctorId]
  );

  const showPsychotherapieForm = () => {
    if (!currentForm || !currentField) {
      return;
    }
    currentForm?.setFieldValue(currentField.name, !currentForm.value);
  };

  useEffect(() => {
    setMappedIsOpenGroupServiceCode(
      values?.psychotherapy?.reduce((curr, _, i) => {
        curr[i] =
          typeof mappedIsOpenGroupServiceCode?.[i] != 'undefined'
            ? mappedIsOpenGroupServiceCode[i]
            : true;
        return curr;
      }, {})
    );
  }, [values?.psychotherapy]);

  useEffect(() => {
    if (values.scheinId) {
      showPsychotherapieForm();
    }
  }, [values.scheinId]);

  useEffect(() => {
    setPsychotherapieRef(navRef?.querySelector('.psychotherapie'));
  }, [navRef]);

  const contentRef: MutableRefObject<HTMLHeadingElement | null> = createRef();

  const menuClick = () => {
    if (!contentRef.current) {
      return;
    }
    scrollToElem(contentRef.current);
    return false;
  };

  const removePyschotherapyFields = (
    setFormField: (key: string, value: any) => void
  ) => {
    setFormField('ps4244', []);
    setFormField('isInsuranceInformedTherapy', null);
    setFormField('ps4250', null);
    setFormField('ps4251', null);
    setFormField('ps4253', []);
    setFormField('ps4256', []);
    setFormField('ps4235', null);
    setFormField('ps4299', null);
    setFormField('ps4247', null);
    setFormField('ps4245', null);
    setFormField('ps4246', null);
    setFormField('pausingStartDate', null);
    setFormField('pausingEndDate', null);
    setFormField('ps4254', null);
    setFormField('ps4252', null);
    setFormField('ps4257', null);
    setFormField('ps4255', null);
  };

  useEffect(() => {
    const hasErrorInSection = PSYCHOTHERAPIE_FIELDS.some(
      (field) => !!errors[field]
    );

    if (hasErrorInSection) {
      setExpand(true);
      return;
    }
    if (
      !!values.scheinDetails?.ps4234 ||
      Number(values.scheinDetails?.psychotherapy.length) > 0
    ) {
      setExpand(true);
    }
  }, [errors, isValidating, values?.scheinDetails?.psychotherapy]);

  const onRemoveServiceCodeByIndex = (
    index: number,
    psychoIndex: number,
    field?: string
  ) => {
    const newServiceCodes =
      values?.psychotherapy?.[psychoIndex]?.[field ?? 'groupServicesCode'] ??
      [];
    if (newServiceCodes.length == 0) {
      return;
    }
    if (newServiceCodes.length - 1 < index) {
      return;
    }
    newServiceCodes.splice(index, 1);
    setFieldValue(
      `psychotherapy.${psychoIndex}.${field ?? 'groupServicesCode'}`,
      newServiceCodes
    );
  };

  const onRemovePsychothearpy = (index: number) => {
    const newPsychotherapy = values?.psychotherapy;
    if (newPsychotherapy.length == 0) {
      return;
    }
    if (newPsychotherapy.length - 1 < index) {
      return;
    }
    newPsychotherapy.splice(index, 1);
    setFieldValue(`psychotherapy`, newPsychotherapy);
  };

  const onRemoveCareGiverByIndex = (index: number, psychoIndex: number) => {
    const newServiceCodes =
      values?.psychotherapy?.[psychoIndex]?.groupCareGiver ?? [];
    if (newServiceCodes.length == 0) {
      return;
    }
    if (newServiceCodes.length - 1 < index) {
      return;
    }
    newServiceCodes.splice(index, 1);
    setFieldValue(
      `psychotherapy.${psychoIndex}.groupCareGiver`,
      newServiceCodes
    );
  };

  const onSelectServiceCode =
    (fieldName: string, fieldAppend: string) =>
      (item: IMenuItem<string | number>) => {
        let listvalues: string[] = [];
        switch (item.value) {
          case '3550X':
            listvalues = ListServiceCodeGroup3550X;
            break;
          case '3551X':
            listvalues = ListServiceCodeGroup3551X;
            break;
          case '3552X':
            listvalues = ListServiceCodeGroup3552X;
            break;
          case '3553X':
            listvalues = ListServiceCodeGroup3553X;
            break;
          case '3554X':
            listvalues = ListServiceCodeGroup3554X;
            setServiceCodes([]);
            break;
          case '3555X':
            listvalues = ListServiceCodeGroup3555X;
            break;
          default:
            setFieldValue(fieldName, item.value);
            if (item.value == '') {
              setFieldValue(
                fieldName.replace('.serviceCode', '.amountBilled'),
                '0'
              );
            }
            setServiceCodes([]);
            return;
        }
        if (listvalues.length > 0) {
          const index = Number(fieldName?.split('.')?.[3] ?? -1);
          if (index != -1) {
            const v: GroupServicesCode[] = get(values, fieldAppend) ?? [];
            const refilter = listvalues.filter(
              (l) => !v.some((c) => c.serviceCode == l)
            );
            v.splice(
              index,
              1,
              ...refilter.map((x) => ({
                serviceCode: x,
                amountBilled: 0,
              }))
            );
            setFieldValue(fieldAppend, v);
          }
        }
      };
  const onTakeOver = async (p: TimelineModel) => {
    if (!p.id) return;
    const res = await getPsychotherapyById({
      psychotherapyId: p.id,
    });
    setFieldValue(`psychotherapy`, [
      ...values.psychotherapy,
      {
        ...res.data.data,
        id: getUUID(),
        groupServicesCode: res.data.data.groupServicesCode.map((s) => {
          s.amountBilled =
            p.encounterPsychotherapy?.entries?.[s.serviceCode]?.amountBilled ??
            s.amountBilled;
          return s;
        }),
        takeOverId: p.id,
      } as Psychotherapy,
    ]);
  };

  const removeValueIfApprovalDateBefore2017 = (i: number) => {
    setFieldValue(`psychotherapy.${i}.ps4250`, null);
    setFieldValue(`psychotherapy.${i}.ps4251`, null);
    setFieldValue(`psychotherapy.${i}.ps4299`, '');
    setFieldValue(`psychotherapy.${i}.groupCareGiver`, []);
    setFieldValue(`psychotherapy.${i}.ps4255`, '');
    setFieldValue(`psychotherapy.${i}.groupServicesCode`, []);
    setFieldValue(`psychotherapy.${i}.ps4245`, 0);
  };

  const removeValueIfApprovalDateAfter2017 = (i: number) => {
    setFieldValue(`psychotherapy.${i}.ps4250`, null);
    setFieldValue(`psychotherapy.${i}.ps4251`, null);
    setFieldValue(`psychotherapy.${i}.ps4299`, '');
    setFieldValue(`psychotherapy.${i}.ps4255`, '');
    setFieldValue(`psychotherapy.${i}.groupServicesCodeBefore2017`, []);
    setFieldValue(`psychotherapy.${i}.groupServicesCode`, [
      INITIAL_GROUP_SERVICECODE,
    ]);
    setFieldValue(`psychotherapy.${i}.groupCareGiver`, [
      INITIAL_GROUP_SERVICECODE,
    ]);
    setFieldValue(`psychotherapy.${i}.ps4245`, 0);
  };

  const appendInitialGroupServiceCodeBefore2017 = (i: number) => {
    setFieldValue(`psychotherapy.${i}.groupServicesCodeBefore2017`, [
      INITIAL_GROUP_SERVICECODE_BEFORE_2017,
    ]);
  };

  const filterOption = (
    option: FilterOptionOption<ReactSelectValueOption>,
    inputValue: string
  ): boolean =>
    option.data.label.includes(inputValue) ||
    option.data.description.toLowerCase().includes(inputValue.toLowerCase());

  const onChangePausingDate = (i: number) => {
    setIsPausingDate((prev) => {
      const result = { ...prev };
      result[i] = !result[i];
      if (!result[i]) {
        setFieldValue(`psychotherapy.${i}.pausingStartDate`, null);
        setFieldValue(`psychotherapy.${i}.pausingEndDate`, null);
        setFieldValue(`psychotherapy.${i}.isReason`, false);
      }
      return result;
    });
  };

  useEffect(() => {
    if (!scrollToTop && contentRef.current) {
      debouncedScroll(contentRef.current, 100)()
    }
  }, [scrollToTop, psychotherapieRef, contentRef]);

  return (
    <Flex column className={getCssClass(className, 'form-info')}>
      {psychotherapieRef
        ? ReactDOM.createPortal(
          <H3
            className="label-section psychotherapie-form"
            onClick={menuClick}
          >
            {t('title')}
          </H3>,
          psychotherapieRef
        )
        : null}
      <H3
        className="label-section"
        onClick={() => setExpand(!isExpanded)}
        ref={contentRef}
      >
        {t('title')}
        <Icon
          className="expand-icon"
          icon={isExpanded ? 'chevron-up' : 'chevron-down'}
        />
      </H3>
      <Flex
        column
        className={getCssClass('form-body', isExpanded ? 'expanded' : '')}
      >
        <Flex column>
          {mapFields.get('ps4236') && values.kvTreatmentCase == '0101' && (
            <Flex className="row checkbox-group">
              <Field name="ps4236">
                {({ field, form }) => {
                  return (
                    <Checkbox
                      className="schein-checkbox"
                      checked={field.value}
                      onChange={() => {
                        form.setFieldValue(field.name, !field.value);
                      }}
                      onKeyDown={(e) => {
                        if (e.keyCode === 13) {
                          e.preventDefault();
                          form.setFieldValue(field.name, !field.value);
                        }
                      }}
                    >
                      <span>{t('ps4236')}</span>
                    </Checkbox>
                  );
                }}
              </Field>
            </Flex>
          )}
          {mapFields.get('ps4234') && mapFields.get('ps4235') && (
            <React.Fragment>
              <Flex className="row checkbox-group">
                <Field name="ps4234">
                  {({ field, form }) => {
                    return (
                      <Checkbox
                        className="schein-checkbox"
                        checked={field.value}
                        onChange={() => {
                          if (field.value) {
                            setIsResetPsychotherapy(true);
                          } else {
                            form.setFieldValue(field.name, true);
                            const initialPsychotherapy: Psychotherapy[] = [];
                            if (takeOver.length == 0) {
                              initialPsychotherapy.push({
                                ...INITIAL_PSYCHOTHERAPY,
                                id: getUUID(),
                              });
                            }
                            form.setFieldValue(
                              `psychotherapy`,
                              initialPsychotherapy
                            );
                            setIsOpen(true);
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.keyCode === 13) {
                            e.preventDefault();
                            form.setFieldValue(field.name, !field.value);
                          }
                        }}
                      >
                        {t('ps4234')}
                      </Checkbox>
                    );
                  }}
                </Field>
              </Flex>

              {values.ps4234 && (
                <React.Fragment>
                  {isSuccess &&
                    takeOver.map((p) => {
                      if (!p.encounterPsychotherapy) return;
                      const start = moment(
                        p.encounterPsychotherapy.approvalDate
                      );
                      const takeOverInPsychotherapy =
                        values.psychotherapy.reduce(
                          (prev: string[], next: Psychotherapy) => {
                            if (next.takeOverId) {
                              return [...prev, next.takeOverId];
                            }
                            return prev;
                          },
                          [] as string[]
                        );
                      if (takeOverInPsychotherapy.includes(String(p.id)))
                        return;
                      return (
                        <React.Fragment key={p.id}>
                          <MessageBar
                            hasBullet={false}
                            type="warning"
                            content={t('takeoverTitle')}
                            actionButtonGroup={
                              <Flex>
                                <Button
                                  className="bp5-button-takeover"
                                  small={true}
                                  intent={Intent.PRIMARY}
                                  onClick={() => {
                                    onTakeOver(p);
                                  }}
                                  style={{ width: 'auto', minWidth: 'auto' }}
                                >
                                  {t('takeover')}
                                </Button>
                              </Flex>
                            }
                            subDescription={
                              <React.Fragment>
                                <p>
                                  <span className="bullet">&#8226;&nbsp;</span>
                                  <span>
                                    {t('takeoverStartDate', {
                                      date: start.format(DATE_FORMAT),
                                    })}
                                  </span>
                                </p>
                                <p>
                                  <span className="bullet">&#8226;&nbsp;</span>
                                  <span>
                                    {t('takeoverInfo', {
                                      amountApproval:
                                        p.encounterPsychotherapy.amountApproval,
                                      serviceCodes: Object.keys(
                                        p.encounterPsychotherapy.entries
                                      )
                                        .filter(
                                          (k) =>
                                            !p.encounterPsychotherapy?.referenceServiceCodes?.includes(
                                              k
                                            )
                                        )
                                        .map(
                                          (s) =>
                                            `${s} (${t('billed')}: ${p?.encounterPsychotherapy
                                              ?.entries[s].amountBilled ?? 0
                                            })`
                                        )
                                        .join(', '),
                                    })}
                                  </span>
                                </p>
                                {!!p.encounterPsychotherapy
                                  ?.referenceAmountApproval && (
                                    <p>
                                      <span className="bullet">
                                        &#8226;&nbsp;
                                      </span>
                                      <span>
                                        {t('takeoverReferenceInfo', {
                                          amountApproval:
                                            p.encounterPsychotherapy
                                              .referenceAmountApproval,
                                          serviceCodes: Object.keys(
                                            p.encounterPsychotherapy.entries
                                          )
                                            .filter((k) =>
                                              p.encounterPsychotherapy?.referenceServiceCodes?.includes(
                                                k
                                              )
                                            )
                                            .map(
                                              (s) =>
                                                `${s} (${t('billed')}: ${p?.encounterPsychotherapy
                                                  ?.entries[s].amountBilled ?? 0
                                                })`
                                            )
                                            .join(', '),
                                        })}
                                      </span>
                                    </p>
                                  )}
                              </React.Fragment>
                            }
                          />
                        </React.Fragment>
                      );
                    })}
                  {values.psychotherapy.map((v, i) => {
                    const isBefore2017 = values?.psychotherapy?.[i]?.ps4247
                      ? moment
                        .utc(
                          moment(values.psychotherapy[i].ps4247).format(
                            DATE_TIME_TRANSFER_UTC
                          )
                        )
                        .toDate()
                        .getTime() <
                      moment
                        .utc(
                          moment('2017-04-01').format(DATE_TIME_TRANSFER_UTC)
                        )
                        .toDate()
                        .getTime()
                      : false;
                    return (
                      <React.Fragment key={i}>
                        <H5
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            cursor: 'pointer',
                            marginBottom: 16,
                            marginTop: 16,
                            width: '100%',
                          }}
                          id="collapse-contact-person"
                        >
                          {t('title')} {i + 1}
                          <Flex>
                            <Flex className="sl-Flex-minus-button">
                              {values.psychotherapy.length > 1 && (
                                <Button
                                  intent={Intent.NONE}
                                  onClick={() => {
                                    setSelectedRemoveIndex(i);
                                    setIsResetPsychotherapy(true);
                                  }}
                                >
                                  <Svg src={MinusIcon} />
                                </Button>
                              )}
                            </Flex>
                            <Flex className="sl-Flex-minus-button">
                              <Icon
                                onClick={() => setIsOpen(!isOpen)}
                                className="expand-icon"
                                icon={isOpen ? 'chevron-up' : 'chevron-down'}
                                intent={Intent.NONE}
                                style={{
                                  color: COLOR.TEXT_TERTIARY_SILVER,
                                }}
                              />
                            </Flex>
                          </Flex>
                        </H5>
                        <Collapse
                          isOpen={isOpen}
                          className="sl-Collapse-psychotherapy"
                        >
                          <Flex auto justify="space-between">
                            <Flex className="half-row">
                              <InputDateTimeValue
                                nestedTrans={nestedTrans}
                                namespace={namespace}
                                idField={`psychotherapy.${i}.ps4235`}
                                titleField="ps4235"
                                setFieldTouched={props.setFieldTouched}
                                validationType={
                                  FieldValidationType.FieldValidationType_Required
                                }
                                submitCount={submitCount}
                                touched={touched}
                                errors={errors}
                                setErrMapMessage={setErrMapMessage}
                              />
                            </Flex>
                            <Flex
                              className={getCssClass(
                                'half-row',
                                'input_start-date',
                                {
                                  'wrapper-case_2017': isBefore2017,
                                }
                              )}
                            >
                              <InputDateTimeValue
                                nestedTrans={nestedTrans}
                                namespace={namespace}
                                idField={`psychotherapy.${i}.ps4247`}
                                titleField="ps4247"
                                tooltipContent={t('ps4247Tooltip')}
                                setFieldTouched={props.setFieldTouched}
                                validationType={validationList.ps4247}
                                submitCount={submitCount}
                                touched={touched}
                                errors={errors}
                                setErrMapMessage={setErrMapMessage}
                                onChange={(formValue) => {
                                  const val = get(
                                    formValue,
                                    `psychotherapy.${i}.ps4247`
                                  );
                                  if (val) {
                                    const momentVal = moment(val);
                                    if (momentVal.isValid()) {
                                      if (
                                        momentVal.isBefore(
                                          moment('01.04.2017', DATE_FORMAT)
                                        )
                                      ) {
                                        removeValueIfApprovalDateBefore2017(i);
                                        appendInitialGroupServiceCodeBefore2017(
                                          i
                                        );
                                      } else {
                                        removeValueIfApprovalDateAfter2017(i);
                                      }
                                    }
                                  } else {
                                    removeValueIfApprovalDateAfter2017(i);
                                  }
                                }}
                              />
                              {isBefore2017 && (
                                <Flex gap={8} align="flex-start">
                                  <Svg src={AlertCircle} />
                                  <span className="msg-before_2017 bp5-form-helper-text">
                                    {t('startDateBefore2017')}
                                  </span>
                                </Flex>
                              )}
                            </Flex>
                          </Flex>
                          {isBefore2017 ? (
                            <Box mb={16}>
                              <H5
                                style={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  cursor: 'pointer',
                                  marginBottom: 16,
                                  marginTop: 16,
                                  width: '100%',
                                }}
                                id="collapse-contact-person"
                              >
                                {t('insuredPerson')}
                                <Flex className="sl-Flex-minus-button">
                                  <Icon
                                    onClick={() =>
                                      setIsCollapse((prev) => !prev)
                                    }
                                    className="expand-icon"
                                    icon={
                                      isCollapse ? 'chevron-up' : 'chevron-down'
                                    }
                                    intent={Intent.NONE}
                                    style={{
                                      color: COLOR.TEXT_TERTIARY_SILVER,
                                    }}
                                  />
                                </Flex>
                              </H5>
                              <Collapse
                                isOpen={isCollapse}
                                className="sl-Collapse-psychotherapy"
                              >
                                {v.groupServicesCodeBefore2017.map(
                                  (g, ig) =>
                                    mapFields.get('ps4244') && (
                                      <FormGroup key={ig}>
                                        <Flex
                                          auto
                                          justify="space-between"
                                          className="wrap-before-2017"
                                          gap={16}
                                        >
                                          <Flex className="serviceCode">
                                            <FormGroup2
                                              className={FormUtils.renderFormClass(
                                                submitCount,
                                                !!touched['serviceCode'],
                                                errors['serviceCode']
                                              )}
                                            >
                                              <Field
                                                name={`psychotherapy.${i}.groupServicesCodeBefore2017.${ig}.serviceCode`}
                                              >
                                                {({ field }) => {
                                                  const serviceCodeSelected =
                                                    v.groupServicesCodeBefore2017.map(
                                                      (g) => g.serviceCode
                                                    );
                                                  const searchFilterd =
                                                    serviceCodes.filter(
                                                      (s) =>
                                                        !serviceCodeSelected.includes(
                                                          s.code!
                                                        )
                                                    );
                                                  return (
                                                    <ReactSelectValue
                                                      idField={field.name}
                                                      label={t('serviceCode')}
                                                      listValues={searchFilterd.map(
                                                        (s) => ({
                                                          key: String(s.code),
                                                          text: String(s.code),
                                                          description:
                                                            s.description,
                                                          sortName:
                                                            s.isGroup ? (
                                                              <Tag
                                                                slState="info"
                                                                slStyle="outline"
                                                              >
                                                                {t(
                                                                  'groupTherapy'
                                                                )}
                                                              </Tag>
                                                            ) : undefined,
                                                        })
                                                      )}
                                                      namespace={namespace}
                                                      validationType={
                                                        FieldValidationType.FieldValidationType_Optional
                                                      }
                                                      selectedValue={
                                                        field.value
                                                      }
                                                      nestedTrans={nestedTrans}
                                                      submitCount={submitCount}
                                                      forceShowTypeOfValidation
                                                      touched={touched}
                                                      errors={errors}
                                                      setFieldTouched={
                                                        props.setFieldTouched
                                                      }
                                                      hideIdListItem={true}
                                                      onQueryChange={async (
                                                        query
                                                      ) => {
                                                        if (query) {
                                                          const selectedDate =
                                                            moment(v.ps4235);
                                                          await searchServiceCodes(
                                                            query,
                                                            selectedDate.isValid()
                                                              ? selectedDate.toDate()
                                                              : undefined,
                                                            true
                                                          );
                                                        }
                                                      }}
                                                      className="psychotherapy_select_servicecode"
                                                      customStyle={{
                                                        menu: (base) => ({
                                                          ...base,
                                                          width: 470,
                                                        }),
                                                      }}
                                                      filterOption={
                                                        filterOption
                                                      }
                                                    />
                                                  );
                                                }}
                                              </Field>
                                            </FormGroup2>
                                          </Flex>
                                          <Flex className="amountApproval">
                                            <InputNumberValue
                                              nestedTrans={nestedTrans}
                                              namespace={namespace}
                                              idField={`psychotherapy.${i}.groupServicesCodeBefore2017.${ig}.amountApproval`}
                                              titleField="amountApproval"
                                              validationType={
                                                g.serviceCode
                                                  ? FieldValidationType.FieldValidationType_Required
                                                  : FieldValidationType.FieldValidationType_Optional
                                              }
                                              submitCount={submitCount}
                                              touched={touched}
                                              errors={errors}
                                              maxLength={3}
                                              disable={
                                                !values?.psychotherapy?.[i]
                                                  ?.groupServicesCodeBefore2017?.[
                                                  ig
                                                ]?.serviceCode
                                              }
                                            />
                                          </Flex>
                                          <Flex className="amountBilled">
                                            <InputNumberValue
                                              nestedTrans={nestedTrans}
                                              namespace={namespace}
                                              idField={`psychotherapy.${i}.groupServicesCodeBefore2017.${ig}.amountBilled`}
                                              titleField="amountBilled"
                                              submitCount={submitCount}
                                              touched={touched}
                                              errors={errors}
                                              validationType={
                                                g.serviceCode
                                                  ? FieldValidationType.FieldValidationType_Required
                                                  : FieldValidationType.FieldValidationType_Optional
                                              }
                                              maxLength={3}
                                              disable={
                                                !values?.psychotherapy?.[i]
                                                  ?.groupServicesCodeBefore2017?.[
                                                  ig
                                                ]?.serviceCode
                                              }
                                            />
                                          </Flex>
                                          <Flex className="sl-Flex-minus-button">
                                            {v?.groupServicesCodeBefore2017
                                              ?.length > 0 &&
                                              ig != 0 && (
                                                <Button
                                                  intent={Intent.NONE}
                                                  onClick={() =>
                                                    onRemoveServiceCodeByIndex(
                                                      ig,
                                                      i,
                                                      'groupServicesCodeBefore2017'
                                                    )
                                                  }
                                                >
                                                  <Svg src={MinusIcon} />
                                                </Button>
                                              )}
                                          </Flex>
                                        </Flex>
                                      </FormGroup>
                                    )
                                )}
                                <Flex className="sl-Flex-button_add_service_code">
                                  <Button
                                    intent={Intent.PRIMARY}
                                    className="btn-cancel"
                                    onClick={() => {
                                      setFieldValue(
                                        `psychotherapy.${i}.groupServicesCodeBefore2017`,
                                        [
                                          ...(values.psychotherapy?.[i]
                                            ?.groupServicesCodeBefore2017 ??
                                            []),
                                          {
                                            amountBilled: 0,
                                            serviceCode: '',
                                            amountApproval: 0,
                                          } as GroupServicesCodeBefore2017,
                                        ]
                                      );
                                    }}
                                  >
                                    <Flex>
                                      <Svg src={PlusSVG} />
                                      {t('addServiceCode')}
                                    </Flex>
                                  </Button>
                                </Flex>
                              </Collapse>
                              <Box mt={12}>
                                <Flex className="row checkbox-group">
                                  <Field
                                    name={`psychotherapy.${i}.isInsuranceInformedTherapy`}
                                  >
                                    {({ field, form }) => {
                                      return (
                                        <Checkbox
                                          className="schein-checkbox"
                                          checked={field.value}
                                          onChange={() => {
                                            form.setFieldValue(
                                              field.name,
                                              !field.value
                                            );
                                          }}
                                          onKeyDown={(e) => {
                                            if (e.keyCode === 13) {
                                              e.preventDefault();
                                              form.setFieldValue(
                                                field.name,
                                                !field.value
                                              );
                                            }
                                          }}
                                        >
                                          <span>
                                            {t('isInsuranceInformedTherapy')}
                                          </span>
                                        </Checkbox>
                                      );
                                    }}
                                  </Field>
                                </Flex>
                                <Flex className="row checkbox-group">
                                  <Checkbox
                                    className="schein-checkbox"
                                    checked={isPausingDate[i]}
                                    onChange={() => {
                                      setIsPausingDate((prev) => {
                                        const result = { ...prev };
                                        result[i] = !result[i];
                                        return result;
                                      });
                                    }}
                                    onKeyDown={(e) => {
                                      if (e.keyCode === 13) {
                                        e.preventDefault();
                                        setIsPausingDate((prev) => {
                                          const result = { ...prev };
                                          result[i] = !result[i];
                                          return result;
                                        });
                                      }
                                    }}
                                  >
                                    {t('isPausingDate')}
                                  </Checkbox>
                                </Flex>
                                <Wrapper>
                                  <Flex auto gap={8} justify="space-between">
                                    <InputDateTimeValue
                                      nestedTrans={nestedTrans}
                                      namespace={namespace}
                                      idField={`psychotherapy.${i}.pausingStartDate`}
                                      titleField="pausingStartDate"
                                      setFieldTouched={props.setFieldTouched}
                                      validationType={validationList.ps4235}
                                      submitCount={submitCount}
                                      touched={touched}
                                      errors={errors}
                                      setErrMapMessage={setErrMapMessage}
                                      className="ps4235Style"
                                      disabled={!isPausingDate[i]}
                                      maxDate={
                                        values['psychotherapy'][i]?.pausingEndDate
                                          ? new Date(
                                            values['psychotherapy'][
                                              i
                                            ].pausingEndDate!
                                          )
                                          : undefined
                                      }
                                    />
                                    <InputDateTimeValue
                                      nestedTrans={nestedTrans}
                                      namespace={namespace}
                                      idField={`psychotherapy.${i}.pausingEndDate`}
                                      titleField="pausingEndDate"
                                      setFieldTouched={props.setFieldTouched}
                                      validationType={validationList.ps4235}
                                      submitCount={submitCount}
                                      touched={touched}
                                      errors={errors}
                                      setErrMapMessage={setErrMapMessage}
                                      className="ps4235Style"
                                      minDate={
                                        values?.psychotherapy?.[i]
                                          ?.pausingStartDate
                                          ? new Date(
                                            Number(
                                              values?.psychotherapy?.[i]
                                                ?.pausingStartDate
                                            )
                                          )
                                          : undefined
                                      }
                                      disabled={!isPausingDate[i]}
                                    />
                                  </Flex>
                                </Wrapper>
                                <Wrapper>
                                  <Flex className="row checkbox-group">
                                    <Field name={`psychotherapy.${i}.isReason`}>
                                      {({ field, form }) => {
                                        return (
                                          <Checkbox
                                            className="schein-checkbox"
                                            checked={field.value}
                                            onChange={() => {
                                              form.setFieldValue(
                                                field.name,
                                                !field.value
                                              );
                                            }}
                                            disabled={
                                              Number(values?.ps4244?.length) >
                                              0 || !isPausingDate
                                            }
                                            onKeyDown={(e) => {
                                              if (e.keyCode === 13) {
                                                e.preventDefault();
                                                form.setFieldValue(
                                                  field.name,
                                                  !field.value
                                                );
                                              }
                                            }}
                                          >
                                            {t('isReason')}
                                          </Checkbox>
                                        );
                                      }}
                                    </Field>
                                  </Flex>
                                </Wrapper>
                              </Box>
                            </Box>
                          ) : (
                            <React.Fragment>
                              <Flex className="collapse-wrapper">
                                <Flex
                                  justify="space-between"
                                  onClick={() => {
                                    setMappedIsOpenGroupServiceCode((prev) => ({
                                      ...prev,
                                      [i]: !prev[i],
                                    }));
                                  }}
                                >
                                  <Label
                                    name="versicherten"
                                    className="label-group label-group__sub-group--title"
                                  >
                                    {t('versicherten.title')}
                                  </Label>
                                  <Icon
                                    className="expand-icon"
                                    icon={
                                      mappedIsOpenGroupServiceCode?.[i]
                                        ? 'chevron-up'
                                        : 'chevron-down'
                                    }
                                    intent={Intent.NONE}
                                    style={{
                                      color: COLOR.TEXT_TERTIARY_SILVER,
                                    }}
                                  />
                                </Flex>
                                <Collapse
                                  isOpen={mappedIsOpenGroupServiceCode?.[i]}
                                >
                                  {mapFields.get('ps4244') && (
                                    <FormGroup>
                                      <InputNumberValue
                                        nestedTrans={nestedTrans}
                                        namespace={namespace}
                                        idField={`psychotherapy.${i}.ps4245`}
                                        titleField="ps4245"
                                        validationType={
                                          values?.psychotherapy?.[
                                            i
                                          ]?.groupServicesCode.find(
                                            (g) => !!g.serviceCode
                                          )
                                            ? FieldValidationType.FieldValidationType_Required
                                            : FieldValidationType.FieldValidationType_Optional
                                        }
                                        submitCount={submitCount}
                                        touched={touched}
                                        errors={errors}
                                        maxLength={3}
                                        disable={
                                          !values?.psychotherapy?.[i]
                                            ?.groupServicesCode?.length
                                        }
                                      />
                                      {values?.psychotherapy?.[
                                        i
                                      ]?.groupServicesCode.map((g, ig) => {
                                        const isFilledServiceCode =
                                          g.serviceCode;
                                        const filteredServiceCode =
                                          values?.psychotherapy?.[
                                            i
                                          ]?.groupServicesCode?.map(
                                            (g) => g.serviceCode
                                          ) ?? [];
                                        const searchedServiceCodes =
                                          serviceCodes.filter(
                                            (s) =>
                                              !filteredServiceCode.includes(
                                                s.code!
                                              )
                                          );
                                        return (
                                          <Flex
                                            auto
                                            justify="space-between"
                                            key={ig}
                                            gap={16}
                                          >
                                            <Flex className="half-row-service_input">
                                              <FormGroup2
                                                className={FormUtils.renderFormClass(
                                                  submitCount,
                                                  !!touched['serviceCode'],
                                                  errors['serviceCode']
                                                )}
                                              >
                                                <Field
                                                  name={`psychotherapy.${i}.groupServicesCode.${ig}.serviceCode`}
                                                >
                                                  {({ field }) => {
                                                    return (
                                                      <ReactSelectValue
                                                        idField={field.name}
                                                        label={t('ps4244')}
                                                        listValues={searchedServiceCodes.map(
                                                          (s) => ({
                                                            key: String(s.code),
                                                            text: String(
                                                              s.code
                                                            ),
                                                            description:
                                                              s.description,
                                                            sortName:
                                                              s.isGroup ? (
                                                                <Tag
                                                                  slState="info"
                                                                  slStyle="outline"
                                                                >
                                                                  {t(
                                                                    'groupTherapy'
                                                                  )}
                                                                </Tag>
                                                              ) : undefined,
                                                          })
                                                        )}
                                                        namespace={namespace}
                                                        validationType={
                                                          FieldValidationType.FieldValidationType_Optional
                                                        }
                                                        selectedValue={
                                                          field.value
                                                        }
                                                        nestedTrans={
                                                          nestedTrans
                                                        }
                                                        submitCount={
                                                          submitCount
                                                        }
                                                        forceShowTypeOfValidation
                                                        touched={touched}
                                                        errors={errors}
                                                        setFieldTouched={
                                                          props.setFieldTouched
                                                        }
                                                        hideIdListItem={true}
                                                        onQueryChange={async (
                                                          query
                                                        ) => {
                                                          if (query) {
                                                            const selectedDate =
                                                              moment(v.ps4235);
                                                            await searchServiceCodes(
                                                              query,
                                                              selectedDate.isValid()
                                                                ? selectedDate.toDate()
                                                                : undefined
                                                            );
                                                          }
                                                        }}
                                                        className="psychotherapy_select_servicecode"
                                                        onSelected={onSelectServiceCode(
                                                          field.name,
                                                          `psychotherapy.${i}.groupServicesCode`
                                                        )}
                                                        customStyle={{
                                                          menu: (base) => ({
                                                            ...base,
                                                            width: 470,
                                                          }),
                                                        }}
                                                        filterOption={
                                                          filterOption
                                                        }
                                                      />
                                                    );
                                                  }}
                                                </Field>
                                              </FormGroup2>
                                            </Flex>
                                            <Flex className="half-row-service_input">
                                              <InputNumberValue
                                                nestedTrans={nestedTrans}
                                                namespace={namespace}
                                                idField={`psychotherapy.${i}.groupServicesCode.${ig}.amountBilled`}
                                                titleField="ps4246"
                                                submitCount={submitCount}
                                                validationType={
                                                  isFilledServiceCode
                                                    ? FieldValidationType.FieldValidationType_Required
                                                    : FieldValidationType.FieldValidationType_Optional
                                                }
                                                touched={touched}
                                                errors={errors}
                                                maxLength={3}
                                                disable={
                                                  !values?.psychotherapy?.[i]
                                                    ?.groupServicesCode?.[ig]
                                                    ?.serviceCode
                                                }
                                              />
                                            </Flex>
                                            <Flex className="sl-Flex-minus-button">
                                              {ig !== 0 && (
                                                <Button
                                                  intent={Intent.NONE}
                                                  onClick={() =>
                                                    onRemoveServiceCodeByIndex(
                                                      ig,
                                                      i
                                                    )
                                                  }
                                                >
                                                  <Svg src={MinusIcon} />
                                                </Button>
                                              )}
                                            </Flex>
                                          </Flex>
                                        );
                                      })}
                                      <Flex className="sl-Flex-button_add_service_code">
                                        <Button
                                          intent={Intent.PRIMARY}
                                          className="btn-cancel"
                                          onClick={() => {
                                            setFieldValue(
                                              `psychotherapy.${i}.groupServicesCode`,
                                              [
                                                ...(values.psychotherapy?.[i]
                                                  ?.groupServicesCode ?? []),
                                                {
                                                  amountBilled: 0,
                                                  serviceCode: '',
                                                } as GroupServicesCode,
                                              ]
                                            );
                                          }}
                                        >
                                          <Flex>
                                            <Svg src={PlusSVG} />
                                            {t('addServiceCode')}
                                          </Flex>
                                        </Button>
                                      </Flex>
                                    </FormGroup>
                                  )}
                                </Collapse>
                              </Flex>
                              {!!(
                                mapFields.get('ps4256') ||
                                mapFields.get('ps4257') ||
                                mapFields.get('ps4255')
                              ) && (
                                  <Flex className="collapse-wrapper">
                                    <Flex
                                      justify="space-between"
                                      onClick={() => {
                                        setMappedIsOpenGroupServiceCode(
                                          (prev) => ({
                                            ...prev,
                                            [i]: !prev[i],
                                          })
                                        );
                                      }}
                                    >
                                      <Label
                                        name="bezugsperson"
                                        className="label-group label-group__sub-group--title"
                                      >
                                        {t('bezugsperson.title')}
                                      </Label>
                                      <Icon
                                        className="expand-icon"
                                        icon={
                                          mappedIsOpenGroupServiceCode?.[i]
                                            ? 'chevron-up'
                                            : 'chevron-down'
                                        }
                                        intent={Intent.NONE}
                                        style={{
                                          color: COLOR.TEXT_TERTIARY_SILVER,
                                        }}
                                      />
                                    </Flex>
                                    <Collapse
                                      isOpen={mappedIsOpenGroupServiceCode?.[i]}
                                    >
                                      <FormGroup>
                                        <Flex className="group-field" column>
                                          <Flex className="row">
                                            <InputNumberValue
                                              nestedTrans={nestedTrans}
                                              namespace={namespace}
                                              idField={`psychotherapy.${i}.ps4255`}
                                              titleField="bezugsperson.ps4255"
                                              validationType={
                                                values?.psychotherapy?.[
                                                  i
                                                ]?.groupCareGiver.find(
                                                  (g) => !!g.serviceCode
                                                )
                                                  ? FieldValidationType.FieldValidationType_Required
                                                  : FieldValidationType.FieldValidationType_Optional
                                              }
                                              submitCount={submitCount}
                                              touched={touched}
                                              errors={errors}
                                              maxLength={3}
                                              disable={
                                                Number(values?.ps4244?.length) >
                                                0 || isBefore2017
                                              }
                                            />
                                          </Flex>
                                          {values?.psychotherapy?.[
                                            i
                                          ]?.groupCareGiver?.map((g, ig) => {
                                            const isFilledServiceCode =
                                              g.serviceCode;
                                            return (
                                              <Flex
                                                auto
                                                justify="space-between"
                                                key={ig}
                                                gap={16}
                                              >
                                                <Flex className="half-row-service_input">
                                                  <FormGroup2
                                                    className={FormUtils.renderFormClass(
                                                      submitCount,
                                                      !!touched['serviceCode'],
                                                      errors['serviceCode']
                                                    )}
                                                  >
                                                    <Field
                                                      name={`psychotherapy.${i}.groupCareGiver.${ig}.serviceCode`}
                                                    >
                                                      {({ field }) => {
                                                        const filteredServiceCode =
                                                          values?.psychotherapy?.[
                                                            i
                                                          ]?.groupCareGiver?.map(
                                                            (s) => s.serviceCode
                                                          );
                                                        const searchedServiceCodes =
                                                          serviceCodes.filter(
                                                            (s) =>
                                                              !filteredServiceCode.includes(
                                                                s.code!
                                                              )
                                                          );
                                                        return (
                                                          <ReactSelectValue
                                                            idField={field.name}
                                                            label={t(
                                                              'bezugsperson.ps4256'
                                                            )}
                                                            listValues={searchedServiceCodes.map(
                                                              (s) => ({
                                                                key: String(
                                                                  s.code
                                                                ),
                                                                text: String(
                                                                  s.code
                                                                ),
                                                                description:
                                                                  s.description,
                                                                sortName:
                                                                  s.isGroup ? (
                                                                    <Tag
                                                                      slState="info"
                                                                      slStyle="outline"
                                                                    >
                                                                      {t(
                                                                        'groupTherapy'
                                                                      )}
                                                                    </Tag>
                                                                  ) : undefined,
                                                              })
                                                            )}
                                                            namespace={namespace}
                                                            validationType={
                                                              isFilledServiceCode
                                                                ? FieldValidationType.FieldValidationType_Required
                                                                : FieldValidationType.FieldValidationType_Optional
                                                            }
                                                            selectedValue={
                                                              field.value
                                                            }
                                                            nestedTrans={
                                                              nestedTrans
                                                            }
                                                            submitCount={
                                                              submitCount
                                                            }
                                                            forceShowTypeOfValidation
                                                            touched={touched}
                                                            errors={errors}
                                                            setFieldTouched={
                                                              props.setFieldTouched
                                                            }
                                                            hideIdListItem={true}
                                                            disabled={
                                                              isBefore2017
                                                            }
                                                            onQueryChange={async (
                                                              query
                                                            ) => {
                                                              if (query) {
                                                                const selectedDate =
                                                                  moment(
                                                                    v.ps4235
                                                                  );
                                                                await searchServiceCodes(
                                                                  query,
                                                                  selectedDate.isValid()
                                                                    ? selectedDate.toDate()
                                                                    : undefined
                                                                );
                                                              }
                                                            }}
                                                            onSelected={onSelectServiceCode(
                                                              field.name,
                                                              `psychotherapy.${i}.groupCareGiver`
                                                            )}
                                                            customStyle={{
                                                              menu: (base) => ({
                                                                ...base,
                                                                width: 470,
                                                              }),
                                                            }}
                                                            filterOption={
                                                              filterOption
                                                            }
                                                          />
                                                        );
                                                      }}
                                                    </Field>
                                                  </FormGroup2>
                                                </Flex>
                                                <Flex className="half-row-service_input">
                                                  <InputNumberValue
                                                    nestedTrans={nestedTrans}
                                                    namespace={namespace}
                                                    idField={`psychotherapy.${i}.groupCareGiver.${ig}.amountBilled`}
                                                    titleField="bezugsperson.ps4257"
                                                    validationType={
                                                      isFilledServiceCode
                                                        ? FieldValidationType.FieldValidationType_Required
                                                        : FieldValidationType.FieldValidationType_Optional
                                                    }
                                                    submitCount={submitCount}
                                                    touched={touched}
                                                    errors={errors}
                                                    maxLength={3}
                                                    disable={
                                                      !values?.psychotherapy?.[i]
                                                        ?.groupCareGiver?.[ig]
                                                        ?.serviceCode ||
                                                      isBefore2017
                                                    }
                                                  />
                                                </Flex>
                                                <Flex className="sl-Flex-minus-button">
                                                  {ig !== 0 && (
                                                    <Button
                                                      intent={Intent.NONE}
                                                      onClick={() =>
                                                        onRemoveCareGiverByIndex(
                                                          ig,
                                                          i
                                                        )
                                                      }
                                                    >
                                                      <Svg src={MinusIcon} />
                                                    </Button>
                                                  )}
                                                </Flex>
                                              </Flex>
                                            );
                                          })}
                                          <Flex className="sl-Flex-button_add_service_code">
                                            <Button
                                              intent={Intent.PRIMARY}
                                              className="btn-cancel"
                                              onClick={() => {
                                                setFieldValue(
                                                  `psychotherapy.${i}.groupCareGiver`,
                                                  [
                                                    ...(values.psychotherapy?.[i]
                                                      ?.groupCareGiver ?? []),
                                                    {
                                                      amountBilled: 0,
                                                      serviceCode: '',
                                                    } as GroupServicesCode,
                                                  ]
                                                );
                                              }}
                                            >
                                              <Flex>
                                                <Svg src={PlusSVG} />
                                                {t('addServiceCode')}
                                              </Flex>
                                            </Button>
                                          </Flex>
                                        </Flex>
                                      </FormGroup>
                                    </Collapse>
                                  </Flex>
                                )}
                              {mapFields.get('ps4251') &&
                                mapFields.get('ps4250') && (
                                  <Flex className="row checkbox-group">
                                    <Field name={`psychotherapy.${i}.ps4250`}>
                                      {({
                                        field,
                                        form,
                                      }: FieldProps<boolean>) => {
                                        return (
                                          <Checkbox
                                            className="schein-checkbox"
                                            checked={field.value}
                                            onChange={() => {
                                              form.setFieldValue(
                                                field.name,
                                                !field.value
                                              );
                                              form.setFieldValue(
                                                `psychotherapy.${i}.ps4251`,
                                                undefined,
                                                true
                                              );
                                              form.setFieldValue(
                                                `psychotherapy.${i}.ps4299`,
                                                undefined,
                                                false
                                              );
                                            }}
                                            disabled={
                                              Number(values?.ps4244?.length) >
                                              0 || isBefore2017
                                            }
                                            onKeyDown={(e) => {
                                              if (e.keyCode === 13) {
                                                e.preventDefault();
                                                form.setFieldValue(
                                                  field.name,
                                                  !field.value
                                                );
                                              }
                                            }}
                                          >
                                            {t('ps4250')}
                                          </Checkbox>
                                        );
                                      }}
                                    </Field>
                                  </Flex>
                                )}
                              <Wrapper>
                                <Field name={`psychotherapy.${i}.ps4251`}>
                                  {({ field, form }: FieldProps<number>) => {
                                    return (
                                      <ReactSelectValue
                                        idField={`psychotherapy.${i}.ps4251`}
                                        label={t('ps4251')}
                                        listValues={catalogsData['ps4251']}
                                        namespace={namespace}
                                        nestedTrans={nestedTrans}
                                        validationType={
                                          v.ps4250
                                            ? FieldValidationType.FieldValidationType_Required
                                            : FieldValidationType.FieldValidationType_Optional
                                        }
                                        submitCount={submitCount}
                                        touched={touched}
                                        errors={errors}
                                        setFieldTouched={props.setFieldTouched}
                                        className="ps4251Style"
                                        disabled={
                                          !values?.psychotherapy?.[i]?.ps4250 ||
                                          isBefore2017
                                        }
                                        selectedValue={
                                          field.value
                                            ? field.value
                                              .toString()
                                              .padStart(1, '0')
                                            : undefined
                                        }
                                        onSelected={(item) => {
                                          form.setFieldValue(
                                            field.name,
                                            Number(item.value)
                                          );
                                          if (
                                            [1, 2, 0].includes(
                                              Number(item.value)
                                            )
                                          ) {
                                            setTimeout(() => {
                                              form.setFieldValue(
                                                `psychotherapy.${i}.ps4299`,
                                                ''
                                              );
                                            });
                                          }
                                        }}
                                      />
                                    );
                                  }}
                                </Field>
                              </Wrapper>
                              {mapFields.get('ps4299') && (
                                <Wrapper>
                                  <InputNumberValue
                                    nestedTrans={nestedTrans}
                                    namespace={namespace}
                                    idField={`psychotherapy.${i}.ps4299`}
                                    titleField="ps4299"
                                    validationType={
                                      checkRequired4299FieldBy4251Field(values)
                                        ? FieldValidationType.FieldValidationType_Required
                                        : validationList.ps4299
                                    }
                                    submitCount={submitCount}
                                    touched={touched}
                                    errors={errors}
                                    maxLength={9}
                                    disable={
                                      [1, 2].includes(
                                        Number(values.psychotherapy[i].ps4251)
                                      ) ||
                                      !values.psychotherapy[i].ps4251 ||
                                      isBefore2017
                                    }
                                  />
                                </Wrapper>
                              )}
                              <Flex className="row checkbox-group">
                                <Field
                                  name={`psychotherapy.${i}.isInsuranceInformedTherapy`}
                                >
                                  {({ field, form }) => {
                                    return (
                                      <Checkbox
                                        className="schein-checkbox"
                                        checked={field.value}
                                        onChange={() => {
                                          form.setFieldValue(
                                            field.name,
                                            !field.value
                                          );
                                        }}
                                        onKeyDown={(e) => {
                                          if (e.keyCode === 13) {
                                            e.preventDefault();
                                            form.setFieldValue(
                                              field.name,
                                              !field.value
                                            );
                                          }
                                        }}
                                      >
                                        <span>
                                          {t('isInsuranceInformedTherapy')}
                                        </span>
                                      </Checkbox>
                                    );
                                  }}
                                </Field>
                              </Flex>
                              <Flex className="row checkbox-group">
                                <Checkbox
                                  className={`schein-checkbox psychotherapy.${i}.isPausingTime`}
                                  checked={isPausingDate[i]}
                                  onChange={() => {
                                    onChangePausingDate(i);
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.keyCode === 13) {
                                      e.preventDefault();
                                      onChangePausingDate(i);
                                    }
                                  }}
                                >
                                  {t('isPausingDate')}
                                </Checkbox>
                              </Flex>
                              <Wrapper>
                                <Flex auto justify="space-between">
                                  <Flex className="half-row">
                                    <InputDateTimeValue
                                      nestedTrans={nestedTrans}
                                      namespace={namespace}
                                      idField={`psychotherapy.${i}.pausingStartDate`}
                                      titleField="pausingStartDate"
                                      setFieldTouched={props.setFieldTouched}
                                      validationType={validationList.ps4235}
                                      submitCount={submitCount}
                                      touched={touched}
                                      errors={errors}
                                      setErrMapMessage={setErrMapMessage}
                                      className="ps4235Style"
                                      disabled={!isPausingDate[i]}
                                      maxDate={
                                        values['psychotherapy'][i]?.pausingEndDate
                                          ? new Date(
                                            values['psychotherapy'][
                                              i
                                            ].pausingEndDate!
                                          )
                                          : undefined
                                      }
                                    />
                                  </Flex>
                                  <Flex className="half-row">
                                    <InputDateTimeValue
                                      nestedTrans={nestedTrans}
                                      namespace={namespace}
                                      idField={`psychotherapy.${i}.pausingEndDate`}
                                      titleField="pausingEndDate"
                                      setFieldTouched={props.setFieldTouched}
                                      validationType={validationList.ps4235}
                                      submitCount={submitCount}
                                      touched={touched}
                                      errors={errors}
                                      setErrMapMessage={setErrMapMessage}
                                      className="ps4235Style"
                                      minDate={
                                        values?.psychotherapy?.[i]
                                          ?.pausingStartDate
                                          ? new Date(
                                            Number(
                                              values?.psychotherapy?.[i]
                                                ?.pausingStartDate
                                            )
                                          )
                                          : undefined
                                      }
                                      disabled={!isPausingDate[i]}
                                    />
                                  </Flex>
                                </Flex>
                              </Wrapper>
                              <Wrapper>
                                <Flex className="row checkbox-group">
                                  <Field name={`psychotherapy.${i}.isReason`}>
                                    {({ field, form }) => {
                                      return (
                                        <Checkbox
                                          className="schein-checkbox"
                                          checked={field.value}
                                          onChange={() => {
                                            form.setFieldValue(
                                              field.name,
                                              !field.value
                                            );
                                          }}
                                          disabled={
                                            !(
                                              values?.psychotherapy?.[i]
                                                ?.pausingStartDate &&
                                              isPausingDate[i]
                                            )
                                          }
                                          onKeyDown={(e) => {
                                            if (e.keyCode === 13) {
                                              e.preventDefault();
                                              form.setFieldValue(
                                                field.name,
                                                !field.value
                                              );
                                            }
                                          }}
                                        >
                                          {t('isReason')}
                                        </Checkbox>
                                      );
                                    }}
                                  </Field>
                                </Flex>
                              </Wrapper>
                            </React.Fragment>
                          )}
                        </Collapse>
                      </React.Fragment>
                    );
                  })}
                  <Flex className="sl-Flex-button_add_service_code add-psychotherapy">
                    <Button
                      intent={Intent.PRIMARY}
                      onClick={() => {
                        setFieldValue(`psychotherapy`, [
                          ...(values.psychotherapy ?? []),
                          INITIAL_PSYCHOTHERAPY,
                        ]);
                      }}
                    >
                      <Flex>
                        <Svg src={PlusSVG} />
                        {t('addPsychotherapy')}
                      </Flex>
                    </Button>
                  </Flex>
                </React.Fragment>
              )}
            </React.Fragment>
          )}
        </Flex>
      </Flex>
      <DeleteConfirmDialog
        isOpen={isResetPsychotherapy}
        close={() => {
          setSelectedRemoveIndex(0);
          setIsResetPsychotherapy(false);
        }}
        confirm={() => {
          if (selectedRemoveIndex) {
            onRemovePsychothearpy(selectedRemoveIndex);
          } else {
            setFieldValue('ps4234', false);
            removePyschotherapyFields(setFieldValue);
            setFieldValue(`psychotherapy`, []);
          }

          setSelectedRemoveIndex(0);
          setIsResetPsychotherapy(false);
        }}
        text={{
          btnCancel: tButtonActions('no'),
          btnOk: tButtonActions('yesRemove'),
          title: t('RemoveConfirmDialog.title'),
          message: t('RemoveConfirmDialog.description'),
        }}
      />
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(PsychotherapieForm, {
    namespace: namespace,
    nestedTrans: nestedTrans,
  })
);
