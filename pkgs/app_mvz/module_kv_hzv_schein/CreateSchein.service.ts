import pickBy from 'lodash/pickBy';

import { Nullable } from '@tutum/design-system/infrastructure/models';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import {
  CreateScheinRequest,
  CreateScheinResponse,
} from '@tutum/hermes/bff/app_mvz_schein';
import {
  FieldError,
  FieldErrorCode,
  ValidationType,
} from '@tutum/hermes/bff/common';
import * as ScheinApi from '@tutum/hermes/bff/legacy/app_mvz_schein';
import * as ScheinModel from '@tutum/hermes/bff/schein_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import catalogsData from './catalogs-data.json';
import { ICustomWarning } from './types';
import { ICustomInsuranceInfo } from '@tutum/mvz/_utils/checkInsurance';

const orOperator: string = '$or';
const ifOperator: string = '$if';
const conditionOperator: string = '$condition';
const thenOperator: string = '$then';

type ScheinAllFields = ScheinModel.ScheinDetail &
  CreateScheinRequest &
  ScheinModel.UpdateScheinRequest;

export interface ScheinInfo extends Omit<ScheinAllFields, 'g4101'> {
  g4101?: string;
  takeOverDiagnoseIds?: string[];
  mappingTreatmentRelevent?: {
    [key: string]: boolean;
  };
  markedAsBilled: boolean;
}

export const INITIAL_GROUP_SERVICECODE_BEFORE_2017: ScheinModel.GroupServicesCodeBefore2017 =
{
  serviceCode: '',
  amountApproval: 0,
  amountBilled: 0,
};

export const INITIAL_GROUP_SERVICECODE: ScheinModel.GroupServicesCode = {
  serviceCode: '',
  amountBilled: 0,
};

export const INITIAL_PSYCHOTHERAPY: ScheinModel.Psychotherapy = {
  groupServicesCode: [INITIAL_GROUP_SERVICECODE],
  groupCareGiver: [INITIAL_GROUP_SERVICECODE],
  groupServicesCodeBefore2017: [INITIAL_GROUP_SERVICECODE_BEFORE_2017],
  isInsuranceInformedTherapy: undefined,
  ps4250: undefined,
  ps4251: undefined,
  ps4235: undefined,
  ps4299: undefined,
  ps4247: undefined,
  ps4245: undefined,
  ps4246: undefined,
  pausingStartDate: undefined,
  pausingEndDate: undefined,
  ps4254: undefined,
  ps4252: undefined,
  ps4257: undefined,
  ps4255: undefined,
  isReason: false,
};

export const formatTransferCode = (tsvgTransferCode: string) => {
  return tsvgTransferCode.replaceAll('-', '');
};

// for convinience to convert datatype
export const SUPPORT_SPECIAL_FIELD_DATE_DATATYPE_NAME_INSTRING = {
  bgAccidentDate: 'Date',
  g4110: 'Date',
  g4102: 'Date',
  ad4206: 'Date',
  ps4247: 'Date',
  ps4235: 'Date',
};

const proccessRequestData = (scheinGeneralInfo: ScheinInfo) => {
  const request = {
    ...scheinGeneralInfo,
  };
  if (scheinGeneralInfo.g4101Quarter && scheinGeneralInfo.g4101Year) {
    request.g4101Quarter = scheinGeneralInfo.g4101Quarter;
    request.g4101Year = scheinGeneralInfo.g4101Year;
  } else {
    const timeNow = datetimeUtil.date();
    request.g4101Quarter = datetimeUtil.getQuarter(timeNow);
    request.g4101Year = timeNow.getFullYear();
  }
  scheinGeneralInfo.bgAccidentDate = scheinGeneralInfo?.bgAccidentDate
    ? new Date(scheinGeneralInfo.bgAccidentDate).getTime()
    : undefined;
  scheinGeneralInfo.g4102 = scheinGeneralInfo?.g4102
    ? new Date(scheinGeneralInfo.g4102).getTime()
    : undefined;
  scheinGeneralInfo.ad4206 = scheinGeneralInfo?.ad4206
    ? new Date(scheinGeneralInfo.ad4206).getTime()
    : undefined;
  scheinGeneralInfo.psychotherapy =
    scheinGeneralInfo?.psychotherapy?.map((p) => {
      return {
        ...p,
        ps4247: p?.ps4247 ? new Date(p?.ps4247).getTime() : undefined,
        ps4235: p?.ps4235 ? new Date(p?.ps4235).getTime() : undefined,
      };
    }) ?? [];

  request.scheinDetails = scheinGeneralInfo as ScheinModel.ScheinDetail;
  delete (request as any).psychotherapy;
  return request;
};

export async function createSchein(
  scheinGeneralInfo: ScheinInfo
): Promise<CreateScheinResponse> {
  const result = await ScheinApi.createSchein(
    proccessRequestData(scheinGeneralInfo) as CreateScheinRequest
  );
  return result.data;
}
export async function updateSchein(
  scheinGeneralInfo: ScheinInfo
): Promise<void> {
  const updateRequest = proccessRequestData(scheinGeneralInfo);
  await ScheinApi.updateSchein(updateRequest);
}

export type CreateOrUpdateSchein = typeof createSchein | typeof updateSchein;

export const dataTransform = (data: ScheinInfo): ScheinInfo => {
  data.g4104 = data?.g4104 ? Number(data?.g4104) : undefined;
  data.ps4246 = data?.ps4246 ? String(data?.ps4246) : undefined;
  data.ps4299 = data?.ps4299 ? Number(data?.ps4299) : undefined;
  data.psychotherapy =
    data.psychotherapy?.map((p) => {
      return Object.assign(pickBy(p, (x) => !isEmpty(x)));
    }) ?? [];
  const newData: ScheinInfo = Object.assign(pickBy(data, (x) => !isEmpty(x)));
  newData.psychotherapy = data.psychotherapy.map((p) => ({
    ...p,
    ps4255: Number(p?.ps4255),
    groupServicesCode: p.groupServicesCode
      .filter((s) => s.serviceCode != '')
      .map((s) => ({
        ...s,
        amountBilled: s.amountBilled ? Number(s.amountBilled) : 0,
      })),
    groupCareGiver: p.groupCareGiver
      .filter((g) => g.serviceCode)
      .map((g) => ({
        ...g,
        amountBilled: g.amountBilled ? Number(g.amountBilled) : 0,
      })),
    groupServicesCodeBefore2017: p.groupServicesCodeBefore2017.map((g) => ({
      ...g,
      amountBilled: g.amountBilled ? Number(g.amountBilled) : 0,
      amountApproval: g.amountApproval ? Number(g.amountApproval) : 0,
    })),
    ps4251: p?.ps4251 ?? undefined,
  }));
  newData.re4233 = data.re4233?.filter((item) => item.from && item.to);
  return newData;
};

export async function getScheinDetailById(
  scheinId: string
): Promise<ScheinModel.GetScheinDetailByIdResponse> {
  const result = await ScheinApi.getScheinDetailById({
    scheinId,
  } as ScheinModel.GetScheinDetailByIdRequest);
  return result.data;
}

export async function getScheinList(
  patientId: string
): Promise<ScheinModel.GetScheinsOverviewResponse> {
  const result = await ScheinApi.getScheinsOverview({
    patientId,
  });
  return result.data;
}

export async function getLastSelectedTreatmentCaseSubgroupByPatientId(
  patientId: string
): Promise<ScheinModel.GetSelectedTreatmentCaseSubgroupResponse> {
  const result = await ScheinApi.getSelectedTreatmentCaseSubgroup({
    patientId: patientId,
  } as ScheinModel.GetSelectedTreatmentCaseSubgroupRequest);
  return result.data;
}

export type IdAndText = {
  key: string;
  text: string;
  description?: string;
  sortName?: Nullable<string | React.ReactElement>;
  extendDescription?: string;
};

export type QueryIf = {
  condition: QueryValue[];
  then: QueryOperator[];
};

export type QueryValue = {
  key: string;
  value: string;
};

export type QueryOperator = {
  operator: '|';
  values: QueryValue[];
};

export function parsingStatement(
  obj: Object,
  statements: Array<QueryIf | QueryValue | QueryOperator>
) {
  for (const [key, value] of Object.entries(obj)) {
    if (key == ifOperator) {
      const ifStatement = { condition: [], then: [] } as QueryIf;
      parsingIfStatement(value, ifStatement);
      statements.push(ifStatement);
    } else if (key == orOperator) {
      const orStatement = { operator: '|', values: [] } as QueryOperator;
      parsingStatement(value, orStatement.values);
      statements.push(orStatement);
    } else if (isNaN(Number(key))) {
      statements.push({ key: key, value: value } as QueryValue);
    } else {
      parsingStatement(value, statements);
    }
  }
}

function parsingIfStatement(obj: Object, ifStatement: QueryIf) {
  for (const [key, value] of Object.entries(obj)) {
    if (key == conditionOperator) {
      parsingStatement(value, ifStatement.condition);
    } else if (key == thenOperator) {
      parsingStatement(value, ifStatement.then);
    }
  }
}

export const serverValidate = async (
  scheinInfo: ScheinInfo,
  insurances: ICustomInsuranceInfo[]
) => {
  try {
    const result = await ScheinApi.isValid({
      createScheinRequest: proccessRequestData(
        dataTransform(scheinInfo)
      ) as CreateScheinRequest,
      insurances: insurances,
    });

    return result.data;
  } catch (err) {
    return err;
  }
};

export function parsingQueryValidator(
  operator: QueryOperator | QueryIf | QueryValue,
  value: ScheinInfo,
  arrFieldError: string[]
): boolean {
  const tmpIfStatement = operator as QueryIf;
  if (
    tmpIfStatement.condition &&
    tmpIfStatement.condition.length > 0 &&
    tmpIfStatement.then.length > 0
  ) {
    if (isEmpty(value[tmpIfStatement.condition[0].key])) {
      return false;
    }
    return parsingQueryValidator(tmpIfStatement.then[0], value, arrFieldError);
  } else {
    const parseOperator = operator as QueryOperator;
    if (parseOperator.values) {
      const arrChecking: boolean[] = [];
      for (let i = 0; i < parseOperator.values.length; i++) {
        const tmpValue = { ...parseOperator.values[i] } as QueryValue;
        arrChecking.push(isEmpty(value[tmpValue.key], true));
        arrFieldError.push(tmpValue.key);
      }
      return arrChecking.reduce((previousValue, nextValue) => {
        return previousValue && nextValue;
      });
    } else {
      const parseValue = operator as QueryValue;
      arrFieldError.push(parseValue.key);
      return isEmpty(value[parseValue.key], true);
    }
  }
}

export const generalFormPrefix = 'g';
export const additionFormPrefix = 'a';
export const psychotherapyFormPrefix = 'p';
export const referralFormPrefix = 'r';
export const employeeFormPrefix = 'bgEmployer';

export function parseCatalogsData() {
  const data: { [key: string]: IdAndText[] } = {};
  for (const catalogKeyProp in catalogsData) {
    const listObj = catalogsData[catalogKeyProp];
    const listKeyValues: IdAndText[] = [];
    Object.keys(listObj)
      .sort()
      .forEach((key) => {
        listKeyValues.push({ key, text: listObj[key] });
      });
    data[catalogKeyProp] = listKeyValues;
  }
  return data;
}

// TODO: want to remove this function
export const parsingErrorObject = (errorList: {
  [key: string]: FieldError;
}): {
  errors: ICustomWarning;
  warnings: ICustomWarning;
  notices: ICustomWarning;
  disabledFields: ICustomWarning;
} | null => {
  const errors = {};
  const warnings = {};
  const notices = {};
  const disabledFields = {};
  if (!errorList) {
    return null;
  }
  Object.keys(errorList)?.forEach((eK) => {
    const errorValida = errorList[eK];
    // Assume From To field is always the same validation type
    if (eK.endsWith('From')) {
      eK = eK.substring(0, eK.lastIndexOf('From'));
      errorValida.field = eK;
    } else if (eK.endsWith('To')) {
      eK = eK.substring(0, eK.lastIndexOf('To'));
      errorValida.field = eK;
    } else if (eK.includes('re4233')) {
      // for date range, only show required validation on the first field
      eK = 're4233.0';
      errorValida.field = eK;
    }

    if (errorValida?.validationType === ValidationType.ValidationType_Warning) {
      warnings[eK] = errorValida;
      return;
    }

    if (errorValida?.validationType === ValidationType.ValidationType_Error) {
      if (
        errorValida.errorCode === FieldErrorCode.FieldErrorCode_MustNilOrEmpty
      ) {
        disabledFields[eK] = errorValida;
        return;
      }
      errors[eK] = errorValida;
      return;
    }

    if (errorValida?.validationType === ValidationType.ValidationType_Notice) {
      notices[eK] = errorValida;
      return;
    }
  });
  return { errors, warnings, notices, disabledFields };
};

export const stringToAlphanumeric = (str: string): string => {
  return str
    .split('')
    .map((e) => (/^[a-z0-9]+$/i.test(e) ? e : ''))
    .join('');
};

export const stringToNumberic = (str: string): string => {
  return str
    .split('')
    .map((e) => (/^[0-9]+$/i.test(e) ? e : ''))
    .join('');
};

export const parsingReferralCode = (referralCode: string) => {
  if (!referralCode) {
    return '';
  }
  const stringAlphanumeric = stringToAlphanumeric(referralCode);
  let stringParse = stringAlphanumeric;
  if (stringAlphanumeric.length > 4) {
    stringParse = `${stringAlphanumeric.slice(0, 4)}-${stringAlphanumeric.slice(
      4,
      8
    )}`;
  }
  if (stringAlphanumeric.length > 8) {
    stringParse = `${stringParse}-${stringAlphanumeric.slice(8)}`;
  }
  return stringParse;
};

export const compareDay = (day: number) => {
  const now = datetimeUtil.now();
  if (now < day) {
    return false;
  }
  return true;
};
