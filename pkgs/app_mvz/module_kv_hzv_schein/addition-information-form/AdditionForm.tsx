import React, { createRef, memo, useEffect, useState, useMemo } from 'react';
import ReactDOM from 'react-dom';
import { Field, FormikProps } from 'formik';

import { IMvzTheme } from '@tutum/mvz/theme';
import { IdAndText, ScheinInfo } from '../CreateSchein.service';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { Flex, H3 } from '@tutum/design-system/components';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import {
  FormGroup,
  Icon,
  Checkbox,
  Label,
} from '@tutum/design-system/components/Core';
import {
  InputValue,
  InputDateTimeValue,
  InputTimeRangeValue,
  ReactSelectValue,
  InputTextArea,
} from '@tutum/mvz/module_kv_hzv_schein/ScheinComponents';
import {
  scrollToElem,
  checkIfObjH<PERSON><PERSON><PERSON>,
  checkIfObjectHasValue,
} from '../utils';
import { FieldValidationType } from '@tutum/hermes/bff/schein_common';
import { FieldError } from '@tutum/hermes/bff/common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ICustomInsuranceInfo } from '@tutum/mvz/_utils/checkInsurance';
import { isEmpty } from 'lodash';

export interface IAdditionFormProps {
  className?: string;
  theme?: IMvzTheme;
  mapFields: Map<string, string>;
  setErrMapMessage: (fieldName: string, value: string) => void;
  catalogsData: { [key: string]: IdAndText[] };
  navRef: HTMLElement | null;
  validationList: {
    [key: string]: FieldValidationType;
  };
  customWarnings: { [key: string]: FieldError };
  onContactTypeChange: (item: string) => void;
  setValidations: (validate: any) => void;
  validations: {};
  isEdit: boolean;
  updateInsurances: ICustomInsuranceInfo[];
  onChangeField: (values: ScheinInfo) => void;
}
const namespace = 'Schein';
const nestedTrans = 'additionInfo';
const ADDITION_FIELDS = [
  'g4102',
  'ad4206',
  'ad4125',
  'ad4204',
  'ad4202',
  'tsvgContactType',
  'tsvgInfor',
  'tsvgTranferCode',
  'tsvgContactDate',
  'ad4123',
  'ad4126',
  'ad4124',
];

function AdditionForm(
  props: IAdditionFormProps & II18nFixedNamespace & FormikProps<ScheinInfo>
): React.FunctionComponentElement<IAdditionFormProps> {
  const {
    className,
    t,
    mapFields,
    submitCount,
    touched,
    errors,
    values,
    setErrMapMessage,
    setFieldTouched,
    navRef,
    catalogsData,
    validationList,
    isValidating,
    customWarnings,
    isEdit,
    updateInsurances,
    onContactTypeChange,
    onChangeField,
    setFieldValue,
  } = props;
  const [additionalRef, setAdditionalRef] = useState<Element | null>(null);
  const [isExpanded, setExpand] = useState<boolean | null>(null);
  if (isExpanded === null && checkIfObjHasKey(ADDITION_FIELDS, values)) {
    setExpand(checkIfObjectHasValue(ADDITION_FIELDS, values));
  }

  const patientFileStore = usePatientFileStore();

  useEffect(() => {
    setAdditionalRef(navRef?.querySelector('.additional') || null);
  }, [navRef]);
  const contentRef: React.RefObject<HTMLDivElement> = createRef();

  const menuClick = () => {
    if (!contentRef.current) {
      return;
    }
    scrollToElem(contentRef.current);
    return false;
  };

  const listTsvgContactType = useMemo(() => {
    if (values?.kvTreatmentCase === '0102') {
      return catalogsData['tsvgContactType'];
    }
    return catalogsData['tsvgContactType'].filter((value) => value.key !== '3');
  }, [values.kvTreatmentCase]);

  useEffect(() => {
    const hasErrorInSection = ADDITION_FIELDS.some((field) => !!errors[field]);

    if (hasErrorInSection) {
      setExpand(true);
    }
  }, [errors, isValidating]);

  let dateOfIssueInitialMonth;
  if (!errors?.g4102 && values?.g4102) {
    dateOfIssueInitialMonth = datetimeUtil.date(values?.g4102);
  } else {
    const { g4101Quarter, g4101Year } = values;
    dateOfIssueInitialMonth = datetimeUtil
      .getStartOfBySelectedQuarter(g4101Quarter, g4101Year, false)
      .toDate();
  }

  useEffect(() => {
    if (isEdit) {
      return;
    }

    if (values.g4101Quarter && values.g4101Year) {
      const machedInsurance = (
        (!isEmpty(updateInsurances)
          ? updateInsurances
          : patientFileStore.patient.current?.patientInfo.insuranceInfos) || []
      ).find((insuranceInfo) => insuranceInfo.id === values.insuranceId);

      if (machedInsurance) {
        const vknr = `${machedInsurance.insuranceCompanyId}`.slice(2, 5);

        if (+vknr >= 800) {
          const fromDate = +datetimeUtil.getStartOfBySelectedQuarter(
            values.g4101Quarter,
            values.g4101Year,
            false,
          );
          const toDate = +datetimeUtil.getEndOfSelectedQuarter(
            values.g4101Quarter,
            values.g4101Year
          );

          setFieldValue('ad4125From', fromDate);
          setFieldValue('ad4125To', toDate);

          return;
        }
      }
    }

    setFieldValue('ad4125From', null);
    setFieldValue('ad4125To', null);
  }, [
    isEdit,
    values.insuranceId,
    values.g4101Quarter,
    values.g4101Year,
    updateInsurances,
    patientFileStore.patient.current?.patientInfo.insuranceInfos,
  ]);

  return (
    <Flex
      column
      className={getCssClass(className, 'form-info')}
      id="additional-form"
    >
      {additionalRef
        ? ReactDOM.createPortal(
          <H3 className="label-section additional-form" onClick={menuClick}>
            {t('title')}
          </H3>,
          additionalRef
        )
        : null}
      <H3
        className="label-section"
        onClick={() => setExpand(!isExpanded)}
        ref={contentRef}
      >
        {t('title')}
        <Icon
          className="expand-icon"
          icon={isExpanded ? 'chevron-up' : 'chevron-down'}
        />
      </H3>
      <Flex
        column
        className={getCssClass('form-body', isExpanded ? 'expanded' : '')}
      >
        {mapFields.get('g4102') && (
          <Flex className="row">
            <InputDateTimeValue
              nestedTrans={nestedTrans}
              namespace={namespace}
              idField="g4102"
              titleField="g4102"
              customOutOfRangeErrorMessage="outOfRangeError4102"
              setFieldTouched={setFieldTouched}
              validationType={validationList.g4102}
              submitCount={submitCount}
              touched={touched}
              errors={errors}
              maxDate={datetimeUtil.date()}
              setErrMapMessage={setErrMapMessage}
              initialMonth={dateOfIssueInitialMonth}
            />
          </Flex>
        )}

        {(mapFields.get('ad4206') || mapFields.get('ad4125')) && (
          <Flex auto justify="space-between">
            {mapFields.get('ad4206') && (
              <Flex className="half-row">
                <InputDateTimeValue
                  nestedTrans={nestedTrans}
                  namespace={namespace}
                  idField="ad4206"
                  titleField="ad4206"
                  setFieldTouched={props.setFieldTouched}
                  validationType={validationList.ad4206}
                  submitCount={submitCount}
                  touched={touched}
                  errors={errors}
                  setErrMapMessage={setErrMapMessage}
                />
              </Flex>
            )}
            {mapFields.get('ad4125') && (
              <Flex className="half-row">
                <InputTimeRangeValue
                  namespace={namespace}
                  idField="ad4125"
                  nestedTrans={nestedTrans}
                  titleField="ad4125"
                  setFieldTouched={props.setFieldTouched}
                  validationType={validationList.ad4125}
                  submitCount={submitCount}
                  touched={touched}
                  errors={errors}
                />
              </Flex>
            )}
          </Flex>
        )}

        {(mapFields.get('ad4204') || mapFields.get('ad4202')) && (
          <Flex className="row">
            <FormGroup>
              {mapFields.get('ad4204') && (
                <Field name="ad4204">
                  {({ field, form }) => {
                    return (
                      <Checkbox
                        className="schein-checkbox"
                        checked={field.value}
                        onChange={() => {
                          form.setFieldValue(field.name, !field.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.keyCode === 13) {
                            e.preventDefault();
                            form.setFieldValue(field.name, !field.value);
                          }
                        }}
                      >
                        {t('ad4204')}
                      </Checkbox>
                    );
                  }}
                </Field>
              )}
              {mapFields.get('ad4202') && (
                <Field name="ad4202">
                  {({ field, form }) => {
                    return (
                      <Checkbox
                        className="schein-checkbox"
                        checked={field.value}
                        onChange={() => {
                          form.setFieldValue(field.name, !field.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.keyCode === 13) {
                            e.preventDefault();
                            form.setFieldValue(field.name, !field.value);
                          }
                        }}
                      >
                        {t('ad4202')}
                      </Checkbox>
                    );
                  }}
                </Field>
              )}
            </FormGroup>
          </Flex>
        )}
        {mapFields.get('tsvgContactType') ? (
          <Flex column>
            <FormGroup>
              <Flex className="group-field" column>
                <Label
                  name="tsvgSubgroup"
                  className="label-group label-group__sub-group--title"
                >
                  {t('tsvgSubgroupTitle')}
                </Label>
                <Flex auto justify="space-between" column>
                  <Flex>
                    <ReactSelectValue
                      idField="tsvgContactType"
                      listValues={listTsvgContactType}
                      namespace={namespace}
                      nestedTrans={nestedTrans}
                      validationType={validationList.tsvgContactType}
                      onItemClick={onContactTypeChange}
                      submitCount={submitCount}
                      forceShowTypeOfValidation
                      touched={touched}
                      errors={errors}
                      setFieldTouched={props.setFieldTouched}
                      className="tsvgContactTypeStyle"
                      hideIdListItem={true}
                    />
                  </Flex>
                  <Flex>
                    <InputTextArea
                      nestedTrans={nestedTrans}
                      namespace={namespace}
                      idField="tsvgInfor"
                      titleField="tsvgInfor"
                      validationType={validationList.tsvgInfor}
                      submitCount={submitCount}
                      touched={touched}
                      errors={errors}
                      disable={!values.tsvgContactType}
                      onChange={onChangeField}
                    />
                  </Flex>
                </Flex>
                <Flex auto justify="space-between">
                  <Flex className="half-row">
                    <InputValue
                      nestedTrans={nestedTrans}
                      namespace={namespace}
                      idField="tsvgTranferCode"
                      titleField="tsvgTranferCode"
                      validationType={validationList.tsvgTranferCode}
                      submitCount={submitCount}
                      touched={touched}
                      errors={errors}
                      disable={!values.tsvgContactType}
                      maxLength={14}
                      isReferralCode
                      onChange={onChangeField}
                    />
                  </Flex>
                  <Flex className="half-row">
                    {!values.tsvgContactType ? (
                      <InputValue
                        nestedTrans={nestedTrans}
                        namespace={namespace}
                        idField="tsvgContactDate"
                        titleField="tsvgContactDate"
                        validationType={validationList.tsvgContactDate}
                        submitCount={submitCount}
                        touched={touched}
                        errors={errors}
                        disable
                      />
                    ) : (
                      <InputDateTimeValue
                        nestedTrans={nestedTrans}
                        namespace={namespace}
                        maxDate={datetimeUtil.date()}
                        idField="tsvgContactDate"
                        titleField="tsvgContactDate"
                        setFieldTouched={props.setFieldTouched}
                        validationType={validationList.tsvgContactDate}
                        submitCount={submitCount}
                        touched={touched}
                        errors={errors}
                        warning={
                          customWarnings['tsvgContactDate']
                            ? t(customWarnings['tsvgContactDate'].errorCode)
                            : ''
                        }
                        setErrMapMessage={setErrMapMessage}
                        onChange={onChangeField}
                      />
                    )}
                  </Flex>
                </Flex>
              </Flex>
            </FormGroup>
          </Flex>
        ) : null}

        {(mapFields.get('ad4123') ||
          mapFields.get('ad4126') ||
          mapFields.get('ad4124')) && (
            <Flex column>
              <FormGroup>
                <Flex className="group-field" column>
                  <Label
                    name="otherPayers"
                    className="label-group label-group__sub-group--title"
                  >
                    {t('otherPayers')}
                  </Label>
                  {mapFields.get('ad4123') && (
                    <Flex className="row">
                      <ReactSelectValue
                        idField="ad4123"
                        listValues={catalogsData['ad4123']}
                        namespace={namespace}
                        nestedTrans={nestedTrans}
                        validationType={validationList.ad4123}
                        submitCount={submitCount}
                        forceShowTypeOfValidation
                        touched={touched}
                        errors={errors}
                        setFieldTouched={props.setFieldTouched}
                        className="ad4123"
                        hideIdListItem={true}
                      />
                    </Flex>
                  )}
                  <Flex column>
                    {mapFields.get('ad4126') && (
                      <Flex>
                        <InputValue
                          namespace={namespace}
                          idField="ad4126"
                          nestedTrans={nestedTrans}
                          titleField="ad4126"
                          validationType={validationList.ad4126}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          // maxLength={60}
                          onChange={onChangeField}
                        />
                      </Flex>
                    )}
                    {mapFields.get('ad4124') && (
                      <Flex>
                        <InputValue
                          namespace={namespace}
                          idField="ad4124"
                          nestedTrans={nestedTrans}
                          titleField="ad4124"
                          validationType={validationList.ad4124}
                          submitCount={submitCount}
                          touched={touched}
                          errors={errors}
                          maxLength={60}
                          minLength={5}
                          onChange={onChangeField}
                        />
                      </Flex>
                    )}
                  </Flex>
                </Flex>
              </FormGroup>
            </Flex>
          )}
      </Flex>
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(AdditionForm, {
    namespace: namespace,
    nestedTrans: nestedTrans,
  })
);
