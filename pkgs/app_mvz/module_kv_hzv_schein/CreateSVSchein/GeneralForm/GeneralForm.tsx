import {
  BodyTextM,
  BodyTextS,
  Flex,
  H3,
  alertWarning,
} from '@tutum/design-system/components';
import { Icon } from '@tutum/design-system/components/Core';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { useQueryGetContractById } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import { useQueryGetPatientProfileById } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { FieldValidationType } from '@tutum/hermes/bff/schein_common';
import { ContractInformation } from '@tutum/hermes/bff/service_domains_enrollment';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { IdAndText } from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';
import {
  InputDateTimeValue,
  InputDateTimeValueSelectFirstQuarter,
  RadioGroupValue,
  SelectDoctorWithBsnr,
} from '@tutum/mvz/module_kv_hzv_schein/ScheinComponents';
import SelectQuarter from '@tutum/mvz/module_kv_hzv_schein/components/SelectQuarter/SelectQuarter.styled';
import { scrollToElem } from '@tutum/mvz/module_kv_hzv_schein/utils';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { FormikProps } from 'formik';
import {
  MutableRefObject,
  createRef,
  memo,
  useEffect,
  useMemo,
  useState,
} from 'react';
import ReactDOM from 'react-dom';
import { ScheinInfo } from '../helpers';

export interface IGeneralFormProps {
  className?: string;
  theme?: IMvzTheme;
  doctors: IEmployeeProfile[];
  mapDoctor: Map<string, IEmployeeProfile>;
  onDoctorChange: (doctorId: string) => void;
  catalogsData: { [key: string]: IdAndText[] };
  navRef: HTMLElement | null;
  isHzv: boolean;
  patientId?: string;
  currentUser: IEmployeeProfile;
  dataGetContractInformation?: ContractInformation;
  mode?: 'create' | 'update';
}
const namespace = 'Schein';
const nestedTrans = 'generalInfo';

const GENERAL_FIELDS = [
  'doctorId',
  'kvScheinSubGroup',
  'insuranceId',
  'g4122',
  'g4101',
  'g3116',
  'g4106',
  'bgType',
  'bgAccidentDate',
  'bgAccidentTime',
  'bgWorkingTimeFrom',
  'bgWorkingTimeTo',
  'job',
  'tariffType',
];

const CONTRACT_SUPPORTS_MOD = 'AWH_01';
const CHARGE_SYSTEM_ID = 'AOK_BW_IV_P';

function GeneralForm({
  className,
  t,
  patientId,
  doctors,
  mapDoctor,
  submitCount,
  errors,
  touched,
  setFieldTouched,
  navRef,
  isValidating,
  isHzv,
  values,
  dataGetContractInformation,
  onDoctorChange,
  mode,
}: IGeneralFormProps & II18nFixedNamespace & FormikProps<ScheinInfo>) {
  const [supportIVP, setSupportIVP] = useState(false);
  const { data: patientProfile, isLoading: isLoadingPatient } =
    useQueryGetPatientProfileById(
      {
        id: patientId as string,
      },
      {
        enabled: Boolean(patientId),
      }
    );

  const { isContractSupport: hasSupportVERT647 } = useCheckContractSupport(
    ['VERT647'],
    [dataGetContractInformation?.contractId]
  );

  const doctorsList = useMemo(() => {
    return doctors?.filter((d) => {
      return (isHzv ? d.hzvContracts : d.favContracts)?.find(
        (c) => c.contractId === dataGetContractInformation?.contractId
      );
    });
  }, [doctors, isHzv, dataGetContractInformation]);

  const doctor = doctorsList.find((d) => d.id == values.doctorId);

  if (!doctor) {
    const treatMentDoctor = doctors?.find((d) => d.id === values.doctorId);
    treatMentDoctor && doctorsList.push(treatMentDoctor);
  }


  const { data: contractData } = useQueryGetContractById(
    {
      contractId: dataGetContractInformation?.contractId as string,
      selectedDate: datetimeUtil.getNowStartDay(),
    },
    {
      enabled: Boolean(dataGetContractInformation?.contractId),
    }
  );
  const chargeSystemName = useMemo(() => {
    if (!contractData || !dataGetContractInformation?.contractId) return {};
    const chargeSystemNameMap = {};
    for (const chargeSystem of contractData?.chargeSystems) {
      chargeSystemNameMap[chargeSystem.id] = chargeSystem.description;
    }
    for (const chargeSystem of contractData?.moduleChargeSystems) {
      chargeSystemNameMap[chargeSystem.id] = chargeSystem.description;
    }
    return chargeSystemNameMap;
  }, [contractData, dataGetContractInformation?.contractId]);

  const feeCatalogueList = useMemo(() => {
    return doctor?.hzvContracts
      ?.filter((c) => c.contractId === dataGetContractInformation?.contractId)
      ?.map((c) => {
        return {
          key: c.chargeSystemId,
          text: chargeSystemName?.[c.chargeSystemId] || c.chargeSystemId,
        };
      });
  }, [doctor, chargeSystemName, dataGetContractInformation]);

  const [generalRefSlot, setGeneralRefSlot] = useState<Nullable<Element>>(null);
  const [isExpaned, setExpand] = useState(true);
  useEffect(() => {
    setGeneralRefSlot(navRef?.querySelector('.general'));
  }, [navRef]);
  const contentRef: MutableRefObject<HTMLDivElement | null> = createRef();

  const menuClick = () => {
    if (!contentRef.current) {
      return;
    }
    scrollToElem(contentRef.current);
    return false;
  };

  useEffect(() => {
    const hasErrorInSection = GENERAL_FIELDS.some((field) => !!errors[field]);

    if (hasErrorInSection) {
      setExpand(true);
    }
  }, [errors, isValidating]);

  useEffect(() => {
    const supportIVP = !!feeCatalogueList?.some(
      (item) => item.key === CHARGE_SYSTEM_ID
    );
    setSupportIVP(supportIVP);
  }, [feeCatalogueList]);

  if (isLoadingPatient || !patientProfile) return null;

  return (
    <Flex
      column
      className={getCssClass(className, 'form-info')}
      id="general-form"
    >
      {generalRefSlot
        ? ReactDOM.createPortal(
            <H3
              className="label-section general-form active"
              onClick={menuClick}
            >
              {t('title')}
            </H3>,
            generalRefSlot
          )
        : null}
      <H3
        className="label-section"
        onClick={() => setExpand(!isExpaned)}
        ref={contentRef}
      >
        {t('title')}
        <Icon
          className="expand-icon"
          icon={isExpaned ? 'chevron-up' : 'chevron-down'}
        />
      </H3>
      <Flex
        column
        className={getCssClass('form-body', isExpaned ? 'expanded' : '')}
      >
        <Flex className="row">
          <SelectDoctorWithBsnr
            mapDoctor={mapDoctor}
            namespace={namespace}
            nestedTrans={nestedTrans}
            idField="doctorId"
            doctors={doctorsList}
            validationType="required"
            submitCount={submitCount}
            touched={touched}
            errors={errors}
            setFieldTouched={setFieldTouched}
            values={values}
            onChangeField={onDoctorChange}
          />
          {!!doctor && (
            <Flex
              column
              gap={8}
              p="8px 16px"
              mb={16}
              w="100%"
              style={{
                backgroundColor: COLOR.NEUTRAL_SECONDARY_HOVER,
              }}
            >
              <Flex align="center" gap={8}>
                <Flex minWidth="64px" w="64px">
                  <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                    {t('bsnr')}:
                  </BodyTextS>
                </Flex>
                <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                  {doctor.bsnr}
                </BodyTextM>
              </Flex>
              <Flex align="center" gap={8}>
                <Flex minWidth="64px" w="64px">
                  <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                    {t('lanr')}:
                  </BodyTextS>
                </Flex>
                <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                  {doctor.lanr}
                </BodyTextM>
              </Flex>
              <Flex gap={8}>
                <Flex minWidth="64px" w="64px">
                  <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                    {t('expertise')}:
                  </BodyTextS>
                </Flex>
                <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                  {(doctor.areaOfExpertise || []).join(', ')}
                </BodyTextM>
              </Flex>
              {isHzv &&
                dataGetContractInformation?.contractId ===
                  CONTRACT_SUPPORTS_MOD && (
                  <Flex gap={8}>
                    <Flex minWidth="64px" w="64px">
                      <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                        {t('feeCatalogue')}:
                      </BodyTextS>
                    </Flex>
                    <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                      {CONTRACT_SUPPORTS_MOD}
                    </BodyTextM>
                  </Flex>
                )}
            </Flex>
          )}
        </Flex>
        <Flex auto gap={8} justify="space-between">
          <SelectQuarter
            // isDisabled={isDisabledQuarter}
            nestedTrans={nestedTrans}
            namespace={namespace}
            idField="g4101"
            validationType={FieldValidationType.FieldValidationType_Required}
            submitCount={submitCount}
            touched={touched}
            errors={errors}
            isExcludeCurrentQuarter={!supportIVP}
            isDisabled={mode === 'update'}
          />
          <InputDateTimeValue
            nestedTrans={nestedTrans}
            namespace={namespace}
            idField="onlineParticipation"
            titleField="onlineParticipation"
            setFieldTouched={setFieldTouched}
            validationType={FieldValidationType.FieldValidationType_Required}
            submitCount={submitCount}
            touched={touched}
            errors={errors}
            minDate={
              new Date(
                +datetimeUtil.getStartOfBySelectedQuarter(
                  values.g4101Quarter || 0,
                  values.g4101Year || 0,
                  false,
                )
              )
            }
            maxDate={
              new Date(
                +datetimeUtil.getEndOfSelectedQuarter(
                  values.g4101Quarter || 0,
                  values.g4101Year || 0
                )
              )
            }
            disabled={
              !values.g4101Year || !values.g4101Quarter || mode === 'update'
            }
          />
        </Flex>

        <Flex auto gap={8} justify="space-between">
          <InputDateTimeValueSelectFirstQuarter
            nestedTrans={nestedTrans}
            namespace={namespace}
            idField="startDate"
            titleField="startDate"
            setFieldTouched={setFieldTouched}
            validationType=""
            submitCount={submitCount}
            touched={touched}
            errors={errors}
            onChange={() => {
              if (hasSupportVERT647) {
                alertWarning(t('changeStartDate'));
              }
            }}
          />
          <InputDateTimeValue
            nestedTrans={nestedTrans}
            namespace={namespace}
            idField="endDate"
            titleField="endDate"
            setFieldTouched={setFieldTouched}
            validationType=""
            submitCount={submitCount}
            touched={touched}
            errors={errors}
          />
        </Flex>

        {isHzv && (
          <Flex column gap={16}>
            <Flex className="row">
              <RadioGroupValue
                nestedTrans={nestedTrans}
                idField="feeCatalogue"
                listValues={feeCatalogueList}
                isLabel
                validationType={
                  FieldValidationType.FieldValidationType_Required
                }
                submitCount={submitCount}
                touched={touched}
                errors={errors}
                value={values['feeCatalogue']}
              />
            </Flex>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(GeneralForm, {
    namespace: namespace,
    nestedTrans: nestedTrans,
  })
);
