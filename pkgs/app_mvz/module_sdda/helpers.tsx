import React from 'react';

import type SDDAI18n from '@tutum/mvz/locales/en/SDDA.json';

import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { TagMedicine, Svg } from '@tutum/design-system/components';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import CheckCircleSolid1 from '@tutum/mvz/public/images/check-circle-solid-1.svg';
import { DataCenterInfo } from '@tutum/hermes/bff/app_mvz_edmp';
import {
  TableAction,
  TableActionItem,
} from '@tutum/design-system/components/Table/TableAction/TableAction';

interface IGenColumnsParams {
  t: IFixedNamespaceTFunction<keyof typeof SDDAI18n.tableSetting>;
  onEdit: (row: DataCenterInfo) => void;
  onOpenConfirmDelete: (row: DataCenterInfo) => void;
}

export const genColumns = ({
  t,
  onEdit,
  onOpenConfirmDelete,
}: IGenColumnsParams): Array<IDataTableColumn<DataCenterInfo>> => [
    {
      name: t('dataCenter'),
      selector: (row) => row.dataCenter?.name,
      minWidth: '430px',
    },
    {
      name: t('organisation'),
      selector: (row) => row.dataCenter?.organisation || '',
      minWidth: '430px',
    },
    {
      name: t('postalCode'),
      selector: (row) => row.dataCenter?.postalCode,
      width: '104px',
    },
    {
      name: t('city'),
      selector: (row) => row.dataCenter?.city,
      width: '240px',
    },
    {
      name: t('kvConnect'),
      width: '96px',
      cell: (row) => {
        const hasKvConnect = row.dataCenter.kV?.length;
        const validKvConnectAddress = row.dataCenter.kV?.some(
          (kv) => kv?.kVAddress
        );
        if (hasKvConnect && validKvConnectAddress) {
          return <CheckCircleSolid1 />;
        }
        return null;
      },
    },
    {
      name: '',
      width: '124px',
      cell: (row) => {
        if (!row.dataCenter.selfCreated) {
          return null;
        }

        return <TagMedicine className="sl-info">{t('selfCreated')}</TagMedicine>;
      },
    },
    {
      id: 'action',
      width: '40px',
      style: {
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
      },
      cell: (row) => {
        const actions: TableActionItem[] = [
          {
            id: 'edit',
            label: t('edit'),
            icon: <Svg src={'/images/edit-value.svg'} />,
            onClick: () => onEdit(row),
          },
        ];
        if (row.dataCenter.selfCreated) {
          actions.push({
            id: 'remove',
            label: t('remove'),
            hoverColor: 'danger',
            icon: <Svg src={'/images/trash-bin-red.svg'} />,
            onClick: () => onOpenConfirmDelete(row),
          });
        }
        return <TableAction actions={actions} />;
      },
    },
  ];
