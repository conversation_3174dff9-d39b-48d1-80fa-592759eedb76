import {
  ConditionalStyles,
  IDataTableColumn,
  IDataTableStyles,
} from '@tutum/design-system/components/Table';
import { findingValueindex } from '@tutum/design-system/infrastructure/utils';
import { BACKGROUND } from '@tutum/design-system/themes/styles/color/background';
import { ScheinItem } from '@tutum/hermes/bff/legacy/schein_common';
import {
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import type AppointmentI18n from '@tutum/mvz/locales/en/Appointment.json';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import type DoctorLetterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import TimelineService from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import { renderCalendarText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/appointment-entry/utils';
import { renderDiagnoseText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/diagnose-entry/helpers';
import { renderDigaText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/diga-entry/utils';
import { renderDoctorLetterEntryText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/doctor-letter-entry';
import { renderFormText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/form-entry/FormEntry';
import { renderNoteText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/freetext-entry/FreetextEntry';
import { renderGoaServiceText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/util';
import { renderHeimiText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/heimi-prescription/time-line-form-detail/TimeLineFormDetail';
import { renderHimiContentText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/himi-prescription/time-line-form-detail/TimeLineFormDetail';
import {
  handleFormName,
  renderLabFormText,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/lab-entry/LabEntry';
import {
  getActionType,
  handleCompare,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/medication-plan-history-entry/MedicationPlanHistoryEntry';
import { handleGetMedicineString } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/medication-prescription/time-line-form-detail/helpers';
import { renderPsychotherapyText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/psychotherapy-entry/util';
import {
  renderServiceChainText,
  renderServiceText,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/service-entry/utils';
import moment from 'moment';
import React, { ReactElement } from 'react';
import type { DefaultTheme } from '@tutum/design-system/themes';
import { renderCustomizeText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/customize-entry/CustomizeEntry.helper';

const highlightText = (keyword: string, content: string): React.JSX.Element => {
  const keyWordTrimed = keyword.trim();

  if (keyWordTrimed) {
    const [start, end, matchedWord] = findingValueindex(content, keyWordTrimed);
    const startWord = content.substring(0, start);
    const endWord = content.substring(end);

    if (end > 0 && start >= 0) {
      return (
        <>
          {startWord}
          <span
            style={{ backgroundColor: BACKGROUND.BACKGROUND_TEXT_HIGHLIGHT }}
          >
            {matchedWord}
          </span>
          {endWord}
        </>
      );
    }
  }
  return <>{content}</>;
};

function MultiLineText({ keyword, text }: { keyword: string; text: string }) {
  return (
    <span style={{ whiteSpace: 'pre-line' }}>
      {highlightText(keyword, text)}
    </span>
  );
}

const scanAndHighlight = (node: string, keyword: string): ReactElement => {
  if (node.includes('\n')) {
    return <MultiLineText text={node} keyword={keyword} />;
  }

  return <>{highlightText(keyword, node)}</>;
};

export const getDescriptionEntry = (
  entry: TimelineModel,
  originalSchein: ScheinItem[],
  {
    tFormTimeline,
    tTimeLineFormDetail,
    tHimiTimeline,
    tMedicationPlanHistoryEntry,
    tDoctorLetter,
    tLabForm,
    tBilling,
    tAppointment,
    tSchein,
  },
  withDocumentationDate = false,
  showCommandDiagnose = true
): string => {
  let content: string | JSX.Element = withDocumentationDate
    ? entry.createdAtString + ' '
    : '';

  switch (entry.type) {
    case TimelineEntityType.TimelineEntityType_Diagnose: {
      if (entry.encounterDiagnoseTimeline) {
        content += renderDiagnoseText(
          entry.encounterDiagnoseTimeline,
          showCommandDiagnose
        );
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Note: {
      if (entry.encounterNoteTimeline) {
        content += renderNoteText(entry.encounterNoteTimeline);
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Form: {
      if (entry.encounterForm) {
        content += renderFormText(
          tFormTimeline,
          entry.encounterForm?.prescribe?.formName
        );
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Lab: {
      if (entry.encounterLab) {
        content += `${renderLabFormText(
          tLabForm,
          entry.encounterLab
        )} • ${handleFormName(entry?.encounterLab?.labForm?.formName)}`;
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Service: {
      if (entry?.encounterServiceTimeline) {
        content += renderServiceText(entry?.encounterServiceTimeline);
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Service_Chain: {
      const services = entry?.encounterServiceChain?.services;

      if (services) {
        for (const service of services) {
          content += renderServiceChainText(service);
        }
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_MedicinePrescription: {
      if (entry?.encounterMedicinePrescription) {
        content += entry?.encounterMedicinePrescription?.formInfos
          ?.reduce((acc, item) => [...acc, ...item.medicines], [])
          ?.map((medicine) =>
            handleGetMedicineString(medicine, tTimeLineFormDetail)
          )
          ?.join('\n');
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_HimiPrescription: {
      if (entry?.encounterHimiPrescription) {
        content += renderHimiContentText({
          isControllable: !!entry?.encounterHimiPrescription?.additionalForm,
          t: tHimiTimeline,
          form: entry?.encounterHimiPrescription,
          contract: patientFileActions.getAvailableContractById(
            entry?.contractId
          ),
        });
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_HeimiPrescription: {
      if (entry?.encounterHeimiPrescription) {
        content += renderHeimiText(entry?.encounterHeimiPrescription);
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_MedicinePlan: {
      if (entry?.encounterMedicinePlanHistory) {
        const { compare, displayName, substanceName } = handleCompare(
          tMedicationPlanHistoryEntry,
          entry?.encounterMedicinePlanHistory
        );
        const actionType = getActionType(
          tMedicationPlanHistoryEntry,
          entry?.encounterMedicinePlanHistory
        );
        content += `${tMedicationPlanHistoryEntry(
          'medicationPlan'
        )} - ${actionType} - ${displayName} - ${substanceName}\n${compare
          ?.map(
            (c) =>
              `${c?.attributeName}: ${c?.old || 'Empty'} → ${c?.new || 'Empty'}`
          )
          ?.join('\n')}`;
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_DoctorLetter: {
      if (entry.doctorLetter) {
        content += renderDoctorLetterEntryText(
          entry.doctorLetter,
          tDoctorLetter,
          tBilling
        );
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Service_GOA: {
      if (entry?.encounterGoaService) {
        content += renderGoaServiceText(entry?.encounterGoaService);
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Diga: {
      if (entry?.digaPrescriptionTimeline) {
        content += renderDigaText(entry.digaPrescriptionTimeline);
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Calendar: {
      if (entry?.encounterAppointmentTimeline) {
        content += renderCalendarText(
          tAppointment,
          entry?.encounterAppointmentTimeline
        );
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Psychotherapy: {
      if (entry?.encounterPsychotherapy) {
        content += renderPsychotherapyText(
          entry?.encounterPsychotherapy,
          originalSchein,
          tSchein,
          true
        );
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_GDT: {
      if (entry?.encounterGDT) {
        content += '\n' + entry.encounterGDT.note;
      }
      break;
    }
    case TimelineEntityType.TimelineEntityType_Customize: {
      if (entry?.encounterCustomize) {
        content += renderCustomizeText(entry.encounterCustomize);
      }
      break;
    }
    default:
      break;
  }

  return content;
};

export const RenderHightlighContentElement = (
  entry: TimelineModel,
  keyword: string
): React.JSX.Element => {
  const { t: tFormTimeline } = I18n.useTranslation({
    namespace: 'Form',
  });
  const { t: tTimeLineFormDetail } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'TimeLineFormDetail',
  });
  const { t: tHimiTimeline } = I18n.useTranslation({
    namespace: 'Himi',
    nestedTrans: 'Timeline',
  });
  const { t: tMedicationPlanHistoryEntry } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'MedicationPlanHistoryEntry',
  });
  const { t: tDoctorLetter } = I18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({
    namespace: 'DoctorLetter',
  });
  const { t: tLabForm } = I18n.useTranslation<keyof typeof LabI18n.Timeline>({
    namespace: 'Lab',
    nestedTrans: 'Timeline',
  });
  const { t: tBilling } = I18n.useTranslation<
    keyof typeof BillingI18n['PrivateBilling']
  >({
    namespace: 'Billing',
    nestedTrans: 'PrivateBilling',
  });

  const { t: tAppointment } = I18n.useTranslation<
    keyof typeof AppointmentI18n['Timeline']
  >({
    namespace: 'Appointment',
    nestedTrans: 'Timeline',
  });

  const { t: tSchein } = I18n.useTranslation<Paths<typeof ScheinI18n>>({
    namespace: 'Schein',
  });
  const patientFileStore = usePatientFileStore();

  const content = getDescriptionEntry(
    entry,
    patientFileStore.schein.originalList,
    {
      tFormTimeline,
      tTimeLineFormDetail,
      tHimiTimeline,
      tMedicationPlanHistoryEntry,
      tDoctorLetter,
      tLabForm,
      tBilling,
      tAppointment,
      tSchein,
    },
    false,
    false
  );

  return scanAndHighlight(content, keyword);
};

export const genColumns = (
  originalList: ScheinItem[],
  {
    tFormTimeline,
    tTimeLineFormDetail,
    tHimiTimeline,
    tMedicationPlanHistoryEntry,
    tDoctorLetter,
    tLabForm,
    tBilling,
    tAppointment,
    tSchein,
  }: any
): IDataTableColumn<TimelineModel>[] => [
    {
      name: tDoctorLetter('timelineEntry'),
      selector: (row) => row.createdAt || '',
      width: '140px',
      format: (row: TimelineModel) =>
        row?.createdAt ? moment(row?.createdAt).format(DATE_FORMAT) : null,
    },
    {
      cell: (row: TimelineModel) => {
        return (
          <div style={{ whiteSpace: 'break-spaces', color: 'inherit' }}>
            {getDescriptionEntry(row, originalList, {
              tFormTimeline,
              tTimeLineFormDetail,
              tHimiTimeline,
              tMedicationPlanHistoryEntry,
              tDoctorLetter,
              tLabForm,
              tBilling,
              tAppointment,
              tSchein,
            })}
          </div>
        );
      },
    },
  ];

export const customStyles: IDataTableStyles = {
  rows: {
    style: {
      minHeight: 'unset',
      color: 'inherit',
    },
  },
  cells: {
    style: {
      paddingTop: '12px',
      paddingBottom: '12px',
      color: 'inherit',
    },
  },
};

export const getConditionalRowStyles = (
  theme: DefaultTheme
): ConditionalStyles<TimelineModel>[] => [
    {
      when: () => true,
      style: (row) => ({
        backgroundColor: `${TimelineService.parseEntryColor(row, theme.timelineTheme)?.bg
          }`,
        color: TimelineService.parseEntryColor(row, theme.timelineTheme)?.text,
      }),
    },
  ];
