import React from 'react';
import { ITimeInputProps, ITimeInputStates } from './TimeInputModel';
import { InputGroup } from '@tutum/design-system/components/Core';

const KEY_BACKSPACE = 'Backspace';
const KEY_TAB = 'Tab';
const KEYS_SPLIT = [' ', ':'];
const KEY_SPLIT_DISPLAY = ':';
const PLACEHOLDER = 'hh:mm';
const MAX_LENGTH = 5;
const POSITION_TO_ADD_SPLIT_KEYWORD = [1];
const POSITION_TO_REMOVE_SPLIT_KEYWORD = [3];
const POSITION_TO_SAVE_DATA = 4;

export class OriginalTimeInput extends React.PureComponent<
  ITimeInputProps,
  ITimeInputStates
> {
  codeInputRef: HTMLInputElement | null = null;
  bindInputRef = (ref: HTMLInputElement) => {
    this.codeInputRef = ref;
    if (!this.codeInputRef) {
      return;
    }
    this.codeInputRef.onkeydown = (e) => {
      const { dateString } = this.state;
      const key = e.key;
      //Accept for tab keyword
      if (key === KEY_TAB) {
        return;
      }
      e.preventDefault();
      e.stopPropagation();
      const regex = /[0-9 .]|Backspace/;
      //Check key input is valid
      if (
        !regex.test(key) ||
        (key !== KEY_BACKSPACE &&
          !KEYS_SPLIT.includes(key) &&
          dateString &&
          dateString.length >= MAX_LENGTH)
      ) {
        e.returnValue = false;
        if (e.preventDefault) e.preventDefault();
        return;
      }

      //Handle date string with special keyword and special position
      let newDateString = dateString || '';
      const dateLength = dateString.length;
      if (key === KEY_BACKSPACE) {
        const numberOfCharToDelete = POSITION_TO_REMOVE_SPLIT_KEYWORD.includes(
          dateLength
        )
          ? 2
          : 1;
        newDateString = newDateString.substr(
          0,
          newDateString.length - numberOfCharToDelete
        );
        this.onReset();
        this.onChange(key, dateString.length, newDateString, undefined!);
      } else if (!KEYS_SPLIT.includes(key)) {
        newDateString += POSITION_TO_ADD_SPLIT_KEYWORD.includes(dateLength)
          ? e.key + KEY_SPLIT_DISPLAY
          : e.key;
        this.onChange(key, dateLength, newDateString, undefined!);
      } else {
        switch (dateLength) {
          case POSITION_TO_ADD_SPLIT_KEYWORD[0]:
            newDateString = `0${newDateString}${KEY_SPLIT_DISPLAY}`;
            break;
          default:
            return;
        }
      }

      //Handle keyword display
      let newQuery = PLACEHOLDER;
      if (newDateString.length > 0) {
        newQuery =
          newDateString +
          newQuery.substr(
            newDateString.length,
            MAX_LENGTH - newDateString.length
          );
        this.setState(
          {
            dateStringDisplay: newQuery,
            dateString: newDateString,
          },
          () => {
            this.codeInputRef?.setSelectionRange(
              newDateString.length,
              newDateString.length,
              'forward'
            );
          }
        );

        //Set value and message error to outside component
        if (
          dateString.length == POSITION_TO_SAVE_DATA &&
          key != KEY_BACKSPACE
        ) {
          this.setState(
            {
              dateValue: newQuery,
            },
            () => {
              this.onComplete();
              this.onChange(key, POSITION_TO_SAVE_DATA, newQuery, newQuery);
            }
          );
        }
      } else {
        this.setState({
          dateStringDisplay: '',
          dateString: '',
        });
      }
    };
  };

  componentDidUpdate(prevProps: Readonly<ITimeInputProps>) {
    const { dateValue } = this.props;
    if (prevProps !== this.props && dateValue && !prevProps.dateValue) {
      this.setState({
        dateValue: dateValue,
        dateString: dateValue,
        dateStringDisplay: dateValue,
      });
    }
    if (this.props != prevProps) {
      const { dateValue, dateString, dateStringDisplay, onChange } = this.props;
      if (!onChange) {
        this.setState({
          dateValue: dateValue,
        });
        return;
      }
      if (!dateValue) {
        this.setState({
          dateString: '',
          dateStringDisplay: '',
          dateValue: undefined,
        });
        return;
      }
      if (dateValue) {
        if (dateString && dateStringDisplay) {
          this.setState({
            dateString: dateString,
            dateStringDisplay: dateStringDisplay,
            dateValue: this.props.dateValue,
          });
          return;
        }
        this.setState({
          dateString: dateValue,
          dateStringDisplay: dateValue,
          dateValue: this.props.dateValue,
        });
        return;
      }
      if (dateString && dateStringDisplay) {
        this.setState({
          dateValue: undefined,
          dateString: dateString ? dateString : '',
          dateStringDisplay: dateStringDisplay ? dateStringDisplay : '',
        });
      } else {
        this.setState({
          dateValue: undefined,
        });
      }
    }
  }

  convertDatetimeToString(dateValue: Date): string {
    const date =
      dateValue.getDate() < 10
        ? `0${dateValue.getDate()}`
        : `${dateValue.getDate()}`;
    const month =
      dateValue.getMonth() + 1 < 10
        ? `0${dateValue.getMonth() + 1}`
        : `${dateValue.getMonth() + 1}`;
    const year = dateValue.getFullYear();
    return `${date}${KEY_SPLIT_DISPLAY}${month}${KEY_SPLIT_DISPLAY}${year}`;
  }

  constructor(props: ITimeInputProps) {
    super(props);
    const { dateValue } = props;
    if (dateValue) {
      this.state = {
        dateStringDisplay: dateValue,
        dateString: dateValue,
        dateValue: dateValue,
      };
    } else {
      this.state = {
        dateStringDisplay: '',
        dateString: '',
      };
    }
  }

  render() {
    const { className, name, onBlur } = this.props;
    const { dateStringDisplay } = this.state;
    return (
      <InputGroup
        type="text"
        name={name}
        className={className}
        placeholder={PLACEHOLDER}
        value={dateStringDisplay}
        inputRef={this.bindInputRef}
        onBlur={onBlur}
        intent={this.props.intent}
        autoComplete="off"
      />
    );
  }

  onComplete = () => {
    const { onComplete } = this.props;
    const { dateValue } = this.state;
    onComplete(dateValue);
  };

  onReset = () => {
    const { onReset } = this.props;
    const { dateValue } = this.state;
    if (dateValue !== undefined) {
      onReset();
    }
  };

  onChange = (
    keyword,
    position: number,
    dateString: string,
    dateValue: string
  ) => {
    const { onChange } = this.props;
    if (!onChange) {
      return;
    }
    onChange(keyword, position, dateValue, dateString);
  };
}
