import { Dialog, OverlayToaster } from '@tutum/design-system/components/Core';
import React, { useEffect, useRef, useState } from 'react';

import {
  ErrorBoundaryWithRouter,
  IErrorData,
  LoadingState,
} from '@tutum/design-system/components';

import Provider from '@tutum/design-system/themes/Provider';
import {
  useListenDeviceChange,
  useListenHandleUpdateEmployee,
} from '@tutum/hermes/bff/app_admin';
import { useListenBSNRDeactivate } from '@tutum/hermes/bff/app_bsnr';
import { useListenSettingChange } from '@tutum/hermes/bff/app_mvz_settings';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { useMutationLogout } from '@tutum/hermes/bff/legacy/app_mvz_auth';
import { SettingsFeatures } from '@tutum/hermes/bff/legacy/settings_common';
import useLockAppSetting from '@tutum/infrastructure/hook/useLockAppSetting';
import I18n from '@tutum/infrastructure/i18n';
import { saveSystemDate } from '@tutum/infrastructure/utils/datetime.util';
import AccountManagementUtil from '@tutum/infrastructure/utils/form.util';
import WebWorker from '@tutum/infrastructure/web-worker-services';
import { toasterRef } from '@tutum/mvz/components/toaster';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { employeeActions } from '@tutum/mvz/hooks/useEmployee';
import { useErrorCodeI18n } from '@tutum/mvz/hooks/useErrorCode';
import { useHpmVersionState } from '@tutum/mvz/hooks/useHpmVersionState';
import {
  settingActions,
  useSettingStore,
} from '@tutum/mvz/hooks/useSetting.store';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import InitialService from '@tutum/mvz/services/initial.service';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { useRouter } from 'next/router';

export interface IAppSkeletonProps {
  children: (param: {
    hasLoggedIn: boolean;
    isForbidden: boolean;
  }) => React.ReactNode;
  apiErrorRedirect: {
    [key: number]: string;
  };
}

export interface IAppSkeletonState {
  userProfile?: IEmployeeProfile;
  isForbidden: boolean;
  configs?: {
    [key: string]: object;
  };
}

const excludeLoadingOnFirstMountPaths = [ROUTING.PATIENT];

function AppSkeleton(props: IAppSkeletonProps) {
  const { children, apiErrorRedirect } = props;
  const [isLoading, setIsLoading] = useState(true);
  const [state, setState] = useState<IAppSkeletonState>({
    isForbidden: false,
  });
  const router = useRouter();
  const { lang } = I18n.useTranslation({ namespace: undefined });
  const errorCodeT = useErrorCodeI18n();
  const setting = useSettingStore();
  const isFirstMount = useRef(true);

  const { updateLastActivity } = useLockAppSetting({
    userProfileId: state.userProfile?.id,
  });

  const loadUserProfile = () => {
    return InitialService.getInitial().then((initial) => {
      const userProfile = initial?.userProfile
        ? ({
            ...initial.userProfile,
            fullName: AccountManagementUtil.getFullName(
              initial.userProfile?.title,
              '',
              initial.userProfile?.lastName,
              initial.userProfile?.firstName,
              true
            ),
          } as IEmployeeProfile)
        : undefined;
      setState((prevState) => ({
        ...prevState,
        ...initial,
        userProfile,
        isForbidden: false,
      }));
      employeeActions.setUserProfile(userProfile);
      // const allowRemove = userHasRequiredRole(
      //   userProfile?.types,
      //   UserType.MANAGER
      // );
      settingActions.setAllowRemoveTimeline(true);
      return userProfile;
    });
  };

  useEffect(() => {
    const systemDate = setting?.systemDate;
    if (systemDate) {
      saveSystemDate(parseInt(systemDate));
    }
  }, [setting?.systemDate]);

  useHpmVersionState({ showOnlyOncePerDay: true });

  useEffect(() => {
    loadUserProfile()
      .then((res) => {
        WebWorker.initWorker();
        if (!res) return;

        updateLastActivity();

        return Promise.all([
          settingActions.loadSettings(),
          settingActions.loadUserSetting(),
        ]);
      })
      .catch((error) => {
        console.error(error);

        setState((pre) => {
          return { ...pre, isForbidden: error.statusCode === 403 };
        });
        const route = apiErrorRedirect[error.statusCode];
        if (route) {
          return router.push(route);
        }
      })
      .finally(() => {
        setIsLoading(false);
        isFirstMount.current = false;
      });

    return () => {
      WebWorker.terminateWorker();
    };
  }, []);

  useListenHandleUpdateEmployee((data) => {
    if (data.id != state.userProfile?.id) return;

    loadUserProfile().then((res) => {
      // Check if bsnrId has changed after reloading the profile then load settings with bsnrId
      if (res && res.bsnrId !== state.userProfile?.bsnrId) {
        settingActions.loadSettings(res.bsnrId);
      }
    });
  });

  useListenDeviceChange((data) => {
    if (data.deviceId != state.userProfile?.deviceId) return;
    loadUserProfile();
  });

  useListenSettingChange((dataChange) => {
    // there are some settings without bsnrId
    if (dataChange.bsnrId && dataChange.bsnrId !== state.userProfile?.bsnrId)
      return;

    switch (dataChange.feature) {
      case SettingsFeatures.SettingsFeatures_KbvMedication: {
        settingActions.setMedication(dataChange.settings);
        break;
      }
      case SettingsFeatures.SettingsFeatures_SystemDate: {
        window.location.reload();
        break;
      }
      case SettingsFeatures.SettingsFeatures_TimelineEntryColor: {
        settingActions.setEntryColor(dataChange.settings);
        break;
      }
      default:
        break;
    }
  });

  useListenBSNRDeactivate(({ bsnr }) => {
    if (!bsnr?.id || !state?.userProfile?.bsnrIds?.includes(bsnr.id)) {
      return;
    }

    loadUserProfile();
  });

  useEffect(() => {
    import('moment').then((moment) => {
      moment.default.locale(lang);
    });
  }, [lang]);

  const timelineTheme = {
    scaleNumber: setting.timelineSetting.scaleNumber,
    entryColor: setting.timelineSetting.entryColor,
  };

  const logoutUser = () => {
    employeeActions.setUserProfileIdNull();
    setState((prevState) => ({
      ...prevState,
      isForbidden: false,
      userProfile: prevState.userProfile
        ? { ...prevState.userProfile, id: undefined }
        : undefined,
    }));
  };

  const logout = useMutationLogout({
    onSuccess: async () => {
      await logoutUser();
      return router.push(ROUTING.LOGIN);
    },
  });

  const setIsForbidden = (isForbidden: boolean) => {
    setState((prevState) => ({
      ...prevState,
      isForbidden: isForbidden,
    }));
  };

  useEffect(() => {
    const handleRouteChangeComplete = (destinatedUrl: string) => {
      // don't need to handle for the same url
      if (window.location.href.includes(destinatedUrl)) {
        return;
      }

      // reset state if direct from patient detail page;
      if (router.route === ROUTING.PATIENT) {
        patientFileActions.resetStateWhenChangePatient();
      }
      setIsLoading(true);
    };

    const routeChangeComplete = () => {
      setIsLoading(false);
    };

    router.events.on('routeChangeStart', handleRouteChangeComplete);
    router.events.on('routeChangeComplete', routeChangeComplete);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeComplete);
      router.events.off('routeChangeComplete', routeChangeComplete);
    };
  }, [router]);

  // try to hande serverError (error code from backend), the err may include params (error details from backend)
  // if error code is not translated, it will return the original error
  // if error code is translated, it will return the translated error
  const handleErrorCode = (err: IErrorData) => {
    if (!err?.serverError) {
      return err;
    }
    const errorCode = err.serverError as any;

    // handle specify error code
    if (errorCode === ErrorCode.ErrorCode_Patient_Not_Found) {
      router.push(ROUTING.PATIENTS);
      return;
    }

    // check if the error code is translated
    const translatedError = errorCodeT(errorCode);
    if (translatedError === errorCode) {
      // if not translated, return the original error
      return err;
    }

    // if translated, return the translated error
    return {
      ...err,
      serverError: '', // it is translated, so we don't need to show the original error
      message: translatedError,
    } as IErrorData;
  };

  useEffect(() => {
    if (router.asPath.includes('signedOut=true')) {
      logout.mutate({});
    }
  }, [router]);

  const isFirstMountExclude =
    excludeLoadingOnFirstMountPaths.includes(router.pathname) &&
    isFirstMount.current;

  return (
    <Provider timelineTheme={timelineTheme}>
      <OverlayToaster ref={toasterRef} />
      {isLoading && !isFirstMountExclude && (
        <LoadingState shadow={!!state.userProfile} />
      )}
      {(!isLoading || !!state.userProfile) && (
        <ErrorBoundaryWithRouter
          user={state.userProfile}
          Dialog={Dialog}
          Toaster={OverlayToaster}
          apiErrorRedirect={apiErrorRedirect}
          t={errorCodeT}
          errorCodes={ErrorCode}
          redirectCallBack={() => {
            loadUserProfile();
          }}
          processExpectedServerError={handleErrorCode}
        >
          <GlobalContext.Provider
            userProfile={state.userProfile}
            isForbidden={state.isForbidden}
            reloadUserProfile={loadUserProfile}
            toasterRef={toasterRef}
            setGlobalLoading={setIsLoading}
            logoutUser={logoutUser}
            setIsForbidden={setIsForbidden}
          >
            {children({
              hasLoggedIn: !!state.userProfile,
              isForbidden: state.isForbidden,
            })}
          </GlobalContext.Provider>
        </ErrorBoundaryWithRouter>
      )}
    </Provider>
  );
}

export default AppSkeleton;
