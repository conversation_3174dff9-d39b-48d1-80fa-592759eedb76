import { Flex, FormGroup2 } from '@tutum/design-system/components';
import { RangeSlider } from '@tutum/design-system/components/Core';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { RangeNumberItem } from '@tutum/mvz/components/filter-group/FilterGroup';
import { useFormikContext } from 'formik';
import { useEffect, useState } from 'react';

const FilterPriceRange = ({ item }: { item: RangeNumberItem }) => {
  const { setFieldValue, values } = useFormikContext();
  const [left, setLeft] = useState<number>(values?.[item.fieldName1]);
  const [right, setRight] = useState<number>(values?.[item.fieldName2]);

  useEffect(() => {
    setLeft(values?.[item.fieldName1]);
    setRight(values?.[item.fieldName2]);
  }, [values?.[item.fieldName1], values?.[item.fieldName2]]);

  return (
    <FormGroup2 label={item.fieldLabel}>
      <Flex gap={20} align="center">
        <NumberInput
          style={{ width: 85 }}
          isFloat
          fixedDecimalScale
          defaultValue={values?.[item.fieldName1]}
          onValueChange={({ value }) => {
            setFieldValue(item.fieldName1, +value);
          }}
          rightElement={<span className="sl-input-right-element">€</span>}
        />
        <RangeSlider
          labelRenderer={false}
          value={[left, right]}
          min={item.min}
          max={item.max}
          onChange={([minPrice, maxPrice]) => {
            setLeft(+minPrice);
            setRight(+maxPrice);
          }}
          onRelease={([minPrice, maxPrice]) => {
            setFieldValue(item.fieldName1, +minPrice);
            setFieldValue(item.fieldName2, +maxPrice);
          }}
        />
        <NumberInput
          style={{ width: 85 }}
          isFloat
          fixedDecimalScale
          defaultValue={values?.[item.fieldName2]}
          onValueChange={({ value }) => {
            setFieldValue(item.fieldName2, +value);
          }}
          rightElement={<span className="sl-input-right-element">€</span>}
        />
      </Flex>
    </FormGroup2>
  );
};

export default FilterPriceRange;
