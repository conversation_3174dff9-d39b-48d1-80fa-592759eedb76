import {
  FormGroup2,
  IMenuItem,
  MultiSelect,
} from '@tutum/design-system/components';
import { MultipleSelectItem } from '@tutum/mvz/components/filter-group/FilterGroup';
import { useFormikContext } from 'formik';

const FilterMultipleSelect = ({ item }: { item: MultipleSelectItem }) => {
  const { setFieldValue, values } = useFormikContext<{
    [key: string]: (number | string)[],
  }>();
  return (
    <FormGroup2 label={item.fieldLabel}>
      <MultiSelect
        isClearable={false}
        options={item.options}
        value={item.options.filter((e) =>
          values[item.fieldName].includes(e.value)
        )}
        onChange={(newValue: IMenuItem[]) => {
          const tp = newValue.map((e) => e.value);
          setFieldValue(item.fieldName, tp);
        }}
      />
    </FormGroup2>
  );
};

export default FilterMultipleSelect;
