import { BodyTextM, Button, Flex, Svg } from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';
import theme from '@tutum/mvz/theme';
import { Tooltip } from '@tutum/design-system/components/Core';
import { useMemo } from 'react';

const CloseIcon = '/images/close.svg';

const Badge = theme.styled(Button).attrs({
  intent: 'primary',
  small: true,
})`
  padding: 8px !important;
  border-radius: 50% !important;
`;

export interface FilterButtonProps {
  selectedValues: string[];
  srcIcon: string;
  label: string;
  onClick: () => void;
  isSelect: boolean;
  onClear?: () => void;
  bgColor?: string;
}

const FilterButton = ({
  selectedValues,
  srcIcon,
  label,
  onClick,
  isSelect = false,
  onClear,
  bgColor,
}: FilterButtonProps) => {
  const count = useMemo(() => {
    return selectedValues.length;
  }, [selectedValues.length]);

  const rightIcon = useMemo(() => {
    return isSelect && !!count ? (
      <Badge>
        <BodyTextM fontWeight={600} color={COLOR.TEXT_WHITE}>
          {count}
        </BodyTextM>
      </Badge>
    ) : (
      srcIcon && <Svg size={16} src={srcIcon} />
    );
  }, [count, isSelect, srcIcon]);

  const hoverContent = useMemo(() => {
    return isSelect && !!count ? (
      <Flex p={5} column>
        <BodyTextM fontWeight={600}>{label + ':'}</BodyTextM>
        <ul>
          {selectedValues.map((e) => (
            <li key={e}>{e}</li>
          ))}
        </ul>
      </Flex>
    ) : undefined;
  }, [isSelect, count, selectedValues]);

  return (
    <Tooltip
      usePortal={false}
      position="bottom-left"
      minimal
      content={hoverContent}
    >
      <Button
        outlined={!!bgColor || !isSelect}
        style={{
          borderRadius: 16,
          backgroundColor: bgColor
            ? bgColor
            : isSelect
              ? COLOR.BACKGROUND_SELECTED
              : undefined,
        }}
        onClick={onClick}
        rightIcon={
          <>
            {rightIcon}
            {isSelect && (
              <Svg
                size={16}
                onClick={(e) => {
                  e.stopPropagation();
                  onClear?.();
                }}
                src={CloseIcon}
              />
            )}
          </>
        }
      >
        <BodyTextM
          fontWeight={600}
          color={isSelect ? COLOR.TEXT_INFO : COLOR.TEXT_SECONDARY_NAVAL}
        >
          {label}
        </BodyTextM>
      </Button>
    </Tooltip>
  );
};

export default FilterButton;
