import { BodyText<PERSON>, <PERSON>, Button, Flex } from '@tutum/design-system/components';
import { <PERSON><PERSON><PERSON>, Drawer, Popover } from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import FilterCheckBox from '@tutum/mvz/components/filter-group/filter-item/FilterCheckbox';
import FilterMultipleSelect from '@tutum/mvz/components/filter-group/filter-item/FilterMultipleSelect';
import FilterPriceRange from '@tutum/mvz/components/filter-group/filter-item/FilterPriceRange';
import type Common from '@tutum/mvz/locales/en/Common.json';
import { Formik, FormikProps } from 'formik';
import set from 'lodash/set';
import { forwardRef, Ref, useEffect, useMemo, useState } from 'react';
import FilterButton from './FilterButton';
import { transformFormatCurrency } from '@tutum/mvz/_utils/text';

const FilterIcon = '/images/filter.svg';
const ArrowDownIcon = '/images/chevron-down.svg';

export type OptionItem<T> = {
  value: T;
  label: string;
};

export type BasicField = {
  fieldName: string;
  fieldLabel: string;
};

export type MultipleSelectItem<T = any> = {
  type: 'MULTIPLE_SELECT';
  options: OptionItem<T>[];
  value: T[];
} & BasicField;

export type CheckBoxItem<T = any> = {
  type: 'CHECK_BOX';
  options: OptionItem<T>[];
  value: T[];
} & BasicField;

export type RangeNumberItem = {
  type: 'PRICE_RANGE';
  fieldName1: string;
  fieldName2: string;
  fieldLabel: string;
  min: number;
  max: number;
  value: {
    left: number;
    right: number;
  };
};

export type FilterItem =
  | MultipleSelectItem<any>
  | RangeNumberItem
  | CheckBoxItem<any>;

export type FilterGroupProps = {
  items: FilterItem[];
  displayStyle: 'Popover' | 'Drawer';
  className?: string;
  onChange: (value: any) => void;
};

function renderFilterByType(item: FilterItem) {
  switch (item.type) {
    case 'MULTIPLE_SELECT':
      return <FilterMultipleSelect key={item.fieldName} item={item} />;
    case 'CHECK_BOX':
      return <FilterCheckBox key={item.fieldName} item={item} />;
    case 'PRICE_RANGE':
      return <FilterPriceRange key={`${item.fieldName1}-${item.fieldName2}`} item={item} />;
  }
}

interface RenderContentProps {
  values: any;
  displayStyle: 'Popover' | 'Drawer';
  items: FilterItem[];
  setFieldValue: (field: string, value: any) => void;
  resetForm: () => void;
  onChange: (value: any) => void;
}

const RenderContent = ({
  values,
  displayStyle,
  items,
  setFieldValue,
  resetForm,
  onChange,
}: RenderContentProps) => {
  const { t } = I18n.useTranslation<keyof typeof Common.Filter>({
    namespace: 'Common',
    nestedTrans: 'Filter',
  });
  const [isShowFilter, setIsShowFilter] = useState<boolean>(false);

  console.log('debug values', values);

  const selectedFilter = useMemo(() => {
    return items.reduce<string[]>((acc, item) => {
      let isSelect = false;
      switch (item.type) {
        case 'MULTIPLE_SELECT':
        case 'CHECK_BOX':
          isSelect =
            values[item.fieldName].length > 0 &&
            values[item.fieldName].length < item.options.length;
          break;
        case 'PRICE_RANGE':
          isSelect =
            item.value.left !== values[item.fieldName1] ||
            item.value.right !== values[item.fieldName2];
          break;
      }

      if (isSelect) {
        acc.push(item.fieldLabel);
      }

      return acc;
    }, []);
  }, [items, values]);

  const filterButtons = useMemo(() => {
    return items.map((item, idx) => {
      let selectedValues: string[] = [];
      let label = item.fieldLabel;
      let isSelect = false;
      let srcIcon = ArrowDownIcon;
      let onClear = () => { };

      switch (item.type) {
        case 'MULTIPLE_SELECT':
        case 'CHECK_BOX':
          selectedValues = item.options
            .filter((e) => values[item.fieldName].includes(e.value))
            .map((e) => e.label);
          isSelect =
            selectedValues.length > 0 &&
            selectedValues.length < item.options.length;
          onClear = () => {
            setFieldValue(item.fieldName, item.value);
          };
          break;
        case 'PRICE_RANGE':
          selectedValues = values[item.fieldName1];
          isSelect =
            item.value.left !== values[item.fieldName1] ||
            item.value.right !== values[item.fieldName2];
          if (isSelect) {
            srcIcon = '';
          }
          onClear = () => {
            setFieldValue(item.fieldName1, item.value.left);
            setFieldValue(item.fieldName2, item.value.right);
          };
          if (isSelect) {
            label += `: ${transformFormatCurrency(
              values[item.fieldName1]
            )}-${transformFormatCurrency(values[item.fieldName2])}€`;
          }
          break;
      }

      return (
        <Popover
          key={idx}
          usePortal={false}
          position="bottom-left"
          content={
            <Box p={20} minWidth={400}>
              {renderFilterByType(item)}
            </Box>
          }
        >
          <FilterButton
            srcIcon={srcIcon}
            selectedValues={selectedValues}
            label={label}
            isSelect={isSelect}
            onClear={onClear}
            onClick={() => { }}
          />
        </Popover>
      );
    });
  }, [items, values]);

  const filters = useMemo(() => {
    return (
      <Flex gap={16} column mx={20} py={20}>
        <BodyTextL fontWeight={700}>{t('filter')}</BodyTextL>
        <Divider style={{ margin: '0 -20px' }} />
        {items.map(renderFilterByType)}
        <Divider style={{ margin: '0 -20px' }} />
        <Flex justify="flex-end" w="100%">
          <Button intent="primary" fill={false} outlined onClick={resetForm}>
            {t('reset')}
          </Button>
        </Flex>
      </Flex>
    );
  }, [items, resetForm]);

  const filterBtn = useMemo(() => {
    return (
      <FilterButton
        srcIcon={FilterIcon}
        selectedValues={selectedFilter}
        isSelect={selectedFilter.length > 0}
        onClear={resetForm}
        label={t('allFilters')}
        bgColor="white"
        onClick={() => {
          setIsShowFilter((prev) => !prev);
        }}
      />
    );
  }, [selectedFilter, resetForm]);

  useEffect(() => {
    onChange(values);
  }, [values, onChange]);

  return (
    <>
      <Flex gap={8}>
        {displayStyle === 'Popover' ? (
          <Popover
            usePortal={false}
            isOpen={isShowFilter}
            onClose={() => {
              setIsShowFilter(false);
            }}
            content={filters}
          >
            {filterBtn}
          </Popover>
        ) : (
          filterBtn
        )}
        <Divider style={{ height: 'auto', margin: 0 }} />
        {filterButtons}
      </Flex>
      {displayStyle === 'Drawer' && (
        <Drawer
          usePortal={false}
          size="40%"
          isOpen={isShowFilter}
          canEscapeKeyClose={false}
          onClose={() => setIsShowFilter(false)}
        >
          {filters}
        </Drawer>
      )}
    </>
  );
}

const FilterGroup = forwardRef(
  (
    { items, className, displayStyle = 'Drawer', onChange }: FilterGroupProps,
    formikRef: Ref<FormikProps<any>>
  ) => {
    const initialValues = buildInitialValues(items);

    return (
      <Box className={className}>
        <Formik
          initialValues={initialValues}
          enableReinitialize
          innerRef={formikRef}
          onSubmit={() => { }}
        >
          {({ values, setFieldValue, resetForm }) =>
            <RenderContent
              values={values}
              displayStyle={displayStyle}
              items={items}
              setFieldValue={setFieldValue}
              resetForm={resetForm}
              onChange={onChange}
            />
          }
        </Formik>
      </Box>
    );
  }
);

function buildInitialValues(items: FilterItem[]): any {
  const values = {};
  for (const item of items) {
    switch (item.type) {
      case 'MULTIPLE_SELECT':
      case 'CHECK_BOX':
        set(values, item.fieldName, item.value);
        break;
      case 'PRICE_RANGE':
        set(values, item.fieldName1, item.value?.left);
        set(values, item.fieldName2, item.value?.right);
        break;
    }
  }
  return values;
}

export default FilterGroup;
