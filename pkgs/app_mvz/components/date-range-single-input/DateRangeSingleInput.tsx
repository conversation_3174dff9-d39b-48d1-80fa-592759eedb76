import React from 'react';
import _isEqual from 'lodash/isEqual';
import I18n, {
  IFixedNamespaceTFunction,
  II18nFixedNamespace,
} from '@tutum/infrastructure/i18n';
import { LocaleUtils } from 'react-day-picker';
import { InputGroup, Popover } from '@tutum/design-system/components/Core';
import {
  DateRangePicker,
  DateRange,
  DateRangeShortcut,
} from '@tutum/design-system/components/DateTime';
import { Flex, Svg } from '@tutum/design-system/components';
import { IMvzTheme } from '@tutum/mvz/theme';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import moment from 'moment';
// Include the locale utils designed for moment
import MomentLocaleUtils from 'react-day-picker/moment';
import { Keys } from '@tutum/design-system/components/Core';
// Make sure moment.js has the required locale data
import 'moment/locale/de';
import { clone } from 'lodash';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { getQuartersByLength } from '@tutum/mvz/_utils/getQuarters';

export interface IDateRangeSingleInputProps {
  className?: string;
  theme?: IMvzTheme;
  onChange: (startDate: Date | null, endDate: Date | null) => void;
  onBlur?: () => void;
  locale?: string;
  localeUtils?: typeof LocaleUtils;
  disabled?: boolean;
  isCloseCalendarWhenChosen?: boolean;
  defaultStartDate?: Date;
  defaultEndDate?: Date;
  value?: [Date, Date];
  placeholder?: string;
  fill?: boolean;
  isQuarterShortcuts?: boolean;
  quarterLength?: number;
}

export interface IDateRangeSingleInputState {
  isLoading?: boolean;
  startDate: Date | null;
  endDate: Date | null;
  openCalendar: boolean;
}

const CalendarIcon = '/images/calendar-default.svg';

// https://github.com/palantir/blueprint/blob/develop/packages/datetime/src/shortcuts.tsx
// reference the code above to do translation in our side since the original codes does not support localization
function createShortcut(
  label: string,
  dateRange: DateRange
): DateRangeShortcut {
  return { dateRange, label };
}

function createDefaultShortcuts(
  allowSingleDayRange: boolean,
  hasTimePrecision: boolean,
  useSingleDateShortcuts: boolean,
  t: IFixedNamespaceTFunction
) {
  const today = datetimeUtil.date();
  const makeDate = (action: (d: Date) => void) => {
    const returnVal = clone(today);
    action(returnVal);
    returnVal.setDate(returnVal.getDate() + 1);
    return returnVal;
  };

  const tomorrow = makeDate(() => null);
  const yesterday = makeDate((d) => d.setDate(d.getDate() - 2));
  const oneWeekAgo = makeDate((d) => d.setDate(d.getDate() - 7));
  const oneMonthAgo = makeDate((d) => d.setMonth(d.getMonth() - 1));
  const threeMonthsAgo = makeDate((d) => d.setMonth(d.getMonth() - 3));
  const sixMonthsAgo = makeDate((d) => d.setMonth(d.getMonth() - 6));
  const oneYearAgo = makeDate((d) => d.setFullYear(d.getFullYear() - 1));
  const twoYearsAgo = makeDate((d) => d.setFullYear(d.getFullYear() - 2));

  const singleDayShortcuts =
    allowSingleDayRange || useSingleDateShortcuts
      ? [
          createShortcut(t('shortcuts.Today'), [
            today,
            hasTimePrecision ? tomorrow : today,
          ]),
          createShortcut(t('shortcuts.Yesterday'), [
            yesterday,
            hasTimePrecision ? today : yesterday,
          ]),
        ]
      : [];

  return [
    ...singleDayShortcuts,
    createShortcut(
      useSingleDateShortcuts
        ? t('shortcuts.1 week ago')
        : t('shortcuts.Past week'),
      [oneWeekAgo, today]
    ),
    createShortcut(
      useSingleDateShortcuts
        ? t('shortcuts.1 month ago')
        : t('shortcuts.Past month'),
      [oneMonthAgo, today]
    ),
    createShortcut(
      useSingleDateShortcuts
        ? t('shortcuts.3 months ago')
        : t('shortcuts.Past 3 months'),
      [threeMonthsAgo, today]
    ),
    // Don't include a couple of these for the single date shortcut
    ...(useSingleDateShortcuts
      ? []
      : [createShortcut(t('shortcuts.Past 6 months'), [sixMonthsAgo, today])]),
    createShortcut(
      useSingleDateShortcuts
        ? t('shortcuts.1 year ago')
        : t('shortcuts.Past year'),
      [oneYearAgo, today]
    ),
    ...(useSingleDateShortcuts
      ? []
      : [createShortcut(t('shortcuts.Past 2 years'), [twoYearsAgo, today])]),
  ];
}

function createQuarterShortcuts(
  quarterLength: number,
  t: IFixedNamespaceTFunction
) {
  const today = datetimeUtil.date();

  return getQuartersByLength(quarterLength).map((item) => {
    const startDate = datetimeUtil.getStartOfBySelectedQuarter(
      item.quarter,
      item.year,
      false
    );

    return createShortcut(
      t('shortcuts.Quarter', {
        quarter: item.quarter,
        year: item.year,
      }),
      [startDate.toDate(), today]
    );
  });
}

class DateRangeSingleInput extends React.PureComponent<
  IDateRangeSingleInputProps & II18nFixedNamespace<any>,
  IDateRangeSingleInputState
> {
  codeInputRef: HTMLInputElement | null = null;
  bindInputRef = (ref: HTMLInputElement) => {
    this.codeInputRef = ref;
  };

  constructor(props) {
    super(props);
    this.state = {
      startDate: null,
      endDate: null,
      openCalendar: false,
    };
  }

  componentDidMount(): void {
    if (
      this.props.value?.length === 2 &&
      this.props.value.every((v) => v != null)
    ) {
      this.onDateSelected(this.props.value);
    } else if (this.props.defaultStartDate && this.props.defaultEndDate) {
      this.onDateSelected([
        this.props.defaultStartDate,
        this.props.defaultEndDate,
      ]);
    }
  }

  componentDidUpdate(
    prevProps: Readonly<IDateRangeSingleInputProps & II18nFixedNamespace<any>>
  ): void {
    if (
      this.props.value != null &&
      !_isEqual(this.props.value, prevProps.value)
    ) {
      if (
        this.props.value.length === 2 &&
        this.props.value[0] == null &&
        this.props.value[1] == null
      ) {
        this.clearDateSelection();
        return;
      }
      const [startDate, endDate] = this.props.value;
      this.setInputValue([startDate, endDate]);
      this.setState({
        startDate,
        endDate,
      });
    }
  }

  render() {
    const {
      t,
      className,
      locale,
      localeUtils,
      disabled,
      placeholder,
      isQuarterShortcuts,
      quarterLength = 8,
      onBlur,
    } = this.props;

    return (
      <Flex auto className={className} align="center">
        <Flex className="sl-date-range" w="100%">
          <Popover
            content={
              <DateRangePicker
                singleMonthOnly
                allowSingleDayRange
                onChange={this.onDateSelected}
                locale={locale ?? 'de'}
                localeUtils={localeUtils ?? MomentLocaleUtils}
                value={[this.state.startDate, this.state.endDate]}
                shortcuts={
                  isQuarterShortcuts
                    ? createQuarterShortcuts(quarterLength, t)
                    : createDefaultShortcuts(true, false, false, t)
                }
              />
            }
            isOpen={this.state.openCalendar}
            onClose={this.closeCalendar}
            autoFocus={false}
            enforceFocus={false}
            fill={this.props.fill}
          >
            <InputGroup
              onFocus={this.openCalendar}
              // need add this to avoid turn off popover immediately after click input
              onClick={(e) => e.stopPropagation()}
              onChange={this.onChangeInput}
              inputRef={this.bindInputRef}
              onKeyDown={this.onKeyDown}
              disabled={disabled}
              leftElement={
                <Svg
                  src={CalendarIcon}
                  style={
                    disabled
                      ? {
                          pointerEvents: 'none',
                          userSelect: 'none',
                        }
                      : undefined
                  }
                />
              }
              onBlur={onBlur}
              placeholder={placeholder || t('dateRangeInput')}
            />
          </Popover>
        </Flex>
      </Flex>
    );
  }

  openCalendar = () => {
    this.setState({ openCalendar: true });
  };

  closeCalendar = () => {
    this.setState({ openCalendar: false });
  };

  clearDateSelection = () => {
    this.setState({
      startDate: null,
      endDate: null,
    });

    if (this.codeInputRef) {
      this.codeInputRef.value = '';
    }
    this.props.onChange(null, null);
  };

  private setInputValue(selectedDates: DateRange) {
    const [startDate, endDate] = selectedDates;
    // in case both startDate and endDate have same null value input can show invalid date
    const formatted0 = startDate && moment(startDate).format(DATE_FORMAT);
    const formatted1 = endDate && moment(endDate).format(DATE_FORMAT);

    if (!this.codeInputRef) {
      return;
    }

    if (endDate == null) {
      this.codeInputRef.value = formatted0 || '';
    } else if (startDate == null) {
      this.codeInputRef.value = formatted1 || '';
    } else if (moment(startDate).isSame(endDate, 'day')) {
      this.codeInputRef.value = formatted0 || '';
    } else {
      this.codeInputRef.value = `${formatted0} - ${formatted1}`;
    }
  }

  onDateSelected = (selectedDates: DateRange) => {
    if (!selectedDates) {
      return;
    }
    let [, endDate] = selectedDates;
    const startDate = selectedDates[0];
    // NOTE: update display values
    this.setInputValue(selectedDates);

    if (
      startDate != null &&
      endDate != null &&
      startDate.getTime() === endDate.getTime()
    ) {
      endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000 - 1);
    }

    if (this.props.isCloseCalendarWhenChosen && startDate && endDate) {
      this.closeCalendar();
    }

    this.setState({
      startDate,
      endDate,
    });

    this.props.onChange(startDate, endDate);
  };

  onChangeInput: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const dateRangeInput = e.target.value;
    const dateParts = dateRangeInput.split('-');
    let startDate: Date | null = null;
    let endDate: Date | null = null;
    if (dateParts.length === 2) {
      const startDateString = dateParts[0].trim();
      const endDateString = dateParts[1].trim();

      const parsedStartDate = moment(startDateString, DATE_FORMAT, true);
      const parsedEndDate = moment(endDateString, DATE_FORMAT, true);

      startDate = parsedStartDate.isValid() ? parsedStartDate.toDate() : null;
      endDate = parsedEndDate.isValid() ? parsedEndDate.toDate() : null;

      // automatically swap start date and end date if end date is before start date
      if (startDate && endDate && endDate.getTime() <= startDate.getTime()) {
        const temp = startDate;
        startDate = endDate;
        endDate = temp;
        this.setInputValue([startDate, endDate]);
      }
      // remove separator " - " and the last character of startDate if we delete from input value"dd.mm.yyyy - "
      if (startDate && dateRangeInput.endsWith(' -') && this.codeInputRef) {
        this.codeInputRef.value = dateRangeInput.slice(0, -3);
      }
    } else {
      const startDateString = dateParts[0].trim();
      const parsedStartDate = moment(startDateString, DATE_FORMAT, true);
      startDate = parsedStartDate.isValid() ? parsedStartDate.toDate() : null;
      // auto add separator
      if (startDate && this.codeInputRef) {
        this.codeInputRef.value = `${dateParts[0]} - `;
      }
    }

    this.setState({
      startDate,
      endDate,
    });
    this.props.onChange(startDate, endDate);
  };

  onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const isTabPressed = e.which === Keys.TAB;
    const isEnterPressed = e.which === Keys.ENTER;

    if (isTabPressed) {
      this.setState({
        openCalendar: false,
      });
    }

    if (isEnterPressed) {
      this.setState({
        openCalendar: !this.state.openCalendar,
      });
      // this avoid to submit form
      e.preventDefault();
    }
  };
}

export default I18n.withTranslation(DateRangeSingleInput, {
  namespace: 'Common',
  nestedTrans: 'DateTimePicker',
});
