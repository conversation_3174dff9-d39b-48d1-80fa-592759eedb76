import React, { memo, useMemo, useRef, useEffect, useState } from 'react';
import {
  Flex,
  Button,
  Tooltip,
  LoadingState,
  BodyTextM,
  BodyTextS,
} from '@tutum/design-system/components';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import MvzTextmodule from '@tutum/design-system/textmodule';
import { MaxLengthPlugin } from '@tutum/design-system/textmodule/plugins/MaxLength.plugin';
import { FormName } from '@tutum/hermes/bff/form_common';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import { AUTO_FOCUS_ATTRIBUTE } from '@tutum/mvz/constant/custom-attribute';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
  HEIMI_FORM,
} from '@tutum/mvz/constant/form';
import { VALIATED_GROUP_FIELDS_RULE_6 } from '@tutum/mvz/module_form/muster-form-dialog/form-validation/muster12.validation';
import {
  NUMBERIC_FIELDS_FORM_61A,
  NUMBERIC_FIELDS_FORM_61B,
  SMALL_TEXTBOX_FIELDS,
} from '@tutum/mvz/module_form/muster-form-dialog/form-validation/muster61.validation';
import {
  IMusterPrescribe,
  musterFormDialogActions,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import type { LexicalEditor } from 'lexical';
import { HandleMultiLineBackspacePlugin } from '@tutum/design-system/textmodule/plugins/HandleMultiLineBackspacePlugin.plugin';
import { NoBreakLinePlugin } from '@tutum/design-system/textmodule/plugins/NoBreakLine.plugin';
import { SetInstancePlugin } from '@tutum/design-system/textmodule/plugins/SetInstance.plugin';
import I18n from '@tutum/infrastructure/i18n';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import { IMusterFormComponentDateRange } from '../../module_form/muster-form/MusterForm.helper';
import BsnrSelection from './bsnr-selection/BsnrSelection.styled';
import FormCheckBox from './check-box/CheckBox.styled';
import DateFormInput from './date-input/DateFormInput.styled';
import {
  ICustomAnnotationComponent,
  IFormField,
  IFormFieldType,
  IFormInfo,
} from './FormAnnotation.type';
import ToggleNumber from './toogle-number/ToggleNumber.styled';
import {
  useReferralThroughTssStore,
  referralThroughTssActions,
} from '@tutum/mvz/hooks/useReferralThroughTss.store';
import { EHIC_PRF_NR, KBV_PRF_NR } from '@tutum/hermes/bff/app_mvz_form';
import { COLOR } from '@tutum/design-system/themes/styles';
import nameUtils from '@tutum/infrastructure/utils/name.utils';

export interface IRenderAnnotationProps {
  className?: string;
  classNameItem?: string;
  currentFormName?: string;
  disabledBsnr?: boolean;
  formField: IFormField;
  formMetaData?: IFormInfo;
  onChangeEvent: (
    formField: IFormField,
    newVal?: string | number | boolean
  ) => void;
  onChangeBsnr?: (data: Record<string, number | string>) => void;
  componentValue?: FORM_SETTING_TYPE;
  prescribeDate?: number;
  customComponents?: ICustomAnnotationComponent[];
  isViewOnly?: boolean;
  isRefillOnly?: boolean;
  formInfoMap?: FORM_SETTING_OBJECT;
  labelFontSize?: string;
  formImgId?: string;
  page?: number;
  fieldIndex?: number;
  dateFormat?: string;
  doctorStamp?: IEmployeeProfile;
  dateRange?: IMusterFormComponentDateRange;
  musterFormDialogStore?: IMusterPrescribe;
  handleLexical?: Function;
  textModuleInstance?: Record<string, LexicalEditor>;
  formGroupBreakLineInput?: string[][];
  defaultDoctorStampFontSize?: number;
  textFontSize?: string;
  insuranceStatusValue?: string;
}

interface IBadgeErrorProps {
  formField: IFormField;
  componentStyle: React.CSSProperties;
  isSmall?: boolean;
  isHeimiForms?: boolean;
}

export const BadgeError = ({
  formField,
  componentStyle,
  isSmall,
  isHeimiForms,
}: IBadgeErrorProps) => {
  const { hasError, errorIndex, hasWarning, warningIndex } =
    musterFormDialogActions.isFieldHasErrorOrWarning(
      formField.name,
      isHeimiForms
    );

  if (hasError || hasWarning) {
    return (
      <div
        style={{
          background: hasWarning
            ? `${COLOR.TAG_BACKGROUND_YELLOW}`
            : `${COLOR.TAG_BACKGROUND_RED}`,
          borderRadius: '100px',
          position: 'absolute',
          width: isSmall ? 14 : 20,
          height: isSmall ? 14 : 20,
          top: `calc(${componentStyle.top}px - 8px)`,
          left: `calc(${componentStyle.left}px - 8px + ${componentStyle.width}px)`,
          color: 'white',
          zIndex: 2,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: isSmall ? 10 : 'initial',
          pointerEvents: 'none',
        }}
      >
        {hasError ? errorIndex : null}
        {hasWarning ? warningIndex : null}
      </div>
    );
  }
  return null;
};

function setAnnotationCoordinates(
  el: HTMLElement | null,
  autoFocus: boolean,
  borderStyle: string
) {
  if (!el) return;
  if (autoFocus) {
    el.setAttribute(AUTO_FOCUS_ATTRIBUTE, 'true');
  }

  // for case custom component Muster Suggest
  const musterSuggest = el.closest('.muster-suggest');
  const singleValue = musterSuggest
    ? musterSuggest.querySelector('.css-1dimb5e-singleValue')
    : null;

  el.style.opacity =
    musterSuggest && singleValue && singleValue.innerHTML ? '0' : '1';
  el.style.border = borderStyle;
  el.style.width = borderStyle ? '100%' : '';
}

export const calculateTextWidth = (text: string, labelFontSize?: string) => {
  if (text) {
    const element = document.createElement('canvas');
    const context = element.getContext('2d');

    if (!context) {
      return undefined;
    }

    context.font = `${labelFontSize ?? '17px'} Courier New`;
    const width = context.measureText(text).width;
    return width;
    //backup when context not work
    //0.45 is the scale factor
    // return 25 * text.length * 0.45;
  }

  return undefined;
};

export const calculateTextMaxLengthOnInit = (
  componentWidth: number,
  labelFontSize?: string
) => {
  const oneCharacterTextWidth = calculateTextWidth('a', labelFontSize) || 1;
  return Math.floor(componentWidth / oneCharacterTextWidth);
};

// For prefill content case
export const calculateTextPossibleMaxLength = (
  textToMeasure: string,
  componentToMeasure: HTMLElement | null
) => {
  if (componentToMeasure && textToMeasure) {
    const textWidth = calculateTextWidth(textToMeasure) || 0;
    if (textWidth > componentToMeasure.offsetWidth - 35) {
      textToMeasure = textToMeasure.slice(0, -1);
      return calculateTextPossibleMaxLength(textToMeasure, componentToMeasure);
    } else {
      return textToMeasure.length;
    }
  }
};

export const calculateTextPossibleMaxLengthPromise = (
  textToMeasure: string,
  componentNameToMeasure: string
) => {
  return new Promise((resolve) => {
    const waitComponentInterVal = setInterval(() => {
      const componentToMeasure = document.getElementById(
        componentNameToMeasure
      );
      if (componentToMeasure && textToMeasure) {
        const maxLength = calculateTextPossibleMaxLength(
          textToMeasure,
          componentToMeasure
        );
        clearInterval(waitComponentInterVal);
        resolve(maxLength);
      }
    }, 500);
  });
};

export const canThisComponentFitContent = (
  textToMeasure: string,
  componentToMeasure: HTMLElement | null
) => {
  if (componentToMeasure && textToMeasure) {
    const textWidth = calculateTextWidth(textToMeasure) || 0;
    if (textWidth > componentToMeasure.offsetWidth - 35) {
      return false;
    } else {
      return true;
    }
  }
  return true;
};

export const findNextOrPrevElement = (
  currentElementName: string,
  next: boolean,
  breakLineInputs: string[][]
) => {
  const groupInputArray = breakLineInputs.find((item) => {
    const currentElement = item.find(
      (elementName) => elementName === currentElementName
    );
    if (currentElement) return item;
    return null;
  });
  const currentIndex = groupInputArray?.findIndex(
    (item) => item === currentElementName
  );
  if (next) {
    if (currentIndex && currentIndex < (groupInputArray || []).length - 1) {
      return groupInputArray?.[currentIndex + 1];
    }
  } else {
    if (currentIndex && currentIndex !== 0) {
      return groupInputArray?.[currentIndex - 1];
    }
  }
};

const usedForList = [TextModuleUseFor.TextModuleUseFor_Form];

function RenderAnnotation(props: IRenderAnnotationProps & IMvzThemeProps) {
  const {
    className,
    formField,
    formMetaData,
    componentValue,
    customComponents,
    isViewOnly,
    isRefillOnly,
    formImgId,
    page,
    dateFormat,
    classNameItem,
    doctorStamp,
    dateRange,
    currentFormName,
    onChangeEvent,
    onChangeBsnr,
    fieldIndex,
    handleLexical,
    textModuleInstance,
    labelFontSize,
    formInfoMap,
    formGroupBreakLineInput,
    musterFormDialogStore,
    disabledBsnr,
    defaultDoctorStampFontSize = 16,
    textFontSize,
    insuranceStatusValue = '',
  } = props;

  const { t: tReferralThroughTSSDialog } = I18n.useTranslation<
    keyof typeof FormI18n.ReferralThroughTSSDialog
  >({
    namespace: 'Form',
    nestedTrans: 'ReferralThroughTSSDialog',
  });

  const { kvConnectUsername, tssCode, isSendGetTssCode } =
    useReferralThroughTssStore();

  const isHeimiForms = HEIMI_FORM.includes(currentFormName as FormName);
  const isEAUForms = [FormName.Muster_1].includes(currentFormName as FormName);

  const [fontSizeDoctorStamp, setFontSizeDoctorStamp] = useState<number>(
    defaultDoctorStampFontSize
  );

  const doctorStampEle = useRef<HTMLDivElement | null>(null);

  const doctorStampValues = useMemo(() => {
    if (isEAUForms) {
      const treatmentDoctor = musterFormDialogStore?.doctor?.data;

      if (!treatmentDoctor) {
        return [];
      }

      return [
        treatmentDoctor.bsnrName,
        treatmentDoctor.bsnr,
        [treatmentDoctor.bsnrStreet || '', treatmentDoctor.bsnrNumber || ''
        ].join(' ')
          .trim(),
        [treatmentDoctor.bsnrPostCode || '', treatmentDoctor.bsnrCity || '']
          .join(' ')
          .trim(),
        `Tel: ${treatmentDoctor.bsnrPhoneNumber || ''}`,
        `Fax: ${treatmentDoctor.bsnrFaxNumber || ''}`,
        `Email: ${treatmentDoctor.bsnrEmail || ''}`,
        nameUtils.getDoctorName(treatmentDoctor),
        treatmentDoctor.lanr,
      ];
    }
    if (doctorStamp?.doctorStamp || doctorStamp?.bsnrPracticeStamp) {
      const data =
        doctorStamp?.doctorStamp || doctorStamp?.bsnrPracticeStamp || '';
      return data.split('\n');
    }

    return [];
  }, [isEAUForms, doctorStamp?.doctorStamp, doctorStamp?.bsnrPracticeStamp, musterFormDialogStore?.doctor?.data]);

  const { hasError, hasWarning } =
    musterFormDialogActions.isFieldHasErrorOrWarning(
      formField.name,
      isHeimiForms
    );

  const hideError = useMemo(() => {
    return (
      [FormName.Muster_16, FormName.Private].includes(
        currentFormName as FormName
      ) && formField.name === 'label_medication'
    );
  }, [currentFormName, formField.name]);

  const borderStyle = useMemo(() => {
    return hideError || (!hasError && !hasWarning)
      ? ''
      : `3px solid ${hasError ? COLOR.TAG_BACKGROUND_RED : COLOR.TAG_BACKGROUND_YELLOW
      }`;
  }, [hideError, hasError, hasWarning]);

  useEffect(() => {
    setFontSizeDoctorStamp(defaultDoctorStampFontSize);
    musterFormDialogActions.setCurrentMusterFormSetting({
      label_doctor_stamp: doctorStampValues.join('\n'),
    });
  }, [doctorStampValues]);

  useEffect(() => {
    if (doctorStampEle.current) {
      const innerEle = doctorStampEle.current.querySelector(
        '.label_doctor_stamp'
      ) as HTMLElement;
      const heightDoctorStampEle = doctorStampEle.current.offsetHeight;
      const heightInnerEle = innerEle?.offsetHeight || 0;

      if (heightDoctorStampEle <= heightInnerEle) {
        setFontSizeDoctorStamp((prev) => prev - 1);
      }
    }
  }, [doctorStampValues, fontSizeDoctorStamp]);

  const formId = formImgId || 'img-form';
  // more warning condition should go here
  const mainImgForm = document.getElementById(formId);

  const mainWidth = mainImgForm?.offsetWidth || 0;
  const scaleNumber =
    (mainWidth * 1.0) / (formMetaData?.formPages[page || 0].width || 1);

  const pdfjsLib = global['PDFJS'];

  const rect = formField.rect;
  const view = formMetaData?.formPages[page || 0].view || {
    y1: 0,
    y2: 0,
  };
  const mData = pdfjsLib?.Util?.normalizeRect([
    rect.x1,
    view.y2 - rect.y1 + view.y1,
    rect.x2,
    view.y2 - rect.y2 + view.y1,
  ]) || [0, 0, 0, 0];

  const x = mData[0];
  const y = mData[1];
  const w = Math.abs(mData[2] - mData[0]);
  const h = Math.abs(mData[3] - mData[1]);
  const isAutoFocus = fieldIndex === 0;

  // eslint-disable-next-line no-unused-vars
  const componentStyle: React.CSSProperties = {
    position: 'absolute',
    width: w * scaleNumber,
    height: h * scaleNumber,
    top: y * scaleNumber,
    left: x * scaleNumber,
  };

  const maxLength = useMemo(() => {
    if (formField.maxLength) {
      return formField.maxLength;
    }

    if (formField.type === IFormFieldType.AREA_TEXT_BOX) {
      const maxCharPerLine = calculateTextMaxLengthOnInit(
        componentStyle.width as number,
        textFontSize
      );

      return maxCharPerLine * Math.floor((componentStyle.height as number) / 20 - 1);
    }

    if (
      ![
        'textbox_quartal_m',
        'textbox_quartal_yy',
        'textbox_m54_0',
        ...VALIATED_GROUP_FIELDS_RULE_6.map((field) => field.fieldName),
        ...NUMBERIC_FIELDS_FORM_61A,
        ...NUMBERIC_FIELDS_FORM_61B,
        ...SMALL_TEXTBOX_FIELDS,
      ].includes(formField.name)
    ) {
      return calculateTextMaxLengthOnInit(
        componentStyle.width as number,
        textFontSize
      );
    }
  }, [componentStyle.width as number, labelFontSize]);

  const renderFieldLabel = () => {
    if (formInfoMap) {
      // https://youtrack.mediverbund-rz.de/issue/MEDIPVS-2985
      // PRO-3742 [TestLAB Oct/2022][StakeholderFeedback7][Priority = 8 ][Print Preview] Show print preview only pages to fill out
      // only show first page for this form, so we remove page index for patient information label
      if (formInfoMap[formField.name]) {
        return formInfoMap[formField.name];
      } else if (
        Object.getOwnPropertyNames(formInfoMap).includes(
          formField.name.slice(0, -2)
        )
      ) {
        return formInfoMap[formField.name.slice(0, -2)];
      }
      return formInfoMap[formField.name];
    }
    return null;
  };

  const isInvalidFieldsReferralThroughTss = useMemo(() => {
    if (currentFormName !== FormName.Muster_PTV_11A) {
      return false;
    }

    return [
      'checkbox_ambulante',
      'checkbox_diePsychotherapeutischeNight',
      'checkbox_weitervermittlung',
      'checkbox_zeitnahErforderlich',
    ]
      ?.map(
        (fieldName) => musterFormDialogStore?.currentFormSetting?.[fieldName]
      )
      ?.some((isFalse) => !isFalse);
  }, [
    musterFormDialogStore?.currentFormSetting?.checkbox_ambulante,
    musterFormDialogStore?.currentFormSetting
      ?.checkbox_diePsychotherapeutischeNight,
    musterFormDialogStore?.currentFormSetting?.checkbox_weitervermittlung,
    musterFormDialogStore?.currentFormSetting?.checkbox_zeitnahErforderlich,
  ]);

  const allowASVTeamNumber = useMemo(() => {
    return ![FormName.Muster_20A].includes(currentFormName as FormName);
  }, [currentFormName]);

  if (!mainImgForm) {
    return null;
  }

  const renderComponent = (borderStyle: string) => {
    const customComponent = customComponents?.find(
      (item) => item.fieldName === formField.name
    );
    if (customComponent) {
      return (
        <Flex id={formField.name} column w="100%">
          {customComponent?.component(componentStyle, (el) =>
            setAnnotationCoordinates(el, isAutoFocus, borderStyle)
          )}
        </Flex>
      );
    }

    switch (formField.type) {
      case IFormFieldType.CHECK_BOX:
        return (
          <FormCheckBox
            field={formField}
            componentWidth={componentStyle.width}
            componentHeight={componentStyle.height}
            isChecked={componentValue ? Boolean(componentValue) : false}
            onCheck={(field) => onChangeEvent(field)}
            isViewOnly={isViewOnly || formField?.disabled}
            checkboxRef={(el) =>
              setAnnotationCoordinates(el, isAutoFocus, borderStyle)
            }
          />
        );

      case IFormFieldType.TOGGLE_NUMBER:
        return (
          <ToggleNumber
            isViewOnly={isViewOnly || formField?.disabled}
            field={formField}
            componentHeight={componentStyle.height}
            componentWidth={componentStyle.width}
            isChecked={componentValue ? Boolean(componentValue) : false}
            onCheck={(field) => onChangeEvent(field)}
            innerRef={(el) =>
              setAnnotationCoordinates(el, isAutoFocus, borderStyle)
            }
          />
        );

      case IFormFieldType.DATE_PICKER:
        return (
          <DateFormInput
            className={classNameItem}
            isViewOnly={isViewOnly || formField?.disabled}
            isRefillOnly={isRefillOnly}
            field={formField}
            componentHeight={componentStyle.height}
            componentWidth={componentStyle.width}
            onChange={(newDate) => onChangeEvent(formField, newDate)}
            dateValue={+(componentValue || 0)}
            dateFormat={formField?.dateFormat || dateFormat}
            dateRange={dateRange}
            inputRef={(el) =>
              setAnnotationCoordinates(el, isAutoFocus, borderStyle)
            }
            disabled={!!formField?.disabled}
            timeDayType={formField?.timeDayType}
          />
        );

      case IFormFieldType.AREA_TEXT_BOX:
        return (
          <div
            className="lexical-wrapper-area"
            id={formField.name}
            style={{ border: borderStyle }}
          >
            <MvzTextmodule
              autoFocus={false}
              usedForList={usedForList}
              value={componentValue?.toString()}
              className="input-lexical"
              placeholder="---"
              onContentChange={({ text }) => {
                onChangeEvent(formField, text);
              }}
              isViewOnly={isViewOnly || formField?.disabled}
              isNeedCombineParagraph={true}
            >
              <MaxLengthPlugin
                maxLength={maxLength || Number.MAX_SAFE_INTEGER}
              />
              <SetInstancePlugin
                formField={formField}
                handleLexical={handleLexical}
              />
            </MvzTextmodule>
          </div>
        );

      case IFormFieldType.TEXT_BOX: {
        if (formField?.numeric) {
          return (
            <NumberInput
              isRaw={!formField?.isFloat}
              isFloat={formField?.isFloat}
              className={getCssClass(
                classNameItem,
                isViewOnly || formField?.disabled ? 'sl-view-mode' : null
              )}
              maxLength={maxLength || undefined}
              defaultValue={componentValue as string}
              fill
              disabled={isViewOnly || formField?.disabled}
              onValueChange={({ value }) => {
                let result =
                  value.toString() !== ''
                    ? formField?.allowLeadingZeros
                      ? value.toString()
                      : +value
                    : '';
                // format float number value to show on pdf
                if (formField.isFloat) result = result.toLocaleString('de-DE');
                onChangeEvent(formField, result);
              }}
              id={formField.name}
              getInputRef={(el: HTMLElement) =>
                setAnnotationCoordinates(el, isAutoFocus, borderStyle)
              }
              style={{
                height: componentStyle.height,
                background: 'transparent',
                ...formField.style,
              }}
            />
          );
        }

        const isIcdChecked =
          currentFormName !== FormName.Muster_PTV_11A ||
          ([
            'textbox_icd10_code1',
            'textbox_icd10_code2',
            'textbox_icd10_code3',
          ].includes(formField.name) &&
            musterFormDialogStore?.currentFormSetting?.[
            'checkbox_beiIhnenWurdenFolgende'
            ]);

        return (
          <div
            className="lexical-wrapper"
            id={formField.name}
            style={{ border: borderStyle }}
          >
            <MvzTextmodule
              autoFocus={false}
              isViewOnly={isViewOnly || formField?.disabled}
              shouldStopTabPropagation
              isInlineQuestionnaire
              usedForList={usedForList}
              value={isIcdChecked ? componentValue?.toString() : ''}
              className="sl-textmodule-contenteditable"
              placeholder="---"
              onContentChange={({ text, editor, hasSelectFormReactSelect }) => {
                onChangeEvent(formField, text);
                if (!editor?._updates.length) return;
                if (isViewOnly || formField?.disabled) return;
                const componentToMeasure = document.getElementById(
                  formField.name
                );
                const possibleTextMaxLength = calculateTextPossibleMaxLength(
                  text,
                  componentToMeasure
                );
                const canFit = canThisComponentFitContent(
                  text,
                  componentToMeasure
                );

                // If still can fit, update value
                if (canFit) {
                  return onChangeEvent(formField, text);
                } else {
                  // If don't have next element, just update value
                  if (hasSelectFormReactSelect) {
                    text = text.slice(0, possibleTextMaxLength);
                  }
                  return onChangeEvent(formField, text);
                }
              }}
              isNeedCombineParagraph={true}
            >
              <NoBreakLinePlugin />
              <MaxLengthPlugin
                maxLength={maxLength || Number.MAX_SAFE_INTEGER}
              />
              <HandleMultiLineBackspacePlugin
                formField={formField}
                textModuleInstance={textModuleInstance}
                formGroupBreakLineInput={formGroupBreakLineInput}
              />
              <SetInstancePlugin
                formField={formField}
                handleLexical={handleLexical}
              />
            </MvzTextmodule>
          </div>
        );
      }

      case IFormFieldType.LABEL: {
        if (formField?.name?.includes('label_doctor_stamp')) {
          return (
            <div
              id={formField?.name}
              ref={doctorStampEle}
              style={{
                display: 'inline-block',
                padding: '10px',
              }}
            >
              <div
                className="label_doctor_stamp"
                style={{ fontSize: fontSizeDoctorStamp, lineHeight: 1.2 }}
              >
                {doctorStampValues.map((value, index) => (
                  <div key={index}>{value}</div>
                ))}
              </div>
            </div>
          );
        } else if (formField?.name?.includes(KBV_PRF_NR.KBV_PRF_LABEL)) {
          // render value from source of true
          return (
            <p id={formField.name}>{formInfoMap?.[KBV_PRF_NR.KBV_PRF_LABEL]}</p>
          );
        } else if (formField?.name?.includes(EHIC_PRF_NR.EHIC_PRF_LABEL)) {
          return (
            <p id={formField.name}>
              {formInfoMap?.[EHIC_PRF_NR.EHIC_PRF_LABEL]}
            </p>
          );
        }

        if (
          formField?.name.includes('label_bsnr') &&
          !disabledBsnr &&
          allowASVTeamNumber
        ) {
          return (
            <BsnrSelection
              isViewOnly={!!isViewOnly || !!formField?.disabled}
              fieldName={formField?.name}
              formInfoMap={formInfoMap}
              insuranceStatusValue={insuranceStatusValue}
              onChangeBsnr={onChangeBsnr}
              currentDoctor={doctorStamp}
            />
          );
        }

        if (
          !isViewOnly &&
          formField?.name?.includes('label_referral_through_tss')
        ) {
          let tooltipContent: any = null;
          if (!kvConnectUsername) {
            tooltipContent =
              tReferralThroughTSSDialog('missingKVConnectTooltip') || null;
          } else if (isInvalidFieldsReferralThroughTss) {
            tooltipContent = (
              <Flex column>
                <BodyTextM color={COLOR.BACKGROUND_PRIMARY_WHITE}>
                  {tReferralThroughTSSDialog('tooltipDescription_1')}
                </BodyTextM>
                <BodyTextS
                  color={COLOR.TEXT_TERTIARY_SILVER}
                  whiteSpace="pre-wrap"
                >
                  {tReferralThroughTSSDialog('tooltipDescription_2')}
                </BodyTextS>
              </Flex>
            );
          }

          return (
            <>
              {!tssCode ? (
                <Tooltip content={tooltipContent} placement="right">
                  {isSendGetTssCode ? (
                    <Button
                      outlined
                      disabled={true}
                      className="btn-referral-loading"
                    >
                      <LoadingState
                        size={15}
                        className="icon-wait"
                        height="unset"
                        width="unset"
                        border={'2px'}
                      />
                      {tReferralThroughTSSDialog('title')}
                    </Button>
                  ) : (
                    <Button
                      intent="primary"
                      outlined
                      onClick={() =>
                        referralThroughTssActions.setIsOpenReferralDialog(true)
                      }
                      disabled={
                        kvConnectUsername === '' ||
                        isInvalidFieldsReferralThroughTss
                      }
                    >
                      {tReferralThroughTSSDialog('title')}
                    </Button>
                  )}
                </Tooltip>
              ) : null}
            </>
          );
        }

        return (
          <div
            id={formField.name}
            className={`sl-label-display ${formField.name}`}
            style={{ width: borderStyle ? '100%' : '', border: borderStyle }}
          >
            {renderFieldLabel()}
          </div>
        );
      }
      case IFormFieldType.PICTURE:
        return null;

      default:
        return null;
    }
  };

  return (
    <>
      {!hideError && (
        <BadgeError
          formField={formField}
          componentStyle={componentStyle}
          isSmall={[
            FormName.BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1,
          ].includes(currentFormName as FormName)}
          isHeimiForms={isHeimiForms}
        />
      )}
      <Flex
        className={getCssClass(className, {
          'patient-header': ['label_bsnr', 'date_prescribe'].some((name) =>
            formField.name.includes(name)
          ),
        })}
        style={componentStyle}
      >
        {renderComponent(borderStyle)}
      </Flex>
    </>
  );
}

export default memo(RenderAnnotation);
