import React from 'react';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import FormLocales from '@tutum/mvz/locales/en/Form.json';
import { Avatar, BodyTextM, Flex } from '@tutum/design-system/components';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { MedicationPrescribeResponse } from '@tutum/hermes/bff/service_medicine';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { buildContent } from '@tutum/mvz/module_medication_kbv/medication-print-preview/medication-info/MedicationInfo';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { MedicineShoppingBagInfo } from '@tutum/hermes/bff/medicine_common';

export const genColumns = ({
  t,
  tMedicine,
  doctorList,
}: {
  t: IFixedNamespaceTFunction<
    keyof typeof FormLocales.SelectMedicinesDialog.Table
  >;
  tMedicine: IFixedNamespaceTFunction<
    keyof typeof MedicationI18n.RenderAnnotation
  >;
  doctorList: IEmployeeProfile[];
}): IDataTableColumn<MedicationPrescribeResponse>[] => [
  {
    id: 'prescribed',
    name: t('prescribed'),
    width: '128px',
    cell: (row) => (
      <Flex column>
        <BodyTextM>
          {DatetimeUtil.dateTimeFormat(row.prescribeDate, DATE_FORMAT)}
        </BodyTextM>
        <BodyTextM>
          {DatetimeUtil.hourMinTimeFormat(row.prescribeDate)}
        </BodyTextM>
      </Flex>
    ),
  },
  {
    id: 'medication',
    name: t('medication'),
    width: '450px',
    cell: (row) => {
      return (
        <Flex column w="100%">
          {buildContent(row as unknown as MedicineShoppingBagInfo, tMedicine)}
        </Flex>
      );
    },
  },
  {
    id: 'prescribedBy',
    name: t('prescribedBy'),
    width: '188px',
    cell: (row) => {
      const doctorInfo = doctorList.find(
        (doctor) => doctor.id === row.treatmentDoctorId
      );

      return (
        <Flex gap={8} align="center">
          <Avatar initial={doctorInfo?.initial!} />
          <Flex>{nameUtils.getDoctorName(doctorInfo)}</Flex>
        </Flex>
      );
    },
  },
];
