import React, { useEffect, useState } from 'react';
import { styled } from '@tutum/design-system/themes';

import I18n from '@tutum/infrastructure/i18n';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  Tooltip,
  Svg,
  Flex,
  BodyTextS,
  BodyTextM,
  Tag,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  InsuranceStatus,
  PatientType,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import {
  ScheinStatus,
  ScheinItem,
  MainGroup,
} from '@tutum/hermes/bff/schein_common';
import { InsuranceInfo } from '@tutum/hermes/bff/patient_profile_common';
import stringUtils from '@tutum/infrastructure/utils/string.util';
import catalogJson from '@tutum/mvz/public/data/catalogs_data.json';
import {
  formatUnixToDateString,
  getCssClass,
} from '@tutum/design-system/infrastructure/utils';
import wopTypes from '@tutum/mvz/types/wop.type';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import {
  formatScheinItemLabel,
  mappingScheinIcon,
  getSubTitleScheinItemLabel,
  backgroundMainGroup,
  colorMainGroup,
  checkIsPrivateSchein,
  checkIsSvSchein,
  getSubgroupKVSchein,
  checkIsBgSchein,
  checkIfShowSubTitleScheinItemLabel,
} from '@tutum/mvz/_utils/scheinFormat';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

const mitgliedIcon = '/images/insurance/mitglied.svg';
const familienmitgliedIcon = '/images/insurance/familienmitglied.svg';
const rentnerIcon = '/images/insurance/rentner.svg';
const userIcon = '/images/patient/selector/special-group.svg';
const kvRegionIcon = '/images/patient/selector/kv-region.svg';
const coPaymentIcon = '/images/patient/selector/co-payment.svg';
const coPaymentExpiredIcon = '/images/patient/selector/co-payment-expired.svg';
const patientReceiptIcon = '/images/patient/selector/patient-receipt.svg';
const longTermIcon = '/images/patient/selector/long-term-approval.svg';
const readCardIcon = '/images/patient/selector/read-card-icon.svg';
const checkCircleIcon = '/images/check-circle-solid-success.svg';
const xCircleIcon = '/images/x-circle-solid.svg';
const markNotBilledIcon = '/images/patient/selector/mark-not-billed.svg';
const FocusIcon = '/images/focus.svg';
const FocusingIcon = '/images/focus-white.svg';
const devicePhonePhoneIcon = '/images/device-phone-phone.svg';
const faxIcon = '/images/fax.svg';
const receipt = '/images/receipt-warning-solid.svg';

const heightIcon = 16;
const weightIcon = 16;
const EditIcon = '/images/edit-2.svg';

interface QuarterSectionProp {
  $isShow: boolean;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  .divide {
    line-height: 15px;
  }
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
`;

const InfoScheinAndInsurance = styled.div`
  display: flex;
  padding: 0 0 8px 0;
  gap: 8px;
  flex-direction: column;
`;

const QuarterSection = styled.div<QuarterSectionProp>`
  display: ${(p) => (p.$isShow ? 'flex' : 'none')};
  gap: 8px;
  margin-bottom: 4px;
  align-items: flex-start;
  padding: 4px 8px;
  font-size: 11px;
  font-style: normal;
  font-weight: 600;
  background-color: ${COLOR.BACKGROUND_SECONDARY_SHINE};
  color: ${COLOR.TEXT_SECONDARY_NAVAL};
  align-self: stretch;
`;

const InsuranceSection = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0 12px 4px 17px;
  margin-top: 1px;
  border-radius: 6px;
  &::before {
    content: '';
    display: block;
    position: absolute;
    top: 3px;
    left: 7px;
    width: 2px;
    height: 91%;
    background-color: ${COLOR.BACKGROUND_SELECTED_STRONG};
  }
  .sl-edit-icon {
    display: none;
  }
  &:hover {
    background-color: ${COLOR.BACKGROUND_HOVER};
    .sl-edit-icon {
      display: inline-block;
    }
  }

  .insurance-info-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px 0 0 0;
  }
  .insurance-name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    p {
      flex: 0 0 94%;
    }
    img {
      flex: 0 0 6%;
    }
  }
  .insurance-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .contact-info {
    display: flex;
    width: 100%;
    gap: 4px;
    align-items: center;
    color: ${COLOR.TEXT_SECONDARY_NAVAL};
  }

  .insurance-infor {
    display: flex;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    color: ${COLOR.TEXT_PRIMARY_BLACK};

    span.bp5-popover-target {
      display: flex;
      align-items: center;
    }
  }

  .icon-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .bp5-popover-target {
      display: flex;
      margin: 0;
      justify-content: center;
    }

    .special-group {
      display: flex;
      align-items: center;
    }

    .kv-region {
      display: flex;
      align-items: center;
    }

    .textIcon {
      font-size: 13px;
      font-style: normal;
      font-weight: 600;
      color: ${COLOR.TEXT_PRIMARY_BLACK};
      margin-left: 2px;
      line-height: 16px;
    }
  }

  .insurance-expired {
    margin-left: 4px;
    line-height: 16px;
    color: ${COLOR.TEXT_NEGATIVE};
  }
`;

const ScheinContent = styled.div`
  &.selected {
    background-color: ${COLOR.INFO_SECONDARY_PRESSED};
    border: 1px solid ${COLOR.BACKGROUND_SELECTED_STRONG};
    border-radius: 4px;
  }
  .option {
    .bp5-popover-target {
      display: flex;
    }
  }
`;

const Schein = styled.div`
  display: flex;
  cursor: pointer;
  flex-direction: column;

  .divide-section {
    height: auto;
    min-width: 2px;
    background-color: ${COLOR.BACKGROUND_SELECTED_STRONG};
  }

  .schein-content {
    display: flex;
    gap: 4px;
    padding: 8px;
    width: 100%;
    justify-content: space-between;
    min-height: 40px;
  }
  .schein-content.selected {
    background-color: ${COLOR.INFO_SECONDARY_PRESSED};
    border: 1px solid ${COLOR.BACKGROUND_SELECTED_STRONG};
    border-radius: 4px;
  }

  .section-left {
    display: flex;
    align-items: center;
    .bp5-popover-target {
      height: 100%;
    }
  }

  .content-subgroup {
    display: flex;
    align-items: center;
    margin-right: 4px;
  }

  .content-subgroup-billed {
    display: flex;
    align-items: center;
  }

  .mark-billed-icon {
    margin-right: 6px;
  }

  .main-group {
    border-radius: 4px;
    justify-content: center;
    align-items: baseline;
    display: flex;
    border: 1px solid rgba(19, 50, 75, 0.1);
    padding: 2px;
    margin-right: 4px;
    font-size: 11px;
    font-weight: 600;
    flex-shrink: 0;
    min-width: 24px;
    line-height: 18px;
  }

  .title-schein-billed {
    margin-right: 4px;
    color: ${COLOR.TEXT_TERTIARY_SILVER};
    display: flex;
    align-items: center;
  }
  .title-schein {
    margin-right: 4px;
    display: flex;
    align-items: center;
    .schein-label {
      font-size: 13px;
      color: ${COLOR.TEXT_PRIMARY_BLACK};
      margin-right: 4px;
    }
    .selected-schein-label {
      font-size: 13px;
      color: ${COLOR.TEXT_PRIMARY_BLACK};
      font-weight: 600;
      margin-right: 4px;
    }
    .schein-sub-label {
      margin-top: 2px;
      font-size: 11px;
      min-width: 24px;
      color: ${COLOR.TEXT_SECONDARY_NAVAL};
    }
    .referral {
      display: flex;
      height: 20px;
      padding: 2px 8px;
      justify-content: center;
      align-items: center;
      background: transparent !important;
      border: 1px solid rgba(19, 50, 75, 0.1);
      span {
        color: ${COLOR.TAG_CONTENT_BLUE} !important;
      }
    }
  }

  .bp5-popover-target {
    display: flex;
    flex-shrink: 0;
  }

  .sl-tag-ref-code {
    background: ${COLOR.INFO_SECONDARY_PRESSED};
    padding: 2px 8px;
    border: 1px solid ${COLOR.BACKGROUND_SELECTED_STRONG};
    border-radius: 12px;
    font-weight: 600;
    color: ${COLOR.BACKGROUND_SELECTED_STRONG};
    font-size: 11px;
    cursor: default;
    line-height: 16px;
  }

  .read-card-icon {
    margin-top: 2px;
  }

  .bp5-popover-target {
    align-items: center;
    height: 24px;
  }

  .schein-focusing {
    padding: 4px;
    background-color: ${COLOR.TAG_BACKGROUND_PURPLE};
    border-radius: 4px;
  }
`;

const NoSchein = styled.div`
  display: flex;
  border: 1px dashed ${COLOR.BACKGROUND_TERTIARY_DIM};
  justify-content: center;
  align-items: center;
  padding: 8px;
  gap: 4px;
  border-radius: 4px;
  font-size: 13px;

  .content-no-schein {
    color: ${COLOR.TEXT_TERTIARY_SILVER};
    font-weight: 400;
    font-style: normal;
  }

  .btn-create-schein {
    cursor: pointer;
    color: ${COLOR.BACKGROUND_SELECTED_STRONG};
    font-weight: 600;
    line-height: 20px;
  }
`;

export type QuarterGroupKey = {
  quarter: number;
  year: number;
};

export type GroupScheinItem = {
  scheinItem: ScheinItem;
  insurance: InsuranceInfo;
};

export type QuarterGroup = {
  groupKey: QuarterGroupKey;
  groupValue: GroupScheinItem[];
};

interface IScheinDetailProps {
  listScheinQuarters: QuarterGroup[];
  className?: string;
  isReadOnly?: boolean;
  isNeedActiveSchein: boolean;
  openCreateSchein: (scheinId?: string) => void;
  setScheinIdToDelete: (scheinId: string) => void;
  menuItem: (selectedSchein: ScheinItem) => React.JSX.Element;
  onSelectSchein?: (selectedSchein: ScheinItem) => void;
  onConfirmMarkNotBilled?: (scheinItem: ScheinItem) => void;
  onCloseMarkNotBilled?: () => void;
  scheinSelected: ScheinItem;
  onOpenInsuranceModal: (
    isOpenEdit: boolean,
    isPrivateSchein: boolean,
    insuranceId: string
  ) => void;
}
const ScheinDetail = (props: IScheinDetailProps) => {
  const t2 = I18n.useTranslation<any>({
    namespace: 'Schein',
  }).t;

  const { listScheinQuarters, onOpenInsuranceModal } = props;
  const [scheinSelectedState, setScheinSelected] = useState<ScheinItem>(
    props.scheinSelected
  );
  const [scheinHover, setScheinHover] = useState('');
  const patientFileStore = usePatientFileStore();

  const openInsuranceModal = (
    insuranceId: string,
    isPrivateSchein: boolean
  ) => {
    onOpenInsuranceModal(true, isPrivateSchein, insuranceId);
  };

  const contentTooltipInsurance = (
    schein: ScheinItem,
    insuranceInfo: InsuranceInfo
  ) => {
    if (!insuranceInfo) {
      return null;
    }

    const textIkNumber =
      insuranceInfo.ikNumber !== 0
        ? t2('generalInfo.ikNumber', { ikNumber: insuranceInfo.ikNumber })
        : null!;

    const content: string[] = [];
    if (
      insuranceInfo.insuranceNumber &&
      insuranceInfo.insuranceNumber !== '' &&
      insuranceInfo.endDate
    ) {
      content.push(
        insuranceInfo.insuranceNumber,
        textIkNumber,
        t2('generalInfo.insuranceDate', {
          endDate: formatUnixToDateString(insuranceInfo.endDate),
        })
      );
    } else if (
      insuranceInfo.insuranceNumber &&
      insuranceInfo.insuranceNumber !== '' &&
      !insuranceInfo.endDate
    ) {
      content.push(insuranceInfo.insuranceNumber, textIkNumber);
    } else if (
      (!insuranceInfo.insuranceNumber ||
        insuranceInfo.insuranceNumber === '') &&
      insuranceInfo.endDate
    ) {
      content.push(
        textIkNumber,
        t2('generalInfo.insuranceDate', {
          endDate: formatUnixToDateString(insuranceInfo.endDate),
        })
      );
    } else {
      content.push(textIkNumber);
    }
    const hasTelOrFax = !!insuranceInfo.tel || !!insuranceInfo.fax;
    const getInsuranceCompanyName = () => {
      const name = insuranceInfo.insuranceCompanyName;
      const moduleName = getInsuranceChargeSystemName();
      return name + moduleName;
    };
    // AWH_01 is the main contract of AWH
    // AWH_01, AOK_BW_BVKJ, AOK_BW_IV_P are the modules contract of AWH_01
    const getInsuranceChargeSystemName = () => {
      if (schein.hzvContractId !== 'AWH_01') return '';
      const moduleContractName = {
        AWH_01: '(Hauptvertrag)',
        AOK_BW_BVKJ: '(BVKJ)',
        AOK_BW_IV_P: '(IVP)',
      };
      return ' ' + moduleContractName[schein.chargeSystemId!] || '';
    };

    const isEditableInsurance = (schein: ScheinItem) => {
      if (
        !patientFileStore.patient.current ||
        !patientFileStore.patient.current.patientInfo
      ) {
        return false;
      }

      const patientInfo = patientFileStore.patient.current.patientInfo;
      if (
        schein.scheinMainGroup === MainGroup.KV &&
        patientInfo.genericInfo.patientType === PatientType.PatientType_Private
      ) {
        return false;
      }

      return true;
    };

    return (
      <div className="insurance-info-wrapper">
        <div className="insurance-name">
          <p>{getInsuranceCompanyName()}</p>
          {schein.scheinMainGroup !== MainGroup.BG &&
            isEditableInsurance(schein) && (
              <Svg
                src={EditIcon}
                className="sl-edit-icon"
                onClick={() =>
                  openInsuranceModal(
                    insuranceInfo.id,
                    checkIsPrivateSchein(schein)
                  )
                }
              />
            )}
        </div>
        <div className="insurance-info">
          <BodyTextS>{content.filter((item) => !!item).join(' • ')}</BodyTextS>
          {hasTelOrFax && (
            <Tooltip content={t2('generalInfo.contactHint')}>
              <div className="contact-info">
                {!!insuranceInfo.tel && (
                  <>
                    <Svg src={devicePhonePhoneIcon} />
                    <BodyTextS>Tel: {insuranceInfo.tel}</BodyTextS>
                  </>
                )}
                {!!insuranceInfo.tel && !!insuranceInfo.fax && (
                  <BodyTextS>{' • '}</BodyTextS>
                )}
                {!!insuranceInfo.fax && (
                  <>
                    <Svg src={faxIcon} />
                    <BodyTextS>Fax: {insuranceInfo.fax}</BodyTextS>
                  </>
                )}
              </div>
            </Tooltip>
          )}
        </div>
      </div>
    );
  };

  const contentTooltipInsuranceStatus = (insuranceStatus: InsuranceStatus) => {
    switch (insuranceStatus) {
      case InsuranceStatus.Familienmitglied:
        return t2('generalInfo.familienmitglied');
      case InsuranceStatus.Rentner:
        return t2('generalInfo.rentner/in');
      case InsuranceStatus.Mitglied:
        return t2('generalInfo.mitglied');
      default:
        return '';
    }
  };

  const contentTooltipKvRegion = (kvRegion: string) => {
    const valueWop = wopTypes.WOP_LIST.find((item) => item.value === kvRegion);
    return (
      <Flex column>
        <div style={{ fontWeight: '600' }}>
          {t2('generalInfo.kvRegion', { kvRegion: kvRegion })}
        </div>
        {!!valueWop && <div>{valueWop?.name}</div>}
      </Flex>
    );
  };

  const contentTooltipPatientReceipt = () => {
    return t2('generalInfo.patientReceipt');
  };

  const contentTooltipSpecialGroup = (specialGroup: string) => {
    const catalogSpecialGroup = catalogJson['g4131'];
    return (
      <Flex column>
        <div style={{ fontWeight: '600' }}>
          {t2('generalInfo.specialGroup', { specialGroup: specialGroup })}
        </div>
        <div>{catalogSpecialGroup[specialGroup]}</div>
      </Flex>
    );
  };

  const contentTooltipCoPayment = (coPaymentDate: number) => {
    const now = datetimeUtil.now();
    if (coPaymentDate && coPaymentDate < now) {
      return t2('generalInfo.coPaymentExpired');
    }
    return t2('generalInfo.coPayment', {
      date: formatUnixToDateString(coPaymentDate),
    });
  };

  const statusInsurance = (insuranceStatus: InsuranceStatus) => {
    if (insuranceStatus === InsuranceStatus.Familienmitglied)
      return familienmitgliedIcon;
    else if (insuranceStatus === InsuranceStatus.Rentner) return rentnerIcon;
    return mitgliedIcon;
  };

  const getScheinTitle = (scheinItem: ScheinItem) => {
    if (checkIsSvSchein(scheinItem)) {
      return `${scheinItem.scheinMainGroup}`;
    }

    if (checkIsPrivateSchein(scheinItem)) {
      return `${t2(
        `scheinBlock.${scheinItem.scheinMainGroup}`
      )} (${formatUnixToDateString(scheinItem.issueDate)})`;
    }

    if (checkIsBgSchein(scheinItem)) {
      return `${t2(`scheinBlock.${scheinItem.scheinMainGroup}`)}`;
    }

    const kvTreatmentCase = t2(
      `generalInfo.kvTreatmentCaseValues.${scheinItem?.kvTreatmentCase}`
    );
    return `${kvTreatmentCase} (${scheinItem?.kvScheinSubGroup})`;
  };

  const getScheinTssTag = (scheinItem: ScheinItem) => {
    const tsvgContactType = catalogJson['tsvgContactType'];
    return stringUtils.contactTypeTerse(
      tsvgContactType[scheinItem?.tsvgContactType || '']
    );
  };

  const getTssContentToolTip = (scheinItem: ScheinItem) => {
    const tsvgContactType = catalogJson['tsvgContactType'];
    return tsvgContactType[scheinItem?.tsvgContactType || '01'];
  };

  const selectSchein = (schein: ScheinItem) => {
    props?.onSelectSchein?.(schein);
  };

  const onCopyInvoiceNumber = (invoiceNumber: string) => {
    navigator.clipboard.writeText(invoiceNumber);
    alertSuccessfully(t2('copySuccess'));
  };

  const noSchein = (
    <NoSchein>
      <div className="content-no-schein no-schein">
        {t2('scheinBlock.noScheinCreated')}
      </div>
      <div
        className="btn-create-schein create-schein"
        onClick={() => props.openCreateSchein()}
      >
        {t2('scheinBlock.createSchein')}
      </div>
    </NoSchein>
  );

  const shouldShowQuarterGroup = (quarterGroup: QuarterGroup): boolean => {
    if (!quarterGroup.groupValue?.length) return false;

    const notBilledScheinMap = new Map<string, boolean>();

    for (let i = 0; i < quarterGroup.groupValue.length; i++) {
      const scheinGroup = quarterGroup.groupValue[i];
      const scheinItem = scheinGroup.scheinItem;
      if (scheinItem && !scheinItem.markedAsBilled) {
        notBilledScheinMap.set(scheinItem.scheinId, true);
      }
    }
    return props.isNeedActiveSchein ? notBilledScheinMap.size > 0 : true;
  };

  const renderContentNotBilled = (
    schein: ScheinItem,
    insuarance: InsuranceInfo
  ) => {
    if (!schein) return null;
    const isPublicInsurance =
      !!insuarance &&
      [TypeOfInsurance.Public].includes(insuarance.insuranceType);
    const notBilledScheins = !schein.markedAsBilled;
    const isSelected = scheinSelectedState?.scheinId === schein.scheinId;

    return (
      <>
        {notBilledScheins ? (
          <>
            <Schein
              onClick={() => {
                setScheinSelected(schein);
                selectSchein(schein);
                patientFileActions.schein.setFocusMode(false);
              }}
              className={`selected-schein selected-schein-${schein.scheinId}`}
            >
              {isSelected && <div className="divide-section"></div>}
              <ScheinContent
                data-test-id={schein.scheinId}
                id={schein.scheinId}
                className={`schein-content ${isSelected ? 'selected' : ''}`}
              >
                <div className="section-left">
                  <div className="content-subgroup">
                    <div
                      className="main-group"
                      style={{
                        backgroundColor: backgroundMainGroup(
                          schein.scheinMainGroup
                        ),
                        color: colorMainGroup(schein.scheinMainGroup),
                      }}
                    >
                      {`${mappingScheinIcon(
                        schein.scheinMainGroup
                      )} ${getSubgroupKVSchein(schein)}`}
                    </div>
                    <div className="title-schein">
                      <BodyTextM
                        className={
                          isSelected ? 'selected-schein-label' : 'schein-label'
                        }
                      >
                        {`${formatScheinItemLabel(t2, schein)} `}
                      </BodyTextM>
                      {schein.referralDoctor && (
                        <Tag className="referral">
                          {t2('scheinOverview.referralDoctorSchein')}
                        </Tag>
                      )}
                      {checkIfShowSubTitleScheinItemLabel(schein) &&
                        !!getSubTitleScheinItemLabel(schein) && (
                          <div className="schein-sub-label">
                            {getSubTitleScheinItemLabel(schein)}
                          </div>
                        )}
                    </div>
                  </div>
                  {schein?.tsvgContactType && (
                    <Tooltip
                      content={getTssContentToolTip(schein)}
                      position="bottom"
                    >
                      <span className="sl-tag-ref-code">{`${getScheinTssTag(
                        schein
                      )}`}</span>
                    </Tooltip>
                  )}
                  {schein?.invoiceNumber &&
                    schein.scheinStatus ===
                      ScheinStatus.ScheinStatus_Printed && (
                      <Tooltip
                        content={t2('generalInfo.invoiceNumber', {
                          invoiceNumber: schein.invoiceNumber,
                        })}
                        position="bottom"
                      >
                        <Svg
                          className="icon-card"
                          src={receipt}
                          height={heightIcon}
                          width={weightIcon}
                          onClick={() =>
                            onCopyInvoiceNumber(schein.invoiceNumber!)
                          }
                          style={{ marginRight: '4px' }}
                        />
                      </Tooltip>
                    )}
                  {isEVCase(
                    insuarance,
                    datetimeUtil
                      .getLastDayOfQuarterByQuarterAndYear(
                        schein.g4101Quarter!,
                        schein.g4101Year!
                      )
                      .getTime(),
                    schein
                  ) && (
                    <Tooltip
                      content={t2('generalInfo.readCard')}
                      position="bottom"
                    >
                      <Svg
                        className="icon-card"
                        src={readCardIcon}
                        height={heightIcon}
                        width={weightIcon}
                      />
                    </Tooltip>
                  )}
                </div>
                <Flex align="center" onClick={(e) => e.stopPropagation()}>
                  <Tooltip
                    content={t2(
                      patientFileStore.schein.isFocusMode
                        ? 'focusingMode'
                        : 'focusMode'
                    )}
                    position="bottom"
                  >
                    <Flex
                      className={getCssClass({
                        scheinFocusing:
                          isSelected && patientFileStore.schein.isFocusMode,
                      })}
                      onClick={(event) => {
                        event.stopPropagation();
                        if (isSelected) {
                          patientFileActions.schein.setFocusMode(
                            !patientFileStore.schein.isFocusMode
                          );
                          return;
                        }
                        setScheinSelected(schein);
                        selectSchein(schein);
                        patientFileActions.schein.setFocusMode(true);
                      }}
                    >
                      <Svg
                        src={
                          isSelected && patientFileStore.schein.isFocusMode
                            ? FocusingIcon
                            : FocusIcon
                        }
                      />
                    </Flex>
                  </Tooltip>
                  {(!patientFileStore.schein.isFocusMode || !isSelected) &&
                    schein?.scheinStatus !==
                      ScheinStatus.ScheinStatus_Printed &&
                    props.menuItem(schein)}
                </Flex>
              </ScheinContent>
              {isSelected && insuarance && !insuarance.isTerminated && (
                <InsuranceSection>
                  <div className="insurance-infor">
                    {contentTooltipInsurance(schein, insuarance)}
                  </div>
                  <div className="icon-info">
                    {isPublicInsurance && (
                      <>
                        {insuarance.insuranceStatus && (
                          <>
                            <Tooltip
                              content={contentTooltipInsuranceStatus(
                                insuarance.insuranceStatus
                              )}
                            >
                              <Svg
                                src={statusInsurance(
                                  insuarance.insuranceStatus
                                )}
                                height={20}
                                width={20}
                              />
                            </Tooltip>{' '}
                            <div className="divide">|</div>
                          </>
                        )}
                        {insuarance.specialGroup && (
                          <div className="special-group">
                            <Tooltip
                              content={contentTooltipSpecialGroup(
                                insuarance.specialGroup
                              )}
                            >
                              <Svg
                                src={userIcon}
                                height={heightIcon}
                                width={weightIcon}
                              />
                            </Tooltip>
                            <div className="textIcon">
                              {insuarance.specialGroup}
                            </div>
                          </div>
                        )}
                        {!!insuarance.wop && (
                          <div className="kv-region">
                            <Tooltip
                              content={contentTooltipKvRegion(insuarance.wop)}
                            >
                              <Svg
                                src={kvRegionIcon}
                                height={heightIcon}
                                width={weightIcon}
                              />
                            </Tooltip>
                            <div className="textIcon">{insuarance.wop}</div>
                          </div>
                        )}
                        {(insuarance.copaymentExemptionTillDate ||
                          insuarance.havePatientReceipt ||
                          insuarance.haveHeimiLongTermApproval) &&
                        (!!insuarance.wop ||
                          insuarance.specialGroup ||
                          insuarance.insuranceStatus) ? (
                          <div className="divide">|</div>
                        ) : null}

                        {insuarance.copaymentExemptionTillDate && (
                          <Tooltip
                            content={contentTooltipCoPayment(
                              insuarance.copaymentExemptionTillDate
                            )}
                          >
                            <Svg
                              src={
                                insuarance.copaymentExemptionTillDate &&
                                datetimeUtil.now() >
                                  insuarance.copaymentExemptionTillDate
                                  ? coPaymentExpiredIcon
                                  : coPaymentIcon
                              }
                              height={heightIcon}
                              width={weightIcon}
                            />
                          </Tooltip>
                        )}
                      </>
                    )}
                    {insuarance.havePatientReceipt && (
                      <Tooltip content={contentTooltipPatientReceipt()}>
                        <Svg
                          src={patientReceiptIcon}
                          height={heightIcon}
                          width={weightIcon}
                        />
                      </Tooltip>
                    )}
                    {insuarance.haveHeimiLongTermApproval && (
                      <Tooltip content={t2('generalInfo.longTerm')}>
                        <Svg
                          src={longTermIcon}
                          height={heightIcon}
                          width={weightIcon}
                        />
                      </Tooltip>
                    )}
                    {insuarance.endDate &&
                      insuarance.endDate < datetimeUtil.now() && (
                        <Flex>
                          <div className="divide">|</div>
                          <div className="insurance-expired">{`bis ${formatUnixToDateString(
                            insuarance.endDate
                          )}`}</div>
                        </Flex>
                      )}
                  </div>
                </InsuranceSection>
              )}
            </Schein>
          </>
        ) : null}
      </>
    );
  };

  const mapIconTitleScheinItem = (schein: ScheinItem) => {
    if (schein?.scheinStatus === ScheinStatus.ScheinStatus_Canceled) {
      return (
        <Svg
          className="mark-billed-icon"
          src={xCircleIcon}
          height={heightIcon}
          width={weightIcon}
        />
      );
    }
    if (
      schein.markedAsBilled ||
      schein?.scheinStatus === ScheinStatus.ScheinStatus_Billed
    ) {
      return (
        <Svg
          className="mark-billed-icon"
          src={checkCircleIcon}
          height={heightIcon}
          width={weightIcon}
        />
      );
    }
    return (
      <div
        className="main-group"
        style={{
          backgroundColor: backgroundMainGroup(schein.scheinMainGroup),
          color: colorMainGroup(schein.scheinMainGroup),
        }}
      >
        {mappingScheinIcon(schein.scheinMainGroup)}
      </div>
    );
  };

  const isValidMarkedAsBilled = (schein: ScheinItem) => {
    if (
      schein?.scheinStatus === ScheinStatus.ScheinStatus_Canceled ||
      schein.markedAsBilled
    )
      return true;
    return false;
  };

  const renderAllSchein = (schein: ScheinItem, insuarance: InsuranceInfo) => {
    if (!schein) return null;
    const isPublicInsurance =
      !!insuarance &&
      [TypeOfInsurance.Public].includes(insuarance.insuranceType);
    const isSelected =
      scheinSelectedState?.scheinId === schein.scheinId &&
      ![
        ScheinStatus.ScheinStatus_Billed,
        ScheinStatus.ScheinStatus_Canceled,
      ].includes(schein.scheinStatus!);
    const isHover = scheinHover === schein.scheinId;

    return (
      <>
        <Schein
          onMouseEnter={() => setScheinHover(schein.scheinId)}
          onMouseLeave={() => setScheinHover('')}
          className={`selected-schein selected-schein-${schein.scheinId}`}
          style={{
            backgroundColor: isHover ? COLOR.BACKGROUND_HOVER : '',
          }}
        >
          {isSelected && <div className="divide-section"></div>}
          <div
            className={`schein-content ${isSelected ? 'selected' : ''}`}
            style={{
              backgroundColor: isSelected
                ? COLOR.INFO_SECONDARY_PRESSED
                : 'unset',
            }}
          >
            <div className="section-left">
              <div
                className={
                  getScheinTitle(schein).length > 34
                    ? 'content-subgroup'
                    : 'content-subgroup-billed'
                }
              >
                {mapIconTitleScheinItem(schein)}
                <div
                  className={
                    schein.markedAsBilled
                      ? 'title-schein-billed'
                      : 'title-schein'
                  }
                >
                  {getScheinTitle(schein)}
                </div>
              </div>
              {schein?.tsvgContactType && (
                <Tooltip
                  content={getTssContentToolTip(schein)}
                  position="bottom"
                >
                  <span className="sl-tag-ref-code">{`${getScheinTssTag(
                    schein
                  )}`}</span>
                </Tooltip>
              )}
              {isEVCase(
                insuarance,
                datetimeUtil
                  .getLastDayOfQuarterByQuarterAndYear(
                    schein.g4101Quarter!,
                    schein.g4101Year!
                  )
                  .getTime(),
                schein
              ) && (
                <Tooltip content={t2('generalInfo.readCard')} position="bottom">
                  <Svg
                    className="icon-card"
                    src={readCardIcon}
                    height={heightIcon}
                    width={weightIcon}
                  />
                </Tooltip>
              )}
            </div>
            {isValidMarkedAsBilled(schein) && (isSelected || isHover) && (
              <Tooltip content={t2('generalInfo.markAsBilled')}>
                <Svg
                  src={markNotBilledIcon}
                  onClick={() => props.onConfirmMarkNotBilled?.(schein)}
                />
              </Tooltip>
            )}
          </div>
          {isSelected && !!insuarance && (
            <InsuranceSection>
              <div className="insurance-infor">
                {contentTooltipInsurance(schein, insuarance)}
              </div>
              <div className="icon-info">
                {isPublicInsurance && (
                  <>
                    {insuarance.insuranceStatus && (
                      <>
                        <Tooltip
                          content={contentTooltipInsuranceStatus(
                            insuarance.insuranceStatus
                          )}
                        >
                          <Svg
                            src={statusInsurance(insuarance.insuranceStatus)}
                            height={20}
                            width={20}
                          />
                        </Tooltip>{' '}
                        <div className="divide">|</div>
                      </>
                    )}
                    {insuarance.specialGroup && (
                      <div className="special-group">
                        <Tooltip
                          content={contentTooltipSpecialGroup(
                            insuarance.specialGroup
                          )}
                        >
                          <Svg
                            src={userIcon}
                            height={heightIcon}
                            width={weightIcon}
                          />
                        </Tooltip>
                        <div className="textIcon">
                          {insuarance.specialGroup}
                        </div>
                      </div>
                    )}
                    {!!insuarance.wop && (
                      <div className="kv-region">
                        <Tooltip
                          content={contentTooltipKvRegion(insuarance.wop)}
                        >
                          <Svg
                            src={kvRegionIcon}
                            height={heightIcon}
                            width={weightIcon}
                          />
                        </Tooltip>
                        <div className="textIcon">{insuarance.wop}</div>
                      </div>
                    )}
                    {(insuarance.copaymentExemptionTillDate ||
                      insuarance.havePatientReceipt ||
                      insuarance.haveHeimiLongTermApproval) &&
                    (insuarance.wop ||
                      insuarance.specialGroup ||
                      insuarance.insuranceStatus) ? (
                      <div className="divide">|</div>
                    ) : null}
                    {insuarance.copaymentExemptionTillDate && (
                      <Tooltip
                        content={contentTooltipCoPayment(
                          insuarance.copaymentExemptionTillDate
                        )}
                      >
                        <Svg
                          src={
                            insuarance.copaymentExemptionTillDate &&
                            datetimeUtil.now() >
                              insuarance.copaymentExemptionTillDate
                              ? coPaymentExpiredIcon
                              : coPaymentIcon
                          }
                          height={heightIcon}
                          width={weightIcon}
                        />
                      </Tooltip>
                    )}
                  </>
                )}
                {insuarance?.havePatientReceipt && (
                  <Tooltip content={contentTooltipPatientReceipt()}>
                    <Svg
                      src={patientReceiptIcon}
                      height={heightIcon}
                      width={weightIcon}
                    />
                  </Tooltip>
                )}
                {insuarance?.haveHeimiLongTermApproval && (
                  <Tooltip content={t2('generalInfo.longTerm')}>
                    <Svg
                      src={longTermIcon}
                      height={heightIcon}
                      width={weightIcon}
                    />
                  </Tooltip>
                )}
                {insuarance?.endDate &&
                  insuarance.endDate < datetimeUtil.now() && (
                    <Flex>
                      <div className="divide">|</div>
                      <div className="insurance-expired">{`bis ${formatUnixToDateString(
                        insuarance.endDate
                      )}`}</div>
                    </Flex>
                  )}
              </div>
            </InsuranceSection>
          )}
        </Schein>
      </>
    );
  };

  useEffect(() => {
    setScheinSelected(props.scheinSelected);
  }, [props.scheinSelected]);

  if (listScheinQuarters.length === 0) {
    return noSchein;
  }

  return (
    <Container className={props.className}>
      {listScheinQuarters.map((item) => {
        const quarterYear = `Q${item.groupKey.quarter}/${item.groupKey.year}`;
        return (
          <Content key={quarterYear}>
            <QuarterSection $isShow={shouldShowQuarterGroup(item)}>
              {quarterYear}
            </QuarterSection>
            {item.groupValue &&
              item.groupValue.map((schein) => {
                return (
                  <InfoScheinAndInsurance key={schein.scheinItem.scheinId}>
                    {props.isNeedActiveSchein
                      ? renderContentNotBilled(
                          schein.scheinItem,
                          schein.insurance
                        )
                      : renderAllSchein(schein.scheinItem, schein.insurance)}
                  </InfoScheinAndInsurance>
                );
              })}
          </Content>
        );
      })}
    </Container>
  );
};
export default ScheinDetail;
