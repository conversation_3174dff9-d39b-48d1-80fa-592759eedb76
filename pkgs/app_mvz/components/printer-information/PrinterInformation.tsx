import React from 'react';

import type CommonI18n from '@tutum/mvz/locales/en/Common.json';

import { Formik } from 'formik';
import {
  PrinterProfile,
  PrintUnits,
} from '@tutum/hermes/bff/legacy/printer_common';
import PrinterInformationForm from './PrinterInformationForm';
import i18n from '@tutum/infrastructure/i18n';

const initialValues: PrinterProfile = {
  printerName: '',
  formId: '',
  name: '',
  createdDate: 0,
  units: PrintUnits.PrintUnits_in,
};

export interface PrinterInformationProps {
  className?: string;
  contractId?: string;
  formId: string;
  okv?: string;
  ikNumber?: number;
  isFormPrint?: boolean;
}

const PrinterInformation = ({
  className,
  contractId,
  formId,
  ikNumber,
  okv,
  isFormPrint,
}: PrinterInformationProps) => {
  const { t } = i18n.useTranslation<keyof typeof CommonI18n.PrinterSetting>({
    namespace: 'Common',
    nestedTrans: 'PrinterSetting',
  });

  const validate = (values: PrinterProfile) => {
    const errors: Record<string, string> = {};

    if (!values.printerName) {
      errors.printerName = t('printerRequired');
    }

    if (!values.copies) {
      errors.copies = t('copiesRequired');
    }

    if (!values.formSize || !values.orientation) {
      errors.paperSize = t('paperSizeRequired');
    }

    return errors;
  };

  return (
    <Formik<PrinterProfile>
      onSubmit={() => {}}
      initialValues={initialValues}
      validate={validate}
      validateOnMount
    >
      {(formikProps) => {
        return (
          <PrinterInformationForm
            className={className}
            formikProps={formikProps}
            contractId={contractId}
            formId={formId}
            ikNumber={ikNumber}
            okv={okv}
            isFormPrint={isFormPrint}
          />
        );
      }}
    </Formik>
  );
};

export default PrinterInformation;
