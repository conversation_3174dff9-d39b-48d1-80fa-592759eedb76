import React, { useState } from 'react';
import moment from 'moment';

import type FormI18n from '@tutum/mvz/locales/en/PatientManagement.json';

import {
  Avatar,
  BodyTextM,
  Flex,
  Svg,
  Tooltip,
  alertSuccessfully,
} from '@tutum/design-system/components';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { renderDiagnoseText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/diagnose-entry/helpers';
import {
  customAdditionalInfos,
  renderServiceText,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/service-entry/utils';
import { renderNoteText } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/freetext-entry/FreetextEntry';
import {
  TimelineEntityType,
  TimelineModel,
  ActionType,
} from '@tutum/hermes/bff/timeline_common';
import AdditionalInfoEntry from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/service-entry/AdditionalInfoEntry';
import I18n from '@tutum/infrastructure/i18n';
import { Dialog, PopoverPosition } from '@tutum/design-system/components/Core';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import {
  timelineActions,
  useTimeLineStore,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { AdditionalInfoParent } from '@tutum/hermes/bff/legacy/repo_encounter';
import { HistoryDto } from '@tutum/hermes/bff/legacy/audit_log_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

const actionHistoryIcon = '/images/action-history.svg';
const restoreIcon = '/images/restore.svg';
const createdIcon = '/images/plus-circle-grey.svg';
const removedIcon = '/images/trash-bin.svg';
const updatedIcon = '/images/edit-2.svg';
const restoredIcon = '/images/historization/restore.svg';

const isHideTimeLineAction = [
  TimelineEntityType.TimelineEntityType_Diagnose,
  TimelineEntityType.TimelineEntityType_Service,
  TimelineEntityType.TimelineEntityType_Note,
  TimelineEntityType.TimelineEntityType_Error,
  TimelineEntityType.TimelineEntityType_EDMPEnrollment_Document,
];
export interface TimelineActionHOCProps {
  children: React.ReactNode;
  className?: string;
  entry?: TimelineModel;
  hasBilled?: boolean;
  displayInfo?: (
    info: Partial<AdditionalInfoParent>
  ) => string | React.ReactNode;
}

const getHistoryContent = (row: HistoryDto, t: any) => {
  if (row.eventType === ActionType.Remove) return t('contentRestore');
  switch (row.dataType) {
    case TimelineEntityType.TimelineEntityType_Diagnose: {
      const encounter = row?.timelineModel?.encounterDiagnoseTimeline;
      return `${renderDiagnoseText(encounter)}`;
    }
    case TimelineEntityType.TimelineEntityType_Service: {
      const encounter = row?.timelineModel?.encounterServiceTimeline;
      return `${renderServiceText(encounter!)}`;
    }
    case TimelineEntityType.TimelineEntityType_Note: {
      const encounter = row?.timelineModel?.encounterNoteTimeline;
      return `${renderNoteText(encounter!)}`;
    }
    default:
      break;
  }
};

const getActionIcon = (row: string) => {
  switch (row) {
    case ActionType.Add: {
      return createdIcon;
    }
    case ActionType.Edit: {
      return updatedIcon;
    }
    case ActionType.Remove: {
      return removedIcon;
    }
    case ActionType.Restore: {
      return restoredIcon;
    }
    default:
      break;
  }
};

const AdditionalInfo = ({ entry, displayInfo }) => {
  const { treatmentDoctorId } = entry;
  const { getDoctorById } = GlobalContext.useContext();
  if (!entry?.encounterServiceTimeline?.additionalInfos?.length) return null;
  const userProfile = getDoctorById(treatmentDoctorId);
  const additionalInfos = customAdditionalInfos(
    entry?.encounterServiceTimeline?.additionalInfos,
    userProfile!
  );

  return (
    <span key={`additioalInfo_history`} className="add-info__container">
      {additionalInfos.map((info, idx) => (
        <AdditionalInfoEntry
          key={`${idx}_${info.fK}`}
          info={info}
          isChild={false}
          isLast={additionalInfos?.length - 1 === idx}
          displayInfo={displayInfo}
        />
      ))}
    </span>
  );
};

const ReferralBlock = ({ entry }) => {
  const { t: tComposer } = I18n.useTranslation<keyof typeof FormI18n.Composer>({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  if (
    !entry ||
    !entry.encounterServiceTimeline ||
    !entry.encounterServiceTimeline.referralDoctorInfo
  ) {
    return null;
  }

  const referralDoctorInfo = entry.encounterServiceTimeline.referralDoctorInfo;
  if (!referralDoctorInfo.lanr || !referralDoctorInfo.bsnr) return null;
  const lanrText = `${tComposer('referralInfoLanr')}: ${
    referralDoctorInfo.lanr
  };`;
  const bsnrText = `${tComposer('referralInfoBsnr')}: ${
    referralDoctorInfo.bsnr
  }`;

  return (
    <Flex gap={8}>
      <BodyTextM>{lanrText}</BodyTextM>
      <BodyTextM>{bsnrText}</BodyTextM>
    </Flex>
  );
};

const genColumns = (
  t,
  entry: TimelineModel,
  displayInfo: (
    info: Partial<AdditionalInfoParent>
  ) => string | React.ReactNode,
  getDoctorName: Function,
  getDoctorInitial: Function,
  getContentRestore: (row: HistoryDto) => void,
  onRestore: (row: HistoryDto) => void
): IDataTableColumn<HistoryDto>[] => [
  {
    name: <Flex className="column-title">{t('madeOn')}</Flex>,
    maxWidth: '200px',
    style: { position: 'relative' },
    cell: (row: HistoryDto) => {
      return <Flex> {moment(row.madeOn).calendar()}</Flex>;
    },
  },
  {
    name: <Flex className="column-title">{t('activity')}</Flex>,
    maxWidth: '180px',
    style: { position: 'relative' },
    cell: (row: HistoryDto) => {
      return (
        <Flex>
          <Svg className="history-content" src={getActionIcon(row.eventType)!} />
          {t(`${row.eventType}`)}
        </Flex>
      );
    },
  },
  {
    name: <Flex className="column-title">{t('madeBy')}</Flex>,
    maxWidth: '200px',
    style: { position: 'relative' },
    cell: (row: HistoryDto) => {
      return (
        <Flex className="doctor-avatar" gap={8}>
          <Avatar initial={getDoctorInitial(row.madeBy)} />
          <Flex style={{ alignItems: 'center' }}>
            {getDoctorName(row.madeBy)}
          </Flex>
        </Flex>
      );
    },
  },
  {
    name: <Flex className="column-title">{t('changedTo')}</Flex>,
    minWidth: '500px',
    style: { position: 'relative' },
    cell: (row: HistoryDto) => {
      return (
        <Flex column style={{ gap: '4px' }}>
          {row?.eventType !== ActionType.Restore
            ? getHistoryContent(row, t)
            : getContentRestore(row)}
          {row?.eventType !== ActionType.Remove && (
            <>
              <ReferralBlock entry={entry} />
              <AdditionalInfo entry={entry} displayInfo={displayInfo} />
            </>
          )}
        </Flex>
      );
    },
  },
  {
    name: <Flex className="column-title" />,
    width: '40px',
    cell: (row: HistoryDto) => {
      return (
        <Flex>
          {row?.eventType &&
            ![ActionType.Restore, ActionType.Remove].includes(
              row.eventType as ActionType
            ) && (
              <Svg
                src={restoreIcon}
                className="restore-icon"
                onClick={() => onRestore(row)}
              />
            )}
        </Flex>
      );
    },
  },
];

interface IHistoyTableProps {
  t: any;
  getDoctorName: Function;
  getDoctorInitial: Function;
  entry?: TimelineModel;
  displayInfo?: (
    info: Partial<AdditionalInfoParent>
  ) => string | React.ReactNode;
  onSuccess: (row: HistoryDto) => void;
}

const HistoryTable = (props: IHistoyTableProps) => {
  const { t, getDoctorName, getDoctorInitial, entry, displayInfo, onSuccess } =
    props;
  const timelineStore = useTimeLineStore();

  const getContentRestore = (row: HistoryDto) => {
    const item = (timelineStore.viewingEntryHistories || []).find(
      (item) => item?.id === row?.restoreToAuditLogId
    );
    if (!item) return t('contentRestore');
    const content = getHistoryContent(item, t);
    return content;
  };

  return (
    <Flex className="sl-table">
      <Table
        columns={genColumns(
          t,
          entry!,
          displayInfo!,
          getDoctorName,
          getDoctorInitial,
          getContentRestore,
          onSuccess
        )}
        data={timelineStore.viewingEntryHistories || []}
        persistTableHead
        progressPending={false}
        noHeader
        fixedHeader
        pagination
        paginationResetDefaultPage
      />
    </Flex>
  );
};

export const TimelineActionHOC = ({
  children,
  entry,
  className,
  hasBilled,
  displayInfo,
}: TimelineActionHOCProps) => {
  const { getDoctorName, getDoctorInitial } = GlobalContext.useContext();
  const { t } = I18n.useTranslation<keyof typeof FormI18n.VersionHistory>({
    namespace: 'PatientManagement',
    nestedTrans: 'VersionHistory',
  });

  const timelineStore = useTimeLineStore();
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [auditLogCurrent, setAuditLogCurrent] = useState<HistoryDto | null>(null);

  const handleViewHistoryDialog = () => {
    timelineActions.setViewingEntryHistory(entry!);
    timelineActions.setVersionHistoryDialog(true);
  };

  const actionToast = () => {
    alertSuccessfully(t('restoreSuccess'));
  };

  if (!timelineStore.isHistoryMode) {
    return <>{children}</>;
  }

  if ((entry && entry.auditLogs && entry.auditLogs.length <= 1) || hasBilled) {
    return;
  }

  return (
    <div className={className}>
      {isHideTimeLineAction.includes(entry?.type!) && (
        <Tooltip
          popoverClassName="sl-EntryStatus"
          content={<Flex column>{t('viewVersionHistory')}</Flex>}
          position={PopoverPosition.TOP}
        >
          <Svg
            className="history-action"
            src={actionHistoryIcon}
            onClick={handleViewHistoryDialog}
          />
        </Tooltip>
      )}
      {timelineStore.viewingEntryHistory?.id && (
        <Dialog
          className={`bp5-dialog-fullscreen ${className}`}
          isOpen={
            timelineStore.isOpenVersionHistoryDialog &&
            entry?.id === timelineStore.viewingEntryHistory?.id
          }
          title={t('versionHistory', {
            date: timelineStore.viewingEntryHistory.createdAtString,
          })}
          onClose={() => {
            timelineActions.setViewingEntryHistory(undefined);
            timelineActions.setVersionHistoryDialog(false);
          }}
          canOutsideClickClose={false}
        >
          <HistoryTable
            displayInfo={displayInfo}
            t={t}
            entry={entry}
            getDoctorName={getDoctorName}
            getDoctorInitial={getDoctorInitial}
            onSuccess={(row) => {
              setConfirmDialog(true);
              setAuditLogCurrent(row);
            }}
          />
          <DeleteConfirmDialog
            className="confirm-dialog"
            isOpen={confirmDialog}
            close={() => setConfirmDialog(false)}
            confirm={() => {
              timelineActions.restoreEntryHistory({
                auditLogId: auditLogCurrent?.id!,
              });
              setConfirmDialog(false);
              setAuditLogCurrent(null);
              timelineActions.setVersionHistoryDialog(false);
              actionToast();
            }}
            text={{
              btnCancel: t('btnNo'),
              btnOk: t('btnYes'),
              title: t('titleConfirmDialog'),
              message: t('contentConfirmDialog', {
                date:
                  datetimeUtil.dateTimeFormat(
                    auditLogCurrent?.madeOn,
                    DATE_TIME_WITHOUT_SECONDS_FORMAT
                  ) ||
                  datetimeUtil.dateTimeFormat(
                    datetimeUtil.now(),
                    DATE_TIME_WITHOUT_SECONDS_FORMAT
                  ),
              }),
            }}
          />
        </Dialog>
      )}
    </div>
  );
};
