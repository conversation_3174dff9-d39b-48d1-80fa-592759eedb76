{"Employee": {"accountManagement": "Benutzermanagement", "createAccount": "<PERSON><PERSON><PERSON>", "title": "Titel", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "userName": "<PERSON><PERSON><PERSON><PERSON>", "initials": "Initialien", "role": "<PERSON><PERSON>", "editEmployee": "<PERSON><PERSON><PERSON>", "moreAction": "<PERSON><PERSON>", "resetPassword": "Passwort zurücksetzen", "deactivateAccount": "Benutzerkonto deaktivieren", "resetPasswordConfirmTitle": "Passwort zurücksetzen?", "resetPasswordConfirmContent1": "Sie sind dabei ein neues Passwort zu erstellen für  ", "resetPasswordConfirmContent2": "Sie können Ihr altes Passwort nicht mehr wiederherstellen, sobald Sie ein neues festgel<PERSON>t haben.", "resetPasswordTitle": "Passwort zurücksetzen", "cancelResetPasswordBtn": "Abbrechen", "resetPasswordBtn": "Passwort zurücksetzen", "saveResetPasswordBtn": "Speichern", "newPasswordLabel": "Neues Passwort", "activateParticipationSuccess": "Aktivierung der Teilnahme erfolgreich", "deactivateParticipationSuccess": "<PERSON><PERSON>nah<PERSON> erfolg<PERSON><PERSON>", "confirmPasswordLabel": "Passwort bestätigen", "successToaster": "Passwort wurde erfolgreich zurückgesetzt", "errConfirmPasswordEmpty": "MUSS einen Großbuchstaben enthalten \nMUSS einen Kleinbuchstaben enthalten \nMUSS eine Zahl enthalten \nMUSS ein Sonderzeichen enthalten \nMUSS mindestens 8 Zeichen enthalten", "errConfirmPasswordNotEqualPassword": "Passwort stimmt nicht überein", "errSamePassWithOldPass": "Das neue Passwort darf nicht identisch zum vorherigen Passwort sein", "resetPasswordSuccess": "Passwort wurde erfolgreich zurückgesetzt", "saveAccountSuccess": "Das Erstellen des Benutzerkonto war erfolgreich", "editAccountSuccess": "Benutzerkonto wurde erfolgreich bearbeitet", "titleDefault": "-", "titleV1": "Dr.", "titleV2": "Dr. med.", "titleV3": "Prof.", "titleV4": "Prof. Dr.", "titleV5": "Prof. Dr. med.", "titleV6": "PD Dr.", "titleV7": "PD Dr. med.", "titleV8": "Sr. Dr.", "titleV9": "Sr.", "noResults": "<PERSON><PERSON> vorhanden. Enter drücken zum Auswählen.", "additionalName": "Namenszusatz", "DOCTOR": "Arz<PERSON>", "MANAGER": "Manager", "MFA": "MFA", "CEO": "CEO"}, "UpdateEmployee": {"updateEmployee": "Benutzerkonto aktualisieren", "accountInformation": "Benutzerkonto Informationen", "doctorInformation": "Arztinformationen", "doctorStamp": "Arztstempel", "contractInformation": "Vertragsinformationen", "role": "<PERSON><PERSON>", "doctor": "Arzt/ Ärztin", "mfa": "MFA", "admin": "Admin", "additionalAdmin": "Administratorrechte für den Zugriff auf die Historisierung?", "isAdminNo": "<PERSON><PERSON>", "isAdminYes": "<PERSON>a", "gender": "Geschlecht", "maleGender": "<PERSON><PERSON><PERSON><PERSON>", "femaleGender": "<PERSON><PERSON><PERSON>", "indefiniteGender": "Unbestimmt", "unknownGender": "Unbekannt", "diversGender": "Divers", "title": "Titel", "titleDefault": "-", "titleV1": "Dr.", "titleV2": "Dr. med.", "titleV3": "Prof.", "titleV4": "Prof. Dr.", "titleV5": "Prof. Dr. med.", "titleV6": "PD Dr.", "titleV7": "PD Dr. med.", "titleV8": "Sr. Dr.", "titleV9": "Sr.", "noResults": "<PERSON><PERSON> vorhanden. Enter drücken zum Auswählen.", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "userName": "<PERSON><PERSON><PERSON><PERSON>", "createPassword": "Passwort erstellen", "confirmPassword": "Passwort bestätigen", "lanr": "LANR", "pseudoLanr": "P<PERSON>udo LANR", "bsnr": "BSNR", "areaOfExpertise": "Fachgebiet", "searchAreaOfExpertise": "Fachgebiet auswählen", "hasContracts": "<PERSON><PERSON><PERSON> dieser Arzt/Ärztin an Selektivverträgen teil?", "isContractNo": "<PERSON><PERSON>", "isContractYes": "<PERSON>a", "hasFavContracts": "Facharztverträge (MEDI)", "hasHzvContracts": "Hausarztverträge (HÄVG)", "havgId": "HÄVG- ID", "havgVpId": "HÄVG VP-ID", "noData": "<PERSON><PERSON> vorhanden", "havgVpIdWarningTooltip": "HÄVG-ID und die HPM-Zuweisung sind notwendig, um Daten abrufen zu können.", "havgVpIdErrorTooltip": "Daten sind aufgrund einer Verbindungsunterbrechung veraltet", "havgVpIDHint": "Bitte beachten Sie, dass sie nur Änderungen im nach Bestätigung durch die HÄVG vorgenommen werden dürfen. Bei Rückfragen kontaktieren Sie bitte den Support der HÄVG.", "mediId": "MEDI- ID", "mediVpId": "MEDI VP-ID", "mediVpIdWarningTooltip": "MEDI-ID und Einschreibe URL sind für den Datenabruf verpflichtend.", "mediVpIdErrorTooltip": "Daten sind aufgrund einer Verbindungsunterbrechung veraltet", "contract": "Vertrag", "enrollmentType": "Art der Einschreibung", "enrollmentType_online": "Online", "enrollmentType_offline": "Offline", "contractValidDate": "Vertragsdatum", "startOn": "Beginndatum", "endOn": "Endedatum", "selectContract": "Vertrag auswählen", "contractStartDate": "BEGINNDATUM", "contractEndDate": "ENDDATUM", "addContract": "Weiteren Vertrag auswählen", "optional": "OPTIONAL", "firstNamePlaceholder": "z.B. \"<PERSON>\"", "lastNamePlaceholder": "z.B. \"Mustermann\"", "areaOfExpertisePlaceholder": "Fachgebiet auswählen", "lanrPlaceholder": "z.B. \"********9\"", "bsnrPlaceholder": "z.B. \"********9\"", "havgIDPlaceholder": "z.B. \"1234567\"", "havgVPIDPlaceholder": "z.B. \"H00012345\"", "mediIDPlaceholder": "z.B. \"********\"", "mediVPIDPlaceholder": "z.B. \"FAABBCCDDE\"", "phoneNumberPlaceholder": "z.B. 0151 ********", "finishCreateAccount": "<PERSON><PERSON><PERSON><PERSON>", "saveAccount": "Speichern", "cancelAccount": "Abbrechen", "createAnotherAccount": "Spe<PERSON><PERSON> und ein weiteres Benutzerkonto erstellen", "errFirstNameEmpty": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "errLastNameEmpty": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "errUserNameEmpty": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "errInitialEmpty": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "existInitial": "Initialen existieren bereits", "atLeastChars": "Mindestens {{value}} Z<PERSON><PERSON>", "invalidUserName": "Ungültiger Benutzername. Bitte versuchen Sie es erneut.", "allowCharacter": "Mögliche Eingaben:", "allowCharacterLetters": "Buchstaben (a-z, A-Z)", "allowCharacterNumbers": "<PERSON><PERSON><PERSON><PERSON> (0-9)", "allowCharacterSpecial": "Besondere Eingaben:\n._-!#$%&‘*+/=?^_{|}~", "characterCaseSensitive": "Groß- und Kleinschreibung beachten. 'Benutzer123' ist unterschiedlich zu 'benutzer123'.", "errPasswordUpperCase": "MUSS einen Großbuchstaben enthalten", "errPasswordLowerCase": "MUSS einen Kleinbuchstaben enthalten", "errPasswordNumber": "MUSS eine Zahl enthalten", "errPasswordSpecial": "MUSS ein Sonderzeichen enthalten", "errPasswordLength": "MUSS mindestens 8 Zeichen enthalten", "errConfirmPasswordEmpty": "MUSS einen Großbuchstaben enthalten \nMUSS einen Kleinbuchstaben enthalten \nMUSS eine Zahl enthalten \nMUSS ein Sonderzeichen enthalten \nMUSS mindestens 8 Zeichen enthalten", "errConfirmPasswordNotEqualPassword": "Passwort stimmt nicht überein", "errTypeEmpty": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "errSelectAreaOfExpertise": "Bitte wählen Sie eine Option aus", "errLanrInvalidCommonRule": "Muss 9 Ziffern enthalten", "errLanrIsPseudoLanrFormat": "LANR des Vertragsarztes/Vertragspsychotherapeuten darf nicht 555555 sein", "errLanrInvalidDigitRule": "LANR des Vertragsarztes/Vertragspsychotherapeuten (Format nnnnnnmff) hat einen ungültigen m-Wert", "errBsnrInvalid": "Muss 9 Ziffern enthalten", "errHzvInvalid": "Muss 5 bis 7 Ziffern enthalten", "errHzvContractInvalid": "Bitte wählen Sie mindestens einen Vertrag aus", "errFavInvalid": "Muss 8 <PERSON>iffern enthalten", "errFavContractInvalid": "Bitte wählen Sie mindestens einen Vertrag aus", "errPhoneNumberInvalid": "Ungültiges Format, bitte versuchen Sie es erneut.", "errInvalidBsnr": "Ungültige BSNR-Eingabe, bitte versuchen Sie es erneut.", "errInvalidDevice": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "duplicateHzvID": "Diese HÄVG- ID wurde bereits verwendet", "duplicateMediID": "<PERSON>se <PERSON>DI- ID wurde bereits verwendet", "duplicateLanrID": "Diese LANR wurde bereits verwendet", "duplicateInitials": "Diese INITIALIEN wurde bereits verwendet", "leavePageTitle": "<PERSON>öchten Sie die Seite verlassen?", "leavePageContent": "<PERSON>cht gespeicherte Änderungen gehen verloren, wenn Sie die Seite verlassen.", "confirmLeave": "Ja, verlassen", "cancelLeave": "<PERSON><PERSON>", "feeCatalogue": "Gebührenordnung", "captionChargeSystemIDs": "Es kann nur eine Gebührenordnung zugewiesen werden. Diese Aktion kann nicht rückgängig gemacht werden.", "mobileNumber": "Mobilnummer", "startDate": "Beginndatum", "endDate": "Enddatum", "assignedPractice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "device": "Standardauswahl", "deviceTooltip": "Standard- Arbeitsplatz wenn sich der Benutzer anmeldet. Der Benutzer kann selbst jederzeit Änderungen vornehmen.", "markAsBillingDoctor": "Als abrechnende/n <PERSON>rzt/<PERSON><PERSON><PERSON> markieren", "additionalName": "Namenszusatz", "intendWord": "Vorsatzwort", "salutation": "<PERSON><PERSON><PERSON>", "Herr": "<PERSON>", "Frau": "<PERSON><PERSON>", "Keine": "<PERSON><PERSON>", "initials": "Initialien", "jobDescription": "Berufsbezeichnung", "errJobDescription": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "activeDMPProgram": "Teilnahme DMP-Programm(e)", "include": "beinhalten", "asvTeamNumber": "ASV Team-<PERSON>ummer", "digitRule": "{{fieldName}} (format {{pattern}}) ist ungültig.", "markAsEmployedDoctor": "Arzt im Angestelltenverhältnis", "responsibleDoctor": "Abrechnender Arzt", "responsibleDoctorHelpText": "<PERSON><PERSON><PERSON><PERSON> Sie diese Option, wenn die Abrechnung nicht durch den aktuellen Arzt, sondern durch einen anderen erfolgt (z.B. bei einen auszubildenden Arzt).", "representativeDoctor": "Stellvertretender Arzt", "errInvalidResponsibleDoctor": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "errInvalidRepresentativeDoctor": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "initialPassword": "Initiales Passwort", "BankInformation": {"BankInformationSection": "Zahlungsinformationen", "bankName": "Name der Bank", "accountHolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iban": "IBAN", "bic": "BIC", "AddNewBankInfo": "Bankkonto hinzufügen"}, "urlRequired": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "eHKSSelection": "Auswahl der eHKS Dokumentationen", "eHKSDermatologist": "eHKS Dermatologen", "eHKSNonDermatologist": "eHKS Nicht-Dermatologen", "existUserName": "Benutzername existiert bereits", "potentialContract": "Potenzielle Verträge in anderen Regionen", "contractAvailable": "Verträge für Ihre Region", "endDateBeforeStartDate": "Das Enddatum muss nach dem Startdatum liegen", "overlapDate": "Startdatum überschneidet sich mit einem bestehenden Vertrag.", "favHint": "Bitte beachten Sie dass die Facharztverträge:\n- Rheumatologie über den AOK Baden-Württemberg Orthopädie Vertrag aktiviert wird\n- Diabetologie über den AOK Baden-Württemberg Gastroenterologie oder Kardiologie aktiviert wird"}, "CreateEmployee": {"hpmSetup": {"title": "HPM Konfiguration", "doctorCheckbox": "Teilnahme an Selektivverträgen (HzV/FaV)", "url": "HPM URL", "confirmTitle": "Möchten Sie die HPM URL ändern?", "confirmDescription": "Das kann zu Verbindungsproblemen führen und dazu das die Praxis nicht mehr mit HPM verbunden ist.", "confirmAction": "<PERSON><PERSON>, bestätigen", "warningNoBSNR": {"title": "<PERSON>s wurde keine BSNR ausgewählt", "description": "<PERSON>ür die Einrichtung der Selektivverträge für Ärzte ist ein BSNR mit HPM-Anbindung erforderlich."}, "warningNoHpm": {"title": "Die Verbindung zum HPM konnte nicht hergestellt werden.", "description": "In der aktuellen BSNR wurde keine HPM-Verbindung gefunden. Bitte wählen Sie eine andere mit HPM-Konfiguration."}}}}