{"weWillNotBeAbleToUndo": "Diese Aktion kann nicht rückgängig gemacht werden.", "keyboardCommandNavigateBetweenSections": "Alt+↑↓ um zu navigieren", "navigateBetweenSections": "um zu navigieren", "Table": {"rowsPerPage": "Zeilen pro Seite", "all": "Alle", "of": "von", "loading": "<PERSON><PERSON> l<PERSON>, einen Augenblick bitte noch…", "noRecords": "<PERSON><PERSON> vorhanden"}, "Input": {"search": "<PERSON><PERSON>"}, "Filter": {"allFilters": "<PERSON>e Filter", "reset": "Auf Standard zurücksetzen", "filter": "Filter"}, "Select": {"select": "Auswählen", "noResults": "<PERSON><PERSON> vorhanden", "search": "<PERSON>e…", "headerAdvanceSearch": "oder wählen Sie ein anderes Suchkriterium", "searchUsingCriteria": "suche mit kriterien", "AtleastOneSearch": "At least one characters search"}, "MultiSelect": {"selectAll": "Alle auswählen", "deselectAll": "Alle abwählen", "select": "Auswählen", "placeholder": "<PERSON>e…", "noResult": "<PERSON><PERSON> vorhand<PERSON>.", "itemsSelected": "{{ count }} gew<PERSON><PERSON>t", "all": "Alle"}, "DateTimePicker": {"DD_MM_YYYY": "TT.MM.JJJJ", "DD_MM_YY": "TT.MM.JJ", "dd_mm_yyyy": "tt.mm.jjjj", "DD_MM_YYYY_at_HH_MM": "DD.MM.YYYY at HH:MM", "Q_YY": "Q.JJ", "q_yyyy": "q.jjjj", "MM_YY": "MM.JJ", "fromTo": "Vom - Bis", "dateRangeInput": "TT.MM.JJJJ - TT.MM.JJJJ", "shortcuts": {"Today": "<PERSON><PERSON>", "Yesterday": "Gestern", "1 week ago": "<PERSON>or einer W<PERSON>e", "Past week": "Letzte Woche", "1 month ago": "<PERSON>or e<PERSON><PERSON>", "Past month": "Letzter Monat", "3 months ago": "Letzten 3 Monate", "Past 3 months": "Letzten 3 Monate", "Past 6 months": "Letzten 6 Monate", "1 year ago": "<PERSON><PERSON> e<PERSON>", "Past year": "Letztes Jahr", "Past 2 years": "Letzten 2 Jahre", "Quarter": "Quartal {{quarter}}/{{year}}"}, "today": "<PERSON><PERSON>", "clear": "<PERSON><PERSON>", "outOfRange": "<PERSON><PERSON><PERSON>", "invalidDate": "Ungültiges Datum"}, "NavigationSuggest": {"listNavigation": "↑↓ zum navigieren. Enter um Auswahl zu bestätigen"}, "SuggestList": {"inputPlaceholder": "Auswählen oder Dokumentation starten", "selectText": "Auswählen"}, "FormValidation": {"fieldRequired": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "errPostCodeInvalid": "Bitte geben Si<PERSON> eine gültige PLZ ein", "inValidCode": "Das Format muss 9 Charakter haben", "codeExisted": "<PERSON>se <PERSON>mer existiert bereits", "inValidNumber": "<PERSON><PERSON> ist ungültig", "valueMustbeMoreThanZero": "Wert muss ≥ 0 sein", "valueMustbeInRange": "Wert muss zwischen 0 und 100 liegen", "existValue": "Dieser Wert existiert bereits. Bitte gebe einen einmalig vorkommenden Wert an.", "lengthExceed": "Maximal {{maxLength}} Charakter."}, "ButtonActions": {"cancelText": "Abbrechen", "saveText": "Speichern", "printText": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "<PERSON>e ansehen", "addText": "Hinzufügen", "removeText": "Entfernen", "continueText": "<PERSON><PERSON>", "yesRemove": "<PERSON><PERSON>, entfernen", "yesCancel": "Ja, abbrechen", "yesInform": "<PERSON><PERSON>, informiert", "yesDelete": "<PERSON><PERSON>, entfernen", "yesUpdate": "Ja, aktualisieren", "yesUndo": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yesReset": "<PERSON><PERSON>, zurücksetzen", "yesContinue": "<PERSON><PERSON>, weiter", "okText": "Ok", "close": "Schließen", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "reset": "Z<PERSON>ücksetzen", "apply": "<PERSON><PERSON><PERSON>", "updatedText": "Aktualisieren", "back": "Zurück", "submit": "Übertragen", "resetDefault": "Z<PERSON>ücksetzen", "send": "Send", "sendViaKim": "Über KIM versenden", "noReject": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "yesAllow": "<PERSON><PERSON>, zulassen", "gotoPatientProfile": "Gehe zum Patientenprofil", "assignText": "<PERSON><PERSON><PERSON><PERSON>g", "editText": "<PERSON><PERSON><PERSON>", "deleteText": "Löschen", "uploadText": "Hochladen", "openAdmin": "Admin-App", "importAgain": "Erneut importieren"}, "LeavingModal": {"leavePageTitle": "Seite ohne Speichern verlassen?", "leavePageContent": "<PERSON>cht gespeicherte Änderungen gehen verloren, wenn Sie die Seite verlassen.", "cancelLeave": "<PERSON><PERSON>", "confirmLeave": "Ja, verlassen"}, "InputCreateValueComponent": {"create": "<PERSON><PERSON><PERSON><PERSON>", "selectOrCreate": "Wählen oder erstellen Sie durch Eingabe", "btnCancel": "Abbrechen", "btnOk": "Löschen", "title": "Diesen Wert löschen?", "message": "Diese Aktion kann nicht rückgängig gemacht werden."}, "PrintPreviewDialog": {"title": "Vorschau", "keyboardNavigationHint": "Alt+↑↓ um zu navigieren", "printSettings": "Druckeinstellungen", "printer": "<PERSON>ucker", "tray": "Fach", "cancelledDialog": {"title": "Druckvorgang abgebrochen", "description": "Vorgang wurde abgebrochen. Sie werden zur Vorschau weitergeleitet."}}, "UnitMedication": {"kg": "kg", "m": "m", "cm": "cm", "mmHg": "mmHg"}, "HintError": {"hintGenderMRejectM": "nur bei männlichen Patienten", "hintGenderMRejectNotM": "überwiegend bei männlichen Patienten", "hintGenderWRejectM": "nur bei weiblichen Patienten", "hintGenderWRejectNotM": "überwiegend bei weiblichen Patienten", "ageUnit": "Jahre", "hintMaxAgeFrom124MinAge1": "Ab {{age}} <PERSON><PERSON><PERSON>", "hintMaxAgeFrom124WithoutMinAge1": "Ab {{age}} <PERSON><PERSON><PERSON>", "hintMaxAgeTo124": "Altersgruppe zwischen {{ageMin}} Jahren und unter {{ageMax}} Jahren", "hintMaxAgeFrom124MinAge28": "Ab {{age}} <PERSON><PERSON>", "hintAge": "Bis {{age}} <PERSON><PERSON><PERSON>", "hintPermanentDiagnosis": "Bitte die Kodierung überprüfen: Dieser Kode beschreibt einen akuten, vorübergehenden Zustand und ist für die Kategorisierung als \"Dauerdiagnose\" im weiteren Behandlungsverlauf nicht geeignet."}, "Cancelled": "Abgebrochen", "Optional": "Optional", "More": "<PERSON><PERSON>", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Edit": "<PERSON><PERSON><PERSON>", "View": "Anzeigen", "Remove": "Löschen", "Saved": "Gespe<PERSON>rt", "Printed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Finished": "Fertiggestellt", "Warning": "Achtung", "Error": "Fehlermeldung", "AddNewHashString": "Hash-String hinzufügen", "DateTimeFormatWithoutDot": "TTMMJJJJ", "autoDoucumentFailed": "Das automatische Dokumentieren der Leistungsziffer ist fehlgeschlagen", "autoDocumentSuccess": "Leistungsziffer {{code}} wurde automatisch dokumentiert. ", "TextModuleDialog": {"OmimGFileUpdatedTitle": "OMIM-G Daten aktualisiert", "OmimGFileUpdatedHeader": "Die OMIM-G-Datei wurde aktualisiert. Bitte sehen <PERSON> sich die folgenden OMIM-G-Ketten an:"}, "Layout": {"willExpire": "l<PERSON><PERSON>t innerhalb von {{countDays}} Tagen ab", "postpone": "Verschieben", "dismiss": "<PERSON><PERSON><PERSON>s dauer<PERSON> abweisen"}, "AutoDocumentModal": {"titleSent": "Allow automatic documenting for sending eDoctor Letter?", "titleReceive": "Möchten Sie die automatische Dokumentation von Leistungsziffern für empfangene eArztbriefe freigeben?", "descriptionSent": "Allow the system to automatically document service code {{code}} for for each sent eDoctor Letter within the current quarter.", "descriptionReceive": "Freigabe zum automatischen Dokumentieren der Leistungsziffer {{code}} für jeden empfangenen eArztbrief im laufenden Quartal.", "subDescription": "Die Einstellungen können innerhalb der Admin Seite unter Praxiseinstellungen - Abrechnung geändert werden", "descriptionSend": "Freigabe zum automatischen Dokumentieren der Leistungsziffer {{code}} für jeden versendeten eArztbrief im laufenden Quartal.", "titleSend": "<PERSON><PERSON> Leistungsziffern nach dem Senden eines eArztbriefes automatisch dokumentiert werden?"}, "ErrorMessages": {"invalidFormat": "{{fieldName}} hat ein ungültiges Format. Format muss sein: {{pattern}}.", "restrictLANRValue": "Der (<PERSON><PERSON>tz-)wert „{{fieldValue}}“ ist obsolet und als Feldinhalt von FK 0212, 4241, 4242, 5099 und 4299 unzulässig."}, "Form": {"required": "{{name}} ist ein <PERSON>"}, "PrinterSetting": {"printerProfile": "Druckerprofile", "printerSettings": "Druckeinstellungen", "printer": "<PERSON>ucker", "tray": "Tray", "copies": "Kopien", "paperSize": "Papierformat", "printAsDuplex": "Als Duplex drucken", "printAsBlancForm": "Als Blankoformular drucken", "margin": "Seitenrand", "units": "Exemplare", "top": "Top", "left": "Linksbündig", "right": "Right", "bottom": "Fußzeile", "adjustWidth": "<PERSON><PERSON><PERSON> an<PERSON>en", "adjustHeight": "<PERSON><PERSON><PERSON> an<PERSON>", "in": "in", "mm": "mm", "A4_portrait": "A4 - Portrait", "A4_landscape": "A4 - Querformat", "A5_portrait": "A5 - Portrait", "A5_landscape": "A5 - Querformat", "A6_portrait": "A6 - Portrait", "A6_landscape": "A6 - Querformat", "printerProfileRequired": "Druckerprofil ist ein Pflichtfeld", "printerRequired": "Druckauswahl ist ein Pflichtfeld", "trayRequired": "<PERSON>ach ist ein P<PERSON>feld", "copiesRequired": "<PERSON><PERSON>n ist ein Pflichtfeld", "paperSizeRequired": "Papierformat ist ein Pflichtfeld", "noPrinterProfileFound": "<PERSON>s wurde kein Druckerprofil erstellt"}, "InputPinModal": {"title": "Eingabe der PIN", "subTitle": "Gehen Sie bitte zum Kartenterminal, in das diese Karte eingesteckt wird, und geben Sie die PIN ein, um das Formular zu un<PERSON>chreiben, oder entnehmen Sie die Karte, um den Vorgang abzubrechen."}, "InputPinFailedModal": {"title": "Signieren und versenden fehlgeschlagen", "subTitle": "Signatur ist fehlgeschlagen. Bitte versuchen Sie es erneut."}}