{"EbmDialog": {"titleCreate": "Leistungsz<PERSON><PERSON> man<PERSON> erstellen", "titleEdit": "Leistungsziffer bear<PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "save": "Aktualisieren", "cancel": "Abbrechen", "statistiks": "Statistiken", "errServiceCodeEmpty": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "errShortDescriptionEmpty": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "shortServiceCode": "Die eingegebene Leistungsziffer ist zu kurz", "outOfRangeErrorMessage": "Sie geben ein Datum außerhalb des zulässigen Bereichs ein", "invalidDateErrorMessage": "Ungültige Zeitangabe", "errorCreateUpdateEbm": {"EndDateOutOfDate": "Enddatum ungültig"}, "validity": "Gültigkeit", "validityPlaceholder": "GÜLTIG AB", "billableFrom": "<PERSON><PERSON><PERSON><PERSON> ab", "checkQuaterEbm": "Updates werden nur für dieses Quartal gespeichert (Q{{quater}}/{{year}}). Das betrifft nicht die Leistungsziffern, die schon dokumentiert oder abgerechnet sind.", "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kurzbeschreibung", "longDescription": "Vollständige Beschreibung", "chapterAssignment": "Kapitelzuordnung", "receiptDescription": "Quittungstext", "notes": "Anmerkung", "fromDate": "<PERSON><PERSON><PERSON><PERSON> ab", "validTo": "gültig bis", "startBillingFrom": "Abrechnungsbeginn am", "evaluation": "Basiswert", "pointValue": "Punktewert", "priceInEuro": "Preis in €", "testTime": "Prüfzeit in min", "testTimeText": "Prüfzeit", "requiredTimeText": "Zeitbedarf", "profileTime": "Zeitprofil", "requiredTime": "Zeitbedarf in min", "profitTime": "Zeitprofit", "performanceGroup": "Leistungsgruppe", "unit": "Einheit", "duration": "<PERSON><PERSON>", "leavePageTitle": "<PERSON>öchten Sie die Seite verlassen?", "leavePageContent": "<PERSON>cht gespeicherte Änderungen gehen verloren, wenn Sie die Seite verlassen.", "confirmLeave": "Ja, verlassen", "cancelLeave": "<PERSON><PERSON>", "minutes": "Minuten", "units": {"0": "-", "1": "Punkte", "2": "Euro (€)", "5": "Unbewertet"}, "invalidCode": "Ungültiges Format. Bitte geben Sie 5 Ziffern (nnnnn) oder 5 Ziffern mit einem Buchstaben (nnnnnA) in.", "ErrorCode_SDEBM_Service_Code_Existed": "Leistungsziffer existiert bereits"}, "MaterialCostDialog": {"createMaterialCostTitle": "Sachkosten", "description": "Beschreibung", "price": "Pre<PERSON>", "manufacturer": "<PERSON><PERSON><PERSON>", "productNumber": "Produktnummer", "confirmCreateMaterialCost": "Sachkosten erstellen", "viewAll": "<PERSON>e ansehen", "cancelCreate": "Abbrechen", "createSuccessMaterialCost": "Materialkosten wurden erfolgreich dokumentiert", "leaveCreateMaterialCostContent": "Diese Sachkosten existieren nicht. Möchten Sie diese jetzt erstellen?", "invoiceNumber": "Rechnungsnummer"}, "MaterialCostOverview": {"Search": "<PERSON><PERSON>", "title": "Sachkosten", "descriptionCol": "Name", "createSuccess": "Sachkosten wurden erfolgreich hinzugefügt", "updateSuccess": "Sachkosten wurden erfolgreich aktualisiert ", "deleteSuccess": "Sachkosten wurden entfernt", "editMc": "Sachkosten bearbeiten", "removeMc": "Sachkosten löschen", "priceCol": "Pre<PERSON>", "productNumberCol": "Produktnummer", "manufacturerCol": "<PERSON><PERSON><PERSON>", "removeCostTitle": "Sachkosten löschen?", "cancelButtonCloseFormAlert": "<PERSON><PERSON>", "confirmButtonCloseFormAlert": "Ja, löschen", "contentConfirmCloseAlert": "Diese Aktion kann nicht rückgängig gemacht werden.", "invoiceNumberCol": "Rechnungsnummer"}, "AdditionalInfo": {"5002": "Art der Untersuchung", "5003": "(N)BSNR des vermittelten Facharztes", "5005": "Multiplikator", "5006": "Um-Uhrzeit", "5008": "DKM", "5009": "freier Begründungstext", "5302": "LANR/BSNR des Auftraggebers", "5010": "<PERSON><PERSON><PERSON><PERSON>", "5011": "Sachkosten", "5012": "Sachkosten in Cent", "5013": "Prozent der Leistung", "5015": "Organ", "5016": "Name des Arztes", "5017": "Besuchsort bei Hausbesuchen", "5018": "Zone bei Besuchen", "5019": "Erbringungsort/Standort des Gerätes", "5020": "Wiederholungsuntersuchung", "5021": "Jahr der letzten Krebsfrüherkennungsuntersuchung", "5023": "GO-Nummern-Zusatz", "5024": "GNR-Zusatzkennzeichen poststationär erbrachte Leistungen", "5025": "Aufnahmedatum", "5026": "Entlassungsdatum", "5034": "OP-Datum", "5035": "OP-Schlüssel", "5036": "GNR als Begründung", "5038": "Komplikation", "5040": "Patientennummer (EDV) des FEK-Bogens oder der eDokumentation Hautkrebs-Screening", "5041": "Seitenlokalisation OPS", "5042": "Mengenangabe KM /AM", "5043": "Maßeinheit KM /AM", "5070": "OMIM-G-Kode", "5071": "OMIM-P-Kode", "5072": "Gen-Name", "5073": "Art der Erkrankung", "5074": "Name Her<PERSON><PERSON>/ Lieferant", "5075": "Artikel-/ Modelln<PERSON>mer", "5098": "(N)BSNR des Ortes der Leistungserbringung", "5099": "Lebenslange Arztnummer (LANR) des Vertragsarztes/Vertragspsychotherapeuten", "5100": "ASV-Teamnummer des Arzt", "5101": "Pseudo-LANR (für Krankenhausärzte im Rahmen der ASV-Abrechnung) des LE", "5102": "Krankenhaus-IK (im Rahmen der ASV-Abrechnung)", "5200": "Pseudo-GNR", "5900": "Tagtrennung", "5300": "Gesamt", "factor": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "price": "Pre<PERSON>", "5050": "Melde-ID Implantateregister", "5051": "Hash-String Implantateregister", "5052": "Hash-<PERSON><PERSON>ateregister", "5076": "Rechnungsnummer", "5077": "HGNC-Gensymbol", "5078": "Gen-Name", "5079": "Art der Erkrankung"}, "DefaultOmimGNote": "Der eingegebene Wert ist nicht im Katalog enthalten und darf nicht in die Abrechnung mit übernommen werden! Stattdessen wird der Ersatzwert übernommen. Der Gen-Name muss eingegeben werden.", "DefaultOmimPNote": "Der eingegebene Wert ist nicht im Katalog enthalten und darf nicht in die Abrechnung übernommen werden! Stattdessen wird der Ersatzwert übernommen.", "SubstituteValue": "Ersatzwert", "OmimGChains": "OMIM-G-Kette", "OmimGCodes": "OMIM-G-Kode", "HgncChains": "HGNC-Kette", "HgncCodes": "HGNC-Kode", "chain": "<PERSON><PERSON>", "TagRecommanded": "<PERSON><PERSON><PERSON><PERSON>", "OPS_laterity": {"left": "Links", "right": "<PERSON><PERSON><PERSON>", "both_sides": "Beidseitig"}, "DropDownItems": {"zone of visits": {"Z1": "Z1", "Z2": "Z2", "Z3": "Z3", "Z4": "Z4"}, "repet of treatment": {"1": "<PERSON>a", "0": "<PERSON><PERSON>"}, "ops": {"R": "<PERSON><PERSON><PERSON>", "L": "Links", "B": "Beidseitig"}, "unit of measure km / am": {"1": "1", "2": "2", "3": "3"}}, "SdebmOverview": {"Search": "<PERSON><PERSON>", "viewEbm": "Details anzeigen", "editEbm": "Leistungsziffer bear<PERSON>", "removeEbm": "Leistungsziffer löschen", "title": "EBM Le<PERSON>sziffern", "codeCol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createSuccessEbm": "Leistungsziffer wurde erfolgreich hinzugefügt", "deleteSuccessEbm": "Leistungsz<PERSON><PERSON> wurde entfernt", "updateSuccessEbm": "Leistungsziffer wurde aktualisiert", "descriptionCol": "Kurzbeschreibung", "validFromCol": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "validToCol": "Gültig bis", "priceCol": "Pre<PERSON>", "pointsCol": "Punkte", "removeEbmTitle": "Diese <PERSON>iff<PERSON> löschen?", "cancelButtonCloseFormAlert": "<PERSON><PERSON>", "confirmButtonCloseFormAlert": "Ja, löschen", "contentConfirmCloseAlert": "Die Leistungsziffer wird dauerhaft für (Q{{quater}}/{{year}}) gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "contentConfirmCloseAlertVersion2": "Die Leistungsziffer wird dauerhaft entfernt. Diese Aktion kann nicht rückgängig gemacht werden.", "ErrorCode_SDEBM_Cannot_Deleted": "<PERSON><PERSON>iffer existiert bereits in diesem Quartal.", "updated": "<PERSON>ktual<PERSON><PERSON>", "selfCreated": "<PERSON><PERSON>", "onlyShowSelfCreatedServiceCodes": "<PERSON>ur manuell erstellte Leistungsziffern anzeigen"}, "GoaOverview": {"title": "GOÄ Leistungsziffern", "numberCol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "descriptionCol": "Beschreibung", "pointsCol": "Punkte", "priceCol": "Pre<PERSON>", "pointValue": "Punktewert: 1 Punkt = {{value}} Cent", "editGoa": "<PERSON><PERSON><PERSON>", "removeGoa": "Löschen", "updated": "<PERSON>ktual<PERSON><PERSON>", "selfCreated": "<PERSON><PERSON>", "noResultFoundTitle": "<PERSON>s wurden keine Ergebnisse gefunden", "createGoaService": "Leistungsziffer <PERSON>", "CreateDialog": {"titleEdit": "Leistungsziffer bear<PERSON>", "titleCreate": "Leistungsziffer <PERSON>", "serviceCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remark": "Anmerkung", "goaNumberRequired": "Leistungsziffer ist ein Pflichtfeld", "chapter": "<PERSON><PERSON><PERSON>", "description": "Beschreibung", "descriptionRequired": "Beschreibung ist ein Pflichtfeld", "validFrom": "g<PERSON><PERSON><PERSON> ab", "validTo": "gültig bis", "evaluation": "Basiswert", "serviceFactor": "<PERSON><PERSON><PERSON>", "unit": "Einheit", "Unit_Euros": "Euro", "Unit_Points": "Punkte", "maximumEvaluation": "<p>Maximaler Basiswert: <strong>{{maxEvaluation}}</strong></p>", "rules": "Regeln", "excludedCodes": "ausgeschlossene Leistungsziffern:", "SelectExcludedCodesDialog": {"title": "Wählen Sie ausgeschlossene Leistungsziffern aus", "selectedCodes": "Ausgewählte Leistungen:", "searchPlaceHolder": "Suche...", "cancelBtn": "Abbrechen", "addBtn": "Ausgewählte Leistungen hinzufügen", "number": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Beschreibung"}, "selectExcludedCodes": "Auswahl ausgeschlossener Leistungsziffern", "createSuccess": "Leistungsziffer wurde erstellt.", "createFailed": "Leistungsziffer konnte nicht erstellt werden.", "deleteSuccess": "Leistungsz<PERSON><PERSON> wurde entfernt", "deleteFailed": "Leistungsziffer konnte nicht entfernt werden", "updateSuccess": "Leistungsziffer wurde aktualisiert", "updateFailed": "Leistungsziffer konnte nicht aktualisiert werden.", "outOfRangeErrorMessage": "Sie geben ein Datum außerhalb des zulässigen Bereichs ein", "invalidDateErrorMessage": "Ungültiger Zeitraum"}}, "UVGoaOverview": {"title": "UV-GOÄ Leistungsziffern", "numberCol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortDescriptionCol": "Kurzbeschreibung", "descriptionCol": "Beschreibung", "validFromCol": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "generalTreatmentCol": "Allgemeine Behandlung", "specificTreatmentCol": "Besondere Behandlung", "viewGoa": "Anzeigen", "editGoa": "<PERSON><PERSON><PERSON>", "removeGoa": "Löschen", "updated": "<PERSON>ktual<PERSON><PERSON>", "selfCreated": "<PERSON><PERSON>", "noResultFoundTitle": "<PERSON>s wurden keine Ergebnisse gefunden", "createGoaService": "Leistungsziffer <PERSON>", "CreateDialog": {"titleEdit": "Leistungsziffer bear<PERSON>", "titleCreate": "Leistungsziffer <PERSON>", "notBillable": "<PERSON>cht abrechenbar", "number": "GNR", "serviceCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberRequired": "Leistungsziffer ist ein Pflichtfeld", "shortDescription": "Kurzbeschreibung", "shortDescriptionRequired": "Kurzbeschreibung ist ein Pflichtfeld", "description": "Beschreibung", "descriptionRequired": "Beschreibung ist ein Pflichtfeld", "validFrom": "g<PERSON><PERSON><PERSON> ab", "validFromRequired": "gültig ab ist ein Pflichtfeld", "validTo": "gültig bis", "evaluation": "Auswertung (in Euro)", "unit": "€", "placeHolder": "0,00", "generalTreatment": "Allgemeine Behandlung", "specificTreatment": "Besondere Behandlung", "generalCosts": "Allgemeine Kosten", "specialCosts": "Besondere Kosten", "outOfRangeErrorMessage": "Sie geben ein Datum außerhalb des zulässigen Bereichs ein", "invalidDateErrorMessage": "Ungültiger Zeitraum"}}}