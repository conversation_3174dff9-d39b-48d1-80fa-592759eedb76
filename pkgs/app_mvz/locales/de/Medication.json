{"titlePopupUpdateStatus": "Medikationsdatenbank ist veraltet.", "descPopupUpdateStatus": "Die Medikationsdatenbank ist veraltet. Bitte wenden Sie sich an Ihren Softwarehersteller.", "notFoundPzn": "Pzn not found", "MedicationPlanEmptyState": {"noMedicationsYet": "Noch keine hinzugefügten Medikamente vorhanden", "noMedicationDescription": "Dauerhaft gespeicherte Medikamente werden hier angezeigt"}, "GreenRezept": {"quantity": "<PERSON><PERSON>", "intakeInterval": "Einnahmezeiten (z.B. 0-0-0-0)", "asNeeded": "Bedarfsmedikation", "furtherInfo": "Zusätzliche Informationen", "favPatientHint": "„Teilnahme Facharzt-Prg.\""}, "KkRezept": {"quantity": "<PERSON><PERSON>", "intakeInterval": "Einnahmezeiten (z.B. 0-0-0-0)", "asNeeded": "Bedarfsmedikation", "furtherInfo": "Zusätzliche Informationen"}, "MedicationIntakeInterval": {"intakeInterval": "Einnahmezeiten (z.B. 0-0-0-0)", "dj": "Dj", "djTooltip": "Markieren sie diese Checkbox falls der Patient schon Anweisungen zur Einnahme erhalten hat", "gema": "gemäß schriftlicher Anweisung"}, "MedicationPrintPreview": {"regulations": "Vorschau", "form": "Formular", "forms": "Formulare", "rezeptSaved": "Rezept wurde ges<PERSON>ichert", "rezeptsSaved": "Rezepte wurden gespeichert", "1rezeptsSaved": "Ihre Verordnung wurde erfolgreich gespeichert", "1rezeptPrescribed": "Verordnung erfolgreich", "ERezeptStore": "eRezept wurde ges<PERSON>ichert", "ERezeptSent": "eRezept(e) erfolgreich versendet", "ERezeptsSent": "eRezepte erfolgreich versendet", "rezeptPrinted": "<PERSON><PERSON><PERSON> wurde ged<PERSON>t"}, "MedicationShoppingBag": {"title": "Rezeptübersicht", "rezept": "Rezept", "prescribe": "<PERSON><PERSON><PERSON><PERSON>", "medication": "Medikation", "quantity": "<PERSON><PERSON>", "rezeptpool": "Rezeptübersicht", "removeAll": "Alle löschen", "headerRemoveAll": "Alle Medikamente vom Rezept löschen?", "removeAllDescription": "Alle Medikamente auf dem Rezept werden gelöscht. Dies kann nicht rückgängig gemacht werden.", "keepIt": "Abbrechen", "removedFromRezeptpool": "Erfolgreich aus der Rezeptübersicht entfernt", "keyboardHint": "Tastenkombination: Strg+V (Verordnen), Strg+Entf (Alle Medikamente löschen)", "noItemInRezeptPool": "Keine Medikamente in der Rezeptübersicht gespeichert.", "goToPrintPreview": "Vorschau", "failedToCreateEPrescription": "eRezept konnte nicht erstellt werden", "erezeptTrainingDoctorAuthorization": "Ein Ausbildungsarzt ist nicht berechtigt eRezepte zu erstellen"}, "Medication": {"title": "Beschluss des G-BA gem. § 35a SGB V", "medicationSearch": "Medikationssuche", "medicationPlan": "BMP", "prescribedMedication": "Verordnete Medikamente", "noResultFound": "<PERSON>s wurden keine Ergebnisse gefunden", "noResultMessage": "<PERSON>s ist keine andere Packungsgröße für dieses Medikament vorhanden.", "noSubtanceMessage": "Es sind keine anderen Konzentrationen verfügbar für dieses Medikament.", "gbaNote": "<PERSON>s wurden keine Beschlüsse zu den Diagnosen gefunden", "filterPackageSizes": "PACKUNGSGRÖßE"}, "SearchMedicationBox": {"headerAdvanceSearch": "oder wählen Sie hier ein Suchkriterium", "atcCode": "ATC- Klassifikation", "manufacturer": "<PERSON><PERSON><PERSON>", "pzn": "PZN", "significanceOfTheATCCode": "Signifikanz der ATC-Klassifikation", "substanceName": "Kombinierte Wirkstoffsuche", "substanceSearch": "Mono-Wirkstoffsuche", "tradeName": "Handelsname", "inputAnySearch": "Suchen Sie nach dem Medikationsnamen oder wählen Sie ein Suchkriterium aus", "addAsFreetext": "Als Freitext einfügen", "icdCode": "ICD-10", "searchIn": "<PERSON><PERSON> in", "noResults": "<PERSON><PERSON>", "readhand": "Rote-Hand-<PERSON><PERSON><PERSON>", "iww": "Indikationsgerechten wirtschaftlichen Wirkstoffauswahl", "amrl": "Arzneimittel-Richtlinie", "iconList": "Rezeptübersicht"}, "MedicationHeaderResult": {"tradeName": "Handelsname", "price": "AVP", "coPayment": "Zuzahlung", "size": "Größe", "form": "Formular", "drugCategory": "Arzneimittelkategorie", "Manufacturer": "<PERSON><PERSON><PERSON>"}, "MedicineRow": {"secondaryInformation": "Fachinformationen", "discount": "<PERSON><PERSON><PERSON><PERSON>", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez", "gRez": "G-Rez", "otc": "OTC", "lifeStyle": "Lifestyle", "av": "AV", "pIcon": "P", "tootlTipExpandComponentList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die in der Regel durch qualitative und wirtschaftliche Alternativen unter Beachtung medizinischer Ausschlusskriterien substituiert werden können.", "addToRezept": "Zum Rezept hinzufügen", "tootipPharmacyRequire": "Unwirtschaftliches Präparat", "tooltipNegativeListMedication": "Negativliste (unwirtschaftliches Präparat)", "tooltipAv": "<PERSON><PERSON><PERSON>", "tooltipPricus": "Priscus-Liste (für ältere Menschen potentiell ungeeignet)", "tooltipLifeStyle": "Anwendung zur Erhöhung der Lebensqualität", "tooltipOtc": "Over the counter", "tooltipTReceipt": "T-Rezept", "tooltipGReceipt": "<PERSON><PERSON><PERSON><PERSON>", "tooltipKReceipt": "Kassenrezept", "tooltipBtmReceipt": "Betäubungsmittel", "attention": "Achtung", "ignore": "Ignorieren", "selectAnotherOne": "Alternative auswählen", "attentionAv": "<PERSON><PERSON><PERSON>", "attentionAvContent": "<PERSON><PERSON><PERSON>, möglicherweise in einzelnen Verpackungen erhältlich.", "attentionPricus": "<PERSON><PERSON><PERSON>", "attentionPricusContent": "Dieses Medikament ist nicht für Patienten ab dem 65. Lebensjahr geeignet.", "attentionNegativeList": "Negativliste", "attentionNegativeListContent": "Bitte prüfen Sie, ob das Medikament ökonomisch und wirtschaftlich sinnvoll ist.", "rezepturPool": "Rezeptübersicht", "medicationPlan": "BMP", "addTo": "Hinzufügen", "btnRecipePool": "Rezeptübersicht", "addToShoppingBag": "Zum Rezept hinzugefügt", "priscusMedicationWarning": "Dieses Medikament ist nicht für Patienten ab dem 65. Lebensjahr geeignet."}, "SearchEmptyState": {"searchForAMedication": "Medikation suchen", "startTypingAboveToSearch": "Beginnen Sie ihre Eingabe um Suchergebnisse hier angezeigt zu bekommen", "noSearchResultFor": "<PERSON><PERSON>", "add": "Hinzufügen", "asFreeTextPrescription": "als Freitextverordnung", "addedMedicineToRezept": "Zur Rezeptübersicht hinzugefügt"}, "SearchMedicationResultView": {"tradeName": "Handelsname", "price": "AVP", "coPayment": "Zuzahlung", "size": "Größe", "form": "Formular", "drugCategory": "Arzneimittelkategorie", "Manufacturer": "<PERSON><PERSON><PERSON>", "backToSearchResults": "Z<PERSON><PERSON> zu den Suchergebnissen", "rationalePharmakotherapie": "Rationale Pharmakotherapie", "addedMedicineToRezept": "Zum Rezept hinzugefügt", "ingore": "Ignorieren", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "SecondaryMedicationView": {"importOrReImportProduct": "<PERSON>s handelt sich um ein Re- / Importprodukt", "medicalDevice": "Medizinprodukt", "tfg": "TFG", "coPayment": "Zuzahlung", "furterCost": "Mehrkosten", "totalPayment": "Gesamtbetrag", "interactions": "Interaktionen", "sideEffects": "Nebenwirkungen", "usingDuringPregnacy": "Verwendung während der Schwangerschaft und dem Stillen", "indications": "Gebrauchsanweisungen", "contraindications": "Kontraindikationen", "regulations": "Regulierungshinweise", "hintsAndWarnings": "Standard Hinweise", "ingredients": "Weitere Inhaltsstoffe", "medicineDirective": "Arzneimittel- Richtlinie"}, "SecondLayerDialog": {"headerAccessOtherView": "Ansicht wechseln", "btnPriceComparison": "Preisvergleich", "btnComposition": "Zusammensetzung", "btnTechnicalInformation": "Fachinformation", "btnAlternatives": "Alternativen", "btnGBADecision": "G-BA Richtlinie", "btnPracticeSpecifics": "Praxisbesonderheiten", "btnPriscusList": "<PERSON><PERSON><PERSON>", "btnTherapyHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headerImportanceInformation": "Wichtige Informationen", "headerGeneralInfo": "Allgemeine Information", "lblAtcCode": "ATC", "lblDosageForm": "Darreichungsform", "lblActiveIngredient": "<PERSON><PERSON><PERSON><PERSON>", "lblOtherIngredients": "Weitere Inhaltsstoffe", "lblManufacturer": "<PERSON><PERSON><PERSON>", "lblDivisible": "Teilbarkeit", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "headerPricing": "Preisübersicht", "lblPrice": "Apothekenverkaufspreis", "lblFixedPrice": "Festbetragskennzeichnung", "lblCoPayment": "Zuzahlung", "lblAdditionalCosts": "Mehrzahl<PERSON>", "lblTotalCoPayment": "Gesamtzuzahlung", "patientCosts": "Patientenkosten (GKV)", "noCopayment": "keine <PERSON>", "tooltipLowerThanFixedAmount": "Geringer als der Festbetrag", "tooltipHigherThanFixedAmount": "<PERSON><PERSON><PERSON> als der Festbetrag", "headerMedicinesDirective": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyboardHint": "Strg+R (zum Rezept hinzufügen), Strg+M (zum Medikationsplan hinzufügen)", "btnMP": "Auf BMP übernehmen", "btnRecipePool": "<PERSON>f Rezept übernehmen", "addToShoppingBag": "Zum Rezept hinzugefügt", "processingRefill": "Wiederverordnung läuft...", "priscusMedicationWarning": "Dieses Medikament ist nicht für Patienten geeignet, die 65 Jahre oder älter sind.", "attention": "Achtung", "ignore": "Ignorieren", "selectAnotherOne": "Auswahl ändern", "updateSuccess": "Medikationsplan wurde aktualisiert", "doctorSampleAddedToTimeline": "Ärztemuster wurde dem Behandlungsverlauf hinzugefügt", "timeline": "<PERSON><PERSON><PERSON><PERSON>", "freeCopayment": "befreit", "prescribableMedicalProductTitle4": "Anlage IV: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prescribableMedicalProductTitle5": "Anlage V: Verordnungsfähiges Medizinprodukt nach § 31 Abs. 1 \nMedizinisch notwendige Fälle:", "prescribableMedicalProductTitle": "Verordnungsfähiges Medizinprodukt nach § 31 Abs. 1 \nMedizinische notwendige Fälle:", "limitation": "Befristung", "discount130b": "Erstattung nach §130b", "titlePznAtcCode": "M<PERSON>chten Si<PERSON> fortfahren?", "bodyPznAtcCode": "Es gibt ein Rezept aus der Vergangenheit mit denselben Wirkstoffen ({{name}}), von dem der Patient noch bis zum {{endDate}} übrig haben muss anhand der letzten Dosieranweisung.", "buttonYesPznAtcCode": "<PERSON><PERSON>, ve<PERSON><PERSON><PERSON>", "buttonNoPznAtcCode": "<PERSON><PERSON>, abbrechen"}, "RenderAnnotation": {"edit": "<PERSON><PERSON><PERSON>", "asNeeded": "Bedarfsmedikation", "favPatientHint": "„Teilnahme Facharzt-Prg.\"", "gema": "gemäß schriftlicher Anweisung", "digitalHealth": "Digitale Gesundheitsanwendung\n---------------------\n{{pzn}}\n---------------------\n{{name}}\n---------------------"}, "MedicineDetail": {"removedFromRezept": "Aus dem Rezept entfernt", "noPackageSize": "<PERSON><PERSON> weiteren Packungsgrößen verfügbar", "freetextPlaceholder": "Handelsname/ Wirkstoffname", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez", "gRez": "G-Rez", "private": "Privat", "aokNordwet": "AOK Nordwest", "aokBremen": "AOK Bremen", "muster16aBay": "M16a bay", "asNeeded": "Bedarfsmedikation", "furtherInformation": "Zusätzliche Informationen", "ersatzverordnung": "Ersatzverordnung", "artificialInsemination": "Indikation künstl. Befruchtung", "sonderkennzeichen": "Sonderkennzeichen", "gema": "gemäß schriftlicher Anweisung", "updateMedTypeSuccess": "<PERSON>ktual<PERSON><PERSON>", "vaccination": "Impfung", "eRezept": "eRezept", "drugForm": "Darreichungsform", "Muster_16": "K-Rez", "Gruenes_Rezept": "G-Rez", "Blaues_Rezept": "Privat"}, "PrintPreviewSetting": {"prescribedDiga": "Verordnete DiGA", "caveHeader": "CAVE", "printSetting": "Druckereinstellungen", "doctorLabel": "Arzt/Ärztin", "rezept": "Rezept", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez", "gRez": "G-Rez", "print": "<PERSON><PERSON><PERSON>", "printer": "<PERSON>ucker", "tray": "Fach", "save": "Speichern", "private": "Privat", "add": "Hinzufügen", "ePrescription": "eRezept", "printQR": "eRezept-Ausdruck", "preScribePartial": "Mehrfachverordnung", "partialPrescriptionTooltipContent": "Ärzte können bis zu 4 Teilrezepte ausstellen. Ziel ist die langfristige Versorgung von Patienten, die eine kontinuierliche Versorgung mit einem bestimmten Medikament benötigen.", "startDate": "Beginndatum", "endDate": "Enddatum", "prescribeAll": "Alle verordnen", "prescribe": "<PERSON><PERSON><PERSON><PERSON>", "specialInformation": "Fachinformation", "btmSelectLabel": "Sonderkennzeichen", "warnMedicines": "Eine erneute Verordnung von {{warnMedicines}} mit der Packungsgröße N1 wird aus wirtschaftlicher Sicht nicht empfohlen.", "signBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "signWith": "Signiert mit", "comfortSignatureStatus": {"one": "{{signatures}} Komfortsignatur läuft in {{count}} Stunden ab", "other": "{{signatures}} Komfortsignatur läuft in {{count}} Stunden ab"}, "hba": "HBA", "smcb": "SMC-B", "optional": "(Optional)", "invalidDateRange": "Das Enddatum muss mit dem Startdatum übereinstimmen oder nach dem Startdatum liegen", "prescribePartial": "Mehrfachverordnung", "preview": "Vorschau", "medication": "Medikation", "sprechsundenbedarf": "Sprechstundenbedarf", "quarter": "Quartal {{quarter}}/{{year}}", "vos": "VoS", "vosSubmitted": "VoS wurden übertragen"}, "MedicationPlan": {"textmoduleHint": "In Freitextfeldern Shortcut eingeben oder Strg + Leertaste drücken.", "medicationPlan": "Medikationsplan", "substanceName": "<PERSON><PERSON><PERSON><PERSON>", "prescribed": "Datum", "tradeName": "Handelsname", "tradeNameColumn": "Handelsname/Wirkstoff", "form": "FORM", "unit": "Einheit", "hint": "<PERSON><PERSON><PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "modified": "Verändert", "updateSuccess": "Medikationsplan wurde aktualisiert", "mergedSuccess": "Medikationspläne wurden zusammengeführt", "rearrangeIconDescription": "<PERSON><PERSON><PERSON> Si<PERSON> die Datei, die Si<PERSON> verschieben möchten, mit der linken Maustaste an und lassen Sie die Taste gedrückt.", "medication": "Medikation", "recipe": "Rezept", "freetext": "Freitext", "subheading": "Überschrift", "titleAdditionalData": "Zusatzdaten", "addAdditionalData": "Patientenangaben hinzufügen", "editAdditionalData": "Patientenangaben bearbeiten", "updateOutdatePzn": "Die Gültigkeit der Medikamente mit der Datenbank überprüfen", "gender": "Geschlecht", "pregnant": "<PERSON><PERSON><PERSON>", "breastfeeding": "Stillend", "allergies": "Allergien", "weight": "Gewicht", "height": "Größe", "creatinine": "<PERSON><PERSON><PERSON><PERSON>", "additionalData": "Zusätzlicher Freitext", "scanMedicationPlan": "Medikationsplan scannen", "printMedicationPlan": "Medikationsplan drucken", "addNewEntry": "<PERSON><PERSON><PERSON> Eintrag", "addNewRecipe": "Neues Rezept hinzufügen", "addNewFreeText": "Freitext hinzufügen", "addNewSubheading": "Überschrift hinzufügen", "addNewMedication": "Medikamente hinzufügen", "editRecipe": "<PERSON><PERSON><PERSON> bearbeiten", "editFreeText": "Freitext bearbeiten", "editSubheading": "Überschrift bearbeiten", "editMedication": "Medikamente bearbeiten", "optional": "Optional", "additionalLine": "Zusatzzeile", "cancel": "Abbrechen", "save": "Speichern", "remove": "Löschen", "notNow": "Abbrechen", "saveAnyway": "Fortfahren und PZN löschen", "requiredField": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "confirmRemoveMessage": "Medikation wird aus dem Medikationsplan entfernt.", "confirmEditMedicationMessage": "Änderungen sollten nur erfolgen, wenn sie unbedingt er<PERSON> sind, da hierdurch eine PZN-basierte AMTS-Prüfung nur noch eingeschränkt möglich ist.", "refill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editDetails": "Details bearbeiten", "viewDetails": "Details anzeigen", "secondaryInfo": "Fachinformationen", "removeEntry": "Eintrag löschen", "editAMedication": "Medikament bearbeiten", "drugUnit": "Einheit", "drugForm": "Arzneimittelform", "intakeInterval": "Einnahmezeiten (z.B. 0-0-0-0)", "substance": "<PERSON><PERSON><PERSON><PERSON>", "concentration": "Konzentration", "addSubstance": "<PERSON><PERSON><PERSON><PERSON> hinzufügen", "showMoreSubstance": "Alle anzeigen ({{number}})", "showLessSubstance": "<PERSON><PERSON> anzeigen", "print": "<PERSON><PERSON><PERSON>", "doctor": "Arzt/ Ärztin", "startScanTitle": "Medikationsplan wird gescannt…", "startScanDescription": "<PERSON>te richten Sie den Scanner auf den Barcode aus und warten Sie, bis der Scanvorgang abgeschlossen ist", "stopScanning": "<PERSON><PERSON><PERSON> abbrechen", "scanningTitle": "Seite {{scannedPages}} von {{totalPages}} Seiten gescannt", "scanningDescription": "Dieser Medikationsplan hat {{totalPages}} Seite/n, Sie können das Scannen fortsetzen oder die gescannte/n Seite/n zusammenführen.", "useScannedPages": "Gescannte Seite(n) verwenden", "stopScanningAndUseScannedPage": "Scanvorgang unterbrechen und eingescannte(n) Seite(n) verwenden", "scanNextPage": "Nächste Seite scannen", "unmatchedPatient": "Fehlerhafter <PERSON>gleich", "scanAgain": "<PERSON><PERSON>", "newStatus": "<PERSON>eu", "matchedStatus": "<PERSON><PERSON> Verä<PERSON>", "conflictingStatus": "Konflikt", "outDateStatus": "PZN wurde nicht gefunden", "duplicateSubstance": "WIederholte aufkommender Wirkstoff", "unmatchedPatientMessage": "Der von Ihnen gescannte Medikamentenplan gehört:", "unmatchedPatientDescriptionForScanner": "<PERSON> anderer möglicher Patient:", "unmatchedPatientDescriptionForCurrent": "Aktuell ausgewählter Patient:", "unmatchedPatientMessageNoExisted": "Der von Ihnen gescannte Medikamentenplan gehört:", "correctPatient": "<PERSON><PERSON><PERSON>", "scanCompleteTitle": "Scannen abgeschlossen", "scanCompleteDescription": "Der von Ihnen gescannte Medikationsplan enthält keine abweichenden Informationen.", "backToMP": "Zurück zum Medikationsplan", "scanDuplicatedMessage": "Diese Seite wurde bereits gescannt!", "scanFailedMessage": "Scannen fehlgeschlagen, bitte versuchen Sie es erneut", "currentConflictDataTitle": "Konflikte aus dem vorherigen Medikationsplan", "mergeDescription": "{{new}} neue Einträge und {{conflict}} Konflikte gefunden. Wählen Sie die Einträge aus, welche übernommen werden sollen:", "quitMerge": "Zusammenführung der Datensätze beenden", "takeOver": "Zusammenführen", "notedMerge": "Die ausgewählte Medikation wird mit dem neuen Medikationsplan zusammengeführt. Unterüberschriften können nicht zusammengeführt werden.", "mergeDialogTitle": "Medikationsplan zusammenführen {{patientInfo}}", "currentMedicationPlan": "Aktueller Medikationsplan", "scannedMedicationPlan": "Eingescannter Medikationsplan", "patientInformation": "Patienteninformation", "conflict": "Konflikt", "new": "<PERSON>eu", "quitMergingTitle": "Zusammenführen beenden", "quitMergingDescription": "<PERSON>hr Vorgang wird nicht gespeichert. Diese Aktion kann nicht rückgängig gemacht werden.", "continueMerging": "<PERSON><PERSON>", "quitMerging": "Ja, beenden", "printCompleted": "<PERSON>uck er<PERSON><PERSON><PERSON><PERSON>", "changePackageSize": "Packungsgröße ändern", "prescribeAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "Zur Wiederverordnung hinzufügen", "changeConcentration": "Konzentration ändern", "tradeNameMerge": "Handelsname", "strength": "Stärke", "status": "Status", "timeout": "Zeitüberschreitung", "markAsDoctorSample": "Als Ärztemuster markieren", "pznNotFound": "Die PZN aus dem eingescannten Medikationsplan konnte in der Arzneimitteldatenbank nicht gefunden werden. Bitte prüfen Sie die PZN auf Richtigkeit und Aktualität. Sie können Informationen nach dem Zusammenführen ergänzen und ändern.", "shortage_of_patient_parameter": "Der Patientenparameter wurde aus Platzgründen gekürzt. Bitte überprüfen Sie diese noch einmal, falls Anpassungen erforderlich sind.", "additionalDataHeader": {"allergy": "Allergie", "isShowGender": "Geschlecht", "isPregnant": "<PERSON><PERSON><PERSON>", "isBreastFeeding": "Stillzeit", "weight": "Gewicht", "height": "Größe", "creatinine": "<PERSON><PERSON><PERSON><PERSON>", "additionalValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "Zusatzdaten", "details": "Details"}, "syncVitalParameter": "Datenübernahme Vitalwerte", "syncVitalParameterFail": "Vitalparameter konnten nicht übernommen werden", "syncVitalParameterSuccessfully": "Vitalwerte wurden aktualisiert"}, "Medical": {"weight": "Gewicht", "height": "Größe", "allergy": "Allergie(n)", "creatinine": "<PERSON><PERSON><PERSON><PERSON>", "gender": "Geschlecht"}, "MedicationPlanRow": {"editDetails": "Details bearbeiten", "secondaryInfo": "Fachinformationen", "remove": "Löschen", "removeMedicationHeader": "Medikament löschen?", "youAreGoingToRemove": "Sie sind dabei das Medikament", "fromMedicationPlan": "aus dem Medikationsplan zu löschen.", "cancel": "Abbrechen", "medicationPlanUpdated": "Medikationsplan wurde aktualisiert"}, "PrescribedMedication": {"prescribed": "DATUM", "tradeName": "HANDELSNAME", "size": "GRÖßE", "furtherInfo": "ZUSÄTZLICHE INFORMATION", "prescribedBy": "ARZT/ ÄRZTIN", "mp": "BMP", "addToRezept": "Zum Rezept hinzufügen", "secondaryInfo": "Fachinformationen", "status": "Status", "date": "Datum", "doctor": "Arz<PERSON>"}, "EmptyPrescribeMedicationView": {"noPrescribed": "<PERSON><PERSON> V<PERSON>nungen vorhanden", "noPrescribeDescription": "Hier werden Medikamente angezeigt, die in ihrer Praxis verordnet wurden."}, "PrescribedMedicineRow": {"addToRezept": "Zum Rezept hinzufügen", "secondaryInfo": "Fachinformationen", "addToMedicationPlan": "Hinzufügen zum Medikationsplan", "removeFromMedicationPlan": "Aus dem Medikationsplan löschen", "medicationPlanUpdated": "Medikationsplan wurde aktualisiert", "removeMedicationHeader": "Medikament löschen?", "youAreGoingToRemove": "Sie sind dabei das Medikament", "fromMedicationPlan": "aus dem Medikationsplan zu löschen.", "cancel": "Abbrechen", "remove": "Löschen", "asNeeded": "Bedarfsmedikation", "refill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewForm": "Anzeigen", "prescribeAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "Zur Wiederverordnung hinzufügen", "changePackageSize": "Packungsgröße ändern", "viewSecondaryInfo": "Fachinformationen anzeigen", "changeConcentration": "Konzentration ändern", "printed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saved": "Gespe<PERSON>rt"}, "RefillMedicationView": {"refillHeader": "Rationale Pharmakotherapie", "of": "von", "addedMedicineToRezept": "Zum Rezept hinzugefügt"}, "SubsitutionView": {"selectedMedication": "Das ausgewählte Medikament ist", "substitution": "Substitution", "substitutionDescription": "Bitte prüfen Sie, ob der ausgewiesene Substitutionsvorschlag im konkreten Einzelfall, z.B. in Bezug auf Zulassungsindikationen, Wirkstärke und Darreichungsform, medizinisch umsetzbar ist."}, "WarningMissingDiagnoseDialog": {"missingDiagnose": "<PERSON><PERSON><PERSON><PERSON> Diagnose", "missingDiagnoseHint": "Die Medikation des Versicherten deutet auf eine bestimmte Krankheit bzw. Diagnose hin (z.B. Insulin auf Diabetes mellitus). In diesem Fall konnte in der Dokumentation keine passende Diagnose ermittelt werden. Bitte überprüfen Sie die Diagnosen und deren Kodierung.", "ingore": "Ignorieren", "selectAnotherMedication": "Wählen Sie eine Substitution"}, "RemoveShoppingBagDialog": {"replaceAll": "<PERSON><PERSON>, er<PERSON><PERSON>", "headerReplaceAll": "Medikamente in der Rezeptübersicht ersetzen?", "replaceAllDescription": "Alle Medikamente, die vorher zur Rezeptübersicht hinzugefügt wurden, werden dauerhaft entfernt. Nur die ausgewählten Medikamente verbleiben in der Rezeptübersicht. Wollen Sie trotzdem fortfahren?", "keepIt": "<PERSON><PERSON>, abbrechen", "removedFromRezeptpool": "<PERSON><PERSON>, er<PERSON><PERSON>"}, "SearchResults": {"onlyShowFavouriteMedications": "Anzeige Hausapotheke", "onlyShowDiscountedProducts": "<PERSON>ur rabattierte Produkte", "noDiscountedProductsAvailable": "<PERSON><PERSON> raba<PERSON>en Produkte verfügbar", "noFavouriteAvailable": "<PERSON>s wurden keine Favoriten mit den angegebenen Suchparametern gefunden. <PERSON><PERSON> werden nun in die Medikationssuche weitergeleitet.", "onlyShowRegisteredProducts": "Nur registrierte Produkte", "onlyMonoSearch": "Nur Monopräpar<PERSON>"}, "MedicationTable": {"colMedication": "Medikation", "colSize": "Packungsgröße", "colDosageForm": "Form", "colPrice": "AVP", "tooltipColZuza_1": "Berechnung:", "tooltipColZuza_2": "Wenn Apothekenpreis > Festbetrag und dieser < 50 EUR", "tooltipColZuza_3": "=> Zuzahlung = 5 EUR + Apothekenpreis - Festbetrag", "tooltipColZuza_4": "Wenn ein vertraulicher Erstattungsbetrag vorliegt, erfolgt die Zuzahlung nach § 61 Satz 1 SGB V.", "colZuza": "Zuzahlung", "colManufacturer": "<PERSON><PERSON><PERSON>", "tooltipLowerThanFixedAmount": "Geringer als der Festbetrag", "tooltipHigherThanFixedAmount": "<PERSON><PERSON><PERSON> als der Festbetrag", "tooltipAlternativeProductsAvaiable": "Preisgünstigere Alternativen mit demselben Wirkstoff sind verfügbar", "tooltipAV": "<PERSON><PERSON><PERSON>", "noDataMsg": "<PERSON><PERSON> gefunden", "prescribeFreetextMedication": "<PERSON><PERSON><PERSON>verordnung", "addToMedicationPlan": "Hinzufügen zum Medikationsplan", "tooltipAMR": "AM-RL Anlage", "tooltipAMRIII": "AM-RL Anlage III", "tooltipAMRIV": "AM-RL Anlage IV", "tooltipAMRV": "AM-RL Anlage V", "tooltipP": "Priscus-Liste", "tooltipPOver65Age": "Priscus-Liste (für ältere Menschen potentiell ungeeignet)", "tooltipLifestyle": "Lifestyle-Arzneimittel - Anwendung zur Erhöhung der Lebensqualität", "conditionalTooltipLifestyle": "bedingtes Lifestyle-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelLifestyle": "Lifestyle Arzneimittel", "labelConditionalTooltipLifestyle": "Bedingtes Lifestyle Arzneimittel", "tooltipPharmacy": "Apothekenpflichtig", "tooltipThumbdown": "Negativliste - unwirtschaftliches Präparat", "tooltipAM": "Ärztemuster", "tooltipPrescriptionRegulations": "Verordnungseinschränkung", "tooltipPrescriptionExclusions": "<PERSON><PERSON>rd<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltipTherapyHint": "Therapiehinweis der G-BA vorhanden", "tooltipImportProduct": "<PERSON>s handelt sich um ein Importprodukt", "tooltipTFG": "Dokumentationspflicht Transfusionsgesetz", "labelLifeStyle": "Lifestyle", "labelConditionalLifestyle": "Bedingtes Lifestyle", "tooltipGPA": "G-BA Beschluss §35a SGB V", "tooltipARV": "Arzneimittel ist von einer regionalen Vereinbarung betroffen", "tooltipOTC": "Over the counter", "tooltipOTX": "O<PERSON>s, die verordnungsfähig sind", "tooltipDrugGreen": "Es liegt eine Rabattvereinbarung vor.", "tooltipDrugRed": "Es liegen Rabattvereinbarungen nach § 130a Absatz 8 SGB V für die jeweilige Krankenkasse vor", "tooltipTRez": "T-Rezepte sind Spezialrezepte, die ausschließlich zur Verschreibung von Arzneimitteln mit den Wirkstoffen Lenalidomid, Pomalidomid und Thalidomid verwendet werden dürfen.", "tooltipKRez": "Kassenrezept", "tooltipGRez": "<PERSON><PERSON><PERSON><PERSON>", "tooltipBTM": "Betäubungsmittelrezept", "tooltipPrescriptionRequired": "Verschreibungspflichtig", "tooltipMedicineProduct": "Medizinprodukt", "viewRedHandLetters": "Rote-Hand-Briefe anzeigen", "viewBlueHandLetters": "Blaue-Hand-Briefe anzeigen", "tooltipBandage": "Verordnungsfähiges Verbandmittel nach §31 Absatz 1a SGB V", "isFavourite": "<PERSON><PERSON><PERSON><PERSON>", "tooltipMedicationSortAsc": "KBV Sortierung (↑)", "tooltipMedicationSortDesc": "Aufsteigende Sortierung nach Handelsname (↓)"}, "HandLettersDialog": {"titleRedHand": "Rote-Hand-<PERSON><PERSON><PERSON>", "titleBlueHand": "<PERSON><PERSON><PERSON>-Hand-Brief<PERSON>"}, "PriceComparision": {"hint": "Bitte prüfen Sie, ob der ausgewiesene Substitutionsvorschlag im konkreten Einzelfall, z.B. in Bezug auf Zulassungsindikationen, Wirkstärke und Darreichungsform, medizinisch umsetzbar ist.", "hintForSpecificMedications": "Für die gewünschte Packungsgröße gibt es keine Substitute, es werden die Substitute für alle Normgrößen angezeigt", "title": "<PERSON>isvergleich für „{{tradeName}} {{size}} {{price}}“", "lblOnlyDiscount": "<PERSON>ur rabattierte Produkte", "lblMedicineOriginal": "Das ausgewählte Medikament ist:", "lblMedicineComparison": "Rabattierte Produkte", "txtNoDiscound": "Keine reduzierten Produkte verfügbar"}, "Refill": {"title": "Auswahl Medikation zur Wiederverordnung", "lblBack": "Zurück", "lblNext": "<PERSON><PERSON>", "lblFinish": "Vorschau", "lbInfoText": "Es könnten günstigere Alternativen für dieses Medikament zur Verfügung stehen. Wählen Sie, welches Medikament Sie verordnen möchten.", "lbSVInfoText": "Bitte prüfen Sie, ob der ausgewiesene Substitutionsvorschlag im konkreten Einzelfall, z.B. in Bezug auf Zulassungsindikationen, Wirkstärke und Darreichungsform, medizinisch umsetzbar ist.", "lbPreviouslyPrescribed": "<PERSON><PERSON><PERSON><PERSON>(en)", "lbCheaperMedications": "Günstigere Medikamente", "lbOtherMedications": "Andere Medikation", "lblCancel": "Abbrechen", "lblPrescribe": "<PERSON><PERSON><PERSON><PERSON>"}, "Alternatives": {"modalTitle": "Alternativen anzeigen", "modalDescription": "<PERSON>s stehen mehrere Alternativen für dieses Medikament zur Verfügung.", "modalLblView": "Anzeigen", "modalLblViewPlaceholder": "Auswählen", "modalCptCancel": "Abbrechen", "modalCptContinue": "<PERSON><PERSON>", "title": "Alternativen für „{{tradeName}} {{size}} {{price}}“", "lblMedicineOriginal": "Das ausgewählte Medikament ist:", "lblMedicineAlternatives": "{{name}} Substitutionsvorschlag:", "lblMedicineAlternativesDescription": "Bitte prüfen Sie, ob der ausgewiesene Substitutionsvorschlag im konkreten Einzelfall, z.B. in Bezug auf Zulassungsindikationen, Wirkstärke und Darreichungsform, medizinisch umsetzbar ist.", "lblFilter": "Filter", "titleFilterDialog": "Filter", "lblFilterDialogReset": "Z<PERSON>ücksetzen", "lblFilterDialogApply": "<PERSON><PERSON> anwenden", "lblFilterDialogConcentration": "Konzentration", "lblFilterDialogSize": "Größe", "lblFilterDialogDosageForm": "Darreichungsform", "placeholderFilterDialogMinimum": "Minimum", "placeholderFilterDialogMaximum": "Maximum", "placeholderFilterDialogSelectOptions": "Optionen wählen", "placeholderFilterDialogSelect": "Auswählen", "filterApplied": "Filter wurde angewendet"}, "SecondaryView": {"shortcut_keys_secondary_view": "Alt+↑↓ (zum navigieren), Strg+R (zum Rezept hinzufügen), Strg+M (zum Medikationsplan hinzufügen)", "btnAddRP": "<PERSON>f Rezept übernehmen", "btnAddMP": "Zum BMP hinzufügen"}, "EditDrugName": {"editDrugName": "Medikamentennamen bearbeiten", "description": "Durch das Bearbeiten des Medikamentennamen, wird die existierende PZN aus der Rezeptübersicht entfernt. Sind Si<PERSON> sicher, dass Si<PERSON> fortfahren wollen?  ", "drugName": "Medikamentenname", "save": "Speichern", "cancel": "Abbrechen", "editDrugNameSuccess": "Medikament erfolgreich bearbeitet"}, "GBADialog": {"title": "Indikationsgerechte wirtschaftliche Wirkstoffauswahl (IWW)", "search": "<PERSON><PERSON>", "standardTooltip": "Standard (grün)", "reserveDrugTooltip": "Reserve-Präparat (gelb)", "notRecommendedTooltip": "nicht empfohlen (rot)", "noIndicationTooltip": "(nur KV Bremen) (grau) keine Indikation"}, "IWWRegulations": {"title": "Indikationsgerechte wirtschaftliche Wirkstoffauswahl", "search": "<PERSON><PERSON>", "validityFromTo": "Gültigkeit: {{fromDate}} bis {{toDate}}", "validityFrom": "Gültigkeit: ab dem {{fromDate}}", "createdOn": "Erstellt am: {{date}}", "titleCollapse": "Arzneimittelvereinbarungen", "anhang": "<PERSON><PERSON>", "arzneimittelvereinbarung": "Arzneimittelvereinbarung - {{title}}"}, "MedicationStatistics": {"title": "Verordnungs-/Medikamentenstatistik", "patientPrescriptions": "Patientenverordnungen", "consultationPrescriptions": "Sprechstundenbedarf"}, "PatientPrescriptions": {"medicationStatistics": "Medikamentenstatistik", "filters": "Filter", "export": "Export", "dateOfPrescription": "Verordnet am", "prescribingDoctor": "ve<PERSON><PERSON><PERSON> von", "patientReference": "Patient/in", "insuranceNo": "Versicherungsnummer", "ATCCode": "ATC", "amount": "<PERSON><PERSON>", "tradeName": "Handelsname", "activeSubstance": "<PERSON><PERSON><PERSON><PERSON>", "PZN": "PZN", "price": "Pre<PERSON>", "filtersFor": "<PERSON><PERSON>", "patients": "Patienten", "gotoPatientProfile": "Gehe zum Patientenprofil", "scheinType": "<PERSON><PERSON><PERSON>", "kv": "KV", "hzvOrFav": "SV (HzV oder FaV)", "privateOrIgel": "Privat (Privat oder IGeL)"}, "Common": {"cannotOpenSecondaryInfo": "Fachinformationen können nicht angezeigt werden", "outDateStatus": "PZN wurde nicht gefunden", "outDateStatusDesciption": "In der aktuellen Medikamentendatenbank liegen keine Angaben zur PZN vor. Fehlende Daten können nach dem Hinzufügen ergänzt oder ersetzt werden.", "titleCannotPrintBmpCauseSubHeading": "BMP kann nicht gedruckt werden", "cannotPrintBmpCauseSubHeading": "Medikationsplan kann nicht generiert werden. Zwischenüberschriften ohne Eintragszuordnung sind nicht zulässig.\nBitte entfernen Sie Einträge oder weisen die Überschrift Einträgen zu."}, "PatientInformationModal": {"title": "Patienteninformation", "emptyData": "<PERSON>ine Patienteninformation vorhanden", "description": "Kehren Sie zum Patientenprofil zurück, um Informationen einzugeben."}, "GeneralGBA": {"AM-RL": "AM-RL", "title": "Arzneimittel-Richtlinie"}}