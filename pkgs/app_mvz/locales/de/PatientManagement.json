{"OmimGChain": {"overview": "OMIM-G-Ketten Übersicht", "placeholder": "Suche nach OMIM-G-Kette", "name": "Name", "omimGChain": "OMIM-G-Kette", "add": "OMIM-G-<PERSON><PERSON>", "addCode": "OMIM hinzufügen", "edit": "OMIM-G<PERSON><PERSON><PERSON> bear<PERSON>", "remove": "OMIM-G-<PERSON><PERSON> l<PERSON>", "submit": "submit", "close": "Schließen", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "OMIM-G-<PERSON><PERSON> au<PERSON>wählen", "requiredShortCut": "Aufrufkürzel ist in Pflichtfeld", "titleConfirmRemove": "OMIM-<PERSON><PERSON><PERSON><PERSON>?", "titleConfirmDescription": "Die OMIM-G-<PERSON><PERSON> wird dauerhaft gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "titleConfirmSuccess": "Erfolg<PERSON><PERSON>", "ominCodes": "OMIM-G CODE {{code}}", "createOmimGChainSuccess": "OMIM-G-<PERSON><PERSON> erfolgreich erstellt", "updateOmimGChainSuccess": "OMIM-G-<PERSON><PERSON> erfolgreich geupdatet", "createChain": "OMIM-<PERSON><PERSON>"}, "HgncChain": {"overview": "HGNC-Ketten Übersicht", "placeholder": "Suche nach HGNC-Kette", "name": "Name", "hgncChain": "HGNC-Kette", "add": "HGNC-<PERSON><PERSON>", "edit": "HGNC-<PERSON><PERSON> bear<PERSON>", "addCode": "HGNC hinzufügen", "remove": "HGNC-<PERSON><PERSON> l<PERSON>", "submit": "submit", "close": "Schließen", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredShortCut": "Aufrufkürzel ist in Pflichtfeld", "select": "HGNC-<PERSON><PERSON> au<PERSON>w<PERSON>hlen", "titleConfirmRemove": "HGNC-<PERSON><PERSON>?", "titleConfirmDescription": "Die HGNC-Kette wird dauerhaft gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "titleConfirmSuccess": "Erfolg<PERSON><PERSON>", "hgncCodes": "HGNC CODE {{code}}", "createHgncChainSuccess": "HGNC-<PERSON><PERSON> erfolgreich erstellt", "updateHgncChainSuccess": "HGNC-<PERSON><PERSON> erfolgreich geupdatet", "createChain": "HGNC-<PERSON><PERSON>"}, "PatientSignatureForm": {"getPatientSignature": "Unterschrift des Patienten", "getPatientSignatureContent": "Der Patient hat alle Dokumente zur Vertragsteilnahmeerklärung unterzeichnet. Die unterschriebene Teilnahmeerklärung verbleibt in der Praxis, eine Kopie ist dem Patienten auszuhändigen.", "signature1Label": "Patient hat die Teilnahmeerklärung unterschrieben", "signature2Label": "Patient hat die Versorgungszusage unterschrieben", "signatureRegisterLabel": "Patient hat die Teilnahmeerklärung unterschrieben", "signatureParticipationLabel": "Patient hat die Teilnahmeerklärung unterzeichnet"}, "PatientOfflineEnrollment": {"getPatientSignature": "Patientenunterschrift", "offlineEnrollment": "Der Patient muss neben dem Versicherteneinschreibebeleg zusätzlich die Teilnahmeerklärung des Versicherten unterzeichnen. Die unterschriebene Teilnahmeerklärung verbleibt in der Praxis, eine Kopie muss dem Patienten ausgehändigt werden."}, "PatientEnrollmentForm": {"errorMessageAlreadyParticipating": "Der Patient ist bereits in diesem Vertrag eingeschrieben.", "errorSignature1": "Für die Teilnahme an der HzV wird die Unterschrift des Versicherten benötigt!", "errorSignature2": "Die Versicherten-Unterschrift ist Voraussetzung für die Vertragsteilnahme!", "hzvOnlineEnrollmentSubmitted": "HzV Einschreibung wurde übermittelt", "favOnlineEnrollmentSubmitted": "FaV Einschreibung wurde übermittelt", "hzvOfflineEnrollmentSaved": "HzV- Einschreibung wurde abgeschlossen", "enrollError": "<PERSON>s ist ein Problem aufgetreten. Wenn das Problem weiterhin besteht, wenden <PERSON> sich bitte an ihr Systemhaus, um Unterstützung zu bekommen.", "submitOfflineButtonLabel": "Speichern", "submitOnlineButtonLabel": "Speichern", "cancelButtonCloseFormAlert": "Abbrechen", "confirmButtonCloseFormAlert": "Verlassen", "titleConfirmCloseAlert": "Einschreibung abbrechen", "contentConfirmCloseAlert": "Sie sind dabei die Einschreibung abzubrechen, Ihre Daten wurden noch nicht übermittelt. Sie könnten damit später fortfahren.", "defaultMessageHpmError": "Die Datenübermittlung von Versichertenteilnahmeerklärungen ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "prepareContractForms": "Vertragsformulare werden vorbereitet...", "printRequestSent": "Druckan<PERSON><PERSON> wurde versendet", "hzvContractName": "HZV-CONTRACT-NAME", "favContractName": "FAV-CONTRACT-NAME", "errorSignatureParticipationHzV": "Für die Teilnahme an der HzV wird die Unterschrift des Versicherten benötigt!", "errorSignatureParticipationFAV": "Für die Teilnahme an der FaV wird die Unterschrift des Versicherten benötigt!", "errorRegistration": "Die Versicherten-Unterschrift ist Voraussetzung für die Vertragsteilnahme!", "progressSaveOn": "Zuletzt gespeichert am {{ time }}"}, "PrintForm": {"printFormsLabel": "<PERSON><PERSON><PERSON>", "printFormLabel": "<PERSON><PERSON><PERSON>", "tagPrintedForm": "(GEDRUCKT)", "registrationDocument": "Teilnahmeerklärung", "declareParticipation": "Teilnahmeerklärung", "careManagement1": "Pflege-Management 1", "careManagement2": "Pflege-Management 2", "printButtonLabel": "<PERSON><PERSON><PERSON>", "hzvContractName": "HzV Vertrag", "favContractName": "FaV Vertrag", "print": "<PERSON><PERSON><PERSON>", "printAgain": "<PERSON><PERSON><PERSON> drucken", "printBlankForm": "Blankoformular", "formPrinted": "Das Formular wurde gedru<PERSON>t"}, "PrintReviewForm": {"loadingPDFReview": "Vorschau wird vorbereitet...", "btnPrint": "<PERSON><PERSON><PERSON>", "formDetails": "Formularinformationen"}, "SelectContractDoctorForm": {"reasonNotSupportOkvsOrIkNumber": "Dieser Vertrag ist in ihrer KV-Region oder für diesen Kostenträger nicht verfügbar", "reasonDoctorNotSupport": "Der Arzt nimmt nicht an diesem Vertrag teil", "reasonAlreadyParticipated": "Der Patient ist bereits eingeschrieben", "reasonContractRequirementNotSatisfied": "Die Vertragsansprüche werden nicht erfüllt", "contractDoctorLabel": "Wählen Sie den/die Arzt/ Ärztin und den zugehörigen Vertrag", "selectContractLabel": "Vertrag auswählen", "noResult": "<PERSON><PERSON>", "selectAnOptionLabel": "Wählen Sie eine Option", "selectDoctorLabel": "Einschreibung bei", "selectChangeDoctorReasonLabel": "<PERSON><PERSON><PERSON>", "loadingContractsInformations": "Verträge werden vorbereitet...", "noAvailableContracts": "<PERSON><PERSON>/<PERSON><PERSON><PERSON> nimmt an diesem Vertrag teil", "checkingParticipationCondition": "Teilnahmevoraussetzungen prüfen", "hzv": "HzV", "fav": "FaV"}, "TeCodeForm": {"incorrectInputtedTeCode": "Das Eingabefeld TE-Code enthält einen falschen Wert. Nutzen Sie die gedruckte Teilnahmeerklärung, um den richtigen TE-Code einzugeben.", "enterTeCodeLabel": "Geben Sie den auf der Teilnahmeerklärung gedruckten TE-Code ein", "teCodeContent": "Mit Eingabe des TE-Codes wird vom Anwender bestätigt, dass die Angaben zum Versicherten gemäß seiner elektronischen Gesundheitskarte (eGK) vollständig erfasst und geprüft wurden, die Teilnahmeerklärung mit Unterschrift des Versicherten in Schriftform vorliegt, diese vorschriftsmäßig verwahrt wird und im Rahmen einer Prüfung vorgezeigt werden kann und für eventuell auftretende Schäden durch hier gemachte Falschangaben gehaftet wird."}, "ActiveContractParticipation": {"selectParticipationDate": "Vertragsbeginn", "createActiveCustodianParticipationDoctorRequired": "Die Angabe des einzuschreibenden Arztes ist verpflichtend", "createActiveCustodianParticipationContractRequired": "Vertrag muss angegeben werden", "createActiveCustodianParticipationStartDateRequired": "Startdatum ist ein Pflich<PERSON>feld", "applyFavGroup": "Für alle FaV-Verträge übernehmen", "cancelButton": "Abbrechen", "saveButton": "Speichern", "activateButton": "Aktivieren", "1stDayOfQuarter": "<PERSON><PERSON><PERSON> von Q{{quarter}}/{{year}}", "noEnrollmentWarning": "In der Vertragssoftware ist keine Beantragung der Patientenvertragsteilnahme vorhanden. <PERSON><PERSON> <PERSON> sicher sind, dass die Einschreibung dieser Patienten bei Ihnen bestätigt wurde, können Sie diese Patienten hiermit ohne Beantragung aktivieren."}, "DatePickerForm": {"errorDateIsRequired": "Datum ist ein P<PERSON>feld", "applyFavGroup": "Für alle FaV-Verträge übernehmen"}, "EnrollmentMenu": {"checkHpmParticipationOnlineHistory": "HPM-Prüfverlauf", "checkHpmParticipationOnline": "Online- Teilnahmeprüfung", "checkingHpmParticipationOnline": "Teilnahmestatus wird geprüft...", "hpmInactiveToolTip": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer.", "hpmActiveWithMvzNotEnrollment": "In der Vertragssoftware ist keine Beantragung der Patientenvertragsteilnahme vorhanden. <PERSON><PERSON> <PERSON> sicher sind, dass die Einschreibung dieser Patienten bei Ihnen bestätigt wurde, können Sie diese Patienten hiermit ohne Beantragung aktivieren.", "hpmActive": "Die Behandlung dieses Patienten ist für alle im HzV-Ziffernkranz enthaltenen Leistungen über die HzV abzurechnen.", "hpmActiveWithMvzTerminated": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "lastCheckHpmParticipation": "Letzte Teilnahmeprüfung war am {{checkedDate}} um {{checkedTime}}", "hpmConnectionFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut."}, "FavButton": {"contractTypeFaV": "FaV", "createActiveFavParticipationLabelDialog": "Vertragsteilnahme aktivieren", "activateFavGroupContractLabelItem": "Vertragsteilnahme aktivieren", "undoTerminateParticipationLabelItem": "<PERSON><PERSON> rückgä<PERSON><PERSON> machen", "undoTerminatedContractTitle": "Beendigung rückgängig machen?", "undoTerminatedContractContent": "Sie werden zu dem Stand vor Vertragsbeendigung zurückgesetzt.", "undoTerminatedGroupContractTitle": "Vertragsbeendigung rückgängig machen?", "undoTerminatedGroupContractContent": "Sie werden auf den Stand vor Vertragsbeendigung gesetzt.", "undoTerminatedContractConfirmButton": "<PERSON><PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON> machen", "cancelButton": "Abbrechen", "checkHpmParticipationSuccessfully": "Der Patient ist in die FAV eingeschrieben", "checkHpmParticipationSuccessfullyDeputy": "Der Patient ist in die FAV eingeschrieben", "checkHpmParticipationNoActiveParticipation": "Der Patient ist derzeit kein aktiver FaV- Vertragsteilnehmer", "checkHpmParticipationFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "cancelContractConfirmTitle": "Vertragsteilnahme stornieren?", "cancelContractConfirmContent": "<PERSON>e können diese Aktion nicht rückgängig machen.", "cancelContractCancelButton": "<PERSON><PERSON>", "cancelContractConfirmButton": "Ja, beenden", "cancelEnrollmentConfirmTitle": "Möchten Sie Ihren Einschreibeprozess wirklich abbrechen?", "cancelEnrollmentConfirmContent": "<PERSON>e können diese Aktion nicht rückgängig machen.", "cancelEnrollmentCancelButton": "<PERSON><PERSON>", "cancelEnrollmentConfirmButton": "Ja, beenden", "createEnrollmentLabelItem": "Einschreibung starten", "continueEnrollmentLabelItem": "Einschreibung fortsetzen", "cancelEnrollmentLabelItem": "Einschreibung abbrechen", "labelTodayShortcutDatePicker": "<PERSON><PERSON>", "1stDayOfQuarter1": "<PERSON><PERSON><PERSON> von Q1/{{year}}", "1stDayOfQuarter2": "<PERSON><PERSON><PERSON> von Q2/{{year}}", "1stDayOfQuarter3": "<PERSON><PERSON><PERSON> von Q3/{{year}}", "1stDayOfQuarter4": "<PERSON><PERSON><PERSON> von Q4/{{year}}", "activateButton": "Aktivieren", "createActiveFavGroupContractLabel": "Vertragsteilnahme aktivieren", "favButton_InvalidIkNumber": "Keine Einschreibung wegen ungültigem Kostenträger möglich", "favButton_CannotEnrollYet": "Derzeit ist keine Einschreibung möglich", "favButton_NotSupportedIkNumber": "Derzeit ist keine Einschreibung möglich (z.B. aufgrund fehlender Vertragsteilnahme des Arztes)", "favButton_Enrolled": "Aktiver FaV- Vertragsteilnehmer", "favButton_Terminated": "Die Vertragsteilnahme wurde beendet.", "favButton_Printed": "Der Patient ist derzeit kein aktiver FaV- Vertragsteilnehmer", "favButton_Pending": "Die Vertragsteilnahme wurde beantragt.", "favButton_NotEnrollYet": "Der Patient kann über den Facharztvertrag abgerechnet werden, wenn er eine Teilnahmeerklärung für das Facharztprogramm unterzeichnet hat und Sie diese versendet haben (Sofortabrechnung nach Einschreibung) und der Patient bereits am Hausarztvertrag teilnimmt.\nAlternativ kann dieser Patient über die KV abgerechnet werden.", "favButton_Faulty": "FEHLERHAFT\nDie Einschreibung des Patienten war nicht erfolgreich - klicken Sie hier um diesen erneut einzuschreiben", "favButton_Created": "ERZEUGT\nDer Patient ist derzeit kein aktiver Vertragsteilnehmer", "activateParticipationLabelItem": "Vertragsteilnahme aktivieren", "cancelParticipationGroupLabelItem": "Vertragsteilnahme stornieren", "terminateParticipationGroupLabelItem": "Vertragsteilnah<PERSON> beenden", "terminateGroupConractLabel": "Vertragsteilnahme(n) beenden", "terminateButton": "<PERSON>den", "changeParticipationStartDateLabelItem": "Beginndatum ändern", "changeParticipationGroupStartDateLabelItem": "Beginndatum ändern", "undoTerminateParticipationGroupLabelItem": "Beendigung rückgä<PERSON><PERSON> machen", "changeGroupContractStartDateTitle": "Beginndatum ändern", "changeContractStartDateSubmit": "Ändern", "favMainContractsTopDivider": "Facharztverträge", "performHpmCheckOnlineFavGroupLabelItem": "Online- Teilnahmeprüfung", "showHpmCheckHistoryDialogLabelItem": "HPM-Prüfverlauf", "participationForm": "Teilnahmeerklärung", "HpmHistoryDialogTitle": "Verlauf der HPM-Vertrag Teilnahme", "treatAsDeputyLabelItem": "Als Vertreterarzt behandeln", "activateCustodianTreatmentLabelItem": "Als Betreuarzt behandeln", "performHpmCheckOnlineParticipationLabelItem": "Online- Teilnahmeprüfung", "menuFooterFavTerminated": "<PERSON><PERSON> von {{updatedBy}} {{updatedDate}} um {{updatedTime}}", "terminateParticipationLabelItem": "Vertragsteilnah<PERSON> beenden", "cancelParticipationLabelItem": "Vertragsteilnahme stornieren", "system": "System"}, "FavHpmCheckHistory": {"OK_Custodian": "In die FAV eingeschrieben", "OK_Deputy": "Der Patient ist aktiver Vertragsteilnehmer (Vertreterfall)", "FAILED": "Verbindungsfehler", "NO_INFORMATION": "<PERSON><PERSON>", "message_OK": "Der Patient ist bereits aktiver FaV- Vertragsteilnehmer", "message_FAILED": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "message_NO_INFORMATION": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer"}, "FavContractItem": {"MissingInsuranceNumber": "Der Teilnahmestatus des Patienten kann nicht ermittelt werden, da keine gültige persönliche\nVersichertennummer der elektronischen Gesundheitskarte vorliegt.", "activateParticipationLabelItem": "Vertragsteilnahme aktivieren", "createActiveCustodianParticipationItem": "Vertragsteilnahme aktivieren", "cancelParticipationLabelItem": "Vertragsteilnahme stornieren", "changeDoctorLabelItem": "Arzt/ Ärztin wechseln", "terminateParticipationLabelItem": "Vertragsteilnah<PERSON> beenden", "changeParticipationStartDateLabelItem": "Beginndatum ändern", "undoTerminateParticipationLabelItem": "<PERSON><PERSON> rückgä<PERSON><PERSON> machen", "treatAsDeputyLabelItem": "Als Vertreterarzt behandeln", "activateCustodianTreatmentLabelItem": "Als Betreuarzt behandeln", "createEnrollmentLabelItem": "Einschreibung starten", "continueEnrollmentLabelItem": "Einschreibung fortfahren", "cancelEnrollmentLabelItem": "Einschreibung abbrechen", "changeContractStartDateTitle": "Beginndatum ändern", "changeContractStartDateSubmit": "Ändern", "changeContractStartDateCancel": "Abbrechen", "1stDayOfQuarter1": "1. <PERSON> von Q1/{{year}}", "1stDayOfQuarter2": "1. <PERSON> von Q2/{{year}}", "1stDayOfQuarter3": "1. <PERSON> von Q3/{{year}}", "1stDayOfQuarter4": "1. <PERSON> Q4/{{year}}", "labelTodayShortcutDatePicker": "<PERSON><PERSON>", "teminateContractParticipationTitle": "Vertragsteilnah<PERSON> beenden", "terminateContractParticipationSubmit": "<PERSON>den", "cancelButton": "Abbrechen", "createActiveCustodianParticipationLabelDialog": "Vertragsteilnahme aktivieren", "activateButton": "Aktivieren", "favContract_Created_Line1": "ERZEUGT", "favContract_Created_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "favContract_Printed_Line1": "GEDRUCKT", "favContract_Printed_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "favContract_Faulty_Line1": "FEHLERHAFT", "favContract_Faulty_Line2": "Die Einschreibung des Patienten war nicht erfolgreich - klicken Sie hier um diesen erneut einzuschreiben", "favContract_CannotEnrollYet_Line1": "Derzeit ist keine Einschreibung möglich", "favContract_NotEnrollYet_Line1": "Der Patient kann über den Facharztvertrag abgerechnet werden, \nwenn er eine Teilnahmeerklärung für das Facharztprogramm unterzeichnet hat und \nSie diese versendet haben (Sofortabrechnung nach Einschreibung) und der Patient \nbereits am Hausarztvertrag teilnimmt. \nAlternativ kann dieser Patient über die KV abgerechnet werden.", "favContract_Pending_Online_Line1": "Teilnahme an der FaV wurde beantragt am {{appliedDate}} um {{appliedTime}}.", "favContract_Pending_Offline_Line1": "Teilnahme an der FaV wurde beantragt am {{appliedDate}} um {{appliedTime}}.", "favContract_Enrolled_Line1": "Der Patient ist bereits aktiver FaV- Vertragsteilnehmer", "favContract_Enrolled_Line2": "{{contractName}} seit {{startDate}}", "favContract_Terminated_Line1": "Die Vertragsteilnahme wurde beendet am: {{endDate}}", "favContract_Terminated_HpmActive_Line1": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "favContract_Rejected_Line1": "Die Einschreibung wurde abgelehnt", "menuFooterFavTerminated": "<PERSON><PERSON> von {{updatedBy}} {{updatedDate}} um {{updatedTime}}", "button_Terminated_Line1": "Die Vertragsteilnahme wurde von {{userName}} zum {{endDate}} beendet", "hzvButton_Terminated_HpmActive_Line1": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "hzvButton_Terminated_ChangedIknumber_Line1": "<PERSON><PERSON><PERSON> den Patienten ist aufgrund eines Kassenwechsels eine Neueinschreibung erforderlich"}, "FavContractsList": {"favContractsTopDivider": "FaV im Detail"}, "HzvButton": {"statisHintPracticeLabel": "Der Patient ist bereits aktiver HzV- Vertragsteilnehmer", "custodianDoctorLabel": "<PERSON>rstbehand<PERSON>", "sinceLabel": "seit", "noResult": "<PERSON><PERSON>", "selectAnOptionLabel": "Wählen Sie eine Option", "atLabel": "am", "notEnrollentYetLabel": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "checkEnrollmentStatusLabel": "Einschreibestatus wird abgefragt...", "contractTypeHZV": "HzV", "contractTypeFaV": "FaV", "createEnrollmentLabelItem": "Einschreibung starten", "continueEnrollmentLabelItem": "Einschreibung fortsetzen", "activateParticipationLabelItem": "Vertragsteilnahme aktivieren", "createActiveCustodianParticipationItem": "Vertragsteilnahme aktivieren", "checkPotentialVERAHLabelItem": "Prüfe VERAH® Konditionen", "VERAHActivated": "VERAH® TopVersorgt wurde aktiviert", "VERAHActivatedDescription": "Die Leistung VERAH ® TopVersorgt wurde bei diesem Versicherten dokumentiert.", "VERAHEligible": "Berechtigt für VERAH® TopVersorgt", "VERAHEligibleDescription": "Patient ist für die Teilnahme an VERAH® TopVersorgt geeignet. <PERSON><PERSON>, dass Schulungsfähigkeit der Patienten vorliegen muss (vgl. Anlage 14, <PERSON><PERSON> 7) - zur Unterstützung können Sie sich am VERAH TopVersorgt-Leitfaden orientieren.", "VERAHNotEligible": "Nicht berechtigt für VERAH® TopVersorgt", "VERAHNotEligibleDescription": "Dieser Versicherte kann leider nicht im Rahmen von VERAH ® TopVersorgt betreut und abgerechnet werden.", "closeBtn": "OK, schließen", "cancelEnrollmentLabelItem": "Einschreibung abbrechen", "performHpmCheckOnlineFavGroupLabelItem": "Check Online Participation", "performHpmCheckOnlineLabelItem": "Online- Teilnahmeprüfung", "showHpmCheckHistoryDialogLabelItem": "HPM-Prüfverlauf", "cancelParticipationLabelItem": "Vertragsteilnahme stornieren", "changeDoctorLabelItem": "Arzt/Ärztin wechseln", "terminateParticipationLabelItem": "Vertragsteilnah<PERSON> beenden", "changeParticipationStartDateLabelItem": "Vertragsbeginn ändern", "undoTerminateParticipationLabelItem": "<PERSON><PERSON> rückgä<PERSON><PERSON> machen", "treatAsDeputyLabelItem": "Als Vertreterarzt behandeln", "activateCustodianTreatmentLabelItem": "Als Betreuarzt behandeln", "selectParticipationDate": "Vertragsbeginn", "createActiveCustodianParticipationLabelDialog": "Vertragsteilnahme aktivieren", "createActiveCustodianParticipationDoctorRequired": "Behandelnder Arzt muss angegeben werden", "createActiveCustodianParticipationContractRequired": "Vertragsauwahl ist ein Pflichtfeld", "createActiveCustodianParticipationStartDateRequired": "Beginndatum ist ein Pf<PERSON>feld", "createActiveCustodianParticipationSuccessful": "Vertragsteilnahme wurde erfolgreich aktiviert", "cancelContractConfirmTitle": "Vertragsteilnahme stornieren?", "cancelContractConfirmContent": "Dies kann nicht rückgängig gemacht werden.", "cancelContractCancelButton": "<PERSON><PERSON>", "cancelContractConfirmButton": "Ja, beenden", "cancelEnrollmentConfirmTitle": "Möchten Sie die Einschreibung abbrechen?", "cancelEnrollmentConfirmContent": "Dies kann nicht rückgängig gemacht werden.", "cancelEnrollmentCancelButton": "<PERSON><PERSON>", "cancelEnrollmentConfirmButton": "Ja, beenden", "undoTerminatedContractTitle": "Vertragsbeendigung rückgängig machen?", "undoTerminatedContractContent": "Sie werden auf den Stand vor Vertragsbeendigung gesetzt.", "undoTerminatedContractConfirmButton": "<PERSON><PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON> machen", "teminateContractParticipationTitle": "Vertragsteilnah<PERSON> beenden", "terminateContractParticipationSubmit": "<PERSON>den", "changeContractStartDateTitle": "Vertragsbeginn ändern", "changeContractStartDateSubmit": "<PERSON><PERSON><PERSON>", "changeContractStartDateCancel": "Abbrechen", "activateCustodianTitle": "Vertragsteilnahme aktivieren", "earliestDateWarning": "Das Datum der Vertragsaktivierung kann nicht vor dem Datum der Einschreibung liegen.", "activateCustodianCancelButton": "Abbrechen", "activateCustodianSubmitButton": "Aktivieren", "treatAsDeputyTitle": "Als Vertreterarzt behandeln", "treatAsDeputySuccessful": "Vertragsteilnahme (Vertreterfall) erfolgreich aktiviert", "createEnrollmentSuccessful": "Einschreibung wurde erstellt", "activateParticipationSuccessful": "Vertragsteilnahme aktiviert", "cancelEnrollmentSuccessful": "Einschreibung abgebrochen", "cancelContractSuccessful": "HzV- Vertrag storniert", "changeDoctorSuccessful": "Arztwechsel erfolgreich", "terminateParticipationSuccessful": "Vertragste<PERSON><PERSON><PERSON> wurde beendet", "changeParticipationStartDateSuccessful": "Vertragsbeginn geändert", "undoTerminatedContractSucessful": "Beendigung der Vertragsteilnahme erfolgreich rückgängig gemacht", "activateCustodianTreatmentSucessful": "Vertragsteilnahme wurde erfolgreich aktiviert", "hzvButton_CannotEnrollYet_Line1": "Derzeit ist keine Einschreibung möglich", "hzvButton_NotEnrollYet_Line1": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "hzvButton_NotEnrollYet_HpmActive_Line1": "In der Vertragssoftware ist keine Beantragung der Patientenvertragsteilnahme vorhanden. <PERSON><PERSON> <PERSON> sicher sind, dass die Einschreibung dieser Patienten bei Ihnen bestätigt wurde, können Sie diese Patienten hiermit ohne Beantragung aktivieren.", "hzvButton_Created_Line1": "ERZEUGT", "hzvButton_Created_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "hzvButton_Printed_Line1": "GEDRUCKT", "hzvButton_Printed_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "hzvButton_Faulty_Line1": "FEHLERHAFT", "hzvButton_Faulty_Line2": "Die Einschreibung des Patienten war nicht erfolgreich - klicken Sie hier um diesen erneut einzuschreiben", "hzvButton_Pending_IVP_Online_Line1": "HZV und Modulvertrag AOK Baden-Württemberg IV-Pflegeheim wurde beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_Pending_IVP_Online_Line2": "Status: Erfolgreich übermittelt", "hzvButton_Pending_Online_Line1": "HzV-Teilnahme wurde beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_Pending_Online_Line2": "Status: erfolgreich übermittelt", "hzvButton_Pending_Offline_Line1": "HzV-Teilnahme wurde beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputy_Offline_Line1": "Vertretungsfall {{contractName}}", "hzvButton_PendingDeputy_Offline_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputy_Offline_Line3": "Status: g<PERSON><PERSON><PERSON><PERSON>", "hzvButton_PendingDeputy_Online_Line1": "Vertretungsfall {{contractName}}", "hzvButton_PendingDeputy_Online_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputy_Online_Line3": "Status: erfolgreich übermittelt", "hzvButton_PendingDeputyChangingDoctor_Online_Line1": "Vertretungsfall {{contractName}}", "hzvButton_PendingDeputyChangingDoctor_Online_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputyChangingDoctor_Online_Line3": "Status: erfolgreich übermittelt", "hzvButton_PendingChangingDoctor_Online_Line1": "{{contractName}}", "hzvButton_PendingChangingDoctor_Online_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingChangingDoctor_Online_Line3": "Status: erfolgreich übermittelt", "hzvButton_Enrolled_Online_Line1": "Aktiver HzV- Vertragsteilnehmer", "hzvButton_Enrolled_Line5": "Aktiver HzV-Vertragsteilnehmer", "hzvButton_Enrolled_HpmActive_Online_Line1": "Aktiver HzV-Vertragsteilnehmer", "hzvButton_Enrolled_HpmActive_Line1": "Aktiver HzV-Vertragsteilnehmer", "hzvButton_NotAvailable_Line1": "Eine Teilnahmeprüfung für diesen Vertrag ist nicht möglich.", "hzvButton_Enrolled_Line1": "Der Patient ist bereits aktiver HzV- Vertragsteilnehmer", "hzvButton_Enrolled_Line2": "{{<PERSON><PERSON><PERSON>}}", "hzvButton_Enrolled_Line3": "<PERSON>rstbehand<PERSON>", "hzvButton_Enrolled_Line4": "{{contractName}} seit {{startDate}}", "button_Terminated_Line1": "Die Vertragsteilnahme wurde von {{userName}} zum {{endDate}} beendet", "hzvButton_Terminated_HpmActive_Line1": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "hzvButton_Terminated_ChangedIknumber_Line1": "<PERSON><PERSON><PERSON> den Patienten ist aufgrund eines Kassenwechsels eine Neueinschreibung erforderlich", "hzvButton_Rejected_Line1": "Die Einschreibung wurde abgelehnt", "hzvButton_Deputy_Line1": "Vertretungsfall {{contractName}} - Der Patient ist in einer anderen Praxis eingeschrieben", "hzvButton_InvalidIkNumber_Line1": "<PERSON>ine Einschreibung aufgrund von ungültiger IK-Nummer möglich", "hzvButton_NotSupportedIkNumber_Line1": "Derzeit ist keine Einschreibung möglich (z.B. aufgrund fehlender Vertragsteilnahme des Arztes)", "MissingInsuranceNumber": "Der Teilnahmestatus des Patienten kann nicht ermittelt werden, da keine gültige\nVersichertennummer der elektronischen Gesundheitskarte vorliegt.", "menuFooterHzvTerminated": "Vertragsteilnah<PERSON> wurde beendet von {{updatedBy}} zu dem {{updatedDate}} um {{updatedTime}}", "lastHpmInformation": "Ihre letzte Teilnahmeprüfung am {{checkedDate}} um {{checkedTime}} {{status}} Vertragsteilnahme", "active": "Aktiv", "inActive": "<PERSON><PERSON>", "hpmConnectionFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "1stDayOfQuarter1": "1. <PERSON> von Q1/{{year}}", "1stDayOfQuarter2": "1. <PERSON> von Q2/{{year}}", "1stDayOfQuarter3": "1. <PERSON> von Q3/{{year}}", "1stDayOfQuarter4": "1. <PERSON> Q4/{{year}}", "labelTodayShortcutDatePicker": "<PERSON><PERSON>", "selectContractLabel": "Teilnahme beantragen für", "cancelButton": "Abbrechen", "saveButton": "Speichern", "activateButton": "Aktivieren", "checkHpmParticipationSuccessfully": "Der Patient ist in dieser Praxis in die HZV eingeschrieben", "checkHpmParticipationSuccessfullyDeputy": "Der Patient ist in einer anderen Praxis in die HZV eingeschrieben (Vertreterfall)", "checkHpmParticipationNoActiveParticipation": "Der Patient ist derzeit kein aktiver HzV- Vertragsteilnehmer", "checkHpmParticipationFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "hzv_iKChanged": "<PERSON><PERSON>r den Patienten ist aufgrund eines Kassenwechsels eine Neueinschreibung erforderlich."}, "HzvSelectCustodianDoctor": {"title": "HZV Betreuarzt", "description": "Der Patient ist aktiver HZV - Vertragsteilnehmer in dieser Praxis", "contract": "Vertrag:", "doctor": "Betreuarzt", "doctorRequired": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "btnSkip": "Überspringen", "btnUpdate": "Update doctor"}, "HzvHpmCheckHistory": {"OK_Custodian": "In dieser Praxis e<PERSON>schrieben", "OK_Deputy": "Der Patient ist aktiver Vertragsteilnehmer (Vertreterfall)", "FAILED": "Verbindungsfehler", "NO_INFORMATION": "<PERSON><PERSON>", "message_OK": "Der Patient ist bereits aktiver HzV- Vertragsteilnehmer", "message_FAILED": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "message_NO_INFORMATION": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer"}, "PatientEnrollmentWidget": {"lastHpmInformation": "Ihre letzte Teilnahmeprüfung am {{checkedDate}} um {{checkedTime}} {{contractId}} {{status}} Vertragsteilnahme", "active": "Aktiv", "inActive": "<PERSON><PERSON>", "hpmConnectionFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "hpmEnrollmentFail": "Die Datenübermittlung von Versichertenteilnahmeerklärungen ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut."}, "TreatAsDeputy": {"selectContractLabel": "Teilnahme aktivieren für", "noResult": "<PERSON><PERSON>", "selectAnOptionLabel": "Wählen Sie eine Option", "cancelButton": "Abbrechen", "activateButton": "Aktivieren", "Doctor": "Doctor"}, "Composer": {"commandsDescription": {"anamnese": "Anamnese", "anamnestic-diagnose": "Anamnestische Diagnose", "findings": "Befund", "acute-diagnose": "Akutdiagnose", "permanent-diagnose": "Dauerdiagnose", "service": "Le<PERSON><PERSON>", "therapy": "<PERSON><PERSON><PERSON>", "notes": "Notiz"}, "controlSpace": "Strg+Leertaste", "controlEnter": "Enter", "toSubmit": "zum Übertragen", "forTextmodule": "für Textmodule", "encryptionTooltip": "Verschlüsselungsanweisungen", "typeCommand": "G<PERSON>en Sie einen Befehl ein oder \"+\" um sich die Befehlsoptionen anzeigen zu lassen", "typeService": "Starten Sie die Eingabe, um nach einer Leistungsziffer zu suchen...", "typeDiagnose": "Starten Sie die Eingabe, um nach einer Diagnose zu suchen...", "typeFreeText": "Textmodul-Shortcut eingeben oder Eingabe fortsetzen", "typeParenthesis": "Eingabe '{{key}}' um <PERSON>er ergänzen zu können", "addAdditionalInfo": "Zusatzfelder hinzufügen", "selectAddtionalInfo": "<PERSON><PERSON><PERSON>en sie ein <PERSON>", "addtionalInfo": "<PERSON><PERSON><PERSON><PERSON>", "command": "<PERSON><PERSON><PERSON>", "certainty": "Diagnosesicherheit", "typeCertainty": "Diagnosesicherheit auswählen", "typeLaterality": "Lokalisation auswählen (optional)", "laterality": "Lokalisation", "title": "Überweisungsinformationen", "referralInfoLanr": "LANR", "referralInfoTypeLanr": "Eingabe der LANR des überweisenden Arztes", "referralInfoBsnr": "BSNR", "referralInfoTypeBsnr": "Eingabe (N)BSNR des überweisenden Arztes an", "careFacilityTitle": "Pflegeeinrichtung", "careFacilityName": "Name", "careFacilityTypeName": "Eingabe Name der Pflegeeinrichtung", "careFacilityOrt": "Ort", "careFacilityTypeOrt": "Eingabe Name der Pflegeeinrichtung", "ops": "OPS", "chooseLaterality": "Wähle Lokalisation", "submitted": "Daten wurden übermittelt", "noEdit": "Kann nicht bearbeitet werden", "generalPractitionerText1": "<PERSON>s konnte leider kein Suchtreffer im Hausarztkatalog erzielt werden", "generalPractitionerText2": "Möchten Sie jetzt eine weitere Suche im \"Systematischen und Alphabetischen Verzeichnis\" starten?", "specialistGroupText1": "<PERSON>s konnte leider kein Suchtreffer im Fachgruppenkatalog {{specialListGroup}} erzielt werden.", "specialistGroupText2": ".", "noResultsFound": "<PERSON><PERSON> vorhanden", "CreateServiceCode": "Leistungsziffer <PERSON>", "changeToFavSchein": "oder möchten Sie auf einem FaV-Schein dokumentieren?", "specialistGroupText3": "Möchten Sie jetzt eine weitere Suche im \"Systematischen und Alphabetischen Verzeichnis\" starten?", "createSuccessEbm": "Leistungsziffer wurde erstellt", "typeActionChain": "Beginnen Sie die Eingabe um nach einer Aktionskette suchen zu können.", "deleteSuccessfully": "Eintrag wurde erfolgreich gelöscht!", "pseudoGnr": "Technische Kennziffer", "EncryptionInstructions": "Verschlüsselungsanweisungen", "noData": "<PERSON><PERSON>", "systematicText2": "Im systematischen Katalog konnten leider keine Suchtreffer gefunden werden.", "systematicText3": "Möchten Sie eine Suche im „Systematischen und Alphabetischen Verzeichnis“ starten?", "createSuccessPseudoGnr": "Die Sachkosten werden nach einem Pseudo-GNR auf Basis der SDKV-Regeln abgerechnet.", "actionCannotMarkAsPermanentDiagnose": "Die eingegebene Diagnose darf nicht als Dauerdiagnose erfasst werden", "titlePopupShowLicense": "OMIM <PERSON>", "specialistGroupNoResult": "Im Fachgruppenkatalog {{specialListGroup}} konnten leider keine Suchtreffer gefunden werden.", "selfCreated": "<PERSON><PERSON>", "factor": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "price": "Pre<PERSON>", "fieldRequired": "An<PERSON><PERSON> und Faktor sind Pflichtangaben", "fieldInvalid": "<PERSON><PERSON><PERSON> muss > 0 sein und die Anzahl muss ≥ 1 sein", "fieldLowerThanRequired": "<PERSON><PERSON><PERSON> kann nicht kleiner als 1 sein", "createJustificationTitle": "Abrechnungsbegründung erstellen", "timelineUpdated": "Behandlungsverlauf erfolgreich aktualisiert", "timelineCreated": "Ein<PERSON>g wurde erstellt", "additionalInfo": "Information", "invalidFactorQuantity": "<PERSON><PERSON><PERSON> muss > 0 und <PERSON><PERSON> muss ≥ 1 sein", "invalidHgncGenSymbol": "Die eingegebenen HGNC-Symbole sind nicht gültig"}, "CreateSvSchein": {"title": "Es existiert kein Schein dem dieser Eintrag zugewiesen werden könnte", "description": "Abrechnungsrel<PERSON><PERSON> Einträge (Diagnosen, Leistungsziffern) müssen einem Schein zugewiesen werden, damit diese abgerechnet werden können.", "cancelButton": "Überspringen", "confirmButton": "Neuen Schein erstellen"}, "Encounter": {"encounterTitle": "{{case}} Behandlungsfall", "outPatient": "Ambulante Behandlung", "accident": "Unfall", "preventive": "Präventiver Behandlungsfall (Vorsorgeuntersuchung/Schutzimpfung)", "encounterCase": "Behandlungsfall: ", "encounterSubmitted": "Sie können keine Daten editieren, welche bereits abgerechnet wurden.", "warningNoContracts": "<PERSON>ür das ausgewählte Datum liegt keine Vertragsteilnahme vor.\nBitte wählen Si<PERSON> ein anderes Datum aus.", "systematicAndAlphabetical": "Systematik und Alphabet", "systematic": "Systematik", "generalPractitioner": "Hausarztkatalog", "specialistGroup": "Fachgruppenkatalog", "allSpecialistGroups": "Alle Facharztgruppen", "setDefaultCatalog": "Als Standard übernehmen", "selectCatalog": "Katalog auswählen", "clearAllContent": "Alles löschen?", "submitEntry": "Eintrag bestätigen", "clearAll": "Alles löschen"}, "PatientFile": {"AKA_ABRD1544_errorMessage": "LANR und BSNR des überweisenden Arztes sind verpflichtend, sofern eine Überweisung vorliegt.", "WELCOME_TO_TUTUM": "<PERSON><PERSON><PERSON> Willkommen bei garrioPRO!", "SEARCH_OR_ADD_PATIENT": "Patientensuche öffnen mit Alt+S, Patienten erstellen mit Alt+P", "TIME_LINE_TAB": "Behandlungsverlauf", "MEDICATION_TAB": "Medikation", "PRESCRIPTIONS_TAB": "Verordnungen", "HIMI_TAB": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FORMS_TAB": "Formulare", "DIGA_TAB": "DiGA", "HEIMI_TAB": "Heilmittel", "LAB_TAB": "Labor", "editSuccessLb": "Patienteninformationen aktualisiert", "missingSchein": "Keinen Schein erstellt", "createScheinToSeeSomesthingHere": "Bitte erstellen Sie einen Schein, um mit der Dokumentation zu beginnen.", "legend": "Legende:", "created": "<PERSON><PERSON><PERSON><PERSON>", "removed": "Gelöscht", "restored": "Wiederhergestellt", "updated": "<PERSON>ktual<PERSON><PERSON>", "historyTitle": "Historie anzeigen", "tooltipViewHistory": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> von {{name}} am {{date}} um {{time}}"}, "TerminatePermanentDiagnoseDialog": {"timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_title": "Enddatum hinz<PERSON>", "timelineContent_diagnoseEntry_documentException_title": "Ausnahme dokumentieren", "timelineContent_diagnoseEntry_documentExplanation_title": "Erläuterung dokumentieren", "timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_message": "<PERSON>te fügen Sie ein Diagnose- Enddatum hinzu:", "timelineContent_diagnoseEntry_addFurtherInfoDialog_message": "Hier können sie eine Diagnoseerläuterung oder -ausnahme ergänzen, welche mit in ihrer Abrechnung übertragen wird.", "timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_calendarTitle": "Enddatum", "timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_ConfirmButton": "OK"}, "TimelineEncounter": {"outPatient": "Ambulante Behandlung", "accident": "Unfall", "preventive": "Präventiver Behandlungsfall (Vorsorgeuntersuchung/Schutzimpfung)", "confirmDeleteDialogTitle": "Aus dem Behandlungsverlauf löschen?", "confirmDeleteDialogMessage": "Dieser Eintrag wird nicht mehr im Behandlungsverlauf angezeigt.", "confirmDeleteDialogConfirmButton": "Ja, löschen", "confirmDeleteDialogCancelButton": "Abbrechen", "editActionTooltip": "<PERSON><PERSON><PERSON>", "editEncounterDateTooltip": "<PERSON><PERSON>", "deleteActionTooltip": "Löschen", "deleteActionDisabledTooltip": "Der Behandlungsfall beinhaltet bereits abgerechnete Einträge.", "KJP4a_sendHpmRequest_buttonLabel": "KJP4a (BVKJ)", "KJP4a_warningMessage": "Die Dokumentation der Voreinschreibeleistung KJP4a ist nur vor Aktivierung der Teilnahme möglich.", "KJP4a_resendHpmRequest_buttonLabel": "KJP4a (BVKJ) erneut senden", "KJP4a_submissionFailedWarningMessage": "KJP4a (BVKJ) Übertragung fehlerhaft", "KJP4a_sendHpmRequest_successToasterMessage": "KJP4a (BVKJ) erfolgreich übertragen", "KJP4a_sendHpmRequest_failedToasterMessage": "KJP4a (BVKJ) Übertragung fehlerhaft", "KJP4a_sendHpmRequest_alertDialog_title": "Möchten Sie mit der Dokumentation der Leistung KJP4a (BVKJ) fortfahren?", "KJP4a_sendHpmRequest_alertDialog_content": "Sie beabsichtigen für diesen Patienten eine KJP4a zu dokumentieren und diese Information an das HÄVG Rechenzentrum zu übertragen. Bitte beachten Si<PERSON>, dass diese Leistung später nur vergütet wird, wenn es zu einer erfolgreichen Einschreibung des Patienten in den HzV-Vertrag bei dem Arzt kommt, welcher die Leistung erbracht hat. Wollen Sie den Vorgang fortsetzen?", "KJP4a_sendHpmRequest_alertDialog_confirmButton": "<PERSON><PERSON>", "KJP4a_sendHpmRequest_alertDialog_cancelButton": "<PERSON><PERSON>", "UHU35_sendHpmRequest_buttonLabel": "AOK-<PERSON> 18+", "UHU35_warningMessage": "Die Dokumentation der Voreinschreibeleistung AOK Check18+ ist nur vor Aktivierung der Teilnahme möglich.", "UHU35_resendHpmRequest_buttonLabel": "AOK-Check 18+ versenden", "UHU35_submissionFailedWarningMessage": "AOK-Check 18+ Übertragung", "UHU35_sendHpmRequest_successToasterMessage": "AOK-Check 18+ erfolgreich übertragen", "UHU35_sendHpmRequest_failedToasterMessage": "AOK-Check 18+ Übermittlung fehlerhaft", "UHU35_sendHpmRequest_alertDialog_title": "Möchten Sie mit der Dokumentation der AOK Check 18+ Leistung fortfahren?", "UHU35_sendHpmRequest_alertDialog_content": "Sie beabsichtigen für diesen Patienten eine AOK-Check 18+ zu dokumentieren und diese Information an das HÄVG-Rechenzentrum zu übertragen. Bitte beachten Si<PERSON>, dass diese Leistung später nur vergütet wird, wenn es zu einer erfolgreichen Einschreibung des Patienten in den HzV-Vertrag bei dem Arzt kommt, welcher die Leistung erbracht hat. Wollen Sie den Vorgang fortsetzen?", "UHU35_sendHpmRequest_alertDialog_confirmButton": "<PERSON><PERSON>", "UHU35_sendHpmRequest_alertDialog_cancelButton": "<PERSON><PERSON>", "modalCaptionCancel": "Abbrechen", "modalCaptionSave": "Speichern", "modalTitle_EditEncounterDate": "<PERSON><PERSON>", "modalTitle_EditEntryDate": "<PERSON><PERSON>", "modalTitle_EditEntryDateSuccess": "Datum des Termins hat sich geändert", "modalTitle_EditEntryDateError": "Ausführung fehlgeschlagen. Bitte versuchen Sie es erneut.", "labelDate": "Datum:", "editEntry": "<PERSON><PERSON><PERSON>"}, "DiagnoseEditor": {"descriptionLabel": "Beschreibung", "certaintyLabel": "Diagnosesicherheit", "lateralityLabel": "Lokalisation (optional)", "searchDiagnosePlaceholder": "Diagnose suchen ..."}, "DiagnoseEntry": {"acuteDiagnose": "Akutdiagnose", "chronicDiagnose": "Dauerdiagnose", "certainty": "Diagnosesicherheit", "laterality": "Lokalisation", "diagnoseEnddate": "Enddatum", "actionEdit": "<PERSON><PERSON><PERSON>", "editSchein": "Fall bearbeiten", "actionRemove": "Löschen", "actionMarkAsAcuteDiagnose": "Als Akutdiagnose markieren", "actionMarkAsPermanentDiagnose": "Als Dauerdiagnose markieren", "actionMarkAsAnamnesticDiagnose": "Als anamnestische Diagnose markieren", "actionAddPermanentDiagnoseEndDate": "Enddatum hinz<PERSON>", "actionDocumentExceptionDiagnose": "Ausnahme dokumentieren", "actionDocumentExplanationDiagnose": "Erläuterung dokumentieren", "actionCannotMarkAsPermanentDiagnose": "Diese Diagnose kann nicht als Dauerdiagnose markiert werden", "actionMarkTreatmentRelevant": "Als behandlungsrelevant markieren", "actionUnMarkTreatmentRelevant": "Nicht als behandlungsrelevant markieren", "actionMarkAllTreatmentRelevant": "Alle anamnestischen Diagnosen ({{value}}) als behandlungsrelevant markieren", "treatment": "Behandlung", "diagnoseSavedSuccessful": "Diagnose g<PERSON><PERSON><PERSON>rt", "diagnoseSavedFailed": "Diagnose konnte nicht gespeichert werden.", "diagnoseEnddateSavedSuccessful": "Enddatum der Diagnose gespeichert.", "diagnoseExceptionSavedSuccessful": "Ausnahme wurde gespeichert.", "diagnoseExplanationSavedSuccessful": "Erläuterung wurde gespeichert.", "diagnoseCancelledSuggestionSavedSuccessful": "Diagnosevorschläge wurden abgebrochen.", "diagnoseCancelledSuggestionSavedFailed": "Das Abbrechen der Auswahl war fehlerhaft.", "diagnoseInfoSavedFailed": "Diagnoseninformation konnte nicht gespeichert werden.", "invalidCertaintyMessage": "Ungültige Diagnosesicherheit", "AKA_ABRD887_errorMessage": "Sie haben eine Diagnose mit dem Zusatz \"V\" dokumentiert. Zu diesem Patienten wurde in einem Vorquartal die gleiche Diagnose ebenfalls mit dem Zusatz \"V\" verschlusselt. Bitte uberprufen Sie, ob es sich weiterhin um eine Verdachtsdiagnose handelt.", "messageCancelled": "Diagnose wird überprüft...", "btnCancelled": "Abbrechen", "btnAddDiagnose": "Diagnose hinzufügen", "btnUpdateDiagnose": "Diagnose ersetzen", "btnDeleteDiagnose": "Diagnose löschen", "msgDiagnoseAddedSuccessful": "Diagnose hinzugefügt", "msgMultipleDiagnoseAddedSuccessful": "{{number}} Diagnosen hinzugefügt", "msgDiagnoseAddedFailed": "Hinzufügen der Diagnose(n) fehlgeschlagen", "msgDiagnoseReplacedSuccessful": "Diagnose wurde ersetzt", "msgDiagnoseReplacedFailed": "Ersetzen der Diagnose fehlgeschlagen", "msgDiagnoseRemoveSuccessful": "Diagnose wurde entfernt", "msgMultipleDiagnoseRemoveSuccessful": "{{number}} Diagnosen wurden entfernt", "msgDiagnoseRemoveFailed": "Löschen der Diagnose(n) fehlgeschlagen", "messageCorrectionProposal": "Korrekturvorschlag der Kodierregeln:", "exception": "Ausnahme", "explanation": "Begründung", "more": "<PERSON><PERSON>", "editEntryDate": "<PERSON><PERSON>", "pseudo": "<PERSON><PERSON><PERSON>", "SuggestionModal": {"titleModal": "Vorgeschlagene Korrektur der Kodierungsregeln", "titleAdd": "Diagnose hinzufügen", "titleReplace": "Diagnose ersetzen", "titleDelete": "Diagnose löschen", "captionSave": "Speichern", "lblCertainty": "Diagnosesicherheit", "lblLaterality": "Lokalisation", "captionCancel": "Abbrechen", "lblReplaceWith": "Ausgewählte Diagnose ersetzen durch…", "lblWarningRemove": "Das Löschen der Diagnose kann nicht rückgängig gemacht werden.", "lblRemove": "Ausgewählte Diagnose löschen", "lblRemoveConflict": "Alternative Diagnose löschen", "optionDontShow": "Diesen Vorschlag für das laufende Quartal bei diesem Patienten nicht mehr anzeigen.", "suggestionNoLongerApplies": "Dieser Vorschlag ist nicht mehr gültig."}, "ErrorMessages": {"ErrorCode_CANNOT_SEARCH_SERVICE_CODE": "<PERSON>s kon<PERSON> keine Leistungsziffer gefunden werden", "ErrorCode_Somethinngs_Went_Wrong": "<PERSON>s ist ein Problem aufgetreten. Wenn das Problem weiterhin besteht, wenden <PERSON> sich bitte an ihr Systemhaus, um Unterstützung zu bekommen.", "ErrorCode_Network_Offline": "Sie sind offline. Bitte überprüfen Sie Ihre Internetverbindung und laden diese neu.", "ErrorCode_ValidationError_NotActive_InsuranceInfo": "Keine gültige Versicherung", "ErrorCode_Login_Invalid_Verify_Password": "Ungültiges Passwort", "ErrorCode_Validation_MessageABRD613": "Der Ersatzwert \"UUU\" ist nur bei Vorliegen von bestimmten Auftragsleistungen möglich.", "ErrorCode_Validation_MessageABRD969": "Sie haben eine Akutdiagnose als Dauerdiagnose gekennzeichnet. Bitte überprüfen Sie Ihre Dokumentation, da diese nicht plausibel ist.", "ErrorCode_Validation_MessageABRD514": "Sie haben eine Akutdiagnose als Dauerdiagnose gekennzeichnet. Bitte überprüfen Sie Ihre Dokumentation, da andernfalls die Abrechnung der Pauschale zur Behandlung chronisch kranker Patienten verhindert werden kann.", "ErrorCode_Validation_MessageABRD612": "Diagnosen mit dem Diagnosezusatz \"Gesichert\" müssen gemäß vertraglicher Vorgaben endständig dokumentiert werden.", "ErrorCode_Validation_MessageABRD786": "Sie haben das Diagnose-Kennzeichen \"Z\" bei einer akuten Krankheit angegeben. Bitte verwenden Sie das Zusatzkennzeichen \"G\", sofern die Krankheit noch besteht und es sich um eine behandlungsbedürftige Krankheit handelt. Falls Sie den Behandlungsanlass als Folge der Erkrankung dokumentieren wollten, stehen <PERSON>hnen unter anderem die wählen Sie bitte aus der folgenden Diagnosen zur Verfügung.", "ErrorCode_Validation_MessageVERT647": "Durch diese Änderung des Aktivierungsdatums liegen dokumentierte oder abgerechnete Leistungen außerhalb des Aktivierungszeitraums", "ErrorCode_Validation_MessageP10470MM": "Bitte Kodierung überprüfen: Kode gilt nur\nfür männliche Patienten.", "ErrorCode_Validation_MessageP10470MF": "Bitte Kodierung überprüfen: Kode gilt nur\nfür weibliche Patienten.", "ErrorCode_Validation_MessageP10470KM": "Bitte Kodierung überprüfen: Kode gilt überwiegend nur für männliche Patienten.", "ErrorCode_Validation_MessageP10470KF": "Bitte Kodierung überprüfen: Kode gilt überwiegend nur für weibliche Patienten.", "ErrorCode_Validation_MessageP10480MM": "Bitte Kodierung überprüfen: Kode gilt nur für Patienten ab einem Alter von {{minAge}} {{minAgeUnit}}", "ErrorCode_Validation_MessageP10480MR": "Bitte Kodierung überprüfen: Kode gilt nur für Patienten in der Altersgruppe zwischen {{minAge}} {{minAgeUnit}} und unter {{maxAge}} {{maxAgeUnit}}.", "ErrorCode_Validation_MessageP10480MZ": "Bitte Kodierung überprüfen: Kode gilt nur für Patienten im Alter von {{minAge}} {{minAgeUnit}}.", "ErrorCode_Validation_MessageP10480KM": "Bitte Kodierung überprüfen: Kode gilt überwiegend nur für Patienten ab einem Alter von {{minAge}} {{minAgeUnit}}.", "ErrorCode_Validation_MessageP10480KR": "Bitte Kodierung überprüfen: Kode gilt überwiegend nur für Patienten in der Altersgruppe zwischen {{minAge}} {{minAgeUnit}} und unter {{maxAge}} {{maxAgeUnit}}.", "ErrorCode_Validation_MessageABRD456": "Bitte dokumentieren Sie zusätzlich die Leistung „Arzt-Patienten-Kontakt“ (0000).", "ErrorCode_Validation_MessageABRD887": "Sie haben eine Diagnose mit dem Zusatz \"V\" dokumentiert. Zu diesem Patienten wurde in einem Vorquartal die gleiche Diagnose ebenfalls mit dem Zusatz \"V\" verschlüsselt. Bitte überprüfen Sie, ob es sich weiterhin um eine Verdachtsdiagnose handelt.", "ErrorCode_Validation_MessageABRD970": "Fehlender Name und Ort", "ErrorCode_Validation_MessageFieldIsRequiredFormat": "%s ist ein Pflichtfeld", "ErrorCode_Validation_MessageMedicineTypeRequireFormat": "%s ist erforderlich mit Medikamententyp %s", "ErrorCode_Validation_MessageMappingError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorCode_Validation_MessageNotBillingAble": "Der ICD-10-GM-Code ist nicht für die Abrechnung und/oder in der Arbeitsunfähigkeitsbescheinigung zugelassen.", "ErrorCode_Validation_MessageNotFillingForBilling": "Der ICD-10-GM-Kode ist nicht mit Inhalt belegt und darf daher nicht zur Abrechnung und/oder bei den Formularen der vertragsärztlichen Versorgung verwendet werden.", "ErrorCode_Validation_MessageRareDiseaseEu": "Bitte Kodierung überprüfen: Diagnosen dieses Kodes sind in Mitteleuropa sehr selten.", "ErrorCode_Validation_MessageIfSG": "Diagnosen dieses Kodes sind gemäß Infektionsschutzgesetz (IfSG) in der Regel meldepflichtig.", "ErrorCode_Validation_MessageNotSuitablePermanent": "Bitte die Kodierung überprüfen: Dieser Kode beschreibt einen akuten, vorübergehenden Zustand und ist für die Kategorisierung als \"Dauerdiagnose\" im weiteren Behandlungsverlauf nicht geeignet.", "ErrorCode_Validation_MessageNeedPrimaryCode": "Der ICD-10-GM-Code kann nicht zur Abrechnung und/oder Ausstellung einer Arbeitsunfähigkeitsbescheinigung verwendet werden. Es muss mindestens ein Primärcode vorhanden sein.", "ErrorCode_CERTAINTY_IS_REQUIRED": "Diagnosesicherheit ist eine Pflichtangabe.", "InvalidLaterality": "Die Seitenlokalisation ist ungültig.", "InvalidCertainty": "Die Diagnosesicherheit ist ungültig.", "EDMPSuggestion": "Die Diagnose ist für die Einschreibung in das DMP qualifiziert. Klicken Sie auf den DMP-Button, um den Einschreibungsprozess zu starten.", "ErrorCode_Validation_Missing_ScheinId": "Eine Scheinzuweisung ist erforderlich, um diesen Eintrag bei der Abrechnung berücksichtigen zu können.", "ErrorCode_Validation_ICD_Code_Not_In_Master_Data": "Der gespeicherte ICD-Kode ist nicht in der ICD-10-GM-Stammdatei vorhanden. <PERSON><PERSON> darf dieser Code nicht zur Abrechnung oder in Formularen verwendet werden.", "ErrorCode_Validation_RangeAge": "<PERSON><PERSON>iff<PERSON> ist aufgrund des Alters des Patienten ungültig", "ErrorCode_Validation_MessageABRG669": "Die Abrechnung der Betreuungsleistung für chronisch kranke Patienten erfordert das Vorliegen mindestens einer gesicherten Dauerdiagnose. Die Abrechnung einer Betreuungsleistung für chronisch kranke Patienten für die ausschließlich Akutdiagnosen als gesicherte Dauerdiagnosen erfasst haben, ist ausgeschlossen.", "ErrorCode_Cannot_Delete_Timeline_Entry": "Eintrag aus dem Behandlungsverlauf kann nicht gelöscht werden"}, "deleteSuccessfully": "Eintrag wurde erfolgreich gelöscht!", "msgAnamsesticSuccess": "Diagnose wurde als anamnestische Diagnose markiert", "msgAccuteSuccess": "Als Akutdiagnose mark<PERSON>t", "msgPermanentSuccess": "Diagnose wurde als Dauerdiagnose markiert", "msgMarkAllTreatmentRelevantSuccess": "Alle anamnestischen Diagnosen ({{value}}) für dieses Quartal wurden als behandlungsrelevant markiert", "msgMarkTreatmentRelevantSuccess": "Markieren als behandlungsrelevant erfolgreich", "msgUnmarkTreatmentRelevantSuccess": "Markieren als nicht behandlungsrelevant erfolgreich", "btnHideErrorMessage": "<PERSON>n<PERSON>s nicht mehr anzeigen", "btnCreateNewSchein": "Neuen Schein erstellen", "hintExceptionGender": "Sie können Ausnahmen oder Erläuterungen ergänzen, die ebenfalls in Ihrer Abrechnung übermittelt werden sollen.", "hintExceptionGenderMust": "Zur Übernahme des Codes im Rahmen der Behandlung muss eine Diagnoseausnahme dokumentiert werden. Hier können Sie eine Diagnoseanforderung oder Ausnahme ergänzen, die ebenfalls in Ihrer Abrechnung übermittelt wird.", "permanentHeader": "Behandlungsrelevante <PERSON>", "permanentContent": "Bei der Abrechnung werden alle behandlungsrelevanten Diagnosen und Akutdiagnosen berücksichtigt."}, "ServiceEntry": {"ErrorCode_Missmatch5005And5050": "<PERSON> Anzahl der erfassten Implantatregister muss mit dem angegebenen Multiplikator für die ausgewählte EBM-Leistung übereinstimmen. Bitte prüfen und passen Sie ein Eintrag an.", "service": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON>", "actionAddMaterialCost": "Sachkosten hinzufügen", "saveServiceEntryToasterMessage": "Leistungsinformation wurde gespeichert.", "saveCareFacilityToasterMessage": "Die Information zur Pflegeeinrichtung wurde gespeichert.", "saveMaterialCostToasterMessage": "Die Information zu Sachkosten wurde gespeichert.", "removeMaterialCostToasterMessage": "Die Informationen zu Sachkosten wurden entfernt.", "documentedTerminalPsychotherapy": "{{code}} wurde erfolgreich dokumentiert", "documentAndTakeover": "{{code}} und übernommene Diagnosen werden auf einem Pseudo-Behandlungsschein dokumentiert", "atLeastSelecteOne": "Mindestens eine Leistungsziffer muss ausgewählt werden.", "referrerInfoLabel": "Informationen zum überweisenden Arzt", "saveReferrerInfoToasterMessage": "Informationen zum überweisenden Arzt wurden gespeichert.", "missingOpsMessage": "Fe<PERSON>ende Angabe des OPS", "invalidOpsLateralityMessage": "Fehlende OPS Lokalisation {{index}}", "invalidOpsMessage": "Ungültiger OPS {{index}}", "editEntryDate": "<PERSON><PERSON>", "missingMusterEmergencyPlanHint": "Bitte füllen Sie zum Abrechnen der Leistung 3740A das Formular \"Notfallplan geriatrischer Patient\" aus.", "removeEntry": "Löschen", "removeApproval": "Als nicht genehmigt markieren", "notAcceptedByKV": "<PERSON><PERSON> gene<PERSON>", "markAsAcceptedByKV": "<PERSON>s genehemigt markieren", "documentIt": "Hinzufügen", "addLabel": "Hinzufügen", "documentLabel": "Anzeigen", "codeDocumented": "{{ code }} doku<PERSON><PERSON>t", "missICDCodeToBillingDialogTitle": "Begründ<PERSON> zu {{code}}", "missICDCodeToBillingDialog": "Die Leistung {{code}} ist nur bei Begründung durch mindestens einer der nachfolgenden Behandlungsdiagnosen berechnungsfähig:", "pseudo": "<PERSON><PERSON><PERSON>", "ErrorCode_ValidationError_MustHaveRVSA": "Für die Leistungsdokumentation ist das RVSA Zertifikat verpflichtend", "ErrorMessages": {"ABRD1544": "Die Leistung wird normalerweise im Rahmen einer Überweisung abgerechnet.", "ABRD1544_subDescription": "<PERSON>te stellen Si<PERSON> sicher, dass der Patient überwiesen wurde und die Überweisung korrekt im FaV-Fall hinterlegt worden ist.", "ErrorCode_Missing_Referral_Doctor": "Die Leistungsziffer kann nur abgerechnet werden, wenn die LANR und BSNR des Auftraggebers angegeben wurde.", "ErrorCode_CANNOT_SEARCH_SERVICE_CODE": "<PERSON>s kon<PERSON> keine Leistungsziffer gefunden werden", "OminGNotNull": "OMIM-G-Kode des untersuchten Gens dürfen nicht leer sein", "OminPNotNull": "OMIM-P-Kode (Art der Erkrankung) dürfen nicht leer sein", "Null": "Bei dem Ersatzwert {{value}} muss das Feld \"{{fieldName}}\" angegeben werden.", "Required": "{{fieldName}} (FK {{field}}) muss gegeben sein.", "RequireBoth": "<PERSON><PERSON>, {{fieldName1}} und {{fieldName2}} müssen vorhanden sein.", "RequireBothOnlyOne": "Es muss genau jeweils ein \"{{fieldName1}}\" und ein \"{{fieldName2}}\" zur Leistung erfasst werden.", "EitherRequire": "Die Leistung muss durch die <PERSON>abe eines {{fieldName1}} (FK {{field1}}) oder {{fieldName2}} (FK {{field2}}) begründet werden.", "ParentChildRequire": "[{{parentValue}}] {{childFieldName}} muss angegeben werden.", "Regex": "{{fieldName}} hat ein ungültiges Format. Das Format kann wie folgt aussehen: {{pattern}}.", "MinLength": "{{fieldName}} (FK {{field}})'s muss gr<PERSON>ßer oder gleich {{minLength}} sein.", "MaxLength": "{{fieldName}} (FK {{field}})'s muss kleiner oder gleich {{maxLength}} sein.", "RequiredLength": "{{fieldName}} (FK {{field}}) muss {{requiredLength}} <PERSON><PERSON><PERSON> lang sein.", "NotEqual": "{{fieldName}} darf nicht {{value}} sein.", "NotEqualValues": "{{fieldName}} ist ungültig. Es sollte {{restrictedValues}} oder {{lastValue}} sein.", "NotRequireBoth": "Der Leistung dürfen kein {{fieldName1}} zugeordnet werden.", "CombinedRangeRegex": "{{fieldName}} ist ungültig ({{pattern}}). Es sollte {{ranges}} sein.", "DigitRule": "{{fieldName}} (format {{pattern}}) ist ungültig.", "Atleast1ICDCodeNotEqualZ017": "Ein ICD- Code (<PERSON><PERSON><PERSON><PERSON> von \"Z01.7\") muss vorhanden sein.", "1_1Relation": "<PERSON>s muss eine {{fieldName}} zur der Leistung erfasst werden.", "1_nRelation": "<PERSON>s muss mindestens ein {{fieldName}} zur Leistung erfasst werden.", "0_1Relation": "{{fieldName}} (FK {{field}}) darf höchstens einmal vorhanden sein.", "DuplicatedSameEntry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von {{fieldName}} (FK {{field}}) ist nicht zulässig.", "DuplicatedSameDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von {{fieldName}} (FK {{field}}) nicht zulässig.", "DuplicatedSameValue": "<PERSON> Wert von {{fieldName}} (FK {{field}}) sollte nicht dupliziert werden.", "ErrorCode_MissICDCodeToBilling": "Die Leistung ist nur bei Begründung durch mindestens einer der nachfolgenden Behandlungsdiagnosen berechnungsfähig.", "AtLeastOneICDWithCertainty": "<PERSON>s muss mindestens eine Diagnose nach ICD-10 dokumentiert sein.", "ErrorCode_ValidationError_MustHaveRVSA": "Für die Leistungsdokumentation ist das RVSA Zertifikat verpflichtend", "ErrorCode_ValidationError_OpsMustInList": "Der angegebene OPS ist nicht als Abrechnungsbegründung für die Leistung definiert.", "ErrorCode_ValidationError_GnrMustInList": "Die angegebene Gebührennummer ist nicht als Abrechnungsbegründung für die Leistung definiert.", "WarningForKvValidateRangeAge": "<PERSON><PERSON>iff<PERSON> ist nicht gültig aufgrund des Alters des Patienten", "GenderKvServiceWarning": "Die Leistung stimmt mit dem angegebenen Geschlecht des Patienten nicht überein.", "MaximumRate": "Sie haben die maximale Anzahl überschritten.", "888888800": "Der (Ersatz-)wert „888888800“ ist obsolet und als Feldinhalt von FK 0212, 4241, 4242, 5099 und 4299 unzulässig.", "TimeHHMMInvalid": "Das Format von {{fieldName}} sollte HHMM sein (HH = Stunde, MM = Minute).", "TimeDDMMYYYYInvalid": "<PERSON>te geben Si<PERSON> ein gültiges Datum ein.", "MissingFk5003": "Bei der Abrechnung der Gebührenordnungsposition {{ code }} ist die (Neben-)Betriebsstättennummer der Praxis, an die der Patient vermittelt wurde, anzugeben.", "Psychotherapy": "Dokumentieren Sie zur Beendigung der Therapie die Pseudo-GOP 88130 (Therapieende ohne anschließende Rezidivprophylaxe), 88131 (Therapieende mit anschließender Rezidivprophylaxe) oder legen Sie fest, dass die Krankenkasse über einen anderen Weg informiert wurde.", "Psychotherapy_88130": "Dokumentieren Sie zur Beendigung der Psychotherapie die Pseudo-GOP 88130.", "Psychotherapy_88131": "Achtung: Sie möchten eine Richtlinientherapie als Rezidivprophylaxe durchführen/abrechnen. Voraussetzung hierfür eine Beedigungsmitteilung für die Richtlinientherapie mit Pseudo-GOP 88131 (§ 16 Abs. 3 Psychotherapie-Vereinbarung). Es wurde im bisherigen Behandlungverlauf keine Beendigung mit der Pseudo-GOP 88131 ubermittelt.", "Psychotherapy_Missing_Diangosis": "<PERSON>ur Abrechnung der Ziffer muss eine Diagnose hinzugefügt werden.", "OPS_5041": "Für die OPS {{opsKeys}} muss eine Seitenlokalisation angegeben werden.", "ReplacedWithServiceCodeWhenBilling": "<PERSON><PERSON> wird durch {{code}} aufgrund des Alters des Patienten ersetzt.", "tss_surcharge_error_common": "Es ist kein zeitgestaffelter Zuschlag abrechenbar, da der Terminservice in der Zukunft liegt", "tss_surcharge_acute_error": "Der Patient wurde nicht am aktuellen Tag oder Folgetag behandelt und ist folglich nicht als TSS-Akutfall kennzeichenbar.", "tss_surcharge_routine_error": "Es ist kein zeitgestaffelter Zuschlag mehr abrechenbar, da die 35- Tage-Frist verstrichen ist.", "tss_surcharge_suggestion": "Ein zeitgestaffelter Zuschlag kann aufgrund des vermittelten TSS -Termins abgerechnet werden. Möchten Sie {{ code }} dokumentieren, um den Zuschlag zu erhalten?", "ErrorCode_Validation_Missing_ScheinId": "Eine Scheinzuweisung ist erforderlich, um diesen Eintrag bei der Abrechnung berücksichtigen zu können.", "LocalizationNotAllow": "Die Seitenlokalisation darf nicht in die Abrechnungsdatei übernommen werden.", "ErrorCode_Validation_Missing_Treatment_Time": "<PERSON><PERSON>iffer muss den Zeitpunkt der Behandlung enthalten", "ErrorCode_Validation_Must_Not_Present_Treatment_Time": "In dieser Leistungsangabe ist die Behandlungszeit nicht enthalten", "ErrorCode_Validation_Missing_Pseudo_GNR": "Sachkosten/ Materialkosten müssen entsprechend den Vorgaben Ihrer KV über eine Pseudonummer abgerechnet werden. Wählen Sie dazu in der Leistung zusätzlich die gewünschte Psuedo-GNR.", "ErrorCode_Validation_Invalid_Pseudo_GNR": "Pseudo-GNR ist ungültig", "ErrorCode_InvalidServiceCode": "Ungültige Leistungsziffer", "ErrorCode_ServiceValidationError": "<PERSON><PERSON><PERSON> ist im Leistungszeitraum nicht gültig.", "ErrorCode_Validation_RangeAge": "<PERSON><PERSON>iff<PERSON> ist aufgrund des Alters des Patienten ungültig", "ErrorCode_Validation_Ad4125_InValid": "Der Leistungstag (FK 5000) muss im angegebenen Gültigkeitszeitraum (FK 4125) liegen. <PERSON>te setzen Si<PERSON> sich mit Ihrem Systemhaus in Verbindung, um Unterstützung zu erhalten.", "InVertretungFuer_LANR": "Die Leistungsziffer kann nur abgerechnet werden, wenn die LANR des Auftraggebers angegeben wurde", "InVertretungFuer_BSNR": "Die Leistungsziffer kann nur abgerechnet werden, wenn die BSNR des Auftraggebers angegeben wurde", "ErrorCode_GoaService_GoaNumber_Not_Found": "Ungültige Leistungsziffer", "ErrorCode_GoaService_Invalid_GOA": "Warningziffer im Leistungszeitraum nicht gültig", "ErrorCode_GoaService_ExcludedCode": "<PERSON><PERSON> darf nicht in Kombination mit {{excludedCodes}} dokumentiert werden", "E440150": "PTP1 darf nur einmal in vier aufeinanderfolgenden Quartalen abgerechnet werden.", "E460212": "PTP1 darf nur einmal in vier aufeinanderfolgenden Quartalen abgerechnet werden.", "E877218": "PTP1 darf nur einmal in vier aufeinanderfolgenden Quartalen abgerechnet werden.", "ErrorCode_Validation_Abrd1564": "Diese EBM- Leistung ist Bestandteil des HZV-Ziffernkranzes und für diesen Patienten ausschließlich über die HZV abzurechnen. Eine Abrechnung dieser EBM-Leistung über die KV kann zu einer Schadensersatzanforderung der Krankenkasse führen. Bitte überprüfen Sie erneut die Dokumentation der Leistung.", "ABRD1062": "Wenn die Pflegeheimadresse nicht mit den Adressdaten auf der Versichertenkarte übereinstimmt, muss zu der Leistung \"Pflegeheimpauschale(0008)\" der Name und Ort der Pflegeeinrichtung dokumentiert werden.", "CONDITIONS_VALIDATION_ERROR": "Diese Le<PERSON>ungsziffer ist in Ihrer Region nicht gültig.", "ErrorCode_Validation_Abrg1565": "{{code}} wurde auf einem KV-Schein dokumentiert. Diese Leistung kann im Rahmen der HZV-Abrechnung mit dem HÄVG Rechenzentrum abgerechnet werden, insofern eine aktive Vertragsteilnahme vorliegt. Bitte stellen sie eine korrekte Abrechnungsübertragung sicher."}, "missing5099": "Die LANR stimmt nicht mit dem abrechnenden Arzt überein. Bitte aktualisieren sie die LANR.", "documentService": "Leistungsziffer für Beendigungsmitteilung", "btnCreateNewSchein": "Neuen Schein erstellen", "createSchein": "Schein erstellen", "FAVHint": "Der Patient ist in die fachärztliche Versorgung eingeschrieben. Die Behandlung dieses Patienten ist für alle im FaV-Ziffernkranz enthaltenen Leistungen - außer im organisierten Notfalldienst - über den MEDIVERBUND abzurechnen. Bitte prüfen Sie die Angaben zur Abrechnung gegenüber der KV.", "HZVHint": "Der Patient ist in die hausarztzentrierte Versorgung eingeschrieben. Die Behandlung dieses Patienten ist für alle im HzV -Ziffernkranz enthaltenen Leistungen - außer im organisierten Notfalldienst - über das HÄVG Rechenzentrum abzurechnen. Bitte prüfen Sie die Angaben zur Abrechnung gegenüber der KV."}, "Timeline": {"entryRemove": "Eintrag wurde gelöscht", "entryRemoveFailed": "Löschen des Eintrages fehlgeschlagen", "sortByCategory": "Sortierung nach Kategorie", "headerTitle": "Behandlungsverlauf", "headerFilterLabel": "Filter", "searchTimelinePlaceholder": "Suchwort", "filterByTypePlaceholder": "Alle", "filterByTypeSelectedText": "Gewählt {{numberOfTypes}}", "typeAnamnese": "Anamnese", "typeFinding": "Befund", "typeDiagnose": "Diagnose", "typeDiagnoseAd": "Anamnestische Diagnose", "typeDiagnoseDd": "Dauerdiagnose", "typeService": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typeTherapy": "<PERSON><PERSON><PERSON>", "typeNote": "Notiz", "typeMedicinePlan": "Medikationsplan", "typeMedicinePrescription": "Medikation", "typeHimi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeHeimi": "Heilmittel", "typePatientMedical": "Patient", "typeLab": "Labor", "typeForm": "Formulare", "typeValidationError": "Validierungsfehler", "typeCalendar": "<PERSON><PERSON><PERSON>", "typeRoom": "Wartezimmer", "typeMailItem": "E-Mail", "typeDoctorLetter": "Arztbrief", "typeBillingPatient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeServiceGoa": "GOÄ Leistungsziffer", "typeEhic": "EHIC", "typePsychotherapy": "PT Bescheid", "typeEdmpenrollment": "DMP Vertragsteilnahme", "typeEdmpenrollmentDocument": "DMP", "typeMedicine": "Ärztemuster", "typeDiga": "DiGA", "typeDocumentManagement": "Dokumentenmanagement", "typeArriba": "Arriba", "typeGdt": "GDT", "typeLdt": "LDT", "typeEdokuDocument": "eHKS", "confirmDeleteDialog_title": "Löschen bestätigen", "confirmDeleteDialog_message": "Sie sind dabei, diesen Eintrag dauerhaft zu löschen.\n\nHinweis: Der Eintrag wird nach {{duration}} Tagen dauerhaft gelöscht.\nEine Wiederherstellung ist nur durch einen Administrator und nur vor Ablauf der Frist möglich.\n\nMöchten Sie diesen Eintrag wirklich löschen?", "confirmDeleteDialog_confirmButton": "Ja, löschen", "confirmDeleteDialog_cancelButton": "<PERSON><PERSON>", "timelineContent_emptyMessage": "Keine Dokumentation vorhanden", "timelineContent_emptySuggestion": "Beginnen Sie ihre Dokumentation im unteren Bereich, um diese hier angezeigt zu bekommen.", "timelineContent_noResultFound": "Es sind noch keine Dokumentationen vorhanden", "AKA_ABRD1544_errorMessage": "Um die Leistung durchführen zu können, benötigen wir die BSNR und LANR des überweisenden Arztes.", "timelineContent_diagnoseEntry_actionDelete": "Löschen", "timelineContent_diagnoseEntry_searchDiagnosePlaceholder": "<PERSON><PERSON> nach Diagnosen …", "timelineContent_diagnoseEntry_searchCertaintyPlaceholder": "Suche nach Diagnosesicherheit …", "timelineContent_diagnoseEntry_searchLateralityPlaceholder": "Suche nach Lokalisation …", "timelineQuarter_missingService0000_actionButtonLabel": "Hinzufügen", "timelineEntryStatus_submittedMessage": "Erfolgreich an das Datenzentrum übermittelt", "timelineEntryStatus_addedByMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {{name}}", "timelineEntryStatus_submittedMessage_V2": "Erfolgreich in Rechnung gestellt von {{name}}", "timelineEntryStatus_lastEditedByMessage": "<PERSON><PERSON><PERSON><PERSON> bearbeitet von {{name}}", "billingTo": "Abrechnender Arzt {{name}}", "lanrBsnr": "LANR: {{lanr}} / BSNR: {{bsnr}}", "havgID": "HÄVG-ID: {{havgId}} / VP-ID: {{havgVpId}}", "mediVPId": "MEDI-ID: {{mediId}} / VP-ID: {{mediVpId}}", "timelineEntryStatus_manualTakeover": "<PERSON><PERSON> erfolgt von {{name}}", "timelineService_careFacility_label": "Pflegeeinrichtung Details", "timelineService_careFacility_missingMessage": "Um die Leistungsziffer \"Pflegeheimpauschale (0008)\" a<PERSON><PERSON><PERSON> zu <PERSON>ö<PERSON>, müssen Sie den Ort der Pflegeeinrichtung dokumentieren.", "timelineService_materialCost_label": "Sachkosten", "timelineService_materialCost_costDescription_label": "Beschreibung", "timelineService_materialCost_costAmount_label": "Betrag (EUR)", "timelineService_materialCost_saveAction_label": "Speichern", "timelineService_materialCost_cancelAction_label": "Abbrechen", "timelineService_materialCost_editAction_label": "<PERSON><PERSON><PERSON>", "timelineService_materialCost_deleteAction_label": "Löschen", "timelineContent_referrerInfo_label": "Information des überweisenden Arztes", "timelineQuarter_save_fav_referral_ok": "Information des überweisenden Arztes", "editEntryDate": "<PERSON><PERSON>", "more": "<PERSON><PERSON>", "OMIMG": "OMIM-G", "OMIMP": "OMIM-P", "dateAtTime": "{{date}} um {{time}} Uhr", "textsize": "Schriftgröße", "timelineTextSizeBtn": "Schriftgröße", "timelineService_care_facility_details": "Pflegeeinrichtung Details", "ReplacedWithServiceCodeWhenBilling": "<PERSON><PERSON> wird durch {{code}} aufgrund des Alters des Patienten ersetzt.", "name": "Name", "location": "Ort", "add": "Hinzufügen", "save": "Speichern", "cancel": "Abbrechen", "email": "E-Mail", "patientBillEntryText": "BRIEF - Patientenquittung - {{billingDoctor}}", "g81EHICEntryText": "Formular - (G81) Patientenerklärung Europäische Krankenversicherung", "g81EHICEntryHint": "Formular wurde gespeichert. Drucken Sie dieses aus.", "restorationMode": "Wiederherstellungsmodus", "timelineEntryStatus_restoreByMessage": "Wiederhergestellt von {{name}}", "timelineEntryStatus_removeByMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> {{name}}", "jumpStart": "Gehe zum Anfang des fokussierten Scheins", "jumpEnd": "Gehe zum Ende des fokussierten Scheins", "AUTO_CREATE_BY_ABRG921": "Die Pseudo- Leistungsziffer zur Kennzeichnung eines präventiven Behandlungsfalles wurde ergänzt", "external": "Extern", "noResult": "<PERSON><PERSON>. Bitte detaillieren Sie Ihre Suche.", "saveChanges": "Änderungen speichern", "viewMore": "<PERSON><PERSON> anzeigen", "hide": "Ausblenden"}, "PatientSearch": {"enter": "Eingabe", "noPatientFound": "<PERSON>s wurde kein Patient gefunden", "createPatient": "Patienten erstellen", "defaultPlaceHolder": "Suche Patienten nach Nachname, Vorname, Geburtsdatum, SKT-Zusatzangaben", "datePlaceHolder": "tt.mm.jjjj", "focusPlaceHolder": "G<PERSON>en Si<PERSON> einen Patientennamen ein oder \"/\" für die erweiterte Suche", "mixNameText": "Nachname, Vorname", "patientNumberText": "Patienten- ID", "headerAdvanceSearch": "oder wählen Si<PERSON> eine andere Suchoption", "insuranceNumberText": "Versicherungsnummer", "birthDateText": "Geburtsdatum", "additionalSKTInforText": "SKT-Zusatzangaben", "emailText": "E-Mail", "mobileText": "Mobilnummer"}, "PatientNavBar": {"enter": "Eingabe", "noPatientFound": "<PERSON>s wurde kein Patient gefunden", "createPatient": "Patienten erstellen", "defaultPlaceHolder": "Suche Patienten nach Namen, Geburtsdatum, Versicherungsnummer", "focusPlaceHolder": "G<PERSON>en Si<PERSON> einen Patientennamen ein oder \"/\" für die erweiterte Suche"}, "MedicationPrescription": {"gRez": "G-Rez", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez"}, "TimeLineFormDetail": {"gRez": "G-Rez", "kRez": "K-Rez", "tagPrivate": "Privat", "btm": "BTM", "tRez": "T-Rez", "dj": "Dj", "autIdem": "aut-idem", "refillForm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewForm": "Anzeigen", "editForm": "<PERSON><PERSON><PERSON>", "msgDidNotPickup": "<PERSON><PERSON> abgeholt", "lblDidNotPickup": "Markieren als \"Nicht abgeholt\"", "didNotPickupSuccess": "Als \"Nicht abgeholt\" markiert", "didNotPickupError": "Ausführung fehlgeschlagen", "printOutHint": "Verordnung wurde vorbereitet. Drucken Sie diese nun aus, um sie zu verordnen.", "more": "<PERSON><PERSON>", "removeForm": "Löschen", "viewQrCode": "QR Code anzeigen", "erezeptLabel": "eRezept", "erezeptPartialPrescriptionLabel": "eRezept - Mehrfachverordnungen", "medicationLabel": "Medikation"}, "MedicationPlanHistoryEntry": {"medicationPlan": "BMP", "addedAMedication": "Medikation wurde hinzugefügt", "updatedAMedication": "Medikation wurde aktualisiert", "deleteMedication": "Medikation wurde entfernt", "more": "<PERSON><PERSON>", "tradeName": "Handelsname", "substance": "Substanz {{index}}", "unit": "Einheit", "drugForm": "Arzneimittelform", "intakeInterval": "Einnahmezeiten", "hint": "<PERSON><PERSON><PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "additionalLine": "Zusätzliche Zeile"}, "TextModule": {"other": "Sonstige"}, "ActionChainTrigger": {"description": "Beschreibung", "reset": "Z<PERSON>ücksetzen", "run": "Starte", "resetSuccess": "Zurücksetzen erfolgreich", "removeStepError": "Sie können den letzten Schritt nicht löschen", "labelCertainty": "Sicherheit", "labelLaterality": "Lokalisation", "runActionChain": "Starte Aktionskette:", "runActionChainKey": "Shift + Enter", "alertRestrictedAction": "Bitte wählen Sie die anamnestischen Diagnosen und/oder Leistungsziffern aus, die in die Aktionskette einbezogen werden sollen. Einträge, die nicht markiert sind, werden nicht berücksichtigt.", "alertInvalidAction": "Ungültige Diagnosen und/oder Leistungsziffern wurden automatisch aus der Aktionskette ausgeschlossen. Es muss manuell im Eingabefeld dokumentiert werden. <PERSON><PERSON> dieser Fehler weiterhin besteht, wenden Si<PERSON> sich bitte an den Administrator, um die Aktionskette anzupassen.", "Category": {"VitalParameter": "Vitalwerte", "form": "Formulare", "Medication": "Medikation", "Service": "Service", "Job": "Arbeit", "Allergy": "Allergie", "Anamnesis": "Anamnese", "Findings": "Befund", "Therapy": "<PERSON><PERSON><PERSON>", "Note": "<PERSON><PERSON><PERSON><PERSON>", "AnamnesticDiagnose": "Anamnestische Diagnose", "AcuteDiagnose": "Akutdiagnose", "ServiceChain": "Leistungsziffern", "PermanentDiagnose": "Dauerdiagnose", "Form": "Formular", "Customize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "VitalParameters": {"labelVitalParameters": "Vitalwerte", "AmountOfBirth": "Anzahl der Geburten", "AmountOfChildren": "<PERSON><PERSON><PERSON>", "AmountOfPregnancies": "Anzahl der Schwangerschaften", "BloodPressure": "Blutdruck", "Creatinine": "<PERSON><PERSON><PERSON><PERSON>", "DateOfPlannedBirth": "<PERSON><PERSON><PERSON><PERSON>", "HeartFrequency": "<PERSON><PERSON><PERSON>", "Height": "Größe", "IsBreastfeeding": "In Stillzeit", "IsPregnant": "<PERSON><PERSON><PERSON>", "Weight": "Gewicht"}, "Form": {"form": "Formulare", "Muster_1": "Form 1", "Muster_2B": "Form 2", "Muster_4": "Form 4", "Muster_6": "Form 6", "Muster_13": "Form 13", "Muster_15": "Form 15", "Muster_16": "Form 16"}}, "CalendarEntry": {"patientPresentIn": "Patient ist anwesend", "waitingTimeIs": "Wartezeit", "minutes": "Minuten", "minute": "Minuten", "note": "<PERSON><PERSON><PERSON><PERSON>", "startTreatmentToDocument": "Starten Sie den Behandlungs-Timer", "startTreatment": "Behandlung starten", "stopTreatmentToDocument": "Stoppen Sie den Behandlungs-Timer", "completeTreatment": "Behandlung abgeschlossen"}, "VersionHistory": {"versionHistory": "Versions<PERSON><PERSON>f für {{date}}", "madeOn": "Datum", "activity": "Aktivität", "madeBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "changedTo": "Geändert zum", "viewVersionHistory": "Versionsver<PERSON><PERSON> anzeigen", "titleConfirmDialog": "Möchten Sie die Version für den Eintrag wiederherstellen?", "contentConfirmDialog": "Der ausgewählte Eintrag wird vom {{date}} wiederhergestellt.", "btnNo": "<PERSON><PERSON>", "btnYes": "<PERSON><PERSON>, wied<PERSON>hers<PERSON>len", "restoreSuccess": "Behandlungsverlauf Ein<PERSON>g wurde wiederhergestellt", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Remove": "Gelöscht", "Edit": "<PERSON>ktual<PERSON><PERSON>", "Restore": "Wiederhergestellt", "afterDays": "{{date}} <PERSON>en", "contentRestore": "(leer)", "afterDaysMsg": "Dauerhaft löschen nach {{date}} Tagen"}, "DoctorSample": {"doctorSample": "Ärztemuster"}, "DigaEntry": {"pznExists": "PZN wurde gefunden. Wiederverordnung mit DiGA", "invalidDiga": "DiGA nicht mehr verordnungsfähig", "processingRefill": "Wiederverordnung läuft...", "diga": "DiGA", "more": "<PERSON><PERSON>", "actionRemove": "Löschen", "viewForm": "View form"}, "ArribaEntry": {"arriba": "Arriba", "more": "<PERSON><PERSON>", "actionRemove": "Löschen", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errorStartMsg": "Arriba ist aktuell nicht aufrufbar, bitte versuchen sie es später erneut.", "startArribaTooltip": "Arriba starten", "ErrorCode_Arriba_Not_Eligible": "Der Patient ist nicht berechtigt für Arriba."}, "HistoryDialog": {"title": "<PERSON><PERSON><PERSON><PERSON> der Profilzugriffe von {{patientName}}", "openBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openOn": "Geöffnet Am", "today": "Heute am {{date}}"}, "GDTExport": {"GDTExportTitle": "{{type}} Export ({{name}})", "exportName": "{{type}} Export Name", "treatmentDate": "Behandlungsdatum", "readingDate": "Einlesedatum", "exportGDT": "{{name}} {{type}} exportiert", "startGDTExport": "Starte {{type}} Export", "gdtBtnText": "Schnittstelle", "startGDTExportAgain": "{{type}} Export erneut versuchen", "failedToExportGDTTitle": "{{type}} Export ist fehlgeschlagen", "treatmentTime": "Behandlungszeit", "readingTime": "Einlesezeit"}, "CustomizeEntry": {"more": "mehr", "editEntry": "Eintrag bearbeiten", "actionRemove": "Löschen"}, "Verah": {"title": "VERAH® TopVersorgt aktivieren", "content": "Patient ist für die Teilnahme an VERAH ® TopVersorgt geeignet. Bitte beachten Si<PERSON>, dass Schulungsfähigkeit des Patienten vorliegen muss (vgl. Anlage 14, Anhang 7) – zur Unterstützung können Sie sich am VERAH-TopVersorgt-Leitfaden orientieren.", "cancelText": "Schließen", "openGuidelines": "<PERSON><PERSON><PERSON><PERSON>"}}