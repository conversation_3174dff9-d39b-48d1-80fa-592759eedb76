{"caveHeader": "Cave", "printSettingsHeader": "Druckeinstellungen", "printerProfileLabel": "Druckerprofil:", "printOptionsLabel": "Druckoptionen", "printImmediately": "<PERSON><PERSON><PERSON>", "sendToPrintQueue": "Zur Druckerwarteschlange hinzufügen", "formPrinted": "Das Formular wurde gedru<PERSON>t", "noInfo": "<PERSON><PERSON> vorhanden", "preview": "Vorschau", "Forms": {"Muster_1": "Arbeitsunfähigkeitsbescheinigung", "Muster_2B": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Krankenhausbehandlung", "Muster_4": "Verordnung einer Krankenbeförderung", "Muster_6": "Überweisungsschein", "CoverLetterForm": "Begleitschreiben", "Muster_7": "Überweisung vor Aufnahme einer Psychotherapie zur Abklärung somatischer Ursachen", "Muster_8": "Sehhilfenverordnung", "Muster_8A": "Verord<PERSON><PERSON> von vergrößernden Sehhilfen", "Muster_11": "Bericht für den Medizinischen Dienst", "Muster_13": "Heilmittelverordnung", "Muster_15": "Ohrenärztliche Verordnung einer Hörhilfe", "Muster_16": "Arzneiverordnungsblatt", "Muster_26A": "Verordnung Soziotherapie gem. § 37a SGB V", "Muster_26B": "Verordnung Soziotherapie gem. § 37a SGB V", "Muster_26C": "Verordnung Soziotherapie gem. § 37a SGB V", "Muster_39A": "Krebsfrüherkennung Zervix-Karzinom", "Muster_39B": "Krebsfrüherkennung Zervix-Karzinom", "Muster_22A": "Konsiliarbericht vor Aufnahme einer Psychotherapie", "Muster_50": "Anfrage zur Zuständigkeit einer anderen Krankenkasse", "Muster_51": "Anfrage zur Zuständigkeit eines sonstigen Kostenträgers", "Muster_52": "Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit", "Muster_52_0_V2": "Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit", "Muster_52_2": "Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit", "Muster_55": "Bescheinigung einer schwerwiegenden chronischen Erkrankung", "Muster_3A": "Zeugnis über den mutmaßlich Tag der Entbindung", "Muster_5": "Abrechnungsschein", "Muster_9": "Bescheinigung Frühgeburt oder Behinderung des Kindes", "Muster_10": "Überweisungsschein für in-vitro-diagnostische Auftragsleitungen", "Muster_10A": "Labor- Anforderungsschein", "Muster_10C": "Auftrag der SARS-CoV-2 Testung", "Muster_12A": "Verordnung häuslicher Krankenpflege", "Muster_27A": "Soziotherapeutischer Betreuungsplangem. § 37a SGB V", "Muster_27B": "Soziotherapeutischer Betreuungsplangem. § 37a SGB V", "Muster_27C": "Soziotherapeutischer Betreuungsplangem. § 37a SGB V", "Muster_28A": "Verordnung Soziotherapie", "Gruenes_Rezept": "<PERSON><PERSON><PERSON><PERSON>", "T-Rezept-Muster": "T-Rezept", "PatientHeader": "<PERSON><PERSON><PERSON><PERSON>", "Muster_19A": "Notfall-/ Vertretungsschein", "Muster_19B": "Notfall-/ Vertretungsschein", "Muster_19C": "Notfall-/ Vertretungsschein", "Muster_21": "Ärztliche Bescheinigung für den Bezug von Krankengeld bei Erkrankung eines Kindes", "Muster_36_E_2017_07": "Präventionsempfehlung", "Muster_20A": "Wiedereingliederungsplan", "Muster_20B": "Wiedereingliederungsplan", "Muster_20C": "Wiedereingliederungsplan", "Muster_20D": "Wiedereingliederungsplan", "Muster_61": "Vordrucke für die Verordnung medizinischer Rehabilitation", "Muster_65A": "Ärztliches Attest Kind", "Muster_64": "Verordnung von medizinischer Vorsorge für Mütter oder Väter", "Muster_64B": "Verordnung von medizinischer Vorsorge für Mütter oder Väter", "Muster_70": "Behandlungsplan", "Muster_70A": "Folge- Behandlungsplan", "Muster_63B": "Verordnungs spezialisierter ambulanter Palliativversorgung", "Muster_63C": "Verordnungs spezialisierter ambulanter Palliativversorgung", "Muster_63D": "Verordnungs spezialisierter ambulanter Palliativversorgung", "Muster_56": "Antrag auf Kostenübernahme Rehasport/Funktionstraining", "Muster_N63A": "Verordnungs spezialisierter ambulanter Palliativversorgung", "BKK_BOSCH_VAG_BW_Praeventionsverordnung": "Präventionsverordnung (BKK_BOSCH_VAG_BW)", "BKK_BY_HZVNotify": "<PERSON><PERSON>r Abrechnungspositionen der Patientenbetreuung ist die Unterschrift des Patienten erforderlich. Bitte leiten Si<PERSON> ein Muster an den Patienten weiter und bewahren Sie das unterschriebene Dokument in Ihrer Praxis auf.", "Muster_PTV_1A": "<PERSON><PERSON><PERSON> auf Psychotherapie", "Muster_PTV_1B": "<PERSON><PERSON><PERSON> auf Psychotherapie", "Muster_PTV_1C": "<PERSON><PERSON><PERSON> auf Psychotherapie", "Muster_PTV_11A": "Individuelle Information zur psychotherapeutischen Sprechstunde", "Muster_PTV_2A": "<PERSON>aben Therapeut*in", "Muster_PTV_3": "Leitfaden zum erstellen des Berichts an die Gutachter*in", "Muster_PTV_10": "Ambulante Psychotherapie in der gesetzlichen Krankenversicherung", "Muster_PTV_12A": "Anzeige einer Akutbehandlung", "G81_EHIC_Bulgarisch": "G81 EHIC Bulgarisch", "G81_EHIC_Danisch": "G81 EHIC <PERSON>", "G81_EHIC_Englisch": "G81 EHIC Englisch", "G81_EHIC_Franzosisch": "G81 EHIC <PERSON>", "G81_EHIC_Griechisch": "G81 EHIC Griechisch", "G81_EHIC_Italienisch": "G81 EHIC Italienisch", "G81_EHIC_Kroatisch": "G81 EHIC Kroatisch", "G81_EHIC_Niederlandisch": "G81 EHIC Niederlandisch", "G81_EHIC_Polnisch": "G81 EHIC Polnisch", "G81_EHIC_Rumanisch": "G81 EHIC Rumanisch", "G81_EHIC_Spanisch": "G81 EHIC Spanisch", "G81_EHIC_Tschechisch": "G81 EHIC Tschechisch", "G81_EHIC_Ungarisch": "G81 EHIC Ungarisch", "G81_EHIC_Finnisch": "G81 EHIC Finnisch", "G81_EHIC_Estnisch": "G81 EHIC Estnisch", "G81_EHIC_Slowenisch": "G81 EHIC Slowenisch", "G81_EHIC_Slowakisch": "G81 EHIC Slowakisch", "G81_EHIC_Schwedisch": "G81 EHIC Schwedisch", "G81_EHIC_Portugiesisch": "G81 EHIC Portugiesisch", "G81_EHIC_Litauisch": "G81 EHIC Litauisch", "G81_EHIC_Lettisch": "G81 EHIC Lettisch", "G81_EHIC_All": "G81 EHIC All", "LKK-Handover-Letter": "Prax<PERSON><PERSON><PERSON><PERSON><PERSON>", "F1000": "Durchgangsarztbericht", "F1050": "Ärztliche Unfallmeldung", "F2100": "Zwischenbericht", "F9990": "<PERSON><PERSON><PERSON><PERSON>", "BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6": "BKK BOSCH BW Schnellinfo Patientenbegleitung"}, "FormOverview": {"formName": "Formular", "formId": "Formular-ID", "FormAction_PrintFull": "<PERSON><PERSON><PERSON>", "FormAction_PrintHeader": "Patientenkopf drucken", "FormAction_PrintWithoutContent": "Blankoformular drucken", "FormAction_PrintWithBSNRAndLANR": "Ausdruck mit BSNR/LANR", "FormAction_OpenNewTab": "<PERSON><PERSON><PERSON>", "print": "<PERSON><PERSON><PERSON>", "form": "Formular", "svDocument": "Dokument", "showSVDocumentsOnly": "Nur Dokumente anzeigen"}, "ToggleSearchType": {"HIMICatalogue": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "Hierarchy": "Katalog", "FreeText": "Freitext"}, "SearchProductViewResult": {"ProductNumber": "Produktnummer", "Description": "Beschreibung", "Manufacturer": "<PERSON><PERSON><PERSON>"}, "SearchFreetext": {"PlaceHolder": "Eingabe Produkttyp, Produktnummer,..."}, "SearchHierachy": {"ProductGroup": "PRODUKTGRUPPE", "ORT": "ANWENDUNGSORT", "Subgroup": "UNTERGRUPPE", "NoResult": "<PERSON><PERSON>."}, "SearchProductTypeViewResult": {"NoResult": "<PERSON><PERSON>", "ShowSingleProductList": "Einzelproduktliste anzeigen"}, "ProductList": {"Reason": "Begründung", "Prescribe": "<PERSON><PERSON><PERSON>", "UseSelectedForm": "Ausgewähltes Formular verwenden", "SingleProductList": "Produktliste zur ausgewählten Produktart", "ProductNotFound": "<PERSON><PERSON>.", "ProductNumber": "PRODUKTNUMMER", "Description": "BEZEICHNUNG", "Manufacturer": "HERSTELLER", "ReasonRequired": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "ReasonPlaceHolder": "Fügen Sie eine Begründung hinzu, warum Sie sich für eine Einzelverordnung entscheiden.", "Tooltip": "Bei Auswahl eines konkreten Produktes ist die Angabe einer Begründung im Freitextfeld- bspw. hinsichtlich besonderer medizinischer und therapeutischer Erfordernisse - gem. Hilfsmittel-RL erforderlich."}, "Prescrible": {"titleEdoctor": "eArztbrief", "textmoduleHint": "In Freitextfeldern Shortcut eingeben oder Strg + Leertaste drücken", "errMissingPrinterConfig": "Drucken fehlgeschlagen. Bitte überprüfen Sie ihre Druckereinstellungen.", "ErrorCode_PrinterProfile_Not_Found": "<PERSON>s wurde kein Druckerprofil gefunden", "ErrorCode_PrinterHost_Not_Found": "<PERSON>s wurden keine Druckeinstellungen gefunden", "PrescribleForm": "Formular", "GetReferralCodeWait": "Überweisungscode wird angefordert, bitte warten.", "GetReferralCodeSuccess": "Überweisungscode erstellt. Klicke hier um ihn hinzuzufügen <a href={{link}}> </a>", "GetReferralCodeFail2": "Die Anforderung eines Vermittlungscode ist fehlgeschlagen. Bitte informieren Sie Ihren Patienten, dass dieser sich an die Terminservicestelle (Tel. 116117, www.116117.de) für die Vermittlung eines Termins wenden kann.", "Print": "<PERSON><PERSON><PERSON>", "Save": "Speichern", "Update": "Aktualisieren", "ErrorMsg": "Ausfüll<PERSON><PERSON>", "WarningMsg": "Das Formular ist nicht vollständig ausgefüllt", "WarningStartDateMessage": "Grundsätzlich ist die Bescheinigung von AU für den Zeitraum vor dem Datum der Feststellung nicht möglich. In Ausnahmefällen kann während des Zeitraums der Entgeltfortzahlung eine Rückdatierung der Arbeitsunfähigkeit bis zu drei Tagen erfolgen. Bei Folgebescheinigungen kann auf die Eintragung des Datums im Feld 'arbeitsunfähig seit' verzichtet werden", "ErrorEndDateMessage": "Das Datum „bis“ darf nicht mehr als 31 Tage nach dem Feld „festgestellt am“ sein.", "InvalidICDCodeError": "Geben Sie die Diagnose(n) als endständige ICD-10-GM-Codes an.", "InvalidICDCodeSecondary": "Der ICD-10-GM-Code ist nicht abrechenbar und kann deshalb nicht zur Ausstellung einer Arbeitsunfähigkeitsbescheinigung verwendet werden.", "InvalidICDCodeReserved": "Der ICD-10-GM-Kode ist nicht mit Inhalt belegt und darf daher nicht zur Abrechnung und/oder bei den Formularen der vertragsärztlichen Versorgung verwendet werden.", "InvalidICDCodeNonExist": "Der gespeicherte ICD-Kode ist nicht in der ICD-10-GM-Stammdatei vorhanden. <PERSON><PERSON> darf dieser Code nicht zur Abrechnung oder in Formularen verwendet werden.", "HintCharactersMsg": "Einige Zeichen werden aufgrund der Überschreitung der maximalen Länge gelöscht. Bitte überprüfen Sie den Inhalt erneut.", "LimitCharacters": "Maximal {{ number }} <PERSON><PERSON><PERSON>", "LeavePageTitle": "Möchten Sie ihre nicht gespeicherten Änderungen verwerfen?", "CancelLeave": "Abbrechen", "ConfirmLeave": "Ja, verwerfen", "RezeptSaved": "Rezept wurde ges<PERSON>ichert", "Doctor": "Arzt/ Ärztin", "Language": "<PERSON><PERSON><PERSON>", "FormPrescribed": "Formular wurde verordnet", "FormSaved": "Formular wur<PERSON> g<PERSON><PERSON><PERSON>", "LabID": "Laborauftragsnummer", "Send": "Senden", "errLabId": "Muss 10 bis 60 Zeichen enthalten", "errDuplicatedLabId": "Diese Laborauftragsnummer wurde bereits verwendet.", "FormConsiderationTitle": "Welches Formular möchten Sie verwenden?", "FormConsideration_PrintedForm": "<PERSON><PERSON><PERSON>", "FormConsideration_Digital": "Elektronische Übermittlung an das Labor", "LDKModuleDisableAlert": "Labor- Prüfmodul ist deaktiviert. Um den elektronischen Versand nutzen zu können, muss dieses aktiviert werden.", "ErrorSelectSchein": "Bitte wählen Sie einen Schein", "ReceiveCodeSuccess": "Überweisungscode hinzugefügt", "labRecipient": "Labor", "additionalInfo": "Zusatzinformationen", "AOK_FA_OC_BW_Antrag_AOK_Sports_V3_additionalInfo": "Vertrag zur Versorgung in den Fachgebieten Orthopädie und Rheumatologie in Baden-Württemberg gemäß § 73c SGB V", "formDetails": "Formularinformationen", "overtakeWarning": "Die maximale Anzahl wurde erreicht", "Recipe": "Rezept", "GBAGuideline": "G-BA Richtlinie", "Adressliste": "Adressliste des Sozialen Dienstes", "transitionManagementHint": "Für die Abrechnung der Leistung(en) \"Überleitungsmanagement\" ist die Unterschrift des Patienten auf dem Patientenmerkblatt zwingend erforderlich. Bitte händigen Sie dieses dem Patienten aus und lassen es von ihm unterschreiben. Das Original des Patientenmerkblatts zum Versorgungsmanagement verbleibt in der Praxis, eine Kopie bzw. ein Exemplar muss jeweils dem Patienten ausgehändigt werden.", "noFaxNumberHint": "<PERSON>ür die Krankenkasse liegt uns keine Fax-Nummer vor. <PERSON><PERSON> <PERSON>, sich bei der zuständigen Krankenkasse direkt nach der Fax-Nummer zu erkundigen.", "includeLabResult": "Laborbefund anhängen", "BKK_BY_HZV_ConfirmLeave": "Sind <PERSON><PERSON> sic<PERSON>, dass Si<PERSON> im vorliegenden Fall keine Schnellinformation an die Krankenkasse senden möchten? Eine frühzeitige Intervention und unterstützende Maßnahmen der Krankenkassen können dazu beitragen, die Krankheitsfolgen zu lindern", "BKK_BY_HZV_YesLeave": "<PERSON><PERSON>, eine Schnellinformation ist nicht erforderlich", "BKK_BY_HZV_CancelLeave": "<PERSON><PERSON>, ich möchte die Schnellinfo zur Patientenbegleitung versenden", "Lab": "Labor", "OrderEntrySystem": "Auftragseingabesystem (Order Entry System)", "ErrorCode_ValidationError_Form_IcdIsRequired": "ICD-Code ist eine Pflichtangabe", "missingEHBA": "Fehlende eHBA", "Warning": "<PERSON><PERSON><PERSON>", "ErrorStartDateOverEndDateMessage": "Das Startdatum muss vor dem Enddatum liegen.", "ErrorMissingIcd10CodeMessage": "Die Angabe einer Diagnose ist ein Pflichtfeld", "warningTextEHBA": "Bitte prüfen Sie, ob die Karte eingesteckt ist und die Einstellungen korrekt sind.", "setUpKimAccount": "<PERSON><PERSON> richten Si<PERSON> ein KIM-Konto ein", "signSuccess": "eArztbrief wurde signiert", "signFailure": "eArztbrief signieren fehlgeschlagen", "signProcessing": "eArztbrief wird mit HBA signiert..."}, "Form": {"M1ICDNotify": "Bitte prüfen Sie bei fortgesetzter AU aufgrund unspezifischer Depression, ob Sie durch gezielte diagnostische und therapeutische Maßnahmen Vorteile für den Krankheitsverlauf erzielen können.", "M1EmploymentDialog": {"ReviewEmploymentInformation": "Arbeitgeberinformationen aktualisieren", "M1ICDNotifyContent": "Bitte füllen Sie vor der Ausstellung der AU den Beschäftigungsstatus und die Beschäftigungsart aus bzw. aktualisieren oder bestätigen Sie die Aktualität der Daten!", "Cancel": "Abbrechen", "Review": "<PERSON><PERSON><PERSON>"}}, "TimePeriod": {"No": "<PERSON><PERSON>", "1Weeks": "1-2 Wochen", "1Month": "1 Monat", "3Months": "3 Monate", "6Months": "6 Monate", "9Months": "9 Monate", "12Months": "12 Monate"}, "Diagnosis": {"DiagnosisOption": "Diagnose(n) (optional)", "Diagnosis": "Diagnose(n)", "PlaceHolder": "<PERSON><PERSON><PERSON>en Sie eine dokumentierte Diagnose aus oder geben Freitext ein"}, "Anotation": {"HimiProduct": "HILFSMITTEL", "Quantity": "MENGE", "TimePeriod": "ZEITRAUM", "FurtherInformation": "WEITERE INFORMATIONEN (OPTIONAL)", "Change": "<PERSON><PERSON><PERSON>", "AddAnotherDiagnosis": "Weitere Diagnose hinzufügen"}, "Muster8Confirm": {"Muster_8": "Sehhilfenverordnung M8", "Muster_8A": "Verord<PERSON>ng von vergrößernden Sehhilfen M8A", "Title": "Welches Formular für die Verordnung von Sehhilfen möchten Sie verwenden?", "Content": "Maßgeblich hierfür ist der Fernvisus. In Fällen, in denen eine vergrößernde Sehhilfe(Muster 8 A) verordnet wird, ist der Wert des bestkorrigierten Fernvisus für das rechte und linke Auge unter Angabe der verwendeten Sehhilfe(Brille und / oder Kontaktlinse) und der benötigte Vergrößerungsbedarf anzugeben."}, "Timeline": {"forms": "Formulare", "PrintOutHint": "Formular wur<PERSON> g<PERSON><PERSON><PERSON>", "NewHimi": "Verordnung neuer Hilfsmittel", "RefillForm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewForm": "Anzeigen", "EditForm": "Formular bear<PERSON>ten", "ControllableFromTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ControllableHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s) Hilfsmittel {{deviceName}} für {{contractId}}", "Remove": "Löschen", "HandedOver": "Als Ausgehändigt markieren", "Received": "Als Eingegangen markieren", "RezeptSaved": "Formular wur<PERSON> g<PERSON><PERSON><PERSON>", "RemovedRezeptur": "Verordnung wurde entfernt", "editEntryDate": "<PERSON><PERSON>", "More": "<PERSON><PERSON>", "M52Hint": "Bitte faxen Sie das ausgefüllte Formular an die links unten auf dem Formular dargestellte Faxnummer.", "M52ForM1Hint": "Mit Vollendung der heutigen AU war der Versicherte mehr als 42 Kalendertage in den letzten180 Kalendertagen krankgeschrieben. Bitte prüfen Sie, ob Sie den „Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit'' (Muster 52.2) ausfüllen müssen.", "TimelineUpdatedSuccess": "Behandlungseintrag wurde bearbeitet", "TimelineUpdatedFail": "Behandlungseintrag konnte nicht aktualisiert werden"}, "RemoveEntryConfirm": {"Title": "Verordnung löschen", "Content": "Sie sind dabei die Verordnung dauerhaft zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "Remove": "Löschen", "Cancel": "Abbrechen"}, "ConfirmDialog": {"printDate": "Patientenkopf Ausdruck", "printDateCheckbox": "Datum für Patientenkopf auswählen", "date": "Patientenkopf Datum", "TitleDialogDummyCannotBePrinted": "Patientenkopf kann nicht gedruckt werden.", "ContentDialogDummyCannotBePrinted": "<PERSON><PERSON><PERSON> den Abrechnungs- und Formulardruck ist die VKNR 74799 nicht zugelassen."}, "AddNewDiagnosisDialog": {"title": "Neue Diagnose hinzufügen", "documentedDate": "Dokumentationsdatum", "treatingDoctor": "Be<PERSON><PERSON>nder Arzt", "diagnosis": "Diagnose(n)", "typeOfDiagnosis": "Art der Diagnose", "certainty": "Diagnosesicherheit", "location": "Lokalisation", "addDiagnosisToTimeline": "Diagnose dem Behandlungsverlauf hinzufügen", "diagnoses": {"anamnestic": "Anamnestische Diagnose", "acute": "Akutdiagnose", "permanent": "Dauerdiagnose"}, "gesichert": "<PERSON><PERSON><PERSON><PERSON>", "verdacht": "Verdacht auf", "zustandNach": "<PERSON><PERSON><PERSON> nach", "ausgeschlossen": "Ausgeschlossen"}, "SelectDiagnosisDialog": {"buttonText": "<PERSON><PERSON>nah<PERSON> von Diagnosen", "buttonTakeover": "Speichern", "title": "Diagnosen auswählen", "Filter": {"search": "<PERSON><PERSON>", "dateRange": "TT.MM.JJJJ - TT.MM.JJJJ"}, "Table": {"date": "Datum", "diagnosis": "Diagnosen", "addDiagnosis": "Diagnose dem Behandlungsverlauf hinzufügen", "noResultFound": "<PERSON><PERSON> wurden keine Daten gefunden", "treatmentRelevant": "behandlungs-relevant", "ANAMNESTIC": "Anamnestische Diagnose ({{count}})", "PERMANENT": "Dauerdiagnose ({{count}})", "ACUTE": "Akutdiagnose ({{count}})", "edit": "<PERSON><PERSON><PERSON>", "add": "Hinzufügen", "unmarkAsTreatmentRelevant": "Nicht behandlungsrelevant", "markAsTreatmentRelevant": "Als behandlungsrelevant markieren", "diagnoseMarkedAsTreatmentRelevant": "Diagnose wurde als behandlungsrelevant markiert", "diagnoseUnMarkedAsTreatmentRelevant": "Diagnose wurde als nicht behandlungsrelevant markiert", "addPermanentDiagnosis": "Dauerdiagnose hinzufügen", "addAcuteDiagnosis": "Akutdiagnose hinzufügen"}, "continueWithoutTakingOver": "Fortfahren ohne Übernahme von Diagnosen", "requiredAtLeast": "Mindestens eine Diagnose muss ausgewählt werden.", "atLeastDiagnoseCanBill": "Es muss mindestens eine abrechenbare Diagnose übernommen werden", "informPsychotherapyCase": "Zur Kodierung der Pseudo-GOP kann ein ICD-10-<PERSON><PERSON> aus dem letzten Behandlungsfall verwendet werden.", "selectedRows": "{{count}} Diagnose(n) ausgewäht"}, "SelectMedicinesDialog": {"buttonText": "Übertragen der verordneten Medikamente", "buttonTakeover": "Transfer", "title": "Auswählen Medikamente", "selectedRows": "{{count}} Einträge ausgewählt", "Table": {"prescribed": "verord<PERSON>", "medication": "Medikation", "prescribedBy": "ve<PERSON><PERSON><PERSON> von", "noResultFound": "Es konnten keine Daten gefunden werden"}}, "ReferralThroughTSSDialog": {"title": "TSS Vermittlungcode", "specialistGroup": "Facharztgruppe", "urgency": "Dringlichkeit", "additionalQualification": "Zusatzqualifikation", "lanr": "LANR", "bsnr": "BSNR", "cancel": "Abbrechen", "getReferralCode": "Vermittlungscode anfordern", "urgent": "Dringend", "urgentDescription": "Der Patient benötigt inner<PERSON>b von 4 Wochen einen Termin beim Facharzt.", "routine": "Nichtdringend", "codesystemService": "TSS Codesystem", "procedure": "Probatorisches Verfahren", "routineDescription": "Eine Wartezeit über 4 Wochen hinaus erscheint zumutbar.", "specialistGroupRequired": "Fachgruppe ist ein Pflichtfeld", "urgencyRequired": "Dringlichkeit ist ein Pflichtfeld", "lanrRequired": "LANR ist ein Pflich<PERSON>feld", "bsnrRequired": "BSNR ist ein Pflichtfeld", "lanrInvalid": "LANR ist ein ungültig", "bsnrInvalid": "BSNR ist ein ungültig", "missingKVConnectTooltip": "Um diese Funktion zu nutzen, wenden Sie sich bitte an ihr Systemhaus, um ein KV-Connect-Konto einzurichten.", "codesystemRequired": "Codesystem ist ein Pflichtfeld", "tooltipDescription_1": "Um diese Funktion zu aktivieren, müssen folgende Informationen ausgewählt werden:", "tooltipDescription_2": "1. Ambulante Psychotherapie\n2. Die psychologische Behandlung kann nicht in dieser Praxis durchgeführt werden\n3. Weitervermittlung\n4. <PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON>"}, "Warning": {"noInsurance": "Achtung: Im aktuellen Quartal liegt kein gültiger Versicherungsnachweis vor. Ohne gültigen Versicherungsnachweis sind Regressforderungen möglich!", "mediContract": "Bitte fügen Sie vor dem Versand der Versichertenteilnahmeerklärung das Therapieverfahren, die Diagnose(n) und die Diagnosesicherheit hinzu.", "indicatedDiagnose": "Eine verordnungsspezifische Erkrankung ist nicht dokumentiert. Bitte überprüfen Sie die Verordnung für diesen Patienten.", "M1Duration": "Mit Vollendung der heutigen AU war der Versicherte mehr als 42 Kalendertage in den letzten 180 Kalendertagen krankgeschrieben. Bitte prüfen Sie, ob Sie den „Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit\" (Muster 52.2) ausfüllen müssen.", "M1ICDNotify": "Bitte prüfen Sie bei fortgesetzter AU aufgrund unspezifischer Depression, ob Sie durch gezielte diagnostische und therapeutische Maßnahmen Vorteile für den Krankheitsverlauf erzielen können", "transitionManagement": "Für die Abrechnung der Leistung \"Überleitungsmanagement\" ist die Unterschrift des Patienten auf dem Patientenmerkblatt zwingend erforderlich. Bitte händigen Sie dieses dem Patienten aus und lassen es von ihm unterschreiben. Das Original des Patientenmerkblatts zum Versorgungsmanagement verbleibt in der Praxis, eine Kopie bzw. ein Exemplar muss jeweils dem Patienten ausgehändigt werden.", "M52Notify": "Bitte faxen Sie das ausgefüllte Formular an die links unten auf dem Formular dargestellte Faxnummer.", "quickInformation": "Zur Abrechnung der Vergütungsposition bezüglich der Patientenbegleitung ist die Unterschrift des Patienten auf dem Patientenmerkblatt zwingend erforderlich. Bitte händigen Sie dieses dem Patienten aus und lassen es von ihm unterschreiben."}, "Error": {"dummyVknr": "Die VKNR 74799 is für Abrechnung und Formularbedruckung nicht zulässig."}, ":": "Druckerprofile", "Ìnformation": {"hint": "<PERSON><PERSON><PERSON> und Beratungen zur häuslichen Krankenpflege stehen Ihnen unsere Mitarbeiter u.a. Pflegefachkräfte und zertifizierte Wundexperten der AOK Baden-Württemberg gerne zur Verfügung."}, "Information": {"hint": "<PERSON><PERSON><PERSON> und Beratungen zur häuslichen Krankenpflege stehen Ihnen unsere Mitarbeiter u.a. Pflegefachkräfte und zertifizierte Wundexperten der AOK Baden-Württemberg gerne zur Verfügung.", "himiQuestionName": "Bitte faxen Sie die Verordnung (Muster 16) und einen etwaigen Fragebogen an die auf dem Muster 16 gedruckte Faxnummer der BKK des Versicherten. Bitte händigen Sie die Verordnung (Muster 16) und das Merkblatt Versicherter zum Thema Hilfsmittel an den Patienten aus."}, "Generated": "<PERSON><PERSON><PERSON><PERSON>", "Printed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Incorrect": "Fehlerhaft", "PrintedCreated": "<PERSON><PERSON><PERSON><PERSON>", "PrintedSuccesfully": "Erfolgreich", "PrintedFailed": "Fehlerhaft", "HandedOver": "Ausgehändi<PERSON>", "Received": "Eingegangen", "ConfirmCreateScheinDialog": {"title": "Es existiert kein Schein mit einer dokumentierten Diagnose", "description": "Um eine Diagnose zu dokumentieren, legen Sie bitte einen Schein an", "confirmBtn": "Neuen Schein erstellen"}, "EAU": {"settings": "eAU", "doctor": "Arz<PERSON>", "printForCopy": "<PERSON><PERSON> ausdrucken", "employer": "Arbeitgeber", "patient": "Patient", "printer": "<PERSON>ucker", "insurance": "Versicherung", "tray": "Tray"}, "HandoverLetter": {"title": "Prax<PERSON><PERSON><PERSON><PERSON><PERSON>", "print": "LKK Praxisübernahme ausdrucken", "requiredField": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "doctor": "Doctor", "timeOfMoving": "Zeitpunkt der Übernahme", "upCommingDoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectPatient": "Wähle Patienten", "maximum": "Maximum {{value}} Patienten", "bsnr": "BSNR", "lanr": "LANR", "expertise": "EXPERTISE", "tel": "Tel.", "salutation": "<PERSON><PERSON> geehrte/r", "male": "<PERSON>", "female": "<PERSON><PERSON>", "savedSuccessfully": "Alle Formulare wurden gespeichert", "savedFail": "Es konnten nicht alle Formulare gespeichert werden", "printedSuccessfully": "Alle Formulare gedruckt", "printedFail": "Drucken ist fehlgeschlagen", "printPreview": {"title": "Praxis<PERSON><PERSON><PERSON><PERSON> d<PERSON>cken", "text1": "Ihre Teilnahme an der Hausarztzentrierten Versorgung", "text2": "gerne informiere ich Si<PERSON> heute, dass ich zum", "text3": "meine hausärztliche Praxis an meinen", "text4": "Praxisnachfolger", "text5": "übergeben werde.", "text6": "Sie haben sich entschieden, bei mir am Hausarztprogramm teilzunehmen.", "text7": "<PERSON><PERSON><PERSON>", "text8": "am Hausarztprogramm können Si<PERSON> gerne in Zukunft auch bei meinem Praxisnachfolger", "text9": "weiterführen.", "text10": "<PERSON>n <PERSON> sich dazu entsch<PERSON>ßen, müssen Si<PERSON> keine weiteren Schritte", "text11": "unternehmen.", "text12": "Sollten Sie jedoch mit Ihrer weiteren Teilnahme am Hausarztprogramm bei meinem", "text13": "Praxisnachfolger nicht einverstanden sein, haben Si<PERSON> die Möglichkeit bei Ihrer Krankenkasse", "text14": "innerhalb von 4 Wochen ab Zugang dieses Schreibens schriftlich", "text15": "Widerspruch", "text16": "einzulegen.", "text17": "<PERSON>hr Widerspruch ist in diesem Fall an folgende Adresse zu richten:", "text18": "Sozialversicherung für Landwirtschaft, Forsten und Gartenbau,", "text19": "<PERSON><PERSON>,", "text20": "Dr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1", "text21": "84036 <PERSON><PERSON><PERSON>", "text22": "Ihre hausärztliche Versorgung durch mich ist in jedem Fall bis zu meinem Ausscheiden aus der", "text23": "Praxis sichergestellt.", "text24": "<PERSON><PERSON> hoffe, dass Sie meinem Praxisnachfolger auch in Zukunft Ihr Vertrauen schenken werden.", "text25": "Mit freundlichen Grüßen"}}, "confirmSickLeaveDialog": {"title": "Krankmeldung: <PERSON><PERSON><PERSON>", "description": "Mit Vollendung der heutigen AU war der Versicherte mehr als 42 Kalendertage in den letzten 180 Kalendertagen krankgeschrieben. Bitte prüfen Sie, ob Sie den „Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit\" (Muster 52.2) ausfüllen müssen.", "cancelButton": "OK", "confirmButton": "M52.2 öffnen"}, "confirmMaximumUVGoaF9990": {"title": "B<PERSON> Rechnung (F9990)", "description": "Nutzen Sie dieses Formular für Rechnungen mit bis zu 14 Positionen. Erstellen Sie weitere BG-Rechnungen über die Briefschreibung (BR).", "cancelButton": "Abbrechen", "confirmButton": "<PERSON><PERSON><PERSON>"}}