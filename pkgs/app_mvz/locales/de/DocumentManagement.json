{"document-overview": " Übersicht Dokumente", "imported-date": "Datum des Imports", "document": "Dokument", "sender": "Absender", "type": "<PERSON><PERSON>", "documentType": "Dokumententyp", "documentView": "Datenansicht", "description": "Beschreibung", "patient": "Patient", "searchPlaceholder": "Suche nach Name, Dokumententyp", "filter": "Filter", "switchFilter": "Anzeige nicht zugeordneter Dokumente", "resetToDefault": "Auf Standard zurücksetzen", "importedDocuments": "Importierte Dokumente", "failToImport": "Import fehlgeschlagen", "errorMessage": "Fehlerbeschreibung", "import": "Importieren", "gdtexport": "GDT Export", "gdtimport": "GDT Import", "externaldocument": "Externes Dokument", "importFailMessage": "Import für {{count}} Dokument/en fehlgeschlagen", "importSuccessMessage": "{{count}} Dokumente importiert", "reimportReqSent": "Erneuter Import wird geprüft", "removeDocumentTitle": "Dokument löschen?", "removeDocumentDesc": "Dokument wird gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "timeline": {"viewDocument": "Dokument anzeigen", "makeAsRead": "Make as read", "makeAsUnread": "Make as unread", "removeDocument": "Remove document", "documentType": "Dokumententyp", "Sender": "Sender", "description": "Beschreibung", "readBy": "<PERSON><PERSON><PERSON> von", "readAt": "Gelesen am", "read": "<PERSON><PERSON><PERSON>", "new": "<PERSON>eu", "markAsRead": "Als gelesen markieren", "markAsUnread": "Als nicht gelesen markieren", "remove": "Löschen", "sender": "Absender"}, "searchPatient": "<PERSON><PERSON><PERSON> Sie den Namen des Patienten ein, um die Suche zu starten.", "searchSender": "<PERSON><PERSON><PERSON>en oder geben Si<PERSON> den Sender ein", "searchDocumentType": "Dokumententyp Schlagwörter", "failedImportMessage": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "gdtImportSuccessMessage": "Die GDT-Datei wird importiert und {{patientName}} zugewiesen.", "ldtImportSuccessMessage": "Die LDT-Datei wird importiert und {{patientName}} zugewiesen.", "gdtImportSuccessMessageNoPatient": "Die GDT-<PERSON><PERSON> wird importiert.", "ldtImportSuccessMessageNoPatient": "Die LDT-<PERSON>i wird importiert.", "notSupportFormat": "Dateiformat nicht unterstützt.", "downloadFile": "<PERSON><PERSON>", "updateDocumentSuccessfully": "Dokument erfolgreich aktualisiert"}