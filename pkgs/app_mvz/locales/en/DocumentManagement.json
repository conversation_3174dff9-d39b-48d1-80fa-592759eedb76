{"document-overview": " Document Overview", "imported-date": "Imported Date", "document": "Document", "sender": "Sender", "type": "Type", "documentType": "Document type", "documentView": "Document View", "description": "Description", "patient": "Patient", "searchPlaceholder": "Search name, type", "filter": "Filter", "switchFilter": "Only show not assigned documents", "resetToDefault": "Reset to default", "importedDocuments": "Imported documents", "failToImport": "Failed to import", "errorMessage": "Error message", "import": "Import", "gdtexport": "GDT Export", "gdtimport": "GDT Import", "externaldocument": "External Document", "importFailMessage": "Failed to import {{count}} documents", "importSuccessMessage": "{{count}} documents imported", "reimportReqSent": "Reimport request submitted", "removeDocumentTitle": "Remove document?", "removeDocumentDesc": "Document will be removed. This action cannot be undone.", "timeline": {"viewDocument": "View document", "markAsRead": "<PERSON> as read", "markAsUnread": "<PERSON> as unread", "remove": "Remove", "documentType": "Document type", "sender": "Sender", "description": "Description", "readBy": "Read by", "readAt": "Read at", "read": "Read", "new": "New"}, "searchPatient": "Enter the patient's name to start the search.", "searchSender": "Search for a sender", "searchDocumentType": "Search for a document type", "failedImportMessage": "An error occurred in the import. Please try again.", "gdtImportSuccessMessage": "The GDT file is imported and assigned to {{patientName}}.", "ldtImportSuccessMessage": "The LDT file is imported and assigned to {{patientName}}.", "gdtImportSuccessMessageNoPatient": "The GDT file is imported.", "ldtImportSuccessMessageNoPatient": "The LDT file is imported.", "notSupportFormat": "File format not supported.", "downloadFile": "Download file", "updateDocumentSuccessfully": "Document updated"}