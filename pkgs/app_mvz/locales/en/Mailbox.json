{"Header": {"mailboxOverview": "KIM Mailbox", "kvConnectMailbox": "KV-Connect Mailbox", "inbox": "Inbox", "outbox": "Outbox", "archived": "Archived"}, "EmptyState": {"noAccountAvailable": "No KIM account available", "noConversationSelected": "No conversation selected", "noKVAccountAvailable": "No KV-Connect account available", "contactToSetUpKVAccount": "Please contact system support to set up a KV-Connect account.", "contactToSetUpAccount": "Please contact system support to set up a KIM account.", "noMessageSelected": "No message selected"}, "ButtonActions": {"forward": "Forward", "reply": "Reply", "viewBillingHistory": "View billing history", "no": "No", "yesArchive": "Yes, archive", "yesDelete": "Yes, delete", "createSchein": "Create a new schein"}, "MailComposer": {"aw": "AW", "fwd": "Fwd", "date": "Date", "To": "To", "Send": "Send", "Subject": "Subject", "RequestDeliveryConfirmation": "Request delivery confirmation", "FileTooLarge": "File is too large", "sentSuccessfully": "Email sent", "leaveThisMail": "Leave this email?", "confirmLeaveThisMail": "Unsaved changes will be discarded if you leave this page.", "failedToSend": "Failed to send email", "selectPatient": "Select patient", "forwardedMessage": "Forwarded message", "SelectCategory": "Select category", "fileUploaded": "File uploaded", "defaultContentBody": "Default email content for eDoctor Letter via KIM", "getSettingFailureMessage": "Failed to retrieve setting"}, "ActionsBar": {"placeholderSubject": "Search for subject", "placeholderFrom": "Search for sender", "placeholderTo": "Search for recipient", "autoSendMDN": "Confirmation of receipt"}, "BulkAction": {"selectAllMalls": "Select all emails", "unselectAllMails": "Deselect all emails", "countItemSelected": "{{ count }} item selected", "confirmHeaderDialog": "Confirm bulk action", "confirmContentDialog": "This action will affect {{ count }} email(s) in the mailbox. Confirm to continue?", "emailsMarkedAsRead": "Email(s) marked as read", "emailsMarkedAsUnread": "Email(s) marked as unread", "emailsArchived": "Email(s) archived", "emailsMovedToInbox": "Email(s) moved to inbox"}, "Subject": "Subject", "From": "From", "To": "To", "toMe": "To me", "someoneElse": "someone else", "archive": "Archive", "unarchive": "Unarchive", "moveToInbox": "Move to inbox", "markAsRead": "<PERSON> as read", "markAsUnread": "<PERSON> as unread", "markAsReadSuccessfully": "Email marked as read", "markAsUnreadSuccessfully": "Email marked as unread", "noResultFound": "No results found", "viewMail": "View email", "viewEDoctorLetter": "View Doctor Letter", "remove": "Remove", "refreshPage": "refresh page", "refreshInBox": "Refresh inbox", "unblockCard": "unblock card", "newEmail": "New Email", "assignpatient": "Assign to <PERSON><PERSON>", "archivePatient": "Archive", "attachment": "Attachments", "activateCard": "activate card", "mail_card_not_available": "Card is not found. To send or receive new e-mails, insert card into card reader. After card is inserted, ", "mail_card_blocked": "Card is blocked. To send or receive new e-mails, ", "mail_card_not_active": "Card is not activated. To send or receive new e-mails, ", "mail_card_valid": "Card valid", "messageSource": "Message source", "viewMessageSource": "View message source", "noMessageSource": "This email has no message source", "notAssignable": "Not Assignable", "tooltipProNotAssignableLine1": "Received professional feedback is not assignable.", "tooltipTechNotAssignableLine1": "Received technical feedback is not assignable.", "tooltipNotAssignableLine2": "Please consult with KV for further instructions", "headerSender": "Sender:", "dispatchedOn": "Dispatched on:", "messageId": "Message ID:", "guid": "GUID:", "setting": {"notAutoDeleteWarning": "Archived emails will not be deleted automatically", "autoDeleteMailWarning": "Archived emails will be deleted automatically after {{ days }} days.", "changeSettings": "Change settings", "settingSaved": "Setting saved", "settingSaveFailed": "Saving the setting failed", "notDelete": "Do not delete archived e-mails", "autoDelete": "Delete archived e-mails automatically", "days": "days", "after": "After", "dialogTitle": "Archive email settings"}, "archiveDialogTitle": "Archive email?", "archiveDialogActiveContent": "Emails will be archived and deleted automatically after {{ days }} days.", "archiveDialogInactiveContent": "Emails will be archived.", "deleteDialogTitle": "Delete email?", "deleteDialogContent": "Do you want to permanently delete this email? This action cannot be undone.", "archived": "Archived", "emailDeleted": "E-mail deleted", "createPatient": "Create patient", "tooltipMDNRequested": "MDN requested", "tooltipMDNReceived": "MDN received", "tooltipMDNSent": "MDN sent", "noActiveKvSchein": "No active KV schein found for this patient", "assignSuccess": "The email is automatically assigned to {{patient}}", "assignFailure": "Failed to assign email by {{error}}.", "canNotAssign": "Cannot assign eDoctor Letter mail automatically", "assignManuallySuccess": "The email is assigned to {{patient}}", "tooltipValidSignature": "Valid signature", "tooltipUnknownSignature": "Unknown signature", "createScheinKVSuccess": "Schein is auto-created", "createScheinKVFailure": "Failed to auto-create schein", "noAddressFound": "No address found. Please contact <PERSON><PERSON> to set up the address book.", "PatientComparison": {"btnConfirmAssign": "I understand the inconsistency", "btnSync": "Sync data", "btnSkip": "<PERSON><PERSON>", "descriptionWarning": "The patient data in garrioPRO does not match the data from the received eDoctor Letter. Please select the data to sync for this patient.", "syncTitle": "Sync patient details: {{ name }}", "personalInfoHeader": "Personal Info", "addressInfoHeader": "Address Info", "insuranceInfoHeader": "Insurance Info", "postOfficeBoxHeader": "Post Office Box", "firstName": "First Name", "lastName": "Last Name", "title": "Title", "additionalNames": "Additional Name", "intendWord": "Intend Word", "gender": "Gender", "dOB": "Date of birth", "street": "Street", "number": "Number", "postCode": "Postal Code", "city": "City", "placeOfResidence": "City", "officeBox": "Post Office Box", "countryCode": "Country", "insuranceNumber": "Insurance Number", "insuranceCompanyName": "Insurance Company", "insuranceStatus": "Insurance Status", "warningContent": "Inconsistent data found", "warningDescription": "The patient data in garrioPRO does not match the data from the received eDoctor Letter.", "dateOfBirth": "Date of birth", "selectAll": "Select all", "ik": "IK: {{ikN<PERSON>ber}}", "eDoctorLetter": "eDoctor Letter"}}