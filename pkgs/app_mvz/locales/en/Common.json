{"weWillNotBeAbleToUndo": "This action cannot be undone.", "keyboardCommandNavigateBetweenSections": "Alt + ↑↓ to navigate within the form", "navigateBetweenSections": "to navigate within the form", "Table": {"rowsPerPage": "Rows per page:", "all": "All", "of": "of", "loading": "Loading…", "noRecords": "There is no record to display"}, "Input": {"search": "Search"}, "Filter": {"allFilters": "All filters", "reset": "Reset to default", "filter": "Filter"}, "Select": {"select": "Select", "noResults": "No result", "search": "Search…", "headerAdvanceSearch": "or select a search option below", "searchUsingCriteria": "search using criteria", "AtleastOneSearch": "At least one character search"}, "MultiSelect": {"selectAll": "Select all", "deselectAll": "Deselect all", "select": "Select", "placeholder": "Search…", "noResult": "No result", "itemsSelected": "{{ count }} selected", "all": "All"}, "DateTimePicker": {"DD_MM_YYYY": "DD.MM.YYYY", "DD_MM_YY": "DD.MM.YY", "dd_mm_yyyy": "dd.mm.yyyy", "DD_MM_YYYY_at_HH_MM": "DD.MM.YYYY at HH:MM", "Q_YY": "Q.YY", "q_yyyy": "q.yyyy", "MM_YY": "MM.YY", "fromTo": "From - To", "dateRangeInput": "DD.MM.YYYY - DD.MM.YYYY", "shortcuts": {"Today": "Today", "Yesterday": "Yesterday", "1 week ago": "1 week ago", "Past week": "Past week", "1 month ago": "1 month ago", "Past month": "Past month", "3 months ago": "Past 3 months", "Past 3 months": "Past 3 months", "Past 6 months": "Past 6 months", "1 year ago": "1 year ago", "Past year": "Past year", "Past 2 years": "Past 2 years", "Quarter": "Quarter {{quarter}}/{{year}}"}, "today": "Today", "clear": "Clear", "outOfRange": "Out of range", "invalidDate": "Invalid Date"}, "NavigationSuggest": {"listNavigation": "↑↓ to select. Enter to choose."}, "SuggestList": {"inputPlaceholder": "<PERSON><PERSON><PERSON>en Sie aus der Liste oder geben Si<PERSON> ein, um neue hinzuzufügen", "selectText": "Select"}, "FormValidation": {"fieldRequired": "This field is required", "errPostCodeInvalid": "Please enter a valid postal code", "inValidCode": "Enter 9 digits for this field", "codeExisted": "This number already exists", "inValidNumber": "This number is invalid", "valueMustbeMoreThanZero": "Value must be ≥ 0", "valueMustbeInRange": "Value must be between 0 and 100", "existValue": "This value already exists. Please enter a unique value.", "lengthExceed": "Maximum {{maxLength}} characters."}, "ButtonActions": {"cancelText": "Cancel", "saveText": "Save", "printText": "Print", "create": "Create", "viewAll": "View all", "addText": "Add", "removeText": "Remove", "continueText": "Continue", "yesRemove": "Yes, remove", "yesCancel": "Yes, cancel", "yesDelete": "Yes, delete", "yesUpdate": "Yes, update", "yesInform": "Yes, inform", "yesUndo": "Yes, undo", "yesReset": "Yes, reset", "yesContinue": "Yes, continue", "okText": "Ok", "close": "Close", "yes": "Yes", "no": "No", "reset": "Reset", "apply": "Apply", "updatedText": "Update", "back": "Back", "submit": "Submit", "resetDefault": "Reset to default", "send": "Send", "sendViaKim": "Send via KIM", "noReject": "No, reject", "yesAllow": "Yes, allow", "gotoPatientProfile": "Go to patient profile", "assignText": "Assign", "editText": "Edit", "deleteText": "Delete", "uploadText": "Upload", "openAdmin": "Open admin", "importAgain": "Import again"}, "LeavingModal": {"leavePageTitle": "Leave without saving?", "leavePageContent": "Unsaved changes will be discarded if you leave this page.", "cancelLeave": "No, stay here", "confirmLeave": "Yes, leave"}, "InputCreateValueComponent": {"create": "Create", "selectOrCreate": "Select an option or create one", "btnCancel": "Cancel", "btnOk": "Remove", "title": "Remove this value?", "message": "This action cannot be undone."}, "PrintPreviewDialog": {"title": "Print preview", "keyboardNavigationHint": "Alt + ↑↓ to navigate within the form", "printSettings": "Print settings", "printer": "Printer", "tray": "Tray", "cancelledDialog": {"title": "Printing cancelled", "description": "Print job was cancelled. You will be redirected to the print preview."}}, "UnitMedication": {"kg": "kg", "m": "m", "cm": "cm", "mmHg": "mmHg"}, "HintError": {"hintGenderMRejectM": "nur bei männliche Patienten", "hintGenderMRejectNotM": "überwiegend bei männliche Patienten", "hintGenderWRejectM": "nur bei  weibliche Patienten", "hintGenderWRejectNotM": "überwiegend bei  weibliche Patienten", "ageUnit": "Jahre", "hintMaxAgeFrom124MinAge1": "Ab {{age}} <PERSON><PERSON><PERSON>", "hintMaxAgeFrom124WithoutMinAge1": "Ab {{age}} <PERSON><PERSON><PERSON>", "hintMaxAgeTo124": "Altersgruppe zwischen {{ageMin}} Jahren und unter {{ageMax}} Jahren", "hintMaxAgeFrom124MinAge28": "Ab {{age}} <PERSON><PERSON>", "hintAge": "Bis {{age}} <PERSON><PERSON><PERSON>", "hintPermanentDiagnosis": "This code describes an acute, temporary condition and is not suitable for categorization as a “permanent diagnosis” in the further course of treatment."}, "Cancelled": "Cancelled", "Optional": "optional", "Create": "Create", "More": "More", "Edit": "Edit", "View": "View", "Remove": "Remove", "Saved": "Saved", "Printed": "Printed", "Finished": "Finished", "Warning": "Warning", "Error": "Error", "AddNewHashString": "Add new hash-string", "DateTimeFormatWithoutDot": "DDMMYYYY", "autoDoucumentFailed": "Failed to auto-document service code", "autoDocumentSuccess": "Service code {{code}} is automatically documented. ", "TextModuleDialog": {"OmimGFileUpdatedTitle": "OMIM-G file updated", "OmimGFileUpdatedHeader": "The OMIM-G file has been updated. Please review below OMIM-G chains:"}, "Layout": {"willExpire": "will expire within {{countDays}} day", "postpone": "Postpone", "dismiss": "Dismiss notice permanently"}, "AutoDocumentModal": {"titleSend": "Allow automatic documenting for sending eDoctor Letter?", "titleReceive": "Allow automatic documenting for receiving eDoctor Letter?", "descriptionSend": "Allow the system to automatically document service code {{code}} for each eDoctor Letter sent within the current quarter.", "descriptionReceive": "Allow the system to automatically document and bill service code {{code}} for each eDoctor Letter received within the current quarter.", "subDescription": "You can change settings for this in the Admin > Practice Settings > Billing"}, "ErrorMessages": {"invalidFormat": "{{fieldName}} has invalid format. Format should be: {{pattern}}.", "restrictLANRValue": "The (substitute) value \"{{fieldValue}}\" is obsolete and not permitted as field content."}, "Form": {"required": "{{name}} is required"}, "PrinterSetting": {"printerProfile": "Printer profile", "printerSettings": "Printer settings", "printer": "Printer", "tray": "Tray", "copies": "Copies", "paperSize": "Paper size", "printAsDuplex": "Print as duplex", "printAsBlancForm": "Print as blank form", "margin": "<PERSON><PERSON>", "units": "Units", "top": "Top", "left": "Left", "right": "Right", "bottom": "Bottom", "adjustWidth": "Adjust width", "adjustHeight": "Adjust height", "in": "in", "mm": "mm", "A4_portrait": "A4 - Portrait", "A4_landscape": "A4 - Landscape", "A5_portrait": "A5 - Portrait", "A5_landscape": "A5 - Landscape", "A6_portrait": "A6 - Portrait", "A6_landscape": "A6 - Landscape", "printerProfileRequired": "Printer profile is required", "printerRequired": "Printer is required", "trayRequired": "Tray is required", "copiesRequired": "Copies is required", "paperSizeRequired": "Paper size is required", "noPrinterProfileFound": "No printer profile found"}, "InputPinModal": {"title": "Input the PIN", "subTitle": "Please go to the card terminal where this card is inserted and enter the PIN to sign the form or remove the card to cancel the process."}, "InputPinFailedModal": {"title": "Failed to sign and send", "subTitle": "Signature failed. Please try again."}}