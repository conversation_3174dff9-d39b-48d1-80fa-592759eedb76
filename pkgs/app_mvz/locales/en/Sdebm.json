{"EbmDialog": {"titleCreate": "Create service code", "titleEdit": "Edit service code", "create": "Create", "save": "Update", "cancel": "Cancel", "statistiks": "Statistics", "errServiceCodeEmpty": "This field is required", "errShortDescriptionEmpty": "This field is required", "shortServiceCode": "Service code is too short", "outOfRangeErrorMessage": "Invalid date", "invalidDateErrorMessage": "Invalid time", "errorCreateUpdateEbm": {"EndDateOutOfDate": "End date is outdated"}, "validity": "Validity", "validityPlaceholder": "Validity", "billableFrom": "Billable from", "checkQuaterEbm": "Updates will only be saved for this quarter (Q{{quater}}/{{year}}). This will not affect service codes that are already billed or documented.", "code": "Service code", "description": "Short description", "longDescription": "Long description", "chapterAssignment": "Chapter assignment", "receiptDescription": "Receipt text", "notes": "Notes", "fromDate": "<PERSON>id from", "validTo": "valid to", "startBillingFrom": "Start billing from", "evaluation": "Evaluation", "pointValue": "Point value", "priceInEuro": "Price in €", "testTime": "Prüzeit in min", "testTimeText": "Prüzeit", "requiredTime": "zeitbedarf in min", "requiredTimeText": "zeitbedarf", "profileTime": "zeitprofit", "performanceGroup": "leistungsgruppe", "unit": "Unit", "duration": "Duration", "leavePageTitle": "Leave without saving?", "leavePageContent": "Unsaved changes will be discarded if you leave this page.", "confirmLeave": "Yes, leave", "cancelLeave": "No, stay here", "minutes": "minutes", "units": {"0": "-", "1": "Points", "2": "Euros", "5": "Unrated"}, "invalidCode": "Invalid format. Please enter 5 digits (nnnnn) or 5 digits with a letter (nnnnnA)", "ErrorCode_SDEBM_Service_Code_Existed": "Service code already exists"}, "MaterialCostDialog": {"createMaterialCostTitle": "Create material cost", "description": "Description", "price": "Price", "manufacturer": "Manufacturer", "productNumber": "Product Number", "confirmCreateMaterialCost": "Create material cost", "viewAll": "View all", "cancelCreate": "Cancel", "leaveCreateMaterialCostContent": "This material cost does not exist. Create a new material cost?", "invoiceNumber": "Invoice number"}, "MaterialCostOverview": {"Search": "Search", "title": "Material Costs", "descriptionCol": "Description name", "createSuccess": "Material cost created", "updateSuccess": "Material cost updated ", "deleteSuccess": "Material cost removed", "editMc": "Edit material cost", "removeMc": "Remove material cost", "priceCol": "Price", "productNumberCol": "product number", "manufacturerCol": "Manufacturer", "removeCostTitle": "Remove this material cost?", "cancelButtonCloseFormAlert": "No", "confirmButtonCloseFormAlert": "Yes, remove", "contentConfirmCloseAlert": "This action cannot be undone.", "invoiceNumberCol": "Invoice number"}, "AdditionalInfo": {"5002": "Type of treatment", "5003": "(N)BSNR of the mediated specialist doctor", "5005": "Multiplier", "5006": "Time", "5008": "DKM", "5009": "Justification", "5302": "LANR/BSNR of referral Doctor", "5010": "Charge number", "5011": "Material costs", "5012": "Material costs in cent", "5013": "Percent of the treatment", "5015": "Organ", "5016": "Doctor name", "5017": "Place of visit for home visits", "5018": "Zone of visits", "5019": "Location of treatment", "5020": "Repeat of treatment", "5021": "Year of the last cancer early detection treatment", "5023": "Go Number addition", "5024": "GNR- additional participation for post-inpatient services rendered", "5025": "Recording date", "5026": "Date of discharge", "5034": "OP Date", "5035": "OP Key", "5036": "GNR as Condition", "5038": "Complication", "5040": "Patient number (EDV) large sheet of FEK or eDocumentation of skin cancer screening", "5041": "Page localization OPS", "5042": "Quantity specification KM / AM", "5043": "Unit of measure KM / AM", "5070": "OMIM-G-Code of the investigated Gens", "5071": "OMIM-P-Code (Type of disease)", "5072": "Gen-Name", "5073": "Type of disease", "5074": "Name producer / supplier", "5075": "Article- /Modellnumber", "5098": "(N)BSNR of the place of service provision", "5099": "Lifelong doctor number of the contract doctor / contract psychotherapist", "5100": "ASV team number of the contract doctor", "5101": "Pseudo-LANR of the doctor (for hospital doctors as part of ASV billing)", "5102": "Hospital ID (as part of ASV billing)", "5200": "Pseudo-GNR", "5900": "Day separation", "5300": "Total Amount", "factor": "Factor", "quantity": "Quantity", "price": "Price", "5050": "Melde-ID Implantateregister", "5051": "Hash-String Implantateregister", "5052": "Hash-<PERSON><PERSON>ateregister", "5076": "Invoice number", "5077": "HGNC-Gensymbol", "5078": "Gen-Name", "5079": "Type of illness"}, "DefaultOmimGNote": "The value entered is not included in the catalog and may not be included in the billing file! Instead, the substitute value is taken over. Gen-Name must be entered.", "DefaultOmimPNote": "The value entered is not included in the catalog and may not be included in the billing file! Instead, the substitute value is taken over.", "SubstituteValue": "Substitute value", "OmimGChains": "OMIM-G CODE CHAIN", "OmimGCodes": "OMIM-G CODES", "HgncChains": "HGNC CODE CHAIN", "HgncCodes": "HGNC CODES", "chain": "Chain", "TagRecommanded": "Recommended", "OPS_laterity": {"left": "Left", "right": "Right", "both_sides": "Both sides"}, "DropDownItems": {"zone of visits": {"Z1": "Z1", "Z2": "Z2", "Z3": "Z3", "Z4": "Z4"}, "repet of treatment": {"1": "Yes", "0": "No"}, "ops": {"R": "Right", "L": "Left", "B": "Both sides"}, "unit of measure km / am": {"1": "1", "2": "2", "3": "3"}}, "SdebmOverview": {"Search": "Search", "viewEbm": "View details", "editEbm": "Edit service code", "removeEbm": "Remove service code", "title": "EBM Service Codes", "codeCol": "service code", "createSuccessEbm": "Service code created", "deleteSuccessEbm": "Service code removed", "updateSuccessEbm": "Service code updated", "descriptionCol": "Short description", "validFromCol": "<PERSON>id from", "validToCol": "Valid to", "priceCol": "Price", "pointsCol": "Points", "removeEbmTitle": "Remove service code?", "cancelButtonCloseFormAlert": "No", "confirmButtonCloseFormAlert": "Yes, remove", "contentConfirmCloseAlert": "Service code will be removed permanently for this quarter (Q{{quater}}/{{year}}). This action cannot be undone.", "contentConfirmCloseAlertVersion2": "Service code will be removed permanently for this catalogue. This action cannot be undone.", "ErrorCode_SDEBM_Cannot_Deleted": "This service code already exists in a quarter.", "updated": "Updated", "selfCreated": "Self-created", "onlyShowSelfCreatedServiceCodes": "Only show self-created service codes"}, "GoaOverview": {"title": "GOÄ Service Codes", "numberCol": "Number", "descriptionCol": "Description", "pointsCol": "Points", "priceCol": "Price", "pointValue": "Point value: 1 point = {{value}} Cent", "editGoa": "Edit", "removeGoa": "Remove", "updated": "Updated", "selfCreated": "Self-created", "noResultFoundTitle": "No results found", "createGoaService": "Create service code", "CreateDialog": {"titleEdit": "Edit service code", "titleCreate": "Create service code", "serviceCode": "Service code", "remark": "Remark", "goaNumberRequired": "Service code is required", "chapter": "Chapter", "description": "Description", "descriptionRequired": "Description is required", "validFrom": "valid From", "validTo": "<PERSON><PERSON>", "evaluation": "Evaluation", "serviceFactor": "Service Factor", "unit": "Unit", "Unit_Euros": "Euros", "Unit_Points": "Points", "maximumEvaluation": "<p>Maximum value: <strong>{{maxEvaluation}}</strong></p>", "rules": "Rules", "excludedCodes": "Excluded codes:", "selectExcludedCodes": "Select excluded codes", "SelectExcludedCodesDialog": {"title": "Select excluded service codes", "selectedCodes": "Selected codes:", "searchPlaceHolder": "Search", "cancelBtn": "Cancel", "addBtn": "Add selected codes", "number": "Service code", "description": "Description"}, "createSuccess": "Service code created.", "createFailed": "Failed to create service code", "deleteSuccess": "Service code removed", "deleteFailed": "Failed to remove service code", "updateSuccess": "Service code updated", "updateFailed": "Failed to update service code", "outOfRangeErrorMessage": "Invalid date", "invalidDateErrorMessage": "Invalid time"}}, "UVGoaOverview": {"title": "UV-GOÄ Service Codes", "numberCol": "Number", "shortDescriptionCol": "Short Description", "descriptionCol": "Description", "validFromCol": "<PERSON>id from", "generalTreatmentCol": "General Treatment", "specificTreatmentCol": "Specific Treatment", "viewGoa": "View", "editGoa": "Edit", "removeGoa": "Remove", "updated": "Updated", "selfCreated": "Self-created", "noResultFoundTitle": "No results found", "createGoaService": "Create service code", "CreateDialog": {"titleEdit": "Edit service code", "titleCreate": "Create service code", "notBillable": "Not billable", "number": "GNR", "serviceCode": "Service code", "numberRequired": "Service code is required", "shortDescription": "Short description", "shortDescriptionRequired": "Short description is required", "description": "Long description", "descriptionRequired": "Long description is required", "validFrom": "valid From", "validFromRequired": "Valid from is required", "validTo": "<PERSON><PERSON>", "evaluation": "Evalution (in Euro)", "unit": "€", "placeHolder": "0,00", "generalTreatment": "General treatment", "specificTreatment": "Specific treatment", "generalCosts": "General costs", "specialCosts": "Special costs", "outOfRangeErrorMessage": "Sie geben ein Datum außerhalb des zulässigen Bereichs ein", "invalidDateErrorMessage": "Ungültiger Zeitraum"}}}