{"ContactInformation": {"contactInfo": "Contact Info", "primaryContactNumber": "Primary contact number", "furtherContactNumber": "Further Contact Number", "formatPhoneNumberEg": "e.g. 0151 12345678", "emailAddress": "E-mail Address", "contactPerson": "Contact Person", "name": "Name", "relationship": "Relationship", "phoneNumber": "Phone Number", "add": "Add", "removeContactInfo": "Remove this Contact Info?", "removeContactPerson": "Remove this Contact Person?", "printNewTemplate": "Print", "noTemeplate": "No template found.", "setupTemplate": "Please go to Admin > Documents > Template Overview to manage all templates", "fileUploaded": "File uploaded", "NoFileUploaded": "No file uploaded"}, "CreatePatient": {"save": "Save", "create": "Create", "cancel": "Cancel", "createPatientProfile": "Create Patient Profile", "editPatientDetails": "<PERSON> <PERSON>ient <PERSON>", "errFirstNameEmpty": "First name is required", "errLastNameEmpty": "Last name is required", "errDob": "Date of birth is required", "invalidDob": "Date of birth is invalid or out of range", "invalidDobWithDod": "The date of death must be after the date of birth.", "errGender": "Gender is required", "errStreet": "Street is required", "errHouseNumber": "House number is required", "errPostCode": "Postal code must be 5 digits", "errCity": "City/State is required", "errCountryCode": "Country is required", "errInsuranceNumberPublicCostUnit": "Invalid format. Please enter 6 to 12 digits, or 1 letter + 9 digits (e.g. A123456789)", "errInsuranceNumberOtherCostUnits": "Please enter a valid insurance number in the format 6-12 numbers", "errInsuranceStatus": "Insurance status is required", "warningInsuranceNumber": "The {{insuranceNumber}} is a dummy patient insurance number which is used for testing of ePrescription only.", "errInsuranceCompany": "Insurance company is required", "errInsuranceCompanyMustSelect": "Please select an option from the list", "errDmp": "DMP is required", "errGroup": "Special group is required", "errPhoneNumber": "Should be from 2 to 22 digits", "errWorkingHourInWeek": "Should be greater than 0 and less than 100", "errPrimaryContactNumber": "Primary Contact Number is required", "errInsuranceNumberExisted": "There is already a Patient with the same insurance number", "errIkNumber": "IK Number is required", "errPostCodeRequired": "At least 1 postal code is required", "errFirstRequired": "First ICD code is required", "errDiagnosisGroupRequired": "Diagnosis group is required", "errPostCodeInvalid": "Please enter a valid postal code", "errStreetRequired": "Street is required", "invalidPhoneNumber": "Invalid format. Please try again.", "errCityRequired": "City is required", "errNumberRequired": "Number is required", "errInsuranceStartDate": "Insurance Start Date is required", "errMissingEndDate": "Missing End Date", "errInvalidDateRange": "Start date must be before end date", "errStartDateAfterBirthdate": "Insurance start date must be after the date of birth.", "errInvalidEndDate": "There is already Cost Unit with the same validation date", "errMultiActiveCostUnits": "It is not allowed to have multiple active Cost Units", "errCostUnitTerminated": "The cost unit is terminated", "sdplzInvalid": "Postal code is not found in current KBV PLZ.", "lhm": {"atLeast3PrimaryRemedies": "At least 3 primary remedies must be selected", "atLeast3SupplementaryRemedies": "At least 3 supplementary remedies must be selected", "atLeast3Remedies": "At least 3 remedies (primary or supplementary) must be selected", "atLeast1Remedy": "At least 1 remedy (primary or supplementary) must be selected"}, "errAtleast1FieldRequired": "At least 1 field must be filled out", "leavePageTitle": "Leave without saving?", "leavePageContent": "Unsaved changes will be discarded if you leave this page.", "confirmLeave": "Yes, leave", "cancelLeave": "No, stay here", "menuGenericInfo": "Generic info", "menuPersonalInfo": "Personal info", "menuAddressInfo": "Address info", "menuContactInfo": "Contact info", "menuInsuranceInfo": "Insurance info", "menuG81EHIC": "European health insurance", "menuDoctorInfo": "Doctor info", "menuOtherInfo": "Other info", "menuVisitInfo": "Visit info", "menuMedicalInfo": "Medical data", "menuEmploymentInfo": "Employment info", "menuPatientDeletion": "Patient Deletion", "evInfo": "Please note that all data can be documented and should be documented if available", "cardReadinStatus": {"CardReadin": "Eingelesen", "ManualEntry": "Ersatzverfahren", "NotReadin": "Not Read-in", "OnlineCheckFail": "Online Check Fail", "MobileCardReading": "mobile card reading on"}, "errorCreateUpdatePatient": {"DuplicatedInsuranceNumber": "Duplicated insurance number", "MultipleActiveCostUnits": "Multiple active cost units", "EndDateOutOfDate": "End date is outdated", "LHMUnique": "Multiple LHM Selectors", "CostUnitIsNotAvailableInKvRegion": "Cost unit is not available in your KV region", "CostUnitHasExpired": "Cost unit has expired", "OneCostUnitMustBeActive": "1 active cost unit must be required.", "DuplicateLHMItem": "Duplicate LHM item"}, "keyboardCommandNavigateBetweenSections": "Alt + ↑↓ to navigate within the form", "insuranceCompanyName": "Insurance name", "ikNumber": "IK Number", "errInsuranceNumberRequired": "Insurance number is required", "errInsuranceCompanyId": "Should be 4 digits", "errInsuranceValidUntil": "Should be a date string with the format MM.YYYY", "titleColonoscopy": "Informed patient about preventive colonoscopy information?", "contentColonoscopy": "Preventive colonoscopy from the age of 50 as part of the AOK/Bosch BKK specialist program. Gastroenterologist participants at", "entryInformed": "Vorsorgekoloskopie im Rahmen des AOK-/Bosch BKK-Facharztprogramm - Patient informed", "entryNotInformed": "Vorsorgekoloskopie im Rahmen des AOK-/Bosch BKK-Facharztprogramm - Patient NOT informed"}, "EmploymentInformation": {"title": "Employment info", "isEmployed": "The patient is currently employed", "jobStatus": "Job status", "workingHourInWeek": "Weekly working hours", "workActivity1": "predominantly physical or mental", "workActivity2": "predominantly standing or sitting", "workActivityPhysical": "Physical", "workActivityMental": "Mental", "workActivityStanding": "Standing", "workActivitySitting": "Sitting", "specialProblemAtWork": "Special problems at work", "occupation": "Occupation", "activityProfile": "Activity Profile", "employer": "Employer", "street": "Street", "houseNumber": "Number", "postalCode": "Postal Code", "city": "City", "country": "Country", "employmentOutOfDate": "Employment info is outdated", "employmentOutOfDateContentPleaseUpdate": "Please update the employment info", "employmentInfo": "Employment info", "companyInfo": "Company info", "lastUpdatedDate": "Last updated date", "JobStatus_Employees": "Angestellte", "JobStatus_Executives": "Leitende Angestellte", "JobStatus_ManagingDirector": "Geschäftsfühhrter", "JobStatus_CivilServant": "Zivilbediensteter/Diener", "JobStatus_JobSeeker": "Arbeitssuchend"}, "InsuranceInformation": {"ErrorCode_ValidationError_NotActive_InsuranceInfo": "At least 1 active insurance is required for a public patient", "DuplicatedInsuranceNumber": "Duplicated insurance number", "insuranceInformation": "Insurance information", "insuranceNumber": "Insurance number", "errUniqueInsuranceNumber": "exists. Enter another one or", "errUniqueInsuranceNumberLink": "view patient profile.", "insuranceStatus": "Insurance status", "selectInsuranceStatus": "Select insurance status", "insuranceCompany": "Insurance company", "searchInsuranceCompany": "Search insurance company", "validateOfInsuranceCard": "Expiry date of insurance card", "optional": "optional", "invalidDateErrorMessage": "Invalid time", "outOfRangeErrorMessage": "Invalid date", "dmp": "DMP Labeling", "selectDmp": "Select DMP", "specialGroup": "Special group", "selectGroup": "Select special group", "noInsuranceCompanyFound": "No insurance name found", "noIkFound": "No IK number found", "SpecialGroup_00": "Nicht vorhanden", "SpecialGroup_04": "BSHG (Bundessozialhilfegesetz) § 264 SGB V", "SpecialGroup_06": "BVG (Gesetz über die Versorgung der Opfer des Krieges)", "SpecialGroup_07": "SVA-Kennzeichnung für zwischenstaatliches Krankenversicherungsrecht: - Personen mit Wohnsitz im Inland, Abrechnung nach Aufwand", "SpecialGroup_08": "SVA-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, paus<PERSON>", "SpecialGroup_09": "Em<PERSON><PERSON><PERSON> von Gesundheitsleistungen nach den §§ 4 und 6 des Asylbewerberleistungsgesetzes (AsylbLG)", "warningEditIkNumber": "WARNING: Changing to another insurance company may terminate or cancel the contract with {{insuranceCompanyName}}. This action cannot be undone.", "insuranceIkNumber": "IK Number", "insuranceStartDate": "Insurance Start Date", "insuranceEndDate": "Insurance End Date", "coPaymentExemptionTill": "Co-payment exemption until", "validTill": "<PERSON>id <PERSON>", "patientReceipt": "Patient bill", "patientReceiptDescription": "The patient bill is a list of all services and costs in a clear, patient-friendly form. This allows the patient to understand which services were provided at what cost in your practice.", "heimiLongTermApproval": "HEIMI long-term approval (LHM)", "selectIkNumbers": "Select IK Numbers", "addInsurance": "Add insurance", "cancel": "Cancel", "remove": "Remove", "removeThisInsuranceInfo": "Remove this insurance info?", "weWillNotBeAbleToUndo": "This action cannot be undone.", "1stICDCode": "First ICD code", "2ndICDCode": "Second ICD code", "diagnosisCode": "Diagnosis Group", "standardCombination": "Standard combination", "primaryRemedies": "Primary remedies", "complementaryRemedies": "Supplementary remedies", "validUntil": "Valid until", "note": "Note", "addLHM": "Add LHM", "removeLHMDialogTitle": "Remove this LHM?", "select": "Select", "createInsurance": "Create insurance", "addIKToExistingInsurance": "Add IK to existing insurance", "errInvalidEndDate": "There is already Cost Unit with the same validation date", "Error": {"CostUnitHasExpired": "Cost unit has expired. This may result in billing errors.", "IKNumberHasExpired": "IK number has expired.", "CostUnitIsNotAvailableInKvRegion": "Cost unit is not available in your KV region", "CostUnitIsTerminated": "Cost unit is terminated"}, "insuranceCategories": {"SearchType_IkNumbers": "IK number", "SearchType_LocationName": "Location name", "SearchType_SearchName": "Search name", "SearchType_VKNR": "VKNR", "SearchType_CostUnitName": "Cost unit name", "SearchType_BLANK": "Search"}, "addIkToCostUnit": "Add IK to existing cost unit", "or": "or", "insuranceType": "Insurance Type", "publicLb": "Public", "privateLb": "Private", "bgLb": "BG", "healInsuranceId": "Health Insurance ID", "NotFoundInsurance": "At least 1 active insurance is required for a public patient", "InvalidInsuranceDate": "Invalid insurance date range", "NoInsuranceYetDescription": "\"Add insurance\" to start filling out insurance information", "ErrorCode_Cannot_Delete_Schein": "Unable to delete the insurance associated with an active schein", "NotFoundActiveInsurance": "An active insurance is required for a public patient", "updatedSuccessful": "Insurance information updated", "InsuranceTitle": "Insurance", "wop": "WOP-Kennzeichen/ KV-Region", "markAsActive": "<PERSON> as active", "markedAsActive": "Marked as active", "ErrorCode_Empty_InsuranceInfo": "At least 1 active insurance is required for a public patient", "ErrorCode_CardReader_Cannot_Delete_Insurance": "Cannot delete insurance read by card in this quarter", "ErrorCode_Cannot_Delete_Activated_Insurance": "This insurance cannot be deleted. There must be at least one insurance."}, "G81EHICInformation": {"content": "EHIC (European Health Insurance Card) approval", "description": "The Patient’s Declaration European Health Insurance form with the patient’s signature is required. Please complete the form legibly and in full.", "prescribeBtn": "Prescribe", "prescribedBtn": "Prescribed", "represcribeBtn": "Re-prescribe", "printAll": "Print All"}, "GenericInformation": {"title": "Generic Info", "typeOfPatient": "Type Of Insurance", "publicLb": "Public", "privateLb": "Private", "patientIdLb": "Patient ID", "cardReadInStatusLb": "Card read-in status", "lastReadingDayOfCardLb": "Last reading day of card"}, "PersonalInformation": {"optional": "optional", "selectAnOption": "Select an option", "title": "Title", "lastName": "Last name", "firstName": "First name", "intendWord": "Intend word", "additionalName": "Additional name", "dateOfBirth": "Date of Birth", "dateOfDeath": "Date of Death", "outOfRangeErrorMessage": "Invalid date", "gender": "Gender", "genderM": "M", "genderW": "W", "genderU": "U", "genderX": "X", "genderD": "D"}, "OtherInformation": {"cave": "CAVE", "medicalHistoryComplete": "Medical history form completed", "livingWillAvailable": "Living will available", "agreeBilling": "Agree with billing via PVS", "privacyPolicySigned": "Privacy Policy Signed", "printNewTemplate": "Print", "noTemeplate": "No template found.", "setupTemplate": "Please go to Admin > Documents > Template Overview to manage all templates", "fileUploaded": "File uploaded", "NoFileUploaded": "No file uploaded", "declarationOfAgreement": "Declaration of agreement to contact"}, "VisitInformation": {"treatmentDoctor": "Treatment doctor (current practice)", "additionalVisitInfo": "Additional Visit Info", "select": "Select"}, "DoctorInformation": {"lanr": "LANR", "bsnr": "BSNR", "phoneNumber": "PHONE NUMBER", "email": "E-MAIL ADDRESS", "areaOfExpertise": "Area of expertise", "areaOfExpertisePlaceholder": "Select expertise", "generalPractitioner": "General Practitioner", "specialist": "Specialist", "treatmentDoctor": "Treatment Doctor", "addDoctor": "Add", "selectDoctor": "Select a doctor", "confirmDialog": {"btnRemove": "Remove", "btnCancel": "Cancel", "title": "Remove this doctor?", "message": "This action cannot be undone."}, "select": "Select"}, "AddressInformation": {"houseNumber": "Number", "city": "City", "country": "Country", "postalCode": "Postal code", "street": "Street", "distance": "Distance", "additionalAddressInfo": "ADDITIONAL ADDRESS INFO", "km": "km", "address": "Address", "billingAddress": "Billing address", "sdplzInvalid": "Postal code is not found in current KBV PLZ.", "postOffice": "Post Office", "placeOfResidence": "Place of residence", "officeBox": "Post Office Box"}, "Medical": {"medicalData": "Medical data", "editMedicalData": "Edit medical record details", "addMedicalData": "Add medical data record", "weight": "weight", "height": "Height", "bmi": "BMI", "bloodPressure": "Blood pressure", "heartFrequency": "Heart frequency", "allergy": "Allergy", "creatinine": "Creatinine", "pregnant": "Pregnant", "breastfeeding": "Breastfeeding", "patientIsPregnant": "Patient is pregnant", "patientIsBreastfeeding": "Patient is breastfeeding", "patientIsSmoker": "Smoker", "cigarettesPerDay": "Cigarettes per day", "note": "Note", "gender": "Gender", "dateOfPlannedBirth": "Date of planned birth", "amountOfBirths": "Amount of births", "amountOfPregnancies": "Amount of pregnancies", "careLevel": "Care level", "amountOfChildren": "Amount of children", "noMedicalData": "No medical data record", "lastUpdatedTime": "Last updated time: {{date}}", "viewAllRecords": "View all records", "allergies": "Allergies", "addAllergy": "Add allergy", "editAllergy": "Edit allergies", "isPrescriptionRelated": "Mark as prescription related", "allergyPlaceholder": "Select or enter allergy name", "save": "Save", "cancel": "Cancel", "amountOfBirth": "Amount of birth", "validate": {"generic": "Invalid {{field}}", "pulseOxiMetricMaximum": "Pulse oximetric oxygen saturation cannot be more than 100%"}, "AllergiesInline": {"addAllergies": "Add allergies", "addAllergy": "Add allergy", "markAsPrescriptionRelated": "Mark as prescription related", "markAsPrescriptionRelatedTooltip": "If marked as prescription related, this allergy will appear at CAVE in sidebar info."}, "allergyComponent": {"create": "Create", "selectOrCreate": "Select an option or create one", "btnCancel": "Cancel", "btnOk": "Remove", "title": "Remove this allergy value?", "message": "This action cannot be undone."}, "allergyDialog": {"btnCancel": "Cancel", "btnOk": "Remove", "title": "Remove this allergy?", "message": "This action cannot be undone."}, "medicalDataRecord": "Medical data records", "addMedicalRecord": "Add medical record", "cm": "cm", "kg": "kg", "bpm": "bpm", "mmHg": "mmHg", "mgdl": "mg/dl", "editMedicalRecordToolTip": "Edit medical record details", "editAllergiesTooltip": "Edit allergies", "noResults": "No results.", "medicalRecordIsUpdated": "Medical record is updated", "createdOn": "Created on", "lastEditedOn": "Last edited on", "lastEditedBy": "Last edited by", "createdBy": "Created by", "allergiesTooltip": "Allergies", "patientHasNoAllergies": "Patient has no known allergies", "bodyTemperature": "Body temperature", "pulseOxiMetricCol": "O2 saturation", "pulseOxiMetric": "Pulse oximetric oxygen saturation (O2 saturation)", "pulse": "Pulse", "Pulse_Rhythmic": "Rhythmic", "Pulse_Irregular": "Irregular"}, "PatientDeletion": {"WarningDeletePatient": "Deleting this patient will permanently delete all associated patient information and records from the system. This action cannot be undone.", "ProceedDeletePatient": "If you still wish to proceed, please make sure the following are fulfilled:", "NoActiveSchein": "No active schein is associated with this patient.", "QuarterEnded": "The current quarter has ended.", "DeletePatientBtn": "Delete patient", "WarningDeletionDialog": {"DeletePatientConfirm": "Delete patient?", "PermanentlyDelete": "This will permanently delete all associated patient information and records from the system. This action cannot be undone.", "ToConfirm": "TO CONFIRM DELETION, TYPE “{{textConfirm}}” IN UPPERCASE BELOW", "No": "No", "Yes": "Yes, delete", "TextConfirm": "PATIENT LÖSCHEN", "PatientDeleted": "Patient deleted"}, "RestrictDeletionDialog": {"CanNotDeletePatient": "Patient cannot be deleted", "DeletingReasons": "Deleting this patient is not possible for the following reasons:", "AssociatedSchein": "There is at least 1 active schein associated with this patient.", "PatientCard": "The patient’s card has already been read in the current quarter. ", "ToDeletePatient": "To delete this patient, the following must be fulfilled:", "NoSchein": "No active schein should be associated with this patient.", "EndOfQuarter": "Please wait until the end of the current quarter.", "Close": "Close"}}}