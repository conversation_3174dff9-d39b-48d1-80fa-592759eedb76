{"CostUnitDialog": {"titleCreate": "Create cost unit", "titleEdit": "Edit cost unit", "addIkNumber": "Add IK", "addKtab": "Add KTAB", "cancel": "Cancel", "create": "Create", "edit": "Edit", "update": "Update", "select": "Select", "leavePageTitle": "Leave without saving?", "leavePageContent": "Unsaved changes will be discarded if you leave this page.", "confirmLeave": "Yes, leave", "cancelLeave": "No, stay here", "createSuccess": "Cost unit created", "createFailed": "Failed to create cost unit", "editSuccess": "Cost unit updated", "editFailed": "Failed to update cost unit", "validateSearchNameRequired": "Search name is required", "validateLocaltionNameRequired": "Location name is required", "validateInsuranceNameRequired": "Insurance name is required", "validatePostNumberAndStreet": "At least 1 of 'Street' or 'Postbox number' is required", "validateVKNRInvalid": "Invalid VKNR. There must be 5 characters.", "validateVKNRRequired": "VKNR is required", "validateVKNRDuplicated": "VKNR already exists", "validateIKNumberInvalid": "Invalid IK Number. There must be 9 characters.", "validateIkNumberDuplicated": "IK Number is duplicated", "validateKTabDuplicated": "KTAB is duplicated", "validateIkNumberRequired": "IK Number is required", "validateIkNumberExisted": "IK Number already exists", "validateAddressStreetLength": "Street field must not exceed 46 characters", "validateAddressNumberLength": "Address number field must not exceed 9 characters", "validateAddressPostalCodeLength": "Postal code field must not exceed 10 characters", "validateAddressCityLength": "City field must not exceed 40 characters", "validateKtabRequired": "KTAB printing name is required", "validateKtabPrintingNameLength": "Printing name must be less than 24 characters", "formFields": {"insuranceName": "Search name", "locationNames": "Location Names", "addLocationNames": "Add Location Names", "address": "Address", "street": "Street", "number": "Number", "postalCode": "Postal code", "city": "City", "postboxNumber": "Postbox number", "billingInfo": "Billing information", "feeCatalogue": "Fee catalogue", "vknr": "VKNR", "ikNumber": "IK Number", "ktab": "KTAB", "printingName": "Printing name", "ktabInfo": "KTAB"}, "invalidBillableQuarterYear": "Invalid last billable quarter", "invalidTerminationDate": "Invalid termination date", "terminateSuccess": "Cost unit terminated", "terminateFailed": "Cannot terminate cost unit", "insuranceCompanyNameRequired": "Must choose an insurance company"}, "CostUnitMenuItem": {"expired": "Expired", "selfCreated": "Self-created", "iK": "IK"}, "statutoryHealthInsuranceLabel": "Statutory Health Insurance", "selfCreatedType": "Self-created", "statusTerminated": "Terminated", "terminate": "Terminate", "searchByName": "Search by name, VKNR, IK number", "insuranceName": "Insurance name", "vknr": "VKNR", "ikNumber": "IK number", "validFrom": "<PERSON><PERSON><PERSON><PERSON> ab", "validTo": "Gültig bis", "detailSearchName": "Search name", "detailAddress": "Address", "detailPostmailAddress": "Postmail address", "detailSearchLocations": "Search locations", "feeCatalogue": "Fee catalogue", "costUnitGroup": "Cost unit group", "ktab": "KTAB", "costUnitDetails": "Cost unit details", "billingInfo": "Billing info", "validityPeriod": "Validity period", "acquiredCostUnit": "Acquired cost unit", "editCostUnit": "Edit cost unit", "insuranceCompanyName": "Insurance name", "add": "Add", "addIkToCostUnit": "Add IK to existing cost unit", "terminationDate": "Termination date", "lastBillableQuarter": "Last billable quarter", "terminateCostUnit": "Terminate Insurance?", "terminateCostUnitDesc": "Insurance will be removed permanently. This action cannot be undone.", "yesTerminate": "Yes, terminate", "validity": "Validity: {{validity}}", "version": "Version", "totalCostUnitNumber": "Total: {{total}} cost units", "titleBgInsurance": "BG Insurance", "toDate": "Valid to", "fromDate": "<PERSON>id from", "description": "Description (name 2)", "search": "Search", "ViewInsurance": {"detail": "Cost unit details", "addressInfo": "Address info", "fax": "Fax", "telephone": "Telephone", "postmailAddress": "Postmail address", "address": "Address", "validFrom": "<PERSON>id from", "validUntil": "Valid until", "ikNumber": "IK Number", "name": "name", "description": "Description (name 2)"}}