{"caveHeader": "Cave", "printSettingsHeader": "Print Settings", "printerProfileLabel": "Printer Profile", "printOptionsLabel": "Print Options", "printImmediately": "Print immediately", "sendToPrintQueue": "Send to print queue", "formPrinted": "Form printed", "noInfo": "No info", "preview": "Preview", "Forms": {"Muster_1": "Sick leave", "Muster_2B": "Prescription of hospital care", "Muster_4": "Prescription of a sick person's transport", "Muster_6": "Letter of referral", "CoverLetterForm": "Cover letter form", "Muster_7": "Referral for checking the somatic symptoms before requesting Psychotherapy", "Muster_8": "Prescription of visual aids", "Muster_8A": "Prescription of increasing visual aids", "Muster_11": "Report for the medical institution of insurance company", "Muster_13": "HEIMI prescription", "Muster_15": "Prescription of hearing aids", "Muster_16": "Red form- Prescription of medicine", "Muster_26A": "Sociotherapy prescription according to § 37a SGB V", "Muster_26B": "Verordnung Soziotherapie gem. § 37a SGB V", "Muster_26C": "Verordnung Soziotherapie gem. § 37a SGB V", "Muster_39A": "Early detection of cervical cancer", "Muster_39B": "Early detection of cervical cancer", "Muster_22A": "Consultation report before starting psychotherapy", "Muster_50": "Request for responsibility of another health insurance", "Muster_51": "Request for the responsibility of another cost unit", "Muster_52": "Report for the health insurance company if the inability to work persists", "Muster_52_0_V2": "Report for the health insurance company if the inability to work persists", "Muster_52_2": "Report for the health insurance company if the inability to work persists", "Muster_55": "Certificate of a serious chronic illness", "Muster_3A": "Muster 3", "Muster_5": "Billing form", "Muster_9": "Certificate of premature birth or child disability", "Muster_10": "Referral form for in-vitro diagnostic order lines", "Muster_10C": "Order for SARS-CoV-2 testing", "Muster_10A": "Anforderungsschein für Laboratoriumsuntersuchungen bei Laborgemeinschaften", "Muster_12A": "Prescription of home nursing care", "Muster_27A": "Sociotherapeutic care plan according to § 37a SGB V", "Muster_27B": "Sociotherapeutic care plan according to § 37a SGB V", "Muster_27C": "Sociotherapeutic care plan according to § 37a SGB V", "Muster_28A": "Regulation sociotherapy", "Gruenes_Rezept": "Green form", "T-Rezept-Muster": "T form", "PatientHeader": "<PERSON><PERSON>", "Muster_19A": "Emergency/Representation card", "Muster_19B": "Emergency/Representation card", "Muster_19C": "Emergency/Representation card", "Muster_21": "Medical certificate for the receipt of sickness benefit in case of illness of a child", "Muster_36_E_2017_07": "Prevention recommendation", "Muster_20A": "re-integration plan", "Muster_20B": "re-integration plan", "Muster_20C": "re-integration plan", "Muster_20D": "re-integration plan", "Muster_61": "Forms for prescribing medical rehabilitation", "Muster_65A": "Medical certificate child", "Muster_64": "Prescribing medical care for mothers or fathers", "Muster_64B": "Prescribing medical care for mothers or fathers", "Muster_70": "Treatment plan", "Muster_70A": "Follow-up treatment plan", "Muster_63B": "Prescription of specialized outpatient palliative care", "Muster_63C": "Prescription of specialized outpatient palliative care", "Muster_63D": "Prescription of specialized outpatient palliative care", "Muster_56": "Application for reimbursement of rehabilitation sports/functional training", "Muster_N63A": "Prescription of specialized outpatient palliative care", "BKK_BOSCH_VAG_BW_Praeventionsverordnung": "Präventionsverordnung (BKK_BOSCH_VAG_BW)", "BKK_BY_HZVNotify": "For billing positions of patient support the signature of patient is required. Please forward one example to patient and store the signed document in your practice.", "Muster_PTV_1A": "Application for psychotherapy", "Muster_PTV_1B": "Application for psychotherapy", "Muster_PTV_1C": "Application for psychotherapy", "Muster_PTV_11A": "Individuelle Information Zur Psychotherapeutischen Sprechstunde", "Muster_PTV_2A": "Details Therapist", "Muster_PTV_3": "Leitfaden zum erstellen des Berichts an die Gutachter*in", "Muster_PTV_10": "Ambulante Psychotherapie in der gesetzlichen Krankenversicherung", "Muster_PTV_12A": "Report Of Acute Treatment", "G81_EHIC_Bulgarisch": "G81_EHIC_Bulgarisch", "G81_EHIC_Danisch": "G81_E<PERSON><PERSON>_<PERSON>", "G81_EHIC_Englisch": "G81_EHIC_Englisch", "G81_EHIC_Franzosisch": "G81_EHIC_<PERSON>", "G81_EHIC_Griechisch": "G81_EHIC_Griechisch", "G81_EHIC_Italienisch": "G81_EHIC_Italienisch", "G81_EHIC_Kroatisch": "G81_EHIC_Kroatisch", "G81_EHIC_Niederlandisch": "G81_EHIC_Niederlandisch", "G81_EHIC_Polnisch": "G81_EHIC_Polnisch", "G81_EHIC_Rumanisch": "G81_EHIC_Rumanisch", "G81_EHIC_Spanisch": "G81_EHIC_Spanisch", "G81_EHIC_Tschechisch": "G81_EHIC_Tschechisch", "G81_EHIC_Ungarisch": "G81_EHIC_Ungarisch", "G81_EHIC_Finnisch": "G81_EHIC_<PERSON>isch", "G81_EHIC_Estnisch": "G81_EHIC_Estnisch", "G81_EHIC_Slowenisch": "G81_EHIC_Slowenisch", "G81_EHIC_Slowakisch": "G81_EHIC_Slow<PERSON>sch", "G81_EHIC_Schwedisch": "G81_EHIC_Schwedisch", "G81_EHIC_Portugiesisch": "G81_EHIC_Portugiesisch", "G81_EHIC_Litauisch": "G81_EHIC_Litauisch", "G81_EHIC_Lettisch": "G81_EHIC_Lettisch", "G81_EHIC_All": "G81_EHIC_All", "LKK-Handover-Letter": "Practice handover letter", "F1000": "Durchgangsarztbericht", "F1050": "Ärztliche Unfallmeldung", "F2100": "Zwischenbericht", "F9990": "<PERSON><PERSON><PERSON><PERSON>", "BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6": "BKK BOSCH BW Schnellinfo Patientenbegleitung"}, "FormOverview": {"formName": "File name", "formId": "Description", "FormAction_PrintFull": "Open", "FormAction_PrintHeader": "Print Patient header", "FormAction_PrintWithoutContent": "Print Blanco", "FormAction_PrintWithBSNRAndLANR": "Print with BSNR and LANR", "FormAction_OpenNewTab": "Open", "print": "Print", "form": "Form", "svDocument": "SV Document", "showSVDocumentsOnly": "Only show documents"}, "ToggleSearchType": {"HIMICatalogue": "HIMI catalogue", "Hierarchy": "Hierarchy", "FreeText": "Freetext"}, "SearchProductViewResult": {"ProductNumber": "Product Number", "Description": "Description", "Manufacturer": "Manufacturer"}, "SearchFreetext": {"PlaceHolder": "enter product type, product number,..."}, "SearchHierachy": {"ProductGroup": "PRODUCT GROUP", "ORT": "PLACE OF APPLICATION", "Subgroup": "SUB-GROUP", "NoResult": "No results."}, "SearchProductTypeViewResult": {"NoResult": "No results found", "ShowSingleProductList": "Show single product list"}, "ProductList": {"Reason": "Reason", "Prescribe": "Prescribe", "UseSelectedForm": "Use selected form", "SingleProductList": "Produktliste zur ausgewählten Produktart", "ProductNotFound": "No results.", "ProductNumber": "PRODUCT NUMBER", "Description": "BEZEICHNUNG", "Manufacturer": "HERSTELLERNAME", "ReasonRequired": "Field is required", "Tooltip": "Bei Auswahl eines konkreten Produktes ist die Angabe einer Begründung im Freitextfeld- bspw. hinsichtlich besonderer medizinischer und therapeutischer Erfordernisse - gem. HilfsmittelRL erforderlich.", "ReasonPlaceHolder": "Add a reason why you choose a single product"}, "Prescrible": {"titleEdoctor": "eDoctor Letter", "textmoduleHint": "In freetext fields, type a Textmodule shortcut or press Strg + Space.", "errMissingPrinterConfig": "Failed to print. Please check printer configuration.", "ErrorCode_PrinterHost_Not_Found": "Printer host not found", "ErrorCode_PrinterProfile_Not_Found": "Printer profile not found", "PrescribleForm": "Prescribe", "GetReferralCodeWait": "Getting referral code. Please stay on this screen.", "GetReferralCodeSuccess": "Referral code is ready. Please click to <a href={{link}}>insert the referral code to the form.</a>", "GetReferralCodeFail2": "Die Anforderung eines Vermittlungscode ist fehlgeschlagen. Bitte informieren Sie Ihren Patienten, dass dieser sich an die Terminservicestelle (Tel. 116117, www.116117.de) für die Vermittlung eines Termins wenden kann.", "Print": "Print", "Save": "Save", "Update": "Update", "ErrorMsg": "<PERSON><PERSON><PERSON>", "WarningMsg": "Das Formular ist nicht vollständig ausgefüllt", "WarningStartDateMessage": "Grundsätzlich ist die Bescheinigung von AU für den Zeitraum vor dem Datum der Feststellung nicht möglich. In Ausnahmefällen kann während des Zeitraums der Entgeltfortzahlung eine Rückdatierung der Arbeitsunfähigkeit bis zu drei Tagen erfolgen. Bei Folgebescheinigungen kann auf die Eintragung des Datums im Feld 'arbeitsunfähig seit' verzichtet werden", "ErrorEndDateMessage": "The date in the Expected-AU-to field should not be 31 days greater than the date in the Determined-on field.", "ErrorStartDateOverEndDateMessage": "The start date must be before the end date.", "ErrorMissingIcd10CodeMessage": "ICD-10 code is required", "InvalidICDCodeError": "Geben Sie die Diagnose(n) als endständige ICD-10-GM-Codes an.", "InvalidICDCodeSecondary": "The ICD-10 GM code cannot be used to issue a certificate of incapacity for work. There must be at least one primary code.", "InvalidICDCodeReserved": "Der ICD-10-GM-Kode ist nicht mit Inhalt belegt und darf daher nicht zur Abrechnung und/oder bei den Formularen der vertragsärztlichen Versorgung verwendet werden.", "InvalidICDCodeNonExist": "The stored ICD code does not exist in the ICD-10 GM master file. Therefore this code must not be used for billing or in forms.", "HintCharactersMsg": "Some characters are deleted due to exceeding max length. Please review its content again.", "LimitCharacters": "Maximum {{ number }} characters", "LeavePageTitle": "Discard unsaved changes?", "CancelLeave": "Cancel", "ConfirmLeave": "Yes, discard", "RezeptSaved": "Prescription saved", "Doctor": "Doctor", "Language": "Language", "FormPrescribed": "Form prescribed", "FormSaved": "Form saved", "LabID": "Lab ID", "Send": "Send", "errLabId": "Should be from 10 to 60 digits", "errDuplicatedLabId": "The lab order number has been used for another patient.", "FormConsiderationTitle": "What form would you like to use?", "FormConsideration_PrintedForm": "Printed form", "FormConsideration_Digital": "Send to a lab electronically", "LDKModuleDisableAlert": "Testmodule turned off. For submission electronically, please turn on LDK Testmodule.", "ErrorSelectSchein": "Please select a schein", "ReceiveCodeSuccess": "Referral code inserted", "labRecipient": "Lab Recipient", "additionalInfo": "Additional info", "AOK_FA_OC_BW_Antrag_AOK_Sports_V3_additionalInfo": "Vertrag zur Versorgung in den Fachgebieten Orthopädie und Rheumatologie in Baden-Württemberg gemäß § 73c SGB V", "formDetails": "Form Details", "overtakeWarning": "Maximum number of digits reached", "Recipe": "Prescription", "GBAGuideline": "Additional file", "Adressliste": "Adressliste des Sozialen Dienstes", "transitionManagementHint": "For billing of that form you have to ensure patients signature. Please store the original form in your practice and offer one sample to patient", "noFaxNumberHint": "There is no fax number given, please contact insurance company to ask for fax number.", "includeLabResult": "Include Lab result", "BKK_BY_HZV_ConfirmLeave": "Are you sure you don't need to fill out the form for insurance company? An early intervention can support patient treatment and ensure faster help for patient's symptoms.", "BKK_BY_HZV_YesLeave": "Yes, it is not needed and can be filled out later.", "BKK_BY_HZV_CancelLeave": "No, I want to fill out", "Lab": "Lab", "OrderEntrySystem": "Order Entry System", "ErrorCode_ValidationError_Form_IcdIsRequired": "ICD code is required", "missingEHBA": "Missing eHBA", "Warning": "Warning", "warningTextEHBA": "Please check whether the card is inserted and the settings on the Admin is set up correctly.", "setUpKimAccount": "Please set up a KIM account", "signSuccess": "eDoctor Letter signed", "signFailure": "Failed to sign eDoctor Letter", "signProcessing": "Signing eDoctor Letter..."}, "Form": {"M1ICDNotify": "If sickness continues due to non-specific depression, please check whether you can take targeted treatment diagnostic and therapeutic measures to achieve benefits for the course of the disease.", "M1EmploymentDialog": {"ReviewEmploymentInformation": "Review employment information", "M1ICDNotifyContent": "Bitte füllen Sie vor der Ausstellung der AU den Beschäftigungsstatus und die Beschäftigungsart aus bzw. aktualisieren oder bestätigen Sie die Aktualität der Daten!", "Cancel": "Cancel", "Review": "Review"}}, "TimePeriod": {"No": "No", "1Weeks": "1-2 Weeks", "1Month": "1 Month", "3Months": "3 Months", "6Months": "6 Months", "9Months": "9 Months", "12Months": "12 Months"}, "Diagnosis": {"DiagnosisOption": "Diagnosis (optional)", "Diagnosis": "Diagnosis", "PlaceHolder": "Select a documented diagnosis or enter a freetext diagnosis"}, "Anotation": {"HimiProduct": "HIMI PRODUCT", "Quantity": "QUANTITY", "TimePeriod": "TIME PERIOD", "FurtherInformation": "FURTHER INFORMATION (OPTIONAL)", "Change": "Edit", "AddAnotherDiagnosis": "ADD ANOTHER DIAGNOSIS"}, "Muster8Confirm": {"Muster_8": "M8 Sehhilfenverordnung", "Muster_8A": "M8A Verordnung von vergrößernden Sehhilfen", "Title": "Select a form", "Content": "Which form would you like to use for prescribing visual aids? The distance visual acuity is decisive for this.In cases in who are prescribed a visual aid(sample 8 A) is the value of the state the best corrected distance vision for the right and left eye, specifying the visual aid used(glasses and / or contact lenses) and the required magnification."}, "Timeline": {"forms": "Forms", "PrintOutHint": "Prescription has been prepared. Please print it out to complete.", "NewHimi": "Prescribed new HIMI", "RefillForm": "Refill", "ViewForm": "View Form", "EditForm": "Edit Form", "ControllableFromTitle": "Controllable HIMI", "ControllableHint": "Controllable HIMI additional form for {{deviceName}} in {{contractId}}", "Remove": "Remove", "HandedOver": "<PERSON> as Handed over", "Received": "<PERSON> as Received", "RezeptSaved": "Prescription saved", "RemovedRezeptur": "Removed prescription", "editEntryDate": "Edit entry date", "More": "More", "M52Hint": "Please fax that form to fax number what you see on the bottom left.", "M52ForM1Hint": "With this form you are going to prescribe a sickness period of more than 42 days within the last 180 days. Please check if you need to fill out M52.2 additionally.", "TimelineUpdatedSuccess": "Timeline entry updated", "TimelineUpdatedFail": "Failed to update timeline entry."}, "RemoveEntryConfirm": {"Title": "Remove prescription", "Content": "The prescription will be removed permanently. This action cannot be undone.", "Remove": "Remove", "Cancel": "Cancel"}, "ConfirmDialog": {"printDate": "Print out patient header", "printDateCheckbox": "Select date for patient header", "date": "Date", "TitleDialogDummyCannotBePrinted": "Patient header cannot be printed.", "ContentDialogDummyCannotBePrinted": "The VKNR 74799 is not permitted for billing and form printing"}, "AddNewDiagnosisDialog": {"title": "Add new diagnosis", "documentedDate": "Documented Date", "treatingDoctor": "Treating Doctor", "diagnosis": "Diagnosis", "typeOfDiagnosis": "Type Of Diagnosis", "certainty": "Certainty", "location": "Location", "addDiagnosisToTimeline": "Add diagnosis to Timeline", "diagnoses": {"anamnestic": "Anamnestic Diagnosis", "acute": "Acute Diagnosis", "permanent": "Permanent Diagnosis"}, "gesichert": "<PERSON><PERSON><PERSON><PERSON>", "verdacht": "Verdacht auf", "zustandNach": "<PERSON><PERSON><PERSON> nach", "ausgeschlossen": "Ausgeschlossen"}, "SelectDiagnosisDialog": {"buttonText": "Transfer diagnoses", "buttonTakeover": "Save", "title": "Select diagnosis", "Filter": {"search": "Search", "dateRange": "DD.MM.YYYY - DD.MM.YYYY"}, "Table": {"date": "Date", "diagnosis": "Diagnosis", "addDiagnosis": "Add diagnosis to Timeline", "noResultFound": "No data found", "treatmentRelevant": "Treatment relevant", "ANAMNESTIC": "Anamnestic Diagnosis ({{count}})", "PERMANENT": "Permanent Diagnosis ({{count}})", "ACUTE": "Acute Diagnosis ({{count}})", "edit": "Edit", "add": "Add", "unmarkAsTreatmentRelevant": "Unmark as treatment relevant", "markAsTreatmentRelevant": "Mark as treatment relevant", "diagnoseMarkedAsTreatmentRelevant": "Diagnosis marked as treatment relevant", "diagnoseUnMarkedAsTreatmentRelevant": "Diagnosis unmarked as treatment relevant", "addPermanentDiagnosis": "Add permanent diagnosis", "addAcuteDiagnosis": "Add acute diagnosis"}, "continueWithoutTakingOver": "Continue without taking over diagnosis", "requiredAtLeast": "At least one diagnosis must be selected.", "atLeastDiagnoseCanBill": "At least 1 treatment relevant diagnosis must be selected", "informPsychotherapyCase": "An ICD-10 code from the last treatment case can be used to code the pseudo-GOP.", "selectedRows": "{{count}} items selected"}, "SelectMedicinesDialog": {"buttonText": "Transfer prescribed medication", "buttonTakeover": "Transfer", "title": "Select prescribed medication", "selectedRows": "{{count}} items selected", "Table": {"prescribed": "prescribed", "medication": "medication", "prescribedBy": "prescribed by", "noResultFound": "No data found"}}, "ReferralThroughTSSDialog": {"title": "Referral through TSS", "specialistGroup": "Specialist group", "urgency": "Urgency", "additionalQualification": "Additional Qualification", "lanr": "LANR", "bsnr": "BSNR", "cancel": "Cancel", "getReferralCode": "Get referral code", "urgent": "<PERSON><PERSON>", "urgentDescription": "The patient needs an appointment within 4 weeks at the specialist.", "routine": "Routine", "codesystemService": "Codesystem service", "procedure": "Probatorik procedure", "routineDescription": "A waiting period of more than 4 weeks seems reasonable.", "specialistGroupRequired": "Specialist Group is mandatory", "urgencyRequired": "Urgency is mandatory", "lanrRequired": "LANR is mandatory", "bsnrRequired": "BSNR is mandatory", "lanrInvalid": "LANR is invalid", "bsnrInvalid": "BSNR is invalid", "missingKVConnectTooltip": "To enable this feature, please contact system support to set up a KV-Connect account.", "codesystemRequired": "Codesystem is mandatory", "tooltipDescription_1": "To enable this feature, the information must be selected:", "tooltipDescription_2": "1. ambulante Psychotherapie\n2. Die Psychologische Behandlung kann nicht in dieser Praxis durchgeführt werden\n3. Weitervemittlung\n4. zeit nah erford<PERSON>h", "empty": "No selection"}, "Warning": {"noInsurance": "Attention: There is no valid proof of insurance in the current quarter. Claims for recourse are possible without valid proof of insurance!", "mediContract": "Before sending the declaration of participation by the insured person, please add the therapy procedure, the diagnosis(es) and the diagnostic certainty.", "indicatedDiagnose": "No indicating diagnosis is documented. Please check your prescription.", "M1Duration": "With the completion of today's sick note (AU), the insured person has been on sick leave for more than 42 calendar days in the last 180 calendar days. Please check if you need to fill out the \"Report for the health insurance company in case of continued incapacity to work\" (Form 52.2).", "M1ICDNotify": "In case of continued sick leave (AU) due to unspecified depression, please check if you can achieve benefits for the course of the disease through targeted diagnostic and therapeutic measures.", "transitionManagement": "For billing the service \"Transition Management,\" the patient's signature on the patient information leaflet is mandatory. Please provide this to the patient and have them sign it. The original patient information leaflet for care management remains in the practice, and a copy or one specimen must be handed out to the patient.", "M52Notify": "Please fax the completed form to the fax number shown at the bottom left of the form.", "quickInformation": "For billing the remuneration position regarding patient accompaniment, the patient's signature on the patient information leaflet is mandatory. Please provide this to the patient and have them sign it."}, "Error": {"dummyVknr": "The VKNR 74799 is not permitted for billing and form printing."}, "Information": {"hint": "Our staff, including nursing professionals and certified care experts from AOK Baden-Württemberg, will be happy to answer any questions you may have and provide consultations regarding home nursing care.", "himiQuestionName": "Please fax the HIMI prescription with controllable HIMI questionnaire to the fax number of the patient's insurance company. After faxing it, please provide these documents to the patient."}, "Generated": "Generated", "Printed": "Printed", "Incorrect": "Incorrect", "PrintedCreated": "Created", "PrintedSuccesfully": "Submitted", "PrintedFailed": "Failed", "HandedOver": "Handed over", "Received": "Received", "ConfirmCreateScheinDialog": {"title": "No schein with diagnosis found", "description": "To add a diagnosis, please create a schein for the patient first.", "confirmBtn": "Create new Schein"}, "EAU": {"settings": "Settings", "doctor": "Doctor", "printForCopy": "Print copy for", "employer": "Employer", "patient": "Patient", "printer": "Printer", "insurance": "Insurance", "tray": "Tray"}, "HandoverLetter": {"title": "LKK Handover Letter", "print": "Print LKK handover letter", "requiredField": "This field is required", "doctor": "Doctor", "timeOfMoving": "Time of moving", "upCommingDoctor": "Upcoming treating doctor", "selectPatient": "Select patients", "maximum": "Maximum {{value}} patients", "bsnr": "BSNR", "lanr": "LANR", "expertise": "EXPERTISE", "tel": "Tel.", "salutation": "<PERSON><PERSON> geehrte/r", "male": "<PERSON>", "female": "<PERSON><PERSON>", "savedSuccessfully": "All forms saved", "savedFail": "Failed to save all", "printedSuccessfully": "All forms printed", "printedFail": "Failed to print all", "printPreview": {"title": "Print handover letters", "text1": "Ihre Teilnahme an der Hausarztzentrierten Versorgung", "text2": "gerne informiere ich Si<PERSON> heute, dass ich zum", "text3": "meine hausärztliche Praxis an meinen", "text4": "Praxisnachfolger", "text5": "übergeben werde.", "text6": "Sie haben sich entschieden, bei mir am Hausarztprogramm teilzunehmen.", "text7": "<PERSON><PERSON><PERSON>", "text8": "am Hausarztprogramm können Si<PERSON> gerne in Zukunft auch bei meinem Praxisnachfolger", "text9": "weiterführen.", "text10": "<PERSON>n <PERSON> sich dazu entsch<PERSON>ßen, müssen Si<PERSON> keine weiteren Schritte", "text11": "unternehmen.", "text12": "Sollten Sie jedoch mit Ihrer weiteren Teilnahme am Hausarztprogramm bei meinem", "text13": "Praxisnachfolger nicht einverstanden sein, haben Si<PERSON> die Möglichkeit bei Ihrer Krankenkasse", "text14": "innerhalb von 4 Wochen ab Zugang dieses Schreibens schriftlich", "text15": "Widerspruch", "text16": "einzulegen.", "text17": "<PERSON>hr Widerspruch ist in diesem Fall an folgende Adresse zu richten:", "text18": "Sozialversicherung für Landwirtschaft, Forsten und Gartenbau,", "text19": "<PERSON><PERSON>,", "text20": "Dr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1", "text21": "84036 <PERSON><PERSON><PERSON>", "text22": "Ihre hausärztliche Versorgung durch mich ist in jedem Fall bis zu meinem Ausscheiden aus der", "text23": "Praxis sichergestellt.", "text24": "<PERSON><PERSON> hoffe, dass Sie meinem Praxisnachfolger auch in Zukunft Ihr Vertrauen schenken werden.", "text25": "Mit freundlichen Grüßen"}}, "confirmSickLeaveDialog": {"title": "Sick Leave: Further Steps Required", "description": "With that form of sickness the patient is longer sick than 42 days within last 180 days. Please check if its necessary to fill out form 52.2.", "cancelButton": "OK", "confirmButton": "Open form M52.2"}, "confirmMaximumUVGoaF9990": {"title": "Confirm F9990", "description": "Use this form for invoices with up to 14 service codes. Create BG invoices separately.", "cancelButton": "Cancel", "confirmButton": "Open"}}