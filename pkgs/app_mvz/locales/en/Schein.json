{"invalidDateErrorMessage": "invalid", "outOfRangeErrorMessage": "Invalid date", "outOfRangeError4102": "Date of issue must not be in the future.", "optional": "optional", "noResults": "No results.", "focusMode": "Focus this schein", "focusingMode": "Quit focus mode", "copySuccess": "Copied invoice number", "createSchein": {"title": "Create K<PERSON>", "updateTitle": "Edit KV <PERSON>", "titlePrivate": "Create Private Schein", "updateTitlePrivate": "Edit <PERSON>", "titleBg": "Create BG schein", "updateTitleBg": "Update BG schein", "titleIgel": "Create <PERSON><PERSON>", "updateTitleIgel": "<PERSON> <PERSON><PERSON>", "titleHzV": "Create HzV <PERSON>", "updateTitleHzV": "Edit HzV <PERSON>", "titleFaV": "Create Fa<PERSON>", "updateTitleFaV": "Edit FaV <PERSON>", "createScheinBtn": "C<PERSON> <PERSON><PERSON>", "updateScheinBtn": "<PERSON>", "leavePageTitle": "Leave without saving?", "leavePageContent": "Unsaved changes will be discarded if you leave this page.", "confirmLeave": "Yes, leave", "cancelLeave": "No, stay here", "changeTreatmentCaseTitle": "Would you like to change the treatment case?", "changeTreatmentCaseContent": "Unsaved changes will be lost. This action cannot be undone.", "noChange": "No", "yesChange": "Yes", "required": "This field is required", "startDateBeforeEndDate": "Start date must before end date", "9digitLanr": "This field must be 9 digits", "errLanrIsPseudoLanrFormat": "Lifelong doctor number of the contract doctor / contract psychotherapist must not be 555555", "invalidFormat_DDMMJJannnnn": "For other cost units, the Bundeswehr, this is a mandatory field.", "OnlyOneFieldAllowed": "Only one of the fields may be present:\t\n• ASV team number\t\n• N(BSNR) of the referrer\t\n• Referral from other doctors", "codeFormat": "Referral code must be 12 digits long", "codeExists": "Referral code already exists. Please try another one.", "timeNotValid": "Future date is not allowed", "invalidDateOfIssue": "The date of issue must be within the selected quarter.", "invalidQuarter": "Invalid Quarter", "InvalidKtab": "Der Kostenträger-Abrechnungsbereich zum vorliegenden Kostenträger ist nicht mehr gültig.", "SubGroup28NotAllowESS": "The ESS is not allowed for the schein subgroup 28.", "InsuranceStatusNotFoundIn9407": "Die Versicherungsart in der Kombination mit der Kostenträgergruppe und des ausgewählten KTABs ist in ihrer KV nicht zu lässig.", "cannotSetQuarterToAFutureQuarter": "<PERSON><PERSON> set Quarter to a future quarter", "created": "<PERSON><PERSON> created", "updated": "Schein updated", "warningHzvContent_ABRG1006": "Der Patient ist in die hausarztzentrierte Versorgung eingeschrieben. Die Behandlung dieses Patienten ist\nfür alle im Gesamtziffernkranz enthaltenen HZV-Leistungen – außer im organisierten Notfalldienst – über\ndie HZV abzurechnen. <PERSON><PERSON><PERSON>, die zusätzlich am AOK-Facharzt-Programm teilnehmen, gilt ebenfalls\nder Gesamtziffernkranz und die dort definierten Leistungen aus dem fachärztlichen Bereich. Bitte prüfen\nSie die Angaben zur Abrechnung gegenüber der KV.", "warningHzvContent_ABRG829": "The patient is enrolled in the family doctor-centered care (HzV) programme. Treatment of this patient for all services included in the HZV code set - except for urgent treatments - must be billed through HzV. Please check the billing information for KV.", "warningHzvContent_VERT484": "Die Behandlung dieses Patienten ist für alle im HzV-Ziffernkranz enthaltenen Leistungen über die HzV abzurechnen.", "warningFavContent": "Dieser Patient darf nur im organisierten Notfalldienst über die KV abgerechnet werden", "warningHzvFavContent": "Die Behandlung dieses Patienten ist für alle im HzV-Ziffernkranz enthaltenen Leistungen über die HzV abzurechnen. Dieser Patient darf nur im organisierten Notfalldienst über die KV abgerechnet werden", "cancel": "Cancel", "save": "Save", "create": "Create", "keyboardCommandNavigateBetweenSections": "Alt + ↑↓ to navigate within the form", "feeCatalogue": "Fee catalogue", "Error": {"CostUnitHasExpired": "Cost unit has expired. This may result in billing errors.", "IKNumberHasExpired": "IK number has expired.", "CostUnitIsNotAvailableInKvRegion": "Cost unit is not available in your KV region", "CostUnitIsTerminated": "Cost unit is terminated", "TheBillingAreaIsOutOfValidityDateRange": "The Billing Area is out of the valid date range", "WarningKvx3SKTAddtional": "The following should be provided under SKT additional information", "ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter": "The insurance information is not valid this quarter."}, "markedNotBilled": "Billed schein cannot be edited. Please \"mark as unbilled\" to edit the schein and its documented entries.", "btnMarkNotBilled": "<PERSON> schein as unbilled", "errorOutDateRange": "The validity period must be within the quarter of the schein.", "restrictLANRValue": "The (substitute) value \"{{fieldValue}}\" is obsolete and not permitted as field content.", "errLanrInvalidDigitRule": "Lifelong doctor number of the contract doctor / contract psychotherapist (format nnnnnnmff) has invalid m value", "errorRe4229": "It must be a technical code for identifying miners' union cases. The format must be numeric and have a length of 5 characters.", "warningValidate": "Warning", "warningTreatmentCategory": "The record \"kvx3\" of SDKV is required:", "warningMissingRe4214": "Erfassen Sie bei IVD-Leistungen den Behandlungstag", "warningMissingRe4214Only": "Im Fall einer Überweisung in-vitro-diagnostischer Leistungen (IVD) soll im Rahmen der Abrechnung der Behandlungstag der IVD-Leistungen übertragen werden.", "addMoreInformation": "Do you want to add more information?", "maximumValue": "The amount must be less than {{ amount }}", "cannotHigherField": "The amount must be less than {{ field }} ({{ amount }})", "amountApprovedRequired": "Amount approved is required", "totalAmountServiceCode": "The amount billed for therapy sessions cannot be higher than the number of approved sessions", "ConfirmDialog": {"title": "No <PERSON>hein created for the documented date", "description": "Entries like diagnosis, service code require a Schein to be able to submit for billing later.", "btnNo": "<PERSON><PERSON>", "btnYes": "Create new Schein", "titleForDoctorLetter": "No <PERSON>hein created for current quarter", "descriptionForDoctorLetter": "Takeover of patient and sender information requires a Schein."}, "ErrorCode_Doctor_Not_Found": "Doctor is not found", "SubGroupNotFoundIn9406": "The selected subgroup is not allowed in the current region.", "AccidentDateShouldBeforeArrivalDate": "Accident date should be before arrival date", "errWorkingHourInWeek": "Should be greater than 0 and less than 100", "createOnShouldBeforeEndDate": "\"Created on\" should be before \"end date\"", "workingTimeStartShouldBeforeEnd": "\"Working time start\" should be before \"working time end\"", "TheBillingAreaIsInvalid": "The billing area is invalid"}, "AutoFill21Modal": {"title": "Übernahme Auftrag von M39 ins Auftragsfeld", "content": {"group1": {"title": "Abrechnungsart", "input": [{"key": "P", "caption": "Primärscreening"}, {"key": "A", "caption": "Abklärungsdiagnostik"}]}, "group2": {"title": "Request", "input": [{"key": "Zyto", "caption": "Zyto"}, {"key": "HPV", "caption": "HPV"}, {"key": "KoTest", "caption": "KoTest"}]}}, "captionCancel": "Cancel", "captionOk": "Ok"}, "AutoFill27Modal": {"title": "Auftrag bei Muster 10C", "content": "Bei der Auswahl der Scheinuntergruppe “27” muss das Feld Auftrag zusatzlich befüllt werden. Wie mochten Si<PERSON> vorgehen?", "captionCancel": "Cancel", "captionOk": "Ok", "autoFillMuster10CText": "Diagnostische Abklarung Muster 10C", "captionApplyMuster10C": "“Diagnostische Abklarung Muster 10C” eintragen", "captionApplyFreeText": "Freetext"}, "AutoFill4205Modal": {"title": "Text blocks", "addButton": "Add text blocks", "boxSearchPlaceholder": "<PERSON><PERSON>", "nameHeader": "Name", "contentHeader": "Inhalt", "editButton": "Edit", "removeButton": "Remove", "newEntry": "<PERSON><PERSON><PERSON> Eintrag", "captionOk": "Transfer", "captionCancel": "Cancel", "editValue": "Edit", "removeValue": "Remove", "RemoveConfirm": {"title": "Remove this request element?", "message": "This action cannot be undone.", "btnCancel": "No", "btnOk": "Yes, remove"}}, "deleteScheinModal": {"title": "Delete Schein?", "btnNo": "No", "btnYes": "Yes, remove", "content": "Do you want to remove this schein? This action cannot be undone", "warningTitle": "Cannot remove this Schein", "warningBtnOk": "Okay", "warningContent": "Cannot remove <PERSON><PERSON> with documented entries. Please remove all Schein records and try again."}, "confirmMarkNotBilled": {"title": "<PERSON> schein as unbilled?", "content": "This schein and its content will be marked as unbilled. This action cannot be undone.", "btnNo": "No", "btnYes": "Yes, update"}, "createScheinModal": {"title": "Create New KV Schein?", "btnNo": "No", "btnYes": "Yes", "content": "KV Schein has not been created yet for the quarter. Would you like to create a schein?"}, "generalInfo": {"title": "General Information", "optional": "optional", "doctorId": "treatment doctor", "kvTreatmentCase": "treatment case", "kvTreatmentCaseValues": {"0101": "Ambulante Behandlung", "0102": "Überweisung", "0103": "Belegärztliche Behandlung", "0104": "Notfall", "0109": "Kurärztliche Behandlung", "sadt1": "SADT-Ambulante Behandlung", "sadt2": "SADT-Überweisung", "sadt3": "SADT-Belegärztliche Behandlung", "BG": "BG case", "IGEL": "IGel case", "PRIVATE": "Private case", "0106": "Urlaubs- bzw. Krankheitsvertretung bei belegärztlicher Behandlung"}, "kvScheinSubGroup": "Scheinuntergruppe", "g4101": "Quarter", "g4102Private": "Date of issue", "g4131": "Special Person Group", "g4132": "DMP Labelling", "g4122": "Settlement area", "g4106": "KTAB", "g4110": "Insurance end date", "bgType": "BG Type", "bgAccidentDate": "Accident date", "bgAccidentTime": "Accident time", "bgWorkingTimeFrom": "Arbeitszeit am Unfalltag von", "bgWorkingTimeTo": "Bis", "job": "<PERSON><PERSON><PERSON>", "tariffType": "<PERSON><PERSON><PERSON> typ", "insuranceCompany": "INSURANCE NAME", "searchInsuranceCompany": "Search insurance company", "g3116": "WOP-Kennzeichen/ KV-Region", "g4104": "VKNR", "excludeFromBilling": "Exclude this schein from billing", "mitglied": "<PERSON><PERSON><PERSON><PERSON>", "familienmitglied": "Familie<PERSON><PERSON><PERSON><PERSON>", "rentner/in": "Rentner/in", "kvRegion": "WOP-Kennzeichen/ KV-Region {{kvRegion}}", "patientReceipt": "Patient receipt", "longTerm": "HEIMI long-term approval", "ehicApproval": "EHIC approval", "ehicNotApproval": "EHIC form has not been printed out yet", "coPayment": "Co-payment exemption until {{date}}", "coPaymentExpired": "Co-payment exemption expired", "readCard": "Ersatzverfahren (EV)", "specialGroup": "Special Person Group: {{specialGroup}}", "insuranceDate": "Until {{endDate}}", "ikNumber": "IK: {{ikN<PERSON>ber}}", "markAsBilled": "<PERSON> as unbilled", "markAsActive": "<PERSON> as active", "markedAsActive": "Marked as active", "activeInsurance": "Active", "referralM39": "M39 referral", "discountUnit": "Discount unit", "discountNumber": "Discount", "tel": "Tel: {{tel}}", "fax": "Fax: {{fax}}", "contactHint": "Supply management contact health insurance company", "invoiceNumber": "Invoice: {{invoiceNumber}}", "terminated": "Terminated", "euro": "Euro", "percent": "Percent", "bsnr": "BSNR", "lanr": "LANR", "expertise": "Expertise", "feeCatalogue": "Fee catalogue", "includeText": "Include", "onlineParticipation": "Date for Online participation check", "startDate": "Start date", "1stDayOfQuarter": "First day of Q{{quarter}}/{{year}}", "changeStartDate": "Changing the contract start date places the billed items outside the activated participation period.", "fileNumber": "File Number", "createdOn": "Created On", "endDate": "End On", "personalAccident": "Personal Accident", "subPersonalAccident": "Check if the patient is an employer of BG insurance", "accidentDate": "Accident Date and time", "arrivalDate": "Arrival Date and time", "titleAccidentInfo": "Accident info", "titleWorkingDay": "Working time on the day of the accident", "workingTimeStart": "Working time start", "workingTimeEnd": "Working time end", "generalAction": "Allgemeine Handlungsbedarf", "specialAction": "Besonderer Handlungsbedarf"}, "additionInfo": {"title": "Additional Information", "g4102": "Date of issue", "ad4206": "Mutm. Tag der Entbindung", "ad4125": "Gültigkeitszeitraum", "ad4126": "SKT-Bemerkung", "ad4124": "SKT-Zusatzangaben", "ad4204": "Eingeschränkter Leistungsanspruch gemäß §16 Abs. 3a SGB V", "ad4202": "Unfallfolgen", "tsvgSubgroupTitle": "Terminservice - und Versorgungsgesetz", "tsvgContactType": "Vermittlungs-/Kontaktart", "tsvgInfor": "Ergänzende Informationen zur Vermittlungs-/Kontaktart", "tsvgTranferCode": "Vermittlungscode", "tsvgContactDate": "Datum der Kontaktaufnahme", "warningTsvgContactDate": "Bei Hausarzt-Vermittlungsfällen ist der Tag der durch den Hausarzt festgestellten Behandlungsnotwendigkeit anzugeben.", "tssTerminfall": "TSS-Terminfall", "tssAkutfall": "TSS-Akutfall", "havErmittlungsfall": "HA-Vermittlungsfall", "offeneSprechstunde": "Offene Sprechstunde", "neupatient": "Neupatient", "tssRoutineTermin": "TSS-Routine-Termin", "ad4123": "Treatment category", "otherPayers": "Sonstige Kostenträger"}, "additionInfoWarning": {"ad4125": "Gültigkeitszeitraum", "ad4126": "Bemerkung der Entschädigungsbehörde unter SKT-Bemerkungen", "ad4124": "SKT-Zusatzangaben", "ad4123": "Personenkreis/Untersuchungsart", "noticeWarningRequired": "{{fieldName}} is required", "noticeWarning": {"1": "Basic list number care office", "2": "Registriernummer", "3": "reference number of the compensation authority", "4": "File number of the cost unit institution", "5": "Name of foreign country", "6": "Personal number", "7": "Personal ID number", "8": "Department", "9": "School/university/nursery school", "10": "Personal number of department"}}, "psychotherapy": {"title": "Psychotherapy", "showOptionFields": "Show Optional Fields", "ps4299": "LANR of Psychotherapist", "bezugsperson": {"title": "Other Person", "ps4256": "Approved service code", "ps4257": "Amount of billed sessions", "ps4255": "Amount of approved therapy sessions"}, "versicherten": {"title": "Patient", "ps4253": "Approved service code", "ps4254": "Amount of documented therapy", "ps4252": "Amount of approved therapy sessions"}, "ps4247": "Date of request", "ps4247Tooltip": "Date of request (Group therapy)", "ps4244": "Approved service code", "ps4245": "Amount approved therapy sessions", "ps4246": "Amount billed therapy sessions ", "ps4236": "Checking somatic symptoms before requesting Psychotherapy", "ps4250": "Combinated treatment of single and group therapy", "ps4251": "Type of combinated treatment", "ps4234": "Psychotherapy approval", "ps4235": "Date of approval", "isPausingDate": "Therapy pause", "pausingStartDate": "Pausing start date", "pausingEndDate": "Pausing end date", "isInsuranceInformedTherapy": "Insurance is informed about end of therapy", "titleEntry": "Psychotherapy - Approval added for {{ title }}", "descriptionAmountEntry": "Amount of approved sessions:", "descriptionApprovalDate": "Date of approval:", "descriptionRequestDate": "Date of request:", "isReason": "Informal justification to the health insurance company in accordance to §13 of the psychotherapy agreement", "groupTherapy": "Group Therapy", "addPsychotherapy": "Add psychotherapy approval", "takeover": "Transfer", "takeoverTitle": "Transfer approval from previous schein", "takeoverStartDate": "Date of the notice of recognition: {{date}}", "takeoverInfo": "{{amountApproval}} approved therapy units for insured persons: {{serviceCodes}}", "takeoverReferenceInfo": "{{amountApproval}} approved therapy units for reference persons: {{serviceCodes}}", "billed": "billed", "startDateBefore2017": "Since approval date is before 01.04.2017, some fields are not applicable and are disabled.", "addServiceCode": "Create service code", "serviceCode": "Service code", "amountApproval": "Amount <PERSON><PERSON><PERSON><PERSON>", "amountBilled": "Amount Billed", "insuredPerson": "Insured person", "RemoveConfirmDialog": {"title": "Remove psychotherapy approval?", "description": "Psychotherapy approval will be removed permanently. This action cannot be undone."}}, "employer": {"title": "Employer", "bgEmployerName": "Employer Name", "bgEmployerStreet": "Street", "bgEmployerHousenumber": "House number", "bgEmployerPostcode": "Postal code", "bgEmployerCity": "City", "bgEmployerCountry": "Country"}, "referral/consulting": {"noOptions": "No options", "title": "Referral/Consulting", "placeHolder": "search by name, LANR or BSNR", "erstveranlassers": {"title": "Erstveranlassers", "re4241": "LANR", "re4248": "Pseudo-LANR", "re4225": "ASV - Team number", "re4217": "(N)BSNR"}, "uberweisers": {"title": "<PERSON><PERSON><PERSON>", "re4242": "LANR", "re4249": "Pseudo-LANR", "re4226": "ASV - Team number", "re4218": "(N)BSNR"}, "re4220": "Referral to", "re4221": "Service type", "re4243": "Further treating doctor", "re4219": "Referral from other doctors", "re4208": "Finding/Medication", "re4207": "Diagnosis/Suspected diagnosis", "re4205": "Order", "re4209": "Additional information on examinations", "re4233": "Treatment Duration", "re4229": "Exception indication", "createDoctor": "Create doctor", "viewAll": "View all", "lanr": "LANR", "bsnr": "BSNR", "areaOfExpertiseIs": "Area of expertise is", "isNotGivenInSDAV": "is not given in SDAV", "noName": "no name", "unknown": "empty", "re4214": "Treatment day for IVD-Services", "re4214Tooltip": {"title": "Bei der Erfassung des Wertes Behandlungstages bei IVD-Leistungen gilt die folgende Priorität:", "description": "1. Probenentnahmedatum\n2. sofern das Probenentnahmedatum nicht bekannt ist:\na) Entweder das Ausstellungsdatum\nb) Oder das Probeneingangsdatum."}, "addTreatmentDuration": "Add treatment duration", "fromTo": "from ... to"}, "scheinOverview": {"title": "Schein Overview", "quarter": "QUARTER", "schein": "SCHEIN", "notFoundSchein": "No schein found", "notCreatedSchein": "No Schein created yet.", "viewDetails": "View Details", "edit": "<PERSON>", "editPrivateSchein": "<PERSON>", "editInsurance": "Edit insurance", "remove": "Remove", "createOne": "Create one", "createSchein": "C<PERSON> <PERSON><PERSON>", "viewAll": "View All", "more": "More", "diagnosisTakeover": "Transfer diagnoses", "makeAsReferral": "Make as referral", "referralDoctor": "Referral Doctor", "removeReferral": "Remove referral", "diagnosisTookOver": "Diagnoses transferred", "removeReferralDoctorTitle": "Remove referral doctor?", "removeReferralDoctorMessage": "Remove this referral doctor? This action cannot be undone.", "referralDoctorRemoved": "Referral doctor removed", "referralDoctorMarked": "Referral doctor added", "referralDoctorSelect": "Select", "referralDoctorSchein": "Referral"}, "scheinBlock": {"scheins": "Schein:", "selectSchein": "Select Schein", "noScheinCreated": "No Schein created yet.", "createSchein": "C<PERSON> <PERSON><PERSON>", "editInsurance": "Edit insurance info", "directionSelectSchein": "↑↓  zum navigieren", "patientReceipt": "Patient bill", "specialGroup": "Special group", "coPayment": "Co-payment exemption until", "europeanHealthInsuranceSaved": "EHIC form’s not printed out", "europeanHealthInsurancePrinted": "EHIC approval", "heimiLongtermApproval": "HEIMI long-term approval", "selectAnotherSchein": "Select another schein", "excluded": "Excluded", "markScheinAsNotBilled": "Reopen Schein", "scheinIsBilled": "<PERSON><PERSON> is billed", "numberQuarter": "({{totalQuarter}} unbilled cases)", "titleScheinHistory": "Fallübersicht", "select": "Select", "toolTipAddInsurance": "Add new insurance", "toolTipAddSchein": "Create new schein", "toolTipFullSchein": "View full Schein history", "tooltipCloseFullSchein": "Close schein history", "lblNewPrivateSchein": "New Private schein", "lblNewIgelSchein": "New IGel schein", "lblNewKvSchein": "New KV schein", "lblNewBgSchein": "New BG schein", "lblNewHzvSchein": "New HzV schein", "lblNewFavSchein": "New FaV schein", "lblNew": "New", "lblNewInsurance": "New Insurance", "PRIVATE": "Private", "IGEL": "IGel", "BG": "BG case", "scheinReopenSuccess": "Schein is reopened", "updateInsuranceSuccess": "Insurance updated", "deleteInsuranceSuccess": "Insurance deleted"}, "OvertakeDiagnosesDialog": {"title": "Transfer diagnoses to current quarter?", "diagnosis": "Diagnosis", "yesOvertake": "Yes, transfer", "description": "Select documented diagnoses you want to carry over to current quarter. Anamnestic diagnosis (AD) will be converted to permanent diagnosis (DD) upon adoption.", "treatment": "Treatment"}, "PrivateContractGroup": {"title": "Private Contract Group", "selectGroup": "Select group", "medical": "Medical", "fee": "Fee", "lab": "Lab", "additional": "Additional", "technical": "Technical", "valueAddedTax": "Value added tax", "withoutMwstastz": "Without mwstastz", "withMwstastz": "With mwstastz", "contractNumber": "Contract Number", "description": "Description", "increaseFactor": "Increase factor", "optionNew": "New"}, "AddressInfo": {"title": "Address info", "sendInvoice": "Send invoice to", "billingAddress": "Billing Address", "mainAddress": "Main Address", "warningAddress": "Address is incomplete. Please update full address in the patient profile.", "houseNumber": "Number", "city": "City", "country": "Country", "postalCode": "Postal code", "street": "Street", "distance": "Distance", "additionalAddressInfo": "ADDITIONAL ADDRESS INFO", "titleAddress": "Title", "lastName": "Last name", "firstName": "First name", "intendWord": "Intend word", "additionalName": "Additional name"}, "ModalContractGroup": {"titleCreate": "Create contract group", "titleEdit": "Edit contract group", "withoutMwstastz": "Without mwstastz", "withMwstastz": "With mwstastz", "contractNumber": "Contract number", "description": "Description", "valueAddedTax": "Value added tax", "increaseFactor": "Increase factor", "medicalServiceFactor": "Medical services", "feeServiceFactor": "Fee services", "technicalServiceFactor": "Technical services", "additionalFactor": "Additional services", "labServiceFactor": "Lab services", "cancel": "Cancel", "saveForAllPatients": "Save for all patients", "remove": "Remove", "create": "Create", "enter": "Enter", "createSuccess": "Contract group created", "updateSuccess": "Contract group is updated for all patients", "createError": "Failed to create", "updateError": "Failed to update", "removeSuccess": "Contract group has been removed for all patients", "removeError": "Failed to remove", "btnNo": "No", "btnRemove": "Yes, remove", "titleConfirmDialog": "Remove private contract group", "contentConfirmDialog": "The contract group will be removed permanently. This action cannot be undone.", "Error_Private_Contract_Group_Assigned_To_Schein": "Cannot remove a contract group that is bonded with active treatment cases.", "minimumValue": "Minimum value is 1,00"}}