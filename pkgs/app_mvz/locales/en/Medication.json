{"titlePopupUpdateStatus": "Medication database is not up-to-date.", "descPopupUpdateStatus": "The medication database is not up-to-date. Please contact your care provider.", "notFoundPzn": "Pzn not found", "MedicationPlanEmptyState": {"noMedicationsYet": "No medications yet", "noMedicationDescription": "Medications that are saved as permanent will appear here"}, "GreenRezept": {"quantity": "Quantity", "intakeInterval": "Intake interval e.g. 0-0-0-0", "asNeeded": "As needed", "furtherInfo": "Further information", "favPatientHint": "FaV participant"}, "KkRezept": {"quantity": "Quantity", "intakeInterval": "Intake interval e.g. 0-0-0-0", "asNeeded": "As needed", "furtherInfo": "Further information"}, "MedicationIntakeInterval": {"intakeInterval": "Intake interval e.g. 0-0-0-0", "dj": "Dj", "djTooltip": "Mark this checkbox if this patient has already received a dosage instruction from you", "gema": "gemäß schriftlicher Anweisung"}, "MedicationPrintPreview": {"regulations": "Print preview", "form": "form", "forms": "forms", "rezeptSaved": "Prescription saved", "rezeptsSaved": "Prescriptions saved", "1rezeptsSaved": "1 prescription saved", "1rezeptPrescribed": "1 prescription prescribed", "ERezeptStore": "ePrescription stored", "ERezeptSent": "ePrescription(s) sent", "ERezeptsSent": "ePrescriptions sent", "rezeptPrinted": "Prescription printed"}, "MedicationShoppingBag": {"title": "All prescriptions", "rezept": "Prescription", "prescribe": "Prescribe", "medication": "Medication", "quantity": "Quantity", "rezeptpool": "Prescription", "removeAll": "Remove all", "headerRemoveAll": "Remove all items from prescription?", "removeAllDescription": "All medications in the prescription will be removed permanently. This action cannot be undone.", "keepIt": "Keep it", "removedFromRezeptpool": "Removed from prescription", "keyboardHint": "Strg + V (prescribe), Strg + Delete (Remove all medication)", "noItemInRezeptPool": "There is no medication in the prescription.", "goToPrintPreview": "Go to print preview", "failedToCreateEPrescription": "Failed to create ePrescription", "erezeptTrainingDoctorAuthorization": "Training doctor is not authorized to create ePrescriptions"}, "Medication": {"title": "Beschluss des G-BA gem. § 35a SGB V", "medicationSearch": "Medication Search", "medicationPlan": "Medication plan (BMP)", "prescribedMedication": "Prescribed Medication", "noResultFound": "No results found", "noResultMessage": "No other package size available for this medication.", "noSubtanceMessage": "No other concentrations available for this medication.", "gbaNote": "<PERSON>s wurden keine Beschlüsse zu den Diagnosen gefunden", "filterPackageSizes": "PACKAGE SIZE"}, "SearchMedicationBox": {"headerAdvanceSearch": "or select a search option below", "atcCode": "ATC Code", "manufacturer": "Manufacturer", "pzn": "PZN", "significanceOfTheATCCode": "Significance of the ATC code", "substanceName": "Substance name", "substanceSearch": "Substance search", "tradeName": "Trade name", "inputAnySearch": "Input any search criteria", "addAsFreetext": "Add as freetext", "icdCode": "ICD-10", "searchIn": "Search in", "noResults": "No results", "readhand": "Red-Hand-Letters", "iww": "Indikationsgerechten wirtschaftlichen Wirkstoffauswahl", "amrl": "Arzneimittel-Richtlinie", "iconList": "Prescription Overview"}, "MedicationHeaderResult": {"tradeName": "Trade Name", "price": "Price", "coPayment": "Co-payment", "size": "Size", "form": "Form", "drugCategory": "Drug Category", "Manufacturer": "Manufacturer"}, "MedicineRow": {"secondaryInformation": "Secondary information", "discount": "Discounted", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez", "gRez": "G-Rez", "otc": "OTC", "lifeStyle": "Lifestyle", "av": "AV", "pIcon": "P", "tootlTipExpandComponentList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die in der Regel durch qualitative und wirtschaftliche Alternativen unter Beachtung medizinischer Ausschlusskriterien substituiert werden können.", "addToRezept": "Add to prescription", "tootipPharmacyRequire": "Unwirtschaftliches Präparat", "tooltipNegativeListMedication": "Negativliste - unwirtschaftliches Präparat", "tooltipAv": "<PERSON><PERSON><PERSON>", "tooltipPricus": "Pricus-Liste (für ältere Menschen potentiell ungeeignet)", "tooltipLifeStyle": "Anwendung zur Erhöhung der Lebensqualität", "tooltipOtc": "Non-prescription", "tooltipTReceipt": "T-Rezept", "tooltipGReceipt": "<PERSON><PERSON><PERSON><PERSON>", "tooltipKReceipt": "Kassenrezept", "tooltipBtmReceipt": "Betäubungsmittel", "attention": "Attention", "ignore": "Ignore", "selectAnotherOne": "Select another one", "attentionAv": "<PERSON><PERSON><PERSON>", "attentionAvContent": "No longer sold, may be in individual packs.", "attentionPricus": "<PERSON><PERSON><PERSON>", "attentionPricusContent": "This medication is not suitable for patients who are 65 years old or older.", "attentionNegativeList": "Negativeliste", "attentionNegativeListContent": "Please check whether the selected drug is economically justifiable for your practice.", "rezepturPool": "Prescription", "medicationPlan": "Medication plan", "addTo": "Add to", "btnRecipePool": "Add to prescription", "addToShoppingBag": "Added to prescription", "priscusMedicationWarning": "This medication is not suitable for patients who are 65 years old or older."}, "SearchEmptyState": {"searchForAMedication": "Search for a medication", "startTypingAboveToSearch": "Start typing above to see search results here", "noSearchResultFor": "No results for", "add": "Add", "asFreeTextPrescription": "as freetext prescription", "addedMedicineToRezept": "Added to prescription"}, "SearchMedicationResultView": {"tradeName": "Trade Name", "price": "Price", "coPayment": "Co-payment", "size": "Size", "form": "Form", "drugCategory": "Drug Category", "Manufacturer": "Manufacturer", "backToSearchResults": "Back to search results", "rationalePharmakotherapie": "Rationale Pharmakotherapie", "addedMedicineToRezept": "Added to prescription", "ingore": "Ignore", "yes": "Yes", "no": "No"}, "SecondaryMedicationView": {"importOrReImportProduct": "<PERSON>s handelt sich um ein Re- / Importprodukt", "medicalDevice": "Medical device", "tfg": "TFG", "coPayment": "Co-payment", "furterCost": "Further costs", "totalPayment": "Total payment", "interactions": "Interactions", "sideEffects": "Side effects", "usingDuringPregnacy": "Use during pregnancy and lactation", "indications": "Indications", "contraindications": "Contraindications", "regulations": "Regulation notices", "hintsAndWarnings": "Standard hints", "ingredients": "Other ingredients", "medicineDirective": "Medicine directive"}, "SecondLayerDialog": {"headerAccessOtherView": "Access other view", "btnPriceComparison": "Price comparison", "btnComposition": "Composition", "btnTechnicalInformation": "Technical information", "btnAlternatives": "Alternatives", "btnGBADecision": "G-BA decision", "btnPracticeSpecifics": "Practice specialities", "btnPriscusList": "Priscus List", "btnTherapyHint": "Therapy Hint", "headerImportanceInformation": "Important information", "headerGeneralInfo": "General info", "lblAtcCode": "atc code", "lblDosageForm": "Dosage form", "lblActiveIngredient": "Active ingredient", "lblOtherIngredients": "Other ingredients", "lblManufacturer": "Manufacturer", "lblDivisible": "Divisible", "yes": "yes", "no": "no", "headerPricing": "Pricing", "lblPrice": "Price", "lblFixedPrice": "Fixed-price", "lblCoPayment": "Co-payment", "lblAdditionalCosts": "Additional costs", "lblTotalCoPayment": "Total co-payment", "patientCosts": "Patient costs", "noCopayment": "no co-payment", "tooltipLowerThanFixedAmount": "Lower than fixed amount", "tooltipHigherThanFixedAmount": "Higher than fixed amount", "headerMedicinesDirective": "Medicines Directive", "keyboardHint": "Strg + R to add to prescription ; Strg + M to add to MP", "btnMP": "MP", "btnRecipePool": "Add to prescription", "addToShoppingBag": "Added to prescription", "priscusMedicationWarning": "This medication is not suitable for patients who are 65 years old or older.", "attention": "Attention", "ignore": "Ignore", "selectAnotherOne": "Select another one", "updateSuccess": "Medication plan has been updated", "doctorSampleAddedToTimeline": "Doctor sample added to timeline", "timeline": "Timeline", "freeCopayment": "free", "prescribableMedicalProductTitle4": "Anlage IV: <PERSON><PERSON><PERSON>", "prescribableMedicalProductTitle5": "Anlage V: Verordnungsfähiges Medizinprodukt nach § 31 Abs. 1 \nMedizinische notwendige Fälle:", "limitation": "Limitation", "discount130b": "Refund amount §130b", "processingRefill": "Processing refill...", "titlePznAtcCode": "Do you still want to continue?", "bodyPznAtcCode": "There is a prescription from the past available with same substances ({{name}}) where patient must have leftover until {{endDate}}. The last intake interval was Intake interval of the prescribed medication.", "buttonYesPznAtcCode": "Yes, prescribe", "buttonNoPznAtcCode": "No, cancel"}, "RenderAnnotation": {"edit": "edit", "asNeeded": "as needed", "favPatientHint": "„Teilnahme Facharzt-Prg.", "gema": "gemäß schriftlicher Anweisung", "digitalHealth": "Digitale Gesundheitsanwendung\n---------------------\n{{pzn}}\n---------------------\n{{name}}\n---------------------"}, "MedicineDetail": {"removedFromRezept": "Removed from prescription", "noPackageSize": "No more package size", "freetextPlaceholder": "Trade name / Substance name", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez", "gRez": "G-Rez", "private": "Private", "aokNordwet": "AOK Nordwet", "aokBremen": "AOK Bremen", "muster16aBay": "M16a bay", "asNeeded": "As needed", "furtherInformation": "Further information", "ersatzverordnung": "Ersatzverordnung", "artificialInsemination": "artificial insemination", "sonderkennzeichen": "Sonderkennzeichen", "gema": "gemäß schriftlicher Anweisung", "updateMedTypeSuccess": "Updated", "vaccination": "Vaccination", "eRezept": "ePrescription", "drugForm": "Drug form", "Muster_16": "K-Rez", "Gruenes_Rezept": "G-Rez", "Blaues_Rezept": "Private"}, "PrintPreviewSetting": {"prescribedDiga": "Prescribed DiGA", "caveHeader": "CAVE", "printSetting": "Print settings", "doctorLabel": "DOCTOR", "rezept": "Prescription", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez", "gRez": "G-Rez", "print": "Print", "printer": "Printer", "tray": "tray", "save": "Save", "private": "Private", "add": "Add", "ePrescription": "ePrescription", "printQR": "Print QR code for ePrescription", "prescribePartial": "Prescribe partial prescription", "partialPrescriptionTooltipContent": "Doctors can issue up to 4 partial prescriptions. The goal is to ensure long-term care for patients who need a continuous supply of a specific drug.", "startDate": "Start Date", "endDate": "End Date", "prescribeAll": "Prescribe All", "prescribe": "Prescribe", "specialInformation": "Special information", "btmSelectLabel": "Sonderkennzeichen", "warnMedicines": "Prescribing {{warnMedicines}} in size N1 again is not economically recommended.", "signBy": "Sign by", "signWith": "Sign With", "comfortSignatureStatus": {"one": "{{signatures}} comfort signature left, expires in {{count}} hour", "other": "{{signatures}} comfort signature left, expires in {{count}} hours"}, "hba": "HBA", "smcb": "SMC-B", "optional": "(Optional)", "invalidDateRange": "The end date must be the same as the start date or after the start date", "preview": "Preview", "medication": "Medication", "sprechsundenbedarf": "Sprechsundenbedarf", "quarter": "Quarter {{quarter}}/{{year}}", "vos": "VoS", "vosSubmitted": "VoS submitted"}, "MedicationPlan": {"textmoduleHint": "In freetext fields, type a Textmodule shortcut or press Strg + Space", "medicationPlan": "Medication plan", "substanceName": "Substance name", "prescribed": "Prescribed", "tradeName": "Trade name", "tradeNameColumn": "Trade name/Substance name", "form": "Form", "unit": "Unit", "hint": "Hint", "reason": "Reason", "modified": "Modified", "updateSuccess": "Medication plan has been updated", "mergedSuccess": "Medication plan merged.", "rearrangeIconDescription": "Click and hold to move", "medication": "Medication", "recipe": "Prescription", "freetext": "Freetext", "subheading": "Subheading", "titleAdditionalData": "Additional data", "addAdditionalData": "Add additional data", "editAdditionalData": "Edit additional data", "updateOutdatePzn": "Check the validity of medicines with Medication DB", "gender": "Gender", "pregnant": "Pregnant", "breastfeeding": "Breastfeeding", "allergies": "Allergies", "weight": "Weight", "height": "Height", "creatinine": "Creatinine", "additionalData": "Additional value", "scanMedicationPlan": "Scan medication plan", "printMedicationPlan": "Print medication plan", "addNewEntry": "Add new entry", "addNewRecipe": "Add new prescription", "addNewFreeText": "Add new freetext", "addNewSubheading": "Add new subheading", "addNewMedication": "Add new medication", "editRecipe": "Edit prescription", "editFreeText": "Edit freetext", "editSubheading": "Edit subheading", "editMedication": "Edit medication", "optional": "Optional", "additionalLine": "Additional line", "cancel": "Cancel", "save": "Save", "remove": "Remove", "notNow": "Not now", "saveAnyway": "Save anyway", "requiredField": "Required field", "confirmRemoveMessage": "Medication will be removed from medication plan.", "confirmEditMedicationMessage": "Update this medication with new info (trade name/ substance/ concentration) may cause conflicts later.", "refill": "Refill", "editDetails": "Edit details", "viewDetails": "View details", "secondaryInfo": "View secondary info", "removeEntry": "Remove an entry", "editAMedication": "Edit a medication", "drugUnit": "Drug unit", "drugForm": "Drug form", "intakeInterval": "Intake interval e.g. 0-0-0-0", "substance": "Substance", "concentration": "Concentration", "addSubstance": "Add", "showMoreSubstance": "Show more ({{number}})", "showLessSubstance": "Show less", "print": "Print", "doctor": "Doctor", "startScanTitle": "Scanning medication plan…", "startScanDescription": "Please align scanner to the 2D barcode and wait for scanning to complete", "stopScanning": "Stop scanning", "scanningTitle": "Scanned page {{scannedPages}} in {{totalPages}} pages", "scanningDescription": "This medication plan has {{totalPages}} pages, you can continue to scan or use the scanned page(s) to merge.", "useScannedPages": "Use scanned page(s)", "stopScanningAndUseScannedPage": "Stop scanning and use scanned page(s)", "scanNextPage": "<PERSON>an next page", "unmatchedPatient": "Patient mismatch", "scanAgain": "Continue", "newStatus": "New", "matchedStatus": "No change", "conflictingStatus": "Conflicting", "outDateStatus": "PZN not found", "duplicateSubstance": "Duplicate Substance", "unmatchedPatientMessage": "The medication plan you scanned belongs to:", "unmatchedPatientDescriptionForScanner": "Another possible match:", "unmatchedPatientDescriptionForCurrent": "Current patient profile:", "unmatchedPatientMessageNoExisted": "The medication plan you scanned belongs to:", "correctPatient": "Correct patient", "scanCompleteTitle": "Scanning complete", "scanCompleteDescription": "The medication plan you have scanned has no different information.", "backToMP": "Back to medication plan", "scanDuplicatedMessage": "This page has been scanned already!", "scanFailedMessage": "<PERSON><PERSON><PERSON> failed. Please try again.", "currentConflictDataTitle": "Conflicts from the previous medication plan", "mergeDescription": "Found {{new}} new and {{conflict}} conflicting entries. Select entries to merge:", "quitMerge": "Quit merging", "takeOver": "<PERSON><PERSON>", "notedMerge": "Checked medication will be merged to the new medication plan. Subheadings cannot be merged.", "mergeDialogTitle": "Merge medication plan of {{patientInfo}}", "currentMedicationPlan": "Current medication plan", "scannedMedicationPlan": "Scanned medication plan", "patientInformation": "Patient information", "conflict": "Conflict", "new": "New", "quitMergingTitle": "Quit merging", "quitMergingDescription": "You will lose your progress. This action cannot be undone.", "continueMerging": "No", "quitMerging": "Yes, quit", "printCompleted": "Print completed", "changePackageSize": "Change package size", "prescribeAgain": "Prescribe again", "add": "Add to refill", "changeConcentration": "Change Concentration", "tradeNameMerge": "Trade name", "strength": "Strength", "status": "Status", "timeout": "Timeout scanning", "markAsDoctorSample": "<PERSON> as doctor sample", "pznNotFound": "The PZN from the scanned medication plan could not be found in the drug database. Please check that the PZN is correct and up-to-date. You can add and change information after merging.", "shortage_of_patient_parameter": "Patient parameter has been shortened due to limited space. Please review again if adjustments need to be done.", "additionalDataHeader": {"allergy": "Allergy", "isShowGender": "Gender", "isPregnant": "Pregnant", "isBreastFeeding": "Breastfeeding", "weight": "Weight", "height": "Height", "creatinine": "Creatinine", "additionalValue": "Additional value", "field": "Additional data", "details": "Details"}, "syncVitalParameter": "Sync from vital parameter", "syncVitalParameterFail": "Failed to sync from vital parameter", "syncVitalParameterSuccessfully": "Successfully synced from vital parameter"}, "Medical": {"weight": "weight", "height": "Height", "allergy": "Allergy", "creatinine": "Creatinine", "gender": "Gender"}, "MedicationPlanRow": {"editDetails": "Edit details", "secondaryInfo": "Secondary info", "remove": "Remove", "removeMedicationHeader": "Remove a medication?", "youAreGoingToRemove": "You are going to remove", "fromMedicationPlan": "from medication plan.", "cancel": "Cancel", "medicationPlanUpdated": "Medication plan has been updated"}, "PrescribedMedication": {"prescribed": "PRESCRIBED", "tradeName": "TRADE NAME", "size": "SIZE", "furtherInfo": "FURTHER INFO", "prescribedBy": "PRESCRIBED BY", "mp": "MP", "addToRezept": "Add to prescription", "secondaryInfo": "Secondary info", "status": "Status", "date": "Date", "doctor": "Doctor"}, "EmptyPrescribeMedicationView": {"noPrescribed": "Nothing prescribed yet", "noPrescribeDescription": "Medications your practice have prescribed for this patient will appear here"}, "PrescribedMedicineRow": {"addToRezept": "Add to prescription", "secondaryInfo": "Secondary info", "addToMedicationPlan": "Add to medication plan", "removeFromMedicationPlan": "Remove from medication plan", "medicationPlanUpdated": "Medication plan has been updated", "removeMedicationHeader": "Remove a medication?", "youAreGoingToRemove": "You are going to remove", "fromMedicationPlan": "from medication plan.", "cancel": "Cancel", "remove": "Remove", "asNeeded": "As needed", "refill": "Refill", "viewForm": "View", "prescribeAgain": "Prescribe Again", "add": "Add to refill", "changePackageSize": "Change package size", "viewSecondaryInfo": "View secondary info", "changeConcentration": "Change Concentration", "printed": "Printed", "saved": "Saved"}, "RefillMedicationView": {"refillHeader": "Rationale Pharmakotherapie", "of": "of", "addedMedicineToRezept": "Added to prescription"}, "SubsitutionView": {"selectedMedication": "Der ausgewahlte Medikament ist", "substitution": "Substitution", "substitutionDescription": "Bitte prüfen Sie, ob der ausgewiesene Substitutionsvorschlag im konkreten Einzelfall, z.B. in Bezug auf Zulassungsindikationen, Wirkstärke und Darreichungsform, medizinisch umsetzbar ist."}, "WarningMissingDiagnoseDialog": {"missingDiagnose": "Missing Diagnose", "missingDiagnoseHint": "Die Medikation des Versicherten deutet auf eine bestimmte Krankheit bzw. Diagnose hin (z.B. Insulin auf Diabetes mellitus). In diesem Fall konnte in der Dokumentation keine passende Diagnose ermittelt werden. Bitte überprüfen Sie die Diagnosen und deren Kodierung.", "ingore": "Ignore", "selectAnotherMedication": "Select another medication"}, "RemoveShoppingBagDialog": {"replaceAll": "Yes, Replace", "headerReplaceAll": "Replace items in the prescription?", "replaceAllDescription": "Do you want to clear all previous medications from the prescription? Only the medications in the selected form will be added to prescription.", "keepIt": "No, cancel", "removedFromRezeptpool": "Yes, replace"}, "SearchResults": {"onlyShowFavouriteMedications": "Only show favorite medications", "onlyShowDiscountedProducts": "Only show discounted products", "noDiscountedProductsAvailable": "No discounted products available", "noFavouriteAvailable": "No favorite medications found. Please start medication search.", "onlyShowRegisteredProducts": "Only show registered products", "onlyMonoSearch": "Use Monosearch"}, "MedicationTable": {"colMedication": "Medication", "colSize": "Size", "colDosageForm": "Dosage Form", "colPrice": "Price", "colZuza": "<PERSON><PERSON>", "tooltipColZuza_1": "Calculation:", "tooltipColZuza_2": "If pharmacy price > fixed amount and this is < 50 EUR", "tooltipColZuza_3": "=> Co-payment = E EUR + pharmacy price - fixed amount", "tooltipColZuza_4": "If a confidential reimbursement amount is available, the co-payment is made in accordance with § 61 sentence 1 SGB V.", "colManufacturer": "Manufacturer", "tooltipLowerThanFixedAmount": "Lower than fixed amount", "tooltipHigherThanFixedAmount": "Higher than fixed amount", "tooltipAlternativeProductsAvaiable": "Alternative products with the same active ingredient are available for health insurers", "tooltipAV": "<PERSON><PERSON><PERSON>", "noDataMsg": "No results found", "prescribeFreetextMedication": "Prescribe freetext medication", "addToMedicationPlan": "Add to Medication Plan", "tooltipAMR": "AM-RL Anlage", "tooltipAMRIII": "AM-RL Anlage III", "tooltipAMRIV": "AM-RL Anlage IV", "tooltipAMRV": "AM-RL Anlage V", "tooltipP": "Priscus-Liste", "tooltipPOver65Age": "Priscus-Liste (für ältere Menschen potentiell ungeeignet)", "tooltipLifestyle": "Lifestyle drugs", "conditionalTooltipLifestyle": "Conditional lifestyle drug", "labelLifestyle": "Lifestyle drug", "labelConditionalTooltipLifestyle": "Conditional lifestyle drug", "tooltipGPA": "G-BA Beschluss §35a SGB V", "tooltipARV": "Regionale Arzneimittelvereinbarungen", "tooltipOTC": "Over-the-counter", "tooltipOTX": "O<PERSON>s, die verordnungsfähig sind", "tooltipPharmacy": "Apothekenpflichtig", "tooltipThumbdown": "Negativliste - unwirtschaftliches Präparat", "tooltipAM": "Doctor sample", "tooltipPrescriptionRegulations": "Prescription regulation", "tooltipPrescriptionExclusions": "Prescription exclusion", "tooltipTherapyHint": "Therapiehinweis der G-BA vorhanden", "tooltipImportProduct": "<PERSON>s handelt sich um ein Importprodukt", "tooltipTFG": "Dokumentationspflicht Transfusionsgesetz", "labelLifeStyle": "Lifestyle", "labelConditionalLifestyle": "Conditional lifestyle", "tooltipDrugGreen": "There is a discount agreement.", "tooltipDrugRed": "There are discounted product agreements acc §130a Absatz 8 SGB V available", "tooltipTRez": "T-rezept, are special prescriptions that may only be used to prescribe drugs containing the active ingredients lenalidomide, pomalidomide and thalidomide.", "tooltipKRez": "Kassenrezept", "tooltipGRez": "<PERSON><PERSON><PERSON><PERSON>", "tooltipBTM": "Betäubungsmittelrezept", "tooltipPrescriptionRequired": "Verschreibungspflichtig", "tooltipMedicineProduct": "Medicine product", "viewRedHandLetters": "View red hand letters", "viewBlueHandLetters": "View blue hand letters", "tooltipBandage": "Prescription bandage according to §31 (1a) SGB V", "isFavourite": "Favorite", "tooltipMedicationSortAsc": "(↑) Sorting by KBV rules", "tooltipMedicationSortDesc": "(↓) Ascending sort by trade name. Descending sort by trade name is currently not supported."}, "HandLettersDialog": {"titleRedHand": "Red Hand Letters", "titleBlueHand": "Blue Hand Letters"}, "PriceComparision": {"hint": "Please check if there are available substitutions to prescribe in case it matches with indications, concentration, dosage form.", "hintForSpecificMedications": "For your selected package size there are no substitutions available. The substitutions will be shown for overall available package sizes.", "title": "Price comparison for „{{tradeName}} {{size}} {{price}}“", "lblOnlyDiscount": "Only show discounted products", "lblMedicineOriginal": "Der ausgewahlte Medikament ist:", "lblMedicineComparison": "Only show discounted products", "txtNoDiscound": "No discounted products available"}, "Refill": {"title": "Select medication for refill", "lblBack": "Back", "lblNext": "Continue", "lblFinish": "Go to print preview", "lbInfoText": "There might be cheaper alternatives for these medications. Select which medication you want to refill.", "lbSVInfoText": "Please check whether it is possible to continue with the proposed substitution for this specific case, e.g. if they are the same dosage form, concentration, etc.", "lbPreviouslyPrescribed": "The previously prescribed medication", "lbCheaperMedications": "Cheaper medications", "lbOtherMedications": "Other medications", "lblCancel": "Cancel", "lblPrescribe": "Prescribe"}, "Alternatives": {"modalTitle": "View Alternative", "modalDescription": "There are multiple alternatives view available for this medication.", "modalLblView": "View", "modalLblViewPlaceholder": "Select", "modalCptCancel": "Cancel", "modalCptContinue": "Continue", "title": "Alternativen für „{{tradeName}} {{size}} {{price}}“", "lblMedicineOriginal": "Der ausgewahlte Medikament ist:", "lblMedicineAlternatives": "{{name}} medications alternatives:", "lblMedicineAlternativesDescription": "Please check whether it is possible to continue with the proposed substitution for this specific case, e.g. if they are the same dosage form, concentration, etc.", "lblFilter": "Filter", "titleFilterDialog": "Filter", "lblFilterDialogReset": "Reset to default", "lblFilterDialogApply": "Apply", "lblFilterDialogConcentration": "Concentration", "lblFilterDialogSize": "Size", "lblFilterDialogDosageForm": "Dosage form", "placeholderFilterDialogMinimum": "Minimum", "placeholderFilterDialogMaximum": "Maximum", "placeholderFilterDialogSelectOptions": "Select options", "placeholderFilterDialogSelect": "Select", "filterApplied": "Filter applied"}, "SecondaryView": {"shortcut_keys_secondary_view": "Alt + ↑↓ to navigate; ↑↓  to scroll content;  Strg + R to add to prescription; Strg + M to add to MP", "btnAddRP": "Add to prescription", "btnAddMP": "+ MP"}, "EditDrugName": {"editDrugName": "Edit drug name", "description": "By editing the drug name below, the existing PZN will be removed from prescription. Do you want to continue?  ", "drugName": "Drug name", "save": "Save anyway", "cancel": "Cancel", "editDrugNameSuccess": "Medication successfully edited"}, "GBADialog": {"title": "G-BA decision", "search": "Search", "standardTooltip": "Standard (green)", "reserveDrugTooltip": "Reserve drug (yellow)", "notRecommendedTooltip": "not recommended (red)", "noIndicationTooltip": "(only KV Bremen) (gray) no indication"}, "IWWRegulations": {"title": "IWW regulations", "search": "Search", "validityFromTo": "Validity: {{fromDate}} to {{toDate}}", "validityFrom": "Validity: from {{fromDate}}", "createdOn": "Created on: {{date}}", "titleCollapse": "Medicines Directive", "anhang": "<PERSON><PERSON>", "arzneimittelvereinbarung": "Arzneimittelvereinbarung - {{title}}"}, "MedicationStatistics": {"title": "Medication Statistics", "patientPrescriptions": "Patient prescriptions", "consultationPrescriptions": "Consultation Prescriptions"}, "PatientPrescriptions": {"medicationStatistics": "Medication Statistics", "filters": "Filters", "export": "Export", "dateOfPrescription": "Date of prescription", "prescribingDoctor": "Prescribing Doctor", "patientReference": "Patient Reference", "insuranceNo": "Insurance no.", "ATCCode": "ATC Code", "amount": "Amount", "tradeName": "Trade Name", "activeSubstance": "Active Substance", "PZN": "PZN", "price": "Price", "filtersFor": "Filters for", "patients": "Patients", "gotoPatientProfile": "Go to patient profile", "scheinType": "Schein type", "kv": "KV", "hzvOrFav": "SV (HzV or FaV)", "privateOrIgel": "Private (Private or IGeL)"}, "Common": {"cannotOpenSecondaryInfo": "Cannot open secondary Info", "outDateStatus": "PZN not found", "outDateStatusDesciption": "There is no information regarding the PZN in the current medication database. Missing data can be added or replaced after adding.", "titleCannotPrintBmpCauseSubHeading": "Cannot print BMP", "cannotPrintBmpCauseSubHeading": "Medication plan cannot be generated. Subheadings without any entry assignment are not allowed.\nPlease remove or assign entries to the heading."}, "PatientInformationModal": {"title": "Patient information", "emptyData": "No patient information available", "description": "Return to patient profile to input information"}, "GeneralGBA": {"AM-RL": "AM-RL", "title": "Arzneimittel-Richtlinie"}}