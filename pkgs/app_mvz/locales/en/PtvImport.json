{"title": "PTV Import", "PtvImportListContract": {"ptvImportContractHeader": "New data import", "ptvImportContractDescriptionRemark": "Hint", "ptvImportContractDescriptionDetail1": "Sie können die Funktion zum Import der  Patiententeilnahmeinformationen aus Ihren Informationsbriefen Patiententeilnahmestatus (kurz: PTV-Import) mithilfe des ICodes erst nutzen, wenn Sie erfolgreich im Arztportal registriert sind und dort Ihre vertraulichen Dokumente (d.h. Abrechnungsnachweis und Informationsbrief Patiententeilnahmestatus) herunterladen. Sollten Sie noch nicht im Arztportal registriert sein, so holen dies jetzt unter ", "ptvImportContractDescriptionDetailLink": "arztportal.net", "ptvImportContractDescriptionDetail2": "nach und nutzen Sie ab sofort die Vorteile des PTV-Importes.", "step1": "Import for", "doctor": "Doctor", "doctorPlaceHolder": "Arzt auswählen", "icode": "icode", "verifyDoctor": "Request", "upload": "Upload", "uploadSuccess": "Data uploaded", "uploadFailed": "Failed to upload. Please try again.", "uploadOlderErrorTitle": "Cannot import older data", "uploadOlderErrorDescription": "You have already imported PTV data for more recent quarter. You cannot import data for previous quarters.", "closeBtn": "OK, close", "dataRequested": "Data requested", "dataRequestFailed": "Failed to request. Please try again.", "iCodeOutDated": "ICode is outdated. Please enter the new ICode.", "step2": "Select", "descriptionOfListContract": "Bitte wähle für den Import eine PTV Datel aus", "contractId": "vertrag", "quarterTime": "quartal", "versionDocument": "version", "status": "Status", "tooltipImported": "Already imported", "importBtn": "Import", "importedLabel": "Imported", "notYetImportLabel": "New", "importingLabel": "Importing", "pendingLabel": "Pending", "noResultFound": "No result found", "ConfirmDialog": {"title": "Replace imported file?", "description": "The selected file has already been imported. Do you want to replace it? This action cannot be undone.", "cancelBtn": "No", "confirmBtn": "Yes, replace"}}, "PtvImportConfirmImportContract": {"titleWarning": "<PERSON><PERSON><PERSON>(en)", "descriptionWarning": "wurde bereits schon importiert. Möchtest du die zuvor importierte Datei ersetzen?", "yesWarning": "<PERSON>a", "noWarning": "No"}, "PtvImportList": {"downloaded": "Download PDF", "import": "Import", "LANR": "LANR: {{value}}", "vpID": "HÄVG VP-ID: {{value}}", "contractId": "vertrag", "doctorName": "arzt", "quarterTime": "zeitraum", "status": "Status", "importerName": "import<PERSON>t durch", "importerTime": "importiert am", "importNew": "New", "importSuccess": "Imported", "importInProgress": "Uploading", "importPending": "Pending", "patientTotal": "patient", "importProtocol": "Import-Protokoll", "cantImport": "Cannot import old PTV", "testRunReport": "Test run report", "noResultFound": "No data imported yet"}, "PtvImportProtocol": {"importProtocolTitle": "Import Protocol", "contractId": "Contract", "doctor": "Doctor:", "doctorInfo": "{{doctor<PERSON><PERSON>}} (LANR: {{doctor<PERSON>anr}}, HÄVG VP-ID: {{doctorHavgVpId}})", "contract": "Contract:", "quarterTime": "Quarter:", "importInfo": "Import By:", "importDate": "Import Start Date:", "importDateFormat": "{{date}} at {{time}}", "totalAmounted": "total amount:", "patients": "{{total}} patient(s)", "updateAll": "Update all", "autoImportTag": "Normal participations", "total": "total", "noResultFound": "No patient", "firstName": "first name", "firstNamePtv": "ptv vorname", "firstNameIv": "iv vorname", "lastName": "last name", "lastNamePtv": "ptv nachname", "lastNameIv": "iv nachname", "dob": "date of birth", "dobPtv": "ptv geburtsdatum", "dobIv": "iv geburtsdatum", "insuranceNumber": "Insurance no.", "insuranceNumberPtv": "ptv vers.nr.", "insuranceNumberIv": "iv vers.nr.", "status": "STATUS garrioPRO", "statusPtv": "STATUS PTV", "statusIv": "STATUS garrioPRO", "beginDateContract": "start date", "beginDateContractPtv": "ptv beginndatum", "beginDateContractIv": "iv beginndatum", "endDateContract": "end date", "endDateContractPtv": "ptv enddatum", "endDateContractIv": "iv enddatum", "requestDate": "request date", "rejectDate": "termination date", "beginDate": "Start", "endDate": "End", "ikNumber": "Kassen-IK", "ikNumberPtv": "PTV Kassen-IK", "ikNumberIv": "IV Kassen-IK", "reason": "termination reason", "note": "Note", "hint": "Hint", "conflictImportTag": "Special contract participations", "conflictDescription": "Folgende HZV-Teilnehmer konnten nicht automatisch in Ihrer Praxissoftware aktualisiert werden! Bitte prüfen Sie diese Fälle und führen Sie die Aktualisierungen der HZV-Teilnahmen manuell durch. Verwenden Sie stets den aktuellen Infobrief, um eine korrekte Abrechnung sicherzustellen. Wenn Si<PERSON> hierzu Rückfragen haben, steht Ihnen unter der Telefonnummer 02203/5756-1111 der HÄVG-Kundenservice gerne zur Verfügung.", "conflictFooter": "Unterschiedliche Werte in der Praxissoftware und in der Patiententeilnehmerverzeichnis (PTV) für {{quarterTime}} werden fett dargestellt", "resolveConflictBtn": "Resolve conflict", "conflictResolved": "Review", "actions": "Actions", "keepExistVersion": "bestehende patienteninformation behalten", "updateTo": "aktualisieren zu", "saveBtn": "Speichem", "missingImportTag": "Patients not found", "importAll": "Importiere alle", "assignPatient": "Assign patient", "assignTo": "Assigned to {{patientName}}", "assignPatientDialog": {"title": "Assign imported data to", "selectPatient": "Select patient", "selectPatientRequired": "This field is required", "noResultFound": "No results found", "createNewPatient": "Create new patient", "patientAssigned": "Patient assigned", "dob": "Date of birth:", "insuranceNumber": "Insurance Number:", "insuranceInfo": "Insurance Info:", "ik": "IK"}, "proccessing": "Importing PTV", "missingDescription": "„Nicht gefundene Patienten können beim Import nicht eindeutig identifiziert werden. Wurde ein Patient nicht eindeutig identifiziert, so stimmen in der Regel die Versichertennummer und das Geburtsdatum eines Patienten in Ihrer Praxissoftware und im Patiententeilnehmerverzeichnis nicht überein oder die Teilnahmeanfrage ist noch ausstehend. Die Teilnahmeinformationen für nicht gefundene Patienten müssen deshalb manuell bearbeitet werden.“", "missingPtvDescription": "Folgende Patienten ({{participantNo}}) sind in der Praxissoftware: enthalten, konnten aber nicht in der Teilnehmerdatei gefunden werden", "downloadPdf": "Download PDF", "zeroPatient": "0 patient"}, "PtvImportDryRun": {"title": "Test run report", "titleBasicImport": "Import protocol: Basic import", "titleFullImport": "Full Import", "contractId": "Contract", "doctor": "Doctor:", "doctorInfo": "{{doctorName}} (LANR: {{doctor<PERSON>anr}}, HAVG VP ID: {{doctorHavgVpId}})", "contract": "Contract:", "quarterTime": "Quarter:", "importInfo": "Import By:", "importDate": "Import Start Date:", "titleNormalList": "A. Normal participations", "description": "Description", "numberPatient": "Patient Count", "unchangedCase": "Unchanged participation", "newCase": "Newly reported participation", "terminatedCase": "Newly terminated participation", "requestedCase": "Participations currently under consideration", "rejectedCase": "Rejected participations", "titleSpecialList": "B. Special contract participations", "specialCase": "Number of special participations", "titleNotFoundList": "<PERSON><PERSON> not found", "titleSectionD": "D. Projected participants: before and after standard import", "additionalInfo1": "Special participations are, for example, cancellations, retroactive registrations, retroactive terminations.", "additionalInfo2": "The data for special participations and not found patients are not automatically imported into the practice software during the standard import. You can choose to perform a full import after the test run to automatically import the participation data for special participations and uniquely identified not found patients into the practice software. Otherwise, you must check the data for these patients after the import and manually enter them into the practice software. The data for special participations and not found patients are listed in detail in the import protocol after the standard import.", "additionalInfo3": "Not found patients cannot be uniquely identified (Stammdaten) and therefore cannot be automatically imported in general. With the full import, manual follow-ups can be reduced.", "missingInHPM": "Patients in practice software (status: requested or activated) missing from participant directory.", "missingInPractice": "Patients in patient directory missing from practice software.", "beforeImport": "Before import", "afterImport": "After import", "downloadPdf": "Download report", "import": "Import", "backStep": "Back", "nextStep": "Next", "updateAll": "Update for all", "ignoreConflictsAndUpdateAll": "Ignore conflicts and update for all", "confirmImportDialog": {"title": "Import PTV data?", "description": "Patient participation statuses will be updated accordingly. Confirm to import PTV data?", "cancelBtn": "Cancel", "confirmBtn": "Yes, import", "titleIgnoreConflict": "Ignore unresolved conflicts?", "descriptionIgnoreConflict": "Are you sure you want to ignore all patients with unresolved conflicts?", "cancelBtnIgnoreConflict": "Cancel", "confirmBtnIgnoreConflict": "Yes, ignore", "titleImportType": "Basic or Full Import?", "descriptionImportType": "Basic import only updates the status of the patients with conflicts or missing records. Full import updates the status of all patients.", "confirmBtnImportTypeBasic": "Basic Import", "confirmBtnImportTypeFull": "Full Import"}, "dataImported": "Data imported for {{contract}}", "dataImportFailed": "Failed to import data. Please try again.", "totalAmountContractAfterImport": "Total amount of contract {{contract}} after the import: {{count}}", "fullImportDescription": "„Diese Patienten konnten beim Import nicht eindeutig identifiziert werden. Wurde ein Patient nicht eindeutig identifiziert, so ist dieser in der Regel nicht den vorhandenen Stammdaten in Ihrer Praxissoftware zuordenbar. Die Teilnahmeinformationen müssen deshalb manuell bearbeitet werden.“"}, "PtvImportResolve": {"title": "Resolve conflict: {{patient<PERSON>ame}}", "note": "Inconsistent data found", "description": "Data mismatch: Patient data in garrioPRO does not match imported PTV data. Please select data to sync for this patient.", "firstName": "First Name", "lastName": "Last Name", "dob": "Date of birth", "status": "Status", "activeStatus": "Active", "requestedStatus": "Requested", "terminatedStatus": "Terminated", "rejectedStatus": "Rejected", "custodianType": "<PERSON><PERSON><PERSON><PERSON>", "deputyType": "Deputy", "beginDateContract": "Start Date", "contractEndDate": "End Date", "insuranceNumber": "Insurance Number", "insurance": "Insurance", "ikNumber": "IK: {{ikN<PERSON>ber}}", "gender": "Gender", "keepCurrent": "GARRIOPRO", "updateTo": " PTV IMPORTED", "selectAll": "Select all", "reason": "Begründung", "save": "Save selection", "treatmentType": "Treatment Type"}}