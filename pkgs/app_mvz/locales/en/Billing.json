{"Billing": {"Header": "SV Billing", "UnBillTitle": "Open billing", "SubmissionProtocolsTitle": "HzV submission protocols", "BillingHistoryTile": "Billing history", "ev": "replacement procedure (EV)"}, "SubmissionProtocols": {"timePeriod": "TIME PERIOD", "contractName": "CONTRACT NAME", "doctor": "DOCTOR", "submittedBy": "SUBMITTED BY", "submittedTime": "SUBMITTED TIME", "downloadFile": "Download file", "close": "Close", "shippingLog": "Preview", "downloadProtocolsZip": "Download (ZIP)", "saveAsPdf": "Save as pdf", "hzv": "HzV", "fav": "FaV", "transferId": "Transfer ID", "billingSubmitted_title": "Billing has been successfully submitted", "billingSubmitted_description": "Die Abrechnung wurde erfolgreich durchgeführt. Eine genaue Auflistung über die in der Abrechnung übertragenen Ziffern finden Sie im Versandprotokoll.", "billingSubmitted_billingProtocol": "Billing protocol", "billingSubmitted_confirmButton": "OK", "billingType": "Type", "BillingHistoryType_Prescription": "Prescription", "BillingHistoryType_Document": "Document", "BillingHistoryType_Offline": "Offline"}, "UnBilled": {"drTitle": "Dr.", "selectBilling": "Billing selection", "noThingToBillYet": "Nothing to bill yet", "yourBillingWillAppearHere": "Your billing data will appear here as soon as documentation is available in patient file.", "time": "Time", "deselectAll": "Deselect all", "timeBillingDes": "These are the quarters with open billing", "doctorLabel": "Doctor", "doctors": "doctors", "allDoctorLabel": "These are the doctors in your practice", "contract": "contract", "contracts": "contracts", "contractDes": "These are the contracts the selected doctors participate in", "submitPrescriptionData": "Submit prescription data", "filterP4Service": "Enable P4 validation list", "btnStartTroubleShooting": "Start troubleshooting", "allTime": "All time", "allDoctors": "All doctor", "allContracts": "All contracts", "selectAll": "Select all", "notSelectBilling": "Nothing selected yet. Please select something below to continue.", "patientIdHeader": "PATIENT ID", "nameHeader": "NAME", "dateOfBirthHeader": "DATE OF BIRTH", "doctorHeader": "DOCTOR", "contractHeader": "CONTRACT", "descriptionHeader": "ERROR DESCRIPTION", "statusHeader": "STATUS", "noError": "No error", "warning": "warning", "warnings": "warnings", "errors": "errors", "errorList": "Validation list", "showError": "Show errors", "showWarning": "Hints", "errorContinueDes": "Patients with ignored errors will not be included in the generated con file", "saveAsPdf": "Save as pdf", "print": "Print", "IgnoreAndContinue": "Ignore and continue", "congrasNoError": "Congratulations! There are no errors to fix", "noDocumentSubmit": "No documents has been submitted", "continue": "Continue", "checkForValidation": "Check for validation", "billingSummary": "Billing summary", "submitBilling": "Submit billing", "submitAllBilling": "Submit all billing data", "submitPrescriptionOnly": "Submit prescription only", "createBilling": "Create billing", "saveAsExcel": "Save as Excel", "summaryWarningExcludedErr": "All errors will not be included in billing submission", "totalAmountOfServiceCodes": "Total amount of service codes", "totalAmountOfDiagnoseCodes": "Total amount of diagnose codes", "totalAmountOfMultimorbidPatients": "Total amount of multimorbid patients", "totalAmountOfPatients": "Total amount of patients", "serviceCode": "Service code", "diagnoseCode": "Diagnosis code", "multimorbidPatient": "Multimorbid patient", "patient": "Patient", "patients": "patients", "HZV": "HzV", "FAV": "FaV", "all": "All", "hint": "hint", "hints": "hints", "hintProtocol": "Hint protocol", "noHint": "No hint", "congrasNoHint": "There is no hint", "downloadAllHZVProtocols": "Download all HzV protocols", "printAllProtocol": "Print all protocol", "successfulSubmission": "Successful submission", "submittionSuccessDes1": "Die Abrechnung wurde erfolgreich durchgeführt.", "submittionSuccessDes2": "Eine genaue Auflistung über die in der Abrechnung übertragenen Ziffern finden Sie im Versandprotokoll.", "submittionFailedDes1": "Something went wrong. We could not submit everything.", "submittionFailedDes2": "Please try to submit again in a few minutes.", "submissionStatus": "Submission status", "status": "status", "diagnose": "Diagnose", "service": "Service", "failedSubmission": "Failed submission", "pleaseTryInFiewMinutes": "Please try to submit again in a few minutes.", "showAll": "Show all", "more": "more", "confirmation": "Confirmation", "confirmationDes": "You are about to submit billing to the data centre. It cannot be retrieved once submitted. Please review your submission carefully before submitting it.", "cancel": "Cancel", "submit": "Submit", "reSubmit": "Re-submit", "submitted": "Submitted", "error": "Error", "fixValidation": "Fix validation", "warningNotBilling": "Die abzurechnenden Leistungen liegen außerhalb der vertraglichen Nachreichfrist. Eine Vergütung dieser Leistungen ist dadurch im Regelfall nicht mehr möglich.", "leavePageTitle": "Leave without saving?", "leavePageContent": "Billing process is not complete. We will not be able to resume your progress if you leave this page.", "confirmLeave": "Yes, leave", "cancelLeave": "No, stay here", "multimorbidPatientDialogTitle": "Patientenliste Multimorbiditätszuschlagsberechtigung", "multibidPatientDes": "Diese Auswertung bezieht sich ausschließlich auf die aktuell noch nicht abgerechneten Leistungen von Patienten. Eine mehrmalige Durchführung dieser Auswertung ist möglich, bereits abgerechnete und übermittelte Patienten können in der Auswertung jedoch nicht mehr berücksichtigt werden. Die Auswertung beinhaltet keinen Anspruch auf eine tatsächliche Vergütung - diese erfolgt im Rechenzentrum auf Basis aller für die Abrechnung übermittelten Patienten und deren Diagnosen", "greenDotDes": "Service code 56544 was documented one time", "blueDotDes": "Service code 56544 was documented two times", "createdOn": "Created on", "sicknessPicturesHeader": "KRANNKHEITSBILDER", "p4Service": "P4-Le<PERSON><PERSON>", "gotoSubmissionProtocols": "Go to submission protocols", "failedSubmissionDes": "Something went wrong. Some items could not be submitted.", "failedSubmissionDes2": "Please try to submit again in a few minutes.", "gotoBillingHistory": "Go to billing history", "submitAgain": "Submit again", "checkingConnection": "Checking connection...", "refreshThePage": "refresh the page", "youAreOffline": "You are offline. Please check your internet connection and reload this page.", "hpmConnectionNotAvailiable": "HPM connection is not available. To ensure you can submit billing, please make sure you are connected to HPM.", "timePeriod": "Time period", "group": "Group", "contractName": "Contract name", "doctor": "Doctor", "prescription": "Prescription", "submittingBilling": "Submitting billing packages...", "hzvBilingSubmitted": "MEDI billing submitted", "hzvBilingCreated": "SV billing has been created successfully and can now be downloaded to generate the billing file/CD.", "failedToSubmitBilling": "Failed to submit MEDI billing", "success": "Success", "fail": "Fail", "downloadFile": "Download file", "close": "Close", "preview": "Preview", "hzvBillingPackgaesSubmittedSuccessfully": "HzV billing packages submitted", "viewAllHZVProtocols": "View all HzV protocols", "downloadCDImage": "Download CD Image", "failedToSubmitHzvBilling": "Failed to submit HzV billing", "submitNewBilling": "Submit new billing", "hzvBillingPackagesAgain": "HzV billing packages again", "encounter": "Encounter", "billedPatient": "Billed patient", "type": "TYPE", "deputy": "Deputy", "custodian": "<PERSON><PERSON><PERSON><PERSON>", "editBillingSelection": "Edit billing selection", "editErrorList": "Edit error list", "editHintProtocol": "Edit hint protocol", "selectATimeOnTheLeft": "Select a time on the left", "selectADoctorOnTheLeft": "Select a doctor on the left", "nextPatient": "Next patient", "fixThisError": "Fix this error", "fixThisHint": "Fix this hint", "billingSubmission": "billing submission", "titleBillingSubmissionErrors": "Billing submission errors", "timeline": "timeline", "viewErrorDetails": "view error details", "fixRemainingErrors": "Fix remaining errors?", "descFixRemainingErrors": "This patient still has errors. Errors that have not been fixed will affect your billing submission.", "ignore": "Ignore", "yesFixIt": "Yes, fix it", "fixBillingSubmissionErrors": "Fix billing submission errors", "fixedAllHpmError": "Billing submission errors fixed", "failedToFixBillingSubmissionError": "Failed to fix billing submission errors", "submitionSuccessDes": "Pending billing items", "downloadAllProtocol": "Download protocol", "Quarter": "Quarter", "Group": "Group", "BilledPatient": "Billed Patient", "Encounter": "Encounter", "Diagnose": "Diagnose", "Service": "Service", "Prescription": "Prescription", "ContractName": "CONTRACT NAME", "OPS": "OPS", "successfullySubmitted": "Submitted", "total": "TOTAL", "p4Title": "View multimorbid patient list", "noData": "There is no data to display", "documentHeader": "Document", "documentCreatedByAndAt": "Document created by {{createdBy}} at {{createdAt}}", "roleHeader": "Role", "unknown": "Unknown"}, "KvBilling": {"header": "KV Billing", "billingSelection": "Billing Selection", "checkFor": "Check for", "errors": "errors", "warnings": "warnings", "codingRules": "coding rules", "hint": "hints", "hints": "hints", "error": "error", "warning": "warning", "codingRule": "coding rule", "selectPratice": "Please select practices. Your selections below will be displayed here.", "quarterWithOpenBilling": "QUARTERS WITH OPEN BILLING", "practice": "PRACTICE", "validation": "VALIDATION", "settings": "SETTINGS", "Errors": "Errors", "Warnings": "Warnings", "CodingRules": "Coding Rules", "Hint": "Hints", "Hints": "Hints", "startTroubleshooting": "Start Troubleshooting", "validationList": "Validation list", "information": "information", "from": "from", "outOf": "out of", "patients": "patients", "ignoreError": "Patients with ignored errors will not be included in the generated con file", "editBillingSelection": "Edit billing selection", "ignoreAndContinue": "Ignore & Continue", "checkForValidation": "Check for validation", "theFileReadyForDownload": "These files are ready for download", "allErrorIncludeIntoConfile": "Patients with ignored errors will not be included in the generated con file", "editValidationList": "Edit validation list", "downloadEncryptedConFile": "Download encrypted con file", "schein": "schein", "doctor": "doctor", "insurance": "insurance", "markedAsFixed": "Edit", "isShowError": "Show errors", "isShowWarning": "Warnings", "isShowCodingRules": "Show coding rules", "isShowHints": "Hints", "isShowServiceCodes": "Show service codes eligible for HÄVG Rechenzentrum", "totalScheins": "{{totalSchein}} scheins from {{totalPatients}} patients", "totalScheinsMark": "Mark {{totalSchein}} scheins as billed", "billingSummary": "Billing summary", "CollectingData": "Collecting Data", "CollectingDataDone": "Collecting Data Done", "BuildingConFile": "Building con file", "BuildingConFileDone": "Building con file done", "XPMProcessing": "XPM Processing", "XPMProcessingDone": "XPM Processing Done", "UploadingData": "Uploading Data", "UploadingDataDone": "Uploading Data Done", "ExtractingError": "Extracting Error", "ExtractingErrorDone": "Extracting Error Done", "ReturningData": "Returning Data", "ReturningDataDone": "Returning Data Done", "Done": "Done", "sendKvConnect": "Send via KV-Connect", "OpenBilling": "Open billing", "BillingHistory": "Billing history", "generalError": "General <PERSON><PERSON><PERSON>", "otherTiApplications": "Other TI applications", "ePAStufe1": "ePA Stufe 1", "eRezept": "ePrescription", "epa": "ePA", "none": "None", "ePAStufe2": "ePA Stufe 2", "nfdm": "NFDM", "emp": "E-MP", "kim": "KIM", "eau": "eAU", "eartzbrief": "eDoctor Letter", "kartenterminal": "Kartenterminal", "smcb": "SMC-B", "ehba": "eHBA", "contactAdmin": "Please contact system support or log in to the admin app to edit.", "tiApplications": "TI applications", "billOpenScheinFromPreQuarter": "Bill open schein from previous quarter", "autoBillingDescription": "Transfer all documented service codes (86900 and 86901) to billing", "viewList": "View list", "autoBillingEdoctorLetter": "Auto-billing for eDoctor Letter", "patient": "Patient", "autoBilleDoctorLetter": "Auto-bill eDoctor Letter", "quarter": "Quarter {{quarter}}", "age": "<PERSON>.", "month": "Month", "day": "Tag.", "tooltipCodeEabSending": "eArztbrief-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, je versendeten eArztbrief/ Richtlinie Elektronischer Brief gemäß § 383 SGB V sowie Anlage 8 der Anlage 32 BMV-Ä", "tooltipCodeEabReceiving": "eArztbief-Empfangspauschale, je empfangenen eArztbrief/ Richtlinie Elektronischer Brief gemäß § 383 SGB V sowie Anlage 8 der Anlage 32 BMV-Ä", "ePAStufe3": "ePA Stufe 3"}, "PatientValidation": {"fixValidationFor": "Fix validation for", "countOfPatients": "{{ currentPatientIndex }} of {{ lengthPatient }} patients", "validationList": "Validation List", "infoValidation": "To view updated list, go back and check for validation.", "countOfErrors": "{{ countOfErrors }} errors", "countOfWarnings": "{{ countOfWarnings }} warnings", "countOfCodingRules": "{{count}} suggestion for this patient", "countsOfCodingRules": "{{count}} suggestions for this patient", "countsOfHints": "{{count}} hints for this patient", "titleTimeLine": "Timeline {{count}}", "titlePatientInfor": "Patient Information {{count}}", "entryDate": "Entry date: {{entryDate}}"}, "1ClickBilling": {"sendViaKim": "Send via KIM", "sendViaKvSubmit": "Send via KV-Connect", "sendViaKimSubmit": "Send via KIM", "titleModal": "Send via KV-Connect", "cancelSend": "Cancel", "Send": "Send", "Quarter": "Quarter", "Practice": "Practice", "KIMAccount": "KIM Account", "TypeOfBilling": "Type of billing", "testMailTo": "Test mail to", "testBody": "Test body", "MarkComplete": "<PERSON> as complete billing", "sentSuccess": "Billing sent", "noData": "No Data", "timePeriod": "Time period", "type": "Type", "cases": "Cases", "checkModuleStatus": "Check Module Status", "encryptStatus": "Encryption status", "1ClickStatus": "1-Click status", "kvConnectUserName": "Username", "submittedTime": "Submitted time", "submittedBy": "SUBMITTED by", "onlyPartialBilling": "Only show partial billing", "markNotbill": "<PERSON> scheins as unbilled", "markBill": "<PERSON> s<PERSON> as billed", "sendBill": "Send correction billing", "ResendViaKIM": "Send via KIM", "missingKvConnect": "KV-Connect account is missing", "ResendViaKVConnect": "Send via KV-Connect", "retry": "Retry", "downloadBill": "Download billing file", "totalScheinsMarkTitle": "Mark {{totalSchein}} scheins as unbilled?", "totalScheinsMarkContent": "{{totalSchein}} scheins and its content will be marked as unbilled. This action cannot be undone.", "SendRealBilling": "Send as real billing", "SendTestBilling": "Send as test billing", "billSent": "Billing sent", "ok": "Ok", "Sent": "<PERSON><PERSON>", "partial": "partial", "Sending": "Sending", "Confirmed": "Confirmed", "Feedback_Available": "Feedback Available", "Empty": "Empty", "Timeout_ReceiveEmail": "no feedback", "Protocol": "Protocol", "Sending_Failed": "Sending Failed", "ReceiveEmail_Failed": "E-mail cannot be received", "incorrect": "Incorrect", "warning": "Warning", "aborted": "Aborted", "pending": "Pending", "success": "Success", "failed": "Failed", "realBilling": "Real billing", "testBilling": "Test billing", "correctionBilling": "Correction Billing", "confileDownloaded": "Con file downloaded", "downloadFiles": "Download billing files", "downloadFileContent1": "These files are ready for download: ", "onClickError": "1-<PERSON><PERSON> error", "searchBSNR": "Search BSNR", "mailTypeKv": "KV-Connect", "mailTypeKim": "KIM", "ErrorCode_Real_Billing_Already_Sent": "The real billing of BSNR in this quarter was already sent", "ErrorCode_Cannot_Correct_Billing_Before_Real_Billing": "Correction billing cannot be sent before real billing", "onSubmitted": "On {{date}}", "notSupportKv": "The KV does not support the receipt of 1-Click-Billing as real billing", "billOpenScheinFromPreQuarter": "Bill open schein from previous quarter", "tooltipContentNoFeedback": "Please contact your KV for consultation"}, "DmpBilling": {"header": "DMP Billing", "openBilling": "Open Billing", "billingHistory": "Billing history", "stepBillingSelect": "Billing selection", "stepValidateList": "Validation list", "stepDataCheck": "Billing data check", "stepSummary": "Billing summary", "titleStepValidate": "{{ed}} ED, {{fd}} FD from {{patientNumbers}} patients", "quarter": "Quarter", "doctor": "doctor", "patient": "patient", "insuranceName": "Insurance name", "dmpProgramme": "Disease Management Programs", "dmpDocumentation": "DMP Documentation", "dataCenter": "data centre", "troubleshooting": "Start troubleshooting", "dmpCaseNumber": "DMP Case number", "checkDMPDocumentation": "Check for DMP documentation", "pleaseSelectDMP": "Please select a DMP", "checkValidation": "Check for validation", "finishAll": "Finish all", "editBillingSelection": "Edit billing selection", "editValidationList": "Edit validation list", "complete": "Complete", "abrechnungRuckgangig": "Undo billing", "uberKVConnectVersenden": "Send via KV-Connect", "undoBillingSubmissionTitle": "Undo billing submission?", "undoBillingSubmissionLine1": "wollen sie diese DMP-abrechnung rückgänig machen?", "undoBillingSubmissionLine2": "Achtung: <PERSON><PERSON><PERSON>, die zwischenzeitlich verändert wurden, sind davon nicht betroffen!", "undoBillingSubmissionYes": "Yes, undo", "undoBillingSubmissionNo": "No", "downloadFileContent": "These files are ready for download and have already been sent to the data centre:", "undoSubmitBilling": "Undo billing submission?", "undoBillingSubmissionContent": "wollen sie diese DMP-abrechnung rückgänig machen?\n\nAchtung: <PERSON><PERSON><PERSON>, die zwischenzeitlich verändert wurden, sind davon nicht betroffen!", "download": "Download", "timePeriod": "Time period", "submittedBy": "Submitted by", "submittedTime": "Submitted time", "downloadFiles": "Download billing files", "status": "Status", "transferLetter": {"title": "Transfer letter", "description": "The media used must be labeled with stickers or labeled directly. The corresponding labels must contain the following information:\n • Sender ((N)BSNR of the place of activity where the file was created or hospital IK)\n • Recipient (data collection point IK or KV number)\n • Current number\n • Creation Date"}}, "PrivateBilling": {"header": "Private Billing", "allStatuses": "All statuses", "No invoice": "No invoice", "Unpaid": "Unpaid", "Paid": "Paid", "Cancelled": "Cancelled", "reminder": "Reminder", "1st reminder": "1st reminder", "2nd reminder": "2nd reminder", "3rd reminder": "3rd reminder", "invoiceDate": "Invoice date", "patient": "patient", "doctor": "doctor", "invoiceNumber": "Invoice number", "lastDocumented": "Last documented", "insurance": "Insurance", "amount": "Amount", "reviewed": "Reviewed", "status": "Status", "printInvoices": "Print invoices", "alarmTooltip": "Payment term exceeded", "printReminders": "Print reminders", "itemSeleceted": "Item(s) selected", "noResultFoundTitle": "No private billing schein created", "noResultFoundDescription": "When private billing schein is created from patient profile, invoices will appear here.", "showPrintedDocuments": "Show printed documents", "showInvoice": "Show invoice", "print1stReminder": "Print 1st reminder", "show1stReminder": "Show 1st reminder", "print2ndReminder": "Print 2nd reminder", "show2ndReminder": "Show 2nd reminder", "print3rdReminder": "Print 3rd reminder", "show3rdReminder": "Show 3rd reminder", "markAsPaid": "Mark as paid", "markAsUnPaid": "<PERSON> as unpaid", "markAsPartialPaid": "Mark as partially paid", "cancelinvoice": "Cancel invoice", "daterange": "Date range", "paidAmount": "Paid amount", "titleParialPaid": "Mark invoice as partially paid", "totalParialPaid": "Total amount to be paid", "remainingAmount": "Remaining amount", "markedAsPaid": "Invoice successfully paid", "invoiceReviewed": "Invoice reviewed", "invoiceNotReviewed": "Invoice not reviewed", "markedAsPartialPaid": "Marked invoice as partially paid", "markedAsUnpaid": "Marked invoice as unpaid", "invoiceCancelled": "Invoice cancelled", "invoiceCancelledTitle": "Cancel invoice?", "invoiceCancelledDes": "Private billing invoice will be cancelled.", "invoiceCancelleFailed": "Failed to cancel invoice", "onlyDelayPaid": "Only show invoices that have not been paid in time", "doctors": "Doctors"}, "PreventiveBilling": {"header": "Preventive Billing", "openBilling": "Open Billing", "dispatchList": "Dispatch list", "stepBillingSelect": "Billing selection", "stepValidateList": "Validation list", "stepSummary": "Billing summary", "quarter": "Quarter with open billing", "practice": "practice", "documentType": "Document type", "troubleshooting": "Start troubleshooting", "billOpenScheinFromPreQuarter": "Bill open schein from previous quarter", "editBillingSelection": "Edit billing selection", "checkForValidation": "Check for validation", "createBilling": "Create billing", "caseNumber": "Case number", "patient": "patient", "insuranceName": "Insurance name", "statusHeader": "STATUS", "type": "Type", "saved": "saved", "finished": "finished", "notfinished": "not finished", "fromPatient": "from {{count}} patients", "search": "Search", "viewDocumentation": "View documentation", "downloadBillingFile": "Download billing file", "sendViaKim": "Send via KIM", "reSendViaKim": "Re-send via KIM", "undoBillingSubmission": "Undo billing submission", "prepareForShipping": "Prepare for shipping", "recipientKv": "recipient kv", "undoBillingSubmissionTitle": "Undo billing submission?", "undoBillingSubmissionContentLine1": "Do you really want to undo this preventive billing?", "undoBillingSubmissionContentLine2": "Attention: Forms that have been modified are not affected!", "undoBillingSubmissionYes": "Yes, undo", "undoBillingSubmissionNo": "No", "cancel": "Cancel", "save": "Save", "sendViaKimBtn": "Send", "sendViaKIMQuaterLabel": "QUARTER", "sendViaKIMPracticeLabel": "PRACTICE", "sendViaKIMAccountLabel": "KIM ACCOUNT", "sendViaKIMTestMailToLabel": "TEST MAIL TO", "billingSummaryViceTitle": "These fields are ready for download and sending:", "eHKS_10": "Electronic Skin Cancer Screening", "prinTransportLetter": "Print transport letter", "printTransportLetterDesc": "In order to send the preventive billing by data carrier, the accompanying transport letter must be printed out and signed with the date.", "labelDataCarrier": "Label data carrier", "labelDataCarrierDesc": "The media used must be labeled with stickers or labeled directly. The corresponding labels must contain the following information:", "labelDataCarrierCategorySender": "Sender ((N)BSNR of the place of activity where the file was created or hospital IK)", "labelDataCarrierCategoryRecipient": "Recipient (data collection point IK or KV number)", "labelDataCarrierCategoryCurNumber": "Current number", "labelDataCarrierCategoryCreationDate": "Creation date", "recipientPractice": "RECIPIENT PRACTICE", "selectBillingDataStorageLocation": "Select billing data storage location", "selectBillingDataStorageLocationDesc": "Please select where the billing data should be stored. For postal shipping, please print the transport labels afterwards.", "selectBillingDataStorageLocationLabel": "STORAGE LOCATION", "sendViaKimSubmitted": "Billing files sent via KIM", "downloadBillingFiles": "Download billing files", "downloadBillingDesc": "These files are ready for download and have already been sent:", "close": "Close", "sendViaKimFailMessage": "If the error occurs repeatedly, please inform your software company or sales and service partner.", "sendViaKimPendingMessage": "Please contact the recipient by phone or e-mail.", "validationSuccess": "Validation successfully checked"}}