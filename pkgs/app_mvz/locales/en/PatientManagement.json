{"OmimGChain": {"overview": "OMIM-G chain overview", "placeholder": "search OMIM-G by name", "name": "Name", "omimGChain": "OMIM-G Chain", "add": "Create OMIM-G codes chain", "edit": "Update OMIM-G codes chain", "addCode": "Add code", "remove": "remove OMIM-G", "submit": "submit", "close": "close", "required": "required", "requiredShortCut": "Shortcut field is required", "select": "Select OMIM-G", "titleConfirmRemove": "Delete OMIM-G Chain?", "titleConfirmDescription": "Do you want to permanently delete the OMIM-G Chain? This action cannot be undone.", "titleConfirmSuccess": "Removed successfully", "ominCodes": "OMIM-G CODE {{code}}", "createOmimGChainSuccess": "OMIM-G Chain created succesfully", "updateOmimGChainSuccess": "OMIM-G Chain updated successfully", "createChain": "Create Chain"}, "HgncChain": {"overview": "HGNC chain overview", "placeholder": "search HGNC by name", "name": "Name", "hgncChain": "HGNC Chain", "add": "Create HGNC codes chain", "edit": "Update HGNC codes chain", "addCode": "Add code", "remove": "remove HGNC", "submit": "submit", "close": "close", "required": "required", "requiredShortCut": "Shortcut field is required", "select": "Select HGNC", "titleConfirmRemove": "Delete HGNC Chain?", "titleConfirmDescription": "Do you want to permanently delete the HGNC Chain? This action cannot be undone.", "titleConfirmSuccess": "Removed successfully", "hgncCodes": "HGNC CODE {{code}}", "createHgncChainSuccess": "HGNC Chain created succesfully", "updateHgncChainSuccess": "HGNC Chain updated successfully", "createChain": "Create Chain"}, "PatientSignatureForm": {"getPatientSignature": "Get patient's signature", "getPatientSignatureContent": "The patient has to sign in addition the policyholder participation statement for the contract in addition to the insured a registration document. The signed declaration of participation remains in pratice, a copy must be given to the patient.", "signature1Label": "<PERSON><PERSON> has signed the participation letter", "signature2Label": "<PERSON><PERSON> has signed the supply control letter", "signatureRegisterLabel": "<PERSON><PERSON> has signed the registration document", "signatureParticipationLabel": "<PERSON><PERSON> has signed the participation document"}, "PatientOfflineEnrollment": {"getPatientSignature": "Get patient's signature", "offlineEnrollment": "The patient must sign the policyholder participation statement and a registration document. The practice retains the signed declaration and provides the patient with a copy."}, "PatientEnrollmentForm": {"errorMessageAlreadyParticipating": "The patient is already participating in the same contract.", "errorSignature1": "The insured person's signature is a prerequisite for participation in the HzV!", "errorSignature2": "The insured person's signature is necessary for optimal health care management by the health insurance company!", "hzvOnlineEnrollmentSubmitted": "HzV enrollment submitted", "favOnlineEnrollmentSubmitted": "FAV enrollment submitted", "hzvOfflineEnrollmentSaved": "HzV enrollment has been saved", "enrollError": "An issue occurred. If the problem persists, please contact system support for assistance.", "submitOfflineButtonLabel": "Save", "submitOnlineButtonLabel": "Save", "cancelButtonCloseFormAlert": "Cancel", "confirmButtonCloseFormAlert": "Quit", "titleConfirmCloseAlert": "Quit the enrollment process", "contentConfirmCloseAlert": "Enrollment data has not been submitted yet. Enrollment process can be resumed later.", "defaultMessageHpmError": "Online data transmission of declarations of participation by insured persons is currently not possible. Please start the request again at a later date.", "prepareContractForms": "Preparing contract forms...", "printRequestSent": "Print request sent", "hzvContractName": "HZV-CONTRACT-NAME", "favContractName": "FAV-CONTRACT-NAME", "errorSignatureParticipationHzV": "The insured person's signature is a prerequisite for participation in the HzV!", "errorSignatureParticipationFAV": "The insured person's signature is a prerequisite for participation in the FaV!", "errorRegistration": "The insured person's signature is necessary for optimal health care management by the health insurance company!", "progressSaveOn": "Progress save on {{ time }}"}, "PrintForm": {"printFormsLabel": "Print forms", "printFormLabel": "Print form", "tagPrintedForm": "(PRINTED)", "registrationDocument": "Registration document", "declareParticipation": "Declaration of participation", "careManagement1": "Care management 1", "careManagement2": "Care management 2", "printButtonLabel": "Print selected form", "hzvContractName": "HZV-CONTRACT-NAME", "favContractName": "FAV-CONTRACT-NAME", "print": "Form Print", "printAgain": "Print again", "printBlankForm": "Blanc form", "formPrinted": "Form printed"}, "PrintReviewForm": {"loadingPDFReview": "Preparing the print preview...", "btnPrint": "Print", "formDetails": "Form details"}, "SelectContractDoctorForm": {"reasonNotSupportOkvsOrIkNumber": "This contract is not available in the doctor KV region or IK number", "reasonDoctorNotSupport": "The doctor does not participate in this contract", "reasonAlreadyParticipated": "Patient already participated", "reasonContractRequirementNotSatisfied": "Contract's requirement is not satisfied", "contractDoctorLabel": "Select Contract and Doctor", "selectContractLabel": "Select Contract", "noResult": "No Result", "selectAnOptionLabel": "Select an option", "selectDoctorLabel": "Enrol with doctor", "selectChangeDoctorReasonLabel": "Reason", "loadingContractsInformations": "Preparing the contracts...", "noAvailableContracts": "There are no doctors participating in this contract", "checkingParticipationCondition": "Checking participation's condition", "hzv": "HzV", "fav": "FaV"}, "TeCodeForm": {"incorrectInputtedTeCode": "Invalid TE code. Please check the printed declaration of participation for the correct TE code.", "enterTeCodeLabel": "Enter TE code", "teCodeContent": "Mit Eingabe des TE-Codes wird vom Anwender bestätigt, dass die Angaben zum Versicherten gemäß seiner elektronischen Gesundheitskarte (eGK) vollständig erfasst und geprüft wurden, die Teilnahmeerklärung mit Unterschrift des Versicherten in Schriftform vorliegt, diese vorschriftsmäßig verwahrt wird und im Rahmen einer Prüfung vorgezeigt werden kann und für eventuell auftretende Schäden durch hier gemachte Falschangaben gehaftet wird."}, "ActiveContractParticipation": {"selectParticipationDate": "Contract participation date", "createActiveCustodianParticipationDoctorRequired": "Doctor is required", "createActiveCustodianParticipationContractRequired": "Contract is required", "createActiveCustodianParticipationStartDateRequired": "Start date is required", "applyFavGroup": "Apply to all FaV contracts.", "cancelButton": "Cancel", "saveButton": "Save", "activateButton": "Activate", "1stDayOfQuarter": "First day of Q{{quarter}}/{{year}}", "noEnrollmentWarning": "There is no application for participation in the patient contract in the contract software. If you are sure that the enrollment of these patients has been confirmed with you, you can register these patients hereby activate without request."}, "DatePickerForm": {"errorDateIsRequired": "Date is required", "applyFavGroup": "Apply to all FaV contracts."}, "EnrollmentMenu": {"checkHpmParticipationOnlineHistory": "HPM Check History", "checkHpmParticipationOnline": "Check online participation", "checkingHpmParticipationOnline": "Checking online participation...", "hpmInactiveToolTip": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer.", "hpmActiveWithMvzNotEnrollment": "In der Vertragssoftware ist keine Beantragung der Patientenvertragsteilnahme vorhanden. <PERSON><PERSON> <PERSON> sicher sind, dass die Einschreibung dieser Patienten bei Ihnen bestätigt wurde, können Sie diese Patienten hiermit ohne Beantragung aktivieren.", "hpmActive": "Die Behandlung dieses Patienten ist für alle im HzV-Ziffernkranz enthaltenen Leistungen über die HzV abzurechnen.", "hpmActiveWithMvzTerminated": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "lastCheckHpmParticipation": "Last participation check {{checkedDate}} at {{checkedTime}}", "hpmConnectionFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut"}, "FavButton": {"contractTypeFaV": "FaV", "createActiveFavParticipationLabelDialog": "Activate contract participation", "activateFavGroupContractLabelItem": "Activate contract participation", "undoTerminateParticipationLabelItem": "Undo termination", "undoTerminatedContractTitle": "Undo contract termination?", "undoTerminatedContractContent": "We will revert to the changes you made before you terminated the contract.", "undoTerminatedGroupContractTitle": "Undo contracts termination?", "undoTerminatedGroupContractContent": "We will revert to the changes you made before you terminated the contracts.", "undoTerminatedContractConfirmButton": "Yes, undo", "cancelButton": "Cancel", "checkHpmParticipationSuccessfully": "The patient is already an active custodian participant in the FaV contract", "checkHpmParticipationSuccessfullyDeputy": "The patient is already an active deputy participant in the FaV contract", "checkHpmParticipationNoActiveParticipation": "No active contract participation", "checkHpmParticipationFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut", "cancelContractConfirmTitle": "Cancel contract participation?", "cancelContractConfirmContent": "This action cannot be undone.", "cancelContractCancelButton": "Stay", "cancelContractConfirmButton": "Yes, cancel", "cancelEnrollmentConfirmTitle": "Cancel enrollment process?", "cancelEnrollmentConfirmContent": "This action cannot be undone.", "cancelEnrollmentCancelButton": "Stay", "cancelEnrollmentConfirmButton": "Yes, cancel", "createEnrollmentLabelItem": "Start enrollment process", "continueEnrollmentLabelItem": "Continue enrollment process", "cancelEnrollmentLabelItem": "Cancel enrollment process", "labelTodayShortcutDatePicker": "Today", "1stDayOfQuarter1": "1st day of Q1/{{year}}", "1stDayOfQuarter2": "1st day of Q2/{{year}}", "1stDayOfQuarter3": "1st day of Q3/{{year}}", "1stDayOfQuarter4": "1st day of Q4/{{year}}", "activateButton": "Active", "createActiveFavGroupContractLabel": "Activate contract participation", "favButton_InvalidIkNumber": "<PERSON>ine Einschreibung aufgrund von ungültiger IK-Nummer möglich", "favButton_CannotEnrollYet": "Derzeit ist keine Einschreibung möglich", "favButton_NotSupportedIkNumber": "Derzeit ist keine Einschreibung möglich (z.B. aufgrund fehlender Vertragsteilnahme des Arztes)", "favButton_Enrolled": "Der Patient ist aktiver Vertragsteilnehmer.", "favButton_Terminated": "Die Vertragsteilnehme wurde beendet.", "favButton_Printed": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "favButton_Pending": "Die Vertragsteilnehme wurde beantragt.", "favButton_NotEnrollYet": "Der Patient kann über den Facharztvertrag abgerechnet werden, wenn er eine Teilnahmeerklärung für das Facharztprogramm unterzeichnet hat und Sie diese versendet haben (Sofortabrechnung nach Einschreibung) und der Patient bereits am Hausarztvertrag teilnimmt.\nAlternativ kann dieser Patient über die KV abgerechnet werden.", "favButton_Faulty": "FEHLERHAFT\nDie Einschreibung des Patienten war nicht erfolgreich - klicke hier um diesen erneut einzuschreiben", "favButton_Created": "ERZEUGT\nDer Patient ist derzeit kein aktiver Vertragsteilnehmer", "cancelParticipationLabelItem": "Cancel contract participation", "activateParticipationLabelItem": "Activate contract participation", "cancelParticipationGroupLabelItem": "Cancel contract participation", "terminateParticipationLabelItem": "Terminate contract participation", "terminateParticipationGroupLabelItem": "Terminate contract participation", "terminateGroupConractLabel": "Terminate group contract participation", "terminateButton": "Terminate", "changeParticipationStartDateLabelItem": "Change start of contract", "changeParticipationGroupStartDateLabelItem": "Change start of contract", "undoTerminateParticipationGroupLabelItem": "Undo terminate", "changeGroupContractStartDateTitle": "Change start of contracts", "changeContractStartDateSubmit": "Change", "favMainContractsTopDivider": "FAV MAIN CONTRACT", "performHpmCheckOnlineFavGroupLabelItem": "Check Online Participation", "showHpmCheckHistoryDialogLabelItem": "HPM Check History", "participationForm": "Participation Form", "HpmHistoryDialogTitle": "HPM Contract Participation History", "treatAsDeputyLabelItem": "<PERSON><PERSON><PERSON> as deputy", "activateCustodianTreatmentLabelItem": "Active custodian treatment", "performHpmCheckOnlineParticipationLabelItem": "Check Online Participation", "menuFooterFavTerminated": "Terminated by {{updatedBy}} {{updatedDate}} at {{updatedTime}}", "system": "System"}, "FavHpmCheckHistory": {"OK_Custodian": "Active Custodian Participation", "OK_Deputy": "Active Deputy Participation", "FAILED": "Connection error", "NO_INFORMATION": "No contract participation", "message_OK": "The patient is already an active participant in the FaV contract", "message_FAILED": "The insured person participation check is currently not possible. Please start the request again at a later date.", "message_NO_INFORMATION": "The patient is currently not an active contract participant"}, "FavContractItem": {"MissingInsuranceNumber": "The patient's participation status cannot be determined because there is no valid personal.\nThe insurance number of the electronic health card is available.", "activateParticipationLabelItem": "Activate contract participation", "createActiveCustodianParticipationItem": "Activate contract participation", "cancelParticipationLabelItem": "Cancel contract participation", "changeDoctorLabelItem": "Change doctor", "terminateParticipationLabelItem": "Terminate contract participation", "changeParticipationStartDateLabelItem": "Change start of contract", "undoTerminateParticipationLabelItem": "Undo termination", "treatAsDeputyLabelItem": "<PERSON><PERSON><PERSON> as deputy", "activateCustodianTreatmentLabelItem": "Activate custodian treatment", "createEnrollmentLabelItem": "Start enrollment process", "continueEnrollmentLabelItem": "Continue enrollment process", "cancelEnrollmentLabelItem": "Cancel enrollment process", "changeContractStartDateTitle": "Change start of contract", "changeContractStartDateSubmit": "Change", "changeContractStartDateCancel": "Cancel", "1stDayOfQuarter1": "1st day of Q1/{{year}}", "1stDayOfQuarter2": "1st day of Q2/{{year}}", "1stDayOfQuarter3": "1st day of Q3/{{year}}", "1stDayOfQuarter4": "1st day of Q4/{{year}}", "labelTodayShortcutDatePicker": "Today", "teminateContractParticipationTitle": "Terminate contract participation", "terminateContractParticipationSubmit": "Terminate", "cancelButton": "Cancel", "createActiveCustodianParticipationLabelDialog": "Activate contract participation", "activateButton": "Active", "favContract_Created_Line1": "ERZEUGT", "favContract_Created_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "favContract_Printed_Line1": "GEDRUCKT", "favContract_Printed_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "favContract_Faulty_Line1": "FEHLERHAFT", "favContract_Faulty_Line2": "Die Einschreibung des Patienten war nicht erfolgreich - klicke hier um diesen erneut einzuschreiben", "favContract_CannotEnrollYet_Line1": "Derzeit ist keine Einschreibung möglich", "favContract_NotEnrollYet_Line1": "Der Patient kann über den Facharztvertrag abgerechnet werden, \nwenn er eine Teilnahmeerklärung für das Facharztprogramm unterzeichnet hat und \nSie diese versendet haben und der Patient bereits am Hausarztvertrag teilnimmt. \nAlternativ kann dieser Patient über die KV abgerechnet werden.", "favContract_Pending_Online_Line1": "Teilnahme an der FaV wurde beantragt am {{appliedDate}} um {{appliedTime}}.", "favContract_Pending_Offline_Line1": "Teilnahme an der FaV wurde beantragt am {{appliedDate}} um {{appliedTime}}.", "favContract_Enrolled_Line1": "The patient is already an active participant in the FaV contract", "favContract_Enrolled_Line2": "{{contractName}} seit {{startDate}}", "favContract_Terminated_Line1": "Die Vertragsteilnahme wurde beendet am: {{endDate}}", "favContract_Terminated_HpmActive_Line1": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "favContract_Rejected_Line1": "Die Einschreibung wurde abgelehnt", "menuFooterFavTerminated": "Terminated by {{updatedBy}} {{updatedDate}} at {{updatedTime}}", "button_Terminated_Line1": "Die Vertragsteilnahme wurde von {{userName}} zum {{endDate}} beendet", "hzvButton_Terminated_HpmActive_Line1": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "hzvButton_Terminated_ChangedIknumber_Line1": "<PERSON><PERSON><PERSON> den Patienten ist aufgrund eines Kassenwechsels eine Neueinschreibung erforderlich"}, "FavContractsList": {"favContractsTopDivider": "SUB-CONTRACTS"}, "HzvButton": {"showHpmCheckHistoryDialogLabelItem": "HPM Check History", "performHpmCheckOnlineLabelItem": "Check Online Participation", "statisHintPracticeLabel": "Der Patient ist bereits aktiver Vertragsteilnehmer", "custodianDoctorLabel": "<PERSON><PERSON><PERSON><PERSON>", "sinceLabel": "since", "noResult": "No Result", "selectAnOptionLabel": "Select an option", "atLabel": "at", "notEnrollentYetLabel": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer - klicke hier um den Patienten einzuschreiben", "checkEnrollmentStatusLabel": "Checking enrollment status...", "contractTypeHZV": "HzV", "contractTypeFaV": "FaV", "createEnrollmentLabelItem": "Start enrollment process", "continueEnrollmentLabelItem": "Continue enrollment process", "activateParticipationLabelItem": "Activate contract participation", "createActiveCustodianParticipationItem": "Activate custodian participation", "checkPotentialVERAHLabelItem": "Check VERAH® conditions", "VERAHActivated": "VERAH® TopVersorgt is activated", "VERAHActivatedDescription": "Die Leistung VERAH ® TopVersorgt wurde bei diesem Versicherten dokumentiert.", "VERAHEligible": "Eligible for VERAH® TopVersorgt", "VERAHEligibleDescription": "VERAH® TopVersorgt wegen vorliegender Diagnosen möglich: Zusatzhonorar durch intensivierte Betreuung durch VERAH.", "VERAHNotEligible": "Not eligible for VERAH® TopVersorgt", "VERAHNotEligibleDescription": "Dieser Versicherte kann leider nicht im Rahmen von VERAH ® TopVersorgt betreut und abgerechnet werden.", "closeBtn": "OK, close", "cancelEnrollmentLabelItem": "Cancel enrollment process", "cancelParticipationLabelItem": "Cancel contract participation", "changeDoctorLabelItem": "Change doctor", "terminateParticipationLabelItem": "Terminate contract participation", "changeParticipationStartDateLabelItem": "Change start of contract", "undoTerminateParticipationLabelItem": "Undo termination", "treatAsDeputyLabelItem": "<PERSON><PERSON><PERSON> as deputy", "activateCustodianTreatmentLabelItem": "Activate custodian treatment", "selectParticipationDate": "Contract participation date", "createActiveCustodianParticipationLabelDialog": "Activate custodian participation", "createActiveCustodianParticipationDoctorRequired": "Doctor is required", "createActiveCustodianParticipationContractRequired": "Contract is required", "createActiveCustodianParticipationStartDateRequired": "Start date is required", "createActiveCustodianParticipationSuccessful": "Custodian participation activated", "cancelContractConfirmTitle": "Cancel contract participation?", "cancelContractConfirmContent": "This action cannot be undone.", "cancelContractCancelButton": "Stay", "cancelContractConfirmButton": "Yes, cancel", "cancelEnrollmentConfirmTitle": "Cancel enrollment process?", "cancelEnrollmentConfirmContent": "This action cannot be undone.", "cancelEnrollmentCancelButton": "Stay", "cancelEnrollmentConfirmButton": "Yes, cancel", "undoTerminatedContractTitle": "Undo contract termination?", "undoTerminatedContractContent": "We will revert to the changes you made before you terminated the contract.", "undoTerminatedContractConfirmButton": "Yes, undo", "teminateContractParticipationTitle": "Terminate contract participation", "terminateContractParticipationSubmit": "Terminate", "changeContractStartDateTitle": "Change start of contract", "changeContractStartDateSubmit": "Change", "changeContractStartDateCancel": "Cancel", "activateCustodianTitle": "Activate custodian treatment", "earliestDateWarning": "The start date must be after the enrollment submission date.", "activateCustodianCancelButton": "Cancel", "activateCustodianSubmitButton": "Activate", "treatAsDeputyTitle": "<PERSON><PERSON><PERSON> as deputy", "treatAsDeputySuccessful": "<PERSON><PERSON><PERSON> as deputy activated", "createEnrollmentSuccessful": "Enrollment created", "activateParticipationSuccessful": "Contract participation activated", "cancelEnrollmentSuccessful": "Enrollment process cancelled", "cancelContractSuccessful": "HzV contract cancelled", "changeDoctorSuccessful": "Doctor changed", "terminateParticipationSuccessful": "Contract participation terminated", "changeParticipationStartDateSuccessful": "Contract's start date changed", "undoTerminatedContractSucessful": "Contract termination undone", "activateCustodianTreatmentSucessful": "Custodian treatment activated", "hzvButton_CannotEnrollYet_Line1": "Derzeit ist keine Einschreibung möglich", "hzvButton_NotEnrollYet_Line1": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer - klicke hier um den Patienten einzuschreiben", "hzvButton_NotEnrollYet_HpmActive_Line1": "In der Vertragssoftware ist keine Beantragung der Patientenvertragsteilnahme vorhanden. <PERSON><PERSON> <PERSON> sicher sind, dass die Einschreibung dieser Patienten bei Ihnen bestätigt wurde, können Sie diese Patienten hiermit ohne Beantragung aktivieren.", "hzvButton_Created_Line1": "ERZEUGT", "hzvButton_Created_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "hzvButton_Printed_Line1": "GEDRUCKT", "hzvButton_Printed_Line2": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer", "hzvButton_Faulty_Line1": "FEHLERHAFT", "hzvButton_Faulty_Line2": "Die Einschreibung des Patienten war nicht erfolgreich - klicke hier um diesen erneut einzuschreiben", "hzvButton_Pending_IVP_Online_Line1": "HZV und Modulvertrag AOK Baden-Württemberg IV-Pflegeheim wurde beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_Pending_IVP_Online_Line2": "Status: Erfolgreich übermittelt", "hzvButton_Pending_Online_Line1": "HzV-Teilnahme wurde beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_Pending_Online_Line2": "Status: erfolgreich übermittelt", "hzvButton_Pending_Offline_Line1": "HzV-Teilnahme wurde beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputy_Offline_Line1": "Vertretungsfall {{contractName}}", "hzvButton_PendingDeputy_Offline_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputy_Offline_Line3": "Status: g<PERSON><PERSON><PERSON><PERSON>", "hzvButton_PendingDeputy_Online_Line1": "Vertretungsfall {{contractName}}", "hzvButton_PendingDeputy_Online_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputy_Online_Line3": "Status: erfolgreich übermittelt", "hzvButton_PendingDeputyChangingDoctor_Online_Line1": "Vertretungsfall {{contractName}}", "hzvButton_PendingDeputyChangingDoctor_Online_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingDeputyChangingDoctor_Online_Line3": "Status: erfolgreich übermittelt", "hzvButton_PendingChangingDoctor_Online_Line1": "{{contractName}}", "hzvButton_PendingChangingDoctor_Online_Line2": "Arztwechsel beantragt am {{appliedDate}} um {{appliedTime}}", "hzvButton_PendingChangingDoctor_Online_Line3": "Status: erfolgreich übermittelt", "hzvButton_Enrolled_Online_Line1": "Active HzV contract participant", "hzvButton_Enrolled_Line5": "hzvButton_Enrolled_Line5", "hzvButton_Enrolled_HpmActive_Online_Line1": "hzvButton_Enrolled_HpmActive_Online_Line1", "hzvButton_Enrolled_HpmActive_Line1": "hzvButton_Enrolled_HpmActive_Line1", "hzvButton_NotAvailable_Line1": "A participation check for this contract is not possible.", "hzvButton_Enrolled_Line1": "Der Patient ist bereits aktiver Vertragsteilnehmer", "hzvButton_Enrolled_Line2": "{{<PERSON><PERSON><PERSON>}}", "hzvButton_Enrolled_Line3": "<PERSON><PERSON><PERSON><PERSON>", "hzvButton_Enrolled_Line4": "{{contractName}} seit {{startDate}}", "button_Terminated_Line1": "The contract participation was terminated by {{userName}} to {{endDate}}", "hzvButton_Terminated_HpmActive_Line1": "Es liegt eine aktive Versichertenteilnahme vor, diese wurde jedoch von {{updatedBy}} manuell am {{updatedDate}} beendet.", "hzvButton_Terminated_ChangedIknumber_Line1": "<PERSON><PERSON><PERSON> den Patienten ist aufgrund eines Kassenwechsels eine Neueinschreibung erforderlich", "hzvButton_Rejected_Line1": "Die Einschreibung wurde abgelehnt", "hzvButton_Deputy_Line1": "Vertretungsfall {{contractName}} - Der Patient ist bei einem anderen Arzt eingeschrieben", "hzvButton_InvalidIkNumber_Line1": "<PERSON>ine Einschreibung aufgrund von ungültiger IK-Nummer möglich", "hzvButton_NotSupportedIkNumber_Line1": "Derzeit ist keine Einschreibung möglich (z.B. aufgrund fehlender Vertragsteilnahme des Arztes)", "MissingInsuranceNumber": "The patient's participation status cannot be determined because there is no valid personal\ninsurance number of the electronic health card available.", "menuFooterHzvTerminated": "Terminated by {{updatedBy}} {{updatedDate}} at {{updatedTime}}", "lastHpmInformation": "Ihre letzte Teilnahmeprüfung amm {{checkedDate}} um {{checkedTime}} {{status}} Vertragsteilnahme", "active": "Active", "inActive": "No", "hpmConnectionFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut", "1stDayOfQuarter1": "1st day of Q1/{{year}}", "1stDayOfQuarter2": "1st day of Q2/{{year}}", "1stDayOfQuarter3": "1st day of Q3/{{year}}", "1stDayOfQuarter4": "1st day of Q4/{{year}}", "labelTodayShortcutDatePicker": "Today", "selectContractLabel": "Teilnahme beantragen für", "cancelButton": "Cancel", "saveButton": "Save", "activateButton": "Activate", "checkHpmParticipationSuccessfully": "The patient is already an active custodian participant in the HzV contract", "checkHpmParticipationSuccessfullyDeputy": "The patient is already an active deputy participant in the HzV contract", "checkHpmParticipationNoActiveParticipation": "No active contract participation", "checkHpmParticipationFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut", "hzv_iKChanged": "Due to a change in health insurance provider, this patient must enrol again."}, "HzvSelectCustodianDoctor": {"title": "HzV custodian doctor", "description": "The patient has activated custodian participation in HzV", "contract": "Contract:", "doctor": "Custodian doctor", "doctorRequired": "This field is required", "btnSkip": "<PERSON><PERSON>", "btnUpdate": "Update doctor"}, "HzvHpmCheckHistory": {"OK_Custodian": "Active Custodian Participation", "OK_Deputy": "Active Deputy Participation", "FAILED": "Verbindungsfehler", "NO_INFORMATION": "<PERSON><PERSON>", "message_OK": "Der Patient ist bereits aktiver HzV- Vertragsteilnehmer", "message_FAILED": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut.", "message_NO_INFORMATION": "Der Patient ist derzeit kein aktiver Vertragsteilnehmer"}, "PatientEnrollmentWidget": {"lastHpmInformation": "Ihre letzte Teilnahmeprüfung amm {{checkedDate}} um {{checkedTime}} {{contractId}} {{status}} Vertragsteilnahme", "active": "Aktive", "inActive": "No", "hpmConnectionFail": "Die Versichertenteilnahmeprüfung ist derzeit nicht möglich. Bitte starten Sie die Anfrage zu einem späteren Zeitpunkt erneut", "hpmEnrollmentFail": "Online data transmission of declarations of participation by insured persons is currently not possible. Please start the request again at a later date."}, "TreatAsDeputy": {"selectContractLabel": "Apply for participation", "noResult": "No Result", "selectAnOptionLabel": "Select an option", "cancelButton": "Cancel", "activateButton": "Activate", "Doctor": "Doctor"}, "Composer": {"commandsDescription": {"anamnese": "Anamnese", "anamnestic-diagnose": "Anamnestic Diagnosis", "findings": "Finding", "acute-diagnose": "Acute Diagnosis", "permanent-diagnose": "Permanent Diagnosis", "service": "Service", "therapy": "Therapy", "notes": "Notes"}, "controlSpace": "Strg + Space", "controlEnter": "Enter", "toSubmit": "to submit", "forTextmodule": "for Textmodules", "encryptionTooltip": "View encryption instructions", "typeCommand": "Type a command or \"+\" to open command list", "typeService": "Type to search for service code. Enter twice when you complete", "typeDiagnose": "Type to search for diagnosis code. Enter twice when you complete.", "typeFreeText": "Type a Textmodule shortcut or continue typing", "typeParenthesis": "Type '{{key}}' to add additional info", "addAdditionalInfo": "Add additional info", "selectAddtionalInfo": "select additional info", "additionalInfo": "Information", "command": "Command", "certainty": "Certainty", "typeCertainty": "Type to search for certainty", "typeLaterality": "Type to search for laterality", "laterality": "Laterality", "title": "Referral information", "referralInfoLanr": "LANR", "referralInfoTypeLanr": "Type LANR number of the referral doctor. Enter twice when you complete", "referralInfoBsnr": "BSNR", "referralInfoTypeBsnr": "Type BSNR number of the referral doctor. Enter twice when you complete", "careFacilityTitle": "Care Facility", "careFacilityName": "Name", "careFacilityTypeName": "Type name of care facility. Enter twice when you complete", "careFacilityOrt": "Ort", "careFacilityTypeOrt": "Type ort of care facility. Enter twice when you complete", "ops": "OPS", "chooseLaterality": "Type to select laterality", "submitted": "Submitted to data centre", "noEdit": "Cannot be edited", "generalPractitionerText1": "Unfortunately, no search hits could be found in the family doctor catalogue.", "generalPractitionerText2": "Would you like to start another search in the \"Systematic and Alphabetical Directory\"?", "specialistGroupNoResult": "Unfortunately, no search hits could be found in the specialist {{specialListGroup}} group catalogue.", "noResultsFound": "No results found", "CreateServiceCode": "Create service code", "changeToFavSchein": "or do you want to document on the FaV schein?", "specialistGroupText3": "Would you like to start another search in the \"Systematic and Alphabetical Directory\"?", "createSuccessEbm": "Service code created", "deleteSuccessfully": "Entry removed", "typeActionChain": "Type to search for action chain. Enter twice when you complete", "pseudoGnr": "Technische Kennziffer", "EncryptionInstructions": "Encryption instructions", "noData": "No Data", "systematicText2": "Unfortunately, no search hits could be found in the Systematic catalogue.", "systematicText3": "Would you like to start another search in the \"Systematic and Alphabetical Directory\"?", "createSuccessPseudoGnr": "The material cost will be billed under a pseudo-GNR based on SDKV rules.", "actionCannotMarkAsPermanentDiagnose": "Not suitable as a permanent diagnosis", "titlePopupShowLicense": "OMIM License", "factor": "Factor", "quantity": "Quantity", "price": "Price", "fieldRequired": "Quantity and Factor are required.", "fieldInvalid": "Factor must be > 0, and quantity must be ≥ 1", "fieldLowerThanRequired": "Factor cannot be lower than 1", "createJustificationTitle": "Create justification", "selfCreated": "Self-created", "timelineUpdated": "Entry updated", "timelineCreated": "Timeline entry created", "invalidFactorQuantity": "Factor must be > 0, and quantity must be ≥ 1", "invalidHgncGenSymbol": "The entered HGNC symbols are not valid"}, "CreateSvSchein": {"title": "No <PERSON>hein created for the documented date", "description": "Entries like diagnosis, service code require a Schein to be able to submit for billing later.", "cancelButton": "<PERSON><PERSON>", "confirmButton": "Create new Schein"}, "Encounter": {"encounterTitle": "{{case}} Encounter", "outPatient": "Outpatient", "accident": "Accident", "preventive": "Preventive", "encounterCase": "Encounter case: ", "encounterSubmitted": "You are not allowed to edit Encounter case that already submitted for billing.", "warningNoContracts": "No contract participation for the selected date.\nPlease select another one.", "systematicAndAlphabetical": "Systematic And Alphabet", "systematic": "Systematic", "generalPractitioner": "GP", "specialistGroup": "Specialist", "allSpecialistGroups": "All specialist groups", "setDefaultCatalog": "Set as default catalogue", "selectCatalog": "Select catalogue", "clearAllContent": "Clear all content?", "submitEntry": "Submit entry", "clearAll": "Clear all"}, "PatientFile": {"AKA_ABRD1544_errorMessage": "LANR and BSNR of the referring doctor are mandatory if a referral has been made.", "WELCOME_TO_TUTUM": "Welcome to garrioPRO", "SEARCH_OR_ADD_PATIENT": "ALT+S to search, ALT+P to create new patient", "TIME_LINE_TAB": "Timeline", "MEDICATION_TAB": "Medication", "PRESCRIPTIONS_TAB": "Prescriptions", "HIMI_TAB": "HIMI", "FORMS_TAB": "Forms", "DIGA_TAB": "DiGA", "HEIMI_TAB": "HEIMI", "LAB_TAB": "Lab", "editSuccessLb": "Patient details updated", "missingSchein": "Missing <PERSON><PERSON>", "createScheinToSeeSomesthingHere": "Please create schein to start documenting", "legend": "Legend:", "created": "Created", "removed": "Removed", "restored": "Restored", "updated": "Updated", "historyTitle": "View open history", "tooltipViewHistory": "Last opened  by {{name}} {{date}} at {{time}}"}, "TerminatePermanentDiagnoseDialog": {"timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_title": "Add end date", "timelineContent_diagnoseEntry_documentException_title": "Document exception", "timelineContent_diagnoseEntry_documentExplanation_title": "Document explanation", "timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_message": "Bitte fügen Sie ein Enddatum für die Diagnose hinzu:", "timelineContent_diagnoseEntry_addFurtherInfoDialog_message": "Gutartige Neubildung der Prostata (D29.1)", "timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_calendarTitle": "End date", "timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_ConfirmButton": "OK"}, "TimelineEncounter": {"outPatient": "Outpatient", "accident": "Accident", "preventive": "Preventive", "confirmDeleteDialogTitle": "Remove from timeline?", "confirmDeleteDialogMessage": "This item will no longer be displayed in the timeline.", "confirmDeleteDialogConfirmButton": "Yes, delete", "confirmDeleteDialogCancelButton": "Cancel", "editActionTooltip": "Edit", "editEncounterDateTooltip": "Edit encounter date", "deleteActionTooltip": "Delete", "deleteActionDisabledTooltip": "Encounter contains submitted items.", "KJP4a_sendHpmRequest_buttonLabel": "KJP4a BVKJ", "KJP4a_warningMessage": "Documentation of the pre-enrolled service KJP4a is only possible when you activate the contract status.", "KJP4a_resendHpmRequest_buttonLabel": "Resend KJP4a BVKJ", "KJP4a_submissionFailedWarningMessage": "KJP4a BVKJ submission failed", "KJP4a_sendHpmRequest_successToasterMessage": "KJP4a BVKJ submitted", "KJP4a_sendHpmRequest_failedToasterMessage": "KJP4a BVKJ submission failed", "KJP4a_sendHpmRequest_alertDialog_title": "Continue documenting KJP4a BVKJ?", "KJP4a_sendHpmRequest_alertDialog_content": "Sie beabsichtigen für diesen Patienten eine KJP4a zu dokumentieren und diese Information an das HÄVG Rechenzentrum zu übertragen. Bitte beachten Si<PERSON>, dass diese Leistung später nur vergütet wird, wenn es zu einer erfolgreichen Einschreibung des Patienten in den HzV-Vertrag bei dem Arzt kommt, welcher die Leistung erbracht hat. Wollen Sie den Vorgang fortsetzen?", "KJP4a_sendHpmRequest_alertDialog_confirmButton": "Continue", "KJP4a_sendHpmRequest_alertDialog_cancelButton": "No", "UHU35_sendHpmRequest_buttonLabel": "AOK-<PERSON> 18+", "UHU35_warningMessage": "Documentation of the pre-enrolled service UHU35 is only possible when you activate the contract status.", "UHU35_resendHpmRequest_buttonLabel": "Resend AOK-Check 18+", "UHU35_submissionFailedWarningMessage": "AOK-<PERSON> 18+ submission failed", "UHU35_sendHpmRequest_successToasterMessage": "AOK-Check 18+ submitted", "UHU35_sendHpmRequest_failedToasterMessage": "AOK-<PERSON> 18+ submission failed", "UHU35_sendHpmRequest_alertDialog_title": "Continue documenting AOK Check 18+?", "UHU35_sendHpmRequest_alertDialog_content": "Sie beabsichtigen für diesen Patienten eine AOK-Check 18+ zu dokumentieren und diese Information an das HÄVG-Rechenzentrum zu übertragen. Bitte beachten Si<PERSON>, dass diese Leistung später nur vergütet wird, wenn es zu einer erfolgreichen Einschreibung des Patienten in den HzV-Vertrag bei dem Arzt kommt, welcher die Leistung erbracht hat. Wollen Sie den Vorgang fortsetzen?", "UHU35_sendHpmRequest_alertDialog_confirmButton": "Continue", "UHU35_sendHpmRequest_alertDialog_cancelButton": "No", "modalCaptionCancel": "Cancel", "modalCaptionSave": "Save", "modalTitle_EditEncounterDate": "Edit encounter date", "modalTitle_EditEntryDate": "Edit entry date", "modalTitle_EditEntryDateSuccess": "Encounter date changed", "editEntry": "Edit", "modalTitle_EditEntryDateError": "Failed to execute. Please try again.", "labelDate": "Date:"}, "DiagnoseEditor": {"descriptionLabel": "Description", "certaintyLabel": "Certainty", "lateralityLabel": "Laterality (optional)", "searchDiagnosePlaceholder": "Search diagnosis ..."}, "DiagnoseEntry": {"acuteDiagnose": "Acute Diagnosis", "chronicDiagnose": "Permanent Diagnosis", "certainty": "Certainty", "laterality": "Laterality", "diagnoseEnddate": "End date", "actionMarkAsAcuteDiagnose": "Mark as acute diagnosis", "actionMarkAsPermanentDiagnose": "Mark as permanent diagnosis", "actionMarkAsAnamnesticDiagnose": "Mark as anamnestic diagnosis", "actionAddPermanentDiagnoseEndDate": "Add diagnosis end date", "actionDocumentExceptionDiagnose": "Document exception", "actionDocumentExplanationDiagnose": "Document explanation", "actionMarkTreatmentRelevant": "Mark as treatment-relevant", "actionUnMarkTreatmentRelevant": "Unmark as treatment-relevant", "actionMarkAllTreatmentRelevant": "Mark all AD ({{value}}) as treatment-relevant this quarter", "treatment": "Treatment", "actionRemove": "Remove", "actionEdit": "Edit", "editSchein": "<PERSON>", "actionCannotMarkAsPermanentDiagnose": "Not suitable as a permanent diagnosis", "diagnoseSavedSuccessful": "Diagnosis saved.", "diagnoseSavedFailed": "Failed to save diagnosis", "diagnoseEnddateSavedSuccessful": "Diagnosis end date saved.", "diagnoseExceptionSavedSuccessful": "Exception saved.", "diagnoseExplanationSavedSuccessful": "Explanation saved.", "diagnoseCancelledSuggestionSavedSuccessful": "Diagnosis suggestion is cancelled.", "diagnoseCancelledSuggestionSavedFailed": "Failed to cancel suggested diagnosis", "diagnoseInfoSavedFailed": "Failed to save diagnosis info", "invalidCertaintyMessage": "Invalid diagnosis certainty", "AKA_ABRD887_errorMessage": "You have documented a diagnosis with the suffix “V”. The same diagnosis for this patient was also coded with the suffix “V” in a previous quarter. Please check whether this is still a suspected diagnosis.", "messageCancelled": "Validating diagnosis… this may take some time.", "btnCancelled": "Cancel", "btnAddDiagnose": "Add diagnosis", "btnUpdateDiagnose": "Replace diagnosis", "btnDeleteDiagnose": "Remove diagnosis", "msgDiagnoseAddedSuccessful": "Diagnosis added", "msgMultipleDiagnoseAddedSuccessful": "{{number}} diagnoses added", "msgDiagnoseAddedFailed": "Failed to add diagnosis", "msgDiagnoseReplacedSuccessful": "Diagnosis replaced", "msgDiagnoseReplacedFailed": "Failed to replace diagnosis", "msgDiagnoseRemoveSuccessful": "Diagnosis removed", "msgMultipleDiagnoseRemoveSuccessful": "{{number}} diagnoses removed", "msgDiagnoseRemoveFailed": "Failed to remove diagnosis", "messageCorrectionProposal": "correction proposal of the coding rules", "exception": "Exception", "explanation": "Explanation", "more": "More", "editEntryDate": "Edit entry date", "pseudo": "<PERSON><PERSON><PERSON>", "SuggestionModal": {"titleModal": "Suggested correction of the coding rules", "titleAdd": "Add suggested diagnosis", "titleReplace": "Replace diagnosis", "titleDelete": "Remove diagnosis", "captionSave": "Save", "lblCertainty": "Certainty", "lblLaterality": "Laterality", "captionCancel": "Cancel", "lblReplaceWith": "Replace selected diagnosis with…", "lblWarningRemove": "Removing diagnosis cannot be undone.", "lblRemove": "Remove the selected diagnosis", "lblRemoveConflict": "Remove alternative diagnosis", "optionDontShow": "Do not show this suggestion again this quarter for this patient.", "suggestionNoLongerApplies": "This suggestion no longer applies."}, "ErrorMessages": {"ErrorCode_Somethinngs_Went_Wrong": "An issue occurred. If the problem persists, please contact system support for assistance.", "ErrorCode_Network_Offline": "You are offline. Please check your internet connection and reload this page.", "ErrorCode_ValidationError_NotActive_InsuranceInfo": "no active insurance", "ErrorCode_Login_Invalid_Verify_Password": "Invalid password", "ErrorCode_Validation_MessageABRD613": "Der Ersatzwert \"UUU\" ist nur bei Vorliegen von bestimmten Auftragsleistungen möglich.", "ErrorCode_Validation_MessageABRD969": "Sie haben eine Akutdiagnose als Dauerdiagnose gekennzeichnet. Bitte uberprufen Sie Ihre Dokumentation, da dies nicht plausibel ist.", "ErrorCode_Validation_MessageABRD514": "You have marked an acute diagnosis as a permanent diagnosis. Please check your documentation, otherwise the billing of the flat rate for the treatment of chronically ill patients may be prevented.", "ErrorCode_Validation_MessageABRD612": "Diagnoses with the diagnostic suffix “Secured” must be finally documented in accordance with contractual requirements.", "ErrorCode_Validation_MessageABRD786": "You have given the diagnosis code \"Z\" for an acute illness. Please use the additional code “G” if the illness still exists and it is an illness that requires treatment. If you wanted to document the reason for treatment as a result of the illness, please choose from the following diagnoses, among others.", "ErrorCode_Validation_MessageVERT647": "Due to this change in the activation date, documented or billed services are outside the activation period.", "ErrorCode_Validation_MessageP10470MM": "Please check the code: The code \napplies to male patients only.", "ErrorCode_Validation_MessageP10470MF": "Please check the code: The code applies\nto female patients only.", "ErrorCode_Validation_MessageP10470KM": "Please check the code: The code applies mainly to male patients only.", "ErrorCode_Validation_MessageP10470KF": "Please check the code: The code applies mainly to female patients only.", "ErrorCode_Validation_MessageP10480MM": "Please check the code: The code applies to patients aged {{minAge}} only.", "ErrorCode_Validation_MessageP10480MR": "Please check the code: The code applies to patients in the age group between {{minAge}} and {{maxAge}} only.", "ErrorCode_Validation_MessageP10480MZ": "Please check the code: The code applies mainly to patients aged {{minAge}} only.", "ErrorCode_Validation_MessageP10480KM": "Please check the code: The code applies mainly to patients aged {{minAge}} only.", "ErrorCode_Validation_MessageP10480KR": "Please check the code: The code applies mainly to patients in the age group between {{minAge}} and {{maxAge}} only.", "ErrorCode_Validation_MessageABRD456": "Bitte dokumentieren Sie zusätzlich die Leistung „Arzt-Patienten-Kontakt“ (0000).", "ErrorCode_Validation_MessageABRD887": "You have documented a diagnosis with the suffix “V”. The same diagnosis for this patient was also coded with the suffix “V” in a previous quarter. Please check whether this is still a suspected diagnosis.", "ErrorCode_Validation_MessageABRD970": "Fehlender Name und Ort", "ErrorCode_Validation_MessageFieldIsRequiredFormat": "%s ist ein Pflichtfeld", "ErrorCode_Validation_MessageMedicineTypeRequireFormat": "%s ist erforderlich mit Medikamententyp %s", "ErrorCode_Validation_MessageMappingError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorCode_Validation_MessageNotBillingAble": "Der ICD-10-GM-Code ist nicht für die Abrechnung und/oder in der Arbeitsunfähigkeitsbescheinigung zugelassen.", "ErrorCode_Validation_MessageNotFillingForBilling": "Der ICD-10-GM-Kode ist nicht mit Inhalt belegt und darf daher nicht zur Abrechnung und/oder bei den Formularen der vertragsärztlichen Versorgung verwendet werden.", "ErrorCode_Validation_MessageRareDiseaseEu": "Bitte Kodierung überprüfen: Diagnosen dieses Kodes sind in Mitteleuropa sehr selten.", "ErrorCode_Validation_MessageIfSG": "Diagnoses with this code are generally notifiable according to the Infection Protection Act (IfSG).", "ErrorCode_Validation_MessageNotSuitablePermanent": "Please check the code: The code describes an acute, temporary condition and is not suitable for categorization as a \"permanent diagnosis\" in the further course of treatment.", "ErrorCode_Validation_MessageNeedPrimaryCode": "The ICD-10 GM code cannot be used for accounting and/or issuance of a certificate of incapacity for work. There must be at least one primary code.", "ErrorCode_CERTAINTY_IS_REQUIRED": "Certainty is required.", "InvalidLaterality": "Invalid laterality", "InvalidCertainty": "Invalid certainty", "EDMPSuggestion": "This diagnosis is qualified for DMP enrollment. Click on the DMP button to start the enrollment process.", "ErrorCode_Validation_Missing_ScheinId": "A schein is required so that the record can be transferred to billing.", "ErrorCode_Validation_ICD_Code_Not_In_Master_Data": "The stored ICD code does not exist in the ICD-10 GM master file. Therefore this code must not be used for billing or in forms.", "ErrorCode_Validation_RangeAge": "This service code is invalid due to the patient's age", "ErrorCode_Validation_MessageABRG669": "For chronic patients, it is not possible to do billing for only acute diagnoses that are documented as permanent diagnoses.", "ErrorCode_Cannot_Delete_Timeline_Entry": "Cannot delete timeline entry"}, "deleteSuccessfully": "Entry removed", "msgAnamsesticSuccess": "Marked as anamestic diagnosis", "msgAccuteSuccess": "Marked as acute diagnosis", "msgPermanentSuccess": "Marked as permanent diagnosis", "msgMarkTreatmentRelevantSuccess": "Diagnosis marked as treatment relevant", "msgUnmarkTreatmentRelevantSuccess": "Diagnosis not marked as treatment relevant", "msgMarkAllTreatmentRelevantSuccess": "All AD ({{value}}) this quarter marked as treatment relevant", "btnHideErrorMessage": "Hide and don't show again", "btnCreateNewSchein": "Create new Schein", "hintExceptionGender": "Here you can complement a diagnostic leaning or exception, which is also transmitted in your billing.", "hintExceptionGenderMust": "In order to transfer the code as part of treatment, a diagnosis exception is required. Here you can add a diagnosis explanation or exception, which is also transmitted in the billing.", "permanentHeader": "Treatment relevant Diagnosis", "permanentContent": "All treatment-relevant diagnoses and acute diagnoses will be considered in the billing."}, "ServiceEntry": {"ErrorCode_Missmatch5005And5050": "The number of recorded implant registers must match the specified multiplier for the selected EBM service. Please review and adjust your entries.", "service": "Service", "warning": "Warning", "actionAddMaterialCost": "Add material cost", "saveServiceEntryToasterMessage": "Service info saved.", "saveCareFacilityToasterMessage": "Care facility info saved.", "saveMaterialCostToasterMessage": "Material cost info saved.", "removeMaterialCostToasterMessage": "Material cost info removed.", "documentedTerminalPsychotherapy": "Documented {{code}}", "documentAndTakeover": "{{code}} and transferred diagnoses are documented on a Pseudo Treatment Schein", "atLeastSelecteOne": "At least one service code must be selected.", "referrerInfoLabel": "Referrer info", "saveReferrerInfoToasterMessage": "Referrer info saved.", "missingOpsMessage": "Missing OPS", "invalidOpsLateralityMessage": "Missing OPS laterality {{index}}", "invalidOpsMessage": "Invalid OPS {{index}}", "editEntryDate": "Edit entry date", "missingMusterEmergencyPlanHint": "Please ensure to fill out document \"Emergency plan for geriatric patients\" before you are going to bill 3740A", "removeEntry": "Remove", "removeApproval": "<PERSON> as not accepted by KV", "notAcceptedByKV": "Not accepted by KV", "markAsAcceptedByKV": "<PERSON> as accepted by KV", "documentIt": "Document it", "addLabel": "Add", "documentLabel": "Document", "codeDocumented": "{{ code }} documented", "missICDCodeToBillingDialogTitle": "Justification for {{code}}", "missICDCodeToBillingDialog": "The service code {{code}} can only be billed if justified by one of the following treatment diagnoses:", "pseudo": "<PERSON><PERSON><PERSON>", "ErrorCode_ValidationError_MustHaveRVSA": "This service code requires an RVSA", "ErrorMessages": {"ABRD1544": "Die Leistung wird normalerweise im Rahmen einer Überweisung abgerechnet.", "ABRD1544_subDescription": "<PERSON>te stellen Si<PERSON> sicher, dass der Patient überwiesen wurde und die Überweisung korrekt im FaV-Fall hinterlegt worden ist.", "ErrorCode_Missing_Referral_Doctor": "Die Leistungsziffer kann nur abgerechnet werden, wenn die LANR und BSNR des Auftraggebers angegeben wurde.", "LocalizationNotAllow": "The lateral attribute is not allowed to be transferred to a billing file with this service code.", "ErrorCode_CANNOT_SEARCH_SERVICE_CODE": "Cannot search service code", "OminGNotNull": "OMIM-G code of the gene must not be empty", "OminPNotNull": "OMIM-P code (type of disease) must not be empty", "Null": "Bei dem Ersatzwert {{value}} muss das Feld {{fieldName}} angegeben werden.", "Required": "{{fieldName}} (FK {{field}}) is required.", "RequireBoth": "Both {{fieldName1}} and {{fieldName2}} must be present.", "RequireBothOnlyOne": "Only one \"{{fieldName1}}\" and one \"{{fieldName2}}\" must be specified.", "EitherRequire": "Either {{fieldName1}} (FK {{field1}}) or {{fieldName2}} (FK {{field2}}) is required.", "ParentChildRequire": "[{{parentValue}}] {{childFieldName}} is required.", "Regex": "{{fieldName}} has invalid format. Format should be: {{pattern}}.", "MinLength": "{{fieldName}} (FK {{field}}) must not less than {{minLength}} characters.", "MaxLength": "{{fieldName}} (FK {{field}}) must not exceed {{maxLength}} characters.", "RequiredLength": "{{fieldName}} (FK {{field}}) must contain {{requiredLength}} characters.", "NotEqual": "{{fieldName}} must not be {{value}}", "NotEqualValues": "{{fieldName}} has invalid value. Value should be {{restrictedValues}} or {{lastValue}}.", "NotRequireBoth": "Der Leistung dürfen kein {{fieldName1}} zugeordnet werden.", "CombinedRangeRegex": "{{fieldName}} has invalid format kk ({{pattern}}). kk should be {{ranges}}.", "DigitRule": "{{fieldName}} (format {{pattern}}) is invalid.", "Atleast1ICDCodeNotEqualZ017": "An ICD code (except \"Z01.7\") is required.", "1_1Relation": "This service code requires the {{fieldName}} (FK {{field}}).", "1_nRelation": "This service code requires at least one {{fieldName}}.", "0_1Relation": "{{fieldName}} (FK {{field}}) must exist at most once.", "DuplicatedSameEntry": "The {{fieldName}} (FK {{field}}) must be unique.", "DuplicatedSameDate": "The {{fieldName}} (FK {{field}}) must be unique.", "DuplicatedSameValue": "The value of {{fieldName}} (FK {{field}}) must be unique.", "ErrorCode_MissICDCodeToBilling": "This service code requires at least one ICD code.", "AtLeastOneICDWithCertainty": "At least one ICD-10 code with certainty must be documented.", "ErrorCode_ValidationError_MustHaveRVSA": "This service code requires an RVSA", "ErrorCode_ValidationError_OpsMustInList": "Der angegebene OPS ist nicht als Abrechnungsbegründung für die Leistung definiert.", "ErrorCode_ValidationError_GnrMustInList": "The specified GNR does not match the selected service number. This message can also appear if you entered an GNR that is correct but not included in the KBV EBM file. Please check again.", "WarningForKvValidateRangeAge": "This service code is invalid for this age of patient", "GenderKvServiceWarning": "Die Leistung stimmt mit dem angegebenen Geschlecht des Patienten nicht überein.", "MaximumRate": "Sie haben die maximale Anzahl überschritten.", "888888800": "The (substitute) value \"888888800\" is obsolete and not permitted as field content", "TimeHHMMInvalid": "The format of {{fieldName}} should be HHMM (HH = hour, MM = Minute).", "TimeDDMMYYYYInvalid": "Please enter a valid date.", "MissingFk5003": "Bei der Abrechnung der Gebührenordnungsposition {{ code }} ist die (Neben-)Betriebsstättennummer der Praxis, an die der Patient vermittelt wurde, anzugeben.", "Psychotherapy": "Dokumentieren Sie zur Beendigung der Therapie die Pseudo-GOP 88130 (Therapieende ohne anschließende Rezidivprophylaxe), 88131 (Therapieende mit anschließender Rezidivprophylaxe) oder legen Sie fest, dass die Krankenkasse über einen anderen Weg informiert wurde.", "Psychotherapy_88130": "Please document and transfer the service code 88130 for termination notification.", "Psychotherapy_88131": "Attention: You want to conduct/bill a guideline therapy as a relapse prophylaxis. Prerequisite for this is a completion notification for the guideline therapy with pseudo-GOP 88131 (§ 16 para. 3 Psychotherapy Agreement). So far, no completion with the pseudo-GOP 88131 has been transmitted in the previous course of treatment.", "Psychotherapy_Missing_Diangosis": "A diagnosis is required in order to bill the code.", "OPS_5041": "Für die OPS {{opsKeys}} muss eine Seitenlokalisation angegeben werden.", "ReplacedWithServiceCodeWhenBilling": "This service will be replaced by service with code {{code}} when billing based on patient's age", "tss_surcharge_error_common": "Es ist kein zeitgestaffelter Zuschlag abrechenbar, da der Terminservice in der Zukunft liegt", "tss_surcharge_acute_error": "Der Patient wurde nicht am aktuellen Tag oder Folgetag behandelt und ist folglich nicht als TSS-Akutfall kennzeichenbar.", "tss_surcharge_routine_error": "Es ist kein zeitgestaffelter Zuschlag mehr abrechenbar, da die 35- Tage-Frist verstrichen ist.", "tss_surcharge_suggestion": "Ein zeitgestaffelter Zuschlag kann aufgrund des vermittelten TSS -Termins  abgerechnet werden. Do you want to document {{ code }} to get surcharge?", "ErrorCode_Validation_Missing_ScheinId": "A schein is required so that the record can be transferred to billing.", "ErrorCode_Validation_Missing_Treatment_Time": "This service code requires a time of treatment", "ErrorCode_Validation_Must_Not_Present_Treatment_Time": "This service code must NOT present Time of treatment", "ErrorCode_Validation_Missing_Pseudo_GNR": "This service code requires a Pseudo-GNR.", "ErrorCode_Validation_Invalid_Pseudo_GNR": "Invalid Pseudo-GNR", "ErrorCode_InvalidServiceCode": "Invalid service code", "ErrorCode_ServiceValidationError": "Number is not valid during the service period.", "ErrorCode_Validation_RangeAge": "This service code is invalid for this patient based on patient's age", "ErrorCode_Validation_Ad4125_InValid": "The service day (FK 5000) must be within the specified validity period (FK 4125). Please contact system support for assistance.", "InVertretungFuer_LANR": "Service code can only be billed when LANR of referred person has been input", "InVertretungFuer_BSNR": "Service code can only be billed when BSNR of referred person has been input", "ErrorCode_GoaService_ExcludedCode": "This service code is not allowed to be documented with {{excludedCodes}}", "ErrorCode_GoaService_GoaNumber_Not_Found": "Invalid service code", "ErrorCode_GoaService_Invalid_GOA": "Invalid GOÄ", "E440150": "PTP1 darf nur einmal in vier aufeinanderfolgenden Quartalen abgerechnet werden.", "E460212": "PTP1 darf nur einmal in vier aufeinanderfolgenden Quartalen abgerechnet werden.", "E877218": "PTP1 darf nur einmal in vier aufeinanderfolgenden Quartalen abgerechnet werden.", "ErrorCode_Validation_Abrd1564": "This EBM service is part of the HZV code set and should be billed exclusively through HZV for this patient. Billing this EBM service through KV may lead to a claim for damages by the health insurance company. Please recheck the documentation of the service.", "ABRD1062": "If the address of facility care does not match with your documented address, please make sure to document location of facility care into service code 0008.", "CONDITIONS_VALIDATION_ERROR": "Expectation: <PERSON><PERSON> ist in Ihrer Region nicht gültig.", "ErrorCode_Validation_Abrg1565": "{{code}} was documented to KV-Schein. This service code can be billed with HÄVG Rechenzentrum in case your patient is contract participant, please make sure you bill with correct data center."}, "missing5099": "The LANR is not from a billing physician. Please update the LANR.", "documentService": "Document service code", "btnCreateNewSchein": "Create new Schein", "createSchein": "Create schein", "FAVHint": "This patient is enrolled into FaV contract. All service codes must be billed with MEDIVERBUND except for the organised emergency service. Please check the billing information provided to the KV.", "HZVHint": "This patient is enrolled into HzV contract. All service codes must be billed with HÄVG except for the organised emergency service. Please check the billing information provided to the KV."}, "Timeline": {"entryRemove": "Timeline entry removed", "entryRemoveFailed": "Failed to remove timeline entry", "sortByCategory": "Sort by category", "headerTitle": "Timeline", "headerFilterLabel": "Filter", "searchTimelinePlaceholder": "Keyword", "filterByTypePlaceholder": "All types", "filterByTypeSelectedText": "Types {{numberOfTypes}}", "typeAnamnese": "Anamnese", "typeFinding": "Finding", "typeDiagnose": "Diagnosis", "typeDiagnoseAd": "Anamnestic Diagnosis", "typeDiagnoseDd": "Permanent Diagnosis", "typeService": "Service Code", "typeTherapy": "Therapy", "typeNote": "Note", "typeMedicinePlan": "Medication Plan", "typeMedicinePrescription": "Medication", "typeHimi": "HIMI", "typeHeimi": "HEIMI", "typePatientMedical": "Patient Medical", "typeLab": "Lab", "typeForm": "Form", "typeValidationError": "Validation Error", "typeCalendar": "Calendar", "typeRoom": "Room", "typeMailItem": "E-mail", "typeDoctorLetter": "Doctor Letter", "typeBillingPatient": "Billing", "typeServiceGoa": "Service GOÄ", "typeEhic": "EHIC", "typePsychotherapy": "Psychotherapy", "typeEdmpenrollment": "DMP Enrollment", "typeEdmpenrollmentDocument": "DMP", "typeMedicine": "<PERSON>", "typeDiga": "DiGA", "typeDocumentManagement": "Document Management", "typeArriba": "Arriba", "typeGdt": "GDT", "typeLdt": "LDT", "typeEdokuDocument": "eHKS", "confirmDeleteDialog_title": "Confirm Deletion", "confirmDeleteDialog_message": "You are about to delete this entry.\n\nNote: The entry will be permanently deleted after {{duration}} days.\nOnly admin users can restore entries given before the deadline expires.\n\nDo you really want to delete it?", "confirmDeleteDialog_confirmButton": "Yes, delete", "confirmDeleteDialog_cancelButton": "No", "timelineContent_emptyMessage": "Nothing documented yet", "timelineContent_emptySuggestion": "Start typing below to see something here", "timelineContent_noResultFound": "There is no documentation for this encounter yet", "AKA_ABRD1544_errorMessage": "Um die Leistung durchführen zu können, benötigen wir die BSNR und LANR des über weisenden Arztes.", "timelineContent_diagnoseEntry_actionDelete": "Delete", "timelineContent_diagnoseEntry_searchDiagnosePlaceholder": "Search diagnosis…", "timelineContent_diagnoseEntry_searchCertaintyPlaceholder": "Search certainty…", "timelineContent_diagnoseEntry_searchLateralityPlaceholder": "Search laterality…", "timelineQuarter_missingService0000_actionButtonLabel": "Hinzufügen", "timelineEntryStatus_submittedMessage": "Submitted to data centre", "timelineEntryStatus_addedByMessage": "Added by {{name}}", "timelineEntryStatus_submittedMessage_V2": "Successfully invoiced by {{name}}", "timelineEntryStatus_restoreByMessage": "Restored by {{name}}", "timelineEntryStatus_removeByMessage": "Removed by {{name}}", "timelineEntryStatus_lastEditedByMessage": "Last edited by {{name}}", "billingTo": "Billing to {{name}}", "lanrBsnr": "LANR: {{lanr}} / BSNR: {{bsnr}}", "havgID": "HÄVG-ID: {{havgId}} / VP-ID: {{havgVpId}}", "mediVPId": "MEDI-ID: {{mediId}} / VP-ID: {{mediVpId}}", "timelineEntryStatus_manualTakeover": "<PERSON> transferred by {{name}}", "timelineService_careFacility_label": "Care Facility Details", "timelineService_careFacility_missingMessage": "The name and location of the care facility must be documented for the \"Pflegeheimpauschale (0008)\" service.", "timelineService_materialCost_label": "Material cost", "timelineService_materialCost_costDescription_label": "Code description", "timelineService_materialCost_costAmount_label": "Cost Amount (EUR)", "timelineService_materialCost_saveAction_label": "Save", "timelineService_materialCost_cancelAction_label": "Cancel", "timelineService_materialCost_editAction_label": "Edit", "timelineService_materialCost_deleteAction_label": "Delete", "timelineContent_referrerInfo_label": "Referrer information", "timelineQuarter_save_fav_referral_ok": "Referral information", "editEntryDate": "Edit entry date", "more": "More", "OMIMG": "OMIM-G", "OMIMP": "OMIM-P", "dateAtTime": "{{date}} at {{time}}", "textsize": "TEXT SIZE", "timelineTextSizeBtn": "Timeline text size", "timelineService_care_facility_details": "Care Facility Details", "ReplacedWithServiceCodeWhenBilling": "This service will be replaced by service with code {{code}} when billing based on patient's age", "name": "Name", "location": "Location", "add": "Add", "save": "Save", "cancel": "Cancel", "email": "E-mail", "patientBillEntryText": "BRIEF - Patient bill - {{billingDoctor}}", "g81EHICEntryText": "Forms - (G81) Patient’s Declaration European Health Insurance", "g81EHICEntryHint": "Form was saved. Please print the document.", "restorationMode": "History and restore", "jumpStart": "Jump to the start of the focused Schein", "jumpEnd": "Jump to the end of the focused Schein", "AUTO_CREATE_BY_ABRG921": "The pseudo service code to identify a preventive treatment case has been added", "external": "External", "noResult": "No Results. Please detail your search.", "saveChanges": "Save changes", "viewMore": "View more", "hide": "<PERSON>de"}, "PatientSearch": {"enter": "Enter", "noPatientFound": "No patient found", "createPatient": "Create patient", "defaultPlaceHolder": "Search patient by last name, first name, date of birth, additional SKT information", "datePlaceHolder": "dd.mm.yyyy", "focusPlaceHolder": "Type patient name or \"/\" for advanced search", "mixNameText": "Last name, First name", "patientNumberText": "Patient ID", "headerAdvanceSearch": "or select a search option below", "insuranceNumberText": "Insurance number", "birthDateText": "Date of birth", "additionalSKTInforText": "Additional SKT information (FK 4124)", "emailText": "E-mail", "mobileText": "Mobile number"}, "PatientNavBar": {"enter": "Enter", "noPatientFound": "No Patient Found", "createPatient": "Create patient", "defaultPlaceHolder": "Search patient by name, birthday, insurance number", "focusPlaceHolder": "Type patient name or \"/\" for advanced search"}, "MedicationPrescription": {"gRez": "G-Rez", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez"}, "TimeLineFormDetail": {"gRez": "G-Rez", "kRez": "K-Rez", "btm": "BTM", "tRez": "T-Rez", "tagPrivate": "Private", "dj": "Dj", "autIdem": "aut-idem", "refillForm": "Refill", "viewForm": "View form", "editForm": "Edit form", "msgDidNotPickup": "Did not pick up", "lblDidNotPickup": "<PERSON> as \"Did not pick up\"", "didNotPickupSuccess": "Marked as \"Did not pick up\"", "didNotPickupError": "Failed to execute. Please try again.", "printOutHint": "Prescription has been prepared. To complete, please print it out.", "more": "More", "removeForm": "Remove", "viewQrCode": "View QR Code", "erezeptLabel": "ePrescription", "erezeptPartialPrescriptionLabel": "ePrescription - partial prescription", "medicationLabel": "Medication"}, "MedicationPlanHistoryEntry": {"medicationPlan": "BMP", "addedAMedication": "Added a medication", "updatedAMedication": "Updated a medication", "deleteMedication": "Removed a medication", "more": "More", "tradeName": "Trade name", "substance": "Substance {{index}}", "unit": "Unit", "drugForm": "Drug form", "intakeInterval": "Intake interval", "hint": "Hint", "reason": "Reason", "additionalLine": "Additional Line"}, "TextModule": {"other": "Other"}, "ActionChainTrigger": {"description": "Description", "reset": "Reset", "run": "Run", "resetSuccess": "Reset successful", "removeStepError": "You cannot remove the last step", "labelCertainty": "Certainty", "labelLaterality": "Laterality", "runActionChain": "Run action chain:", "runActionChainKey": "Shift + Enter", "alertRestrictedAction": "Please select the anamnestic diagnoses and/or service codes to include in the action chain. Entries that are not marked will not be included.", "alertInvalidAction": "Invalid diagnoses and/or service codes have been automatically excluded from the action chain. It needs to be manually documented again in the Composer. If this error persists, please contact admin to adjust the action chain.", "Category": {"VitalParameter": "Vital Parameter", "Form": "Form", "Medication": "Medication", "Service": "Service", "Job": "Job", "Allergy": "Allergy", "Anamnesis": "Anam<PERSON><PERSON>", "Findings": "Finding", "Therapy": "Therapy", "Note": "Note", "AnamnesticDiagnose": "Anamnestic Diagnosis", "AcuteDiagnose": "Acute Diagnosis", "ServiceChain": "Service chain", "PermanentDiagnose": "Permanent Diagnosis", "Customize": "Customize"}, "VitalParameters": {"labelVitalParameters": "Vital Parameter", "AmountOfBirth": "Amount Of Birth", "AmountOfChildren": "Amount Of Children", "AmountOfPregnancies": "Amount Of Pregnancies", "BloodPressure": "Blood Pressure", "Creatinine": "Creatinine", "DateOfPlannedBirth": "Pregnancy due date", "HeartFrequency": "Heart Frequency", "Height": "Height", "IsBreastfeeding": "Is breastfeeding", "IsPregnant": "<PERSON>", "Weight": "Weight"}, "Form": {"form": "Form", "Muster_1": "Form 1", "Muster_2B": "Form 2", "Muster_4": "Form 4", "Muster_6": "Form 6", "Muster_13": "Form 13", "Muster_15": "Form 15", "Muster_16": "Form 16"}}, "CalendarEntry": {"patientPresentIn": "Patient Present In", "waitingTimeIs": "Waiting time is", "minutes": "minutes", "minute": "minute", "note": "Note", "startTreatmentToDocument": "Start treatment to document", "startTreatment": "Start Treatment", "stopTreatmentToDocument": "Stop treatment to document", "completeTreatment": "Complete Treatment"}, "VersionHistory": {"versionHistory": "Version history for {{date}}", "madeOn": "Made on", "activity": "Activity", "madeBy": "Made by", "viewVersionHistory": "View version history", "changedTo": "Changed to", "titleConfirmDialog": "Restore the version for the selected entry?", "contentConfirmDialog": "The selected timeline entry will revert to the version from {{date}}.", "btnNo": "No", "btnYes": "Yes, restore", "restoreSuccess": "Timeline entry restored", "Add": "Created", "Remove": "Removed", "Edit": "Updated", "Restore": "Restored", "afterDays": "{{date}}d", "afterDaysMsg": "Permanently delete after {{date}} day(s)", "contentRestore": "(empty)"}, "DoctorSample": {"doctorSample": "<PERSON>"}, "DigaEntry": {"pznExists": "PZN found. Refill with DiGA data", "invalidDiga": "DiGA no longer eligible for prescription", "processingRefill": "Processing refill...", "diga": "DiGA", "more": "More", "actionRemove": "Remove", "viewForm": "View form"}, "ArribaEntry": {"arriba": "Arriba", "more": "More", "actionRemove": "Remove", "description": "Imported from Arriba", "errorStartMsg": "<PERSON><PERSON><PERSON> cannot be opened, please try again later.", "startArribaTooltip": "Start Arriba", "ErrorCode_Arriba_Not_Eligible": "The patient is not eligible for Arriba"}, "HistoryDialog": {"title": "Profile Access History of {{patientName}}", "openBy": "Opened By", "openOn": "Opened On", "today": "Today at {{date}}"}, "GDTExport": {"GDTExportTitle": "{{type}} export ({{name}})", "exportName": "{{type}} Export Name", "treatmentDate": "Treatment date", "readingDate": "Reading date", "exportGDT": "{{name}} {{type}} exported", "startGDTExport": "Start {{type}} Export", "gdtBtnText": "Interface", "failedToExportGDTTitle": "Failed to export {{type}}", "startGDTExportAgain": "Start {{type}} export again", "treatmentTime": "Treatment time", "readingTime": "Reading time"}, "CustomizeEntry": {"more": "more", "editEntry": "Edit entry", "actionRemove": "Remove"}, "Verah": {"title": "Activate VERAH® TopVersorgt", "content": "Patient ist für die Teilnahme an VERAH ® TopVersorgt geeignet. Bitte beachten Si<PERSON>, dass Schulungsfähigkeit des Patienten vorliegen muss (vgl. Anlage 14, Anhang 7) – zur Unterstützung können Sie sich am VERAH-TopVersorgt-Leitfaden orientieren.", "cancelText": "Close", "openGuidelines": "Open guidelines"}}