{"externalAddress": {"title": "Address Book", "modalTitle": "Hospitals and practices", "placeHolder": "Search by BSNR, LANR, address name", "areaTitle": "Area of expertise", "btnCancel": "Cancel", "btnSelect": "Select colleague", "createExternalAddress": "Create external address", "generalInfo": "General info", "doctorInfo": "Doctor info", "contactInfo": "Contact info", "addressType": "Address type", "addressName": "Address name", "street": "Street", "number": "Number", "postalCode": "Postal code", "city": "City", "asvTeamNumber": "ASV Team Number", "titleField": "Title", "firstName": "First name", "lastName": "Last name", "salutation": "Salutation", "intentWord": "Intend word", "additionalName": "Additional name", "areaOfExpertise": "Area of expertise", "expertise": "Expertise", "phoneNumber": "Phone number", "mobilePhoneNumber": "Mobile phone number", "emailAddress": "E-mail address", "fax": "Fax", "contactPerson": "Contact person", "add": "Add", "cancel": "Cancel", "save": "Update", "practice": "Practice", "hospital": "Hospital", "other": "Other", "generalInfoBsnrRequired": "BSNR is required", "generalInfoAddressTypeRequired": "Address type is required", "generalInfoAddressTypeInvalid": "Address type is invalid", "notExceedCharacter": "Maximum length is {{limit}}.", "titleDefault": "-", "titleV1": "Dr.", "titleV2": "Dr. Dr.", "titleV3": "Prof.", "titleV4": "Prof. Dr.", "titleV5": "Prof. Dr. Dr.", "titleV6": "PD Dr.", "titleV7": "PD Dr. Dr.", "titleV8": "Sr. Dr.", "titleV9": "Sr.", "noResults": "No options. Press enter to choose this title.", "invalidMobilePhone": "Invalid mobile phone number", "areaOfExpertisePlaceholder": "Select an expertise", "invalidFormat": "{{fieldName}} has invalid format. Format should be: {{pattern}}.", "digitRule": "{{fieldName}} (format {{pattern}}) has invalid p value.", "errAsvTeamnumber": "Must include 9 digits", "errLanr": "Must include 9 digits", "errLanrRequired": "LANR is required", "createSuccess": "External address created", "createFailed": "Failed to create external address", "create": "Create", "editExternalAddress": "Edit external address", "editSuccess": "External address updated", "editFailed": "Failed to update external address", "removeExternalAddress": "Remove external address?", "editDoctor": "Edit doctor", "createDoctor": "Create Doctor", "removeDoctor": "Remove doctor?", "no": "No", "yesRemove": "Yes, remove", "removeSuccess": "External address removed", "removeFailed": "Failed to remove external address", "missingDoctorAddress": "Practice Address and Doctor Name are required. Please update the information to take over receiver information.", "removeCannotUndone": "This action cannot be undone.", "yesLeave": "Yes, leave", "areYouSure": "Leave without saving?", "contentWillNotSaved": "Unsaved changes will be lost. This action cannot be undone.", "missingDoctorName": "Doctor name is missing. Please click on the edit icon to add further details."}, "externalTable": {"bsnr": "BSNR", "nbsnr": "(N)BSNR", "addressName": "address name", "lanr": "LANR", "nameDoctor": "doctor", "areaOfExpertise": "area of expertise", "addressDetail": "address", "removeExternalAddress": "Remove", "editExternalAddress": "Edit", "selfCreated": "Self-created"}}