import { proxy, useSnapshot } from 'valtio';

import { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';

interface CardProfileStore {
  cardProfile: PatientInfo | undefined;
}

interface CardProfileAction {
  setCardProfile(card: PatientInfo): void;
  terminateInfor(): void;
}

export const cardProfileStore = proxy<CardProfileStore>({
  cardProfile: undefined,
});

export const cardProfileActions: CardProfileAction = {
  setCardProfile(card: PatientInfo) {
    cardProfileStore.cardProfile = card;
  },
  terminateInfor() {
    cardProfileStore.cardProfile = undefined;
  },
};

export const useCardInformationStore = (): [
  CardProfileStore,
  CardProfileAction
] => {
  const snapshot = useSnapshot(cardProfileStore);
  return [snapshot, cardProfileActions];
};
