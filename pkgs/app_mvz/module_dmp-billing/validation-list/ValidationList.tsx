import React, {
  memo,
  useState,
  useCallback,
  useEffect,
  useContext,
} from 'react';
import I18n from '@tutum/infrastructure/i18n';
import Collapse from '@tutum/mvz/components/collapsev2';
import Table from '@tutum/design-system/components/Table';

import { Box, Button, Flex, IMenuItem } from '@tutum/design-system/components';

import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import {
  useDmpBillingStore,
  dmpBillingActions,
  DmpBillingStore,
  ValidationListForValidation,
} from '../dmp.store';
import { IStepDMPBilling } from '../dmp.type';
import {
  customStyles,
  genColumns,
  getValidationStatistic,
} from '../dmp.helper';
import {
  DataCenterInfo,
  getEnrollmentDocument,
} from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import DMPDocumentationOverview from '@tutum/mvz/module_patient-management/patient-file/DMPDocumentationOverview';
import {
  DMPBillingFieldsValidationResult,
  EnrollmentDocumentInfoModel,
  DocumentType,
  FieldValidationResult,
} from '@tutum/hermes/bff/edmp_common';
import { PatientProfileResponse } from '@tutum/hermes/bff/app_mvz_patient_profile';
import { isEmpty } from 'lodash';
import PatientManagementService from '@tutum/mvz/module_patient-management/PatientManagement.service';
import GlobalContext from '@tutum/mvz/contexts/Global.context';

export interface Props {
  className?: string;
}

const ValidationList = ({ className }: Props) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.DmpBilling>({
    namespace: 'Billing',
    nestedTrans: 'DmpBilling',
  });
  const { t: tBilling } = I18n.useTranslation<keyof typeof BillingI18n.Billing>(
    {
      namespace: 'Billing',
      nestedTrans: 'Billing',
    }
  );
  const {
    loading,
    currentStep,
    validationList,
    selectedList,
    dMPBillingFieldsValidationResults,
  } = useDmpBillingStore();
  const {
    setDmpValidationList,
    setCurrentStep,
    checkValidation,
    resetBillingSelection,
    setDataCenter,
    setDMPBillingFieldsValidationResults,
  } = dmpBillingActions;

  const { ed, fd, patientNumbers } = getValidationStatistic(validationList);

  const [currentDocumentType, setCurrentDocumentType] = useState<DocumentType>(
    DocumentType.DocumentType_ED
  );
  const [currentDmpDocument, setCurrentDmpDocument] = useState<string[]>([]);
  const [currentDocumentationOverviews, setCurrentDocumentationOverviews] =
    useState<{
      [documentId: string]: EnrollmentDocumentInfoModel & {
        patientInfo: PatientProfileResponse;
        errors: FieldValidationResult[];
      };
    }>({});
  const [currentSelection, setCurrentSelection] =
    useState<DMPBillingFieldsValidationResult | null>(null);
  const [activePatientIdx, setActivePatientIdx] = useState<number>(0);
  const [selectedDocumentationList, setSelectedDocumentationList] = useState<
    ValidationListForValidation[]
  >([]);
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();

  const onSelectRows = ({ selectedRows }) => {
    setDmpValidationList(selectedRows);
  };

  const onEditBillingSelection = () => {
    resetBillingSelection();
    setDmpValidationList();
    setCurrentStep(IStepDMPBilling.BILLING_SELECTION);
  };

  const checkValidations = async (
    selectedList: ValidationListForValidation[]
  ) => {
    const resp = await checkValidation(selectedList, currentLoggedinUser.id!);

    if (!resp) {
      setDMPBillingFieldsValidationResults([]);
      return;
    }

    const dMPBillingFieldsValidationResults =
      resp.dMPBillingFieldsValidationResults || [];

    setSelectedDocumentationList(selectedList);
    setDMPBillingFieldsValidationResults(dMPBillingFieldsValidationResults);

    if (
      dMPBillingFieldsValidationResults[0].documentId ===
      currentSelection?.documentId
    ) {
      DmpBillingStore.loading.checkValidation = false;
      return;
    }

    setCurrentSelection(dMPBillingFieldsValidationResults[0]);
    setActivePatientIdx(0);
  };

  const onSelectDataCenter = (
    dataCenters: DataCenterInfo[],
    item: IMenuItem,
    rowId: string
  ) => {
    setDataCenter(dataCenters, item, rowId);
  };

  const getDocumentationOverviewData = useCallback(async () => {
    if (
      !currentSelection ||
      currentDocumentationOverviews[currentSelection.documentId]
    ) {
      return;
    }

    const { data } = await getEnrollmentDocument({
      enrollmentDocumentId: currentSelection.documentId,
    });

    if (!data) {
      DmpBillingStore.loading.checkValidation = false;
      return;
    }

    // TODO use react query
    const patientInfo = await PatientManagementService.getPatientProfileById(
      data.enrollmentDocumentInfoModel.patient.patientId
    );

    setCurrentDocumentationOverviews((prevValues) => ({
      ...prevValues,
      [currentSelection.documentId]: {
        ...data.enrollmentDocumentInfoModel,
        patientInfo: patientInfo!,
        errors: currentSelection.fieldValidationResults,
      },
    }));
    setCurrentDocumentType(
      data.enrollmentDocumentInfoModel.documentationOverview.documentType
    );
    setCurrentDmpDocument([
      data.enrollmentDocumentInfoModel.documentationOverview.dMPLabelingValue,
    ]);
    DmpBillingStore.loading.checkValidation = false;
  }, [currentSelection, currentDocumentationOverviews]);

  const onNextPatient = useCallback(() => {
    setCurrentSelection(
      dMPBillingFieldsValidationResults[activePatientIdx + 1]
    );
    setActivePatientIdx(activePatientIdx + 1);
  }, [activePatientIdx, dMPBillingFieldsValidationResults]);

  const onPrevPatient = useCallback(() => {
    setCurrentSelection(
      dMPBillingFieldsValidationResults[activePatientIdx - 1]
    );
    setActivePatientIdx(activePatientIdx - 1);
  }, [activePatientIdx, dMPBillingFieldsValidationResults]);

  useEffect(() => {
    if (currentSelection) {
      getDocumentationOverviewData();
    }
  }, [currentSelection]);

  return (
    <div
      className={className}
      style={{
        height:
          currentStep === IStepDMPBilling.VALIDATION_LIST
            ? 'calc(100vh - 220px)'
            : '42px',
      }}
    >
      <Collapse
        title={t('stepValidateList')}
        viceTitle={
          <p className="text-title">
            {t('titleStepValidate', {
              ed,
              fd,
              patientNumbers: patientNumbers.length,
            })}
          </p>
        }
        initialOpen={currentStep === IStepDMPBilling.VALIDATION_LIST}
        stepNumber={2}
        isActive={currentStep === IStepDMPBilling.VALIDATION_LIST}
      >
        <Box className="sl-table-validation-list">
          <Table
            columns={genColumns({
              t,
              selectedList,
              onSelectDataCenter,
              tBilling,
            })}
            customStyles={customStyles}
            selectableRows
            onSelectedRowsChange={onSelectRows}
            data={validationList}
            noHeader
            fixedHeader
            // overflowY
            sortServer
          />
        </Box>

        <Flex className="button_section" justify="flex-end" mt={20}>
          <Button
            minimal
            outlined
            intent="primary"
            large
            loading={loading.checkValidation}
            onClick={onEditBillingSelection}
          >
            {t('editBillingSelection')}
          </Button>
          &nbsp;&nbsp;
          <Button
            minimal
            outlined
            intent="primary"
            large
            loading={loading.checkValidation}
            disabled={!selectedList?.length}
            onClick={() => checkValidations(selectedList)}
          >
            {t('checkValidation')}
          </Button>
          &nbsp;&nbsp;
          <Button
            intent="primary"
            large
            loading={loading.checkValidation}
            onClick={() => checkValidations(validationList)}
          >
            {t('finishAll')}
          </Button>
        </Flex>
      </Collapse>

      {!!currentSelection && !isEmpty(currentDocumentationOverviews) && (
        <DMPDocumentationOverview
          isOpen
          isBilling
          isLoadingCheckValidation={DmpBillingStore.loading.checkValidation}
          activePatientIdx={activePatientIdx}
          totalReviewingPatients={dMPBillingFieldsValidationResults.length}
          currentDmp={currentDmpDocument}
          currentDocumentType={currentDocumentType}
          currentDocumentationOverview={
            currentDocumentationOverviews[currentSelection.documentId]
          }
          validationErrors={
            currentDocumentationOverviews[currentSelection.documentId]?.errors
          }
          patientInfo={
            currentDocumentationOverviews[currentSelection.documentId]
              ?.patientInfo
          }
          closeModal={() => {
            setCurrentDocumentationOverviews({});
            setCurrentDmpDocument([]);
            setCurrentDocumentType(DocumentType.DocumentType_ED);
            setCurrentSelection(null);
          }}
          onNextPatient={onNextPatient}
          onPrevPatient={onPrevPatient}
          onFinishAll={() => checkValidations(selectedDocumentationList)}
        />
      )}
    </div>
  );
};

export default memo(ValidationList);
