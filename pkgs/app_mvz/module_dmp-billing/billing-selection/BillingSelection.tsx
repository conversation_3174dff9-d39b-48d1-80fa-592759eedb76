import React, { memo, useContext, useEffect, useMemo } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import useToaster from '@tutum/mvz/hooks/useToaster';
import Collapse from '@tutum/mvz/components/collapsev2';
import {
  BodyTextL,
  Button,
  Flex,
  alertError,
} from '@tutum/design-system/components';
import {
  RadioGroup,
  Radio,
  Checkbox,
  Divider,
} from '@tutum/design-system/components/Core';
import formUtil from '@tutum/infrastructure/utils/form.util';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { useDmpBillingStore, dmpBillingActions } from '../dmp.store';
import { IStepDMPBilling } from '../dmp.type';
import { DMP<PERSON>abeling, Doctor } from '@tutum/hermes/bff/edmp_common';
import { getDMPBillingSelection } from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import GlobalContext from '@tutum/mvz/contexts/Global.context';

export interface Props {
  className?: string;
}

const BillingSelection = ({ className }: Props) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.DmpBilling>({
    namespace: 'Billing',
    nestedTrans: 'DmpBilling',
  });
  const toast = useToaster();

  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();
  const bsnr = currentLoggedinUser?.bsnr;

  const {
    currentStep,
    billingSelection,
    quarters,
    doctors,
    loading,
    dmpLabels,
  } = useDmpBillingStore();
  const {
    setQuarter,
    setDoctor,
    setCurrentStep,
    selectDoctorForBilling,
    addOrRemoveLabel,
    setDmpLabeling,
    troubleShoot,
  } = dmpBillingActions;

  useEffect(() => {
    if (currentStep !== IStepDMPBilling.BILLING_SELECTION) return;
    const { quarter, year } = billingSelection;
    if (!quarter || !year) return;
    getDMPBillingSelection({ quarter, year })
      .then(({ data }) => {
        const res = data ? data : { doctors: [], labelings: [] };
        setDoctor(res.doctors);
        setDmpLabeling(res.labelings);
      })
      .catch((e) => alertError(e.message, { toaster: toast }));
  }, [currentStep, billingSelection.quarter, billingSelection.year]);

  const startTroubleshooting = () => {
    if (!bsnr) return;

    const { quarter, year, doctor, dmpPrograms } = billingSelection;
    if (!doctor || !quarter || !year) return;
    troubleShoot({
      quarterRange: { quarter, year },
      doctorId: doctor.doctorId,
      dMPLabelingValues: dmpPrograms.map((d) => d.value),
      bsnrCode: bsnr,
    });
    setCurrentStep(IStepDMPBilling.VALIDATION_LIST);
  };

  const onSelectProgram = (label: DMPLabeling) => {
    addOrRemoveLabel(label);
  };

  const onSelectDoctor = (doctor: Doctor | undefined) => {
    selectDoctorForBilling(doctor);
  };

  const selectedInfor = useMemo(() => {
    const { year, quarter, doctor } = billingSelection;

    if (!doctor?.doctorId) return '';

    const { fullName } = doctor;

    const infor = `<strong>Q${quarter}.${year} -</strong> ${fullName} - `;

    return infor;
  }, [billingSelection]);

  return (
    <div className={className}>
      <Collapse
        title={<div className="title">{t('stepBillingSelect')}</div>}
        viceTitle={
          <div>
            <div
              className="viceTitle--line1"
              dangerouslySetInnerHTML={{
                __html: selectedInfor + t('checkDMPDocumentation'),
              }}
            />
            {currentStep === IStepDMPBilling.BILLING_SELECTION &&
              !billingSelection.dmpPrograms.length && (
                <p className="viceTitle--line2">{t('pleaseSelectDMP')} </p>
              )}
          </div>
        }
        stepNumber={1}
        initialOpen={currentStep === IStepDMPBilling.BILLING_SELECTION}
        isActive={currentStep === IStepDMPBilling.VALIDATION_LIST}
      >
        <Flex className="content">
          <Flex className="content-col" column>
            <div className="header-col">
              <BodyTextL fontWeight="SemiBold" textTransform="uppercase">
                {t('quarter')}
              </BodyTextL>
            </div>
            <div className="body">
              <RadioGroup
                selectedValue={
                  billingSelection.quarter && billingSelection.year
                    ? `Q${billingSelection.quarter}.${billingSelection.year}`
                    : undefined
                }
                onChange={(e: React.FormEvent<HTMLInputElement>) => {
                  const quarter = e.currentTarget.value.split('.')[0][1],
                    year = e.currentTarget.value.split('.')[1];
                  setQuarter(+quarter, +year);
                }}
              >
                {quarters.map((q, index) => (
                  <Radio
                    key={index}
                    label={`Q${q.quarter}.${q.year}`}
                    value={`Q${q.quarter}.${q.year}`}
                  />
                ))}
              </RadioGroup>
            </div>
          </Flex>
          <Flex className="content-col" column>
            <div className="header-col">
              <BodyTextL fontWeight={`SemiBold`} textTransform="uppercase">
                {t('doctor')}
              </BodyTextL>
            </div>
            <div className="body">
              <RadioGroup
                selectedValue={billingSelection?.doctor?.doctorId}
                onChange={(e: React.FormEvent<HTMLInputElement>) => {
                  onSelectDoctor(
                    (doctors || []).find(
                      (d: Doctor) => d.doctorId === e.currentTarget.value
                    )
                  );
                }}
              >
                {doctors.map((d) => (
                  <Radio
                    key={d.doctorId}
                    label={d.fullName}
                    value={d.doctorId}
                  />
                ))}
              </RadioGroup>
            </div>
          </Flex>
          <Flex className="content-col" column>
            <div className="header-col">
              <BodyTextL fontWeight={`SemiBold`} textTransform="uppercase">
                {t('dmpProgramme')}
              </BodyTextL>
            </div>
            <div className="body">
              {dmpLabels.map((l) => (
                <Checkbox
                  key={l.name}
                  label={l.germanName}
                  value={l.value}
                  onChange={() => onSelectProgram(l)}
                />
              ))}
            </div>
          </Flex>
        </Flex>
        <Flex
          className="button_troubleshooting"
          mx={16}
          justify="flex-end"
          mb={16}
        >
          <Button
            intent="primary"
            onClick={startTroubleshooting}
            loading={loading.billingSelection}
            large
            disabled={
              billingSelection.dmpPrograms.length === 0 ||
              !billingSelection.doctor
            }
          >
            {t('troubleshooting')}
          </Button>
        </Flex>
        <Divider style={{ margin: 0 }} />
      </Collapse>
    </div>
  );
};

export default memo(BillingSelection);
