import React, { useCallback, useEffect, useState } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import Table from '@tutum/design-system/components/Table';

import {
  alertError,
  BodyTextM,
  Button,
  Flex,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';

import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import {
  genColumnsHistory,
  getDownloadUrlForDmpBillingFile,
  historytableStyle,
} from '../dmp.helper';
import {
  DMPBillingHistoryViewModel,
  GetDMPBillingHistoriesResponse,
  getDMPBillingHistories,
  zipDMPBillingHistories,
} from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import {
  useListenChangeEDMPHistoryStatus,
} from '@tutum/hermes/bff/app_mvz_edmp';
import { Order } from '@tutum/hermes/bff/common';
import useToaster from '@tutum/mvz/hooks/useToaster';
import DownloadBillFileModal from '@tutum/mvz/module_kv_billing/1ClickBilling/DownLoadBillFile/DownloadBillFileModal';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface Props {
  className?: string;
}

const DmpBillingHistory = ({ className }: Props) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.DmpBilling>({
    namespace: 'Billing',
    nestedTrans: 'DmpBilling',
  });
  const { t: tBilling } = I18n.useTranslation<
    keyof typeof BillingI18n['1ClickBilling']
  >({
    namespace: 'Billing',
    nestedTrans: '1ClickBilling',
  });
  const toaster = useToaster();

  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(30);
  const [downloadFile, setDownloadFile] = useState<any>([]);
  const [historyitems, setHistoryitems] =
    useState<GetDMPBillingHistoriesResponse | null>(null);
  const [selectedRows, setSelectedRows] = useState<
    DMPBillingHistoryViewModel[]
  >([]);
  const [currentRow, setCurrentRow] =
    useState<DMPBillingHistoryViewModel | undefined>(undefined);

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPage(1);
    setRowsPerPage(currentRowsPerPage);
  };
  const onChangePage = (page: number) => {
    setPage(page);
  };

  const fetchDMPBillingHistories = useCallback(() => {
    getDMPBillingHistories({
      paginationRequest: {
        page,
        pageSize: rowsPerPage,
        sortBy: '',
        order: Order.ASC,
      },
    })
      .then(({ data }) => {
        setHistoryitems(data);
      })
      .catch((e) =>
        alertError(e.message, { timeout: TOASTER_TIMEOUT_CUSTOM, toaster })
      );
  }, [page, rowsPerPage]);

  const onSelectRows = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const onDownload = async (row: DMPBillingHistoryViewModel) => {
    const fileWithDownloadUrl = await getDownloadUrlForDmpBillingFile(
      row.billingFiles
    );

    setCurrentRow(row);
    setDownloadFile(
      fileWithDownloadUrl.map((f) => ({
        url: f.url,
        fileTitle: f.fileName,
        filePath: f.filePath,
      }))
    );
  };

  const onDownloadZip = async () => {
    const res = await zipDMPBillingHistories({
      dMPBillingHistoryIds: selectedRows.map((row) => row.dMPBillingHistoryId),
    });
    const fileToDownload = await getDownloadUrlForDmpBillingFile([
      res.data?.dMPBillingFile,
    ]);
    fileToDownload.forEach((file) => {
      const link = document.createElement('a');
      link.href = file.url;
      link.setAttribute('download', file.fileName);
      link.click();
      link.parentNode?.removeChild(link);
    });
  };

  useEffect(() => {
    fetchDMPBillingHistories();
  }, [page, rowsPerPage, fetchDMPBillingHistories]);

  useListenChangeEDMPHistoryStatus(() => {
    fetchDMPBillingHistories();
  });

  return (
    <div className={className}>
      <Flex p={16} align="baseline">
        <Button
          intent="primary"
          disabled={!selectedRows.length}
          onClick={onDownloadZip}
        >
          {t('download')}(ZIP)
        </Button>
      </Flex>
      <Table
        columns={genColumnsHistory({ t, onDownload })}
        data={historyitems?.dMPBillingHistories!}
        customStyles={historytableStyle}
        highlightOnHover
        noHeader
        persistTableHead
        keyField="key"
        selectableRows
        noDataComponent={
          <BodyTextM
            color={COLOR.TEXT_TERTIARY_SILVER}
            style={{ marginTop: 24 }}
          >
            {tBilling('noData')}
          </BodyTextM>
        }
        onSelectedRowsChange={onSelectRows}
        pagination
        paginationPerPage={rowsPerPage}
        paginationDefaultPage={page}
        paginationServer
        paginationResetDefaultPage
        style={{ paddingBottom: 0, marginBottom: 0 }}
        paginationTotalRows={historyitems?.paginationResponse.total}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
      />

      <DownloadBillFileModal
        isOpen={!!downloadFile?.length}
        files={downloadFile}
        title={t('downloadFiles')}
        description={t('downloadFileContent')}
        currentRow={currentRow}
        onClose={() => setDownloadFile([])}
      />
    </div>
  );
};

export default DmpBillingHistory;
