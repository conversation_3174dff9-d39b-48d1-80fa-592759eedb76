import React from 'react';
import moment from 'moment';
import { uniqBy } from 'lodash';

import {
  Flex,
  BodyTextM,
  BodyTextS,
  ReactSelect,
  Tag,
  IMenuItem,
  Svg,
} from '@tutum/design-system/components';
import FormUtils, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import {
  IDataTableColumn,
  IDataTableStyles,
} from '@tutum/design-system/components/Table';
import { IQuarter } from '../module_kv_billing/KVBilling.store';
import { IValidateParams } from './dmp.type';
import {
  DMPBillingHistoryViewModel,
  DMPBillingValidationModel,
  DataCenterInfo,
} from '@tutum/hermes/bff/app_mvz_edmp';
import {
  DMPBillingFile,
  DMPDocumentationStatus,
  DMPDocumentationType,
  DocumentType,
} from '@tutum/hermes/bff/edmp_common';
import { Tooltip } from '@tutum/design-system/components/Core';
import { ValidationListForValidation } from './dmp.store';
import { COLOR } from '@tutum/design-system/themes/styles';
import { getPresignedGetURL } from '@tutum/hermes/bff/legacy/app_mvz_file';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import { default as DateTimeUtil } from '@tutum/infrastructure/utils/datetime.util';

const readCardIcon = '/images/patient/selector/read-card-icon.svg';
const downloadIcon = '/images/download.svg';

interface IGenColumnsParams {
  t: IFixedNamespaceTFunction<keyof typeof BillingI18n.DmpBilling>;
  tBilling: IFixedNamespaceTFunction<keyof typeof BillingI18n.Billing>;
  selectedDataCenter?: any;
  selectedList: ValidationListForValidation[];
  onSelectDataCenter: (
    datacenters: DataCenterInfo[],
    item: IMenuItem,
    rowId: string
  ) => void;
}

const documentMapping = {
  [DocumentType.DocumentType_ED]: 'ED',
  [DocumentType.DocumentType_FD]: 'FD',
  [DocumentType.DocumentType_PD]: 'PED',
};

export const dmpHasTransferLetter = ['02'];

export const genColumns = ({
  t,
  selectedList,
  onSelectDataCenter,
  tBilling,
}: IGenColumnsParams): IDataTableColumn<DMPBillingValidationModel>[] => [
  {
    id: 'dmpCaseNumber',
    width: '11%',
    minWidth: '200px',
    name: (
      <Flex justify="space-between" align="center">
        {t('dmpCaseNumber')}
      </Flex>
    ),
    cell: (row) => {
      return (
        <Flex align="center">
          <BodyTextM>{row.dMPCaseNumber}</BodyTextM>
        </Flex>
      );
    },
  },
  {
    id: 'patient',
    width: '19%',
    minWidth: '300px',
    name: (
      <Flex justify="space-between" align="center">
        {t('patient')}
      </Flex>
    ),
    cell: (row) => {
      return (
        <Flex column justify="center" w="100%">
          <Flex justify="space-between" align="center">
            <BodyTextM color={COLOR.TEXT_INFO} fontWeight={600}>
              {row.patient.fullName}
            </BodyTextM>
            {isEVCase(
              row.patient.activeInsurance,
              DateTimeUtil.now(),
              undefined
            ) && (
              <Tooltip content={tBilling('ev')} position="top">
                <Svg
                  className="icon-card"
                  src={readCardIcon}
                  style={{ width: 16, height: 16, marginTop: 4 }}
                />
              </Tooltip>
            )}
          </Flex>
          <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
            {getDateOfBirth(row.patient.dateOfBirth).value}
          </BodyTextS>
        </Flex>
      );
    },
  },
  {
    id: 'insuranceName',
    width: '19%',
    minWidth: '300px',
    name: (
      <Flex justify="space-between" align="center">
        {t('insuranceName')}
      </Flex>
    ),
    cell: (row) => <BodyTextM>{row.insuranceName}</BodyTextM>,
  },
  {
    id: 'dmpProgramme',
    width: '19%',
    minWidth: '300px',
    name: (
      <Flex gap={4} justify="space-between">
        {t('dmpProgramme')}
      </Flex>
    ),
    cell: (row) => {
      return <BodyTextM>{row.dMPLabeling.germanName}</BodyTextM>;
    },
  },
  {
    id: 'dmpDocumentation',
    width: '10%',
    minWidth: '200px',
    name: t('dmpDocumentation'),
    cell: (row) => {
      const types: DMPDocumentationType[] = [
        ...row.dMPDocumentationTypeED,
        ...row.dMPDocumentationTypeFD,
        ...row.dMPDocumentationTypePED,
      ];

      return types.map((t) => (
        <Tag
          key={t.documentationIds?.[0]}
          slColor={
            t.documentationStatus ===
            DMPDocumentationStatus.DMPDocumentationStatus_Complete
              ? COLOR.TAG_BACKGROUND_GREEN_SUBTLE
              : COLOR.TAG_BACKGROUND_RED_SUBTLE
          }
          slStyle="fill"
        >
          {documentMapping[t.documentType]}
        </Tag>
      ));
    },
  },
  {
    id: 'dataCenter',
    minWidth: '230px',
    name: (
      <Flex justify="space-between" align="center">
        {t('dataCenter')}
      </Flex>
    ),
    cell: (row) => {
      const { dataCenters, defaultDataCenter } = row;
      if (dataCenters?.length === 1 && defaultDataCenter) {
        return <BodyTextM>{defaultDataCenter.dataCenter.name}</BodyTextM>;
      }
      const currentSelectedList = selectedList.find((el) => el.id === row.id);

      return (
        <Flex align="center" justify="flex-start" gap={8}>
          <ReactSelect
            className="sl-data-center-select "
            items={
              currentSelectedList?.dataCenters.map((d) => ({
                value: d.id,
                label: d.dataCenter.name,
              })) || []
            }
            selectedValue={
              currentSelectedList?.selectedDataCenter.id ??
              currentSelectedList?.defaultDataCenter.id
            }
            isDisabled={!currentSelectedList}
            onItemSelect={(dataCenter: IMenuItem) => {
              onSelectDataCenter(dataCenters, dataCenter, row.id);
            }}
          />
        </Flex>
      );
    },
  },
];

export const customStyles: IDataTableStyles = {
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
      borderRight: 'none',
      '&:not(:first-child)': {
        borderRight: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
      },
    },
  },
  cells: {
    style: {
      borderRight: 'none',
      '&:not(:first-child)': {
        borderRight: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
      },
      padding: '8px',
      alignItems: 'center',
    },
  },
};

export const dmpBillingQuarters = [0, 1, 2, 3, 4].map((q) => {
  return {
    quarter: +moment(datetimeUtil.now()).subtract(q, 'Q').format('Q'),
    year: +moment(datetimeUtil.now()).subtract(q, 'Q').format('Y'),
  } as IQuarter;
});

export const getValidationStatistic = (
  validationList: DMPBillingValidationModel[]
) => {
  let ed = 0;
  let fd = 0;
  const patientNumbers: number[] = [];
  for (let i = 0; i < validationList.length; i++) {
    const patient = validationList[i].patient;

    if (!patientNumbers.includes(patient.patientNumber)) {
      patientNumbers.push(patient.patientNumber);
    }
    const documentTypeED = validationList[i].dMPDocumentationTypeED;
    const documentTypeFD = validationList[i].dMPDocumentationTypeFD;
    if (documentTypeED) ed = ed + documentTypeED.length;
    if (documentTypeFD) fd = fd + documentTypeFD.length;
  }

  return { ed, fd, patientNumbers };
};

export const genColumnsHistory = ({
  t,
  onDownload,
}): IDataTableColumn<DMPBillingHistoryViewModel>[] => [
  {
    id: 'timePeriod',
    width: '6%',
    minWidth: '50px',
    name: (
      <Flex justify="space-between" align="center">
        {t('timePeriod')}
      </Flex>
    ),
    cell: (row) => {
      return <BodyTextM>{`Q${row.quarter} ${row.year}`}</BodyTextM>;
    },
  },
  {
    id: 'dmpProgram',
    width: '40%',
    minWidth: '300px',
    name: (
      <Flex justify="space-between" align="center">
        {t('dmpProgramme')}
      </Flex>
    ),
    cell: (row) => {
      const dMPLabelings = uniqBy(row.dMPLabelings, 'value');

      return (
        <BodyTextM>
          {dMPLabelings.map((dMP) => dMP.germanName).join(', ')}
        </BodyTextM>
      );
    },
  },
  {
    id: 'doctor',
    width: '12%',
    minWidth: '300px',
    name: (
      <Flex justify="space-between" align="center">
        {t('doctor')}
      </Flex>
    ),
    cell: (row) => {
      const firstname = row.doctor?.firstName;
      const lastname = row.doctor?.lastName;
      return (
        <Tooltip content={firstname + ' ' + lastname} position="top">
          <Tag slStyle="fill" slColor={COLOR.TAG_BACKGROUND_RED_SUBTLE}>
            {row.doctor.fullName}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    id: 'status',
    width: '12%',
    minWidth: '160px',
    name: t('status'),
    selector: (row) => row.dmpBillingStatus,
  },
  {
    id: 'submittedBy',
    width: '12%',
    minWidth: '300px',
    name: (
      <Flex gap={4} justify="space-between">
        {t('submittedBy')}
      </Flex>
    ),
    cell: (row) => {
      const firstname = row.submittedBy?.firstName;
      const lastname = row.submittedBy?.lastName;
      return (
        <Tooltip content={firstname + ' ' + lastname} position="top">
          <Tag slStyle="fill" slColor={COLOR.TAG_BACKGROUND_RED_SUBTLE}>
            {firstname.slice(0, 1) + lastname.slice(0, 1)}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    id: 'submittedTime',
    width: '12%',
    minWidth: '200px',
    name: t('submittedTime'),
    cell: (row) => {
      return (
        <BodyTextM>
          {moment(row.submittedTime).format('DD.MM.YYYY - HH.mm')}
        </BodyTextM>
      );
    },
  },
  {
    id: 'action',
    minWidth: '50px',
    name: (
      <Flex justify="space-between" align="center">
        {' '}
      </Flex>
    ),
    cell: (row) => {
      return (
        <Tooltip content={t('download')} position="left">
          <Flex align="center" justify="center">
            <Svg src={downloadIcon} onClick={() => onDownload(row)} />
          </Flex>
        </Tooltip>
      );
    },
  },
];

export const historytableStyle: IDataTableStyles = {
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
      borderRight: 'none',
      '&:not(:first-child)': {
        borderRight: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
      },
    },
  },
  cells: {
    style: {
      borderRight: 'none',
      padding: '8px',
      alignItems: 'center',
      '&:not(:first-child)': {
        borderRight: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
      },
    },
  },
};

export interface DMPBillingFileWitFullURL {
  url: string;
  fileName: string;
  filePath: string;
  fileType?: string;
}

export const getDownloadUrlForDmpBillingFile = async (
  files: DMPBillingFile[]
): Promise<DMPBillingFileWitFullURL[]> => {
  const promisor = files.map((file) => {
    const data = getPresignedGetURL({
      bucketName: 'bucket-dmpbilling',
      objectName: file.fileName,
    });
    return data;
  });
  const urlDownload = await Promise.all(promisor);
  const res: DMPBillingFileWitFullURL[] = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    for (let j = 0; j < urlDownload.length; j++) {
      const { data } = urlDownload[j];
      if (!data) continue;
      const { presignedURL } = data;

      if (
        presignedURL?.includes(file.fileName) &&
        presignedURL?.includes(file.filePath)
      ) {
        res.push({ ...file, url: presignedURL });
      }
    }
  }
  return res;
};

export const onValidate =
  ({ tCommon }: IValidateParams) =>
  (values) => {
    const validateFields: ValidateField[] = [];

    validateFields.push({
      fieldName: 'quarter',
      validateRule: () => values['quarter']?.length == 0,
      errorMessage: tCommon('fieldRequired'),
    });

    validateFields.push({
      fieldName: 'doctor',
      validateRule: () => values['doctor']?.length === 0,
      errorMessage: tCommon('fieldRequired'),
    });
    validateFields.push({
      fieldName: 'typeOfBilling',
      validateRule: () => values['typeOfBilling']?.length == 0,
      errorMessage: tCommon('fieldRequired'),
    });

    const { errors } = FormUtils.validateForm(validateFields, null);
    return errors;
  };
