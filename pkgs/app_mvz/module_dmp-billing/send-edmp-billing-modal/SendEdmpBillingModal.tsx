import React, { useEffect, useState } from 'react';
import { Formik, Form, FastField } from 'formik';
import {
  Flex,
  Button,
  MainDialog,
  FormGroup2,
  ReactSelect,
  IMenuItem,
  alertSuccessfully,
  alertError,
} from '@tutum/design-system/components';
import {
  Divider,
  Radio,
  RadioGroup,
  Checkbox,
} from '@tutum/design-system/components/Core';
import {
  SendKVForDMPBillingRequest,
  sendKVForDMPBilling,
} from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import AccountManagementUtil from '@tutum/infrastructure/utils/form.util';
import { TypeOfBilling } from '@tutum/hermes/bff/billing_history_common';
import BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import Common from '@tutum/mvz/locales/en/Common.json';
import useToaster from '@tutum/mvz/hooks/useToaster';
import I18n from '@tutum/infrastructure/i18n';
import { FIELD_NAMES, INIT_VALUES, VALID_QUARTER } from '../dmp.const';
import { useDmpBillingStore } from '../dmp.store';
import { onValidate } from '../dmp.helper';
import { getUUID } from '@tutum/design-system/infrastructure/utils';

export interface SendEdmpBillingModalProps {
  isOpen: boolean;
  kvConnectId: string;
  dMPBillingHistoryIds: string;
  className?: string;
  onClose: () => void;
}

const SendEdmpBillingModal = ({
  className,
  isOpen,
  kvConnectId,
  dMPBillingHistoryIds,
  onClose,
}: SendEdmpBillingModalProps) => {
  const toast = useToaster();
  const { doctors } = useDmpBillingStore();
  const { t } = I18n.useTranslation<keyof typeof BillingI18n['1ClickBilling']>({
    namespace: 'Billing',
    nestedTrans: '1ClickBilling',
  });
  const { t: tDmpBilling } = I18n.useTranslation<
    keyof typeof BillingI18n['DmpBilling']
  >({
    namespace: 'Billing',
    nestedTrans: 'DmpBilling',
  });
  const { t: tCommon } = I18n.useTranslation<
    keyof typeof Common.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const [initValues, setInitValues] = useState(INIT_VALUES);

  useEffect(() => {
    const quarter = VALID_QUARTER[0].value;
    setInitValues({
      ...initValues,
      quarter: quarter + '',
      markAsCompletedBilling: false,
    });
  }, [doctors]);

  const onSubmitForm = async (
    values: typeof INIT_VALUES,
    { setSubmitting, resetForm }
  ) => {
    if (!dMPBillingHistoryIds) return;
    const time = values.quarter;

    const eDmpPayload: SendKVForDMPBillingRequest = {
      quarter: +time.split('-')[0],
      year: +time.split('-')[1],
      kvConnectId: kvConnectId || getUUID(), //TODO: hardcode getUUID for testing, will be removed after kvConnect is ready
      doctorId: values.doctor,
      typeOfBilling: values.typeOfBilling,
      markAsCompletedBilling: values.markAsCompletedBilling,
      dMPBillingHistoryId: dMPBillingHistoryIds,
    };

    try {
      if (!eDmpPayload) return;
      await sendKVForDMPBilling(eDmpPayload);
      alertSuccessfully(t('billSent'), { toaster: toast });
      onClose();
    } catch (e) {
      alertError(
        e.response?.data?.serverError
          ? t(e.response.data.serverError)
          : `Failed: ${e.message}`,
        { toaster: toast }
      );
    } finally {
      setSubmitting(false);
      resetForm();
    }
  };

  return (
    <Formik
      initialValues={initValues}
      onSubmit={onSubmitForm}
      validate={onValidate({ tCommon })}
      validateOnBlur={true}
      validateOnChange={true}
      enableReinitialize
    >
      {({ handleSubmit, submitCount, errors, touched, isSubmitting }) => (
        <Form>
          <MainDialog
            className={className}
            isOpen={isOpen}
            title={t('titleModal')}
            onClose={onClose}
            actions={
              <>
                <Button
                  intent="primary"
                  outlined
                  minimal
                  onClick={onClose}
                  loading={isSubmitting}
                >
                  {t('cancelSend')}
                </Button>
                <Button
                  intent="primary"
                  disabled={!doctors.length}
                  onClick={() => handleSubmit()}
                  loading={isSubmitting}
                >
                  {t('Send')}
                </Button>
              </>
            }
          >
            <Flex w="100%" column gap={16}>
              <FormGroup2
                label={t('Quarter')}
                name={FIELD_NAMES.QUARTER}
                isRequired
              >
                <FastField name={FIELD_NAMES.QUARTER}>
                  {({ field, form }) => (
                    <ReactSelect
                      isSearchable={false}
                      items={VALID_QUARTER}
                      selectedValue={field.value}
                      onItemSelect={(item: IMenuItem) => {
                        form.setFieldValue(field.name, item.value);
                      }}
                    />
                  )}
                </FastField>
              </FormGroup2>
              <FormGroup2
                label={tDmpBilling('doctor')}
                name={FIELD_NAMES.DOCTOR}
                submitCount={submitCount}
                errors={errors}
                touched={touched}
                isRequired
              >
                <FastField name={FIELD_NAMES.DOCTOR}>
                  {({ field, form }) => (
                    <ReactSelect
                      isSearchable={false}
                      selectedValue={field.value}
                      items={doctors.map((doctor) => ({
                        label: AccountManagementUtil.getFullName(
                          doctor.title,
                          '',
                          doctor.lastName,
                          doctor.firstName
                        ),
                        value: doctor.doctorId,
                      }))}
                      onItemSelect={(item: IMenuItem) =>
                        form.setFieldValue(field.name, item?.value)
                      }
                    />
                  )}
                </FastField>
              </FormGroup2>
              <Divider />
              <FormGroup2
                label={t('TypeOfBilling')}
                name={FIELD_NAMES.BILLING_TYPE}
                isRequired
              >
                <FastField name={FIELD_NAMES.BILLING_TYPE}>
                  {({ field, form }) => (
                    <RadioGroup
                      onChange={(e) =>
                        form.setFieldValue(field.name, e.currentTarget.value)
                      }
                      selectedValue={field.value}
                    >
                      <Radio
                        label={t('SendRealBilling')}
                        value={TypeOfBilling.TypeOfBilling_Send_As_Real_Billing}
                      />
                      <Radio
                        label={t('SendTestBilling')}
                        value={
                          TypeOfBilling.TypeOfBilling_Send_As_Real_Test_Billing
                        }
                      />
                    </RadioGroup>
                  )}
                </FastField>
              </FormGroup2>
              <Divider />
              <FormGroup2 name={FIELD_NAMES.IS_BILLING_COMPLETE}>
                <FastField name={FIELD_NAMES.IS_BILLING_COMPLETE}>
                  {({ field, form }) => (
                    <Checkbox
                      label={t('MarkComplete')}
                      checked={field.value}
                      onChange={() =>
                        form.setFieldValue(field.name, !field.value)
                      }
                    />
                  )}
                </FastField>
              </FormGroup2>
            </Flex>
          </MainDialog>
        </Form>
      )}
    </Formik>
  );
};

export default SendEdmpBillingModal;
