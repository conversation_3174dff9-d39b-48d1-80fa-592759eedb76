import { proxy, useSnapshot } from 'valtio';
import { IStepDMPBilling, IDmpBillingSelection } from './dmp.type';
import { IQuarter } from '../module_kv_billing/KVBilling.store';
import { dmpBillingQuarters } from './dmp.helper';
import {
  DMPBillingFieldsValidationResult,
  DMPBillingFile,
  DMP<PERSON><PERSON><PERSON>,
  Doctor,
} from '@tutum/hermes/bff/edmp_common';
import {
  CheckValidationForDMPBillingResponse,
  DMPBillingHistoryViewModel,
  DMPBillingValidationModel,
  DataCenterInfo,
  GetDMPBillingHistoriesRequest,
  GetDMPBillingHistoriesResponse,
  GetDMPBillingValidationListRequest,
  checkValidationForDMPBilling,
  getDMPBillingHistories,
  getDMPBillingValidationList,
} from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import { IMenuItem } from '@tutum/design-system/components';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { useEffect } from 'react';

export interface ValidationListForValidation extends DMPBillingValidationModel {
  selectedDataCenter: DataCenterInfo;
}

export interface ILoading {
  billingSelection: boolean;
  troubleShootBtn: boolean;
  validationList: {
    table: boolean;
    reTroubleshoot: boolean;
  };
  checkValidation: boolean;
  billingHistory: boolean;
}

export interface IDmpBilling {
  quarters: IQuarter[];
  validationList: ValidationListForValidation[];
  caseNumber: string[];
  dmpLabels: DMPLabeling[];
  doctors: Doctor[];
  billingSelection: IDmpBillingSelection;
  selectedList: ValidationListForValidation[];
  currentStep: IStepDMPBilling;
  totalErrors: number;
  totalWarnings: number;
  loading: ILoading;
  summaries: any[];
  historyList: DMPBillingHistoryViewModel[];
  fileList: DMPBillingFile[];
  transferLetters: DMPBillingFile[] | undefined;
  billingHistoryIds: string;
  dMPBillingFieldsValidationResults: DMPBillingFieldsValidationResult[];
}

const initStore: IDmpBilling = {
  currentStep: IStepDMPBilling.BILLING_SELECTION,
  doctors: [],
  summaries: [],
  quarters: dmpBillingQuarters,
  dmpLabels: [],
  validationList: [],
  caseNumber: [],
  loading: {
    billingSelection: false,
    troubleShootBtn: false,
    validationList: {
      table: false,
      reTroubleshoot: false,
    },
    checkValidation: false,
    billingHistory: false,
  },

  billingSelection: {
    quarter: undefined!,
    year: undefined!,
    doctor: undefined!,
    dmpPrograms: [],
  },
  selectedList: [],
  totalErrors: 0,
  totalWarnings: 0,

  historyList: [],
  fileList: [],
  transferLetters: undefined,
  billingHistoryIds: '',
  dMPBillingFieldsValidationResults: [],
};

export interface IDmpBillingActions {
  setQuarter: (quarter: number, year: number) => void;
  setDoctor: (doctor: any) => void;
  setDmpValidationList: (items?: DMPBillingValidationModel[]) => void;
  setDmpLabeling: (labels: DMPLabeling[]) => void;
  resetBillingSelection: () => void;
  resetSelectedList: () => void;
  addOrRemoveLabel: (labels: DMPLabeling) => void;
  editBillingSelection: (quarter: number, year: number) => void;
  selectDoctorForBilling: (doctor: Doctor | undefined) => void;
  setCurrentStep: (step: IStepDMPBilling) => void;
  troubleShoot: (payload: GetDMPBillingValidationListRequest) => void;
  checkValidation: (
    selectedList: ValidationListForValidation[],
    doctorId: string
  ) => Promise<CheckValidationForDMPBillingResponse | undefined>;
  setHistoryList: (res: GetDMPBillingHistoriesResponse) => void;
  resetAll: () => void;
  setDataCenter: (
    dataCenters: DataCenterInfo[],
    dataCenter: IMenuItem,
    rowId: string
  ) => void;
  getHistoryList: (payload: GetDMPBillingHistoriesRequest) => void;
  setDMPBillingFieldsValidationResults: (
    dMPBillingFieldsValidationResults: DMPBillingFieldsValidationResult[]
  ) => void;
}

export const DmpBillingStore = proxy<IDmpBilling>(initStore);

export const dmpBillingActions: IDmpBillingActions = {
  setQuarter: (quarter: number, year: number) => {
    DmpBillingStore.billingSelection.quarter = quarter;
    DmpBillingStore.billingSelection.year = year;
    dmpBillingActions.resetBillingSelection();
  },
  setDoctor: (doctor: Doctor[]) => {
    DmpBillingStore.doctors = doctor;
  },
  editBillingSelection: (quarter: number, year: number) => {
    DmpBillingStore.billingSelection.quarter = quarter;
    DmpBillingStore.billingSelection.year = year;
  },
  selectDoctorForBilling: (doctor: Doctor | undefined) => {
    DmpBillingStore.billingSelection.doctor = doctor;
  },
  setDmpValidationList: (items?: DMPBillingValidationModel[]) => {
    if (!items && !!DmpBillingStore.selectedList) {
      DmpBillingStore.selectedList.length = 0;
    }
    DmpBillingStore.selectedList = items
      ? items.map((item) => ({
          ...item,
          selectedDataCenter: item.defaultDataCenter,
        }))
      : [];
  },
  setCurrentStep: (step: IStepDMPBilling) => {
    DmpBillingStore.currentStep = step;
  },
  setDmpLabeling: (labels: DMPLabeling[]) => {
    DmpBillingStore.dmpLabels = labels ?? [];
  },
  resetBillingSelection: () => {
    DmpBillingStore.billingSelection.dmpPrograms.length = 0;
    DmpBillingStore.billingSelection.doctor = undefined;
  },
  resetSelectedList: () => {
    DmpBillingStore.selectedList.length = 0;
  },
  addOrRemoveLabel: (label: DMPLabeling) => {
    const { billingSelection } = DmpBillingStore;
    const indexLabel = billingSelection.dmpPrograms.findIndex(
      (el) => el.name === label.name
    );
    if (indexLabel === -1) {
      billingSelection.dmpPrograms.push(label);
      return;
    }
    billingSelection.dmpPrograms = billingSelection.dmpPrograms.filter(
      (el) => el.name != label.name
    );
  },
  troubleShoot: async (payload: GetDMPBillingValidationListRequest) => {
    try {
      DmpBillingStore.loading.troubleShootBtn = true;
      const { data: res } = await getDMPBillingValidationList(payload);
      DmpBillingStore.loading.troubleShootBtn = false;

      if (res?.data) {
        DmpBillingStore.validationList = res.data.map((el) => ({
          ...el,
          selectedDataCenter: el.defaultDataCenter,
          dMPDocumentationTypeED: el.dMPDocumentationTypeED.filter(
            (item) => item.documentationIds[0]
          ),
          dMPDocumentationTypeFD: el.dMPDocumentationTypeFD.filter(
            (item) => item.documentationIds[0]
          ),
          dMPDocumentationTypePED: el.dMPDocumentationTypePED.filter(
            (item) => item.documentationIds[0]
          ),
        }));
      } else {
        DmpBillingStore.validationList = [];
      }
    } catch {
      DmpBillingStore.loading.troubleShootBtn = false;
    }
  },

  setDMPBillingFieldsValidationResults: (
    dMPBillingFieldsValidationResults: DMPBillingFieldsValidationResult[]
  ) => {
    DmpBillingStore.dMPBillingFieldsValidationResults =
      dMPBillingFieldsValidationResults;
  },

  checkValidation: async (
    selectedList: ValidationListForValidation[],
    doctorId: string
  ) => {
    try {
      DmpBillingStore.loading.checkValidation = true;
      const { data: res } = await checkValidationForDMPBilling({
        enrollmentWithDataCenters: selectedList.map((el) => ({
          enrollmentId: el.enrollmentId,
          dataCenter: el.selectedDataCenter.dataCenter,
        })),
        quarter: DmpBillingStore.billingSelection.quarter,
        year: DmpBillingStore.billingSelection.year,
        doctorId,
      });

      if (res.status) {
        DmpBillingStore.fileList = res.dMPBillingFiles || [];
        DmpBillingStore.billingHistoryIds = res.dMPBillingHistoryId || '';
        dmpBillingActions.setCurrentStep(IStepDMPBilling.BILLING_SUMMARY);
        DmpBillingStore.loading.checkValidation = false;
        DmpBillingStore.transferLetters = res.transferLetters;

        return;
      }

      return res;
    } catch (err) {
      DmpBillingStore.loading.troubleShootBtn = false;
      throw err;
    }
  },
  getHistoryList: async (payload: GetDMPBillingHistoriesRequest) => {
    DmpBillingStore.loading.billingHistory = true;
    try {
      await getDMPBillingHistories(payload);
    } finally {
      DmpBillingStore.loading.billingHistory = false;
    }
  },
  setHistoryList: (items: GetDMPBillingHistoriesResponse) => {
    DmpBillingStore.historyList = items.dMPBillingHistories;
  },

  setDataCenter: (
    dataCenters: DataCenterInfo[],
    dataCenter: IMenuItem,
    rowId: string
  ) => {
    for (let i = 0; i < DmpBillingStore.selectedList.length; i++) {
      if (DmpBillingStore.selectedList[i].id === rowId) {
        const foundedDataCenter = dataCenters.find(
          (d) => d.id === dataCenter.value
        );
        if (!foundedDataCenter) {
          continue;
        }
        DmpBillingStore.selectedList[i].selectedDataCenter = foundedDataCenter;
      }
    }
  },
  resetAll: () => {
    DmpBillingStore.billingSelection.dmpPrograms = [];
    DmpBillingStore.billingSelection.doctor = undefined;
    DmpBillingStore.selectedList.length = 0;
    DmpBillingStore.validationList.length = 0;
    DmpBillingStore.currentStep = IStepDMPBilling.BILLING_SELECTION;
    DmpBillingStore.transferLetters = undefined;
  },
};

export function useDmpBillingStore() {
  useEffect(() => {
    const now = datetimeUtil.date();
    dmpBillingActions.editBillingSelection(
      datetimeUtil.getQuarter(now),
      now.getFullYear()
    );
  }, []);

  return useSnapshot(DmpBillingStore);
}
