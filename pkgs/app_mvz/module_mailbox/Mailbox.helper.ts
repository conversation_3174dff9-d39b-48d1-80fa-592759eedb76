import { IMenuItem } from '@tutum/design-system/components';
import { DEFAULT_SELECT_STYLE_CONFIG } from '@tutum/design-system/consts/react-select-config';
import { MailItemDTO } from '@tutum/hermes/bff/app_mvz_mail';
import {
  Address,
  EmailItem,
  MailCategory,
  MailHeaderKey,
  MDNStatus,
} from '@tutum/hermes/bff/mail_common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import DateTimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '../constant/dateTime';
import { CardErrorsType } from '@tutum/mvz/module_mailbox/Mailbox.type';
import { getPresignedGetURL } from '@tutum/hermes/bff/legacy/app_mvz_file';
import { CreateScheinRequest } from '@tutum/hermes/bff/app_mvz_schein';
import { MainGroup, TreatmentCaseNames } from '@tutum/hermes/bff/schein_common';
import { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { COLOR } from '@tutum/design-system/themes/styles';
import { IAttachment } from './attachment';
import { Card, PinStatus } from '@tutum/hermes/bff/card_common';

export const MAX_SIZE_UPLOAD = 15 * 1024 * 1024; // 25MB

export const MDN_STATUS_VALID = [
  MDNStatus.MDNStatus_Pending,
  MDNStatus.MDNStatus_Received,
  MDNStatus.MDNStatus_Sent,
];

export const BASE_MULTISELECT_KIM_RECEIVER = {
  container: (base) => ({
    ...base,
    width: '100%',
    alignSelf: 'flex-start',
  }),
  control: (base, props) => ({
    ...DEFAULT_SELECT_STYLE_CONFIG().control?.(base, props),
    height: 'auto',
    border: '0 !important',
  }),
  multiValue: (base) => ({
    ...base,
    padding: 0,
    borderRadius: '10px',
    overflow: 'hidden',
  }),
  menu: (base) => ({
    ...base,
    width: 'auto',
    minWidth: '250px',
    '.sub-info': {
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      '&.fullname': { fontWeight: 500, color: `${COLOR.TEXT_PRIMARY_BLACK}` },
    },
  }),
};

export const E_DOCTOR_LETTER = 'Arztbrief';

export enum StateAssignPatient {
  auto = 'auto',
  manualy = 'manual',
  default = 'default',
}

const DEFAULT_VALUE_SCHEIN = '00';

export const KIM_CATEGORY: IMenuItem[] = [
  {
    label: 'KIM-Mail;Default;V1.0',
    value: 'KIM-Mail;Default;V1.0',
  },
  {
    label: 'KIM-Mail;Abwesend;V1.0',
    value: 'KIM-Mail;Abwesend;V1.0',
  },
  {
    label: 'DALE-UV;Einsendung;V1.0',
    value: 'DALE-UV;Einsendung;V1.0',
  },
  {
    label: 'DALE-UV;Quittung;V1.0',
    value: 'DALE-UV;Quittung;V1.0',
  },
  {
    label: 'DiMus;Lieferung;V1.0',
    value: 'DiMus;Lieferung;V1.0',
  },
  {
    label: 'DiMus;Eingangsbestaetigung;V1.0',
    value: 'DiMus;Eingangsbestaetigung;V1.0',
  },
  {
    label: 'DiMus;Rueckmeldung;V1.0',
    value: 'DiMus;Rueckmeldung;V1.0',
  },
  {
    label: 'eRezept;Zuweisung;V1.0',
    value: 'eRezept;Zuweisung;V1.0',
  },
  {
    label: 'eRezept;Kommunikation;V1.0',
    value: 'eRezept;Kommunikation;V1.0',
  },
  {
    label: 'eAU;Lieferung;V1.0',
    value: 'eAU;Lieferung;V1.0',
  },
  {
    label: 'eAU;Storno-Arzt;V1.0',
    value: 'eAU;Storno-Arzt;V1.0',
  },
  {
    label: 'eAU;Fehler-Kasse;V1.0',
    value: 'eAU;Fehler-Kasse;V1.0',
  },
  {
    label: 'Arztbrief;VHitG-Versand;V1.0',
    value: 'Arztbrief;VHitG-Versand;V1.0',
    isDisabled: true,
  },
  {
    label: 'Arztbrief;VHitG-Versand;V1.2',
    value: 'Arztbrief;VHitG-Versand;V1.2',
    isDisabled: true,
  },
  {
    label: 'Arztbrief;Eingangsbestaetigung;V1.0',
    value: 'Arztbrief;Eingangsbestaetigung;V1.0',
    isDisabled: true,
  },
  {
    label: 'Arztbrief;Eingangsbestaetigung;V1.2',
    value: 'Arztbrief;Eingangsbestaetigung;V1.2',
    isDisabled: true,
  },
  {
    label: 'EBZ;PAR;1.4.0',
    value: 'EBZ;PAR;1.4.0',
  },
  {
    label: 'EBZ;ZER;1.4.0',
    value: 'EBZ;ZER;1.4.0',
  },
  {
    label: 'EBZ;KGL;1.4.0',
    value: 'EBZ;KGL;1.4.0',
  },
  {
    label: 'EBZ;KBR;1.4.0',
    value: 'EBZ;KBR;1.4.0',
  },
  {
    label: 'EBZ;KFO;1.4.0',
    value: 'EBZ;KFO;1.4.0',
  },
  {
    label: 'EBZ;ANW;1.4.0',
    value: 'EBZ;ANW;1.4.0',
  },
  {
    label: 'EBZ;FEH;1.4.0',
    value: 'EBZ;FEH;1.4.0',
  },
  {
    label: 'EBZ;MIT;1.4.0',
    value: 'EBZ;MIT;1.4.0',
  },
  {
    label: 'EBZ;PMB;1.4.0',
    value: 'EBZ;PMB;1.4.0',
  },
  {
    label: 'eDMP;Einsendung;V1.0',
    value: 'eDMP;Einsendung;V1.0',
  },
  {
    label: 'eDMP;Quittung;V1.0',
    value: 'eDMP;Quittung;V1.0',
  },
  {
    label: 'eDokumentation-QSHLT;Lieferung;V2.0',
    value: 'eDokumentation-QSHLT;Lieferung;V2.0',
  },
  {
    label: 'eDokumentation-QSKE;Lieferung;V2.0',
    value: 'eDokumentation-QSKE;Lieferung;V2.0',
  },
  {
    label: 'eDokumentation-QSMG;Lieferung;V2.0',
    value: 'eDokumentation-QSMG;Lieferung;V2.0',
  },
  {
    label: 'eDokumentation-QSHGV;Lieferung;V2.0',
    value: 'eDokumentation-QSHGV;Lieferung;V2.0',
  },
  {
    label: 'eDokumentation-QSHGVK;Lieferung;V2.0',
    value: 'eDokumentation-QSHGVK;Lieferung;V2.0',
  },
  {
    label: 'eDokumentation-QSHLT;Status;V2.0',
    value: 'eDokumentation-QSHLT;Status;V2.0',
  },
  {
    label: 'eDokumentation-QSKE;Status;V2.0',
    value: 'eDokumentation-QSKE;Status;V2.0',
  },
  {
    label: 'eDokumentation-QSMG;Status;V2.0',
    value: 'eDokumentation-QSMG;Status;V2.0',
  },
  {
    label: 'eDokumentation-QSHGV;Status;V2.0',
    value: 'eDokumentation-QSHGV;Status;V2.0',
  },
  {
    label: 'eDokumentation-QSHGVK;Status;V2.0',
    value: 'eDokumentation-QSHGVK;Status;V2.0',
  },
  {
    label: 'eDokumentation;Eingangsbestaetigung;V2.0',
    value: 'eDokumentation;Eingangsbestaetigung;V2.0',
  },
  {
    label: 'abD-eHKS;Einsendung;V1.0',
    value: 'abD-eHKS;Einsendung;V1.0',
  },
  {
    label: 'abD-eHKS;Quittung;V1.0',
    value: 'abD-eHKS;Quittung;V1.0',
  },
  {
    label: 'eNachricht;Lieferung;V2.1',
    value: 'eNachricht;Lieferung;V2.1',
  },
  {
    label: 'eNachricht;Eingangsbestaetigung;V2.1',
    value: 'eNachricht;Eingangsbestaetigung;V2.1',
  },
  {
    label: 'KZVAbr;Lieferung;V1.0',
    value: 'KZVAbr;Lieferung;V1.0',
  },
  {
    label: 'KZVAbr;Rueckmeldung;V1.0',
    value: 'KZVAbr;Rueckmeldung;V1.0',
  },
  {
    label: 'LDT-Auftrag;Lieferung;V1.0',
    value: 'LDT-Auftrag;Lieferung;V1.0',
  },
  {
    label: 'LDT-Auftrag;Status;V1.0',
    value: 'LDT-Auftrag;Status;V1.0',
  },
  {
    label: 'LDT-Auftrag;Eingangsbestaetigung;V1.0',
    value: 'LDT-Auftrag;Eingangsbestaetigung;V1.0',
  },
  {
    label: 'LDT-Befund;Lieferung;V1.0',
    value: 'LDT-Befund;Lieferung;V1.0',
  },
  {
    label: 'LDT-Befund;Trigger;V1.0',
    value: 'LDT-Befund;Trigger;V1.0',
  },
  {
    label: 'LDT-Befund;Status;V1.0',
    value: 'LDT-Befund;Status;V1.0',
  },
  {
    label: 'LDT-Befund;Eingangsbestaetigung;V1.0',
    value: 'LDT-Befund;Eingangsbestaetigung;V1.0',
  },
  {
    label: 'MDN',
    value: 'MDN',
  },
  {
    label: 'MIO;Lieferung;V1.0',
    value: 'MIO;Lieferung;V1.0',
  },
  {
    label: 'MIO;Rueckmeldung;V1.0',
    value: 'MIO;Rueckmeldung;V1.0',
  },
  {
    label: 'QSPB;Empfangsbestaetigung;V2.0',
    value: 'QSPB;Empfangsbestaetigung;V2.0',
  },
  {
    label: 'QSPB;Empfangsbestaetigung_RM;V2.0',
    value: 'QSPB;Empfangsbestaetigung_RM;V2.0',
  },
  {
    label: 'QSPB;Miniprotokoll;V2.0',
    value: 'QSPB;Miniprotokoll;V2.0',
  },
  {
    label: 'QSPB;Lieferung;V2.0',
    value: 'QSPB;Lieferung;V2.0',
  },
  {
    label: 'QSPB;Datenflussprotokoll;V2.0',
    value: 'QSPB;Datenflussprotokoll;V2.0',
  },
  {
    label: 'QSPB;Rueckmeldebericht;V2.0',
    value: 'QSPB;Rueckmeldebericht;V2.0',
  },
  {
    label: 'UTeilnahme;Lieferung;V1.0',
    value: 'UTeilnahme;Lieferung;V1.0',
  },
  {
    label: 'UTeilnahme;Quittung;V1.0',
    value: 'UTeilnahme;Quittung;V1.0',
  },
  {
    label: 'Selbsttest;Lieferung;V1.0',
    value: 'Selbsttest;Lieferung;V1.0',
  },
  {
    label: '1ClickAbrechnung;Lieferung;V2.0',
    value: '1ClickAbrechnung;Lieferung;V2.0',
  },
  {
    label: '1ClickAbrechnung;Eingangsbestaetigung;V2.0',
    value: '1ClickAbrechnung;Eingangsbestaetigung;V2.0',
  },
  {
    label: '1ClickAbrechnung;Rueckmeldung;V2.0',
    value: '1ClickAbrechnung;Rueckmeldung;V2.0',
  },
];

const getIndexSelectedMail = (
  selectedMailId: string,
  mails: MailItemDTO[]
): number => {
  return mails.findIndex((mail) => mail.id === selectedMailId);
};

const parseAddressToName = (
  addresses: Address[],
  t?: IFixedNamespaceTFunction<keyof typeof MailboxI18n>,
  currentMailAddress?: string
): string[] => {
  const names: string[] = [];

  addresses
    ?.filter((addresse) => !!addresse)
    .forEach((mailAddress) => {
      if (mailAddress.address === currentMailAddress) {
        names.unshift(t?.('toMe')!);
      } else {
        names.push(mailAddress.name || mailAddress.address);
      }
    });

  return names;
};

const handleDownloadFile = async (attachment: IAttachment) => {
  // down load from minio
  const url = attachment.url;
  if (!url) {
    return;
  }
  const index = url.indexOf('/');
  const bucketName = url.slice(0, index);
  const objectName = url.slice(index + 1, url.length);
  const res = await getPresignedGetURL({
    bucketName,
    objectName,
    fileName: attachment.name,
  });

  window.open(res.data.presignedURL, 'download');
};

const convertMailAddress = (data: Address | Address[]): string => {
  if (Array.isArray(data)) {
    return data.map((mail) => `${mail.name} <${mail.address}>`).join(', ');
  }

  return `${data.name} <${data.address}>`;
};

const formatDateTime = (date: number | string): string =>
  DateTimeUtil.dateTimeFormat(date, DATE_TIME_WITHOUT_SECONDS_FORMAT);

const groupAndGetSubject = (subject: string, keyword: string): string =>
  subject.includes(keyword) ? subject : `${keyword} ${subject}`;

const getRecipientAddresses = (mail?: EmailItem): Address[] => {
  if (!mail) {
    return []
  }

  const adresses: Address[] = [];
  if (mail.to) {
    adresses.push(...mail.to);
  }

  if (mail.cc) {
    adresses.push(...mail.cc);
  }

  if (mail.bcc) {
    adresses.push(...mail.bcc);
  }

  return adresses;
};

const groupMailByThread = (
  mails: MailItemDTO[]
): Array<MailItemDTO | MailItemDTO[]> => {
  const res: Array<MailItemDTO | MailItemDTO[]> = [];

  const keyMap = new Map<string, MailItemDTO[]>();

  for (let i = 0; i < mails.length; i++) {
    const mail = mails[i];

    const keyThread =
      mail.emailItem.inReplyTo?.length === 1
        ? mail.emailItem.inReplyTo[0]
        : mail.emailItem.messageID;

    let appended = (keyMap.get(keyThread) || []).concat(mail);

    appended = appended.sort((a, b) => {
      return a.emailItem.received! - b.emailItem.received!;
    });

    keyMap.set(keyThread, appended);
  }

  keyMap.forEach((value) => {
    res.push(value.length === 1 ? value[0] : value);
  });

  return res.sort((a, b) => {
    const mailA = a instanceof Array ? a[a.length - 1] : a;
    const mailB = b instanceof Array ? b[b.length - 1] : b;

    return mailB.emailItem.received! - mailA.emailItem.received!;
  });
};

const isMDNRequested = (mailItem: EmailItem) => {
  const mailHeader = mailItem.mailHeader!;

  if (mailHeader['Disposition-Notification-To']) {
    return true;
  }

  if (
    mailHeader['Content-Type'].includes(
      'multipart/report; report-type=disposition-notification'
    )
  ) {
    return false;
  }
};

const emailValidToAutoAssignEAB = (email: MailItemDTO) => {
  if (!email?.emailItem) return false;
  const { emailItem } = email;
  const mailHeaderCategory =
    emailItem?.mailHeader &&
    emailItem?.mailHeader[MailHeaderKey.MailHeaderKey_ServiceIdentifer];
  return (
    mailHeaderCategory === MailCategory.MailCategory_EAB && !emailItem?.isRead
  );
};

const isEABMail = (email?: EmailItem): boolean => {
  if (!email || !email.mailHeader) return false;
  const mailHeaderCategory =
    email.mailHeader &&
    email.mailHeader[MailHeaderKey.MailHeaderKey_ServiceIdentifer];
  return mailHeaderCategory === MailCategory.MailCategory_EAB;
};

const createDefaultPayloadSchein = (
  patientId: string,
  doctorId: string,
  patientInfo: PatientInfo
): CreateScheinRequest => {
  return {
    scheinMainGroup: MainGroup.KV,
    patientId,
    g4101Year: DateTimeUtil.getYear(),
    g4101Quarter: DateTimeUtil.getQuarter(),
    g4101: `${DateTimeUtil.getYear()}.${DateTimeUtil.getQuarter()}`,
    g4122: DEFAULT_VALUE_SCHEIN,
    kvTreatmentCase: TreatmentCaseNames.TCKvOutpatient,
    kvScheinSubGroup: '00',
    excludeFromBilling: false,
    takeOverDiagnoseInfos: [],
    insuranceId: getActiveInsurance(patientInfo.insuranceInfos)?.id!,
    doctorId,
    scheinDetails: {
      g4104: +getActiveInsurance(patientInfo.insuranceInfos)?.insuranceCompanyId!,
      g4106: DEFAULT_VALUE_SCHEIN,
      psychotherapy: [],
      ps4256: undefined!,
      ps4253: undefined!,
      ps4244: undefined!,
    },
  };
};

const mapErrorCard = (cardAccount: Card) => {
  const cardPin = cardAccount.pins.find((pin) => pin.iccsn);
  if (!cardPin || !cardPin.pinStatus) {
    return null;
  }
  switch (cardPin.pinStatus) {
    case PinStatus.PinStatusEnumBLOCKED:
      return CardErrorsType.CardBlocked;
    case PinStatus.PinStatusEnumLOAD_FAIL:
      return CardErrorsType.CardNotActive;
    case PinStatus.PinStatusEnumVERIFIED:
      return null;
    default:
      return CardErrorsType.CardNotAvailable;
  }
};

export {
  getIndexSelectedMail,
  parseAddressToName,
  convertMailAddress,
  formatDateTime,
  groupAndGetSubject,
  getRecipientAddresses,
  groupMailByThread,
  handleDownloadFile,
  isMDNRequested,
  emailValidToAutoAssignEAB,
  isEABMail,
  createDefaultPayloadSchein,
  mapErrorCard,
};
