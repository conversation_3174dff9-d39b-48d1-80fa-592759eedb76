import React, { FC, useMemo } from 'react';

import { BodyTextM, Flex, Svg, Button } from '@tutum/design-system/components';
import { Attachment } from '@tutum/hermes/bff/legacy/mail_common';

const PDFIcon = '/images/pdf-icon.svg';
const FileIcon = '/images/file.svg';
const DownloadIcon = '/images/download.svg';
const CloseIcon = '/images/close.svg';

export interface IAttachment extends Partial<Attachment> {
  type?: string;
  attachment?: File;
}

export enum FileExtensions {
  PDF = 'pdf',
  XML = 'xml',
}

export interface IAttachmentProps {
  className?: string;
  action: 'download' | 'remove';
  attachment: IAttachment;
  onClickAttachment: (file: any, isRemove?: boolean) => void;
}

const Attachments: FC<IAttachmentProps> = (props) => {
  const { className, onClickAttachment, action, attachment } = props;
  const { name, type } = attachment;

  const icon = useMemo<string>(() => {
    if (
      type?.includes(FileExtensions.PDF) ||
      name?.includes(FileExtensions.PDF)
    ) {
      return PDFIcon;
    }

    return FileIcon;
  }, [type, name]);

  const attachmentIcon = (_action: typeof action) => {
    let icon: React.ReactElement | null = null;
    switch (_action) {
      case 'download':
        icon = <Svg src={DownloadIcon} />;
        break;
      case 'remove':
        icon = (
          <Button
            isActionIcon
            iconOnly
            minimal
            icon={<Svg src={CloseIcon} />}
            onClick={() => onClickAttachment(attachment, true)}
          />
        );
        break;
      default:
        break;
    }
    return icon;
  };

  const onHandleClick = () => {
    return onClickAttachment(attachment);
  };

  return (
    <Button minimal className={className} onClick={onHandleClick}>
      <Flex gap={4} align="center">
        <Svg src={icon} />
        <BodyTextM
          fontWeight="SemiBold"
          className="sl-mailbox-attachment__name"
        >
          {name}
        </BodyTextM>
        {attachmentIcon(action)}
      </Flex>
    </Button>
  );
};

export default Attachments;
