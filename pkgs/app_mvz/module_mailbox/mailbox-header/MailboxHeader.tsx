import React, { FC, memo, useMemo } from 'react';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import I18n from '@tutum/infrastructure/i18n';
import {
  Flex,
  H1,
  ITabProps,
  TabId,
  Tabs,
  ReactSelect,
  IMenuItem,
} from '@tutum/design-system/components';

import { mailboxActions, useMailboxStore } from '../Mailbox.store';
import { MailboxType, TabIds } from '../Mailbox.type';
import ArchiveBoxWarning from '@tutum/mvz/module_mailbox/mailbox-header/ArchiveBoxWarning';

export interface IMailboxHeaderProps {
  className?: string;
}

const MailboxHeader: FC<IMailboxHeaderProps> = (props) => {
  const { className } = props;
  const { t } = I18n.useTranslation<keyof typeof MailboxI18n.Header>({
    namespace: 'Mailbox',
    nestedTrans: 'Header',
  });

  const {
    inbox,
    outbox,
    archived,
    selectedTabId,
    selectedAccount,
    listAccount,
    mailboxType,
  } = useMailboxStore();

  const tabs = useMemo<ITabProps[]>(
    () => [
      {
        key: TabIds.Inbox + inbox.countUnread,
        id: TabIds.Inbox,
        title: `${t('inbox')}${inbox.countUnread > 0 ? ` (${inbox.countUnread})` : ''
          }`,
      },
      {
        key: TabIds.Outbox + outbox.countUnread,
        id: TabIds.Outbox,
        title: `${t('outbox')}${outbox.countUnread > 0 ? ` (${outbox.countUnread})` : ''
          }`,
      },
      {
        key: TabIds.Archived + archived.countUnread,
        id: TabIds.Archived,
        title: `${t('archived')}${archived.countUnread > 0 ? ` (${archived.countUnread})` : ''
          }`,
      },
    ],
    [inbox.countUnread, outbox.countUnread, archived.countUnread]
  );

  const items = useMemo<Array<IMenuItem<string>>>(
    () =>
      listAccount.map((account) => ({
        label: account.email,
        value: account.email,
      })),
    [listAccount]
  );

  const handleChangeTab = (newTabId: TabId) => {
    mailboxActions.setActiveTab(newTabId as TabIds);
  };

  const onChangeMailAccount = (mailAccount: IMenuItem<string>) => {
    mailboxActions.setActiveAccount(mailAccount?.value ?? '');
    mailboxActions.setCardError(null!);
    mailboxActions.setViewMail(null!);
  };

  return (
    <>
      <Flex
        className={className}
        justify="space-between"
        align="center"
        px={16}
      >
        <Flex>
          <H1 padding="16px 16px 16px 0">
            {mailboxType === MailboxType.Kim
              ? t('mailboxOverview')
              : t('kvConnectMailbox')}
          </H1>
          <Tabs
            id="mailbox-tabs"
            onChange={handleChangeTab}
            selectedTabId={selectedTabId}
            tabs={tabs}
          />
        </Flex>
        {selectedAccount.email && (
          <ReactSelect
            className="sl-mail-account"
            selectedValue={selectedAccount.email}
            items={items}
            onItemSelect={onChangeMailAccount}
            menuPlacement="auto"
          />
        )}
      </Flex>
      {selectedTabId === TabIds.Archived && <ArchiveBoxWarning />}
    </>
  );
};

export default memo(MailboxHeader);
