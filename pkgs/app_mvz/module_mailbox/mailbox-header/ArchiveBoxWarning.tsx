import { Form, Formik } from 'formik';
import { useEffect, useState } from 'react';

import {
  alertSuccessfully,
  Button,
  Dialog,
  FormGroup2,
  Svg,
} from '@tutum/design-system/components';
import { styled } from '@tutum/design-system/models';
import { COLOR } from '@tutum/design-system/themes/styles';
import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import I18n from '@tutum/infrastructure/i18n';
import { useMailboxStore } from '@tutum/mvz/module_mailbox/Mailbox.store';
import {
  InputGroup,
  Intent,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import Common from '@tutum/admin/locales/en/Common.json';
import {
  getArchiveSetting,
  saveArchiveSetting,
} from '@tutum/hermes/bff/legacy/app_mvz_mail';

const WarningIcon = '/images/alert-circle-solid.svg';

const Warning = styled.div`
  height: 50px;
  width: 100%;
  background-color: ${COLOR.WARNING_LIGHT};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  .dialog .cancel-btn {
    color: ${COLOR.NEGATIVE_BASE};
  }
`;
const Content = styled.div`
  color: ${COLOR.TAG_BACKGROUND_YELLOW};
  font-family: 'Work Sans';
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 153.846% */
  letter-spacing: -0.2px;
`;
const Action = styled.div`
  color: ${COLOR.BACKGROUND_SELECTED_STRONG};
  font-family: 'Work Sans';
  font-size: 13px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: -0.2px;
  user-select: none;
  cursor: pointer;
`;
const DialogContent = styled.div`
  padding: 24px 16px;
  .input-wrapper {
    padding: 0 24px;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .input {
    position: relative;
  }
  .input::after {
    content: attr(data-suffix);
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(-50%, 50%);
    color: ${COLOR.TEXT_TERTIARY_SILVER};
    text-align: right;
    font-family: 'Work Sans';
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 153.846% */
    letter-spacing: -0.2px;
  }
`;
const DialogAction = styled.div`
  .cancel-btn span.bp5-button-text {
    color: ${COLOR.BACKGROUND_SELECTED_STRONG} !important;
  }
  .cancel-btn {
    border: 1px solid ${COLOR.BACKGROUND_SELECTED_STRONG} !important;
    background-color: ${COLOR.BACKGROUND_PRIMARY_WHITE} !important;
  }
`;

const initSetting = {
  isActive: false,
  deleteAfter: 0,
};
const ArchiveBoxWarning = () => {
  const { t } = I18n.useTranslation<keyof typeof MailboxI18n.setting>({
    namespace: 'Mailbox',
    nestedTrans: 'setting',
  });
  const { t: tButton } = I18n.useTranslation<keyof typeof Common.ButtonActions>(
    {
      namespace: 'Common',
      nestedTrans: 'ButtonActions',
    }
  );
  const [setting, setSetting] = useState(initSetting);
  const [newSetting, setNewSetting] = useState(initSetting);
  const { selectedAccount } = useMailboxStore();
  const [open, setOpen] = useState(false);

  const handleSave = () => {
    saveArchiveSetting({
      accountEmail: selectedAccount.email,
      isActive: newSetting.isActive,
      deleteAfter: newSetting.deleteAfter,
    }).then(() => {
      setOpen(false);
      setSetting(newSetting);
      alertSuccessfully(t('settingSaved'));
    });
  };

  useEffect(() => {
    getArchiveSetting({ accountEmail: selectedAccount.email }).then((res) => {
      setNewSetting(res.data?.setting);
      setSetting(res.data?.setting);
    });
  }, []);
  return (
    <Warning>
      <Svg src={WarningIcon} />
      <Content>
        {setting.isActive &&
          t('autoDeleteMailWarning', { days: setting?.deleteAfter })}
        {!setting.isActive && t('notAutoDeleteWarning')}
      </Content>
      <Action onClick={() => setOpen(true)}>{t('changeSettings')}</Action>
      <Dialog
        title={t('dialogTitle')}
        isOpen={open}
        onClose={() => setOpen(false)}
        actions={
          <DialogAction>
            <Button
              intent={Intent.PRIMARY}
              className="cancel-btn"
              onClick={() => setOpen(false)}
            >
              {tButton('cancelText')}
            </Button>
            <Button intent={Intent.PRIMARY} onClick={handleSave}>
              {tButton('saveText')}
            </Button>
          </DialogAction>
        }
      >
        <DialogContent>
          <Formik initialValues={{}} onSubmit={handleSave}>
            <Form>
              <RadioGroup
                name="isActive"
                onChange={(e) => {
                  const value = e.currentTarget.value;
                  setNewSetting({
                    ...newSetting,
                    isActive: value === 'active',
                  });
                }}
                selectedValue={newSetting.isActive ? 'active' : 'inactive'}
              >
                <Radio label={t('notDelete')} value={'inactive'} />
                <Radio label={t('autoDelete')} value={'active'} />
                <FormGroup2
                  label={t('after')}
                  name={'deleteAfter'}
                  className="input-wrapper"
                >
                  <div className="input" data-suffix={t('days')}>
                    <InputGroup
                      isNumeric
                      value={newSetting.deleteAfter.toString()}
                      onChange={(e) =>
                        setNewSetting({
                          ...newSetting,
                          deleteAfter: Number(e.target.value),
                        })
                      }
                      type="number"
                      disabled={!newSetting.isActive}
                    />
                  </div>
                </FormGroup2>
              </RadioGroup>
            </Form>
          </Formik>
        </DialogContent>
      </Dialog>
    </Warning>
  );
};
export default ArchiveBoxWarning;
