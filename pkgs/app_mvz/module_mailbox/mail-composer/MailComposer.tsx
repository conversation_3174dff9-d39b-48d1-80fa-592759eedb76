import React, { FC, useEffect, useId, useMemo, useState } from 'react';
import { components, OptionProps, GroupBase } from 'react-select';
import { useRouter } from 'next/router';
import debounce from 'lodash/debounce';
import {
  CLEAR_EDITOR_COMMAND,
  $getRoot,
  $createParagraphNode,
  $createTextNode,
} from 'lexical';
import type { LexicalEditor, EditorState } from 'lexical';
import {
  LexicalComposer,
  InitialConfigType,
} from '@lexical/react/LexicalComposer';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { ClearEditorPlugin } from '@lexical/react/LexicalClearEditorPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';

import { ROUTING } from '@tutum/mvz/types/route.type';
import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';

import {
  useMutationSendMail,
  SendMailRequest,
  useQuerySearchMailAddress,
  useMutationGenPresignedPutAttachmentURL,
} from '@tutum/hermes/bff/legacy/app_mvz_mail';
import {
  useMutationSendMail as useMutationSendEAB,
  getConsentDocumenting,
} from '@tutum/hermes/bff/legacy/app_mvz_eab';
import { AutomaticDocumentingCase } from '@tutum/hermes/bff/eab_common';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { PatientItemRenderer } from '@tutum/mvz/components/patient-select';
import { Checkbox } from '@tutum/design-system/components/Core';
import useToaster from '@tutum/mvz/hooks/useToaster';
import i18n from '@tutum/infrastructure/i18n';
import {
  BASE_MULTISELECT_KIM_RECEIVER,
  convertMailAddress,
  formatDateTime,
  groupAndGetSubject,
  KIM_CATEGORY,
  MAX_SIZE_UPLOAD,
  E_DOCTOR_LETTER,
  handleDownloadFile,
} from '../Mailbox.helper';
import {
  Flex,
  Button,
  Box,
  BodyTextM,
  Svg,
  AsyncSelect,
  ReactSelect,
  IMenuItem,
  alertError,
  alertSuccessfully,
  CallbackLoadOptionsType,
  MultiSelect,
  IMenuItemWithData,
  alertWarning,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import Attachment from '../attachment/Attachment.styled';
import {
  CardErrorsType,
  ComposerSize,
  ReceiverType,
  TabIds,
} from '../Mailbox.type';
import {
  Address,
  ContentTypeType,
  EmailItem,
  Attachment as AttachmentItem,
} from '@tutum/hermes/bff/mail_common';
import { useMailboxStore, mailboxActions } from '../Mailbox.store';
import { IPatientSearchResult } from '@tutum/mvz/module_patient-management/patient-search/PatientSearch.type';
import { MailAddressEntry } from '@tutum/hermes/bff/legacy/app_mvz_mail';
import {
  ExposeEditorInstancePlugin,
  useEditorInstance,
} from '@tutum/design-system/lexical/plugins/ExposeEditorInstance';
import { SearchingType } from '@tutum/hermes/bff/app_mvz_patient_search';
import PatientSearchService from '@tutum/mvz/module_patient-management/patient-search/PatientSearch.service';
import {
  $$transformToTextModuleNodes,
  TextModuleTypeaheadPlugin,
} from '@tutum/design-system/textmodule/plugins/TextModuleTypeahead';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import { renderDefaultTextmoduleMenu } from '@tutum/design-system/textmodule/TextmoduleEditor';
import { TextmoduleTabable } from '@tutum/design-system/textmodule/plugins/TextModuleTabable.plugin';
import { PlaceholderInputNode } from '@tutum/design-system/textmodule/nodes/PlaceholderInputNode';
import { QuestionnaireNode } from '@tutum/design-system/textmodule/nodes/QuestionnaireNode';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { getPositionsToHighlight } from '@tutum/infrastructure/utils/match';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { useErrorCodeI18n } from '@tutum/mvz/hooks/useErrorCode';
import fetchWithRetry from '@tutum/mvz/_utils/fetch-retry';

const TrashIcon = '/images/trash-bin-red.svg';
const ClipPaperIcon = '/images/clip-paper.svg';

export interface IMailComposerProps {
  className?: string;
  size?: ComposerSize;
  cardError?: CardErrorsType;
  defaultUserToReply?: Address;
  replySubject?: string;
  replyTo?: string;
  getSubmitManual?: (submitFunc: () => Promise<void>) => void;
}

function removeDuplicatedMenuItem(
  newValue: Array<IMenuItem<any>>
): Array<IMenuItem<any>> {
  const uniqueValue = newValue.filter((obj, index) => {
    return index === newValue.findIndex((o) => obj.id === o.id);
  });
  return uniqueValue;
}

function onError(error: Error) {
  // console.error(error);
}

const textmoduleUsedForList = [
  TextModuleUseFor.TextModuleUseFor_Note,
  TextModuleUseFor.TextModuleUseFor_Anamnesis,
  TextModuleUseFor.TextModuleUseFor_Findings,
  TextModuleUseFor.TextModuleUseFor_Therapy,
];

type EnhancedOptionProps = OptionProps<
  IMenuItem<any>,
  boolean,
  GroupBase<any>
> & {
  query: string;
};

const EnhancedOption = (props) => {
  if (!props.data || !props.data.value || !props.query) return null;
  const item = props.data.value;
  const styleTexts = getPositionsToHighlight(item.mail, props.query);

  return (
    <components.Option {...props}>
      <Flex gap={8}>
        <BodyTextM limitLines={1} className="sub-info fullname">
          {`${props.data?.value?.name}`}
        </BodyTextM>
        <BodyTextM>
          (
          {styleTexts.length > 0
            ? styleTexts.map((itemSub, index) => {
              return itemSub.highlight ? (
                itemSub.value
              ) : (
                <strong key={index}>{itemSub.value}</strong>
              );
            })
            : item.mail}
          )
        </BodyTextM>
      </Flex>
      <Box>
        <BodyTextM className="sub-info">
          {`${props.data?.value?.location} | ${props.data?.value?.specialization}`}
        </BodyTextM>
      </Box>
    </components.Option>
  );
};

const initialConfigEditor = {
  namespace: 'MailComposer',
  theme: { width: '100%' },
  onError,
};

const initOption = { cc: false, bcc: false };

const MailComposer: FC<IMailComposerProps> = (props) => {
  const {
    className,
    defaultUserToReply,
    replySubject,
    replyTo,
    getSubmitManual,
  } = props;
  const toast = useToaster();
  const { t } = i18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
  });
  const { t: tMailComposer } = i18n.useTranslation<
    keyof typeof MailboxI18n.MailComposer
  >({
    namespace: 'Mailbox',
    nestedTrans: 'MailComposer',
  });
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const [editorRef, setEditorRef] = useEditorInstance();
  const tError = useErrorCodeI18n();
  const router = useRouter();
  const [option, setOption] = useState<Record<string, boolean>>(initOption);
  const [body, setBody] = useState('');
  const [subject, setSubject] = useState('');
  const [fileUpload, setFileUpload] = useState<File[]>([]);
  const [fileAttachBySystem, setFileAttachBySystem] = useState<
    AttachmentItem[] | null
  >([]);
  const [receiver, setReceiver] = useState<Array<IMenuItem<any>>>([]);
  const [cc, setCc] = useState<Array<IMenuItem<any>>>([]);
  const [bcc, setBcc] = useState<Array<IMenuItem<any>>>([]);
  const [receiverList, setReceiverList] = useState<IMenuItem<MailAddressEntry>[]>([]);
  const [ccList, setCcList] = useState<Array<IMenuItem<any>>>([]);
  const [bccList, setBccList] = useState<Array<IMenuItem<any>>>([]);
  const [category, setCategory] = useState('');
  const [selectedPatient, setSelectedPatient] =
    useState<IPatientSearchResult | undefined>(undefined);
  const [comfirnDelivery, setComfirnDelivery] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);
  const [searchKey, setSearchKey] = useState('');
  const [searchType, setSearchType] = useState<ReceiverType | undefined>(undefined);

  const {
    selectedAccount,
    cardError,
    forwardData,
    isReplying,
    eabItem,
    isSubmitByConsentModal,
  } = useMailboxStore();

  const { globalData } = GlobalContext.useContext();

  const { mutate: sendMailEAB } = useMutationSendEAB({
    onSuccess: () => {
      alertSuccessfully(tMailComposer('sentSuccessfully'));
      mailboxActions.reloadList(TabIds.Outbox);
      mailboxActions.setComposer(null);
      const URL = `${ROUTING.PATIENT.replace(
        '[patientId]',
        eabItem?.patientProfile.id!
      )}#timeline`;
      router.push(URL);
      mailboxActions.setEABItem(undefined);
    },
    onError: (error) => {
      if (
        error.response.data.serverError ===
        ErrorCode.ErrorCode_KIM_Account_Invalid
      ) {
        alertError(tError(error.response.data.serverError));
      }
      setIsSending(false);
    },
    throwOnError: false,
  });

  const { mutate: sendMail } = useMutationSendMail({
    onSuccess: () => {
      alertSuccessfully(tMailComposer('sentSuccessfully'));
      mailboxActions.reloadList(TabIds.Inbox);
      mailboxActions.setComposer(null);
    },
    onError: (error) => {
      const cardError = error.response.data.message as CardErrorsType;
      if (cardError && cardError === CardErrorsType.CardNotAvailable) {
        mailboxActions.setCardError(cardError);
      }
      if (
        error.response.data.message === ErrorCode.ErrorCode_KIM_Account_Invalid
      ) {
        alertError(tError(error.response.data.message));
      } else {
        alertError(tMailComposer('failedToSend'));
      }
      setIsSending(false);
    },
    throwOnError: false,
  });

  const { mutateAsync: genPresignedPutAttachmentURL } =
    useMutationGenPresignedPutAttachmentURL();

  const {
    data: searchMailRes,
    isSuccess,
    isLoading: isLoadingSearchMail,
    isError: isErrorSearchMail,
  } = useQuerySearchMailAddress(
    {
      value: searchKey,
    },
    {
      enabled: Boolean(searchKey),
      throwOnError: false,
    }
  );

  useEffect(() => {
    if (!isSuccess || !searchMailRes || !searchMailRes.mailAddresses) {
      setSearchData([]);
      return;
    }

    const addresses = searchMailRes.mailAddresses.map((r, key) => ({
      id: key,
      data: r.mail,
      value: r,
      label: r.name,
    }));
    setSearchData(addresses);
  }, [isSuccess, searchMailRes]);

  useEffect(() => {
    if (isErrorSearchMail) {
      alertWarning(t('noAddressFound'));
    }
  }, [isErrorSearchMail]);

  const configEditor = useMemo<InitialConfigType>(() => {
    if (eabItem) {
      return {
        ...initialConfigEditor,
        editorState: () => {
          const paragraph = $createParagraphNode();
          const text = tMailComposer('defaultContentBody');
          paragraph.append($createTextNode(text));
          $getRoot().append(paragraph);
          setBody(text);
        },
      };
    }

    if (!forwardData) {
      return initialConfigEditor;
    }

    return {
      ...initialConfigEditor,
      editorState: () => {
        if (forwardData) {
          const { body, from, to, subject } = forwardData.emailItem;
          const paragraph = $createParagraphNode();
          const text = `\n\n---------- ${tMailComposer(
            'forwardedMessage'
          )} --------\n${t('From')}: ${convertMailAddress(
            from
          )} \n${tMailComposer('date')}: ${formatDateTime(
            forwardData.date
          )} \n${tMailComposer('Subject')}: ${subject} \n${tMailComposer(
            'To'
          )}: ${convertMailAddress(to)}  \n\n${body}`;
          paragraph.append($createTextNode(text));
          $getRoot().append(paragraph);
          setBody(text);
        }
      },
    };
  }, [forwardData, isReplying, t, eabItem]);

  const resetComposer = () => {
    setBody('');
    setCc([]);
    setBcc([]);
    setSubject('');
    setOption(initOption);
    if (isReplying) {
      mailboxActions.setReplying(false);
    }
  };

  useEffect(() => {
    if (editorRef && isReplying) {
      editorRef.focus();
    }
  }, [editorRef, isReplying]);

  useEffect(() => {
    setReceiver(
      defaultUserToReply
        ? [
          {
            id: defaultUserToReply.name,
            label: defaultUserToReply.address,
            value: defaultUserToReply.address,
          },
        ]
        : []
    );
  }, [defaultUserToReply]);

  useEffect(() => {
    return () => {
      mailboxActions.setForwardData(undefined);
    };
  }, []);

  useEffect(() => {
    let subject = '';
    if (isReplying) {
      subject = groupAndGetSubject(replySubject!, `${tMailComposer('aw')}:`);
    }

    if (forwardData) {
      subject = groupAndGetSubject(
        forwardData.emailItem.subject,
        `${tMailComposer('fwd')}:`
      );
    }

    setCc([]);
    setBcc([]);
    setOption(initOption);
    setSubject(subject);
  }, [forwardData, isReplying, replySubject, groupAndGetSubject]);

  useEffect(() => {
    if (eabItem) {
      setSubject(E_DOCTOR_LETTER);
      setCategory(E_DOCTOR_LETTER);
      const { patientProfile } = eabItem;
      const activeInsurance = getActiveInsurance(
        patientProfile.patientInfo.insuranceInfos
      );
      const iPatientProfile: IPatientSearchResult = {
        id: patientProfile.id,
        ad4124: undefined,
        firstName: patientProfile.firstName,
        lastName: patientProfile.lastName,
        patientNumber: patientProfile.patientInfo.patientNumber,
        dOB: patientProfile.patientInfo.personalInfo.dOB,
        dateOfBirth: patientProfile.patientInfo.personalInfo.dateOfBirth,
        insuranceNumber: activeInsurance?.insuranceNumber!,
        typeOfInsurance: activeInsurance?.insuranceType,
        dateOfDeath: patientProfile.patientInfo.personalInfo.dateOfDeath,
      };
      setSelectedPatient(iPatientProfile);

      const fileEABPdf: AttachmentItem = {
        name: eabItem.signedFile?.name || E_DOCTOR_LETTER,
        url: eabItem.pDFUrl!,
        headers: {},
      };
      const fileEABXml: AttachmentItem = {
        name: eabItem.xmlFile?.name || E_DOCTOR_LETTER,
        url: eabItem.xmlFile?.url!,
        headers: {},
      };
      setFileAttachBySystem([fileEABXml, fileEABPdf]);
    }
  }, [eabItem]);

  const handleUploadFile = (files: FileList | null) => {
    if (!files) return;
    const fileArr: File[] = Array.from(files);

    if (
      fileArr.length > 10 ||
      fileArr.some((file) => file.size > MAX_SIZE_UPLOAD)
    ) {
      alertWarning(tMailComposer('FileTooLarge'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
        toaster: toast,
      });
      return;
    }
    const newFiles = fileArr.filter((n) =>
      fileUpload.every((n2) => n.name !== n2.name)
    );
    setFileUpload((prev) => {
      return prev.concat(newFiles);
    });
    alertSuccessfully(tMailComposer('fileUploaded'));
  };

  const onClickAttachment = (file: File, isRemove: boolean) => {
    if (isRemove) {
      return setFileUpload((prev) => prev.filter((f) => f.name !== file.name));
    }
  };

  const handleFilesToAttachments = async (): Promise<AttachmentItem[]> => {
    if (fileUpload.length === 0) {
      return [];
    }

    const attachments: AttachmentItem[] = [];
    for (const file of fileUpload) {
      const { data } = await genPresignedPutAttachmentURL({
        fileName: file.name,
      });
      if (!data) {
        continue;
      }

      attachments.push({
        name: file.name,
        url: `${data.bucket}/${file.name}`,
        contentType: file.type,
        headers: {
          'Content-Type': file.type,
          'Content-Tranfer-Encoding': 'base64',
          'Content-Disposition': `attachment; filename="${file.name}"`,
        },
      });
      await fetchWithRetry(data.presignedURL, {
        method: 'PUT',
        body: file,
      });
    }

    return attachments;
  };

  const transformPayload = (arr: Array<IMenuItem<any>>) => {
    return arr.map(
      (el) =>
      ({
        name: el?.label ?? el.data?.name,
        address: el.data,
      } as Address)
    );
  };

  const submitSendEabMail = async () => {
    const payload: EmailItem = {
      attachments: await handleFilesToAttachments(),
      returnPath: null!,
      messageID: null!,
      messageSource: null!,
      contentType: ContentTypeType.ContentTypeType_Html,
      requestDeliveryConfirmation: comfirnDelivery,
      isImportant: false,
      isDaft: false,
      isRead: false,
      isInvalidSignature: false,
      patient: selectedPatient && {
        id: selectedPatient.id!,
        fistName: selectedPatient.firstName,
        lastName: selectedPatient.lastName,
      },
      category: category,
      inReplyTo: [replyTo!],
      subject: subject,
      body: body,
      from: {
        name: null!, // BE will handle this
        address: selectedAccount.email,
      },
      to: transformPayload(receiver),
      cc: transformPayload(cc),
      bcc: transformPayload(bcc),
      sourceUrl: null!,
      statusMDN: null!,
      isMailSent: true,
    };
    sendMailEAB({
      eABId: eabItem?.id!,
      emailItem: payload,
      bsnrCode: globalData.userProfile?.bsnr!,
    });
  };

  useEffect(() => {
    if (isSubmitByConsentModal) {
      getSubmitManual?.(submitSendEabMail);
    }
  }, [isSubmitByConsentModal]);

  const submitSendMail = async () => {
    setIsSending(true);
    // send eAB via KIM
    if (eabItem) {
      try {
        const resConsent = await getConsentDocumenting({
          automaticDocumentingCase: AutomaticDocumentingCase.Sending,
        });
        if (
          resConsent.data.setting &&
          !resConsent.data.setting.isConsentTriggerForSending
        ) {
          mailboxActions.setSettingConsentData(
            resConsent.data.setting,
            AutomaticDocumentingCase.Sending
          );
          mailboxActions.setSubmitByConsent(true);
          return;
        }
        submitSendEabMail();
      } catch (err) {
        alertWarning(tMailComposer('getSettingFailureMessage'), {
          timeout: TOASTER_TIMEOUT_CUSTOM,
          toaster: toast,
        });
      }
    } else {
      const payload: SendMailRequest = {
        extraHeaders: null!,
        inReplyTo: [replyTo!],
        subject: subject,
        body: body,
        attachments: await handleFilesToAttachments(),
        category: category,
        deliveryConfirmation: comfirnDelivery,
        patient: selectedPatient && {
          id: selectedPatient.id!,
          fistName: selectedPatient.firstName,
          lastName: selectedPatient.lastName,
        },
        from: {
          name: null!, // BE will handle this
          address: selectedAccount.email,
        },
        to: transformPayload(receiver),
        cc: transformPayload(cc),
        bcc: transformPayload(bcc),
        isInvalidSignature: true,
      };
      sendMail(payload);
    }
  };

  const onSelectPatient = (item: IPatientSearchResult) => {
    setSelectedPatient(item);
  };

  const setSearchData = (addresses: IMenuItem<MailAddressEntry>[]) => {
    switch (searchType) {
      case ReceiverType.To:
        setReceiverList(addresses);
        break;
      case ReceiverType.Cc:
        setCcList(addresses);
        break;
      default:
        setBccList(addresses);
        break;
    }
  };

  const handleSearchMailAddress = (query: string, type: ReceiverType) => {
    setSearchKey(query);
    setSearchType(type);
    if (query.length === 0) {
      setSearchData([]);
      return;
    }
  };

  const onChangeEditor = (_: EditorState, editor: LexicalEditor) => {
    const stringifiedEditorState = JSON.stringify(
      editor.getEditorState().toJSON()
    );
    const parsedEditorState = editor.parseEditorState(stringifiedEditorState);
    const editorStateTextString = parsedEditorState.read(() =>
      $getRoot().getTextContent()
    );
    setBody(editorStateTextString);
  };

  const searchPatient = debounce(
    async (
      keyword: string,
      cb: CallbackLoadOptionsType<IPatientSearchResult>
    ) => {
      const patients = await PatientSearchService.searchPatients({
        keyword,
        type: SearchingType.PatientName,
      });
      cb(
        patients.map((patient, key) => ({
          id: key,
          label: patient.firstName!,
          value: patient.id!,
          data: patient!,
        }))
      );
    },
    300
  );

  const onConfirmDelete = () => {
    editorRef?.dispatchCommand(CLEAR_EDITOR_COMMAND, undefined);
    setReceiverList([]);
    setCcList([]);
    setBccList([]);
    setReceiver([]);
    setFileUpload([]);
    setConfirmDelete(false);
    resetComposer();
    mailboxActions.setComposer(null);
    mailboxActions.setEABItem(undefined);
  };

  const customNoOptionsMessage = ({ inputValue }) => {
    if (inputValue === '' || !inputValue.trim()) {
      return null;
    } else {
      return t('noResultFound');
    }
  };

  return (
    <Flex column className={className}>
      <Flex column className="send">
        <Flex className="send-option" align="center" justify="space-between">
          <Flex w="27px">{tMailComposer('To')}</Flex>
          <MultiSelect
            autoFocus
            value={receiver}
            isClearable
            isMulti
            hideSelectedOptions
            styles={BASE_MULTISELECT_KIM_RECEIVER}
            options={receiverList as any}
            filterOption={() => true}
            components={{
              Option: (props) => (
                <EnhancedOption {...props} query={searchKey} />
              ),
            }}
            noOptionsMessage={customNoOptionsMessage}
            onChange={(newValue: Array<IMenuItem<any>>) => {
              setReceiver(removeDuplicatedMenuItem(newValue));
            }}
            onInputChange={debounce((query: string) => {
              handleSearchMailAddress(query.trim(), ReceiverType.To);
            }, 300)}
            isLoading={isLoadingSearchMail}
          />
          <Flex shrink={0}>
            <Button
              small
              onClick={() =>
                setOption((prev) => {
                  if (prev.cc) setCcList([]);
                  return { ...prev, cc: !prev.cc };
                })
              }
            >
              <strong>Cc</strong>
            </Button>
            <Button
              small
              onClick={() =>
                setOption((prev) => {
                  if (prev.bcc) setBccList([]);
                  return { ...prev, bcc: !prev.bcc };
                })
              }
            >
              <strong>Bcc</strong>
            </Button>
          </Flex>
        </Flex>
        {option.cc && (
          <div className="sub-option">
            <Flex shrink={0} w="25px">
              Cc
            </Flex>
            <MultiSelect
              value={cc}
              isClearable
              isSearchable
              isMulti
              hideSelectedOptions
              styles={BASE_MULTISELECT_KIM_RECEIVER}
              options={ccList}
              filterOption={() => true}
              components={{
                Option: (props) => (
                  <EnhancedOption {...props} query={searchKey} />
                ),
              }}
              noOptionsMessage={customNoOptionsMessage}
              onChange={(newValue: Array<IMenuItem<any>>) =>
                setCc(removeDuplicatedMenuItem(newValue))
              }
              onInputChange={debounce((query: string) => {
                handleSearchMailAddress(query.trim(), ReceiverType.Cc);
              }, 400)}
            />
          </div>
        )}
        {option.bcc && (
          <div className="sub-option">
            <Flex shrink={0} w="25px">
              Bcc
            </Flex>
            <MultiSelect
              value={bcc}
              isClearable
              isMulti
              hideSelectedOptions
              styles={BASE_MULTISELECT_KIM_RECEIVER}
              options={bccList}
              filterOption={() => true}
              components={{
                Option: (props) => (
                  <EnhancedOption {...props} query={searchKey} />
                ),
              }}
              noOptionsMessage={customNoOptionsMessage}
              onChange={(newValue: Array<IMenuItem<any>>) =>
                setBcc(removeDuplicatedMenuItem(newValue))
              }
              onInputChange={debounce((query: string) => {
                handleSearchMailAddress(query.trim(), ReceiverType.Bcc);
              }, 400)}
            />
          </div>
        )}
        <Flex align="center" className="subject">
          {tMailComposer('Subject')}&nbsp;&nbsp;
          <input
            type="text"
            maxLength={200}
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
          />
        </Flex>
      </Flex>
      <Flex className="composer-area">
        <LexicalComposer
          key={`lexical-composer${forwardData ? forwardData.id : ''}`}
          initialConfig={{
            ...configEditor,
            nodes: [PlaceholderInputNode, QuestionnaireNode],
          }}
        >
          <PlainTextPlugin
            contentEditable={<ContentEditable />}
            placeholder={<></>}
            ErrorBoundary={LexicalErrorBoundary}
          />
          <OnChangePlugin
            key={useId()}
            onChange={onChangeEditor}
            ignoreSelectionChange
            ignoreHistoryMergeTagChange
          />
          <ExposeEditorInstancePlugin setEditorRef={setEditorRef} />
          <ClearEditorPlugin />
          <TextmoduleTabable />
          <TextModuleTypeaheadPlugin
            isInDialog
            shouldNodesRegistered
            usedForList={textmoduleUsedForList}
            createNodes={(textModuleValue) => {
              const [nodes, selectableNode] =
                $$transformToTextModuleNodes(textModuleValue);
              return [nodes, selectableNode];
            }}
            isBottomHeadMenu={false}
            renderFn={renderDefaultTextmoduleMenu}
          />
        </LexicalComposer>
      </Flex>
      <Flex flexWrap className="files-area">
        {eabItem &&
          !!fileAttachBySystem?.length &&
          fileAttachBySystem.map((file) => (
            <Attachment
              key={file.name}
              attachment={file}
              action={'download'}
              onClickAttachment={() => handleDownloadFile(file)}
            />
          ))}
        {fileUpload?.map((file) => (
          <Attachment
            key={file.name}
            attachment={file}
            action={'remove'}
            onClickAttachment={onClickAttachment}
          />
        ))}
      </Flex>
      <Flex
        justify="space-between"
        style={{ marginTop: 'auto', paddingTop: 10 }}
      >
        <Flex className="option-select" align="baseline">
          <ReactSelect
            isDisabled={Boolean(eabItem)}
            items={KIM_CATEGORY}
            selectedValue={category}
            isClearable={false}
            menuPosition="fixed"
            placeholder={tMailComposer('SelectCategory')}
            styles={{ control: (base) => ({ ...base, width: 160 }) }}
            onItemSelect={(category) =>
              setCategory(category['value'] as string)
            }
          />
          <AsyncSelect<IPatientSearchResult>
            components={{
              Option: (props) => (
                <components.Option {...props}>
                  <PatientItemRenderer
                    data={props.data.data!}
                    searchingKeyword={props.selectProps.inputValue}
                    handleClick={onSelectPatient}
                  />
                </components.Option>
              ),
              NoOptionsMessage: (props) => (
                <components.NoOptionsMessage {...props}>
                  {t('noResultFound')}
                </components.NoOptionsMessage>
              ),
            }}
            styles={{
              menu: (base) => ({
                ...base,
                width: 360,
              }),
              control: (base) => ({
                ...base,
                width: 180,
              }),
            }}
            value={
              selectedPatient
                ? {
                  id: selectedPatient.id!,
                  label: `${selectedPatient.lastName}, ${selectedPatient.firstName}`,
                  value: selectedPatient.id!,
                }
                : undefined
            }
            loadOptions={(query, setOptions) => {
              searchPatient(
                query,
                (patients: Array<IMenuItemWithData<IPatientSearchResult>>) => {
                  setOptions(patients);
                }
              );
            }}
            placeholder={tMailComposer('selectPatient')}
            isDisabled={Boolean(eabItem)}
          />
          <Checkbox
            checked={comfirnDelivery}
            label={tMailComposer('RequestDeliveryConfirmation')}
            onChange={() => setComfirnDelivery((prev) => !prev)}
          />
        </Flex>
        <Flex align="center" className="footer">
          <Button
            isActionIcon
            iconOnly
            minimal
            icon={<Svg src={TrashIcon} />}
            onClick={() => setConfirmDelete(true)}
          />
          <label htmlFor="file-upload">
            <input
              id="file-upload"
              name="file-upload"
              type="file"
              accept="application/pdf, text/xml, .ukf, "
              multiple
              hidden
              onChange={(e) => handleUploadFile(e.currentTarget.files)}
              onClick={(e) => ((e.target as HTMLInputElement).value = null!)}
            />
            <Button
              className="btn-file"
              iconOnly
              isActionIcon
              minimal
              icon={<Svg src={ClipPaperIcon} />}
            />
          </label>
          <Button
            intent="primary"
            disabled={
              !receiver.length ||
              !body ||
              !(subject.trim().length || forwardData) ||
              !!cardError
            }
            loading={isSending}
            onClick={submitSendMail}
          >
            {tMailComposer('Send')}
          </Button>
        </Flex>
      </Flex>

      <ConfirmDialog
        isOpen={confirmDelete}
        close={() => setConfirmDelete(false)}
        confirm={onConfirmDelete}
        text={{
          btnCancel: tButtonActions('cancelText'),
          btnOk: tButtonActions('removeText'),
          title: tMailComposer('leaveThisMail'),
          message: tMailComposer('confirmLeaveThisMail'),
        }}
      />
    </Flex>
  );
};

export default MailComposer;
