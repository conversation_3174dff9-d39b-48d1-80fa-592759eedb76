import React, { FC, useState } from 'react';
import { debounce } from 'lodash';
import { useRouter } from 'next/router';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import {
  Flex,
  H1,
  Button,
  Svg,
  BodyTextL,
  BodyTextS,
  H4,
  BodyTextM,
  Intent,
  Tooltip,
  LoadingState,
  Box,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { TabIds, MailboxType } from '@tutum/mvz/module_mailbox/Mailbox.type';
import i18n from '@tutum/infrastructure/i18n';
import {
  MailItemDTO,
  useQueryGetArchiveSetting,
} from '@tutum/hermes/bff/legacy/app_mvz_mail';
import {
  handleDownloadFile,
  formatDateTime,
  getRecipientAddresses,
  parseAddressToName,
  isEABMail,
  MDN_STATUS_VALID,
  StateAssignPatient,
} from '../Mailbox.helper';
import { mailboxActions, useMailboxStore } from '../Mailbox.store';
import { kvBillingActions } from '@tutum/mvz/module_kv_billing/KVBilling.store';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { Address, MailHeaderKey } from '@tutum/hermes/bff/mail_common';
import { SPACE } from '@tutum/design-system/styles';
import { BillingMailHeader } from '@tutum/hermes/bff/billing_history_common';
import { Callout } from '@tutum/design-system/components/Core';
import ButtonCreateSchein from '../create-schein/ButtonCreateSchein.styled';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MailStatus } from '@tutum/hermes/bff/legacy/app_mvz_mail';
import InfoConfirmDialogHOC from '@tutum/design-system/components/Modal/info-confirm-dialog-hoc';
import { MailCategory, MDNStatus } from '@tutum/hermes/bff/legacy/mail_common';
import { getPatientXMLData } from '@tutum/hermes/bff/legacy/app_mvz_eab';
import { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { MailComposer } from '../mail-composer';
import { MailContent } from '../mail-content';
import { MailTag } from '../mail-tag';
import { Attachment } from '../attachment';

const MailIcon = '/images/email.svg';
const MailReadIcon = '/images/mail-read.svg';
const ArchiveIcon = '/images/archive.svg';
const ForwardIcon = '/images/chevron-corner-up-right.svg';
const ReplyIcon = '/images/chevron-corner-up-left.svg';
const ViewSourceIcon = '/images/information-grey-circle.svg';
const AlertCircle = '/images/alert-circle.svg';
const DeleteIcon = '/images/delete-mail.svg';
const MailAlertIcon = '/images/mail-alert.svg';
const MailApprovedIcon = '/images/mail-approved.svg';
const InfoWarningIcon = '/images/info-solid-warn.svg';
const ValidSignatureIcon = '/images/qes/valid-signature.svg';
const UnknownSignatureIcon = '/images/qes/unknown-signature.svg';

export interface IMailDetailsProps extends Partial<MailItemDTO> {
  className?: string;
  isView?: boolean;
}

const MailDetails: FC<IMailDetailsProps> = (props) => {
  const { className, emailItem, date, id, isView } = props;
  const {
    composerArea,
    selectedAccount,
    selectedTabId,
    mailboxType,
    selectedMail,
    selectedProposalPatients,
    selectedProposalTextSearch,
  } = useMailboxStore();

  const [defaultUserToReply, setDefaultUserToReply] = useState<Address | undefined>(undefined);

  const { t } = i18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
  });
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof MailboxI18n.ButtonActions
  >({
    namespace: 'Mailbox',
    nestedTrans: 'ButtonActions',
  });

  const mailboxStore = useMailboxStore();

  const toaster = useToaster();

  const router = useRouter();

  const { loadingMailDetails, currentStateAssign } = mailboxStore;

  const {
    from,
    to,
    body,
    subject,
    contentType,
    category,
    attachments,
    isRead,
    received,
    mailHeader,
    archivedAt,
    patient,
    isInvalidSignature,
  } = emailItem || {};

  const recipients = parseAddressToName(
    getRecipientAddresses(emailItem),
    t,
    selectedAccount.email
  );
  const sender = parseAddressToName([from!], t);
  const isKIM = mailboxType === MailboxType.Kim;

  const isNotAssigable =
    mailHeader?.[BillingMailHeader.BillingMailHeader_NotAssignable];

  const mailGuid = mailHeader?.[BillingMailHeader.BillingMailHeader_GUID];
  const msgType =
    mailHeader?.[MailHeaderKey.MailHeaderKey_ServiceIdentifer]?.split(';');
  const isProfessionalFeefback = msgType && msgType[1] == 'Rueckmeldung';
  const headerSender = mailHeader?.[BillingMailHeader.BillingMailHeader_From];
  const messageId = mailHeader?.[BillingMailHeader.BillingMailHeader_MessageId];
  const dispatchedOn =
    mailHeader?.[BillingMailHeader.BillingMailHeader_DispatchedOn];

  const onOpenSchein = () => {
    mailboxActions.getInformationSchein(
      selectedMail?.emailItem.patient?.id || ''
    );
    mailboxActions.setStageDialog({
      stage: 'openCreateSchein',
      context: {
        patientId: selectedMail?.emailItem.patient?.id!,
      },
    });
    mailboxActions.setDialogPatientOrSchein(true);
  };

  const openComposer = () => {
    if (!composerArea?.size) {
      mailboxActions.setComposer('sm');
    }
  };

  const renderContentDynamic = (content: string) => {
    return content;
  };

  const onReplyMail = () => {
    setDefaultUserToReply(selectedTabId === TabIds.Inbox ? from : to?.[0]);
    mailboxActions.setForwardData(null!);
    mailboxActions.setReplying(true);
    openComposer();
  };

  const onForwardMail = () => {
    mailboxActions.setForwardData({ emailItem: emailItem!, date: date!, id: id! });
    mailboxActions.setReplying(false);
    setDefaultUserToReply(null!);
    openComposer();
  };

  const onMarkAsReadOrUnread = debounce(() => {
    mailboxActions
      .markAsReadOrUnread({
        id: id!,
        isRead: !isRead,
        inbox: selectedTabId === TabIds.Inbox,
      })
      .then(() => {
        alertSuccessfully(
          isRead ? t('markAsUnreadSuccessfully') : t('markAsReadSuccessfully'),
          { toaster }
        );
        mailboxActions.reloadList(selectedTabId);
      });
  }, 300);

  const onArchiveMail = () => {
    mailboxActions.changeMailsStatus({
      ids: [id!],
      status: MailStatus.Archived,
    });
    mailboxActions.reloadList(selectedTabId);
    mailboxActions.reloadList(TabIds.Archived);
    alertSuccessfully(t('archived'), { toaster });
  };

  const onDeleteMail = async () => {
    await mailboxActions.deleteMails([id!]);
    mailboxActions.reloadList(selectedTabId);
    alertSuccessfully(t('emailDeleted'), { toaster });
  };

  const { data: settings } = useQueryGetArchiveSetting({
    accountEmail: selectedAccount.email,
  });

  const noResults = () => {
    return (
      <Flex justify="center" align="center" gap={4} p="8px 16px">
        <BodyTextM className="text-no-result">{t('noResultFound')}</BodyTextM>
        <BodyTextM
          className="btn-link-create-patient"
          onClick={async () => {
            let patientInfo: PatientInfo | undefined = undefined;
            if (isEABMail(emailItem)) {
              const data = await getPatientXMLData({
                mailId: selectedMail?.id!,
              });
              patientInfo = data.data?.patientInfo;
            }

            mailboxActions.setStageDialog({
              stage: 'openCreatePatient',
              context: patientInfo!,
            });
            mailboxActions.setDialogPatientOrSchein(true);
          }}
        >
          {t('createPatient')}
        </BodyTextM>
      </Flex>
    );
  };

  const renderEABIndicator = () => {
    const identifier = mailHeader?.['X-Kim-Dienstkennung'];
    if (
      !identifier ||
      ![
        String(MailCategory.MailCategory_EAB),
        'Arztbrief;Eingangsbestaetigung;V1.2',
      ].includes(identifier)
    ) {
      return;
    }

    if (emailItem?.statusMDN === MDNStatus.MDNStatus_Pending) {
      return (
        <Tooltip placement="top" content={t('tooltipMDNRequested')}>
          <Svg className="sl-mail-details__body--icon" src={MailAlertIcon} />
        </Tooltip>
      );
    } else {
      return (
        <Tooltip
          placement="top"
          content={
            selectedTabId === TabIds.Inbox
              ? t('tooltipMDNSent')
              : t('tooltipMDNReceived')
          }
        >
          <Svg className="sl-mail-details__body--icon" src={MailApprovedIcon} />
        </Tooltip>
      );
    }
  };

  const renderQESStatus = () => {
    return (
      <Tooltip
        content={
          isInvalidSignature === null || isInvalidSignature === true
            ? t('tooltipUnknownSignature')
            : t('tooltipValidSignature')
        }
      >
        <Svg
          className="sl-mail-details__body--icon"
          src={
            isInvalidSignature === null || isInvalidSignature === true
              ? UnknownSignatureIcon
              : ValidSignatureIcon
          }
        />
      </Tooltip>
    );
  };

  const bannerNoSchein = () => {
    return (
      <Flex className="sl-banner-no-schein" justify="space-between">
        <Flex
          gap={8}
          className="container-left-no-schein"
          align="center"
          justify="center"
        >
          <Svg src={InfoWarningIcon} />
          <BodyTextM className="text-title-no-schein">
            {t('noActiveKvSchein')}
          </BodyTextM>
        </Flex>
        <Flex>
          <ButtonCreateSchein
            t={t}
            selectedMail={selectedMail!}
            onOpenSchein={onOpenSchein}
            onSuccess={() =>
              mailboxActions.setScheinIsExist({
                isExist: false,
                state: StateAssignPatient.default,
              })
            }
          />
        </Flex>
      </Flex>
    );
  };

  const isValidToShowBanner =
    patient &&
    !isView &&
    mailboxStore.selectedTabId === TabIds.Inbox &&
    !currentStateAssign.isExistSchein &&
    currentStateAssign.state === StateAssignPatient.auto;

  return !emailItem ? null : (
    <Flex className={className} column justify="space-between">
      <div>
        <Flex
          className="sl-mail-details__header"
          justify="space-between"
          align="flex-start"
          column
        >
          <Flex className="sl-mail-subject">
            <H1>{subject}</H1>
            <Flex align="center">
              {category && <MailTag t={t} value={category} type="category" />}
              {isNotAssigable && (
                <MailTag t={t} value={category} type="notAssignable" />
              )}
              {!isView && (
                <>
                  {isKIM && (
                    <MailTag
                      t={t}
                      className="sl-mail-details__header--patient"
                      editable={mailboxStore.selectedTabId !== TabIds.Outbox}
                      type="patient"
                      patient={patient}
                      noResults={noResults()}
                      proposalPatients={selectedProposalPatients}
                      proposalTextSearch={selectedProposalTextSearch}
                    />
                  )}
                  <Tooltip
                    content={isRead ? t('markAsUnread') : t('markAsUnread')}
                    usePortal
                  >
                    <Button
                      isActionIcon
                      iconOnly
                      minimal
                      icon={<Svg src={isRead ? MailReadIcon : MailIcon} />}
                      onClick={onMarkAsReadOrUnread}
                    />
                  </Tooltip>
                  {!archivedAt && (
                    <InfoConfirmDialogHOC
                      isShowIconTitle
                      title={t('archiveDialogTitle')}
                      content={
                        settings?.setting?.isActive
                          ? t('archiveDialogActiveContent', {
                            days: settings?.setting?.deleteAfter,
                          })
                          : t('archiveDialogInactiveContent')
                      }
                      cancelText={tButtonActions('no')}
                      confirmText={tButtonActions('yesArchive')}
                    >
                      <Tooltip content={t('archivePatient')} position="left">
                        <Button
                          iconOnly
                          isActionIcon
                          minimal
                          icon={<Svg src={ArchiveIcon} />}
                          onClick={onArchiveMail}
                        />
                      </Tooltip>
                    </InfoConfirmDialogHOC>
                  )}
                  {archivedAt && (
                    <InfoConfirmDialogHOC
                      type={Intent.DANGER}
                      isShowIconTitle
                      title={t('deleteDialogTitle')}
                      content={t('deleteDialogContent')}
                      cancelText={tButtonActions('no')}
                      confirmText={tButtonActions('yesDelete')}
                    >
                      <Button
                        iconOnly
                        isActionIcon
                        minimal
                        icon={<Svg src={DeleteIcon} />}
                        onClick={onDeleteMail}
                      />
                    </InfoConfirmDialogHOC>
                  )}
                </>
              )}
            </Flex>
          </Flex>
          <br />

          <Flex w="100%">
            {isNotAssigable && (
              <Callout
                intent={Intent.WARNING}
                icon={<></>}
                className="sl-mail-details__warning-message"
              >
                <Flex>
                  <Svg className="sl-callout-icon" src={AlertCircle} />
                  <BodyTextM
                    className="sl-mail-details__warning-message--title"
                    fontWeight="SemiBold"
                    color={COLOR.TEXT_WARNING}
                  >
                    {isProfessionalFeefback
                      ? t('tooltipProNotAssignableLine1')
                      : t('tooltipTechNotAssignableLine1') +
                      ' ' +
                      t('tooltipNotAssignableLine2')}
                  </BodyTextM>
                </Flex>
                <Box ml="20px" style={{ color: COLOR.TEXT_PRIMARY_BLACK }}>
                  <Flex ml="5px">
                    {t('headerSender')}
                    <BodyTextM className="sl-mail-details__warning-message--detail">
                      {headerSender}
                    </BodyTextM>
                  </Flex>
                  <Flex ml="5px">
                    {t('dispatchedOn')}
                    <BodyTextM className="sl-mail-details__warning-message--detail">
                      {dispatchedOn}
                    </BodyTextM>
                  </Flex>
                  <Flex ml="5px">
                    {t('messageId')}
                    <BodyTextM className="sl-mail-details__warning-message--detail">
                      {messageId}
                    </BodyTextM>
                  </Flex>
                  {isProfessionalFeefback && (
                    <Flex ml="5px">
                      {t('guid')}
                      <BodyTextM className="sl-mail-details__warning-message--detail">
                        {mailGuid}
                      </BodyTextM>
                    </Flex>
                  )}
                </Box>
              </Callout>
            )}
          </Flex>
        </Flex>
        {isValidToShowBanner && (
          <>
            {bannerNoSchein()}
            <br />
          </>
        )}
        <div className="sl-mail-details__body">
          <div className="sl-mail-details__body--info">
            <Flex justify="space-between">
              <Flex column>
                <H4
                  className="sl-mail-details__body--name"
                  fontWeight="SemiBold"
                >
                  {sender}
                </H4>
                <BodyTextL>
                  {renderContentDynamic(`(${from?.address})`)}
                </BodyTextL>
              </Flex>

              <Flex className="sl-mail-details__body--actions" align="center">
                <BodyTextS className="sl-mail-details__body--date">
                  {formatDateTime(received || date!)}
                </BodyTextS>
                {isEABMail(emailItem) && renderQESStatus()}
                {isEABMail(emailItem) &&
                  MDN_STATUS_VALID.includes(emailItem?.statusMDN) &&
                  renderEABIndicator()}
                {!isView && (
                  <Flex align="center">
                    <Tooltip content={t('viewMessageSource')} usePortal>
                      <Svg
                        className="cursor-pointer"
                        src={ViewSourceIcon}
                        onClick={() => {
                          mailboxActions.setOpenModalMessageSource(true);
                          mailboxActions.setViewMailData(
                            selectedMail?.emailItem!
                          );
                        }}
                      />
                    </Tooltip>
                  </Flex>
                )}
              </Flex>
            </Flex>
            <Flex align="flex-start">
              {recipients.length > 1 ? (
                <>
                  <BodyTextM>
                    {renderContentDynamic(`${recipients[0]}`)}
                  </BodyTextM>
                  <Tooltip content={recipients.slice(1).join(', ')}>
                    <BodyTextM>
                      {renderContentDynamic(`, ${t('someoneElse')}`)}
                    </BodyTextM>
                  </Tooltip>
                </>
              ) : (
                <BodyTextM>
                  {renderContentDynamic(`${recipients[0]}`)}
                </BodyTextM>
              )}
            </Flex>
          </div>
          {loadingMailDetails ? (
            <LoadingState />
          ) : (
            <div>
              <MailContent
                className="sl-mail-details__body--content"
                content={body!}
                contentType={contentType!}
              />
              {!!attachments?.length && (
                <Flex className="sl-mail-details__body--attachments" gap={8}>
                  {attachments.map((attachment, index) => (
                    <Attachment
                      key={`${attachment.name}-${index}`}
                      attachment={attachment}
                      action="download"
                      onClickAttachment={() => handleDownloadFile(attachment)}
                    />
                  ))}
                </Flex>
              )}
            </div>
          )}
        </div>
        {!isView && (
          <Flex
            className="sl-mail-details__footer"
            justify="flex-end"
            mr={SPACE.SPACE_S}
          >
            {isKIM ? (
              <Flex gap={8} align="center">
                <Button
                  outlined
                  icon={<Svg src={ForwardIcon} />}
                  intent={Intent.PRIMARY}
                  onClick={onForwardMail}
                >
                  {tButtonActions('forward')}
                </Button>
                <Button
                  intent={Intent.PRIMARY}
                  icon={<Svg src={ReplyIcon} />}
                  onClick={onReplyMail}
                >
                  {tButtonActions('reply')}
                </Button>
              </Flex>
            ) : (
              <Button
                intent={Intent.PRIMARY}
                onClick={() => {
                  kvBillingActions.setIsOpenBillingHistory(true);
                  router.push(ROUTING.BILLING_KV);
                }}
              >
                {tButtonActions('viewBillingHistory')}
              </Button>
            )}
          </Flex>
        )}
      </div>
      {composerArea.size === 'sm' && (
        <MailComposer
          size={composerArea.size}
          defaultUserToReply={defaultUserToReply}
          replySubject={subject}
        />
      )}
    </Flex>
  );
};

export default MailDetails;
