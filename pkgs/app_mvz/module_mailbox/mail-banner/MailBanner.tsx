import React from 'react';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import { CardErrorsType } from '../Mailbox.type';

import {
  Banner,
  BannerActionTypes,
  Intent,
} from '@tutum/design-system/components';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

export interface IMailBannerProps {
  cardError: CardErrorsType;
  t: IFixedNamespaceTFunction<keyof typeof MailboxI18n>;
}

const MailBanner: React.FC<IMailBannerProps> = (props) => {
  const { cardError, t } = props;

  const action = React.useMemo<BannerActionTypes | undefined>(() => {
    if (cardError === CardErrorsType.CardNotAvailable) {
      return {
        name: t('refreshPage'),
        handleClick: () => window.location.reload(),
      };
    }

    return undefined;
  }, [cardError]);

  return (
    <Banner intent={Intent.WARNING} action={action}>
      {t(cardError)}
    </Banner>
  );
};

export default MailBanner;
