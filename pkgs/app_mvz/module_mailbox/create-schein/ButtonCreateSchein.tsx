import React, { FC, useContext, useState } from 'react';
import { sortBy } from 'lodash';

import useToaster from '@tutum/mvz/hooks/useToaster';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { DoctorInfo } from '@tutum/hermes/bff/patient_profile_common';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useQueryGetListBSNRName } from '@tutum/hermes/bff/legacy/app_bsnr';
import {
  getScheinsOverview,
  useMutationCreateSchein,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { Button, Intent } from '@tutum/design-system/components/Core';
import { getPatientProfileById } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { createDefaultPayloadSchein } from '../Mailbox.helper';
import { MailItemDTO } from '@tutum/hermes/bff/app_mvz_mail';
import { alertError, alertSuccessfully } from '@tutum/design-system/components';

export interface IButtonCreateScheinProps {
  className?: string;
  t: any;
  onSuccess?: () => void;
  onOpenSchein: () => void;
  selectedMail: MailItemDTO;
}

const ButtonCreateSchein: FC<IButtonCreateScheinProps> = (props) => {
  const { selectedMail, onSuccess, t, onOpenSchein } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const globalContext = useContext(GlobalContext.instance);
  const doctors = globalContext.useGetDoctorList();

  const currentUser = globalContext.useGetLoggedInUserProfile();
  const { data: bSNRData } = useQueryGetListBSNRName();
  const toaster = useToaster();

  const { mutate: onCreateScheinKV } = useMutationCreateSchein({
    onSuccess: () => {
      alertSuccessfully(t('createScheinKVSuccess'), { toaster });
      onSuccess?.();
    },
    onError: () => {
      alertError(t('createScheinKVFailure'), { toaster });
      onOpenSchein();
    },
  });

  const getDefaultDoctor = async (
    doctorInfo?: DoctorInfo
  ): Promise<Nullable<string>> => {
    if (doctorInfo?.treatmentDoctorId) {
      return doctorInfo?.treatmentDoctorId;
    }
    if (currentUser && currentUser.markAsBillingDoctor) {
      return currentUser.id;
    }
    if (
      bSNRData?.data?.length === 1 &&
      doctors.length === 1 &&
      doctors[0].markAsBillingDoctor
    ) {
      return doctors[0].id;
    }
    const schein = await getScheinsOverview({
      patientId: selectedMail?.emailItem?.patient?.id!,
    });
    const filtered = sortBy(schein.data.scheinItems, (o) => o.createdTime);
    const findLastSchein = filtered.at(-1);
    return findLastSchein?.doctorId;
  };

  const onClickAutoCreateSchein = async () => {
    setLoading(true);
    if (selectedMail?.emailItem?.patient?.id) {
      try {
        const resPatientProfile = await getPatientProfileById({
          id: selectedMail?.emailItem?.patient?.id,
        });
        const defaultDoctor = await getDefaultDoctor(
          resPatientProfile.data.patientInfo.doctorInfo
        );
        const payload = createDefaultPayloadSchein(
          selectedMail.emailItem.patient.id,
          defaultDoctor as string,
          resPatientProfile.data.patientInfo
        );
        onCreateScheinKV(payload);
      } finally {
        setLoading(false);
      }
    } else {
      onOpenSchein();
    }
  };

  return (
    <Button
      intent={Intent.PRIMARY}
      onClick={onClickAutoCreateSchein}
      loading={loading}
    >
      {t('ButtonActions.createSchein')}
    </Button>
  );
};

export default ButtonCreateSchein;
