import React, { useEffect, useMemo, useState } from 'react';
import debounce from 'lodash/debounce';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';

import {
  Flex,
  Svg,
  Button,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Intent,
  Checkbox,
  Tooltip,
} from '@tutum/design-system/components/Core';
import CategoriesSearch, {
  IMenuItem,
} from '@tutum/mvz/components/categories-search';
import I18n from '@tutum/infrastructure/i18n';
import { SearchType } from '@tutum/hermes/bff/mail_common';
import { SettingsFeatures_KIMAccount_SendMDN } from '@tutum/hermes/bff/legacy/settings_common';
import { mailboxActions, useMailboxStore } from '../Mailbox.store';
import { MailboxType, TabIds } from '../Mailbox.type';
import { MailStatus } from '@tutum/hermes/bff/legacy/app_mvz_mail';
import InfoConfirmDialogHOC from '@tutum/design-system/components/Modal/info-confirm-dialog-hoc';

const RefreshIcon = '/images/refresh-grey.svg';
const MailAddIcon = '/images/mail-add-white.svg';
const SearchIcon = '/images/search-sidebar-disable.svg';
const MailReadIcon = '/images/mail-read.svg';
const MailNew2Icon = '/images/mail-new-2.svg';
const ArchiveIcon = '/images/archive.svg';

export interface IActionsBarProps {
  className?: string;
}

const debounceSearch = debounce((searchFunc) => searchFunc(), 500);

const ConfirmDialog = ({ countSelected, children }) => {
  const { t } = I18n.useTranslation({
    namespace: 'Mailbox',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  return (
    <InfoConfirmDialogHOC
      title={t('BulkAction.confirmHeaderDialog')}
      content={t('BulkAction.confirmContentDialog', {
        count: countSelected,
      })}
      confirmText={tButtonActions('yes')}
    >
      {children}
    </InfoConfirmDialogHOC>
  );
};

const ActionsBar = (props: IActionsBarProps) => {
  const { className } = props;

  const [isAutoSendMDN, setIsAutoSendMDN] = useState<boolean>(true);

  const { t } = I18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
    nestedTrans: '',
  });
  const { t: tActionsBar } = I18n.useTranslation<
    keyof typeof MailboxI18n.ActionsBar
  >({
    namespace: 'Mailbox',
    nestedTrans: 'ActionsBar',
  });
  const { t: tBulkAction } = I18n.useTranslation<
    keyof typeof MailboxI18n.BulkAction
  >({
    namespace: 'Mailbox',
    nestedTrans: 'BulkAction',
  });

  const {
    loadingMailList,
    cardError,
    isSearching,
    loadingSyncMails,
    mailboxType,
    archived,
    selectedTabId,
    sendMDNotificationSupported,
  } = useMailboxStore();
  const mailboxStore = useMailboxStore();
  const searchInfo = mailboxStore[selectedTabId].searchInfo;
  const { searchType, searchValue } = searchInfo;
  const {
    setSearchInfo,
    reloadList,
    setComposer,
    syncInboxMails,
    changeMailsStatus,
    setReplying,
    clearSelectedIds,
    unarchiveMail,
    selectAllMails,
    setConfirmDelivery,
    setSelectedIds,
  } = mailboxActions;

  useEffect(() => {
    if (searchInfo && isSearching) {
      debounceSearch(reloadList);
    }
  }, [searchInfo.searchValue, isSearching]);

  useEffect(() => {
    if (
      ![
        SettingsFeatures_KIMAccount_SendMDN.CheckMDNRequest,
        SettingsFeatures_KIMAccount_SendMDN.UnCheckMDNRequest,
      ].includes(sendMDNotificationSupported)
    ) {
      setConfirmDelivery(true);
    }
    setIsAutoSendMDN(
      sendMDNotificationSupported ===
      SettingsFeatures_KIMAccount_SendMDN.CheckMDNRequest
    );
  }, [sendMDNotificationSupported]);

  const placeholder = useMemo<string>(() => {
    const placeholderPrefix = 'placeholder';
    switch (searchType) {
      case SearchType.SearchType_Subject:
        return tActionsBar(
          `${placeholderPrefix}${SearchType.SearchType_Subject}`
        );
      case SearchType.SearchType_To:
        return tActionsBar(`${placeholderPrefix}${SearchType.SearchType_To}`);
      default:
        return tActionsBar(`${placeholderPrefix}${SearchType.SearchType_From}`);
    }
  }, [searchType]);

  const disableSyncMail = useMemo(() => {
    if (mailboxType === MailboxType.KVConnect) {
      return loadingMailList;
    }

    return loadingMailList || !!cardError;
  }, [loadingMailList, cardError, mailboxType]);

  const getCategories = (): IMenuItem[] => {
    return Object.keys(SearchType)
      .filter((key) => SearchType[key] !== SearchType.SearchType_From)
      .map((key) => ({
        label: t(`${SearchType[key] as SearchType}`),
        value: SearchType[key],
      }));
  };

  const handleSetSearchType = (searchType: IMenuItem | null) => {
    setSearchInfo(
      {
        ...searchInfo,
        searchType:
          (searchType?.value as SearchType) || SearchType.SearchType_From,
      },
      false
    );
  };

  const onItemSelect = (item: IMenuItem) => {
    handleSetSearchType(item);
  };

  const onQueryChange = (searchValue: string) => {
    setSearchInfo({ ...searchInfo, searchValue });
  };

  const clearSearch = () => {
    handleSetSearchType(null);
    if (searchInfo.searchValue) {
      setSearchInfo({ ...searchInfo, searchValue: '' });
    }
  };

  const onAddNewMail = () => {
    setComposer('lg');
    setReplying(false);
  };

  const currentMailbox = mailboxStore[selectedTabId];

  const isSelectAll = currentMailbox.isSelectAll;
  const isSelectMode =
    currentMailbox.selectedIds.length > 0 || currentMailbox.isSelectAll;
  const countSelected = isSelectAll
    ? currentMailbox.total
    : currentMailbox.selectedIds.length;
  const isInboxTab = selectedTabId === TabIds.Inbox;
  const isArchivedTab = selectedTabId === TabIds.Archived;
  const someUnread = currentMailbox.selectedIds.some(
    (el) =>
      !currentMailbox.mails.find((mail) => mail.id === el)?.emailItem.isRead
  );
  const toastSuccess = (message) => {
    alertSuccessfully(message);
  };

  const reloadMailbox = () => {
    clearSelectedIds(TabIds.Inbox);
    clearSelectedIds(TabIds.Archived);
    reloadList(TabIds.Inbox);
    reloadList(TabIds.Archived);
  };

  const handleMarkAsRead = async () => {
    await changeMailsStatus({
      ids: currentMailbox.selectedIds,
      status: MailStatus.Read,
      isAll: currentMailbox.isSelectAll,
    });
    reloadList(selectedTabId);
    toastSuccess(tBulkAction('emailsMarkedAsRead'));
  };

  const handleMarkAsUnread = async () => {
    await changeMailsStatus({
      ids: currentMailbox.selectedIds,
      status: MailStatus.Unread,
      isAll: currentMailbox.isSelectAll,
    });
    reloadList(selectedTabId);
    toastSuccess(tBulkAction('emailsMarkedAsUnread'));
  };

  const handleArchivedEmail = async () => {
    await changeMailsStatus({
      ids: currentMailbox.selectedIds,
      status: MailStatus.Archived,
      isAll: currentMailbox.isSelectAll,
    });
    reloadMailbox();
    toastSuccess(tBulkAction('emailsArchived'));
    if (currentMailbox.isSelectAll) selectAllMails(selectedTabId, false);
  };

  const handleUnarchiveMail = async () => {
    await unarchiveMail({
      ids: archived.selectedIds,
      isAll: currentMailbox.isSelectAll,
    });
    reloadMailbox();
    toastSuccess(tBulkAction('emailsMovedToInbox'));
    if (currentMailbox.isSelectAll) selectAllMails(selectedTabId, false);
  };

  const handleSelectAll = () => {
    selectAllMails(selectedTabId, true);
    setSelectedIds(
      selectedTabId,
      currentMailbox.mails.map((mail) => mail.id),
      true
    );
  };

  const handleClearSelectALl = () => {
    selectAllMails(selectedTabId, false);
    clearSelectedIds(selectedTabId);
  };

  return (
    <Flex
      className={className}
      column
      style={{
        position: 'relative',
      }}
      gap={7}
    >
      {!isSelectMode && (
        <>
          <Flex align="center">
            <CategoriesSearch
              className="sl-actions-bar__seach-bar"
              listCategories={getCategories()}
              searchType={getCategories().find(
                (category) => category.value === searchType
              ) || null}
              items={[]}
              clearCallback={clearSearch}
              itemRenderer={() => <></>}
              query={searchValue}
              onQueryChange={onQueryChange}
              onItemSelect={onItemSelect}
              handleSetSearchType={handleSetSearchType}
              inputValueRenderer={() => searchValue}
              placeholder={placeholder}
              inputProps={{ leftIcon: <Svg src={SearchIcon} /> }}
            />
            <Tooltip content={t('refreshInBox')}>
              <Button
                outlined
                iconOnly
                loading={loadingSyncMails}
                disabled={disableSyncMail}
                icon={<Svg src={RefreshIcon} />}
                className="sl-actions-bar__button sl-actions-bar__button--refresh"
                onClick={() => syncInboxMails()}
              />
            </Tooltip>
            {mailboxType === MailboxType.Kim && (
              <Tooltip content={t('newEmail')}>
                <Button
                  iconOnly
                  intent={Intent.PRIMARY}
                  icon={<Svg src={MailAddIcon} />}
                  className="sl-actions-bar__button sl-actions-bar__button--add"
                  onClick={onAddNewMail}
                />
              </Tooltip>
            )}
          </Flex>
          {selectedTabId === TabIds.Inbox && (
            <Flex>
              <Checkbox
                className="sl-isAutoSendMDN"
                checked={isAutoSendMDN}
                label={tActionsBar('autoSendMDN')}
                onChange={() => {
                  setConfirmDelivery(!isAutoSendMDN);
                  setIsAutoSendMDN((prev) => !prev);
                }}
              />
            </Flex>
          )}
        </>
      )}
      {isSelectMode && (
        <div className="select-mode">
          <div className="counting">
            <span>
              {tBulkAction('countItemSelected', { count: countSelected })}
            </span>
            {isSelectAll && (
              <span onClick={handleClearSelectALl}>
                {tBulkAction('unselectAllMails')}
              </span>
            )}
            {!isSelectAll && (
              <span onClick={handleSelectAll}>
                {tBulkAction('selectAllMalls')}
              </span>
            )}
          </div>
          <div className={'actions'}>
            {someUnread && (
              <Button
                icon={<Svg src={MailReadIcon} />}
                onClick={handleMarkAsRead}
              >
                {t('markAsRead')}
              </Button>
            )}
            {!someUnread && (
              <Button
                icon={<Svg src={MailNew2Icon} />}
                onClick={handleMarkAsUnread}
              >
                {t('markAsUnread')}
              </Button>
            )}
            {isInboxTab && (
              <ConfirmDialog countSelected={countSelected}>
                <Button
                  icon={<Svg src={ArchiveIcon} />}
                  onClick={handleArchivedEmail}
                >
                  {t('archive')}
                </Button>
              </ConfirmDialog>
            )}
            {isArchivedTab && (
              <ConfirmDialog countSelected={countSelected}>
                <Button
                  icon={<Svg src={ArchiveIcon} />}
                  onClick={handleUnarchiveMail}
                >
                  {t('moveToInbox')}
                </Button>
              </ConfirmDialog>
            )}
          </div>
        </div>
      )}
    </Flex>
  );
};

export default ActionsBar;
