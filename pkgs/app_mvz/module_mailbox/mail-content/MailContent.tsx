import React, { FC } from 'react';

import { BodyTextM } from '@tutum/design-system/components';

export enum ContentType {
  textPlain = 'text/plain',
  texHtml = 'text/html',
}

export enum MailContentType {
  kv1ClickBillibg = 'kv1ClickBillibg',
}

export interface IMailContentProps {
  className?: string;
  content: string;
  contentType: string;
  onlyPlainText?: boolean;
}

const MailContent: FC<IMailContentProps> = (props) => {
  const { className, content, contentType, onlyPlainText } = props;

  const extractTextFromString = (content: string, type: MailContentType) => {
    let text = '';
    const html = new DOMParser().parseFromString(content, 'text/html');

    switch (type) {
      case MailContentType.kv1ClickBillibg:
        text = html.querySelector('h1')?.innerText!;
        break;
      default:
        break;
    }
    return text;
  };

  const stringifyContent = (content): string => {
    if (contentType === ContentType.texHtml) {
      const strContent = Buffer.from(content, 'base64').toString();
      if (onlyPlainText) {
        return extractTextFromString(
          strContent,
          MailContentType.kv1ClickBillibg
        );
      }
      return strContent;
    }

    return content;
  };

  return (
    <div className={className}>
      {contentType === ContentType.texHtml ? (
        <BodyTextM
          dangerouslySetInnerHTML={{ __html: stringifyContent(content) }}
        />
      ) : (
        <pre>{stringifyContent(content)}</pre>
      )}
    </div>
  );
};

export default MailContent;
