import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';

import GlobalContext from '@tutum/mvz/contexts/Global.context';
import {
  mailboxActions,
  useMailboxStore,
  mailboxStore,
} from '@tutum/mvz/module_mailbox/Mailbox.store';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import {
  TabIds,
  MailboxType,
  CardErrorsType,
} from '@tutum/mvz/module_mailbox/Mailbox.type';
import {
  StateAssignPatient,
  mapErrorCard,
} from '@tutum/mvz/module_mailbox/Mailbox.helper';
import {
  useQueryGetById,
  saveSetting,
  useMutationCheckAutomaticallyAssignPatient,
  assignPatient,
} from '@tutum/hermes/bff/legacy/app_mvz_eab';
import {
  EventMDNStatusChanged,
  useListenMDNStatusChanged,
} from '@tutum/hermes/bff/app_mvz_eab';
import {
  alertError,
  alertSuccessfully,
  alertWarning,
  BodyTextM,
  Flex,
  H3,
  LoadingState,
} from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';
import I18n from '@tutum/infrastructure/i18n';
import {
  useListenNewMail,
  EventNewMail,
  MailType,
} from '@tutum/hermes/bff/app_mvz_mail';
import PatientOrScheinDialog from '../patient-schein-dialog';
import { PatientComparison } from '../patient-comparison';
import { usePatientComparisonStore } from '../patient-comparison/PatientComparison.store';
import { AutoDocumentDialog } from '@tutum/mvz/components/e-doctor-letter-components';
import { AutomaticDocumentingCase } from '@tutum/hermes/bff/eab_common';
import { MDNStatus, Patient } from '@tutum/hermes/bff/mail_common';
import useToaster from '@tutum/mvz/hooks/useToaster';
import ViewMailModal from '../view-mail-modal/ViewMailModal.styled';
import { ActionsBar } from '../actions-bar';
import { EmptyState } from '../empty-state';
import { MailComposer } from '../mail-composer';
import { MailList } from '../mail-list';
import { MailboxHeader } from '../mailbox-header';
import { MailBanner } from '../mail-banner';
import { MailDetailWrapper } from '../mail-detail-wrapper';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';

export interface IKimCommunicationProps {
  className?: string;
}

const KimCommunication: FC<IKimCommunicationProps> = (props) => {
  const { className } = props;

  const { t } = I18n.useTranslation<keyof typeof MailboxI18n.EmptyState>({
    namespace: 'Mailbox',
    nestedTrans: 'EmptyState',
  });

  const { t: tMailBox } = I18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
  });

  const toaster = useToaster();

  const {
    selectedAccount,
    selectedMail,
    selectedTabId,
    cardError,
    composerArea,
    loading,
    currentThread,
    eabItem,
    isOpenPatientOrSchein,
    mailboxType,
    patientManagement,
    currentStateAssign,
    stageDialogScheinOrPatient,
    settingConsentData,
  } = useMailboxStore();

  const { setActiveTab } = mailboxActions;

  const {
    getAccounts,
    reloadList,
    setNotification,
    syncInboxMails,
    setDialogPatientOrSchein,
    setScheinIsExist,
    clearInformationSchein,
    setSelectedMail,
    setViewMail,
  } = mailboxActions;

  const { data: itemEABResponse, isSuccess } = useQueryGetById(
    {
      id: eabItem ? eabItem.id! : '',
    },
    {
      enabled: Boolean(eabItem) && Boolean(eabItem?.id),
    }
  );

  const { globalData } = GlobalContext.useContext();

  const settingStore = useSettingStore();

  const submitSendmail = useRef<(() => Promise<void>) | null>(null);

  const router = useRouter();
  const { tab, mailId } = router.query;

  const { mutateAsync: mutateAutomaticallyAssignPatient } =
    useMutationCheckAutomaticallyAssignPatient({
      onSuccess: (data) => {
        if (!data.data) {
          const selectedMailUpdate = {
            ...mailboxStore.selectedMail!,
            emailItem: {
              ...mailboxStore.selectedMail?.emailItem!,
              patient: undefined,
            },
          };
          setSelectedMail(selectedMailUpdate);
          setScheinIsExist({
            isExist: false,
            state: StateAssignPatient.default,
          });
          alertWarning(
            <BodyTextM color={COLOR.TEXT_WHITE}>
              {tMailBox('canNotAssign')}
            </BodyTextM>,
            { toaster }
          );
          return;
        }
        const { patientInfo, isExistSchein } = data.data;
        const patient = {
          id: patientInfo?.patientId!,
          lastName: patientInfo?.personalInfo.lastName!,
          fistName: patientInfo?.personalInfo.firstName!,
        };
        const selectedMailUpdate = {
          ...mailboxStore.selectedMail!,
          emailItem: {
            ...mailboxStore.selectedMail?.emailItem!,
            patient: patient,
          },
        };
        setSelectedMail(selectedMailUpdate);
        alertSuccessfully(
          tMailBox('assignSuccess', {
            patient: `${patient.lastName}, ${patient.fistName}`,
          }),
          { toaster }
        );

        setScheinIsExist({
          isExist: isExistSchein,
          state: StateAssignPatient.auto,
        });
      },
      onError: () => {
        const selectedMailUpdate = {
          ...mailboxStore.selectedMail!,
          emailItem: {
            ...mailboxStore.selectedMail?.emailItem!,
            patient: undefined,
          },
        };
        setSelectedMail(selectedMailUpdate);
        setScheinIsExist({
          isExist: false,
          state: StateAssignPatient.default,
        });
        alertError(tMailBox('assignFailure'), { toaster });
      },
    });

  const selectedTabData = mailboxStore[selectedTabId];

  const { patientComparisonStore, patientComparisonActions } =
    usePatientComparisonStore();
  const { openComparison, patientCompareData } = patientComparisonStore;
  const { setOpenComparison, resetStore: patientCompareResetStore } =
    patientComparisonActions;

  const [isLoadingConsent, setIsLoadingConsent] = useState<boolean>(false);

  useEffect(() => {
    if (isSuccess) {
      mailboxActions.setEABItem(itemEABResponse.data);
      mailboxActions.setComposer('lg');
    }
  }, [itemEABResponse]);

  useEffect(() => {
    if (globalData.userProfile?.bsnr) {
      getAccounts({ bsnrCodes: [globalData.userProfile.bsnr] });
    }
  }, [globalData.userProfile?.bsnr]);

  useEffect(() => {
    if (selectedAccount.email) {
      syncInboxMails(false);
    }
    if (selectedAccount.email && settingStore) {
      const { cardTerminates } = settingStore;
      if (cardTerminates.length > 0) {
        const cards = cardTerminates.map((terminate) => terminate.cards).flat();
        const cardAccount = cards.find(
          (card) => card?.iccsn === selectedAccount?.assignCardId
        );
        if (cardAccount) {
          const errorCard = mapErrorCard(cardAccount);
          mailboxActions.setCardError(errorCard!);
        }
      }
    }
  }, [selectedAccount, settingStore.cardTerminates]);

  useEffect(() => {
    return () => {
      mailboxActions.resetStore();
      patientCompareResetStore();
    };
  }, []);

  useListenNewMail((data: EventNewMail) => {
    if (
      data.mailType === MailType.KIMMail &&
      data.mails.includes(selectedAccount.email)
    ) {
      setNotification(true);
      reloadList(TabIds.Inbox);
    }
  });

  useListenMDNStatusChanged((data: EventMDNStatusChanged) => {
    if (data.mdnStatus === MDNStatus.MDNStatus_Sent) {
      setNotification(true);
      reloadList(TabIds.Inbox);
    }
  });

  const onClosePatientOrScheinDialog = () => {
    mailboxActions.setStageDialog({ stage: 'default', context: undefined });
    clearInformationSchein();
    setDialogPatientOrSchein(false);
  };

  const onPreparePatientDataAfterCreate = async (patientId: string) => {
    await onAssignPatient(patientId);
  };

  const onAssignPatient = async (patientId: string) => {
    try {
      const res = await assignPatient({
        patientId: patientId,
        mailId: selectedMail?.id!,
      });
      const selectedMailUpdated = {
        ...selectedMail!,
        emailItem: {
          ...selectedMail?.emailItem!,
          patient: {
            id: patientId,
            lastName: res.data.patientInfo?.personalInfo.lastName!,
            fistName: res.data.patientInfo?.personalInfo.firstName!,
          },
        },
      };
      mailboxActions.setSelectedMail(selectedMailUpdated);
      alertSuccessfully(
        tMailBox('assignManuallySuccess', {
          patient: `${res.data.patientInfo?.personalInfo.lastName}, ${res.data.patientInfo?.personalInfo.firstName}`,
        }),
        { toaster }
      );
    } catch (error) {
      alertError(tMailBox('assignFailure'), { toaster });
    }
  };

  const submitSendEabMail = (func: () => Promise<void>) => {
    submitSendmail.current = func;
  };

  const onReject = async () => {
    setIsLoadingConsent(true);
    const payload =
      settingConsentData.type === AutomaticDocumentingCase.Sending
        ? {
          isAutoSending: false,
          isAutoReceiving: !!settingConsentData.settingData?.isAutoReceiving,
        }
        : {
          isAutoSending: !!settingConsentData.settingData?.isAutoSending,
          isAutoReceiving: false,
        };
    await saveSetting(payload);
    if (settingConsentData.type === AutomaticDocumentingCase.Sending) {
      await submitSendmail?.current?.();
    } else {
      await mutateAutomaticallyAssignPatient({
        mailId: selectedMail?.id!,
      });
    }
    setIsLoadingConsent(false);
    mailboxActions.setSettingConsentData(
      null!,
      AutomaticDocumentingCase.Sending
    );
  };

  const onAllow = async () => {
    setIsLoadingConsent(true);
    const payload =
      settingConsentData.type === AutomaticDocumentingCase.Sending
        ? {
          isAutoSending: true,
          isAutoReceiving: !!settingConsentData.settingData?.isAutoReceiving,
        }
        : {
          isAutoSending: !!settingConsentData.settingData?.isAutoSending,
          isAutoReceiving: true,
        };
    await saveSetting(payload);
    if (settingConsentData.type === AutomaticDocumentingCase.Sending) {
      await submitSendmail?.current?.();
    } else {
      await mutateAutomaticallyAssignPatient({
        mailId: selectedMail?.id!,
      });
    }
    setIsLoadingConsent(false);
    mailboxActions.setSettingConsentData(
      null!,
      AutomaticDocumentingCase.Sending
    );
  };

  const mainArea = useCallback(() => {
    if (composerArea.size === 'lg') {
      return (
        <MailComposer
          cardError={cardError}
          getSubmitManual={(payload) => submitSendEabMail(payload)}
        />
      );
    }

    if (selectedMail) {
      return (
        <MailDetailWrapper
          className="mailDetailsWrapper"
          {...selectedMail}
          thread={currentThread}
        />
      );
    }

    return (
      <Flex className="sl-mailbox__empty-state" justify="center">
        <H3>{t('noConversationSelected')}</H3>
      </Flex>
    );
  }, [selectedMail, currentThread, composerArea, cardError]);

  useEffect(() => {
    if (!tab || !mailId) return;
    setActiveTab(tab as TabIds);
  }, [mailId, tab]);

  useEffect(() => {
    const { mails } = selectedTabData;
    if (!mails || !mails.length || !mailId || !tab) return;
    const selectedMail = mails.find((mail) => mail.id === mailId)!;
    setViewMail(mailId as string, [selectedMail]);
  }, [selectedTabData, mailId, tab]);
  return (
    <>
      <div className={className} onClick={() => setNotification(false)}>
        {loading ? (
          <LoadingState />
        ) : (
          <>
            {!!cardError && cardError !== CardErrorsType.CardValid && (
              <MailBanner cardError={cardError} t={tMailBox} />
            )}
            <MailboxHeader />
            {selectedAccount.email ? (
              <Flex
                className={`sl-mailbox ${cardError && 'sl-mailbox-card-error'
                  } ${selectedTabId === TabIds.Archived ? 'archive' : ''}`}
              >
                <div className="sl-mailbox__left">
                  <ActionsBar />
                  <MailList {...selectedTabData} mailboxType={mailboxType} />
                </div>
                <div className="sl-mailbox__right">{mainArea()}</div>
              </Flex>
            ) : (
              <EmptyState />
            )}
            <ViewMailModal mailboxType={MailboxType.Kim} />
            <PatientOrScheinDialog
              onClose={onClosePatientOrScheinDialog}
              isOpen={isOpenPatientOrSchein}
              patientManagement={patientManagement!}
              onUpdateMailAfterCreateSchein={() => {
                setScheinIsExist({
                  isExist: true,
                  state: StateAssignPatient.default,
                });
              }}
              onAssignPatientAfterCreate={onPreparePatientDataAfterCreate}
              stageDialog={stageDialogScheinOrPatient}
            />
          </>
        )}
      </div>
      {openComparison && (
        <PatientComparison
          isExistSchein={currentStateAssign.isExistSchein}
          isOpen={openComparison}
          onClose={() => setOpenComparison(false)}
          onAssignPatient={() =>
            onAssignPatient(patientCompareData?.selectedPatientId!)
          }
        />
      )}

      {settingConsentData.settingData && (
        <AutoDocumentDialog
          onAllow={onAllow}
          onReject={onReject}
          type={settingConsentData.type}
          loading={isLoadingConsent}
        />
      )}
    </>
  );
};

export default KimCommunication;
