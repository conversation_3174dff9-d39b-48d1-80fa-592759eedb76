import React, { FC, useMemo, useRef } from 'react';
import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';

import { MailItemDTO } from '@tutum/hermes/bff/app_mvz_mail';
import { MDNStatus, Patient, SearchType } from '@tutum/hermes/bff/mail_common';

import {
  alertError,
  alertSuccessfully,
  alertWarning,
  BodyTextM,
  BodyTextS,
  Flex,
  H4,
  Svg,
} from '@tutum/design-system/components';
import {
  parseAddressToName,
  emailValidToAutoAssignEAB,
  getIndexSelectedMail,
  StateAssignPatient,
} from '../Mailbox.helper';
import { TabIds } from '../Mailbox.type';
import DateTimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { MDN_STATUS_VALID, isEABMail } from '../Mailbox.helper';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '@tutum/infrastructure/shared/date-format';
import i18n from '@tutum/infrastructure/i18n';
import { getPositionsToHighlight } from '@tutum/infrastructure/utils/match';
import { mailboxActions, useMailboxStore } from '../Mailbox.store';
import { BillingMailHeader } from '@tutum/hermes/bff/legacy/billing_history_common';
import { Checkbox, Tooltip } from '@tutum/design-system/components/Core';
import { MailStatus } from '@tutum/hermes/bff/legacy/app_mvz_mail';
import {
  MailCategory,
  MailHeaderKey,
} from '@tutum/hermes/bff/legacy/mail_common';
import {
  useMutationCheckAutomaticallyAssignPatient,
  getConsentDocumenting,
  useMutationGetProposalPatients,
} from '@tutum/hermes/bff/legacy/app_mvz_eab';
import { AutomaticDocumentingCase } from '@tutum/hermes/bff/eab_common';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { IPatientSearchResult } from '@tutum/mvz/module_patient-management/patient-search/PatientSearch.type';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MailContent } from '../mail-content';
import { MailTag } from '../mail-tag';

const AttachmentIcon = '/images/attachment.svg';
const MailAlertIcon = '/images/mail-alert.svg';
const MailApprovedIcon = '/images/mail-approved.svg';

export interface IMailItemProps extends MailItemDTO {
  className?: string;
  isOutbox: boolean;
  thread?: MailItemDTO[];
}

const MailItem: FC<IMailItemProps> = (props) => {
  const { className, emailItem, id, date, isOutbox, thread } = props;
  const {
    subject,
    body,
    from,
    to,
    category,
    patient,
    contentType,
    attachments,
    received,
    mailHeader,
  } = emailItem;
  const { name, address } = from;
  const isRead = (thread || []).every((mail) => mail.emailItem.isRead);

  const mailboxStore = useMailboxStore();
  const toaster = useToaster();

  const { t } = i18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
  });
  const inputRef = useRef<HTMLInputElement | null>(null);
  const { selectedMailId, selectedTabId } = mailboxStore;

  const {
    setViewMail,
    setSelectedIds,
    selectAllMails,
    changeMailsStatus,
    reloadList,
    setSelectedMail,
    setScheinIsExist,
    setSelectedProposalPatients,
    setSelectedProposalTextSearch,
  } = mailboxActions;

  const { mutate: mutateAutomaticallyAssignPatient } =
    useMutationCheckAutomaticallyAssignPatient({
      onSuccess: (data) => {
        if (!data.data) {
          const selectedMailUpdate = {
            ...mailboxStore.selectedMail!,
            emailItem: {
              ...mailboxStore.selectedMail?.emailItem!,
              patient: undefined,
            },
          };
          setSelectedMail(selectedMailUpdate);
          setScheinIsExist({
            isExist: false,
            state: StateAssignPatient.default,
          });
          alertWarning(
            <BodyTextM color={COLOR.TEXT_WHITE} fontSize={13}>
              {t('canNotAssign')}
            </BodyTextM>,
            { toaster }
          );
          return;
        }
        const { patientInfo, isExistSchein } = data.data;
        const patient = {
          id: patientInfo?.patientId!,
          lastName: patientInfo?.personalInfo.lastName!,
          fistName: patientInfo?.personalInfo.firstName!,
        };
        const selectedMailUpdate = {
          ...mailboxStore.selectedMail!,
          emailItem: {
            ...mailboxStore.selectedMail?.emailItem!,
            patient: patient,
          },
        };
        setSelectedMail(selectedMailUpdate);
        alertSuccessfully(
          t('assignSuccess', {
            patient: `${patient.lastName}, ${patient.fistName}`,
          }),
          { toaster }
        );
        setScheinIsExist({
          isExist: isExistSchein,
          state: StateAssignPatient.auto,
        });
      },
      onError: (error) => {
        const selectedMailUpdate = {
          ...mailboxStore.selectedMail!,
          emailItem: {
            ...mailboxStore.selectedMail?.emailItem!,
            patient: undefined,
          },
        };
        setSelectedMail(selectedMailUpdate);
        setScheinIsExist({
          isExist: false,
          state: StateAssignPatient.default,
        });
        alertError(
          t('assignFailure', {
            error: error?.response?.data?.message,
          }),
          { toaster }
        );
        return;
      },
    });

  const { mutate: mutateGetProposalPatients } = useMutationGetProposalPatients({
    onSuccess: (data) => {
      if (!data.data) {
        return;
      }
      const listProposalPatients: IPatientSearchResult[] =
        data.data.proposalPatients?.map((p) => {
          return p as IPatientSearchResult;
        });
      setSelectedProposalPatients(listProposalPatients);
      setSelectedProposalTextSearch(data.data.proposalTextSearch);
    },
  });

  const msgType =
    mailHeader?.[MailHeaderKey.MailHeaderKey_ServiceIdentifer]?.split(';');
  const isProfessionalFeefback = msgType && msgType[1] == 'Rueckmeldung';
  const isNotAssigable =
    mailHeader?.[BillingMailHeader.BillingMailHeader_NotAssignable];

  const handleClick = async () => {
    setViewMail(id, thread);
    setSelectedProposalPatients([]);
    setSelectedProposalTextSearch('');
    const { mails } = mailboxStore[selectedTabId];
    const indexSelectedMail = getIndexSelectedMail(id, mails);
    const emailSelected = mails[indexSelectedMail];

    if (
      emailValidToAutoAssignEAB(emailSelected) &&
      selectedTabId === TabIds.Inbox
    ) {
      const resConsent = await getConsentDocumenting({
        automaticDocumentingCase: AutomaticDocumentingCase.Receiving,
      });
      if (
        resConsent.data.setting &&
        !resConsent.data.setting.isConsentTriggerForReceiving
      ) {
        mailboxActions.setSettingConsentData(
          resConsent.data.setting,
          AutomaticDocumentingCase.Receiving
        );
      } else {
        mutateAutomaticallyAssignPatient({
          mailId: emailSelected.id,
        });
      }
    }

    if (
      emailSelected?.emailItem?.isRead &&
      selectedTabId === TabIds.Inbox &&
      isEABMail(emailSelected?.emailItem)
    ) {
      const stateAssign =
        !!emailSelected?.emailItem?.patient?.id &&
          !!emailSelected?.emailItem?.patient?.lastName
          ? StateAssignPatient.auto
          : StateAssignPatient.default;
      mailboxActions.checkScheinIsExist(
        emailSelected?.emailItem?.patient?.id!,
        stateAssign
      );
    }

    if (!isRead) {
      changeMailsStatus({
        ids: thread?.map((mail) => mail.id) || [id],
        status: MailStatus.Read,
      }).then(() => {
        reloadList(selectedTabId);
      });
    }

    // get proposal patients
    if (selectedTabId === TabIds.Inbox && isEABMail(emailSelected?.emailItem)) {
      mutateGetProposalPatients({
        mailId: emailSelected.id,
      });
    }
  };

  const renderHighlightTextSearching = (text: string) => {
    const { searchValue, searchType } = mailboxStore[selectedTabId].searchInfo;
    if (searchValue && searchType === SearchType.SearchType_Subject) {
      const styleTexts = getPositionsToHighlight(text, searchValue);

      return styleTexts.length
        ? styleTexts.map((item, index) => {
          return item.highlight ? (
            <strong key={index}>{item.value}</strong>
          ) : (
            item.value
          );
        })
        : text;
    }

    return text;
  };

  const renderEABIndicator = () => {
    const identifier = mailHeader?.[MailHeaderKey.MailHeaderKey_ServiceIdentifer];
    if (
      !identifier ||
      ![
        String(MailCategory.MailCategory_EAB),
        'Arztbrief;Eingangsbestaetigung;V1.2',
      ].includes(identifier)
    ) {
      return;
    }

    if (emailItem.statusMDN === MDNStatus.MDNStatus_Pending) {
      return (
        <Tooltip placement="top" content={t('tooltipMDNRequested')}>
          <Svg className="sl-mailbox-item__mailalert" src={MailAlertIcon} />
        </Tooltip>
      );
    } else {
      return (
        <Tooltip placement="top" content={t('tooltipMDNReceived')}>
          <Svg
            className="sl-mailbox-item__mailapproved"
            src={MailApprovedIcon}
          />
        </Tooltip>
      );
    }
  };

  const currentMailbox = mailboxStore[selectedTabId];
  const isSelected =
    currentMailbox.selectedIds.includes(id) || currentMailbox.isSelectAll;

  const classes = useMemo<string>(() => {
    const classesArr = [className];

    if (selectedMailId === id || isSelected) {
      classesArr.push('sl-mailbox-item__active');
    }

    if (isRead) {
      classesArr.push('sl-mailbox-item__read');
    }

    return classesArr.join(' ');
  }, [isRead, selectedMailId, id, isSelected]);

  const handleSelect = (e) => {
    e.stopPropagation();
    const checked = !inputRef?.current?.checked;
    setSelectedIds(
      selectedTabId,
      (thread || []).map((mail) => mail.id!),
      checked
    );
    if (!checked) selectAllMails(selectedTabId, false);
  };

  const onCreatePatient = () => { };

  const noResults = (): React.ReactNode => {
    return (
      <Flex justify="center" align="center" gap={4} p="8px 16px">
        <BodyTextM className="text-no-result">{t('noResultFound')}</BodyTextM>
        <BodyTextM
          className="btn-link-create-patient"
          onClick={onCreatePatient}
        >
          {t('createPatient')}
        </BodyTextM>
      </Flex>
    );
  };

  return (
    <div className={classes} onClick={handleClick}>
      <Flex align="center" justify="space-between">
        <Flex className="sl-mailbox-item__name" align="center" mb={4}>
          {!isRead && selectedTabId === TabIds.Inbox && (
            <div className="sl-mailbox-item__unread-dot" />
          )}
          <H4 fontWeight="SemiBold">
            {`${isOutbox
              ? `${t('To')}: ${parseAddressToName(to, t).join(', ')}`
              : name || address
              }`}
          </H4>
        </Flex>
        <Flex align="center" gap={5}>
          <BodyTextS className="sl-mailbox-item__created-at" as={'p'}>
            {DateTimeUtil.dateTimeFormat(
              received || date,
              DATE_TIME_WITHOUT_SECONDS_FORMAT
            )}
          </BodyTextS>
          {isEABMail(emailItem) &&
            MDN_STATUS_VALID.includes(emailItem?.statusMDN) &&
            renderEABIndicator()}
          {!!attachments?.length && (
            <Tooltip content={t('attachment')}>
              <Svg
                className="sl-mailbox-item__attachment"
                src={AttachmentIcon}
              />
            </Tooltip>
          )}

          {/*Cheat to prevent open mail when checkbox checked*/}
          <div className="sl-mailbox-item__checkbox" onClick={handleSelect}>
            <Checkbox inputRef={inputRef} checked={isSelected} />
          </div>
        </Flex>
      </Flex>
      <BodyTextM>{renderHighlightTextSearching(subject)}</BodyTextM>
      <MailContent
        className="sl-mailbox-item__content"
        content={body}
        contentType={contentType}
        onlyPlainText={true}
      />
      {(category || patient) && (
        <Flex className="sl-mailbox-item__tag">
          {category && <MailTag t={t} value={category} type="category" />}
          {patient && (
            <MailTag
              t={t}
              type="patient"
              noResults={noResults()}
              isViewOnly
              patient={patient}
            />
          )}
          {isNotAssigable && (
            <MailTag
              t={t}
              value={t('notAssignable')}
              type="notAssignable"
              isProfessionalFeefback={isProfessionalFeefback}
            />
          )}
        </Flex>
      )}
    </div>
  );
};

export default MailItem;
