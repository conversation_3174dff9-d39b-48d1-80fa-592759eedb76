import React, { FC } from 'react';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MailItemDTO } from '@tutum/hermes/bff/app_mvz_mail';
import { Attachment } from '@tutum/mvz/module_mailbox/attachment';
import { isMDNRequested } from '@tutum/mvz/module_mailbox/Mailbox.helper';
import {
  BodyTextL,
  BodyTextM,
  BodyTextS,
  Flex,
  LoadingState,
  Svg,
  Tooltip,
  H4,
  H1,
  Intent,
  Box,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  formatDateTime,
  getRecipientAddresses,
  handleDownloadFile,
  parseAddressToName,
} from '../Mailbox.helper';
import { mailboxActions, useMailboxStore } from '../Mailbox.store';
import Button from '@tutum/design-system/components/Button/Button';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { Callout } from '@tutum/design-system/components/Core';
import { BillingMailHeader } from '@tutum/hermes/bff/billing_history_common';
import { EmailItem, MailCategory } from '@tutum/hermes/bff/legacy/mail_common';
import { MailContent } from '../mail-content';
import { MailTag } from '../mail-tag';

export interface MailThreadProps {
  t: IFixedNamespaceTFunction<keyof typeof MailboxI18n>;
  thread: MailItemDTO[];
  className?: string;
  isView?: boolean;
}

const MailIcon = '/images/email.svg';
const MailReadIcon = '/images/mail-read.svg';
const ViewSourceIcon = '/images/information-grey-circle.svg';
const AlertCircle = '/images/alert-circle.svg';
const MailAlertIcon = '/images/mail-alert.svg';
const MailApprovedIcon = '/images/mail-approved.svg';

const MailThread: FC<MailThreadProps> = (props) => {
  const { t, thread, className } = props;

  const mailboxStore = useMailboxStore();
  const { loadingMailDetails } = mailboxStore;

  const renderEABIndicator = (emailItem: EmailItem) => {
    const mailHeader = emailItem.mailHeader;

    const identifier = mailHeader?.['X-Kim-Dienstkennung'];
    if (
      !identifier ||
      ![
        String(MailCategory.MailCategory_EAB),
        'Arztbrief;Eingangsbestaetigung;V1.2',
      ].includes(identifier)
    ) {
      return;
    }

    const isRequested = isMDNRequested(emailItem);
    if (isRequested) {
      return (
        <Tooltip placement="top" content={t('tooltipMDNRequested')}>
          <Svg className="sl-mail-details__body--icon" src={MailAlertIcon} />
        </Tooltip>
      );
    } else {
      return (
        <Tooltip placement="top" content={t('tooltipMDNReceived')}>
          <Svg className="sl-mail-details__body--icon" src={MailApprovedIcon} />
        </Tooltip>
      );
    }
  };

  return thread.map((mail) => {
    const {
      from,
      body,
      subject,
      contentType,
      category,
      attachments,
      patient,
      isRead,
      received,
      mailHeader,
    } = mail.emailItem || {};

    const sender = parseAddressToName([from], t);
    const recipients = parseAddressToName(
      getRecipientAddresses(mail.emailItem),
      t,
      mailboxStore.selectedAccount.email
    );

    const isNotAssigable =
      mailHeader?.[BillingMailHeader.BillingMailHeader_NotAssignable];
    const msgType = mailHeader?.['X-Kim-Dienstkennung']?.split(';');
    const isProfessionalFeefback = msgType && msgType[1] == 'Rueckmeldung';
    const headerSender = mailHeader?.[BillingMailHeader.BillingMailHeader_From];
    const messageId = mailHeader?.[BillingMailHeader.BillingMailHeader_MessageId];
    const dispatchedOn =
      mailHeader?.[BillingMailHeader.BillingMailHeader_DispatchedOn];
    const mailGuid = mailHeader?.[BillingMailHeader.BillingMailHeader_GUID];

    const onMarkAsReadOrUnread = () => {
      mailboxActions.markAsReadOrUnread({
        id: mail.id,
        isRead: !isRead,
        inbox: true,
      });
      alertSuccessfully(
        isRead ? t('markAsUnreadSuccessfully') : t('markAsReadSuccessfully')
      );
    };
    const noResults = (): React.ReactNode => {
      return (
        <Flex justify="center" align="center" gap={4} p="8px 16px">
          <BodyTextM className="text-no-result">{t('noResultFound')}</BodyTextM>
          <BodyTextM className="btn-link-create-patient">
            {t('createPatient')}
          </BodyTextM>
        </Flex>
      );
    };

    return (
      <Box className={className} key={mail.id}>
        <Flex
          className="sl-mail-details__header"
          justify="space-between"
          align="flex-start"
          column
        >
          <Flex className="sl-mail-subject">
            <H1>{subject}</H1>
            <Flex align="center" className="sl-mail-details__header--tags">
              {category && <MailTag t={t} value={category} type="category" />}
              &nbsp;
              <MailTag
                t={t}
                className="sl-mail-details__header--patient"
                editable
                type="patient"
                patient={patient}
                noResults={noResults()}
              />
              <Tooltip
                position="left"
                content={isRead ? t('markAsUnread') : t('markAsUnread')}
                usePortal
              >
                <Button
                  isActionIcon
                  iconOnly
                  minimal
                  icon={<Svg src={isRead ? MailReadIcon : MailIcon} />}
                  onClick={onMarkAsReadOrUnread}
                />
              </Tooltip>
            </Flex>
          </Flex>
          <br />
          {isNotAssigable && (
            <Flex w="100%">
              <Callout
                intent={Intent.WARNING}
                icon={<></>}
                className="sl-mail-details__warning-message"
              >
                <Flex>
                  <Svg className="sl-callout-icon" src={AlertCircle} />
                  <BodyTextM
                    className="sl-mail-details__warning-message--title"
                    fontWeight="SemiBold"
                    color={COLOR.TEXT_WARNING}
                  >
                    {isProfessionalFeefback
                      ? t('tooltipProNotAssignableLine1')
                      : t('tooltipTechNotAssignableLine1') +
                      ' ' +
                      t('tooltipNotAssignableLine2')}
                  </BodyTextM>
                </Flex>
                <Box ml="20px" style={{ color: COLOR.TEXT_PRIMARY_BLACK }}>
                  <Flex ml="5px">
                    {t('headerSender')}
                    <BodyTextM className="sl-mail-details__warning-message--detail">
                      {headerSender}
                    </BodyTextM>
                  </Flex>
                  <Flex ml="5px">
                    {t('dispatchedOn')}
                    <BodyTextM className="sl-mail-details__warning-message--detail">
                      {dispatchedOn}
                    </BodyTextM>
                  </Flex>
                  <Flex ml="5px">
                    {t('messageId')}
                    <BodyTextM className="sl-mail-details__warning-message--detail">
                      {messageId}
                    </BodyTextM>
                  </Flex>
                  {isProfessionalFeefback && (
                    <Flex ml="5px">
                      {t('guid')}
                      <BodyTextM className="sl-mail-details__warning-message--detail">
                        {mailGuid}
                      </BodyTextM>
                    </Flex>
                  )}
                </Box>
              </Callout>
            </Flex>
          )}
        </Flex>
        <div key={mail.id} className="sl-mail-details__body">
          <div className="sl-mail-details__body--info">
            <Flex justify="space-between">
              <Flex column>
                <H4
                  className="sl-mail-details__body--name"
                  fontWeight="SemiBold"
                >
                  {sender}
                </H4>
                <BodyTextL>{`(${from.address})`}</BodyTextL>
              </Flex>
              <Flex className="sl-mail-details__body--actions" align="center">
                <BodyTextS className="sl-mail-details__body--date">
                  {formatDateTime(received || mail.date)}
                </BodyTextS>
                {renderEABIndicator(mail.emailItem)}
                <Flex className="sl-mail-details__body--infos" align="center">
                  <Tooltip content={t('viewMessageSource')} usePortal>
                    <Button
                      isActionIcon
                      iconOnly
                      minimal
                      icon={<Svg src={ViewSourceIcon} />}
                      onClick={() => {
                        mailboxActions.setViewMailData(mail.emailItem);
                        mailboxActions.setOpenModalMessageSource(true);
                      }}
                    />
                  </Tooltip>
                </Flex>
              </Flex>
            </Flex>
            <Flex align="flex-start">
              {recipients.length > 1 ? (
                <>
                  <BodyTextM>{`${recipients[0]}`}</BodyTextM>
                  <Tooltip content={recipients.slice(1).join(', ')}>
                    <BodyTextM>{`, ${t('someoneElse')}`}</BodyTextM>
                  </Tooltip>
                </>
              ) : (
                <BodyTextM>{`${recipients[0]}`}</BodyTextM>
              )}
            </Flex>
          </div>
          {loadingMailDetails ? (
            <LoadingState />
          ) : (
            <div>
              <MailContent
                className="sl-mail-details__body--content"
                content={body}
                contentType={contentType}
              />
              {!!attachments?.length && (
                <Flex className="sl-mail-details__body--attachments" gap={6}>
                  {attachments.map((attachment, index) => (
                    <Attachment
                      key={`${attachment.name}-${index}`}
                      attachment={attachment}
                      action="download"
                      onClickAttachment={() => handleDownloadFile(attachment)}
                    />
                  ))}
                </Flex>
              )}
            </div>
          )}
        </div>
      </Box>
    );
  });
};

export default MailThread;
