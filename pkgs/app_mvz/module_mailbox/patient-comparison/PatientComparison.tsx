import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import I18n from '@tutum/infrastructure/i18n';
import { COLOR } from '@tutum/design-system/themes/styles';
import { Modal, ModalSize } from '@tutum/design-system/components/Modal';
import { Classes, Divider, Radio } from '@tutum/design-system/components/Core';
import {
  BodyTextM,
  BodyTextS,
  Box,
  Button,
  Flex,
  MessageBar,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import { IKNumberNotFoundData } from '@tutum/mvz/components/navigation-bar/IKNumberNotFound/IKNumberNotFound';
import IKNumberNotFound from '@tutum/mvz/components/navigation-bar/IKNumberNotFound';
import { customStyles, headerStyles } from './PatientComparison.styled';
import { ROUTING } from '@tutum/mvz/types/route.type';
import {
  usePatientComparisonStore,
  patientComparisonActions,
} from './PatientComparison.store';
import {
  ObjectRowData,
  ITableComparation,
  PERSONAL_FIELDS,
  ADDRESS_INFO,
  INSURANCE_INFO,
  POST_BOX,
  transform,
  initDataObjectChecked,
  normalizePayloadUpdateInsurance,
  normalizeDataWithSdkt,
  RowType,
  ITableComparationData,
} from './PatientComparison.helper';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { checkExistInsuranceByIKNumber } from '@tutum/hermes/bff/legacy/app_mvz_eab';
import { useMutationUpdatePatientProfileV2 } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  InsuranceStatus,
  PatientInfo,
} from '@tutum/hermes/bff/patient_profile_common';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { SdktCatalogItem } from '@tutum/hermes/bff/app_mvz_catalog_sdkt';
import { mailboxActions } from '../Mailbox.store';

export interface IPatientComparisonProps {
  className?: string;
  isExistSchein: boolean;
  isOpen?: boolean;
  onClose: () => void;
  onSkip?: () => void;
  onAssignPatient: () => Promise<void>;
}

const stylingHeader = {
  width: '100%',
  backgroundColor: COLOR.BACKGROUND_SECONDARY_SHINE,
  fontWeight: 600,
  color: COLOR.TEXT_SECONDARY_NAVAL,
  border: 0,
};

const stylingTitle = {
  width: '100%',
  fontWeight: 600,
  color: COLOR.TEXT_SECONDARY_NAVAL,
  border: 0,
};

const PatientComparison = (props: IPatientComparisonProps) => {
  const { isExistSchein, className, onClose, onAssignPatient } = props;
  const { t } = I18n.useTranslation<keyof typeof MailboxI18n.PatientComparison>(
    {
      namespace: 'Mailbox',
      nestedTrans: 'PatientComparison',
    }
  );
  const { t: t2 } = I18n.useTranslation({
    namespace: 'Schein',
  });

  const { patientComparisonStore } = usePatientComparisonStore();

  const router = useRouter();

  const { patientCompareData: data } = patientComparisonStore;

  const title = `${data?.selectedPatient?.personalInfo.firstName}, ${data?.selectedPatient?.personalInfo.lastName}`;
  const patientId = data?.selectedPatientId!;

  const [personalInfo, setPersonalInfo] = useState<ITableComparation | undefined>(undefined);
  const [addressInfo, setAddressInfo] = useState<ITableComparation | undefined>(undefined);
  const [postOfficeBox, setPostOfficeBox] = useState<ITableComparation | undefined>(undefined);
  const [insuranceInfo, setInsuranceInfo] = useState<ITableComparation | undefined>(undefined);
  const [iKNumberNotFound, setIKNumberNotFound] =
    useState<IKNumberNotFoundData | undefined>(undefined);
  const [objectChecked, setObjectChecked] = useState<
    Record<string, ObjectRowData>
  >(initDataObjectChecked);
  const [isLoadingSync, setIsLoadingSync] = useState<boolean>(false);
  const [objectCompareData, setObjectCompareData] = useState<Array<string>>([]);

  const mapInsuranceStatus = (insuranceStatus: InsuranceStatus) => {
    switch (insuranceStatus) {
      case InsuranceStatus.Familienmitglied:
        return `(${insuranceStatus}) ${t2('generalInfo.familienmitglied')}`;
      case InsuranceStatus.Rentner:
        return `(${insuranceStatus}) ${t2('generalInfo.rentner/in')}`;
      case InsuranceStatus.Mitglied:
        return `(${insuranceStatus}) ${t2('generalInfo.mitglied')}`;
      default:
        return '';
    }
  };

  useEffect(() => {
    const selectedPatientPersonalData = data?.selectedPatient?.personalInfo;
    const xmlPatientPersonalData = data?.xmlPatient?.personalInfo;
    const selectedPatientAddressInfo = data?.selectedPatient?.addressInfo.address;
    const xmlPatientAddressInfo = data?.xmlPatient?.addressInfo.address;
    const selectedPostOfficeBox = data?.selectedPatient?.postOfficeBox;
    const xmlPostOfficeBox = data?.xmlPatient?.postOfficeBox;

    const selectedPatientInsuranceInfo = getActiveInsurance(
      data?.selectedPatient?.insuranceInfos
    );

    const xmlPatientInsuranceInfo = data?.xmlPatient?.insuranceInfos.find(
      (item) => item
    );

    const personalTable: ITableComparation = {
      header: t('personalInfoHeader'),
      data: PERSONAL_FIELDS.map((fieldName) => {
        if (fieldName === 'dOB') {
          return {
            fieldName,
            garrioPro: transform(
              selectedPatientPersonalData?.[fieldName],
              (d) => datetimeUtil.dateTimeNumberFormat(d, DATE_FORMAT) || ''
            ),
            eAB: transform(
              xmlPatientPersonalData?.[fieldName],
              (d) => datetimeUtil.dateTimeNumberFormat(d, DATE_FORMAT) || ''
            ),
          };
        }
        return {
          fieldName,
          garrioPro: transform(selectedPatientPersonalData?.[fieldName], (d) =>
            String(d || '')
          ),
          eAB: transform(xmlPatientPersonalData?.[fieldName], (d) =>
            String(d || '')
          ),
        };
      }),
    };

    setPersonalInfo(personalTable);

    const addressInfo: ITableComparation = {
      header: t('addressInfoHeader'),
      data: ADDRESS_INFO.map((fieldName) => ({
        fieldName,
        garrioPro: transform(selectedPatientAddressInfo?.[fieldName], fieldName),
        eAB: transform(xmlPatientAddressInfo?.[fieldName], fieldName),
      })),
    };
    setAddressInfo(addressInfo);

    const insuranceInfo: ITableComparation = {
      header: t('insuranceInfoHeader'),
      data: INSURANCE_INFO.map((fieldName) => {
        if (fieldName === 'insuranceStatus') {
          return {
            fieldName,
            garrioPro: transform(selectedPatientInsuranceInfo?.[fieldName], (d) =>
              mapInsuranceStatus(d)
            ),
            eAB: transform(xmlPatientInsuranceInfo?.[fieldName], (d) =>
              mapInsuranceStatus(d)
            ),
          };
        }
        if (fieldName === 'insuranceCompanyName') {
          return {
            fieldName,
            garrioPro: transform(
              selectedPatientInsuranceInfo?.[fieldName],
              (d) => `${d}_${selectedPatientInsuranceInfo?.['ikNumber']}`
            ),
            eAB: transform(
              xmlPatientInsuranceInfo?.[fieldName],
              (d) => `${d ? d : ' '}_${xmlPatientInsuranceInfo?.['ikNumber']}`
            ),
          };
        }
        return {
          fieldName,
          garrioPro: transform(selectedPatientInsuranceInfo?.[fieldName], (d) =>
            String(d || '')
          ),
          eAB: transform(xmlPatientInsuranceInfo?.[fieldName], (d) =>
            String(d || '')
          ),
        };
      }),
    };
    setInsuranceInfo(insuranceInfo);

    const dataTable = [
      ...personalTable.data,
      ...addressInfo.data,
      ...insuranceInfo.data,
    ];

    if (!!xmlPostOfficeBox?.officeBox) {
      const postOfficeInfo: ITableComparation = {
        header: t('postOfficeBoxHeader'),
        data: POST_BOX.map((fieldName) => ({
          fieldName,
          garrioPro: transform(selectedPostOfficeBox?.[fieldName], fieldName),
          eAB: transform(xmlPostOfficeBox?.[fieldName], fieldName),
        })),
      };
      setPostOfficeBox(postOfficeInfo);
      dataTable.push(...postOfficeInfo.data);
    }

    dataTable.map((row) => {
      if (row.fieldName === 'insuranceCompanyName') {
        const [_insuranceNameEAB, ikNumberEAB] = row.eAB.split('_');
        const [_insuranceNameGarrio, ikNumberGarrio] = row.garrioPro.split('_');
        if (ikNumberEAB !== ikNumberGarrio) {
          setObjectCompareData((prevItems) => [...prevItems, row.fieldName]);
        }
        return;
      }
      if (row.eAB !== row.garrioPro) {
        setObjectCompareData((prevItems) => [...prevItems, row.fieldName]);
      }
    });
  }, [data]);

  const updateData = (fieldName: string, newData: ObjectRowData) => {
    setObjectChecked((prevData) => ({
      ...prevData,
      [fieldName]: newData,
    }));
  };

  const { mutateAsync: updatePatientProfile } =
    useMutationUpdatePatientProfileV2();

  const goToSchein = () => {
    mailboxActions.getInformationSchein(patientId);
    patientComparisonActions.setOpenComparison(false);
    onAssignPatient().then(() => {
      mailboxActions.setStageDialog({
        stage: 'openCreateSchein',
        context: { patientId },
      });
      mailboxActions.setDialogPatientOrSchein(true);
    });
  };

  const mappingInsuranceSpecial = (row: string) => {
    const [insuranceName, ikNumber] = row.split('_');
    return (
      <Flex column>
        <BodyTextM fontWeight={600}>{insuranceName}</BodyTextM>
        <BodyTextS>{t('ik', { ikNumber })}</BodyTextS>
      </Flex>
    );
  };

  const onClickSelectAll = (row: RowType) => {
    objectCompareData.map((key) => {
      setObjectChecked((prevData) => ({
        ...prevData,
        [key]: {
          value: prevData[key].value,
          checkedEAB: row === RowType.eAb ? true : false,
          checkedGarrioPro: row === RowType.eAb ? false : true,
        },
      }));
    });
  };

  function Header() {
    return (
      <Table
        columns={[
          {
            id: 1,
            name: 'blank',
            width: '196px',
            style: {
              width: '100%',
              backgroundColor: COLOR.BACKGROUND_SECONDARY_SHINE,
            },
          },
          {
            id: 2,
            name: 'garrioPro',
            width: '282px',
            style: {
              ...stylingHeader,
              textTransform: 'uppercase',
            },
            cell: () => {
              return (
                <Flex justify="space-between" align="center" w={'100%'}>
                  <BodyTextM fontWeight={600}>garrio Pro</BodyTextM>
                  {!isExistSchein && (
                    <BodyTextM
                      fontWeight={600}
                      color={COLOR.BACKGROUND_SELECTED_STRONG}
                      className="cursor-pointer"
                      onClick={() => onClickSelectAll(RowType.garrioPro)}
                    >
                      {t('selectAll')}
                    </BodyTextM>
                  )}
                </Flex>
              );
            },
          },
          {
            id: 3,
            name: 'eAB',
            style: {
              ...stylingHeader,
              textTransform: 'uppercase',
            },
            width: '282px',
            cell: () => {
              return (
                <Flex justify="space-between" align="center" w={'100%'}>
                  <BodyTextM fontWeight={600}>
                    {t('eDoctorLetter').toLocaleUpperCase()}
                  </BodyTextM>
                  {!isExistSchein && (
                    <BodyTextM
                      className="cursor-pointer"
                      color={COLOR.BACKGROUND_SELECTED_STRONG}
                      fontWeight={600}
                      onClick={() => onClickSelectAll(RowType.eAb)}
                    >
                      {t('selectAll')}
                    </BodyTextM>
                  )}
                </Flex>
              );
            },
          },
        ]}
        data={[
          {
            blank: '',
            garrioPro: 'garrio Pro',
            eAB: 'EDoctor letter',
          },
        ]}
        customStyles={headerStyles}
        highlightOnHover
        noTableHead
        noHeader
      />
    );
  }

  const isNotExactData = (row: ITableComparationData) => {
    if (row.fieldName === 'insuranceCompanyName') {
      const [_insuranceNameEAB, ikNumberEAB] = row.eAB.split('_');
      const [_insuranceNameGarrio, ikNumberGarrio] = row.garrioPro.split('_');
      return ikNumberEAB != ikNumberGarrio;
    }
    return row.eAB !== row.garrioPro;
  };

  function Content(data: ITableComparation) {
    return (
      <>
        <Table
          className="table-header"
          columns={[
            {
              id: 1,
              name: 'fieldName',
              selector: (row: ITableComparationData) => row.fieldName,
              style: {
                ...stylingTitle,
                textTransform: 'uppercase',
                padding: 0,
              },
            },
          ]}
          data={[
            {
              blank: '',
              fieldName: data.header,
            },
          ]}
          customStyles={customStyles}
          noTableHead
          noHeader
        />
        <Divider className="comparison-divider" />
        <Table
          className="table-body"
          columns={[
            {
              id: 1,
              name: 'fieldName',
              width: '196px',
              cell: (row: ITableComparationData) => {
                const label = t(
                  row.fieldName as keyof typeof MailboxI18n.PatientComparison
                );
                return (
                  <BodyTextM className="comparison-freetext sl-field-name">
                    {label}
                  </BodyTextM>
                );
              },
            },
            {
              id: 2,
              name: 'garrioPro',
              width: '282px',
              cell: (row: ITableComparationData) => {
                return (
                  <Flex
                    justify="space-between"
                    align="center"
                    className="comparison-freetext"
                  >
                    {row.fieldName === 'insuranceCompanyName' ? (
                      mappingInsuranceSpecial(row.garrioPro)
                    ) : (
                      <BodyTextM>{row.garrioPro}</BodyTextM>
                    )}
                    {isNotExactData(row) && !isExistSchein && (
                      <Radio
                        value={`garrioPro${row.fieldName}`}
                        className="radio-select"
                        checked={objectChecked[row.fieldName].checkedGarrioPro}
                        onChange={() => {
                          updateData(row.fieldName, {
                            value: row.garrioPro,
                            checkedGarrioPro: true,
                            checkedEAB: false,
                          });
                        }}
                      ></Radio>
                    )}
                  </Flex>
                );
              },
              conditionalCellStyles: [
                {
                  when: (row: ITableComparationData) => isNotExactData(row),
                  style: {
                    background: COLOR.WARNING_LIGHT,
                  },
                },
              ],
            },
            {
              id: 3,
              name: 'eAB',
              width: '282px',
              cell: (row: ITableComparationData) => {
                return (
                  <Flex
                    justify="space-between"
                    align="center"
                    className="comparison-freetext"
                  >
                    {row.fieldName === 'insuranceCompanyName' ? (
                      mappingInsuranceSpecial(row.eAB)
                    ) : (
                      <BodyTextM>{row.eAB}</BodyTextM>
                    )}
                    {isNotExactData(row) && !isExistSchein && (
                      <Radio
                        className="radio-select"
                        value={`eAB_${row.fieldName}`}
                        checked={objectChecked[row.fieldName].checkedEAB}
                        onChange={() => {
                          updateData(row.fieldName, {
                            value: row.eAB,
                            checkedGarrioPro: false,
                            checkedEAB: true,
                          });
                        }}
                      ></Radio>
                    )}
                  </Flex>
                );
              },
              conditionalCellStyles: [
                {
                  when: (row: ITableComparationData) => isNotExactData(row),
                  style: {
                    background: COLOR.WARNING_LIGHT,
                  },
                },
              ],
            },
          ]}
          data={data.data}
          customStyles={customStyles}
          noTableHead
          noHeader
        />
        <Divider className="comparison-divider" />
      </>
    );
  }

  const normalizeDefaultPayload = () => {
    const payload = {
      ...data?.selectedPatient!,
      postOfficeBox: {
        ...data?.selectedPatient?.postOfficeBox!,
      },
      personalInfo: {
        ...data?.selectedPatient?.personalInfo!,
      },
      addressInfo: {
        ...data?.selectedPatient?.addressInfo!,
        address: { ...data?.selectedPatient?.addressInfo.address! },
      },
      insuranceInfos: data?.selectedPatient?.insuranceInfos.map((i) => i)!,
    };

    const xmlPatient = data?.xmlPatient!;
    const changedFieldNames = Object.keys(objectChecked).filter(
      (fieldName) => objectChecked[fieldName].checkedEAB
    );
    changedFieldNames.forEach((fieldName) => {
      if (PERSONAL_FIELDS.includes(fieldName)) {
        payload.personalInfo[fieldName] = xmlPatient.personalInfo[fieldName];
        if (fieldName === 'dOB') {
          payload.personalInfo.dateOfBirth =
            xmlPatient.personalInfo.dateOfBirth;
        }
      }
      if (ADDRESS_INFO.includes(fieldName)) {
        payload.addressInfo.address[fieldName] =
          xmlPatient.addressInfo.address[fieldName];
      }
      if (POST_BOX.includes(fieldName)) {
        payload.postOfficeBox[fieldName] = xmlPatient.postOfficeBox[fieldName];
        if (fieldName === 'city') {
          payload.postOfficeBox.placeOfResidence =
            xmlPatient.postOfficeBox.placeOfResidence;
        }
      }
    });
    return payload;
  };

  const normalizePayloadUpdatePatientOnly = (payload) => {
    const xmlPatient = data?.xmlPatient!;
    const changedFieldNames = Object.keys(objectChecked).filter(
      (fieldName) => objectChecked[fieldName].checkedEAB
    );
    changedFieldNames.forEach((fieldName) => {
      if (INSURANCE_INFO.includes(fieldName)) {
        payload.insuranceInfos = payload.insuranceInfos.map((insurance) => {
          if (insurance.isActive) {
            if (fieldName === 'insuranceCompanyName') {
              return insurance;
            }
            return {
              ...insurance,
              [fieldName]: xmlPatient.insuranceInfos.find(
                (insuranceXML) => insuranceXML
              )?.[fieldName],
            };
          }
          return insurance;
        });
      }
    });
    return payload;
  };

  async function handleSyncData() {
    setIsLoadingSync(true);
    const checkedEAB = objectChecked.insuranceCompanyName.checkedEAB;
    let payload = normalizeDefaultPayload();
    if (!checkedEAB) {
      await updatePatientProfile({
        id: patientId,
        patientInfo: normalizePayloadUpdatePatientOnly(payload),
        patientMedicalData: null!,
      });
      goToSchein();
    } else {
      const iKNumber = data?.xmlPatient?.insuranceInfos[0].ikNumber;
      const res = await checkExistInsuranceByIKNumber({
        iKNumber: String(iKNumber),
      });

      if (res.data.isExist) {
        const sdktInfo = res.data.insuranceCompany!;
        payload = normalizePayloadUpdateInsurance(
          payload,
          sdktInfo,
          data?.xmlPatient!
        );
        await updatePatientProfile({
          id: patientId,
          patientInfo: payload,
          patientMedicalData: null!,
        });
        goToSchein();
      } else {
        const iKNumber = data?.xmlPatient?.insuranceInfos.find(
          (ins) => ins
        )?.ikNumber;
        const insuranceName = getActiveInsurance(
          data?.selectedPatient?.insuranceInfos
        )?.insuranceCompanyName!;
        setIKNumberNotFound({
          ikNumber: String(iKNumber),
          insuranceName: insuranceName,
          isAddIkNumber: false,
        });
      }
    }
  }

  function handleRouting() {
    setIsLoadingSync(true);
    let url = `${ROUTING.PATIENT.replace('[patientId]', patientId)}#timeline`;
    if (!patientId) {
      url = ROUTING.PATIENT_OVERVIEW;
    }
    router.push(url);
    setIsLoadingSync(false);
  }

  const onUpdatePayload = async (dataSdkt?: SdktCatalogItem) => {
    let payload = normalizeDefaultPayload();
    const iKNumber = data?.xmlPatient?.insuranceInfos.find((ins) => ins)?.ikNumber!;
    payload = normalizeDataWithSdkt(payload, dataSdkt!, iKNumber);
    await updatePatientProfile({
      id: patientId,
      patientInfo: payload,
      patientMedicalData: null!,
    });
    goToSchein();
  };

  return (
    <Modal
      className={className}
      enforceFocus={false}
      size={ModalSize.FULLSCREEN}
      isOpen={true}
      canOutsideClickClose={false}
      onClose={onClose}
      title={t('syncTitle', { name: title })}
    >
      <Box className={Classes.DIALOG_BODY} style={{ padding: '16px 0' }}>
        <Flex justify="center" column className={'comparison-body'} gap={16}>
          <MessageBar
            type="warning"
            content={t('warningContent')}
            subDescription={
              isExistSchein ? t('warningDescription') : t('descriptionWarning')
            }
            className="warning-bar"
            hasBullet={false}
          />
          <Box>
            {Header()}
            {personalInfo && Content(personalInfo)}
            {addressInfo && Content(addressInfo)}
            {postOfficeBox && Content(postOfficeBox)}
            {insuranceInfo && Content(insuranceInfo)}
          </Box>
        </Flex>
      </Box>
      <Box className={Classes.DIALOG_FOOTER} style={{ position: 'relative' }}>
        <Flex column className={'comparison-body'} align="flex-end">
          {isExistSchein ? (
            <Button
              intent="primary"
              outlined
              large
              loading={isLoadingSync}
              onClick={() => {
                onAssignPatient();
                handleRouting();
              }}
            >
              {t('btnConfirmAssign')}
            </Button>
          ) : (
            <Flex>
              <Button
                intent="primary"
                outlined
                large
                loading={isLoadingSync}
                onClick={() => {
                  goToSchein();
                }}
                className="btn-skip"
              >
                {t('btnSkip')}
              </Button>
              <Button
                intent="primary"
                large
                loading={isLoadingSync}
                onClick={() => {
                  handleSyncData();
                }}
              >
                {t('btnSync')}
              </Button>
            </Flex>
          )}
        </Flex>
      </Box>
      {!!iKNumberNotFound && (
        <IKNumberNotFound
          data={iKNumberNotFound}
          onClose={() => setIKNumberNotFound(undefined)}
          onCreateSuccess={async (data) => {
            onUpdatePayload(data);
          }}
          isUseForEAB
        />
      )}
    </Modal>
  );
};

export default PatientComparison;
