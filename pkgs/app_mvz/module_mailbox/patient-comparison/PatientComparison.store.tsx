import { proxy, useSnapshot } from 'valtio';

import { PatientCompareData } from '@tutum/hermes/bff/legacy/eab_common';
interface PatientComparisonStore {
  patientCompareData: PatientCompareData | undefined;
  openComparison: boolean;
}

const initState: PatientComparisonStore = {
  patientCompareData: undefined,
  openComparison: false,
};

interface PatientComparisonActions {
  setPatientCompareData(data: PatientCompareData): void;
  setOpenComparison(open: boolean): void;
  resetStore(): void;
}

export const patientComparisonStore = proxy<PatientComparisonStore>({
  patientCompareData: undefined,
  openComparison: false,
});

export const patientComparisonActions: PatientComparisonActions = {
  setPatientCompareData(data: PatientCompareData) {
    patientComparisonStore.patientCompareData = data;
  },
  setOpenComparison(open: boolean) {
    patientComparisonStore.openComparison = open;
  },
  resetStore() {
    Object.assign(patientComparisonStore, { ...initState });
  },
};

export const usePatientComparisonStore = (): {
  patientComparisonStore: PatientComparisonStore;
  patientComparisonActions: PatientComparisonActions;
} => {
  const snapshot = useSnapshot(patientComparisonStore);
  return { patientComparisonStore: snapshot, patientComparisonActions };
};
