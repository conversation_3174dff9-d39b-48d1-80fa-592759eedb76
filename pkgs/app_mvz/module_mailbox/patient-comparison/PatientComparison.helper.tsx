import { getUUID } from '@tutum/design-system/infrastructure/utils';
import { SdktCatalogItem } from '@tutum/hermes/bff/app_mvz_catalog_sdkt';
import { SdktCatalog } from '@tutum/hermes/bff/catalog_sdkt_common';
import { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import { data } from '@tutum/mvz/module_diga/mock';
import { initInsuranceItem } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';

export interface ITableComparationData {
  fieldName: string;
  garrioPro: string;
  eAB: string;
}

export interface ITableComparation {
  header: string;
  data: ITableComparationData[];
}
export interface ObjectRowData {
  value: string;
  checkedGarrioPro: boolean;
  checkedEAB: boolean;
}

export enum RowType {
  eAb = 'eAb',
  garrioPro = 'garrioPro',
}

export const PERSONAL_FIELDS = ['firstName', 'lastName', 'gender', 'dOB'];

export const ADDRESS_INFO = ['street', 'number', 'postCode', 'city'];

export const POST_BOX = ['officeBox', 'postCode', 'placeOfResidence'];

export const INSURANCE_INFO = [
  'insuranceNumber',
  'insuranceCompanyName',
  'insuranceStatus',
];

export const normalizePayloadUpdateInsurance = (
  payload: PatientInfo,
  sdkt: SdktCatalog,
  xmlPatient: PatientInfo
): PatientInfo => {
  const result = { ...payload };
  let insuranceInfo = result.insuranceInfos;
  insuranceInfo = insuranceInfo.map((item) => {
    return {
      ...item,
      isActive: false,
    };
  });
  insuranceInfo.push({
    ...initInsuranceItem,
    insuranceCompanyName: sdkt.name,
    insuranceCompanyId: sdkt.vknr,
    id: getUUID(),
    ikNumber: sdkt.iKNumbers.find(
      (i) => i.value === xmlPatient.insuranceInfos[0].ikNumber
    )?.value!,
    insuranceNumber: xmlPatient.insuranceInfos.find((i) => i)?.insuranceNumber,
    insuranceStatus: xmlPatient.insuranceInfos.find((i) => i)?.insuranceStatus!,
    validity: sdkt.validity,
    address: sdkt.address,
    isActive: true,
    startDate: sdkt.validity.fromDate,
    endDate: sdkt.validity.toDate,
    feeCatalogue: sdkt.feeCatalogue,
    feeSchedule: sdkt.feeSchedule,
  });
  return {
    ...result,
    insuranceInfos: insuranceInfo,
  };
};

export const normalizeDataWithSdkt = (
  payload: PatientInfo,
  sdktItem: SdktCatalogItem,
  ikNumber: number
) => {
  if (!sdktItem) return payload;
  const { sdkt } = sdktItem;
  const result = { ...payload };
  let insuranceInfo = result.insuranceInfos;
  insuranceInfo = insuranceInfo.map((item) => {
    return {
      ...item,
      isActive: false,
    };
  });
  const iKNumberValue = sdkt.iKNumbers.find(
    (ikItem) => ikItem.value === ikNumber
  );
  insuranceInfo.push({
    ...initInsuranceItem,
    insuranceCompanyName: sdkt.name,
    insuranceCompanyId: sdkt.vknr,
    id: getUUID(),
    ikNumber: iKNumberValue ? iKNumberValue.value! : sdkt.iKNumbers[0]?.value!,
    validity: sdkt.validity,
    address: sdkt.address,
    isActive: true,
    startDate: sdkt.validity.fromDate,
    endDate: sdkt.validity.toDate,
    feeCatalogue: sdkt.feeCatalogue,
    feeSchedule: sdkt.feeSchedule,
  });
  return {
    ...result,
    insuranceInfos: insuranceInfo,
  };
};

const transFuncs: { [key: string]: (d: unknown) => any } = {
  default: (d: unknown) => String(d || ''),
  dateOfBirth: (d: any) =>
    `${String(d.date).padStart(2, '0')}.${String(d.month).padStart(2, '0')}.${d.year
    }`,
  insuranceCompanyName: (d: any) =>
    d.insuranceCompanyName
      ? `${d.insuranceCompanyName} ${d.insuranceType} • IK: ${d.ikNumber}`
      : `IK: ${d.ikNumber}`,
  insuranceNumber: (d: any) => d.insuranceNumber,
  insuranceStatus: (d: any) => d.insuranceStatus,
};

export function transform<T>(d: T, f: string | any) {
  if (typeof f === 'string') {
    const func = transFuncs[f] || transFuncs.default;
    return func(d);
  }
  return f(d);
}

export const initDataObjectChecked: Record<string, ObjectRowData> = {
  firstName: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  lastName: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  gender: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  dOB: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  street: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  number: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  postCode: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  city: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  countryCode: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  insuranceNumber: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  insuranceCompanyName: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
  insuranceStatus: {
    value: '',
    checkedGarrioPro: true,
    checkedEAB: false,
  },
};
