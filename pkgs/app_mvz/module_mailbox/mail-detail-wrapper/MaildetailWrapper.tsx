import React from 'react';

import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';
import {
  mailboxActions,
  useMailboxStore,
} from '@tutum/mvz/module_mailbox/Mailbox.store';
import i18n from '@tutum/infrastructure/i18n';
import { MailItemDTO } from '@tutum/hermes/bff/app_mvz_mail';
import { Flex, Intent, Svg } from '@tutum/design-system/components';
import { SPACE } from '@tutum/design-system/styles';
import Button from '@tutum/design-system/components/Button/Button';
import { MailComposer } from '../mail-composer';
import { MailDetails } from '../mail-details';
import { MailThread } from '../mail-thread';

export interface MailDetailsWrapperProps extends Partial<MailItemDTO> {
  thread: MailItemDTO[];
  className?: string;
}

const ForwardIcon = '/images/chevron-corner-up-right.svg';
const ReplyIcon = '/images/chevron-corner-up-left.svg';

const MailDetailWrapper = (props: MailDetailsWrapperProps) => {
  const { id, className, emailItem, thread, date } = props;

  const mailboxStore = useMailboxStore();
  const { t } = i18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
  });
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof MailboxI18n.ButtonActions
  >({
    namespace: 'Mailbox',
    nestedTrans: 'ButtonActions',
  });

  const openComposer = () => {
    if (!mailboxStore.composerArea?.size) {
      mailboxActions.setComposer('sm');
    }
  };

  const onReplyThread = () => {
    mailboxActions.setForwardData(undefined);
    mailboxActions.setReplying(true);
    openComposer();
  };

  const onForwardThread = () => {
    const haveValue = emailItem && date && id;
    if (!haveValue) return;
    mailboxActions.setForwardData({ emailItem, date, id });
    mailboxActions.setReplying(false);
    openComposer();
  };

  if (thread.length === 1) {
    return emailItem ? <MailDetails {...props} /> : <></>;
  }

  return (
    <div className={className}>
      <div className="threadContent">
        <MailThread className="thread" t={t} thread={thread} />

        <Flex className="threadContent__actions" p={SPACE.SPACE_S}>
          <Flex ml="auto">
            <Button
              outlined
              minimal
              className="threadContent__actions--forward"
              icon={<Svg src={ForwardIcon} />}
              onClick={onForwardThread}
            >
              {tButtonActions('forward')}
            </Button>
            &nbsp;&nbsp;
            <Button
              className="threadContent__actions--reply"
              intent={Intent.PRIMARY}
              icon={<Svg src={ReplyIcon} />}
              onClick={onReplyThread}
            >
              {tButtonActions('reply')}
            </Button>
          </Flex>
        </Flex>
      </div>

      {mailboxStore.composerArea.size === 'sm' && thread.length > 1 && (
        <MailComposer
          size={mailboxStore.composerArea.size}
          defaultUserToReply={emailItem?.from}
          replySubject={emailItem?.subject}
        />
      )}
    </div>
  );
};

export default MailDetailWrapper;
