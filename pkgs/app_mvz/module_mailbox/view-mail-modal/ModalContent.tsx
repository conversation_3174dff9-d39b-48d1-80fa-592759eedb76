import React, { useEffect, useState } from 'react';
import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';

import { EmailItem } from '@tutum/hermes/bff/legacy/mail_common';
import { MailDetails } from '../mail-details';
import { BodyTextL, Flex, LoadingState } from '@tutum/design-system/components';
import { SPACE } from '@tutum/design-system/styles';
import { getPresignedGetURL } from '@tutum/hermes/bff/legacy/app_mvz_file';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

type ModalContentProps = {
  viewMailData: EmailItem | undefined;
  isOpenModalMessageSource: boolean;
  t: IFixedNamespaceTFunction<keyof typeof MailboxI18n>;
};

const Content = ({ message }: { message: string }) => {
  return (
    <div className="sl-view-mail__content">
      <Flex
        px={SPACE.SPACE_S}
        py={SPACE.SPACE_M}
        className="sl-view-mail__content--source"
      >
        <pre>
          <BodyTextL>{message}</BodyTextL>
        </pre>
      </Flex>
    </div>
  );
};

export function ModalContent({
  t,
  viewMailData,
  isOpenModalMessageSource,
}: ModalContentProps) {
  if (!isOpenModalMessageSource) {
    return <MailDetails emailItem={viewMailData} isView={!!viewMailData} />;
  }

  if (!viewMailData?.sourceUrl) {
    return <Content message={t('noMessageSource')} />;
  }

  const [messageSource, setMessageSource] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { sourceUrl } = viewMailData;
    const index = sourceUrl.indexOf('/');
    const bucketName = sourceUrl.slice(0, index);
    const objectName = sourceUrl.slice(index + 1, sourceUrl.length);
    setIsLoading(true);
    getPresignedGetURL({
      bucketName,
      objectName,
    })
      .then(({ data }) => {
        fetch(data.presignedURL)
          .then((response) => response.text())
          .then((data) => {
            setMessageSource(data);
            setIsLoading(false);
          });
      })
      .catch(() => {
        setIsLoading(false);
      });
  }, [viewMailData]);

  return isLoading ? <LoadingState /> : <Content message={messageSource} />;
}
