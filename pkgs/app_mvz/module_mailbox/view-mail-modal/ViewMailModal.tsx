import React, { memo } from 'react';

import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';

import {
  Button,
  Flex,
  Intent,
  Modal,
  ModalSize,
} from '@tutum/design-system/components';
import i18n from '@tutum/infrastructure/i18n';

import { mailboxActions, useMailboxStore } from '../Mailbox.store';
import { MailboxType } from '../Mailbox.type';
import { ModalContent } from './ModalContent';

export interface IViewMailModalProps {
  className?: string;
  mailboxType: MailboxType;
}

const ViewMailModal = (props: IViewMailModalProps) => {
  const { className } = props;

  const { viewMailData, isOpenModalMessageSource } = useMailboxStore();
  const { t } = i18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
  });
  const { t: tButtonCommonActions } = i18n.useTranslation<
    keyof typeof CommonI18n.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const handleClose = () => {
    mailboxActions.setOpenModalMessageSource(false);
    mailboxActions.setViewMailData(undefined);
  };

  return (
    <Modal
      className={className}
      title={viewMailData ? t('viewMail') : t('messageSource')}
      isOpen={!!viewMailData}
      onClose={handleClose}
      size={ModalSize.HALFSCREEN}
      usePortal
    >
      <ModalContent
        viewMailData={viewMailData}
        isOpenModalMessageSource={isOpenModalMessageSource}
        t={t}
      />
      <Flex className="sl-view-mail__footer" justify="flex-end">
        <Button large intent={Intent.PRIMARY} onClick={handleClose}>
          {tButtonCommonActions('close')}
        </Button>
      </Flex>
    </Modal>
  );
};

export default memo(ViewMailModal);
