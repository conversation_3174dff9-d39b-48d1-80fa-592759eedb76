import React, { useState, useRef, useContext } from 'react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';

import type patientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import type CreateScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import { Dialog, Svg, BodyTextM, Flex } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import {
  ICustomInsuranceInfo,
  stageSelectedInsurance,
} from '@tutum/mvz/_utils/checkInsurance';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { Divider } from '@tutum/design-system/components/Core';
import { IPatientManagement } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import CreatePatientBody from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatientContent';
import { WrapperCreatePatientDialog } from './PatientAndScheinDialogContents.styled';
import FormContent from '@tutum/mvz/module_kv_hzv_schein/FormContent.styled';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { StageDialog } from '../Mailbox.type';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import { InsuranceActionEnum } from '@tutum/mvz/module_insurance/ListInsurance.type';
import { IFormikRef } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import { PatientProfileResponse } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';

const ListInsurancesModal = dynamic(
  () => import('@tutum/mvz/module_insurance/ListInsurance.styled'),
  { ssr: false }
);

export interface ICreatePatientOrScheinDialogProps {
  className?: string;
  isOpen: boolean;
  onClose: () => void;
  description?: string;
  patientManagement?: IPatientManagement;
  onUpdateMailAfterCreateSchein?: () => void;
  onAssignPatientAfterCreate?: (patientId: string) => Promise<void>;
  stageDialog: StageDialog;
}

const CreatePatientOrScheinDialog = (
  props: ICreatePatientOrScheinDialogProps
) => {
  const {
    className,
    isOpen,
    onClose,
    patientManagement,
    onUpdateMailAfterCreateSchein,
    onAssignPatientAfterCreate,
    stageDialog,
  } = props;

  const { stage, context } = stageDialog;

  const formikRef = useRef<IFormikRef>(null);
  const router = useRouter();
  const { t: tCreateSchein } = I18n.useTranslation<
    keyof typeof CreateScheinI18n.createSchein
  >({
    namespace: 'Schein',
    nestedTrans: 'createSchein',
  });

  const { t: tCreatePatient } = I18n.useTranslation<
    keyof typeof patientManagementI18n.CreatePatient
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'CreatePatient',
  });

  const [initInsurances, setInitInsurances] = useState<ICustomInsuranceInfo[]>(
    []
  );
  const [insuranceState, setInsuranceState] = useState<{
    insurance: ICustomInsuranceInfo[];
    insuranceDeleted: ICustomInsuranceInfo[];
  }>({
    insurance: [],
    insuranceDeleted: [],
  });
  const globalContext = useContext(GlobalContext.instance);

  const [isChangeInsurance, setIsChangeInsurance] = useState(false);
  const [openInsuranceList, setOpenInsuranceList] = useState(false);

  const onToggleInsuranceList = () => {
    setOpenInsuranceList((prev) => !prev);
  };

  const onSaveNewInsurances = (items: ICustomInsuranceInfo[]) => {
    setInitInsurances(items);
    setInsuranceState((prev) => ({
      ...prev,
      insurance: items.map((i) => ({
        ...i,
        id: i.id || getUUID(),
      })),
    }));
    setIsChangeInsurance(true);
  };

  const onCloseDialog = () => {
    onClose();
  };

  const renderPanelTitle = (): React.ReactNode => {
    return (
      <Flex justify="space-between" maxWidth={760}>
        <BodyTextM fontSize={24} fontWeight={700}>
          {stage === 'openCreateSchein'
            ? tCreateSchein('title')
            : tCreatePatient('createPatientProfile')}
        </BodyTextM>
        <Flex>
          <div>
            <Divider className="sl-review-patient-header-divider" />
          </div>
          <Svg
            onClick={onCloseDialog}
            style={{ height: '24px', cursor: 'pointer' }}
            src="/images/close.svg"
          />
        </Flex>
      </Flex>
    );
  };

  const submitCreateSchein = () => {
    onUpdateMailAfterCreateSchein?.();
    const URL = ROUTING.PATIENT.replace(
      '[patientId]',
      patientManagement?.patientId?.value!
    );
    router?.push(URL);
  };

  const handlerUpdatePatientMail = async (patientId: string) => {
    await onAssignPatientAfterCreate?.(patientId);
  };

  return (
    <Dialog
      className={className}
      size="full"
      isOpen={isOpen}
      onClose={onClose}
      isCloseButtonShown={false}
      title={renderPanelTitle()}
      style={{
        '--sl-dialog-header-padding-inline': '0',
      }}
    >
      {stage === 'openCreatePatient' && (
        <WrapperCreatePatientDialog>
          <CreatePatientBody
            createPatientDefaultValue={context}
            isOpen={true}
            ref={formikRef}
            isApplyForEab={true}
            className="sl-review-patient-body"
            isLoadingPatient={false}
            onToggleInsuranceList={onToggleInsuranceList}
            initInsurances={initInsurances}
            insuranceState={insuranceState}
            setInitInsurances={(value) => setInitInsurances(value)}
            isChangeInsurance={isChangeInsurance}
            onClose={onCloseDialog}
            handlerUpdatePatientMail={handlerUpdatePatientMail}
          />
        </WrapperCreatePatientDialog>
      )}
      {stage === 'openCreateSchein' && !!patientManagement?.patientId?.value && (
        <Flex className="sl-review-patient-body">
          <FormContent
            isOpen={true}
            patientId={context.patientId}
            onCancel={onCloseDialog}
            onClose={onCloseDialog}
            globalContext={globalContext}
            isCreateReadCard={false}
            setShowDMPEnrollDialog={() => { }}
            patientManagement={patientManagement}
            onCreateScheinMailBox={submitCreateSchein}
          />
        </Flex>
      )}
      {openInsuranceList && (
        <ListInsurancesModal
          scenario={{ action: InsuranceActionEnum.Create }}
          className="sl-ListInsurancesModal"
          show={openInsuranceList}
          onClose={onToggleInsuranceList}
          initInsurance={initInsurances}
          onSubmit={onSaveNewInsurances}
          stateSelectInsurance={
            stage === 'openCreateSchein'
              ? stageSelectedInsurance.KV_createSchein
              : stageSelectedInsurance.Public_createPatient
          }
          patient={formikRef.current?.getValue() as unknown as PatientProfileResponse}
        />
      )}
    </Dialog>
  );
};

export default CreatePatientOrScheinDialog;
