import { proxy, useSnapshot } from 'valtio';

import {
  SearchInfoType,
  ComposerSize,
  TabIds,
  CardErrorsType,
  MailboxType,
  StageDialog,
} from './Mailbox.type';
import {
  getIndexSelectedMail,
  isEABMail,
  StateAssignPatient,
} from './Mailbox.helper';
import {
  assignPatient,
  AssignPatientRequest,
  deleteMails,
  getArchivedMails,
  getInboxMails,
  getInboxMailsByBillingHistoryId,
  getKimAccounts,
  GetKimAccountsRequest,
  GetMailsRequest,
  getOutboxMails,
  MailItemDTO,
  markInboxMailStatus,
  markRead,
  MarkReadRequest,
  MarkStatusRequest,
  syncInboxMailByAccount,
  unarchiveMail,
  UnarchiveMailRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_mail';
import PatientManagementService from '@tutum/mvz/module_patient-management/PatientManagement.service';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import type { IPatientManagement } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { Order, PaginationRequest } from '@tutum/hermes/bff/common';
import {
  EmailItem,
  MailCategory,
  SearchType,
  MailHeaderKey,
} from '@tutum/hermes/bff/mail_common';
import {
  checkKVConnect,
  CheckKVConnectRequest,
} from '@tutum/hermes/bff/app_kv_connect';
import {
  AccountStatus,
  MailAccountDto,
} from '@tutum/hermes/bff/legacy/mail_common';
import {
  GetInboxMailsByBillingHistoryIdRequest,
  MailBox,
} from '@tutum/hermes/bff/app_mvz_mail';
import {
  getSetting,
  saveSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_settings';
import {
  KIMAccountKey,
  SettingsFeatures,
  SettingsFeatures_KIMAccount_SendMDN,
} from '@tutum/hermes/bff/legacy/settings_common';
import { EABModel } from '@tutum/hermes/bff/legacy/eab_common';
import {
  assignPatient as assignPatientEAB,
  unAssignPatient,
  checkExistSchein,
  preparePatientCompare,
} from '@tutum/hermes/bff/legacy/app_mvz_eab';
import { patientComparisonActions } from './patient-comparison/PatientComparison.store';
import {
  AutomaticDocumentingCase,
  EABSetting,
} from '@tutum/hermes/bff/eab_common';
import { Terminal } from '@tutum/hermes/bff/legacy/app_card_operation';
import { ErrorType } from '@tutum/hermes/bff/legacy/api_client';
import { IPatientSearchResult } from '@tutum/mvz/module_patient-management/patient-search/PatientSearch.type';

const INIT_PATIENT_MANAGER: IPatientManagement = {
  patient: undefined,
  patientId: { value: '' },
  ikNumber: 0,
  loadingPatient: false,
  availableHzvContracts: [],
  availableFavContracts: [],
  selectedContractDoctor: {
    bsnrId: undefined,
    doctorId: undefined,
    contractId: undefined,
    chargeSystemId: undefined,
    availableDoctor: [],
    encounterCase: undefined,
  },
  readyToUsePatientParticipationData: false,
  getPatientParticipationResponse: {
    participations: [],
  },
  activeParticipations: [],
  isShowHzvButton: false,
  indicatorActiveIngredients: [],
  showHintVSST785: false,
  pznAtcForHighPrescriptions: [],
};

export interface IMailInfo {
  mails: MailItemDTO[];
  selectedIds: string[];
  isSelectAll: boolean;
  countUnread: number;
  total: number;
  pagination: PaginationRequest;
  searchInfo: SearchInfoType;
  isReload: boolean;
}

type SuccessDataTerminal = {
  stage: 'success';
  data: Terminal[];
};

type ErrDataTerminal = {
  stage: 'failure';
  error: ErrorType;
};

type DefaultTerminal = {
  stage: 'default';
};

type ResTerminal = SuccessDataTerminal | ErrDataTerminal | DefaultTerminal;

export interface IMailboxStore {
  mailboxType: MailboxType | undefined;
  loadingMailList: boolean;
  loadingMailDetails: boolean;
  loading: boolean;
  loadingSyncMails: boolean;
  isSearching: boolean;
  listAccount: MailAccountDto[];
  notification: {
    [MailboxType.KVConnect]: boolean;
    [MailboxType.Kim]: boolean;
  };
  totalUnreadMail: {
    [MailboxType.KVConnect]: number;
    [MailboxType.Kim]: number;
  };
  selectedTabId: TabIds.Inbox | TabIds.Outbox | TabIds.Archived;
  selectedAccount: MailAccountDto;
  sendMDNotificationSupported: SettingsFeatures_KIMAccount_SendMDN;
  selectedMailId: string | undefined;
  selectedMail: MailItemDTO | undefined;
  selectedProposalPatients: IPatientSearchResult[];
  selectedProposalTextSearch: string;
  cardError: CardErrorsType | undefined;
  [TabIds.Inbox]: IMailInfo;
  [TabIds.Outbox]: IMailInfo;
  [TabIds.Archived]: IMailInfo;
  composerArea: {
    size: ComposerSize;
  };
  viewMailData: EmailItem | undefined;
  forwardData: MailItemDTO | undefined;
  isReplying: boolean;
  isOpenModalMessageSource: boolean;
  currentThread: MailItemDTO[];
  currentbillingHistoryId: string;
  eabItem: EABModel | undefined;
  isOpenPatientOrSchein: boolean;
  patientManagement: IPatientManagement | null;
  currentStateAssign: {
    isExistSchein: boolean;
    state: StateAssignPatient;
  };
  settingConsentData: {
    settingData: EABSetting | null;
    type: AutomaticDocumentingCase;
  };
  stageDialogScheinOrPatient: StageDialog;
  isSubmitByConsentModal: boolean;
  resTerminal: ResTerminal;
}

export interface IMailboxActions {
  setMailboxType: (type: MailboxType) => void;
  setActiveTab: (selectedTab: TabIds) => void;
  setComposer: (size: ComposerSize) => void;
  setActiveAccount: (selectedAccount: string) => void;
  setConfirmDelivery: (isAllow: boolean) => void;
  setPagination: (pagination: PaginationRequest) => void;
  setSearchInfo: (searchInfo: SearchInfoType, isSearching?: boolean) => void;
  setViewMail: (selectedMailId: string, thread?: MailItemDTO[]) => void;
  setNotification: (notification: boolean) => void;
  setTotalMails: (type: MailboxType, totalUnread: number) => void;
  setViewMailData: (emailItem: EmailItem | undefined) => void;
  setForwardData: (payload: MailItemDTO | undefined) => void;
  reloadList: (selectedTabId?: TabIds) => void;
  getMails: (payload: GetMailsRequest, tabId: TabIds) => Promise<void>;
  getAccounts: (payload: GetKimAccountsRequest) => Promise<void>;
  getKVConnectAccount: (payload: CheckKVConnectRequest) => Promise<void>;
  prepareMailbox: () => void;
  markAsReadOrUnread: (payload: MarkReadRequest) => Promise<void>;
  changeMailsStatus: (payload: MarkStatusRequest) => Promise<void>;
  unarchiveMail: (payload: UnarchiveMailRequest) => Promise<void>;
  assignPatientToMail: (
    payload: Partial<AssignPatientRequest>
  ) => Promise<void>;
  unAssignPatientToMail: (
    payload: Partial<AssignPatientRequest>
  ) => Promise<void>;
  syncInboxMails: (isLoading?: boolean) => void;
  setCardError: (cardError: CardErrorsType) => void;
  setReplying: (isReplying: boolean) => void;
  setOpenModalMessageSource: (isOpen: boolean) => void;
  resetStore: () => void;
  getMailsByBillingHistoryId: (GetInboxMailsByBillingHistoryIdRequest) => void;
  setBillingHistoryId: (id: string) => void;
  setSelectedIds: (tabId: TabIds, ids: string[], selected: boolean) => void;
  clearSelectedIds: (tabId: TabIds) => void;
  selectAllMails: (tabId: TabIds, selected: boolean) => void;
  deleteMails: (ids: string[]) => Promise<any>;
  setEABItem: (item?: EABModel) => void;
  setDialogPatientOrSchein: (isOpenPatientOrSchein: boolean) => void;
  getInformationSchein: (patientId: string) => void;
  setScheinIsExist: (currentStateAssign: {
    isExist: boolean;
    state: StateAssignPatient;
  }) => void;
  checkScheinIsExist: (
    patientId: string,
    state?: StateAssignPatient
  ) => Promise<void>;
  setSelectedMail: (selectedMail: MailItemDTO) => void;
  clearInformationSchein: () => void;
  setSettingConsentData: (
    settingData: EABSetting,
    type: AutomaticDocumentingCase
  ) => void;
  setStageDialog: (stage: StageDialog) => void;
  setSubmitByConsent: (isSubmit: boolean) => void;
  setCompareData: (patientId: string) => Promise<void>;
  setSelectedProposalPatients: (patients: IPatientSearchResult[]) => void;
  setSelectedProposalTextSearch: (textSerach: string) => void;
  setTerminalData: (data: ResTerminal) => void;
}

const initMailInfo: IMailInfo = {
  mails: [],
  selectedIds: [],
  isSelectAll: false,
  countUnread: 0,
  total: 0,
  pagination: {
    page: 1,
    pageSize: 30,
    sortBy: '',
    order: Order.DESC,
  },
  searchInfo: {
    searchType: SearchType.SearchType_From,
    searchValue: '',
  },
  isReload: false,
};

/**
 * Mailbox initial state
 */
const initState: IMailboxStore = {
  mailboxType: undefined,
  loading: false,
  loadingMailList: false,
  loadingMailDetails: false,
  loadingSyncMails: false,
  isSearching: false,
  listAccount: [],
  inbox: initMailInfo,
  outbox: initMailInfo,
  archived: initMailInfo,
  notification: {
    [MailboxType.Kim]: false,
    [MailboxType.KVConnect]: false,
  },
  totalUnreadMail: {
    [MailboxType.Kim]: 0,
    [MailboxType.KVConnect]: 0,
  },
  selectedTabId: TabIds.Inbox, // Make default selected tab id is inbox tab
  selectedAccount: {
    email: '',
  },
  sendMDNotificationSupported:
    SettingsFeatures_KIMAccount_SendMDN.CheckMDNRequest,
  selectedMailId: undefined,
  selectedMail: undefined,
  cardError: undefined,
  composerArea: {
    size: null,
  },
  viewMailData: undefined,
  forwardData: undefined,
  isReplying: false,
  isOpenModalMessageSource: false,
  currentThread: [],
  currentbillingHistoryId: '',
  eabItem: undefined,
  isOpenPatientOrSchein: false,
  patientManagement: INIT_PATIENT_MANAGER,
  currentStateAssign: {
    isExistSchein: false,
    state: StateAssignPatient.default,
  },
  settingConsentData: {
    settingData: null,
    type: AutomaticDocumentingCase.Sending,
  },
  stageDialogScheinOrPatient: { stage: 'default', context: undefined },
  isSubmitByConsentModal: false,
  selectedProposalPatients: [],
  selectedProposalTextSearch: '',
  resTerminal: { stage: 'default' },
};

export const mailboxStore = proxy<IMailboxStore>(initState);

/**
 * Define Mailbox actions
 */
export const mailboxActions: IMailboxActions = {
  setMailboxType: (type) => {
    mailboxStore.mailboxType = type;
  },
  setActiveTab: (newTabId: TabIds) => {
    mailboxStore.selectedTabId = newTabId;
    if (newTabId === TabIds.Outbox) {
      mailboxActions.reloadList(newTabId);
    }
    mailboxStore.selectedMail = undefined;
  },
  setActiveAccount: (selectedAccount: string) => {
    mailboxStore.selectedAccount.email = selectedAccount;
    if (selectedAccount) {
      saveSettings({
        feature: SettingsFeatures.SettingsFeatures_KIMAccount,
        settings: {
          account: selectedAccount,
          [KIMAccountKey.KIMAccountKey_AutoSendMDNRequest]:
            mailboxStore.sendMDNotificationSupported || '',
        },
      });
    }
  },
  setConfirmDelivery: (isAllow: boolean) => {
    const autoSendMDNRequest = isAllow
      ? SettingsFeatures_KIMAccount_SendMDN.CheckMDNRequest
      : SettingsFeatures_KIMAccount_SendMDN.UnCheckMDNRequest;
    mailboxStore.sendMDNotificationSupported = autoSendMDNRequest;
    saveSettings({
      feature: SettingsFeatures.SettingsFeatures_KIMAccount,
      settings: {
        account: mailboxStore.selectedAccount.email,
        [KIMAccountKey.KIMAccountKey_AutoSendMDNRequest]: autoSendMDNRequest,
      },
    });
  },
  setViewMail: async (selectedMailId: string, thread: MailItemDTO[] = []) => {
    const { selectedTabId, mailboxType } = mailboxStore;
    const { mails } = mailboxStore[selectedTabId];
    mailboxStore.selectedMailId = selectedMailId;

    if (!selectedMailId) {
      mailboxStore.selectedMail = undefined;
      mailboxStore.currentThread = [];
    } else {
      mailboxStore.loadingMailDetails = true;
      const indexSelectedMail = getIndexSelectedMail(selectedMailId, mails);
      const emailSelected = mails[indexSelectedMail];
      const mailHeaderCategory =
        emailSelected.emailItem?.mailHeader &&
        emailSelected.emailItem.mailHeader[
        MailHeaderKey.MailHeaderKey_ServiceIdentifer
        ];
      if (mailHeaderCategory === MailCategory.MailCategory_EAB) {
        mailboxStore.selectedMail = {
          ...emailSelected,
          emailItem: {
            ...emailSelected.emailItem,
            attachments: emailSelected.emailItem.attachments?.filter(
              (item) => !item.name.includes('.p7s')
            ),
          },
        };
      } else {
        mailboxStore.selectedMail = emailSelected;
      }
      if (mailboxType === MailboxType.Kim) {
        mailboxActions.setComposer(null);
      }
      if (thread.length > 0) {
        mailboxStore.currentThread = thread;
      }
      mailboxStore.loadingMailDetails = false;
    }
  },
  setPagination: (pagination: PaginationRequest) => {
    mailboxStore[mailboxStore.selectedTabId].pagination = pagination;
    mailboxActions.reloadList();
  },
  setSearchInfo: (searchInfo: SearchInfoType, isSearching = true) => {
    mailboxStore[mailboxStore.selectedTabId].searchInfo = searchInfo;
    mailboxStore.isSearching = isSearching;
  },
  setNotification: (notification: boolean) => {
    if (mailboxStore.mailboxType) {
      mailboxStore.notification[mailboxStore.mailboxType] = notification;
    }
  },
  setTotalMails: (type: MailboxType, totalUnread: number) => {
    mailboxStore.totalUnreadMail[type] = totalUnread;
  },
  setViewMailData: (emailItem: EmailItem | undefined) => {
    mailboxStore.viewMailData = emailItem;
  },
  setForwardData: (payload) => {
    mailboxStore.forwardData = payload;
  },
  reloadList: (selectedTabId: TabIds = mailboxStore.selectedTabId) => {
    mailboxStore[selectedTabId] = {
      ...mailboxStore[selectedTabId],
      isReload: true,
    };
  },
  getMailsByBillingHistoryId: async (
    payload: GetInboxMailsByBillingHistoryIdRequest
  ) => {
    if (mailboxStore[TabIds.Inbox].isReload) {
      mailboxStore.loadingMailList = true;
      try {
        const { data } = await getInboxMailsByBillingHistoryId(payload);
        const resMails = (data.mails || []).reverse();

        mailboxStore[TabIds.Inbox] = {
          ...mailboxStore[TabIds.Inbox],
          mails: resMails,
          countUnread: resMails.filter((m) => !m.emailItem.isRead).length,
          total: resMails.length,
          isReload: false,
        };

        const selectedMail = resMails[0];

        const thread =
          selectedMail?.emailItem instanceof Array
            ? selectedMail[0]
            : [selectedMail];

        mailboxActions.setViewMail(selectedMail?.id, thread);
      } finally {
        mailboxStore.loadingMailList = false;
        mailboxStore.isSearching = false;
      }
    }
  },
  getMails: async (payload: GetMailsRequest, selectedTabId: TabIds) => {
    if (mailboxStore[selectedTabId].isReload) {
      mailboxStore.loadingMailList = true;
      try {
        const fullyPayload: GetMailsRequest = {
          ...payload,
          searchType:
            payload.searchValue &&
              mailboxStore[selectedTabId].searchInfo.searchType
              ? mailboxStore[selectedTabId].searchInfo.searchType
              : undefined,
        };

        const getMailFunc =
          {
            [TabIds.Inbox]: getInboxMails,
            [TabIds.Outbox]: getOutboxMails,
            [TabIds.Archived]: getArchivedMails,
          }[selectedTabId] || getInboxMails;
        const {
          data: { emailItems, total, unread },
        } = await getMailFunc(fullyPayload);

        if (selectedTabId === TabIds.Inbox) {
          mailboxActions.setTotalMails(
            payload.mailBox === MailBox.KV_CONNECT
              ? MailboxType.KVConnect
              : MailboxType.Kim,
            unread
          );
        }

        mailboxStore[selectedTabId] = {
          ...mailboxStore[selectedTabId],
          mails: emailItems,
          countUnread: unread,
          total,
          isReload: false,
        };

        if (mailboxStore.selectedMail && mailboxStore[selectedTabId].mails) {
          mailboxStore.selectedMail = mailboxStore[selectedTabId].mails.find(
            (mail) => mail.id === mailboxStore.selectedMail?.id
          );
        }
      } finally {
        mailboxStore.loadingMailList = false;
        mailboxStore.isSearching = false;
      }
    }
  },
  getAccounts: async (payload: GetKimAccountsRequest) => {
    mailboxStore.loading = true;
    try {
      const {
        data: { accounts },
      } = await getKimAccounts(payload);
      if (accounts?.length) {
        const lastAccountSelectedResp = await getSetting({
          feature: SettingsFeatures.SettingsFeatures_KIMAccount,
          settings: [],
        });
        const lastAccountSelected =
          lastAccountSelectedResp.data?.settings?.account;
        const autoSendMDNRequest =
          lastAccountSelectedResp?.data?.settings?.autoSendMDNRequest;
        mailboxStore.sendMDNotificationSupported = !!autoSendMDNRequest
          ? (autoSendMDNRequest as SettingsFeatures_KIMAccount_SendMDN)
          : SettingsFeatures_KIMAccount_SendMDN.CheckMDNRequest;
        const existedAccount = accounts.find(
          (account) => account.email === lastAccountSelected
        );
        if (existedAccount) {
          mailboxStore.selectedAccount = existedAccount;
        } else {
          mailboxStore.selectedAccount = accounts[0];
          saveSettings({
            feature: SettingsFeatures.SettingsFeatures_KIMAccount,
            settings: {
              account: accounts[0].email,
              [KIMAccountKey.KIMAccountKey_AutoSendMDNRequest]:
                autoSendMDNRequest,
            },
          });
        }
        mailboxStore.listAccount = accounts;
      } else {
        mailboxStore.selectedAccount.email = '';
      }
    } finally {
      mailboxStore.loading = false;
    }
  },
  getKVConnectAccount: async (payload) => {
    mailboxStore.loading = true;
    try {
      const response = await checkKVConnect(payload);
      const username = response?.data?.username ?? '';
      mailboxStore.selectedAccount.email = username;
    } finally {
      mailboxStore.loading = false;
    }
  },
  prepareMailbox: () => {
    mailboxStore.inbox.isReload = true;
    mailboxStore.outbox.isReload = true;
    mailboxStore.archived.isReload = true;
  },
  markAsReadOrUnread: async (payload: MarkReadRequest) => {
    const { id, isRead } = payload;
    const { selectedTabId } = mailboxStore;
    try {
      await markRead({
        ...payload,
        inbox: [TabIds.Inbox, TabIds.Archived].includes(
          mailboxStore.selectedTabId
        ),
      });
    } finally {
      const mailIndex = mailboxStore[selectedTabId].mails.findIndex(
        (mail) => mail.id === id
      );
      if (mailIndex !== -1) {
        mailboxStore[selectedTabId].mails[mailIndex].emailItem.isRead = isRead;
      }

      if (!isRead) {
        // mailboxStore.selectedMailId = undefined;
        mailboxStore[selectedTabId].countUnread += 1;
      } else {
        mailboxStore[selectedTabId].countUnread -= 1;
      }
    }
  },
  changeMailsStatus: async (payload: MarkStatusRequest) => {
    await markInboxMailStatus({
      ...payload,
    });
  },
  assignPatientToMail: async (payload: Partial<AssignPatientRequest>) => {
    const { selectedMailId, selectedTabId } = mailboxStore;
    const { mails } = mailboxStore[selectedTabId];
    const indexSelectedMail = getIndexSelectedMail(
      selectedMailId || '',
      mailboxStore[selectedTabId].mails
    );
    const emailSelected = mails[indexSelectedMail];
    try {
      if (
        selectedTabId === TabIds.Inbox &&
        isEABMail(emailSelected.emailItem)
      ) {
        await assignPatientEAB({
          mailId: selectedMailId || '',
          patientId: payload.patient
            ? payload.patient.id
            : payload.oldPatientId!,
        });
      } else {
        await assignPatient({
          id: selectedMailId || '',
          inbox: selectedTabId === TabIds.Inbox,
          ...payload,
        });
      }
    } finally {
      const { patient } = payload;
      mailboxStore[selectedTabId].mails[indexSelectedMail].emailItem.patient =
        patient;
      mailboxStore.selectedMail = {
        ...mailboxStore.selectedMail!,
        emailItem: {
          ...mailboxStore.selectedMail?.emailItem!,
          patient: patient,
        },
      };
    }
  },
  unAssignPatientToMail: async (payload: Partial<AssignPatientRequest>) => {
    const { selectedMailId, selectedTabId } = mailboxStore;
    const { mails } = mailboxStore[selectedTabId];
    const indexSelectedMail = getIndexSelectedMail(
      selectedMailId || '',
      mailboxStore[selectedTabId].mails
    );
    const emailSelected = mails[indexSelectedMail];
    try {
      if (
        selectedTabId === TabIds.Inbox &&
        isEABMail(emailSelected.emailItem)
      ) {
        await unAssignPatient({
          mailId: selectedMailId || '',
          patientId: payload.patient
            ? payload.patient.id
            : payload.oldPatientId!,
        });
      } else {
        await assignPatient({
          id: selectedMailId || '',
          inbox: selectedTabId === TabIds.Inbox,
          ...payload,
        });
      }
    } finally {
      const { patient } = payload;
      mailboxStore[selectedTabId].mails[indexSelectedMail].emailItem.patient =
        patient;
      mailboxStore.selectedMail = {
        ...mailboxStore.selectedMail!,
        emailItem: {
          ...mailboxStore.selectedMail?.emailItem!,
          patient: patient,
        },
      };
    }
  },
  syncInboxMails: async (isLoading = true) => {
    if (mailboxStore.selectedAccount) {
      mailboxStore.loadingSyncMails = isLoading;
      try {
        await syncInboxMailByAccount({
          accountEmail: mailboxStore.selectedAccount.email,
        });
      } catch (error) {
        if (error?.message === CardErrorsType.CardNotAvailable) {
          mailboxStore.cardError = error?.message as CardErrorsType;
        }
      } finally {
        mailboxStore.loadingSyncMails = false;
      }
    }
  },
  setCardError: (cardError) => {
    mailboxStore.cardError = cardError;
  },
  resetStore: () => {
    Object.assign(mailboxStore, { ...initState });
  },
  setComposer: (size: ComposerSize) => {
    mailboxStore.composerArea = {
      size: size,
    };
  },
  setReplying: (isReplying) => {
    mailboxStore.isReplying = isReplying;
  },
  setOpenModalMessageSource: (isOpen) => {
    mailboxStore.isOpenModalMessageSource = isOpen;
  },
  setBillingHistoryId: (id: string) => {
    mailboxStore.currentbillingHistoryId = id;
  },
  setSelectedIds: (tabId, ids, selected) => {
    let selectedIds = [...mailboxStore[tabId].selectedIds];
    if (selected) {
      selectedIds.push(...ids);
      selectedIds = [...new Set(selectedIds)];
    } else {
      selectedIds = selectedIds.filter((id) => !ids.includes(id));
    }
    mailboxStore[tabId].selectedIds = selectedIds;
  },
  clearSelectedIds: (tabId) => {
    mailboxStore[tabId].selectedIds = [];
  },
  unarchiveMail: async (payload) => {
    await unarchiveMail(payload);
  },
  selectAllMails: (tabId, selected) => {
    mailboxStore[tabId].isSelectAll = selected;
  },
  deleteMails: (ids) => deleteMails({ ids }),
  setTerminalData: (res: ResTerminal) => {
    mailboxStore.resTerminal = res;
  },

  // eDoctorLetter ~ eAB
  setEABItem: (item?: EABModel) => {
    mailboxStore.eabItem = item;
  },
  setDialogPatientOrSchein: (isOpen: boolean) => {
    mailboxStore.isOpenPatientOrSchein = isOpen;
  },
  setStageDialog: (stage: StageDialog) => {
    mailboxStore.stageDialogScheinOrPatient = stage;
  },
  getInformationSchein: (patientId: string) => {
    Promise.all([
      PatientManagementService.getPatientProfileById(patientId),
    ]).then(([patient]) => {
      if (mailboxStore.patientManagement) {
        mailboxStore.patientManagement.patient = patient!;

        if (mailboxStore.patientManagement.patientId) {
          mailboxStore.patientManagement.patientId.value = patientId;
        }
      }

      patientFileActions.patient.setCurrent(patient!);
    });
    patientFileActions.schein.getScheinsOverview(patientId);
  },
  clearInformationSchein: () => {
    mailboxStore.patientManagement = INIT_PATIENT_MANAGER;
  },
  setScheinIsExist: (currentStateAssign: {
    isExist: boolean;
    state: StateAssignPatient;
  }) => {
    mailboxStore.currentStateAssign = {
      isExistSchein: currentStateAssign.isExist,
      state: currentStateAssign.state,
    };
  },
  checkScheinIsExist: async (patientId: string, state?: StateAssignPatient) => {
    if (!patientId) return;
    try {
      const res = await checkExistSchein({ patientId });
      mailboxStore.currentStateAssign = {
        isExistSchein: res.data.isExist,
        state: state ? state : StateAssignPatient.auto,
      };
    } catch (err) {
      console.error(err);
    }
  },
  setSelectedMail: (selectedMail: MailItemDTO) => {
    mailboxStore.selectedMail = selectedMail;
  },
  setSettingConsentData: (
    settingData: EABSetting,
    type: AutomaticDocumentingCase
  ) => {
    mailboxStore.settingConsentData.settingData = settingData;
    mailboxStore.settingConsentData.type = type;
  },
  setSubmitByConsent: (isSubmit: boolean) => {
    mailboxStore.isSubmitByConsentModal = isSubmit;
  },
  setCompareData: async (patientId: string) => {
    const { selectedMailId } = mailboxStore;
    try {
      const response = await preparePatientCompare({
        mailId: selectedMailId || '',
        patientId,
      });
      const { patientCompareData } = response.data;
      patientComparisonActions.setPatientCompareData(patientCompareData);
      patientComparisonActions.setOpenComparison(true);
    } catch (error) {
      throw error;
    }
  },
  setSelectedProposalPatients(proposalPatients) {
    mailboxStore.selectedProposalPatients = proposalPatients;
  },
  setSelectedProposalTextSearch: (textSearch: string) => {
    mailboxStore.selectedProposalTextSearch = textSearch;
  },
};

export function useMailboxStore() {
  return useSnapshot(mailboxStore);
}
