import React, { memo, useState, useContext, useRef } from 'react';
import isEmpty from 'lodash/isEmpty';

import { Flex, LeaveConfirmModal } from '@tutum/design-system/components';
import { IMvzTheme } from '@tutum/mvz/theme';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { Modal, ModalSize } from '@tutum/design-system/components/Modal';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import ScheinContent from '../schein-content/ScheinContent.styled';
import { MainGroup } from '@tutum/hermes/bff/schein_common';

export interface ICreateScheinDialogProps {
  className?: string;
  theme?: IMvzTheme;
  isOpen: boolean;
  id?: string;
  onClose: () => void;
  patientId?: string;
  reloadQuarters?: () => void;
  typeSchein: MainGroup;
}

function CreateScheinDialog(
  props: ICreateScheinDialogProps & II18nFixedNamespace<any>
): React.FunctionComponentElement<ICreateScheinDialogProps> {
  const [confirmOpen, setConfirmOpen] = useState<boolean>(false);
  const ref = useRef<any>(null);

  const { isOpen, onClose, className, t, id, patientId, typeSchein } = props;
  const globalContext = useContext(GlobalContext.instance);
  const { patientManagement } = useContext(PatientManagementContext.instance);

  const onClosePopoverConfirm = () => {
    setConfirmOpen(false);
  };

  const renderTitleSchein = () => {
    if (typeSchein === MainGroup.IGEL) {
      return t(!id ? 'titleIgel' : 'updateTitleIgel');
    }
    return t(!id ? 'titlePrivate' : 'updateTitlePrivate');
  };

  const handleConfirmClose = () => {
    // TODO: we should use formik.dirty, but since FormContent component set formvalue directly, dirty is always = true
    if (!isEmpty(ref.current?.formik?.touched)) {
      setConfirmOpen(true);
    } else {
      onClose();
    }
  };

  return (
    <Flex className={props.className}>
      <Modal
        className={getCssClass(
          'bp5-dialog-fullscreen',
          'bp5-dialog-content-scrollable',
          className
        )}
        size={ModalSize.FULLSCREEN}
        title={renderTitleSchein()}
        isOpen={isOpen}
        canOutsideClickClose={false}
        isDisableModalBodyScroll
        onClose={handleConfirmClose}
        autoFocus
        enforceFocus={false}
        tabIndex={-1}
      >
        <ScheinContent
          ref={ref}
          isOpen={isOpen}
          patientId={patientId!}
          patientManagement={patientManagement}
          globalContext={globalContext}
          onCancel={handleConfirmClose}
          onClose={onClose}
          typeSchein={typeSchein}
          id={id}
        />
      </Modal>
      <LeaveConfirmModal
        isOpen={confirmOpen}
        onConfirm={() => {
          onClose();
          onClosePopoverConfirm();
        }}
        onClose={onClosePopoverConfirm}
      />
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(CreateScheinDialog, {
    namespace: 'Schein',
    nestedTrans: 'createSchein',
  })
);
