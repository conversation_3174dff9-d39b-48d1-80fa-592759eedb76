import { memo, useState, useEffect, useRef } from 'react';
import { Formik, Form, FormikProps } from 'formik';
import isEmpty from 'lodash/isEmpty';

import {
  BodyTextL,
  BodyTextS,
  Dialog,
  Flex,
} from '@tutum/design-system/components';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import {
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components/Toaster';
import {
  PrivateContractGroup,
  PrivateContractGroupItem,
  ValueAddTax,
} from '@tutum/hermes/bff/private_contract_group_common';
import {
  useMutationCreatePrivateContractGroup,
  useMutationDeletePrivateContractGroup,
  useMutationUpdatePrivateContractGroup,
} from '@tutum/hermes/bff/legacy/private_contract_group';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import {
  InputNumberValue,
  InputFloatNumberValue,
  InputValue,
} from '../ScheinComponents';
import {
  Button,
  Label,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import CommonLocales from '@tutum/admin/locales/en/Common.json';
import { onValidate } from '../schein.helper';
import { Error } from '@tutum/hermes/bff/legacy/private_contract_group_common';

export interface IModalContractGroupProps {
  className?: string;
  isOpen: boolean;
  isEdit: boolean;
  defaultValue?: PrivateContractGroupItem;
  onClose: (id?: string) => void;
  setValues: (values: PrivateContractGroupItem) => void;
}

const namespace = 'Schein';
const nestedTrans = 'ModalContractGroup';

const DEFAULT_FORM_VALUES = {
  contractNumber: 0,
  description: '',
  feeServiceFactor: 0,
  medicalServiceFactor: 0,
  additionalFactor: 0,
  technicalServiceFactor: 0,
  labServiceFactor: 0,
  group: '',
  valueAddTax: ValueAddTax.VAT_WithoutMwstsatz,
};

function ModalContractGroup(
  props: IModalContractGroupProps & II18nFixedNamespace
): React.FunctionComponentElement<IModalContractGroupProps> {
  const { className, isOpen, onClose, setValues, t, isEdit, defaultValue } =
    props;
  const formRef = useRef<FormikProps<PrivateContractGroup>>(null);
  const { t: tFormValidation } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });
  const [mwstsatz, setMwstsatz] = useState<ValueAddTax>(
    ValueAddTax.VAT_WithoutMwstsatz
  );
  const [confirmDialog, setConfirmDialog] = useState<boolean>(false);

  const normalizedData = (values: PrivateContractGroup) => {
    return {
      ...values,
      medicalServiceFactor: parseFloat(
        values.medicalServiceFactor.toString().replace(',', '.')
      ),
      technicalServiceFactor: parseFloat(
        values.technicalServiceFactor.toString().replace(',', '.')
      ),
      labServiceFactor: parseFloat(
        values.labServiceFactor.toString().replace(',', '.')
      ),
      feeServiceFactor: parseFloat(
        values.feeServiceFactor.toString().replace(',', '.')
      ),
      additionalFactor: parseFloat(
        values.additionalFactor.toString().replace(',', '.')
      ),
      valueAddTax: mwstsatz,
      contractNumber: values.contractNumber,
    };
  };

  const { mutate: mutateCreate } = useMutationCreatePrivateContractGroup({
    onSuccess: (resp) => {
      formRef.current?.setSubmitting(false);
      setValues(resp.data);
      alertSuccessfully(t('createSuccess'));
      onClose();
    },
    onError: (err) => {
      formRef.current?.setSubmitting(false);
      if (
        err?.response?.data?.['serverError'] ===
        Error.Error_Exist_Private_Contract_Group_Contract_Number
      ) {
        formRef?.current?.setErrors({
          ...formRef?.current?.errors,
          contractNumber: tFormValidation('existValue'),
        });
        return;
      }
      alertError(t('createError'));
    },
    throwOnError: false,
  });

  const { mutate: mutateUpdate } = useMutationUpdatePrivateContractGroup({
    onSuccess: (resp) => {
      formRef.current?.setSubmitting(false);
      setValues(resp.data);
      alertSuccessfully(t('updateSuccess'));
      onClose();
    },
    onError: (err) => {
      formRef.current?.setSubmitting(false);
      if (
        err?.response?.data?.['serverError'] ===
        Error.Error_Exist_Private_Contract_Group_Contract_Number
      ) {
        formRef?.current?.setErrors({
          ...formRef?.current?.errors,
          contractNumber: tFormValidation('existValue'),
        });

        return;
      }
      alertError(t('updateError'));
    },
    throwOnError: false,
  });

  const { mutate: mutateDelete } = useMutationDeletePrivateContractGroup({
    onSuccess: () => {
      alertSuccessfully(t('removeSuccess'));

      setConfirmDialog(false);
      onClose(defaultValue?.id);
    },
    onError: (err) => {
      let message = t('removeError');

      if (
        err?.response?.data?.['serverError'] ===
        Error.Error_Private_Contract_Group_Assigned_To_Schein
      ) {
        message = t('Error_Private_Contract_Group_Assigned_To_Schein');
        setConfirmDialog(false);
      }

      alertError(message);
    },
    throwOnError: false,
  });

  const onSubmit = (values: PrivateContractGroup) => {
    if (isEdit) {
      const payload: PrivateContractGroupItem = {
        contractGroup: normalizedData(values),
        id: defaultValue?.id!,
      };
      mutateUpdate(payload);
    } else {
      const payload: PrivateContractGroupItem = {
        contractGroup: normalizedData(values),
        id: null!,
      };
      mutateCreate(payload);
    }
  };

  const onConfirmRemoveRecords = () => {
    mutateDelete({
      id: defaultValue?.id!,
    });
  };

  useEffect(() => {
    if (defaultValue?.contractGroup?.valueAddTax) {
      setMwstsatz(defaultValue?.contractGroup?.valueAddTax);
    }
  }, [defaultValue]);

  return (
    <>
      <Formik<PrivateContractGroup>
        innerRef={formRef}
        initialValues={
          defaultValue ? defaultValue.contractGroup : DEFAULT_FORM_VALUES
        }
        enableReinitialize
        onSubmit={onSubmit}
        validate={onValidate(tFormValidation, t)}
      >
        {({ submitForm, errors, touched, submitCount, isSubmitting }) => (
          <Dialog
            size="half"
            className={className}
            style={{
              '--sl-dialog-width': '768px',
              '--sl-dialog-header-padding-inline': '16px',
              '--sl-dialog-body-overflow': 'auto',
              '--sl-dialog-header-padding-top': '16px',
            }}
            isOpen={isOpen}
            onClose={() => onClose()}
            title={isEdit ? t('titleEdit') : t('titleCreate')}
            actions={
              <Flex w="100%" justify="flex-end">
                <Button
                  intent="primary"
                  className="bp5-medium"
                  outlined
                  minimal
                  onClick={() => {
                    onClose();
                  }}
                  disabled={isSubmitting}
                >
                  {t('cancel')}
                </Button>
                {isEdit && (
                  <Button
                    outlined
                    minimal
                    intent="danger"
                    className="bp5-medium"
                    loading={isSubmitting}
                    onClick={() => setConfirmDialog(true)}
                  >
                    {t('remove')}
                  </Button>
                )}
                <Button
                  intent="primary"
                  className="bp5-medium"
                  loading={isSubmitting}
                  disabled={!isEmpty(errors) || isSubmitting}
                  onClick={submitForm}
                >
                  {isEdit ? t('saveForAllPatients') : t('create')}
                </Button>
              </Flex>
            }
          >
            <Form className="sl-form-body">
              <Flex className="section" gap={16}>
                <Flex className="half-row">
                  <InputNumberValue
                    nestedTrans={nestedTrans}
                    namespace={namespace}
                    idField="contractNumber"
                    titleField="contractNumber"
                    validationType="required"
                    submitCount={submitCount}
                    touched={touched}
                    errors={errors}
                    maxLength={8}
                    disable={isEdit}
                  />
                </Flex>
                <Flex className="row">
                  <InputValue
                    placeholder={t('enter')}
                    nestedTrans={nestedTrans}
                    namespace={namespace}
                    idField="description"
                    titleField="description"
                    validationType="required"
                    submitCount={submitCount}
                    touched={touched}
                    errors={errors}
                  />
                </Flex>
              </Flex>

              <Flex className="section-title" column>
                <Label name="valueAddTax" className="label">
                  <BodyTextS>{t('valueAddedTax')}</BodyTextS>
                </Label>
                <RadioGroup
                  onChange={(e) => {
                    setMwstsatz(e.currentTarget.value as ValueAddTax);
                  }}
                  inline
                  selectedValue={mwstsatz}
                >
                  <Radio
                    className="half-row"
                    label={t('withoutMwstastz')}
                    value={ValueAddTax.VAT_WithoutMwstsatz}
                  />
                  <Radio
                    label={t('withMwstastz')}
                    value={ValueAddTax.VAT_WithMwstsatz}
                  />
                </RadioGroup>
              </Flex>

              {/* section title increase factor */}
              <Flex className="section-title">
                <BodyTextL className="title-factor">
                  {t('increaseFactor')}
                </BodyTextL>
              </Flex>

              <Flex className="section" gap={16}>
                <Flex className="half-row">
                  <InputFloatNumberValue
                    nestedTrans={nestedTrans}
                    namespace={namespace}
                    idField="medicalServiceFactor"
                    titleField="medicalServiceFactor"
                    validationType="required"
                    submitCount={submitCount}
                    touched={touched}
                    errors={errors}
                  />
                </Flex>
                <Flex className="half-row">
                  <InputFloatNumberValue
                    nestedTrans={nestedTrans}
                    namespace={namespace}
                    idField="technicalServiceFactor"
                    titleField="technicalServiceFactor"
                    validationType="required"
                    submitCount={submitCount}
                    touched={touched}
                    errors={errors}
                  />
                </Flex>
                <Flex className="half-row">
                  <InputFloatNumberValue
                    nestedTrans={nestedTrans}
                    namespace={namespace}
                    idField="labServiceFactor"
                    titleField="labServiceFactor"
                    validationType="required"
                    submitCount={submitCount}
                    touched={touched}
                    errors={errors}
                  />
                </Flex>
              </Flex>

              <Flex gap={16}>
                <Flex className="half-row">
                  <InputFloatNumberValue
                    nestedTrans={nestedTrans}
                    namespace={namespace}
                    idField="feeServiceFactor"
                    titleField="feeServiceFactor"
                    validationType="required"
                    submitCount={submitCount}
                    touched={touched}
                    errors={errors}
                  />
                </Flex>
                <Flex className="half-row">
                  <InputFloatNumberValue
                    nestedTrans={nestedTrans}
                    namespace={namespace}
                    idField="additionalFactor"
                    titleField="additionalFactor"
                    validationType="required"
                    submitCount={submitCount}
                    touched={touched}
                    errors={errors}
                  />
                </Flex>
                <div className="half-row"></div>
              </Flex>
            </Form>
          </Dialog>
        )}
      </Formik>
      <DeleteConfirmDialog
        className="confirm-dialog"
        isOpen={confirmDialog}
        close={() => {
          setConfirmDialog(false);
        }}
        confirm={onConfirmRemoveRecords}
        text={{
          btnCancel: t('btnNo'),
          btnOk: t('btnRemove'),
          title: t('titleConfirmDialog'),
          message: t('contentConfirmDialog'),
        }}
      />
    </>
  );
}
export default memo(
  I18n.withTranslation(ModalContractGroup, {
    namespace: namespace,
    nestedTrans: nestedTrans,
  })
);
