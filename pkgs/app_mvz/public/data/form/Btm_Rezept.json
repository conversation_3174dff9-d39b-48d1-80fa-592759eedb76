{"formName": "Btm_Rezept.pdf", "meta": {"fields": [{"name": "toggle_6_bvg", "description": "bvg", "isReadOnly": false, "rect": {"x1": 772.562, "y1": 753.202, "x2": 815.001, "y2": 804.102}, "type": "TOGGLE_NUMBER", "displayValue": "6"}, {"name": "toggle_9_bedarf", "description": "<PERSON><PERSON><PERSON>", "isReadOnly": false, "rect": {"x1": 899.248, "y1": 751.455, "x2": 942.549, "y2": 803.043}, "type": "TOGGLE_NUMBER", "displayValue": "9"}, {"name": "label_unfalltag", "description": "Unfalltag", "isReadOnly": false, "rect": {"x1": 32.4918, "y1": 81.1334, "x2": 189.33, "y2": 20.1863}, "type": "LABEL"}, {"name": "label_unfallbetriebNummer", "description": "Unfallbetrieb oder Arbeitgebernummer", "isReadOnly": false, "rect": {"x1": 201.238, "y1": 20.1863, "x2": 599.235, "y2": 81.1334}, "type": "LABEL"}, {"name": "label_special_exceedings", "isReadOnly": false, "rect": {"x1": 859.183, "y1": 252.705, "x2": 930.215, "y2": 315.958}, "type": "LABEL"}, {"name": "label_doctor_stamp", "isReadOnly": true, "rect": {"x1": 948.754, "y1": 171.997, "x2": 1244.89, "y2": 406.323}, "type": "LABEL"}, {"name": "checkbox_autIdem1", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 334.108, "x2": 67.1123, "y2": 377.175}, "type": "CHECK_BOX"}, {"name": "checkbox_autIdem2", "description": "autIdem2", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 262.143, "x2": 67.1123, "y2": 309.377}, "type": "CHECK_BOX"}, {"name": "checkbox_autIdem3", "description": "autIdem3", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 187.85, "x2": 67.1123, "y2": 234.795}, "type": "CHECK_BOX"}, {"name": "checkbox_gebuhrfrei", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 761.637, "x2": 67.1123, "y2": 808.521}, "type": "CHECK_BOX"}, {"name": "checkbox_gebpfl", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 709.922, "x2": 67.1123, "y2": 757.274}, "type": "CHECK_BOX"}, {"name": "checkbox_noctu", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 656.306, "x2": 67.1123, "y2": 703.659}, "type": "CHECK_BOX"}, {"name": "checkbox_arbeitsunfall", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 436.535, "x2": 67.1123, "y2": 483.888}, "type": "CHECK_BOX"}, {"name": "label_insurance_name", "isReadOnly": true, "rect": {"x1": 79.8758, "y1": 807.83, "x2": 612.511, "y2": 779.582}, "type": "LABEL"}, {"name": "label_wop", "isReadOnly": true, "rect": {"x1": 629.019, "y1": 807.83, "x2": 748.721, "y2": 779.582}, "type": "LABEL"}, {"name": "label_patientInfo_line1", "isReadOnly": true, "rect": {"x1": 82.3201, "y1": 702.582, "x2": 751.658, "y2": 730.83}, "type": "LABEL"}, {"name": "label_patientInfo_line2", "isReadOnly": true, "rect": {"x1": 80.1562, "y1": 653.828, "x2": 612.851, "y2": 683.629}, "type": "LABEL"}, {"name": "label_date_of_birth", "isReadOnly": true, "rect": {"x1": 615.187, "y1": 653.828, "x2": 753.058, "y2": 683.629}, "type": "LABEL"}, {"name": "label_patientInfo_line3", "isReadOnly": true, "rect": {"x1": 82.2389, "y1": 620.443, "x2": 754.426, "y2": 650.454}, "type": "LABEL"}, {"name": "label_patientInfo_line4", "isReadOnly": true, "rect": {"x1": 81.5851, "y1": 588.674, "x2": 611.422, "y2": 619.659}, "type": "LABEL"}, {"name": "label_insurance_end_date", "isReadOnly": true, "rect": {"x1": 613.687, "y1": 588.674, "x2": 755.743, "y2": 619.659}, "type": "LABEL"}, {"name": "label_ik_number", "isReadOnly": true, "rect": {"x1": 81.2168, "y1": 525.888, "x2": 260.245, "y2": 561.999}, "type": "LABEL"}, {"name": "label_insurance_number", "isReadOnly": true, "rect": {"x1": 271.564, "y1": 527.073, "x2": 547.014, "y2": 558.058}, "type": "LABEL"}, {"name": "label_insurance_status", "isReadOnly": true, "rect": {"x1": 555.943, "y1": 527.073, "x2": 750.702, "y2": 559.243}, "type": "LABEL"}, {"name": "label_bsnr", "isReadOnly": true, "rect": {"x1": 80.8685, "y1": 457.892, "x2": 306.692, "y2": 490.061}, "type": "LABEL"}, {"name": "label_lanr", "isReadOnly": true, "rect": {"x1": 318.492, "y1": 457.797, "x2": 545.542, "y2": 487.598}, "type": "LABEL"}, {"name": "date_prescribe", "isReadOnly": true, "rect": {"x1": 554.174, "y1": 454.265, "x2": 749.754, "y2": 487.619}, "type": "DATE_PICKER"}, {"name": "checkbox_sonstige", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 582.26, "x2": 67.1123, "y2": 629.612}, "type": "CHECK_BOX"}, {"name": "checkbox_unfall", "isReadOnly": true, "rect": {"x1": 23.5079, "y1": 508.213, "x2": 67.1123, "y2": 555.565}, "type": "CHECK_BOX"}, {"name": "label_medication", "description": "medication", "isReadOnly": true, "rect": {"x1": 74.9852, "y1": 184.62, "x2": 846.698, "y2": 397.001}, "type": "LABEL"}, {"name": "label_medication4", "description": "medication", "isReadOnly": true, "rect": {"x1": 77.4163, "y1": 314.172, "x2": 846.559, "y2": 403.428}, "type": "LABEL"}, {"name": "label_medication1", "description": "medication", "isReadOnly": true, "rect": {"x1": 76.7358, "y1": 328.126, "x2": 845.582, "y2": 392.653}, "type": "LABEL"}, {"name": "label_medication5", "description": "medication", "isReadOnly": true, "rect": {"x1": 76.104, "y1": 222.202, "x2": 845.186, "y2": 310.337}, "type": "LABEL"}, {"name": "label_medication2", "description": "medication", "isReadOnly": true, "rect": {"x1": 75.9715, "y1": 251.622, "x2": 848.158, "y2": 317.341}, "type": "LABEL"}, {"name": "label_medication3", "description": "medication", "isReadOnly": true, "rect": {"x1": 76.6933, "y1": 185.283, "x2": 845.131, "y2": 249.097}, "type": "LABEL"}, {"name": "checkbox_begr", "description": "<PERSON><PERSON><PERSON>", "isReadOnly": true, "rect": {"x1": 957.967, "y1": 752.243, "x2": 1001.12, "y2": 803.583}, "type": "CHECK_BOX"}]}, "formPages": [{"width": 1255.0, "height": 879.0, "view": {"x1": 0.0, "y1": 0.0, "x2": 1255.0, "y2": 879.0}}]}