import React, { memo } from 'react';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { Flex } from '@tutum/design-system/components';
import { Divider } from '@tutum/design-system/components/Core';
import { groupBy, sortBy } from '@tutum/design-system/infrastructure/utils';
import { HintsAndWarning } from '@tutum/hermes/bff/app_mvz_medicine';

export interface IStandardHintsViewProps {
  className?: string;
  hintsAndWarnings?: HintsAndWarning[];
}

function StandardHintsViewMemo(
  props: IStandardHintsViewProps & IMvzThemeProps
) {
  const { className, hintsAndWarnings } = props;
  const sortByIndex = sortBy(hintsAndWarnings!, 'standardIndex', 'asc');
  const groupByIndex = groupBy(sortByIndex, (item) =>
    item.standardIndex.charAt(0)
  );
  return (
    <Flex column className={className}>
      {Object.keys(groupByIndex).map((key) => (
        <Flex key={key}>
          <Flex column>
            <Flex className="hint-header">
              {titleForStandardIndexCode(key)}
            </Flex>
            <Flex column>
              {groupByIndex[key].map((item) => (
                <Flex key={item.standardIndex}>{item.text}</Flex>
              ))}
            </Flex>
            <Flex pt={1}>
              <Divider />
            </Flex>
          </Flex>
        </Flex>
      ))}
    </Flex>
  );
}

function titleForStandardIndexCode(code) {
  switch (code) {
    case 'A':
      return 'Anwendung und Dosierung';
    case 'H':
      return 'Hilfsstoffe';
    case 'L':
      return 'Laktation (Stillzeit)';
    case 'S':
      return 'Schwangerschaft';
    case 'W':
      return 'Allgemeiner Hinweis oder Warnhinweis';
    default:
      return '';
  }
}

export default memo(StandardHintsViewMemo);
