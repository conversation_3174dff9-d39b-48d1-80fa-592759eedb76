import React, { memo, useCallback, useContext } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { Divider, Drawer, Tag } from '@tutum/design-system/components/Core';
import { Flex, Svg } from '@tutum/design-system/components';
import { medicationUtil } from '../../utils/medication-util';
import TogglePanel from '../toggle-panel/TogglePanel.styled';
import abmedUtls from '../../utils/abmed-utls';
import StandardHintsView from './standard-hints-view/StandardHintsView.styled';
import IngredientsView from './ingredients-view/IngredientsView.styled';
import MedicineDirectiveView from './medicine-directive-view/MedicineDirectiveView.styled';
import { isNotEmpty } from '@tutum/design-system/infrastructure/utils';
import MedicationContext from '../../context/MedicationContext';

const closeIcon = '/images/close.svg';

export interface ISecondaryMedicationViewProps {
  className?: string;
}

function SecondaryMedicationViewMemo(
  props: ISecondaryMedicationViewProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.SecondaryMedicationView>
) {
  const medicationContext = useContext(MedicationContext);
  const { className, t } = props;
  const { viewSecondaryMedicine, setViewSecondaryMedicine } = medicationContext;

  const requestClose = useCallback(() => {
    return setViewSecondaryMedicine(undefined);
  }, [setViewSecondaryMedicine]);

  if (!viewSecondaryMedicine) {
    return null;
  }

  const sideEffectText = viewSecondaryMedicine.textInformation?.items?.find(
    (item) => item.codeName === 'SIDEEFFECTS'
  )?.content;
  const regulationsText = viewSecondaryMedicine.textInformation?.items?.find(
    (item) => item.codeName === 'RESTRICTIONS'
  )?.content;
  const indicationText = viewSecondaryMedicine.textInformation?.items?.find(
    (item) => item.codeName === 'INDICATION'
  )?.content;
  const contrindicationText =
    viewSecondaryMedicine.textInformation?.items?.find(
      (item) => item.codeName === 'CONTRAINDICATION'
    )?.content;

  const renderSideEffects = sideEffectText && (
    <TogglePanel
      title={<Flex className="sl-toggle-header">{t('sideEffects')}</Flex>}
      content={
        <Flex
          column
          className="sl-toggle-panel-content"
          dangerouslySetInnerHTML={{
            __html: abmedUtls.format(sideEffectText),
          }}
        />
      }
    />
  );

  const renderStandardHints = viewSecondaryMedicine.hintsAndWarnings && (
    <TogglePanel
      title={<Flex className="sl-toggle-header">{t('hintsAndWarnings')}</Flex>}
      content={
        <StandardHintsView
          className="sl-toggle-panel-content"
          hintsAndWarnings={viewSecondaryMedicine.hintsAndWarnings}
        />
      }
    />
  );

  const renderRegulationNotice = regulationsText && (
    <TogglePanel
      title={<Flex className="sl-toggle-header">{t('regulations')}</Flex>}
      content={
        <Flex
          column
          className="sl-toggle-panel-content"
          dangerouslySetInnerHTML={{
            __html: abmedUtls.format(regulationsText),
          }}
        />
      }
    />
  );

  const renderIndications = indicationText && (
    <TogglePanel
      title={<Flex className="sl-toggle-header">{t('indications')}</Flex>}
      content={
        <Flex
          column
          className="sl-toggle-panel-content"
          dangerouslySetInnerHTML={{
            __html: abmedUtls.format(indicationText),
          }}
        />
      }
    />
  );
  const renderContraindications = contrindicationText && (
    <TogglePanel
      title={<Flex className="sl-toggle-header">{t('contraindications')}</Flex>}
      content={
        <Flex
          column
          dangerouslySetInnerHTML={{
            __html: abmedUtls.format(contrindicationText),
          }}
          className="sl-toggle-panel-content"
        />
      }
    />
  );

  const nonEmptyLegalNotes =
    viewSecondaryMedicine.productInformation?.legalNotes?.filter((legalNote) =>
      isNotEmpty(legalNote.text)
    ) || [];

  const renderMedicineDirective = nonEmptyLegalNotes.length > 0 && (
    <TogglePanel
      title={<Flex className="sl-toggle-header">{t('medicineDirective')}</Flex>}
      content={
        <MedicineDirectiveView
          className="sl-toggle-panel-content"
          legalNotes={nonEmptyLegalNotes}
        />
      }
    />
  );

  const renderIngredients = viewSecondaryMedicine.drugInformation
    ?.components && (
      <TogglePanel
        title={<Flex className="sl-toggle-header">{t('ingredients')}</Flex>}
        content={
          <IngredientsView
            components={viewSecondaryMedicine.drugInformation.components}
            className="sl-toggle-panel-content"
          />
        }
      />
    );

  return (
    <Drawer
      className={className}
      autoFocus
      isOpen={medicationContext.viewSecondaryMedicine !== undefined}
      canOutsideClickClose
      lazy
      size="480px"
      onClose={requestClose}
    >
      <Flex column className="sl-bg-gray">
        <Flex
          className="sl-trade-name-part"
          align="center"
          justify="space-between"
        >
          <Flex column>
            <Flex className="sl-header-medicine">
              {viewSecondaryMedicine.productInformation?.shortName}
            </Flex>
            <Flex>{`${viewSecondaryMedicine.drugInformation?.aTCA} • ${viewSecondaryMedicine.pzn}`}</Flex>
          </Flex>
          <Svg
            onClick={requestClose}
            className="sl-close-icon"
            src={closeIcon}
          />
        </Flex>
        <Flex flexWrap className="sl-tag-list">
          {viewSecondaryMedicine.productInformation?.importReimport && (
            <Tag large round>
              {t('importOrReImportProduct')}
            </Tag>
          )}
          {viewSecondaryMedicine.productInformation?.productGroup === 2 && (
            <Tag round large>
              {t('medicalDevice')}
            </Tag>
          )}
          {viewSecondaryMedicine.productInformation?.documentRequired === 2 && (
            <Tag round large>
              {t('tfg')}
            </Tag>
          )}
        </Flex>
      </Flex>
      <Flex column className="sl-content-panel">
        <Flex className="sl-price-group" justify="space-between">
          <Flex column>
            <Flex>{t('coPayment')}</Flex>
            <Flex className="sl-price">
              {medicationUtil.formatPrice(
                viewSecondaryMedicine.priceInformation?.copayment!
              )}
            </Flex>
          </Flex>
          <Flex column>
            <Flex>{t('furterCost')}</Flex>
            <Flex align="center" className="sl-price">
              {medicationUtil.formatPrice(
                viewSecondaryMedicine.priceInformation?.additionalCost!
              )}
            </Flex>
          </Flex>
          <Flex column>
            <Flex>{t('totalPayment')}</Flex>
            <Flex className="sl-price">
              {medicationUtil.formatPrice(
                viewSecondaryMedicine.priceInformation?.totalPayment!
              )}
            </Flex>
          </Flex>
        </Flex>
        <Divider />
        <Flex className="sl-panel-scrollable" column>
          {renderMedicineDirective}
          {renderSideEffects}
          {renderRegulationNotice}
          {renderContraindications}
          {renderIndications}
          {renderStandardHints}
          {renderIngredients}
        </Flex>
      </Flex>
    </Drawer>
  );
}

export default memo(
  I18n.withTranslation(SecondaryMedicationViewMemo, {
    namespace: 'Medication',
    nestedTrans: 'SecondaryMedicationView',
  })
);
