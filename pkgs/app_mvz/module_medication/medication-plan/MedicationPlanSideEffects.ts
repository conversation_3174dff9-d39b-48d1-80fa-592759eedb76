import match from 'conditional-expression';
import {
  addEntry,
  addKeytab,
  deleteEntry,
  getBmpList,
  getSubheadingsList,
  saveMergeResult,
  saveSortedIdList,
  updateEntry,
} from '@tutum/hermes/bff/legacy/app_mvz_bmp';
import { deleteMedicationPlan } from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { alertSuccessfully } from '@tutum/design-system/components';
import { ACTION_TYPES } from '@tutum/mvz/module_medication/medication-plan/MedicationPlan.reducer';
import {
  GroupType,
  KeytabType,
  MedicationPlanGroupCompared,
} from '@tutum/hermes/bff/service_domains_bmp';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export const showSuccessMessage = (options) => {
  const { t } = options;
  alertSuccessfully(t('Medication:MedicationPlan.updateSuccess'));
};

export const showSuccessMergeMessage = (options) => {
  const { t } = options;
  alertSuccessfully(t('Medication:MedicationPlan.mergedSuccess'));
};

export const handleAddEntry = (dispatch, action, options) => {
  addEntry({
    ...action.data,
  })
    .then((newPlanItem) => {
      showSuccessMessage(options);
      dispatch({
        type: ACTION_TYPES.ADD_NEW_ENTRY_SUCCESS,
        newPlanItem: newPlanItem.data,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.ADD_NEW_ENTRY_FAILURE,
        error,
      });
    });
};
export const handleUpdateEntry = (dispatch, action, options) => {
  return updateEntry({
    ...action.data,
  })
    .then((updatedEntry) => {
      showSuccessMessage(options);
      dispatch({
        type: ACTION_TYPES.UPDATE_ENTRY_SUCCESS,
        updatedPlanItem: updatedEntry.data,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.UPDATE_ENTRY_FAILURE,
        error,
      });
    });
};

export const handleRemoveEntry = (dispatch, action, options) => {
  return deleteEntry({
    ...action.data,
  })
    .then(() => {
      showSuccessMessage(options);
      dispatch({
        type: ACTION_TYPES.REMOVE_ENTRY_SUCCESS,
        id: action.data.entryId,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.REMOVE_ENTRY_FAILURE,
        error,
      });
    });
};

export const handleRemoveEntryMedication = (dispatch, action, options) => {
  return deleteMedicationPlan({
    ...action.data,
  })
    .then(() => {
      showSuccessMessage(options);
      dispatch({
        type: ACTION_TYPES.REMOVE_ENTRY_SUCCESS,
        id: action.data.entryId,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.REMOVE_ENTRY_FAILURE,
        error,
      });
    })
    .finally(() => {
      handleFetchMedicationPlans(dispatch, {
        patientId: action.data.patientId,
        isPrivateSchein: action.data.isPrivateSchein,
      });
    });
};

export const handleFetchMedicationPlans = (dispatch, action) => {
  getBmpList({
    patientId: action.patientId,
    isPrivateSchein: action.isPrivateSchein,
  })
    .then((medicationPlan) => {
      dispatch({
        type: ACTION_TYPES.FETCH_MEDICATION_PLANS_SUCCESS,
        medicationPlan: medicationPlan.data,
        additionalData: medicationPlan.data.additionalData,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.FETCH_MEDICATION_PLANS_FAILURE,
        error,
      });
    });
};

export const handleUpdateSortOrder = (dispatch, action) => {
  saveSortedIdList({
    patientId: action.patientId,
    medicationPlanGroupIds: action.planItems.map((planItem) => planItem.id),
  })
    .then(() => {
      dispatch({
        type: ACTION_TYPES.UPDATE_SORT_ORDER_SUCCESS,
        planItems: action.planItems,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.UPDATE_SORT_ORDER_FAILURE,
        error,
      });
    });
};

export const handleFetchSubheadings = (dispatch) => {
  getSubheadingsList()
    .then((subheading) => {
      dispatch({
        type: ACTION_TYPES.FETCH_SUBHEADINGS_SUCCESS,
        subheadings: subheading.data.planKeytabs,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.FETCH_SUBHEADINGS_FAILURE,
        error,
      });
    });
};

export const handleAddNewSubheading = (dispatch, action) => {
  const { name } = action;
  const newSubheading = {
    type: KeytabType.K_SUBHEADING,
    name,
    code: '',
  };
  addKeytab(newSubheading)
    .then(() => {
      dispatch({
        type: ACTION_TYPES.ADD_NEW_SUBHEADING_SUCCESS,
        newSubheading,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.ADD_NEW_SUBHEADING_FAILURE,
        error,
      });
    });
};

const convertConflictDataToMedicationPlanItem = (
  data: MedicationPlanGroupCompared
) => {
  if (data.type === GroupType.FREETEXT) {
    const freetextInfo = data.freeTextInformation;
    return {
      id: data.id,
      subHeading: null,
      medicationInformation: null,
      type: GroupType.FREETEXT,
      recipeInformation: null,
      freeTextInformation: freetextInfo,
    };
  }
  if (data.type === GroupType.RECIPE) {
    const recipeInfo = data.recipeInformation;
    return {
      id: data.id,
      subHeading: null,
      medicationInformation: null,
      type: GroupType.RECIPE,
      recipeInformation: recipeInfo,
      freeTextInformation: null,
    };
  }
  if (data.type === GroupType.SUBHEADING) {
    const subHeading = data.subHeading;
    return {
      id: data.id,
      subHeading,
      medicationInformation: null,
      type: GroupType.SUBHEADING,
      recipeInformation: null,
      freeTextInformation: null,
    };
  }
  if (data.type === GroupType.MEDICATION) {
    const medicationInformation = data.medicationInformation;
    return {
      id: data.id,
      subHeading: null,
      medicationInformation: {
        formType: null,
        quantity: null,
        intakeInterval: {
          morning: medicationInformation?.intakeInterval?.morning
            ? medicationInformation.intakeInterval.morning[0]
            : null,
          afternoon: medicationInformation?.intakeInterval?.afternoon
            ? medicationInformation.intakeInterval.afternoon[0]
            : null,
          evening: medicationInformation?.intakeInterval?.evening
            ? medicationInformation.intakeInterval.evening[0]
            : null,
          night: medicationInformation?.intakeInterval?.night
            ? medicationInformation.intakeInterval.night[0]
            : null,
          freetext: medicationInformation?.intakeInterval?.freetext
            ? medicationInformation.intakeInterval.freetext[0]
            : null,
        },
        drugInformation: medicationInformation?.drugInformation,
        pzn: medicationInformation?.pzn,
        substances: medicationInformation?.substances,
        tradeName: medicationInformation?.tradeName,
        drugForm: medicationInformation?.drugForm,
        drugFormDescription: medicationInformation?.drugFormDescription,
        unit: medicationInformation?.unit,
        unitDescription: medicationInformation?.unitDescription,
        hint: medicationInformation?.hint[0],
        reason: medicationInformation?.reason[0],
        additionalLine: medicationInformation?.additionalLine[0],
        modifiedDate: datetimeUtil.now(),
        productInformation: medicationInformation?.productInformation,
        isEditMedication: medicationInformation?.isEditMedication,
        isOutDatePzn: !!medicationInformation?.isEditMedication
          ? false
          : medicationInformation?.isOutDatePzn,
      },
      type: GroupType.MEDICATION,
      recipeInformation: null,
      freeTextInformation: null,
    };
  }
};

export const handleMergeEntries = (dispatch, action, options) => {
  return saveMergeResult({
    patientId: action.patientId,
    currentMedicationPlanSelected:
      action?.data?.currentMedicationPlan?.map((bmp) =>
        convertConflictDataToMedicationPlanItem(bmp)
      ) || [],
    mergeMedicationPlanSelected:
      action?.data?.mergeMedicationPlan?.map((bmp) =>
        convertConflictDataToMedicationPlanItem(bmp)
      ) || [],
    additionalData: action?.data?.additionalData,
  })
    .then((response) => {
      showSuccessMergeMessage(options);
      dispatch({
        type: ACTION_TYPES.MERGE_ENTRIES_SUCCESS,
        medicationPlanItems: response.data.medicationPlanGroups,
        additionalData: response.data.additionalData,
      });
    })
    .catch((error) => {
      console.error(error);
      dispatch({
        type: ACTION_TYPES.MERGE_ENTRIES_FAILURE,
        error,
      });
    });
};

export const applyMedicationPlanSideEffectsMiddleWare =
  (dispatch, options) => (action) => {
    dispatch(action);
    match(action.type)
      .equals(ACTION_TYPES.ADD_NEW_ENTRY)
      .then(() => handleAddEntry(dispatch, action, options))
      .equals(ACTION_TYPES.FETCH_MEDICATION_PLANS)
      .then(() => handleFetchMedicationPlans(dispatch, action))
      .equals(ACTION_TYPES.UPDATE_SORT_ORDER)
      .then(() => handleUpdateSortOrder(dispatch, action))
      .equals(ACTION_TYPES.UPDATE_ENTRY)
      .then(() => handleUpdateEntry(dispatch, action, options))
      .equals(ACTION_TYPES.REMOVE_ENTRY)
      .then(() => handleRemoveEntry(dispatch, action, options))
      .equals(ACTION_TYPES.REMOVE_ENTRY_MEDICATION)
      .then(() => handleRemoveEntryMedication(dispatch, action, options))
      .equals(ACTION_TYPES.FETCH_SUBHEADINGS)
      .then(() => handleFetchSubheadings(dispatch))
      .equals(ACTION_TYPES.ADD_NEW_SUBHEADING)
      .then(() => handleAddNewSubheading(dispatch, action))
      .equals(ACTION_TYPES.MERGE_ENTRIES)
      .then(() => handleMergeEntries(dispatch, action, options))
      .else(null);
  };
