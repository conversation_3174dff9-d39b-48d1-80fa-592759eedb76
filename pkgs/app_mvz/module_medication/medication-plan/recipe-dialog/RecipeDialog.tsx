import React, { memo } from 'react';
import { isEmpty } from 'lodash';
import { Form, Formik, Field } from 'formik';
import useTranslation from 'next-translate/useTranslation';
import {
  Dialog,
  Classes,
  Button,
  Intent,
  TextArea,
  Label,
  Divider,
} from '@tutum/design-system/components/Core';
import ValidationFormGroup from '@tutum/mvz/components/validation-form-group/ValidationFormGroup';
import { BodyTextS } from '@tutum/design-system/components';
import { MedicationPlanGroup } from '@tutum/hermes/bff/repo_bmp_common';
import {
  handlePasteValue,
  parseContentToRead,
  parseContentToWrite,
} from '@tutum/mvz/module_medication/medication-plan/MedicationPlan.helper';

export interface IRecipeDialogProps {
  className?: string;
  isOpen: boolean;
  close: () => void;
  saveRecipe: (
    content,
    additionalLine,
    medicationItem?: MedicationPlanGroup
  ) => void;
  medicationPlanItem?: MedicationPlanGroup;
}

const renderRecipeForm = (props, errors, t) => {
  const { close } = props;
  const validateContent = (content) => {
    if (isEmpty(content)) {
      return t('MedicationPlan.requiredField');
    }
    return null;
  };

  const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === '~') {
      e.preventDefault();
    }
  };

  return (
    <Form>
      <div className={`${Classes.DIALOG_BODY} recipe-dialog__body`}>
        <ValidationFormGroup errors={errors} fieldName="content">
          <Label name="content">
            <BodyTextS fontWeight={500}>
              {t('MedicationPlan.recipe').toUpperCase()}
            </BodyTextS>
          </Label>
          <Field name="content" validate={validateContent}>
            {({ form, field }) => {
              const intent = errors.content ? Intent.DANGER : Intent.NONE;
              return (
                <TextArea
                  intent={intent}
                  {...field}
                  className={Classes.FILL}
                  rows={5}
                  maxLength={200}
                  onKeyDown={onKeyDown}
                  onChange={(e) => {
                    const value = handlePasteValue(
                      e.currentTarget.value,
                      200
                    ).replaceAll('\t', ' ');
                    form.setFieldValue(field.name, value);
                  }}
                />
              );
            }}
          </Field>
        </ValidationFormGroup>
        <ValidationFormGroup errors={errors} fieldName="additionalLine">
          <Label name="content">
            <BodyTextS fontWeight={500}>
              {t('MedicationPlan.additionalLine').toUpperCase()}
              <span className={Classes.TEXT_MUTED}>
                {' '}
                ({t('MedicationPlan.optional').toUpperCase()})
              </span>
            </BodyTextS>
          </Label>
          <Field name="additionalLine">
            {({ form, field }) => {
              return (
                <TextArea
                  {...field}
                  className={Classes.FILL}
                  rows={5}
                  maxLength={200}
                  onKeyDown={onKeyDown}
                  onChange={(e) => {
                    const value = handlePasteValue(
                      e.currentTarget.value,
                      200
                    ).replaceAll('\t', ' ');
                    form.setFieldValue(field.name, value);
                  }}
                />
              );
            }}
          </Field>
        </ValidationFormGroup>
      </div>
      <Divider />
      <div className={`${Classes.DIALOG_FOOTER} recipe-dialog__footer`}>
        <div
          className={`${Classes.DIALOG_FOOTER_ACTIONS} recipe-dialog__footer-actions`}
        >
          <Button
            className={`${Classes.FILL} secondary`}
            onClick={close}
            intent={Intent.PRIMARY}
          >
            {t('MedicationPlan.cancel')}
          </Button>
          <Button
            className={Classes.FILL}
            intent={Intent.PRIMARY}
            type="submit"
          >
            {t('MedicationPlan.save')}
          </Button>
        </div>
      </div>
    </Form>
  );
};

const RecipeDialog = (props: IRecipeDialogProps) => {
  const { t } = useTranslation('Medication');
  const { className, isOpen, close, saveRecipe, medicationPlanItem } = props;
  const title = isEmpty(medicationPlanItem)
    ? t('MedicationPlan.addNewRecipe')
    : t('MedicationPlan.editRecipe');
  const initialValues = isEmpty(medicationPlanItem)
    ? {
      content: '',
      additionalLine: '',
    }
    : {
      content: parseContentToRead(
        medicationPlanItem.recipeInformation?.content!
      ),
      additionalLine: parseContentToRead(
        medicationPlanItem.recipeInformation?.additionalLine!
      ),
    };
  const save = (formValues) => {
    saveRecipe(
      parseContentToWrite(formValues.content),
      parseContentToWrite(formValues.additionalLine),
      medicationPlanItem
    );
    close();
  };
  return (
    <Dialog
      className={`${className} dialog-right`}
      title={title}
      isOpen={isOpen}
      onClose={close}
      canOutsideClickClose={false}
    >
      <Formik
        onSubmit={save}
        initialValues={initialValues}
        render={({ errors }) => renderRecipeForm(props, errors, t)}
      />
    </Dialog>
  );
};

export default memo(RecipeDialog);
