import React from 'react';
import { isEmpty } from 'lodash';
import Cell from '@tutum/mvz/module_medication/medication-plan/medication-plan-table/cell/Cell.styled';
import { getInformationString } from '../medication-row/MedicationRow';

export interface IIntakeIntervalCellProps {
  className?: string;
  intakeInterval: any;
  isMergeModal?: boolean;
}

const IntakeIntervalCell = (props) => {
  const { intakeInterval, className, isMergeModal } = props;
  if (isEmpty(intakeInterval)) {
    return (
      <Cell
        className={`${className} free-text`}
        width={`${isMergeModal ? '260px' : '180px'}`}
        minWidth={`${isMergeModal ? '260px' : '180px'}`}
        maxWidth={`${isMergeModal ? '260px' : '180px'}`}
      />
    );
  }
  const { morning, afternoon, evening, night, freetext } = intakeInterval;
  
  if (morning && afternoon && evening && night) {
    return (
      <Cell
        className={className}
        width={`${isMergeModal ? '260px' : '180px'}`}
        minWidth={`${isMergeModal ? '260px' : '180px'}`}
        maxWidth={`${isMergeModal ? '260px' : '180px'}`}
      >
        <div className="intake-interval">{morning ? getInformationString(morning) : 0}</div>
        <div className="intake-interval">{afternoon ? getInformationString(afternoon) : 0}</div>
        <div className="intake-interval">{evening ? getInformationString(evening) : 0}</div>
        <div className="intake-interval">{night ? getInformationString(night) : 0}</div>
      </Cell>
    );
  }

  return (
    <Cell
      className={`${className} free-text`}
      width={`${isMergeModal ? '260px' : '180px'}`}
      minWidth={`${isMergeModal ? '260px' : '180px'}`}
      maxWidth={`${isMergeModal ? '260px' : '180px'}`}
    >
      {getInformationString(freetext)}
    </Cell>
  );
};

export default IntakeIntervalCell;
