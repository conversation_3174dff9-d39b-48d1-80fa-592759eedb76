import React, { useState, useEffect, useRef, useContext } from 'react';
import moment from 'moment';
import { isEmpty, isEqual } from 'lodash';
import { useRouter } from 'next/router';
import {
  Button,
  Classes,
  Dialog,
  Intent,
  Radio,
} from '@tutum/design-system/components/Core';
import { alertWarning, alertError } from '@tutum/design-system/components';
import {
  parseBarcode,
  parseBarcodeForUnmatch,
  compareWithBmpEvent,
  useListenResultCompareBmp,
} from '@tutum/hermes/bff/app_mvz_bmp';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import {
  EntryMergeStatus,
  GroupType,
  MedicationPlanGroupCompared,
  MedicationPlanGroupComparedStatus,
  ParseBarcodeResponse,
  ScannedError,
  ScannedPatientInfo,
  ScannedErrorType,
  PatientInfomationBarcode,
} from '@tutum/hermes/bff/service_domains_bmp';
import { BodyTextM, Flex, H3, Svg } from '@tutum/design-system/components';
import useTranslation from 'next-translate/useTranslation';
import {
  patientFileActions,
  patientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import deConvert from 'convert-layout/de';
import debounce from 'lodash/debounce';
import {
  DATE_FORMAT,
  DATE_BMP_FORM_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { IMetaDataMergeEntries } from '../MedicationPlan';
import { AdditionalData } from '@tutum/hermes/bff/repo_bmp_common';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import { InitalAdditionalData } from '../MedicationPlan';

const alertIcon = '/images/alert-circle-solid-big.svg';
const successIcon = '/images/check-circle-solid-2.svg';
const barCodeScannerIcon = '/images/barcode-scanner.svg';

export const UNMATCHED_TYPE = {
  SCANNER: 'scanner',
  CURRENT_PATIENT: 'current-patient',
};

export interface IScanDialog {
  className?: string;
  isOpen: boolean;
  close: () => void;
  doctorId: string | undefined;
  patient: IPatientProfile | undefined;
  isEmptyMedicationPlanItems: boolean;
  openMergeDialog: (
    data: MedicationPlanGroupComparedStatus[],
    metaData: IMetaDataMergeEntries
  ) => void;
  mergeEntries: (
    current: MedicationPlanGroupCompared[],
    merge: MedicationPlanGroupCompared[],
    metaData?: IMetaDataMergeEntries
  ) => void;
  setSessionId: React.Dispatch<React.SetStateAction<string>>;
  sessionId?: string;
  continuePatient: string;
  additionalData: AdditionalData | undefined;
}

// TODO: @DucDo update insurance number optional
const ScanDialog = (props: IScanDialog) => {
  const {
    className,
    isOpen,
    close,
    doctorId,
    patient,
    isEmptyMedicationPlanItems,
    openMergeDialog,
    mergeEntries,
    setSessionId,
    sessionId,
    continuePatient,
    additionalData,
  } = props;
  const [patientInfoBarcode, setPatientInfoBarcode] =
    useState<PatientInfomationBarcode>();
  const [conflictFields, setConflictFields] = useState<{
    [key: string]: string[];
  }>({});
  const [result, setResult] = useState<ParseBarcodeResponse | undefined>(undefined);
  const [barcodeContent, setBarcodeContent] = useState<string>('');
  const [isScanning, setScanning] = useState<boolean>(
    !!patientFileStore.barcodeMedicationPlanContent
  );
  const [isUnmatchedPatientFlow, setUnmatchedPatientFlow] =
    useState<boolean>(false);
  const [isScanComplete, setScanComplete] = useState(false);
  const [isScanNextPage, setScanNextPage] = useState<boolean>(false);
  const [currentPatient, setCurrentPatient] = useState<string | undefined>(continuePatient);
  const { t } = useTranslation('Medication');
  const router = useRouter();
  const dataReader = useRef<string | undefined>('');
  const isPreviousAltGr = useRef<boolean>(false);
  const timesRun = useRef<number>(0);
  const timeouRef = useRef<NodeJS.Timeout>();
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();
  const comboKeys = useRef<string[]>([]);

  useListenResultCompareBmp((data) => {
    if (
      data.userId == currentLoggedinUser?.id &&
      data.patientId == patient?.id
    ) {
      setScanning(false);
      setSessionId(data.sessionId);
      if (
        isEmptyMedicationPlanItems &&
        isEqual(
          { ...InitalAdditionalData, ...data.additionalData, gender: 'm' },
          { ...InitalAdditionalData, ...additionalData, gender: 'm' }
        )
      ) {
        const medicationPlanItems = data.medicationPlanGroupCompared.map(
          (m) => m.medicationPlanGroupCompared
        );
        const countPznNotFoundMMI = data.medicationPlanGroupCompared.filter(
          (m) => m.status.includes(EntryMergeStatus.PZN_NOT_FOUND)
        ).length;

        mergeEntries([], medicationPlanItems, {
          countPznNotFoundMMI,
          additionalData: data.additionalData,
        });
        return;
      }

      const hasNewData = data.medicationPlanGroupCompared
        .filter(
          (medicationCompared) =>
            medicationCompared.medicationPlanGroupCompared.type !==
            GroupType.SUBHEADING
        )
        .some(
          (medicationCompared) =>
            medicationCompared.status.includes(EntryMergeStatus.CONFLICT) ||
            medicationCompared.status.includes(EntryMergeStatus.NEW)
        );
      const allMatched = data.medicationPlanGroupCompared.some((mc) =>
        mc.status.includes(EntryMergeStatus.MATCHED)
      );
      if (
        !hasNewData &&
        allMatched &&
        isEqual(
          { ...InitalAdditionalData, ...data.additionalData, gender: 'm' },
          { ...InitalAdditionalData, ...additionalData, gender: 'm' }
        )
      ) {
        setScanComplete(true);
        return;
      }
      openMergeDialog(data.medicationPlanGroupCompared, {
        additionalData: data.additionalData,
      });
      closeModal();
    }
  });

  const stopsession = debounce(() => {
    try {
      if (dataReader.current) {
        if (checkIsGermanLayoutKeyboard(dataReader.current)) {
          dataReader.current = deConvert
            .toEn(dataReader.current)
            .replaceAll('AltGraph', '');
        }
        setBarcodeContent(
          dataReader.current?.match(/<MP.+<\/MP>/g)?.[0]?.replaceAll('–', '') || ''
        );
        dataReader.current = '';
      }
    } catch (err) {
      alertWarning(t('MedicationPlan.timeout'));
      dataReader.current = '';
      setBarcodeContent('');
      setScanning(false);
    }
  }, 2000);

  const closeModal = () => {
    setResult(undefined);
    setScanComplete(false);
    patientFileActions.setBarcodeMedicationPlanContent('', '', '');
    close();
  };
  const compareData = (sessionId: string) => {
    setScanning(true);
    compareWithBmpEvent({
      mergeSessionId: sessionId,
      patientId: patientFileStore?.patient?.current?.id!,
      isPrivateSchein: false,
    });
  };

  const checkIsGermanLayoutKeyboard = (barcode: string) => {
    if (barcode.includes('´Ä') || barcode.includes('-:')) {
      return true;
    }
    return false;
  };

  const scanBarCodeContinueFromOtherPatient = (
    barcodeContent: string,
    currentPatient: string
  ) => {
    setScanning(true);
    if (barcodeContent == '') {
      return;
    }
    let data = barcodeContent;
    if (checkIsGermanLayoutKeyboard(barcodeContent)) {
      data = deConvert.toEn(barcodeContent);
    }
    const method = parseBarcodeForUnmatch;

    method({
      pageContent: data,
      doctorId: doctorId!,
      patientId: currentPatient || patient?.id!,
    })
      .then((response) => {
        setScanning(false);
        if (response.patientInfomationBarcode) {
          setPatientInfoBarcode(response.patientInfomationBarcode);
        }
        setSessionId(response.sessionId);
        compareData(response.sessionId);
      })
      .catch((error) => {
        alertError(t('MedicationPlan.scanFailedMessage'));
        throw error;
      })
      .finally(() => {
        setScanning(false);
        setBarcodeContent('');
      });
  };

  const scanBarCode = (barcodeContent: string, currentPatient: string) => {
    if (barcodeContent == '') {
      return;
    }
    let data = barcodeContent;
    if (checkIsGermanLayoutKeyboard(barcodeContent)) {
      data = deConvert.toEn(barcodeContent);
    }
    const method = isUnmatchedPatientFlow
      ? parseBarcodeForUnmatch
      : parseBarcode;

    method({
      pageContent: data,
      doctorId: doctorId!,
      patientId: currentPatient || patient?.id!,
    })
      .then((response) => {
        setScanning(false);
        if (response.patientInfomationBarcode) {
          setPatientInfoBarcode(response.patientInfomationBarcode);
        }
        setSessionId(response.sessionId);
        if (isEmpty(response.scannedError)) {
          if (!isEmpty(result) && result.sessionId !== response.sessionId) {
            alertError(t('MedicationPlan.scanFailedMessage'));
            return;
          }

          setScanNextPage(false);

          if (response.isFullyScan) {
            compareData(response.sessionId);

            return;
          }
          setResult(response);
          return;
        }
        if (
          isEmpty(result) &&
          (response.scannedError.errorType ===
            ScannedErrorType.UNMATCH_PATIENT ||
            response.scannedError.errorType ===
            ScannedErrorType.PATIENT_NOT_EXIST)
        ) {
          setScanNextPage(false);
          setResult(response);
          setCurrentPatient(
            response.scannedError.matchedPatientInfos?.find(
              (info) => info.patientId !== patient?.id
            )?.patientId
          );
          if (response.patientInfomationBarcode) {
            const newconflictFields = { ...conflictFields };
            const patientInfo = response.patientInfomationBarcode;
            newconflictFields['default'] = [];
            response?.scannedError?.matchedPatientInfos?.forEach((mp) => {
              if (mp.firstName !== patientInfo.firstName) {
                if (newconflictFields[mp.patientId!]) {
                  newconflictFields[mp.patientId!].push('firstName');
                } else {
                  newconflictFields[mp.patientId!] = ['firstName'];
                }
                newconflictFields['default'].push('firstName');
              }
              if (mp.lastName !== patientInfo.lastName) {
                if (newconflictFields[mp.patientId!]) {
                  newconflictFields[mp.patientId!].push('lastName');
                } else {
                  newconflictFields[mp.patientId!] = ['lastName'];
                }
                newconflictFields['default'].push('lastName');
              }
              if (
                mp.birthday !== patientInfo.dob &&
                moment(mp.birthday, 'YYYYMMDD').format(DATE_FORMAT) !==
                patientInfo.dob
              ) {
                if (newconflictFields[mp.patientId!]) {
                  newconflictFields[mp.patientId!].push('birthday');
                } else {
                  newconflictFields[mp.patientId!] = ['birthday'];
                }
                newconflictFields['default'].push('birthday');
              }
            });
            setConflictFields(newconflictFields);
          }
          return;
        }
        alertError(t('MedicationPlan.scanFailedMessage'));
      })
      .catch((error) => {
        setScanning(false);
        setBarcodeContent('');
        alertError(t('MedicationPlan.scanFailedMessage'));
        throw error;
      });
  };

  const handleContinueUnmatchedPatient = async () => {
    if (currentPatient && currentPatient !== patient?.id) {
      setScanning(true);
      patientFileActions.setBarcodeMedicationPlanContent(
        barcodeContent,
        sessionId!,
        currentPatient
      );
      router.push(`/patients/${currentPatient}${ID_TABS.MEDICATION}`);

      return;
    }

    setResult(undefined);
    setUnmatchedPatientFlow(true);
  };

  const handleScanNextPage = () => {
    setBarcodeContent('');
    setScanNextPage(true);
  };

  const startScanContent = (
    <div className={`${Classes.DIALOG_BODY} scan-dialog__body`}>
      <H3 className="title">
        <Svg className="status-icon" src={alertIcon} />
        {t('MedicationPlan.startScanTitle')}
      </H3>
      <Flex align="flex-start" column>
        <p>{t('MedicationPlan.startScanDescription')}</p>
        <Svg className="scanner-icon" src={barCodeScannerIcon} />
      </Flex>
      <Button
        className={`${Classes.FILL} secondary stop-button bold-text`}
        intent={Intent.PRIMARY}
        disabled={isScanning}
        loading={isScanning}
        onClick={
          isScanNextPage ? () => compareData(result?.sessionId!) : closeModal
        }
      >
        {isScanNextPage
          ? t('MedicationPlan.stopScanningAndUseScannedPage')
          : t('MedicationPlan.stopScanning')}
      </Button>
    </div>
  );

  const renderScanningContent = (result: ParseBarcodeResponse) => (
    <div className={`${Classes.DIALOG_BODY} scan-dialog__body`}>
      <H3 className="title">
        <Svg className="status-icon" src={successIcon} />
        {t('MedicationPlan.scanningTitle', {
          scannedPages: result.scannedPages,
          totalPages: result.totalPages,
        })}
      </H3>
      <Flex align="flex-start" column>
        <p>
          {t('MedicationPlan.scanningDescription', {
            totalPages: result.totalPages,
          })}
        </p>
        <Svg className="scanner-icon" src={barCodeScannerIcon} />
      </Flex>
      <div className={Classes.DIALOG_FOOTER_ACTIONS}>
        <Button
          className={`${Classes.FILL} secondary bold-text`}
          intent={Intent.PRIMARY}
          disabled={isScanning}
          loading={isScanning}
          onClick={() => compareData(result.sessionId)}
        >
          {t('MedicationPlan.useScannedPages')}
        </Button>
        <Button
          className={`${Classes.FILL} bold-text`}
          intent={Intent.PRIMARY}
          disabled={isScanning}
          loading={isScanning}
          onClick={handleScanNextPage}
        >
          {t('MedicationPlan.scanNextPage')}
        </Button>
      </div>
    </div>
  );

  const getPatientDescription = ({
    firstName,
    lastName,
    birthday,
    insuranceNumber,
  }: {
    firstName: string;
    lastName: string;
    birthday: string;
    insuranceNumber: string;
  }) => {
    return (
      <BodyTextM>
        <b>{`${firstName} ${lastName}`}</b>
        {` (${moment(birthday, DATE_BMP_FORM_FORMAT).format(DATE_FORMAT)}${insuranceNumber ? ` | ${insuranceNumber}` : ''
          })`}
      </BodyTextM>
    );
  };

  const renderUnmatchedPatientBody = (
    matchedPatientInfos: ScannedPatientInfo[]
  ) => {
    const scannedPatientInfoBirthDay = moment(
      patientInfoBarcode?.dob,
      'YYYYMMDD'
    );
    let scannedBirthDay = scannedPatientInfoBirthDay.format(DATE_FORMAT);
    if (!scannedPatientInfoBirthDay.isValid()) {
      if (patientInfoBarcode?.dob.length == 8) {
        scannedBirthDay = `${patientInfoBarcode?.dob?.slice(
          6,
          8
        )}.${patientInfoBarcode?.dob?.slice(
          4,
          6
        )}.${patientInfoBarcode?.dob?.slice(0, 4)}`;
      } else {
        scannedBirthDay = '00.00.0000';
      }
    }
    return (
      <div>
        <BodyTextM margin="0 0 16px">
          {t('MedicationPlan.unmatchedPatientMessage')}
          <span>
            &nbsp;
            <span>
              <b>{patientInfoBarcode?.firstName}</b>
            </span>
            {', '}
            <span>
              <b>{patientInfoBarcode?.lastName} </b>
            </span>
            (<span>{scannedBirthDay}</span>
            {!!patientInfoBarcode?.insuranceNumber && (
              <>
                {' '}
                | <span>{patientInfoBarcode?.insuranceNumber}</span>
              </>
            )}
            )
          </span>
        </BodyTextM>
        <Flex>
          <Radio
            className="scan-dialog__group-unmatched-patient"
            checked={currentPatient === patient?.id}
            value={patient?.id}
            labelElement={
              <div className="scan-dialog__group-unmatched-patient__content">
                <BodyTextM>
                  {t('MedicationPlan.unmatchedPatientDescriptionForCurrent')}
                </BodyTextM>
                <BodyTextM>
                  <span
                    className={getCssClass({
                      conflict: patientInfoBarcode
                        ? patient?.lastName != patientInfoBarcode.lastName
                        : false,
                    })}
                  >
                    <b>{patient?.lastName}</b>
                  </span>
                  {', '}
                  <span
                    className={getCssClass({
                      conflict: patientInfoBarcode
                        ? patient?.firstName != patientInfoBarcode.firstName
                        : false,
                    })}
                  >
                    <b>{patient?.firstName}</b>
                  </span>{' '}
                  (
                  <span
                    className={getCssClass({
                      conflict: patientInfoBarcode
                        ? getDateOfBirth(
                          patient?.patientInfo.personalInfo.dateOfBirth
                        )?.value != scannedBirthDay
                        : false,
                    })}
                  >
                    {getDateOfBirth(
                      patient?.patientInfo.personalInfo.dateOfBirth
                    )?.value ?? '00.00.0000'}
                  </span>
                  {' | '}
                  <span>
                    {patientFileStore.activeInsurance?.insuranceNumber}
                  </span>
                  )
                </BodyTextM>
              </div>
            }
            onChange={(e) => {
              setCurrentPatient(e.currentTarget.value);
            }}
          />
        </Flex>
        {matchedPatientInfos.map((matchedPatient) => {
          if (matchedPatient.patientId === patient?.id) {
            return null;
          }

          const clFields = conflictFields[matchedPatient.patientId || ''];
          const scannedPatientInfoBirthDay = moment(
            matchedPatient?.birthday,
            'YYYYMMDD'
          );
          let scannedBirthDay = scannedPatientInfoBirthDay.format(DATE_FORMAT);
          if (!scannedPatientInfoBirthDay.isValid()) {
            if (matchedPatient?.birthday.length == 8) {
              scannedBirthDay = `${matchedPatient?.birthday?.slice(
                6,
                8
              )}.${matchedPatient?.birthday?.slice(
                4,
                6
              )}.${matchedPatient?.birthday?.slice(0, 4)}`;
            } else {
              scannedBirthDay = '00.00.0000';
            }
          }
          return (
            <Flex key={matchedPatient.patientId}>
              <Radio
                className="scan-dialog__group-unmatched-patient"
                checked={currentPatient === matchedPatient.patientId}
                value={matchedPatient.patientId}
                labelElement={
                  <div className="scan-dialog__group-unmatched-patient__content">
                    <BodyTextM>
                      {t(
                        'MedicationPlan.unmatchedPatientDescriptionForScanner'
                      )}
                    </BodyTextM>
                    <BodyTextM>
                      <span
                        className={getCssClass({
                          conflict: clFields?.includes('firstName'),
                        })}
                      >
                        <b>{matchedPatient.lastName}</b>
                      </span>
                      {', '}
                      <span
                        className={getCssClass({
                          conflict: clFields?.includes('lastName'),
                        })}
                      >
                        <b>{matchedPatient.firstName}</b>
                      </span>{' '}
                      (
                      <span
                        className={getCssClass({
                          conflict: clFields?.includes('birthday'),
                        })}
                      >
                        {scannedBirthDay}
                      </span>
                      {!!matchedPatient.insuranceNumber && (
                        <>
                          {' '}
                          | <span>{matchedPatient.insuranceNumber}</span>
                        </>
                      )}
                      )
                    </BodyTextM>
                  </div>
                }
                onChange={(e) => {
                  setCurrentPatient(e.currentTarget.value);
                }}
              />
            </Flex>
          );
        })}
      </div>
    );
  };

  const renderUnmatchedPatientBodyNoExistedCase = (
    scannedPatientInfo: ScannedPatientInfo
  ) => {
    const scannedPatientInfoBirthDay = moment(
      scannedPatientInfo?.birthday,
      'YYYYMMDD'
    );
    let scannedBirthDay = scannedPatientInfoBirthDay.format(DATE_FORMAT);
    if (!scannedPatientInfoBirthDay.isValid()) {
      if (scannedPatientInfo?.birthday.length == 8) {
        scannedBirthDay = `${scannedPatientInfo?.birthday?.slice(
          6,
          8
        )}.${scannedPatientInfo?.birthday?.slice(
          4,
          6
        )}.${scannedPatientInfo?.birthday?.slice(0, 4)}`;
      } else {
        scannedBirthDay = '00.00.0000';
      }
    }
    return (
      <div>
        <BodyTextM margin="0 0 16px">
          {t('MedicationPlan.unmatchedPatientMessageNoExisted')}
          <span>
            &nbsp;
            <span
              className={getCssClass({
                conflict: scannedPatientInfo
                  ? patient?.lastName != scannedPatientInfo.lastName
                  : false,
              })}
            >
              <b>{scannedPatientInfo.lastName}</b>
            </span>
            {', '}
            <span
              className={getCssClass({
                conflict: scannedPatientInfo
                  ? patient?.firstName != scannedPatientInfo.firstName
                  : false,
              })}
            >
              <b>{scannedPatientInfo.firstName}</b>
            </span>{' '}
            (
            <span
              className={getCssClass({
                conflict: scannedPatientInfo
                  ? getDateOfBirth(patient?.patientInfo.personalInfo.dateOfBirth)
                    ?.value != scannedBirthDay
                  : false,
              })}
            >
              {scannedBirthDay}
            </span>
            {scannedPatientInfo.insuranceNumber && (
              <>
                {' | '}
                <span>{scannedPatientInfo.insuranceNumber}</span>
              </>
            )}
            )
          </span>
        </BodyTextM>
        <div className="scan-dialog__group-unmatched-patient__content">
          <BodyTextM>
            {t('MedicationPlan.unmatchedPatientDescriptionForCurrent')}
          </BodyTextM>
          <BodyTextM>
            <span
              className={getCssClass({
                conflict: scannedPatientInfo
                  ? patient?.lastName != scannedPatientInfo.lastName
                  : false,
              })}
            >
              <b>{patient?.lastName}</b>
            </span>
            {', '}
            <span
              className={getCssClass({
                conflict: scannedPatientInfo
                  ? patient?.firstName != scannedPatientInfo.firstName
                  : false,
              })}
            >
              <b>{patient?.firstName}</b>
            </span>{' '}
            (
            <span
              className={getCssClass({
                conflict: scannedPatientInfo
                  ? getDateOfBirth(patient?.patientInfo.personalInfo.dateOfBirth)
                    ?.value != scannedBirthDay
                  : false,
              })}
            >
              {getDateOfBirth(patient?.patientInfo.personalInfo.dateOfBirth)
                ?.value ?? '00.00.0000'}
            </span>
            {' | '}
            <span>{patientFileStore.activeInsurance?.insuranceNumber}</span>)
          </BodyTextM>
        </div>
      </div>
    );
  };

  const renderUnmatchedPatientContent = ({
    scannedPatientInfo,
    matchedPatientInfos,
  }: ScannedError) => (
    <div className={`${Classes.DIALOG_BODY} scan-dialog__body`}>
      <H3 className="title">
        <Svg className="status-icon" src={alertIcon} />
        {t('MedicationPlan.unmatchedPatient')}
      </H3>
      {matchedPatientInfos?.[0]
        ? renderUnmatchedPatientBody(matchedPatientInfos)
        : renderUnmatchedPatientBodyNoExistedCase(scannedPatientInfo)}
      <div className={Classes.DIALOG_FOOTER_ACTIONS}>
        <Button
          className={`${Classes.FILL} secondary bold-text`}
          intent={Intent.PRIMARY}
          disabled={isScanning}
          loading={isScanning}
          onClick={closeModal}
        >
          {t('MedicationPlan.cancel')}
        </Button>
        <Button
          className={`${Classes.FILL} bold-text`}
          intent={Intent.PRIMARY}
          disabled={isScanning}
          loading={isScanning}
          onClick={handleContinueUnmatchedPatient}
        >
          {t('MedicationPlan.scanAgain')}
        </Button>
      </div>
    </div>
  );

  const scanCompleteContent = (
    <div className={`${Classes.DIALOG_BODY} scan-dialog__body`}>
      <H3 className="title">
        <Svg className="status-icon" src={successIcon} />
        {t('MedicationPlan.scanCompleteTitle')}
      </H3>
      <div>
        <p>{t('MedicationPlan.scanCompleteDescription')}</p>
      </div>
      <Button
        intent={Intent.PRIMARY}
        className={`secondary ${Classes.FILL} stop-button bold-text`}
        onClick={closeModal}
      >
        {t('MedicationPlan.backToMP')}
      </Button>
    </div>
  );

  const handleScanBarcode = (e) => {
    timesRun.current += 1;
    const textInput = e.key;
    if (comboKeys.current.length == 5) {
      comboKeys.current.shift();
    }
    comboKeys.current.push(e.key);

    if (textInput === 'Esc' || textInput === 'Escape') {
      dataReader.current = '';
      return;
    }

    if (['Alt', 'AltGraph'].includes(textInput)) {
      isPreviousAltGr.current = true;
    }

    if (textInput === 'Enter') {
      e.preventDefault();
      setBarcodeContent(dataReader.current || '');
      dataReader.current = '';

      clearTimeout(timeouRef.current);
      return;
    }

    if (textInput === 'Tab') {
      dataReader.current += ' ';

      return;
    }

    if (
      ![
        'CapsLock',
        'Control',
        'Meta',
        'Shift',
        'Clear',
        'Alt',
        'AltGraph',
      ].includes(textInput)
    ) {
      setScanning(true);
      if (textInput == 'Dead') {
        dataReader.current += `<`;
      } else if (textInput == '°') {
        dataReader.current += `>`;
      } else if (textInput == 'Unidentified') {
        dataReader.current += '@';
      } else if (textInput == '–') {
        if (isPreviousAltGr.current) {
          dataReader.current += '~';
        } else {
          dataReader.current += '';
        }
      } else {
        if (isPreviousAltGr.current) {
          if (textInput == 'n') {
            dataReader.current += '~';
          } else if (textInput == 'r') {
            dataReader.current += '®';
          }
        } else {
          switch (JSON.stringify(comboKeys.current)) {
            case JSON.stringify(['Alt', '0', '1', '8', '9']):
              dataReader.current = dataReader.current?.slice(0, -2) + '½';
              break;
            case JSON.stringify(['Alt', '0', '1', '7', '4']):
              dataReader.current = dataReader.current?.slice(0, -2) + '®';
              break;
            case JSON.stringify(['Alt', '0', '1', '8', '8']):
              dataReader.current = dataReader.current?.slice(0, -2) + '¼';
              break;
            case JSON.stringify(['Alt', '0', '1', '9', '0']):
              dataReader.current = dataReader.current?.slice(0, -2) + '¾';
              break;
            default:
              dataReader.current += textInput;
              break;
          }
        }
      }
      isPreviousAltGr.current = false;
    }
    if (dataReader.current) {
      stopsession();
    }
  };
  useEffect(() => {
    if (!isOpen || isScanComplete || !isEmpty(result?.scannedError)) {
      document.removeEventListener('keydown', handleScanBarcode);
      return;
    }
    document.addEventListener('keydown', handleScanBarcode);
    return () => {
      document.removeEventListener('keydown', handleScanBarcode);
    };
  }, [isOpen, isScanComplete, isScanNextPage, result]);

  useEffect(() => {
    if (
      patientFileStore.barcodeMedicationPlanContent &&
      currentPatient == patient?.id
    ) {
      scanBarCodeContinueFromOtherPatient(
        patientFileStore.barcodeMedicationPlanContent,
        patient?.id!
      );
      patientFileActions.setBarcodeMedicationPlanContent('', '', '');
    }
  }, [
    patientFileStore.barcodeMedicationPlanContent,
    patientFileStore.sessionId,
    continuePatient,
  ]);

  useEffect(() => {
    if (barcodeContent) {
      scanBarCode(
        barcodeContent,
        isUnmatchedPatientFlow ? currentPatient! : patient?.id!
      );
    }
  }, [barcodeContent, isUnmatchedPatientFlow]);

  if (isScanComplete) {
    return (
      <Dialog
        className={className}
        isOpen={isOpen}
        onClose={close}
        canOutsideClickClose={false}
      >
        {scanCompleteContent}
      </Dialog>
    );
  }

  if (!result || isScanNextPage) {
    return (
      <Dialog
        className={className}
        isOpen={isOpen}
        onClose={close}
        canOutsideClickClose={false}
      >
        {startScanContent}
      </Dialog>
    );
  }
  if (!isEmpty(result.scannedError)) {
    return (
      <Dialog
        className={className}
        isOpen={isOpen}
        onClose={close}
        canOutsideClickClose={false}
      >
        {renderUnmatchedPatientContent(result.scannedError)}
      </Dialog>
    );
  }
  return (
    <Dialog
      className={className}
      isOpen={isOpen}
      onClose={close}
      canOutsideClickClose={false}
    >
      {renderScanningContent(result)}
    </Dialog>
  );
};

export default ScanDialog;
