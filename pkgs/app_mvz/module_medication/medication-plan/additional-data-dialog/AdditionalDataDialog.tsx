import { OverflowNode } from '@lexical/overflow';
import { <PERSON>Field, Field, Form, Formik, FormikErrors } from 'formik';
import useTranslation from 'next-translate/useTranslation';
import React, { memo, useContext } from 'react';

import {
  BodyTextS,
  Svg,
  alertSuccessfully,
  alertWarning,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import {
  Button,
  Checkbox,
  Classes,
  Dialog,
  InputGroup,
  Intent,
  Label,
} from '@tutum/design-system/components/Core';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { MaxCharactersPlugin } from '@tutum/design-system/lexical/plugins/MaxCharactersPlugin';
import MvzTextmodule from '@tutum/design-system/textmodule';
import { AvoidCharacterPlugin } from '@tutum/design-system/textmodule/plugins/AvoidCharacter.plugin';
import { NoBreakLinePlugin } from '@tutum/design-system/textmodule/plugins/NoBreakLine.plugin';
import { PatientProfileResponse } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { AdditionalData } from '@tutum/hermes/bff/repo_bmp_common';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import ValidationFormGroup from '@tutum/mvz/components/validation-form-group/ValidationFormGroup';
import useToaster from '@tutum/mvz/hooks/useToaster';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { Translate } from 'next-translate';
import { Gender } from '@tutum/hermes/bff/patient_profile_common';

const Refresh = '/images/refresh-cw.svg';
export interface IAdditionalDataProps {
  className?: string;
  isOpen: boolean;
  close: () => void;
  saveAdditionalData: (content?: AdditionalData) => void;
  additionalData?: AdditionalData;
}

const usedForList = [
  TextModuleUseFor.TextModuleUseFor_Note,
  TextModuleUseFor.TextModuleUseFor_Anamnesis,
  TextModuleUseFor.TextModuleUseFor_Findings,
  TextModuleUseFor.TextModuleUseFor_Therapy,
];

const renderAdditionalDataForm = ({
  props,
  errors,
  patient,
  t,
  setFieldValue,
}: {
  props: IAdditionalDataProps;
  errors: FormikErrors<AdditionalData>;
  patient: PatientProfileResponse | undefined;
  t: Translate;
  setFieldValue: (
    field: string,
    value: any,
    shouldValidate?: boolean
  ) => Promise<void | FormikErrors<AdditionalData>>;
}) => {
  const { close } = props;
  const toaster = useToaster();
  const patientMedicalData = patient?.patientMedicalData;
  const onSyncViatalParameter = () => {
    if (!patientMedicalData) {
      alertWarning(t('MedicationPlan.syncVitalParameterFail'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
        toaster,
      });
      return;
    }
    setFieldValue(
      'allergies',
      (patientMedicalData.allergies || []).map((a) => a.allergy).join(', '),
      true
    );
    setFieldValue('weight', patientMedicalData.weight, true);
    setFieldValue('height', patientMedicalData.height, true);
    setFieldValue('creatinine', patientMedicalData.creatinine, true);
    alertSuccessfully(t('MedicationPlan.syncVitalParameterSuccessfully'), {
      timeout: TOASTER_TIMEOUT_CUSTOM,
      toaster,
    });
  };
  return (
    <Form>
      <div className={`${Classes.DIALOG_BODY} additional-data-dialog__body`}>
        <Button
          minimal
          outlined
          icon={<Svg src={Refresh} />}
          onClick={onSyncViatalParameter}
        >
          {t('MedicationPlan.syncVitalParameter')}
        </Button>
        <ValidationFormGroup errors={errors} fieldName="isShowGender">
          <Field name="isShowGender">
            {({ field, form }) => {
              return (
                <Checkbox
                  checked={!!field?.value}
                  label={t('MedicationPlan.gender')}
                  onChange={() => {
                    form.setFieldValue(field.name, !!!field?.value);
                  }}
                />
              );
            }}
          </Field>
        </ValidationFormGroup>
        {[Gender.W, Gender.X, Gender.D, Gender.U].includes(
          patient?.patientInfo?.personalInfo?.gender!
        ) && (
            <React.Fragment>
              <ValidationFormGroup errors={errors} fieldName="isPregnant">
                <Field name="isPregnant">
                  {({ field, form }) => {
                    return (
                      <Checkbox
                        checked={!!field?.value}
                        label={t('MedicationPlan.pregnant')}
                        onChange={() => {
                          form.setFieldValue(field.name, !!!field?.value);
                        }}
                      />
                    );
                  }}
                </Field>
              </ValidationFormGroup>
              <ValidationFormGroup errors={errors} fieldName="isBreastFeeding">
                <Field name="isBreastFeeding">
                  {({ field, form }) => {
                    return (
                      <Checkbox
                        checked={!!field?.value}
                        label={t('MedicationPlan.breastfeeding')}
                        onChange={() => {
                          form.setFieldValue(field.name, !!!field?.value);
                        }}
                      />
                    );
                  }}
                </Field>
              </ValidationFormGroup>
            </React.Fragment>
          )}
        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="allergies">
              <Label name="allergies">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('MedicationPlan.allergies').toUpperCase()}
                </BodyTextS>
              </Label>
              <Field name="allergies">
                {({ field, form }) => {
                  return (
                    <InputGroup
                      {...field}
                      data-tab-id={field.name}
                      id={'allergies'}
                      fill
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
        </div>
        <div className="row">
          <div className="col-sm-6">
            <ValidationFormGroup errors={errors} fieldName="weight">
              <Label name="weight">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('MedicationPlan.weight').toUpperCase()}
                </BodyTextS>
              </Label>
              <Field name="weight">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      isFloat
                      placeholder="0"
                      defaultValue={field.value}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('kg')}
                        </span>
                      }
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
          <div className="col-sm-6">
            <ValidationFormGroup errors={errors} fieldName="height">
              <Label name="height">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('MedicationPlan.height').toUpperCase()}
                </BodyTextS>
              </Label>
              <Field name="height">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      isFloat
                      placeholder="0"
                      defaultValue={field.value}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('cm')}
                        </span>
                      }
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
        </div>
        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="creatinine">
              <Label name="creatinine">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('MedicationPlan.creatinine').toUpperCase()}
                </BodyTextS>
              </Label>
              <FastField name="creatinine">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      isFloat
                      placeholder="0,00"
                      defaultValue={field.value}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('mg/dl')}
                        </span>
                      }
                    />
                  );
                }}
              </FastField>
            </ValidationFormGroup>
          </div>
        </div>
        <ValidationFormGroup errors={errors} fieldName="additionalValue">
          <Label name="additionalValue">
            <BodyTextS fontWeight={500}>
              {t('MedicationPlan.additionalData').toUpperCase()}
            </BodyTextS>
          </Label>
          <Field name="additionalValue">
            {({ field, form }) => {
              return (
                <MvzTextmodule
                  {...field}
                  className={getCssClass('sl-editable-text', {
                    hasError: !!errors['content'],
                  })}
                  nodes={[OverflowNode]}
                  isInDialog
                  usedForList={usedForList}
                  value={field.value}
                  onContentChange={({ text }) => {
                    form.setFieldValue(field.name, text.replaceAll('\n', ' '));
                  }}
                >
                  <AvoidCharacterPlugin value={field.value} character="~" />
                  <MaxCharactersPlugin maxCharacters={200} />
                  <NoBreakLinePlugin />
                </MvzTextmodule>
              );
            }}
          </Field>
        </ValidationFormGroup>
      </div>
      <div
        className={`${Classes.DIALOG_FOOTER} additional-data-dialog__footer`}
      >
        <div
          className={`${Classes.DIALOG_FOOTER_ACTIONS} additional-data-dialog__footer-actions`}
        >
          <Button
            className={`${Classes.FILL} secondary`}
            onClick={close}
            intent={Intent.PRIMARY}
          >
            {t('MedicationPlan.cancel')}
          </Button>
          <Button
            className={Classes.FILL}
            intent={Intent.PRIMARY}
            type="submit"
          >
            {t('MedicationPlan.save')}
          </Button>
        </div>
      </div>
    </Form>
  );
};

const AdditionalDataDialog = (props: IAdditionalDataProps) => {
  const { t } = useTranslation('Medication');
  const { className, isOpen, close, saveAdditionalData, additionalData } =
    props;
  const initialValue =
    additionalData ??
    ({
      isShowGender: false,
      isPregnant: false,
      isBreastFeeding: false,
    } as AdditionalData);

  const { patientManagement } = useContext(PatientManagementContext.instance);
  const patient = patientManagement?.patient;

  const save = (formValues) => {
    saveAdditionalData(formValues);
    close();
  };

  return (
    <Dialog
      className={`${className} dialog-right`}
      title={t(
        !!additionalData
          ? 'MedicationPlan.editAdditionalData'
          : 'MedicationPlan.addAdditionalData'
      )}
      isOpen={isOpen}
      onClose={close}
      canOutsideClickClose={false}
    >
      <Formik<AdditionalData>
        onSubmit={save}
        initialValues={initialValue}
        render={({ errors, setFieldValue }) =>
          renderAdditionalDataForm({ props, errors, patient, t, setFieldValue })
        }
      />
    </Dialog>
  );
};

export default memo(AdditionalDataDialog);
