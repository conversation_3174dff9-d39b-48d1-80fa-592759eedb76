import React, { memo } from 'react';
import { isEmpty, get } from 'lodash';
import { Form, Formik, Field } from 'formik';
import { OverflowNode } from '@lexical/overflow';
import useTranslation from 'next-translate/useTranslation';

import { MedicationPlanGroup } from '@tutum/hermes/bff/repo_bmp_common';
import {
  Dialog,
  Classes,
  Button,
  Intent,
  Label,
  Divider,
} from '@tutum/design-system/components/Core';
import { BodyTextS } from '@tutum/design-system/components';
import ValidationFormGroup from '@tutum/mvz/components/validation-form-group/ValidationFormGroup';
import MvzTextmodule from '@tutum/design-system/textmodule';
import { AvoidCharacterPlugin } from '@tutum/design-system/textmodule/plugins/AvoidCharacter.plugin';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { MaxCharactersPlugin } from '@tutum/design-system/lexical/plugins/MaxCharactersPlugin';
import { NoBreakLinePlugin } from '@tutum/design-system/textmodule/plugins/NoBreakLine.plugin';
import {
  handlePasteValue,
  parseContentToRead,
  parseContentToWrite,
} from '@tutum/mvz/module_medication/medication-plan/MedicationPlan.helper';

export interface IFreeTextProps {
  className?: string;
  isOpen: boolean;
  close: () => void;
  saveFreeText: (content, medicationPlanItem?) => void;
  medicationPlanItem?: MedicationPlanGroup;
}

const usedForList = [TextModuleUseFor.TextModuleUseFor_BMP];

const renderFreeTextForm = (props, errors, t) => {
  const { close } = props;
  const validateContent = (content) => {
    if (isEmpty(content)) {
      return t('MedicationPlan.requiredField');
    }
    return null;
  };
  return (
    <Form>
      <div className={`${Classes.DIALOG_BODY} free-text-dialog__body`}>
        <ValidationFormGroup errors={errors} fieldName="content">
          <Label name="content">
            <BodyTextS fontWeight={500}>
              {t('MedicationPlan.freetext').toUpperCase()}
            </BodyTextS>
          </Label>
          <Field name="content" validate={validateContent}>
            {({ field, form }) => {
              const isHasBreakLine = !!(
                field.value?.includes('\n') || field.value?.includes('~')
              );
              return (
                <MvzTextmodule
                  {...field}
                  className={getCssClass('sl-editable-text', {
                    hasError: !!errors.content,
                  })}
                  nodes={[OverflowNode]}
                  isInDialog
                  usedForList={usedForList}
                  value={field.value}
                  onContentChange={({ text }) => {
                    const value = handlePasteValue(text, 200).replaceAll(
                      '\t',
                      ' '
                    );
                    form.setFieldValue(field.name, value);
                  }}
                  isBottomHeadMenu={true}
                >
                  <AvoidCharacterPlugin character="~" />
                  <MaxCharactersPlugin maxCharacters={200} />
                  {isHasBreakLine && <NoBreakLinePlugin />}
                </MvzTextmodule>
              );
            }}
          </Field>
        </ValidationFormGroup>
      </div>
      <Divider />
      <div className={`${Classes.DIALOG_FOOTER} free-text-dialog__footer`}>
        <div
          className={`${Classes.DIALOG_FOOTER_ACTIONS} free-text-dialog__footer-actions`}
        >
          <Button
            className={`${Classes.FILL} secondary`}
            onClick={close}
            intent={Intent.PRIMARY}
          >
            {t('MedicationPlan.cancel')}
          </Button>
          <Button
            className={Classes.FILL}
            intent={Intent.PRIMARY}
            type="submit"
          >
            {t('MedicationPlan.save')}
          </Button>
        </div>
      </div>
    </Form>
  );
};

const FreeTextDialog = (props: IFreeTextProps) => {
  const { t } = useTranslation('Medication');
  const { className, isOpen, close, saveFreeText, medicationPlanItem } = props;
  const title = isEmpty(medicationPlanItem)
    ? t('MedicationPlan.addNewFreeText')
    : t('MedicationPlan.editFreeText');
  const content = isEmpty(medicationPlanItem)
    ? ''
    : get(medicationPlanItem, 'freeTextInformation.content');
  const save = (formValues) => {
    const content = parseContentToWrite(formValues.content);
    saveFreeText(content, medicationPlanItem);
    close();
  };
  return (
    <Dialog
      className={`${className} dialog-right`}
      title={title}
      isOpen={isOpen}
      onClose={close}
      canOutsideClickClose={false}
    >
      <Formik
        onSubmit={save}
        initialValues={{
          content: parseContentToRead(content!),
        }}
        render={({ errors }) => renderFreeTextForm(props, errors, t)}
      />
    </Dialog>
  );
};

export default memo(FreeTextDialog);
