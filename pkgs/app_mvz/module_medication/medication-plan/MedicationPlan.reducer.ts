import { useReducer, Dispatch } from 'react';
import useTranslation from 'next-translate/useTranslation';
import match from 'conditional-expression';
import { applyMedicationPlanSideEffectsMiddleWare } from '@tutum/mvz/module_medication/medication-plan/MedicationPlanSideEffects';
import { MedicationPlanGroup } from '@tutum/hermes/bff/repo_bmp_common';
import { BmpMedicationPlanKeytab } from '@tutum/hermes/bff/service_domains_bmp';
import { AdditionalData } from "@tutum/hermes/bff/repo_bmp_common"
import { checkEmptyValueAdditionalData } from "./MedicationPlan.helper"

export const ACTION_TYPES = {
  FETCH_MEDICATION_PLANS: 'FETCH_MEDICATION_PLANS',
  FETCH_MEDICATION_PLANS_SUCCESS: 'FETCH_MEDICATION_PLANS_SUCCESS',
  FETCH_MEDICATION_PLANS_FAILURE: 'FETCH_MEDICATION_PLANS_FAILURE',
  UPDATE_SORT_ORDER: 'UPDATE_SORT_ORDER',
  UPDATE_SORT_ORDER_SUCCESS: 'UPDATE_SORT_ORDER_SUCCESS',
  UPDATE_SORT_ORDER_FAILURE: 'UPDATE_SORT_ORDER_FAILURE',
  ADD_NEW_ENTRY: 'ADD_NEW_ENTRY',
  ADD_NEW_ENTRY_SUCCESS: 'ADD_NEW_ENTRY_SUCCESS',
  ADD_NEW_ENTRY_FAILURE: 'ADD_NEW_ENTRY_FAILURE',
  UPDATE_ENTRY: 'UPDATE_ENTRY',
  UPDATE_ENTRY_SUCCESS: 'UPDATE_ENTRY_SUCCESS',
  UPDATE_ENTRY_FAILURE: 'UPDATE_ENTRY_FAILURE',
  REMOVE_ENTRY: 'REMOVE_ENTRY',
  REMOVE_ENTRY_MEDICATION: 'REMOVE_ENTRY_MEDICATION',
  REMOVE_ENTRY_SUCCESS: 'REMOVE_ENTRY_SUCCESS',
  REMOVE_ENTRY_FAILURE: 'REMOVE_ENTRY_FAILURE',
  FETCH_SUBHEADINGS: 'FETCH_SUBHEADINGS',
  FETCH_SUBHEADINGS_SUCCESS: 'FETCH_SUBHEADINGS_SUCCESS',
  FETCH_SUBHEADINGS_FAILURE: 'FETCH_SUBHEADINGS_FAILURE',
  ADD_NEW_SUBHEADING: 'ADD_NEW_SUBHEADING',
  ADD_NEW_SUBHEADING_SUCCESS: 'ADD_NEW_SUBHEADING_SUCCESS',
  ADD_NEW_SUBHEADING_FAILURE: 'ADD_NEW_SUBHEADING_FAILURE',
  MERGE_ENTRIES: 'MERGE_ENTRIES',
  MERGE_ENTRIES_SUCCESS: 'MERGE_ENTRIES_SUCCESS',
  MERGE_ENTRIES_FAILURE: 'MERGE_ENTRIES_FAILURE',
  SET_LOADING: 'SET_LOADING',
};

interface IMedicationPlan {
  medicationPlanId?: string;
  medicationPlanGroups: MedicationPlanGroup[];
}

export interface IState {
  loading: boolean;
  medicationPlan: IMedicationPlan;
  subheadings: BmpMedicationPlanKeytab[];
  additionalData: AdditionalData | undefined;
}

export const initialState: IState = {
  medicationPlan: {
    medicationPlanGroups: [],
  },
  additionalData: undefined,
  subheadings: [],
  loading: true,
};

export const medicationPlanReducer = (state = initialState, action) =>
  match(action.type)
    .equals(ACTION_TYPES.SET_LOADING)
    .then({
      ...state,
      loading: action.isLoading ?? false,
    })
    .equals(ACTION_TYPES.FETCH_MEDICATION_PLANS)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.FETCH_MEDICATION_PLANS_SUCCESS)
    .then({
      ...state,
      loading: false,
      medicationPlan: action.medicationPlan,
      additionalData: !checkEmptyValueAdditionalData(action.additionalData) ? action.additionalData : undefined,
    })
    .equals(ACTION_TYPES.FETCH_MEDICATION_PLANS_FAILURE)
    .then({
      ...state,
      loading: false,
    })
    .equals(ACTION_TYPES.UPDATE_SORT_ORDER)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.UPDATE_SORT_ORDER_SUCCESS)
    .then({
      ...state,
      loading: false,
      medicationPlan: {
        ...state.medicationPlan,
        medicationPlanGroups: action.planItems,
      },
    })
    .equals(ACTION_TYPES.UPDATE_SORT_ORDER_FAILURE)
    .then({
      ...state,
      loading: false,
    })
    .equals(ACTION_TYPES.ADD_NEW_ENTRY)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.ADD_NEW_ENTRY_SUCCESS)
    .then({
      ...state,
      loading: false,
      medicationPlan: {
        ...state.medicationPlan,
        medicationPlanGroups: [
          action.newPlanItem,
          ...state.medicationPlan.medicationPlanGroups,
        ],
      },
    })
    .equals(ACTION_TYPES.ADD_NEW_ENTRY_FAILURE)
    .then({
      ...state,
      loading: false,
    })
    .equals(ACTION_TYPES.UPDATE_ENTRY)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.UPDATE_ENTRY_SUCCESS)
    .then(() => {
      const updatedPlanItem = action.updatedPlanItem;
      const medicationPlanGroups =
        state.medicationPlan.medicationPlanGroups.map((planItem) => {
          if (planItem.id === updatedPlanItem.id) {
            return { ...planItem, ...updatedPlanItem };
          }
          return planItem;
        });
      return {
        ...state,
        loading: false,
        medicationPlan: {
          ...state.medicationPlan,
          medicationPlanGroups,
        },
      };
    })
    .equals(ACTION_TYPES.UPDATE_ENTRY_FAILURE)
    .then({
      ...state,
      loading: false,
    })
    .equals(ACTION_TYPES.REMOVE_ENTRY)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.REMOVE_ENTRY_MEDICATION)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.REMOVE_ENTRY_SUCCESS)
    .then(() => {
      const medicationPlanGroups =
        state.medicationPlan.medicationPlanGroups.filter(
          (planItem) => planItem.id !== action.id
        );
      return {
        ...state,
        loading: false,
        medicationPlan: {
          ...state.medicationPlan,
          medicationPlanGroups,
        },
      };
    })
    .equals(ACTION_TYPES.REMOVE_ENTRY_FAILURE)
    .then({
      ...state,
      loading: false,
    })
    .equals(ACTION_TYPES.FETCH_SUBHEADINGS)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.FETCH_SUBHEADINGS_SUCCESS)
    .then({
      ...state,
      loading: false,
      subheadings: action.subheadings,
    })
    .equals(ACTION_TYPES.FETCH_SUBHEADINGS_FAILURE)
    .then({
      ...state,
      loading: false,
    })
    .equals(ACTION_TYPES.ADD_NEW_SUBHEADING)
    .then({
      ...state,
      loading: true,
    })
    .equals(ACTION_TYPES.ADD_NEW_SUBHEADING_SUCCESS)
    .then({
      ...state,
      loading: false,
      subheadings: [...state.subheadings, action.newSubheading],
    })
    .equals(ACTION_TYPES.ADD_NEW_SUBHEADING_FAILURE)
    .then({
      ...state,
      loading: false,
    })
    .equals(ACTION_TYPES.MERGE_ENTRIES)
    .then({
      ...state,
    })
    .equals(ACTION_TYPES.MERGE_ENTRIES_SUCCESS)
    .then({
      ...state,
      medicationPlan: {
        ...state.medicationPlan,
        medicationPlanGroups: action.medicationPlanItems,
      },
      additionalData: !checkEmptyValueAdditionalData(action.additionalData) ? action.additionalData : undefined,
    })
    .equals(ACTION_TYPES.MERGE_ENTRIES_FAILURE)
    .then({
      ...state,
    })
    .else(state);

const useMedicationReducer = (): [IState, Dispatch<any>] => {
  const { t } = useTranslation();
  const [state, dispatch]: [IState, Dispatch<any>] = useReducer(
    medicationPlanReducer,
    initialState
  );
  const enhancedDispatch = applyMedicationPlanSideEffectsMiddleWare(dispatch, {
    t,
  });
  return [state, enhancedDispatch];
};

export default useMedicationReducer;
