import React, { useMemo, useContext } from 'react';
import { Dialog } from '@tutum/design-system/components/Core';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import Cave from '@tutum/mvz/module_patient-management/patient-file/cave/Cave.styled';
import Allergies from '@tutum/mvz/module_patient-management/patient-file/allergies/Allergies.styled';
import MedicalData from '@tutum/mvz/module_patient-management/patient-file/medical-data/MedicalData.styled';
import I18n from '@tutum/infrastructure/i18n';
import { BodyTextL, Flex } from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';
import { isEmpty } from 'lodash';
import FormUtils from '@tutum/infrastructure/utils/form.util';

export interface PatientInformationDialogProps {
  className?: string;
  close: () => void;
}

const PatientInformationDialog = ({
  className,
  close,
}: PatientInformationDialogProps) => {
  const { t } = I18n.useTranslation({
    namespace: 'Medication',
    nestedTrans: 'PatientInformationModal',
  });

  const {
    patientManagement: { patient },
  } = useContext(PatientManagementContext.instance);

  const isEmptyMedicalData = useMemo(() => {
    const patientMedicalData = patient?.patientMedicalData;
    const weight = patientMedicalData?.weight!;
    const height = patientMedicalData?.height!;
    const bloodPressure = patientMedicalData?.bloodPressure;
    const heartFrequency = patientMedicalData?.heartFrequency;
    const creatinine = patientMedicalData?.creatinine;
    const amountOfChildren = patientMedicalData?.amountOfChildren;
    const amountOfPregnancies = patientMedicalData?.amountOfPregnancies;
    const dateOfPlannedBirth = patientMedicalData?.dateOfPlannedBirth;
    const careLevel = patientMedicalData?.careLevel;
    const amountOfBirth = patientMedicalData?.amountOfBirth;
    const bmi = FormUtils.calculateBMI({ height, weight });

    return !(
      height ||
      weight ||
      bmi ||
      careLevel ||
      heartFrequency ||
      bloodPressure ||
      creatinine ||
      amountOfChildren ||
      dateOfPlannedBirth ||
      amountOfBirth ||
      amountOfPregnancies
    );
  }, [patient]);

  const isEmptyData = useMemo(() => {
    return (
      isEmptyMedicalData &&
      isEmpty(patient?.patientInfo?.otherInfo?.cave) &&
      isEmpty(patient?.patientMedicalData?.allergies)
    );
  }, [patient, isEmptyMedicalData]);

  const renderContent = useMemo(() => {
    if (isEmptyData) {
      return (
        <Flex column m="80px 62.5px 20px" align="center">
          <BodyTextL
            margin="0 0 8px"
            fontSize={20}
            lineHeight="28px"
            fontWeight={700}
            color={COLOR.TEXT_SECONDARY_NAVAL}
          >
            {t('emptyData')}
          </BodyTextL>
          <BodyTextL>{t('description')}</BodyTextL>
        </Flex>
      );
    }

    return (
      <>
        {!isEmpty(patient?.patientInfo?.otherInfo?.cave) && (
          <Cave patient={patient} canEdit={false} />
        )}
        {!isEmpty(patient?.patientMedicalData?.allergies) && (
          <Allergies patient={patient} canEdit={false} />
        )}
        <MedicalData patient={patient} canEdit={false} />
      </>
    );
  }, [isEmptyData, patient]);

  return (
    <Dialog
      className={`${className} dialog-right`}
      title={t('title')}
      isOpen
      onClose={close}
      canOutsideClickClose={false}
    >
      {renderContent}
    </Dialog>
  );
};

export default PatientInformationDialog;
