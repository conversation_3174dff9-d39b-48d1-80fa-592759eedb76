import React, { useRef, useEffect, useMemo } from 'react';
import { Translate } from 'next-translate';
import useTranslation from 'next-translate/useTranslation';

import { Icon, MenuItem } from '@tutum/design-system/components/Core';
import { Suggest } from '@tutum/design-system/components/Select';
import { BmpMedicationPlanKeytab } from '@tutum/hermes/bff/service_domains_bmp';
import {
  BodyTextM,
  BodyTextS,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';

const EnterIcon = '/images/enter.svg';

export interface IKeyTabInputProps {
  items: BmpMedicationPlanKeytab[];
  className?: string;
  onItemSelect: (item: BmpMedicationPlanKeytab) => void;
  defaultSelectedItem: any;
  maxLength?: number;
  hasCreateBtn?: boolean;
  hasArrowIcon?: boolean;
  isShowCode?: boolean;
  onInputChange?: (v: string) => void;
}

export const renderItem = (
  item,
  { handleClick, modifiers },
  isShowCode: boolean
) => {
  if (!modifiers.matchesPredicate) {
    return null;
  }
  return (
    <MenuItem
      active={modifiers.active}
      key={`${item.code}-${item.name}`}
      onClick={handleClick}
      text={
        <Flex justify="space-between" align="center">
          <BodyTextM>{item.name}</BodyTextM>
          {isShowCode && !!item.code && (
            <BodyTextS fontWeight={600} color={COLOR.TEXT_TERTIARY_SILVER}>
              {item.code}
            </BodyTextS>
          )}
        </Flex>
      }
    />
  );
};

const create = (text) => {
  return {
    name: text,
    isNew: true,
  };
};

const renderCreateOption =
  (t: Translate) =>
    (
      query: string,
      active: boolean,
      handleClick: React.MouseEventHandler<HTMLElement>
    ) => {
      return (
        <MenuItem
          text={
            <Flex justify="space-between" className="suggest__create-menu">
              <div>
                {t('ButtonActions.create')}{' '}
                <b className="query">{`"${query}"`}</b>
              </div>
              <Svg className="enter-icon" src={EnterIcon} />
            </Flex>
          }
          active={active}
          onClick={handleClick}
          shouldDismissPopover={false}
        />
      );
    };

const KeyTabInput = (props: IKeyTabInputProps) => {
  const { t } = useTranslation('Common');
  const inputRef = useRef<HTMLInputElement | null>(null);
  const {
    defaultSelectedItem,
    onItemSelect,
    items,
    className,
    maxLength = 50,
    hasCreateBtn = true,
    hasArrowIcon = false,
    isShowCode = false,
    onInputChange,
  } = props;

  const placeholder = useMemo(
    () =>
      t(
        hasArrowIcon ? 'SuggestList.selectText' : 'SuggestList.inputPlaceholder'
      ),
    [hasArrowIcon]
  );
  const defaultTextValue = useMemo(() => {
    return (
      (typeof defaultSelectedItem?.name === 'object' &&
        defaultSelectedItem?.name?.name) ||
      defaultSelectedItem?.name ||
      ''
    );
  }, [defaultSelectedItem]);

  const onFocusInput = () => {
    setTimeout(() => {
      if (!inputRef.current) {
        return;
      }

      inputRef.current.value = defaultTextValue;
      inputRef.current.setAttribute('placeholder', placeholder);
    });
  };

  useEffect(() => {
    if (!inputRef?.current) {
      return;
    }

    const onBlurInput = (e) => {
      const inputValue = e.target.value;

      if (inputValue !== defaultTextValue) {
        onItemSelect(inputValue as BmpMedicationPlanKeytab);
      }
    };

    inputRef.current.addEventListener('blur', onBlurInput);

    return () => {
      inputRef.current?.removeEventListener('blur', onBlurInput);
    };
  });

  const debounceQueryChange = (v: string) => onInputChange?.(v);
  return (
    <Suggest
      className={className}
      popoverProps={{
        usePortal: false,
        popoverClassName: 'popover-wrapper',
        minimal: true,
      }}
      fill={true}
      itemPredicate={(query, item) =>
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.code.toLowerCase().includes(query.toLowerCase())
      }
      inputProps={{
        placeholder,
        maxLength,
        inputRef,
        rightElement: hasArrowIcon ? <Icon icon="caret-down" /> : undefined,
        onFocus: onFocusInput,
      }}
      itemsEqual={(item1, item2) => item1 && item2 && item1.code === item2.code}
      inputValueRenderer={(item) => item.name}
      itemRenderer={(item, action) => renderItem(item, action, isShowCode)}
      selectedItem={defaultSelectedItem}
      onItemSelect={onItemSelect}
      createNewItemFromQuery={create}
      createNewItemRenderer={hasCreateBtn ? renderCreateOption(t) : undefined}
      items={items}
      onQueryChange={(
        query: string,
        _: React.ChangeEvent<HTMLInputElement>
      ) => {
        debounceQueryChange(query);
      }}
    />
  );
};

export default KeyTabInput;
