import { OverflowNode } from '@lexical/overflow';
import {
  BodyTextM,
  BodyTextS,
  Button,
  Flex,
  IntakeIntervalInput,
} from '@tutum/design-system/components';
import Banner from '@tutum/design-system/components/Banner/Banner.styled';
import {
  Checkbox,
  Classes,
  Dialog,
  InputGroup,
  Intent,
  Label,
} from '@tutum/design-system/components/Core';
import MvzTextmodule from '@tutum/design-system/textmodule';
import { AvoidCharacterPlugin } from '@tutum/design-system/textmodule/plugins/AvoidCharacter.plugin';
import { NoBreakLinePlugin } from '@tutum/design-system/textmodule/plugins/NoBreakLine.plugin';
import {
  DrugInformation,
  Medicine,
  PackagingInformation,
  PriceInformation,
  ProductInformation,
  Substance,
  TextInformation,
} from '@tutum/hermes/bff/app_mvz_medicine';
import {
  IntakeInterval,
  MedicationPlanGroup,
} from '@tutum/hermes/bff/repo_bmp_common';
import { BmpMedicationPlanKeytab } from '@tutum/hermes/bff/service_domains_bmp';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';
import I18n from '@tutum/infrastructure/i18n';
import ValidationFormGroup from '@tutum/mvz/components/validation-form-group/ValidationFormGroup';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import {
  handlePasteValue,
  parseContentToRead,
  parseContentToWrite,
} from '@tutum/mvz/module_medication/medication-plan/MedicationPlan.helper';
import KeyTabInput from '@tutum/mvz/module_medication/medication-plan/key-tab-input/KeyTabInput.styled';
import ConfirmEditMedicationDialog from '@tutum/mvz/module_medication/medication-plan/medication-dialog/confirm-edit-medication-dialog/ConfirmEditMedicationDialog.styled';
import Substances from '@tutum/mvz/module_medication/medication-plan/medication-dialog/substances/Substances.styled';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import { Field, Form, Formik } from 'formik';
import { isEmpty, isEqual } from 'lodash';
import useTranslation from 'next-translate/useTranslation';
import React, { memo, useState } from 'react';
import { sortByString } from '../merge-dialog/helpers';
import { onValidate } from './helper';
import { DEFAULT_SUBSTANCE } from './substances/Substances';

const intakeGroupRegex = /^(.+)?-(.+)?-(.+)?-(.+)?$/;
const validDigit = /[0-9]\d{0,3}(?:(?:\,|\.)\d{1,3})?/g;

const addNumberPrefix = (value: string, number = 0) => {
  return ['.', ','].includes(value[0]) ? `${number}${value}` : value;
};

const checkValidIntakeInterval = (intake: string): boolean => {
  if (intake.length > 4) {
    return false;
  }
  if (Number(intake)) {
    return !!intake.match(validDigit);
  }
  return true;
};

const buildIntakeInterval = (intakeGroup = '') => {
  const intakes = intakeGroup.match(intakeGroupRegex);
  if (intakes && intakeGroup.match(/-/g)?.length === 3) {
    const morning = !!intakes[1] ? addNumberPrefix(intakes[1]) : '0';
    const afternoon = !!intakes[2] ? addNumberPrefix(intakes[2]) : '0';
    const evening = !!intakes[3] ? addNumberPrefix(intakes[3]) : '0';
    const night = !!intakes[4] ? addNumberPrefix(intakes[4]) : '0';
    if (
      checkValidIntakeInterval(morning) &&
      checkValidIntakeInterval(afternoon) &&
      checkValidIntakeInterval(evening) &&
      checkValidIntakeInterval(night)
    ) {
      return {
        morning: morning.replace('.', ','),
        afternoon: afternoon.replace('.', ','),
        evening: evening.replace('.', ','),
        night: night.replace('.', ','),
        freetext: '',
        dj: false,
      };
    }
  }
  return {
    morning: '',
    afternoon: '',
    evening: '',
    night: '',
    freetext: intakeGroup,
    dj: true,
  };
};

export interface IMedicationInfo {
  unit: number;
  drugForm: number;
  substances: any[];
  tradeName: string;
  additionalLine?: string;
  hint?: string;
  reason?: string;
  pzn?: string;
  intakeInterval?: IntakeInterval;
  kBVMedicineID: number;
  drugInformation: DrugInformation;
  productInformation: ProductInformation;
  packagingInformation?: PackagingInformation;
  priceInformation: PriceInformation;
  textInformation: TextInformation;
  isEditMedication?: boolean;
}

export interface IMedicationDialogProps {
  className?: string;
  isOpen: boolean;
  medicine?: Medicine;
  isBMPHintTextModule?: boolean;
  freetextPrescriptionName?: string;
  medicationPlanItem?: MedicationPlanGroup;
  units: BmpMedicationPlanKeytab[];
  forms: BmpMedicationPlanKeytab[];
  hints: BmpMedicationPlanKeytab[];
  reasons: BmpMedicationPlanKeytab[];
  close: () => void;
  saveMedication: (
    medicationInfo,
    medicationPlanItem?: MedicationPlanGroup
  ) => void;
  addHint?: (name: string) => void;
  addReason?: (name: string) => void;
}

export const validateRequiredField = (value, t) => {
  if (isEmpty(value) || isEmpty(value.trim())) {
    return t('MedicationPlan.requiredField');
  }
  return null;
};

const getIntakeIntervalValue = (intakeInterval?: IntakeInterval) => {
  if (isEmpty(intakeInterval)) {
    return '';
  }
  if (!isEmpty(intakeInterval.freetext)) {
    return intakeInterval.freetext;
  }
  const { morning, afternoon, evening, night } = intakeInterval;
  return `${morning}-${afternoon}-${evening}-${night}`;
};

const MedicationDialog = (props: IMedicationDialogProps) => {
  const [confirmEditMedicationDialogOpen, setConfirmEditMedicationDialogOpen] =
    useState(false);
  const [medicationInfo, setMedicationInfo] = useState<IMedicationInfo | undefined>(undefined);
  const { t } = useTranslation('Medication');
  const { t: tCommon } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });
  const {
    className,
    isOpen,
    freetextPrescriptionName,
    close,
    medicationPlanItem,
    medicine,
    units,
    forms,
    isBMPHintTextModule,
    saveMedication,
  } = props;
  const title = isEmpty(medicationPlanItem)
    ? t('MedicationPlan.addNewMedication')
    : t('MedicationPlan.editMedication');
  let initialValues = {} as any;
  let substances: {
    name: string;
    concentration: string;
  }[] = [];

  const usedForList = isBMPHintTextModule
    ? [TextModuleUseFor.TextModuleUseFor_BMP]
    : [
      TextModuleUseFor.TextModuleUseFor_Note,
      TextModuleUseFor.TextModuleUseFor_Anamnesis,
      TextModuleUseFor.TextModuleUseFor_Findings,
      TextModuleUseFor.TextModuleUseFor_Therapy,
    ];

  if (freetextPrescriptionName) {
    initialValues = {
      tradeName: freetextPrescriptionName,
      substances: [DEFAULT_SUBSTANCE],
    };
  } else if (!isEmpty(medicine)) {
    const { productInformation, drugInformation, medicationPlanInformation } =
      medicine;
    if (drugInformation?.components?.length) {
      const component = drugInformation.components[0];
      const activeSubstances = component.substances.filter(
        (substance) => substance.substanceType === 1
      );
      substances = activeSubstances.map((substance) => {
        return {
          name: substance.name,
          concentration: `${medicationUtil.transformFormatNumber(
            substance.amount,
            0
          )} ${substance.unit}`,
        };
      });
    }
    const { name, shortName } = productInformation || {};
    const tradeName = shortName ? `${name}(${shortName})}` : name;
    const unitName = medicationPlanInformation?.medicationPlanUnitCode;

    const checkFormEqual = (formVal: string, f: BmpMedicationPlanKeytab) =>
      formVal === f.name.toLowerCase() ||
      formVal === (f.code || '').toLowerCase() ||
      formVal === (f.description || '').toLowerCase();

    const unit = units.find(
      (u) =>
        u.name.toLowerCase() === unitName ||
        (u.code || '').toLowerCase() === unitName ||
        (u.description || '').toLowerCase() === unitName
    );
    const form = forms.find((f) => {
      let formVal = productInformation?.pharmFormCodeIFA?.toLowerCase();
      if (formVal) {
        if (checkFormEqual(formVal, f)) return true;
      }
      formVal = productInformation?.dosageForm?.toLowerCase();
      if (formVal) {
        if (checkFormEqual(formVal, f)) return true;
      }
      formVal = productInformation?.dosageFormCode?.toLowerCase();
      if (formVal) {
        if (checkFormEqual(formVal, f)) return true;
      }
      return false;
    });
    initialValues = {
      tradeName,
      unit,
      form,
      substances,
    };
  } else if (
    !isEmpty(medicationPlanItem) &&
    !isEmpty(medicationPlanItem.medicationInformation)
  ) {
    const medicationInformation = medicationPlanItem.medicationInformation;
    const { tradeName, additionalLine, hint, reason, intakeInterval } =
      medicationInformation;
    const unit = units.find((u) => u.code == medicationInformation.unit) || {
      code: medicationInformation.unit,
      name: medicationInformation.unit,
    };
    const form = forms.find(
      (f) => f.code == medicationInformation.drugForm
    ) || {
      code: medicationInformation.drugForm,
      name: medicationInformation.drugForm,
    };
    const intakeIntervalValue = getIntakeIntervalValue(intakeInterval);
    substances = medicationInformation.substances;
    initialValues = {
      tradeName,
      unit,
      form,
      additionalLine: parseContentToRead(additionalLine),
      hint: parseContentToRead(hint),
      reason: parseContentToRead(reason),
      intakeInterval: intakeIntervalValue,
      substances: medicationInformation.substances,
    };
  } else {
    initialValues = {
      tradeName: '',
      substances: [DEFAULT_SUBSTANCE],
    };
  }

  const save = (medicationInfo) => {
    saveMedication(medicationInfo, medicationPlanItem);
    close();
  };

  const renderMedicationForm = (
    { close, units, forms, className, medicine }: IMedicationDialogProps,
    errors,
    t,
    substances,
    setFieldValue
  ) => {
    return (
      <Form className={className}>
        <Banner intent="primary" canDismiss>
          <BodyTextM style={{ width: '75%' }}>
            {t('MedicationPlan.textmoduleHint')}
          </BodyTextM>
        </Banner>
        <div className={`${Classes.DIALOG_BODY} medication-dialog__body`}>
          <Substances
            errors={errors}
            substances={substances}
            setFieldValue={setFieldValue}
          />
          <div className="row">
            <div className="col-sm-12">
              <ValidationFormGroup errors={errors} fieldName="tradeName">
                <Label name="tradeName">
                  <BodyTextS fontWeight={500}>
                    {t('MedicationPlan.tradeName').toUpperCase()}
                  </BodyTextS>
                </Label>
                <Field name="tradeName">
                  {({ field }) => {
                    return <InputGroup type="text" {...field} maxLength={50} />;
                  }}
                </Field>
              </ValidationFormGroup>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-6">
              <ValidationFormGroup errors={errors} fieldName="unit">
                <Label name="unit">
                  <BodyTextS fontWeight={500}>
                    {t('MedicationPlan.drugUnit').toUpperCase()}
                  </BodyTextS>
                </Label>
                <Field name="unit">
                  {({ field, form }) => {
                    const onItemSelect = (item) => {
                      form.setFieldValue(
                        field.name,
                        typeof item === 'string'
                          ? { code: item, name: item }
                          : item
                      );
                    };
                    return (
                      <KeyTabInput
                        maxLength={20}
                        defaultSelectedItem={form.values[field.name]}
                        items={units.sort((a, b) =>
                          sortByString(a.name, b.name, true)
                        )}
                        hasCreateBtn={false}
                        hasArrowIcon
                        onItemSelect={onItemSelect}
                      />
                    );
                  }}
                </Field>
              </ValidationFormGroup>
            </div>
            <div className="col-sm-6">
              <ValidationFormGroup errors={errors} fieldName="form">
                <Label name="form">
                  <BodyTextS fontWeight={500}>
                    {t('MedicationPlan.drugForm').toUpperCase()}
                  </BodyTextS>
                </Label>
                <Field name="form">
                  {({ field, form }) => {
                    const onItemSelect = (item) => {
                      form.setFieldValue(
                        field.name,
                        typeof item === 'string'
                          ? { code: item, name: item }
                          : item
                      );
                      if (typeof item === 'string') {
                        form.setFieldValue('drugFormDescription', '');
                      }
                    };
                    return (
                      <KeyTabInput
                        maxLength={7}
                        defaultSelectedItem={form.values[field.name]}
                        items={forms.sort((a, b) =>
                          sortByString(a.name, b.name, true)
                        )}
                        hasCreateBtn={false}
                        hasArrowIcon
                        isShowCode
                        onItemSelect={onItemSelect}
                        onInputChange={(v: string) => {
                          form.setFieldValue(field.name, { code: v, name: v });
                          form.setFieldValue('drugFormDescription', '');
                        }}
                      />
                    );
                  }}
                </Field>
              </ValidationFormGroup>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-12">
              <ValidationFormGroup errors={errors} fieldName="intakeInterval">
                <Label name="intakeInterval">
                  <BodyTextS fontWeight={500}>
                    {t('MedicationPlan.intakeInterval').toUpperCase()}
                  </BodyTextS>
                </Label>
                <Field name="intakeInterval">
                  {({ field, form }) => {
                    return (
                      <IntakeIntervalInput
                        defaultValue={field.value}
                        onChange={(value: string) => {
                          form.setFieldValue(field.name, value);
                        }}
                      />
                    );
                  }}
                </Field>
              </ValidationFormGroup>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-12">
              <ValidationFormGroup errors={errors} fieldName="hint">
                <Label name="hint">
                  <BodyTextS fontWeight={500}>
                    {t('MedicationPlan.hint').toUpperCase()}
                  </BodyTextS>
                </Label>
                <Field name="hint">
                  {({ field, form }) => {
                    const isHasBreakLine =
                      form.values?.[field.name]?.includes('\n');

                    return (
                      <MvzTextmodule
                        placeholder=""
                        // {...field}
                        className="sl-editable-text"
                        nodes={[OverflowNode]}
                        isInDialog
                        usedForList={usedForList}
                        defaultValue={field.value}
                        onContentChange={({ text }) => {
                          const value = handlePasteValue(text).replaceAll(
                            '\t',
                            ' '
                          );
                          form.setFieldValue(field.name, value);
                        }}
                      >
                        <AvoidCharacterPlugin character="~" />
                        {isHasBreakLine && <NoBreakLinePlugin />}
                      </MvzTextmodule>
                    );
                  }}
                </Field>
              </ValidationFormGroup>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-12">
              <ValidationFormGroup errors={errors} fieldName="reason">
                <Label name="reason">
                  <BodyTextS fontWeight={500}>
                    {t('MedicationPlan.reason').toUpperCase()}
                  </BodyTextS>
                </Label>
                <Field name="reason">
                  {({ field, form }) => {
                    const isHasBreakLine =
                      form.values?.[field.name]?.includes('\n');

                    return (
                      <MvzTextmodule
                        // {...field}
                        placeholder=""
                        className="sl-editable-text"
                        nodes={[OverflowNode]}
                        isInDialog
                        usedForList={usedForList}
                        defaultValue={field.value}
                        onContentChange={({ text }) => {
                          const value = handlePasteValue(text).replaceAll(
                            '\t',
                            ' '
                          );
                          form.setFieldValue(field.name, value);
                        }}
                      >
                        <AvoidCharacterPlugin character="~" />
                        {isHasBreakLine && <NoBreakLinePlugin />}
                      </MvzTextmodule>
                    );
                  }}
                </Field>
              </ValidationFormGroup>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-12">
              <ValidationFormGroup errors={errors} fieldName="additionalLine">
                <Label name="additionalLine">
                  <BodyTextS fontWeight={500}>
                    {t('MedicationPlan.additionalLine').toUpperCase()}
                  </BodyTextS>
                </Label>
                <Field name="additionalLine">
                  {({ form, field }) => {
                    const isHasBreakLine =
                      form.values?.[field.name]?.includes('\n');
                    return (
                      <MvzTextmodule
                        placeholder=""
                        className="sl-editable-text"
                        nodes={[OverflowNode]}
                        isInDialog
                        usedForList={usedForList}
                        defaultValue={field.value}
                        onContentChange={({ text }) => {
                          const value = handlePasteValue(text).replaceAll(
                            '\t',
                            ' '
                          );
                          form.setFieldValue(field.name, value);
                        }}
                      >
                        <AvoidCharacterPlugin character="~" />
                        {isHasBreakLine && <NoBreakLinePlugin />}
                      </MvzTextmodule>
                    );
                  }}
                </Field>
              </ValidationFormGroup>
            </div>
          </div>

          {Boolean(medicine?.productInformation?.sampleProductFlag) && (
            <div className="row">
              <div className="col-sm-12">
                <Checkbox
                  disabled
                  checked
                  label={t('MedicationPlan.markAsDoctorSample')}
                />
              </div>
            </div>
          )}
        </div>
        <Flex
          justify="flex-end"
          className={`${Classes.DIALOG_FOOTER} medication-dialog__footer`}
        >
          <div
            className={`${Classes.DIALOG_FOOTER_ACTIONS} medication-dialog__footer-actions`}
          >
            <Button
              className={`secondary`}
              large
              onClick={(event) => {
                event.stopPropagation();
                close();
              }}
              intent={Intent.PRIMARY}
            >
              {t('MedicationPlan.cancel')}
            </Button>
            <Button
              intent={Intent.PRIMARY}
              large
              type="submit"
              disabled={Object.keys(errors ?? {}).length > 0}
              onClick={(event) => {
                event.stopPropagation();
              }}
            >
              {t('MedicationPlan.save')}
            </Button>
          </div>
        </Flex>
      </Form>
    );
  };

  const submit = (formValues) => {
    const {
      unit,
      form,
      substances,
      tradeName,
      intakeInterval,
      additionalLine,
      hint,
      reason,
    } = formValues;
    const medicationInfo: IMedicationInfo = {
      unit: unit?.code,
      drugForm: form?.code,
      substances: substances.filter((substance) => substance && substance.name),
      tradeName,
      additionalLine: parseContentToWrite(additionalLine),
      hint: parseContentToWrite(hint),
      reason: parseContentToWrite(reason),
      pzn: medicine?.pzn || medicationPlanItem?.medicationInformation?.pzn,
      drugInformation:
        medicine?.drugInformation ||
        medicationPlanItem?.medicationInformation?.drugInformation!,
      productInformation:
        medicine?.productInformation ||
        medicationPlanItem?.medicationInformation?.productInformation!,
      packagingInformation:
        medicine?.packagingInformation ||
        medicationPlanItem?.medicationInformation?.packagingInformation,
      kBVMedicineID:
        medicine?.productInformation?.id ||
        medicationPlanItem?.medicationInformation?.productInformation?.id!,
      priceInformation:
        medicine?.priceInformation ||
        medicationPlanItem?.medicationInformation?.priceInformation!,
      textInformation:
        medicine?.textInformation ||
        medicationPlanItem?.medicationInformation?.textInformation!,
      isEditMedication:
        medicationPlanItem?.medicationInformation?.isEditMedication,
    };
    if (intakeInterval) {
      medicationInfo.intakeInterval = buildIntakeInterval(intakeInterval);
    }
    if (
      !medicationPlanItem?.medicationInformation?.isEditMedication &&
      (!!freetextPrescriptionName ||
        !!medicationPlanItem?.medicationInformation?.isOutDatePzn ||
        medicationPlanItem?.medicationInformation?.pzn == '00000000')
    ) {
      medicationInfo.isEditMedication = true;
    }
    if (!freetextPrescriptionName && !isEmpty(initialValues)) {
      const trimedMedicationSubstances = medicationInfo.substances.map((s) => ({
        name: s.name?.trimStart?.()?.trimEnd?.(),
        concentration: s.concentration?.trimStart?.()?.trimEnd?.(),
      }));
      const trimedInitalValues = initialValues.substances.map((s) => ({
        name: s.name?.trimStart?.()?.trimEnd?.(),
        concentration: s.concentration?.trimStart?.()?.trimEnd?.(),
      }));
      if (
        !medicationPlanItem?.medicationInformation?.isEditMedication &&
        (tradeName?.trimStart?.()?.trimEnd?.() !==
          initialValues?.tradeName?.trimStart?.()?.trimEnd?.() ||
          !isEqual(trimedMedicationSubstances, trimedInitalValues))
      ) {
        medicationInfo.isEditMedication = true;
        medicationInfo.pzn = '';
        setMedicationInfo(medicationInfo);
        setConfirmEditMedicationDialogOpen(true);
        return;
      }
    }
    save(medicationInfo);
  };

  return (
    <>
      <Dialog
        className={`${className} dialog-right`}
        title={title}
        isOpen={isOpen}
        onClose={(event) => {
          event.stopPropagation();
          close();
        }}
        canOutsideClickClose={false}
      >
        <Formik
          onSubmit={submit}
          validate={onValidate(tCommon)}
          validateOnBlur={true}
          initialValues={initialValues}
        >
          {({ errors, values, setFieldValue }) => {
            return renderMedicationForm(
              props,
              errors,
              t,
              values['substances'],
              setFieldValue
            );
          }}
        </Formik>
      </Dialog>
      <ConfirmEditMedicationDialog
        isOpen={confirmEditMedicationDialogOpen}
        close={() => setConfirmEditMedicationDialogOpen(false)}
        medicationInfo={medicationInfo}
        save={save}
      />
    </>
  );
};

export default memo(MedicationDialog);
