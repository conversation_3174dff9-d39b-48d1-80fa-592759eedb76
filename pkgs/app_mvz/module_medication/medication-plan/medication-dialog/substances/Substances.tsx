import React, { useMemo, useState, useCallback } from 'react';
import { cloneDeep } from 'lodash';
import useTranslation from 'next-translate/useTranslation';
import { Field, FieldArray } from 'formik';

import { Substance } from '@tutum/hermes/bff/repo_bmp_common';
import {
  InputGroup,
  Intent,
  Label,
} from '@tutum/design-system/components/Core';
import {
  Button,
  Flex,
  Link,
  BodyTextS,
  Svg,
} from '@tutum/design-system/components';
import ValidationFormGroup from '@tutum/mvz/components/validation-form-group/ValidationFormGroup';
import { validateRequiredField } from '@tutum/mvz/module_medication/medication-plan/medication-dialog/MedicationDialog';

export interface ISubstancesProps {
  className?: string;
  substances?: Substance[];
  errors?: any;
  setFieldValue: (field: string, value: any) => void;
}

export const MAX_SUBSTANCE = 3;

const MinusIcon = '/images/minus-circle.svg';

export const DEFAULT_SUBSTANCE = {
  name: '',
  concentration: '',
};

const Substances = (props: ISubstancesProps) => {
  const { errors, substances, setFieldValue } = props;
  const { t } = useTranslation('Medication');
  const [isShowMoreSubstances, setShowMoreSubstances] =
    useState<boolean>(false);

  const showedSubstances = useMemo(() => {
    const cloneData = cloneDeep(substances) || [];
    return isShowMoreSubstances ? cloneData : cloneData.slice(0, MAX_SUBSTANCE);
  }, [substances, isShowMoreSubstances]);

  const addSubstance = useCallback(() => {
    const newSubstances = [...(substances || []), DEFAULT_SUBSTANCE];

    setFieldValue('substances', newSubstances);
  }, [substances]);

  const substanceElements = useMemo(() => {
    return (
      <FieldArray
        name="substances"
        render={(arrayHelpers) => {
          return showedSubstances.map((_, index) => {
            if (index === 0) {
              return (
                <div className="row" key={index}>
                  <div className="col-sm-6">
                    <ValidationFormGroup
                      key={`${index}.name`}
                      errors={errors}
                      fieldName={`substances.${index}.name`}
                    >
                      <Label name={`substances.${index}.name`}>
                        <BodyTextS fontWeight={500}>
                          {t('MedicationPlan.substance').toUpperCase()}{' '}
                          {index + 1}
                          <span className="sl-required-signal">*</span>
                        </BodyTextS>
                      </Label>
                      <Field
                        name={`substances.${index}.name`}
                        validate={(value) => validateRequiredField(value, t)}
                      >
                        {({ field }) => {
                          return (
                            <InputGroup
                              type="text"
                              id={`${index}.name`}
                              data-test-id={`substances.${index}.name`}
                              {...field}
                              maxLength={80}
                            />
                          );
                        }}
                      </Field>
                    </ValidationFormGroup>
                  </div>
                  <div className="col-sm-6">
                    <ValidationFormGroup
                      key={`${index}.concentration`}
                      errors={errors}
                      fieldName={`substances.${index}.concentration`}
                    >
                      <Label name={`substances.${index}.concentration`}>
                        <BodyTextS fontWeight={500}>
                          {t('MedicationPlan.concentration').toUpperCase()}
                          <span className="sl-required-signal">*</span>
                        </BodyTextS>
                      </Label>
                      <Flex className="add-substance-row__concentration-field">
                        <Field
                          name={`substances.${index}.concentration`}
                          validate={(value) => validateRequiredField(value, t)}
                        >
                          {({ field }) => {
                            return (
                              <InputGroup
                                type="text"
                                id={`${index}.concentration`}
                                data-test-id={`substances.${index}.concentration`}
                                {...field}
                                maxLength={15}
                              />
                            );
                          }}
                        </Field>
                        <div className="add-substance-row__remove" />
                      </Flex>
                    </ValidationFormGroup>
                  </div>
                </div>
              );
            }
            return (
              <div className="row" key={index}>
                <div className="col-sm-6">
                  <ValidationFormGroup
                    key={`${index}.name`}
                    errors={errors}
                    fieldName={`substances.${index}.name`}
                  >
                    <Label name={`substances.${index}.name`}>
                      <BodyTextS fontWeight={500}>
                        {t('MedicationPlan.substance').toUpperCase()}{' '}
                        {index + 1}
                      </BodyTextS>
                    </Label>
                    <Field name={`substances.${index}.name`}>
                      {({ field }) => {
                        return (
                          <InputGroup
                            type="text"
                            id={`${index}.name`}
                            data-test-id={`substances.${index}.name`}
                            {...field}
                            maxLength={45}
                          />
                        );
                      }}
                    </Field>
                  </ValidationFormGroup>
                </div>
                <div className="col-sm-6">
                  <ValidationFormGroup
                    key={`${index}.concentration`}
                    errors={errors}
                    fieldName={`substances.${index}.concentration`}
                  >
                    <Label name={`substances.${index}.concentration`}>
                      <BodyTextS fontWeight={500}>
                        {t('MedicationPlan.concentration').toUpperCase()}
                      </BodyTextS>
                    </Label>
                    <Flex
                      className="add-substance-row__concentration-field"
                      align="center"
                    >
                      <Field name={`substances.${index}.concentration`}>
                        {({ field }) => {
                          return (
                            <InputGroup
                              type="text"
                              id={`${index}.concentration`}
                              data-test-id={`substances.${index}.concentration`}
                              {...field}
                              maxLength={45}
                            />
                          );
                        }}
                      </Field>
                      <div className="add-substance-row__remove">
                        <Svg
                          className="sl-minus-icon"
                          src={MinusIcon}
                          onClick={() => {
                            arrayHelpers.remove(index);
                          }}
                        />
                      </div>
                    </Flex>
                  </ValidationFormGroup>
                </div>
              </div>
            );
          });
        }}
      />
    );
  }, [showedSubstances, errors]);

  const actionsSubtancesElement = useMemo(() => {
    const remainSubstances = (substances || []).length - MAX_SUBSTANCE;

    return (
      <Flex className="add-substance-row" align="center" gap={16}>
        {remainSubstances > 0 && !isShowMoreSubstances ? (
          <Link onClick={() => setShowMoreSubstances(true)}>
            {t('MedicationPlan.showMoreSubstance', {
              number: remainSubstances,
            })}
          </Link>
        ) : (
          <>
            <Button
              className={`secondary`}
              data-test-id="addSubtance"
              onClick={addSubstance}
              intent={Intent.PRIMARY}
            >
              {t('MedicationPlan.addSubstance')}
            </Button>
            {remainSubstances > 0 && (
              <Link onClick={() => setShowMoreSubstances(false)}>
                {t('MedicationPlan.showLessSubstance')}
              </Link>
            )}
          </>
        )}
      </Flex>
    );
  }, [isShowMoreSubstances, substances]);

  return (
    <div className={props.className}>
      {substanceElements}
      {actionsSubtancesElement}
    </div>
  );
};

export default Substances;
