import React, { memo, useCallback, useContext, useMemo, useState } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import {
  useMutationCreateMedicationPlan,
  useMutationDeleteMedicationPlan,
  MedicationPrescribeResponse,
  Medicine,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import {
  BodyTextM,
  Flex,
  Svg,
  H1,
  alertError,
  TOASTER_TIMEOUT_CUSTOM,
  alertSuccessfully,
  Avatar,
} from '@tutum/design-system/components';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { medicationUtil } from '../../utils/medication-util';
import { flatten } from '@tutum/design-system/infrastructure/utils';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import {
  Button,
  Classes,
  Dialog,
  Menu,
  MenuDivider,
  MenuItem,
  Popover,
  Switch,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import MedicationContext from '../../context/MedicationContext';
import { medicationActions } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import {
  SearchType as SearchTypeKBV,
  changeConcentration,
  markFavourite,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  DATE_FORMAT,
  HOUR_MIN_TIME_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import Star from '@tutum/mvz/public/images/star.svg';
import StarSolid from '@tutum/mvz/public/images/star-solid.svg';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IPrescribedMedicineRowProps {
  medicine: MedicationPrescribeResponse;
  prescriberProfile: IEmployeeProfile;
  selectedContractDoctor: ISelectedContractDoctor;
  onRequestChangePackageSize: (medicine: MedicationPrescribeResponse) => void;
  onRequestRefill: (medicine: MedicationPrescribeResponse, isAdd: boolean) => void;
  parentClass?: string;
  isSVPatient: boolean;
  ikNumber: number;
  contractId?: string;
  reloadMedicationPrescribed: () => Promise<void>;
}

const moreIcon = '/images/more-vertical.svg';
const refillIcon = '/images/refill.svg';
const addIcon = '/images/plus-circle-grey.svg';
const infoIcon = '/images/circle-info.svg';
const warningIcon = '/images/alert-circle-solid-big.svg';
const packageIcon = '/images/package-small.svg';
const changeConcentrationIcon = '/images/percent.svg';

function PrescribedMedicineRowMemo(
  props: IPrescribedMedicineRowProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof MedicationI18n.PrescribedMedicineRow>
) {
  const {
    className,
    medicine: prescribedMedicine,
    t,
    prescriberProfile,
    parentClass,
    selectedContractDoctor,
    onRequestRefill,
    onRequestChangePackageSize,
    ikNumber,
    isSVPatient: isSvPatient,
    contractId,
    reloadMedicationPrescribed,
  } = props;

  const {
    patientManagement: { patient },
  } = useContext(PatientManagementContext.instance);

  const patientFileStore = usePatientFileStore();
  const medicationContext = useContext(MedicationContext);
  const { t: tCommon } = I18n.useTranslation({
    namespace: 'Medication',
    nestedTrans: 'Common',
  });
  const [showRemoveConfirm, setShowRemoveConfirm] = useState<boolean>(false);
  const isActivatedSvSchein = patientFileStore?.schein?.isActivatedSvSchein;

  const requestViewSecondaryInfo = useCallback(() => {
    if (isActivatedSvSchein) {
      medicationContext.setViewSecondaryMedicine({
        ...prescribedMedicine,
        search: '',
        id: '',
      } as Medicine);
    } else {
      try {
        if (
          prescribedMedicine?.productInformation &&
          prescribedMedicine?.textInformation?.items?.length &&
          prescribedMedicine?.pzn
        ) {
          medicationContext.setSecondaryInfo({
            ...prescribedMedicine,
          } as unknown as Medicine);
        } else {
          alertError(tCommon('cannotOpenSecondaryInfo'), {
            timeout: TOASTER_TIMEOUT_CUSTOM,
          });
        }
      } catch (err) {
        alertError(tCommon('cannotOpenSecondaryInfo'), {
          timeout: TOASTER_TIMEOUT_CUSTOM,
        });
      }
    }
    // const secondaryViewMedicine = {
    //   ...prescribedMedicine,
    //   search: '',
    // } as Medicine;
    //  medicationContext.setViewSecondaryMedicine(secondaryViewMedicine);
  }, [prescribedMedicine.id]);

  const renderPrescribedDate = useMemo(() => {
    const date = new Date(prescribedMedicine.prescribeDate);
    return (
      <Flex column>
        <Flex className="sl-font-13">
          {datetimeUtil.dateTimeFormat(date, DATE_FORMAT)}
        </Flex>
        <Flex className="sl-sub-line">
          {datetimeUtil.dateTimeFormat(date, HOUR_MIN_TIME_FORMAT)}
        </Flex>
      </Flex>
    );
  }, [prescribedMedicine.prescribeDate]);

  const renderTradeName = useMemo(() => {
    const componentAndSubstances = medicationUtil.getComponentsAndSubstants(
      prescribedMedicine.drugInformation!
    );
    const substanceName = flatten(
      componentAndSubstances.map((item) => item.substances)
    ).join(', ');

    return (
      <Flex column justify="center">
        <Flex className="sl-font-13">{prescribedMedicine.name}</Flex>
        <Flex className="sl-sub-line">{substanceName}</Flex>
      </Flex>
    );
  }, [prescribedMedicine.drugInformation, prescribedMedicine.name]);

  const renderSize = useMemo(() => {
    const sizeName = prescribedMedicine?.packagingInformation?.packageSize?.nop;

    if (!sizeName) return null;
    return (
      <Flex className="sl-font-13" align="center">
        {sizeName}
      </Flex>
    );
  }, [prescribedMedicine.packagingInformation]);

  const {
    isPending: isDeleteMedicationPlanPending,
    mutate: deleteMedicationPlan,
  } = useMutationDeleteMedicationPlan({
    onSuccess: () => {
      alertSuccessfully(t('medicationPlanUpdated'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
    onSettled: () => {
      setShowRemoveConfirm(false);
    },
  });

  const {
    isPending: isCreateMedicationPlanPending,
    mutate: createMedicationPlan,
  } = useMutationCreateMedicationPlan({
    onSuccess: () => {
      alertSuccessfully(t('medicationPlanUpdated'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });

  const onConfirmRemoveFromMedicationPlan = useCallback(() => {
    return deleteMedicationPlan({
      medicationPlanId: prescribedMedicine.medicationPlanId!,
      patientId: patient?.id!,
      doctorId: selectedContractDoctor.doctorId!,
      contractId: selectedContractDoctor.contractId!,
      encounterCase: selectedContractDoctor.encounterCase!,
    });
  }, [prescribedMedicine.id, prescribedMedicine.medicationPlanId]);

  const toggleMedicineInMedicationPlan = useCallback(() => {
    if (!prescribedMedicine.medicationPlanId) {
      let hint = prescribedMedicine?.asNeeded === true ? t('asNeeded') : '';
      if (prescribedMedicine?.furtherInformation?.length > 0) {
        if (hint.length > 0) {
          hint += `. ${prescribedMedicine.furtherInformation}`;
        } else {
          hint += prescribedMedicine?.furtherInformation;
        }
      }
      return createMedicationPlan({
        prescribedMedicationId: prescribedMedicine.id,
        hint: hint,
      });
    } else {
      setShowRemoveConfirm(true);
      return;
    }
  }, [
    prescribedMedicine.medicationPlanId,
    prescribedMedicine.id,
    selectedContractDoctor?.encounterCase,
    selectedContractDoctor?.contractId,
  ]);

  const renderIntakeInterVal = useMemo(() => {
    const { intakeInterval = {} } = prescribedMedicine;
    const { morning, night, afternoon, evening, freetext, dJ } = intakeInterval;

    if (dJ) {
      return (
        <td colSpan={4}>
          <Flex className="sl-intake-interval" />
        </td>
      );
    }

    if (
      morning !== undefined &&
      night !== undefined &&
      afternoon !== undefined &&
      evening !== undefined
    ) {
      return (
        <React.Fragment>
          <td>
            <Flex
              justify="center"
              align="center"
              className="sl-intake-interval"
            >
              {medicationUtil.getIntakeInterValue(intakeInterval, 'MORNING')}
            </Flex>
          </td>
          <td>
            <Flex
              justify="center"
              align="center"
              className="sl-intake-interval"
            >
              {medicationUtil.getIntakeInterValue(intakeInterval, 'AFTERNOON')}
            </Flex>
          </td>
          <td>
            <Flex
              justify="center"
              align="center"
              className="sl-intake-interval"
            >
              {medicationUtil.getIntakeInterValue(intakeInterval, 'EVENING')}
            </Flex>
          </td>
          <td>
            <Flex
              justify="center"
              align="center"
              className="sl-intake-interval"
            >
              {medicationUtil.getIntakeInterValue(intakeInterval, 'NIGHT')}
            </Flex>
          </td>
        </React.Fragment>
      );
    }
    return (
      <td colSpan={4}>
        <Flex className="sl-intake-interval freetext" align="center">
          {freetext}
        </Flex>
      </td>
    );
  }, [prescribedMedicine.intakeInterval]);

  const renderFurtherInformation = useMemo(() => {
    const furtherInformation: string[] = [];
    if (prescribedMedicine.asNeeded) {
      furtherInformation.push(t('asNeeded'));
    }
    if (prescribedMedicine.furtherInformation?.trim()?.length) {
      furtherInformation.push(prescribedMedicine.furtherInformation);
    }
    return (
      <Flex auto align="center">
        {furtherInformation.join(', ')}
      </Flex>
    );
  }, [prescribedMedicine.furtherInformation, prescribedMedicine.asNeeded]);

  const handleChangeConcentration = async () => {
    try {
      const tempSubtanceIds = prescribedMedicine.drugInformation?.components
        .map((sub) =>
          sub.substances.filter((s) => s.substanceType === 1).map((s) => s.id)
        )
        .flat();

      if (!tempSubtanceIds?.length) {
        return medicationActions.setIsNoConcentration(true);
      }

      const res = await changeConcentration({
        moleculeIds: tempSubtanceIds!,
        providerId: prescribedMedicine.productInformation?.providerID!,
        dosageFormCode: prescribedMedicine.productInformation?.pharmFormCodeIFA!,
        ikNumber,
        isSvPatient,
        contractId,
        referenceDate: datetimeUtil.dateTimeFormat(
          datetimeUtil.now(),
          YEAR_MONTH_DAY_FORMAT
        ),
        patientId: patientFileStore?.patient?.current?.id,
        isPrivateSchein: checkIsPrivateSchein(
          patientFileStore?.schein?.activatedSchein
        ),
      });
      medicationActions.setMedicines(res?.data);
      medicationActions.setIsDisplaySearchMedicineResult(true);
      medicationActions.setRowsPerPage(res?.data.totalRecords);
      medicationActions.setSelectAllRowsItem(true);
      medicationActions.setIsChangeConcentration(true);

      const tempSubtances = prescribedMedicine.drugInformation?.components
        .map((sub) =>
          sub.substances
            .filter((s) => s.substanceType === 1)
            .map((s) => s.name)
        )
        .flat(),
        substanceNames = tempSubtances;
      medicationActions.setMedicineQuery({
        keyword: `${(substanceNames || []).join(', ')}${prescribedMedicine?.productInformation?.provider
            ? `; ${prescribedMedicine.productInformation.provider}`
            : ''
          }${prescribedMedicine?.productInformation?.pharmFormCodeIFA
            ? `; ${prescribedMedicine.productInformation.pharmFormCodeIFA}`
            : ''
          }`,
        type: SearchTypeKBV.All,
      });
    } catch (err) {
      console.error(err);
    }
  };

  const handleMarkFavourite = async () => {
    await markFavourite({
      pzn: prescribedMedicine.pzn!,
      isFavourite: !prescribedMedicine.isFavourite,
    });
    await reloadMedicationPrescribed();
  };

  if (!prescriberProfile) {
    return null;
  }

  return (
    <React.Fragment>
      <tr className={`${className} ${parentClass || ''}`}>
        <td>
          <BodyTextM onClick={() => handleMarkFavourite()}>
            {prescribedMedicine?.isFavourite ? <StarSolid /> : <Star />}
          </BodyTextM>
        </td>
        <td>{renderPrescribedDate}</td>
        <td>{renderTradeName}</td>
        <td width="fit-content">{renderSize}</td>
        {renderIntakeInterVal}
        <td>{renderFurtherInformation}</td>
        <td>
          <Flex
            className="sl-font-13"
            align="center"
            justify="flex-start"
            gap={8}
          >
            <Avatar initial={prescriberProfile.initial} className="avatar" />
            <Flex>{nameUtils.getDoctorName(prescriberProfile)}</Flex>
          </Flex>
        </td>
        <td>
          <Flex>
            <Tooltip
              content={
                !prescribedMedicine.medicationPlanId
                  ? t('addToMedicationPlan')
                  : t('removeFromMedicationPlan')
              }
            >
              <Switch
                onChange={toggleMedicineInMedicationPlan}
                checked={!!prescribedMedicine.medicationPlanId}
                disabled={
                  isDeleteMedicationPlanPending || isCreateMedicationPlanPending
                }
              />
            </Tooltip>
          </Flex>
        </td>
        <td>
          <Flex>
            <Popover
              content={
                <Menu>
                  <Flex pl={16} pt={12} pb={4} align="center">
                    <BodyTextM
                      color={COLOR.TEXT_SECONDARY_NAVAL}
                      textTransform="uppercase"
                      fontSize={12}
                      fontWeight={600}
                    >
                      {t('prescribeAgain')}
                    </BodyTextM>
                  </Flex>
                  <MenuItem
                    onClick={() => onRequestRefill(prescribedMedicine, true)}
                    icon={<Svg src={addIcon} width={16} />}
                    text={t('add')}
                  />
                  <MenuItem
                    onClick={() => onRequestRefill(prescribedMedicine, false)}
                    icon={<Svg src={refillIcon} width={16} />}
                    text={t('refill')}
                  />
                  <MenuItem
                    icon={<Svg src={packageIcon} width={16} />}
                    disabled={!prescribedMedicine.kBVMedicineId}
                    onClick={() =>
                      onRequestChangePackageSize(prescribedMedicine)
                    }
                    text={t('changePackageSize')}
                  />

                  {!isActivatedSvSchein && (
                    <MenuItem
                      onClick={handleChangeConcentration}
                      icon={<Svg src={changeConcentrationIcon} width={16} />}
                      text={t('changeConcentration')}
                    />
                  )}

                  {!!(
                    isActivatedSvSchein ||
                    (prescribedMedicine?.productInformation &&
                      prescribedMedicine?.textInformation?.items?.length &&
                      prescribedMedicine?.pzn)
                  ) && (
                      <React.Fragment>
                        <MenuDivider />
                        <MenuItem
                          onClick={requestViewSecondaryInfo}
                          icon={<Svg src={infoIcon} width={16} />}
                          id="scondary-button"
                          text={t('viewSecondaryInfo')}
                        />
                      </React.Fragment>
                    )}
                </Menu>
              }
            >
              <Svg src={moreIcon} id="more-menu" className="sl-pointer" />
            </Popover>
          </Flex>
        </td>
      </tr>
      <Dialog
        isOpen={showRemoveConfirm}
        canEscapeKeyClose={true}
        canOutsideClickClose={false}
        onClose={() => setShowRemoveConfirm(false)}
      >
        <Flex className={Classes.DIALOG_BODY} column>
          <Flex align="center" gap={16}>
            <Flex>
              <Svg src={warningIcon} />
            </Flex>
            <Flex>
              <H1>{t('removeMedicationHeader')}</H1>
            </Flex>
          </Flex>
          <Flex mt={18} mb={32}>
            <span>
              <span>{t('youAreGoingToRemove')}</span>
              <span className="sl-remove-trade-name">{`"${prescribedMedicine?.name}"`}</span>
              <span>{t('fromMedicationPlan')}</span>
            </span>
          </Flex>
          <Flex justify="space-between">
            <Button
              onClick={() => setShowRemoveConfirm(false)}
              className="sl-diaglog-btn"
              minimal
            >
              {t('cancel')}
            </Button>
            <Button
              className="sl-diaglog-btn"
              intent="danger"
              onClick={onConfirmRemoveFromMedicationPlan}
              loading={isDeleteMedicationPlanPending}
            >
              {t('remove')}
            </Button>
          </Flex>
        </Flex>
      </Dialog>
    </React.Fragment>
  );
}

export default memo(
  I18n.withTranslation(PrescribedMedicineRowMemo, {
    namespace: 'Medication',
    nestedTrans: 'PrescribedMedicineRow',
  })
);
