import MedicationContext, {
  defaultContext,
  IMedicationContext,
  ViewMedicationFormExtends,
} from './MedicationContext';
import React, { useEffect, useState, useContext } from 'react';
import {
  EventViewMedicationForm,
  MedicationPrescribeResponse,
  Medicine,
  MedicineShoppingBagInfo,
  ViewMedicationForm,
  ViewMedicationFormType,
} from '@tutum/hermes/bff/app_mvz_medicine';
import {
  addKeytab,
  getPredefinedDataOfMedication,
} from '@tutum/hermes/bff/legacy/app_mvz_bmp';
import { KeytabType } from '@tutum/hermes/bff/service_domains_bmp';
import { medicationActions } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';

const Provider: React.FC<
  React.PropsWithChildren<{ value: IMedicationContext }>
> = ({ children, value }) => {
  const [medicationState, setMedicationState] = useState<IMedicationContext>({
    ...defaultContext,
    ...value,
    isLoading: false,
  });

  useEffect(() => {
    if (value) {
      setMedicationState((prevState) => ({ ...prevState, ...value }));
    }
  }, [value]);

  const setViewMedicationForm = (payload: EventViewMedicationForm) => {
    const { viewMedicationForm, eventType } = payload;
    if (eventType === ViewMedicationFormType.ViewForm) {
      setMedicationState((prevState) => ({
        ...prevState,
        viewFormInfo: viewMedicationForm,
        showPrintReview: true,
        editingMedicine: undefined,
      }));
    } else if (eventType === ViewMedicationFormType.ChangeTreatmentDoctor) {
      setMedicationState((prevState) => ({
        ...prevState,
        viewFormInfo: {
          ...prevState.viewFormInfo!,
          treatmentDoctorId: viewMedicationForm.treatmentDoctorId,
        },
      }));
    }
  };

  const setInRefillProcessState = (
    state: boolean,
    refillMedicines: MedicationPrescribeResponse[] = []
  ) => {
    setMedicationState((prevState) => ({
      ...prevState,
      isInRefillProcess: false,
      refillMedicine: refillMedicines,
      showPrintReview: false,
      editingMedicine: undefined,
    }));
  };

  const setShowPrintPreviewState = (state: boolean) => {
    setMedicationState((prevState) => ({
      ...prevState,
      editingMedicine: undefined,
      showPrintReview: state,
      refillMedicine: [],
      viewFormInfo: undefined,
    }));
  };

  const setUseMuster16ForOtcDrugLess18YearsOld = (state: boolean) => {
    setMedicationState((prevState) => ({
      ...prevState,
      isUseMuster16ForOtcDrugLess18YearsOld: state,
    }));
  };

  const setEditingMedicine = (medicine: MedicineShoppingBagInfo) => {
    setMedicationState((prevState) => ({
      ...prevState,
      editingMedicine: medicine,
    }));
  };
  const setSecondaryViewMedicine = (medicine: Medicine) => {
    setMedicationState((prevState) => ({
      ...prevState,
      viewSecondaryMedicine: medicine,
    }));
  };

  const setLoadingState = (state: boolean) => {
    setMedicationState((prevState) => ({ ...prevState, isLoading: state }));
  };

  const addHint = (name: string) => {
    const newHint = {
      name,
      type: KeytabType.K_HINT,
    };
    addKeytab(newHint)
      .then(() => {
        setMedicationState((prevState) => ({
          ...prevState,
          hints: [...prevState.hints, newHint],
        }));
      })
      .catch((e) => console.error(e));
  };
  const addReason = (name: string) => {
    const newReason = {
      name,
      type: KeytabType.K_REASON,
    };
    addKeytab(newReason)
      .then(() => {
        setMedicationState((prevState) => ({
          ...prevState,
          reasons: [...prevState.reasons, newReason],
        }));
      })
      .catch((e) => console.error(e));
  };

  const setShowSearchBox = (data: boolean) => {
    setMedicationState((prev: IMedicationContext) => ({
      ...prev,
      showSearchBox: data,
    }));
  };

  const setInRefillProcess = (data: boolean) => {
    setMedicationState((prev) => ({
      ...prev,
      isInRefillProcess: data,
    }));
  };

  const setSecondaryInfo = (medicine: Medicine) => {
    setMedicationState((prev) => ({
      ...prev,
      secondaryInfo: medicine,
    }));
  };

  const setViewFormTimeline = (viewFormInfo: ViewMedicationFormExtends) => {
    setMedicationState((prevState) => ({
      ...prevState,
      viewFormInfo,
      showPrintReview: true,
      editingMedicine: undefined,
    }));
  };

  return (
    <MedicationContext.Provider
      value={{
        ...medicationState,
        setRefillProcessState: setInRefillProcessState,
        setShowPrintPreviewState: setShowPrintPreviewState,
        setUseMuster16ForOtcDrugLess18YearsOld:
          setUseMuster16ForOtcDrugLess18YearsOld,
        setEditingMedication: setEditingMedicine,
        setViewSecondaryMedicine: setSecondaryViewMedicine,
        setLoadingState: setLoadingState,
        addHint,
        addReason,
        setShowSearchBox,
        setInRefillProcess,
        setSecondaryInfo,
        setViewFormTimeline,
        setViewMedicationForm,
        setMedicationState,
      }}
    >
      {children}
    </MedicationContext.Provider>
  );
};

export default Provider;

export const useMedication = () => useContext(MedicationContext);
