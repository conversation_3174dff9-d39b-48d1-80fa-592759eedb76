import {
  EventViewMedicationForm,
  MedicationPrescribeResponse,
  Medicine,
  MedicineShoppingBagInfo,
  ViewMedicationForm,
} from '@tutum/hermes/bff/app_mvz_medicine';
import React from 'react';
import { BmpMedicationPlanKeytab } from '@tutum/hermes/bff/service_domains_bmp';

export interface ViewMedicationFormExtends extends ViewMedicationForm {
  scheinId?: string;
}

export interface IMedicationContext {
  isInRefillProcess: boolean;
  setRefillProcessState: (
    state: boolean,
    refillMedicines: MedicationPrescribeResponse[] | undefined
  ) => void;
  isUseMuster16ForOtcDrugLess18YearsOld: boolean;
  setUseMuster16ForOtcDrugLess18YearsOld: (state: boolean) => void;
  showPrintReview: boolean;
  setShowPrintPreviewState: (state: boolean) => void;
  isLoading: boolean;
  setLoadingState: (state: boolean) => void;
  editingMedicine?: MedicineShoppingBagInfo;
  setEditingMedication: (medicine?: MedicineShoppingBagInfo) => void;
  refillMedicine: MedicationPrescribeResponse[];
  viewSecondaryMedicine?: Medicine;
  setViewSecondaryMedicine: (medicine?: Medicine) => void;
  viewFormInfo?: ViewMedicationFormExtends;
  units: BmpMedicationPlanKeytab[];
  forms: BmpMedicationPlanKeytab[];
  hints: BmpMedicationPlanKeytab[];
  reasons: BmpMedicationPlanKeytab[];
  addHint?: (hint: string) => void;
  addReason?: (reason: string) => void;
  showSearchBox: boolean;
  setShowSearchBox: (data: boolean) => void;
  setInRefillProcess: (data: boolean) => void;
  secondaryInfo?: Medicine;
  setSecondaryInfo: (medicine?: Medicine) => void;
  setViewFormTimeline: (viewFormInfo: ViewMedicationFormExtends) => void;
  setViewMedicationForm: (payload: EventViewMedicationForm) => void;
  setMedicationState: (value) => void;
}

export const defaultContext: IMedicationContext = {
  isInRefillProcess: false,
  setRefillProcessState: (
    state: boolean,
    refillMedicines: MedicationPrescribeResponse[]
  ) => {
    return;
  },
  isUseMuster16ForOtcDrugLess18YearsOld: false,
  setUseMuster16ForOtcDrugLess18YearsOld: (state: boolean) => {
    return;
  },
  showPrintReview: false,
  setShowPrintPreviewState: (state: boolean) => {
    return;
  },
  isLoading: true,
  setLoadingState: (state: boolean) => {
    return;
  },
  editingMedicine: undefined,
  setEditingMedication: (medicine: MedicineShoppingBagInfo) => {
    return;
  },
  refillMedicine: [],
  viewSecondaryMedicine: undefined,
  setViewSecondaryMedicine: (medicine?: Medicine) => {
    return;
  },
  viewFormInfo: undefined,
  units: [],
  reasons: [],
  hints: [],
  forms: [],
  showSearchBox: true,
  setShowSearchBox: (data: boolean) => {
    return;
  },
  setInRefillProcess: (data: boolean) => {
    return;
  },
  secondaryInfo: undefined,
  setSecondaryInfo: (medicine: Medicine) => {
    return;
  },
  setViewFormTimeline: (viewFormInfo: ViewMedicationFormExtends) => {
    return;
  },
  setViewMedicationForm: (payload: EventViewMedicationForm) => {
    return;
  },
  setMedicationState: (value) => {
    return;
  },
};

const MedicationContext =
  React.createContext<IMedicationContext>(defaultContext);

export default MedicationContext;
