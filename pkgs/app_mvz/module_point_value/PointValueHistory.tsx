import React, { memo } from 'react';

import type PointValueI18n from '@tutum/mvz/locales/en/PointValue.json';
import { Flex, H1 } from '@tutum/design-system/components';
import i18n from '@tutum/infrastructure/i18n';
import { Divider } from '@tutum/design-system/components/Core';
import TableHistory from './TableHistory/TableHistory';

export interface PointValueHistoryProps {
  className?: string;
}

const PointValueHistory = (props: PointValueHistoryProps) => {
  const { className } = props;
  const { t } = i18n.useTranslation<keyof typeof PointValueI18n>({
    namespace: 'PointValue',
  });

  return (
    <Flex className={className} column>
      <Flex p="0 16px" align="center">
        <H1 margin="16px 16px 16px 0">{t('title')}</H1>
      </Flex>
      <Divider />
      <TableHistory t={t} />
    </Flex>
  );
};

export default memo(PointValueHistory);
