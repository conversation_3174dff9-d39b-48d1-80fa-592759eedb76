import React, { memo, useMemo, useState } from 'react';

import type PointValueI18n from '@tutum/mvz/locales/en/PointValue.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';

import MoreIcon from '@tutum/mvz/public/images/more-vertical.svg';
import EditIcon from '@tutum/mvz/public/images/edit-2.svg';
import ResetIcon from '@tutum/mvz/public/images/refresh-cw.svg';
import FinishIcon from '@tutum/mvz/public/images/check-circle-solid-2.svg';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import {
  useMutationResetPointValue,
  useQueryGetPointValues,
  PointValue,
  useMutationUpdatePointValue,
} from '@tutum/hermes/bff/legacy/app_mvz_point_value';
import { pointValueActions, usePointValueStore } from '../PointValue.store';
import { PAGE_DEFAULT } from '@tutum/design-system/consts/table';
import i18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import {
  InputGroup,
  Menu,
  MenuItem,
  Popover,
} from '@tutum/design-system/components/Core';
import {
  BodyTextM,
  Button,
  Flex,
  InfoConfirmDialog,
  Tag,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { toPointValueString, customStyles } from '../pointValue.helper';
import { PRICE_EBM } from '@tutum/infrastructure/shared/price-format';
import { REGEX_FLOAT_NUMBER } from '@tutum/infrastructure/utils/match';

interface TableHistoryProps {
  t: IFixedNamespaceTFunction<keyof typeof PointValueI18n>;
}

const TableHistory = (props: TableHistoryProps) => {
  const { t } = props;
  const { pagination } = usePointValueStore();
  const [valueEditing, setValueEditing] = useState('');
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonI18n.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const {
    data: response,
    isLoading,
    isSuccess,
    refetch,
  } = useQueryGetPointValues({
    paginationRequest: pagination,
  });
  const [stage, setStage] = useState<{
    state: 'edit' | 'reset' | 'default';
    year: number | null;
    id?: string;
  }>({
    state: 'default',
    year: null,
  });

  const resetStage = () => {
    setStage({ state: 'default', year: null, id: undefined });
  };

  const onFinishEditing = () => {
    refetch();
    setValueEditing('');
    resetStage();
  };

  const { mutate: resetPointValue, isPending: isResetting } =
    useMutationResetPointValue({
      onSuccess: () => {
        onFinishEditing();
        alertSuccessfully(t('pointValueReset'));
      },
      onError: (err) => {
        alertError(err.message);
      },
    });
  const { mutate: editPointValue, isPending: isEditing } =
    useMutationUpdatePointValue({
      onSuccess: () => {
        onFinishEditing();
        alertSuccessfully(t('pointValueEdited'));
      },
      onError: (err) => {
        alertError(err.message);
      },
    });

  const onChangePage = (page: number) => {
    pointValueActions.setPagination({ ...pagination, page });
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    pointValueActions.setPagination({
      ...pagination,
      page: PAGE_DEFAULT,
      pageSize: currentRowsPerPage,
    });
  };

  const onSubmitEditing = (year: number) => {
    editPointValue({
      pointValue: {
        year,
        value: parseFloat(valueEditing.replace(',', '.')),
      },
    });
  };

  const columns = useMemo((): IDataTableColumn<PointValue>[] => {
    return [
      {
        name: t('startDate'),
        cell: ({ year }) => {
          return datetimeUtil
            .startOf(year.toString(), 'year')
            .format(DATE_FORMAT);
        },
      },
      {
        width: '557px',
        name: t('pointValue'),
        right: true,
        cell: ({ value, year }) => {
          if (stage.state === 'edit' && stage.year === year) {
            return (
              <InputGroup
                autoFocus
                value={valueEditing}
                onChange={(e) => {
                  const value = e.currentTarget.value.replace('.', ',');
                  if (value.match(REGEX_FLOAT_NUMBER)) {
                    setValueEditing(value);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if (stage.year) {
                      onSubmitEditing(stage.year);
                    }
                  }
                  if (e.key === 'Escape') {
                    resetStage();
                  }
                }}
              />
            );
          }

          return `${toPointValueString(value)} ${PRICE_EBM.symbol}`;
        },
      },
      {
        width: '100px',
        cell: ({ isUpdated }) => {
          if (!isUpdated) {
            return;
          }

          return (
            <Tag
              className="sl-updated-tag"
              style={{ margin: 0 }}
              slStyle="fill"
              slState="info"
            >
              {t('updated')}
            </Tag>
          );
        },
      },
      {
        style: {
          padding: 0,
        },
        width: '40px',
        cell: ({ isUpdated, year, value, id }) => {
          if (stage.state === 'edit' && stage.year === year) {
            return (
              <Flex align="center" justify="center" w="100%" h="100%">
                <Button
                  minimal
                  small
                  iconOnly
                  icon={<FinishIcon />}
                  onClick={() => {
                    if (stage.year) {
                      onSubmitEditing(stage.year);
                    }
                  }}
                />
              </Flex>
            );
          }

          return (
            <Flex align="center" justify="center" w="100%">
              {isUpdated ? (
                <Popover
                  className="sl-tooltip-more-icon"
                  placement="bottom-start"
                  content={
                    <Menu className="action-menu">
                      <MenuItem
                        text={
                          <Flex align="center" gap={8}>
                            <EditIcon />
                            <BodyTextM>{t('edit')}</BodyTextM>
                          </Flex>
                        }
                        onClick={() => {
                          setStage({ state: 'edit', year });
                          setValueEditing(value.toString());
                        }}
                      />
                      <MenuItem
                        text={
                          <Flex align="center" gap={8}>
                            <ResetIcon />
                            <BodyTextM>{tButtonActions('reset')}</BodyTextM>
                          </Flex>
                        }
                        onClick={() => {
                          setStage({ state: 'reset', year, id });
                        }}
                      />
                    </Menu>
                  }
                >
                  <MoreIcon className="sl-more-icon" />
                </Popover>
              ) : (
                <Button
                  minimal
                  iconOnly
                  icon={<EditIcon />}
                  onClick={async () => {
                    setStage({ state: 'edit', year });
                    setValueEditing(value.toString());
                  }}
                />
              )}
            </Flex>
          );
        },
      },
    ];
  }, [stage, valueEditing]);

  return (
    <>
      <Table
        columns={columns}
        highlightOnHover
        noHeader
        striped
        fixedHeader
        data={isSuccess ? response.data : []}
        progressPending={isLoading}
        pagination
        paginationServer
        paginationDefaultPage={pagination.page}
        paginationResetDefaultPage
        paginationPerPage={pagination.pageSize}
        paginationTotalRows={isSuccess ? response.meta.total : 0}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
        customStyles={customStyles}
      />
      {stage.state === 'reset' && (
        <InfoConfirmDialog
          disableConfirm={isEditing}
          type="primary"
          isOpen={true}
          title={t('resetModalTitle')}
          confirmText={tButtonActions('yesReset')}
          cancelText={tButtonActions('no')}
          isShowIconTitle={false}
          isCloseButtonShown={false}
          onClose={() => {
            resetStage();
          }}
          onConfirm={() => {
            if (stage.id) {
              resetPointValue({
                pointValueId: stage.id,
              });
            }
          }}
          isLoading={isResetting}
        >
          {t('resetModalContent')}
        </InfoConfirmDialog>
      )}
    </>
  );
};

export default memo(TableHistory);
