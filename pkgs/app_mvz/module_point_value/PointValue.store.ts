import { proxy, useSnapshot } from 'valtio';
import { Order, PaginationRequest } from '@tutum/hermes/bff/legacy/common';
import {
  PAGE_DEFAULT,
  PAGE_SIZE_DEFAULT,
} from '@tutum/design-system/consts/table';

interface PointValueStore {
  pagination: PaginationRequest;
}

interface PointValueActions {
  setPagination: (payload: PaginationRequest) => void;
}

const initStore: PointValueStore = {
  pagination: {
    page: PAGE_DEFAULT,
    pageSize: PAGE_SIZE_DEFAULT,
    order: Order.DESC,
    sortBy: '',
  },
};

const store = proxy<PointValueStore>(initStore);

export const pointValueActions: PointValueActions = {
  setPagination: (payload) => {
    store.pagination = payload;
  },
};

export function usePointValueStore() {
  return useSnapshot(store);
}
