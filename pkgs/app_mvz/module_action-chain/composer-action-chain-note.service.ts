import {
  ComposerRowCommand,
  IComposerRow,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.type';
import { CommonActionChainElementId } from './common-elements';
import { composerActionChainActions } from './composer-action-chain.store';
import domUtils from './dom-utils';
import { ActionInChain } from './type';
import { actionChainActions } from './use-action-chain';
import { generateActionChainCssSelector } from './utils';

function _interactWithComposerCommandInput(
  cb: (composerCommandInput: HTMLInputElement | null) => void
) {
  const composerCommandInputSelector = generateActionChainCssSelector(
    CommonActionChainElementId.COMPOSER_COMMAND_INPUT
  );
  const composerCommandInput = document.querySelector<HTMLInputElement>(
    composerCommandInputSelector
  );

  cb(composerCommandInput);
}

function checkIfComposerIsMounted(): ActionInChain {
  return async () => {
    const composerCssSelector = generateActionChainCssSelector(
      CommonActionChainElementId.COMPOSER
    );
    const composer = await domUtils.waitForElementMount(composerCssSelector);
    composer?.focus();

    // NOTE: disable mouse event on composer
    domUtils.disableClickForElements([composer]);

    return null;
  };
}

function inputComposerCommand(
  composerCommand: ComposerRowCommand
): ActionInChain {
  return async () => {
    _interactWithComposerCommandInput((composerCommandInput) => {
      if (composerCommandInput) {
        composerCommandInput.focus();
        domUtils.setReactInputValue(composerCommandInput, composerCommand);
      }
    });
    return null;
  };
}

function pressSpaceOnKeyboard(): ActionInChain {
  return async () => {
    _interactWithComposerCommandInput((composerCommandInput) => {
      const currentInputValue = composerCommandInput?.getAttribute('value');
      if (composerCommandInput) {
        composerCommandInput.focus();
        const simulatePressSpaceValue = currentInputValue + ' ';
        domUtils.setReactInputValue(
          composerCommandInput,
          simulatePressSpaceValue
        );
      }
    });

    return null;
  };
}

function fillValueIntoComposer(newComposerData: IComposerRow): ActionInChain {
  return async () => {
    composerActionChainActions.setCurrentBlock(newComposerData);
    return null;
  };
}

function handleSubmitActionInComposer(): ActionInChain {
  return async () => {
    try {
      // NOTE: stop the chain
      actionChainActions.pause();

      await composerActionChainActions.submit();

      actionChainActions.resume();
      return null;
    } catch (error) {
      throw error;
    } finally {
      // NOTE: enable mouse event on composer
      const composerCssSelector = generateActionChainCssSelector(
        CommonActionChainElementId.COMPOSER
      );
      const composer = await domUtils.waitForElementMount(composerCssSelector);
      if (composer != null) {
        domUtils.enableClickForElements([composer]);
      }
    }
  };
}

export function generateComposerActionsInChain(
  composerBlockData: IComposerRow
): ActionInChain[] {
  return [
    // NOTE: check if composer mounted
    checkIfComposerIsMounted(),
    // NOTE: set composer command
    // inputComposerCommand(composerBlockData.command as ComposerRowCommand),
    // NOTE: press space
    // pressSpaceOnKeyboard(),
    // NOTE: fill value
    fillValueIntoComposer(composerBlockData),
    // NOTE: call submit
    handleSubmitActionInComposer(),
  ];
}
