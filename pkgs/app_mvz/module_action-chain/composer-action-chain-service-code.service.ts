import { TEST_ID } from '@tutum/design-system/lexical/components/PlainTextEditor/PlainTextEditor.constant';
import {
  ComposerRowCommand,
  IComposerRow,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.type';
import { CommonActionChainElementId } from './common-elements';
import { composerActionChainActions } from './composer-action-chain.store';
// import { composerActionChainActions } from './composer-action-chain.store';
import domUtils from './dom-utils';
import { ActionInChain } from './type';
import { generateActionChainCssSelector } from './utils';

function _interactWithComposerCommandInput(
  cb: (composerCommandInput: HTMLInputElement | null) => void
) {
  const composerCommandInputSelector = generateActionChainCssSelector(
    CommonActionChainElementId.COMPOSER_COMMAND_INPUT
  );
  const composerCommandInput = document.querySelector<HTMLInputElement>(
    composerCommandInputSelector
  );

  cb(composerCommandInput);
}

function checkIfComposerIsMounted(): ActionInChain {
  return async () => {
    const composerCssSelector = generateActionChainCssSelector(
      CommonActionChainElementId.COMPOSER
    );
    const composer = await domUtils.waitForElementMount(composerCssSelector);
    composer?.focus();

    // NOTE: disable mouse event on composer
    // domUtils.disableClickForElements([composer]);

    return null;
  };
}

function inputComposerCommand(
  composerCommand: ComposerRowCommand
): ActionInChain {
  return async () => {
    _interactWithComposerCommandInput((composerCommandInput) => {
      if (composerCommandInput) {
        composerCommandInput.focus();
        domUtils.setReactInputValue(composerCommandInput, composerCommand);
      }
    });
    return null;
  };
}

function pressSpaceOnKeyboard(): ActionInChain {
  return async () => {
    _interactWithComposerCommandInput((composerCommandInput) => {
      if (composerCommandInput) {
        composerCommandInput.focus();

        const event = new KeyboardEvent('keydown', {
          key: ' ',
          code: 'Space',
          keyCode: 32, // Deprecated, but still used in some contexts
          charCode: 32, // Deprecated, but still used in some contexts
          bubbles: true,
        });

        composerCommandInput.dispatchEvent(event);
      }
    });

    return null;
  };
}

function fillValueIntoComposer(newComposerData: IComposerRow): ActionInChain {
  return async () => {
    const serviceInput = await domUtils.waitForElementMount<HTMLInputElement>(
      '#service_node_composer'
    );

    domUtils.setReactInputValue(serviceInput!, newComposerData.code);

    const firstOptionQuery =
      '#react-select-instanceId_service_node_composer-option-0';

    const optionEle = await domUtils.waitForElementMount(firstOptionQuery);

    optionEle?.click();
    composerActionChainActions.setCurrentBlock(newComposerData);

    await domUtils.waitForElementUnmount(firstOptionQuery);

    return null;
  };
}

function handleSubmitActionInComposer(): ActionInChain {
  return async () => {
    try {
      // NOTE: stop the chain

      // await composerActionChainActions.submit();
      const additionalInfoEle = await domUtils.waitForElementMount(
        `.sl-ServiceNodeComponent [data-test-id=${TEST_ID}]`
      );

      const event = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13, // Deprecated, but still used in some contexts
        charCode: 13, // Deprecated, but still used in some contexts
        bubbles: true, // Allow the event to bubble up
      });

      additionalInfoEle?.dispatchEvent(event);

      const composerCommandInputSelector = generateActionChainCssSelector(
        CommonActionChainElementId.COMPOSER_COMMAND_INPUT
      );

      await domUtils.waitForElementMount(composerCommandInputSelector);

      composerActionChainActions.setCurrentBlock(null);

      return null;
    } catch (error) {
      throw error;
    } finally {
      // NOTE: enable mouse event on composer
      const composerCssSelector = generateActionChainCssSelector(
        CommonActionChainElementId.COMPOSER
      );
      await domUtils.waitForElementMount(composerCssSelector);
      // if (composer != null) {
      //   domUtils.enableClickForElements([composer]);
      // }
    }
  };
}

export function generateComposerActionsInChain(
  composerBlockData: IComposerRow
): ActionInChain[] {
  return [
    // NOTE: check if composer mounted
    checkIfComposerIsMounted(),
    // NOTE: set composer command
    inputComposerCommand(composerBlockData.command as ComposerRowCommand),
    // NOTE: press space
    pressSpaceOnKeyboard(),
    // NOTE: fill value
    fillValueIntoComposer(composerBlockData),
    // NOTE: call submit
    handleSubmitActionInComposer(),
  ];
}
