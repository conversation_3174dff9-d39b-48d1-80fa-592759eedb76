import type { ActionInChain } from '../../type';
import { generateActionChainCssSelector } from '../../utils';
import domUtils from '../../dom-utils';
import { PatientJobElementId } from './type';
import { actionChainActions } from '../../use-action-chain';

async function _queryCloseButtons(): Promise<
  [HTMLButtonElement | null, HTMLButtonElement | null]
> {
  // NOTE:  "Save", "Cancel" buttons
  const saveButtonCssSelector = generateActionChainCssSelector(
    PatientJobElementId.DIALOG_SAVE_BUTTON
  );
  const cancelButtonCssSelector = generateActionChainCssSelector(
    PatientJobElementId.DIALOG_CANCEL_BUTTON
  );

  // NOTE: wait til all of the elements are mounted into the DOM
  return await Promise.all([
    domUtils.waitForElementMount<HTMLButtonElement>(saveButtonCssSelector),
    domUtils.waitForElementMount<HTMLButtonElement>(cancelButtonCssSelector),
  ]);
}

function openPatientDialog(): ActionInChain {
  return async () => {
    const buttonOpenDialog =
      await domUtils.waitForElementMount<HTMLSpanElement>(
        generateActionChainCssSelector(PatientJobElementId.DIALOG_TOGGLE_BUTTON)
      );

    buttonOpenDialog?.click();

    const parentCheckboxFieldCssSelector = `${generateActionChainCssSelector(
      PatientJobElementId.CHECKBOX_FIELD
    )}`;

    await domUtils.waitForElementMount<any>(parentCheckboxFieldCssSelector);

    return null;
  };
}

function disableCloseButtonTilHasCloseListeners(): ActionInChain {
  return async () => {
    const closeButtons = await _queryCloseButtons();

    domUtils.disableClickForElements(closeButtons);

    return null;
  };
}

function clickJobCheckbox(): ActionInChain {
  return async () => {
    const checkboxCssSelector = `${generateActionChainCssSelector(
      PatientJobElementId.CHECKBOX_FIELD
    )} [name="employmentInfo.isEmployed"]`;
    const checkbox = await domUtils.waitForElementMount<any>(
      checkboxCssSelector
    );
    const isChecked = checkbox?.checked;
    checkbox?.scrollIntoView({ behavior: 'smooth' });

    if (!isChecked) {
      checkbox?.click();
    }

    const jobField = generateActionChainCssSelector(
      PatientJobElementId.FOCUS_FIELD
    );

    await domUtils.waitForElementMount<HTMLDivElement>(jobField);

    return null;
  };
}

function scrollToEmploymentInfo(): ActionInChain {
  return async () => {
    const input = document.querySelector(
      generateActionChainCssSelector(PatientJobElementId.FOCUS_FIELD)
    );
    input?.scrollIntoView({ behavior: 'smooth' });
    return null;
  };
}

function forcusAndFillData(): ActionInChain {
  return async () => {
    const input = await domUtils.waitForElementMount<HTMLInputElement>(
      generateActionChainCssSelector(PatientJobElementId.FOCUS_FIELD)
    );
    input?.focus();
    return null;
  };
}

function addEventListeners(): ActionInChain {
  return async () => {
    const closeButtons = await _queryCloseButtons();

    // Make sure pointer events are listening
    domUtils.enableClickForElements(closeButtons);

    // NOTE: add click event on cancel, x button

    actionChainActions.pause();

    await domUtils.waitForElementUnmount(
      generateActionChainCssSelector(PatientJobElementId.DIALOG)
    );

    actionChainActions.resume();

    return null;
  };
}

export default function setupPatientJobActions(): ActionInChain[] {
  return [
    openPatientDialog(),
    disableCloseButtonTilHasCloseListeners(),
    clickJobCheckbox(),
    scrollToEmploymentInfo(),
    forcusAndFillData(),
    addEventListeners(),
  ];
}
