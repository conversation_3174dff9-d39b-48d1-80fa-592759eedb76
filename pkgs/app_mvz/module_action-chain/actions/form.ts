import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';

import {
  generateActionChainCssSelector,
  generateActionChainCssSelectorByTestId,
} from '../utils';
import { actionChainActions } from '../use-action-chain';
import type { ActionInChain } from '../type';
import domUtils from '../dom-utils';
import { ActionChainStep } from '@tutum/hermes/bff/action_chain_common';
import { FormName } from '@tutum/hermes/bff/form_common';
import { MedicationElementId } from './medication';

export enum FormElementId {
  FORM = 'form',
  FORM_TAB_BUTTON = 'form-tab-button',
  TIMELINE_TAB_BUTTON = 'form-timeline-tab-button',
  DIALOG_ADD_BUTTON = 'form-dialog-add-button',
  DIALOG_SAVE_BUTTON = 'form-dialog-save-button',
  DIALOG_PRINT_BUTTON = 'form-dialog-print-button',
  DIALOG_TOGGLE_BUTTON = 'form-dialog-toggle-button',
  MEDICATION_TAB_BUTTON = 'form-medication-button',
}

async function _queryCloseButtons(): Promise<(HTMLButtonElement | null)[]> {
  // NOTE:  "Save", "Cancel" buttons
  const printButtonCssSelector = generateActionChainCssSelector(
    FormElementId.DIALOG_PRINT_BUTTON
  );
  const cancelButtonCssSelector = generateActionChainCssSelector(
    FormElementId.DIALOG_SAVE_BUTTON
  );

  // NOTE: wait til all of the elements are mounted into the DOM
  return await Promise.all([
    domUtils.waitForElementMount<HTMLButtonElement>(printButtonCssSelector),
  ]);
}

function goToFormsTab() {
  return () => {
    const buttonTabForm = document.querySelector<HTMLButtonElement>(
      generateActionChainCssSelector(FormElementId.FORM_TAB_BUTTON)
    );

    buttonTabForm?.click();

    return null;
  };
}

function openForm(formId: string = ''): ActionInChain {
  return async () => {
    if (formId === FormName.Muster_16) {
      const buttonAction = document.querySelector<HTMLButtonElement>(
        generateActionChainCssSelectorByTestId(formId)
      );

      buttonAction?.click();

      const input = await domUtils.waitForElementMount<HTMLInputElement>(
        generateActionChainCssSelector(MedicationElementId.INPUT_SEARCH)
      );

      input?.click();

      const prescribeFreeTextBtn =
        await domUtils.waitForElementMount<HTMLButtonElement>(
          generateActionChainCssSelectorByTestId(
            MedicationElementId.PRESCRIBE_MEDICINE_FREETEXT
          )
        );

      prescribeFreeTextBtn?.click();

      const medicineFreeTextInput =
        await domUtils.waitForElementMount<HTMLInputElement>(
          generateActionChainCssSelectorByTestId(
            MedicationElementId.MEDICINE_FREETEXT_INPUT
          )
        );

      medicineFreeTextInput?.focus();
    } else {
      musterFormDialogActions.setFormRuleProcess({ id: formId });
    }

    const dialogCssSelector = generateActionChainCssSelector(
      FormElementId.FORM
    );
    await domUtils.waitForElementMount<HTMLDivElement>(dialogCssSelector);

    return null;
  };
}

function disableCloseButtonTilHasCloseListeners(): ActionInChain {
  return async () => {
    const closeButtons = await _queryCloseButtons();
    domUtils.disableClickForElements(closeButtons);
    return null;
  };
}

function addCloseListeners(): ActionInChain {
  return async () => {
    const closeButtons = await _queryCloseButtons();

    // NOTE: stop the chain
    domUtils.enableClickForElements(closeButtons);

    // NOTE: wait til user click on one of these
    await domUtils.waitForElementUnmount(
      generateActionChainCssSelector(FormElementId.FORM)
    );

    return null;
  };
}

function goToTimelineTab(): ActionInChain {
  return async () => {
    const timeLineTab = document.querySelector<HTMLButtonElement>(
      generateActionChainCssSelector(FormElementId.TIMELINE_TAB_BUTTON)
    );
    timeLineTab?.click();

    return null;
  };
}

export default function setupFormActions(
  formPayload: ActionChainStep['form']
) {
  return [
    goToFormsTab(),
    openForm(formPayload.formId),
    disableCloseButtonTilHasCloseListeners(),
    addCloseListeners(),
    goToTimelineTab(),
  ];
}
