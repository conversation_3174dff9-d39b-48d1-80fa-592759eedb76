import { ActionChainStep } from '@tutum/hermes/bff/action_chain_common';
import { generateActionChainCssSelector } from '../utils';
import { actionChainActions } from '../use-action-chain';
import type { ActionInChain } from '../type';
import domUtils from '../dom-utils';
import { FormElementId } from './form';

export enum MedicationElementId {
  INPUT_SEARCH = 'medication-input-search',
  MEDICATION_TAB_BUTTON = 'medication-tab-button',
  PRESCRIBE_MEDICINE_FREETEXT = 'prescribe-medicine-freetext',
  MEDICINE_FREETEXT_INPUT = 'medicine-freetext-input',
}

function goToMedicationTab() {
  return () => {
    const buttonTabForm = document.querySelector<HTMLButtonElement>(
      generateActionChainCssSelector(FormElementId.MEDICATION_TAB_BUTTON)
    );
    buttonTabForm?.click();

    return null;
  };
}

function searchMedication(name: string): ActionInChain {
  return async () => {
    const input = await domUtils.waitForElementMount<HTMLInputElement>(
      generateActionChainCssSelector(MedicationElementId.INPUT_SEARCH)
    );

    input?.click();
    domUtils.setReactInputValue(input!, name);

    const form = await domUtils.waitForElementMount<HTMLDivElement>(
      generateActionChainCssSelector(FormElementId.FORM)
    );

    if (form) {
      actionChainActions.pause();
      await domUtils.waitForElementUnmount(
        generateActionChainCssSelector(FormElementId.FORM)
      );
      actionChainActions.resume();
    }

    return null;
  };
}

function goToTimelineTab(): ActionInChain {
  return async () => {
    document
      .querySelector<HTMLButtonElement>(
        generateActionChainCssSelector(FormElementId.TIMELINE_TAB_BUTTON)
      )
      ?.click();

    return null;
  };
}

export default function MedicationSchrittsSetup(
  formPayload: ActionChainStep['medication']
) {
  return [
    goToMedicationTab(),
    searchMedication(formPayload.search),
    goToTimelineTab(),
  ];
}
