import type { ActionInChain } from '../type';
import { generateActionChainCssSelector } from '../utils';
import domUtils from '../dom-utils';
import { actionChainActions } from '../use-action-chain';

enum AllergyElementId {
  DIALOG = 'allery-dialog',
  FOCUS_FIELD = 'allery-focus-field',
  DIALOG_ADD_BUTTON = 'allery-dialog-add-button',
  DIALOG_CANCEL_BUTTON = 'allery-dialog-cancel-button',
  DIALOG_SAVE_BUTTON = 'allery-dialog-save-button',
  DIALOG_TOGGLE_BUTTON = 'allery-dialog-toggle-button',
}

async function _queryCloseButtons(): Promise<
  [HTMLButtonElement | null, HTMLButtonElement | null]
> {
  // NOTE:  "Save", "Cancel" buttons
  const saveButtonCssSelector = generateActionChainCssSelector(
    AllergyElementId.DIALOG_SAVE_BUTTON
  );
  const cancelButtonCssSelector = generateActionChainCssSelector(
    AllergyElementId.DIALOG_CANCEL_BUTTON
  );

  // NOTE: wait til all of the elements are mounted into the DOM
  return await Promise.all([
    domUtils.waitForElementMount<HTMLButtonElement>(saveButtonCssSelector),
    domUtils.waitForElementMount<HTMLButtonElement>(cancelButtonCssSelector),
  ]);
}

function openGalleryDialog(): ActionInChain {
  return async () => {
    const buttonOpenDialog =
      await domUtils.waitForElementMount<HTMLSpanElement>(
        generateActionChainCssSelector(AllergyElementId.DIALOG_TOGGLE_BUTTON)
      );

    buttonOpenDialog?.click();

    // NOTE: await the inline popover to open
    const dialogCssSelector = generateActionChainCssSelector(
      AllergyElementId.DIALOG
    );

    await domUtils.waitForElementMount<HTMLDivElement>(dialogCssSelector);

    return null;
  };
}

function disableCloseButtonTilHasCloseListeners(): ActionInChain {
  return async () => {
    const closeButtons = await _queryCloseButtons();
    domUtils.disableClickForElements(closeButtons);
    return null;
  };
}

function addNewAllergy(): ActionInChain {
  return async () => {
    const buttonAddNewAllergy = document.querySelector<HTMLButtonElement>(
      generateActionChainCssSelector(AllergyElementId.DIALOG_ADD_BUTTON)
    );

    buttonAddNewAllergy?.click();

    return null;
  };
}

function focusOnLastAllergyItem(): ActionInChain {
  return async () => {
    const lastInput = await domUtils.waitForElementMount<HTMLInputElement>(
      generateActionChainCssSelector(AllergyElementId.FOCUS_FIELD)
    );

    lastInput?.focus();

    return null;
  };
}

function addCloseListeners(): ActionInChain {
  return async () => {
    const closeButtons = await Promise.all([
      domUtils.waitForElementMount<HTMLSpanElement>(
        generateActionChainCssSelector(AllergyElementId.DIALOG_SAVE_BUTTON)
      ),
      domUtils.waitForElementMount<HTMLSpanElement>(
        generateActionChainCssSelector(AllergyElementId.DIALOG_CANCEL_BUTTON)
      ),
    ]);

    // NOTE: stop the chain
    actionChainActions.pause();

    // Make sure pointer events are listening
    domUtils.enableClickForElements(closeButtons);

    await domUtils.waitForElementUnmount(
      generateActionChainCssSelector(AllergyElementId.DIALOG)
    );

    // NOTE: resumce
    actionChainActions.resume();

    return null;
  };
}

export { AllergyElementId };
export default function setupGalleryActions(): ActionInChain[] {
  return [
    openGalleryDialog(),
    disableCloseButtonTilHasCloseListeners(),
    addNewAllergy(),
    focusOnLastAllergyItem(),
    addCloseListeners(),
  ];
}
