import type { ActionInChain } from '../../type';

import {
  VitalParameterStep,
  VitalParameterCategory,
} from '@tutum/hermes/bff/action_chain_common';
import { generateActionChainCssSelector } from '../../utils';
import domUtils from '../../dom-utils';
import { VitalParameterActionChainElementId } from './type';
import { VITAL_PARAMETER_FIELDS_CSS_SELECTOR } from './constants';
import { actionChainActions } from '../../use-action-chain';
import { isEmpty } from 'lodash';

function handleCheckboxField(checkBox: HTMLInputElement | null) {
  if (isEmpty(checkBox)) {
    return;
  }

  if (checkBox.checked && checkBox.value) {
    return;
  }

  checkBox.click();
}

function isCheckboxField(field: VitalParameterCategory): boolean {
  return (
    field === VitalParameterCategory.VitalParameter_IsPregnant ||
    field === VitalParameterCategory.VitalParameter_IsBreastfeeding
  );
}

type SaveButton = HTMLElement;
type XButton = HTMLElement;
type CancelButton = HTMLElement;

async function queryCloseButtons(): Promise<
  [SaveButton | null, XButton | null, CancelButton | null]
> {
  // NOTE:  "Save", "Cancel", "X" buttons
  const saveButtonCssSelector = generateActionChainCssSelector(
    VitalParameterActionChainElementId.DIALOG_SAVE_BUTTON
  );
  const xButtonCssSelector = generateActionChainCssSelector(
    VitalParameterActionChainElementId.DIALOG_X_BUTTON
  );
  const cancelButtonCssSelector = generateActionChainCssSelector(
    VitalParameterActionChainElementId.DIALOG_CANCEL_BUTTON
  );

  // NOTE: wait til all of the elements are mounted into the DOM
  return await Promise.all([
    domUtils.waitForElementMount(saveButtonCssSelector),
    domUtils.waitForElementMount(xButtonCssSelector),
    domUtils.waitForElementMount(cancelButtonCssSelector),
  ]);
}

function openVitalParameterDialog(): ActionInChain {
  return async () => {
    const dialogToggleButtonSelector = generateActionChainCssSelector(
      VitalParameterActionChainElementId.DIALOG_TOGGLE_BUTTON
    );

    const dialogToggleButton =
      await domUtils.waitForElementMount<HTMLSpanElement>(
        dialogToggleButtonSelector
      );

    dialogToggleButton?.click();

    // NOTE: await the form to open
    const dialogCssSelector = generateActionChainCssSelector(
      VitalParameterActionChainElementId.DIALOG
    );

    await domUtils.waitForElementMount<HTMLDivElement>(dialogCssSelector);

    return null;
  };
}

function disableCloseButtonTilHasCloseListeners(): ActionInChain {
  return async () => {
    const closeButtons = await queryCloseButtons();
    domUtils.disableClickForElements(closeButtons);
    return null;
  };
}

function handlePregnancyInfoIfCollapsed(
  vital: VitalParameterStep
): ActionInChain {
  return async () => {
    if (!vital?.vitalParameterCategories?.length) {
      return null;
    }

    const isRelatedToPregnancy = vital.vitalParameterCategories.some(
      (f) =>
        f === VitalParameterCategory.VitalParameter_IsPregnant ||
        f === VitalParameterCategory.VitalParameter_IsBreastfeeding ||
        f === VitalParameterCategory.VitalParameter_AmountOfBirth ||
        f === VitalParameterCategory.VitalParameter_AmountOfChildren ||
        f === VitalParameterCategory.VitalParameter_AmountOfPregnancies
    );

    if (!isRelatedToPregnancy) {
      return null;
    }

    const pregnantSelector =
      VITAL_PARAMETER_FIELDS_CSS_SELECTOR[
        VitalParameterCategory.VitalParameter_IsPregnant
      ];

    const pregnantCheckBox =
      document.querySelector<HTMLInputElement>(pregnantSelector);

    handleCheckboxField(pregnantCheckBox);

    return null;
  };
}

function autoFocusOnFirstField(vital: VitalParameterStep) {
  return async () => {
    if (!vital?.vitalParameterCategories?.length) {
      return;
    }

    const notCheckboxElements = vital?.vitalParameterCategories
      ?.filter((f) => !isCheckboxField(f))
      .map((f) => {
        const inputSelector = VITAL_PARAMETER_FIELDS_CSS_SELECTOR[f];
        return document.querySelector<HTMLInputElement>(inputSelector);
      });

    if (!notCheckboxElements.length) {
      return;
    }

    const topLeftMostElements =
      domUtils.sortTopLestMostElements(notCheckboxElements as HTMLElement[]);

    const firstElement = topLeftMostElements[0];

    firstElement?.focus();

    return null;
  };
}

function highlightOthersFields(vital: VitalParameterStep) {
  return async () => {
    if (!vital?.vitalParameterCategories?.length) {
      return;
    }
    const otherFields = vital.vitalParameterCategories.slice(1);
    otherFields
      .filter((f) => !isCheckboxField(f))
      .forEach((f) => {
        const inputSelector = VITAL_PARAMETER_FIELDS_CSS_SELECTOR[f];
        const element = document.querySelector<HTMLInputElement>(inputSelector);
        const EVENT = 'focusin';
        // NOTE: create helper text element
        const helperTextDiv = document.createElement('div');
        helperTextDiv.classList.add('bp5-form-helper-text');
        helperTextDiv.innerText = 'Enter new value'; // TODO: translate

        // NOTE: find + warning bp5 form group parent
        const formGroupParent = element?.closest('.bp5-form-group');
        if (formGroupParent) {
          formGroupParent.classList.add('bp5-intent-warning');
          formGroupParent.appendChild(helperTextDiv);
        }

        function eventListener() {
          // NOTE: clean up
          element?.classList.remove('bp5-intent-warning');
          formGroupParent?.classList?.remove('bp5-intent-warning');
          helperTextDiv.remove();
          element?.removeEventListener?.(EVENT, eventListener);
        }

        if (element) {
          element.addEventListener(EVENT, eventListener);
          // NOTE: warning bp5 input
          element.classList.add('bp5-input', 'bp5-intent-warning');
        }
      });
    return null;
  };
}

function addCloseListeners(): ActionInChain {
  return async () => {
    // NOTE: attach event listeners to "Save", "Cancel", "X" button
    const saveButtonCssSelector = generateActionChainCssSelector(
      VitalParameterActionChainElementId.DIALOG_SAVE_BUTTON
    );
    const xButtonCssSelector = generateActionChainCssSelector(
      VitalParameterActionChainElementId.DIALOG_X_BUTTON
    );
    const cancelButtonCssSelector = generateActionChainCssSelector(
      VitalParameterActionChainElementId.DIALOG_CANCEL_BUTTON
    );

    // NOTE: wait til all of the elements are mounted into the DOM
    const closeButtons = await Promise.all([
      domUtils.waitForElementMount(saveButtonCssSelector),
      domUtils.waitForElementMount(xButtonCssSelector),
      domUtils.waitForElementMount(cancelButtonCssSelector),
    ]);

    // Make sure pointer events are listening
    domUtils.enableClickForElements(closeButtons);

    // NOTE: stop the chain
    actionChainActions.pause();

    // NOTE: wait til user click on one of these
    await domUtils.waitTilUserClick(closeButtons);

    // NOTE: resumce
    actionChainActions.resume();

    return null;
  };
}

function handleCheckboxIfAny(vital: VitalParameterStep) {
  return async () => {
    if (!vital?.vitalParameterCategories?.length) {
      return;
    }

    vital.vitalParameterCategories.forEach((f) => {
      if (!isCheckboxField(f)) {
        return;
      }
      const checkboxSelector = VITAL_PARAMETER_FIELDS_CSS_SELECTOR[f];
      const checkboxInput =
        document.querySelector<HTMLInputElement>(checkboxSelector);
      handleCheckboxField(checkboxInput);
    });

    return null;
  };
}

export default function setupVitalParameterActions(
  vital: VitalParameterStep
) {
  return [
    // NOTE: find & open dialog
    openVitalParameterDialog(),
    // NOTE: temp disable close button
    disableCloseButtonTilHasCloseListeners(),
    // NOTE: handle pregnancy info if it is collapsed
    handlePregnancyInfoIfCollapsed(vital),
    // NOTE: highlight on other fields
    highlightOthersFields(vital),
    // NOTE: handle checkbox toggle
    handleCheckboxIfAny(vital),
    // NOTE: auto focus on first field
    autoFocusOnFirstField(vital),
    // NOTE: attach close dialog event listeners
    addCloseListeners(),
  ];
}
