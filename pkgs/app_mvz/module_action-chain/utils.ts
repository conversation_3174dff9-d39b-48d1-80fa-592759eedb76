import { <PERSON><PERSON>hain } from '@tutum/hermes/bff/legacy/app_mvz_action_chain';
import type { ActionInChain } from './type';
import {
  ActionChainCategory,
  ActionChainStep,
} from '@tutum/hermes/bff/legacy/action_chain_common';

export const DATA_SL_ACTION_CHAIN_ELEMENT_ID =
  'data-sl-action-chain-element-id';

export const DATA_TEST_ID = 'data-test-id';
export const RESTRICT_TYPE_STEPS = ['AD', 'L', 'Z'];

export function generateActionChainCssSelector(
  actionChainElementId: string
): string {
  return `[${DATA_SL_ACTION_CHAIN_ELEMENT_ID}=${actionChainElementId}]`;
}

export function generateActionChainCssSelectorByTestId(
  actionChainElementId: string
): string {
  return `[${DATA_TEST_ID}=${actionChainElementId}]`;
}

/**
 * Set data attribute through ref function
 * @param actionChainElementId
 * @returns
 */
export function setAttributeActionChainElementId(actionChainElementId: string) {
  return (element: HTMLElement) => {
    if (!element) return;
    element.setAttribute(DATA_SL_ACTION_CHAIN_ELEMENT_ID, actionChainElementId);
  };
}

/**
 * Set data attribute through props
 * @param actionChainElementId
 * @returns
 */
export function registerActionChainElementId(actionChainElementId: string) {
  return {
    [DATA_SL_ACTION_CHAIN_ELEMENT_ID]: actionChainElementId,
  };
}

/**
 * get skip step info
 * @returns {ActionInChain[]}
 */
export function getSkipStep(actionIndex: number): ActionInChain[] {
  return [
    async () => {
      return {
        name: 'skippingThisStep', // NOTE: translation token
        type: 'skip',
        actionIndex,
      };
    },
  ];
}

export const isStepRestricted = (item: ActionChainStep) => {
  return (
    RESTRICT_TYPE_STEPS.includes(item.diagnose?.diagnose?.command ?? '') ||
    RESTRICT_TYPE_STEPS.includes(item.service?.service?.command ?? '')
    // item.stepCategory === ActionChainCategory.ActionChainCategory_ServiceChain
  );
};

export function isStepInvalid(step: ActionChainStep, invalidSteps: string[]) {
  return (
    invalidSteps.includes(
      step.service?.service?.code ||
      step.service?.goaService?.code ||
      step.diagnose?.diagnose?.code ||
      ''
    ) ||
    step?.serviceChain?.services?.some((service) =>
      invalidSteps.includes(service.code)
    ) ||
    !step.isActive
  );
}

export const getRestrictedActionType = (item: ActionChain) => {
  const { steps = [] } = item;

  const res: ActionChainStep[] = [];

  for (const step of steps) {
    if (isStepRestricted(step)) {
      res.push(step);
    }
  }

  return res;
};
