export const SL_ACTION_CHAIN_EL_HIGHLIGHT_CLASS =
  'sl-action-chain-el--activated';

// TODO: should have time-out
function waitForElementMount<T extends HTMLElement>(
  selector: string
): Promise<T | null> {
  return new Promise((resolve) => {
    const el = document.querySelector<T>(selector);
    if (el) {
      resolve(el);
      return;
    }
    new MutationObserver((_, observer) => {
      // Query for elements matching the specified selector
      Array.from(document.querySelectorAll<T>(selector)).forEach((element) => {
        resolve(element);
        //Once we have resolved we don't need the observer anymore.
        observer.disconnect();
      });
    }).observe(document.documentElement, {
      childList: true,
      subtree: true,
    });
  });
}

function waitForInputHasValue<T extends HTMLInputElement>(
  selector: string
): Promise<T | null> {
  return new Promise((resolve) => {
    let lastValue = '';
    const checkAndResolve = (el: T) => {
      if (
        el.value.trim() !== '' &&
        el.value !== lastValue &&
        +el.value.replace(',', '.') > 0
      ) {
        lastValue = el.value;
        resolve(el);
        clearInterval(polling);
        return true;
      }
      return false;
    };
    const polling = setInterval(() => {
      document.querySelectorAll<T>(selector).forEach((element) => {
        checkAndResolve(element);
      });
    }, 500);
  });
}

function waitForAnyElementMount<T extends HTMLElement>(
  selector: string
): Promise<T | null> {
  return new Promise((resolve) => {
    const el = document.querySelectorAll<T>(selector) ?? [];
    if (el.length) {
      resolve(el[0]);
      return;
    }
    new MutationObserver((_, observer) => {
      Array.from(document.querySelectorAll<T>(selector)).some((element) => {
        resolve(element);
        observer.disconnect();
      });
    }).observe(document.documentElement, {
      childList: true,
      subtree: true,
    });
  });
}

function resolveUnmount(cb: Function) {
  setTimeout(() => {
    cb(null);
  }, 1000);
}

function waitForElementUnmount(selector: string): Promise<null> {
  return new Promise((resolve) => {
    const el = document.querySelector(selector);

    if (!el) {
      resolveUnmount(resolve);
      return;
    }

    new MutationObserver((_, observer) => {
      // Query for elements matching the specified selector
      const isUnmounted = Array.from(document.querySelectorAll(selector)).every(
        (el) => el == null
      );

      if (isUnmounted) {
        resolveUnmount(resolve);

        observer.disconnect();
      }
    }).observe(document.documentElement, {
      childList: true,
      subtree: true,
    });
  });
}

function waitTilUserClick(els: (HTMLElement | null)[]) {
  return new Promise((resolve) => {
    function handler() {
      els.forEach((el) => {
        el?.removeEventListener('click', handler);
      });
      resolve(null);
    }

    els.forEach((el) => {
      el?.addEventListener('click', handler);
    });
  });
}

/**
 * Not supported by Opera Mini and IE 10
 * @param elments
 */
function disableClickForElements(elements: (HTMLElement | null)[]) {
  if (!elements.length) {
    return;
  }

  for (const el of elements) {
    if (!el) {
      continue;
    }

    el.style.pointerEvents = 'none';
  }
}

/**
 *
 * @param elments
 */
function enableClickForElements(elements: (HTMLElement | null)[]) {
  if (!elements.length) {
    return;
  }
  elements.forEach((el) => {
    if (el &&el.style?.pointerEvents === 'none') {
      el.style.pointerEvents = 'auto';
      el.style.cursor = 'pointer';
    }
  });
}

/**
 *
 * @param elements
 */
function sortTopLestMostElements(elements: HTMLElement[]): HTMLElement[] {
  return [...elements].sort((a, b) => {
    if (!a || !b) {
      return 0;
    }
    const rectA = a.getBoundingClientRect();
    const rectB = b.getBoundingClientRect();
    const { right: ax, top: ay } = rectA;
    const { right: bx, top: by } = rectB;
    return Math.abs(ay - by) <= 3 ? ax - bx : ay - by;
  });
}

/**
 * See [Modify React Component's State using jQuery/Plain Javascript from Chrome Extension](https://stackoverflow.com/q/41166005)
 * See https://github.com/facebook/react/issues/11488#issuecomment-347775628
 * See [How to programmatically fill input elements built with React?](https://stackoverflow.com/q/40894637)
 * See https://github.com/facebook/react/issues/10135#issuecomment-401496776
 *
 * @param {HTMLInputElement} input
 * @param {string} value
 */
function setReactInputValue(input: HTMLInputElement, value: string) {
  const previousValue = input.value;
  input.value = value;
  const tracker = (input as any)._valueTracker;
  if (tracker) {
    tracker.setValue(previousValue);
  }
  // 'change' instead of 'input', see https://github.com/facebook/react/issues/11488#issuecomment-381590324
  input.dispatchEvent(new Event('change', { bubbles: true }));
}

// function highlightElement<T extends HTMLElement>(element: T | null) {
//   element?.classList?.add?.(SL_ACTION_CHAIN_EL_HIGHLIGHT_CLASS);
// }

// function unHighlightElement<T extends HTMLElement>(element: T | null) {
//   element?.classList?.remove?.(SL_ACTION_CHAIN_EL_HIGHLIGHT_CLASS);
// }

// function setupGlobalStyle() {
//   const prevStyleEl = document.querySelector(
//     'style[data-sl-action-chain-global-style=textcss]'
//   );
//   prevStyleEl?.remove();
//   const style = document.createElement('style');
//   style.setAttribute('type', 'text/css');
//   style.setAttribute('data-sl-action-chain-global-style', 'textcss');
//   style.appendChild(
//     document.createTextNode(`.${SL_ACTION_CHAIN_EL_HIGHLIGHT_CLASS} {
//       outline: 2px solid ${InfoBaseHover};
//       outline-offset: 2px;
//     }`)
//   );
//   document.getElementsByTagName('head')[0].appendChild(style);
// }

export default {
  waitForElementMount,
  waitForInputHasValue,
  waitTilUserClick,
  disableClickForElements,
  enableClickForElements,
  sortTopLestMostElements,
  setReactInputValue,
  waitForElementUnmount,
  waitForAnyElementMount,
  // highlightElement,
  // unHighlightElement,
  // setupGlobalStyle,
} as const;
