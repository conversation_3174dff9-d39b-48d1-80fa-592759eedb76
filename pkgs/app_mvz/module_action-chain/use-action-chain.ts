import React from 'react';
import {
  ActionChainCategory,
  ActionChainStep,
  ExecutedActionChainStepStatus,
} from '@tutum/hermes/bff/action_chain_common';
import { proxy, useSnapshot } from 'valtio';

import type ComposerCommandI18n from '@tutum/mvz/locales/en/ComposerCommand.json';

import { executeActionChainGenerator } from './action-chain-generator';
import { ActionInChain, ActionInfo } from './type';
import VitalParameterSchrittsSetup from './actions/vital-parameters';
import DiagnoseSchrittsSetup from './actions/diagnose';
import AllergySchrittsSetup from './actions/allergies';
import PatientJobSetup from './actions/patientJob';
import ServiceSchrittsSetup from './actions/service';
import ServiceChainSchrittsSetup from './actions/serviceChain';
import FreetextSchrittsSetup from './actions/freetext';
import CustomizeSchrittsSetup from './actions/customize';
import { Action<PERSON>hain } from '@tutum/hermes/bff/app_admin_action_chain';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import FormSchrittsSetup from './actions/form';
import MedicationSchrittsSetup from './actions/medication';
import {
  executeActionChain,
  stopExecutingActionChain,
  moveNextStep,
} from '@tutum/hermes/bff/legacy/app_mvz_action_chain';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { alertError, alertSuccessfully } from '@tutum/design-system/components';
import { isStepRestricted } from './utils';

interface IProgressInfo {
  id?: string;
  doctorId: string;
  patientId: string;
}

interface IActionChainActions {
  loadActions: (
    action: ActionChainStep[],
    t: IFixedNamespaceTFunction<keyof typeof ComposerCommandI18n>,
    name?: string
  ) => void;
  start: () => void;
  stop: (isDone?: boolean) => void;
  pause: () => void;
  resume: () => void;
  //
  setIsOpenActionChainTriggerDialog: (isOpen: boolean) => void;
  resetCurrentProcessActionChain: (t) => void;
  setSelectionActionChain: (selectionActionChain: ActionChain | null) => void;
  handleRemoveStep: (stepId: string, t) => void;
  handleRunActionChain: (
    t: IFixedNamespaceTFunction<keyof typeof ComposerCommandI18n>,
    selectedActionChain?: ActionChain
  ) => void;
  addIdToStep: () => ActionStepWithId;
  setProgressInfo: (payload: IProgressInfo) => void;
  setMountedSectionsStatus: (
    payload: Partial<ActionChainSectionMountStatus>
  ) => void;
}

type ActionChainSessionStatus =
  | 'idle'
  | 'paused'
  | 'running'
  | 'loading'
  | 'error';

export interface ActionStepWithId extends ActionChain {
  id: string;
}

type ActionChainSections = 'timline-composer' | 'allergy-sidebar';
type ActionChainSectionMountStatus = Record<ActionChainSections, boolean>;
interface IActionChainStore {
  status: ActionChainSessionStatus;
  timerId: NodeJS.Timer | null;
  stopTimeoutId: NodeJS.Timeout | null;
  actionInChain: ActionInChain[];
  totalActions: number;
  currentAction: ActionInfo | null;
  selectedActionChain: ActionChain | null;
  currentProcessActionChain: ActionStepWithId | null; // cache the current process action chain
  isOpenActionChainTriggerDialog: boolean;
  currentIndexAction: number;
  progressInfo: IProgressInfo;
  isLoadingProcess: boolean;
  sectionsMountStatus: ActionChainSectionMountStatus;
}

const defaultStoreValue: IActionChainStore = {
  status: 'idle',
  timerId: null,
  stopTimeoutId: null,
  actionInChain: [],
  totalActions: 0,
  currentAction: null,
  selectedActionChain: null,
  currentProcessActionChain: null,
  isOpenActionChainTriggerDialog: false,
  currentIndexAction: 0,
  progressInfo: {
    id: '',
    doctorId: '',
    patientId: '',
  },
  isLoadingProcess: false,
  sectionsMountStatus: {
    'timline-composer': false,
    'allergy-sidebar': false,
  },
};

const localStore = proxy<IActionChainStore>(defaultStoreValue);

function _clearIntervals() {
  if (localStore.timerId != null) {
    clearInterval(localStore.timerId);
  }
  if (localStore.stopTimeoutId != null) {
    clearTimeout(localStore.stopTimeoutId);
  }

  localStore.timerId = null;
  localStore.stopTimeoutId = null;
}

function _clearActionInChain() {
  if (localStore.actionInChain?.length) {
    localStore.actionInChain = [];
    localStore.currentIndexAction = 0;
  }
}

function _clearMetaInfo() {
  localStore.currentAction = null;
  localStore.totalActions = 0;
  localStore.currentIndexAction = 0;
}

function _fillActionInfo(
  actionIndex: number,
  name: string,
  nextActionName?: string,
  type?: 'skip' | 'complete'
) {
  return async () => ({
    name,
    type,
    actionIndex,
    nextActionName,
  });
}

function handleMoveNextStep() {
  return moveNextStep({
    id: localStore?.progressInfo?.id || '',
    stepStatus:
      ExecutedActionChainStepStatus.ExecutedActionChainStepStatus_Complete,
  });
}

function _convertStepsToInChain(
  _actions: ActionChainStep[],
  t: IFixedNamespaceTFunction<keyof typeof ComposerCommandI18n>,
  _actionName?: string
): ActionInChain[] {
  const actionsInChain: any[] = [];
  let actionIndex = 1;
  let actions: ActionInChain[] = [];
  for (const [index, step] of _actions.entries()) {
    if (!step) {
      actionIndex = actionIndex + 1;
      continue;
    }

    const nextAction = _actions[actionIndex];

    switch (step.stepCategory) {
      case ActionChainCategory.ActionChainCategory_VitalParameter: {
        actions = VitalParameterSchrittsSetup(step.vitalParameter) as ActionInChain[];
        break;
      }
      case ActionChainCategory.ActionChainCategory_PermanentDiagnose:
      case ActionChainCategory.ActionChainCategory_AcuteDiagnose:
      case ActionChainCategory.ActionChainCategory_AnamnesticDiagnose: {
        actions = DiagnoseSchrittsSetup(step.diagnose);
        break;
      }
      case ActionChainCategory.ActionChainCategory_Allergy: {
        actions = AllergySchrittsSetup();
        break;
      }
      case ActionChainCategory.ActionChainCategory_Job: {
        actions = PatientJobSetup();
        break;
      }
      case ActionChainCategory.ActionChainCategory_Service: {
        actions = ServiceSchrittsSetup(step.service, actionIndex);
        break;
      }
      case ActionChainCategory.ActionChainCategory_ServiceChain: {
        actions = ServiceChainSchrittsSetup(step.serviceChain, actionIndex);
        break;
      }
      case ActionChainCategory.ActionChainCategory_Anamnesis:
      case ActionChainCategory.ActionChainCategory_Note:
      case ActionChainCategory.ActionChainCategory_Findings:
      case ActionChainCategory.ActionChainCategory_Therapy: {
        actions = FreetextSchrittsSetup(step.freeText, t);
        break;
      }
      case ActionChainCategory.ActionChainCategory_Form: {
        actions = FormSchrittsSetup(step.form) as ActionInChain[];
        break;
      }
      case ActionChainCategory.ActionChainCategory_Medication: {
        actions = MedicationSchrittsSetup(step.medication) as ActionInChain[];
        break;
      }
      case ActionChainCategory.ActionChainCategory_Customize: {
        actions = CustomizeSchrittsSetup(step.customize);
        break;
      }
    }

    // NOTE: meta-action updates running action info
    actionsInChain.push(
      _fillActionInfo(actionIndex, step.stepCategory, nextAction?.stepCategory)
    );
    // NOTE: load all actions
    actionsInChain.push(actions);
    actionsInChain.push(() => {
      handleMoveNextStep();
      const actionInfoFn = _fillActionInfo(index + 1, 'next', nextAction?.stepCategory);

      return actionInfoFn();
    });

    actionIndex = actionIndex + 1;
  }

  // Complete step
  actionsInChain.push(
    _fillActionInfo(actionIndex, _actionName || '', undefined, 'complete')
  );
  return actionsInChain.flat();
}

function _createActionChainExecutor(
  _actions: ActionChainStep[],
  t: IFixedNamespaceTFunction<keyof typeof ComposerCommandI18n>,
  _actionName?: string
) {
  const actionInChains = _convertStepsToInChain(_actions, t, _actionName);

  return actionInChains;
}

const INTERVAL_MS = 600;
const CLOSE_PANEL_INTERVAL_MS = 3000;

const actionChainActions: IActionChainActions = {
  loadActions: (
    actionSteps: ActionChainStep[],
    t: IFixedNamespaceTFunction<keyof typeof ComposerCommandI18n>,
    actionName?: string
  ) => {
    try {
      if (!actionSteps?.length) {
        return;
      }
      // NOTE: should clean the state first
      localStore.status = 'loading';
      localStore.actionInChain = _createActionChainExecutor(
        actionSteps,
        t,
        actionName
      );
      localStore.totalActions = actionSteps.length + 0.25; // NOTE: plus 0.25 for the complete step
    } catch (error) {
      localStore.status = 'error';
      throw error;
    } finally {
      localStore.status = 'idle';
    }
  },
  start: () => {
    localStore.status = 'running';
    if (localStore.actionInChain != null) {
      const execute = executeActionChainGenerator(
        localStore.actionInChain.slice(localStore.currentIndexAction)
      );

      if (localStore.timerId == null) {
        localStore.timerId = setInterval(() => {
          execute.next().then((maybeResult) => {
            if (maybeResult?.value?.name !== 'next') {
              localStore.currentIndexAction += 1;
            }
            if (maybeResult?.done) {
              _clearIntervals();
              if (!!maybeResult?.value) {
                handleMoveNextStep();
              }
              scheduleStop();
              return;
            }

            if (maybeResult?.value) {
              localStore.currentAction = { ...maybeResult.value };
            }
          });
        }, INTERVAL_MS);
      }
    }
  },
  stop: async (isDone) => {
    if (localStore?.progressInfo?.id && !isDone) {
      localStore.isLoadingProcess = true;

      await stopExecutingActionChain({
        id: localStore.progressInfo.id,
      });

      localStore.isLoadingProcess = false;
    }

    _clearIntervals();
    _clearMetaInfo(); // NOTE: reset
    _clearActionInChain(); // NOTE: reset

    const { progressInfo, ...otherDefaultValues } = defaultStoreValue;
    Object.assign(localStore, otherDefaultValues);
  },
  pause: () => {
    _clearIntervals();
    localStore.currentIndexAction += 1;
    localStore.status = 'paused';
  },
  resume: () => {
    actionChainActions.start();
  },
  // handle UI logics
  setIsOpenActionChainTriggerDialog: (isOpen: boolean) => {
    localStore.isOpenActionChainTriggerDialog = isOpen;
  },
  addIdToStep: () => {
    const currentStep = localStore.selectedActionChain?.steps;
    const steps = currentStep?.map((step) => {
      return {
        ...step,
        id: getUUID(),
        isRestricted: isStepRestricted(step),
      };
    });
    const stepWithId = {
      ...localStore.selectedActionChain,
      steps,
    };
    return stepWithId as ActionStepWithId;
  },
  resetCurrentProcessActionChain: (t) => {
    const actionChainStepWithId = actionChainActions.addIdToStep();
    localStore.currentProcessActionChain = actionChainStepWithId;
    if (t != null) {
      alertSuccessfully(t('ActionChainTrigger.resetSuccess'));
    }
  },
  setSelectionActionChain: (selectionActionChain: ActionChain) => {
    if (!selectionActionChain) {
      localStore.selectedActionChain = null;
      localStore.currentProcessActionChain = null;
      return;
    }
    localStore.selectedActionChain = selectionActionChain;
    localStore.currentProcessActionChain = actionChainActions.addIdToStep();
    // actionChainActions.setIsOpenActionChainTriggerDialog(true);
  },
  handleRemoveStep: (stepId: string, t) => {
    if (localStore.currentProcessActionChain?.steps?.length === 1) {
      alertError(t('ActionChainTrigger.removeStepError'));
      return;
    }
    const steps = localStore.currentProcessActionChain?.steps?.filter(
      (step) => step['id'] !== stepId
    );
    localStore.currentProcessActionChain = {
      ...localStore.currentProcessActionChain,
      steps,
    } as ActionStepWithId;
  },
  handleRunActionChain: async (
    t: IFixedNamespaceTFunction<keyof typeof ComposerCommandI18n>,
    selectedActionChain: ActionChain
  ) => {
    const executeActionChainObject =
      selectedActionChain || localStore.currentProcessActionChain;
    const { data: res } = await executeActionChain({
      actionChainId: executeActionChainObject?.id || '',
      doctorId: localStore.progressInfo?.doctorId || '',
      patientId: localStore.progressInfo?.patientId || '',
      steps: executeActionChainObject?.steps,
    });
    localStore.progressInfo.id = res?.id;
    actionChainActions.loadActions(
      executeActionChainObject?.steps,
      t,
      selectedActionChain?.name
    );
    actionChainActions.start();
    actionChainActions.setSelectionActionChain(null);
    actionChainActions.setIsOpenActionChainTriggerDialog(false);
  },
  setProgressInfo: (payload) => {
    localStore.progressInfo = payload;
  },
  // NOTE: context
  setMountedSectionsStatus: (mountStatus: ActionChainSectionMountStatus) => {
    localStore.sectionsMountStatus = {
      ...localStore.sectionsMountStatus,
      ...mountStatus,
    };
  },
};

function scheduleStop() {
  // NOTE: clean up first
  if (localStore.stopTimeoutId) {
    clearTimeout(localStore.stopTimeoutId);
    localStore.stopTimeoutId = null;
  }

  // NOTE: assign new timeout id
  localStore.stopTimeoutId = setTimeout(
    () => actionChainActions.stop(true),
    CLOSE_PANEL_INTERVAL_MS
  );
}

function useActionChainStore() {
  const actionChainStore = useSnapshot(localStore);
  React.useEffect(() => _clearIntervals(), []);
  return actionChainStore;
}

export { actionChainActions, useActionChainStore, localStore };
