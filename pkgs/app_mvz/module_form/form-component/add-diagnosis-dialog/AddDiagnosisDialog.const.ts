import {
  Certainty,
  DiagnoseType,
  Laterality,
} from '@tutum/hermes/bff/service_domains_patient_file';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { IDiagnoseSearchResult } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/diagnose-block/DiagnoseBlock.type';

export enum FIELD_NAMES {
  DOCUMENTED_DATE = 'documentedDate',
  TREATING_DOCTOR = 'treatingDoctor',
  DIAGNOSIS = 'diagnosis',
  TYPE_OF_DIAGNOSIS = 'typeOfDiagnosis',
  CERTAINTY = 'certainty',
  LOCATION = 'location',
  IS_ADD_DIAGNOSIS_TO_TIMELINE = 'isAddDiagnosisToTimeline',
  IS_ALWAYS_ADD_TO_TIME_LINE = 'isAlwaysAddToTimeLine',
}

export interface IDiagnosis extends IDiagnoseSearchResult {
  value: string;
  label: string;
}

export interface IFormValues {
  [FIELD_NAMES.DOCUMENTED_DATE]: Date;
  [FIELD_NAMES.TREATING_DOCTOR]: string;
  [FIELD_NAMES.DIAGNOSIS]: IDiagnosis;
  [FIELD_NAMES.TYPE_OF_DIAGNOSIS]: DiagnoseType;
  [FIELD_NAMES.CERTAINTY]: Certainty;
  [FIELD_NAMES.LOCATION]: Laterality;
  [FIELD_NAMES.IS_ADD_DIAGNOSIS_TO_TIMELINE]: boolean;
  [FIELD_NAMES.IS_ALWAYS_ADD_TO_TIME_LINE]: boolean;
}

export const INIT_VALUES: IFormValues = {
  documentedDate: datetimeUtil.date(),
  treatingDoctor: '',
  diagnosis: undefined!,
  typeOfDiagnosis: DiagnoseType.DIAGNOSETYPE_ACUTE,
  certainty: undefined!,
  location: undefined!,
  isAddDiagnosisToTimeline: false,
  isAlwaysAddToTimeLine: false,
};

export const MAP_COMMAND = {
  [DiagnoseType.DIAGNOSETYPE_ACUTE]: 'D',
  [DiagnoseType.DIAGNOSETYPE_ANAMNESTIC]: 'AD',
  [DiagnoseType.DIAGNOSETYPE_PERMANENT]: 'DD',
};

export const TYPE_OF_DIAGNOSIS: DiagnoseType[] = [
  DiagnoseType.DIAGNOSETYPE_ACUTE,
  DiagnoseType.DIAGNOSETYPE_ANAMNESTIC,
  DiagnoseType.DIAGNOSETYPE_PERMANENT,
];
