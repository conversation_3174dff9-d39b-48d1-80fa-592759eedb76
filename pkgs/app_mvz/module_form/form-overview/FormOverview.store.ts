import { alertSuccessfully } from '@tutum/design-system/components';
import { ContractType, MainGroup } from '@tutum/hermes/bff/common';
import {
  Form,
  FormAction,
  FormName,
  FormType,
  PrintType,
} from '@tutum/hermes/bff/form_common';
import { getContractTypeByIds } from '@tutum/hermes/bff/legacy/app_mvz_billing';
import {
  GetFileUrlRequest,
  GetFormsRequest,
  PrescribeRequest,
  PrintRequest,
  getFileUrl,
  getForms,
  getIcdForm,
  prescribeV2,
  print,
  printPlainPdf,
} from '@tutum/hermes/bff/legacy/app_mvz_form';
import { PrintOption } from '@tutum/hermes/bff/legacy/form_common';
import { PatientEnrollmentInformationStatus } from '@tutum/hermes/bff/service_domains_enrollment';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { storeFormTranslation } from '@tutum/mvz/hooks/useFormTranslation';
import { referralThroughTssActions } from '@tutum/mvz/hooks/useReferralThroughTss.store';
import {
  ISettingStore,
  initStore as initSettingStore,
} from '@tutum/mvz/hooks/useSetting.store';
import {
  musterFormDialogActions,
  musterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import {
  patientFileActions,
  patientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { onEditTimelineById } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import PrinterService from '@tutum/mvz/services/printer.service';
import { proxy, useSnapshot } from 'valtio';

interface IFormOverview {
  patient: IPatientProfile | null;
  selectedContractDoctor: ISelectedContractDoctor | null;
  currentFormSetting?: {
    [key: string]: number | boolean | string;
  };
  loadingListForms: boolean;
  listForms: Form[];
  settingStore: ISettingStore;
  icdList: string[];
}

interface IPrintActionProps {
  formItem: Form | undefined;
  printDate: number;
  formAction: FormAction | undefined;
  scheinId?: string;
  toastPrintSuccess?: () => void;
}

export interface IFormOverviewActions {
  loadData: (
    patient: IPatientProfile,
    selectedContractDoctor: ISelectedContractDoctor
  ) => void;
  setPatientInForm: (patient: IPatientProfile) => void;
  setSettingStore: (settingStore: ISettingStore) => void;
  reset: () => void;
  printCoverLetterForm: () => void;
  prescribe: (printDate?: number) => void;
  setCurrentFormSetting: (setting) => void;
  setListForms: (listForms: Form[]) => void;
  loadListForms: (isExistSVDoctor: boolean) => void;
  printBlankFormOrPatientHeader: (props: IPrintActionProps) => void;
  print: (himiPrescriptionId: string) => void;
  getOKV(): Promise<string>;
  getFileUrl(fileName: string): Promise<string>;
  setLoadingPrescribe: (isLoading: boolean) => void;
}

const initStore: IFormOverview = {
  patient: null,
  selectedContractDoctor: null,
  loadingListForms: false,
  listForms: [],
  settingStore: initSettingStore,
  icdList: [],
};

const store = proxy<IFormOverview>(initStore);

async function getContractType(contractId: string) {
  let contractType = ContractType.ContractType_KvContract;
  if (contractId) {
    const contractTypeResult = await getContractTypeByIds({
      contractIds: [contractId],
    });
    contractType = contractTypeResult.data.contractIds[contractId];
  }
  return contractType;
}

const handleNavigateAfterPrinted = async (isClear: boolean) => {
  musterFormDialogStore.isLoadingPrescribe = false;
  const activeTabId = musterFormDialogStore.includeLabResult
    ? ID_TABS.LAB
    : ID_TABS.TIMELINE;
  patientFileActions.setActiveTabId(activeTabId);
  if (isClear) {
    musterFormDialogActions.clear();
    referralThroughTssActions.clear();
  }
};

export const formOverviewActions: IFormOverviewActions = {
  loadData: (
    patient: IPatientProfile,
    selectedContractDoctor: ISelectedContractDoctor
  ) => {
    store.patient = patient;
    store.selectedContractDoctor = selectedContractDoctor;
    musterFormDialogStore.contractDoctor = selectedContractDoctor;
  },
  setPatientInForm: (patient: IPatientProfile) => {
    store.patient = patient;
  },
  setSettingStore: (settingStore: ISettingStore) => {
    store.settingStore = settingStore;
  },
  reset: () => { },
  printCoverLetterForm: async () => {
    if (!musterFormDialogStore.isShowCoverLetterForm) {
      return;
    }

    const resp = await printPlainPdf({
      formSetting: JSON.stringify(musterFormDialogStore.currentFormSetting),
      formName: FormName.Begleitschreiben_FaV_V4,
      treatmentDoctorId: musterFormDialogStore.doctor?.data?.id,
    });

    PrinterService.initAndPrint(
      FormName.Begleitschreiben_FaV_V4,
      async () => {
        return resp.data.formUrl;
      },
      {
        printSuccess: async () => { },
        printFailure: () => { },
      }
    );
  },
  print: async (prescriptionId: string) => {
    if (musterFormDialogStore.isControllable) {
      formOverviewActions.printCoverLetterForm();
      await handleNavigateAfterPrinted(true);
      return;
    }

    const printRequest: PrintRequest = {
      prescribeId: prescriptionId,
      printOption: {
        pdfWithBackground: false, //TODO: need to get from printer profile
        dateOfPrint: datetimeUtil.now(),
        formAction: FormAction.FormAction_PrintFull,
      },
      eAUSetting: musterFormDialogStore.eauSetting,
    };
    const currentFormName = musterFormDialogStore.currentFormName as FormName;

    const prescribeCallback = async (isPdfWithBackGround: boolean) => {
      printRequest.printOption.pdfWithBackground = isPdfWithBackGround;
      const response = await print(printRequest);
      return response.data.printData;
    };
    const printSuccess = async () => {
      alertSuccessfully(storeFormTranslation.translate?.('formPrinted'));

      await handleNavigateAfterPrinted(true);
    };
    const printFailure = (err: Error | any) => {
      musterFormDialogStore.isLoadingPrescribe = false;
      throw err;
    };

    await PrinterService.initAndPrint(
      currentFormName,
      prescribeCallback,
      {
        printSuccess,
        printFailure,
      },
      async () => {
        await onEditTimelineById(prescriptionId, (model: TimelineModel) => {
          if (model.encounterForm) {
            model.encounterForm.prescribe.printedDate = datetimeUtil.now();
            model.encounterForm.prescribe.payload = JSON.stringify(
              musterFormDialogStore.currentFormSetting
            );
          }
          return model;
        });
      },
      printFailure
    ).finally(() => {
      musterFormDialogStore.isLoadingPrescribe = false;
    });
  },
  prescribe: async (printDate?: number) => {
    musterFormDialogStore.isLoadingPrescribe = true;
    const contractId =
      musterFormDialogStore.contractDoctor?.contractId ??
      musterFormDialogStore.prescribeContractId;
    const contractType = await getContractType(contractId || '');
    const currentForm = musterFormDialogStore.currentFormName as FormName;

    let scheinId = musterFormDialogStore.currentSchein?.scheinId;

    if (
      musterFormDialogStore.currentSchein?.scheinMainGroup !== MainGroup.FAV
    ) {
      const isTerminateForm = [
        FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5,
      ].includes(currentForm);
      const currentFavSchein = patientFileStore.schein.list
        .reverse()
        .find(
          (schein) =>
            [MainGroup.FAV].includes(schein.scheinMainGroup) &&
            contractId === schein.hzvContractId
        );

      scheinId =
        (isTerminateForm && currentFavSchein?.scheinId) ||
        musterFormDialogStore.currentSchein?.scheinId;
    }

    if (musterFormDialogStore.isViewForm) {
      formOverviewActions.print(
        musterFormDialogStore.formPrescription?.id || ''
      );
      return;
    }
    // Updates value of type of diagnose, refs: PRO-10070
    // Filled by take over => type of diagnose = 2
    if (currentForm === FormName.Muster_2B) {
      musterFormDialogStore.currentFormSetting!['label_takeover_diagnosis'] =
        musterFormDialogStore.hasEditDiagnoseField ? '2' : '1';
    }

    if (musterFormDialogStore.currentFormSetting) {
      if (currentForm === FormName.Muster_1) {
        if (musterFormDialogStore.currentFormSetting['checkbox_folgebes_0']) {
          musterFormDialogStore.currentFormSetting['date_arbeit_0'] = '';
        }
      }
    }

    let formSettingPayload = musterFormDialogStore.currentFormSetting
      ? JSON.stringify(musterFormDialogStore.currentFormSetting)
      : '';

    if (
      currentForm === FormName.Muster_PTV_11A &&
      musterFormDialogStore.currentFormSetting &&
      !musterFormDialogStore.currentFormSetting[
      'checkbox_beiIhnenWurdenFolgende'
      ]
    ) {
      musterFormDialogStore.currentFormSetting['textbox_icd10_code1'] = '';
      musterFormDialogStore.currentFormSetting['textbox_icd10_code2'] = '';
      musterFormDialogStore.currentFormSetting['textbox_icd10_code3'] = '';
      musterFormDialogStore.currentFormSetting['textbox_diagnose_line1'] = '';
      musterFormDialogStore.currentFormSetting['textbox_diagnose_line2'] = '';
      formSettingPayload = JSON.stringify(
        musterFormDialogStore.currentFormSetting
      );
    }

    const isTerminatedContract =
      !!contractId &&
      patientFileStore.patient.currentContract?.status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Terminated; // for Fav contract
    const printOption: PrintOption = {
      pdfWithBackground: true,
      dateOfPrint: printDate || datetimeUtil.now(),
      formAction: FormAction.FormAction_PrintFull,
    };

    const request: PrescribeRequest = {
      prescribe: {
        id: musterFormDialogStore.himiPrescriptionId,
        doctorId: musterFormDialogStore.contractDoctor?.doctorId || '',
        treatmentDoctorId: musterFormDialogStore.doctor?.data?.id || '',
        patientId: store.patient?.id as string,
        createdDate: datetimeUtil.now(),
        printedDate: printDate,
        payload: formSettingPayload,
        formName: currentForm as FormName,
        formTitle:
          (store.listForms || []).find((item) => item.id === currentForm)
            ?.title ?? currentForm,
        encounterCase:
          musterFormDialogStore.contractDoctor?.encounterCase || '',
        contractType: contractType,
        contractId: contractId,
        prescribeDate: musterFormDialogStore.prescriptionDate as number,
        scheinId,
        isTerminated: isTerminatedContract,
        assignedToBsnrId: musterFormDialogStore.doctor?.data?.bsnrId,
      },
      printOption,
      eAUSetting: musterFormDialogStore.eauSetting,
    };

    if (musterFormDialogStore.isControllable) {
      await prescribeV2(request);

      if (printDate) {
        formOverviewActions.printCoverLetterForm();
      }
      await handleNavigateAfterPrinted(true);
      return;
    }

    if (!printDate) {
      await prescribeV2(request);
      await handleNavigateAfterPrinted(true);

      return;
    }

    let result: any = null;

    const getPdfUrl = async (isPdfWithBackground: boolean) => {
      if (request.printOption) {
        request.printOption.pdfWithBackground = isPdfWithBackground;
      }

      result = await prescribeV2(request);
      return result.data.printInfos.map((r) => r.formUrl);
    };

    const printFailure = (err: Error | any) => {
      musterFormDialogStore.isLoadingPrescribe = false;
      throw err;
    };

    const onClose = async () => {
      await handleNavigateAfterPrinted(true);
    };

    const printSuccess = async () => {
      alertSuccessfully(storeFormTranslation.translate?.('formPrinted'));
      formOverviewActions.printCoverLetterForm();
      await handleNavigateAfterPrinted(true);
    };
    await PrinterService.initAndPrint(
      currentForm,
      getPdfUrl,
      {
        printSuccess,
        printFailure,
        onClose,
      },
      undefined,
      printFailure
    ).finally(() => {
      musterFormDialogStore.isLoadingPrescribe = false;
    });
    return result;
  },
  setCurrentFormSetting: (setting) => {
    store.currentFormSetting = setting;
  },
  setListForms: (listForms: Form[]) => {
    store.listForms = listForms;
  },
  loadListForms: async (isExistSVDoctor: boolean) => {
    if (
      store.selectedContractDoctor &&
      store.patient &&
      patientFileStore.schein.activatedSchein
    ) {
      const okv = await formOverviewActions.getOKV();
      const request: GetFormsRequest = {
        oKV: okv,
        ikNumber: patientFileStore.activeInsurance?.ikNumber as number,
        contractId:
          (isExistSVDoctor &&
            patientFileStore.schein.activatedSchein?.hzvContractId) ||
          '',
        chargeSystemId:
          (isExistSVDoctor &&
            patientFileStore.schein.activatedSchein?.chargeSystemId) ||
          '',
        moduleChargeSystemId:
          (isExistSVDoctor &&
            store.selectedContractDoctor.moduleChargeSystemId) ||
          '',
        scheinMainGroup:
          patientFileStore.schein.activatedSchein?.scheinMainGroup,
      };
      store.loadingListForms = true;
      const result = await getForms(request);

      store.loadingListForms = false;
      store.listForms = result.data.forms.map((item) => {
        if (
          item.formType &&
          [
            FormType.FormType_public_document,
            FormType.FormType_public_contract_text,
          ].includes(item.formType)
        ) {
          return item;
        }
        return item;
      });

      if (isExistSVDoctor && store.selectedContractDoctor.contractId) {
        const icdList = await getIcdForm({
          contractId: store.selectedContractDoctor.contractId,
        });

        store.icdList = icdList.data.icdCodes;
      } else {
        store.icdList = [];
      }
    }
  },
  printBlankFormOrPatientHeader: async ({
    formItem,
    printDate,
    toastPrintSuccess,
    formAction,
    scheinId,
  }) => {
    musterFormDialogStore.isLoadingPrescribe = true;

    const contractId = musterFormDialogStore.contractDoctor?.contractId;
    const contractType = await getContractType(contractId || '');
    const currentForm = formItem?.id as FormName;
    const isSpecialCase = [FormName.Muster_61].includes(currentForm);
    const isHideBackground =
      formItem?.isHzvFav &&
      formItem?.printTypes.includes(PrintType.PrintType_formPrint);
    const payload = printDate
      ? {
        date_prescribe: printDate,
      }
      : {};
    const request: PrescribeRequest = {
      prescribe: {
        // id: getUUID(),
        doctorId: musterFormDialogStore.contractDoctor?.doctorId || '',
        treatmentDoctorId:
          musterFormDialogStore.contractDoctor?.doctorId || '',
        patientId: store.patient?.id as string,
        createdDate: datetimeUtil.now(),
        printedDate: printDate,
        payload: JSON.stringify(payload),
        formName: (isSpecialCase ? `${currentForm}A` : currentForm) as FormName,
        encounterCase: '',
        contractType,
        contractId,
        prescribeDate: printDate,
        scheinId,
      },
      printOption: {
        dateOfPrint: printDate,
        pdfWithBackground: !isHideBackground,
        formAction: formAction as FormAction,
        preventAddToTimeline: true,
      },
    };

    const getPdfUrls = async () => {
      const result = await prescribeV2(request);
      return result.data.printInfos.map((r) => r.formUrl);
    };

    const printFailure = (err: Error | any) => {
      musterFormDialogStore.isLoadingPrescribe = false;
      throw err;
    };

    const printSuccess = async () => {
      toastPrintSuccess && toastPrintSuccess();
      musterFormDialogStore.isLoadingPrescribe = false;
    };

    await PrinterService.initAndPrint(
      formItem?.id || '',
      getPdfUrls,
      {
        printSuccess,
        printFailure,
      },
      undefined,
      printFailure
    );
  },

  getOKV: async () => {
    const selectedContractDoctor = musterFormDialogStore.doctor?.data;
    const ukv = selectedContractDoctor?.bsnr?.substring(0, 2) || undefined;
    const okv = await webWorkerServices.convertUkvToOkv(ukv || '');
    return okv;
  },
  getFileUrl: async (fileName: string) => {
    const payload: GetFileUrlRequest = {
      fileName: fileName,
    };
    const {
      data: { fileUrl },
    } = await getFileUrl(payload);
    return fileUrl;
  },
  setLoadingPrescribe: (isLoading) => {
    musterFormDialogStore.isLoadingPrescribe = isLoading;
  },
};

export function useFormOverviewStore() {
  return useSnapshot(store);
}

export default store;
