import { useEffect } from 'react';
import { isEmpty, uniq, uniqBy } from 'lodash';
import moment from 'moment';
import { proxy, useSnapshot } from 'valtio';
import { addComputed } from 'valtio/utils';

import { searchControllableHimi } from '@tutum/hermes/bff/legacy/app_mvz_himi';
import { getPatientFormProfile } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { Art, Base, Produkt } from '@tutum/hermes/bff/himi_common';
import { FormName, Form as FormItem } from '@tutum/hermes/bff/form_common';
import { LabForm, SendTo } from '@tutum/hermes/bff/lab_common';

import {
  get,
  validateDiagnose,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  Form,
  HeimiForm,
} from '@tutum/hermes/bff/service_domains_patient_file';
import {
  TimelineEntityType,
  TimelineModel,
  IcdErrorTypeCheck,
} from '@tutum/hermes/bff/timeline_common';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { FORM_SETTING_OBJECT } from '@tutum/mvz/constant/form';
import { musterFormActions } from '@tutum/mvz/module_form/muster-form/musterForm.store';
import {
  FREQUENCY_FREE_TEXT_GROUP,
  THERAPY_FREE_TEXT_TYPE,
} from '@tutum/mvz/module_heimi/heimi-search-result/consts';
import { patientSpecificKeySymptom } from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection';
import {
  heimiSelectionActions,
  IHeimiSelectionActions,
  IHeimiSelectionStore,
} from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import {
  Diagnosis,
  getDiagnosisByPatientId,
  IDiagnosis,
} from '@tutum/mvz/module_himi/himi-diagnosis-selector/HimiDiagnosisSelector';
import { AvailableDoctor } from '@tutum/mvz/module_himi/himi-doctor-selector/HimiDoctorSelector';
import { TimePeriod } from '@tutum/mvz/module_himi/himi-prescrible/himi-time-period-selector/HimiTimePeriodSelector';
import {
  ILabActions,
  LabPurpose,
} from '@tutum/mvz/module_lab/lab-results/Lab.store';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import type { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { buildDiagnosisValue } from '../form-component/diagnose-input/DiagnoseInput';
import { defaultTssAddress } from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import {
  AOK_BW_Beratungsbogen_Einbindung_SD,
  AWH_01_Kurzantrag_HZV_KinderReha,
  BKK_BOSCH_VAG_BW_Praeventionsverordnung,
  BKK_VAG_BW_Schnellinformation_Patientenbegleitung,
  BKK_VAG_HE_Schnellinformation_Patientenbegleitung,
  muster52_0Validation,
  validateBKK_BOSCH_BW_Schnellinfo_Patientenbegleitung,
  validateBKK_BY_HZV_Schnellinfo_Patientenbegleitung,
  validateFieldsBegleitschreibenFaV,
  validateFieldsMuster20,
  validateFieldsMuster36E201707,
  validateFieldsMuster55,
  validateFieldsMuster56,
  validateFieldsMuster63,
  validateFieldsMuster64,
  validateFieldsMuster65,
  validationFieldsAmbulantesOperieren,
  validationFieldsMuster1,
  validationFieldsMuster10,
  validationFieldsMuster10A,
  validationFieldsMuster10C,
  validationFieldsMuster12,
  validationFieldsMuster13,
  validationFieldsMuster15,
  validationFieldsMuster16,
  validationFieldsMuster19,
  validationFieldsMuster21,
  validationFieldsMuster28,
  validationFieldsMuster2B,
  validationFieldsMuster3,
  validationFieldsMuster39,
  validationFieldsMuster4,
  validationFieldsMuster5,
  validationFieldsMuster52,
  validationFieldsMuster6,
  validationFieldsMuster61,
  validationFieldsMuster70,
  validationFieldsMuster70A,
  validationFieldsMuster8,
  validationFieldsMuster8A,
  validationFieldsMuster9,
  validationFieldsmusterEmergencyPlan,
  validateFieldsMusterPTV1A,
  validateFieldsMusterPTV11A,
  validateFieldsG81_EHIC,
  validateFieldsMuster7,
  validateFieldsMuster11,
  validateFieldsMuster50,
  validateFieldsMuster51,
  validateFieldsMuster22,
  validateFieldsMuster26,
  validateFieldsMuster27,
  validateFieldsMusterPTV12A,
  validateFieldscontrollable_himi_questionnaire,
  validateFieldMEDI_FA_PT_BW,
  validateFieldAOK_FA_NPPP_BW_GDK_Antragsformular,
  validateFieldBKK_GWQ_FA_PT_BW_Ausschreibeformular,
  validateFieldBKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater,
  validationFieldUeberleitungsbogen,
  validationFieldF1050,
  validationFieldF9990,
  validationFieldAOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3,
} from './formValidator';
import { META_FIELD_DIAGNOSE_TEXT } from '../muster-form/custom-component/musterPTV11';
import { referralStore } from '@tutum/mvz/hooks/useReferralThroughTss.store';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import {
  checkValidICDCode as checkICDCode,
  formatICDCode,
  formatICDCodeVer2,
} from '@tutum/infrastructure/shared/icd-format';
import { IPrinterSetting } from '../../hooks/useSetting.store';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { EAUSetting } from '@tutum/hermes/bff/legacy/eau_common';
import { getDefaultBsnrIdOfDoctor } from '@tutum/mvz/_utils/bsnr';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';

export interface IMusterHeimiPrescribe {
  currentFormName: string;
  heimiFormData: HeimiForm;
  isEditHeimi: boolean;
  isRefill: boolean;
  isViewForm: boolean;
  prescriptionDate: number;
}

type ValidationDiagnosisResponse = {
  isValid: boolean;
  hasCheckReservedICDCode: boolean;
  hasCheckNonExitICDCode: boolean;
};

export interface IMusterPrescribe {
  componentActions?: IHeimiSelectionActions | any;
  componentStore?: any;
  contractDoctor?: ISelectedContractDoctor;
  contractTypeEncounter?: IContractInfo;
  currentFormName?: FormName | string;
  currentFormSetting?: FORM_SETTING_OBJECT;
  currentSchein?: ScheinItem;
  diagnore?: IDiagnosis;
  diagnoseFieldName?: string;
  diagnosis?: string;
  doctor?: AvailableDoctor;
  encounterId?: string;
  formPrescription?: Form;
  formSendToRecipient?: SendTo;
  furtherInformation?: string;
  hasBindingFormData?: boolean;
  heimiFormData?: HeimiForm;
  heimiPrescriptionId?: string;
  himiDeviceGroupNumber?: number;
  himiDeviceName?: string;
  himiFreetextPrescribe: string;
  himiPrescriptionId?: string;
  hint?: string[];
  includeLabResult?: boolean;
  invalidFormArray: IFormFieldValidation[];
  isDuplicatedLabId?: boolean;
  isEditHimi: boolean;
  isLoadingPrescribe?: boolean;
  isOpenAddDiagnosisDialog?: boolean;
  isRefill: boolean;
  isSecondaryDiagnosis?: boolean;
  isValidForm?: boolean;
  enableValidation?: boolean;
  isViewForm: boolean;
  isControllable?: boolean;
  labFormId?: string;
  labFormPurpose?: LabPurpose;
  labId?: string;
  listDiagnosis: Diagnosis[];
  patient?: IPatientProfile;
  patientInfoMap?: {
    [key: string]: string;
  };
  prescriptionDate?: number;
  product?: Produkt;
  productFullName?: string;
  productType?: Art;
  productTypeFullName?: string;
  quantity: number;
  questionName?: string;
  reason?: string;
  secondaryDiagnosis?: string;
  timePeriod: TimePeriod;
  warningFormArray: IFormFieldValidation[];
  isHideEditButton?: boolean;
  hasEditDiagnoseField: boolean;
  validationDiagnosisResult: ValidationDiagnosisResponse;
  printerSettingData?: IPrinterSetting;
  currentEntryForm: string;
  eauSetting?: EAUSetting;
  prescribeContractId?: string;
  formViewScheinId?: string;
  isSupportVSST848: boolean;
  isShowCoverLetterForm: boolean;
  EAUDisable?: boolean;
  actionchainForm: Partial<FormItem>;
}

export interface IFormFieldValidation {
  fieldIndex?: number;
  fieldLabel?: string;
  fieldName: string;
  isInValidMaxLength?: boolean;
  isValid: () => boolean;
  isWarning?: () => boolean;
  getErrors?: () => string;
  getWarnings?: () => string;
  maxLength?: number;
  message?: string;
  hasCheckValidICDCode?: boolean;
  hasCheckPrimaryICDCode?: boolean;
  hasCheckReservedICDCode?: boolean;
  hasCheckNonExitICDCode?: boolean;
}

export const checkValidICDCode = (value: string) => {
  if (!value) {
    return true;
  }

  const [icdCode] = value.split(' ');
  return (
    (!icdCode.includes('.') || icdCode?.length >= 5) && !icdCode.includes('-')
  );
};

export const checkICDCodeNonBilling = (item: TimelineModel) => {
  const nonBilAble = true;
  if (!item?.encounterDiagnoseTimeline?.errors) return nonBilAble;
  const hasAnyNotFillingError = item?.encounterDiagnoseTimeline?.errors.some(
    (err) =>
      err?.errorCode === ErrorCode.ErrorCode_Validation_MessageNotBillingAble ||
      err.errorCode ===
        ErrorCode.ErrorCode_Validation_MessageNotFillingForBilling
  );
  return !hasAnyNotFillingError;
};

export const checkICDCodeReserved = (item: TimelineModel) => {
  const reserved = true;
  if (!item?.encounterDiagnoseTimeline?.errors) return reserved;
  const hasAnyNotFillingError = item?.encounterDiagnoseTimeline?.errors.some(
    (err) =>
      err?.errorCode ===
      ErrorCode.ErrorCode_Validation_MessageNotFillingForBilling
  );
  return !hasAnyNotFillingError;
};

export const filterDiagnoseNonBilling = (diagnosis: Diagnosis) => {
  const nonBilAble = true;
  if (!diagnosis?.errorCode) return nonBilAble;
  const hasAnyNotFillingError = diagnosis?.errorCode.some(
    (err) =>
      err.errorCode === ErrorCode.ErrorCode_Validation_MessageNotBillingAble ||
      err.errorCode ===
        ErrorCode.ErrorCode_Validation_MessageNotFillingForBilling
  );
  return !hasAnyNotFillingError;
};

export const filterDiagnoseReserved = (diagnosis: Diagnosis) => {
  let reserved = true;
  if (!diagnosis?.errorCode) return reserved;
  diagnosis.errorCode?.forEach((err) => {
    if (
      err.errorCode ===
      ErrorCode.ErrorCode_Validation_MessageNotFillingForBilling
    ) {
      reserved = false;
    }
  });
  return reserved;
};

const initStore = {
  prescriptionDate: undefined,
  quantity: 1,
  timePeriod: {
    value: 'No',
    name: '',
  },
  isViewForm: false,
  enableValidation: true,
  isRefill: false,
  isEditHimi: false,
  isControllable: false,
  questionName: undefined,
  himiFreetextPrescribe: '',
  invalidFormArray: [],
  warningFormArray: [],
  labId: '',
  currentFormSetting: undefined,
  labFormPurpose: LabPurpose.Print,
  formSendToRecipient: SendTo.OrderEntrySystem,
  hint: [],
  isOpenAddDiagnosisDialog: false,
  diagnoseFieldName: '',
  listDiagnosis: [],
  includeLabResult: false,
  hasEditDiagnoseField: false,
  validationDiagnosisResult: {
    isValid: true,
    hasCheckReservedICDCode: false,
    hasCheckNonExitICDCode: false,
  },
  currentEntryForm: '',
  eauSetting: undefined,
  formViewScheinId: undefined,
  isSupportVSST848: false,
  isShowCoverLetterForm: false,
  EAUDisable: false,
  actionchainForm: { id: '' },
};

export let musterFormDialogStore = proxy<IMusterPrescribe>(initStore);
// devtools(musterFormDialogStore, 'musterFormDialogStore');

const getFrequencyText = (heimiSelectionStore) => {
  let freeFrequencyFreeText = '';
  if (!heimiSelectionStore.currentGroup?.code) {
    return;
  }

  if (
    FREQUENCY_FREE_TEXT_GROUP.includes(heimiSelectionStore.currentGroup?.code)
  ) {
    // For free text group
    const freeTextValue = heimiSelectionStore.currentTherapyFrequencies?.name;
    if (!freeTextValue) {
      return;
    }
    if (
      heimiSelectionStore.currentFrequencyFreeTextType ===
      THERAPY_FREE_TEXT_TYPE.EVERY_X_WEEKS
    ) {
      freeFrequencyFreeText =
        heimiSelectionStore.currentFrequencyFreeTextType?.replace(
          '...',
          freeTextValue + 'x'
        );
    } else {
      freeFrequencyFreeText = `${freeTextValue}x ${heimiSelectionStore.currentFrequencyFreeTextType}`;
    }
  } else {
    // For other group => select frequency from drop down list or input other value
    const selectedFrequencyValue =
      heimiSelectionStore.currentTherapyFrequencies?.name;
    const selectedFrequencyType =
      heimiSelectionStore.currentTherapyFrequencies?.therapyFrequencyType;
    const frequencyOtherValue = heimiSelectionStore.currentFrequencyOtherValue;
    if (selectedFrequencyValue === 'Other' && !frequencyOtherValue) {
      return;
    }
    if (
      String(selectedFrequencyType) === THERAPY_FREE_TEXT_TYPE.EVERY_X_WEEKS
    ) {
      // elle ... woche case
      if (selectedFrequencyValue === 'Other') {
        freeFrequencyFreeText = selectedFrequencyType.replace(
          '...',
          frequencyOtherValue + 'x'
        );
      } else {
        // 1x, 1-2x
        freeFrequencyFreeText = selectedFrequencyType.replace(
          '...',
          selectedFrequencyValue
        );
      }
    } else {
      // tag, woche, monat, jahr
      if (selectedFrequencyValue === 'Other') {
        freeFrequencyFreeText = `${frequencyOtherValue}x ${selectedFrequencyType}`;
      } else {
        freeFrequencyFreeText = `${selectedFrequencyValue} ${selectedFrequencyType}`;
      }
    }
  }
  return freeFrequencyFreeText;
};

type valFn = (
  store: IMusterPrescribe,
  scheinData?: ScheinItem
) => IFormFieldValidation[];

export const getFormValidationErrors = (fn: valFn): valFn => {
  return (store, scheinData) => {
    return fn(store, scheinData).reduce(
      (fields: IFormFieldValidation[], field, index) => {
        if (
          !field.isValid?.() ||
          field?.isInValidMaxLength ||
          field?.getErrors?.()
        ) {
          fields.push({ ...field, fieldIndex: field.fieldIndex || index + 1 });
        }

        return fields;
      },
      []
    );
  };
};

export const getFormWarnings = (fn: valFn): valFn => {
  return (store, scheinData) => {
    return fn(store, scheinData).reduce(
      (fields: IFormFieldValidation[], field, index) => {
        if (field.isWarning?.() || field?.getWarnings?.()) {
          fields.push({ ...field, fieldIndex: field.fieldIndex || index + 1 });
        }

        return fields;
      },
      []
    );
  };
};

const preFillIcdCode = (
  filteredDiagnose: TimelineModel[],
  fieldQuantity: number,
  cb: (item: TimelineModel, index: number) => void
) => {
  if (filteredDiagnose?.length) {
    Array.from({ length: fieldQuantity }).forEach((_, index) => {
      if (filteredDiagnose[index] && musterFormDialogStore.currentFormSetting) {
        const item = filteredDiagnose[index];
        cb?.(item, index);
      }
    });
  }
};

const preFillBCommandContent = (
  filteredBCommand: TimelineModel[],
  cb: Function,
  preProcessContentCb?: Function
) => {
  let fullContent = filteredBCommand
    .map((item) => item?.encounterNoteTimeline?.note.trim())
    .join(', ');

  if (preProcessContentCb) {
    fullContent = preProcessContentCb(fullContent);
  }

  cb(fullContent);
};

const getFilteredByCommandType = (
  result: TimelineModel[],
  types: TimelineEntityType[],
  cb: (item: TimelineModel) => boolean
) => {
  const filteredData: TimelineModel[] = (result || [])
    .filter((item) => item.type && types.includes(item.type) && cb(item))
    .sort((a, b) => a.selectedDate - b.selectedDate);
  return filteredData;
};

addComputed(musterFormDialogStore, {
  productTypeFullName: (store) => {
    if (!store?.productType) {
      return '';
    }

    if (store.productType.freetext) {
      return store.productType.freetext;
    }

    return `${store.productType.base.gruppe
      .toString()
      .padStart(2, '0')}.${store.productType.base.ort
      .toString()
      .padStart(2, '0')}.${store.productType.base.unter
      .toString()
      .padStart(2, '0')}.${store.productType.artId.toString()}-${
      store.productType.bezeichnung
    }`;
  },
  productFullName: (store) => {
    if (!store.product) {
      return '';
    }
    return `${store.product.base.gruppe
      .toString()
      .padStart(2, '0')}.${store.product.base.ort
      .toString()
      .padStart(2, '0')}.${store.product.base.unter
      .toString()
      .padStart(
        2,
        '0'
      )}.${store.product.artId.toString()}${store.product.produkt
      .toString()
      .padStart(3, '0')}-${store.product.bezeichnung}.${
      store.product.hersteller
    }`;
  },
  himiDeviceName: (store) => {
    if (!store?.productFullName && !store?.productTypeFullName) {
      return '';
    }
    return store.productFullName || store.productTypeFullName;
  },
  himiDeviceGroupNumber: (store): number | undefined => {
    if (!store?.product && !store?.productType) {
      return undefined;
    }
    return store.product
      ? store.product.base.gruppe
      : store?.productType?.base.gruppe;
  },

  isValidForm: (store: IMusterPrescribe): boolean => {
    // Disable validation for imported form from bdt
    if (!store.enableValidation) {
      return true;
    }
    const scheinData = patientFileStore.schein.activatedSchein;
    let invalidFieldArray: IFormFieldValidation[] = [];
    let warningFieldArray: IFormFieldValidation[] = [];

    switch (store.currentFormName) {
      case FormName.Muster_16:
      case FormName.Private: {
        invalidFieldArray = [
          ...getFormValidationErrors(validationFieldsMuster16)(store),
          ...getFormValidationErrors(
            validateFieldscontrollable_himi_questionnaire
          )(store),
        ];
        break;
      }

      case FormName.Muster_15: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster15)(
          store
        );
        break;
      }

      case FormName.Muster_8: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster8)(
          store
        );
        break;
      }

      case FormName.Muster_8A: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster8A)(
          store
        );
        break;
      }

      case FormName.Muster_1: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster1)(
          store
        );
        warningFieldArray = getFormWarnings(validationFieldsMuster1)(store);
        break;
      }

      case FormName.Muster_6: {
        invalidFieldArray = [
          ...getFormValidationErrors(validationFieldsMuster6)(store),
          ...getFormValidationErrors(validateFieldsBegleitschreibenFaV)(store),
        ];
        warningFieldArray = getFormWarnings(validationFieldsMuster6)(store);
        break;
      }

      case FormName.Muster_10: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster10)(
          store
        );
        break;
      }

      case FormName.Muster_10A: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster10A)(
          store,
          scheinData
        );
        break;
      }

      case FormName.Muster_10C: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster10C)(
          store,
          scheinData
        );
        break;
      }

      case FormName.Muster_4: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster4)(
          store
        );
        break;
      }

      case FormName.Muster_13: {
        if (!store.isViewForm) {
          invalidFieldArray = getFormValidationErrors(validationFieldsMuster13)(
            store
          );
          warningFieldArray = getFormWarnings(validationFieldsMuster13)(store);
        }

        break;
      }

      case FormName.Muster_39A: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster39)(
          store
        );
        break;
      }

      case FormName.Muster_2B: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster2B)(
          store
        );
        warningFieldArray = getFormWarnings(validationFieldsMuster2B)(store);
        break;
      }

      case FormName.Muster_3A: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster3)(
          store
        );
        break;
      }

      case FormName.Muster_5:
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster5)(
          store
        );
        break;

      case FormName.Muster_9:
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster9)(
          store
        );
        break;

      case FormName.Muster_12A: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster12)(
          store
        );
        break;
      }

      case FormName.Muster_21:
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster21)(
          store
        );
        break;
      case FormName.Muster_19A:
      case FormName.Muster_19B:
      case FormName.Muster_19C:
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster19)(
          store
        );
        break;

      case FormName.Muster_26A:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster26)(
          store
        );
        break;

      case FormName.Muster_28A:
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster28)(
          store
        );
        break;

      case FormName.BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1: {
        invalidFieldArray = getFormValidationErrors(
          validationFieldsmusterEmergencyPlan
        )(store);
        break;
      }
      case FormName.Ambulantes_Operieren_V1: {
        invalidFieldArray = getFormValidationErrors(
          validationFieldsAmbulantesOperieren
        )(store);
        break;
      }

      case FormName.AOK_BW_Beratungsbogen_Einbindung_SD_V7:
      case FormName.BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10:
        invalidFieldArray = getFormValidationErrors(
          AOK_BW_Beratungsbogen_Einbindung_SD
        )(store);
        break;

      case FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1:
        invalidFieldArray = getFormValidationErrors(
          BKK_BOSCH_VAG_BW_Praeventionsverordnung
        )(store);
        break;
      case FormName.AWH_01_Kurzantrag_HZV_KinderReha_V1:
        invalidFieldArray = getFormValidationErrors(
          AWH_01_Kurzantrag_HZV_KinderReha
        )(store);
        break;

      case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
      case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
      case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
      case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3:
      case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
        invalidFieldArray = getFormValidationErrors(
          validationFieldUeberleitungsbogen
        )(store);
        break;

      case FormName.F1050:
      case FormName.F1000:
      case FormName.F2100:
        invalidFieldArray =
          getFormValidationErrors(validationFieldF1050)(store);
        break;
      case FormName.F9990:
        invalidFieldArray =
          getFormValidationErrors(validationFieldF9990)(store);
        break;

      case FormName.Muster_52_0_V2:
      case FormName.Muster_52_2_V3: {
        invalidFieldArray =
          getFormValidationErrors(muster52_0Validation)(store);
        break;
      }
      case FormName.Muster_36_E_2017_07: {
        invalidFieldArray = getFormValidationErrors(
          validateFieldsMuster36E201707
        )(store);
        warningFieldArray = getFormWarnings(validateFieldsMuster36E201707)(
          store
        );
        break;
      }
      case FormName.AWH_01_Checkliste_Somatik_V1: {
        invalidFieldArray = [];
        break;
      }
      case FormName.Muster_55: {
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster55)(
          store
        );
        break;
      }
      case FormName.Muster_20A:
      case FormName.Muster_20B:
      case FormName.Muster_20C:
      case FormName.Muster_20D:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster20)(
          store
        );
        break;
      case FormName.Muster_N63A: {
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster63)(
          store
        );
        warningFieldArray = getFormWarnings(validateFieldsMuster63)(store);
        break;
      }

      case FormName.Muster_61: {
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster61)(
          store
        );
        break;
      }

      case FormName.Muster_65A: {
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster65)(
          store
        );
        warningFieldArray = getFormWarnings(validateFieldsMuster65)(store);
        break;
      }

      case FormName.Muster_70:
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster70)(
          store
        );
        break;
      case FormName.Muster_70A:
        invalidFieldArray = getFormValidationErrors(validationFieldsMuster70A)(
          store
        );
        break;

      case FormName.Muster_64: {
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster64)(
          store
        );
        break;
      }
      case FormName.Begleitschreiben_FaV_V4: {
        invalidFieldArray = getFormValidationErrors(
          validateFieldsBegleitschreibenFaV
        )(store);
        break;
      }
      case FormName.Muster_56: {
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster56)(
          store
        );
        warningFieldArray = getFormWarnings(validateFieldsMuster56)(store);
        break;
      }

      case FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1: {
        invalidFieldArray = getFormValidationErrors(
          BKK_VAG_HE_Schnellinformation_Patientenbegleitung
        )(store);
        break;
      }
      case FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6: {
        invalidFieldArray = getFormValidationErrors(
          validateBKK_BOSCH_BW_Schnellinfo_Patientenbegleitung
        )(store);
        break;
      }
      case FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6: {
        invalidFieldArray = getFormValidationErrors(
          validateBKK_BY_HZV_Schnellinfo_Patientenbegleitung
        )(store);
        break;
      }
      case FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4: {
        invalidFieldArray = getFormValidationErrors(
          BKK_VAG_BW_Schnellinformation_Patientenbegleitung
        )(store);
        break;
      }

      case FormName.Muster_PTV_1A:
        invalidFieldArray = getFormValidationErrors(validateFieldsMusterPTV1A)(
          store
        );
        break;

      case FormName.Muster_PTV_11A:
        invalidFieldArray = getFormValidationErrors(validateFieldsMusterPTV11A)(
          store
        );
        break;
      case FormName.G81_EHIC_Bulgarisch:
      case FormName.G81_EHIC_Danisch:
      case FormName.G81_EHIC_Englisch:
      case FormName.G81_EHIC_Franzosisch:
      case FormName.G81_EHIC_Griechisch:
      case FormName.G81_EHIC_Italienisch:
      case FormName.G81_EHIC_Kroatisch:
      case FormName.G81_EHIC_Niederlandisch:
      case FormName.G81_EHIC_Polnisch:
      case FormName.G81_EHIC_Rumanisch:
      case FormName.G81_EHIC_Spanisch:
      case FormName.G81_EHIC_Tschechisch:
      case FormName.G81_EHIC_Ungarisch:
      case FormName.G81_EHIC_Finnisch:
      case FormName.G81_EHIC_Estnisch:
      case FormName.G81_EHIC_Slowenisch:
      case FormName.G81_EHIC_Slowakisch:
      case FormName.G81_EHIC_Schwedisch:
      case FormName.G81_EHIC_Portugiesisch:
      case FormName.G81_EHIC_Litauisch:
      case FormName.G81_EHIC_Lettisch:
        invalidFieldArray = getFormValidationErrors(validateFieldsG81_EHIC)(
          store
        );
        break;
      case FormName.Muster_7:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster7)(
          store
        );
        break;
      case FormName.Muster_11:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster11)(
          store
        );
        break;
      case FormName.Muster_50:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster50)(
          store
        );
        break;
      case FormName.Muster_51:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster51)(
          store
        );
        break;
      case FormName.Muster_22A:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster22)(
          store
        );
        break;
      case FormName.Muster_27A:
        invalidFieldArray = getFormValidationErrors(validateFieldsMuster27)(
          store
        );
        break;
      case FormName.Muster_PTV_12A:
        invalidFieldArray = getFormValidationErrors(validateFieldsMusterPTV12A)(
          store
        );
        break;
      case FormName.BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17:
        invalidFieldArray = getFormValidationErrors(validateFieldMEDI_FA_PT_BW)(
          store
        );
        break;
      case FormName.AOK_FA_NPPP_BW_GDK_Antragsformular_V6:
      case FormName.BKK_BOSCH_FA_BW_GDK_Antragsformular_V4:
      case FormName.BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3:
      case FormName.BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2:
      case FormName.AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3:
        invalidFieldArray = getFormValidationErrors(
          validateFieldAOK_FA_NPPP_BW_GDK_Antragsformular
        )(store);
        break;
      case FormName.BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2:
      case FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5:
        invalidFieldArray = getFormValidationErrors(
          validateFieldBKK_GWQ_FA_PT_BW_Ausschreibeformular
        )(store);
        break;
      case FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
      case FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
        invalidFieldArray = getFormValidationErrors(
          validateFieldBKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater
        )(store);
        break;
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3:
        invalidFieldArray = getFormValidationErrors(
          validationFieldAOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3
        )(store);
        break;
      default:
        invalidFieldArray = [];
        warningFieldArray = [];
    }
    musterFormDialogStore.invalidFormArray = invalidFieldArray;
    musterFormDialogStore.warningFormArray = warningFieldArray;
    return !invalidFieldArray.length;
  },
});

export const getDoctorFromContractDoctor = (
  contractDoctor: ISelectedContractDoctor | undefined
): AvailableDoctor => {
  if (!contractDoctor) {
    return {
      value: '',
      label: '',
      data: undefined,
    };
  }

  const doctorAvailable = contractDoctor.availableDoctor.find(
    (d) => d?.id === contractDoctor.doctorId
  );
  if (isEmpty(doctorAvailable)) {
    const tmpDoctor = contractDoctor.availableDoctor[0];
    const bsnrId = getDefaultBsnrIdOfDoctor(tmpDoctor);
    return {
      value: (tmpDoctor?.id && bsnrId && `${tmpDoctor?.id}-${bsnrId}`) || '',
      label: nameUtils.getDoctorName(tmpDoctor),
      data: tmpDoctor,
    };
  }

  return {
    value: `${doctorAvailable.id}-${doctorAvailable.bsnrId}`,
    label: nameUtils.getDoctorName(doctorAvailable),
    data: doctorAvailable,
  };
};

export interface IMusterFormDialogActions {
  clear: () => void;
  clearFormWarningList: () => void;
  clearHint: () => void;
  getListDiagnosis: () => void;
  isFieldHasErrorOrWarning: (
    fieldName: string,
    isHeimiForms?: boolean
  ) => {
    hasError: boolean;
    errorIndex: number;
    hasWarning: boolean;
    warningIndex: number;
  };
  loadFormPatientHeaderInfo: (
    patientID: string,
    doctorID: string,
    formName: string,
    ikNumber?: number,
    bsnrId?: string
  ) => void;
  loadQuestionName: (needLoadQuestionName?: boolean) => void;
  onPrescibleM13: (formName: string) => void;
  preFillTimelineData: (
    patient: IPatientProfile,
    selectedContractDoctor: ISelectedContractDoctor,
    onDiagnoses?: (diagnoses: TimelineModel[]) => void
  ) => void;
  prescrible: (
    printDate?: number,
    hasSupportForm907?: boolean,
    timelineId?: string,
    currentFormSetting?: string
  ) => Promise<void>;
  processHeimiBVBLHMLabel: (heimiSelectionStore) => void;
  processMuster13Store: (setting) => void;
  setComponentValueOnInit: (setting, callback?: () => void) => void;
  setContractDoctor: (contractDoctor?: ISelectedContractDoctor) => void;
  setCurrentFormName: (formName: string) => void;
  setCurrentFormNameByDevice: (himiDevice: Art | Produkt | undefined) => void;
  setCurrentMusterFormSetting: (setting, callback?) => void;
  setCurrentSchein: (currentSchein: ScheinItem) => void;
  setCurrentTreatmentDoctor: (doctor?: AvailableDoctor) => void;
  setDiagnoseFieldName: (diagnoseFieldName: string) => void;
  setDiagnosis: (d?: string) => void;
  setHIMIFreetextPrescribe: (value: string) => void;
  setEditHimi: (isEdit: boolean) => void;
  setViewForm: (isViewForm: boolean) => void;
  setFormSendToRecipient: (recipient: string) => void;
  setFurtherInformation: (info?: string) => void;
  setHint: (hint: string) => void;
  setIncludeLabResult: (value: boolean) => void;
  setIsDuplicatedLabId: (value: boolean) => void;
  setIsOpenAddDiagnosisDialog: (isOpenAddDiagnosisDialog: boolean) => void;
  setIsSecondaryDiagosis: (isSecond: boolean) => void;
  setLabFormPurpose: (type: LabPurpose) => void;
  setLabId: (value: string) => void;
  setPatient: (patient: IPatientProfile) => void;
  setPrescriptionDate: (prescriptionDate?: number) => void;
  setProduct: (product?: Produkt, needLoadQuestionName?: boolean) => void;
  setProductType: (art?: Art, needLoadQuestionName?: boolean) => void;
  setQuantity: (quantity: number) => void;
  setReason: (reason: string) => void;
  setSecondaryDiagnosis: (d?: string) => void;
  setStoreAndAction: (prevComponentStore, prevComponentAction) => void;
  setTimePeriod: (time: TimePeriod) => void;
  setEditDiagnoseField: (hasEditDiagnoseField: boolean) => void;
  setSupportVSST848: (isSupported: boolean) => void;
  viewForm: (
    formInfo: IMusterPrescribe,
    encounterId: string,
    contractTypeEncounter: IContractInfo,
    himiPrescriptionId: string,
    isView: boolean,
    isRefill: boolean,
    isControllable?: boolean,
    scheinId?: string
  ) => Promise<void>;
  viewFormLab: (
    encounterId: string,
    labFormId: string,
    formInfo: LabForm,
    isView: boolean,
    isRefill: boolean,
    componentActions: ILabActions,
    scheinId?: string
  ) => Promise<void>;
  viewHeimiForm: (
    formInfo: IMusterHeimiPrescribe,
    encounterId: string | undefined,
    himiPrescriptionId: string | undefined,
    isView: boolean,
    isRefill: boolean,
    componentActions: IHeimiSelectionActions,
    isHideEditButton?: boolean,
    scheinId?: string,
    treatmentDoctor?: IEmployeeProfile
  ) => Promise<void>;
  fetchDataAuftrag: () => void;
  returnDataAuftrag: () => void;
  returnDataAuftragToPrint: () => void;
  validateDiagnosis: () => Promise<ValidationDiagnosisResponse>;
  setPrinterSettingData: (setting?: IPrinterSetting) => void;
  setCurrentEntryForm: (formId: string) => void;
  setEAUSetting: (payload: EAUSetting) => void;
  setPrescribeContractId: (contractId: string) => void;
  setEnableValidation: (enableValidation: boolean) => void;
  setShowCoverLetter: (isShowCoverLetterForm: boolean) => void;
  setEAUDisable: (disable: boolean) => void;
  setLoadingPrescribe: (isLoading: boolean) => void;
  setFormRuleProcess: (formItem: Partial<FormItem>) => void;
}

export const musterFormDialogActions: IMusterFormDialogActions = {
  setFormRuleProcess: (formItem: FormItem) => {
    musterFormDialogStore.actionchainForm = formItem;
  },
  setIsSecondaryDiagosis: (isSecond) => {
    musterFormDialogStore.isSecondaryDiagnosis = isSecond;
  },
  setReason: (reason: string) => {
    if (!musterFormDialogStore.furtherInformation) {
      musterFormDialogStore.furtherInformation = reason;
    }
    musterFormDialogStore.reason = reason;
  },
  setCurrentFormNameByDevice: (himiDevice: Art | Produkt) => {
    if (!himiDevice) {
      musterFormDialogStore.currentFormName = undefined;
      return;
    }
    let formName = '';
    if (himiDevice?.base?.gruppe == 13) {
      formName = FormName.Muster_15;
    } else if (himiDevice?.base?.gruppe != 25) {
      formName = FormName.Muster_16;
    }
    musterFormDialogStore.currentFormName = formName;
  },
  setCurrentSchein: (currentSchein: ScheinItem) => {
    musterFormDialogStore.currentSchein = currentSchein;
  },
  setCurrentFormName: (formName: string) => {
    musterFormDialogStore.currentFormName = formName;
  },
  setQuantity: (quantity: number) =>
    (musterFormDialogStore.quantity = quantity),
  setPrescriptionDate: (prescriptionDate?: number) => {
    musterFormDialogStore.prescriptionDate = prescriptionDate;
  },
  setPatient: (patient: IPatientProfile) => {
    musterFormDialogStore.patient = patient;
  },
  setProductType: (art?: Art, needLoadQuestionName = true) => {
    musterFormDialogStore.productType = art;
    musterFormDialogActions.setCurrentFormNameByDevice(art);
    musterFormDialogActions.loadQuestionName(needLoadQuestionName);
  },
  setProduct: (product?: Produkt, needLoadQuestionName = true) => {
    musterFormDialogStore.product = product;

    musterFormDialogActions.setCurrentFormNameByDevice(product);
    musterFormDialogActions.loadQuestionName(needLoadQuestionName);
  },
  setHIMIFreetextPrescribe: (value: string) => {
    musterFormDialogStore.himiFreetextPrescribe = value;
  },
  setEditHimi: (isEdit: boolean) => {
    musterFormDialogStore.isEditHimi = isEdit;
    musterFormDialogActions.setProduct(undefined);
    musterFormDialogActions.setProductType(undefined);
  },
  setViewForm: (isViewForm: boolean) => {
    musterFormDialogStore.isViewForm = isViewForm;
  },
  setEditDiagnoseField: (hasEditDiagnoseField: boolean) => {
    musterFormDialogStore.hasEditDiagnoseField = hasEditDiagnoseField;
  },
  setSupportVSST848: (isSupported: boolean) => {
    musterFormDialogStore.isSupportVSST848 = isSupported;
  },
  clear: () => {
    musterFormDialogStore = proxy<IMusterPrescribe>(initStore);
    musterFormDialogStore.currentFormName = undefined;
    musterFormDialogStore.diagnosis = undefined;
    musterFormDialogStore.furtherInformation = undefined;
    musterFormDialogStore.product = undefined;
    musterFormDialogStore.productType = undefined;
    musterFormDialogStore.secondaryDiagnosis = undefined;
    musterFormDialogStore.questionName = undefined;
    musterFormDialogStore.printerSettingData = undefined;
    musterFormDialogStore.himiPrescriptionId = undefined;
    musterFormDialogStore.isRefill = false;
    musterFormDialogStore.isControllable = false;
    musterFormDialogActions.setEditDiagnoseField(false);
    musterFormDialogActions.setCurrentTreatmentDoctor(undefined);
    musterFormDialogActions.setShowCoverLetter(false);
    musterFormDialogActions.setHIMIFreetextPrescribe('');
    musterFormDialogActions.setQuantity(initStore.quantity);
    musterFormDialogActions.setTimePeriod(initStore.timePeriod);
    musterFormDialogActions.setEAUDisable(false);
    musterFormDialogStore.currentFormSetting = {};
    musterFormDialogStore.currentEntryForm = '';
    musterFormDialogStore.eauSetting = undefined;
    musterFormDialogStore.formPrescription = undefined;
    musterFormDialogStore.prescribeContractId = undefined;
    musterFormDialogStore.enableValidation = true;
    musterFormDialogStore.formViewScheinId = undefined;
    if (musterFormDialogStore.isViewForm) {
      musterFormDialogStore.isViewForm = false;
      heimiSelectionActions.clear();
    } else {
      musterFormDialogStore.hasBindingFormData = false;
    }
  },
  fetchDataAuftrag: () => {
    if (musterFormDialogStore.currentFormSetting) {
      if (musterFormDialogStore.currentFormName === FormName.Muster_6) {
        // logic dynamic text module and custom component referral input
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line4_temp'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line4'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line4'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line3'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line3'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line2'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line2'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line1'];
      } else if (
        musterFormDialogStore.currentFormName === FormName.Muster_6_cover_letter
      ) {
        // logic dynamic text module and custom component referral input
        musterFormDialogStore.currentFormSetting[
          'textbox_auftrag_line4_0_temp'
        ] = musterFormDialogStore.currentFormSetting['textbox_auftrag_line4_0'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line4_0'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line3_0'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line3_0'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line2_0'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line2_0'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line1_0'];
      } else {
        musterFormDialogStore.currentFormSetting['textbox_text_line4_temp'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line4'];
        musterFormDialogStore.currentFormSetting['textbox_text_line4'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line3'];
        musterFormDialogStore.currentFormSetting['textbox_text_line3_temp'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line3'];
        musterFormDialogStore.currentFormSetting['textbox_text_line3'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line2'];
      }
    }
  },
  returnDataAuftrag: () => {
    if (musterFormDialogStore.currentFormSetting) {
      if (musterFormDialogStore.currentFormName === FormName.Muster_6) {
        // logic dynamic text module and custom component referral input
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line1'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line2'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line2'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line3'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line3'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line4'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line4'] =
          musterFormDialogStore.currentFormSetting[
            'textbox_auftrag_line4_temp'
          ];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line4_temp'] =
          '';
      } else if (
        musterFormDialogStore.currentFormName === FormName.Muster_6_cover_letter
      ) {
        // logic dynamic text module and custom component referral input
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line1_0'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line2_0'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line2_0'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line3_0'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line3_0'] =
          musterFormDialogStore.currentFormSetting['textbox_auftrag_line4_0'];
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line4_0'] =
          musterFormDialogStore.currentFormSetting[
            'textbox_auftrag_line4_0_temp'
          ];
        musterFormDialogStore.currentFormSetting[
          'textbox_auftrag_line4_0_temp'
        ] = '';
      } else {
        musterFormDialogStore.currentFormSetting['textbox_text_line1'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line3'];
        musterFormDialogStore.currentFormSetting['textbox_text_line2'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line4'];
        musterFormDialogStore.currentFormSetting['textbox_text_line3'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line3_temp'];
        musterFormDialogStore.currentFormSetting['textbox_text_line4'] =
          musterFormDialogStore.currentFormSetting['textbox_text_line4_temp'];
        musterFormDialogStore.currentFormSetting['textbox_text_line3_temp'] =
          '';
        musterFormDialogStore.currentFormSetting['textbox_text_line4_temp'] =
          '';
      }
    }
  },
  returnDataAuftragToPrint: () => {
    if (!referralStore.messegeId) {
      return;
    }

    if (musterFormDialogStore.currentFormSetting) {
      // logic dynamic text module and custom component referral input
      if (musterFormDialogStore.currentFormName === FormName.Muster_6) {
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line1'] =
          referralStore.tssCodeText;
      } else if (
        musterFormDialogStore.currentFormName === FormName.Muster_6_cover_letter
      ) {
        musterFormDialogStore.currentFormSetting['textbox_auftrag_line1_0'] =
          referralStore.tssCodeText;
      } else if (
        [FormName.Muster_PTV_11A, FormName.Muster_PTV_11B].includes(
          musterFormDialogStore.currentFormName as FormName
        )
      ) {
        musterFormDialogStore.currentFormSetting['textbox_text_line1'] =
          referralStore.tssCodeText;
        musterFormDialogStore.currentFormSetting['textbox_text_line2'] =
          defaultTssAddress;
      }
    }
  },
  setCurrentTreatmentDoctor: (doctor?: AvailableDoctor) => {
    if (!doctor) {
      musterFormDialogStore.doctor = getDoctorFromContractDoctor(
        musterFormDialogStore.contractDoctor
      );
      return;
    }
    musterFormDialogStore.doctor = doctor;
    musterFormDialogStore.isDuplicatedLabId = false;
  },
  setFurtherInformation: (info?: string) =>
    (musterFormDialogStore.furtherInformation = info),
  setTimePeriod: (time: TimePeriod) => {
    musterFormDialogStore.timePeriod = time;
  },

  setDiagnosis: async (d?: string) => {
    if (musterFormDialogStore?.secondaryDiagnosis === d) {
      musterFormDialogStore.secondaryDiagnosis = undefined;
    }
    musterFormDialogStore.diagnosis = d;
  },
  setSecondaryDiagnosis: (d?: string) => {
    if (musterFormDialogStore?.secondaryDiagnosis === d) {
      musterFormDialogStore.secondaryDiagnosis = undefined;
    }
    musterFormDialogStore.secondaryDiagnosis = d;
  },

  isFieldHasErrorOrWarning: (fieldName: string, isHeimiForms?: boolean) => {
    const fieldError = musterFormDialogStore.invalidFormArray?.find(
      (field: IFormFieldValidation) => field.fieldName == fieldName
    );
    const fieldWarning = musterFormDialogStore.warningFormArray?.find(
      (field: IFormFieldValidation) => field.fieldName == fieldName
    );
    const combineArray = uniqBy(
      [
        ...(musterFormDialogStore.invalidFormArray || []),
        ...(musterFormDialogStore.warningFormArray || []),
      ],
      'fieldIndex'
    ).sort((a, b) => (a.fieldIndex || -1) - (b.fieldIndex || -1));
    const index =
      combineArray.findIndex((field) => field.fieldName == fieldName) + 1;

    return {
      hasError: !!fieldError,
      errorIndex: isHeimiForms ? index : fieldError?.fieldIndex || -1,
      hasWarning: !!fieldWarning,
      warningIndex: isHeimiForms ? index : fieldWarning?.fieldIndex || -1,
    };
  },

  viewForm: async (
    formInfo: IMusterPrescribe,
    encounterId: string,
    contractTypeEncounter: IContractInfo,
    himiPrescriptionId: string,
    isView: boolean,
    isRefill: boolean,
    isControllable?: boolean,
    scheinId?: string
  ) => {
    if (formInfo.product) {
      musterFormDialogActions.setProduct(formInfo.product, isControllable);
    }
    if (formInfo.productType) {
      musterFormDialogActions.setProductType(
        formInfo.productType,
        isControllable
      );
    }
    if (isView) {
      musterFormDialogActions.setPrescriptionDate(formInfo.prescriptionDate);
    }
    musterFormDialogActions.setDiagnosis(formInfo.diagnosis);
    musterFormDialogActions.setSecondaryDiagnosis(formInfo?.secondaryDiagnosis);
    musterFormDialogActions.setFurtherInformation(formInfo.furtherInformation);
    musterFormDialogActions.setQuantity(formInfo.quantity);
    musterFormDialogActions.setTimePeriod(formInfo.timePeriod);
    musterFormDialogActions.setCurrentTreatmentDoctor(formInfo.doctor);

    musterFormDialogStore.isViewForm = isView;
    musterFormDialogStore.isRefill = isRefill;
    musterFormDialogStore.encounterId = encounterId;
    musterFormDialogStore.contractTypeEncounter = contractTypeEncounter;
    musterFormDialogStore.formPrescription = formInfo.formPrescription;
    musterFormDialogStore.isControllable = isControllable;
    musterFormDialogStore.currentFormName = formInfo.currentFormName;
    musterFormDialogStore.formViewScheinId = scheinId;
    const formSetting = await musterFormActions.getFormSetting(
      musterFormDialogStore.currentFormName
    );
    const currentFormSetting = Object.keys(
      formInfo.currentFormSetting || {}
    ).reduce((formSetting, key) => {
      if (key.includes('date_prescribe') && isRefill) {
        formSetting[key] = null;
      } else {
        formSetting[key] = formInfo.currentFormSetting?.[key];
      }

      return formSetting;
    }, {});
    musterFormDialogStore.currentFormSetting = {
      ...formSetting,
      ...currentFormSetting,
    };
    if (!isRefill) {
      musterFormDialogStore.himiPrescriptionId = himiPrescriptionId;
    }
  },

  // When view heimi form (Muster_13) in timeline
  viewHeimiForm: async (
    formInfo: IMusterHeimiPrescribe,
    encounterId: string | undefined,
    heimiPrescriptionId: string | undefined,
    isView: boolean,
    isRefill: boolean,
    componentActions: IHeimiSelectionActions,
    isHideEditButton: boolean,
    scheinId?: string,
    treatmentDoctor?: IEmployeeProfile
  ) => {
    musterFormDialogStore.currentFormName = FormName.Muster_13;
    const formSetting = await musterFormActions.getFormSetting(
      musterFormDialogStore.currentFormName
    );

    musterFormDialogStore.heimiPrescriptionId = heimiPrescriptionId;

    musterFormDialogStore.encounterId = encounterId;
    musterFormDialogStore.componentActions = componentActions;
    musterFormDialogStore.formViewScheinId = scheinId;
    const formData = formInfo.heimiFormData.prescription;
    musterFormDialogActions.setCurrentTreatmentDoctor({
      value:
        (formData.treatmentDoctorId &&
          treatmentDoctor?.bsnrId &&
          `${formData.treatmentDoctorId}-${treatmentDoctor?.bsnrId}`) ||
        '',
      label: treatmentDoctor?.fullName || '',
      data: treatmentDoctor,
    });
    const currentFormSetting = JSON.parse(formData.formSetting);
    const area_textbox_standard_0 =
      heimiSelectionActions.processTherapiesStandardCombination(
        formData.remedies,
        formData.complementaryRemedies
      );

    const idcContent = `${formData.diagnose?.name || ''}${
      formData.secondaryDiagnose?.name ? '; ' : ''
    } ${formData.secondaryDiagnose?.name || ''}`.trim();

    musterFormDialogStore.currentFormSetting = {
      ...formSetting,
      ...currentFormSetting,
      checkbox_zuzahlungsfrei_0: formData.zuzahLungsFrei,
      checkbox_zuzahlungpflicht_0: formData.zuzahLungsPflicht,
      checkbox_unfallfolgen_0: formData.unfallFolgen,
      checkbox_bvg_0: formData.bvg,
      checkbox_physiotherapie_0: formData.area?.name?.split('.')[0] === 'I',
      checkbox_podologische_0: formData.area?.code?.split('.')[0] === 'II',
      checkbox_schlucktherapie_0: formData.area?.code?.split('.')[0] === 'III',
      checkbox_ergotherapie_0: formData.area?.code?.split('.')[0] === 'IV',
      checkbox_ernahrungstherapie_0: formData.area?.code?.split('.')[0] === 'V',
      area_textbox_icd_code10_0: idcContent.substring(0, 230),
      textbox_heilmittel_line1_0: formData.isStandardCombination
        ? area_textbox_standard_0.substring(0, 51)
        : formData.remedies[0]?.name,
      textbox_heilmittel_line2_0: formData.isStandardCombination
        ? area_textbox_standard_0.substring(51, 101)
        : formData.remedies[1]?.name,
      textbox_heilmittel_line3_0: formData.isStandardCombination
        ? area_textbox_standard_0.substring(101, 150)
        : formData.remedies[2]?.name,
      textbox_erganzendes_0: formData.isStandardCombination
        ? null
        : formData.complementaryRemedies[0]?.name,
      textbox_behandlung_line1_0: formData.isStandardCombination
        ? heimiSelectionActions.viewFormStandardCombinationQuantity(
            formData?.standardCombinationQuantity || 0
          )
        : formData.remedies[0]?.isBlankForm
        ? ''
        : formData.remedies[0]?.quantity,
      textbox_behandlung_line2_0: !formData.isStandardCombination
        ? formData.remedies[1]?.quantity
        : '',
      textbox_behandlung_line3_0: !formData.isStandardCombination
        ? formData.remedies[2]?.quantity
        : '',
      textbox_behandlung_line4_0: !formData.isStandardCombination
        ? formData.complementaryRemedies[0]?.quantity
        : '',
      checkbox_therapiebericht_0: formData.theRapyReport,
      checkbox_hausbesuch_ja_0: formData.homeVisit,
      checkbox_hausbesuch_nein_0: !formData.homeVisit,
      area_textbox_therapieziele_0: formData.theRapyFreeText,
      checkbox_dringlicher_14_tagen_0: formData.urgentTreatment,
      date_prescribe_0: formData.prescribeDate,
      area_textbox_standard_0: formData.isStandardCombination
        ? heimiSelectionActions.processTherapiesStandardCombination(
            formData.remedies,
            formData.complementaryRemedies,
            formData.remediesTimeline
          )
        : '',
    };

    musterFormActions.setFormLabelValue({
      label_icd_code10_line1_0: formData.diagnose?.code,
      label_icd_code10_line2_0: formData.secondaryDiagnose?.code,
      label_diagnose_gruppe_0: formData.diagnoseGroup?.code,
      label_therapie_frequenz_0: formData.therapyFrequency?.name,
    });

    musterFormDialogStore.isViewForm = isView;
    musterFormDialogStore.isRefill = isRefill;
    musterFormDialogActions.setContractDoctor(undefined);
    musterFormDialogStore.isHideEditButton = isHideEditButton;
  },

  setContractDoctor: (contractDoctor?: ISelectedContractDoctor) => {
    if (!contractDoctor) {
      return;
    }

    if (!musterFormDialogStore.doctor?.value) {
      musterFormDialogStore.doctor =
        getDoctorFromContractDoctor(contractDoctor);
    }
    musterFormDialogStore.contractDoctor = contractDoctor;
  },
  loadQuestionName: (needLoadQuestionName?: boolean) => {
    if (
      (!musterFormDialogStore?.productType &&
        !musterFormDialogStore?.product) ||
      !musterFormDialogStore?.contractDoctor?.contractId ||
      !needLoadQuestionName
    ) {
      musterFormDialogStore.questionName = undefined;
      return;
    }

    searchControllableHimi({
      artId:
        musterFormDialogStore?.productType?.artId ||
        musterFormDialogStore?.product?.artId ||
        0,
      base:
        musterFormDialogStore?.productType?.base ||
        (musterFormDialogStore?.product?.base as Base),
    })
      .then((result) => {
        if (result?.data?.steuerbareHilfsmittel?.fragebogen) {
          musterFormDialogStore.questionName =
            result?.data?.steuerbareHilfsmittel?.fragebogen;
        }
      })
      .catch((err) => console.error(err));
  },
  loadFormPatientHeaderInfo: (
    patientID: string,
    doctorID: string,
    formName: string,
    ikNumber?: number,
    bsnrId?: string
  ) => {
    const scheinIdEntry = musterFormDialogStore.formViewScheinId;
    const scheinData = patientFileStore.schein.activatedSchein;
    let scheinId = scheinData?.scheinId;
    if (scheinIdEntry) {
      scheinId = scheinIdEntry;
    }

    getPatientFormProfile({
      patientID: patientID,
      doctorID: doctorID,
      scheinId: scheinId,
      formName,
      bsnrId: bsnrId,
      ikNumber,
    }).then(({ data }) => {
      musterFormDialogStore.patientInfoMap = data?.profile;
    });
  },

  setCurrentMusterFormSetting: async (setting, callback?) => {
    const newObj = {
      ...musterFormDialogStore.currentFormSetting,
      ...setting,
    };
    musterFormDialogStore.currentFormSetting = newObj;
    callback?.();
  },

  // When form open by Prescribe in HeimiSelection
  processMuster13Store: (setting) => {
    const heimiSelectionStore =
      musterFormDialogStore.componentStore as IHeimiSelectionStore;

    const freeFrequencyFreeText = getFrequencyText(heimiSelectionStore) || '';

    const area_textbox_standard_0 =
      heimiSelectionActions.processTherapiesStandardCombination(
        heimiSelectionStore.currentRemedies,
        heimiSelectionStore.currentComplementaryRemedies
      );

    const idcContent = `${heimiSelectionStore.documentDiagnose?.name || ''}${
      heimiSelectionStore.currentSecondDiagnose?.name ? '; ' : ''
    }${heimiSelectionStore.currentSecondDiagnose?.name || ''}`.trim();

    const muster13Props = {
      checkbox_zuzahlungsfrei_0: heimiSelectionStore.zuzahLungsFrei,
      checkbox_zuzahlungpflicht_0: heimiSelectionStore.zuzahLungsPflicht,
      checkbox_unfallfolgen_0: heimiSelectionStore.unfallFolgen,
      checkbox_bvg_0: heimiSelectionStore.bvg,
      checkbox_physiotherapie_0:
        heimiSelectionStore.heimiArea?.name?.split('.')[0] === 'I',
      checkbox_podologische_0:
        heimiSelectionStore.heimiArea?.name?.split('.')[0] === 'II',
      checkbox_schlucktherapie_0:
        heimiSelectionStore.heimiArea?.name?.split('.')[0] === 'III',
      checkbox_ergotherapie_0:
        heimiSelectionStore.heimiArea?.name?.split('.')[0] === 'IV',
      checkbox_ernahrungstherapie_0:
        heimiSelectionStore.heimiArea?.name?.split('.')[0] === 'V',
      area_textbox_icd_code10_0: idcContent.substring(0, 230),
      checkbox_leitsymptomatik_a_0:
        !!heimiSelectionStore.currentSelectedSymptoms?.find(
          (item) => item.value === 'a'
        ),
      checkbox_leitsymptomatik_b_0:
        !!heimiSelectionStore.currentSelectedSymptoms?.find(
          (item) => item.value === 'b'
        ),
      checkbox_leitsymptomatik_c_0:
        !!heimiSelectionStore.currentSelectedSymptoms?.find(
          (item) => item.value === 'c'
        ),
      checkbox_patientenindividuelle_0:
        !!heimiSelectionStore.currentSelectedSymptoms?.find(
          (item) => item.value === patientSpecificKeySymptom
        ),
      textbox_heilmittel_line1_0: heimiSelectionStore.isStandardCombination
        ? area_textbox_standard_0.substring(0, 51)
        : heimiSelectionStore.currentRemedies[0]?.name || null,
      textbox_heilmittel_line2_0: heimiSelectionStore.isStandardCombination
        ? area_textbox_standard_0.substring(51, 101)
        : heimiSelectionStore.currentRemedies[1]?.name || null,
      textbox_heilmittel_line3_0: heimiSelectionStore.isStandardCombination
        ? area_textbox_standard_0.substring(101, 150)
        : heimiSelectionStore.currentRemedies[2]?.name || null,
      textbox_erganzendes_0: heimiSelectionStore.isStandardCombination
        ? null
        : heimiSelectionStore.currentComplementaryRemedies?.[0]?.name,
      textbox_behandlung_line1_0: heimiSelectionStore.isStandardCombination
        ? heimiSelectionStore.standardCombinationQuantity
        : heimiSelectionStore.isChooseBlankForm
        ? null
        : heimiSelectionStore.currentRemedies[0]?.quantity,
      textbox_behandlung_line2_0:
        heimiSelectionStore.currentRemedies[1]?.quantity || null,
      textbox_behandlung_line3_0:
        heimiSelectionStore.currentRemedies[2]?.quantity || null,
      textbox_behandlung_line4_0: heimiSelectionStore.isStandardCombination
        ? null
        : heimiSelectionStore.currentComplementaryRemedies?.[0]?.quantity,
      checkbox_therapiebericht_0: heimiSelectionStore.isTherapyReport,
      checkbox_hausbesuch_ja_0: heimiSelectionStore.isHomeVisit,
      checkbox_hausbesuch_nein_0: !heimiSelectionStore.isHomeVisit,
      area_textbox_leitsymtomatik_0:
        heimiSelectionStore.currentSelectedSymptoms?.length &&
        heimiSelectionStore.currentSelectedSymptoms[0]?.value !==
          patientSpecificKeySymptom
          ? heimiSelectionStore.currentSelectedSymptoms
              .reduce((newArray: string[], item) => {
                if (item.name) {
                  const prefix = `${item.value}-`;
                  const name = item.name.startsWith(prefix)
                    ? item.name.split(prefix)[1].trim()
                    : item.name;

                  newArray.push(name);
                }

                return newArray;
              }, [])
              .join('; ')
          : heimiSelectionStore.keySymptomsFreeText,
      area_textbox_therapieziele_0: heimiSelectionStore.isChooseBlankForm
        ? ''
        : heimiSelectionStore.theRapyFreeText,
      checkbox_dringlicher_14_tagen_0: heimiSelectionStore.urgentTreatment,
      label_icd_code10_line1_0: `${
        heimiSelectionStore.documentDiagnose?.code || ''
      } ${heimiSelectionStore.documentDiagnose?.certainty || ''} ${
        heimiSelectionStore.documentDiagnose?.laterality || ''
      }`,
      label_icd_code10_line2_0: `${
        heimiSelectionStore.currentSecondDiagnose?.code || ''
      } ${heimiSelectionStore.currentSecondDiagnose?.certainty || ''} ${
        heimiSelectionStore.currentSecondDiagnose?.laterality || ''
      }`,
      label_diagnose_gruppe_0: heimiSelectionStore.currentGroup?.code,
      label_therapie_frequenz_0: heimiSelectionStore.isChooseBlankForm
        ? ''
        : freeFrequencyFreeText,
      barcode: '',
    };
    musterFormDialogStore.currentFormSetting = { ...setting, ...muster13Props };
    musterFormActions.setFormLabelValue({
      label_icd_code10_line1_0: `${
        heimiSelectionStore.documentDiagnose?.code || ''
      } ${heimiSelectionStore.documentDiagnose?.certainty || ''} ${
        heimiSelectionStore.documentDiagnose?.laterality || ''
      }`,
      label_icd_code10_line2_0: `${
        heimiSelectionStore.currentSecondDiagnose?.code || ''
      } ${heimiSelectionStore.currentSecondDiagnose?.certainty || ''} ${
        heimiSelectionStore.currentSecondDiagnose?.laterality || ''
      }`,
      label_diagnose_gruppe_0: heimiSelectionStore.currentGroup?.code,
    });
    musterFormDialogStore.hasBindingFormData = true;
  },

  processHeimiBVBLHMLabel: (heimiSelectionStore) => {
    let label;
    if (heimiSelectionStore.indicator?.label === 'BVB') {
      label = 'Besonderer Verordnungsbedarf';
    } else if (heimiSelectionStore.indicator?.label === 'LHM') {
      label = 'Langfristiger Heilmittelbedarf';
    }
    if (heimiSelectionStore.indicator?.date) {
      label += `(${moment(heimiSelectionStore.indicator?.date).format(
        DATE_FORMAT
      )})`;
    }
    return label;
  },

  setStoreAndAction: (prevComponentStore, prevComponentAction) => {
    if (prevComponentStore) {
      musterFormDialogStore.componentStore = prevComponentStore;
    }
    if (prevComponentAction) {
      musterFormDialogStore.componentActions = prevComponentAction;
    }
  },

  prescrible: async (
    printDate?: number,
    hasSupportForm907?: boolean,
    timelineId?: string,
    currentFormSetting?: string
  ) => {
    return musterFormDialogStore.componentActions.prescribe(
      printDate,
      hasSupportForm907,
      timelineId,
      currentFormSetting
    );
  },

  setComponentValueOnInit: (setting, callback) => {
    if (
      musterFormDialogStore.currentFormName !== FormName.Muster_13 &&
      (!musterFormDialogStore.currentFormSetting ||
        isEmpty(musterFormDialogStore.currentFormSetting))
    ) {
      musterFormDialogActions.setCurrentMusterFormSetting(setting, callback);
    }
  },

  // When form prescribe in HeimiSelection
  onPrescibleM13: async (formName: string) => {
    musterFormDialogStore.currentFormName = formName;
    const formSetting = await musterFormActions.getFormSetting(
      musterFormDialogStore.currentFormName
    );
    if (
      !musterFormDialogStore.isViewForm &&
      !musterFormDialogStore.isRefill &&
      !musterFormDialogStore.hasBindingFormData
    ) {
      musterFormDialogActions.processMuster13Store(formSetting);
    }
  },

  setLabId: (value: string) => {
    musterFormDialogStore.labId = value;
    musterFormDialogStore.isDuplicatedLabId = false;
  },

  setIsDuplicatedLabId: (value: boolean) => {
    musterFormDialogStore.isDuplicatedLabId = value;
  },

  viewFormLab: async (
    encounterId: string,
    labFormId: string,
    formInfo: LabForm,
    isView: boolean,
    isRefill: boolean,
    componentActions: ILabActions,
    scheinId?: string
  ) => {
    musterFormDialogStore.currentFormName = formInfo?.formName;
    const formSetting = await musterFormActions.getFormSetting(
      musterFormDialogStore.currentFormName
    );

    musterFormDialogStore.componentActions = componentActions;
    musterFormDialogStore.formViewScheinId = scheinId;
    switch (musterFormDialogStore.currentFormName) {
      case FormName.Muster_10A:
        {
          const exams = formInfo?.form10A?.checkbox_exams?.reduce(
            (acc, curr, index) => ({
              ...acc,
              [`label_checkbox_exam-${index + 1}`]: curr,
            }),
            {}
          );

          musterFormDialogStore.currentFormSetting = {
            ...formSetting,
            ...exams,
            checkbox_kurativ: formInfo?.form10A?.checkbox_kurativ,
            checkbox_praventiv: formInfo?.form10A?.checkbox_praventiv,
            checkbox_behandl: formInfo?.form10A?.checkbox_behandl,
            checkbox_unfall: formInfo?.form10A?.checkbox_unfall,
            date_label_custom_abnahmedatum: +(
              formInfo?.form10A?.date_label_custom_abnahmedatum || ''
            ),
            textbox_knappschafts_kennziffer:
              formInfo?.form10A?.textbox_knappschafts_kennziffer,
            textbox_zusat: formInfo?.form10A?.textbox_zusat,
            textbox_ssw: formInfo?.form10A?.textbox_ssw,
            textbox_exam61_line1: formInfo?.form10A?.textbox_exam61_line1,
            textbox_exam61_line2: formInfo?.form10A?.textbox_exam61_line2,
            label_gender: formInfo?.form10A?.label_gender,
            date_abnahmezeit: +(formInfo?.form10A?.date_abnahmezeit || ''),
            ...JSON.parse(formInfo.patientHeader),
          };
        }
        break;
      case FormName.Muster_39A:
        {
          musterFormDialogStore.currentFormSetting = {
            ...formSetting,
            checkbox_30_34_jahre: !!formInfo?.form39?.checkbox_30_34_jahre,
            checkbox_ab_35_jahre: !!formInfo?.form39?.checkbox_ab_35_jahre,
            checkbox_20_29_jahre: !!formInfo?.form39?.checkbox_20_29_jahre,
            checkbox_primar_screening:
              !!formInfo?.form39?.checkbox_primar_screening,
            checkbox_abklarungs_diagnostik:
              !!formInfo?.form39?.checkbox_abklarungs_diagnostik,
            checkbox_hpv_test: !!formInfo?.form39?.checkbox_hpv_test,
            checkbox_ko_testung: !!formInfo?.form39?.checkbox_ko_testung,
            checkbox_zytologie: !!formInfo?.form39?.checkbox_zytologie,
            checkbox_ja: !!formInfo?.form39?.checkbox_ja,
            checkbox_nein: !!formInfo?.form39?.checkbox_nein,
            checkbox_hpv_keine: !!formInfo?.form39?.checkbox_hpv_keine,
            checkbox_hpv_unklar: !!formInfo?.form39?.checkbox_hpv_unklar,
            checkbox_hpv_vollstandig:
              !!formInfo?.form39?.checkbox_hpv_vollstandig,
            checkbox_hpv_unvollstandig:
              !!formInfo?.form39?.checkbox_hpv_unvollstandig,
            checkbox_hpv_hr_liegt_nicht:
              !!formInfo?.form39?.checkbox_hpv_hr_liegt_nicht,
            checkbox_hpv_hr_liegt_vor:
              !!formInfo?.form39?.checkbox_hpv_hr_liegt_vor,
            checkbox_hpv_hr_positiv:
              !!formInfo?.form39?.checkbox_hpv_hr_positiv,
            checkbox_hpv_hr_negativ:
              !!formInfo?.form39?.checkbox_hpv_hr_negativ,
            checkbox_hpv_hr_nicht_verwertb:
              !!formInfo?.form39?.checkbox_hpv_hr_nicht_verwertb,
            checkbox_op_ja: !!formInfo?.form39?.checkbox_op_ja,
            checkbox_op_nein: !!formInfo?.form39?.checkbox_op_nein,
            checkbox_graviditat_nein:
              !!formInfo?.form39?.checkbox_graviditat_nein,
            checkbox_graviditat_ja: !!formInfo?.form39?.checkbox_graviditat_ja,
            checkbox_ausfluss_nein: !!formInfo?.form39?.checkbox_ausfluss_nein,
            checkbox_ausfluss_ja: !!formInfo?.form39?.checkbox_ausfluss_ja,
            checkbox_iup_nein: !!formInfo?.form39?.checkbox_iup_nein,
            checkbox_iup_ja: !!formInfo?.form39?.checkbox_iup_ja,
            checkbox_einnahme_nein: !!formInfo?.form39?.checkbox_einnahme_nein,
            checkbox_einnahme_ja: !!formInfo?.form39?.checkbox_einnahme_ja,
            checkbox_unauffallig: !!formInfo?.form39?.checkbox_unauffallig,
            checkbox_auffallig: !!formInfo?.form39?.checkbox_auffallig,
            label_gruppe: formInfo?.form39?.textbox_gruppe || '',
            textbox_welche: formInfo?.form39?.textbox_welche || '',
            date_custom_wann: formInfo?.form39?.date_custom_wann || '',
            date_custom_jetzt: formInfo?.form39?.date_custom_jetzt || '',
            date_anamnese: +(formInfo?.form39?.date_anamnese || 0),
            area_text_box_erlauterungen:
              formInfo.form39?.area_text_box_erlauterungen || '',
          };
        }
        break;
      case FormName.Muster_10:
        {
          musterFormDialogStore.currentFormSetting = {
            ...formSetting,
            checkbox_kurativ: formInfo?.form10?.checkbox_kurativ,
            checkbox_praventiv: formInfo?.form10?.checkbox_praventiv,
            checkbox_behandl: formInfo?.form10?.checkbox_behandl,
            checkbox_unfall: formInfo?.form10?.checkbox_unfall,
            textbox_knappschafts_kennziffer:
              formInfo?.form10?.textbox_knappschafts_kennziffer,
            date_quartal: formInfo?.form10?.date_quartal,
            checkbox_kontrolluntersuchung:
              formInfo?.form10?.checkbox_kontrolluntersuchung,
            label_gender: formInfo?.form10?.label_gender,
            checkbox_behandlung: formInfo?.form10?.checkbox_behandlung,
            checkbox_eingeschrankter:
              formInfo?.form10?.checkbox_eingeschrankter,
            checkbox_empfangnisregelung:
              formInfo?.form10?.checkbox_empfangnisregelung,
            date_label_custom_abnahmedatum:
              formInfo?.form10?.date_label_custom_abnahmedatum,
            date_abnahmezeit: formInfo?.form10?.date_abnahmezeit,
            textbox_ssw: formInfo?.form10?.textbox_ssw,
            textbox_betri: formInfo?.form10?.textbox_betri,
            textbox_arzt: formInfo?.form10?.textbox_arzt,
            checkbox_befund_eilt: formInfo?.form10?.checkbox_befund_eilt,
            checkbox_telefon: formInfo?.form10?.checkbox_telefon,
            checkbox_fax: formInfo?.form10?.checkbox_fax,
            textbox_nr: formInfo?.form10?.textbox_nr,
            textbox_diagnose: formInfo?.form10?.textbox_diagnose,
            textbox_befund_line1: formInfo?.form10?.textbox_befund_line1,
            textbox_befund_line2: formInfo?.form10?.textbox_befund_line2,
            textbox_auftrag_line1: formInfo?.form10?.textbox_auftrag_line1,
            textbox_auftrag_line2: formInfo?.form10?.textbox_auftrag_line2,
            textbox_auftrag_line3: formInfo?.form10?.textbox_auftrag_line3,
            ...JSON.parse(formInfo.patientHeader),
          };
        }
        break;
      default:
        break;
    }

    musterFormDialogStore.isViewForm = isView;
    musterFormDialogStore.isRefill = isRefill;
    musterFormDialogStore.labId = formInfo?.labOrderNumber;
    musterFormDialogStore.encounterId = encounterId;
    musterFormDialogStore.labFormId = labFormId;
  },

  setLabFormPurpose: (type: LabPurpose) => {
    musterFormDialogStore.labFormPurpose = type;
  },

  setFormSendToRecipient: (recipient: string) => {
    musterFormDialogStore.formSendToRecipient = SendTo[recipient];
  },

  setHint: (hint: string) => {
    musterFormDialogStore.hint = uniq([
      ...(musterFormDialogStore.hint || []),
      hint,
    ]);
  },

  clearHint: () => {
    musterFormDialogStore.hint = [];
  },

  setIsOpenAddDiagnosisDialog: (isOpenAddDiagnosisDialog: boolean) => {
    musterFormDialogStore.isOpenAddDiagnosisDialog = isOpenAddDiagnosisDialog;
  },

  setDiagnoseFieldName: (diagnoseFieldName: string) => {
    musterFormDialogStore.diagnoseFieldName = diagnoseFieldName;
  },

  getListDiagnosis: async () => {
    const listDiagnosis = await getDiagnosisByPatientId(
      musterFormDialogStore?.patient?.id
    );
    musterFormDialogStore.listDiagnosis = listDiagnosis;
  },

  // PRO-3740 [TestLAB Oct/2022][StakeholderFeedback5][Priority = 6 ][Form] AD/DD of the same day automatically prefilled for created form
  // PRO-3741 [TestLAB Oct/2022][StakeholderFeedback6][Priority = 7 ][Form] Overtake findings into my forms
  preFillTimelineData: async (
    patient: IPatientProfile,
    selectedContractDoctor: ISelectedContractDoctor,
    onDiagnoses?: (diagnoses: TimelineModel[]) => void
  ) => {
    if (
      selectedContractDoctor.encounterCase &&
      selectedContractDoctor.doctorId
    ) {
      const result = await get({
        patientId: patient.id,
        contractId: !selectedContractDoctor.isEnrollment
          ? selectedContractDoctor.contractId
          : undefined,
        createdDate: datetimeUtil.now(),
        encounterCase: selectedContractDoctor.encounterCase,
        treatmentDoctorId: selectedContractDoctor.doctorId,
      });

      const timeLineModels = result?.data?.timelineModels ?? [];
      const filteredDiagnose: TimelineModel[] = uniqBy(
        getFilteredByCommandType(
          timeLineModels,
          [TimelineEntityType.TimelineEntityType_Diagnose, TimelineEntityType.TimelineEntityType_Diagnose_DD],
          (item: TimelineModel) => {
            if (!item.encounterDiagnoseTimeline) {
              return false;
            }

            const schein = patientFileStore.schein.originalList.find(
              (schein) => schein.scheinId === item.scheinIds?.[0]
            );
            if (!schein || schein.markedAsBilled) {
              return false;
            }

            return (
              (item.encounterDiagnoseTimeline.command === 'D' ||
                item.encounterDiagnoseTimeline.command === 'DD') &&
              item.encounterDiagnoseTimeline.code != ''
            );
          }
        ),
        (item) =>
          `${item.encounterDiagnoseTimeline?.code}_${item.encounterDiagnoseTimeline?.certainty}_${item.encounterDiagnoseTimeline?.laterality}`
      );
      const filteredBCommand: TimelineModel[] = getFilteredByCommandType(
        timeLineModels,
        [TimelineEntityType.TimelineEntityType_Note],
        (item: TimelineModel) => {
          if (!item.encounterNoteTimeline) {
            return false;
          }
          return (
            item.encounterNoteTimeline.command === 'B' ||
            item.encounterNoteTimeline.command === 'BEFUND'
          );
        }
      );

      onDiagnoses?.(filteredDiagnose);

      const validFilteredDiagnose = filteredDiagnose.filter((item) =>
        checkValidICDCode(item?.encounterDiagnoseTimeline?.code || '')
      );

      const filteredDiagnoseNonAble = filteredDiagnose.filter((item) => {
        return checkICDCodeNonBilling(item);
      });

      const filteredDiagnoseReserved = filteredDiagnose.filter((item) => {
        return checkICDCodeReserved(item);
      });

      if (!musterFormDialogStore.currentFormSetting) {
        return;
      }

      switch (musterFormDialogStore.currentFormName) {
        case FormName.BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17: {
          const values: string[] = [];
          const rawData = {};

          preFillIcdCode(filteredDiagnoseReserved, 3, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');
            values.push(buildValue);
            rawData[buildValue] = {
              code: item?.encounterDiagnoseTimeline?.code,
              certainty: item?.encounterDiagnoseTimeline?.certainty,
              laterality: item?.encounterDiagnoseTimeline?.laterality,
              year: item?.year,
            };
          });

          musterFormDialogActions.setCurrentMusterFormSetting({
            textbox_diagnose_0: values.join(', '),
            textbox_diagnose_1: values.join(', '),
            raw_diagnosis: rawData,
          });
          break;
        }
        case FormName.Muster_1:
          const values = {};

          preFillIcdCode(
            filteredDiagnoseNonAble,
            6,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item?.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');

              values[`label_icd10_code_${index + 1}_0`] = buildValue;
            }
          );

          musterFormDialogActions.setCurrentMusterFormSetting({
            ...values,
          });

          break;
        case FormName.Muster_2B: {
          const value: string[] = [];

          preFillIcdCode(filteredDiagnoseReserved, 4, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting[`textbox_diagnose_line1`] =
            value.join(', ');
          preFillBCommandContent(filteredBCommand, (truncatedContent = '') => {
            musterFormDialogStore.currentFormSetting![
              `textbox_untersuchung_line1`
            ] = truncatedContent;
          });
          break;
        }

        case FormName.Muster_5: {
          const value: string[] = [];

          preFillIcdCode(filteredDiagnoseReserved, 4, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item?.encounterDiagnoseTimeline?.description,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting[`textbox_diagnose_line1`] =
            value.join(',  ');
          break;
        }

        case FormName.Muster_6: {
          const value: string[] = [];

          preFillIcdCode(filteredDiagnoseReserved, 4, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item?.encounterDiagnoseTimeline?.certainty,
              item?.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting[`textbox_diagnose_line1`] =
            value.join(', ');
          musterFormDialogStore.currentFormSetting[
            `textbox_reason_for_contact_line1`
          ] = value.join(', ');

          preFillBCommandContent(
            filteredBCommand,
            (truncatedContent = '') => {
              musterFormDialogStore.currentFormSetting![
                `textbox_befund_line1`
              ] = truncatedContent;
            },
            // preProcessContentCb
            (content: string) => {
              const preFillContent = musterFormActions.getForm6PreFillData();
              const fullContent = [preFillContent, content]
                .filter((item) => item)
                .join(', ');

              return fullContent;
            }
          );
          break;
        }

        case FormName.Muster_10: {
          const value: string[] = [];
          preFillIcdCode(filteredDiagnoseReserved, 2, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item?.encounterDiagnoseTimeline?.certainty,
              item?.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting[`textbox_diagnose`] =
            value.join(', ');
          break;
        }

        case FormName.Muster_15:
          preFillIcdCode(filteredDiagnose, 1, (item: TimelineModel) => {
            const value: string[] = [];
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item?.encounterDiagnoseTimeline?.certainty,
              item?.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);

            musterFormDialogStore.currentFormSetting![
              `textbox_diagnose_line1_0`
            ] = value.join(', ');
          });
          break;
        case FormName.Muster_16:
          preFillIcdCode(
            filteredDiagnose,
            3,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');

              musterFormDialogStore.currentFormSetting![
                `textbox_icd${index + 1}`
              ] = buildValue;
            }
          );
          break;
        case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
        case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
        case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
        case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
        case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3: {
          preFillIcdCode(
            filteredDiagnose,
            2,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_diagnose_line${2 - index}_1`
              ] = buildValue;
            }
          );
          break;
        }
        case FormName.Muster_52_0_V2:
        case FormName.Muster_52_2_V3: {
          preFillIcdCode(
            filteredDiagnose,
            12,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_wegen_icd${index}`
              ] = buildValue;
            }
          );
          break;
        }

        case FormName.Begleitschreiben_FaV_V4: {
          preFillIcdCode(
            filteredDiagnose,
            3,
            (item: TimelineModel, index: number) => {
              musterFormDialogStore.currentFormSetting![
                `textbox_reason_for_contact_line${index + 1}`
              ] = buildDiagnosisValue(
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.description
              );
            }
          );
          break;
        }

        case FormName.Muster_12A: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            4,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_icd10_code${index + 1}_0`
              ] = buildValue;
            }
          );
          break;
        }
        case FormName.Muster_26A: {
          preFillIcdCode(filteredDiagnose, 1, (item: TimelineModel) => {
            const buildValue = [
              item.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');
            musterFormDialogStore.currentFormSetting![`textbox_diagnose`] =
              buildValue;
          });
          break;
        }

        case FormName.Muster_28A: {
          const icdValues: string[] = [];
          preFillIcdCode(filteredDiagnose, 1, (item: TimelineModel) => {
            const buildValue = [
              item.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');
            icdValues.push(buildValue);
          });
          musterFormDialogStore.currentFormSetting[`textbox_diagnose_line1`] =
            icdValues.join(', ');
          break;
        }

        case FormName.Muster_55: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            3,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_icd${index + 1}`
              ] = buildValue;
            }
          );
          break;
        }
        case FormName.Muster_61: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            6,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_icd10_code${index + 1}_0`
              ] = buildValue;
              musterFormDialogStore.currentFormSetting![
                `textbox_icd10_code${index + 1}_1`
              ] = buildValue;
            }
          );
          preFillIcdCode(
            filteredDiagnoseReserved,
            6,
            (item: TimelineModel, index: number) => {
              musterFormDialogStore.currentFormSetting![
                `textbox_diagnose${index + 1}_0`
              ] = item.encounterDiagnoseTimeline?.description || '';
              musterFormDialogStore.currentFormSetting![
                `textbox_diagnose${index + 1}_1`
              ] = item.encounterDiagnoseTimeline?.description || '';
            }
          );
          break;
        }

        case FormName.Muster_N63A: {
          const value: string[] = [];

          preFillIcdCode(filteredDiagnoseReserved, 6, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting[`textbox_diagnose_line1_0`] =
            value.join(', ');
          break;
        }

        case FormName.Muster_64: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            3,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');

              musterFormDialogStore.currentFormSetting![
                `textbox_icd${index + 1}_0`
              ] = buildValue;
              musterFormDialogStore.currentFormSetting![
                `textbox_single_diagnose${index + 1}_0`
              ] = item.encounterDiagnoseTimeline?.description?.trim() || '';
            }
          );
          preFillIcdCode(
            filteredDiagnoseReserved,
            3,
            (item: TimelineModel, index: number) => {
              musterFormDialogStore.currentFormSetting![
                `textbox_vorsorgerelevante_b_line${index + 1}_0`
              ] = item.encounterDiagnoseTimeline?.description || '';
            }
          );
          break;
        }

        case FormName.Muster_65A: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            3,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item?.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');

              musterFormDialogStore.currentFormSetting![
                `textbox_icd${index + 1}_0`
              ] = buildValue;
            }
          );
          preFillIcdCode(
            filteredDiagnoseReserved,
            3,
            (item: TimelineModel, index: number) => {
              musterFormDialogStore.currentFormSetting![
                `textbox_single_diagnose_line${index + 1}_0`
              ] = item.encounterDiagnoseTimeline?.description || '';
            }
          );
          break;
        }
        case FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4:
        case FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6: {
          preFillIcdCode(filteredDiagnose, 3, (item: TimelineModel) => {
            const buildValue = [
              item.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');
            musterFormDialogStore.currentFormSetting![
              'textbox_diagnosenNachICD10_line1'
            ] = buildValue;
          });

          break;
        }
        case FormName.Muster_PTV_2A: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            3,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item?.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_icd10_code${index + 1}`
              ] = buildValue;
            }
          );
          break;
        }
        case FormName.Muster_PTV_11A: {
          const isIcdChecked =
            musterFormDialogStore?.currentFormSetting?.[
              'checkbox_beiIhnenWurdenFolgende'
            ];

          preFillIcdCode(
            validFilteredDiagnose.filter((item) => checkICDCodeReserved(item)),
            3,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter(Boolean)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_icd10_code${index + 1}`
              ] = buildValue;

              const prefix = index ? '; ' : '';
              const newValue = `${prefix}${
                item.encounterDiagnoseTimeline?.description || ''
              }`;

              // NOTE: update value for later reads
              musterFormDialogStore.currentFormSetting![
                META_FIELD_DIAGNOSE_TEXT
              ] = `${
                musterFormDialogStore.currentFormSetting![
                  META_FIELD_DIAGNOSE_TEXT
                ] || ''
              }${newValue}`.trim();

              if (!isIcdChecked) return;

              musterFormDialogStore.currentFormSetting![
                'textbox_diagnose_line1'
              ] += `${
                musterFormDialogStore.currentFormSetting![
                  'textbox_diagnose_line1'
                ] || ''
              }${newValue}`.trim();
            }
          );
          break;
        }
        case FormName.Muster_PTV_12A: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            3,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter(Boolean)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_icd10_code${index + 1}`
              ] = buildValue;
            }
          );

          break;
        }
        case FormName.Muster_56: {
          preFillIcdCode(
            filteredDiagnoseReserved,
            6,
            (item: TimelineModel, index: number) => {
              const buildValue = [
                item.encounterDiagnoseTimeline?.code,
                item.encounterDiagnoseTimeline?.certainty,
                item.encounterDiagnoseTimeline?.laterality,
              ]
                .filter((item) => item)
                .join(' ');
              musterFormDialogStore.currentFormSetting![
                `textbox_icd${index + 1}_0`
              ] = buildValue;
              musterFormDialogStore.currentFormSetting![
                `textbox_single_diagnose${index + 1}_0`
              ] = item.encounterDiagnoseTimeline?.description?.trim() || '';
            }
          );
          preFillIcdCode(
            filteredDiagnoseReserved,
            6,
            (item: TimelineModel, index: number) => {
              const buildValue = buildDiagnosisValue(
                item.encounterDiagnoseTimeline?.code || '',
                item.encounterDiagnoseTimeline?.description || ''
              );

              musterFormDialogStore.currentFormSetting![
                `textbox_56_verordnungsrelevante_line${index + 1}_0`
              ] = buildValue;
            }
          );
          break;
        }
        case FormName.Muster_7:
        case FormName.Muster_11:
        case FormName.AOK_FA_NPPP_BW_GDK_Antragsformular_V6:
        case FormName.BKK_BOSCH_FA_BW_GDK_Antragsformular_V4:
        case FormName.BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3:
        case FormName.BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2:
        case FormName.AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3:
        case FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
        case FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2: {
          const icdValues: string[] = [];
          preFillIcdCode(filteredDiagnoseReserved, 3, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');
            icdValues.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting[`textbox_diagnose_line1`] =
            icdValues.join(', ');
          musterFormDialogStore.currentFormSetting[`textbox_die_dianose`] =
            icdValues.join(', ');
          break;
        }
        case FormName.Ambulantes_Operieren_V1: {
          const icdValues: string[] = [];
          preFillIcdCode(filteredDiagnoseReserved, 3, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');
            icdValues.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting['textbox_diagnose'] =
            icdValues.join(', ');
        }
        case FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1: {
          const icdValues: string[] = [];
          preFillIcdCode(filteredDiagnoseReserved, 3, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');
            icdValues.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting[
            'textbox_diagnosenNachICD10'
          ] = icdValues.join(', ');
        }
        case FormName.F1000:
        case FormName.F1050:
        case FormName.F2100: {
          const value: string[] = [];

          preFillIcdCode(filteredDiagnose, 10, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting['label_icd10_code_0'] =
            value[0];
          musterFormDialogStore.currentFormSetting['area_diagnose_0'] =
            value.join(', ');
        }
        case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3:
        case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2:
        case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3:
        case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3:
        case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3:
        case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3: {
          const value: string[] = [];

          preFillIcdCode(filteredDiagnose, 10, (item: TimelineModel) => {
            const buildValue = [
              item?.encounterDiagnoseTimeline?.code,
              item.encounterDiagnoseTimeline?.certainty,
              item.encounterDiagnoseTimeline?.laterality,
            ]
              .filter((item) => item)
              .join(' ');

            value.push(buildValue);
          });

          musterFormDialogStore.currentFormSetting['area_diagnose_0'] =
            value.join(', ');
        }
        default:
          break;
      }
    }
  },

  clearFormWarningList: () => {
    musterFormDialogStore.warningFormArray = [];
  },

  setIncludeLabResult: (value) => {
    musterFormDialogStore.includeLabResult = value;
  },

  validateDiagnosis: async () => {
    const currentFormSetting =
      musterFormDialogStore.currentFormSetting as FORM_SETTING_OBJECT;
    const arrIcdFormat: string[] = [];
    const errorTypes: IcdErrorTypeCheck[] = [];

    switch (musterFormDialogStore.currentFormName) {
      case FormName.Muster_1: {
        const arrIcdForm = [
          currentFormSetting['label_icd10_code_1_0'] as string,
          currentFormSetting['label_icd10_code_1_2'] as string,
          currentFormSetting['label_icd10_code_2_0'] as string,
          currentFormSetting['label_icd10_code_2_2'] as string,
          currentFormSetting['label_icd10_code_3_0'] as string,
          currentFormSetting['label_icd10_code_3_2'] as string,
          currentFormSetting['label_icd10_code_4_0'] as string,
          currentFormSetting['label_icd10_code_4_2'] as string,
          currentFormSetting['label_icd10_code_5_0'] as string,
          currentFormSetting['label_icd10_code_5_2'] as string,
          currentFormSetting['label_icd10_code_6_0'] as string,
          currentFormSetting['label_icd10_code_6_2'] as string,
        ];
        arrIcdForm.forEach((value) => {
          if (checkICDCode(value)) {
            arrIcdFormat.push(formatICDCode(value));
          }
        });
        errorTypes.push(
          ...[
            IcdErrorTypeCheck.Reserved,
            IcdErrorTypeCheck.Secondary,
            IcdErrorTypeCheck.NonExist,
          ]
        );
        break;
      }
      case FormName.Muster_6_cover_letter: {
        const arrIcdForm = [
          currentFormSetting['textbox_diagnose_line1'],
          currentFormSetting['textbox_diagnose_line2'],
          currentFormSetting['textbox_diagnose_line1_0'],
          currentFormSetting['textbox_diagnose_line2_0'],
        ] as string[];
        arrIcdForm.forEach((value) => {
          if (checkICDCode(value)) {
            arrIcdFormat.push(formatICDCodeVer2(value));
          }
        });
        errorTypes.push(
          ...[IcdErrorTypeCheck.Reserved, IcdErrorTypeCheck.NonExist]
        );
        break;
      }
      case FormName.Muster_8A:
      case FormName.Muster_8: {
        const arrIcdForm = [currentFormSetting['label_medication'] as string];
        arrIcdForm.forEach((value) => {
          if (checkICDCode(value)) {
            arrIcdFormat.push(formatICDCodeVer2(value));
          }
        });
        errorTypes.push(
          ...[IcdErrorTypeCheck.Reserved, IcdErrorTypeCheck.NonExist]
        );
        break;
      }
      case FormName.Muster_13: {
        const arrIcdForm = [
          currentFormSetting['label_icd_code10_line2_0'] as string,
          currentFormSetting['label_icd_code10_line1_0'] as string,
        ];
        arrIcdForm.forEach((value) => {
          if (checkICDCode(value)) {
            arrIcdFormat.push(formatICDCode(value));
          }
        });
        errorTypes.push(
          ...[IcdErrorTypeCheck.Reserved, IcdErrorTypeCheck.NonExist]
        );
        break;
      }
      case FormName.Muster_16:
      case FormName.Muster_16A:
      case FormName.Muster_16A_Bay: {
        const arrIcdForm = [currentFormSetting['label_medication_0'] as string];
        arrIcdForm.forEach((value) => {
          if (checkICDCode(value)) {
            if (value.length > 6) {
              arrIcdFormat.push(formatICDCodeVer2(value));
            } else {
              arrIcdFormat.push(formatICDCode(value));
            }
          }
        });
        errorTypes.push(
          ...[IcdErrorTypeCheck.Reserved, IcdErrorTypeCheck.NonExist]
        );
        break;
      }
      case FormName.Muster_56: {
        const arrIcdForm = [
          currentFormSetting['textbox_56_verordnungsrelevante_0'] as string,
        ];
        arrIcdForm.forEach((value) => {
          if (checkICDCode(value)) {
            arrIcdFormat.push(formatICDCodeVer2(value));
          }
        });
        errorTypes.push(
          ...[IcdErrorTypeCheck.Reserved, IcdErrorTypeCheck.NonExist]
        );
        break;
      }
      default:
        break;
    }

    if (!errorTypes[0]) {
      return {
        isValid: true,
        hasCheckNonExitICDCode: false,
        hasCheckReservedICDCode: false,
      };
    }

    const validationResult = await validateDiagnose({
      icdCode: arrIcdFormat,
      typeCheck: errorTypes,
    });
    const result = validationResult.data.results;

    if (result.length) {
      musterFormDialogStore.validationDiagnosisResult = {
        ...musterFormDialogStore.validationDiagnosisResult,
        isValid: false,
        hasCheckNonExitICDCode: result.some(
          (item) => item.errorType === IcdErrorTypeCheck.NonExist
        ),
        hasCheckReservedICDCode: result.some(
          (item) => item.errorType === IcdErrorTypeCheck.Reserved
        ),
      };

      return musterFormDialogStore.validationDiagnosisResult;
    }

    return {
      isValid: true,
      hasCheckNonExitICDCode: false,
      hasCheckReservedICDCode: false,
    };
  },
  setPrinterSettingData: (setting?: IPrinterSetting) => {
    musterFormDialogStore.printerSettingData = setting;
  },
  setCurrentEntryForm: (formId: string) => {
    musterFormDialogStore.currentEntryForm = formId;
  },
  setEAUSetting: (eauSetting) => {
    musterFormDialogStore.eauSetting = eauSetting;
  },
  setPrescribeContractId: (contractId: string) => {
    musterFormDialogStore.prescribeContractId = contractId;
  },
  setEnableValidation(enableValidation: boolean) {
    musterFormDialogStore.enableValidation = enableValidation;
  },
  setShowCoverLetter: (isShowCoverLetterForm: boolean) => {
    musterFormDialogStore.isShowCoverLetterForm = isShowCoverLetterForm;
  },
  // EAU form imported form bdt do have enough data so we will show the form by anotation and disable print button
  setEAUDisable: (disable: boolean) => {
    musterFormDialogStore.EAUDisable = disable;
  },
  setLoadingPrescribe: (isLoading: boolean) => {
    musterFormDialogStore.isLoadingPrescribe = isLoading;
  },
};

export function useMusterFormDialogStore() {
  useEffect(() => {
    if (!musterFormDialogStore.isViewForm) {
      musterFormDialogActions.setPrescriptionDate(datetimeUtil.now());
    }
    return () => {
      musterFormDialogStore = proxy<IMusterPrescribe>(initStore);
    };
  }, []);
  return useSnapshot(musterFormDialogStore);
}
