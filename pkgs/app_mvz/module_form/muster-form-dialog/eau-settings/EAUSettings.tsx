import React, { memo, useContext, useEffect, useState } from 'react';
import { Formik } from 'formik';
import isEmpty from 'lodash/isEmpty';

import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';

import {
  alertError,
  alertSuccessfully,
  Button,
  Flex,
  H3,
  LoadingState,
} from '@tutum/design-system/components';
import {
  type BuildBundleAndValidationRequest,
  useMutationBuildBundleAndValidation,
} from '@tutum/hermes/bff/legacy/app_mvz_form';
import { useQueryGetKimAccounts } from '@tutum/hermes/bff/legacy/app_mvz_mail';
import { useQueryGetSignatureInfo } from '@tutum/hermes/bff/legacy/app_mvz_qes';
import type { EAUSetting, Meldung } from '@tutum/hermes/bff/legacy/eau_common';
import { FormAction } from '@tutum/hermes/bff/legacy/form_common';
import { FormName } from '@tutum/hermes/bff/form_common';
import { DocumentStatus } from '@tutum/hermes/bff/legacy/qes_common';
import {
  EmploymentInfo,
  PatientType,
} from '@tutum/hermes/bff/legacy/patient_profile_common';
import i18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { referralThroughTssActions } from '@tutum/mvz/hooks/useReferralThroughTss.store';
import { settingActions } from '@tutum/mvz/hooks/useSetting.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  FormElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import type { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { useMutationUpdatePatientProfileV2 } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import {
  musterFormDialogActions,
  useMusterFormDialogStore,
} from '../musterFormDialog.store';
import { formOverviewActions } from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import { StatusText } from '@tutum/hermes/bff/eau_common';
import {
  checkIsPrivateSchein,
  checkIsSvSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import { SPACE_NUMBER } from '@tutum/design-system/styles';
import EAUSettingForm from './EAUSettingForm';
import { ComfortSignature } from '@tutum/mvz/components/comfort-signature';
import { CardTypeType } from '@tutum/hermes/bff/legacy/card_common';
import type { PatientProfileResponse } from '@tutum/hermes/bff/app_mvz_patient_profile';
import { STORNO_STATUS } from '@tutum/mvz/components/form-eau-stylesheet/FormEAUStylesheet';
import { PrinterInformation } from '@tutum/mvz/components/printer-information';
import EmploymentSettings from '../employment-settings/EmploymentSettings';
import type ErrorCodeI18n from '@tutum/mvz/locales/en/ErrorCode.json';

type CheckPrintOnlyProps = {
  printOnly: boolean;
  statusText?: StatusText;
};
interface EAUSettingsProps {
  updatedPatient?: PatientProfileResponse;
  className?: string;
  selectedContractDoctor: ISelectedContractDoctor | null;
  hasDummyVknr: boolean;
  onChangeEmploymentInfo: (data: EmploymentInfo) => void;
  onSuccess?: () => void;
  onDoneForm1844Validation: (data: Meldung[]) => void;
}
const defaultSetting: EAUSetting = {
  printOption: {
    printForEmployer: true,
    printForInsurance: false,
    printForPatient: true,
  },
  cardType: null as unknown as CardTypeType,
  printOnly: false,
  bsnrCode: '',
};

function EAUSettings({
  className,
  updatedPatient,
  selectedContractDoctor,
  hasDummyVknr,
  onSuccess,
  onChangeEmploymentInfo,
  onDoneForm1844Validation,
}: EAUSettingsProps) {
  const { globalData } = GlobalContext.useContext();
  const { setPatient } = useContext(PatientManagementContext.instance);
  const { mutateAsync: updatePatientAsync } =
    useMutationUpdatePatientProfileV2();

  const { t: tPrescrible } = i18n.useTranslation<
    keyof typeof FormI18n.Prescrible
  >({
    namespace: 'Form',
    nestedTrans: 'Prescrible',
  });
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonI18n.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [neededForm1844Validation, setNeededForm1844Validation] =
    useState<boolean>(false);
  const [isSupportForm1844, setIsSupportForm1844] = useState<boolean>(false);
  const [eauSetting, setEauSetting] = useState<EAUSetting>({
    ...defaultSetting,
    bsnrCode: globalData.userProfile?.bsnr,
  });
  const [isCompletedEAU, setIsCompletedEAU] = useState(false);
  const musterFormDialogStore = useMusterFormDialogStore();
  const currentSchein = useCurrentSchein();
  const { patient: p, schein } = usePatientFileStore();

  const { t: tEAU } = i18n.useTranslation<keyof typeof FormI18n.EAU>({
    namespace: 'Form',
    nestedTrans: 'EAU',
  });

  const { t: tErrorCode } = i18n.useTranslation<keyof typeof ErrorCodeI18n>({
    namespace: 'ErrorCode',
  });

  const { data: resKimAccount, isSuccess: isSuccessGetKimAccount } =
    useQueryGetKimAccounts({
      bsnrCodes: globalData.userProfile?.bsnrs as string[],
    });

  const {
    data,
    isSuccess,
    isLoading: isLoadingSignature,
    error: errorSignature,
  } = useQueryGetSignatureInfo(
    {
      bsnr: globalData.userProfile?.bsnr as string,
      doctorId: globalData.userProfile?.id as string,
    },
    {
      enabled:
        !isCompletedEAU && !!currentSchein && !checkIsSvSchein(currentSchein),
      throwOnError: false,
    }
  );

  const canSignByHBA = data?.doctorCard?.canSign;
  const canSignBySMCB = data?.practiceCard?.canSign;
  const canSign = canSignByHBA || canSignBySMCB;

  const checkPatientCanOnlyPrint = (): CheckPrintOnlyProps => {
    if (!p.current || !schein.activatedSchein) {
      return { printOnly: true };
    }

    const patient = p.current.patientInfo;
    const isKimSetup =
      isSuccessGetKimAccount && resKimAccount?.accounts?.length > 0;
    const isPrivateCase =
      patient.genericInfo.patientType === PatientType.PatientType_Private ||
      checkIsPrivateSchein(schein.activatedSchein);
    if (!patient || !canSign || !isKimSetup || isPrivateCase) {
      return { printOnly: true };
    }

    if (patient.europeanHealthInsurance?.hasEuropeanHealthInsuranceCard) {
      return {
        printOnly: true,
        statusText: StatusText.StatusText_Sent,
      };
    }

    const insuranceInfo = patient.insuranceInfos.find(
      (e) => e.id === schein.activatedSchein?.insuranceId
    );
    if (!insuranceInfo) {
      return { printOnly: false };
    }

    if (insuranceInfo.specialGroup === '07') {
      return {
        printOnly: true,
        statusText: StatusText.StatusText_Sent,
      };
    }

    const vknr = Number(
      insuranceInfo.insuranceCompanyId.substring(
        insuranceInfo.insuranceCompanyId.length - 3
      )
    );

    if (vknr >= 800) {
      return {
        printOnly: true,
        statusText: StatusText.StatusText_Sent,
      };
    }

    return { printOnly: false };
  };

  const { printOnly, statusText } = checkPatientCanOnlyPrint();

  useEffect(() => {
    if (!musterFormDialogStore.formPrescription) {
      return;
    }

    const status = musterFormDialogStore.formPrescription.eAUStatus;
    setIsCompletedEAU(status !== DocumentStatus.Status_Created);
  }, [musterFormDialogStore.formPrescription]);

  useEffect(() => {
    if (errorSignature) {
      alertError(tErrorCode(errorSignature.name as keyof typeof ErrorCodeI18n));
    }
  }, [errorSignature]);

  useEffect(() => {
    setEauSetting((prev) => {
      const printOption = { ...prev.printOption };
      if (printOnly || isCompletedEAU) {
        printOption.printForInsurance = true;
      }

      return {
        ...prev,
        printOption,
        printOnly,
        statusText,
      };
    });
  }, [isCompletedEAU, printOnly, statusText]);

  const checkSupportForm1844 = async () => {
    if (!currentSchein || !checkIsSvSchein(currentSchein)) {
      setIsSupportForm1844(false);
      setNeededForm1844Validation(false);
      return;
    }
    const isSupport = await webWorkerServices.doesContractSupportFunctions(
      ['FORM1844'],
      currentSchein?.hzvContractId || '',
      currentSchein?.chargeSystemId
    );
    setNeededForm1844Validation(isSupport);
    setIsSupportForm1844(isSupport);
  };

  useEffect(() => {
    checkSupportForm1844();
  }, [currentSchein]);

  useEffect(() => {
    if (isSupportForm1844) {
      setNeededForm1844Validation(true);
      onDoneForm1844Validation([]);
    }
  }, [musterFormDialogStore.currentFormSetting]);

  const { isPending: loadingForm1844Validation, mutateAsync } =
    useMutationBuildBundleAndValidation();
  async function form1844Validation() {
    const request = {
      prescribe: {
        doctorId: musterFormDialogStore.contractDoctor?.doctorId,
        treatmentDoctorId: musterFormDialogStore.contractDoctor?.doctorId,
        assignedToBsnrId: musterFormDialogStore.contractDoctor?.bsnrId,
        patientId: musterFormDialogStore.patient?.id,
        createdDate: musterFormDialogStore.prescriptionDate,
        payload: JSON.stringify(musterFormDialogStore.currentFormSetting),
        formName: musterFormDialogStore.currentFormName as FormName,
        encounterCase: musterFormDialogStore.contractDoctor?.encounterCase,
        contractType: null,
        contractId: musterFormDialogStore.contractDoctor?.contractId,
        prescribeDate: musterFormDialogStore.prescriptionDate,
        scheinId: musterFormDialogStore.currentSchein?.scheinId,
      },
      printOption: {
        pdfWithBackground: true,
        dateOfPrint: musterFormDialogStore.prescriptionDate,
        formAction: FormAction.FormAction_PrintFull,
      },
    } as unknown as BuildBundleAndValidationRequest;

    return mutateAsync(request).then((res) => {
      setNeededForm1844Validation(false);
      musterFormDialogActions.setLoadingPrescribe(false);
      onDoneForm1844Validation(res.data.eAUValidation?.payload || []);
      return res.data;
    });
  }

  const isDisabledSaveButton =
    hasDummyVknr ||
    (!!musterFormDialogStore.formPrescription?.id &&
      !musterFormDialogStore.isValidForm);
  const isDisabledButton = hasDummyVknr || !musterFormDialogStore.isValidForm;

  useEffect(() => {
    if (musterFormDialogStore.formPrescription?.eAUSetting) {
      setEauSetting(musterFormDialogStore.formPrescription.eAUSetting);
    }
  }, [musterFormDialogStore.formPrescription?.eAUSetting]);

  const isInValidForm = async (): Promise<boolean> => {
    formOverviewActions.setLoadingPrescribe(true);
    musterFormDialogActions.returnDataAuftragToPrint();

    if (!isCompletedEAU) {
      const { isValid } = await musterFormDialogActions.validateDiagnosis();
      if (!isValid) return true;
    }

    if (!currentSchein) {
      alertError(tPrescrible(`ErrorSelectSchein`));
      return true;
    }

    if (!neededForm1844Validation) {
      return false;
    }

    const res = await form1844Validation();
    return !!res.eAUValidation?.confirmRequired;
  };

  const onValidate = () => {
    return (values: EAUSetting) => {
      const errors: Record<string, boolean> = {};

      if (
        !values.printOption.printForEmployer &&
        !values.printOption.printForPatient &&
        !values.printOption.printForInsurance
      ) {
        errors['printOption.printForEmployer'] = true;
        errors['printOption.printForPatient'] = true;
        errors['printOption.printForInsurance'] = true;
      }

      return errors;
    };
  };

  const updateEmploymentInfo = (payload: PatientProfileResponse) => {
    return updatePatientAsync(payload).then(() => {
      setPatient(payload);
    });
  };

  const onSave = async () => {
    await updateEmploymentInfo(updatedPatient!);
    const printerSetting = musterFormDialogStore.printerSettingData
      ? {
        ...musterFormDialogStore.printerSettingData,
      }
      : null;
    await musterFormDialogActions
      .prescrible()
      .then(async () => {
        alertSuccessfully(tPrescrible('FormPrescribed'));
        if (!isEmpty(printerSetting)) {
          await settingActions.savePrinterSetting(printerSetting);
        }
        musterFormDialogActions.clear();
        referralThroughTssActions.clear();
      })
      .finally(() => {
        musterFormDialogActions.setLoadingPrescribe(false);
        onSuccess?.();
      });
  };

  const onSend = async () => {
    await updateEmploymentInfo(updatedPatient!);
    const printerSetting = musterFormDialogStore.printerSettingData
      ? {
        ...musterFormDialogStore.printerSettingData,
      }
      : null;
    await musterFormDialogActions
      .prescrible(datetimeUtil.now())
      .then(async () => {
        if (musterFormDialogStore.currentFormName === FormName.Muster_13) {
          patientFileActions.setActiveTabId(ID_TABS.TIMELINE);
          musterFormDialogStore.componentActions.clear();
        }
        // alertSuccessfully(tPrescrible('FormPrescribed'));
        if (!isEmpty(printerSetting)) {
          await settingActions.savePrinterSetting(printerSetting);
        }
      })
      .finally(() => {
        musterFormDialogActions.setLoadingPrescribe(false);
        onSuccess?.();
      });
  };

  if (isLoadingSignature) {
    return <LoadingState />;
  }

  return (
    <Formik<EAUSetting>
      initialValues={eauSetting}
      onSubmit={() => { }}
      validate={onValidate()}
      enableReinitialize
    >
      {(formikProps) => {
        const { values } = formikProps;
        const { printOption } = values;
        const isDisabledPrinter =
          !printOption.printForEmployer &&
          !printOption.printForInsurance &&
          !printOption.printForPatient;
        const isLoading =
          musterFormDialogStore.isLoadingPrescribe || loadingForm1844Validation;

        const onChangeCardType = (cardType) => {
          formikProps.setFieldValue('cardType', cardType);
        };

        const chooseCardType = () => {
          if (values.cardType) {
            return values.cardType;
          }

          if (canSignByHBA) {
            return CardTypeType.CardTypeTypeHBA;
          }

          if (canSignBySMCB) {
            return CardTypeType.CardTypeTypeSMCB;
          }

          return null;
        };

        const onSubmit = (callBackFunc: () => Promise<void>) => async () => {
          const cardType = chooseCardType();

          musterFormDialogActions.setEAUSetting({
            ...values,
            cardType,
          } as EAUSetting);
          if (await isInValidForm()) {
            return;
          }

          await callBackFunc();
        };

        return (
          <Flex column justify="space-between" className={className}>
            <Flex column className="sl-eau-settings-content">
              <H3>{tEAU('settings')}</H3>
              {!printOnly && isSuccess && !!data && (
                <ComfortSignature
                  showSelectiveCard
                  signatureInfo={data}
                  onChangeCardType={onChangeCardType}
                />
              )}
              <EAUSettingForm
                formikProps={formikProps}
                isCompletedEAU={isCompletedEAU}
                printOnly={printOnly}
                hasSignature={!printOnly && isSuccess && !!data}
                selectedContractDoctor={selectedContractDoctor}
                currentTreatmentDoctor={musterFormDialogStore.doctor}
              />
              <PrinterInformation formId={FormName.Muster_1} />
            </Flex>
            <Flex>
              <EmploymentSettings onChange={onChangeEmploymentInfo} />
            </Flex>
            <Flex
              className="sl-actions-wrapper"
              justify="flex-end"
              py={SPACE_NUMBER.SPACE_XS}
              px={SPACE_NUMBER.SPACE_S}
            >
              <Flex gap={SPACE_NUMBER.SPACE_S}>
                {!isCompletedEAU && (
                  <Button
                    disabled={isDisabledSaveButton}
                    intent="primary"
                    outlined
                    large
                    loading={isLoading}
                    type="submit"
                    onClick={onSubmit(onSave)}
                    {...registerActionChainElementId(
                      FormElementId.DIALOG_SAVE_BUTTON
                    )}
                  >
                    {tPrescrible(
                      musterFormDialogStore.formPrescription?.id
                        ? 'Update'
                        : 'Save'
                    )}
                  </Button>
                )}
                {STORNO_STATUS.includes(
                  musterFormDialogStore.formPrescription
                    ?.eAUStatus as DocumentStatus
                ) ? (
                  <Button
                    large
                    onClick={() => {
                      musterFormDialogActions.clear();
                    }}
                    intent="primary"
                  >
                    {tButtonActions('close')}
                  </Button>
                ) : (
                  <Button
                    disabled={isDisabledButton || isDisabledPrinter}
                    large
                    onClick={onSubmit(onSend)}
                    loading={isLoading}
                    intent="primary"
                    type="submit"
                    {...registerActionChainElementId(
                      FormElementId.DIALOG_PRINT_BUTTON
                    )}
                  >
                    {printOnly ? tPrescrible('Print') : tPrescrible('Send')}
                  </Button>
                )}
              </Flex>
            </Flex>
          </Flex>
        );
      }}
    </Formik>
  );
}

export default memo(EAUSettings);
