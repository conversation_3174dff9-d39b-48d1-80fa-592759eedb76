import React from 'react';
import { FormikProps } from 'formik';

import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';

import {
  Flex,
  IMenuItem,
  Label,
} from '@tutum/design-system/components';
import { EAUSetting } from '@tutum/hermes/bff/legacy/eau_common';
import { Checkbox } from '@tutum/design-system/components/Core';
import i18n from '@tutum/infrastructure/i18n';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import StyledErrorHelpText from '@tutum/design-system/components/ErrorHelpText';
import HimiDoctorSelector from '@tutum/mvz/module_himi/himi-doctor-selector/HimiDoctorSelector.styled';
import { AvailableDoctor } from '@tutum/mvz/module_himi/himi-doctor-selector/HimiDoctorSelector';
import { musterFormDialogActions, useMusterFormDialogStore } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { cloneDeep } from 'lodash';

const FieldNames = {
  printForEmployer: 'printOption.printForEmployer',
  printForInsurance: 'printOption.printForInsurance',
  printForPatient: 'printOption.printForPatient',
  cardType: 'cardType',
} as const;

type EAUSettingFormProps = {
  selectedContractDoctor: ISelectedContractDoctor | null;
  formikProps: FormikProps<EAUSetting>;
  isCompletedEAU: boolean;
  printOnly: boolean;
  currentTreatmentDoctor?: AvailableDoctor;
  hasSignature: boolean;
};

const EAUSettingForm = ({
  formikProps,
  isCompletedEAU,
  selectedContractDoctor,
  printOnly,
  currentTreatmentDoctor,
  hasSignature,
}: EAUSettingFormProps) => {
  const { values, errors, submitCount, setFieldValue } = formikProps;
  const { printOption } = values;
  const store = useMusterFormDialogStore();

  const { t: tEAU } = i18n.useTranslation<keyof typeof FormI18n.EAU>({
    namespace: 'Form',
    nestedTrans: 'EAU',
  });
  const { t: tFormValidation } = i18n.useTranslation<
    keyof typeof CommonI18n.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });
  const requiredCopier =
    errors[FieldNames.printForEmployer] &&
    errors[FieldNames.printForInsurance] &&
    errors[FieldNames.printForPatient];

  const onChangeCheckbox = (e: React.FormEvent<HTMLInputElement>) => {
    const { name, checked } = e.currentTarget;
    setFieldValue(name, checked);
  };

  return (
    <Flex column>
      {selectedContractDoctor && (
        <Flex column my={16}>
          <Label required>{tEAU('doctor')}</Label>
          <HimiDoctorSelector
            contractDoctor={selectedContractDoctor}
            currentTreatmentDoctor={currentTreatmentDoctor}
            isDisable={store.isViewForm || hasSignature}
            onSelectedDoctor={(d) => {
              const selectedDoctor =
                selectedContractDoctor?.availableDoctor?.find?.(
                  (doctor) => doctor.id === d.value
                );

              musterFormDialogActions.setCurrentMusterFormSetting(
                {
                  textbox_ortGesamtsumme:
                    selectedDoctor?.bsnrCity,
                }
              );
              musterFormDialogActions.setCurrentTreatmentDoctor(
                cloneDeep(d)
              );
            }}
          />
        </Flex>
      )}
      <Flex column mb={8}>
        <Label required>{tEAU('printForCopy')}</Label>
        <Flex gap={16} flexWrap>
          <Checkbox
            className={requiredCopier ? 'error-state' : ''}
            name={FieldNames.printForInsurance}
            label={tEAU('insurance')}
            onChange={onChangeCheckbox}
            checked={printOption.printForInsurance}
          />
          <Checkbox
            className={requiredCopier ? 'error-state' : ''}
            name={FieldNames.printForEmployer}
            label={tEAU('employer')}
            onChange={onChangeCheckbox}
            checked={printOption.printForEmployer}
            disabled={!printOnly && !isCompletedEAU}
          />
          <Checkbox
            className={requiredCopier ? 'error-state' : ''}
            name={FieldNames.printForPatient}
            label={tEAU('patient')}
            onChange={onChangeCheckbox}
            checked={printOption.printForPatient}
            disabled={!printOnly && !isCompletedEAU}
          />
        </Flex>
        {requiredCopier && (
          <StyledErrorHelpText
            err={tFormValidation('fieldRequired')}
            touched={true}
            submitCount={submitCount}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default EAUSettingForm;
