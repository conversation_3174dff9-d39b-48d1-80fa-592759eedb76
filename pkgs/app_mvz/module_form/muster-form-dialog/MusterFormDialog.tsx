import { cloneDeep, intersection, isEmpty, uniqBy } from 'lodash';
import React, { FormEvent, memo, useContext, useEffect, useMemo, useState } from 'react';

import {
  BodyTextL,
  BodyTextM,
  Flex,
  H3,
  type IMenuItem,
  LeaveConfirmModal,
  Link,
  LoadingState,
  MessageBar,
  Svg,
  TTypographyFontWeight,
  TagMedicine,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Button,
  Checkbox,
  Classes,
  Divider,
  FormGroup,
  Intent,
  Label,
  MenuItem,
  Radio,
  RadioGroup,
  Tag,
} from '@tutum/design-system/components/Core';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { Select } from '@tutum/design-system/components/Select';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { SPACE, scaleSpace } from '@tutum/design-system/styles';
import { FONT_SIZE } from '@tutum/design-system/styles/typo/font-size';
import { COLOR } from '@tutum/design-system/themes/styles';
import { FONT_WEIGHT } from '@tutum/design-system/themes/styles/typo/font-weight';
import { DiagnoseLabel } from '@tutum/hermes/bff/app_mvz_heimi';
import {
  EventResponseReferenceCode,
  useListenResponseReferenceCode,
} from '@tutum/hermes/bff/app_mvz_tss';
import { EuropeanHealthInsuranceStatus } from '@tutum/hermes/bff/common';
import { ParticipationFormsStatus } from '@tutum/hermes/bff/edmp_common';
import { FormName, FormType } from '@tutum/hermes/bff/form_common';
import { LabFormStatus, SendTo } from '@tutum/hermes/bff/lab_common';
import { useQueryCheckDummyVknr } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { Meldung } from '@tutum/hermes/bff/legacy/eau_common';
import { EmploymentInfo } from '@tutum/hermes/bff/legacy/patient_profile_common';
import { HpmInformationStatus } from '@tutum/hermes/bff/service_domains_profile';
import I18n, { type II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import Cave from '@tutum/mvz/components/cave';
import {
  FormError,
  FormInfo,
  FormWarning,
} from '@tutum/mvz/components/form-common';
import {
  InfoLabel,
  WarningLabel,
} from '@tutum/mvz/components/form-common/FormCommonInfo';
import {
  FormPrintPreviewClasses,
  FormPrintPreviewLayout,
} from '@tutum/mvz/components/form-print-preview-layout';
import { PrinterInformation } from '@tutum/mvz/components/printer-information';
import ReferralThroughTSSDialog from '@tutum/mvz/components/referral-through-tss-dialog';
import { handleChangeInsuranceStatus } from '@tutum/mvz/components/referral-through-tss-dialog/helper';
import {
  BG_FORM_INVOICE,
  type FORM_SETTING_OBJECT,
  HEIMI_FORM,
  HIMI_FORM,
  M12_HINT_CONTRACTS,
  NOTIFY_ICD_CODE_RULE_3,
  REFERRAL_FORM,
  RULE_KP7_90_FORMS,
} from '@tutum/mvz/constant/form';
import { STANDALONE_REMEDY_POSITIONS } from '@tutum/mvz/constant/remedy_position';
import { bsnrActions } from '@tutum/mvz/hooks/useBsnr.store';
import { employeeStore } from '@tutum/mvz/hooks/useEmployee';
import {
  FormTypeSetting,
  printSettingStore,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import {
  referralThroughTssActions,
  useReferralThroughTssStore,
} from '@tutum/mvz/hooks/useReferralThroughTss.store';
import { settingActions } from '@tutum/mvz/hooks/useSetting.store';
import useStateCallbackInline from '@tutum/mvz/hooks/useStateWithCallBackInline';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import type ErrorI18n from '@tutum/mvz/locales/en/ErrorCode.json';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import {
  FormElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import AddDiagnosisDialog, {
  IFormValues as IDiagnosisFormValues,
} from '@tutum/mvz/module_form/form-component/add-diagnosis-dialog';
import {
  IFormOverviewActions,
  formOverviewActions,
  useFormOverviewStore,
} from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import {
  type IFormFieldValidation,
  musterFormDialogActions,
  musterFormDialogStore,
  useMusterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import MusterForm from '@tutum/mvz/module_form/muster-form/MusterForm.styled';
import { FIELD_NAME_CORRESPONDING } from '@tutum/mvz/module_form/muster-form/custom-component/muster13';
import { useMusterFormStore } from '@tutum/mvz/module_form/muster-form/musterForm.store';
import {
  type IHeimiSelectionActions,
  type IHeimiSelectionStore,
  heimiSelectionActions,
  useHeimiSelectionStore,
} from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import HimiDoctorSelector from '@tutum/mvz/module_himi/himi-doctor-selector/HimiDoctorSelector.styled';
import {
  LabPurpose,
  labActions,
} from '@tutum/mvz/module_lab/lab-results/Lab.store';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { DEFAULT_LANGUAGES } from '@tutum/mvz/module_patient-management/create-patient-v2/g81EHICInfo/G81EHICInfo';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import type { IPatientEnrollmentActions } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-form/PatientEnrollmentForm.store';
import {
  patientFileActions,
  patientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { useActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type { IMvzThemeProps } from '@tutum/mvz/theme';
import EAUSettings from './eau-settings/EAUSettings';
import GlobalContext from '@tutum/mvz/contexts/Global.context';

const pdfIcon = '/images/pdf-icon.svg';
const downloadIcon = '/images/download.svg';
const InfoSolidWarn = '/images/info-solid-warn.svg';
const WarningIcon = '/images/alert-circle-solid.svg';

export interface IMusterPrescribeMemoProps {
  className?: string;
  selectedContractDoctor: ISelectedContractDoctor | null;
  patient?: IPatientProfile;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  formName?: string;
  componentStore?: IHeimiSelectionStore | any;
  componentActions?:
  | IHeimiSelectionActions
  | IFormOverviewActions
  | IPatientEnrollmentActions
  | any;
  languages?: IMenuItem[];
  isDMPEnrollmentForm?: boolean;
  isStatistics?: boolean;
  hasSaveButton?: boolean;
  children?: React.ReactNode;
  isFormPrint?: boolean;
  onChangeLanguage?: (language: string) => void;
  onActions?: (
    formSettings?: FORM_SETTING_OBJECT,
    prescribeId?: string
  ) => Promise<void>;
  onPrintPlainForm?: (
    currentFormSetting: string,
    formName: string
  ) => Promise<void>;
  onCustomActions?: (
    language?: string,
    status?: any,
    callback?: () => void
  ) => Promise<void>;
  onCreateSchein?: () => void;
}

const MusterFormDialog = (
  props: IMusterPrescribeMemoProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof FormI18n.Prescrible>
) => {
  const {
    t,
    className,
    selectedContractDoctor,
    patient,
    languages,
    isDMPEnrollmentForm,
    isStatistics,
    hasSaveButton = true,
    children,
    isFormPrint,
    onChangeLanguage,
    onCustomActions,
    onActions,
    onPrintPlainForm,
    onCreateSchein,
    onSuccess,
  } = props;
  const [loading, setLoading] = useState<boolean>(true);
  const { t: tError } = I18n.useTranslation<keyof typeof ErrorI18n>({
    namespace: 'ErrorCode',
  });
  const { t: tMedicineDetail } = I18n.useTranslation<
    keyof typeof MedicationI18n.MedicineDetail
  >({
    namespace: 'Medication',
    nestedTrans: 'MedicineDetail',
  });

  const [employmentInfo, setEmploymentInfo] = useState(
    patient?.patientInfo.employmentInfo
  );
  const [openConfirmCloseDialog, setOpenConfirmCloseDialog] =
    useStateCallbackInline(false);
  const [isSaveFlow, setSaveFlow] = useState<boolean>(false);
  const [valueSpecialGroup, setValueSpecialGroup] = useState<{
    value: string;
    label: string;
  }>({ value: '01', label: 'Hausärzte' });
  const [valueUrgency, setValueUrgency] = useState<string>('');
  const [valueLanr, setValueLanr] = useState<string>('lanr');
  const [valueBsnr, setValueBsnr] = useState<string>('bsnr');
  const [valueAdditional, setValueAdditional] = useState<any>([]);
  const [valueService, setValueService] = useState<any>([]);
  const [valueProcedure, setValueProcedure] = useState<any>([]);
  const [okv, setOkv] = useState('');

  const [currentFormConsiderationType, setCurrentFormConsiderationType] =
    useState<LabPurpose>(LabPurpose.Print);

  const [selectedRecipient, setSelectedRecipient] = useState<SendTo>(
    SendTo.OrderEntrySystem
  );

  const currentSchein = useCurrentSchein();
  const actionBarStore = useActionBarStore();
  const store = useMusterFormDialogStore();
  const heimiStore = useHeimiSelectionStore();
  const musterFormStore = useMusterFormStore();
  const fomrOverviewStore = useFormOverviewStore();
  const referralThroughTssStore = useReferralThroughTssStore();
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();

  const { data, isSuccess } = useQueryCheckDummyVknr(
    {
      scheinId: currentSchein?.scheinId as string,
    },
    {
      enabled: !!currentSchein,
    }
  );

  // const isLabForms = LAB_FORM.includes(store.currentFormName as FormName);
  const isLabForms = false;
  const isHimiForms = HIMI_FORM.includes(store.currentFormName as FormName);
  const isHeimiForms = HEIMI_FORM.includes(store.currentFormName as FormName);
  const isG81EHICForms = DEFAULT_LANGUAGES.includes(
    store.currentFormName?.split('G81_EHIC_')[1] || ''
  );
  const isEAU =
    musterFormStore.formName === FormName.Muster_1 && !store.EAUDisable;
  const isBGFormsInvoice = BG_FORM_INVOICE.includes(
    store.currentFormName as FormName
  );

  const formSetting = useMemo(() => {
    return printSettingStore.formsSetting.find(
      (form) => form.formId === store.currentFormName
    );
  }, [store.currentFormName]);
  const ikNumber = patientFileStore.activeInsurance?.ikNumber;

  const isEnrollment = formSetting?.type === FormTypeSetting.ENROLLMENT_FORM;
  const isReferralForms = REFERRAL_FORM.includes(
    store.currentFormName as FormName
  );
  const isFormM06 = [
    FormName.Muster_6_cover_letter,
    FormName.Muster_6,
  ].includes(store.currentFormName as FormName);
  const isFormPTV11 = [
    FormName.Muster_PTV_11A,
    FormName.Muster_PTV_11B,
  ].includes(store.currentFormName as FormName);

  const isDigitalFrom = [FormName.Muster_10, FormName.Muster_10A].includes(
    store.currentFormName as FormName
  );

  const isErrorLabId =
    store?.isDuplicatedLabId ||
    (isDigitalFrom && store?.labId && store.labId.length < 10) ||
    (!isDigitalFrom && store.labId && store.labId.length < 10);

  const { isContractSupport: hasVSST623 } = useCheckContractSupport(
    ['VSST623'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportForm907 } = useCheckContractSupport(
    ['FORM907'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportForm1479 } = useCheckContractSupport(
    ['FORM1479'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportVSST848 } = useCheckContractSupport(
    ['VSST848'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportForm1413 } = useCheckContractSupport(
    ['FORM1413'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportForm938 } = useCheckContractSupport(
    ['FORM938'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportForm815 } = useCheckContractSupport(
    ['FORM815'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportABRD850 } = useCheckContractSupport(
    ['ABRD850'],
    [store.currentSchein?.hzvContractId]
  );
  const { isContractSupport: hasSupportForm1237 } = useCheckContractSupport(
    ['FORM1237'],
    [store.currentSchein?.hzvContractId]
  );

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    store.doctor?.data?.id,
    store.doctor?.data?.bsnrId
  );

  useListenResponseReferenceCode((response) => {
    if (response?.replyToMessageId === referralThroughTssStore.messegeId) {
      referralThroughTssActions.setTssCode(response);
      musterFormDialogActions.fetchDataAuftrag();
      if (bsnrActions.checkIsHospital(employeeStore?.userProfile?.bsnr || '')) {
        handleChangeInsuranceStatus(store?.currentFormSetting);
      }
    }
  });

  const isLHM = useMemo(() => {
    return heimiStore.indicator?.label === DiagnoseLabel.LHM;
  }, [heimiStore.indicator]);

  const isBVB = useMemo(() => {
    return (
      heimiStore.indicator?.label === DiagnoseLabel.BVB &&
      heimiStore.indicator.date
    );
  }, [heimiStore.indicator]);

  const isBVBInValidDate = useMemo(() => {
    return (
      heimiStore.indicator?.label === DiagnoseLabel.BVB ||
      (!heimiStore.indicator?.label && heimiStore.indicator?.inValidDate)
    );
  }, [heimiStore.indicator]);

  const checkValidRemedyQuantityForStandardCase = (quantity) => {
    if (
      !heimiStore.isStandardCombination ||
      !heimiStore.maxQuantityStandardCombination ||
      heimiStore.isLHMApprove
    ) {
      return true;
    }

    return (
      quantity + heimiStore.totalQuantityStandardCombination <=
      heimiStore.maxQuantityStandardCombination
    );
  };

  const checkValidRemedyQuantityForMassageCase = () => {
    const totalQuantityMassage = heimiStore.currentRemedies?.reduce(
      (sum, item, index) => {
        if (item.isMassage) {
          sum +=
            +Number(
              store?.currentFormSetting?.[
              `textbox_behandlung_line${index + 1}_0`
              ]
            ) || 0;
        }

        return sum;
      },
      0
    );

    if (!totalQuantityMassage || heimiStore.maxQuantityMassage) {
      return true;
    }

    const actualTotal =
      (heimiStore.totalPreviousQuantityRemediesMassage || 0) +
      totalQuantityMassage;
    const maximumTotal = heimiStore.maxQuantityMassage || 0;

    return actualTotal <= maximumTotal;
  };

  const maxRemedyQuantity = useMemo(() => {
    if (
      isLHM ||
      (isBVB && !isBVBInValidDate) ||
      heimiStore.isLHMApprove ||
      isBVBInValidDate
    ) {
      return null;
    }
    return heimiStore.maxQuantity;
  }, [
    isLHM,
    isBVB,
    heimiStore.isLHMApprove,
    heimiStore.maxQuantity,
    isBVBInValidDate,
  ]);

  const checkSpecialComplementaryRemedy = () => {
    const inSpecialList =
      !!heimiStore.currentComplementaryRemedies?.length &&
      STANDALONE_REMEDY_POSITIONS.includes(
        heimiStore.currentComplementaryRemedies[0].position
      );
    return inSpecialList;
  };
  const totalRemedyQuantity =
    +(store.currentFormSetting?.textbox_behandlung_line1_0 || 0) +
    +(store.currentFormSetting?.textbox_behandlung_line2_0 || 0) +
    +(store.currentFormSetting?.textbox_behandlung_line3_0 || 0);

  function checkInvalidHeimiForm() {
    const specialRemedy = checkSpecialComplementaryRemedy();
    if (specialRemedy) {
      return false;
    }
    const invalidHeimiForm =
      (store.currentFormSetting?.checkbox_patientenindividuelle_0 &&
        !store.currentFormSetting?.area_textbox_leitsymtomatik_0) ||
      (maxRemedyQuantity && totalRemedyQuantity > +maxRemedyQuantity) ||
      (maxRemedyQuantity &&
        +(store.currentFormSetting?.textbox_behandlung_line4_0 || 0) >
        +maxRemedyQuantity) ||
      !checkValidRemedyQuantityForStandardCase(
        +(store.currentFormSetting?.textbox_behandlung_line1_0 || 0)
      ) ||
      !checkValidRemedyQuantityForMassageCase() ||
      totalRemedyQuantity <
      +(store.currentFormSetting?.textbox_behandlung_line4_0 || 0);
    return invalidHeimiForm;
  }

  const invalidHeimiForm = checkInvalidHeimiForm();

  const isMatchContract = !!intersection(
    M12_HINT_CONTRACTS,
    patient?.listHpmInformation
      ?.filter(
        (item) =>
          item.status === HpmInformationStatus.HpmInformationStatus_Active
      )
      .map((item) => item.contractId)
  ).length;
  const showHint =
    isMatchContract && store.currentFormName === FormName.Muster_12A;

  useEffect(() => {
    if (!selectedContractDoctor) return;

    musterFormDialogActions.setContractDoctor(selectedContractDoctor);

    formOverviewActions.getOKV().then((res) => setOkv(res));
  }, [JSON.stringify(selectedContractDoctor)]);

  useEffect(() => {
    if (isEmpty(currentLoggedInUser) || !selectedContractDoctor) {
      return;
    }

    const currentTreatmentDoctor = selectedContractDoctor.availableDoctor.find(
      (doctor) => {
        if (currentLoggedInUser.isDoctor) {
          return currentLoggedInUser.id === doctor.id;
        }

        return currentSchein?.doctorId === doctor.id;
      }
    );

    if (currentTreatmentDoctor) {
      musterFormDialogActions.setCurrentTreatmentDoctor({
        label: currentTreatmentDoctor.fullName,
        value: `${currentTreatmentDoctor.id}-${currentTreatmentDoctor.bsnrId}`,
        id: `${currentTreatmentDoctor.id}-${currentTreatmentDoctor.bsnrId}`,
        data: currentTreatmentDoctor,
      });
    }
  }, [JSON.stringify(selectedContractDoctor), currentLoggedInUser, currentSchein]);

  useEffect(() => {
    if (!patient) {
      return;
    }
    musterFormDialogActions.setPatient(patient);
  }, [JSON.stringify(patient)]);

  useEffect(() => {
    !!currentSchein && musterFormDialogActions.setCurrentSchein(currentSchein);
  }, [currentSchein]);

  useEffect(() => {
    if (
      patient?.id &&
      store.doctor?.data?.id &&
      store.doctor?.data?.bsnrId &&
      store.currentFormName
    ) {
      musterFormDialogActions.loadFormPatientHeaderInfo(
        patient?.id,
        store?.doctor?.data?.id,
        store.currentFormName,
        ikNumber,
        store.doctor?.data?.bsnrId
      );
      musterFormDialogActions.getListDiagnosis();
      referralThroughTssActions.checkKVConnect(store.doctor.data.id);
    }
  }, [
    patient?.id,
    store.doctor?.data?.id,
    store.doctor?.data?.bsnrId,
    store.currentFormName,
  ]);

  // Set the passed store and action from component to store
  useEffect(() => {
    musterFormDialogActions.setStoreAndAction(
      props.componentStore,
      props.componentActions
    );
  }, [props.componentStore, props.componentActions]);

  useEffect(() => {
    if (!store.himiPrescriptionId && !store.isRefill) {
      if (
        store.currentFormName &&
        patient &&
        selectedContractDoctor &&
        store.currentFormSetting &&
        musterFormStore.formAnnotationInfo
      ) {
        switch (store.currentFormName) {
          case FormName.Muster_1:
          case FormName.Muster_2B:
          case FormName.Muster_5:
          case FormName.Muster_6:
          case FormName.Muster_10:
          case FormName.Muster_13:
          case FormName.Muster_15:
          case FormName.Muster_16:
          case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
          case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
          case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
          case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
          case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3:
          case FormName.Muster_36_E_2017_07:
          case FormName.Begleitschreiben_FaV_V4:
          case FormName.Muster_N63A:
          case FormName.Muster_65A:
          case FormName.Muster_55:
          case FormName.Muster_56:
          case FormName.Muster_61:
          case FormName.Muster_64:
          case FormName.Muster_12A:
          case FormName.Muster_28A:
          case FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6:
          case FormName.Muster_PTV_11A:
          case FormName.Muster_7:
          case FormName.Muster_11:
          case FormName.Muster_26A:
          case FormName.Muster_PTV_2A:
          case FormName.Muster_PTV_12A:
          case FormName.BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17:
          case FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6:
          case FormName.AOK_FA_NPPP_BW_GDK_Antragsformular_V6:
          case FormName.BKK_BOSCH_FA_BW_GDK_Antragsformular_V4:
          case FormName.BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3:
          case FormName.BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2:
          case FormName.AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3:
          case FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
          case FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
          case FormName.Muster_52_0_V2:
          case FormName.Muster_52_2_V3:
          case FormName.Ambulantes_Operieren_V1:
          case FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1:
          case FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4:
          case FormName.F1000:
          case FormName.F1050:
          case FormName.F2100:
          case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3:
          case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2:
          case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3:
          case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3:
          case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3:
          case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3:
            musterFormDialogActions.preFillTimelineData(
              patient,
              selectedContractDoctor
            );
            break;
          default:
            break;
        }
      }
    }
  }, [
    store.himiPrescriptionId,
    store.isRefill,
    store.currentFormName,
    JSON.stringify(patient),
    JSON.stringify(selectedContractDoctor),
    JSON.stringify(musterFormStore.formAnnotationInfo),
  ]);

  // NOTE: check dummy data
  const hasDummyVknr = isSuccess && data.isDummy;

  const isEV = useMemo(() => {
    const currentInsurance = patient?.patientInfo?.insuranceInfos.find(
      (info) => info.id === store.currentSchein?.insuranceId
    );

    if (!currentInsurance) {
      return false;
    }

    return isEVCase(currentInsurance, DatetimeUtil.now(), store.currentSchein);
  }, [patient, store.currentSchein]);

  const isWarningEV = useMemo(() => {
    return (
      isEV && RULE_KP7_90_FORMS.includes(store.currentFormName as FormName)
    );
  }, [isEV, store.currentFormName]);

  const isWarningMediContract = useMemo(() => {
    return (
      isEnrollment &&
      [
        store.contractDoctor?.contractId,
        store.formPrescription?.contractId,
      ].includes(FormName.MEDI_FA_PT_BW)
    );
  }, [isEnrollment, store.contractDoctor, store.formPrescription]);

  const formWarningList = useMemo(() => {
    return [
      isWarningEV ? 'noInsurance' : '',
      isWarningMediContract ? 'mediContract' : '',
      hasSupportVSST848 && store.isSupportVSST848 ? 'M1ICDNotify' : '',
      hasSupportForm1479 ? 'transitionManagement' : '',
      hasSupportForm1413 &&
        [FormName.Muster_52_0_V2, FormName.Muster_52_2_V3].includes(
          store.currentFormName as FormName
        )
        ? 'M52Notify'
        : '',
      hasSupportForm938 &&
        [
          FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1,
          FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4,
          FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6,
        ].includes(store.currentFormName as FormName)
        ? 'quickInformation'
        : '',
    ].filter((value) => value) as WarningLabel[];
  }, [
    isWarningEV,
    isWarningMediContract,
    hasSupportVSST848,
    store.isSupportVSST848,
    hasSupportForm1479,
    hasSupportForm1413,
    store.currentFormName,
    hasSupportForm938,
  ]);

  const renderMessegeWarning = () => {
    if (referralThroughTssStore.tssCode) {
      return t('HintCharactersMsg');
    }

    return t('overtakeWarning');
  };

  const warningEV = useMemo(() => {
    if (!isWarningEV) {
      return null;
    }

    return {
      fieldIndex: 0,
      fieldName: 'evWarning',
      isWarning: () => true,
    };
  }, [isWarningEV]);

  const getErrorMessage = (field: IFormFieldValidation) => {
    const error = field?.getErrors?.();
    if (error) {
      return t(error as keyof typeof FormI18n.Prescrible);
    }

    if (field.message) {
      return field.message;
    }

    if (field?.hasCheckValidICDCode) {
      return t('InvalidICDCodeError');
    }

    if (field?.isInValidMaxLength) {
      return t('LimitCharacters', { number: field?.maxLength });
    }

    if (field?.hasCheckPrimaryICDCode) {
      return t('InvalidICDCodeSecondary');
    }

    if (field?.hasCheckReservedICDCode) {
      return t('InvalidICDCodeReserved');
    }

    if (field?.hasCheckNonExitICDCode) {
      return t('InvalidICDCodeNonExist');
    }

    return t('WarningMsg');
  };

  const getWarningMessage = (field: IFormFieldValidation, index: number) => {
    const warningMessage = field?.getWarnings?.();
    if (warningMessage) {
      return t(warningMessage as keyof typeof FormI18n.Prescrible);
    }

    if (isHeimiForms && (!warningEV || index)) {
      return field.message;
    }

    return renderMessegeWarning();
  };

  const renderWarningValidation = useMemo(() => {
    if (!store.invalidFormArray?.length && !store.warningFormArray?.length) {
      return null;
    }

    return (
      <>
        <div className="">
          <H3>{t('ErrorMsg')}</H3>
          <div className="sl-error-list">
            {(!!store.invalidFormArray?.length ||
              !!store.warningFormArray?.length) &&
              uniqBy(
                [
                  ...(store.invalidFormArray || []),
                  ...(store.warningFormArray || []),
                ],
                'fieldIndex'
              )
                .sort((a, b) => (a.fieldIndex || 0) - (b.fieldIndex || 0))
                .map((field: IFormFieldValidation, index) => {
                  const warningMessage = field?.getWarnings?.();
                  if (field?.isWarning?.() || warningMessage) {
                    return (
                      <div
                        className={`${Classes.FILL} error-wrapper warning-callout`}
                        key={`${field.fieldName}-${field.fieldIndex}`}
                        data-test-id={`${field.fieldName}-${field.fieldIndex}`}
                      >
                        {!field.fieldIndex ? (
                          <div style={{ marginRight: '16px' }}>
                            <Svg src={WarningIcon} width={20} />
                          </div>
                        ) : (
                          <div
                            style={{
                              width: referralThroughTssStore.tssCode
                                ? '30px'
                                : '',
                            }}
                            className={'warning-badge'}
                          >
                            {isHeimiForms
                              ? index + (warningEV ? 0 : 1)
                              : field.fieldIndex}
                          </div>
                        )}
                        <div>{getWarningMessage(field, index)}</div>
                      </div>
                    );
                  }

                  const errMsg = getErrorMessage(field);
                  return (
                    <div
                      className={`${Classes.FILL} error-wrapper error-callout`}
                      key={`${field.fieldName}-${field.fieldIndex}`}
                      data-test-id={`${field.fieldName}-${field.fieldIndex}`}
                    >
                      <div className={'error-badge'}>
                        {isHeimiForms ? index + 1 : field.fieldIndex}
                      </div>
                      <div>
                        <b>{errMsg}</b>
                      </div>
                    </div>
                  );
                })}
          </div>
        </div>
        <Flex mb={16} />
      </>
    );
  }, [
    store.validationDiagnosisResult,
    store.warningFormArray,
    store.invalidFormArray,
    referralThroughTssStore.tssCode,
  ]);

  const [additionalWarningFormArray, setAdditionalWarningArray] = useState<
    Meldung[]
  >([]);
  const renderForm1844WarningValidation = useMemo(() => {
    if (!additionalWarningFormArray.length) {
      return null;
    }
    return (
      <>
        <div className="col">
          <H3>{t('Warning')}</H3>
          <div className="col-md sl-error-list">
            {additionalWarningFormArray.map((e) => {
              const msg = `${e.code} - ${e.nachricht}`;
              return (
                <div
                  className={`error-wrapper warning-callout`}
                  key={`${e.code}`}
                >
                  <div style={{ marginRight: '16px' }}>
                    <Svg src={WarningIcon} width={20} />
                  </div>
                  <div>{msg}</div>
                </div>
              );
            })}
          </div>
        </div>
        <br />
        <Flex mb={16} />
      </>
    );
  }, [additionalWarningFormArray]);

  const renderQuestionFormInfo = () => {
    return (
      <Flex mb={16}>
        <Flex align="center">
          <Tag round large className="question">
            <BodyTextL fontWeight="Bold">
              {store?.questionName?.toUpperCase()}
            </BodyTextL>
          </Tag>
        </Flex>
        <Flex ml={5} align="center">
          x 1
        </Flex>
      </Flex>
    );
  };

  const isQuickInformationForm = useMemo(() => {
    return (
      hasSupportForm815 &&
      [
        FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1,
        FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4,
        FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6,
        FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6,
      ].includes(store.currentFormName as FormName)
    );
  }, [hasSupportForm815, store.currentFormName]);

  const isDisableSaveButton =
    hasDummyVknr ||
    (!!store?.formPrescription?.id &&
      (isHeimiForms
        ? invalidHeimiForm
        : !store.isValidForm || isErrorLabId || store.EAUDisable));

  const isDisableButton =
    ![FormName.BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1].includes(
      store.currentFormName as FormName
    ) &&
    (hasDummyVknr
      ? true
      : isHeimiForms
        ? invalidHeimiForm
        : !store.isValidForm || isErrorLabId || store.EAUDisable);

  const handleSaveButton = async () => {
    const showToastSuccess = () => {
      alertSuccessfully(t('FormSaved'));
    };

    musterFormDialogActions.returnDataAuftragToPrint();

    if (isG81EHICForms) {
      onCustomActions?.(
        store.currentFormName?.split('G81_EHIC_')[1] || '',
        EuropeanHealthInsuranceStatus.EuropeanHealthInsuranceStatus_Saved,
        showToastSuccess
      );
      return;
    }

    if (isDMPEnrollmentForm) {
      onCustomActions?.(
        store.currentFormName || '',
        ParticipationFormsStatus.ParticipationFormsStatus_Save
      );
      return;
    }

    if (isLabForms) {
      labActions.prescribeForm({ showToastSuccess }, LabFormStatus.Save, true);
    } else {
      // do not remove this code
      const { isValid } = await musterFormDialogActions.validateDiagnosis();
      if (!isValid) return;

      const printerSetting = store.printerSettingData
        ? {
          ...store.printerSettingData,
        }
        : null;
      let timelineId;
      if (isHimiForms) {
        timelineId = store.himiPrescriptionId;
      } else if (isHeimiForms) {
        timelineId = store.heimiPrescriptionId;
      }

      musterFormDialogActions
        .prescrible(undefined, undefined, timelineId)
        .then(async () => {
          if (store.currentFormName === FormName.Muster_13) {
            patientFileActions.setActiveTabId(ID_TABS.TIMELINE);
            store.componentActions.clear();
          }
          showToastSuccess();

          if (!isEmpty(printerSetting)) {
            await settingActions.savePrinterSetting(printerSetting);
          }
          musterFormDialogActions.clear();
          referralThroughTssActions.clear();
          onSuccess?.();
        });
    }
  };

  const renderSaveButton = () => {
    if (store?.isViewForm || !hasSaveButton || isEnrollment) {
      return null;
    }
    return (
      <Button
        disabled={isDisableSaveButton || false}
        className={`${Classes.FILL} secondary`}
        intent="primary"
        loading={store?.isLoadingPrescribe}
        data-test-id="print-preview-save-button"
        onClick={async () => {
          if (isQuickInformationForm) {
            setSaveFlow(true);
            setOpenConfirmCloseDialog(true);
            return;
          }

          handleSaveButton();
        }}
        {...registerActionChainElementId(FormElementId.DIALOG_SAVE_BUTTON)}
      >
        {t(store.formPrescription?.id && !store.isRefill ? 'Update' : 'Save')}
      </Button>
    );
  };

  const renderPrintButton = (
    <>
      <Button
        disabled={isDisableButton || false}
        data-test-id="print-preview-print-button"
        onClick={async () => {
          if (isDisableButton) {
            return;
          }

          const showToastSuccess = () => {
            // alertSuccessfully(t('FormPrescribed'));
          };

          const currentFormSetting = cloneDeep({
            ...musterFormDialogStore.currentFormSetting,
            date_prescribe:
              musterFormDialogStore.currentFormSetting?.['date_prescribe'] ||
              DatetimeUtil.now(),
          });

          if (isEnrollment) {
            if (!musterFormDialogStore.isViewForm) {
              await musterFormDialogActions.prescrible(
                DatetimeUtil.now(),
                hasSupportForm907,
                '',
                JSON.stringify(currentFormSetting)
              );
              await onActions?.(currentFormSetting);
              musterFormDialogActions.clear();
              referralThroughTssActions.clear();
            } else {
              await onPrintPlainForm?.(
                JSON.stringify(currentFormSetting),
                musterFormDialogStore.currentFormName || ''
              );
            }
            return;
          }
          musterFormDialogActions.returnDataAuftragToPrint();

          const printerSetting = store.printerSettingData
            ? {
              ...store.printerSettingData,
            }
            : null;
          const { isValid } = await musterFormDialogActions.validateDiagnosis();
          if (!isValid) return;
          if (isG81EHICForms) {
            onCustomActions?.(
              store.currentFormName?.split('G81_EHIC_')[1] || '',
              EuropeanHealthInsuranceStatus.EuropeanHealthInsuranceStatus_Printed,
              showToastSuccess
            );
            return;
          }

          if (isDMPEnrollmentForm) {
            onCustomActions?.(
              store.currentFormName || '',
              ParticipationFormsStatus.ParticipationFormsStatus_Print
            );
            return;
          }

          if (isLabForms && store.labFormPurpose === LabPurpose.SendDigital) {
            labActions.prescribeForm(
              { showToastSuccess },
              LabFormStatus.Send,
              false
            );
          } else {
            if (!store.isViewForm && !store.currentSchein) {
              alertError(t(`ErrorSelectSchein`));
            } else {
              let timelineId;
              if (isHimiForms) {
                timelineId = store?.himiPrescriptionId;
              } else if (isHeimiForms) {
                timelineId = store?.heimiPrescriptionId;
              }
              musterFormDialogActions
                .prescrible(DatetimeUtil.now(), hasSupportForm907, timelineId)
                .then(async (resp: any) => {
                  if (store.currentFormName === FormName.Muster_13) {
                    patientFileActions.setActiveTabId(ID_TABS.TIMELINE);
                    store.componentActions.clear();
                  }
                  showToastSuccess();
                  if (!isEmpty(printerSetting)) {
                    await settingActions.savePrinterSetting(printerSetting);
                  }

                  if (isBGFormsInvoice && !store.isViewForm) {
                    onActions?.(undefined, resp.data.timelineId);
                  }
                })
                .finally(() => {
                  onSuccess?.();
                });
            }
          }
        }}
        loading={store?.isLoadingPrescribe}
        className={`${isDisableButton ? 'btn-disabled' : ''} ${Classes.FILL}`}
        intent="primary"
        {...registerActionChainElementId(FormElementId.DIALOG_PRINT_BUTTON)}
      >
        {isLabForms && store.labFormPurpose === LabPurpose.SendDigital
          ? t('Send')
          : t('Print')}
      </Button>
    </>
  );

  const handleLeave = () => {
    musterFormDialogActions.clear();
    referralThroughTssActions.clear();
    props.onClose();
  };

  const setValueFormReferralToInit = () => {
    setValueAdditional([]);
    setValueProcedure([]);
    setValueService([]);
    setValueUrgency('');
    setValueSpecialGroup({ value: '01', label: 'Hausärzte' });
    setValueBsnr('bsnr');
    setValueLanr('lanr');
  };

  const recipient = Object.entries(SendTo).map(([_, value]) => value);

  const renderRecipientOption = (item: string, { handleClick }) => {
    return (
      <MenuItem
        key={item}
        text={t(item as keyof typeof FormI18n.Prescrible)}
        onClick={handleClick}
        shouldDismissPopover={true}
      />
    );
  };

  const renderFormConsideration = () => {
    if (!isLabForms) return null;
    return (
      <Flex column>
        <Flex column>
          <FormGroup
            helperText={
              store?.isDuplicatedLabId
                ? t('errDuplicatedLabId')
                : isErrorLabId
                  ? t('errLabId')
                  : ''
            }
            className={isErrorLabId ? Classes.INTENT_DANGER : ''}
          >
            <Label className="sl-label-right-side">{t('LabID')}</Label>
            <NumberInput
              isRaw
              defaultValue={store.labId}
              maxLength={60}
              fill
              disabled={store?.isViewForm}
              onValueChange={({ value }) => {
                musterFormDialogActions.setLabId(value);
              }}
              intent={isErrorLabId ? Intent.DANGER : undefined}
            />
          </FormGroup>
        </Flex>
        <Flex column>
          <Label className="sl-label-right-side">
            {t('FormConsiderationTitle')}
          </Label>
          <RadioGroup
            className="sl-form-consideration-group"
            onChange={(e: FormEvent<HTMLInputElement>) => {
              setCurrentFormConsiderationType(
                LabPurpose[e.currentTarget.value]
              );
              musterFormDialogActions.setLabFormPurpose(
                LabPurpose[e.currentTarget.value]
              );
            }}
            selectedValue={currentFormConsiderationType}
          >
            <Radio
              label={t('FormConsideration_PrintedForm')}
              value={LabPurpose.Print}
            />
            <Radio
              label={t('FormConsideration_Digital')}
              value={LabPurpose.SendDigital}
            />
          </RadioGroup>
          <Flex column pl={26}>
            <Label className="sl-label-right-side">{t('labRecipient')}</Label>
            <div>
              <Select
                className={`${className}`}
                items={recipient}
                filterable={false}
                popoverProps={{ usePortal: false, minimal: true }}
                itemRenderer={renderRecipientOption}
                onItemSelect={(selectedRecipient: string) => {
                  setSelectedRecipient(SendTo[selectedRecipient]);
                  musterFormDialogActions.setFormSendToRecipient(
                    selectedRecipient
                  );
                }}
                disabled={store.labFormPurpose !== LabPurpose.SendDigital}
              >
                <Button
                  text={t(
                    recipient?.find(
                      (d) => d === selectedRecipient
                    ) as keyof typeof FormI18n.Prescrible
                  )}
                  className={`sl-select-search ${Classes.FILL}`}
                  rightIcon="caret-down"
                />
              </Select>
            </div>
          </Flex>
        </Flex>
      </Flex>
    );
  };

  const hints = useMemo(() => {
    const hints = store.hint?.length ? [...store.hint] : [];
    switch (store?.currentFormName) {
      case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
      case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
      case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
      case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
      case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3:
        // hints.push(t('transitionManagementHint'));
        if (
          [
            FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1,
            FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2,
          ].includes(store?.currentFormName)
        ) {
          hints.push(t('noFaxNumberHint'));
        }
        break;
      default:
        break;
    }
    return hints.map((hint: string, index: number) => (
      <BodyTextM key={index}>{hint}</BodyTextM>
    ));
  }, [
    store?.hint,
    store?.currentFormName,
    patient?.patientInfo?.insuranceInfos,
  ]);

  const cbNavigateToLabOverviewPage = useMemo(() => {
    if (store?.currentFormName !== FormName.Begleitschreiben_FaV_V4) {
      return null;
    }
    return (
      <Checkbox
        label={t('includeLabResult')}
        checked={store?.includeLabResult}
        onChange={(e) =>
          musterFormDialogActions.setIncludeLabResult(e.currentTarget.checked)
        }
      />
    );
  }, [store?.currentFormName, store?.includeLabResult]);

  useEffect(() => {
    setLoading(false);
  }, []);

  useEffect(() => {
    if (!isReferralForms || !referralThroughTssStore.tssCode) return;

    referralThroughTssActions.sendTssCode(false);
    const err = referralThroughTssStore.tssCode.error;
    if (err && err.message) {
      let errMsg = err.message;
      if (err.serverErrorParam) {
        errMsg = tError(`${err.message}` as any, {
          code: err.serverErrorParam[0],
          message: err.serverErrorParam[1],
        });
      } else {
        errMsg = tError(`${err.message}` as any);
      }
      alertError(errMsg);
    } else if (referralThroughTssStore.tssCode.data) {
      setTimeout(function () {
        alertSuccessfully(t(`ReceiveCodeSuccess`));
      });
    }
  }, [JSON.stringify(referralThroughTssStore.tssCode)]);

  const handlerError = () => {
    if (!referralThroughTssStore.tssCode?.data) {
      const tssError: EventResponseReferenceCode = {
        emailAddress: '',
        replyToMessageId: '',
        data: undefined,
        error: {
          status: 0,
          message: '',
          serverError: '',
          serverErrorParam: [],
        },
      };
      referralThroughTssActions.setTssCode(tssError);
    }
    referralThroughTssActions.setIsOpenReferralDialog(false);
  };

  const renderReferralFormHeader = useMemo(() => {
    const error = referralThroughTssStore.tssCode?.error;
    if (referralThroughTssStore?.isSendGetTssCode) {
      return (
        <Flex className="header-get-referral-code bg-info">
          <LoadingState
            size={15}
            className="icon-wait"
            height="unset"
            width="unset"
            border={'2px'}
          />
          <div>{t('GetReferralCodeWait')}</div>
        </Flex>
      );
    } else if (error) {
      return (
        <div>
          <Flex
            className="header-get-referral-code bg-error"
            style={{ marginBottom: '1px' }}
          >
            <Svg src={InfoSolidWarn} className="icon-state" />
            <div>{t('GetReferralCodeFail2')}</div>
          </Flex>
        </div>
      );
    }
    return null;
  }, [
    JSON.stringify(referralThroughTssStore.tssCode),
    referralThroughTssStore?.isSendGetTssCode,
  ]);

  useEffect(() => {
    if (
      ![FormName.Muster_1].includes(store.currentFormName as FormName) ||
      isEmpty(store.currentFormSetting)
    ) {
      musterFormDialogActions.setSupportVSST848(false);
      return;
    }

    const isExist = Object.keys(store.currentFormSetting).some((key) => {
      if (!key.includes('label_icd10_code')) {
        return false;
      }

      const value = ((store.currentFormSetting?.[key] as string) || '').split(
        ' '
      )[0];

      return NOTIFY_ICD_CODE_RULE_3.includes(value);
    });
    musterFormDialogActions.setSupportVSST848(isExist);
  }, [store.currentFormSetting, store.currentFormName]);

  if (loading) {
    return <LoadingState />;
  }

  const processCloseForm = () => {
    if (store.isOpenAddDiagnosisDialog) {
      musterFormDialogActions.setIsOpenAddDiagnosisDialog(false);
      return;
    }
    setOpenConfirmCloseDialog(true);
  };

  const renderGBAGuideline = () => {
    if (!hasVSST623) {
      return null;
    }

    const doc = fomrOverviewStore.listForms?.find((f) =>
      f.id.startsWith('G-BA_Hilfsmittel-Richtlinie')
    );
    if (!doc) {
      return null;
    }
    return (
      <>
        <Divider className="sl-divider" />
        <Flex column>
          <H3>{t('GBAGuideline')}</H3>
          <Link
            onClick={async () => {
              const docType =
                doc.formType === FormType.FormType_public_document
                  ? 'Dokumente'
                  : 'Vertragstexte';
              const fileName = `${docType}/${doc.fileNameWithVersion}.pdf`;
              const fileUrl = await formOverviewActions.getFileUrl(fileName);
              window.open(fileUrl, '_blank');
            }}
          >
            <Flex
              align="center"
              gap={scaleSpace(2)}
              style={{ marginLeft: scaleSpace(1) }}
            >
              <Svg src={pdfIcon} />
              {doc.id}.pdf
              <Svg src={downloadIcon} />
            </Flex>
          </Link>
        </Flex>
      </>
    );
  };

  const renderInstructionDocument = () => {
    const doc = fomrOverviewStore.listForms?.find((f) =>
      f.id.startsWith('MerkblattVersicherter_Hilfsmittel')
    );

    if (!doc) {
      return null;
    }

    return (
      <Link
        onClick={async () => {
          const docType =
            doc.formType === FormType.FormType_public_document
              ? 'Dokumente'
              : 'Vertragstexte';
          const fileName = `${docType}/${doc.fileNameWithVersion}.pdf`;
          const fileUrl = await formOverviewActions.getFileUrl(fileName);
          window.open(fileUrl, '_blank');
        }}
      >
        <Flex
          align="center"
          gap={scaleSpace(2)}
          style={{ marginLeft: scaleSpace(1) }}
        >
          <Svg src={pdfIcon} />
          {doc.id}.pdf
          <Svg src={downloadIcon} />
        </Flex>
      </Link>
    );
  };

  const renderAdressliste = () => {
    if (!hasVSST623) {
      return null;
    }

    const formId =
      store.currentSchein?.hzvContractId === 'AWH_01'
        ? 'AWH_01_HZV_Adressliste_SD_V5'
        : 'AOK_FAV_BW_Adressliste_SD_V6';
    const doc = fomrOverviewStore.listForms?.find((f) => f.id === formId);
    if (!doc) {
      return null;
    }
    return (
      <>
        <Divider className="sl-divider" />
        <Flex column>
          <H3>{t('Adressliste')}</H3>
          <Link
            onClick={async () => {
              const docType =
                doc.formType === FormType.FormType_public_document
                  ? 'Dokumente'
                  : 'Vertragstexte';
              const fileName = `${docType}/${doc.fileNameWithVersion}.pdf`;
              const fileUrl = await formOverviewActions.getFileUrl(fileName);
              window.open(fileUrl, '_blank');
            }}
          >
            <Flex
              align="center"
              gap={scaleSpace(2)}
              style={{ marginLeft: scaleSpace(1) }}
            >
              <Svg src={pdfIcon} />
              {doc.id}.pdf
              <Svg src={downloadIcon} />
            </Flex>
          </Link>
        </Flex>
      </>
    );
  };

  const renderLeaveConfirmDialog = () => {
    if (isQuickInformationForm) {
      return (
        <LeaveConfirmModal
          buttonClassName="secondary"
          isOpen={openConfirmCloseDialog}
          contentConfirm={t('BKK_BY_HZV_ConfirmLeave')}
          yesLeave={t('BKK_BY_HZV_YesLeave')}
          cancelLeave={t('BKK_BY_HZV_CancelLeave')}
          onConfirm={() => {
            setOpenConfirmCloseDialog(false, () => {
              if (isSaveFlow) {
                handleSaveButton();
                setSaveFlow(false);
                return;
              }

              handleLeave();
            });
          }}
          onClose={() => {
            setOpenConfirmCloseDialog(false);
            setSaveFlow(false);
          }}
        />
      );
    }

    return (
      <LeaveConfirmModal
        isOpen={openConfirmCloseDialog}
        onConfirm={() => {
          setOpenConfirmCloseDialog(false, () => {
            handleLeave();
          });
        }}
        onClose={() => setOpenConfirmCloseDialog(false)}
      />
    );
  };

  const onChangePatientEmployment = (updatedPatient: EmploymentInfo) => {
    setEmploymentInfo((prev) => ({ ...prev, ...updatedPatient }));
  };

  const updatedPatient = {
    ...patient,
    patientInfo: {
      ...(patient?.patientInfo || {}),
      employmentInfo: {
        ...patient?.patientInfo.employmentInfo,
        ...(employmentInfo || {}),
      },
    },
  } as IPatientProfile;

  if (!props.isOpen) {
    return null;
  }

  return (
    <>
      <FormPrintPreviewLayout
        onClose={() => (store?.isViewForm ? handleLeave() : processCloseForm())}
        title={t('PrescribleForm')}
        className={className}
      >
        <>
          {isReferralForms && renderReferralFormHeader}
          <Flex
            className={`himi-prescrible-body`}
            {...registerActionChainElementId(FormElementId.FORM)}
          >
            <div className={FormPrintPreviewClasses.contentLeftSide}>
              <MessageBar
                type="info"
                hasBullet={false}
                content={t('textmoduleHint')}
              />
              <div className="form-container">
                <MusterForm
                  currentFormSetting={store.currentFormSetting}
                  hasSupportForm907={hasSupportForm907}
                  hasSupportABRD850={hasSupportABRD850}
                  formInfoMap={store.patientInfoMap}
                  isViewOnly={store.isViewForm}
                  isRefillOnly={store.isRefill}
                  isLabForms={isLabForms}
                  isStatistics={isStatistics}
                  patient={patient}
                  currentFormName={store.currentFormName}
                  onChangeProduct={() => {
                    props.onClose();
                  }}
                  doctorStamp={treatmentDoctor}
                  setValueFormReferralToInit={setValueFormReferralToInit}
                  onCreateSchein={onCreateSchein}
                />
              </div>
            </div>
            <Flex column className={FormPrintPreviewClasses.contentRightSide}>
              <Flex column className="sl-content-right-side-content">
                {!![
                  showHint && 'hint',
                  !!store.questionName && 'himiQuestionName',
                ].filter((value) => value)[0] && (
                    <FormInfo
                      labels={
                        [
                          showHint && 'hint',
                          !!store.questionName && 'himiQuestionName',
                        ].filter((value) => value) as InfoLabel[]
                      }
                    />
                  )}
                {/* Show warning if current patient has not been read card yet */}
                {!!formWarningList[0] && (
                  <>
                    <FormWarning labels={formWarningList} />
                    <Flex mb={16} />
                  </>
                )}
                {hasDummyVknr ? <FormError labels={['dummyVknr']} /> : null}
                {renderWarningValidation}
                {renderForm1844WarningValidation}
                {!!hints[0] && <div className="hint">{hints}</div>}
                {!isEmpty(patient?.patientInfo?.otherInfo?.cave) && (
                  <Cave patient={patient} />
                )}
                {!!patient?.patientMedicalData?.allergies?.length && (
                  <Divider className="sl-divider" />
                )}
                {!isEAU && (
                  <>
                    <H3>{t('formDetails')}</H3>
                    {selectedContractDoctor && (
                      <Flex w="100%" mb={8}>
                        <Flex column w="100%">
                          <Label className="sl-label-right-side">
                            {t('Doctor')}
                          </Label>
                          <HimiDoctorSelector
                            contractDoctor={selectedContractDoctor}
                            currentTreatmentDoctor={store?.doctor}
                            onSelectedDoctor={(d) => {
                              const selectedDoctor =
                                selectedContractDoctor?.availableDoctor?.find?.(
                                  (doctor) => doctor.id === d.value
                                );

                              musterFormDialogActions.setCurrentMusterFormSetting(
                                {
                                  textbox_ortGesamtsumme:
                                    selectedDoctor?.bsnrCity,
                                }
                              );
                              musterFormDialogActions.setCurrentTreatmentDoctor(
                                cloneDeep(d)
                              );
                            }}
                            isDisable={store.isViewForm}
                          />
                        </Flex>
                      </Flex>
                    )}
                  </>
                )}

                {children}
                {languages && (
                  <Flex w="100%" mb={8}>
                    <Flex column w="100%">
                      <Label className="sl-label-right-side">
                        {t('Language')}
                      </Label>
                      <Select
                        items={languages}
                        filterable={false}
                        popoverProps={{
                          usePortal: false,
                          minimal: true,
                        }}
                        itemRenderer={(item: IMenuItem, { handleClick }) => {
                          return (
                            <MenuItem
                              key={item.value}
                              text={item.label}
                              shouldDismissPopover
                              onClick={handleClick}
                            />
                          );
                        }}
                        disabled={store.isViewForm}
                        onItemSelect={(item: IMenuItem) => {
                          onChangeLanguage?.(item?.value as string);
                        }}
                      >
                        <Button
                          text={
                            languages?.find(
                              (d) => d.value === store?.currentFormName
                            )?.label
                          }
                          className={`sl-select-search ${Classes.FILL}`}
                          rightIcon="caret-down"
                          disabled={store.isViewForm}
                        />
                      </Select>
                    </Flex>
                  </Flex>
                )}
                {cbNavigateToLabOverviewPage}

                {renderFormConsideration()}

                <br />

                {isHimiForms && (
                  <>
                    <Divider className="sl-divider" />
                    <H3>{t('Recipe')}</H3>

                    {!store.isControllable && (
                      <Flex mb={16} gap={8}>
                        <Flex w={80} align="center">
                          <TagMedicine
                            className={getCssClass({
                              'sl-k-rez':
                                store.currentFormName !== FormName.Private,
                              'sl-private':
                                store.currentFormName === FormName.Private,
                            })}
                          >
                            <BodyTextL
                              color={
                                store.currentFormName === FormName.Private
                                  ? COLOR.INFO_PRESSED
                                  : COLOR.TEXT_NEGATIVE
                              }
                              fontWeight={
                                FONT_WEIGHT.TYPO_FONT_WEIGHT_EMPHASIS as TTypographyFontWeight
                              }
                              fontSize={FONT_SIZE.TYPO_FONT_SIZE_FORM_LABEL}
                              lineHeight={SPACE.SPACE_S}
                            >
                              {store.currentFormName === FormName.Private
                                ? tMedicineDetail('Blaues_Rezept')
                                : tMedicineDetail('Muster_16')}
                            </BodyTextL>
                          </TagMedicine>
                        </Flex>
                        <Flex align="center">x 1</Flex>
                      </Flex>
                    )}
                  </>
                )}

                {store?.questionName && (
                  <div className="row">
                    <div className="col-md">{renderQuestionFormInfo()}</div>
                  </div>
                )}

                {isHimiForms && !store.isControllable && (
                  <Flex column gap={8} mb={16}>
                    {renderGBAGuideline()}
                    {renderInstructionDocument()}
                  </Flex>
                )}
                {hasSupportForm1237 &&
                  [FormName.AOK_BW_Beratungsbogen_Einbindung_SD_V7].includes(
                    store.currentFormName as FormName
                  ) && (
                    <Flex column gap={8} mb={16}>
                      {renderAdressliste()}
                    </Flex>
                  )}
                {!!formSetting && !isEAU && (
                  <PrinterInformation
                    formId={formSetting.formId}
                    contractId={
                      selectedContractDoctor
                        ? selectedContractDoctor.contractId
                        : ''
                    }
                    ikNumber={ikNumber}
                    okv={okv}
                    isFormPrint={isFormPrint}
                  />
                )}
                {isEAU && (
                  <EAUSettings
                    updatedPatient={updatedPatient}
                    selectedContractDoctor={selectedContractDoctor}
                    hasDummyVknr={hasDummyVknr}
                    onSuccess={onSuccess}
                    onDoneForm1844Validation={setAdditionalWarningArray}
                    onChangeEmploymentInfo={onChangePatientEmployment}
                    className="sl-eau-settings-container"
                  />
                )}
              </Flex>
              {(!isEAU || store.EAUDisable) && (
                <div
                  className={
                    referralThroughTssStore.isSendGetTssCode && isFormM06
                      ? 'sl-action-wrapper-m6'
                      : isFormPTV11
                        ? 'sl-actions-wrapper-ptv11'
                        : 'sl-actions-wrapper'
                  }
                >
                  <Flex className="row sl-btn-actions" gap={10}>
                    <div>
                      {renderSaveButton()}
                      <div className="col-md" />
                    </div>
                    <div>{renderPrintButton}</div>
                  </Flex>
                </div>
              )}
            </Flex>
          </Flex>
        </>
      </FormPrintPreviewLayout>
      {store.isOpenAddDiagnosisDialog && (
        <AddDiagnosisDialog
          isOpen={store.isOpenAddDiagnosisDialog}
          patient={patient}
          currentSchein={store.currentSchein}
          actionBarStore={actionBarStore}
          onSave={(values: IDiagnosisFormValues) => {
            let diagnoseText = `(${values?.diagnosis?.code}) ${values?.diagnosis?.description}`;
            const diagnoseLabel = `${values?.diagnosis?.code} ${values.certainty} ${values.location}`;
            switch (store?.currentFormName) {
              case FormName.Muster_1:
              case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
              case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
              case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
              case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
              case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3:
              case FormName.Muster_12A:
              case FormName.Muster_61:
              case FormName.Muster_PTV_11A:
                diagnoseText = `${values?.diagnosis?.code} ${values?.certainty
                  } ${values?.location || ''}`;
                break;
              case FormName.Muster_8:
              case FormName.Muster_8A:
                musterFormDialogActions.setDiagnosis(diagnoseText);
                break;
              case FormName.Muster_15:
                musterFormDialogActions.setDiagnosis(diagnoseText);
                break;
              case FormName.Muster_16:
                if (store.isSecondaryDiagnosis) {
                  musterFormDialogActions.setSecondaryDiagnosis(diagnoseText);
                } else {
                  musterFormDialogActions.setDiagnosis(diagnoseText);
                }
                break;
              case FormName.Muster_13: {
                const diagnoseFieldName = store.diagnoseFieldName || '';

                musterFormDialogActions.setCurrentMusterFormSetting({
                  [diagnoseFieldName]: diagnoseLabel,
                  [FIELD_NAME_CORRESPONDING[diagnoseFieldName]]:
                    values.diagnosis.description,
                });
                const payload: any = {};
                if (store.diagnoseFieldName == 'label_icd_code10_line1_0') {
                  payload.documentDiagnoseValue = values.diagnosis.code;
                  payload.certainty = values.certainty;
                  payload.laterality = values.location;
                }
                if (store.diagnoseFieldName == 'label_icd_code10_line2_0') {
                  payload.secondaryDiagnose = values.diagnosis.code;
                  payload.certainty = values.certainty;
                  payload.laterality = values.location;
                }
                heimiSelectionActions.setForceDocumentDiagnose(payload);
                break;
              }
              default:
                break;
            }
            if (store.currentFormName !== FormName.Muster_13) {
              musterFormDialogActions.setCurrentMusterFormSetting({
                ...store.currentFormSetting,
                [store.diagnoseFieldName || '']: diagnoseText,
              });
              musterFormDialogActions.validateDiagnosis();
            }
          }}
          onClose={() => {
            referralThroughTssActions.sendTssCode(false);
            musterFormDialogActions.setIsOpenAddDiagnosisDialog(false);
          }}
          isAlwaysAddToTimeLine={store.currentFormName === FormName.Muster_13}
          onSubmitCallback={musterFormDialogActions.getListDiagnosis}
        />
      )}
      {/*Confirm dialog*/}
      {renderLeaveConfirmDialog()}
      {referralThroughTssStore?.isOpenReferralDialog && (
        <ReferralThroughTSSDialog
          isOpen={referralThroughTssStore?.isOpenReferralDialog}
          sendTss={() => referralThroughTssActions.sendTssCode(true)}
          onClose={() => {
            referralThroughTssActions.sendTssCode(false);
            referralThroughTssActions.setIsOpenReferralDialog(false);
          }}
          onReferralSubmit={() => {
            referralThroughTssActions.setIsOpenReferralDialog(false);
          }}
          handlerError={handlerError}
          formName={store?.currentFormName || ''}
          valueSpecialGroup={valueSpecialGroup}
          setValueSpecialGroup={setValueSpecialGroup}
          valueUrgency={valueUrgency}
          setValueUrgency={setValueUrgency}
          valueBsnr={valueBsnr}
          setValueBsnr={setValueBsnr}
          valueLanr={valueLanr}
          setValueLanr={setValueLanr}
          valueAdditional={valueAdditional}
          setValueAdditional={setValueAdditional}
          setValueProcedure={setValueProcedure}
          valueProcedure={valueProcedure}
          setValueService={setValueService}
          valueService={valueService}
        />
      )}
    </>
  );
};

export default memo(
  I18n.withTranslation(MusterFormDialog, {
    namespace: 'Form',
    nestedTrans: 'Prescrible',
  })
);
