import { IMusterPrescribe } from '../musterFormDialog.store';
import { isOptionalCheckBoxValid } from './helper.validation';

const checkboxPTV11A = [
  'checkbox_keine',
  'checkbox_praventionsma',
  'checkbox_ambulante',
  'checkbox_hausarztliche',
  'checkbox_facharztliche',
  'checkbox_ambulanteAkutbehandlung',
  'checkbox_stationare',
  'checkbox_andere',
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;
  const checkboxPTV11AValues = checkboxPTV11A.map(
    (item) => !!currentFormSetting?.[item]
  );
  const validationCheckbox = checkboxPTV11A.reduce((validations, fieldName) => {
    return [
      ...validations,
      {
        fieldName,
        fieldIndex: 4,
        isValid: (): boolean => {
          return isOptionalCheckBoxValid(checkboxPTV11AValues);
        },
      },
    ];
  }, []);

  return [
    {
      fieldName: 'date_label_custom_date',
      fieldIndex: 1,
      isValid: (): boolean => {
        return (
          !currentFormSetting?.date_label_custom_date2 ||
          !!(+currentFormSetting?.date_label_custom_date &&
            +currentFormSetting?.date_label_custom_date <=
            +currentFormSetting?.date_label_custom_date2)
        );
      },
    },
    {
      fieldName: 'date_label_custom_date2',
      fieldIndex: 1,
      isValid: (): boolean => {
        return (
          !currentFormSetting?.date_label_custom_date ||
          !!(currentFormSetting?.date_label_custom_date2 &&
            +currentFormSetting?.date_label_custom_date <=
            +currentFormSetting?.date_label_custom_date2)
        );
      },
    },
    {
      fieldName: 'checkbox_beiIhnenWurdenKeine',
      fieldIndex: 2,
      isValid: (): boolean => {
        return (
          !!currentFormSetting?.checkbox_beiIhnenWurdenKeine ||
          !!currentFormSetting?.checkbox_beiIhnenWurdenFolgende
        );
      },
    },
    {
      fieldName: 'checkbox_beiIhnenWurdenFolgende',
      fieldIndex: 2,
      isValid: (): boolean => {
        return (
          !!currentFormSetting?.checkbox_beiIhnenWurdenKeine ||
          !!currentFormSetting?.checkbox_beiIhnenWurdenFolgende
        );
      },
    },
    ...validationCheckbox,
    {
      fieldName: 'textbox_fachgebiet',
      fieldIndex: 5,
      isValid: (): boolean => {
        return (
          !currentFormSetting?.checkbox_facharztliche ||
          !!currentFormSetting?.textbox_fachgebiet
        );
      },
    },
    {
      fieldName: 'checkbox_diePsychotherapeutische',
      fieldIndex: 6,
      isValid: (): boolean => {
        return (
          !currentFormSetting?.checkbox_ambulante ||
          !!currentFormSetting?.checkbox_diePsychotherapeutische ||
          !!currentFormSetting?.checkbox_diePsychotherapeutischeNight
        );
      },
    },
    {
      fieldName: 'checkbox_diePsychotherapeutischeNight',
      fieldIndex: 6,
      isValid: (): boolean => {
        if (currentFormSetting?.checkbox_ambulanteAkutbehandlung) {
          return !!currentFormSetting?.checkbox_diePsychotherapeutischeNight;
        }

        return (
          !currentFormSetting?.checkbox_ambulante ||
          !!currentFormSetting?.checkbox_diePsychotherapeutische ||
          !!currentFormSetting?.checkbox_diePsychotherapeutischeNight
        );
      },
    },
    {
      fieldName: 'checkbox_weitervermittlung',
      fieldIndex: 6,
      isValid: (): boolean => {
        if (currentFormSetting?.checkbox_ambulanteAkutbehandlung) {
          return !!currentFormSetting?.checkbox_weitervermittlung;
        }

        return (
          !currentFormSetting?.checkbox_diePsychotherapeutischeNight ||
          !!currentFormSetting?.checkbox_weitervermittlung
        );
      },
    },
    {
      fieldName: 'checkbox_zeitnahErforderlich',
      fieldIndex: 6,
      isValid: (): boolean => {
        return (
          !currentFormSetting?.checkbox_ambulanteAkutbehandlung ||
          !!currentFormSetting?.checkbox_zeitnahErforderlich
        );
      },
    },
    {
      fieldName: 'date_label_custom_ausstellungsdatum',
      fieldIndex: 7,
      isValid: (): boolean => {
        return !!currentFormSetting?.date_label_custom_ausstellungsdatum;
      },
    },
    {
      fieldName: 'textbox_text_line3',
      fieldIndex: 8,
      isValid: (): boolean => {
        return true;
      },
      isWarning: () => {
        return !!(
          currentFormSetting?.textbox_text_line3_temp as string
        )?.trim();
      },
    },
  ];
};
