import { IMusterPrescribe } from '../musterFormDialog.store';
import { getRelatedList, IFieldValidation } from './helper.validation';

export const FIELD_RULE_1 = getRelatedList('', 6, '');

export const NUMBERIC_FIELDS_FORM_61A = getRelatedList('textbox_ursache', 6);

const getFieldByOtherLine = (field: string, data: string[], page = '0') => {
  const textPage = page ? `_${page}` : '';

  return data.reduce<string[]>((otherFields, fieldLine) => {
    if (fieldLine !== field) {
      otherFields.push(
        ...[
          `textbox_diagnose${fieldLine}${textPage}`,
          `textbox_icd10_code${fieldLine}${textPage}`,
        ]
      );
    }

    return otherFields;
  }, []);
};

export const VALIATED_GROUP_FIELDS_RULE_1 = FIELD_RULE_1.reduce(
  (fields, field) => {
    const rowData = [
      {
        fieldName: `textbox_diagnose${field}_0`,
        fieldIndex: 1,
        required: true,
        includeFields: [`textbox_icd10_code${field}_0`],
        otherFieldLines: getFieldByOtherLine(field, FIELD_RULE_1),
      },
      {
        fieldName: `textbox_icd10_code${field}_0`,
        fieldIndex: 1,
        required: true,
        includeFields: [`textbox_diagnose${field}_0`],
        otherFieldLines: getFieldByOtherLine(field, FIELD_RULE_1),
      },
      {
        fieldName: `textbox_ursache${field}_0`,
        fieldIndex: 2,
        required: false,
        minValue: 1,
        maxValue: 5,
      },
    ];

    return [...fields, ...rowData];
  },
  []
);

export const NUMBERIC_FIELDS_FORM_61B = getRelatedList(
  'textbox_ursache',
  6,
  '1'
);

export const SMALL_TEXTBOX_FIELDS = [
  'textbox_mobilitatTug_1',
  'textbox_mobilitatChairRise_1',
  'textbox_kognitionMmst_1',
  'textbox_schmerz_1',
  'textbox_herzErgometrie_1',
  'textbox_mobilitatHand_1',
  'textbox_mobilitatOder_1',
  'textbox_kognitionGds15_1',
  'textbox_herzFev1_1',
  'textbox_herzVk_1',
  'textbox_mobilitatDemmi_1',
  'textbox_mobilitatTinetti_1',
  'textbox_kognitionUhren_1',
  'textbox_herzNyha_1',
];

export const VALIATED_GROUP_FIELDS_RULE_1_PAGE_2 = FIELD_RULE_1.reduce(
  (fields, field) => {
    const rowData = [
      {
        fieldName: `textbox_diagnose${field}_1`,
        fieldIndex: 1,
        required: true,
        includeFields: [`textbox_icd10_code${field}_1`],
        otherFieldLines: getFieldByOtherLine(field, FIELD_RULE_1, '1'),
      },
      {
        fieldName: `textbox_icd10_code${field}_1`,
        fieldIndex: 1,
        required: true,
        includeFields: [`textbox_diagnose${field}_1`],
        otherFieldLines: getFieldByOtherLine(field, FIELD_RULE_1, '1'),
      },
      {
        fieldName: `textbox_ursache${field}_1`,
        fieldIndex: 2,
        required: false,
        minValue: 1,
        maxValue: 5,
      },
    ];

    return [...fields, ...rowData];
  },
  []
);

const VALIDATED_FIELDS: IFieldValidation[] = [
  ...VALIATED_GROUP_FIELDS_RULE_1,
  ...VALIATED_GROUP_FIELDS_RULE_1_PAGE_2,
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      const value = currentFormSetting?.[field.fieldName]!;
      const hasValue = !field.required || !!value;
      const isMinValue =
        value === '' || !field.minValue || +value >= +field.minValue;
      const isMaxValue =
        value === '' || !field.maxValue || +value <= +field.maxValue;
      const isValid =
        (hasValue && isMinValue && isMaxValue) ||
        !!field?.relateValues?.some(
          (fieldName) => !!currentFormSetting?.[fieldName]
        ) ||
        !!(field.includeFields?.every(
          (fieldName) => !currentFormSetting?.[fieldName]
        ) &&
          !!field.otherFieldLines?.some(
            (fieldName) => !!currentFormSetting?.[fieldName]
          ));
      return isValid;
    },
  }));
};
