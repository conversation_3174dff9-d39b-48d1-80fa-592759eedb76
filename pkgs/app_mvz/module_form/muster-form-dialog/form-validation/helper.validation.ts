import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { IMusterPrescribe } from '../musterFormDialog.store';

export interface IGroup {
  groups: string[];
  fieldIndex: number;
  hasCheckReservedICDCode?: boolean;
  hasCheckNonExitICDCode?: boolean;
  isValid: () => boolean;
}

export interface IErrorItem {
  fieldName: string;
  fieldIndex: number;
  isValid: () => boolean;
}

export interface IFieldValidation {
  fieldName: string;
  fieldIndex: number;
  relateValues?: string[];
  includeFields?: string[];
  otherFieldLines?: string[];
  minValue?: string | number;
  maxValue?: string | number;
  required?: boolean;
}

export const isOptionalCheckBoxValid = (field: boolean[]) => {
  return field?.some((value) => value);
};

// skip validate when schein is not 27 or 28
// https://www.notion.so/silenteer/Validate-KBV-rules-on-form-10-10A-a6057e22340e45e494af660348e944eb
export const skipValidateForm10And10A = (
  store: IMusterPrescribe,
  scheinData: ScheinItem
) => {
  const { currentFormSetting = {} } = store;
  if (
    scheinData?.kvScheinSubGroup !== '27' &&
    scheinData?.kvScheinSubGroup !== '28'
  ) {
    return true;
  } else if (scheinData?.kvScheinSubGroup === '28') {
    return currentFormSetting.name === 'checkbox_empfangnisregelung';
  }
  return false;
};

export const handleMapFieldValue = (
  nameFields: string[],
  currentFormSetting
) => {
  return nameFields.map((name) => currentFormSetting?.[name]);
};

export const handleListErrorParser = (listGroups: IGroup[]): IErrorItem[] => {
  return (listGroups || []).reduce((acc: IErrorItem[], item) => {
    item?.groups?.forEach((fieldName) => {
      acc.push({
        fieldName,
        fieldIndex: item?.fieldIndex,
        isValid: item?.isValid,
      });
    });
    return acc;
  }, []);
};

export const getRelatedList = (fieldName: string, length = 6, page = '0') => {
  const textPage = page ? `_${page}` : '';

  return Array.from({ length }).map(
    (_, index) => `${fieldName}${index + 1}${textPage}`
  );
};

export const isGroupInputsAllFilled = (
  fieldNames: string[],
  formObj: Record<string, string | number | boolean>
): boolean => {
  return fieldNames.every((f) => Boolean(formObj[f]));
};
