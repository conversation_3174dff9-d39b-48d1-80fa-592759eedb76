import { IMusterPrescribe } from '../musterFormDialog.store';

const VALIDATED_FIELDS = [
  {
    fieldName: 'date_datumStay',
    fieldIndex: 1,
  },
  {
    fieldName: 'checkbox_confirmation',
    fieldIndex: 2,
  },
  {
    fieldName: 'textbox_insuranceName',
    fieldIndex: 3,
  },
  {
    fieldName: 'textbox_treatingDoctor',
    fieldIndex: 4,
  },
  {
    fieldName: 'textbox_patientName',
    fieldIndex: 5,
  },
  {
    fieldName: 'textbox_strabe',
    fieldIndex: 6,
  },
  {
    fieldName: 'textbox_plz',
    fieldIndex: 7,
  },
  {
    fieldName: 'textbox_land',
    fieldIndex: 8,
  },
  {
    fieldName: 'checkbox_temporaryAddress1',
    fieldIndex: 9,
    relateValues: ['checkbox_temporaryAddress2'],
  },
  {
    fieldName: 'checkbox_temporaryAddress2',
    fieldIndex: 9,
    relateValues: ['checkbox_temporaryAddress1'],
  },
  {
    fieldName: 'checkbox_identity1',
    fieldIndex: 10,
    relateValues: ['checkbox_identity2'],
  },
  {
    fieldName: 'checkbox_identity2',
    fieldIndex: 10,
    relateValues: ['checkbox_identity1'],
  },
  {
    fieldName: 'textbox_idNumber',
    fieldIndex: 11,
  },
  {
    fieldName: 'date_datumCurrent',
    fieldIndex: 12,
  },
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      return (
        !!currentFormSetting?.[field.fieldName] ||
        !!field.relateValues?.some(
          (fieldName) => !!currentFormSetting?.[fieldName]
        )
      );
    },
  }));
};
