import { IMusterPrescribe } from '../musterFormDialog.store';
import { IFieldValidation } from './helper.validation';

const FIELD_RULE_2 = Array.from({ length: 4 }).map(
  (_, index) => `${index + 1}`
);

const getFieldByOtherLine = (field: string) => {
  return FIELD_RULE_2.reduce<string[]>((otherFields, fieldLine) => {
    if (fieldLine !== field) {
      otherFields.push(
        ...[
          `date_label_custom_vom${fieldLine}`,
          `date_label_custom_bis${fieldLine}`,
          `textbox_tatigkeit_line${fieldLine}`,
          `textbox_taglich_line${fieldLine}`,
        ]
      );
    }

    return otherFields;
  }, []);
};

export const VALIATED_GROUP_FIELDS_RULE_2 = FIELD_RULE_2.reduce(
  (fields, field) => {
    const rowData = [
      {
        fieldName: `date_label_custom_vom${field}`,
        fieldIndex: 2,
        includeFields: [
          `date_label_custom_bis${field}`,
          `textbox_tatigkeit_line${field}`,
          `textbox_taglich_line${field}`,
        ],
        otherFieldLines: getFieldByOtherLine(field),
        maxValue: `date_label_custom_bis${field}`,
      },
      {
        fieldName: `date_label_custom_bis${field}`,
        fieldIndex: 2,
        includeFields: [
          `date_label_custom_vom${field}`,
          `textbox_tatigkeit_line${field}`,
          `textbox_taglich_line${field}`,
        ],
        otherFieldLines: getFieldByOtherLine(field),
        minValue: `date_label_custom_vom${field}`,
      },
      {
        fieldName: `textbox_tatigkeit_line${field}`,
        fieldIndex: 2,
        includeFields: [
          `date_label_custom_vom${field}`,
          `date_label_custom_bis${field}`,
          `textbox_taglich_line${field}`,
        ],
        otherFieldLines: getFieldByOtherLine(field),
      },
      {
        fieldName: `textbox_taglich_line${field}`,
        fieldIndex: 2,
        includeFields: [
          `date_label_custom_vom${field}`,
          `date_label_custom_bis${field}`,
          `textbox_tatigkeit_line${field}`,
        ],
        otherFieldLines: getFieldByOtherLine(field),
      },
    ];

    return [...fields, ...rowData];
  },
  []
);

const VALIDATED_FIELDS: IFieldValidation[] = [
  {
    fieldName: 'textbox_zuletzt_line1',
    fieldIndex: 1,
    relateValues: ['textbox_zuletzt_line2', 'textbox_zuletzt_line3'],
  },
  {
    fieldName: 'textbox_zuletzt_line2',
    fieldIndex: 1,
    relateValues: ['textbox_zuletzt_line1', 'textbox_zuletzt_line3'],
  },
  {
    fieldName: 'textbox_zuletzt_line3',
    fieldIndex: 1,
    relateValues: ['textbox_zuletzt_line1', 'textbox_zuletzt_line2'],
  },
  {
    fieldName: 'textbox_zuletzt_stunden',
    fieldIndex: 1,
  },
  {
    fieldName: 'date_label_custom_datum',
    fieldIndex: 3,
  },
  ...VALIATED_GROUP_FIELDS_RULE_2,
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      const value = currentFormSetting?.[field.fieldName];
      const minValue = currentFormSetting?.[field.minValue!];
      const maxValue = currentFormSetting?.[field.maxValue!];
      const isValid =
        (!!value &&
          (!minValue || value >= minValue) &&
          (!maxValue || value <= maxValue)) ||
        !!field.relateValues?.some(
          (fieldName) => !!currentFormSetting?.[fieldName]
        ) ||
        !!(field.includeFields?.every(
          (fieldName) => !currentFormSetting?.[fieldName]
        ) &&
          !!field.otherFieldLines?.some(
            (fieldName) => !!currentFormSetting?.[fieldName]
          ));

      return isValid;
    },
  }));
};
