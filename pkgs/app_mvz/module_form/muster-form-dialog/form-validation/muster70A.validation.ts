import { IMusterPrescribe } from '../musterFormDialog.store';

export const NUMBERIC_FIELDS_FORM_70 = [
  ...Array.from({ length: 6 }).map(
    (_, index) => `textbox_summeArztliche1_line${index + 1}`
  ),
  ...Array.from({ length: 8 }).map(
    (_, index) => `textbox_summeArztliche2_line${index + 1}`
  ),
];

const VALIDATED_FIELDS = [
  {
    fieldName: 'textbox_indikation_line1',
    fieldIndex: 1,
    relateValues: ['textbox_indikation_line2'],
  },
  {
    fieldName: 'textbox_indikation_line2',
    fieldIndex: 1,
    relateValues: ['textbox_indikation_line1'],
  },
  {
    fieldName: 'checkbox_inseminationIm',
    fieldIndex: 2,
    relateValues: [
      'checkbox_inseminationNach',
      'checkbox_inVitroFertilisation',
      'checkbox_intratubarerGametenTransfer',
      'checkbox_intracytoplasmatische',
    ],
  },
  {
    fieldName: 'checkbox_inseminationNach',
    fieldIndex: 2,
    relateValues: [
      'checkbox_inseminationIm',
      'checkbox_inVitroFertilisation',
      'checkbox_intratubarerGametenTransfer',
      'checkbox_intracytoplasmatische',
    ],
  },
  {
    fieldName: 'checkbox_inVitroFertilisation',
    fieldIndex: 2,
    relateValues: [
      'checkbox_inseminationIm',
      'checkbox_inseminationNach',
      'checkbox_intratubarerGametenTransfer',
      'checkbox_intracytoplasmatische',
    ],
  },
  {
    fieldName: 'checkbox_intratubarerGametenTransfer',
    fieldIndex: 2,
    relateValues: [
      'checkbox_inseminationIm',
      'checkbox_inseminationNach',
      'checkbox_inVitroFertilisation',
      'checkbox_intracytoplasmatische',
    ],
  },
  {
    fieldName: 'checkbox_intracytoplasmatische',
    fieldIndex: 2,
    relateValues: [
      'checkbox_inseminationIm',
      'checkbox_inseminationNach',
      'checkbox_inVitroFertilisation',
      'checkbox_intratubarerGametenTransfer',
    ],
  },
  {
    fieldName: 'textbox_anzahl',
    fieldIndex: 2,
  },
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      return (
        !!currentFormSetting?.[field.fieldName] ||
        !!field.relateValues?.some(
          (fieldName) => !!currentFormSetting?.[fieldName]
        )
      );
    },
  }));
};
