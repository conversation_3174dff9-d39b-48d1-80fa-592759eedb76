import { IMusterPrescribe } from '../musterFormDialog.store';
import { IFieldValidation } from './helper.validation';

const VALIDATED_FIELDS: IFieldValidation[] = [
  {
    fieldName: 'date_label_custom_vom',
    fieldIndex: 3,
    maxValue: 'date_label_custom_bis',
  },
  {
    fieldName: 'date_label_custom_bis',
    fieldIndex: 4,
    minValue: 'date_label_custom_vom',
  },
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      const value = currentFormSetting?.[field.fieldName];
      const minValue = currentFormSetting?.[field.minValue!];
      const maxValue = currentFormSetting?.[field.maxValue!];

      return (
        !!value &&
        (!minValue || value >= minValue) &&
        (!maxValue || value <= maxValue)
      );
    },
  }));
};
