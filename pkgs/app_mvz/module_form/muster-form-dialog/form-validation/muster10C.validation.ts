import { IMusterPrescribe } from '../musterFormDialog.store';

const VALIDATED_FIELDS = [
  {
    fieldName: 'date_quartal_0',
    fieldIndex: 1,
  },
  {
    fieldName: 'checkbox_ersttestung_0',
    fieldIndex: 3,
    relateValues: ['checkbox_weitereTestung_0'],
  },
  {
    fieldName: 'checkbox_weitereTestung_0',
    fieldIndex: 3,
    relateValues: ['checkbox_ersttestung_0'],
  },
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      return (
        !!currentFormSetting?.[field.fieldName] ||
        !!field.relateValues?.some(
          (fieldName) => !!currentFormSetting?.[fieldName]
        )
      );
    },
  }));
};
