import { isOptionalCheckBoxValid } from './helper.validation';

export default (store) => {
  const currentFormSetting = store.currentFormSetting;
  return [
    {
      fieldName: 'checkbox_20_29_jahre',
      fieldIndex: 1,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_20_29_jahre,
          currentFormSetting?.checkbox_30_34_jahre,
          currentFormSetting?.checkbox_ab_35_jahre,
        ]);
      },
    },
    {
      fieldName: 'checkbox_30_34_jahre',
      fieldIndex: 1,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_20_29_jahre,
          currentFormSetting?.checkbox_30_34_jahre,
          currentFormSetting?.checkbox_ab_35_jahre,
        ]);
      },
    },
    {
      fieldName: 'checkbox_ab_35_jahre',
      fieldIndex: 1,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_20_29_jahre,
          currentFormSetting?.checkbox_30_34_jahre,
          currentFormSetting?.checkbox_ab_35_jahre,
        ]);
      },
    },
    {
      fieldName: 'checkbox_primar_screening',
      fieldIndex: 2,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_primar_screening,
          currentFormSetting?.checkbox_abklarungs_diagnostik,
        ]);
      },
    },
    {
      fieldName: 'checkbox_abklarungs_diagnostik',
      fieldIndex: 2,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_primar_screening,
          currentFormSetting?.checkbox_abklarungs_diagnostik,
        ]);
      },
    },
    {
      fieldName: 'checkbox_zytologie',
      fieldIndex: 3,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_zytologie,
          currentFormSetting?.checkbox_hpv_test,
          currentFormSetting?.checkbox_ko_testung,
        ]);
      },
    },
    {
      fieldName: 'checkbox_hpv_test',
      fieldIndex: 3,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_zytologie,
          currentFormSetting?.checkbox_hpv_test,
          currentFormSetting?.checkbox_ko_testung,
        ]);
      },
    },
    {
      fieldName: 'checkbox_ko_testung',
      fieldIndex: 3,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_zytologie,
          currentFormSetting?.checkbox_hpv_test,
          currentFormSetting?.checkbox_ko_testung,
        ]);
      },
    },
    {
      fieldName: 'checkbox_nein',
      fieldIndex: 4,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_nein,
          currentFormSetting?.checkbox_ja,
        ]);
      },
    },
    {
      fieldName: 'checkbox_ja',
      fieldIndex: 4,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_nein,
          currentFormSetting?.checkbox_ja,
        ]);
      },
    },
    {
      fieldName: 'date_anamnese',
      fieldIndex: 5,
      isValid: (): boolean => {
        return !currentFormSetting?.checkbox_ja || !!currentFormSetting?.date_anamnese;
      },
    },
    {
      fieldName: 'checkbox_hpv_hr_liegt_nicht',
      fieldIndex: 6,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_hpv_hr_liegt_nicht,
          currentFormSetting?.checkbox_hpv_hr_liegt_vor,
        ]);
      },
    },
    {
      fieldName: 'checkbox_hpv_hr_liegt_vor',
      fieldIndex: 6,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_hpv_hr_liegt_nicht,
          currentFormSetting?.checkbox_hpv_hr_liegt_vor,
        ]);
      },
    },
    {
      fieldName: 'checkbox_unauffallig',
      fieldIndex: 7,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_unauffallig,
          currentFormSetting?.checkbox_auffallig,
        ]);
      },
    },
    {
      fieldName: 'checkbox_auffallig',
      fieldIndex: 7,
      isValid: (): boolean => {
        return isOptionalCheckBoxValid([
          currentFormSetting?.checkbox_unauffallig,
          currentFormSetting?.checkbox_auffallig,
        ]);
      },
    },
    {
      fieldName: 'area_text_box_erlauterungen',
      fieldIndex: 8,
      isValid: (): boolean => {
        return currentFormSetting?.area_text_box_erlauterungen
          ? currentFormSetting?.area_text_box_erlauterungen?.length <= 330
          : true;
      },
    },
  ];
};
