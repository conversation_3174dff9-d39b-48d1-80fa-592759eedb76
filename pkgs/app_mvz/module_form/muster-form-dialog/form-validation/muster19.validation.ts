import { IMusterPrescribe } from '../musterFormDialog.store';

const VALIDATED_FIELDS = [
  {
    fieldName: 'checkbox_behandlung',
    fieldIndex: 1,
    relateValues: ['checkbox_belegarztl', 'checkbox_unfall'],
  },
  {
    fieldName: 'checkbox_belegarztl',
    fieldIndex: 1,
    relateValues: ['checkbox_behandlung', 'checkbox_unfall'],
  },
  {
    fieldName: 'checkbox_unfall',
    fieldIndex: 1,
    relateValues: ['checkbox_behandlung', 'checkbox_belegarztl'],
  },
  {
    fieldName: 'date_quartal',
    fieldIndex: 2,
  },
  {
    fieldName: 'textbox_diagnose_line1',
    fieldIndex: 3,
    relateValues: ['textbox_diagnose_line2', 'textbox_diagnose_line3'],
  },
  {
    fieldName: 'textbox_diagnose_line2',
    fieldIndex: 3,
    relateValues: ['textbox_diagnose_line1', 'textbox_diagnose_line3'],
  },
  {
    fieldName: 'textbox_diagnose_line3',
    fieldIndex: 3,
    relateValues: ['textbox_diagnose_line1', 'textbox_diagnose_line2'],
  },
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      return (
        !!currentFormSetting?.[field.fieldName] ||
        !!field.relateValues?.some(
          (fieldName) => !!currentFormSetting?.[fieldName]
        )
      );
    },
  }));
};
