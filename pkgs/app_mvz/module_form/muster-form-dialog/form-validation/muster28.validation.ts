import { IMusterPrescribe } from '../musterFormDialog.store';

export const GROUP_FIELDS_RULE_6 = Array.from({ length: 5 }).map(
  (_, index) => `${index + 1}`
);

const getFieldByOtherLine = (field: string) => {
  return GROUP_FIELDS_RULE_6.reduce<string[]>((otherFields, fieldLine) => {
    if (fieldLine !== field) {
      otherFields.push(
        ...[
          `date_label_custom_folgende_datum${fieldLine}`,
          `textbox_abrechnung_leistung${fieldLine}`,
        ]
      );
    }

    return otherFields;
  }, []);
};

export const VALIATED_GROUP_FIELDS_RULE_6 = GROUP_FIELDS_RULE_6.reduce(
  (fields, field) => {
    const rowData = [
      {
        fieldName: `date_label_custom_folgende_datum${field}`,
        fieldIndex: 5,
        includeFields: [`textbox_abrechnung_leistung${field}`],
        otherFieldLines: getFieldByOtherLine(field),
      },
      {
        fieldName: `textbox_abrechnung_leistung${field}`,
        fieldIndex: 5,
        includeFields: [`date_label_custom_folgende_datum${field}`],
        otherFieldLines: getFieldByOtherLine(field),
      },
    ];

    return [...fields, ...rowData];
  },
  []
);

const VALIDATED_FIELDS = [
  {
    fieldName: 'textbox_diagnose_line1',
    fieldIndex: 1,
  },
];

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;

  return VALIDATED_FIELDS.map((field) => ({
    ...field,
    isValid: (): boolean => {
      return !!currentFormSetting?.[field.fieldName];
    },
  }));
};
