import { ProductDetail } from '@tutum/hermes/bff/app_mvz_heimi';
import { STANDALONE_REMEDY_POSITIONS } from '@tutum/mvz/constant/remedy_position';
import { IHeimiSelectionStore } from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import { isNumber } from '../../../../tutum-design-system/infrastructure/utils/validators';
import { IMusterPrescribe } from '../musterFormDialog.store';

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;
  const { isValid, hasCheckNonExitICDCode, hasCheckReservedICDCode } =
    store.validationDiagnosisResult;
  const heimiStore = store.componentStore as IHeimiSelectionStore;
  const isWarningRule1 =
    !heimiStore?.documentDiagnose?.code ||
    !currentFormSetting?.area_textbox_icd_code10_0;
  const isWarningRule2 =
    !currentFormSetting?.checkbox_patientenindividuelle_0 &&
    !currentFormSetting?.area_textbox_leitsymtomatik_0;
  const isWarningRule3 = () => {
    const validationDatas: ProductDetail[] =
      heimiStore?.complementaryRemedies?.filter((c: ProductDetail) =>
        Boolean(STANDALONE_REMEDY_POSITIONS.includes(c.position))
      ) || [],
      flagValidation = validationDatas.find(
        (v: ProductDetail) =>
          v.name == currentFormSetting?.textbox_erganzendes_0
      );
    if (flagValidation || heimiStore?.isStandardCombination) {
      return false;
    }
    return !String(currentFormSetting?.textbox_heilmittel_line1_0)?.length;
  };
  const isWarningRule4 = () => {
    const validationDatas: ProductDetail[] =
      heimiStore?.complementaryRemedies?.filter((c: ProductDetail) =>
        Boolean(STANDALONE_REMEDY_POSITIONS.includes(c.position))
      ) || [],
      flagValidation = validationDatas.find(
        (v: ProductDetail) =>
          v.name == currentFormSetting?.textbox_erganzendes_0
      );
    if (heimiStore?.currentGroup?.isBlankForm || flagValidation) {
      return false;
    }

    if (!heimiStore?.isStandardCombination) {
      return !(
        String(currentFormSetting?.textbox_behandlung_line1_0).length <= 3 &&
        isNumber(Number(currentFormSetting?.textbox_behandlung_line1_0)) &&
        Number(currentFormSetting?.textbox_behandlung_line1_0) > 0
      );
    }

    return false;
  };
  const isWarningRule5 = () => {
    let isHeilmittelLine2Valid = false;
    if (
      !heimiStore?.isStandardCombination &&
      heimiStore?.currentRemedies[1]?.name
    ) {
      isHeilmittelLine2Valid = !String(
        currentFormSetting?.textbox_heilmittel_line2_0
      )?.length;
    }
    return isHeilmittelLine2Valid;
  };
  const isWarningRule6 = () => {
    return !heimiStore?.isStandardCombination &&
      heimiStore?.currentRemedies[1]?.name
      ? !currentFormSetting?.textbox_behandlung_line2_0
      : false;
  };
  const isWarningRule7 = () => {
    let isHeilmittelLine3Valid = false;
    if (
      !heimiStore?.isStandardCombination &&
      heimiStore?.currentRemedies[2]?.name
    ) {
      isHeilmittelLine3Valid = !String(
        currentFormSetting?.textbox_heilmittel_line3_0
      )?.length;
    }
    return isHeilmittelLine3Valid;
  };
  const isWarningRule8 = () => {
    return !heimiStore?.isStandardCombination &&
      heimiStore?.currentRemedies[2]?.name
      ? !currentFormSetting?.textbox_behandlung_line3_0
      : false;
  };
  const isWarningRule9 = () => {
    let iserganzendesValid = false;
    if (
      !heimiStore?.isStandardCombination &&
      heimiStore?.currentComplementaryRemedies?.[0]?.name
    ) {
      iserganzendesValid = !String(currentFormSetting?.textbox_erganzendes_0)
        ?.length;
    }
    return iserganzendesValid;
  };
  const isWarningRule10 = () => {
    return !heimiStore?.isStandardCombination &&
      heimiStore?.currentComplementaryRemedies?.[0]?.name
      ? !currentFormSetting?.textbox_behandlung_line4_0
      : false;
  };
  const checkValidRemedyQuantityForMassageCase = () => {
    const totalQuantityMassage = heimiStore.currentRemedies.reduce(
      (sum, item, index) => {
        if (item.isMassage) {
          sum +=
            +(store.currentFormSetting?.[
              `textbox_behandlung_line${index + 1}_0`
            ] || 0);
        }

        return sum;
      },
      0
    );

    if (!totalQuantityMassage || heimiStore.maxQuantityMassage) {
      return true;
    }

    return (
      heimiStore.totalPreviousQuantityRemediesMassage! + totalQuantityMassage <=
      heimiStore.maxQuantityMassage!
    );
  };

  return [
    {
      fieldName: 'area_textbox_icd_code10_0',
      message: 'Diagnosis sollte ausgefüllt sein.',
      isValid: (): boolean => {
        return (
          isWarningRule1 ||
          (!!heimiStore?.documentDiagnose?.code &&
            !!currentFormSetting?.area_textbox_icd_code10_0)
        );
      },
      isWarning: (): boolean => {
        return isWarningRule1;
      },
    },
    {
      fieldName: 'area_textbox_leitsymtomatik_0',
      message: 'Leitsymptomatik sollte ausgefüllt sein.',
      isValid: (): boolean => {
        if (
          isWarningRule2 ||
          !currentFormSetting?.checkbox_patientenindividuelle_0
        ) {
          return true;
        }

        return !!currentFormSetting?.area_textbox_leitsymtomatik_0;
      },
      isWarning: (): boolean => {
        return isWarningRule2;
      },
    },
    {
      fieldName: 'textbox_heilmittel_line1_0',
      message: 'Heilmittel nach Maßgabe des Kataloges sollte ausgefüllt sein.',
      isValid: (): boolean => {
        return true;
      },
      isWarning: (): boolean => {
        return isWarningRule3();
      },
    },
    {
      fieldName: 'textbox_behandlung_line1_0',
      message: 'Behandlungseinheiten sollte ausgefüllt sein.',
      isValid: (): boolean => {
        if (isWarningRule4()) {
          return true;
        }

        if (
          !heimiStore?.currentRemedies[0]?.name ||
          !+currentFormSetting?.textbox_behandlung_line1_0!
        ) {
          return true;
        }

        if (heimiStore?.isStandardCombination) {
          if (
            !heimiStore.maxQuantityStandardCombination ||
            heimiStore.isLHMApprove
          ) {
            return true;
          }

          return (
            +currentFormSetting?.textbox_behandlung_line1_0! +
            heimiStore.totalQuantityStandardCombination! <=
            heimiStore.maxQuantityStandardCombination
          );
        }

        const totalQuantity =
          +currentFormSetting?.textbox_behandlung_line1_0! +
          +currentFormSetting?.textbox_behandlung_line2_0! +
          +currentFormSetting?.textbox_behandlung_line3_0!;

        if (
          !heimiStore.maxRemedyQuantity ||
          totalQuantity <= heimiStore.maxRemedyQuantity
        ) {
          return checkValidRemedyQuantityForMassageCase();
        }

        return (
          +currentFormSetting?.textbox_behandlung_line1_0! <=
          heimiStore.maxRemedyQuantity - totalQuantity
        );
      },
      isWarning: (): boolean => {
        return isWarningRule4();
      },
    },
    {
      fieldName: 'textbox_heilmittel_line2_0',
      message: 'Heilmittelbereich sollte ausgefüllt sein.',
      isValid: (): boolean => {
        return true;
      },
      isWarning: (): boolean => {
        return isWarningRule5();
      },
    },
    {
      fieldName: 'textbox_behandlung_line2_0',
      message: 'Behandlungseinheiten sollte ausgefüllt sein.',
      isValid: (): boolean => {
        if (isWarningRule6()) {
          return true;
        }

        if (
          heimiStore?.isStandardCombination ||
          !heimiStore?.currentRemedies[1]?.name ||
          !+currentFormSetting?.textbox_behandlung_line2_0!
        ) {
          return true;
        }

        const totalQuantity =
          +currentFormSetting?.textbox_behandlung_line1_0! +
          +currentFormSetting?.textbox_behandlung_line2_0! +
          +currentFormSetting?.textbox_behandlung_line3_0!;

        if (
          !heimiStore.maxRemedyQuantity ||
          totalQuantity <= heimiStore.maxRemedyQuantity
        ) {
          return checkValidRemedyQuantityForMassageCase();
        }

        return (
          +currentFormSetting?.textbox_behandlung_line2_0! <=
          heimiStore.maxRemedyQuantity - totalQuantity
        );
      },
      isWarning: (): boolean => {
        return isWarningRule6();
      },
    },
    {
      fieldName: 'textbox_heilmittel_line3_0',
      message: 'Heilmittelbereich sollte ausgefüllt sein.',
      isValid: (): boolean => {
        return true;
      },
      isWarning: (): boolean => {
        return isWarningRule7();
      },
    },
    {
      fieldName: 'textbox_behandlung_line3_0',
      message: 'Behandlungseinheiten sollte ausgefüllt sein.',
      isValid: (): boolean => {
        if (isWarningRule8()) {
          return true;
        }

        if (
          heimiStore?.isStandardCombination ||
          !heimiStore?.currentRemedies[2]?.name ||
          !+currentFormSetting?.textbox_behandlung_line3_0!
        ) {
          return true;
        }

        const totalQuantity =
          +currentFormSetting?.textbox_behandlung_line1_0! +
          +currentFormSetting?.textbox_behandlung_line2_0! +
          +currentFormSetting?.textbox_behandlung_line3_0!;

        if (
          !heimiStore.maxRemedyQuantity ||
          totalQuantity <= heimiStore.maxRemedyQuantity
        ) {
          return checkValidRemedyQuantityForMassageCase();
        }

        return (
          +currentFormSetting?.textbox_behandlung_line3_0! <=
          heimiStore.maxRemedyQuantity - totalQuantity
        );
      },
      isWarning: (): boolean => {
        return isWarningRule8();
      },
    },
    {
      fieldName: 'textbox_erganzendes_0',
      message: 'Ergänzendes Heilmittel sollte ausgefüllt sein.',
      isValid: (): boolean => {
        return true;
      },
      isWarning: (): boolean => {
        return isWarningRule9();
      },
    },
    {
      fieldName: 'textbox_behandlung_line4_0',
      message: 'Behandlungseinheiten sollte ausgefüllt sein.',
      isValid: (): boolean => {
        if (isWarningRule10()) {
          return true;
        }

        const totalQuantity =
          +currentFormSetting?.textbox_behandlung_line1_0! +
          +currentFormSetting?.textbox_behandlung_line2_0! +
          +currentFormSetting?.textbox_behandlung_line3_0!;
        const maxComplementaryRemedyQuantity =
          totalQuantity || heimiStore?.maxRemedyQuantity;

        if (
          heimiStore?.isStandardCombination ||
          !heimiStore?.currentComplementaryRemedies?.[0]?.name ||
          !+currentFormSetting?.textbox_behandlung_line4_0! ||
          !maxComplementaryRemedyQuantity
        ) {
          return true;
        }

        return (
          +currentFormSetting?.textbox_behandlung_line4_0! <=
          maxComplementaryRemedyQuantity
        );
      },
      isWarning: (): boolean => {
        return isWarningRule10();
      },
    },
    {
      fieldName: 'label_icd_code10_line1_0',
      hasCheckReservedICDCode,
      hasCheckNonExitICDCode,
      isValid: (): boolean => {
        return isValid;
      },
    },
    {
      fieldName: 'label_icd_code10_line2_0',
      hasCheckReservedICDCode,
      hasCheckNonExitICDCode,
      isValid: (): boolean => {
        return isValid;
      },
    },
  ];
};
