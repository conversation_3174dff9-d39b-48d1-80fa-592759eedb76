const FIELD_RULE_4 = Array.from({ length: 4 }).map(
  (_, index) => `${index + 1}`
);

const getFieldByOtherLine = (field: string) => {
  return FIELD_RULE_4.reduce<string[]>((otherFields, fieldLine) => {
    if (fieldLine !== field) {
      otherFields.push(
        ...[
          `textbox_art${fieldLine}`,
          `textbox_frequenz${fieldLine}`,
          `textbox_zeitraum${fieldLine}`,
        ]
      );
    }

    return otherFields;
  }, []);
};

export default (store) => {
  const currentFormSetting = store.currentFormSetting;

  if (currentFormSetting == null) {
    return [];
  }

  const VALIATED_GROUP_FIELDS_RULE_4 = FIELD_RULE_4.reduce((fields, field) => {
    const rowData = [
      {
        fieldName: `textbox_art${field}`,
        fieldIndex: 4,
        isValid: (): boolean => {
          const includeFields = [
            `textbox_frequenz${field}`,
            `textbox_zeitraum${field}`,
          ];
          const otherFieldLines = getFieldByOtherLine(field);
          return (
            !!currentFormSetting?.[`textbox_art${field}`] ||
            (includeFields?.every(
              (fieldName) => !currentFormSetting?.[fieldName]
            ) &&
              otherFieldLines?.some(
                (fieldName) => !!currentFormSetting?.[fieldName]
              ))
          );
        },
      },
      {
        fieldName: `textbox_frequenz${field}`,
        fieldIndex: 4,
        isValid: (): boolean => {
          const includeFields = [
            `textbox_art${field}`,
            `textbox_zeitraum${field}`,
          ];
          const otherFieldLines = getFieldByOtherLine(field);
          return (
            !!currentFormSetting?.[`textbox_frequenz${field}`] ||
            (includeFields?.every(
              (fieldName) => !currentFormSetting?.[fieldName]
            ) &&
              otherFieldLines?.some(
                (fieldName) => !!currentFormSetting?.[fieldName]
              ))
          );
        },
      },
      {
        fieldName: `textbox_zeitraum${field}`,
        fieldIndex: 4,
        isValid: (): boolean => {
          const includeFields = [
            `textbox_art${field}`,
            `textbox_frequenz${field}`,
          ];
          const otherFieldLines = getFieldByOtherLine(field);
          return (
            !!currentFormSetting?.[`textbox_zeitraum${field}`] ||
            (includeFields?.every(
              (fieldName) => !currentFormSetting?.[fieldName]
            ) &&
              otherFieldLines?.some(
                (fieldName) => !!currentFormSetting?.[fieldName]
              ))
          );
        },
      },
    ];

    return [...fields, ...rowData];
  }, []);

  return [
    {
      fieldName: 'textbox_ik',
      fieldIndex: 1,
      isValid: (): boolean => {
        return !!currentFormSetting?.['textbox_ik'];
      },
    },
    {
      fieldName: 'textbox_therapieziele_line1',
      fieldIndex: 2,
      isValid: (): boolean => {
        return !!currentFormSetting?.['textbox_therapieziele_line1'];
      },
    },
    {
      fieldName: 'textbox_verodnete1',
      fieldIndex: 3,
      isValid: (): boolean => {
        return !!currentFormSetting?.['textbox_verodnete1'];
      },
    },
    ...VALIATED_GROUP_FIELDS_RULE_4,
    {
      fieldName: 'date_label_custom_datum',
      fieldIndex: 5,
      isValid: (): boolean => {
        return !!currentFormSetting?.['date_label_custom_datum'];
      },
    },
  ];
};
