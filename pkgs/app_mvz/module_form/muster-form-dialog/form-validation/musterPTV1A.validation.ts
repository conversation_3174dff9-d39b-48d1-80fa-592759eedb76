import {
  groupCheckbox1,
  groupCheckbox2,
  groupCheckbox3,
} from '@tutum/mvz/module_form/muster-form/process-muster/musterPTV1A';
import { IMusterPrescribe } from '../musterFormDialog.store';
import { isOptionalCheckBoxValid } from './helper.validation';

export default (store: IMusterPrescribe) => {
  const currentFormSetting = store.currentFormSetting;
  const checkboxPTV1AValues1 = groupCheckbox1.map(
    (item) => !!currentFormSetting?.[item]
  );
  const validationCheckbox1 = groupCheckbox1.reduce(
    (validations, fieldName) => {
      return [
        ...validations,
        {
          fieldName,
          fieldIndex: 2,
          isValid: (): boolean => {
            return isOptionalCheckBoxValid(checkboxPTV1AValues1);
          },
        },
      ];
    },
    []
  );
  const checkboxPTV1AValues2 = groupCheckbox2.map(
    (item) => !!currentFormSetting?.[item]
  );
  const validationCheckbox2 = groupCheckbox2.reduce(
    (validations, fieldName) => {
      return [
        ...validations,
        {
          fieldName,
          fieldIndex: 2,
          isValid: (): boolean => {
            return isOptionalCheckBoxValid(checkboxPTV1AValues2);
          },
        },
      ];
    },
    []
  );
  const checkboxPTV1AValues3 = groupCheckbox3.map(
    (item) => !!currentFormSetting?.[item]
  );
  const validationCheckbox3 = groupCheckbox3.reduce(
    (validations, fieldName) => {
      return [
        ...validations,
        {
          fieldName,
          fieldIndex: 3,
          isValid: (): boolean => {
            return isOptionalCheckBoxValid(checkboxPTV1AValues3);
          },
        },
      ];
    },
    []
  );

  return [
    {
      fieldName: 'date_label_custom_date',
      fieldIndex: 1,
      isValid: (): boolean => {
        return (
          !currentFormSetting?.date_label_custom_date2 ||
          !!(+currentFormSetting?.date_label_custom_date &&
            +currentFormSetting?.date_label_custom_date <=
            +currentFormSetting?.date_label_custom_date2)
        );
      },
    },
    ...validationCheckbox1,
    ...validationCheckbox2,
    ...validationCheckbox3,
    {
      fieldName: 'date_label_custom_zwar',
      fieldIndex: 4,
      isValid: (): boolean => {
        return (
          !currentFormSetting?.checkbox_ja ||
          !!currentFormSetting?.date_label_custom_zwar
        );
      },
    },
    {
      fieldName: 'date_label_custom_datum',
      fieldIndex: 5,
      isValid: (): boolean => {
        return !!currentFormSetting?.date_label_custom_datum;
      },
    },
  ];
};
