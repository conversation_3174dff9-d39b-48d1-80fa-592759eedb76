import {
  IFormField,
  IFormFieldType,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import { IMusterPrescribe } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import {
  IHeimiSelectionActions,
  IHeimiSelectionStore,
} from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import { processOptionalCheckBox } from './helper';

export default (
  formField: IFormField,
  cloned: object,
  musterFormDialogStore: IMusterPrescribe
) => {
  const actions =
    musterFormDialogStore.componentActions as IHeimiSelectionActions;
  const heimiSelectionStore =
    musterFormDialogStore.componentStore as IHeimiSelectionStore;

  // if case view in heimi statistic, by pass
  if (!actions || !heimiSelectionStore) {
    return;
  }
  const onlyCheckedCoPayment = [
    'checkbox_zuzahlungsfrei_0',
    'checkbox_zuzahlungpflicht_0',
  ];

  if (formField.type === IFormFieldType.CHECK_BOX) {
    if (onlyCheckedCoPayment.includes(formField.name)) {
      processOptionalCheckBox(onlyCheckedCoPayment, formField, cloned);
    }
  }

  switch (formField.name) {
    case 'checkbox_zuzahlungsfrei_0':
      actions.setZuzahLungsFrei(cloned[formField.name]);
      break;
    case 'checkbox_zuzahlungpflicht_0':
      actions.setZuzahLungsPflicht(cloned[formField.name]);
      break;
    case 'checkbox_unfallfolgen_0':
      actions.setUnfallFolgen(cloned[formField.name]);
      break;
    case 'checkbox_bvg_0':
      actions.setBvg(cloned[formField.name]);
      break;
    case 'textbox_icd_code10_line1_0':
      // actions.setDocumentDiagnose({
      //   ...heimiSelectionStore.documentDiagnose,
      //   name: cloned[formField.name],
      // });
      break;
    // case 'textbox_icd_code10_line2_0':
    // actions.setSecondaryDiagnoses({
    //   ...store.secondDiagnoses,
    //   name: cloned[formField.name],
    // });
    // break;
    // the flowing is readonly field
    case 'checkbox_physiotherapie_0':
    case 'checkbox_podologische_0':
    case 'checkbox_schlucktherapie_0':
    case 'checkbox_ergotherapie_0':
    case 'checkbox_ernahrungstherapie_0':
    case 'checkbox_leitsymptomatik_a_0':
    case 'checkbox_leitsymptomatik_b_0':
    case 'checkbox_leitsymptomatik_c_0':
    case 'checkbox_patientenindividuelle_0':
      cloned[formField.name] = !cloned[formField.name];
      break;
    case 'textbox_heilmittel_line1_0':
    case 'textbox_heilmittel_line2_0':
    case 'textbox_heilmittel_line3_0': {
      actions?.UpdateNameCurrentRemedies(formField, cloned);
      break;
    }
    case 'textbox_behandlung_line1_0':
      actions.setStandardCombinationQuantity(cloned[formField.name]);
      break;
    case 'textbox_behandlung_line2_0':
    case 'textbox_behandlung_line3_0': {
      const changedRemidyQuantity = heimiSelectionStore.currentRemedies.map(
        (remedy, index) => {
          if (
            index ===
            +formField.name.split('').reverse().join('').charAt(2) - 1 &&
            remedy
          ) {
            return { ...remedy, quantity: +cloned[formField.name] };
          }
          return remedy;
        }
      );
      actions.setCurrentRemedies(changedRemidyQuantity);
      break;
    }
    case 'textbox_erganzendes_0': {
      const complementaryRemedies =
        (heimiSelectionStore.currentComplementaryRemedies || []).map(
          (remedy, index) => {
            if (index === 0 && remedy) {
              return { ...remedy, name: cloned[formField.name] };
            }

            return undefined!;
          }
        );
      actions.setCurrentComplementaryRemedies(complementaryRemedies);
      break;
    }
    case 'textbox_behandlung_line4_0': {
      const complementaryRemedies =
        (heimiSelectionStore.currentComplementaryRemedies || []).map(
          (remedy, index) => {
            if (index === 0 && remedy) {
              return { ...remedy, quantity: +cloned[formField.name] };
            }

            return undefined!;
          }
        );
      actions.setCurrentComplementaryRemedies(complementaryRemedies);
      break;
    }
    case 'checkbox_therapiebericht_0':
      actions.setTherapyReport(cloned[formField.name]);
      break;
    case 'checkbox_hausbesuch_ja_0':
      cloned['checkbox_hausbesuch_nein_0'] = !cloned[formField.name];
      actions.setHomeVisit(true);
      break;
    case 'checkbox_hausbesuch_nein_0':
      cloned['checkbox_hausbesuch_ja_0'] = !cloned[formField.name];
      actions.setHomeVisit(false);
      break;
    case 'checkbox_dringlicher_14_tagen_0':
      actions.setUrgentTreatment(cloned[formField.name]);
      break;
    case 'area_textbox_leitsymtomatik_0':
      if (heimiSelectionStore?.patientIndividualSymptom) {
        actions.setKeySymptomsFreeText(cloned[formField.name]);
      }
      break;
    case 'area_textbox_therapieziele_0':
      actions.setTheRapyFreeText(cloned[formField.name]);
      break;
    case 'area_textbox_standard_0':
      actions.setRemediesTimeline(cloned[formField.name]);
      break;
    default:
      break;
  }
};
