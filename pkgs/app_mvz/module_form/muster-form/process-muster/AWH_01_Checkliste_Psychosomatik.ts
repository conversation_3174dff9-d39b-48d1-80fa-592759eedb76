import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import {
  IFormField,
  IFormFieldType,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import FormI18n from '@tutum/mvz/locales/en/Form.json';
import { IMusterPrescribe } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { processOptionalCheckBox } from './helper';

const singleSelectionGroup1 = ['checkbox_vorsor', 'checkbox_rehabili'];
const singleSelectionGroup2 = ['checkbox_f1_mabig', 'checkbox_f1_schwere'];
const singleSelectionGroup3 = ['checkbox_f2_leicht', 'checkbox_f2_mabig'];
const singleSelectionGroup4 = ['checkbox_f3_mabig', 'checkbox_f3_schwere'];
const singleSelectionGroup5 = [
  'checkbox_f4_in_der_left',
  'checkbox_f4_in_der_right',
];

const groupAllCheckBox = [
  singleSelectionGroup1,
  singleSelectionGroup2,
  singleSelectionGroup3,
  singleSelectionGroup4,
  singleSelectionGroup5,
];

interface IProcessMusterInterface {
  formField?: IFormField;
  cloned?: object;
  musterFormDialogStore?: IMusterPrescribe;
  t?: IFixedNamespaceTFunction<keyof typeof FormI18n.Form>;
  patient?: IPatientProfile;
}
export default (props: IProcessMusterInterface) => {
  const { formField, cloned } = props;

  if (formField?.type === IFormFieldType.CHECK_BOX) {
    groupAllCheckBox.forEach((group) => {
      if (group.includes(formField.name)) {
        processOptionalCheckBox(group, formField, cloned!);
      }
    });
  }
};
