import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import {
  IFormField,
  IFormFieldType,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import FormI18n from '@tutum/mvz/locales/en/Form.json';
import { IMusterPrescribe } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { processOptionalCheckBox } from './helper';

const singleSelectionGroup1 = [
  'checkbox_rehabilitation_0',
  'checkbox_vorsorge_0',
];
const singleSelectionGroup2 = [
  'checkbox_mannlich_kindes_0',
  'checkbox_weiblich_kindes_0',
];
const singleSelectionGroup3 = [
  'checkbox_geset_kran_0',
  'checkbox_privat_kran_0',
];
const singleSelectionGroup4 = [
  'checkbox_ist_die_nein_1',
  'checkbox_ist_die_js_1',
];
const singleSelectionGroup5 = ['checkbox_sind_nein_1', 'checkbox_sind_ja_1'];
const singleSelectionGroup6 = [
  'checkbox_ist_das_nein_2',
  'checkbox_ist_das_ja_2',
];

const singleSelectionGroup7 = [
  'checkbox_impfschaden_ja_1',
  'checkbox_impfschaden_nein_1',
];

const singleSelectionGroup8 = [
  'checkbox_wurden_nein_1',
  'checkbox_wurden_ja_1',
];

const singleSelectionGroup9 = [
  'checkbox_ist_die_nein_1',
  'checkbox_ist_die_ja_1',
];

const groupAllCheckBox = [
  singleSelectionGroup1,
  singleSelectionGroup2,
  singleSelectionGroup3,
  singleSelectionGroup4,
  singleSelectionGroup5,
  singleSelectionGroup6,
  singleSelectionGroup7,
  singleSelectionGroup8,
  singleSelectionGroup9,
];

interface IProcessMusterInterface {
  formField?: IFormField;
  cloned?: object;
  musterFormDialogStore?: IMusterPrescribe;
  t?: IFixedNamespaceTFunction<keyof typeof FormI18n.Form>;
  patient?: IPatientProfile;
}
export default (props: IProcessMusterInterface) => {
  const { formField, cloned } = props;

  if (formField?.type === IFormFieldType.CHECK_BOX) {
    groupAllCheckBox.forEach((group) => {
      if (group.includes(formField.name)) {
        processOptionalCheckBox(group, formField, cloned!);
      }
    });
  }
};
