import { isEmpty, reject } from 'lodash';

import { FormName } from '@tutum/hermes/bff/form_common';
import { EventResponseReferenceCode } from '@tutum/hermes/bff/app_mvz_tss';
import {
  IFormField,
  IFormFieldType,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import { IMusterPrescribe } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import {
  IPatientFileStore,
  patientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  fieldValidationMuster1,
  fieldValidationMuster8A,
  fieldValidationMuster10,
  fieldValidationMuster10A,
  fieldValidationMuster2B,
  fieldValidationMuster3,
  fieldValidationMuster4,
  fieldValidationMuster5,
  fieldValidationMuster6,
  fieldValidationMuster12,
  IFormValidation,
  fieldValidationMuster19,
  fieldValidationBKK_BOSCH_VAG_BW_Praeventionsverordnung,
  fieldValidationAOK_BW_Beratungsbogen_Einbindung_SD,
  fieldValidationAWH_01_Kurzantrag_HZV_KinderReha,
  fieldValidationMuster22,
  fieldValidationMuster27,
  fieldValidationMuster28,
  fieldValidationMuster52_2,
  fieldValidationMuster20,
  fieldValidationMuster61,
  fieldValidationMuster70,
  fieldValidationMuster13,
  fieldValidationMusterPTV1A,
  fieldValidationMusterPTV11A,
  fieldValidationG81_EHIC,
  fieldValidationMuster11,
  fieldValidationMuster26,
  fieldValidationMuster55,
  fieldValidationMuster15,
  fieldValidationMuster16,
  fieldValidationMusterPTV2A,
  fieldValidationMusterPTV12A,
  fieldValidationSchnellinformation_Patientenbegleitung,
  fieldValidationBKK_BOSCH_BW_Schnellinfo_Patientenbegleitung,
  fieldValidationBKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung,
  fieldValidationAOK_FA_NPPP_BW_GDK_Antragsformular,
  fieldValidationBKK_GWQ_FA_PT_BW_Ausschreibeformular,
  fieldValidationBKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater,
  fieldValidationAOK_HE_HZV_Versichertenteilnahmeerklaerung_V12,
  fieldValidationAOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck,
  fieldValidationTransitionManagement,
  fieldValidationF2100,
  fieldValidationF9990,
} from './validationFields';
import { parsingReferralCode } from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { GroupByQuarter } from '@tutum/hermes/bff/app_mvz_timeline';
import { TimelineEntityType } from '@tutum/hermes/bff/timeline_common';
import { getBillingInfo } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/util';
import { getUvGoaCatalogByCodes } from '@tutum/hermes/bff/legacy/app_mvz_catalog_uv_goa';

export const isScheinSubGroup27Or28 = (patientFileStore: IPatientFileStore) => {
  return (
    patientFileStore.schein.activatedSchein?.kvScheinSubGroup == '27' ||
    patientFileStore.schein.activatedSchein?.kvScheinSubGroup == '28'
  );
};

export const preProcessFieldAnnotationList = (
  fields: IFormField[] | undefined,
  musterFormDialogStore: IMusterPrescribe
): IFormField[] => {
  fields = reject(fields, (field) => field.name.startsWith('textbox_te_code'));
  //edit mode need hide label_date or it will overlap the date component
  switch (musterFormDialogStore.currentFormName) {
    case FormName.Muster_1: {
      // reject field on page 2 and page 3 as default, only bind data on that pages when print
      fields = reject(
        fields,
        (field) => field.name.endsWith('_1') || field.name.endsWith('_2')
      );
      //prescribe case

      const isPrescribeCase =
        !musterFormDialogStore.isViewForm && !musterFormDialogStore.isRefill;
      if (isPrescribeCase) {
        fields = reject(fields, (field) => {
          return (
            field.name === 'label_date_arbeit_0' ||
            field.name === 'label_date_vorau_0' ||
            field.name === 'label_date_festgestellt_0'
          );
        });
        //in prescribe mode but user change to follow-up case
        if (musterFormDialogStore.currentFormSetting?.['checkbox_folgebes_0']) {
          fields = reject(fields, (field) => {
            return field.name === 'date_arbeit_0';
          });
        }
      }

      if (musterFormDialogStore.isRefill && !isEmpty(fields)) {
        const date_festgestellt_0 = fields.find(
          (item) => item.name === 'date_festgestellt_0'
        ) as IFormField;
        const date_arbeit_0 = fields.find(
          (item) => item.name === 'date_arbeit_0'
        ) as IFormField;
        fields = reject(fields, (field) => {
          return (
            field.name === 'date_arbeit_0' ||
            field.name === 'label_date_festgestellt_0' ||
            field.name === 'label_date_vorau_0'
          );
        });
        //in prescribe mode but user change to follow-up case
        if (musterFormDialogStore.currentFormSetting?.['checkbox_erstbes_0']) {
          fields = [...fields, date_festgestellt_0, date_arbeit_0];
        }
      }
      if (musterFormDialogStore.isViewForm) {
        fields = reject(fields, (field) => {
          return (
            field.name === 'date_arbeit_0' ||
            field.name === 'date_vorau_0' ||
            field.name === 'date_festgestellt_0'
          );
        });
      }
      return fields;
    }
    case FormName.Muster_4: {
      return reject(fields, (field) => {
        return (
          field.name === 'label_date_label_custom_vom_no_space' ||
          field.name === 'label_date_label_custom_bis_no_space'
        );
      });
    }

    case FormName.Muster_5: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return [
            'label_date_quartal',
            'label_date_quartal_quarter',
            'label_date_quartal_year',
            'label_date_label_custom_notificationDate_no_space',
            'label_date_label_custom_mutmablicher_no_space',
          ].includes(field.name);
        });
      } else {
        return reject(fields, (field) => {
          return [
            'date_quartal',
            'label_date_quartal_quarter',
            'label_date_quartal_year',
            'label_date_label_custom_notificationDate_no_space',
            'label_date_label_custom_mutmablicher_no_space',
          ].includes(field.name);
        });
      }
    }

    case FormName.Muster_6: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return [
            'label_date_quartal',
            'label_date_quartal_quarter',
            'label_date_quartal_year',
            'label_date_label_custom_der_op_no_space',
            'label_date_label_custom_au_bis_no_space',
          ].includes(field.name);
        });
      } else {
        return reject(fields, (field) => {
          return [
            'date_quartal',
            'label_date_quartal_quarter',
            'label_date_quartal_year',
            'label_date_label_custom_der_op_no_space',
            'label_date_label_custom_au_bis_no_space',
          ].includes(field.name);
        });
      }
    }

    case FormName.Muster_10: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return [
            'label_date_quartal',
            'label_date_quartal_quarter',
            'label_date_quartal_year',
            'label_date_label_custom_abnahmedatum_no_space',
            'label_date_abnahmezeit',
            'textbox_knappschafts_kennziffer_comp',
            'textbox_arzt_comp',
            'textbox_betri_comp',
            'textbox_ssw_comp',
            'label_date_abnahmezeit_hour',
            'label_date_abnahmezeit_minute',
          ].includes(field.name);
        });
      } else {
        return reject(fields, (field) => {
          return [
            'date_quartal',
            'label_date_quartal_quarter',
            'label_date_quartal_year',
            'date_abnahmezeit',
            'textbox_arzt_comp',
            'textbox_betri_comp',
            'textbox_knappschafts_kennziffer_comp',
            'textbox_ssw_comp',
            'label_date_abnahmezeit_hour',
            'label_date_abnahmezeit_minute',
            'label_date_label_custom_abnahmedatum_no_space',
          ].includes(field.name);
        });
      }
    }

    case FormName.Muster_10A: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return [
            'label_textbox_knappschafts_kennziffer',
            'label_date_abnahmezeit',
            'label_textbox_ssw',
            'label_date_abnahmezeit_hour',
            'label_date_abnahmezeit_minute',
            'label_date_label_custom_abnahmedatum_no_space',
          ].includes(field.name);
        });
      } else {
        return reject(fields, (field) => {
          return [
            'textbox_knappschafts_kennziffer',
            'date_abnahmezeit',
            'textbox_ssw',
            'label_date_abnahmezeit_hour',
            'label_date_abnahmezeit_minute',
            'label_date_label_custom_abnahmedatum_no_space',
          ].includes(field.name);
        });
      }
    }

    case FormName.Muster_10C: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return [
            'label_date_label_full_date_0',
            'label_date_abnahmezeit_0',
            'label_date_quartal_0',
            'label_textbox_betri_0',
            'label_textbox_arzt_0',
          ].includes(field.name);
        });
      } else {
        return reject(fields, (field) => {
          return [
            'date_label_full_date_0',
            'date_abnahmezeit_0',
            'date_quartal_0',
            'textbox_betri_0',
            'textbox_arzt_0',
          ].includes(field.name);
        });
      }
    }

    case FormName.Muster_13: {
      if (
        musterFormDialogStore.isViewForm &&
        musterFormDialogStore.isHideEditButton
      ) {
        return reject(fields, (field) => {
          return [
            'label_patient_fullname_1',
            'label_ik_number_1',
            'label_insurance_number_1',
            'label_edit_button_0',
            'area_textbox_standard_0',
            'label_prf_nr_1',
          ].includes(field.name);
        });
      }
      return reject(fields, (field) => {
        return [
          'label_patient_fullname_1',
          'label_ik_number_1',
          'label_insurance_number_1',
          'area_textbox_standard_0',
          'label_prf_nr_1',
        ].includes(field.name);
      });
    }

    case FormName.Muster_15: {
      return reject(fields, (field) => {
        return field.name.endsWith('_1');
      });
    }

    case FormName.Muster_39A: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return (
            field.name === 'label_month_year_date_anamnese' ||
            field.name === 'label_date_custom_jetzt' ||
            field.name === 'label_date_custom_wann'
          );
        });
      } else {
        return reject(fields, (field) => {
          return (
            field.name === 'date_anamnese' ||
            field.name === 'date_custom_jetzt' ||
            field.name === 'date_custom_wann'
          );
        });
      }
    }

    case FormName.Muster_20A: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_datum_no_space',
          'label_date_label_custom_vom1_no_space',
          'label_date_label_custom_bis1_no_space',
          'label_date_label_custom_vom2_no_space',
          'label_date_label_custom_bis2_no_space',
          'label_date_label_custom_vom3_no_space',
          'label_date_label_custom_bis3_no_space',
          'label_date_label_custom_vom4_no_space',
          'label_date_label_custom_bis4_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_21: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_vom_no_space',
          'label_date_label_custom_bis_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_22A: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_datum_no_space',
          'label_textbox_custom_arztnummer',
          'label_textbox_custom_betrieb',
        ].includes(field.name);
      });
    }

    case FormName.Muster_27A: {
      return reject(fields, (field) => {
        return ['label_date_label_custom_datum_no_space'].includes(field.name);
      });
    }

    case FormName.Muster_28A: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_datum_no_space',
          'label_date_label_custom_folgende_datum1_no_space',
          'label_date_label_custom_folgende_datum2_no_space',
          'label_date_label_custom_folgende_datum3_no_space',
          'label_date_label_custom_folgende_datum4_no_space',
          'label_date_label_custom_folgende_datum5_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_50: {
      const groupDate = Array.from({ length: 10 }).map(
        (_, index) =>
          `label_date_label_custom_behandlungstag${index + 1}_no_space`
      );
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_datum_no_space',
          'label_date_label_custom_geburtsdatum_no_space',
          ...groupDate,
        ].includes(field.name);
      });
    }

    case FormName.Muster_51: {
      const groupDate = Array.from({ length: 18 }).map(
        (_, index) =>
          `label_date_label_custom_behandlungstag${index + 1}_no_space`
      );
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_datum_no_space',
          'label_date_label_custom_ausschlieBlich_vom_no_space',
          'label_date_label_custom_ausschlieBlich_bis_no_space',
          'label_date_label_custom_anderer_leiden_vom_no_space',
          'label_date_label_custom_anderer_leiden_bis_no_space',
          ...groupDate,
        ].includes(field.name);
      });
    }

    case FormName.Muster_52_0_V2:
    case FormName.Muster_52_2_V3: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_datum_no_space',
          'label_date_label_custom_weidereintritts_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_55: {
      return reject(fields, (field) => {
        return [
          'label_month_year_have_dot_date_kontinuierliche',
          'label_date_label_custom_seit_no_space',
          'label_date_label_custom_datum_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_56: {
      return reject(fields, (field) => {
        return ['label_date_label_custom_datum_1_no_space'].includes(
          field.name
        );
      });
    }

    case FormName.Muster_61: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_datum_0_no_space',
          'label_date_label_custom_datum_4_no_space',
          'label_date_label_custom_datum2_4_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_N63A: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_vom_0_no_space',
          'label_date_label_custom_bis_0_no_space',
          'label_date_label_custom_datum_1_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_64: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_geburtsdatum1_1_no_space',
          'label_date_label_custom_geburtsdatum2_1_no_space',
          'label_date_label_custom_geburtsdatum3_1_no_space',
          'label_date_label_custom_datum_1_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_9: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_geburtsdatum_no_space',
          'label_date_label_custom_datum_no_space',
          'label_date_label_custom_prematureBirth_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_PTV_1A: {
      return reject(fields, (field) => {
        return [
          'label_date_label_custom_zwar_no_space',
          'label_date_label_custom_ggf_no_space',
          'label_date_label_custom_datum_no_space',
        ].includes(field.name);
      });
    }

    case FormName.Muster_PTV_11A: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return [
            'label_date_label_custom_ausstellungsdatum_no_space',
            'label_date_label_custom_abnahmedatum_no_space',
            'label_date_label_custom_date2_no_space',
            'label_date_label_custom_date_no_space',
            'label_date_label_custom_datumErklarung_no_space',
            'label_date_abnahmezeit',
            'label_date_abnahmezeit_hour',
            'label_date_abnahmezeit_minute',
          ].includes(field.name);
        });
      } else {
        return reject(fields, (field) => {
          return [
            'label_date_label_custom_ausstellungsdatum_no_space',
            'label_date_label_custom_abnahmedatum_no_space',
            'label_date_label_custom_date2_no_space',
            'label_date_label_custom_date_no_space',
            'label_date_label_custom_datumErklarung_no_space',
            'date_abnahmezeit',
            'label_date_abnahmezeit_hour',
            'label_date_abnahmezeit_minute',
          ].includes(field.name);
        });
      }
    }

    case FormName.Muster_19B: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return [
            'label_date_label_custom_arbeitsunfahigkeit_have_space',
            'label_date_quartal_quarter',
            'label_date_quartal_year',
          ].includes(field.name);
        });
      }

      return reject(fields, (field) => {
        return [
          'label_date_quartal',
          'label_date_quartal_quarter',
          'label_date_quartal_year',
        ].includes(field.name);
      });
    }
    case FormName.Muster_12A: {
      return reject(fields, (field) => {
        return (
          field.name.startsWith('label_date_month_') ||
          [
            'label_date_label_custom_vom_0_no_space',
            'label_date_label_custom_bis_0_no_space',
          ].includes(field.name)
        );
      });
    }

    case FormName.Muster_70:
    case FormName.Muster_70A:
    case FormName.Muster_PTV_12A: {
      return reject(fields, (field) => {
        return field.name.endsWith('no_space');
      });
    }
    case FormName.Muster_26A:
    case FormName.Muster_PTV_2A: {
      return reject(fields, (field) => {
        return field.name.endsWith('no_space') || field.name.endsWith('_comp');
      });
    }

    case FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6:
    case FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6: {
      return reject(fields, (field) => {
        return [
          'label_date_abnahmezeit',
          'label_date_abnahmezeit_hour',
          'label_date_abnahmezeit1',
        ].includes(field.name);
      });
    }

    case FormName.AOK_BW_Beratungsbogen_Einbindung_SD_V7:
    case FormName.BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10: {
      return reject(fields, (field) => {
        return [
          'label_date_abnahmezeit_hour_0',
          'label_date_abnahmezeit_0',
          'label_date_abnahmezeit1_0',
        ].includes(field.name);
      });
    }

    case FormName.F1050: {
      if (!musterFormDialogStore.isViewForm) {
        return reject(fields, (field) => {
          return ['label_date_label_custom_start_datum_0'].includes(field.name);
        });
      }

      return reject(fields, (field) => {
        return ['date_label_custom_start_datum_0'].includes(field.name);
      });
    }

    default:
      return fields;
  }
};

export const getDateFormat = (
  formField: IFormField,
  musterFormDialogStore: IMusterPrescribe
): string => {
  let dateFormat = DATE_FORMAT;
  switch (musterFormDialogStore.currentFormName) {
    case FormName.Muster_10C: {
      if (formField.type === IFormFieldType.DATE_PICKER) {
        if (formField.name === 'date_quartal_0') {
          dateFormat = 'Q.YY';
        }
      }
      break;
    }

    case FormName.Muster_5:
    case FormName.Muster_6:
    case FormName.Muster_10:
    case FormName.Muster_19B: {
      if (formField.type === IFormFieldType.DATE_PICKER) {
        if (formField.name === 'date_quartal') {
          dateFormat = 'Q.YY';
        }
      }
      break;
    }

    case FormName.Muster_39A: {
      if (formField.type === IFormFieldType.DATE_PICKER) {
        if (formField.name === 'date_anamnese') {
          dateFormat = 'MM.YY';
        }
      }
      break;
    }
  }

  return dateFormat;
};

export interface IMusterFormComponentDateRange {
  minDate?: Date;
  maxDate?: Date;
}

export const getDateRange = (
  formField: IFormField,
  musterFormDialogStore: IMusterPrescribe
): IMusterFormComponentDateRange => {
  if (formField.type === IFormFieldType.DATE_PICKER) {
    switch (musterFormDialogStore.currentFormName) {
      case FormName.Muster_1:
        if (
          formField.name === 'date_arbeit_0' ||
          formField.name === 'date_vorau_0'
        ) {
          const dateRange = {
            minDate: undefined,
            maxDate: undefined,
          };
          return dateRange;
        }
        break;
      case FormName.BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2:
      case FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5:
        if (formField.name === 'date_label_custom_patient') {
          return {
            minDate: undefined,
            maxDate: datetimeUtil.date(),
          };
        }
        break;
      case FormName.AWH_01_Kurzantrag_HZV_KinderReha_V1:
        if (
          [
            'date_label_custom_geburtsdatum_line1_0',
            'date_label_custom_geburtsdatum_line2_0',
            'date_label_custom_geburtsdatum_kindes_0',
          ].includes(formField.name)
        ) {
          const minDate = new Date(1900, 0, 1);

          return {
            minDate,
            maxDate: undefined,
          };
        }

        break;
      default:
        break;
    }
  }

  return {
    minDate: undefined,
    maxDate: undefined,
  };
};

export const handleClassNameItem = (
  formField: IFormField,
  isLabForms: boolean
) => {
  switch (formField.type) {
    case IFormFieldType.DATE_PICKER:
      return isLabForms ? 'sl-form-annotation-date-picker' : '';
    case IFormFieldType.TEXT_BOX:
      return isLabForms ? 'sl-form-annotation-text-box' : '';
    default:
      return '';
  }
};

const handleValidation = (
  formField: IFormField,
  validationFields: IFormValidation
) => {
  switch (formField.type) {
    case IFormFieldType.TEXT_BOX:
    case IFormFieldType.CHECK_BOX:
    case IFormFieldType.DATE_PICKER:
    case IFormFieldType.AREA_TEXT_BOX: {
      if (Object.keys(validationFields).includes(formField.name)) {
        return {
          ...formField,
          maxLength: validationFields[formField.name].maxLength,
          numeric: validationFields[formField.name].numeric,
          disabled: validationFields[formField.name].disabled,
          isFloat: validationFields[formField.name].isFloat,
          style: validationFields[formField.name].style,
          allowLeadingZeros: validationFields[formField.name].allowLeadingZeros,
          dateFormat: validationFields[formField.name].dateFormat,
          timeDayType: validationFields[formField.name].timeDayType,
        };
      }
      return formField;
    }
    default:
      return formField;
  }
};

export const passValidateToTextField = (
  formField: IFormField,
  musterFormDialogStore: IMusterPrescribe,
  isHeimiBlankForm?: boolean
): IFormField => {
  switch (musterFormDialogStore.currentFormName) {
    case FormName.Muster_2B:
      return handleValidation(formField, fieldValidationMuster2B);
    case FormName.Muster_3A: {
      return handleValidation(formField, fieldValidationMuster3);
    }
    case FormName.Muster_4: {
      return handleValidation(
        formField,
        fieldValidationMuster4(musterFormDialogStore)
      );
    }
    case FormName.Muster_8:
    case FormName.Muster_8A: {
      return handleValidation(formField, fieldValidationMuster8A);
    }
    case FormName.Muster_10: {
      return handleValidation(formField, fieldValidationMuster10);
    }
    case FormName.Muster_10A: {
      return handleValidation(formField, fieldValidationMuster10A);
    }
    case FormName.Muster_5: {
      return handleValidation(formField, fieldValidationMuster5);
    }
    case FormName.Muster_6: {
      return handleValidation(
        formField,
        fieldValidationMuster6(musterFormDialogStore)
      );
    }
    case FormName.Muster_1: {
      return handleValidation(
        formField,
        fieldValidationMuster1(musterFormDialogStore)
      );
    }
    case FormName.AOK_BW_Beratungsbogen_Einbindung_SD_V7:
    case FormName.BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10: {
      return handleValidation(
        formField,
        fieldValidationAOK_BW_Beratungsbogen_Einbindung_SD()
      );
    }
    case FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1: {
      return handleValidation(
        formField,
        fieldValidationBKK_BOSCH_VAG_BW_Praeventionsverordnung()
      );
    }
    case FormName.Ambulantes_Operieren_V1:
    case FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6:
    case FormName.AWH_01_Checkliste_Psychosomatik_V1:
    case FormName.AWH_01_Checkliste_Somatik_V1: {
      return handleValidation(
        formField,
        fieldValidationBKK_BOSCH_BW_Schnellinfo_Patientenbegleitung()
      );
    }
    case FormName.BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17: {
      return handleValidation(
        formField,
        fieldValidationBKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung()
      );
    }
    case FormName.Muster_11: {
      return handleValidation(formField, fieldValidationMuster11);
    }
    case FormName.Muster_12A: {
      return handleValidation(
        formField,
        fieldValidationMuster12(musterFormDialogStore)
      );
    }
    case FormName.Muster_19A:
    case FormName.Muster_19B:
    case FormName.Muster_19C:
      return handleValidation(formField, fieldValidationMuster19);
    case FormName.AWH_01_Kurzantrag_HZV_KinderReha_V1: {
      return handleValidation(
        formField,
        fieldValidationAWH_01_Kurzantrag_HZV_KinderReha(musterFormDialogStore)
      );
    }
    case FormName.Muster_22A:
      return handleValidation(formField, fieldValidationMuster22);
    case FormName.Muster_26A:
      return handleValidation(
        formField,
        fieldValidationMuster26(musterFormDialogStore)
      );
    case FormName.Muster_27A:
      return handleValidation(formField, fieldValidationMuster27);
    case FormName.Muster_28A:
      return handleValidation(formField, fieldValidationMuster28);
    case FormName.Muster_52_2_V3:
      return handleValidation(formField, fieldValidationMuster52_2);
    case FormName.Muster_20A:
    case FormName.Muster_20B:
    case FormName.Muster_20C:
    case FormName.Muster_20D:
      return handleValidation(formField, fieldValidationMuster20);
    case FormName.Muster_55:
      return handleValidation(formField, fieldValidationMuster55);
    case FormName.Muster_61:
      return handleValidation(
        formField,
        fieldValidationMuster61(musterFormDialogStore)
      );
    case FormName.Muster_70:
    case FormName.Muster_70A:
      return handleValidation(formField, fieldValidationMuster70);
    case FormName.Muster_13:
      return handleValidation(
        formField,
        fieldValidationMuster13(!!isHeimiBlankForm)
      );
    case FormName.Muster_15:
      return handleValidation(formField, fieldValidationMuster15);
    case FormName.Muster_16:
      return handleValidation(
        formField,
        fieldValidationMuster16(musterFormDialogStore)
      );
    case FormName.Muster_PTV_1A: {
      return handleValidation(
        formField,
        fieldValidationMusterPTV1A(musterFormDialogStore)
      );
    }
    case FormName.Muster_PTV_2A: {
      return handleValidation(formField, fieldValidationMusterPTV2A);
    }
    case FormName.Muster_PTV_11A: {
      return handleValidation(
        formField,
        fieldValidationMusterPTV11A(musterFormDialogStore)
      );
    }
    case FormName.Muster_PTV_12A: {
      return handleValidation(
        formField,
        fieldValidationMusterPTV12A(musterFormDialogStore)
      );
    }
    case FormName.G81_EHIC_Bulgarisch:
    case FormName.G81_EHIC_Danisch:
    case FormName.G81_EHIC_Englisch:
    case FormName.G81_EHIC_Franzosisch:
    case FormName.G81_EHIC_Griechisch:
    case FormName.G81_EHIC_Italienisch:
    case FormName.G81_EHIC_Kroatisch:
    case FormName.G81_EHIC_Niederlandisch:
    case FormName.G81_EHIC_Polnisch:
    case FormName.G81_EHIC_Rumanisch:
    case FormName.G81_EHIC_Spanisch:
    case FormName.G81_EHIC_Tschechisch:
    case FormName.G81_EHIC_Ungarisch:
    case FormName.G81_EHIC_Finnisch:
    case FormName.G81_EHIC_Estnisch:
    case FormName.G81_EHIC_Slowenisch:
    case FormName.G81_EHIC_Slowakisch:
    case FormName.G81_EHIC_Schwedisch:
    case FormName.G81_EHIC_Portugiesisch:
    case FormName.G81_EHIC_Litauisch:
    case FormName.G81_EHIC_Lettisch: {
      return handleValidation(formField, fieldValidationG81_EHIC);
    }
    case FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1:
    case FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4:
      return handleValidation(
        formField,
        fieldValidationSchnellinformation_Patientenbegleitung
      );
    case FormName.AOK_FA_NPPP_BW_GDK_Antragsformular_V6:
    case FormName.BKK_BOSCH_FA_BW_GDK_Antragsformular_V4:
    case FormName.BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3:
    case FormName.BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2:
    case FormName.AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3:
      return handleValidation(
        formField,
        fieldValidationAOK_FA_NPPP_BW_GDK_Antragsformular
      );
    case FormName.BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2:
    case FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5:
      return handleValidation(
        formField,
        fieldValidationBKK_GWQ_FA_PT_BW_Ausschreibeformular(
          musterFormDialogStore
        )
      );
    case FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
    case FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
      return handleValidation(
        formField,
        fieldValidationBKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater
      );
    case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
    case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
    case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
    case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
    case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3:
      return handleValidation(
        formField,
        fieldValidationTransitionManagement(musterFormDialogStore)
      );
    case FormName.AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12:
    case FormName.AOK_PLUS_Versichertenteilnahmeerklaerung_V6:
    case FormName.AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4:
    case FormName.AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7:
    case FormName.AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8:
    case FormName.AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3:
    case FormName.IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12:
    case FormName.RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5:
    case FormName.SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7: {
      return handleValidation(
        formField,
        fieldValidationAOK_HE_HZV_Versichertenteilnahmeerklaerung_V12()
      );
    }
    case FormName.AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3:
    case FormName.AOK_FA_BW_TE_HepCModul_V3:
    case FormName.AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2:
    case FormName.AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2:
    case FormName.AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9:
    case FormName.AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11:
    case FormName.AOK_NO_HH_Versichertenteilnahmeerklaerung_V5:
    case FormName.AWH_01_BVKJ_Anlage_7b_Osteopathie_V2:
    case FormName.AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12:
    case 'BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4':
    case FormName.BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11:
    case FormName.BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2:
    case FormName.BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3:
    case FormName.BKK_BOSCH_FA_TE_HepCModul_V4:
    case FormName.BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9:
    case FormName.BKK_VAG_FA_BW_TE_HepCModul_V2:
    case FormName.BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5:
    case FormName.BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4:
    case FormName.DAK_HZV_VersichertenTeilnahmeerklaerung_V4:
    case FormName.EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4:
    case FormName.EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7:
    case FormName.EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3:
    case FormName.EK_NO_HZV_Versichertenteilnahmeerklaerung_V2:
    case FormName.EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3:
    case FormName.EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5:
    case FormName.EK_SN_HZV_Versichertenteilnahmeerklaerung_V3:
    case FormName.EK_WL_HZV_Versichertenteilnahmeerklaerung_V2:
    case FormName.HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4:
    case FormName.LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15:
    case 'LKK_BY_Teilnahme-und_Einwilligungserklaerung_V4':
    case 'LKK_Teilnahme-und_Einwilligungserklaerung_V6':
    case FormName.TK_HZV_Versichertenteilnahmeerklaerung_V9:
    case FormName.Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6:
    case FormName.Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8:
    case FormName.Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5:
    case FormName.Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5:
    case FormName.Versichertenteilnahmeerklaerung_Online_Variante_A_V11:
      return handleValidation(
        formField,
        fieldValidationAOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck()
      );
    case FormName.F1050:
    case FormName.F1000:
    case FormName.F2100:
      return handleValidation(
        formField,
        fieldValidationF2100(musterFormDialogStore)
      );
    case FormName.F9990:
      return handleValidation(formField, fieldValidationF9990);

    default:
      return formField;
  }
};

export const checkCopaymentExemptionTillDate = (
  copaymentExemptionTillDate?: number
) => {
  return (
    copaymentExemptionTillDate &&
    copaymentExemptionTillDate >= datetimeUtil.now()
  );
};

export const convertDataToStringValue = (data: (string | undefined)[], seperate = ', ') => {
  return data
    .filter((value) => value)
    .join(seperate)
    .trim();
};

export const defaultTssAddress = `www.116117.de; Tel:116117`;

export const parseInputReferral = (
  tssCode: EventResponseReferenceCode,
  key: string
) => {
  const data = tssCode?.data;
  if (tssCode?.error) {
    return defaultTssAddress; // default data error
  } else if (data) {
    return `${key}: ${parsingReferralCode(
      data?.referenceCode
    )}; ${defaultTssAddress}`;
  }
  return null;
};

export const patientHeaderKey = [
  'label_bsnr',
  'label_date_of_birth',
  'label_ik_number',
  'label_insurance_name',
  'label_insurance_number',
  'label_insurance_status',
  'label_lanr',
  'label_patientInfo_line1',
  'label_patientInfo_line2',
  'label_patientInfo_line3',
  'label_patientInfo_line4',
  'label_wop',
  'label_insurance_end_date',
];

export const baseSVGPath = '/data/form/';

export const questionMapWithSVGFile = {
  himifb039906: 'HIMIFB039906_V3',
  himifb1865: 'HIMIFB1865_V3',
  himifb31: 'HIMIFB31_V3',
  himifb1424: 'HIMIFB1424_V3',
  himifb1940: 'HIMIFB1940_V3',
  himifb1129: 'HIMIFB1129_V3',
  himifb0399054: 'HIMIFB0399054_V3',
  himifb0399051: 'HIMIFB0399051_V3',
  himifb1525192: 'HIMIFB1525192_V3',
  himifb1846er: 'HIMIFB1846ER_V3',
  himifb213401: 'HIMIFB213401_V4',
  himifb0440: 'HIMIFB0440_V3',
};

export interface EncounterUvGoaServiceForm {
  selectedDate: number;
  price: number;
  freeText: string;
  code: string;
}

export const MAXIMUM_UV_GOA_SERVICE_F9990 = 14;

export const getTimelineServiceEntries = async (
  timelineState: GroupByQuarter[],
  scheinId?: string
) => {
  const serviceCodes = timelineState.reduce(
    (uvGoaEntries: EncounterUvGoaServiceForm[], item) => {
      const filterData = item.timelineModels.filter(
        (entry) =>
          entry.scheinIds?.[0] === scheinId &&
          [
            TimelineEntityType.TimelineEntityType_Service_UV_GOA,
            TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain,
          ].includes(entry.type as TimelineEntityType) &&
          !getBillingInfo(patientFileStore.schein.originalList, entry, () => '')
            ?.billingSubmitted
      );
      const data = filterData.reduce(
        (data: EncounterUvGoaServiceForm[], datum) => {
          if (
            datum.type ===
            TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain
          ) {
            return [
              ...data,
              ...(datum.encounterUvGoaServiceChain?.uvGoaServices || []).map(
                (item) => ({
                  selectedDate: datum.selectedDate,
                  price: item.price,
                  freeText: item.freeText,
                  code: item.code,
                })
              ),
            ];
          }

          return [
            ...data,
            {
              selectedDate: datum.selectedDate,
              price: datum.encounterUvGoaService?.price,
              freeText: datum.encounterUvGoaService?.freeText,
              code: datum.encounterUvGoaService?.code,
            },
          ];
        },
        []
      );

      return [...uvGoaEntries, ...data];
    },
    []
  );

  const data = await getUvGoaCatalogByCodes({
    numbers: serviceCodes.map(item => item.code!),
  })

  return serviceCodes.filter(serviceCode => !!data.data.items.find(item => item.code === serviceCode.code && !item.isNotBillable));
};
