import type ExternalAddressI18n from '@tutum/mvz/locales/en/ExternalAddress.json';
import type { SdavCatalog } from '@tutum/hermes/bff/catalog_sdav_common';

import React, { memo, useState, useMemo } from 'react';
import _debounce from 'lodash/debounce';

import {
  alertError,
  alertSuccessfully,
  BodyTextM,
  Flex,
  H1,
  InfoConfirmDialog,
  ReactSelect,
} from '@tutum/design-system/components';
import { InputGroup } from '@tutum/design-system/components/Core';
import { Svg } from '@tutum/design-system/components/Svg';
import { Button } from '@tutum/design-system/components/Button';
import { Order } from '@tutum/hermes/bff/common';
import I18n from '@tutum/infrastructure/i18n';
import { SortField } from '@tutum/hermes/bff/catalog_sdav_common';
import {
  useMutationDeleteSdav,
  useQueryGetSdav,
} from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdav';
import ExternalAddressOverviewTable from './external-address-table/Ex-address.styled';
import ExternalAddressStyleWrapper from './ex-address-overview.styled';
import {
  useExAddressOverviewStore,
  externalAddressOverviewAction,
} from './ex-address-overview.store';
import { CreateExternalAddressDialog } from './CreateExternalAddressDialog';
import { EditExternalAddressDialog } from './EditExternalAddressDialog';
import { useQueryGetAreaOfExpertises } from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdav';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { useDebounce } from '@uidotdev/usehooks';

export const SearchIconSvgURL = '/images/search-sidebar-disable.svg';
export const PlusIconSvgURL = '/images/plus-white.svg';
export const MoreVerticalIconSvgURL = '/images/more-vertical.svg';
export const EditIconSvgURL = '/images/edit-value.svg';
export const CloseIconSvgURL = '/images/circle-close-outlined.svg';

interface ExternalAddressOverviewProps {
  isInDialog?: boolean;
  onSelectedRowsChange?: (data: any) => void;
}

const ExternalAddressOverview = ({
  isInDialog = true,
  onSelectedRowsChange,
}: ExternalAddressOverviewProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof ExternalAddressI18n['externalAddress']
  >({
    namespace: 'ExternalAddress',
    nestedTrans: 'externalAddress',
  });

  const store = useExAddressOverviewStore();
  const { setPage, setRowsPerPage, getExternalAddress } =
    externalAddressOverviewAction;
  const { page, rowsPerPage } = store;

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [areaValue, setAreaValue] = useState<string>('');
  const [inputValue, setInputValue] = useState<string>('');
  const [sortType, setSortType] = useState<{ field: SortField; order: Order }>({
    field: SortField.Bsnr,
    order: Order.ASC,
  });

  const [isOpenDialog, setIsOpenDialog] = useState(false);
  const [editSdav, setEditSdav] = useState<SdavCatalog | null>(null);
  const [removeId, setRemoveId] = useState<string | null>(null);

  const debouncedSearch = useDebounce(searchQuery, 500);
  const {
    data: sdavData,
    isLoading,
    isSuccess,
    refetch: refetchSdav,
  } = useQueryGetSdav(
    {
      pagination: {
        page: page,
        pageSize: rowsPerPage,
        sortBy: sortType?.field,
        order: sortType?.order,
      },
      query: debouncedSearch,
      filter: areaValue,
    },
    {
      enabled: true,
      select: (data) => data.data,
    }
  );

  const handlerSort = (field: SortField, _order: '' | Order) => {
    const sort = {
      field,
      order: _order || null!,
    };
    setSortType(sort);
    setPage(page);
  };

  const handlerSearch = (e: string) => {
    setPage(1);
    setSearchQuery(e);
  };

  const refetchExternalAddresses = () => refetchSdav();

  const { mutate: deleteSdavMutate } = useMutationDeleteSdav({
    onError: () => {
      alertError(t('removeFailed'));
    },
    onSuccess: () => {
      setRemoveId(null);
      alertSuccessfully(t('removeSuccess'));
      refetchExternalAddresses();
    },
  });

  const { data: areaOfExpertises = [] } = useQueryGetAreaOfExpertises({
    select: (data) => data.data.items || [],
  });

  const filterOptions = useMemo(() => {
    const lockArray = [
      '000',
      '070',
      '080',
      '081',
      '082',
      '083',
      '084',
      '086',
      '088',
      '090',
      '092',
      '093',
      '094',
      '095',
      '096',
      '097',
      '098',
    ];
    return areaOfExpertises.filter((item) => !lockArray.includes(item?.v));
  }, [areaOfExpertises]);

  return (
    <ExternalAddressStyleWrapper
      className={getCssClass({
        'in-dialog': isInDialog,
      })}
    >
      <Flex align="center" className="sl-Header">
        <H1 margin="16px">{t('title')}</H1>
      </Flex>
      <div className="external-address-panel">
        <div className="container-search">
          <InputGroup
            autoFocus
            className="external-address-panel-search-input"
            type="text"
            leftElement={<Svg src={SearchIconSvgURL} />}
            placeholder={t('placeHolder')}
            value={searchQuery}
            onInput={(e) => {
              handlerSearch((e.target as HTMLInputElement).value);
            }}
          />
        </div>
        <div className="select-area">
          <div className="title-area">{t('areaTitle').toUpperCase()}</div>
          <ReactSelect
            className="area-select-filter"
            isSearchable
            theme={undefined}
            selectedValue={areaValue}
            items={filterOptions.map((item) => ({
              value: item.v,
              label: item.dN,
            }))}
            inputValue={inputValue}
            styles={{
              control: (base) => ({
                ...base,
                minHeight: '32px !important',
              }),
            }}
            onKeyDown={(event) => {
              if (event.code === 'Enter') {
                event.preventDefault();

                setTimeout(() => {
                  (event.target as any).blur();
                });
              }
            }}
            onInputChange={(value, { action, prevInputValue }) => {
              switch (action) {
                case 'input-change':
                  setInputValue(value);
                  return inputValue;
                case 'input-blur':
                  const areaCode =
                    filterOptions.find((datum) => datum.dN === inputValue)?.v ||
                    inputValue;

                  setAreaValue(areaCode);
                  return inputValue;
                default:
                  return prevInputValue;
              }
            }}
            onItemSelect={(item: any) => {
              setAreaValue(item.value);
              setInputValue(item.label);
              setPage(1);
            }}
          />
        </div>
        <div className="sl-spacer" />
        <Button
          className="external-address-panel__action-button"
          intent="primary"
          icon={<Svg src={PlusIconSvgURL} />}
          onClick={() => setIsOpenDialog(true)}
        />
      </div>
      <ExternalAddressOverviewTable
        className="external-address-container"
        isLoading={isLoading}
        isLoadingSorting={store?.isLoadingSorting}
        results={isSuccess ? sdavData.items : []}
        rowsPerPage={rowsPerPage}
        page={page}
        total={isSuccess ? sdavData.total : 0}
        sortType={sortType}
        setPage={setPage}
        setRowsPerPage={setRowsPerPage}
        handleSort={handlerSort}
        onEdit={(data) => setEditSdav(data)}
        onRemove={(id) => setRemoveId(id)}
        onSelectedRowsChange={onSelectedRowsChange}
      />

      {isOpenDialog && (
        <CreateExternalAddressDialog
          isOpen
          onClose={() => setIsOpenDialog(false)}
          onSuccess={() => {
            alertSuccessfully(t('createSuccess'));
            refetchExternalAddresses();
          }}
          onError={() => alertError(t('createFailed'))}
          title={t('createExternalAddress')}
        />
      )}

      {editSdav != null && (
        <EditExternalAddressDialog
          isOpen
          defaultValue={editSdav}
          onClose={() => setEditSdav(null)}
          onSuccess={() => {
            alertSuccessfully(t('editSuccess'));
            refetchExternalAddresses();
          }}
          onError={() => alertError(t('editFailed'))}
          title={t('editExternalAddress')}
        />
      )}

      {removeId && (
        <InfoConfirmDialog
          isOpen
          type="secondary"
          isCloseButtonShown={false}
          cancelText={t('no')}
          confirmText={t('yesRemove')}
          title={t('removeExternalAddress')}
          onClose={() => setRemoveId(null)}
          onConfirm={() => {
            deleteSdavMutate({
              id: removeId,
            });
          }}
        >
          <BodyTextM>{t('removeCannotUndone')}</BodyTextM>
        </InfoConfirmDialog>
      )}
    </ExternalAddressStyleWrapper>
  );
};

export default memo(ExternalAddressOverview);
