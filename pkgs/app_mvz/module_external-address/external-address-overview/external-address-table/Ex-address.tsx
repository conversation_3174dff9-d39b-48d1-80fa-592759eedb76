import type { SdavCatalog } from '@tutum/hermes/bff/catalog_sdav_common';

import React, { memo, useState } from 'react';

import Table from '@tutum/design-system/components/Table';
import I18n from '@tutum/infrastructure/i18n';
import type ExternalAddress from '@tutum/mvz/locales/en/ExternalAddress.json';
import { SortField } from '@tutum/hermes/bff/catalog_sdav_common';
import { Order } from '@tutum/hermes/bff/common';

import { genColumns, customStyles } from '../ex-address-overview.helper';

export interface IExAddressOverviewTableProps {
  className?: string;
  results: any;
  isLoading: boolean;
  isLoadingSorting: boolean;
  rowsPerPage: number;
  page: number;
  total: number;
  setPage: (page: number) => void;
  setRowsPerPage: (rowsPerPage: number) => void;
  sortType: { field: SortField; order: Order };
  handleSort: (field: SortField, order: '' | Order) => void;
  onEdit: (data: SdavCatalog) => void;
  onRemove: (id: string) => void;
  onSelectedRowsChange?: (data: any) => void;
}

const ExternalAddressTable = ({
  className,
  results,
  isLoading,
  isLoadingSorting,
  page,
  total,
  rowsPerPage,
  setPage,
  setRowsPerPage,
  sortType,
  handleSort,
  onEdit,
  onRemove,
  onSelectedRowsChange,
}: IExAddressOverviewTableProps) => {
  const { t } = I18n.useTranslation<keyof typeof ExternalAddress.externalTable>(
    {
      namespace: 'ExternalAddress',
      nestedTrans: 'externalTable',
    }
  );

  const onChangePage = (page: number) => {
    setPage(page);
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPage(1);
    setRowsPerPage(currentRowsPerPage);
  };

  const [selectedRow, setSelectedRow] = useState<SdavCatalog | undefined>(undefined);
  const onSelectedRowChange = (item: SdavCatalog) => {
    setSelectedRow(item);
    onSelectedRowsChange?.(item);
  };
  return (
    <div className={className}>
      <Table
        className={`sl-table ${isLoadingSorting ? 'sl-loading-sorting' : ''}`}
        columns={genColumns(
          t,
          handleSort,
          onEdit,
          onRemove,
          sortType,
          onSelectedRowsChange ? { selectedRow, onSelectedRowChange } : undefined
        )}
        data={results}
        stickyLastColumn
        selectableRowsNoSelectAll={true}
        highlightOnHover
        customStyles={customStyles}
        progressPending={isLoading}
        paginationDefaultPage={page}
        noHeader
        persistTableHead
        pagination
        paginationPerPage={rowsPerPage}
        paginationServer
        paginationResetDefaultPage
        paginationTotalRows={total}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
      />
    </div>
  );
};

export default memo(ExternalAddressTable);
