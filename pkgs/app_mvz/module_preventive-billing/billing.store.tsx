import { proxy, useSnapshot } from 'valtio';
import { IStepDMPBilling } from './billing.type';
import {
  DMPBillingValidationModel,
  DataCenterInfo,
} from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import { YearQuarter } from '@tutum/hermes/bff/common';
import { BSNR } from '@tutum/hermes/bff/billing_history_common';
import { BillingValidationListModel } from '@tutum/hermes/bff/billing_edoku_common';
import {
  DocumentationOverview,
  FieldValidationResult,
} from '@tutum/hermes/bff/legacy/edmp_common';
import { PatientProfileResponse } from '@tutum/hermes/bff/app_mvz_patient_profile';

export interface ValidationListForValidation extends DMPBillingValidationModel {
  selectedDataCenter: DataCenterInfo;
}

export interface ILoading {
  billingSelection: boolean;
  troubleShootBtn: boolean;
  validationList: {
    table: boolean;
    reTroubleshoot: boolean;
  };
  checkValidation: boolean;
  billingHistory: boolean;
  isLoadingOpenEhksDialog: boolean;
}

export interface IOpenEHKSDocumentDialog {
  isOpen: boolean;
  isReadOnly: boolean;
  ableShowReopenWarn: boolean;
}

export interface IOpenDialog {
  undoBillingSubmission: boolean;
  sendViaKIM: boolean;
  prepareForShipping: boolean;
  downloadBillingFile: boolean;
  eHKSDocumentOverview: IOpenEHKSDocumentDialog;
}

export const TAB_VALUES = {
  OPEN_BILLING: 'open-billing',
  DISPATCH_LIST: 'dispatch-list',
};

export interface IPreventiveBilling {
  currentStep: IStepDMPBilling;
  caseNumber: string[];
  loading: ILoading;
  selectedQuarter: YearQuarter | null;
  selectedPractice: BSNR | null;
  selectedDocumentType: string | null;
  billOpenScheinFromPreQuarter: boolean;
  validationListData: BillingValidationListModel[];
  totalPatientInValidationList: number;
  dMPBillingHistoryId?: string;
  selectedTab: string;
  isOpenDialog: IOpenDialog;
  edokuDocumentationsOverview: DocumentationOverview[];
  patientProfilesMap: Record<string, PatientProfileResponse>;
  fieldValidationResultsByDocumentationId: Record<
    string,
    FieldValidationResult[]
  >;
  selectedEdokuDocumentIds: string[];
}

const initStore: IPreventiveBilling = {
  currentStep: IStepDMPBilling.BILLING_SELECTION,
  caseNumber: [],

  loading: {
    billingSelection: false,
    troubleShootBtn: false,
    validationList: {
      table: false,
      reTroubleshoot: false,
    },
    checkValidation: false,
    billingHistory: false,
    isLoadingOpenEhksDialog: false,
  },
  selectedQuarter: null,
  selectedPractice: null,
  selectedDocumentType: null,
  billOpenScheinFromPreQuarter: false,
  validationListData: [],
  totalPatientInValidationList: 0,
  dMPBillingHistoryId: '',
  selectedTab: TAB_VALUES.OPEN_BILLING,
  isOpenDialog: {
    undoBillingSubmission: false,
    sendViaKIM: false,
    prepareForShipping: false,
    downloadBillingFile: false,
    eHKSDocumentOverview: {
      isOpen: false,
      isReadOnly: false,
      ableShowReopenWarn: true,
    },
  },
  edokuDocumentationsOverview: [],
  patientProfilesMap: {},
  fieldValidationResultsByDocumentationId: {},
  selectedEdokuDocumentIds: [],
};

export const PreventiveBillingStore = proxy<IPreventiveBilling>(initStore);

export interface IPreventiveBillingActions {
  resetAll: () => void;
  setCurrentStep: (step: IStepDMPBilling) => void;
  setSelectedQuarter: (quarter: YearQuarter) => void;
  setSelectedPractice: (practice: BSNR) => void;
  setSelectedDocumentType: (documentType: string) => void;
  setBillOpenScheinFromPreQuarter: (
    billOpenScheinFromPreQuarter: boolean
  ) => void;
  updateValidationListData: (
    list: BillingValidationListModel[],
    totalPatient: number
  ) => void;
  setDMPBillingHistoryId: (id?: string) => void;
  setSelectedTab: (tab: string) => void;
  setOpenDialog: (openDialog: Partial<IOpenDialog>) => void;
  setEdokuDocumentationsOverview: (
    edokuDocumentationOverview: DocumentationOverview[]
  ) => void;
  setPatientProfilesMap: (
    patientProfilesMap: Record<string, PatientProfileResponse>
  ) => void;

  setFieldValidationResultsByDocumentationId: (
    fieldValidationResultsByDocumentationId: Record<
      string,
      Array<FieldValidationResult>
    >
  ) => void;
  setSelectedEdokuDocumentIds: (selectedEdokuDocumentIds: string[]) => void;
  setLoadingPreventiveBilling: (isLoadingObj: Partial<ILoading>) => void;
}

export const preventiveBillingActions: IPreventiveBillingActions = {
  resetAll: () => {
    PreventiveBillingStore.currentStep = IStepDMPBilling.BILLING_SELECTION;
    PreventiveBillingStore.selectedQuarter = null;
    PreventiveBillingStore.selectedPractice = null;
    PreventiveBillingStore.selectedDocumentType = null;
    PreventiveBillingStore.validationListData = [];
    PreventiveBillingStore.totalPatientInValidationList = 0;
    PreventiveBillingStore.dMPBillingHistoryId = '';
    PreventiveBillingStore.edokuDocumentationsOverview = [];
    PreventiveBillingStore.fieldValidationResultsByDocumentationId = {};
    PreventiveBillingStore.selectedEdokuDocumentIds = [];
  },
  setCurrentStep: (step: IStepDMPBilling) => {
    PreventiveBillingStore.currentStep = step;
  },
  setSelectedQuarter: (quarter: YearQuarter) => {
    PreventiveBillingStore.selectedQuarter = quarter;
  },
  setSelectedPractice: (practice: BSNR) => {
    PreventiveBillingStore.selectedPractice = practice;
  },
  setSelectedDocumentType: (documentType: string) => {
    PreventiveBillingStore.selectedDocumentType = documentType;
  },
  setBillOpenScheinFromPreQuarter: (billOpenScheinFromPreQuarter: boolean) => {
    PreventiveBillingStore.billOpenScheinFromPreQuarter =
      billOpenScheinFromPreQuarter;
  },
  updateValidationListData: (
    list: BillingValidationListModel[],
    totalPatient: number
  ) => {
    PreventiveBillingStore.validationListData = list;
    PreventiveBillingStore.totalPatientInValidationList = totalPatient;
  },
  setDMPBillingHistoryId: (id?: string) => {
    PreventiveBillingStore.dMPBillingHistoryId = id;
  },
  setSelectedTab: (tab: string) => {
    PreventiveBillingStore.selectedTab = tab;
  },
  setOpenDialog: (openDialog: Partial<IOpenDialog>) => {
    PreventiveBillingStore.isOpenDialog = {
      ...PreventiveBillingStore.isOpenDialog,
      ...openDialog,
    };
  },

  setEdokuDocumentationsOverview: (edokuDocumentationsOverview) => {
    PreventiveBillingStore.edokuDocumentationsOverview =
      edokuDocumentationsOverview;
  },
  setPatientProfilesMap: (patientProfilesMap) => {
    PreventiveBillingStore.patientProfilesMap = patientProfilesMap;
  },

  setFieldValidationResultsByDocumentationId: (
    fieldValidationResultsByDocumentationId
  ) => {
    PreventiveBillingStore.fieldValidationResultsByDocumentationId =
      fieldValidationResultsByDocumentationId;
  },
  setSelectedEdokuDocumentIds: (selectedEdokuDocumentIds) => {
    PreventiveBillingStore.selectedEdokuDocumentIds = selectedEdokuDocumentIds;
  },
  setLoadingPreventiveBilling: (isLoadingObj: Partial<ILoading>) => {
    PreventiveBillingStore.loading = {
      ...PreventiveBillingStore.loading,
      ...isLoadingObj,
    };
  },
};

export function usePreventiveBillingStore() {
  return useSnapshot(PreventiveBillingStore);
}
