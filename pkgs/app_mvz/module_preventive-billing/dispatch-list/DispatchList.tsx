import React, { memo, useState } from 'react';
import i18n from '@tutum/infrastructure/i18n';
import { useDebounce } from '@uidotdev/usehooks';
import {
  Flex,
  Svg,
  Tag,
  Button,
  SLTagState,
  BodyTextM,
  Avatar,
  Tooltip,
  LoadingState,
} from '@tutum/design-system/components';
import {
  InputGroup,
  Menu,
  MenuItem,
  Popover,
} from '@tutum/design-system/components/Core';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { SPACE_NUMBER } from '@tutum/design-system/styles';

import type Billingi18n from '@tutum/mvz/locales/en/Billing.json';
import {
  useQueryGetDispatchList,
} from '@tutum/hermes/bff/legacy/app_mvz_billing_edoku';
import {
  BillingHistoryModel,
  BillingStatus,
} from '@tutum/hermes/bff/legacy/billing_edoku_common';
import {
  CheckModuleStatus,
  EncryptionStatus,
} from '@tutum/hermes/bff/legacy/billing_history_common';
import moment from 'moment';
import { useListenBillingEDokuStatusChanged } from '@tutum/hermes/bff/app_mvz_billing_edoku';
import {
  preventiveBillingActions,
  usePreventiveBillingStore,
} from '../billing.store';
import DownloadBillingFile from '../dialog/download-billing-file';
const SearchIcon = '/images/search-sidebar-disable.svg';
const MoreIcon = '/images/more-vertical.svg';
import { useRouter } from 'next/router';
import { TabIds as TabIdsMailBox } from '@tutum/mvz/module_mailbox/Mailbox.type';
import GLOBAL_COLOR_TOKEN from '@tutum/design-system/themes/styles/color/global.color';
import { useOpenEhksDocuments } from '../hooks';

export interface DispatchListProps {
  className?: string;
}

const StatusTag = ({ status, mailId }: { status: string; mailId?: string }) => {
  const arrowUpRightIcon = '/images/arrow-up-right.svg';
  let statusTag: SLTagState = '';
  const router = useRouter();
  switch (status) {
    case BillingStatus.BillingStatus_Submitted:
    case CheckModuleStatus.CheckModuleStatus_Ok:
    case EncryptionStatus.EncryptionStatus_Success:
      statusTag = 'lightGreen';
      break;
    case BillingStatus.BillingStatus_Failed:
    case CheckModuleStatus.CheckModuleStatus_Incorrect:
    case BillingStatus.BillingStatus_Cancelled:
      statusTag = 'error';
      break;
    case BillingStatus.BillingStatus_Pending:
    case EncryptionStatus.EncryptionStatus_Pending:
      statusTag = 'warning';
      break;

    default:
      break;
  }

  if (statusTag === '') {
    return null;
  }

  const handleRedirectBaseOnStatus = (status: string, mailId?: string) => {
    if (!mailId) return;
    switch (status) {
      case BillingStatus.BillingStatus_Submitted:
        router.push(
          `/mailbox/kim?tab=${TabIdsMailBox.Outbox}&mailId=${mailId}`
        );
        break;
      case BillingStatus.BillingStatus_Failed:
        router.push(`/mailbox/kim?tab=${TabIdsMailBox.Inbox}&mailId=${mailId}`);
        break;
      default:
        return;
    }
  };

  return (
    <Tag
      slStyle="fill"
      slState={statusTag}
      onClick={() => handleRedirectBaseOnStatus(status, mailId)}
    >
      <Flex gap={4} align="center">
        {status.split('_').length > 1 ? status.split('_')[1] : status}
        {[
          BillingStatus.BillingStatus_Submitted,
          BillingStatus.BillingStatus_Failed,
        ].includes(status as BillingStatus) &&
          mailId && <Svg src={arrowUpRightIcon} />}
      </Flex>
    </Tag>
  );
};

const DispatchList = ({ className }: DispatchListProps) => {
  const { t } = i18n.useTranslation<keyof typeof Billingi18n.PreventiveBilling>(
    {
      namespace: 'Billing',
      nestedTrans: 'PreventiveBilling',
    }
  );

  const { loading } = usePreventiveBillingStore();
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchValue = useDebounce(searchValue, 300);

  const { data, isLoading, isSuccess, refetch } = useQueryGetDispatchList({
    query: debouncedSearchValue,
  });
  const historyIcon = '/images/history.svg';
  const eyeIcon = '/images/eye-on.svg';
  const undoIcon = '/images/undo-terminate-icon.svg';
  const downloadIcon = '/images/download.svg';
  const sendMailIcon = '/images/mail-add.svg';
  const [selectedDispatchBillingHistory, setSelectedDispatchBillingHistory] =
    useState<BillingHistoryModel | undefined>();

  const { isOpenDialog } = usePreventiveBillingStore();

  const {
    setOpenDialog,
    setSelectedQuarter,
    setSelectedPractice,
    setDMPBillingHistoryId,
    setSelectedEdokuDocumentIds,
  } = preventiveBillingActions;

  const { handleOpenEhksDocuments } = useOpenEhksDocuments();
  const getDispatchStatus = (
    billingHistory: BillingHistoryModel
  ): { mailId: string; messageColor: string; messageKey: string } => {
    let result = {
      messageColor: '',
      mailId: '',
      messageKey: '',
    };
    const { status, mailSendId, mailErrorId } = billingHistory;
    switch (status) {
      case BillingStatus.BillingStatus_Failed:
        result = {
          mailId: mailErrorId!,
          messageColor: GLOBAL_COLOR_TOKEN.red500,
          messageKey: 'sendViaKimFailMessage',
        };
        break;
      case BillingStatus.BillingStatus_Pending:
        result = {
          mailId: '',
          messageColor: GLOBAL_COLOR_TOKEN.yellow500,
          messageKey: 'sendViaKimPendingMessage',
        };
        break;
      case BillingStatus.BillingStatus_Submitted:
        result = {
          mailId: mailSendId!,
          messageColor: '',
          messageKey: '',
        };
        break;
      default:
        break;
    }
    return result;
  };

  const dispatchListActionsByStatus = (
    status: string,
    row: BillingHistoryModel
  ): React.JSX.Element[] => {
    const commonActions = [
      <MenuItem
        key="viewDocumentation"
        icon={<Svg src={eyeIcon} />}
        text={t('viewDocumentation')}
        onClick={() =>
          handleOpenEhksDocuments({
            edokuDocumentIds: row.edokuDocumentIds,
            isReadOnly: true,
            ableShowReopenWarn: false,
          })
        }
      />,
      <MenuItem
        key="downloadBillingFile"
        text={t('downloadBillingFile')}
        icon={<Svg src={downloadIcon} />}
        onClick={() => {
          setSelectedDispatchBillingHistory(row);
          setOpenDialog({
            downloadBillingFile: true,
          });
        }}
      />,
    ];
    const dispatchListActionsObject = {
      [BillingStatus.BillingStatus_Pending]: [...commonActions],
      [BillingStatus.BillingStatus_Failed]: [
        ...commonActions,
        <MenuItem
          key="undoBillingSubmission"
          icon={<Svg src={undoIcon} />}
          text={t('undoBillingSubmission')}
          onClick={() => {
            setDMPBillingHistoryId(row.id);
            setSelectedEdokuDocumentIds(row.edokuDocumentIds);
            setOpenDialog({
              undoBillingSubmission: true,
            });
          }}
        />,
      ],
      [BillingStatus.BillingStatus_Submitted]: [
        ...commonActions,
        <MenuItem
          key="re-sendViaKim"
          text={t('reSendViaKim')}
          icon={<Svg src={sendMailIcon} />}
          onClick={() => {
            setSelectedQuarter({
              quarter: row.quarter,
              year: row.year,
            });
            setSelectedPractice(row.bsnr);
            setDMPBillingHistoryId(row.id);
            setOpenDialog({
              sendViaKIM: true,
            });
          }}
        />,
      ],
      [BillingStatus.BillingStatus_Cancelled]: [
        <MenuItem
          key="viewDocumentation"
          icon={<Svg src={eyeIcon} />}
          text={t('viewDocumentation')}
          onClick={() =>
            handleOpenEhksDocuments({
              edokuDocumentIds: row.edokuDocumentIds,
              isReadOnly: true,
              ableShowReopenWarn: false,
            })
          }
        />,
      ],
      fullActions: [
        ...commonActions,
        <MenuItem
          key="sendViaKim"
          text={t('sendViaKim')}
          icon={<Svg src={sendMailIcon} />}
          onClick={() => {
            setSelectedQuarter({
              quarter: row.quarter,
              year: row.year,
            });
            setSelectedPractice(row.bsnr);
            setDMPBillingHistoryId(row.id);
            setOpenDialog({
              sendViaKIM: true,
            });
          }}
        />,
        <MenuItem
          key="undoBillingSubmission"
          icon={<Svg src={undoIcon} />}
          text={t('undoBillingSubmission')}
          onClick={() => {
            setDMPBillingHistoryId(row.id);
            setSelectedEdokuDocumentIds(row.edokuDocumentIds);
            setOpenDialog({
              undoBillingSubmission: true,
            });
          }}
        />,
      ],
    };
    return dispatchListActionsObject[status];
  };

  const columns: IDataTableColumn<BillingHistoryModel>[] = [
    {
      name: 'timePeriod',
      cell: (row) => {
        const isPreviousQuarter = row.isOpenPreviousQuarter;

        return (
          <Flex align="center" justify="space-between" flex={1} pl={8}>
            <BodyTextM>{`Q${row.quarter} ${row.year}`}</BodyTextM>
            {isPreviousQuarter && (
              <Tooltip
                content={t('billOpenScheinFromPreQuarter')}
                placement="top"
              >
                <Svg src={historyIcon} />
              </Tooltip>
            )}
          </Flex>
        );
      },
    },
    {
      name: 'practice',
      cell: (row) => row.bsnr.name,
    },
    {
      name: 'recipientKV',
      cell: (row) => {
        if (!row.recipientKv) return null;
        return <BodyTextM>{row.recipientKv}</BodyTextM>;
      },
    },
    {
      name: 'type',
      cell: (row) => {
        if (!row.dMPValue) return null;
        const kvType =
          `eHKS_${row.dMPValue}` as keyof typeof Billingi18n.PreventiveBilling;
        return <BodyTextM>{t(kvType)}</BodyTextM>;
      },
    },
    {
      name: 'checkModuleStatus',
      cell: (row) => <StatusTag status={row.checkModuleStatus!} />,
    },
    {
      name: 'encryptionStatus',
      cell: (row) => <StatusTag status={row.encryptionStatus!} />,
    },
    {
      name: 'dispatchStatus',
      cell: (row) => {
        const dispatchStatusObj = getDispatchStatus(row);
        return (
          <Flex column>
            <StatusTag status={row.status!} mailId={dispatchStatusObj.mailId} />
            {[
              BillingStatus.BillingStatus_Failed,
              BillingStatus.BillingStatus_Pending,
            ].includes(row.status!) && (
                <p
                  style={{
                    marginLeft: 4,
                    marginTop: 4,
                    color: dispatchStatusObj.messageColor,
                  }}
                >
                  {t(dispatchStatusObj.messageKey as keyof typeof Billingi18n.PreventiveBilling)}
                </p>
              )}
          </Flex>
        );
      },
    },
    {
      name: 'submittedBy',
      cell: (row) => {
        if (!row.submittedUser) return null;
        const { firstName, lastName } = row.submittedUser;
        return (
          <Tooltip content={`${firstName} ${lastName}`} placement="right-start">
            <Avatar
              className="avatar"
              initial={`${firstName} ${lastName}`}
              size="medium"
            />
          </Tooltip>
        );
      },
    },
    {
      name: 'submittedTime',
      cell: (row) => {
        if (!row.submittedAt) return null;
        return (
          <BodyTextM>
            {moment(row.submittedAt).format('DD.MM.YY [at] HH:mm')}
          </BodyTextM>
        );
      },
    },
    {
      name: 'actions',
      cell: (row) => {
        return (
          <Popover
            content={
              <Menu>
                {dispatchListActionsByStatus(row.status || 'fullActions', row)}
              </Menu>
            }
          >
            <Button
              onClick={() => { }}
              minimal
              iconOnly
              icon={<Svg size={16} src={MoreIcon} />}
            />
          </Popover>
        );
      },
      width: '100px',
      right: true,
    },
  ];

  const onSearch = (value: string) => {
    setSearchValue(value);
  };

  useListenBillingEDokuStatusChanged((data) => {
    refetch();
  });

  return (
    <div className={className}>
      <Flex m={SPACE_NUMBER.SPACE_S}>
        <InputGroup
          placeholder={t('search')}
          value={searchValue}
          onChange={(e) => onSearch(e.target.value)}
          leftElement={
            <Flex justify="center" pt={10}>
              <Svg src={SearchIcon} />
            </Flex>
          }
        />
      </Flex>
      {loading.isLoadingOpenEhksDialog && <LoadingState />}
      <Flex column className="sl__table">
        <Table
          noHeader
          data={isSuccess && data.billingHistories ? data.billingHistories : []}
          columns={columns}
          progressPending={isLoading}
          customStyles={{
            cells: {
              style: {
                alignItems: 'flex-start',
              },
            },
          }}
        />
      </Flex>
      {selectedDispatchBillingHistory && (
        <DownloadBillingFile
          isOpenDialog={isOpenDialog.downloadBillingFile}
          onCloseDialog={() => {
            setOpenDialog({ downloadBillingFile: false });
          }}
          billingHistory={selectedDispatchBillingHistory}
        />
      )}
    </div>
  );
};

export default memo(DispatchList);
