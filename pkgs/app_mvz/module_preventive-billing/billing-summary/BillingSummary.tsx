import Collapse from '@tutum/mvz/components/collapsev2';
import I18n from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import {
  preventiveBillingActions,
  usePreventiveBillingStore,
} from '../billing.store';
import { IStepDMPBilling } from '@tutum/mvz/module_dmp-billing/dmp.type';
import {
  Box,
  Flex,
  Link,
  LoadingState,
  Svg,
  H2,
} from '@tutum/design-system/components';
import { scaleSpace } from '@tutum/design-system/styles';
import Table from '@tutum/design-system/components/Table';
import { Button } from '@tutum/design-system/components';
import { useMemo } from 'react';
import {
  useMutationPrepareForShipping,
  useQueryGetBillingHistory,
} from '@tutum/hermes/bff/legacy/app_mvz_billing_edoku';
import PrepareForShippingDialog from '../dialog/prepare-for-shipping';
import { EIconType } from '../FileDownloader';
import FileDownloader from '../FileDownloader';
export interface Props {
  className?: string;
}

const BillingSummary = ({ className }: Props) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.PreventiveBilling>(
    {
      namespace: 'Billing',
      nestedTrans: 'PreventiveBilling',
    }
  );

  const { currentStep, dMPBillingHistoryId, isOpenDialog } =
    usePreventiveBillingStore();
  const { setOpenDialog } = preventiveBillingActions;
  const {
    data: billingHistoryRes,
    isLoading: isLoadingBillingHistory,
    isSuccess: isSuccessBillingHistory,
  } = useQueryGetBillingHistory(
    {
      billingHistoryId: dMPBillingHistoryId || '',
    },
    {
      enabled: !!dMPBillingHistoryId,
    }
  );

  const { mutate: onPrepareForShiping } = useMutationPrepareForShipping();

  const columns = useMemo(
    () => [
      {
        id: 'patientKv',
        width: '50%',
        minWidth: '200px',
        name: (
          <Flex justify="space-between" align="center">
            {t('recipientKv')}
          </Flex>
        ),
        selector: (row: any) => row.patientKv,
      },
      {
        id: 'type',
        width: '50%',
        minWidth: '200px',
        name: (
          <Flex justify="space-between" align="center">
            {t('type')}
          </Flex>
        ),
        selector: (row: any) => row.type,
      },
    ],
    []
  );

  if (isLoadingBillingHistory) {
    return <LoadingState />;
  }

  if (
    !isSuccessBillingHistory ||
    !billingHistoryRes ||
    !billingHistoryRes.billingHistory
  ) {
    return null;
  }

  const { billingHistory } = billingHistoryRes;

  const isActiveCollapse = currentStep === IStepDMPBilling.BILLING_SUMMARY;

  const viceTitle = billingHistory.transferLetterFileUrl ? (
    <div>
      <p className="viceTitle">{t('billingSummaryViceTitle')}</p>
    </div>
  ) : (
    <></>
  );

  const getRecipient = () => {
    const typeKey =
      `eHKS_${billingHistory.dMPValue}` as keyof typeof BillingI18n.PreventiveBilling;
    return [
      {
        patientKv: billingHistory.recipientKv || '',
        type: t(typeKey),
      },
    ];
  };
  const handlePrepareForShipping = () => { };
  return (
    <div className={className}>
      <Collapse
        title={
          <div className={`title ${isActiveCollapse ? 'active' : 'inactive'}`}>
            {t('stepSummary')}
          </div>
        }
        stepNumber={3}
        initialOpen={currentStep === IStepDMPBilling.BILLING_SUMMARY}
        isActive={currentStep === IStepDMPBilling.BILLING_SUMMARY}
        viceTitle={isActiveCollapse ? viceTitle : ''}
      >
        <Box className="sl-billing-summary-container">
          <Flex column gap={scaleSpace(4)}>
            <Flex column gap={scaleSpace(2)}>
              {(billingHistory.xPMResultFiles || []).map((file) => (
                <FileDownloader key={file.filePath} fileUrl={file.filePath} iconType={EIconType.PDF} />
              ))}
              {billingHistory.companionFileUrl && (
                <FileDownloader fileUrl={billingHistory.companionFileUrl} iconType={EIconType.IDX} />
              )}
              {billingHistory.zipFileUrl && (
                <FileDownloader fileUrl={billingHistory.zipFileUrl} iconType={EIconType.ZIP} />
              )}
              {billingHistory.xkmFileUrl && (
                <FileDownloader fileUrl={billingHistory.xkmFileUrl} iconType={EIconType.IDX} />
              )}
            </Flex>
            <Flex column gap={scaleSpace(1)}>
              <H2>{t('prinTransportLetter')}</H2>
              <p>{t('printTransportLetterDesc')}</p>
              {billingHistory.transferLetterFileUrl && (
                <FileDownloader fileUrl={billingHistory.transferLetterFileUrl} iconType={EIconType.PDF} />
              )}
            </Flex>
            <Flex column gap={scaleSpace(1)}>
              <H2>{t('labelDataCarrier')}</H2>
              <Box className="sl-label-data-carrier-box">
                <p>{t('labelDataCarrierDesc')}</p>
                <ul>
                  <li>{t('labelDataCarrierCategorySender')}</li>
                  <li>{t('labelDataCarrierCategoryRecipient')}</li>
                  <li>{t('labelDataCarrierCategoryCurNumber')}</li>
                  <li>{t('labelDataCarrierCategoryCreationDate')}</li>
                </ul>
              </Box>
            </Flex>
          </Flex>
          <Box className="sl-table-billing-summary">
            <Table columns={columns} data={getRecipient()} />
          </Box>
          <Flex className="button_section" justify="flex-end" mt={20} mx={16}>
            <Button
              minimal
              outlined
              intent="primary"
              large
              onClick={() => {
                setOpenDialog({
                  undoBillingSubmission: true,
                });
              }}
            >
              {t('undoBillingSubmission')}
            </Button>
            &nbsp;&nbsp;
            <Button
              minimal
              outlined
              intent="primary"
              large
              disabled={!billingHistory.recipient}
              onClick={() => {
                setOpenDialog({
                  sendViaKIM: true,
                });
              }}
            >
              {t('sendViaKim')}
            </Button>
            &nbsp;&nbsp;
          </Flex>
        </Box>
      </Collapse>
      {billingHistory && (
        <PrepareForShippingDialog
          isOpenDialog={isOpenDialog.prepareForShipping}
          onCloseDialog={() => {
            setOpenDialog({
              ...isOpenDialog,
              prepareForShipping: false,
            });
          }}
          billingHistory={billingHistory}
          handlePrepareForShipping={handlePrepareForShipping}
        />
      )}
    </div>
  );
};

export default BillingSummary;
