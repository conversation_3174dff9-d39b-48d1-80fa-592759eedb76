import {
  preventiveBillingActions,
  usePreventiveBillingStore,
} from '../billing.store';
import Collapse from '@tutum/mvz/components/collapsev2';
import { IStepDMPBilling } from '../billing.type';
import I18n from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import {
  alertSuccessfully,
  Box,
  Button,
  Flex,
  LoadingState,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import { customStyles, genColumns } from '../billing.helper';
import { useMemo } from 'react';
import { DMPValueEnum, DocumentStatus } from '@tutum/hermes/bff/edmp_common';
import {
  getEdokuDocumentByIds,
  useMutationCheckForValidation,
  useMutationCreateBilling,
} from '@tutum/hermes/bff/legacy/app_mvz_billing_edoku';
import { uniq } from 'lodash';
import { getPatientProfileByIds } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { PatientProfileResponse } from '@tutum/hermes/bff/app_mvz_patient_profile';
import { DMPBillingFieldsValidationResult } from '@tutum/hermes/bff/legacy/edmp_common';
import { useRouter } from 'next/router';
import { QuickFilter } from '@tutum/hermes/bff/app_mvz_patient_overview';
import { BillingValidationListModel } from '@tutum/hermes/bff/billing_edoku_common';
import { useOpenEhksDocuments } from '../hooks';
export interface Props {
  className?: string;
}

const ValidationList = ({ className }: Props) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.PreventiveBilling>(
    {
      namespace: 'Billing',
      nestedTrans: 'PreventiveBilling',
    }
  );

  const { t: tBilling } = I18n.useTranslation<keyof typeof BillingI18n.Billing>(
    {
      namespace: 'Billing',
      nestedTrans: 'Billing',
    }
  );

  const {
    validationListData,
    currentStep,
    loading,
    totalPatientInValidationList,
    selectedQuarter,
    selectedPractice,
    selectedEdokuDocumentIds,
    billOpenScheinFromPreQuarter,
  } = usePreventiveBillingStore();
  const router = useRouter();
  const {
    resetAll,
    setCurrentStep,
    setDMPBillingHistoryId,
    setOpenDialog,
    setEdokuDocumentationsOverview,
    setPatientProfilesMap,
    setFieldValidationResultsByDocumentationId,
    setSelectedEdokuDocumentIds,
  } = preventiveBillingActions;
  const onEditBillingSelection = () => {
    resetAll();
  };

  const { handleOpenEhksDocuments } = useOpenEhksDocuments();
  const { mutate: checkForValidation, isPending: isCheckingForValidation } =
    useMutationCheckForValidation();
  const { mutate: createBilling, isPending: isCreatingBilling } =
    useMutationCreateBilling();

  const onSelectRows = ({ selectedRows }) => {
    const selectedDocumentIds = selectedRows.map((row) => row.documentId);
    setSelectedEdokuDocumentIds(selectedDocumentIds);
  };

  const viceTitle = useMemo(() => {
    if (!validationListData) return undefined;
    const finishedDocument = validationListData.filter(
      (data) => data.status === DocumentStatus.DocumentStatus_Finished
    );
    if (finishedDocument.length === 0) return undefined;

    const finishedCount = finishedDocument.length;
    const notFinishCount = validationListData.length - finishedCount;
    return (
      <div>
        <p className="viceTitle">
          {finishedCount} {t('finished')},{' '}
          <span
            className="not-finish"
            onClick={() => {
              router.push(
                `/patient-overview?type=${QuickFilter.EHKSPatients_NotFinishedDocument}`
              );
            }}
          >
            {notFinishCount} {t('notfinished')}
          </span>{' '}
          {t('fromPatient', { count: totalPatientInValidationList })}
        </p>
      </div>
    );
  }, [validationListData]);

  const handleValidationError = async (
    dMPBillingFieldsValidationResults: DMPBillingFieldsValidationResult[]
  ) => {
    const edokuDocumentIdsFailValid = dMPBillingFieldsValidationResults.flatMap(
      (item) =>
        item && (item.fieldValidationResults || []).length
          ? item.documentId
          : []
    );
    if (!edokuDocumentIdsFailValid.length) {
      alertSuccessfully(t('validationSuccess'));
      return;
    }

    const fieldValidationResultsByDocumentationId =
      dMPBillingFieldsValidationResults.reduce((acc, cur) => {
        if (!cur?.fieldValidationResults?.length) return acc;
        return {
          ...acc,
          [cur.documentId]: cur.fieldValidationResults,
        };
      }, {});

    const edokuDocumentsFailValidResponse = await getEdokuDocumentByIds({
      documentIds: edokuDocumentIdsFailValid,
    });
    const { documents } = edokuDocumentsFailValidResponse.data;
    const patientIds = uniq(
      documents.flatMap((doc) => (doc.patientId ? doc.patientId : []))
    );
    const patientProfilesResponse = await getPatientProfileByIds({
      ids: patientIds,
    });
    const { patients } = patientProfilesResponse.data;
    const patientsMap = patients.reduce<Record<string, PatientProfileResponse>>(
      (acc, patient) => {
        return {
          ...acc,
          [patient.id]: patient,
        };
      },
      {}
    );
    setPatientProfilesMap(patientsMap);
    setEdokuDocumentationsOverview(documents);
    setFieldValidationResultsByDocumentationId(
      fieldValidationResultsByDocumentationId
    );
    setOpenDialog({
      eHKSDocumentOverview: {
        isOpen: true,
        isReadOnly: false,
        ableShowReopenWarn: true,
      },
    });
  };

  const onCheckForValidation = () => {
    if (!selectedQuarter) return;
    checkForValidation(
      {
        quarter: {
          year: selectedQuarter.year,
          quarter: selectedQuarter.quarter,
        },
        documentIds: selectedEdokuDocumentIds,
        bsnr: selectedPractice?.code || '',
      },
      {
        onSuccess: async (res) => {
          const { dMPBillingFieldsValidationResults, status } = res.data;
          if (!status) {
            await handleValidationError(dMPBillingFieldsValidationResults);
          } else {
            alertSuccessfully(t('validationSuccess'));
          }
        },
      }
    );
  };

  const onCreateBilling = () => {
    if (!selectedQuarter || !selectedPractice) return;
    createBilling(
      {
        quarter: selectedQuarter,
        documentIds: selectedEdokuDocumentIds,
        bsnr: selectedPractice.code,
        dMPValue: DMPValueEnum.DMPValueEnum_EDO_SkinCancer,
        isOpenPreviousQuarter: billOpenScheinFromPreQuarter,
      },
      {
        onSuccess: (res) => {
          const { dMPBillingHistoryId } = res.data;
          setDMPBillingHistoryId(dMPBillingHistoryId);
          setCurrentStep(IStepDMPBilling.BILLING_SUMMARY);
        },
      }
    );
  };
  const isActiveCollapse = [
    IStepDMPBilling.BILLING_SUMMARY,
    IStepDMPBilling.VALIDATION_LIST,
  ].includes(currentStep);

  return (
    <div className={className}>
      <Collapse
        title={
          <div className={`title ${isActiveCollapse ? 'active' : 'inactive'}`}>
            {t('stepValidateList')}
          </div>
        }
        viceTitle={isActiveCollapse ? viceTitle : ''}
        stepNumber={2}
        initialOpen={currentStep === IStepDMPBilling.VALIDATION_LIST}
        isActive={isActiveCollapse}
      >
        <Box className="sl-table-validation-list">
          <Table
            columns={genColumns({
              t,
              tBilling,
            })}
            customStyles={customStyles}
            selectableRows
            onSelectedRowsChange={onSelectRows}
            selectableRowDisabled={(row: BillingValidationListModel) =>
              row.status === DocumentStatus.DocumentStatus_Saved
            }
            onRowClicked={(row: BillingValidationListModel) => {
              handleOpenEhksDocuments({
                edokuDocumentIds: [row.documentId],
                isReadOnly:
                  row.status === DocumentStatus.DocumentStatus_Finished,
                ableShowReopenWarn: true,
              });
            }}
            data={validationListData}
            noHeader
            fixedHeader
            sortServer
          />
        </Box>
        {loading.isLoadingOpenEhksDialog && <LoadingState />}
        <Flex className="button_section" justify="flex-end" mt={20} mx={16}>
          <Button
            minimal
            outlined
            intent="primary"
            large
            loading={loading.checkValidation}
            onClick={onEditBillingSelection}
          >
            {t('editBillingSelection')}
          </Button>
          &nbsp;&nbsp;
          <Button
            minimal
            outlined
            intent="primary"
            large
            loading={isCheckingForValidation}
            disabled={!selectedQuarter || selectedEdokuDocumentIds.length === 0}
            onClick={onCheckForValidation}
          >
            {t('checkForValidation')}
          </Button>
          &nbsp;&nbsp;
          <Button
            intent="primary"
            large
            loading={isCreatingBilling}
            disabled={
              !selectedQuarter ||
              !selectedPractice ||
              selectedEdokuDocumentIds.length === 0
            }
            onClick={onCreateBilling}
          >
            {t('createBilling')}
          </Button>
        </Flex>
      </Collapse>
    </div>
  );
};

export default ValidationList;
