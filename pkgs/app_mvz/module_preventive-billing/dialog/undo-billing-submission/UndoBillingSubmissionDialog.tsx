import { Box, Flex, Svg } from '@tutum/design-system/components';
import { Classes, Dialog } from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { Button } from '@tutum/design-system/components';
import { useMutationUndoSubmission } from '@tutum/hermes/bff/legacy/app_mvz_billing_edoku';
import {
  preventiveBillingActions,
  TAB_VALUES,
  usePreventiveBillingStore,
} from '../../billing.store';
import { IStepDMPBilling } from '../../billing.type';
import { useMutationUpdateStatusEdokuDocumentByIds } from '@tutum/hermes/bff/legacy/app_mvz_edoku';
import { DocumentStatus } from '@tutum/hermes/bff/legacy/edmp_common';

export interface IUndoBillingSubmissionDialogProps {
  className?: string;
  isOpenDialog: boolean;
  onCloseDialog: () => void;
}

const UndoBillingSubmissionDialog = (
  props: IUndoBillingSubmissionDialogProps
) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.PreventiveBilling>(
    {
      namespace: 'Billing',
      nestedTrans: 'PreventiveBilling',
    }
  );
  const { isOpenDialog, onCloseDialog, className } = props;
  const { dMPBillingHistoryId, selectedTab, selectedEdokuDocumentIds } =
    usePreventiveBillingStore();
  const { setCurrentStep, setSelectedEdokuDocumentIds } =
    preventiveBillingActions;
  const alertIcon = '/images/alert-circle-solid.svg';

  const { mutateAsync: onUndoBillingSubmission } = useMutationUndoSubmission();
  const { mutateAsync: onUpdateStatusEdokuDocuments } =
    useMutationUpdateStatusEdokuDocumentByIds();

  const handleUndoBillingSubmission = async () => {
    try {
      await onUndoBillingSubmission({ billingHistoryId: dMPBillingHistoryId! });
      await onUpdateStatusEdokuDocuments({
        documentIds: selectedEdokuDocumentIds,
        status: DocumentStatus.DocumentStatus_Saved,
      });
      if (selectedTab === TAB_VALUES.OPEN_BILLING) {
        setCurrentStep(IStepDMPBilling.BILLING_SELECTION);
      }
      onCloseDialog();
      setSelectedEdokuDocumentIds([]);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Dialog
      title={t('undoBillingSubmissionTitle')}
      isOpen={isOpenDialog}
      icon={<Svg src={alertIcon} className="header-icon" />}
      onClose={onCloseDialog}
      canOutsideClickClose={false}
      className={className}
    >
      <Flex column className={Classes.DIALOG_BODY}>
        <Flex>{t('undoBillingSubmissionContentLine1')}</Flex>
        <Flex mt={'32px'}>{t('undoBillingSubmissionContentLine2')}</Flex>
      </Flex>
      <Flex justify="space-between" className={Classes.DIALOG_FOOTER}>
        <Box w="46%">
          <Button fill outlined onClick={onCloseDialog} large>
            {t('undoBillingSubmissionNo')}
          </Button>
        </Box>
        <Box w="46%">
          <Button
            fill
            onClick={handleUndoBillingSubmission}
            intent="danger"
            large
          >
            {t('undoBillingSubmissionYes')}
          </Button>
        </Box>
      </Flex>
    </Dialog>
  );
};
export default UndoBillingSubmissionDialog;
