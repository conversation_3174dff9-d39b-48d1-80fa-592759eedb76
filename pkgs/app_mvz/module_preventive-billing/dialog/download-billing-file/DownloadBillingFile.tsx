import {
  Box,
  Button,
  Flex,
  H3,
} from '@tutum/design-system/components';
import { Classes, Dialog } from '@tutum/design-system/components/Core';
import { scaleSpace } from '@tutum/design-system/styles';
import { BillingHistoryModel } from '@tutum/hermes/bff/legacy/billing_edoku_common';
import I18n from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { EIconType } from '../../FileDownloader';
import FileDownloader from '../../FileDownloader';

export interface DownloadBillingFileProps {
  className?: string;
  isOpenDialog: boolean;
  onCloseDialog: () => void;
  billingHistory: BillingHistoryModel;
}

const DownloadBillingFile = (props: DownloadBillingFileProps) => {
  const { className, isOpenDialog, onCloseDialog, billingHistory } = props;
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.PreventiveBilling>(
    {
      namespace: 'Billing',
      nestedTrans: 'PreventiveBilling',
    }
  );
  return (
    <Dialog
      className={className}
      isOpen={isOpenDialog}
      onClose={onCloseDialog}
      canOutsideClickClose={false}
      title={t('downloadBillingFiles')}
      isCloseButtonShown={false}
    >
      <Flex gap={scaleSpace(4)} column className={Classes.DIALOG_BODY}>
        <Flex
          gap={scaleSpace(2)}
          column
          className={'sl-download-billing-desc-box'}
        >
          <p>{t('downloadBillingDesc')}</p>
          {(billingHistory.xPMResultFiles || []).map((file) => {
            return (
              <FileDownloader key={file.fileName} fileUrl={file.filePath} iconType={EIconType.PDF} />
            )
          })}
          {billingHistory.companionFileUrl && (
            <FileDownloader fileUrl={billingHistory.companionFileUrl} iconType={EIconType.IDX} />
          )}
          {billingHistory.zipFileUrl && (
            <FileDownloader fileUrl={billingHistory.zipFileUrl} iconType={EIconType.ZIP} />
          )}
          {billingHistory.xkmFileUrl && (
            <FileDownloader fileUrl={billingHistory.xkmFileUrl} iconType={EIconType.IDX} />
          )}
        </Flex>

        <Flex gap={scaleSpace(2)} column className="sl-print-transport-letter">
          <H3>{t('prinTransportLetter')}</H3>
          <p>{t('printTransportLetterDesc')}</p>
          {billingHistory.transferLetterFileUrl && (
            <FileDownloader fileUrl={billingHistory.transferLetterFileUrl} iconType={EIconType.PDF} />
          )}
        </Flex>
        <Flex gap={scaleSpace(2)} column>
          <H3>{t('prinTransportLetter')}</H3>
          <Box className="sl-label-data-carrier-box">
            <p style={{ marginBottom: 8 }}>{t('labelDataCarrierDesc')}</p>
            <ul>
              <li>{t('labelDataCarrierCategorySender')}</li>
              <li>{t('labelDataCarrierCategoryRecipient')}</li>
              <li>{t('labelDataCarrierCategoryCurNumber')}</li>
              <li>{t('labelDataCarrierCategoryCreationDate')}</li>
            </ul>
          </Box>
        </Flex>
      </Flex>
      <Flex flex={1} className={Classes.DIALOG_FOOTER}>
        <Button outlined onClick={onCloseDialog} intent="primary" large>
          {t('close')}
        </Button>
      </Flex>
    </Dialog>
  );
};

export default DownloadBillingFile;
