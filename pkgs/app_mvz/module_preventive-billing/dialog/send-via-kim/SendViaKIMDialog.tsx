import {
  alertSuccessfully,
  Box,
  Flex,
  Label,
  ReactSelect,
} from '@tutum/design-system/components';
import {
  Classes,
  Dialog,
  FormGroup,
  InputGroup,
} from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { Button } from '@tutum/design-system/components';
import { Field, Form, Formik } from 'formik';
import {
  preventiveBillingActions,
  TAB_VALUES,
  usePreventiveBillingStore,
} from '@tutum/mvz/module_preventive-billing/billing.store';
import { useQueryGetKimAccounts } from '@tutum/hermes/bff/legacy/app_mvz_mail';
import { useMutationSendMail } from '@tutum/hermes/bff/legacy/app_mvz_billing_edoku';
import { useEffect } from 'react';

const fieldsName = {
  kimAccount: 'kimAccount',
  testMailTo: 'testMailTo',
  quarter: 'quarter',
  practice: 'practice',
};
export interface ISendViaKIMDialogProps {
  className?: string;
  isOpenDialog: boolean;
  onCloseDialog: () => void;
}

export interface ISendViaKimFormProps {
  kimAccount: string;
  testMailTo: string;
}

export interface ISelectOption {
  value: string;
  label: string;
}

const SendViaKIMDialog = (props: ISendViaKIMDialogProps) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.PreventiveBilling>(
    {
      namespace: 'Billing',
      nestedTrans: 'PreventiveBilling',
    }
  );

  const { isOpenDialog, onCloseDialog, className } = props;
  const { selectedQuarter, selectedPractice, dMPBillingHistoryId } =
    usePreventiveBillingStore();
  const { setSelectedTab, resetAll } = preventiveBillingActions;

  const bsnrCodes: string[] = [];
  if (selectedPractice?.code) {
    bsnrCodes.push(selectedPractice.code);
  }

  const { data: kimAccounts } = useQueryGetKimAccounts(
    {
      bsnrCodes,
    },
    {
      enabled: bsnrCodes.length > 0,
    }
  );
  const { mutate: sendMail, isPending: isLoadingSendMail } =
    useMutationSendMail({
      onSuccess: () => {
        onCloseDialog();
        resetAll();
        setSelectedTab(TAB_VALUES.DISPATCH_LIST);
        alertSuccessfully(t('sendViaKimSubmitted'));
      },
    });

  const getKimAccountsOptions = () => {
    if (!kimAccounts || !kimAccounts.accounts) {
      return [];
    }

    return kimAccounts.accounts.map((kimAccount) => ({
      label: kimAccount.email,
      value: kimAccount.id,
    }));
  };

  const quarterOptions: readonly ISelectOption[] = [
    {
      label: `${selectedQuarter?.quarter}.${selectedQuarter?.year}`,
      value: `${selectedQuarter?.quarter}.${selectedQuarter?.year}`,
    },
  ];

  const practiceOptions: readonly ISelectOption[] = [
    {
      label: `${selectedPractice?.code}-${selectedPractice?.name}`,
      value: `${selectedPractice?.code}-${selectedPractice?.name}`,
    },
  ];

  const handleSendViaKIM = (values: ISendViaKimFormProps) => {
    sendMail({
      billingHistoryId: dMPBillingHistoryId!,
      senderMailSettingId: values.kimAccount,
      testMailTo: values.testMailTo,
    });
  };

  return (
    <Dialog
      title={t('sendViaKim')}
      isOpen={isOpenDialog}
      onClose={onCloseDialog}
      canOutsideClickClose={false}
      className={className}
    >
      <Formik
        initialValues={{
          kimAccount: '',
          testMailTo: '',
        }}
        onSubmit={handleSendViaKIM}
      >
        {({ values }) => (
          <Form>
            <Flex column className={Classes.DIALOG_BODY}>
              <Flex flex={1}>
                <Field name={fieldsName.quarter}>
                  {({ field }) => {
                    return (
                      <FormGroup>
                        <Label
                          htmlFor={fieldsName.quarter}
                          name={fieldsName.quarter}
                          required
                        >
                          {t('sendViaKIMQuaterLabel')}
                        </Label>
                        <ReactSelect
                          name={fieldsName.quarter}
                          {...field}
                          isSearchable={false}
                          options={quarterOptions}
                          selectedValue={quarterOptions[0].value}
                          id={fieldsName.quarter}
                          instanceId={fieldsName.quarter}
                          className="sl-send-via-kim__select-quarter"
                          isDisabled={true}
                        />
                      </FormGroup>
                    );
                  }}
                </Field>
              </Flex>
              <Flex flex={1}>
                <Field name={fieldsName.practice}>
                  {({ field }) => {
                    return (
                      <FormGroup>
                        <Label
                          htmlFor={fieldsName.practice}
                          name={fieldsName.practice}
                          required
                        >
                          {t('sendViaKIMPracticeLabel')}
                        </Label>
                        <ReactSelect
                          name={fieldsName.practice}
                          {...field}
                          options={practiceOptions}
                          selectedValue={practiceOptions[0].value}
                          isSearchable={false}
                          id={fieldsName.practice}
                          instanceId={fieldsName.practice}
                          className="sl-send-via-kim__select-practice"
                          isDisabled={true}
                        />
                      </FormGroup>
                    );
                  }}
                </Field>
              </Flex>
              <Flex flex={1}>
                <Field name={fieldsName.kimAccount}>
                  {({ field, form }) => {
                    useEffect(() => {
                      if (kimAccounts?.accounts?.length === 1) {
                        form.setFieldValue(
                          field.name,
                          kimAccounts.accounts[0].id
                        );
                      }
                    }, [kimAccounts]);
                    return (
                      <FormGroup>
                        <Label
                          htmlFor={fieldsName.kimAccount}
                          name={fieldsName.kimAccount}
                          required
                        >
                          {t('sendViaKIMAccountLabel')}
                        </Label>
                        <ReactSelect
                          name={fieldsName.kimAccount}
                          {...field}
                          isSearchable={false}
                          id={fieldsName.kimAccount}
                          instanceId={fieldsName.kimAccount}
                          className="sl-send-via-kim__select-kimAccount"
                          items={getKimAccountsOptions()}
                          onItemSelect={(item) => {
                            form.setFieldValue(field.name, item.value);
                          }}
                          selectedValue={field.value}
                        />
                      </FormGroup>
                    );
                  }}
                </Field>
              </Flex>
              <Flex flex={1} mb={'8px'}>
                <Field name={fieldsName.testMailTo}>
                  {({ field }) => {
                    return (
                      <FormGroup>
                        <Label
                          htmlFor={fieldsName.testMailTo}
                          name={fieldsName.testMailTo}
                        >
                          {t('sendViaKIMTestMailToLabel')}
                        </Label>
                        <InputGroup
                          id={fieldsName.testMailTo}
                          name={fieldsName.testMailTo}
                          {...field}
                          className="sl-send-via-kim__input-testMailTo"
                        />
                      </FormGroup>
                    );
                  }}
                </Field>
              </Flex>
            </Flex>
            <Flex
              justify="flex-end"
              mt={'12px'}
              className={Classes.DIALOG_FOOTER}
            >
              <Box mr={'16px'}>
                <Button outlined onClick={onCloseDialog} intent="primary" large>
                  {t('cancel')}
                </Button>
              </Box>
              <Box>
                <Button
                  intent="primary"
                  large
                  type="submit"
                  loading={isLoadingSendMail}
                  disabled={values.kimAccount === ''}
                >
                  {t('sendViaKimBtn')}
                </Button>
              </Box>
            </Flex>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default SendViaKIMDialog;
