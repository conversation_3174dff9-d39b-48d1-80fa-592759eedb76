import React, { memo } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import Collapse from '@tutum/mvz/components/collapsev2';
import { Button, Flex, Svg, Tooltip } from '@tutum/design-system/components';
import { Divider } from '@tutum/design-system/components/Core';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import { IStepDMPBilling } from '../billing.type';
import {
  QuarterSection,
  PracticeSection,
  DocumentTypeSection,
} from './Components';
import { useSelectionData } from './hook';
import { useMutationGetValidationList } from '@tutum/hermes/bff/legacy/app_mvz_billing_edoku';
import { preventiveBillingActions } from '../billing.store';

// Main Component
export interface Props {
  className?: string;
}

const HISTORY_ICON_URL = '/images/history.svg';

const BillingSelection = ({ className }: Props) => {
  const { t } = I18n.useTranslation<keyof typeof BillingI18n.PreventiveBilling>(
    {
      namespace: 'Billing',
      nestedTrans: 'PreventiveBilling',
    }
  );

  const {
    currentStep,
    loading,
    selectedQuarter,
    selectedPractice,
    selectedDocumentType,
    billOpenScheinFromPreQuarter,
    selectionData,
    setCurrentStep,
    setSelectedQuarter,
    setSelectedPractice,
    setSelectedDocumentType,
    setBillOpenScheinFromPreQuarter,
  } = useSelectionData();

  const { updateValidationListData } = preventiveBillingActions;

  const { mutateAsync: getValidationList, isPending } =
    useMutationGetValidationList();

  const startTroubleshooting = async () => {
    try {
      const response = await getValidationList({
        bsnrId: selectedPractice?.id!,
        quarter: selectedQuarter!,
        documentType: selectedDocumentType!,
        openPreviousQuarter: billOpenScheinFromPreQuarter,
      });
      setCurrentStep(IStepDMPBilling.VALIDATION_LIST);
      updateValidationListData(
        response.data?.billingValidationList || [],
        response.data?.totalPatient || 0
      );
    } catch (error) {
      console.error(error);
    }
  };

  if (!selectionData) return null;
  return (
    <div className={className}>
      <Collapse
        title={<div className={`title`}>{t('stepBillingSelect')}</div>}
        viceTitle={
          <div>
            <p className="viceTitle">
              <b>
                {selectedQuarter?.quarter && (
                  <>
                    Q{selectedQuarter?.quarter}.{selectedQuarter?.year}{' '}
                  </>
                )}
              </b>
              {billOpenScheinFromPreQuarter && (
                <Tooltip
                  content={t('billOpenScheinFromPreQuarter')}
                  placement="top"
                >
                  <Svg src={HISTORY_ICON_URL} />
                </Tooltip>
              )}
              {selectedPractice?.name ? ` - ${selectedPractice?.name}` : ''}
            </p>
          </div>
        }
        stepNumber={1}
        initialOpen={currentStep === IStepDMPBilling.BILLING_SELECTION}
        isActive={true}
      >
        <Flex className="content">
          <QuarterSection
            quarters={selectionData.quarters}
            selectedQuarter={selectedQuarter}
            onQuarterSelect={setSelectedQuarter}
            billOpenScheinFromPreQuarter={billOpenScheinFromPreQuarter}
            onTogglePreQuarter={() =>
              setBillOpenScheinFromPreQuarter(!billOpenScheinFromPreQuarter)
            }
          />
          <PracticeSection
            practices={selectionData.bsnrs}
            selectedPractice={selectedPractice}
            onPracticeSelect={setSelectedPractice}
          />
          <DocumentTypeSection
            documentTypes={selectionData.documentTypes}
            selectedDocumentType={selectedDocumentType}
            onDocumentTypeSelect={setSelectedDocumentType}
          />
        </Flex>
        <Flex
          className="button_troubleshooting"
          mx={16}
          justify="flex-end"
          mb={16}
        >
          <Button
            intent="primary"
            onClick={startTroubleshooting}
            loading={isPending}
            large
            disabled={
              !selectedPractice || !selectedQuarter || !selectedDocumentType
            }
          >
            {t('troubleshooting')}
          </Button>
        </Flex>
      </Collapse>
    </div>
  );
};

export default memo(BillingSelection);
