import React, { forwardRef, Ref } from 'react';
import { FormikProps } from 'formik';

import type BGBillingI18n from '@tutum/mvz/locales/en/BGBilling.json';
import {
  type Doctor,
  type BillingStatus,
} from '@tutum/hermes/bff/bg_billing_common';

import { FilterGroup, FilterItem } from '@tutum/mvz/components/filter-group';
import I18n from '@tutum/infrastructure/i18n';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { InsuranceInfo } from '@tutum/hermes/bff/patient_profile_common';

export interface FilterModalProps {
  className?: string;
  onChange: (values) => void;
  doctorList: Doctor[];
  tCommon: IFixedNamespaceTFunction<any>;
  bgInsurances: InsuranceInfo[];
  listStatus: BillingStatus[];
  rangeAmount: {
    min: number;
    max: number;
  };
}

const FilterModal = forwardRef(
  ({
    className,
    onChange,
    bgInsurances,
    doctorList,
    listStatus,
    rangeAmount,
  }: FilterModalProps, formRef: Ref<FormikProps<any>>) => {
    const { t } = I18n.useTranslation<keyof typeof BGBillingI18n.FilterModal>({
      namespace: 'BGBilling',
      nestedTrans: 'FilterModal',
    });

    const items: FilterItem[] = [
      {
        type: 'MULTIPLE_SELECT',
        fieldLabel: t('doctor'),
        fieldName: 'doctorIds',
        options: doctorList.map((doctor) => ({
          label: doctor.fullName,
          value: doctor.doctorId,
        })),
        value: [],
      },
      {
        type: 'MULTIPLE_SELECT',
        fieldLabel: t('insurance'),
        fieldName: 'ikNumber',
        options: bgInsurances.map((insurance) => ({
          label: insurance.insuranceCompanyName,
          value: +insurance.ikNumber,
        })),
        value: [],
      },
      {
        type: 'MULTIPLE_SELECT',
        fieldLabel: t('status'),
        fieldName: 'status',
        options: listStatus.map((status) => ({
          label: status,
          value: status,
        })),
        value: [],
      },
      {
        type: 'PRICE_RANGE',
        fieldLabel: t('amount'),
        fieldName1: 'minPrice',
        fieldName2: 'maxPrice',
        min: rangeAmount.min,
        max: rangeAmount.max,
        value: { left: 1, right: rangeAmount.max },
      },
    ];

    return (
      <FilterGroup
        ref={formRef}
        className={className}
        items={items}
        displayStyle="Popover"
        onChange={onChange}
      />
    );
  }
);

export default FilterModal;
