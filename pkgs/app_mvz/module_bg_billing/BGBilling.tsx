import React, { useContext, useEffect, useState, useMemo, useRef } from 'react';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type BGBillingI18n from '@tutum/mvz/locales/en/BGBilling.json';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';

import {
  alertError,
  alertSuccessfully,
  BodyTextL,
  BodyTextM,
  Button,
  Flex,
  H1,
  InfoConfirmDialog,
  LoadingState,
  Svg,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import Table from '@tutum/design-system/components/Table';
import PrintInvoiceDialog, {
  PrintInvoiceType,
  SELECT_TYPE,
} from './PrintInvoiceDialog';
import { BG_ACTION, genColumns, submitLetterForBgBilling } from './helpers';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MainGroup, PaginationRequest } from '@tutum/hermes/bff/common';
import { PAGINATION_DEFAULT } from '@tutum/design-system/consts/table';
import { Divider, InputGroup } from '@tutum/design-system/components/Core';
import {
  useQueryGetBgBilling,
  useMutationMarkBgBillingCancelled,
  useMutationMarkBgBillingPaid,
  useMutationMarkBgBillingUnpaid,
  useQueryGetPrintedInvoices,
  useQueryGetListDoctor,
  useQueryGetListInsurance,
  useQueryGetListStatus,
  useQueryGetRangeAmount,
} from '@tutum/hermes/bff/legacy/bg_billing';
import {
  BgBillingItem,
  BgBillingFilter,
} from '@tutum/hermes/bff/legacy/bg_billing_common';
import { DoctorLetterCreateEditDialog } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog';
import {
  BulkBillingPayload,
  printPreviewPdfActions,
  usePrintPreviewPdfStore,
} from '@tutum/mvz/hooks/usePrintPreviewPdf.store';
import {
  getPdfPresignedUrl,
  useMutationHandleBgInvoice,
  useMutationGetPdfPresignedUrl,
  useMutationHandleBgInvoicByForm,
} from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import { useQueryGetTimelineEntryByIds } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import {
  musterFormDialogActions,
  useMusterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { musterFormActions } from '@tutum/mvz/module_form/muster-form/musterForm.store';
import { FormName, FormType } from '@tutum/hermes/bff/form_common';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import {
  PatientProfileResponse,
  useQueryGetPatientProfileById,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { useQueryGetScheinItemById } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import { useQueryGroupByQuarter } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { formOverviewActions } from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import useStateCallbackInline from '@tutum/mvz/hooks/useStateWithCallBackInline';
import {
  getTimelineServiceEntries,
  MAXIMUM_UV_GOA_SERVICE_F9990,
} from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import { formTranslationActions } from '@tutum/mvz/hooks/useFormTranslation';
import { useGetTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import AccountManagementUtil from '@tutum/infrastructure/utils/form.util';
import { useQueryGetForms } from '@tutum/hermes/bff/legacy/app_mvz_form';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { useDoctorLetterTimelineStore } from '@tutum/mvz/module_doctor-letter/DoctorLetterTimeline.store';
import { DoctorLetterExpanded } from '@tutum/mvz/_utils/doctorLetter.type';
import { BillingStatus } from '@tutum/hermes/bff/bg_billing_common';
import FilterModal from './FilterModal';

const SearchIcon = '/images/search-sidebar-disable.svg';

export interface BGBillingProps {
  className?: string;
}

const BGBilling = ({ className }: BGBillingProps) => {
  const { t } = I18n.useTranslation<keyof typeof BGBillingI18n>({
    namespace: 'BGBilling',
  });

  const { t: tTable } = I18n.useTranslation<keyof typeof BGBillingI18n.Table>({
    namespace: 'BGBilling',
    nestedTrans: 'Table',
  });

  const { t: tCancelConfirmDialog } = I18n.useTranslation<
    keyof typeof BGBillingI18n.CancelConfirmDialog
  >({
    namespace: 'BGBilling',
    nestedTrans: 'CancelConfirmDialog',
  });

  const { t: tCommonInput } = I18n.useTranslation<
    keyof typeof CommonLocales.Input
  >({
    namespace: 'Common',
    nestedTrans: 'Input',
  });

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { t: tDoctorLetter } = I18n.useTranslation({
    namespace: 'DoctorLetter',
  });

  const { t: tConfirmMaximumUVGoaF9990 } = I18n.useTranslation<
    keyof typeof FormI18n.confirmMaximumUVGoaF9990
  >({
    namespace: 'Form',
    nestedTrans: 'confirmMaximumUVGoaF9990',
  });

  const { t: tForm } = I18n.useTranslation({
    namespace: 'Form',
  });

  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();
  const musterFormDialogStore = useMusterFormDialogStore();
  const getForms = useQueryGetForms({
    oKV: '',
    ikNumber: 0,
    contractId: '',
    chargeSystemId: '',
    scheinMainGroup: MainGroup.BG,
  });

  const [searchValue, setSearchValue] = useState<string>('');
  const [isClearSelected, setClearSelected] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<BgBillingItem | undefined>(
    undefined
  );
  const [selectedRows, setSelectedRows] = useState<BgBillingItem[]>([]);
  const [currentAction, setCurrentAction] = useState<BG_ACTION>('');
  const [prescribeId, setPrescribeId] = useState<string>('');
  const [filter, setFilter] = useState<BgBillingFilter | undefined>(undefined);
  const [pagination, setPagination] =
    useState<PaginationRequest>(PAGINATION_DEFAULT);
  const formRef = useRef(null);

  const [openConfirmMaximumUVGoaF9990, setOpenConfirmMaximumUVGoaF9990] =
    useStateCallbackInline(false);

  const { activeBilling, invoiceEntry } = useDoctorLetterTimelineStore();

  const getTreatmentDoctorWithBsnr = useGetTreatmentDoctorWithBsnr();

  const getAllBgBilling = useQueryGetBgBilling({
    search: searchValue,
    pagination,
    filter: filter,
  });

  const getPatientProfileById = useQueryGetPatientProfileById(
    {
      id: selectedRow?.patient.patientId || '',
    },
    {
      enabled: !!selectedRow && ['View', 'EditForm'].includes(currentAction),
    }
  );

  const getScheinById = useQueryGetScheinItemById(
    {
      scheinId: selectedRow?.scheinId || '',
    },
    {
      enabled:
        !!selectedRow && ['Print', 'ConfirmF9990'].includes(currentAction),
    }
  );

  const timelineEntry = useQueryGetTimelineEntryByIds(
    {
      entryIds:
        selectedRows?.length > 0
          ? selectedRows
              .map((row) => row.currentInvoiceTimelineId)
              .filter((id): id is string => !!id)
          : [invoiceEntry].filter((id): id is string => !!id),
    },
    {
      enabled: !!selectedRows?.length || !!invoiceEntry,
    }
  );

  const getPrintedInvoices = useQueryGetPrintedInvoices(
    {
      billingId: selectedRow?.id || '',
      patientId: selectedRow?.patient.patientId || '',
    },
    {
      enabled: !!selectedRow && ['View'].includes(currentAction),
    }
  );

  const handleAction = (row: BgBillingItem | undefined, action: BG_ACTION) => {
    setSelectedRow(row);
    setCurrentAction(action);
    setPrescribeId('');
    printPreviewPdfActions.setOpenPrintPreviewDialog(null);
  };

  const handleCloseAction = () => {
    switch (currentAction) {
      case 'EditTemplate':
        if (!!printPreviewPdfStore.fileUrl) {
          return;
        }
        printPreviewPdfActions.setCurrentViewInvoice(undefined);
      case 'PreviewTemplate':
        setCurrentAction('');
        return;
      default:
        handleAction(undefined, '');
    }
  };

  const markBgBillingCancelled = useMutationMarkBgBillingCancelled({
    onSuccess: () => {
      getAllBgBilling.refetch();
      alertSuccessfully(t('markedAsCancelled'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
      handleCloseAction();
    },
    onError: () => {
      alertError(t('invoiceCancelledFailed'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });

  const getPdfUrl = useMutationGetPdfPresignedUrl({
    onSuccess: (data) => {
      if (!data?.data?.url) {
        alertError('Cannot open print preview for this doctor letter');
        return;
      }
      printPreviewPdfActions.setOpenPrintPreviewDialog(data.data.url);
    },
    onError: () => {
      alertError('Cannot open print preview for this doctor letter');
    },
  });

  const mutationMarkBgBillingPaid = useMutationMarkBgBillingPaid({
    onSuccess: () => {
      getAllBgBilling.refetch();
      alertSuccessfully(t('markedAsPaid'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
      handleCloseAction();
    },
    onError: () => {
      alertError(t('invoicePaidFailed'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });

  const mutationMarkBgBillingUnpaid = useMutationMarkBgBillingUnpaid({
    onSuccess: () => {
      handleCloseAction();
      getAllBgBilling.refetch();
      alertSuccessfully(t('markedAsUnpaid'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
    onError: () => {
      alertError(t('invoiceUnpaidFailed'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });

  const mutationHandleBgInvoicByForm = useMutationHandleBgInvoicByForm({
    onSuccess: () => {
      getAllBgBilling.refetch();
      alertSuccessfully(t('markedAsUnpaid'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
      handleCloseAction();
    },
    onError: () => {
      alertError(t('invoiceUnpaidFailed'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });

  const { data: doctorsResponse, isSuccess: isSuccessDoctors } =
    useQueryGetListDoctor();
  const { data: insuranceResponse, isSuccess: isSuccessInsurance } =
    useQueryGetListInsurance();
  const { data: statusResponse, isSuccess: isSuccessStatus } =
    useQueryGetListStatus();

  const { data: rangeAmount } = useQueryGetRangeAmount();

  const groupByQuarter = useQueryGroupByQuarter(
    {
      patientId: selectedRow?.patient?.patientId || '',
      isSortByCategory: false,
      timelineEntityTypes: [
        {
          timelineEntityType:
            TimelineEntityType.TimelineEntityType_Service_UV_GOA,
        },
      ],
      isHistoryMode: false,
    },
    {
      enabled: !!selectedRow,
    }
  );

  const printPreviewPdfStore = usePrintPreviewPdfStore();

  const onHandlePrintInvoice = useMutationHandleBgInvoice({ retry: 2 });

  const onChangePage = (page: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      page,
    }));
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      pageSize: currentRowsPerPage,
    }));
  };

  const handleBulkPrint = () => {
    setSelectedRow(undefined);
    printPreviewPdfActions.setSelectedBillingItems(
      selectedRows.map((item) => ({
        ...item,
        stager: 'bgBilling',
      }))
    );
    setCurrentAction('EditTemplate');
  };

  const handleOpenF9990 = () => {
    setCurrentAction('EditForm');
    formOverviewActions.setPatientInForm({
      id: selectedRow?.patient?.patientId || '',
    } as PatientProfileResponse);
    musterFormDialogActions.setCurrentFormName(FormName.F9990);
    musterFormActions.setFormName(
      FormName.F9990,
      getPatientProfileById.data,
      getScheinById.data?.scheinItems || undefined,
      undefined,
      false,
      groupByQuarter.data?.groupByQuarters
    );
  };

  const handleConfirm = async (action: BG_ACTION, values: PrintInvoiceType) => {
    switch (action) {
      case 'Print':
        switch (values.selectType) {
          case SELECT_TYPE.BG_INVOICE_TEMPLATE:
            setCurrentAction('EditTemplate');
            selectedRow &&
              printPreviewPdfActions.setSelectedBillingItems(
                [selectedRow].map((item) => ({
                  ...item,
                  stager: 'bgBilling',
                }))
              );
            break;
          case SELECT_TYPE.F2100:
            setCurrentAction('EditForm');
            formOverviewActions.setPatientInForm({
              id: selectedRow?.patient?.patientId || '',
            } as PatientProfileResponse);
            musterFormDialogActions.setCurrentFormName(FormName.F2100);
            musterFormActions.setFormName(
              FormName.F2100,
              getPatientProfileById.data,
              getScheinById.data?.scheinItems || undefined,
              undefined,
              false,
              groupByQuarter.data?.groupByQuarters
            );
            break;
          case SELECT_TYPE.F9990:
            const uvGoaEntries = await getTimelineServiceEntries(
              groupByQuarter.data?.groupByQuarters || [],
              selectedRow?.scheinId || ''
            );

            if (uvGoaEntries.length > MAXIMUM_UV_GOA_SERVICE_F9990) {
              setCurrentAction('ConfirmF9990');
              setOpenConfirmMaximumUVGoaF9990(true);
              return;
            }

            handleOpenF9990();
            break;
          default:
            break;
        }
        break;
      default:
        break;
    }
  };

  const onBulkPrintSuccess = async (invoices: BulkBillingPayload[]) => {
    try {
      const tasks = invoices.map((invoice) =>
        submitLetterForBgBilling(
          invoice.scheinId,
          invoice.patientId,
          invoice.formValue,
          onHandlePrintInvoice,
          'print',
          invoice.billingId
        )
      );
      await Promise.all(tasks);
      handleAction(undefined, '');
      printPreviewPdfActions.resetBulkBillingPayload();
      getAllBgBilling.refetch();
    } catch (error) {
      alertError(error.message);
    }
  };

  const onSelectedRowChange = (selectedRowState: {
    allSelected: boolean;
    selectedCount: number;
    selectedRows: BgBillingItem[];
  }) => {
    if (selectedRowState.selectedCount > 0) {
      setSelectedRows(selectedRowState.selectedRows);
    } else {
      setSelectedRows([]);
      printPreviewPdfActions.setSelectedBillingItems([]);
    }
  };

  const handlePrintDoctorLetter = async (
    editedTimelineModel?: TimelineModel
  ) => {
    if (!editedTimelineModel?.id) {
      alertError('Cannot print this doctor letter');
      return null;
    }

    const { data } = await getPdfPresignedUrl({
      doctorLetterId: editedTimelineModel.id,
    });

    if (!data?.url) {
      alertError('Cannot open print preview for this doctor letter');
      return null;
    }

    printPreviewPdfActions.setPrintingDoctorLetter({
      timelineModelId: editedTimelineModel.id,
      doctorLetter: editedTimelineModel.doctorLetter,
    });
    printPreviewPdfActions.setOpenPrintPreviewDialog(data.url);
    setCurrentAction('PreviewTemplate');

    return true;
  };

  const handleSubmitDoctorLetter = async (
    newDoctorLetterValues: DoctorLetterExpanded
  ) => {
    const { billingId, scheinId, patientId, id } = newDoctorLetterValues;

    if (billingId && scheinId && patientId) {
      return await submitLetterForBgBilling(
        scheinId,
        patientId,
        { ...newDoctorLetterValues, id },
        onHandlePrintInvoice,
        'save'
      ) || undefined;
    }

    if (activeBilling?.privScheinId) {
      return await submitLetterForBgBilling(
        activeBilling.privScheinId,
        activeBilling.patient.patientId,
        {
          ...newDoctorLetterValues,
          id: activeBilling.privScheinId,
        },
        onHandlePrintInvoice,
        'save'
      ) || undefined;
    }

    return undefined;
  };

  const renderFormDoctorLetter = () => {
    if (selectedRows.length || selectedRow) {
      const bulkSelected = selectedRow
        ? [selectedRow]
        : selectedRows.length
          ? selectedRows
          : [];

      return (
        <DoctorLetterCreateEditDialog
          bulkSelected={bulkSelected.map((row) => ({
            ...row,
            stager: 'bgBilling',
          }))}
          data-testid="create-edit-doctor-letter-dialog"
          isDisableTemplate
          mode="create"
          activatedSchein={{
            type: 'bg',
            scheinId:
              printPreviewPdfStore.currentViewInvoice?.stager === 'bgBilling'
                ? printPreviewPdfStore.currentViewInvoice?.scheinId
                : bulkSelected[0].scheinId,
          }}
          onlyBulkStager
          defaultDoctorLetterValue={undefined}
          timelineSelected={timelineEntry.data?.timelineModels ?? []}
          onPrint={handlePrintDoctorLetter}
          successToastMessage={tTable('saveSuccess')}
          onSubmit={handleSubmitDoctorLetter}
          onBulkActionSuccess={() => getAllBgBilling.refetch()}
          onClose={handleCloseAction}
        />
      );
    }

    return null;
  };

  useEffect(() => {
    if (!selectedRow) {
      return;
    }
    if (currentAction === 'MarkAsPaid') {
      mutationMarkBgBillingUnpaid.mutate({
        patientId: selectedRow.patient.patientId,
        billingId: selectedRow.id,
      });
    } else if (currentAction === 'MarkAsUnPaid') {
      mutationMarkBgBillingPaid.mutate({
        patientId: selectedRow.patient.patientId,
        billingId: selectedRow.id,
        invoiceNumber: selectedRow.invoiceNumber,
      });
    }
  }, [currentAction, selectedRow]);

  useEffect(() => {
    if (
      selectedRow &&
      currentAction === 'View' &&
      selectedRow.currentInvoiceTimelineId &&
      getPrintedInvoices.data
    ) {
      const printedInvoices = getPrintedInvoices.data;

      if (!printedInvoices.timelineModels) {
        return;
      }

      const formEntry = printedInvoices.timelineModels.find(
        (item) => !!item.encounterForm
      );

      if (formEntry?.encounterForm) {
        const treatmentDoctor = getTreatmentDoctorWithBsnr(
          formEntry.treatmentDoctorId || '',
          formEntry.assignedToBsnrId || ''
        );
        const form = formEntry?.encounterForm;

        setCurrentAction('EditForm');

        musterFormDialogActions.setCurrentEntryForm(formEntry?.id || '');
        musterFormDialogActions.viewForm(
          AccountManagementUtil.convertFormData(form, treatmentDoctor),
          form.encounterId || '',
          {} as IContractInfo,
          form?.id || '',
          !!form.prescribe.printedDate,
          false,
          false,
          formEntry.scheinIds?.[0] || ''
        );
        return;
      }

      const timelineModel = printedInvoices.timelineModels?.[0];

      if (!timelineModel) {
        return;
      }

      getPdfUrl.mutate({
        doctorLetterId: timelineModel?.doctorLetter?.id || '',
      });
    }
  }, [currentAction, selectedRow, getPrintedInvoices.data]);

  useEffect(() => {
    if (selectedRow && currentAction === 'PrintForm') {
      mutationHandleBgInvoicByForm.mutate({
        patientId: selectedRow?.patient?.patientId || '',
        billingId: selectedRow.id,
        invoiceTimelineId: prescribeId,
      });
    }
  }, [currentAction, selectedRow]);

  useEffect(() => {
    formTranslationActions.setTranslate(tForm);
  }, []);

  useEffect(() => {
    if (getForms.isSuccess && getForms.data) {
      const listForms = (getForms.data.forms || []).map((item) => {
        if (
          item.formType &&
          [
            FormType.FormType_public_document,
            FormType.FormType_public_contract_text,
          ].includes(item.formType)
        ) {
          return item;
        }
        return item;
      });

      formOverviewActions.setListForms(listForms);
    }
  }, [getForms.isSuccess, getForms.data]);

  const listDoctor = useMemo(() => {
    if (!isSuccessDoctors) {
      return [];
    }
    return doctorsResponse.doctors || [];
  }, [isSuccessDoctors]);

  const listInsurance = useMemo(() => {
    if (!isSuccessInsurance) {
      return [];
    }
    return insuranceResponse.insurances || [];
  }, [isSuccessInsurance]);

  const listStatus = useMemo(() => {
    if (!isSuccessStatus) {
      return [];
    }
    return statusResponse.status || [];
  }, [isSuccessStatus]);

  const onChangeFilter = (values: BgBillingFilter) => {
    setFilter(values);
  };

  return (
    <Flex className={className} column>
      <H1 padding="16">{t('title')}</H1>
      <Divider className="sl-divider" style={{ margin: 0 }} />
      <Flex column gap={16} className="sl-bg-billing-body">
        <Flex m={16} align="center" justify="space-between">
          <Flex gap={16} className="sl-bg-billing-header">
            <InputGroup
              data-testid="bg-billing-search-input"
              type="text"
              className="search-input"
              leftElement={<Svg src={SearchIcon} />}
              placeholder={tCommonInput('search')}
              onChange={(e) => {
                setSearchValue(e.target.value);
                setClearSelected(true);
              }}
            />

            {selectedRows.length === 0 ? (
              <FilterModal
                rangeAmount={
                  rangeAmount
                    ? {
                        min: rangeAmount.minPrice,
                        max: rangeAmount.maxPrice,
                      }
                    : {
                        min: 0,
                        max: 2100,
                      }
                }
                listStatus={listStatus}
                doctorList={listDoctor}
                bgInsurances={listInsurance}
                tCommon={tButtonActions}
                onChange={onChangeFilter}
                ref={formRef}
              />
            ) : (
              <Button
                color={COLOR.TEXT_PRIMARY_BLACK}
                className="sl-bg-billing-print-button"
                disabled={selectedRows.length === 0}
                onClick={handleBulkPrint}
              >
                {t('printInvoice')}
              </Button>
            )}
          </Flex>

          <Flex>
            <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL}>
              {selectedRows.length}&nbsp;
            </BodyTextM>
            <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL}>
              {t('itemSeleceted')}
            </BodyTextM>
          </Flex>
        </Flex>
        <Table
          data-testid="bg-billing-table"
          className="flex-1"
          columns={genColumns({
            t: tTable,
            handleAction,
          })}
          highlightOnHover
          noHeader
          persistTableHead
          striped
          selectableRows
          fixedHeader
          data={getAllBgBilling.data?.items || []}
          responsive={false}
          progressPending={getAllBgBilling.isPending}
          clearSelectedRows={isClearSelected}
          selectableRowDisabled={(row: BgBillingItem) => {
            const isSelected = selectedRows.some(
              (datum) => datum.id === row.id
            );

            return (
              !isSelected &&
              row.status &&
              ![BillingStatus.BillingStatus_NoInvoice].includes(row.status)
            );
          }}
          noDataComponent={
            <Flex mt={40} column>
              <BodyTextL
                fontSize="20px"
                fontWeight={700}
                color={COLOR.TEXT_SECONDARY_NAVAL}
              >
                {tTable('noResultFoundTitle')}
              </BodyTextL>
            </Flex>
          }
          pagination
          paginationServer
          paginationDefaultPage={pagination.page}
          paginationResetDefaultPage
          paginationPerPage={pagination.pageSize}
          paginationTotalRows={getAllBgBilling.data?.total || 0}
          onSelectedRowsChange={onSelectedRowChange}
          onChangePage={onChangePage}
          onChangeRowsPerPage={onChangeRowsPerPage}
        />
      </Flex>
      {currentAction === 'Print' && !!selectedRow && (
        <PrintInvoiceDialog
          data-testid="confirm-print-invoice-dialog"
          isOpen
          isPending={
            getPatientProfileById.isFetching ||
            getScheinById.isFetching ||
            groupByQuarter.isFetching ||
            getForms.isFetching
          }
          onClose={handleCloseAction}
          onConfirm={(value) => handleConfirm('Print', value)}
        />
      )}
      {currentAction === 'Cancel' && !!selectedRow && (
        <InfoConfirmDialog
          data-testid="cancel-bg-billing-dialog"
          type="secondary"
          isOpen
          title={tCancelConfirmDialog('title')}
          cancelText={tButtonActions('cancelText')}
          confirmText={tButtonActions('okText')}
          isLoading={markBgBillingCancelled.isPending}
          disableConfirm={markBgBillingCancelled.isPending}
          isShowIconTitle
          isCloseButtonShown={false}
          isConfirmButtonShown
          onClose={handleCloseAction}
          onConfirm={() => {
            markBgBillingCancelled.mutate({
              patientId: selectedRow.patient.patientId,
              billingId: selectedRow.id,
            });
          }}
        >
          {tCancelConfirmDialog('description')}
        </InfoConfirmDialog>
      )}
      {currentAction === 'EditTemplate' && renderFormDoctorLetter()}
      {currentAction === 'PreviewTemplate' && (
        <PrintPreviewPdfDialog
          data-testid="print-preview-doctor-letter-pdf-dialog"
          formId="Private_Invoice"
          file={printPreviewPdfStore.fileUrl || undefined}
          isBulkPrint
          onPrintSuccess={async () => {
            onBulkPrintSuccess(printPreviewPdfStore.bulkBillingPayload);
            alertSuccessfully(tDoctorLetter('printSuccess'));
          }}
          onClose={handleCloseAction}
        />
      )}
      {currentAction === 'EditForm' && getPatientProfileById.data && (
        <MusterFormDialog
          data-testid="muster-form-dialog"
          patient={getPatientProfileById.data}
          selectedContractDoctor={{
            bsnrId: currentLoggedInUser?.bsnrId || '',
            doctorId: currentLoggedInUser?.id || '',
            contractId: '',
            chargeSystemId: '',
            availableDoctor: [currentLoggedInUser],
          }}
          isOpen={!!musterFormDialogStore.currentFormName}
          componentActions={formOverviewActions}
          onActions={async (_, prescribeId) => {
            setPrescribeId(prescribeId || '');
            setCurrentAction('PrintForm');
          }}
          onClose={handleCloseAction}
        />
      )}
      {!!printPreviewPdfStore.fileUrl && currentAction === 'View' && (
        <PrintPreviewPdfDialog
          data-testid="view-doctor-letter-pdf-dialog"
          isViewOnly
          formId="Doctor_Letter"
          file={printPreviewPdfStore.fileUrl}
          timelineId={selectedRow?.currentInvoiceTimelineId || ''}
          onClose={handleCloseAction}
          PageProps={{
            scale: 1.5,
          }}
        />
      )}
      {currentAction === 'ConfirmF9990' && (
        <InfoConfirmDialog
          data-testid="confirm-f9990-dialog"
          type="primary"
          isOpen={openConfirmMaximumUVGoaF9990}
          title={tConfirmMaximumUVGoaF9990('title')}
          confirmText={tConfirmMaximumUVGoaF9990('confirmButton')}
          cancelText={tConfirmMaximumUVGoaF9990('cancelButton')}
          isShowIconTitle={false}
          isCloseButtonShown={false}
          onConfirm={() => {
            setOpenConfirmMaximumUVGoaF9990(false, handleOpenF9990);
          }}
          onClose={() => setOpenConfirmMaximumUVGoaF9990(false)}
        >
          <Flex column>{tConfirmMaximumUVGoaF9990('description')}</Flex>
        </InfoConfirmDialog>
      )}
      {(getPrintedInvoices.isFetching || onHandlePrintInvoice.isPending) && (
        <LoadingState />
      )}
    </Flex>
  );
};

export default BGBilling;
