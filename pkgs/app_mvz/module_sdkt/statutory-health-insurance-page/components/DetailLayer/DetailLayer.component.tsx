import React, { useMemo } from 'react';
import Collapse from 'react-collapse';
import { Drawer } from '@tutum/design-system/components/Core';
import { Svg } from '@tutum/design-system/components/Svg';
import {
  BodyTextS,
  BodyTextM,
  H1,
} from '@tutum/design-system/components/Typography';
import {
  formatUnixToDateString,
  getCssClass,
} from '@tutum/design-system/infrastructure/utils';
import type { SdktCatalog } from '@tutum/hermes/bff/catalog_sdkt_common';
import { KeyTab } from '@tutum/hermes/bff/catalog_sdkt_common';
import { Flex } from '@tutum/design-system/components/Flexbox';
import i18n from '@tutum/infrastructure/i18n';

import DetailLayerStyleWrapper, {
  InfoTableStyleWrapper,
} from './DetailLayer.styled';
import { feeCatalogueOptionLabelsMap } from '@tutum/mvz/module_sdkt/cost-unit-dialog/cost-unit-dialog.constant';
import { KTabBillingArea } from '@tutum/hermes/bff/legacy/catalog_sdkt_common';
import { useQueryGetSdktCatalogByVknr } from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdkt';
import { LoadingState } from '@tutum/design-system/components';
import { COLOR } from '@tutum/design-system/themes/styles';

interface DetailLayerProps {
  isOpen: boolean;
  item: SdktCatalog;
  onClose(): void;
}

const CloseIconSVGURL = '/images/close.svg';

interface InfoTableProps {
  title: string;
  rows: JSX.Element[];
  collapsible?: boolean;
}

const InfoTable: React.FC<InfoTableProps> = (props) => {
  return (
    <InfoTableStyleWrapper>
      <Flex>
        <BodyTextM className="title">{props.title}</BodyTextM>
      </Flex>

      <table className="table">
        <tbody>{props.rows}</tbody>
      </table>
      <Collapse isOpen>a</Collapse>
    </InfoTableStyleWrapper>
  );
};

const DetailLayer: React.FC<DetailLayerProps> = (props) => {
  const { t } = i18n.useTranslation({
    namespace: 'CostUnit',
  });
  const { item, isOpen, onClose } = props;

  const { data: sdkt, isLoading } = useQueryGetSdktCatalogByVknr(
    {
      vknr: item.vknr,
      selectedDate: undefined,
    },
    {
      select: ({ data }) => data?.data,
      enabled: Boolean(isOpen),
    }
  );

  const costUnitInfoRows = useMemo<JSX.Element[]>(() => {
    if (!sdkt) {
      return [];
    }

    return [
      <Flex as="tr" key={1}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('detailSearchName')}
          </BodyTextS>
        </td>
        <td>
          <BodyTextM>{sdkt.searchName}</BodyTextM>
        </td>
      </Flex>,
      <Flex as="tr" key={2}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('detailAddress')}
          </BodyTextS>
        </td>
        <td>
          <BodyTextM>
            {getCssClass(sdkt.address?.street, sdkt.address?.number)}
          </BodyTextM>
          <BodyTextM>
            {getCssClass(sdkt.address?.postCode, sdkt.address?.city)}
          </BodyTextM>
        </td>
      </Flex>,
      <Flex as="tr" key={3}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('detailPostmailAddress')}
          </BodyTextS>
        </td>
        {sdkt.postboxAddress ? (
          <td>
            <BodyTextM>
              {getCssClass(sdkt.postboxAddress?.postboxNumber)}
            </BodyTextM>
            <BodyTextM>
              {getCssClass(
                sdkt.postboxAddress?.postCode,
                sdkt.postboxAddress?.city
              )}
            </BodyTextM>
          </td>
        ) : null}
      </Flex>,
      <Flex as="tr" key={4}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('detailSearchLocations')}
          </BodyTextS>
        </td>
        <td>
          <BodyTextM>
            {sdkt.locationNames?.filter((name) => name).join(', ')}
          </BodyTextM>
        </td>
      </Flex>,
    ];
  }, [t, sdkt]);

  const getCostUnitGroup = () => {
    const key = Object.keys(KeyTab).find((key) => {
      const code = key.split('_')[1];
      return code === sdkt?.vknrGroup;
    });

    return `${sdkt?.vknrGroup ? `(${sdkt.vknrGroup})` : ''} ${key ? KeyTab[key] : ''
      }`;
  };

  const billingInfoRows = useMemo<JSX.Element[]>(() => {
    if (!sdkt) {
      return [];
    }

    return [
      <Flex as="tr" key={1}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('vknr')}
          </BodyTextS>
        </td>
        <td>
          <BodyTextM>{sdkt.vknr}</BodyTextM>
        </td>
      </Flex>,
      <Flex as="tr" key={2}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('feeCatalogue')}
          </BodyTextS>
        </td>
        <td>
          <BodyTextM>
            {feeCatalogueOptionLabelsMap[sdkt.feeCatalogue]}
          </BodyTextM>
        </td>
      </Flex>,
      <Flex as="tr" key={3}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('costUnitGroup')}
          </BodyTextS>
        </td>
        <td>
          <BodyTextM>{getCostUnitGroup()}</BodyTextM>
        </td>
      </Flex>,
      <Flex as="tr" key={4}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('validityPeriod')}
          </BodyTextS>
        </td>
        <td>
          <BodyTextM>
            {formatUnixToDateString(sdkt.validity.fromDate)}
            {sdkt.validity.toDate != null && (
              <>
                {' - '}
                {formatUnixToDateString(sdkt.validity.toDate)}
              </>
            )}
          </BodyTextM>
        </td>
      </Flex>,
      <Flex as="tr" key={5}>
        <td>
          <BodyTextS
            color={COLOR.TEXT_TERTIARY_SILVER}
            textTransform="uppercase"
          >
            {t('acquiredCostUnit')}
          </BodyTextS>
        </td>
        <td>
          {sdkt.accquiredCostUnit && (
            <BodyTextM>{`${sdkt.accquiredCostUnitName
              ? `${sdkt.accquiredCostUnitName} (${sdkt.accquiredCostUnit})`
              : sdkt.accquiredCostUnit
              }`}</BodyTextM>
          )}
        </td>
      </Flex>,
    ];
  }, [t, sdkt]);

  const ikNumberRows = useMemo<JSX.Element[]>(() => {
    if (!sdkt) {
      return [];
    }
    const ikNumbers = sdkt.iKNumbers;

    const _rows = ikNumbers?.map((ik) => (
      <Flex as="tr" key={ik.value}>
        <td width="60%">
          <BodyTextM>{ik.valueString}</BodyTextM>
        </td>
        <td width="20%">
          <BodyTextM>{formatUnixToDateString(ik.validity?.fromDate)}</BodyTextM>
        </td>
        <td width="20%">
          <BodyTextM>{formatUnixToDateString(ik.validity?.toDate)}</BodyTextM>
        </td>
      </Flex>
    ));

    return [
      <Flex as="tr" key={0} className="header-row">
        <td width="60%">
          <BodyTextS
            color={COLOR.TEXT_SECONDARY_NAVAL}
            textTransform="uppercase"
            fontWeight={600}
          >
            {t('ikNumber')}
          </BodyTextS>
        </td>
        <td width="20%">
          <BodyTextS
            color={COLOR.TEXT_SECONDARY_NAVAL}
            textTransform="uppercase"
            fontWeight={600}
          >
            {t('validFrom')}
          </BodyTextS>
        </td>
        <td width="20%">
          <BodyTextS
            color={COLOR.TEXT_SECONDARY_NAVAL}
            textTransform="uppercase"
            fontWeight={600}
          >
            {t('validTo')}
          </BodyTextS>
        </td>
      </Flex>,
      ..._rows,
    ];
  }, [t, sdkt?.iKNumbers]);

  const ktabRows = useMemo<JSX.Element[]>(() => {
    if (!sdkt) {
      return [];
    }
    const kTABs = sdkt.kTABs;

    const _rows = kTABs?.map((ktab) => {
      const description = `(${ktab.value}) ${KTabBillingArea[`KTabBillingArea_${ktab.value}`]
        }`;

      return (
        <Flex as="tr" key={ktab.value}>
          <td width="60%">
            <BodyTextM>{description}</BodyTextM>
          </td>
          <td width="20%">
            <BodyTextM>
              {formatUnixToDateString(ktab.validity?.fromDate)}
            </BodyTextM>
          </td>
          <td width="20%">
            <BodyTextM>
              {formatUnixToDateString(ktab.validity?.toDate)}
            </BodyTextM>
          </td>
        </Flex>
      );
    });

    return [
      <Flex as="tr" key={0} className="header-row">
        <td width="60%">
          <BodyTextS
            color={COLOR.TEXT_SECONDARY_NAVAL}
            textTransform="uppercase"
            fontWeight={600}
          >
            {t('ktab')}
          </BodyTextS>
        </td>
        <td width="20%">
          <BodyTextS
            color={COLOR.TEXT_SECONDARY_NAVAL}
            textTransform="uppercase"
            fontWeight={600}
          >
            {t('validFrom')}
          </BodyTextS>
        </td>
        <td width="20%">
          <BodyTextS
            color={COLOR.TEXT_SECONDARY_NAVAL}
            textTransform="uppercase"
            fontWeight={600}
          >
            {t('validTo')}
          </BodyTextS>
        </td>
      </Flex>,
      ..._rows,
    ];
  }, [t, sdkt?.kTABs]);

  if (!sdkt) {
    return <></>;
  }

  return (
    <Drawer
      isOpen={isOpen}
      size="500px"
      lazy
      usePortal
      autoFocus
      canEscapeKeyClose
      canOutsideClickClose
      shouldReturnFocusOnClose
      transitionDuration={250}
      onClose={() => onClose()}
    >
      {isLoading ? (
        <LoadingState />
      ) : (
        <DetailLayerStyleWrapper
          data-test-id={`cost-unit-detail-layer-${sdkt.vknr}`}
        >
          <div className="header">
            <H1 className="title" fontWeight={600}>
              {sdkt.name}
            </H1>
            <div className="spacer" />
            <Svg
              src={CloseIconSVGURL}
              style={{ cursor: 'pointer' }}
              onClick={() => onClose()}
            />
          </div>
          <div className="body flex-column">
            <InfoTable title={t('costUnitDetails')} rows={costUnitInfoRows} />
            <InfoTable title={t('billingInfo')} rows={billingInfoRows} />
            <InfoTable title={t('ikNumber')} rows={ikNumberRows} />
            <InfoTable title={t('ktab')} rows={ktabRows} />
          </div>
        </DetailLayerStyleWrapper>
      )}
    </Drawer>
  );
};

export default React.memo(DetailLayer);
