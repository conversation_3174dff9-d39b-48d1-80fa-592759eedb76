import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type ScheinLocales from '@tutum/mvz/locales/en/Schein.json';

import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { BgScheinItem } from '@tutum/hermes/bff/schein_common';
import { ComposerDiagnosisType } from '../components/select-diagnosis-dialog/helpers';
import FormUtils, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';

export type ScheinInfo = BgScheinItem & {
  takeOverDiagnoseIds?: string[];
  mappingTreatmentRelevent?: {
    [key: string]: boolean;
  };
  newTakeOverDiagnosis?: ComposerDiagnosisType[];
};

export const scrollToElem = (elem: HTMLDivElement) => {
  elem.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
    inline: 'nearest',
  });
};

export const onValidate =
  (
    t: IFixedNamespaceTFunction<keyof typeof CommonLocales.FormValidation>,
    tSchein: IFixedNamespaceTFunction<keyof typeof ScheinLocales.createSchein>
  ) =>
  (values: BgScheinItem) => {
    const validateFields: ValidateField[] = [];
    validateFields.push({
      fieldName: 'doctorId',
      validateRule: () => !values.doctorId,
      errorMessage: t('fieldRequired'),
    });

    validateFields.push({
      fieldName: 'arrivalDate',
      validateRule: () => !values.arrivalDate,
      errorMessage: t('fieldRequired'),
    });

    validateFields.push({
      fieldName: 'accidentDate',
      validateRule: () => !values.accidentDate,
      errorMessage: t('fieldRequired'),
    });

    validateFields.push({
      fieldName: 'createdOn',
      validateRule: () => !values.createdOn,
      errorMessage: t('fieldRequired'),
    });

    if (values.endDate) {
      validateFields.push({
        fieldName: 'endDate',
        validateRule: () => {
          const createdOn = values.createdOn;
          const endDate = values.endDate!;
          return createdOn > endDate;
        },
        errorMessage: tSchein('createOnShouldBeforeEndDate'),
      });
    }

    if (values.workingTimeEnd) {
      validateFields.push({
        fieldName: 'workingTimeEnd',
        validateRule: () => {
          const workingTimeStart = values.workingTimeStart;
          const workingTimeEnd = values.workingTimeEnd;
          return workingTimeStart > workingTimeEnd;
        },
        errorMessage: tSchein('workingTimeStartShouldBeforeEnd'),
      });
    }

    validateFields.push({
      fieldName: 'arrivalDate',
      validateRule: () => {
        const arrivalDate = values.arrivalDate;
        const accidentDate = values.accidentDate;
        return accidentDate > arrivalDate!;
      },
      errorMessage: tSchein('AccidentDateShouldBeforeArrivalDate'),
    });

    validateFields.push({
      fieldName: 'employmentInfo.workingHourInWeek',
      validateRule: () =>
        !!values.employmentInfo.isEmployed &&
        !!values.employmentInfo.workingHourInWeek &&
        !(
          0 <= values.employmentInfo.workingHourInWeek &&
          values.employmentInfo.workingHourInWeek < 100
        ),
      errorMessage: tSchein('errWorkingHourInWeek'),
    });

    const { errors } = FormUtils.validateForm(validateFields, null);
    return errors;
  };

export const MIN_LENGTH_POSTCODE = 5;
export const MAX_LENGTH_POSTCODE = 10;
