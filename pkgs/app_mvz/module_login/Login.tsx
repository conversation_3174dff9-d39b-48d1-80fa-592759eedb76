import React, { useState, useMemo, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Avatar,
  BodyTextL,
  BodyTextM,
  BodyTextS,
  Flex,
  H2,
  LoadingState,
  Svg,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import type LoginI18n from '@tutum/mvz/locales/en/Login.json';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import Employee from './Employee';
import { LEGACY_TOPIC_Login } from '@tutum/hermes/bff/legacy/app_mvz_auth';
import { LOGGED_IN_USERS } from '@tutum/mvz/constant/general';
import { COLOR } from '@tutum/design-system/themes/styles';
import nameUtil from '@tutum/infrastructure/utils/name.utils';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import { isEmpty } from 'lodash';

export interface ILoginProps {
  className?: string;
  theme?: IMvzTheme;
}

export interface ILoginState {
  step: number;
  password: string;
  selectedUser: IEmployeeProfile;
  isProcessing: boolean;
}

const Logo = '/images/logo-garrio.svg';
const DownArrow = '/images/chevron-down.svg';
const AddUserIcon = '/images/add-user-1.svg';

const Login: React.FC<ILoginProps> = ({ className }) => {
  const { t } = I18n.useTranslation<keyof typeof LoginI18n>({
    namespace: 'Login',
  });
  const [state, setState] = useState<ILoginState>({
    password: undefined!,
    step: 1,
    selectedUser: undefined!,
    isProcessing: false,
  });

  const [sortType, setSortType] = useState<Order>(Order.ASC);

  const router = useRouter();

  const userList = useMemo(() => {
    const cacheData = JSON.parse(localStorage.getItem(LOGGED_IN_USERS) || '{}');

    return Object.values(cacheData) as IEmployeeProfile[];
  }, []);

  const userListSorted = useMemo(() => {
    const isAsc = sortType === Order.ASC;

    return userList.sort((a, b) => {
      const fullNameA = nameUtil.getDoctorName(isAsc ? a : b);
      const fullNameB = nameUtil.getDoctorName(isAsc ? b : a);

      return fullNameA.localeCompare(fullNameB);
    });
  }, [userList, sortType]);

  const handleNewAccount = () => {
    setState((prev) => ({ ...prev, isProcessing: true }));
    router.push(`${LEGACY_TOPIC_Login}`);
  };

  const renderUserList = useMemo(() => {
    return (
      <>
        <Flex gap={8} ml={32} align="center">
          <Flex gap={4}>
            <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER}>
              {t('sortBy')}
            </BodyTextM>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
              {t(sortType === Order.ASC ? 'type' : 'typeZA')}
            </BodyTextM>
          </Flex>
          <Popover
            content={
              <Menu className="action-menu">
                <MenuItem
                  key="a-z"
                  text={t('type')}
                  data-test-id="login-sort-asc"
                  onClick={() => setSortType(Order.ASC)}
                />
                <MenuItem
                  key="z-a"
                  text={t('typeZA')}
                  data-test-id="login-sort-desc"
                  onClick={() => setSortType(Order.DESC)}
                />
              </Menu>
            }
          >
            <Flex>
              <Svg
                className="cursor-pointer"
                src={DownArrow}
                width={16}
                height={16}
                data-test-id="login-sort-icon"
              />
            </Flex>
          </Popover>
        </Flex>
        <Flex column mt={16}>
          {userListSorted.map((user, index) => (
            <Employee
              key={user.id}
              {...user}
              dataTestId={`login-user-${index}`}
              handleClick={() => {
                setState((prev) => ({ ...prev, isProcessing: true }));
                router.push(
                  `${LEGACY_TOPIC_Login}?username=${encodeURIComponent(
                    user.userName!
                  )}&orgId=${user.orgId}`
                );
              }}
            />
          ))}
          <Flex
            className="employee"
            gap={16}
            align="center"
            data-test-id="login-user-new"
            onClick={handleNewAccount}
          >
            <Flex
              className="add-user-icon"
              borderRadius="50%"
              w={64}
              h={64}
              justify="center"
              align="center"
            >
              <Svg width={44} height={44} src={AddUserIcon} />
            </Flex>
            <Flex column>
              <BodyTextL color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
                {t('otherAccount')}
              </BodyTextL>
            </Flex>
          </Flex>
        </Flex>
      </>
    );
  }, [userListSorted, sortType]);

  useEffect(() => {
    if (isEmpty(userListSorted)) {
      handleNewAccount();
    }
  }, [userListSorted]);

  return (
    <Flex
      className={className}
      data-test-id="login"
      align="center"
      justify="center"
      w="100vw"
      h="100vh"
      column
      gap={16}
    >
      {state.isProcessing && <LoadingState dataTestId="login-loading" />}
      <Flex className="sl-Login__inner" column>
        <Flex column gap={16} align="center">
          <Svg
            className="logo"
            src={Logo}
            alt="Logo garrioPRO"
            width={200}
            height="auto"
          />
          <H2 fontSize="24px" lineHeight="32px" fontWeight={700}>
            {t('guide')}
          </H2>
        </Flex>

        <Flex column my={24}>
          {state.step === 1 && renderUserList}
        </Flex>
      </Flex>
      <Flex>
        <BodyTextS>{t('note')}</BodyTextS>
      </Flex>
    </Flex>
  );
};

export default Login;
