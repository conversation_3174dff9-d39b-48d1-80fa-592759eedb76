import { DoctorLetterCreateEditDialog } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog';
import { printPreviewPdfActions } from '@tutum/mvz/hooks/usePrintPreviewPdf.store';
import {
  getTimelineEntryIdForPrivateBilling,
  submitLetterForPrivateBilling,
} from '@tutum/mvz/module_doctor-letter/DoctorLetter.helper';
import { alertError } from '@tutum/design-system/components';
import { getPdfPresignedUrl } from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import {
  PrivateBillingItem,
  PrivateBillingStatus,
} from '@tutum/hermes/bff/legacy/private_billing_common';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import {
  CreateDoctorLetterRequest,
  useMutationHandlePrivateInvoice,
} from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import { shouldSubmitNormalDoctorLetter } from '../../PrivateBilling.helper';
import { BillingItem } from '@tutum/mvz/_utils/doctorLetter.type';
interface NoInvoicePayload {
  activeBilling: PrivateBillingItem;
  activeTimelineEntry: TimelineModel;
  invoiceEntryId: string;
}
interface UnPaidPayload {
  activeBilling: PrivateBillingItem;
  activeTimelineEntry: TimelineModel;
  invoiceEntryId: string;
}

interface PrivateBillingDialogContentProps {
  t: any;
  status: PrivateBillingStatus;
  payload: any;
  onClose: () => void;
  onSubmitDoctorLetter: (
    payload: Omit<CreateDoctorLetterRequest, 'patientId' | 'treatmentDoctorId'>
  ) => Promise<TimelineModel | null> | null;
}

const PrivateBillingDialogContent = ({ t, status, payload, onClose, onSubmitDoctorLetter }: PrivateBillingDialogContentProps) => {
  const { activeBilling, activeTimelineEntry, invoiceEntryId } = payload as NoInvoicePayload;

  if (!activeBilling && !activeTimelineEntry) return null;
  if (invoiceEntryId && !activeTimelineEntry) return null;

  const HandlePrivateInvoice = useMutationHandlePrivateInvoice({ retry: 2 });

  const handlepPrint = async (
    editedTimelineModel: TimelineModel | undefined,
    activeBilling: PrivateBillingItem,
    activeTimelineEntry: TimelineModel
  ) => {
    const value = editedTimelineModel?.doctorLetter ? editedTimelineModel : activeTimelineEntry;
    const { id, doctorLetter } = value;
    if (!doctorLetter?.id) return alertError('Cannot print');

    const data = await getPdfPresignedUrl({
      doctorLetterId: doctorLetter.id,
    });
    if (!data?.data?.url) return alertError('Cannot print');

    const { url } = data.data;

    printPreviewPdfActions.setPrintingDoctorLetter({
      timelineModelId: id!,
      doctorLetter,
      activeBilling: activeBilling as BillingItem,
    });
    printPreviewPdfActions.setOpenPrintPreviewDialog(url);
  };

  if ([
    PrivateBillingStatus.PrivBillingStatus_NoInvoice, 
    PrivateBillingStatus.PrivBillingStatus_1stReminder, 
    PrivateBillingStatus.PrivBillingStatus_2ndReminder,
    PrivateBillingStatus.PrivBillingStatus_UnPaid,
  ].includes(status)) {
    let existingDoctorLetter = activeTimelineEntry?.doctorLetter;

    const templateInTimelienEntry = getTimelineEntryIdForPrivateBilling(
      invoiceEntryId,
      status,
      activeTimelineEntry?.doctorLetter?.privateInvoice?.status!
    );

    if (!templateInTimelienEntry) {
      existingDoctorLetter = undefined;
    }

    return (
      <DoctorLetterCreateEditDialog
        mode={existingDoctorLetter ? 'edit' : 'create'}
        activatedSchein={{
          type: 'private',
          scheinId: activeBilling.privScheinId,
        }}
        patientId={activeBilling.patient.patientId}
        successToastMessage={t('saveSuccess')}
        defaultDoctorLetterValue={existingDoctorLetter}
        bulkSelected={
          activeBilling
            ? [activeBilling].map((row) => ({
                ...row,
                stager: 'privateBilling',
              }))
            : undefined
        }
        timelineSelected={activeTimelineEntry ? [activeTimelineEntry] : undefined}
        onPrint={(timelineModel) =>
          handlepPrint(timelineModel, activeBilling, activeTimelineEntry)
        }
        onSubmit={async (newDoctorLetterValues) => {
          const { type } = newDoctorLetterValues;
          if (activeBilling.privScheinId) {
            if (shouldSubmitNormalDoctorLetter(type)) {
              return await onSubmitDoctorLetter({
                doctorLetter: newDoctorLetterValues,
              }) || undefined;
            }
            const updatedTimelineEntry = await submitLetterForPrivateBilling(
              activeBilling.privScheinId,
              activeBilling.patient.patientId,
              {
                ...newDoctorLetterValues,
                id: existingDoctorLetter?.id,
              },
              HandlePrivateInvoice,
              'save'
            );
            return updatedTimelineEntry || undefined;
          }

          return undefined;
        }}
        onClose={onClose}
      />
    );
  }

  return <></>;
};

export default PrivateBillingDialogContent;
