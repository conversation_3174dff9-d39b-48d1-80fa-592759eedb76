import React, { useContext, useEffect, useState } from 'react';
import debounce from 'lodash/debounce';
import cloneDeep from 'lodash/cloneDeep';

import type BillingTrans from '@tutum/mvz/locales/en/Billing.json';

import {
  BodyTextL,
  BodyTextM,
  Box,
  Flex,
  H1,
  Svg,
  Button,
  alertSuccessfully,
  alertError,
  TOASTER_TIMEOUT_CUSTOM,
  LoadingState,
} from '@tutum/design-system/components';
import { Divider, Popover, Dialog } from '@tutum/design-system/components/Core';
import { DoctorLetterCreateEditDialog } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog';
import I18n from '@tutum/infrastructure/i18n';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import Table from '@tutum/design-system/components/Table';
import {
  BulkBillingPayload,
  printPreviewPdfActions,
  usePrintPreviewPdfStore,
} from '@tutum/mvz/hooks/usePrintPreviewPdf.store';
import { useSVFeatureEnable } from '@tutum/mvz/hooks/useSVFeatureEnable';
import { PaginationRequest } from '@tutum/hermes/bff/common';
import {
  doctorLetterTimelineActions,
  useDoctorLetterTimelineStore,
} from '@tutum/mvz/module_doctor-letter/DoctorLetterTimeline.store';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import {
  PrivateBillingFilter,
  PrivateBillingStatus,
} from '@tutum/hermes/bff/legacy/private_billing_common';
import InputGroup from '@tutum/design-system/components/Core/InputGroup/InputGroup';
import {
  TABLE_STYLES,
  genColumns,
  PAGINATION_DEFAULT_SORT,
} from '../../PrivateBilling.helper';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  useMutationToggleReviewPrivateBilling,
  useMutationMarkPrivateBillingPaid,
  useMutationMarkPrivateBillingCancelled,
  useMutationMarkPrivateBillingUnpaid,
  useMutationMarkPrivateBillingPartiallyPaid,
  useQueryGetPrivateBillings,
  useQueryGetPrintedInvoices,
} from '@tutum/hermes/bff/legacy/private_billing';
import { PrivateBillingItem } from '@tutum/hermes/bff/private_billing_common';
import { useQueryGetTimelineEntryByIds } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { submitLetterForPrivateBilling } from '@tutum/mvz/module_doctor-letter/DoctorLetter.helper';
import {
  CreateDoctorLetterRequest,
  createDoctorLetter,
  getPdfPresignedUrl,
  useMutationHandlePrivateInvoice,
  useQueryGetTemplates,
} from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import PrivateBillingDialogContent from '../../components/PrivateBillingDialog/PrivateBillingDialogContent';
import GoaFilter from '../../components/GoaFilter/GoaFilter.styled';
import {
  INIT_FILTER,
  MAX_SELECTED_INVOICES,
  getFilterOptions,
} from '../../PrivateBilling.utils';
import { useQueryGetSdikCatalogs } from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdik';
import { Order } from '@tutum/hermes/bff/legacy/common';
import PartialPaidDialog from '../../components/PartialPaidDialog/PartialPaidDialog.styled';
import { DoctorLetterExpanded } from '@tutum/mvz/_utils/doctorLetter.type';
import DoctorLetterViewOnlyDialog from '../../components/DoctorLetterViewOnlyDialog/DoctorLetterViewOnlyDialog';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { isEmpty } from 'lodash';

const chevronDownIcon = '/images/chevron-down.svg';
const SearchIconSvgURL = '/images/search-sidebar-disable.svg';
const FilterActiveIcon = '/images/fill-filter.svg';
const QuickFilterIcon = '/images/filter.svg';
const PrintIcon = '/images/printer.svg';

interface NoInvoicePayload {
  activeBilling: PrivateBillingItem;
  activeTimelineEntry: TimelineModel;
  invoiceEntryId: string;
}

type DialogContent = {
  [PrivateBillingStatus.PrivBillingStatus_NoInvoice]: NoInvoicePayload;
  [PrivateBillingStatus.PrivBillingStatus_UnPaid]: NoInvoicePayload;
  [PrivateBillingStatus.PrivBillingStatus_1stReminder]: NoInvoicePayload;
  [PrivateBillingStatus.PrivBillingStatus_2ndReminder]: NoInvoicePayload;
  [PrivateBillingStatus.PrivBillingStatus_3rdReminder]: NoInvoicePayload;
};

export interface PrivateBillingHistoryProps {
  className?: string;
}

const PrivateBillingHistory = ({ className }: PrivateBillingHistoryProps) => {
  const { t } = I18n.useTranslation<keyof typeof BillingTrans.PrivateBilling>({
    namespace: 'Billing',
    nestedTrans: 'PrivateBilling',
  });
  const { t: tDoctorLetter } = I18n.useTranslation({
    namespace: 'DoctorLetter',
  });
  const { t: tCommon } = I18n.useTranslation<unknown>({
    namespace: 'Common',
  });

  const printPreviewPdfStore = usePrintPreviewPdfStore();
  const { getEmployeeProfiles } = GlobalContext.useContext();
  const currentLoggedInUser = useContext(
    GlobalContext.instance
  ).useGetLoggedInUserProfile();
  const { isExistFAVDoctor } = useSVFeatureEnable();

  const { activeTimelineModel, activeBilling, invoiceEntry } =
    useDoctorLetterTimelineStore();
  const [openViewOnlyDocument, setOpenViewOnlyDocument] = useState(false);
  const [printInvoicesDialog, setPrintInvoicesDialog] = useState(false);
  const [openItemCancel, setOpenItemCancel] =
    useState<PrivateBillingItem | null>(null);
  const [filter, setFilter] = useState<PrivateBillingFilter>(INIT_FILTER);
  const [doctorList, setDoctorList] = useState<IEmployeeProfile[]>([]);
  const [query, setQuery] = useState<string>('');
  const [_selectedRows, setSelectedRows] = useState<PrivateBillingItem[]>([]);
  const [openListStatus, setOpenListStatus] = useState<boolean>(false);
  const [quickFilter, setQuickFilter] = useState<PrivateBillingStatus | null>(
    null
  );
  const [openFilter, setOpenFilter] = useState(false);
  const [isOpenLetterDialog, setIsOpenLetterDialog] = useState(false);
  const [partialPaidItem, setPartialPaidItem] =
    useState<PrivateBillingItem | null>(null);
  const [pagination, setPagination] = useState<PaginationRequest>(
    PAGINATION_DEFAULT_SORT
  );
  const [documentForBilling, setDocumentForBilling] =
    useState<PrivateBillingItem | undefined>(undefined);
  const [currentPrintedDocument, setCurrentPrintedDocument] =
    useState<TimelineModel | undefined>(undefined);
  const isBulkPrint = printPreviewPdfStore.selectedBillingItems?.length > 0;

  const selectedRows = cloneDeep(_selectedRows).sort((a, b) =>
    a.createdAt > b.createdAt ? -1 : 1
  );

  const selectedRowsNoInvoice = selectedRows.filter(
    (bill) => bill.status === PrivateBillingStatus.PrivBillingStatus_NoInvoice
  );
  const selectedRowsReminder = selectedRows.filter(
    (bill) =>
      [
        PrivateBillingStatus.PrivBillingStatus_UnPaid,
        PrivateBillingStatus.PrivBillingStatus_1stReminder,
        PrivateBillingStatus.PrivBillingStatus_2ndReminder,
      ].includes(bill.status) &&
      !bill.isImported
  );
  const FILTER_OPTIONS = getFilterOptions(t);

  const isFilterActive =
    filter.status ||
    filter.dateRange.from ||
    filter.dateRange.to ||
    filter.delayPaid ||
    !!filter.doctorIds.length ||
    !!filter.ikNumber.length;

  useEffect(() => {
    let isMounted = true;
    getEmployeeProfiles().then((userList) => {
      if (isMounted) setDoctorList(userList);
    });
    return () => {
      isMounted = false;
    };
  }, []);

  const HandlePrivateInvoice = useMutationHandlePrivateInvoice({ retry: 2 });

  const GetPrintedInvoices = useQueryGetPrintedInvoices(
    {
      privateBillingId: documentForBilling?.id!,
      patientId: documentForBilling?.patient.patientId!,
    },
    {
      enabled: !!documentForBilling?.id,
    }
  );

  const getExcludeTags = () => {
    const excludeTags: string[] = [];
    if (!isExistFAVDoctor) {
      excludeTags.push('FAV');
    }
    return excludeTags;
  };
  const templates = useQueryGetTemplates(
    {
      query: '',
      excludeTags: getExcludeTags(),
      bsnrId: currentLoggedInUser.bsnrId,
    },
    {
      enabled: !!currentLoggedInUser.bsnrId,
    }
  );

  const privBillings = useQueryGetPrivateBillings({
    pagination,
    search: query,
    filter: { ...filter, status: quickFilter! },
  });
  const isLoading = privBillings.isLoading || templates.isLoading;

  const ToggleReview = useMutationToggleReviewPrivateBilling({
    onSuccess: (res) => {
      privBillings.refetch();
      if (res.data.reviewed) {
        alertSuccessfully(t('invoiceReviewed'), {
          timeout: TOASTER_TIMEOUT_CUSTOM,
        });
      } else {
        alertError(t('invoiceNotReviewed'), {
          timeout: TOASTER_TIMEOUT_CUSTOM,
        });
      }
    },
  });

  const MarkPartialPaid = useMutationMarkPrivateBillingPartiallyPaid({
    onSuccess: () => {
      privBillings.refetch();
      alertSuccessfully(t('markedAsPartialPaid'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });
  const MarkCancelled = useMutationMarkPrivateBillingCancelled({
    onSuccess: () => {
      privBillings.refetch();
      alertSuccessfully(t('invoiceCancelled'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
      setOpenItemCancel(null);
    },
    onError: () => {
      alertError(t('invoiceCancelleFailed'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });
  const MarkPrivateBillingPaid = useMutationMarkPrivateBillingPaid({
    onSuccess: () => {
      privBillings.refetch();
      alertSuccessfully(t('markedAsPaid'), { timeout: TOASTER_TIMEOUT_CUSTOM });
    },
  });
  const MarkPrivateBillingUnpaid = useMutationMarkPrivateBillingUnpaid({
    onSuccess: () => {
      privBillings.refetch();
      alertSuccessfully(t('markedAsUnpaid'), {
        timeout: TOASTER_TIMEOUT_CUSTOM,
      });
    },
  });

  const timelineEntry = useQueryGetTimelineEntryByIds(
    {
      entryIds:
        selectedRows?.length > 0
          ? selectedRows
            .map((row) => row.currentInvoiceTimelineId!)
            .filter((e) => !!e)
          : [invoiceEntry!],
    },
    {
      enabled: !!selectedRows?.length || !!invoiceEntry,
    }
  );
  const SdikCatalogs = useQueryGetSdikCatalogs({
    value: '',
    pagination: { page: 1, pageSize: 999, order: Order.ASC, sortBy: '' },
    isOnlySelfCreated: false,
  });
  const sdikList = SdikCatalogs.data?.items || [];

  const handleSelectStatus = (status: PrivateBillingStatus) => {
    let newStatus: PrivateBillingStatus | null = status;

    switch (newStatus) {
      case t('allStatuses'):
        newStatus = null;
        break;
      case t('reminder'):
        newStatus = PrivateBillingStatus.PrivBillingStatus_1stReminder;
        break;
      default:
        break;
    }
    setQuickFilter(newStatus);
    setFilter(INIT_FILTER);
    setOpenListStatus(false);
  };

  const getStatusList = () => {
    return Object.entries(FILTER_OPTIONS).map((pair) => (
      <Flex
        key={pair[0]}
        pb={8}
        style={{ cursor: 'pointer' }}
        onClick={() => handleSelectStatus(pair[0] as PrivateBillingStatus)}
      >
        <Box p={2}>{pair[1]}</Box>
      </Flex>
    ));
  };

  const onChangePage = (page: number) => setPagination({ ...pagination, page });

  const onChangeRowsPerPage = (rowsPerPage: number) =>
    setPagination({ ...pagination, pageSize: rowsPerPage });

  const handleSearch = debounce((query = '') => {
    setQuery(query);
  }, 500);

  const onSelectedRowChange = (selectedRowState: {
    allSelected: boolean;
    selectedCount: number;
    selectedRows: PrivateBillingItem[];
  }) => {
    if (selectedRowState.selectedCount > 0) {
      setSelectedRows(selectedRowState.selectedRows);
    } else {
      setSelectedRows([]);
      printPreviewPdfActions.setSelectedBillingItems([]);
    }
  };

  const onSwitchReviewed = (item: PrivateBillingItem) => {
    ToggleReview.mutate({ id: item.id });
  };

  const onPrint = (item: PrivateBillingItem) => {
    setSelectedRows([]);
    printPreviewPdfActions.setSelectedBillingItems([]);
    printPreviewPdfActions.setCurrentViewInvoice(undefined);
    printPreviewPdfActions.toggleClearTableSelection();
    doctorLetterTimelineActions.setInvoiceEntry(item.currentInvoiceTimelineId!);
    doctorLetterTimelineActions.setActiveBilling(item);
    setIsOpenLetterDialog(true);
  };

  useEffect(() => {
    if (isEmpty(timelineEntry.data?.timelineModels) || !isEmpty(selectedRows)) {
      doctorLetterTimelineActions.setActiveTimelineModel(null);
      return;
    }

    doctorLetterTimelineActions.setDialogMode('edit');
    const currentInvoiceTimeline = timelineEntry.data?.timelineModels[0];

    if (currentInvoiceTimeline) {
      doctorLetterTimelineActions.setActiveTimelineModel(
        currentInvoiceTimeline
      );
    }
  }, [timelineEntry.data?.timelineModels, JSON.stringify(selectedRows)]);

  const onCloseDialogDoctorLetter = () => {
    privBillings.refetch();
    setIsOpenLetterDialog(false);
    printPreviewPdfActions.setCurrentViewInvoice(undefined);
  };

  const submitCreateDoctorLetter = async (
    payload: CreateDoctorLetterRequest
  ) => {
    payload = {
      ...payload,
      patientId: activeBilling?.patient.patientId!,
      treatmentDoctorId: currentLoggedInUser.id!,
    };
    const resp = await createDoctorLetter(payload);
    if (
      !resp?.data?.timelineModel?.id ||
      !resp?.data?.timelineModel?.doctorLetter
    ) {
      alertError('Cannot print this doctor letter');
      return null;
    }
    return resp.data.timelineModel || null;
  };

  const renderDoctorLetterDialog = (
    isOpenLetterDialog: boolean,
    activeBilling: PrivateBillingItem,
    activeTimelineEntry: TimelineModel,
    invoiceEntryId: string
  ) => {
    if (!isOpenLetterDialog) return;
    if (!activeBilling && !activeTimelineEntry) return;
    const billingStatus = activeBilling.status;
    if (
      [
        PrivateBillingStatus.PrivBillingStatus_NoInvoice,
        PrivateBillingStatus.PrivBillingStatus_UnPaid,
        PrivateBillingStatus.PrivBillingStatus_1stReminder,
        PrivateBillingStatus.PrivBillingStatus_2ndReminder,
        PrivateBillingStatus.PrivBillingStatus_3rdReminder,
      ].includes(billingStatus)
    ) {
      //  Payload 1/2/3 remidner is same with NoInvoice
      const payload: DialogContent[PrivateBillingStatus.PrivBillingStatus_NoInvoice] =
        { activeBilling, activeTimelineEntry, invoiceEntryId };

      return (
        <PrivateBillingDialogContent
          t={tDoctorLetter}
          status={billingStatus}
          payload={payload}
          onClose={onCloseDialogDoctorLetter}
          onSubmitDoctorLetter={submitCreateDoctorLetter}
        />
      );
    }
  };

  const onMarkAsPaid = (item: PrivateBillingItem) => {
    MarkPrivateBillingPaid.mutate({
      patientId: item.patient.patientId,
      privateBillingId: item.id,
      invoiceNumber: item.invoiceNumber,
    });
  };

  const onMarkAsUnpaid = (item: PrivateBillingItem) => {
    MarkPrivateBillingUnpaid.mutate({
      patientId: item.patient.patientId,
      privateBillingId: item.id,
    });
  };

  const onMarkAsPartialPaid = (item: PrivateBillingItem) => {
    setPartialPaidItem(item);
  };

  const onSubmitFilter = async (payload: PrivateBillingFilter) => {
    if (payload.dateRange.from && !payload.dateRange.to) return;
    setQuery('');
    setFilter(payload);
    setPagination(PAGINATION_DEFAULT_SORT);
  };

  const onSavePartialPaid = (paidAmount: number) => {
    if (!paidAmount || !partialPaidItem) return;
    MarkPartialPaid.mutate({
      paidAmount,
      privateBillingId: partialPaidItem.id,
      patientId: partialPaidItem.patient.patientId,
    });
    setPartialPaidItem(null);
  };

  const onCancel = (item: PrivateBillingItem) => {
    setOpenItemCancel(item);
  };
  const onConfirmCancel = (item: PrivateBillingItem) => {
    MarkCancelled.mutate({
      privateBillingId: item.id,
      patientId: item.patient.patientId,
    });
  };

  const onBulkPrintSuccess = async (invoices: BulkBillingPayload[]) => {
    const promisors: Array<Promise<TimelineModel | null>> = [];
    invoices.forEach((invoice) => {
      promisors.push(
        submitLetterForPrivateBilling(
          invoice.scheinId,
          invoice.patientId,
          invoice.formValue,
          HandlePrivateInvoice,
          'print',
          invoice.billingId
        )
      );
    });
    try {
      await Promise.all(promisors);
    } catch (error) {
      alertError(error.message);
    } finally {
      setPrintInvoicesDialog(false);
      setDocumentForBilling(undefined);
      printPreviewPdfActions.resetBulkBillingPayload();
      privBillings.refetch();
    }
  };

  const onLoadPrintedDocument = (item: PrivateBillingItem) => {
    setDocumentForBilling(item);
  };
  const onSelectPrintedDocument = (docId: string) => {
    const selectedDoument = GetPrintedInvoices.data?.timelineModels.find(
      (timeline) => timeline.id === docId
    );
    if (selectedDoument) {
      setCurrentPrintedDocument(selectedDoument);
      setOpenViewOnlyDocument(true);
    }
  };

  const shouldShowBulkPrint = (items: PrivateBillingItem[] = []) => {
    if (items.length > MAX_SELECTED_INVOICES) {
      alertError(tDoctorLetter('maxSelectedPages'));
      return false;
    }
    return true;
  };

  const handleBulkAction = (items: PrivateBillingItem[] = []) => {
    setSelectedRows(items);
    printPreviewPdfActions.setSelectedBillingItems(
      items.map((item) => ({
        ...item,
        stager: 'privateBilling',
      }))
    );
    items.length > 0 && setPrintInvoicesDialog(true);
  };

  return (
    <div className={className}>
      <H1 className="header" padding={16}>
        {t('header')}
      </H1>
      <Box p={16} className="views">
        {selectedRows.length === 0 ? (
          <Flex align="center" className="views__filter">
            <Flex className="views__filter--quickfilter">
              <Popover
                position="bottom-right"
                isOpen={openListStatus}
                content={
                  <Flex column p="8px 16px" gap={8}>
                    {getStatusList()}
                  </Flex>
                }
                onClose={() => setOpenListStatus(false)}
              >
                <Svg
                  src={chevronDownIcon}
                  style={{
                    padding: '6px',
                    border: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
                    borderRadius: 4,
                  }}
                  onClick={() => setOpenListStatus(true)}
                />
              </Popover>
              &nbsp;&nbsp;
              <BodyTextM
                className="views__filter--status"
                margin="3px 0 0 0"
                fontWeight={600}
              >
                {quickFilter === null
                  ? t('allStatuses')
                  : FILTER_OPTIONS[quickFilter]}
              </BodyTextM>
            </Flex>
            <Flex className="views__filter--searchbox">
              <InputGroup
                defaultValue=""
                type="text"
                className="sl-panel-search-input"
                leftElement={<Svg src={SearchIconSvgURL} />}
                placeholder={tCommon('Input.search')}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </Flex>
            <Divider />
            <Flex className="views__filter--filter">
              {quickFilter === null ? (
                <Button
                  minimal
                  style={{
                    minWidth: 20,
                    padding: '18px 12px',
                    borderRadius: 4,
                    border: `1px solid ${isFilterActive
                      ? COLOR.BACKGROUND_SELECTED_STRONG
                      : COLOR.BACKGROUND_TERTIARY_DIM
                      }`,
                  }}
                  icon={
                    <Svg
                      src={isFilterActive ? FilterActiveIcon : QuickFilterIcon}
                    />
                  }
                  onClick={() => setOpenFilter((prev) => !prev)}
                />
              ) : (
                <Popover
                  position="bottom-left"
                  canEscapeKeyClose
                  content={
                    <GoaFilter
                      t={t}
                      className="sl-GoaFilter"
                      mode="popover"
                      currentFilter={filter}
                      tCommon={tCommon}
                      doctorList={doctorList}
                      sdikList={sdikList}
                      onClose={() => { }}
                      onReset={() => setFilter(INIT_FILTER)}
                      onSubmit={onSubmitFilter}
                    />
                  }
                  onClose={() => setOpenListStatus(false)}
                >
                  <Svg
                    src={isFilterActive ? FilterActiveIcon : QuickFilterIcon}
                    style={{
                      padding: '9px 12px',
                      border: `1px solid ${isFilterActive
                        ? COLOR.BACKGROUND_SELECTED_STRONG
                        : COLOR.BACKGROUND_TERTIARY_DIM
                        }`,
                      borderRadius: 4,
                    }}
                  />
                </Popover>
              )}
            </Flex>
          </Flex>
        ) : (
          <Flex
            align="center"
            justify="space-between"
            className="views__bulkActions"
          >
            <Flex className="views__bulkAction--buttons">
              <Button
                disabled={
                  selectedRowsNoInvoice.length === 0 || timelineEntry.isFetching
                }
                onClick={() => {
                  if (shouldShowBulkPrint(selectedRowsNoInvoice)) {
                    handleBulkAction(selectedRowsNoInvoice);
                  }
                }}
              >
                <Flex>
                  <Svg width={16} src={PrintIcon} /> &nbsp;&nbsp;
                  {t('printInvoices')}
                </Flex>
              </Button>
              <Button
                disabled={
                  selectedRowsReminder.length === 0 || timelineEntry.isFetching
                }
                onClick={() => {
                  if (shouldShowBulkPrint(selectedRowsReminder)) {
                    handleBulkAction(selectedRowsReminder);
                  }
                }}
              >
                <Flex>
                  <Svg width={16} src={PrintIcon} /> &nbsp;&nbsp;
                  {t('printReminders')}
                </Flex>
              </Button>
            </Flex>
            <Flex>
              <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL}>
                {selectedRows.length}&nbsp;
              </BodyTextM>
              <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL}>
                {t('itemSeleceted')}
              </BodyTextM>
            </Flex>
          </Flex>
        )}
      </Box>

      <Table
        className="sl-table"
        columns={genColumns(
          t,
          onSwitchReviewed,
          onPrint,
          onMarkAsPaid,
          onMarkAsPartialPaid,
          onMarkAsUnpaid,
          onCancel,
          onLoadPrintedDocument,
          onSelectPrintedDocument,
          GetPrintedInvoices.isFetching,
          documentForBilling?.id!,
          GetPrintedInvoices.data?.timelineModels || []
        )}
        noHeader
        selectableRowsHighlight
        striped
        fixedHeader
        selectableRows
        persistTableHead
        highlightOnHover
        data={privBillings.data?.items ?? []}
        progressPending={isLoading}
        onSelectedRowsChange={onSelectedRowChange}
        pagination
        clearSelectedRows={printPreviewPdfStore.toggleCleared}
        paginationServer
        paginationDefaultPage={pagination.page}
        paginationResetDefaultPage
        paginationPerPage={pagination.pageSize}
        paginationTotalRows={privBillings.data?.pagination.total}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
        customStyles={TABLE_STYLES}
        noDataComponent={
          <Flex mt={100} style={{ textAlign: 'center' }} column>
            <BodyTextL
              fontSize="20px"
              fontWeight={700}
              color={COLOR.TEXT_SECONDARY_NAVAL}
            >
              {t('noResultFoundTitle')}
            </BodyTextL>
            <BodyTextL padding="8px 0 0" color={COLOR.TEXT_SECONDARY_NAVAL}>
              {t('noResultFoundDescription')}
            </BodyTextL>
          </Flex>
        }
      />

      {/* OPEN FILTER DRAWER */}
      {quickFilter === null ? (
        <Dialog
          title={'Filter'}
          isOpen={openFilter}
          className={`${className} dialog-right`}
          style={{ width: 500 }}
          canEscapeKeyClose={true}
          isCloseButtonShown={true}
          backdropProps={{ hidden: true }}
          onClose={() => setOpenFilter(false)}
          canOutsideClickClose={false}
        >
          <GoaFilter
            t={t}
            mode="drawer"
            currentFilter={filter}
            tCommon={tCommon}
            doctorList={doctorList}
            sdikList={sdikList}
            onClose={() => setOpenFilter(false)}
            onSubmit={onSubmitFilter}
            onReset={() => setFilter(INIT_FILTER)}
          />
        </Dialog>
      ) : null}

      {/* OPEN LETTER FROM PRIVATE BILLING HISTORY */}
      {renderDoctorLetterDialog(
        isOpenLetterDialog,
        activeBilling!,
        activeTimelineModel!,
        invoiceEntry!
      )}

      {/* OPEN PREVEW FROM PRIVATE BILLING HISTORY FOR BULK ONLY*/}
      {printPreviewPdfStore.fileUrl && (
        <PrintPreviewPdfDialog
          formId="Private_Invoice"
          file={printPreviewPdfStore.fileUrl}
          isBulkPrint
          onPrintSuccess={async () => {
            // view
            if (currentPrintedDocument) {
              alertSuccessfully(tDoctorLetter('printSuccess'));
              setIsOpenLetterDialog(false);
              return;
            }

            // print
            onBulkPrintSuccess(printPreviewPdfStore.bulkBillingPayload);
            alertSuccessfully(tDoctorLetter('printSuccess'));
          }}
          onClose={() => {
            printPreviewPdfActions.setCurrentViewInvoice(undefined);
            printPreviewPdfActions.setOpenPrintPreviewDialog(null);
            setDocumentForBilling(undefined);
            setCurrentPrintedDocument(undefined);
            onCloseDialogDoctorLetter();
          }}
        />
      )}

      {partialPaidItem?.id && (
        <PartialPaidDialog
          className="sl-PartialPaidDialog"
          t={t}
          isLoading={MarkPartialPaid.isPending}
          tButton={tCommon}
          item={partialPaidItem}
          onClose={() => setPartialPaidItem(null)}
          onSave={onSavePartialPaid}
        />
      )}

      {openItemCancel?.id && (
        <ConfirmDialog
          isOpen={true}
          close={() => setOpenItemCancel(null)}
          confirm={() => onConfirmCancel(openItemCancel)}
          text={{
            btnCancel: tCommon('ButtonActions.no'),
            btnOk: tCommon('ButtonActions.yesCancel'),
            title: t('invoiceCancelledTitle'),
            message: t('invoiceCancelledDes'),
          }}
        />
      )}
      {printInvoicesDialog && (
        <DoctorLetterCreateEditDialog
          templates={templates.data?.letterTemplates}
          activatedSchein={{
            type: 'private',
            scheinId: printPreviewPdfStore.currentViewInvoice
              ? printPreviewPdfStore.currentViewInvoice.stager ===
                'privateBilling'
                ? printPreviewPdfStore.currentViewInvoice.privScheinId
                : printPreviewPdfStore.currentViewInvoice.id
              : undefined,
          }}
          onlyBulkStager
          mode="create"
          successToastMessage={tDoctorLetter('saveSuccess')}
          onPrint={async (editedTimelineModel) => {
            if (!editedTimelineModel?.id) {
              alertError('Cannot print this doctor letter');
              return null;
            }

            const { data } = await getPdfPresignedUrl({
              doctorLetterId: editedTimelineModel.id,
            });
            if (!data?.url) {
              alertError('Cannot open print preview for this doctor letter');
              return null;
            }
            printPreviewPdfActions.setPrintingDoctorLetter({
              timelineModelId: editedTimelineModel.id,
              doctorLetter: editedTimelineModel.doctorLetter,
            });
            printPreviewPdfActions.setOpenPrintPreviewDialog(data.url);
          }}
          onSubmit={async (newDoctorLetterValues: DoctorLetterExpanded) => {
            const { billingId, scheinId, patientId } = newDoctorLetterValues;
            if (billingId && scheinId && patientId) {
              const updatedTimelineEntry = await submitLetterForPrivateBilling(
                scheinId,
                patientId,
                {
                  ...newDoctorLetterValues,
                  id: newDoctorLetterValues.id,
                },
                HandlePrivateInvoice,
                'save'
              );
              return updatedTimelineEntry || undefined;
            } else if (activeBilling && activeBilling.privScheinId) {
              const updatedTimelineEntry = await submitLetterForPrivateBilling(
                activeBilling.privScheinId,
                activeBilling.patient.patientId,
                {
                  ...newDoctorLetterValues,
                  id: activeBilling.privScheinId,
                },
                HandlePrivateInvoice,
                'save'
              );
              return updatedTimelineEntry || undefined;
            }

            return undefined;
          }}
          bulkSelected={
            selectedRows
              ? selectedRows.map((row) => ({
                ...row,
                stager: 'privateBilling',
              }))
              : undefined
          }
          timelineSelected={timelineEntry.data?.timelineModels ?? []}
          onClose={() => {
            privBillings.refetch();
            printPreviewPdfActions.setCurrentViewInvoice(undefined);
            setPrintInvoicesDialog(false);
            printPreviewPdfActions.toggleClearTableSelection();
            setSelectedRows([]);
          }}
          onBulkActionSuccess={() => privBillings.refetch()}
        />
      )}

      {openViewOnlyDocument && (
        <DoctorLetterViewOnlyDialog
          t={tDoctorLetter}
          privBilling={documentForBilling}
          letterContent={currentPrintedDocument?.doctorLetter}
          onClose={() => setOpenViewOnlyDocument(false)}
          handlePrivateInvoice={HandlePrivateInvoice}
        />
      )}

      {HandlePrivateInvoice.isPending && (
        <LoadingState />
      )}
    </div>
  );
};

export default PrivateBillingHistory;
