import { Moment } from 'moment';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientFileSidebar.json';

import {
  BodyTextL,
  BodyTextM,
  BodyTextS,
  Flex,
  LoadingState,
  Svg,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Button,
  Collapse,
  Divider,
  Icon,
  Intent,
  Popover,
  PopoverPosition,
  Spinner,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  useListenTimelineCreate,
  useListenTimelineRemove,
  useListenTimelineUpdate,
} from '@tutum/hermes/bff/app_mvz_timeline';
import {
  DiagnoseResponse,
  P4ValidationResponse,
  getP4ValidationReport,
  getP4ValidationReportByQuarter,
  useMutationUpdatePermanentDiagnoseEndDate,
  useQueryGetPermanentDiagnose,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_sidebar';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';
import { DiagnoseType } from '@tutum/hermes/bff/service_domains_patient_file';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { FFWrapper, useFeatureFlag } from '@tutum/mvz/hooks/useFeatureFlag';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IContractDoctors } from '../../types/contract.type';
import { UpdatingData } from '../terminate-permanent-diagnose-dialog/TerminatePermanentDiagnoseDialog';
import TerminatePermanentDiagnoseDialog from '../terminate-permanent-diagnose-dialog/TerminatePermanentDiagnoseDialog.styled';
import { makePermanentDiagnoseContent } from '../timeline/timeline-content/timeline-entry/diagnose-entry/helpers';
import { useQueryGetContractInformationFromMvz } from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { ContractType, PatientParticipationStatus } from '@tutum/hermes/bff/common';

const InfoIcon = '/images/icon-info.svg';
const AddDiagnoseEndDateIcon = '/images/add-diagnose-end-date-icon.svg';

export interface IPermanentDiagnoseProps {
  className?: string;
  theme?: IMvzTheme;
  patientId: string;
  billingQuarter?: Array<{
    year: number;
    quarter: number;
  }>;
  contracts: IContractDoctors[];
}

function PermanentDiagnose(
  props: IPermanentDiagnoseProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.PermanentDiagnose>
) {
  const { className, patientId, t, billingQuarter, contracts } = props;

  const patientContext = useContext(GlobalContext.instance);
  const [p4ValidationReport, setP4ValidationReport] = useState<
    P4ValidationResponse[]
  >([]);

  const [expandPermanentDiagnose, setExpandPermanentDiagnose] =
    useState<boolean>(true);
  const [
    expandTerminatePermanentDiagnose,
    setExpandTerminatePermanentDiagnose,
  ] = useState<boolean>(false);
  const [expandP4Report, setExpandP4Report] = useState<boolean>(true);

  const [
    openTerminatePermanentDiagnoseDialog,
    setOpenTerminatePermanentDiagnoseDialog,
  ] = useState<boolean>(false);

  const [selectedDiagnose, setSelectedDiagnose] =
    useState<DiagnoseResponse | undefined>(undefined);

  const [isLoadingP4, setIsLoadingP4] = useState(false);
  const [lastUpdatedAt, setLastUpdatedAt] =
    useState<Nullable<Moment>>(undefined);

  const ffHZV = useFeatureFlag({
    ffKey: FeatureFlagKey.FeatureFlagKey_SV,
  });

  const { data: dataGetContractInformationHzv } =
    useQueryGetContractInformationFromMvz(
      {
        contractType: ContractType.ContractType_HouseDoctorCare,
        patientId,
      },
      {
        enabled: false,
        select: (res) => {
          return res.data.contractInformations[0];
        },
      }
    );

  const { isLoading, data, refetch } = useQueryGetPermanentDiagnose(
    {
      patientId,
    },
    {
      enabled: !!patientId,
      select: (permanentDiagnoses) => {
        const permanentDiagnosesUnique =
          permanentDiagnoses.data.diagnoses.filter(
            (item, index, self) =>
              index ===
              self.findIndex(
                (t) => t.code === item.code && t.certainty === item.certainty
              )
          );
        return {
          permanentDiagnosesUnique: permanentDiagnosesUnique,
          permanentDiagnoses: permanentDiagnosesUnique,
          terminatedPermanentDiagnoses: permanentDiagnosesUnique
            .filter(
              (item) => item.validUntil && item.validUntil <= datetimeUtil.now()
            )
            .sort((o1, o2) => (o1.validUntil || 0) - (o2.validUntil || 0)),
        };
      },
    }
  );

  const hasAWH_01 = useMemo(() => {
    return !!contracts &&
      !!contracts.find((contract) => contract.contractId === 'AWH_01')
  }, [contracts])

  const hasP4Report = useMemo(() => {
    return p4ValidationReport.some((response) => {
      return response.report.diseaseGroups.length > 0;
    });
  }, [p4ValidationReport]);

  const handleGetP4ValidationReport = useCallback(
    async (isUserForceUpdate?: boolean) => {
      setIsLoadingP4(true);
      try {
        let p4ValidationReport: P4ValidationResponse[] = [],
          lastUpdatedAt: Nullable<Moment> = undefined;
          // TODO: use contract data with requirement ID to check if has ABRD965, now can hardcode to check AWH_01 contract
        if (
          (contracts || []).some((contract) => contract.contractId === 'AWH_01')
        ) {
          if (billingQuarter && billingQuarter.length > 0) {
            const data = await getP4ValidationReportByQuarter({
              patientId,
              yearQuarters: billingQuarter,
            });
            p4ValidationReport = [...data.data.validationReport];
            lastUpdatedAt = datetimeUtil.unixToMoment(data.data.lastUpdatedAt);
          } else {
            const data = await getP4ValidationReport({ patientId });
            p4ValidationReport = [data.data.data];
            lastUpdatedAt = datetimeUtil.unixToMoment(data.data.lastUpdatedAt);
          }
        }
        setP4ValidationReport(p4ValidationReport);
        setLastUpdatedAt(lastUpdatedAt);
        if (isUserForceUpdate) {
          alertSuccessfully(t('p4UpdateSuccess'));
        }
      } catch (err) {
        console.error(err);
        if (isUserForceUpdate) {
          alertError(t('p4UpdateFail'));
        }
      } finally {
        setIsLoadingP4(false);
      }
    },
    [contracts, billingQuarter, patientId]
  );

  const updatePermanentDiagnoseEndDate =
    useMutationUpdatePermanentDiagnoseEndDate({
      onSuccess: () => {
        alertSuccessfully(t('diagnoseEnddateSavedSuccessful'));
        refetch();
        handleGetP4ValidationReport();
        setOpenTerminatePermanentDiagnoseDialog(false);
      },
    });

  const isShowPermanentDiagnoseBar = useMemo(() => {
    return !!data?.permanentDiagnoses.length || !!data?.terminatedPermanentDiagnoses.length;
  }, [data]);

  useEffect(() => {
    handleGetP4ValidationReport();
  }, [handleGetP4ValidationReport]);

  useListenTimelineCreate((response) => {
    if (
      response.timelineModel?.encounterDiagnoseTimeline?.type ===
      DiagnoseType.DIAGNOSETYPE_PERMANENT
    ) {
      refetch();
      handleGetP4ValidationReport();
    }
  });

  useListenTimelineRemove((response) => {
    if (
      response.timelineModel?.encounterDiagnoseTimeline?.type ===
      DiagnoseType.DIAGNOSETYPE_PERMANENT
    ) {
      refetch();
      handleGetP4ValidationReport();
    }
  });

  useListenTimelineUpdate((response) => {
    const oldType = response.oldTimelineModel?.encounterDiagnoseTimeline?.type;
    const updatedType = response.timelineModel?.encounterDiagnoseTimeline?.type;
    if (
      oldType === DiagnoseType.DIAGNOSETYPE_PERMANENT ||
      updatedType === DiagnoseType.DIAGNOSETYPE_PERMANENT
    ) {
      refetch();
      handleGetP4ValidationReport();
    }
  });

  if (isLoading) {
    return null;
  }

  const onSaveTerminatePermanentDiagnoseDialog = (
    diagnoseId: string,
    patientId: string,
    endDate: number
  ) => {
    if (!diagnoseId || !patientId || !endDate) {
      return;
    }

    updatePermanentDiagnoseEndDate.mutate({
      patientId,
      diagnoseId,
      endDate,
    });
  };

  const onOpenTerminatePermanentDiagnoseDialog = (
    diagnose: DiagnoseResponse
  ) => {
    setOpenTerminatePermanentDiagnoseDialog(true);
    setSelectedDiagnose(diagnose);
  };

  const renderTerminatedPermanentDiagnoseDiaglog = () => {
    return (
      <TerminatePermanentDiagnoseDialog
        updatingData={
          {
            encounterDate: selectedDiagnose?.validUntil
              ? new Date(selectedDiagnose.validUntil)
              : selectedDiagnose?.startDate
                ? new Date(selectedDiagnose.startDate)
                : null,
            diagnoseText: selectedDiagnose
              ? makePermanentDiagnoseContent(selectedDiagnose)
              : null,
          } as UpdatingData
        }
        isOpen={openTerminatePermanentDiagnoseDialog}
        onClose={() => setOpenTerminatePermanentDiagnoseDialog(false)}
        isLoading={updatePermanentDiagnoseEndDate.isPending}
        onSave={(diagnoseEndDate) =>
          onSaveTerminatePermanentDiagnoseDialog(
            selectedDiagnose?.id as string,
            selectedDiagnose?.patientId as string,
            diagnoseEndDate?.encounterDate.getTime()
          )
        }
      />
    );
  };

  const renderDate = (datetime: number) => {
    if (!datetime) {
      return null;
    }

    const targetDate = new Date(datetime);
    const hhmm = toDateFormat(targetDate, {
      timeFormat: 'hh:mm',
    });
    const ddmmyyyy = toDateFormat(targetDate, {
      dateFormat: 'dd.MM.yyyy',
    });

    return t('dateAtTime', {
      date: `${ddmmyyyy}`,
      time: `${hhmm}`,
    });
  };

  const renderToolTipContentAddedBy = (name: string, date: number) => {
    return (
      <Flex column>
        <BodyTextS>{t('addedByMessage', { name })}</BodyTextS>
        <BodyTextS>{renderDate(date)}</BodyTextS>
      </Flex>
    );
  };

  const renderToolTipContentEditedBy = (name: string, date: number) => {
    return (
      <Flex column>
        <BodyTextS>{t('lastEditedByMessage', { name })}</BodyTextS>
        <BodyTextS>{renderDate(date)}</BodyTextS>
      </Flex>
    );
  };

  const renderPermanentDiagnoses = () => {
    return (
      <Flex
        auto
        column
        className={'permanent-diagnose-collapse sl-border-bottom'}
      >
        <Flex
          align="center"
          justify="space-between"
          className={'permanent-diagnose-header'}
          onClick={() => setExpandPermanentDiagnose(!expandPermanentDiagnose)}
        >
          <BodyTextL className="permanent-diagose-title" fontWeight={600}>
            {t('permanentDiagnoses')}
          </BodyTextL>
          <Icon
            icon={expandPermanentDiagnose ? 'chevron-up' : 'chevron-down'}
          />
        </Flex>
        {isLoading && (
          <Flex>
            <LoadingState size={50} />
          </Flex>
        )}
        <Collapse isOpen={expandPermanentDiagnose}>
          {data?.permanentDiagnosesUnique.length ? (
            data.permanentDiagnosesUnique.map((diagnose) => {
              return (
                <Flex
                  key={diagnose.id}
                  className="sl-diagnose-item"
                  align="center"
                >
                  <Flex auto>
                    <Tooltip
                      content={renderToolTipContentAddedBy(
                        patientContext.getDoctorName(
                          diagnose.auditLog?.addedUserId || ''
                        ),
                        diagnose.auditLog?.addedDate || 0
                      )}
                      position={PopoverPosition.TOP}
                    >
                      <BodyTextM>{`• ${makePermanentDiagnoseContent(
                        diagnose
                      )}`}</BodyTextM>
                    </Tooltip>
                  </Flex>
                  <Flex>
                    <Tooltip
                      content={<BodyTextS>{t('addDiagnoseEnddate')}</BodyTextS>}
                      position={PopoverPosition.TOP}
                      transitionDuration={0}
                      autoFocus={false}
                    >
                      <Svg
                        src={AddDiagnoseEndDateIcon}
                        onClick={() =>
                          onOpenTerminatePermanentDiagnoseDialog(diagnose)
                        }
                      />
                    </Tooltip>
                  </Flex>
                </Flex>
              );
            })
          ) : (
            <Flex className="empty-report">
              {t('permanentDiagnosesEmptyMessage')}
            </Flex>
          )}
        </Collapse>
        {selectedDiagnose && renderTerminatedPermanentDiagnoseDiaglog()}
      </Flex>
    );
  };

  const renderP4Report = () => {
    return (
      <Flex auto column className={'p4-report-collapse'}>
        <Flex
          justify="space-between"
          className={'p4-report-header'}
          onClick={() => setExpandP4Report(!expandP4Report)}
        >
          <Flex align="center">
            <BodyTextM fontWeight={600}>{t('p4ValidationReport')}</BodyTextM>
            <Popover
              popoverClassName="bp5-popover-content-sizing bp5-dark"
              content={<Flex>{t('p4PopoverInformation')}</Flex>}
              interactionKind="hover"
            >
              <Svg src={InfoIcon} />
            </Popover>
          </Flex>
          <Icon icon={expandP4Report ? 'chevron-up' : 'chevron-down'} />
        </Flex>
        <Collapse isOpen={expandP4Report}>
          {hasP4Report ? (
            <Flex className="p4-report">
              {isLoadingP4 ? (
                <Spinner />
              ) : (
                <React.Fragment>
                  <Flex>
                    <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                      {t('p4Desctiption')}
                    </BodyTextM>
                  </Flex>
                  {p4ValidationReport.map((group) =>
                    group.report.diseaseGroups.map((item) => (
                      <Flex
                        key={group.year + group.quarter + item.groupCode}
                        column
                      >
                        <BodyTextM>{`Q${group.quarter}/${group.year} • ${item.groupName} - ${item.groupCode}`}</BodyTextM>
                      </Flex>
                    ))
                  )}
                </React.Fragment>
              )}
              <br />
            </Flex>
          ) : (
            <Flex className="empty-report">{t('p4EmptyMessage')}</Flex>
          )}
          <Flex align="center" justify="space-between">
            <Flex>
              {isShowPermanentDiagnoseBar && (
                <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL2}>
                  {t('p4LastUpdatedAt')}:{' '}
                  {lastUpdatedAt?.format(DATE_TIME_WITHOUT_SECONDS_FORMAT)}
                </BodyTextS>
              )}
            </Flex>
            {dataGetContractInformationHzv?.participation?.status ===
              PatientParticipationStatus.PatientParticipation_Active && (
                <Button
                  minimal
                  small
                  onClick={() => handleGetP4ValidationReport(true)}
                  intent={Intent.PRIMARY}
                >
                  <BodyTextM fontWeight={600} color={COLOR.TEXT_INFO}>
                    {t('p4Validate')}
                  </BodyTextM>
                </Button>
              )}
          </Flex>
        </Collapse>
      </Flex>
    );
  };

  const renderTerminatedPermanentDiagnose = () => {
    return (
      <Flex
        auto
        column
        className={'terminated-permanent-diagnose-collapse sl-border-bottom'}
      >
        <Flex
          align="center"
          justify="space-between"
          className={'terminated-permanent-diagnose-header'}
          onClick={() =>
            setExpandTerminatePermanentDiagnose(
              !expandTerminatePermanentDiagnose
            )
          }
        >
          <BodyTextM fontWeight={600}>
            {t('terminatedPermanentDiagnoses')}
          </BodyTextM>
          <Icon
            icon={
              expandTerminatePermanentDiagnose ? 'chevron-up' : 'chevron-down'
            }
          />
        </Flex>
        <Collapse isOpen={expandTerminatePermanentDiagnose}>
          {(data?.terminatedPermanentDiagnoses || []).map((diagnose) => (
            <Flex key={diagnose.id} className={'sl-diagnose-item'} column>
              <Tooltip
                content={
                  diagnose.auditLog?.lastUpdatedUserId ||
                    diagnose.auditLog?.lastUpdatedDate
                    ? renderToolTipContentEditedBy(
                      patientContext.getDoctorName(
                        diagnose.auditLog?.lastUpdatedUserId || ''
                      ),
                      diagnose.auditLog?.lastUpdatedDate || 0
                    )
                    : renderToolTipContentAddedBy(
                      patientContext.getDoctorName(
                        diagnose.auditLog?.addedUserId || ''
                      ),
                      diagnose.auditLog?.addedDate || 0
                    )
                }
                position={PopoverPosition.TOP}
              >
                <Flex auto column>
                  <BodyTextM>{`${makePermanentDiagnoseContent(
                    diagnose
                  )}`}</BodyTextM>
                  <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER}>{`${t(
                    'endDate'
                  )} ${diagnose.validUntil ? new Date(diagnose?.validUntil).toLocaleDateString(
                    'de'
                  ) : ''}`}</BodyTextM>
                </Flex>
              </Tooltip>
            </Flex>
          ))}
        </Collapse>
      </Flex>
    );
  };

  if (!isShowPermanentDiagnoseBar && !(ffHZV && hasAWH_01)) {
    return null;
  }

  return (
    <>
      <Flex className={className} column>
        {isShowPermanentDiagnoseBar && renderPermanentDiagnoses()}
        <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_SV}>
          {hasAWH_01 && renderP4Report()}
        </FFWrapper>
        {!!data?.terminatedPermanentDiagnoses.length &&
          renderTerminatedPermanentDiagnose()}
      </Flex>
      <Divider className="side-bar__divider" />
    </>
  )
}

export default I18n.withTranslation(PermanentDiagnose, {
  namespace: 'PatientFileSidebar',
  nestedTrans: 'PermanentDiagnose',
});
