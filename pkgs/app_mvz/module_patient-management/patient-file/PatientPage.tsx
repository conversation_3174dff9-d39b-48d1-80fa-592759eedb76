import dynamic from 'next/dynamic';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import {
  BodyTextL,
  Flex,
  H2,
  LoadingState,
  Svg,
} from '@tutum/design-system/components';
import {
  Divider,
  Tab,
  Tabs,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { MainGroup } from '@tutum/hermes/bff/common';
import { createOrUpdate } from '@tutum/hermes/bff/legacy/app_mvz_daily_list';
import {
  PatientMedicalData,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import PatientManagementUtil from '@tutum/infrastructure/utils/form.util';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { ActionChainPanel } from '@tutum/mvz/module_action-chain';
import {
  FormElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import CreateSchein from '@tutum/mvz/module_bg_schein/CreateSchein.styled';
import { IDigaProps } from '@tutum/mvz/module_diga/Diga.type';
import type { IFormOverviewProps } from '@tutum/mvz/module_form/form-overview';
import { getFormIdDisplay } from '@tutum/mvz/module_form/form-overview/FormOverview.helper';
import { heimiSelectionActions } from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import type { IHeimiProps } from '@tutum/mvz/module_heimi/heimi/Heimi';
import type { IHimiProps } from '@tutum/mvz/module_himi/himi/Himi';
import CreateSvSchein from '@tutum/mvz/module_kv_hzv_schein/CreateSVSchein/CreateSchein.styled';
import Schein from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.styled';
import type { FORM_SECTION } from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import Lab from '@tutum/mvz/module_lab/lab-results/Lab.styled';
import { IMedicationContext } from '@tutum/mvz/module_medication/context/MedicationContext';
import type { IMedicationProps as IMedicationKBVProps } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import MedicalData from '@tutum/mvz/module_patient-management/patient-file/medical-data/MedicalData.styled';
import ScheinHistory from '@tutum/mvz/module_patient-management/patient-file/schein-history';
import Timeline from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.styled';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import CreatePrivateScheinModal from '@tutum/mvz/module_private_igel_schein';
import { IPatientManagement } from '../contexts/patient-management/PatientManagementContext.type';
import {
  getContentHistoryViewer,
  getInsuranceCardByQuarter,
} from './PatientFile.helper';
import Allergies from './allergies/Allergies.styled';
import Cave from './cave/Cave.styled';
import PatientInformation from './patient-information/PatientInformation.styled';
import PatientLog from './patient-log/PatientLog.styled';
import PermanentDiagnose from './permanent-diagnose/PermanentDiagnose.styled';
import { SourceScreen } from './timeline/Timeline.hooks';
import { timelineActions, useTimeLineStore } from './timeline/Timeline.store';
import { getAge } from '@tutum/design-system/infrastructure/utils';
import { formOverviewActions } from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import GlobalContext from '@tutum/mvz/contexts/Global.context';

const legendIcon = '/images/alert-circle-solid-purple.svg';
const historyViewIcon = '/images/clock-ccw.svg';

// Medication components
const MedicationProvider: React.ComponentType<
  React.PropsWithChildren<{
    value: IMedicationContext;
  }>
> = dynamic(
  () => import('@tutum/mvz/module_medication/context/MedicationProvider'),
  {
    loading() {
      return <LoadingState />;
    },
    ssr: false,
  }
);

const MedicationKBV: React.ComponentType<IMedicationKBVProps> = dynamic(
  () =>
    import('@tutum/mvz/module_medication_kbv/medication/MedicationKBV.styled'),
  {
    loading() {
      return <LoadingState />;
    },
    ssr: false,
  }
);

// Form component
const FormOverView: React.ComponentType<IFormOverviewProps> = dynamic(
  () => import('@tutum/mvz/module_form/form-overview'),
  {
    loading() {
      return <LoadingState />;
    },
    ssr: false,
  }
);

// Heimi component
const Heimi: React.ComponentType<IHeimiProps> = dynamic(
  () => import('@tutum/mvz/module_heimi/heimi/Heimi.styled'),
  {
    loading() {
      return <LoadingState />;
    },
    ssr: false,
  }
);

// Himi component
const Himi: React.ComponentType<IHimiProps> = dynamic(
  () => import('@tutum/mvz/module_himi/himi/Himi.styled'),
  {
    loading() {
      return <LoadingState />;
    },
    ssr: false,
  }
);

// DiGA component
const DiGA: React.ComponentType<IDigaProps> = dynamic(
  () => import('@tutum/mvz/module_diga/Diga.styled'),
  {
    loading() {
      return <LoadingState />;
    },
    ssr: false,
  }
);

type PatientPageProps = {
  patientManagement: IPatientManagement;
  viewTimelineFromOutside: boolean;
  billingQuarter: Array<{ year: number; quarter: number }>;
  t: IFixedNamespaceTFunction<keyof typeof PatientManagementI18n.PatientFile>;
  activeTabId: ID_TABS;
  setActiveTabId: (tabId: ID_TABS) => void;
  toggleOpenSchein: (scheinId?: string, scrollToSection?: FORM_SECTION) => void;
  setMedicalData: (medicalData: PatientMedicalData) => void;
  setMedicalDataUpdatedAt: (medicalDataUpdatedAt: number) => void;
  setIsEditingProfile: (state: boolean) => void;
  openEditPatientWithScroll: (sectionId: string) => void;
  scheinIdToEdit?: string;
  currentSchein?: ScheinItem;
  isActivatedSvSchein: boolean;
  activatedSchein: boolean;
  isReadTICard: boolean;
  isHistoryMode: boolean;
  scrollToSection?: FORM_SECTION;
  toggleOpenScheinBySelect: (
    typeSchein: string | null,
    scheinId?: string
  ) => void;
  showCreateSchein: any;
  setScheinIdToEdit: (scheinId?: string) => void;
};

const PatientPage = (props: PatientPageProps) => {
  const {
    patientManagement,
    activeTabId,
    toggleOpenSchein,
    setIsEditingProfile,
    billingQuarter,
    setMedicalData,
    setMedicalDataUpdatedAt,
    setActiveTabId,
    currentSchein,
    viewTimelineFromOutside,
    t,
    scheinIdToEdit,
    activatedSchein,
    isReadTICard,
    isHistoryMode,
    scrollToSection,
    toggleOpenScheinBySelect,
    showCreateSchein,
    setScheinIdToEdit,
  } = props;

  const timelineStore = useTimeLineStore();
  const [isShowDMPEnrollDialog, setShowDMPEnrollDialog] =
    useState<boolean>(false);
  const [isOpenPatientLog, setIsOpenPatientLog] = useState<boolean>(false);
  const { t: tForms } = I18n.useTranslation({
    namespace: 'Form',
    nestedTrans: 'Forms',
  });

  const { globalData } = useContext(GlobalContext.instance);
  const bsnr = globalData.userProfile?.bsnr;
  const timelineRef = useRef<{ reloadQuarters: ReloadQuarterFunc }>(null);
  const isDisabled = useMemo(() => {
    const cardInsurance = getInsuranceCardByQuarter(
      patientManagement?.patient?.patientInfo?.insuranceInfos,
      datetimeUtil.now()
    );

    const isPublicInsurance =
      cardInsurance?.insuranceType === TypeOfInsurance.Public;

    return !!cardInsurance && isPublicInsurance;
  }, [patientManagement?.patient?.patientInfo?.insuranceInfos]);

  const patientFileStore = usePatientFileStore();
  const { history: historyViewPatient } = patientFileStore;

  const patientName = useMemo(() => {
    if (
      patientManagement &&
      patientManagement.patient &&
      patientManagement.patient.patientInfo
    ) {
      return PatientManagementUtil.getFullName(
        patientManagement.patient.patientInfo.personalInfo?.title || '',
        patientManagement.patient.patientInfo.personalInfo?.intendWord || '',
        patientManagement.patient.patientInfo.personalInfo?.lastName || '',
        patientManagement.patient.patientInfo.personalInfo?.firstName || ''
      );
    }
    return '';
  }, [JSON.stringify(patientManagement?.patient?.patientInfo?.personalInfo)]);

  // State to track which tabs have been mounted
  const [mountedTabs, setMountedTabs] = useState<Set<ID_TABS>>(
    new Set([activeTabId])
  );

  useEffect(() => {
    if (!isReadTICard || !currentSchein) {
      return;
    }

    setShowDMPEnrollDialog(patientFileStore.patient.isReadingCard);
  }, [isReadTICard, currentSchein, patientFileStore.patient.isReadingCard]);

  useEffect(() => {
    if (!patientManagement.patient?.id) {
      return;
    }
    createOrUpdate({
      patientId: patientManagement.patient?.id,
      scheinId: currentSchein?.scheinId,
    });
  }, [patientManagement.patient?.id, currentSchein?.scheinId]);

  useEffect(() => {
    if (
      !patientManagement.patient?.id ||
      !patientManagement.selectedContractDoctor?.doctorId
    ) {
      return;
    }

    formOverviewActions.loadData(
      patientManagement.patient,
      patientManagement.selectedContractDoctor
    );

    const age = patientManagement.patient?.dateOfBirth
      ? getAge(new Date(patientManagement.patient.dateOfBirth))
      : -1;

    if (!age || !bsnr) {
      return;
    }

    heimiSelectionActions.loadData(
      patientManagement.patient?.id || '',
      age,
      bsnr,
      patientManagement.selectedContractDoctor
    );
  }, [
    JSON.stringify(patientManagement.patient),
    bsnr,
    JSON.stringify(patientManagement.selectedContractDoctor),
  ]);

  const onClosePrintPreview = () => {
    timelineActions.setPreviewForm('', '', '');
  };

  const handleReload = (payload: { year: number; quarter: number }) => {
    if (timelineRef.current) {
      timelineRef.current.reloadQuarters(payload);
    }
  };

  return (
    <Flex auto column>
      {isHistoryMode && (
        <Flex className="restoration-legend" justify="center" align="center">
          <Flex
            justify="center"
            align="center"
            className="restoration-legend__tag"
          >
            <Flex className="restoration-legend__tag--legend">
              <Svg src={legendIcon} className="restoration-legend__icon" />
              {t('legend')}
            </Flex>
            <span className="restoration-legend__tag--created">
              {t('created')}
            </span>
            <span className="restoration-legend__tag--removed">
              {t('removed')}
            </span>
            <span className="restoration-legend__tag--restored">
              {t('restored')}
            </span>
            <span className="restoration-legend__tag--updated">
              {t('updated')}
            </span>
          </Flex>
        </Flex>
      )}

      <Flex className="sl-Timeline-Wrap">
        {!!patientManagement.patient && (
          <Flex className="side-bar">
            <PatientInformation
              patient={patientManagement.patient}
              activeTabId={activeTabId}
              isDisabled={isDisabled}
              isShowDMPEnrollDialog={isShowDMPEnrollDialog}
              onEdit={() => setIsEditingProfile(true)}
              openCreateSchein={toggleOpenSchein}
            />
            <Divider className="side-bar__divider" />
            <Cave patient={patientManagement.patient} isBottomHeadMenu />
            <Allergies
              patient={patientManagement.patient}
              setMedicalData={setMedicalData}
              setMedicalDataUpdatedAt={setMedicalDataUpdatedAt}
            />
            <ScheinHistory
              patient={patientManagement.patient}
              patientParticipation={
                patientManagement?.getPatientParticipationResponse
                  ?.participations
              }
              openCreateSchein={toggleOpenSchein}
              setShowCreateSchein={toggleOpenScheinBySelect}
              reloadQuarters={handleReload}
            />

            <Divider className="side-bar__divider" />
            <PermanentDiagnose
              billingQuarter={billingQuarter}
              patientId={patientManagement.patient.id}
              contracts={patientManagement.availableHzvContracts}
            />
            <MedicalData
              patient={patientManagement.patient}
              setMedicalData={setMedicalData}
              setMedicalDataUpdatedAt={setMedicalDataUpdatedAt}
            />
          </Flex>
        )}
        {!!patientManagement.patient && (
          <Tabs
            onChange={(newTabId: ID_TABS) => {
              if (newTabId === ID_TABS.HEIMI) {
                heimiSelectionActions.clear();
              }
              // Add the new tab ID to the set of mounted tabs
              setMountedTabs((prev) => new Set(prev).add(newTabId));
              setActiveTabId(newTabId);
            }}
            selectedTabId={activeTabId}
            className="timeline sl-patient-file-tabs"
          >
            <Tab
              id={ID_TABS.TIMELINE}
              className="tab-timeline"
              title={
                <BodyTextL className="sl-tab-header" textAlign="center">
                  {t('TIME_LINE_TAB')}
                </BodyTextL>
              }
              disabled={patientFileStore.schein.originalList.length === 0}
              panel={
                <>
                  {patientFileStore.schein.originalList.length > 0 ||
                    timelineStore.isHasTimelineEntry ? (
                    <Flex className="sl-timeline" column auto>
                      <MedicationProvider value={{} as IMedicationContext}>
                        <Timeline
                          ref={timelineRef}
                          viewTimelineFromOutside={viewTimelineFromOutside}
                          patientId={patientManagement?.patient?.id}
                          openCreateSchein={(scheinMainGroup?: MainGroup) => {
                            if (!scheinMainGroup) {
                              toggleOpenSchein();
                              return;
                            }

                            toggleOpenScheinBySelect(scheinMainGroup);
                          }}
                          sourceScreen={SourceScreen.PATIENT_MANAGEMENT}
                          openEditSchein={toggleOpenSchein}
                        />
                      </MedicationProvider>
                    </Flex>
                  ) : (
                    <Flex className="sl-timeline-empty" column align="center">
                      <H2>{t('missingSchein')}</H2>
                      <BodyTextL>
                        {t('createScheinToSeeSomesthingHere')}
                      </BodyTextL>
                    </Flex>
                  )}
                </>
              }
              {...registerActionChainElementId(
                FormElementId.TIMELINE_TAB_BUTTON
              )}
            />

            <Tab
              id={ID_TABS.MEDICATION}
              title={
                <BodyTextL className="sl-tab-header" textAlign="center">
                  {t('MEDICATION_TAB')}
                </BodyTextL>
              }
              disabled={!currentSchein}
              panel={
                <Flex className="sl-2nd-level-tab" column auto>
                  <MedicationProvider value={{} as IMedicationContext}>
                    {activatedSchein ? (
                      <MedicationKBV
                        isOpen={activeTabId === ID_TABS.MEDICATION}
                      />
                    ) : (
                      <Flex className="sl-timeline-empty" column align="center">
                        <H2>{t('missingSchein')}</H2>
                        <BodyTextL>
                          {t('createScheinToSeeSomesthingHere')}
                        </BodyTextL>
                      </Flex>
                    )}
                  </MedicationProvider>
                </Flex>
              }
              {...registerActionChainElementId(
                FormElementId.MEDICATION_TAB_BUTTON
              )}
            />

            <Tab
              id={ID_TABS.HIMI}
              title={
                <BodyTextL className="sl-tab-header" textAlign="center">
                  {t('HIMI_TAB')}
                </BodyTextL>
              }
              disabled={!currentSchein}
              panel={
                <Himi
                  selectedContractDoctor={
                    patientManagement.selectedContractDoctor
                  }
                  patient={patientManagement.patient}
                />
              }
            />

            <Tab
              id={ID_TABS.HEIMI}
              panelClassName="sl-tab-heimi"
              title={
                <BodyTextL className="sl-tab-header" textAlign="center">
                  {t('HEIMI_TAB')}
                </BodyTextL>
              }
              disabled={!currentSchein}
              panel={
                <Heimi
                  active={activeTabId === ID_TABS.HEIMI}
                  selectedContractDoctor={
                    patientManagement.selectedContractDoctor
                  }
                  patient={patientManagement.patient}
                />
              }
            />
            <Tab
              id={ID_TABS.DIGA}
              title={
                <BodyTextL className="sl-tab-header" textAlign="center">
                  {t('DIGA_TAB')}
                </BodyTextL>
              }
              disabled={!currentSchein}
              panel={<DiGA patient={patientManagement.patient} />}
            />
            <Tab
              id={ID_TABS.FORMS}
              panelClassName="sl-form-overview"
              title={
                <BodyTextL className="sl-tab-header" textAlign="center">
                  {t('FORMS_TAB')}
                </BodyTextL>
              }
              disabled={!currentSchein}
              panel={
                <FormOverView
                  selectedContractDoctor={
                    patientManagement.selectedContractDoctor
                  }
                  patient={patientManagement.patient}
                />
              }
              {...registerActionChainElementId(FormElementId.FORM_TAB_BUTTON)}
            />
            {/* // TODO: hide feature for launch production */}
            <Tab
              id={ID_TABS.LAB}
              title={
                <BodyTextL className="sl-tab-header" textAlign="center">
                  {t('LAB_TAB')}
                </BodyTextL>
              }
              disabled={!currentSchein}
              panel={
                <Lab
                  selectedContractDoctor={
                    patientManagement.selectedContractDoctor
                  }
                  patient={patientManagement.patient}
                  currentTabId={activeTabId}
                />
              }
            />

            {isHistoryMode && (
              <Flex align="center" h="100%" ml="auto" mr={16}>
                <Svg src={historyViewIcon} height={24} width={24} />
                <Tooltip
                  content={t('tooltipViewHistory', {
                    name: getContentHistoryViewer(
                      historyViewPatient.latestViewer
                    ).doctorName,
                    date: getContentHistoryViewer(
                      historyViewPatient.latestViewer
                    ).date,
                    time: getContentHistoryViewer(
                      historyViewPatient.latestViewer
                    ).time,
                  })}
                >
                  <BodyTextL
                    className="text-view-history"
                    onClick={() => setIsOpenPatientLog(true)}
                  >
                    {t('historyTitle')}
                  </BodyTextL>
                </Tooltip>
              </Flex>
            )}
          </Tabs>
        )}
      </Flex>
      {patientFileStore.isOpenSchein && patientManagement.patientId?.value && (
        <Schein
          isOpen
          patientId={patientManagement.patientId?.value}
          id={scheinIdToEdit}
          isCreateReadCard={isReadTICard}
          onClose={toggleOpenSchein}
          setShowDMPEnrollDialog={setShowDMPEnrollDialog}
          reloadQuarters={handleReload}
          scrollToSection={scrollToSection}
        />
      )}

      {[MainGroup.HZV, MainGroup.FAV].includes(showCreateSchein) && (
        <CreateSvSchein
          isOpen
          patientId={patientManagement.patientId?.value}
          id={scheinIdToEdit}
          isHzv={showCreateSchein === MainGroup.HZV}
          isCreateReadCard={isReadTICard}
          onClose={() => {
            toggleOpenScheinBySelect(null);
            setScheinIdToEdit(undefined);
          }}
          reloadQuarters={handleReload}
        />
      )}

      {[MainGroup.PRIVATE, MainGroup.IGEL].includes(showCreateSchein) && (
        <CreatePrivateScheinModal
          patientId={patientManagement.patientId?.value}
          isOpen
          onClose={() => {
            toggleOpenScheinBySelect(null);
            setScheinIdToEdit(undefined);
          }}
          typeSchein={showCreateSchein}
          id={scheinIdToEdit}
        />
      )}

      {[MainGroup.BG].includes(showCreateSchein) &&
        patientManagement.patientId?.value && (
          <CreateSchein
            isOpen
            patientId={patientManagement.patientId?.value}
            id={scheinIdToEdit}
            onClose={() => {
              toggleOpenScheinBySelect(null);
              setScheinIdToEdit(undefined);
            }}
          />
        )}

      {timelineStore.previewEnrollmentForm.printUrl && (
        <PrintPreviewPdfDialog
          isShowCancelBtn={false}
          file={timelineStore.previewEnrollmentForm.printUrl}
          onClose={onClosePrintPreview}
          formId={timelineStore.previewEnrollmentForm.formName}
          titleText={`${getFormIdDisplay(
            timelineStore.previewEnrollmentForm.formName || ''
          )} - ${tForms(timelineStore.previewEnrollmentForm.formName || '')}`}
        />
      )}

      {isOpenPatientLog && (
        <PatientLog
          patientId={patientManagement.patientId?.value}
          onClose={() => {
            setIsOpenPatientLog(false);
          }}
          patientName={patientName}
        />
      )}

      {/* ACTION CHAIN PANEL */}
      <ActionChainPanel />
    </Flex>
  );
};

export default PatientPage;
