import { Flex, H3, Svg } from '@tutum/design-system/components';
import { ErrorField } from '@tutum/design-system/components/ErrorHelpText/component';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  DoctorRelationType,
  DocumentType,
  Field,
  FieldType,
  FieldValidationResult,
} from '@tutum/hermes/bff/edmp_common';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import I18n from '@tutum/infrastructure/i18n';
import { FORM_SETTING_OBJECT } from '@tutum/mvz/constant/form';
import type EHKSI18n from '@tutum/mvz/locales/en/EHKS.json';
import { Formik, FormikProps, FormikValues } from 'formik';
import { useMemo } from 'react';
import ContentDynamicForm from './ContentDynamicForm';
import {
  FilteredDocument,
  getFieldNameWithoutSpecialCharacter,
} from './helpers';
import { HiddenListByFieldName } from './constant';
import { HiddenFieldNamesByHeaderName } from './EHKSDocumentationOverview';
import { PatientProfileResponse } from '@tutum/hermes/bff/app_mvz_patient_profile';

interface RightPanelProps {
  contentRef: any;
  isReadOnly: boolean;
  initValues: {
    [keyMapping: string]: string | Date | undefined;
  };
  dataMapping: FilteredDocument | null;
  isLoading: boolean;
  headersError: {
    [keyMapping: string]: FieldValidationResult[];
  };
  fieldsError: {
    [keyMapping: string]: ErrorField[] | undefined;
  };
  fieldsWarning: {
    [keyMapping: string]: ErrorField[] | undefined;
  };
  schemasError: FieldValidationResult[];
  currentDocumentDate: number;
  formValue: FORM_SETTING_OBJECT;
  errorDMPDocument;
  documentationActive: {
    doctor?: string;
    doctorRelation?: DoctorRelationType;
    documentationType?: string;
    documentStatus?: string;
    name?: string;
  };
  onChangeFormValue: (formSetting: FORM_SETTING_OBJECT) => void;
  setCurrentDocumentDate: (currentDocumentDate: number) => void;
  hasValidSchein: boolean;
  onChangeField: (field: Field, values) => void;
  isValidationList?: boolean;
  currentDocumentationId?: string;
  setFormValueByDocumentationId: (
    arg: Record<string, FORM_SETTING_OBJECT>
  ) => void;
  setHiddenFieldNames: React.Dispatch<
    React.SetStateAction<HiddenFieldNamesByHeaderName>
  >;
  setHiddenHeaderNames: React.Dispatch<
    React.SetStateAction<HiddenListByFieldName>
  >;
  hiddenFieldNames: HiddenFieldNamesByHeaderName;
  documentType: keyof typeof DocumentType;
  patient: PatientProfileResponse;
}

const NothingDocument = '/images/nothing-document.svg';

const RightPanel = ({
  contentRef,
  isReadOnly,
  initValues,
  dataMapping,
  isLoading,
  headersError,
  fieldsError,
  fieldsWarning,
  schemasError,
  currentDocumentDate,
  errorDMPDocument,
  documentationActive,
  setCurrentDocumentDate,
  hasValidSchein,
  onChangeField,
  isValidationList = false,
  setFormValueByDocumentationId,
  currentDocumentationId,
  setHiddenFieldNames,
  hiddenFieldNames,
  setHiddenHeaderNames,
  documentType,
  patient
}: RightPanelProps) => {
  const { t } = I18n.useTranslation<keyof typeof EHKSI18n.rightPanel>({
    namespace: 'EHKS',
    nestedTrans: 'rightPanel',
  });

  const checkValidationItem = (field: Field, values: FormikValues) => {
    const hiddenFieldsNamesValues = Object.values(
      hiddenFieldNames?.[field.header] || {}
    ).flat();

    const errors = {};
    const fieldName = getFieldNameWithoutSpecialCharacter(
      `${field.header}_${field.name}`
    );

    const splitFieldName = field.name.split('_');

    if (
      !field.isRequire ||
      hiddenFieldsNamesValues.includes(
        splitFieldName[splitFieldName.length - 1]
      )
    ) {
      return errors;
    }

    if (field.fieldType === FieldType.FieldType_Checkbox) {
      const hasError = field.options.every(
        (option) =>
          !values[
          getFieldNameWithoutSpecialCharacter(
            `${field.header}_${field.name}_${option.name}`
          )
          ]
      );

      if (hasError) {
        errors[field.name] = t('fieldRequired', {
          fieldName: field.label,
        });
      }

      return errors;
    }

    if (!values[fieldName]) {
      errors[fieldName] = t('fieldRequired', {
        fieldName: field.label,
      });
    }

    return errors;
  };

  const checkValidation = (values: FormikValues) => {
    let errors = {};

    if (!dataMapping) {
      return errors;
    }

    if (!values.documentDate) {
      errors = {
        ...errors,
        documentDate: t('fieldRequired', {
          fieldName: t('examinationDate'),
        }),
      };
    }

    if (!values.additionalContracts) {
      errors = {
        ...errors,
        additionalContracts: t('fieldRequired', {
          fieldName: t('additionalContracts'),
        }),
      };
    }

    dataMapping.headers.forEach((datum) => {
      datum.dMPs.forEach((dMPProgram) => {
        dMPProgram.fields.forEach((field) => {
          if (field.fields[0]) {
            field.fields.forEach((item) => {
              errors = {
                ...errors,
                ...checkValidationItem(
                  {
                    ...item,
                    name: `${field.name}_${item.name}`,
                    header: field.header,
                  },
                  values
                ),
              };
            });
          }

          errors = {
            ...errors,
            ...checkValidationItem(field, values),
          };
        });
      });
    });

    return errors;
  };

  const renderContent = useMemo(() => {
    return (
      <Formik
        innerRef={contentRef}
        initialValues={initValues}
        onSubmit={() => { }}
        enableReinitialize
        validate={checkValidation}
      >
        {(p: FormikProps<any>) => {
          return (
            <ContentDynamicForm
              {...p}
              dataMapping={dataMapping}
              isReadOnly={isReadOnly}
              headersError={headersError}
              fieldsError={fieldsError}
              fieldsWarning={fieldsWarning}
              schemasError={schemasError}
              currentDocumentDate={currentDocumentDate}
              errorDMPDocument={errorDMPDocument}
              setCurrentDocumentDate={setCurrentDocumentDate}
              hasValidSchein={hasValidSchein}
              onChangeField={onChangeField}
              submitCount={isValidationList ? 1 : p.submitCount}
              currentDocumentationId={currentDocumentationId}
              setFormValueByDocumentationId={setFormValueByDocumentationId}
              setHiddenFieldNames={setHiddenFieldNames}
              hiddenFieldNames={hiddenFieldNames}
              setHiddenHeaderNames={setHiddenHeaderNames}
              documentType={documentType}
              patient={patient}
            />
          );
        }}
      </Formik>
    );
  }, [
    initValues,
    dataMapping,
    isReadOnly,
    headersError,
    fieldsError,
    fieldsWarning,
    currentDocumentDate,
    errorDMPDocument,
    hasValidSchein,
    documentationActive,
    onChangeField,
  ]);

  return (
    <Flex
      className={getCssClass(
        'sl-ehks-documentation-overview__right-panel flex-1',
        {
          loading: isLoading,
        }
      )}
    >
      {!isLoading &&
        !dataMapping &&
        errorDMPDocument?.serverError !==
        ErrorCode.ErrorCode_EDMP_Get_Document_Not_Found ? (
        <Flex column justify="center" align="center" h="100%" w="100%">
          <Svg src={NothingDocument} alt={t('noResultFound')} />
          <H3
            margin="40px 0 0"
            fontWeight={700}
            color={COLOR.TEXT_SECONDARY_NAVAL}
          >
            {t('noResultFound')}
          </H3>
        </Flex>
      ) : (
        <div className="sl-ehks-documentation-overview__right-panel__inner">
          {renderContent}
        </div>
      )}
    </Flex>
  );
};

export default RightPanel;
