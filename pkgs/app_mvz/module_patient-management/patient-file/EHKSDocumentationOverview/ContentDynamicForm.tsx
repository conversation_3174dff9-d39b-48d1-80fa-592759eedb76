import {
  BodyTextL,
  BodyTextS,
  Flex,
  FormGroup2,
  H2,
  MessageBar,
  Svg,
} from '@tutum/design-system/components';
import {
  Checkbox,
  Divider,
  Icon,
  InputGroup,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import { DateInput } from '@tutum/design-system/components/DateTime';
import PopoverDateInput from '@tutum/design-system/components/DateTime/PopoverDateInput';
import {
  convertDateValueToObject,
  convertDateValueToObjectAndAutoCompleted,
  convertObjectDateValueToFormValue,
} from '@tutum/design-system/components/DateTime/utils';
import { ErrorField } from '@tutum/design-system/components/ErrorHelpText/component';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { formatValueDateOfBirth, getAge, getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  DocumentType,
  ErrorType,
  Field as FieldCommon,
  FieldType,
  FieldValidationResult,
  HeaderStatus,
} from '@tutum/hermes/bff/edmp_common';
import { AdditionalContractsEnum } from '@tutum/hermes/bff/legacy/edmp_common';
import I18n from '@tutum/infrastructure/i18n';
import {
  DATE_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import type EHKSI18n from '@tutum/mvz/locales/en/EHKS.json';
import { Field, FormikProps } from 'formik';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Collapse } from 'react-collapse';
import {
  FilteredDocument,
  getFieldNameWithoutSpecialCharacter,
} from './helpers';
import { FORM_SETTING_OBJECT } from '@tutum/mvz/constant/form';
import {
  HiddenFieldNameByDocumentType,
  HiddenHeaderNameByDocumentType,
  HiddenListByFieldName,
  RelatedFieldsByDocumentType,
} from './constant';
import { HiddenFieldNamesByHeaderName } from './EHKSDocumentationOverview';
import { PatientProfileResponse } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';

const CalendarIcon = '/images/calendar-default.svg';
const CheckIcon = '/images/check.svg';

const RenderDynamicFieldContent = ({
  field,
  errors,
  warnings,
  touched,
  submitCount,
  isReadOnly,
  form,
  onChangeField,
  setHiddenFieldNames,
  setHiddenHeaderNames,
  documentType,
  isExistChildField = false,
}) => {
  const inputRefs = useRef<any[]>([]);
  const [radioValue, setRadioValue] = useState<string>('');

  const fieldName = useMemo(() => {
    return getFieldNameWithoutSpecialCharacter(`${field.header}_${field.name}`);
  }, [field.header, field.name]);
  const handleUpdateFormValueWhenChangeOrDeselectOption = ({
    selectedOptValue,
    fieldRadioName,
    headerName,
    fieldName,
    isDeselect,
  }) => {
    const hiddenOpt =
      HiddenFieldNameByDocumentType?.[documentType]?.[headerName]?.[
      fieldName
      ]?.[selectedOptValue] || [];
    const hiddenHeaderNames =
      HiddenHeaderNameByDocumentType?.[documentType]?.[fieldName]?.[
      selectedOptValue
      ] || [];
    const formKeys = Object.keys(form.values);
    const newFormValue = formKeys.reduce((acc, key) => {
      const keySplit = key.split('_');
      if (hiddenHeaderNames.includes(keySplit[0])) {
        return {
          ...acc,
          [key]: null,
        };
      }
      return acc;
    }, {});

    return {
      ...form.values,
      ...newFormValue,
      [fieldRadioName]: isDeselect ? null : selectedOptValue,
      ...(hiddenOpt || []).reduce((acc, opt) => {
        return {
          ...acc,
          ...(!isExistChildField
            ? { [`${headerName}_${opt}`]: null }
            : { [`${fieldRadioName}_${opt}`]: null }),
        };
      }, {}),
    };
  };

  useEffect(() => {
    if ([FieldType.FieldType_Radio].includes(field.fieldType)) {
      setRadioValue(form.values[fieldName] || '');
    }
  }, [form.values, fieldName]);

  const getHiddenFieldsWhenHeaderHide = (hiddenHeaderNames) => {
    const hiddenFieldsWhenHeaderHide = hiddenHeaderNames.reduce(
      (acc, headerName) => {
        const hiddenFieldsByHeaderName =
          HiddenFieldNameByDocumentType?.[documentType]?.[headerName] || {};
        const hiddenFieldsByFieldName = Object.keys(
          hiddenFieldsByHeaderName
        ).reduce((acc, fieldName) => {
          return {
            ...acc,
            [fieldName]: Object.values(
              hiddenFieldsByHeaderName?.[fieldName] || {}
            ).flat(),
          };
        }, {});
        return {
          ...acc,
          [headerName]: hiddenFieldsByFieldName,
        };
      },
      {}
    );
    return hiddenFieldsWhenHeaderHide;
  };

  const handleChangeHiddenFieldsAndHeader = ({
    hiddenHeaderNames,
    hiddenFieldNames,
    selectedOptValue,
    fieldRadioName,
    isDeselect,
  }) => {
    const hiddenFieldsWhenHeaderHide =
      getHiddenFieldsWhenHeaderHide(hiddenHeaderNames);
    setHiddenHeaderNames((prev) => ({
      ...prev,
      [field.name]: hiddenHeaderNames,
    }));
    setHiddenFieldNames((prev) => ({
      ...prev,
      ...hiddenFieldsWhenHeaderHide,
      [field.header]: {
        ...(prev[field.header] || {}),
        [field.name]: hiddenFieldNames || [],
      },
    }));

    const newFormValue = handleUpdateFormValueWhenChangeOrDeselectOption({
      selectedOptValue,
      fieldRadioName,
      headerName: field.header,
      fieldName: field.name,
      isDeselect,
    });
    form.setValues((prevValue) => {
      const newValue = {
        ...prevValue,
        ...newFormValue,
        ...(RelatedFieldsByDocumentType?.[documentType]?.[field.header]?.[
          field.name
        ]?.[selectedOptValue] || {}),
      };

      if (field.liveCheck) {
        onChangeField(field, newValue);
      }

      return newValue;
    });
  };

  const getMaximumNumberWithNumberDigitsAndDecimalDigits = (numberDigits, decimalDigits) => {
    if (numberDigits <= 0) return 0;
    const integerPart = '9'.repeat(numberDigits);
    const decimalPart = '9'.repeat(decimalDigits);
    return parseFloat(`${integerPart}.${decimalPart}`);
  }

  const getMinimumNumberWithDecimalDigits = (decimalDigits) => {
    if (decimalDigits <= 0) return 0;
    const decimalPart = '0'.repeat(decimalDigits - 1);
    return parseFloat(`${0}.${decimalPart}1`);
  }



  useEffect(() => {
    if (
      [FieldType.FieldType_Radio].includes(field.fieldType) &&
      inputRefs.current?.[0]
    ) {
      const handleChange = (event) => {
        const value = event.target.value;
        const deSelected = value === radioValue;
        if (deSelected) {
          const fieldRadioName = event.target.name;
          if (value === 'Ja') {
            const hiddenFields =
              HiddenFieldNameByDocumentType?.[documentType]?.[field.header]?.[
              field.name
              ]?.['Nein'] || [];
            const hiddenHeaderNames =
              HiddenHeaderNameByDocumentType?.[documentType]?.[field.name]?.[
              'Nein'
              ] || [];
            handleChangeHiddenFieldsAndHeader({
              hiddenHeaderNames,
              hiddenFieldNames: hiddenFields,
              fieldRadioName,
              selectedOptValue: 'Nein',
              isDeselect: true,
            });
          } else {
            form.setValues((prevValue) => {
              const newValue = {
                ...prevValue,
                [fieldRadioName]: null,
              };

              if (field.liveCheck) {
                onChangeField(field, newValue);
              }
              return newValue;
            });
          }
          setRadioValue('');
        }
      };

      inputRefs.current.forEach((el) => {
        el.addEventListener('click', handleChange);
      });

      return () => {
        inputRefs.current.forEach((el) => {
          el?.removeEventListener('click', handleChange);
        });
      };
    }
  }, [radioValue, field, documentType, form.values]);

  switch (field.fieldType) {
    case FieldType.FieldType_Date: {
      return (
        <FormGroup2
          label={
            <BodyTextS
              fontWeight={500}
              textTransform="uppercase"
              color={COLOR.TEXT_SECONDARY_NAVAL}
              as="span"
            >
              {field.label}
            </BodyTextS>
          }
          name={fieldName}
          isRequired={field.isRequire}
          errors={errors}
          warning={warnings[fieldName]}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name={fieldName}>
            {({ field: fieldDate, form }) => (
              <PopoverDateInput
                {...fieldDate}
                name={fieldDate.name}
                placeholder={DATE_FORMAT}
                formatDate={DATE_FORMAT}
                leftElement={<Svg src={CalendarIcon} />}
                disabled={isReadOnly || field.readOnly}
                value={
                  convertObjectDateValueToFormValue(fieldDate.value, false)
                    .value
                }
                onChange={(dateValue) => {
                  if (!dateValue) {
                    form.setFieldValue(fieldDate.name, null);
                    return;
                  }

                  const method = field.emptyDateFormat
                    ? convertDateValueToObjectAndAutoCompleted
                    : convertDateValueToObject;
                  const convertedValues = method(dateValue);
                  form.setFieldValue(fieldDate.name, convertedValues);
                }}
              />
            )}
          </Field>
        </FormGroup2>
      );
    }
    case FieldType.FieldType_Text: {
      return (
        <FormGroup2
          label={
            <BodyTextS
              fontWeight={500}
              textTransform="uppercase"
              color={COLOR.TEXT_SECONDARY_NAVAL}
              as="span"
            >
              {field.label}
            </BodyTextS>
          }
          name={fieldName}
          isRequired={field.isRequire}
          errors={errors}
          warning={warnings[fieldName]}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name={fieldName}>
            {({ field: fieldInput, form }) => (
              <InputGroup
                {...fieldInput}
                disabled={isReadOnly || field.readOnly}
                data-tab-id={fieldInput.name}
                onChange={(e) => {
                  const value = e.target.value;

                  form.setFieldValue(fieldInput.name, value);
                }}
              />
            )}
          </Field>
        </FormGroup2>
      );
    }
    case FieldType.FieldType_Number: {
      return (
        <FormGroup2
          label={
            <BodyTextS
              fontWeight={500}
              textTransform="uppercase"
              color={COLOR.TEXT_SECONDARY_NAVAL}
              as="span"
            >
              {field.label}
            </BodyTextS>
          }
          name={fieldName}
          isRequired={field.isRequire}
          errors={errors}
          warning={warnings[fieldName]}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name={fieldName}>
            {({ field: fieldInput, form }) => (<NumberInput
              {...fieldInput}
              isFloat={field.isFloat}
              maxLength={
                field.maxLength &&
                field.maxLength +
                (field.decimalDigits ? field.decimalDigits + 1 : 0) // include decimal symbol
              }
              allowLeadingZeros
              defaultValue={fieldInput.value || ''}
              thousandSeparator={false}
              name={fieldInput.name}
              placeholder={field.placeholder}
              rightElement={
                <p className="sl-input-right-element">{field.unit}</p>
              }
              isAllowed={(values) => {
                const { floatValue, value: valueStr } = values;
                if (floatValue === undefined || floatValue === null) return true;
                if (!field.maxLength || !field.minLength) return true;
                if (valueStr.length > field.maxLength && floatValue === 0) return false;
                const decimalPart = valueStr.split('.')[1] || '';
                let condition = floatValue <= getMaximumNumberWithNumberDigitsAndDecimalDigits(field.maxLength, field.decimalDigits)
                if (decimalPart.length) {
                  condition = condition && decimalPart.length <= field.decimalDigits
                    && floatValue >= getMinimumNumberWithDecimalDigits(field.decimalDigits);
                }
                return condition
              }}
              disabled={isReadOnly || field.readOnly}
              onValueChange={({ value }) => {
                form.setFieldValue(fieldInput.name, +value);
              }}
            />)
            }
          </Field>
        </FormGroup2>
      );
    }
    case FieldType.FieldType_Checkbox: {
      return (
        <FormGroup2
          label={
            <BodyTextS
              fontWeight={500}
              textTransform="uppercase"
              color={COLOR.TEXT_SECONDARY_NAVAL}
              as="span"
            >
              {field.label}
            </BodyTextS>
          }
          name={getFieldNameWithoutSpecialCharacter(field.name)}
          isRequired={field.isRequire}
          errors={errors}
          warning={warnings[field.name]}
          touched={touched}
          submitCount={submitCount}
        >
          <Flex className="sl-checkbox-group">
            {field.options?.map((option) => {
              const fieldName = getFieldNameWithoutSpecialCharacter(
                `${field.name}_${option.name}`
              );

              return (
                <Field key={fieldName} name={fieldName}>
                  {({ field: fieldCheckbox, form }) => {
                    return (
                      <Checkbox
                        {...fieldCheckbox}
                        checked={!!form.values[fieldName]}
                        id={fieldCheckbox.name}
                        name={fieldCheckbox.name}
                        labelElement={
                          <Field name={`${fieldName}-option`}>
                            {({ field: fieldChild, form }) => {
                              return option.fieldType ===
                                FieldType.FieldType_Date ? (
                                <DateInput
                                  className="groups__input-group"
                                  value={
                                    fieldChild.value
                                      ? DatetimeUtil.dateTimeFormat(
                                        new Date(+fieldChild.value),
                                        YEAR_MONTH_DAY_FORMAT
                                      )
                                      : null
                                  }
                                  formatDate={(date) =>
                                    DatetimeUtil.dateTimeFormat(
                                      date,
                                      DATE_FORMAT
                                    )
                                  }
                                  parseDate={(str) =>
                                    DatetimeUtil.strToDate(str, DATE_FORMAT)
                                  }
                                  inputProps={{
                                    leftElement: <Svg src={CalendarIcon} />,
                                  }}
                                  placeholder={DATE_FORMAT}
                                  disabled={isReadOnly || field.readOnly}
                                  onChange={(date: string) => {
                                    form.setValues((prevValue) => {
                                      const newValue = {
                                        ...prevValue,
                                        [fieldChild.name]: date,
                                        [`${fieldName}-input`]: option.label,
                                      };

                                      return newValue;
                                    });
                                  }}
                                />
                              ) : (
                                option.label || option.name
                              );
                            }}
                          </Field>
                        }
                        disabled={isReadOnly || field.readOnly}
                        onChange={(event) => {
                          const { checked } = event.target as any;
                          form.setValues((prevValue) => {
                            const newValue = {
                              ...prevValue,
                              [fieldCheckbox.name]: checked || null,
                            };

                            if (field.liveCheck) {
                              onChangeField(field, newValue);
                            }

                            return newValue;
                          });
                        }}
                      />
                    );
                  }}
                </Field>
              );
            })}
          </Flex>
        </FormGroup2>
      );
    }
    case FieldType.FieldType_Radio: {
      return (
        <FormGroup2
          className="bp5-form-group__radio-group"
          label={
            <BodyTextS
              fontWeight={500}
              textTransform="uppercase"
              color={COLOR.TEXT_SECONDARY_NAVAL}
              as="span"
            >
              {field.label}
            </BodyTextS>
          }
          name={fieldName}
          isRequired={field.isRequire}
          errors={errors}
          warning={warnings[fieldName]}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name={fieldName}>
            {({ field: fieldRadio, form }) => (
              <RadioGroup
                {...fieldRadio}
                selectedValue={fieldRadio.value}
                onChange={(e: any) => {
                  const target = e.target;
                  const selectedOption = field.options.find(
                    (option) => option.name === target.value
                  );
                  const hiddenHeaderNames =
                    selectedOption?.hiddenHeaderNames || [];
                  handleChangeHiddenFieldsAndHeader({
                    hiddenHeaderNames,
                    hiddenFieldNames: selectedOption?.hiddenFieldNames || [],
                    selectedOptValue: target.value,
                    fieldRadioName: fieldRadio.name,
                    isDeselect: false,
                  });
                  setRadioValue(target.value);
                  // focus to input
                  const inputEle =
                    target.parentElement.querySelector('input[type="text"]');

                  inputEle?.focus();
                }}
              >
                {field.options?.map((option, idx) => (
                  <Radio
                    key={`${fieldRadio.name}_${option.name}`}
                    inputRef={(el) => {
                      inputRefs.current[idx] = el;
                    }}
                    data-option-name={getFieldNameWithoutSpecialCharacter(
                      option.name
                    )}
                    labelElement={
                      <Field
                        name={getFieldNameWithoutSpecialCharacter(
                          `${fieldRadio.name}_${option.name}-option`
                        )}
                      >
                        {({ field: fieldChild, form }) => {
                          return option.unit ? (
                            <NumberInput
                              {...fieldChild}
                              className="groups__input-group"
                              isFloat={option.isFloat}
                              allowLeadingZeros
                              decimalScale={option.decimalDigits || 1}
                              name={fieldChild.name}
                              minLength={option?.minLength}
                              maxLength={
                                option?.maxLength &&
                                option.maxLength +
                                (option.decimalDigits
                                  ? option.decimalDigits + 1
                                  : 0)
                              } // include decimal symbol
                              defaultValue={fieldChild.value}
                              rightElement={
                                <p className="sl-input-right-element">
                                  {option.unit === 'ml/min/1,73m2KOF'
                                    ? 'ml/min/1,73m²KOF'
                                    : option.unit}
                                </p>
                              }
                              disabled={isReadOnly || field.readOnly}
                              onValueChange={({ value }) => {
                                form.setFieldValue(fieldChild.name, +value);
                              }}
                            />
                          ) : option.fieldType === FieldType.FieldType_Date ? (
                            <DateInput
                              value={
                                fieldChild.value
                                  ? DatetimeUtil.dateTimeFormat(
                                    new Date(+fieldChild.value),
                                    YEAR_MONTH_DAY_FORMAT
                                  )
                                  : null
                              }
                              formatDate={(date) =>
                                DatetimeUtil.dateTimeFormat(date, DATE_FORMAT)
                              }
                              parseDate={(str) =>
                                DatetimeUtil.strToDate(str, DATE_FORMAT)
                              }
                              inputProps={{
                                leftElement: <Svg src={CalendarIcon} />,
                              }}
                              placeholder={DATE_FORMAT}
                              disabled={isReadOnly || field.readOnly}
                              onChange={(date: string) => {
                                form.setValues((prevValue) => {
                                  const newValue = {
                                    ...prevValue,
                                    [fieldChild.name]: date,
                                  };

                                  return newValue;
                                });
                              }}
                            />
                          ) : (
                            option.label || option.name
                          );
                        }}
                      </Field>
                    }
                    disabled={isReadOnly || field.readOnly}
                    value={option.name}
                  />
                ))}
              </RadioGroup>
            )}
          </Field>
        </FormGroup2>
      );
    }
    default: {
      return null;
      // return (
      //   <Flex column gap={16}>
      //     <div>{field.name}</div>
      //     <div style={{ marginLeft: 16 }}>Unknown Data Type</div>
      //   </Flex>
      // );
    }
  }
};

const ContentDynamicForm = ({
  dataMapping,
  submitCount,
  setFormValueByDocumentationId,
  currentDocumentationId,
  errors,
  touched,
  isReadOnly,
  headersError,
  fieldsError,
  fieldsWarning,
  schemasError,
  currentDocumentDate,
  errorDMPDocument,
  setCurrentDocumentDate,
  hasValidSchein,
  onChangeField,
  setHiddenFieldNames,
  hiddenFieldNames,
  setHiddenHeaderNames,
  documentType,
  patient,
  ...props
}: FormikProps<any> & {
  dataMapping: FilteredDocument | null;
  isReadOnly: boolean;
  headersError: {
    [keyMapping: string]: FieldValidationResult[];
  };
  fieldsError: {
    [keyMapping: string]: ErrorField[] | undefined;
  };
  fieldsWarning: {
    [keyMapping: string]: ErrorField[] | undefined;
  };
  schemasError: FieldValidationResult[];
  currentDocumentDate: number;
  errorDMPDocument;
  setCurrentDocumentDate: (currentDocumentDate: number) => void;
  hasValidSchein: boolean;
  onChangeField: (field: FieldCommon, values) => void;
  currentDocumentationId?: string;
  setFormValueByDocumentationId: (
    arg: Record<string, FORM_SETTING_OBJECT>
  ) => void;
  setHiddenFieldNames: React.Dispatch<
    React.SetStateAction<HiddenFieldNamesByHeaderName>
  >;
  setHiddenHeaderNames: React.Dispatch<
    React.SetStateAction<HiddenListByFieldName>
  >;
  hiddenFieldNames: HiddenFieldNamesByHeaderName;
  documentType: keyof typeof DocumentType;
  patient: PatientProfileResponse;
}) => {
  const { t } = I18n.useTranslation<keyof typeof EHKSI18n.rightPanel>({
    namespace: 'EHKS',
    nestedTrans: 'rightPanel',
  });

  const [collapsedInfo, setCollapsedInfo] = useState({});

  useEffect(() => {
    if (!currentDocumentationId) return;

    setFormValueByDocumentationId({
      [currentDocumentationId]: props.values,
    });
  }, [props.values, currentDocumentationId]);

  const renderDynamicField = useMemo(() => {
    if (!dataMapping) {
      return null;
    }

    return dataMapping?.headers?.map((datum, index) => {
      const noDataFields = datum.dMPs.every((dmp) => !dmp.fields[0]);

      if (noDataFields) {
        return null;
      }

      const completed = datum.dMPs.every(
        (dmp) => dmp.headerStatus === HeaderStatus.HeaderStatus_Completed
      );
      const notFilled = datum.dMPs.every(
        (dmp) =>
          !dmp.headerStatus ||
          dmp.headerStatus === HeaderStatus.HeaderStatus_NotFilled
      );
      const inComplete = datum.dMPs.some(
        (dmp) => dmp.headerStatus === HeaderStatus.HeaderStatus_Incomplete
      );

      const matchedHeaderErrors = headersError?.[datum.name];

      return (
        <Flex
          key={datum.name + index} // fix same key issue (2 header Anamnese- und Befunddaten)
          className={getCssClass('groups', {
            'no-border': index === dataMapping.headers?.length - 1,
          })}
          pb={24}
        >
          {completed ? (
            <Svg className="groups__circle" src={CheckIcon} />
          ) : (
            <div
              className={getCssClass('groups__circle', {
                'not-filled': notFilled,
                incompleted: inComplete,
              })}
            />
          )}
          <Flex column gap={16} w="100%">
            <H2>{datum.name}</H2>
            {!!matchedHeaderErrors?.[0] && (
              <Flex column gap={2}>
                {matchedHeaderErrors.map((error, index) => {
                  const warningMessage =
                    error.errorType === ErrorType.ErrorType_Warning
                      ? error.errorMessage
                      : '';
                  const errorMessage =
                    error.errorType === ErrorType.ErrorType_Error
                      ? error.errorMessage
                      : '';

                  return (
                    <MessageBar
                      key={index}
                      type={warningMessage ? 'warning' : 'error'}
                      hasBullet={false}
                      content={warningMessage || errorMessage}
                    />
                  );
                })}
              </Flex>
            )}
            {datum.dMPs?.map((dmpProgram) => {
              if (!dmpProgram.fields[0]) {
                return null;
              }

              return (
                <Flex key={datum.name} column gap={16}>
                  <Flex column gap={16}>
                    {dmpProgram.fields?.map((field, index) => {
                      const headerName = field.header;
                      const parentFieldName = field.name;
                      if (
                        field.fieldType === FieldType.FieldType_Nested &&
                        field.fields?.[0]
                      ) {
                        return (
                          <Flex
                            key={field.name}
                            className="groups__other-info"
                            column
                            data-testid={field.name}
                          >
                            <Flex
                              align="center"
                              justify="space-between"
                              onClick={() => {
                                setCollapsedInfo((prevInfo) => ({
                                  ...prevInfo,
                                  [field.name]: !prevInfo?.[field.name],
                                }));
                              }}
                            >
                              <BodyTextL fontWeight={600}>
                                {field.label}
                              </BodyTextL>
                              <Icon
                                className="expand-icon"
                                icon={
                                  !collapsedInfo?.[field.name]
                                    ? 'chevron-up'
                                    : 'chevron-down'
                                }
                              />
                            </Flex>
                            <Collapse isOpened={!collapsedInfo?.[field.name]}>
                              <Flex
                                className="groups__other-info__groups"
                                column
                                mt={8}
                                p={16}
                                gap={16}
                              >
                                {field.fields?.map((item) => {
                                  const fieldName =
                                    getFieldNameWithoutSpecialCharacter(
                                      `${field.name}_${item.name}`
                                    );

                                  return (
                                    <RenderDynamicFieldContent
                                      key={fieldName}
                                      field={{
                                        ...item,
                                        name: fieldName,
                                      }}
                                      errors={{
                                        ...errors,
                                        ...fieldsError,
                                      }}
                                      warnings={fieldsWarning}
                                      touched={{
                                        ...touched,
                                        [fieldName]:
                                          touched[fieldName] ||
                                          fieldsError[fieldName],
                                      }}
                                      submitCount={submitCount}
                                      isReadOnly={isReadOnly}
                                      form={props}
                                      onChangeField={onChangeField}
                                      setHiddenFieldNames={setHiddenFieldNames}
                                      setHiddenHeaderNames={
                                        setHiddenHeaderNames
                                      }
                                      documentType={documentType}
                                    />
                                  );
                                })}
                              </Flex>
                            </Collapse>
                          </Flex>
                        );
                      }

                      const fieldName = getFieldNameWithoutSpecialCharacter(
                        field.name
                      );

                      return (
                        <Flex
                          key={`${field.name}_${index}`}
                          column
                          data-testid={field.name}
                        >
                          <>
                            <RenderDynamicFieldContent
                              field={field}
                              errors={{
                                ...errors,
                                ...fieldsError,
                              }}
                              warnings={fieldsWarning}
                              touched={{
                                ...touched,
                                [fieldName]:
                                  touched[fieldName] || fieldsError[fieldName],
                              }}
                              submitCount={submitCount}
                              isReadOnly={isReadOnly}
                              form={props}
                              onChangeField={onChangeField}
                              setHiddenFieldNames={setHiddenFieldNames}
                              setHiddenHeaderNames={setHiddenHeaderNames}
                              documentType={documentType}
                              isExistChildField={!!field.fields[0]}
                            />
                            {!!field.fields[0] &&
                              field.fields.every((item) => {
                                return !hiddenFieldNames?.[headerName]?.[
                                  parentFieldName
                                ]?.includes(item.name);
                              }) && (
                                <Flex
                                  className="groups__other-info__groups"
                                  column
                                  mt={8}
                                  p={16}
                                  gap={16}
                                >
                                  {field.fields.map((item) => {
                                    const fieldChildName =
                                      getFieldNameWithoutSpecialCharacter(
                                        `${fieldName}_${item.name}`
                                      );

                                    if (
                                      (
                                        hiddenFieldNames?.[headerName]?.[
                                        parentFieldName
                                        ] || []
                                      ).includes(item.name)
                                    ) {
                                      return <></>;
                                    }

                                    return (
                                      <RenderDynamicFieldContent
                                        key={fieldChildName}
                                        field={{
                                          ...item,
                                          header: field.header,
                                          name: fieldChildName,
                                        }}
                                        errors={{
                                          ...errors,
                                          ...fieldsError,
                                        }}
                                        warnings={fieldsWarning}
                                        touched={{
                                          ...touched,
                                          [fieldChildName]:
                                            touched[fieldChildName] ||
                                            fieldsError[fieldChildName],
                                        }}
                                        submitCount={submitCount}
                                        isReadOnly={isReadOnly}
                                        form={props}
                                        onChangeField={onChangeField}
                                        setHiddenFieldNames={
                                          setHiddenFieldNames
                                        }
                                        setHiddenHeaderNames={
                                          setHiddenHeaderNames
                                        }
                                        documentType={documentType}
                                      />
                                    );
                                  })}
                                </Flex>
                              )}
                          </>
                        </Flex>
                      );
                    })}
                  </Flex>
                </Flex>
              );
            })}
          </Flex>
        </Flex>
      );
    });
  }, [
    errors,
    touched,
    submitCount,
    dataMapping,
    collapsedInfo,
    isReadOnly,
    headersError,
    fieldsError,
    fieldsWarning,
    onChangeField,
  ]);

  const isPatientAgeUnder35 = useMemo(() => {
    return (
      (patient?.patientInfo?.personalInfo?.dateOfBirth && getAge(
        formatValueDateOfBirth(patient?.patientInfo?.personalInfo?.dateOfBirth)
      ) || -1) < 35
    );
  }, [patient?.patientInfo?.personalInfo?.dateOfBirth]);

  return (
    <>
      <FormGroup2
        label={t('examinationDate')}
        name={'documentDate'}
        isRequired
        submitCount={submitCount}
        errors={errors}
        touched={touched}
      >
        <Field name="documentDate">
          {({ field, form }) => (
            <PopoverDateInput
              {...field}
              disabled={isReadOnly || field.readOnly}
              dataTabId={field.fieldName}
              placeholder={DATE_FORMAT}
              formatDate={DATE_FORMAT}
              maxDate={DatetimeUtil.date()}
              leftElement={<Svg src={CalendarIcon} />}
              value={field.value ? moment(field.value).format(DATE_FORMAT) : ''}
              onChange={(date) => {
                const convertedValues = +DatetimeUtil.dateToMoment(
                  Object.values(convertDateValueToObject(date)).join('.'),
                  DATE_FORMAT
                );
                const currentTime = DatetimeUtil.getCurrentTime();

                form.setFieldValue(field.name, convertedValues + currentTime);
                const startOfQuarter =
                  DatetimeUtil.getStartOfQuarterWithoutUTC(currentDocumentDate);
                const endOfQuarter =
                  DatetimeUtil.getEndOfQuarterWithoutUTC(currentDocumentDate);
                const checkDate = convertedValues + currentTime || +DatetimeUtil.date();

                if (+startOfQuarter > checkDate || checkDate > +endOfQuarter) {
                  setCurrentDocumentDate(checkDate);
                }
              }}
            />
          )}
        </Field>
      </FormGroup2>
      <FormGroup2
        label={t('additionalContracts')}
        name={'additionalContracts'}
        isRequired
        submitCount={submitCount}
        errors={errors}
        touched={touched}
        style={{ marginTop: '12px' }}
      >
        <Field name="additionalContracts">
          {({ field, form }) => (
            <RadioGroup
              inline
              selectedValue={field.value}
              disabled={isReadOnly || field.readOnly || isPatientAgeUnder35}
              onChange={(e) => {
                form.setFieldValue(field.name, e.currentTarget.value);
              }}
            >
              <Radio
                label={t('yes')}
                value={AdditionalContractsEnum.AdditionalContractsEnum_Yes}
              />
              <Radio
                label={t('no')}
                value={AdditionalContractsEnum.AdditionalContractsEnum_No}
              />
            </RadioGroup>
          )}
        </Field>
      </FormGroup2>
      <Divider style={{ margin: '24px 0' }} />
      {!!schemasError?.[0] && (
        <>
          <Flex column gap={2}>
            {schemasError.map((error, index) => {
              return (
                <MessageBar
                  key={index}
                  type="error"
                  hasBullet={false}
                  content={error.errorMessage}
                />
              );
            })}
          </Flex>
          <BodyTextS margin="0 0 16px 0" />
        </>
      )}
      {renderDynamicField}
    </>
  );
};

export default ContentDynamicForm;
