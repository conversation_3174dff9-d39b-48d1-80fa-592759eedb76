import {
  BodyTextM,
  Button,
  Dialog,
  Flex,
  H2,
  InfoConfirmDialog,
  LoadingState,
  MessageBar,
  PDFDownloadFile,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { ErrorField } from '@tutum/design-system/components/ErrorHelpText/component';
import { EHKSType } from '@tutum/hermes/bff/common';
import {
  DMPBillingFile,
  DMPValueEnum,
  DoctorRelationType,
  DocumentStatus,
  DocumentType,
  EnrollStatus,
  EnrollmentDocumentInfoModel,
  ErrorType,
  Field,
  FieldValidationResult,
  FieldValidationResultType,
} from '@tutum/hermes/bff/edmp_common';
import { ResponseType } from '@tutum/hermes/bff/legacy/api_client';
import {
  CheckPlausibilityResponse,
  UpdateDocumentationOverviewRequest,
  useMutationCheckPlausibility,
  useMutationCreateDocument,
  useMutationFinishDocumentationOverview,
  useMutationIsCaseNumberExist,
  useMutationSaveDocumentationOverview,
  useQueryGetEDOKUDocument,
} from '@tutum/hermes/bff/legacy/app_mvz_edoku';
import { PatientProfileResponse } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  AdditionalContractsEnum,
  HeaderStatus,
} from '@tutum/hermes/bff/legacy/edmp_common';
import { ScheinStatus } from '@tutum/hermes/bff/schein_common';
import I18n from '@tutum/infrastructure/i18n';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import PatientManagementUtil from '@tutum/infrastructure/utils/form.util';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { formatBirthday } from '@tutum/mvz/_utils/formatBirthday';
import { FORM_SETTING_OBJECT } from '@tutum/mvz/constant/form';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import type EHKSI18n from '@tutum/mvz/locales/en/EHKS.json';
import PatientFileSidebarLocales from '@tutum/mvz/locales/en/PatientFileSidebar.json';
import {
  DMPBillingFileWitFullURL,
  getDownloadUrlForDmpBillingFile,
} from '@tutum/mvz/module_dmp-billing/dmp.helper';
import NavigationPatients from '@tutum/mvz/module_mobile-card-reader/multi-patient-dialog/NavigationPatients';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { handleShowPatientNumber } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import {
  patientFileActions,
  patientFileStore,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import { preventiveBillingActions } from '@tutum/mvz/module_preventive-billing/billing.store';
import { debounce, groupBy, isEmpty, isNil, omit } from 'lodash';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import LeftPanel from './LeftPanel';
import RightPanel from './RightPanel';
import {
  CASE_NUMBER_LENGTH,
  FilteredDocument,
  MAXIMUM_RETRY_CHECK_PLAUSIBILITY,
  convertDocumentValues,
  convertToFormValues,
  getFieldNameWithoutSpecialCharacter,
  handleScrollToFirstError,
  parseFieldName,
} from './helpers';
import { HiddenListByFieldName } from './constant';
import { randomNumberStr } from '@tutum/infrastructure/utils/string.util';

enum ActionsType {
  CHECK = 'checkPlausibility',
  SAVE = 'save',
  FINISH = 'finish',
  FINISH_ALL = 'finishAll',
}

export interface EHKSDocumentationOverviewProps {
  className?: string;
  isOpen: boolean;
  isReadOnly?: boolean;
  isBilling?: boolean;
  isPatientOverview?: boolean;
  isLoadingPatientProfile?: boolean;
  isLoadingCheckValidation?: boolean;
  hasReopen?: boolean;
  activePatientIdx?: number;
  totalReviewingPatients?: number;
  singleDocumentationOverview?: EnrollmentDocumentInfoModel;
  patientDetail?: PatientProfileResponse;
  documentationsOverview?: EnrollmentDocumentInfoModel[];
  validationErrors?: FieldValidationResult[];
  patientInfosMap?: Record<string, PatientProfileResponse>;
  isValidationList?: boolean;
  fieldValidationResultsByDocumentationId?: Record<
    string,
    FieldValidationResult[]
  >;
  ableShowReopenWarn?: boolean;

  additionalContracts?: AdditionalContractsEnum;
  closeModal: () => void;
  onNextPatient?: () => void;
  onPrevPatient?: () => void;
}

export interface LeftPanelValues {
  dmpCaseNumber: string;
  createdAt: Date;
  selectedSchein: string;
  dmpValues: Array<{
    doctor?: string;
    bsnrId?: string;
    documentStatus?: string;
    name?: string;
  }>;
}

export interface HiddenFieldNamesByHeaderName {
  [headerName: string]: HiddenListByFieldName;
}

const EHKSDocumentationOverview = ({
  className,
  isOpen,
  isReadOnly = false,
  isBilling = false,
  isPatientOverview,
  hasReopen = false,
  isLoadingPatientProfile = false,
  isLoadingCheckValidation,
  patientDetail,
  documentationsOverview,
  singleDocumentationOverview,
  patientInfosMap,
  validationErrors,
  additionalContracts,
  closeModal,
  isValidationList = false,
  fieldValidationResultsByDocumentationId,
  ableShowReopenWarn = true,
}: EHKSDocumentationOverviewProps) => {
  const {
    patientManagement: { patient: patientContext },
  } = useContext(PatientManagementContext.instance);
  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();

  const {
    setEdokuDocumentationsOverview,
    setFieldValidationResultsByDocumentationId,
  } = preventiveBillingActions;

  const [currentDocumentationOverview, setCurrentDocumentationOverview] =
    useState<EnrollmentDocumentInfoModel | undefined>(
      isBilling && documentationsOverview?.length
        ? documentationsOverview[0]
        : singleDocumentationOverview
    );
  const [currentDocumentDate, setCurrentDocumentDate] = useState<number>(
    currentDocumentationOverview
      ? currentDocumentationOverview.documentationOverview.documentDate
      : +DatetimeUtil.date()
  );

  const documentsOverviewById = useMemo(() => {
    if (!documentationsOverview) return {};
    return documentationsOverview.reduce(
      (acc, doc) => {
        if (!doc?.documentationOverview?.documentationOverviewId) return acc;
        acc[doc.documentationOverview.documentationOverviewId] = doc;
        return acc;
      },
      {} as Record<string, EnrollmentDocumentInfoModel>
    );
  }, [documentationsOverview]);

  const isAutomaticallyCloseModal = (documentationsOverview || []).every(
    (doc) =>
      doc.documentationOverview.documentStatus ===
      DocumentStatus.DocumentStatus_Finished
  );

  useEffect(() => {
    if (isAutomaticallyCloseModal && isValidationList && !isReadOnly) {
      setActivePatientIdx(0);
    }
  }, [isAutomaticallyCloseModal, isValidationList, isReadOnly]);

  const isFinishedDocumentation =
    currentDocumentationOverview?.documentationOverview.documentStatus ===
    DocumentStatus.DocumentStatus_Finished &&
    (!isPatientOverview || hasReopen);
  const [patientInfo, setPatientInfo] = useState<
    PatientProfileResponse | undefined
  >(
    patientInfosMap
      ? patientInfosMap[
      currentDocumentationOverview?.documentationOverview?.patientId || ''
      ]
      : patientDetail
  );

  const [activePatientIdx, setActivePatientIdx] = useState<number>(0);
  const totalReviewingPatients = documentationsOverview?.length || 0;

  const [hiddenFieldNames, setHiddenFieldNames] =
    useState<HiddenFieldNamesByHeaderName>({});
  const [hiddenHeaderNames, setHiddenHeaderNames] =
    useState<HiddenListByFieldName>({});
  const hiddenHeaderNamesValues = Object.values(hiddenHeaderNames).flat();
  const onNextPatient = () => {
    if (activePatientIdx < totalReviewingPatients) {
      setActivePatientIdx((prev) => prev + 1);
      setInitValuesRightPanel({});
    }
  };
  const onPrevPatient = () => {
    if (activePatientIdx > 0) {
      setActivePatientIdx((prev) => prev - 1);
      setInitValuesRightPanel({});
    }
  };
  const rightPanelRef = useRef<any>(null);

  useEffect(() => {
    if (!patientDetail) return;

    setPatientInfo(patientDetail);
  }, [patientDetail]);

  useEffect(() => {
    if (!documentationsOverview || !patientInfosMap) return;
    const documentation = documentationsOverview[activePatientIdx];
    setPatientInfo(
      patientInfosMap[documentation?.documentationOverview?.patientId || '']
    );
    setCurrentDocumentationOverview(documentation);
    setCurrentDocumentDate(documentation.documentationOverview?.documentDate)
  }, [activePatientIdx]);

  const { t } = I18n.useTranslation<keyof typeof EHKSI18n>({
    namespace: 'EHKS',
  });

  const { t: tLeftPanel } = I18n.useTranslation<
    keyof typeof EHKSI18n.leftPanel
  >({
    namespace: 'EHKS',
    nestedTrans: 'leftPanel',
  });

  const { t: tActions } = I18n.useTranslation<keyof typeof EHKSI18n.actions>({
    namespace: 'EHKS',
    nestedTrans: 'actions',
  });

  const { t: tPatientFile } = I18n.useTranslation<
    keyof typeof PatientFileSidebarLocales.PatientInformationSidebar
  >({
    namespace: 'PatientFileSidebar',
    nestedTrans: 'PatientInformationSidebar',
  });

  const { t: tErrorMessages } = I18n.useTranslation<
    keyof typeof EHKSI18n.errorMessages
  >({
    namespace: 'EHKS',
    nestedTrans: 'errorMessages',
  });

  const patient = useMemo(() => {
    return (
      isBilling ? patientInfo || {} : patientContext
    ) as PatientProfileResponse;
  }, [isBilling, patientInfo, patientContext]);

  const selectedContractDoctor = useMemo(() => {
    return {
      availableDoctor: doctorList,
      doctorId: isBilling
        ? currentDocumentationOverview?.documentationOverview?.doctorId
        : currentLoggedInUser.isDoctor ? currentLoggedInUser.id : doctorList[0]?.id!,
      bsnrId: isBilling
        ? currentDocumentationOverview?.documentationOverview?.bsnrId
        : currentLoggedInUser.isDoctor ? currentLoggedInUser.bsnrId : doctorList[0]?.bsnrId!,
    } as ISelectedContractDoctor;
  }, [
    isBilling,
    currentDocumentationOverview,
    currentLoggedInUser.id,
    currentLoggedInUser.bsnrId,
    doctorList,
  ]);

  const store = usePatientFileStore();
  const scheinData = patientFileStore.schein.activatedSchein;

  const [leftPanelValues, setLeftPanelValues] = useState<LeftPanelValues>({
    dmpCaseNumber: '',
    createdAt: DatetimeUtil.date(),
    selectedSchein: '',
    dmpValues: [],
  });
  const [initValueRightPanel, setInitValuesRightPanel] = useState<{
    [keyMapping: string]: string | Date | undefined;
  }>({
    additionalContracts,
  });
  const [headersError, setHeadersError] = useState<{
    [keyMapping: string]: FieldValidationResult[];
  }>({});
  const [fieldsError, setFieldsError] = useState<{
    [keyMapping: string]: ErrorField[] | undefined;
  }>({});
  const [fieldsWarning, setFieldsWarning] = useState<{
    [keyMapping: string]: ErrorField[] | undefined;
  }>({});
  const [schemasError, setSchemasError] = useState<FieldValidationResult[]>([]);
  const [fieldsErrorChangeField, setFieldsErrorChangeField] = useState<{
    [keyMapping: string]: ErrorField[] | undefined;
  }>({});
  const [fieldsWarningChangeField, setFieldsWarningChangeField] = useState<{
    [keyMapping: string]: ErrorField[] | undefined;
  }>({});
  const [fieldsWarningCustom, setFieldsWarningCustom] = useState<{
    [keyMapping: string]: ErrorField[] | undefined;
  }>({});
  const [isOpenErrorLogDialog, setOpenErrorLogDialog] =
    useState<boolean>(false);
  const [errorLogs, setErrorLogs] = useState<DMPBillingFileWitFullURL[]>([]);

  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>({});
  const [formValueByDocumentationId, _setFormValueByDocumentationId] = useState<
    Record<string, FORM_SETTING_OBJECT>
  >({});
  const setFormValueByDocumentationId = (
    obj: Record<string, FORM_SETTING_OBJECT>
  ) =>
    _setFormValueByDocumentationId((prev) => ({
      ...prev,
      ...obj,
    }));

  const [isReopen, setReopen] = useState<boolean>(false);

  const currentSchein = useCurrentSchein();

  const documentationActive = useMemo(() => {
    return leftPanelValues.dmpValues[0];
  }, [leftPanelValues.dmpValues]);

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    documentationActive?.doctor,
    documentationActive?.bsnrId
  );

  const documentType = useMemo(() => {
    if (currentDocumentationOverview) {
      return currentDocumentationOverview.documentationOverview.documentType;
    }
    return treatmentDoctor?.eHKSType === EHKSType.EHKSType_Dermatologist
      ? DocumentType.DocumentType_EHKS_D
      : DocumentType.DocumentType_EHKS_ND;
  }, [treatmentDoctor?.eHKSType, currentDocumentationOverview]);

  const {
    data: eHKSDocument,
    isFetching: isLoadingEHKSDocument,
    error: errorEHKSDocument,
  } = useQueryGetEDOKUDocument(
    {
      dMPLabelingValue: DMPValueEnum.DMPValueEnum_EDO_SkinCancer,
      documentType,
      documentDate: DatetimeUtil.date(currentDocumentDate),
    },
    {
      select: (res) => res.data.eDOKUDocument,
      enabled: !!treatmentDoctor,
    }
  );

  const checkPlausibility = useMutationCheckPlausibility();
  const createDocument = useMutationCreateDocument();
  const saveDocumentationOverview = useMutationSaveDocumentationOverview();
  const finishDocumentationOverview = useMutationFinishDocumentationOverview();
  const isLoadingCheckPlausibility = useMemo(() => {
    return (
      checkPlausibility.isPending ||
      createDocument.isPending ||
      saveDocumentationOverview.isPending ||
      finishDocumentationOverview.isPending
    );
  }, [
    checkPlausibility.isPending,
    createDocument.isPending,
    saveDocumentationOverview.isPending,
    finishDocumentationOverview.isPending,
  ]);

  const validScheins = useMemo(() => {
    const scheinList = isBilling
      ? store.schein.originalList
      : store.schein.originalList.filter(
        (s) =>
          !s?.isTechnicalSchein &&
          !s?.markedAsBilled &&
          s?.scheinStatus !== ScheinStatus.ScheinStatus_Canceled &&
          s?.scheinStatus !== ScheinStatus.ScheinStatus_Billed
      );
    const time = DatetimeUtil.dateToMoment(currentDocumentDate);
    const quarter = time.quarter();
    const year = time.year();

    return [...scheinList].filter(
      (schein) => schein.g4101Quarter === quarter && schein.g4101Year === year
    );
  }, [isBilling, currentDocumentDate, store.schein.originalList]);

  const filteredDMPDocument: FilteredDocument | null = useMemo(() => {
    if (!eHKSDocument) {
      return null;
    }
    const filteredHeaders = eHKSDocument.headers.filter((header) => {
      return !hiddenHeaderNamesValues.includes(header.name);
    });
    const filteredData = filteredHeaders.map((header) => {
      const filteredDMPs = header.dMPs.map((dMP) => {
        const headerName = dMP.headerName;
        const filteredFields = (dMP.fields || []).filter(
          (field) =>
            !Object.values(hiddenFieldNames[headerName] || {})
              .flat()
              .includes(field.name)
        );
        let filteredFieldsValues: Field[] = [];
        let headerStatus = HeaderStatus.HeaderStatus_NotFilled;
        const documentOverviewData = currentDocumentationOverview;
        if (documentOverviewData) {
          const dmpHeaderName = dMP.headerName || dMP.displayHeader;
          filteredFieldsValues = (
            documentOverviewData.documentationOverview.fields || []
          ).filter(
            (field) =>
              field.header === dmpHeaderName ||
              field.displayHeader === dmpHeaderName
          );

          if (filteredFieldsValues.length) {
            const requiredDmpFields = filteredFields.filter(
              (field) => field.isRequire
            );
            const requiredFieldsValues = filteredFieldsValues.filter(
              (field) => field.isRequire
            );

            const headerStatusComplete =
              filteredFields.length === filteredFieldsValues.length ||
              requiredDmpFields.length === requiredFieldsValues.length;
            headerStatus = headerStatusComplete
              ? HeaderStatus.HeaderStatus_Completed
              : HeaderStatus.HeaderStatus_Incomplete;
          }
        }

        return {
          ...dMP,
          headerStatus,
          fields: filteredFields,
        };
      });

      return {
        ...header,
        dMPs: filteredDMPs.filter((dMP) => !!dMP.fields.length),
      };
    });

    return {
      headers: filteredData,
    };
  }, [
    eHKSDocument,
    currentDocumentationOverview,
    rightPanelRef,
    hiddenFieldNames,
    hiddenHeaderNames,
  ]);

  const defaultSelectSchein = useMemo(() => {
    if (currentDocumentationOverview) {
      return currentDocumentationOverview.documentationOverview.scheinId;
    }

    if (currentSchein) {
      return currentSchein.scheinId;
    }

    return '';
  }, [currentDocumentationOverview, currentSchein]);

  const handleSetHiddenFieldNames = useCallback(() => {
    if (!eHKSDocument) return;
    const compositionDMPs = eHKSDocument.headers.flatMap(
      (header) => header.dMPs
    );
    const compositionFields = compositionDMPs.flatMap((dMP) => dMP.fields);
    const formValueCurrDocument =
      (formValueByDocumentationId || {})[
      currentDocumentationOverview?.documentationOverview
        .documentationOverviewId || ''
      ] || {};
    let hiddenHeaderNamesMap: HiddenListByFieldName = {};
    let hiddenFieldNamesMap: HiddenFieldNamesByHeaderName = {};
    for (const field of compositionFields) {
      const selectedOptionValue =
        formValueCurrDocument[`${field.header}_${field.name}`];
      const hiddenFieldNames: string[] = [];
      const hiddenHeaderNames: string[] = [];
      if (selectedOptionValue) {
        const selectedOption = field.options.find(
          (opt) => opt.name === selectedOptionValue
        );
        const currHiddenFieldNames = selectedOption?.hiddenFieldNames || [];
        const currentHiddenHeaderName = selectedOption?.hiddenHeaderNames || [];
        hiddenFieldNames.push(...currHiddenFieldNames);
        hiddenHeaderNames.push(...currentHiddenHeaderName);
      } else {
        hiddenFieldNames.push(
          ...field.options.flatMap((opt) => opt.hiddenFieldNames || [])
        );
        hiddenHeaderNames.push(
          ...field.options.flatMap((opt) => opt.hiddenHeaderNames || [])
        );
      }
      hiddenFieldNamesMap = {
        ...hiddenFieldNamesMap,
        [field.header]: {
          ...(hiddenFieldNamesMap[field.header] || {}),
          [field.name]: hiddenFieldNames,
        },
      };
      hiddenHeaderNamesMap = {
        ...hiddenHeaderNamesMap,
        [field.name]: hiddenHeaderNames,
      };
    }

    setHiddenFieldNames(hiddenFieldNamesMap);
    setHiddenHeaderNames(hiddenHeaderNamesMap);
  }, [eHKSDocument, currentDocumentationOverview, formValueByDocumentationId]);

  useEffect(() => {
    handleSetHiddenFieldNames();
  }, [eHKSDocument, currentDocumentationOverview, formValueByDocumentationId]);

  const isDMPCaseNumberExist = useMutationIsCaseNumberExist();

  const onCheckExistedCaseNumber = useMemo(() => {
    const documentId =
      currentDocumentationOverview?.documentationOverview
        ?.documentationOverviewId;
    return debounce((caseNumber: string) => {
      caseNumber &&
        isDMPCaseNumberExist.mutate({
          caseNumber,
          ...(documentId
            ? {
              documentId,
            }
            : {}),
        });
    }, 500);
  }, [currentDocumentationOverview]);

  const dMPCaseNumberError = useMemo(() => {
    return leftPanelValues.dmpCaseNumber
      ? isDMPCaseNumberExist.data?.data?.isExist
        ? tLeftPanel('caseNumberExisted')
        : ''
      : tLeftPanel('caseNumberRequired');
  }, [leftPanelValues.dmpCaseNumber, isDMPCaseNumberExist.data?.data?.isExist]);

  const hasValidSchein = useMemo(() => {
    return !!validScheins.length;
  }, [validScheins]);

  const isDisabledFinishPrint = useMemo(() => {
    return !eHKSDocument || !hasValidSchein || !!dMPCaseNumberError;
  }, [eHKSDocument, hasValidSchein, dMPCaseNumberError]);

  const isDisabledAction = useMemo(() => {
    return (
      !isReopen &&
      (isDisabledFinishPrint || isReadOnly || isFinishedDocumentation)
    );
  }, [
    isDisabledFinishPrint,
    isReadOnly,
    currentDocumentationOverview,
    activePatientIdx,
    isReopen,
  ]);

  const title = useMemo(() => {
    if (isEmpty(patient)) {
      return null;
    }

    const patientName = PatientManagementUtil.getFullName(
      patient.patientInfo?.personalInfo?.title || '',
      patient.patientInfo?.personalInfo?.intendWord || '',
      patient.patientInfo?.personalInfo?.lastName || '',
      patient.patientInfo?.personalInfo?.firstName || ''
    );
    const activeInsurance = getActiveInsurance(
      patient.patientInfo.insuranceInfos
    );
    const ikNumberText = activeInsurance
      ? `${t('ikNumber')} ${activeInsurance.ikNumber}`
      : '';
    const birthday = formatBirthday(
      tPatientFile,
      patient.patientInfo?.personalInfo?.dateOfBirth
    );
    const gender = patient?.patientInfo?.personalInfo?.gender;
    const idNumber = `${tPatientFile('ID')}: ${handleShowPatientNumber(
      patient?.patientInfo?.patientNumber
    )}`;
    const contactNumber =
      patient.patientInfo?.contactInfo?.primaryContactNumber;
    const address = patient.patientInfo?.addressInfo?.address;
    const dspAddress = [
      `${address?.street} ${address?.number}`.trim(),
      `${address?.postCode} ${address?.city}`.trim(),
    ]
      .filter((str) => str)
      .join(', ');
    const patientInfo = [
      ikNumberText,
      birthday,
      gender,
      idNumber,
      contactNumber,
      dspAddress,
    ]
      .filter((value) => value)
      .join(' • ');

    return (
      <Flex justify="space-between">
        <Flex column>
          <H2>
            {t('title')} {patientName}
          </H2>
          <BodyTextM>{patientInfo}</BodyTextM>
        </Flex>
        {isBilling && !!totalReviewingPatients && (
          <NavigationPatients
            activePatientIdx={activePatientIdx}
            totalReviewingPatients={totalReviewingPatients}
            onNextPatient={onNextPatient}
            onPrevPatient={onPrevPatient}
          />
        )}
      </Flex>
    );
  }, [
    patient,
    isBilling,
    activePatientIdx,
    totalReviewingPatients,
    onNextPatient,
    onPrevPatient,
  ]);

  const fieldsWarningShow = useMemo(() => {
    if (isEmpty(fieldsWarningCustom)) {
      return fieldsWarning;
    }

    const warning = { ...fieldsWarning };

    Object.keys(fieldsWarningCustom).forEach((key) => {
      const value = fieldsWarningCustom[key] || [];

      if (warning[key]) {
        warning[key] = [...(warning[key] || []), ...value];
      } else {
        warning[key] = value;
      }
    });

    return warning;
  }, [fieldsWarning, fieldsWarningCustom]);

  const handleCheckPlausibilityOnField = useCallback(
    debounce(
      async (
        payLoad,
        callback: (resp: CheckPlausibilityResponse) => void,
        retryCheckPlaus: number
      ) => {
        try {
          const rightPanelValues = rightPanelRef?.current?.values;
          const resp = await checkPlausibility.mutateAsync({
            ...payLoad,
            doctorId: documentationActive?.doctor,
            patientId: patient.id,
            dMPCaseNumber: leftPanelValues.dmpCaseNumber,
            scheinId:
              currentDocumentationOverview?.documentationOverview?.scheinId ||
              leftPanelValues.selectedSchein,
            additionalContracts: rightPanelValues?.additionalContracts,
            isBillingCheck: true,
          });
          callback(resp?.data);
        } catch (err) {
          const { status, headers } = err?.response || {};
          if (
            status === 400 &&
            headers['content-type'] === 'text/html' &&
            retryCheckPlaus < MAXIMUM_RETRY_CHECK_PLAUSIBILITY
          ) {
            handleCheckPlausibilityOnField(
              payLoad,
              callback,
              retryCheckPlaus + 1
            );
          } else {
            throw err;
          }
        }
      },
      200
    ),
    [
      patient,
      leftPanelValues,
      documentationActive,
      currentDocumentationOverview,
    ]
  );

  const handleError = useCallback(
    (fieldValidationResults: FieldValidationResult[], name = '') => {
      const errorsByType = fieldValidationResults?.reduce(
        (errors, error) => {
          const isFieldError = !!error.fieldName;
          const isHeaderError =
            !!error.headerName &&
            eHKSDocument?.headers?.some(
              (header) => header.name === error.headerName
            ) &&
            error.errorType !== ErrorType.ErrorType_Schema;

          if (isFieldError) {
            const fieldName = getFieldNameWithoutSpecialCharacter(
              parseFieldName(error.headerName ?? '', error.fieldName)
            );
            const fieldError = {
              errorCode: error.errorCode,
              errorMessage:
                error.fieldValidationResultType ===
                  FieldValidationResultType.FieldValidationResultType_SelfCheck &&
                  error.errorType === ErrorType.ErrorType_Error
                  ? tErrorMessages(
                    error.errorCode as keyof typeof EHKSI18n.errorMessages
                  )
                  : error.errorMessage,
              errorContent: error?.script?.content,
            };

            const keyMapping =
              error.errorType === ErrorType.ErrorType_Warning
                ? 'fieldsWarning'
                : 'fieldsError';

            if (errors[keyMapping][fieldName]) {
              errors[keyMapping][fieldName].push(fieldError);
            } else {
              errors[keyMapping][fieldName] = [fieldError];
            }
          } else if (isHeaderError) {
            errors.headersError.push(error);
          } else {
            errors.schemasError.push(error);
          }

          return errors;
        },
        {
          fieldsError: {} as {
            [keyMapping: string]: ErrorField[];
          },
          fieldsWarning: {} as {
            [keyMapping: string]: ErrorField[];
          },
          headersError: [] as FieldValidationResult[],
          schemasError: [] as FieldValidationResult[],
        }
      );

      if (name) {
        setFieldsError((prevValue) => {
          if (!Object.keys(prevValue)[0]) {
            return errorsByType.fieldsError;
          }

          if (Object.keys(errorsByType.fieldsError)[0]) {
            return {
              ...prevValue,
              ...errorsByType.fieldsError,
            };
          }

          const newValue = Object.keys(prevValue).reduce(
            (errors, fieldName) => {
              let value = prevValue[fieldName];

              if (name === fieldName || fieldsErrorChangeField[fieldName]) {
                value = undefined;
              }

              return {
                ...errors,
                [fieldName]: value,
              };
            },
            prevValue
          );

          return newValue;
        });
        setFieldsWarning((prevValue) => {
          if (!Object.keys(prevValue)[0]) {
            return errorsByType.fieldsWarning;
          }

          if (Object.keys(errorsByType.fieldsWarning)[0]) {
            return {
              ...prevValue,
              ...errorsByType.fieldsWarning,
            };
          }

          const newValue = Object.keys(prevValue).reduce(
            (warnings, fieldName) => {
              let value = prevValue[fieldName];

              if (name === fieldName || fieldsWarningChangeField[fieldName]) {
                value = undefined;
              }

              return {
                ...warnings,
                [fieldName]: value,
              };
            },
            prevValue
          );

          return newValue;
        });
        setFieldsErrorChangeField(errorsByType.fieldsError);
        setFieldsWarningChangeField(errorsByType.fieldsWarning);

        return;
      }

      setFieldsError(errorsByType?.fieldsError || {});
      setFieldsWarning(errorsByType?.fieldsWarning || {});
      setHeadersError(groupBy(errorsByType?.headersError, 'headerName'));
      setSchemasError(errorsByType?.schemasError || []);
      handleScrollToFirstError();
    },
    [eHKSDocument, fieldsErrorChangeField, fieldsWarningChangeField]
  );

  const handlePlausibilityError = useCallback(
    async (resp: CheckPlausibilityResponse) => {
      handleError(resp.fieldValidationResults || []);

      const errorLogs: DMPBillingFile[] = [];

      if (resp.billingFile) {
        errorLogs.push(resp.billingFile);
      }

      if (resp.xPMPFile) {
        errorLogs.push(resp.xPMPFile);
      }

      const errorLogsUrl = await getDownloadUrlForDmpBillingFile(errorLogs);

      setErrorLogs(errorLogsUrl);
      setOpenErrorLogDialog(true);
    },
    [handleError]
  );

  const handleFinishDocument = async (
    payload: any,
    shouldCloseModal = true
  ) => {
    let response;

    if (currentDocumentationOverview?.id) {
      response = await finishDocumentationOverview.mutateAsync({
        ...currentDocumentationOverview,
        ...payload,
      } as unknown as UpdateDocumentationOverviewRequest);
    } else {
      response = await createDocument.mutateAsync(payload);
    }

    const checkPlausibilityResponse = response.data?.checkPlausibilityResponse;

    if (checkPlausibilityResponse && !checkPlausibilityResponse?.isPlausible) {
      alertError(tActions('checkFailed'));

      handlePlausibilityError(checkPlausibilityResponse);

      return;
    }

    alertSuccessfully(tActions('documentFinished'));
    setFieldValidationResultsByDocumentationId(
      omit(
        {
          ...fieldValidationResultsByDocumentationId,
        },
        `${currentDocumentationOverview?.documentationOverview
          ?.documentationOverviewId
        }`
      )
    );
    setEdokuDocumentationsOverview(
      (documentationsOverview || []).map((doc) => {
        return doc.documentationOverview.documentationOverviewId !==
          currentDocumentationOverview?.documentationOverview
            .documentationOverviewId
          ? doc.documentationOverview
          : {
            ...doc.documentationOverview,
            documentStatus: DocumentStatus.DocumentStatus_Finished,
          };
      })
    );
    setCurrentDocumentationOverview(
      (prev) =>
        ({
          ...prev,
          documentationOverview: {
            ...(prev?.documentationOverview || {}),
            documentStatus: DocumentStatus.DocumentStatus_Finished,
          },
        }) as EnrollmentDocumentInfoModel
    );
    setReopen(false);

    shouldCloseModal && closeModal();
  };

  const handleFinishAll = useCallback(async () => {
    try {
      if (!documentationsOverview?.length) return;
      const checkPlausibilityOnFieldPromise: (() => Promise<
        ResponseType<CheckPlausibilityResponse>
      >)[] = [];
      const finishAllPromise: (() => Promise<ResponseType<any>>)[] = [];
      documentationsOverview?.forEach((doc) => {
        const documentId = doc.documentationOverview.documentationOverviewId;
        if (!documentId) return;
        let rightPanelValues = formValueByDocumentationId[documentId];
        const currentDocument = documentsOverviewById[documentId];
        if (!rightPanelValues) {
          rightPanelValues = convertToFormValues(
            currentDocument?.documentationOverview.fields || [],
            false
          );
        }

        if (
          !documentationActive.doctor ||
          (!currentDocument?.documentationOverview?.scheinId &&
            !leftPanelValues.selectedSchein)
        )
          return;

        const checkPlausibilityPromise = () =>
          checkPlausibility.mutateAsync({
            relatedFields: convertDocumentValues(
              eHKSDocument as FilteredDocument,
              rightPanelValues || {},
              true
            ),
            documentType,
            dMPLabelingValue: DMPValueEnum.DMPValueEnum_EDO_SkinCancer,
            doctorRelationType: DoctorRelationType.DoctorRelationType_Treatment,
            documentDate: +rightPanelValues.documentDate,
            ...(currentDocument?.id && {
              documentId: currentDocument.id,
            }),
            doctorId: documentationActive.doctor || '',
            patientId: patient.id,
            dMPCaseNumber: leftPanelValues.dmpCaseNumber,
            scheinId:
              currentDocument?.documentationOverview?.scheinId ||
              leftPanelValues.selectedSchein ||
              '',
            additionalContracts:
              rightPanelValues?.additionalContracts as AdditionalContractsEnum,
            isBillingCheck: true,
          });
        const finishPromises = () =>
          saveDocumentationOverview.mutateAsync({
            ...currentDocument,
            documentationOverview: {
              ...currentDocument?.documentationOverview,
              documentStatus: DocumentStatus.DocumentStatus_Finished,
              fields: convertDocumentValues(
                eHKSDocument as FilteredDocument,
                rightPanelValues || {}
              ),
            },
          } as unknown as UpdateDocumentationOverviewRequest);

        checkPlausibilityOnFieldPromise.push(checkPlausibilityPromise);
        finishAllPromise.push(finishPromises);
      });
      const checkPlausibilityResponses = await Promise.all(
        checkPlausibilityOnFieldPromise.map((fn) => fn())
      );
      const isExistCheckPlausibilityError = checkPlausibilityResponses.some(
        (resp) => !resp.data.isPlausible
      );
      if (isExistCheckPlausibilityError) {
        alertError(tActions('checkFailed'));
        return;
      }
      await Promise.all(finishAllPromise.map((fn) => fn()));
      closeModal();
    } catch (err) {
      console.error(err);
      alertError(tActions('checkFailed'));
    }
  }, [
    documentationsOverview,
    formValueByDocumentationId,
    documentsOverviewById,
    eHKSDocument,
    leftPanelValues,
  ]);

  const handleFinish = useCallback(
    (action: ActionsType, payload) => {
      if (action === ActionsType.FINISH) {
        handleFinishDocument(
          payload,
          !documentationsOverview?.length ? true : false
        );

        return;
      }
    },
    [currentDocumentationOverview, documentationsOverview]
  );

  const handleActions = useCallback(
    async (action: ActionsType, callback?: () => void) => {
      try {
        if (!leftPanelValues.selectedSchein) {
          handleScrollToFirstError();
          return;
        }

        const documentId =
          currentDocumentationOverview?.documentationOverview
            ?.documentationOverviewId;
        const resp = await isDMPCaseNumberExist.mutateAsync({
          caseNumber: leftPanelValues.dmpCaseNumber,
          ...(documentId
            ? {
              documentId,
            }
            : {}),
        });

        if (resp.data.isExist) {
          return;
        }

        const _rightPanelRef = rightPanelRef?.current;
        const rightPanelValues = _rightPanelRef?.values;
        const value = convertDocumentValues(
          eHKSDocument as FilteredDocument,
          rightPanelValues || {}
        );

        const payload = {
          documentationOverview: {
            enrollmentId: null as unknown as string,
            scheinId: leftPanelValues.selectedSchein,
            dMPCaseNumber: leftPanelValues.dmpCaseNumber,
            dMPLabelingValue: DMPValueEnum.DMPValueEnum_EDO_SkinCancer,
            treatmentDoctorId: treatmentDoctor?.id || '',
            doctorId: treatmentDoctor?.id || '',
            documentType,
            doctorRelationType: DoctorRelationType.DoctorRelationType_Treatment,
            documentStatus: '' as DocumentStatus,
            enrollStatus: '' as EnrollStatus,
            patientId: patient.id,
            fields: value,
            documentDate: +rightPanelValues?.documentDate,
            dMPBillingFile: currentDocumentationOverview
              ? currentDocumentationOverview.documentationOverview
                .dMPBillingFile
              : ({} as DMPBillingFile),
            bsnrCode: treatmentDoctor?.bsnr,
            bsnrId: treatmentDoctor?.bsnrId,
            additionalContracts: rightPanelValues?.additionalContracts,
          },
        };
        if (action === ActionsType.SAVE) {
          payload.documentationOverview.documentStatus =
            DocumentStatus.DocumentStatus_Saved;

          if (currentDocumentationOverview?.id) {
            await saveDocumentationOverview.mutateAsync({
              ...currentDocumentationOverview,
              ...payload,
            });
          } else {
            await createDocument.mutateAsync(payload);
          }

          alertSuccessfully(tActions('documentSaved'));

          if (callback) {
            callback();
          }

          if (!documentationsOverview?.length) {
            closeModal();
          }
          return;
        }

        _rightPanelRef?.handleSubmit();

        // waiting for check validation in right panel
        await new Promise((resolve) => setTimeout(resolve));

        if (!(rightPanelRef?.current as any)?.isValid) {
          handleScrollToFirstError();
          return;
        }

        await handleCheckPlausibilityOnField(
          {
            relatedFields: convertDocumentValues(
              eHKSDocument as FilteredDocument,
              rightPanelValues || {},
              true
            ),
            field: null,
            documentType,
            dMPLabelingValue: DMPValueEnum.DMPValueEnum_EDO_SkinCancer,
            doctorRelationType: DoctorRelationType.DoctorRelationType_Treatment,
            documentDate: +rightPanelValues.documentDate,
            enrollmentId: null,
            ...(currentDocumentationOverview?.id && {
              documentId: currentDocumentationOverview.id,
            }),
          },
          async (resp: CheckPlausibilityResponse) => {
            if (!resp.isPlausible) {
              alertError(tActions('checkFailed'));

              handlePlausibilityError(resp);

              return;
            }

            const headersError = resp.fieldValidationResults?.filter(
              (error) =>
                !error.fieldName &&
                error.errorType === ErrorType.ErrorType_Warning
            );

            handleError(resp.fieldValidationResults);
            setHeadersError(groupBy(headersError, 'headerName'));

            if (action == ActionsType.CHECK) {
              alertSuccessfully(tActions('checkSuccessful'));
            }

            if ([ActionsType.FINISH].includes(action)) {
              payload.documentationOverview.documentStatus =
                DocumentStatus.DocumentStatus_Finished;

              handleFinish(action, payload);

              return;
            }
          },
          1
        );
      } catch (err) {
        if (
          err?.validationErrors?.some((error) => error.field === 'DocumentDate')
        ) {
          rightPanelRef.current.setFieldError(
            'documentDate',
            tActions('documentationDateRequired')
          );
          rightPanelRef.current.setFieldTouched('documentDate', true);

          const element = document.querySelector(`label[name='documentDate']`);

          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }

          return;
        }

        console.error(err);
        alertError('checkFailed');
      }
    },
    [
      isReadOnly,
      leftPanelValues,
      treatmentDoctor,
      patient.id,
      eHKSDocument,
      formValue,
      handleCheckPlausibilityOnField,
      handlePlausibilityError,
      handleFinishDocument,
      documentType,
    ]
  );

  const handleNewData = useCallback(() => {
    setLeftPanelValues((prevValues) => ({
      ...prevValues,
      dmpValues: [
        {
          doctor: selectedContractDoctor.doctorId,
          bsnrId: selectedContractDoctor.bsnrId,
          documentStatus: '',
          name: DMPValueEnum.DMPValueEnum_EDO_SkinCancer,
        },
      ],
      dmpCaseNumber: randomNumberStr(CASE_NUMBER_LENGTH),
    }));
    setInitValuesRightPanel({
      additionalContracts,
      documentDate: DatetimeUtil.date(),
    });
  }, [
    selectedContractDoctor.doctorId,
    selectedContractDoctor.bsnrId,
    patient,
    additionalContracts,
  ]);

  const handleExistedData = useCallback(
    async (
      documentOverviewData: EnrollmentDocumentInfoModel,
      additionalvalue: FORM_SETTING_OBJECT = {}
    ) => {
      const dmpValue = {
        doctor: documentOverviewData?.documentationOverview.doctorId,
        bsnrId: documentOverviewData?.documentationOverview.bsnrId,
        doctorRelation: DoctorRelationType.DoctorRelationType_Treatment,
        name: documentOverviewData?.documentationOverview.dMPLabelingValue,
      };
      setLeftPanelValues((prevValues) => {
        const dmpValues = [dmpValue];

        return {
          ...prevValues,
          createdAt: documentOverviewData?.createdAt
            ? new Date(documentOverviewData.createdAt)
            : DatetimeUtil.date(),
          selectedSchein:
            documentOverviewData?.documentationOverview.scheinId || '',
          dmpCaseNumber:
            documentOverviewData?.documentationOverview.dMPCaseNumber || '',
          dmpValues,
        };
      });

      let formValues = convertToFormValues(
        documentOverviewData?.documentationOverview.fields || [],
        false
      );

      const { documentDate, additionalContracts } =
        documentOverviewData?.documentationOverview || {};
      formValues = {
        ...formValues,
        ...additionalvalue,
        additionalContracts,
        documentDate: documentDate ? new Date(documentDate) : null,
      };
      setInitValuesRightPanel(formValues);
    },
    []
  );

  const onChangeField = async (field: Field, values) => {
    await handleCheckPlausibilityOnField(
      {
        relatedFields: convertDocumentValues(
          eHKSDocument as FilteredDocument,
          values || {},
          true
        ),
        field,
        documentType,
        dMPLabelingValue: DMPValueEnum.DMPValueEnum_EDO_SkinCancer,
        doctorRelationType: DoctorRelationType.DoctorRelationType_Treatment,
        documentDate: +values.documentDate,
        enrollmentId: null,
        ...(currentDocumentationOverview?.id && {
          documentId: currentDocumentationOverview.id,
        }),
      },
      async (resp: CheckPlausibilityResponse) => {
        handleError(resp.fieldValidationResults, field.name);
      },
      3
    );
  };

  useEffect(() => {
    if (currentDocumentationOverview) {
      const currDocumentationId =
        currentDocumentationOverview?.documentationOverview
          ?.documentationOverviewId || '';
      const formValue = currDocumentationId
        ? formValueByDocumentationId[currDocumentationId]
        : {};
      handleExistedData(currentDocumentationOverview, formValue);

      return;
    }

    handleNewData();
  }, [currentDocumentationOverview, handleExistedData, handleNewData]);

  useEffect(() => {
    if (isBilling && patientInfo) {
      patientFileActions.schein.getScheinsOverview(patientInfo?.id);
    }
  }, [isBilling, patientInfo]);

  useEffect(() => {
    if (!validationErrors && !fieldValidationResultsByDocumentationId) return;

    const currDocumentationId =
      currentDocumentationOverview?.documentationOverview
        ?.documentationOverviewId || '';

    const fieldErrors =
      validationErrors ??
      fieldValidationResultsByDocumentationId?.[currDocumentationId] ??
      [];

    handleError(fieldErrors);
  }, [
    validationErrors,
    fieldValidationResultsByDocumentationId,
    currentDocumentationOverview,
  ]);

  return (
    <Dialog
      className={className}
      isOpen={isOpen}
      title={title}
      size="full"
      actions={
        !isFinishedDocumentation || isReopen ? (
          <>
            <Button
              minimal
              outlined
              intent="primary"
              large
              loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
              disabled={isDisabledAction}
              onClick={() => handleActions(ActionsType.CHECK)}
            >
              {tActions('checkPlausibility')}
            </Button>
            <Button
              minimal
              outlined
              intent="primary"
              large
              loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
              disabled={isDisabledAction}
              onClick={() => handleActions(ActionsType.SAVE)}
            >
              {tActions(currentDocumentationOverview?.id ? 'update' : 'save')}
            </Button>
            <Button
              minimal={isBilling && !isPatientOverview}
              outlined={isBilling && !isPatientOverview}
              intent="primary"
              large
              loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
              disabled={isDisabledAction}
              onClick={() => handleActions(ActionsType.FINISH)}
            >
              {tActions('finish')}
            </Button>
            {isBilling && !isPatientOverview && (
              <Button
                intent="primary"
                large
                loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
                onClick={handleFinishAll}
                disabled={isDisabledAction}
              >
                {tActions('finishAll')}
              </Button>
            )}
          </>
        ) : null
      }
      onClose={() => {
        setActivePatientIdx(0);
        closeModal();
      }}
    >
      <Flex column h="100%">
        {ableShowReopenWarn &&
          !isReopen &&
          (isFinishedDocumentation || hasReopen) && (
            <Flex>
              <MessageBar
                type="warning"
                content={t('reopenText')}
                hasBullet={false}
                actionButtonGroup={
                  <Button
                    intent="primary"
                    minimal
                    onClick={() => setReopen(true)}
                  >
                    {t('reopenDocument')}
                  </Button>
                }
              />
            </Flex>
          )}
        <Flex
          h={`${hasReopen && !isReopen ? 'calc(100% - 48px)' : '100%'}`}
          className="sl-eHKSDocumentationOverview-body"
        >
          <LeftPanel
            rightPanelRef={rightPanelRef}
            isReadOnly={
              !isReopen &&
              (isReadOnly || isFinishedDocumentation)
            }
            scheinList={validScheins}
            doctorList={selectedContractDoctor.availableDoctor.filter(
              (doctor) => !isNil(doctor.eHKSType) && !!doctor.bsnrId
            )}
            leftPanelValues={leftPanelValues}
            patient={patient}
            dataMapping={eHKSDocument || null}
            defaultSelectSchein={defaultSelectSchein}
            dMPCaseNumberError={dMPCaseNumberError}
            isCreate={!currentDocumentationOverview}
            setLeftPanelValues={(newValue) => {
              setLeftPanelValues((prevValues) => ({
                ...prevValues,
                ...newValue,
              }));
            }}
            handleResetState={() => {
              setInitValuesRightPanel({});
              setFormValue({});
              setHeadersError({});
              setFieldsWarning({});
              setFieldsError({});
              setSchemasError([]);
              setFieldsWarningChangeField({});
              setFieldsWarningCustom({});
              setFieldsErrorChangeField({});
            }}
            onCheckExistedCaseNumber={onCheckExistedCaseNumber}
          />
          <RightPanel
            isLoading={isLoadingEHKSDocument}
            isReadOnly={!isReopen && (isReadOnly || isFinishedDocumentation)}
            initValues={initValueRightPanel}
            contentRef={rightPanelRef}
            dataMapping={filteredDMPDocument || null}
            headersError={headersError}
            fieldsError={fieldsError}
            fieldsWarning={fieldsWarningShow}
            schemasError={schemasError}
            currentDocumentDate={currentDocumentDate}
            documentationActive={documentationActive}
            formValue={formValue}
            patient={patient}
            errorDMPDocument={errorEHKSDocument?.response?.data}
            onChangeFormValue={(formValue) => {
              setFormValue((prevValues) => ({
                ...prevValues,
                ...formValue,
              }));
            }}
            isValidationList={isValidationList}
            setCurrentDocumentDate={setCurrentDocumentDate}
            hasValidSchein={hasValidSchein}
            onChangeField={onChangeField}
            setHiddenFieldNames={setHiddenFieldNames}
            currentDocumentationId={
              currentDocumentationOverview?.documentationOverview
                ?.documentationOverviewId
            }
            setFormValueByDocumentationId={setFormValueByDocumentationId}
            hiddenFieldNames={hiddenFieldNames}
            setHiddenHeaderNames={setHiddenHeaderNames}
            documentType={documentType}
          />
          {(isLoadingEHKSDocument || isLoadingPatientProfile) && (
            <LoadingState />
          )}
          <InfoConfirmDialog
            type="primary"
            isOpen={isOpenErrorLogDialog}
            title={tActions('checkFailed')}
            cancelText={tActions('close')}
            isCloseButtonShown={false}
            isConfirmButtonShown={false}
            onClose={() => setOpenErrorLogDialog(false)}
          >
            <Flex column>
              {tActions('viewErrorLog')}

              <Flex column mt={18} gap={8}>
                {errorLogs.map((file, index) => {
                  return (
                    <PDFDownloadFile
                      key={index}
                      url={file.url}
                      name={file.fileName}
                    />
                  );
                })}
              </Flex>
            </Flex>
          </InfoConfirmDialog>
        </Flex>
      </Flex>
    </Dialog>
  );
};

export default EHKSDocumentationOverview;
