import { useQuery } from '@tanstack/react-query';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import React, {
  MutableRefObject,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';

import {
  BodyTextL,
  BodyTextM,
  Flex,
  H2,
  LoadingState,
  MessageBar,
  Svg,
  TOASTER_TIMEOUT_CUSTOM,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import Button from '@tutum/design-system/components/Button/Button';
import {
  <PERSON><PERSON>,
  Divider,
  Intent,
  OverlayToaster,
  Position,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog/InfoConfirmDialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { getAge } from '@tutum/design-system/infrastructure/utils';
import {
  useListenAutoAction,
  useListenTimelineCreate,
  useListenTimelineRemove,
  useListenTimelineUpdate,
} from '@tutum/hermes/bff/app_mvz_timeline';
import {
  MainGroup,
  PatientParticipationStatus,
  ScheinWithMainGroup,
} from '@tutum/hermes/bff/common';
import { useQueryGetActiveTimelineDocumentType } from '@tutum/hermes/bff/legacy/app_admin_timeline_document_type';
import {
  reSubmitPreParticipateService,
  submitPreParticipateService,
} from '@tutum/hermes/bff/legacy/app_mvz_billing';
import { getError } from '@tutum/hermes/bff/legacy/app_mvz_billing_kv';
import { searchEbmsComposer } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { useQueryGetPatientParticipation } from '@tutum/hermes/bff/legacy/app_mvz_patient_participation';
import {
  ITimelineEntityType,
  document88130,
  findLatestTimelineEntry,
  useQueryGetPreParticipationServiceCodes,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { PreParticipateType } from '@tutum/hermes/bff/legacy/repo_encounter';
import { PreParticipateServiceSubmissionRequest } from '@tutum/hermes/bff/legacy/service_domains_billing';
import { Sources } from '@tutum/hermes/bff/legacy/service_domains_patient_file';
import { EncounterCase, TreatmentCase } from '@tutum/hermes/bff/repo_encounter';
import { ServiceErrorCode } from '@tutum/hermes/bff/service_domains_validation_timeline';
import {
  ActionType,
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import { default as I18n, default as i18n } from '@tutum/infrastructure/i18n';
import { DATE_TIME_TRANSFER_UTC } from '@tutum/infrastructure/shared/date-format';
import {
  default as DatetimeUtil,
} from '@tutum/infrastructure/utils/datetime.util';
import AccountManagementUtil from '@tutum/infrastructure/utils/form.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { handleAutoDocument } from '@tutum/mvz/_utils/autoDocument';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { useGetTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import { composerActionChainActions } from '@tutum/mvz/module_action-chain';
import { PreviewERP } from '@tutum/mvz/module_e-documents';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { useKvBillingStore } from '@tutum/mvz/module_kv_billing/KVBilling.store';
import type { FORM_SECTION } from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import { MailboxType } from '@tutum/mvz/module_mailbox/Mailbox.type';
import ViewMailModal from '@tutum/mvz/module_mailbox/view-mail-modal/ViewMailModal.styled';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import MedicationPrintPreview from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.styled';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  PatientPostActionType,
  usePatientPostAction,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.hook';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { useActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import ServiceBlockService from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-block/services/service-block.service';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { useStore } from '@tutum/mvz/module_setting/patient_profile';
import { IMvzTheme } from '@tutum/mvz/theme';
import ComposerContainer from '../encounter-v2/ComposerContainer';
import VerahPopup from '@tutum/mvz/module_patient-management/patient-file/verah-pop-up';
import {
  PreEnrollmentService,
  debounceSetModifiedId,
  serviceCode56565,
} from './Timeline.const';
import { SourceScreen, useTimeline } from './Timeline.hooks';
import { timelineActions, useTimeLineStore } from './Timeline.store';
import { GlobalStyleForPortalMenu } from './timeline-content/timeline-entry/diagnose-entry/DiagnoseEntry.styled';
import TimelineQuarter from './timeline-content/timeline-quarter/TimelineQuarter.styled';
import TimelineHeader from './timeline-header/TimelineHeader.styled';
import { isTimelineEabSent, isVerahProcessing } from './utils';
import { BaseComposerRowCommand } from '@tutum/design-system/composer/Composer.type';
import { useFeatureFlag } from '@tutum/mvz/hooks/useFeatureFlag';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';

const JumpStartIcon = '/images/chevrons-up-white.svg';
const JumpEndIcon = '/images/chevrons-down.svg';

export interface ITimelineProps {
  className?: string;
  theme?: IMvzTheme;
  patientId: string;
  contractInfo?: IContractInfo;
  viewTimelineFromOutside: boolean;
  isCodingRules?: boolean;
  sourceScreen: SourceScreen;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
  billingQuarter?: Array<{ year: number; quarter: number }>;
  openEditSchein: (scheinId?: string, scrollToSection?: FORM_SECTION) => void;
  ref?: MutableRefObject<{ reloadQuarters: ReloadQuarterFunc }>;
}

export const toasterRef = React.createRef<OverlayToaster>();

const TimeLine = forwardRef(
  (
    {
      className,
      patientId,
      isCodingRules,
      openCreateSchein,
      sourceScreen,
      billingQuarter,
      openEditSchein,
    }: ITimelineProps,
    ref: React.Ref<{ reloadQuarters: ReloadQuarterFunc }>
  ) => {
    const { t } = i18n.useTranslation<
      keyof typeof PatientManagementI18n.Timeline
    >({
      namespace: 'PatientManagement',
      nestedTrans: 'Timeline',
    });
    const { patientManagement } = useContext(PatientManagementContext.instance);
    const settingStore = useStore();
    const { schein: storeSchein } = usePatientFileStore();
    const commonSettingStore = useSettingStore();
    const { showPrintReview } = useContext(MedicationContext);

    const ffEhks = useFeatureFlag({
      ffKey: FeatureFlagKey.FeatureFlagKey_EHKS,
    });

    const quarterSectionRef = useRef<HTMLDivElement>(null);
    const quarterSectionInnerRef = useRef<HTMLDivElement>(null);
    const [filterTypes, setFilterTypes] = useState<ITimelineEntityType[]>([]);
    const [filterDateRange, setFilterDateRange] = useState<
      [Date, Date] | undefined
    >(undefined);
    const [isShowVerahPopup, setIsShowVerahPopup] = useState<boolean>(false);
    const [selectTerminalPsychotherapy, setSelectTerminalPsychotherapy] =
      useState<ServiceErrorCode | undefined>(undefined);
    const [serviceEntry, setServiceEntry] = useState<TimelineModel | undefined>(
      undefined
    );
    const timelineStore = useTimeLineStore();
    const { timelineState: quarters } = timelineStore;
    const { globalData, getDoctorById } = GlobalContext.useContext();

    const [isJumpStart, setJumpStart] = useState<boolean>(true);
    const [filterSearch, setFilterSearch] = useState<string>('');
    const [currentScrollHeight, setCurrentScrollHeight] = useState(0);
    const toaster = useToaster();
    const { schein } = usePatientFileStore();
    const toasterRef = React.createRef<OverlayToaster>();
    const { doctorId, encounterDate } = useActionBarStore();
    const doctorProfile = useMemo(
      () => getDoctorById(doctorId || ''),
      [doctorId]
    );
    const { t: tPsychotherapy } = I18n.useTranslation<
      keyof typeof PatientManagementI18n.ServiceEntry
    >({
      namespace: 'PatientManagement',
      nestedTrans: 'ServiceEntry',
    });

    const { t: tEncounter } = I18n.useTranslation<
      keyof typeof PatientManagementI18n.TimelineEncounter
    >({
      namespace: 'PatientManagement',
      nestedTrans: 'TimelineEncounter',
    });
    const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
      namespace: 'Common',
    });

    const [preParticipateServiceId, setPreParticipateServiceId] = useState<
      string | undefined
    >(undefined);
    const [isOpenConfirmPreParticipate, setIsOpenConfirmPreParticipate] =
      useState(false);

    const { data: dataPreParticipationServiceCodes } =
      useQueryGetPreParticipationServiceCodes(
        { patientId },
        {
          enabled: !!patientId,
        }
      );
    const preParticipationServiceCodes =
      dataPreParticipationServiceCodes?.timelineModels ?? [];
    const awh01Participate =
      patientManagement.getPatientParticipationResponse?.participations?.find(
        (p) =>
          p.contractId == 'AWH_01' &&
          p.status == PatientParticipationStatus.PatientParticipation_Requested
      );
    const isBVKJChargeSystem =
      awh01Participate?.chargeSystemId === 'AOK_BW_BVKJ';

    const { data: respDocumentTypes } = useQueryGetActiveTimelineDocumentType();

    useEffect(() => {
      timelineActions.setDocumentTypes(
        (respDocumentTypes?.documentTypes || []).filter((documentType) =>
          !ffEhks ? documentType.abbreviation !== 'EHKS' : true
        )
      );
    }, [respDocumentTypes?.documentTypes, ffEhks]);

    const {
      diagnoseSuggestions,
      reloadQuarters,
      totalQuarters,
      fetchSuggestionList,
      onCreateTimelineItem,
      onEditTimelineItem,
      onDeleteTimelineItem,
      fetchMoreTimeline,
      highlightKeyword,
    } = useTimeline(
      {
        patientId,
        fromDate: filterDateRange?.[0]?.getTime?.(),
        toDate: filterDateRange?.[1]?.getTime?.(),
        keyword: filterSearch,
        timelineEntityTypes: filterTypes,
        isSortByCategory: settingStore.isSortByCategory,
        isHistoryMode: timelineStore.isHistoryMode,
        scheinId: schein?.activatedSchein?.scheinId,
      },
      t,
      isCodingRules,
      sourceScreen,
      globalData.userProfile?.bsnr,
      billingQuarter
    );

    useEffect(() => {
      if (patientId) {
        composerActionChainActions.setPatientId(patientId);
      }
    }, [patientId]);

    const checkDate =
      encounterDate ??
      DatetimeUtil.startOf(DatetimeUtil.now(), 'day').unix() * 1000;
    const { data: _contracts } = useQueryGetPatientParticipation(
      { patientId, checkDate },
      {
        enabled: !!patientId && !!checkDate,
        select: (response) =>
          response.data.participations?.map((participation) => ({
            doctorId: participation.doctorId,
            id: participation.contractId,
            chargeSystemId: participation.chargeSystemId,
            role: participation.doctorFunctionType,
            status: participation.status,
            type: participation.contractType,
          })) ?? [],
      }
    );

    useEffect(() => {
      patientFileActions.setAvailableContracts(_contracts || []);
      composerActionChainActions.setContracts(_contracts || []);
    }, [_contracts]);

    const shouldShowPreParticipateServiceCode: boolean = ((): boolean => {
      const chargeSystemId = awh01Participate?.chargeSystemId;
      if (!chargeSystemId) return false;
      if (
        preParticipationServiceCodes.find(
          (t) =>
            t.encounterServiceTimeline?.participateId === awh01Participate.id
        )
      ) {
        return false;
      }

      const patientAge = patientManagement.patient?.dateOfBirth
        ? getAge(new Date(patientManagement.patient.dateOfBirth))
        : -1;
      if (chargeSystemId === 'AWH_01') {
        if (
          preParticipationServiceCodes.find(
            (t) =>
              t.encounterServiceTimeline?.preParticipateType ===
              PreParticipateType.UHU35 &&
              t.encounterServiceTimeline?.participateId === awh01Participate.id
          )
        ) {
          return false;
        }
        return patientAge >= 19 && patientAge <= 35;
      }
      if (chargeSystemId === 'AOK_BW_BVKJ') {
        if (
          preParticipationServiceCodes.find(
            (t) =>
              t.encounterServiceTimeline?.preParticipateType ===
              PreParticipateType.KJP4a &&
              t.encounterServiceTimeline?.participateId === awh01Participate.id
          )
        ) {
          return false;
        }
        return patientAge < 2;
      }
      return false;
    })();

    const isSearching =
      filterSearch.trim().length > 2 ||
      filterTypes?.length > 0 ||
      Boolean(filterDateRange?.[0]) ||
      Boolean(filterDateRange?.[1]);

    useEffect(() => {
      setCurrentScrollHeight(0);
      timelineActions.resetTimelineState();
    }, [
      timelineStore.isHistoryMode,
      filterDateRange,
      filterSearch,
      filterTypes,
    ]);

    useEffect(() => {
      if (!quarterSectionInnerRef.current) {
        return;
      }

      const element = quarterSectionInnerRef.current;
      if (timelineStore.isLoadingTimeline) {
        setCurrentScrollHeight(element.scrollHeight);
        return;
      }

      element.scrollTop = element.scrollHeight - currentScrollHeight;
    }, [timelineStore.isLoadingTimeline]);

    useEffect(() => {
      const handleScroll = () => {
        if (
          !quarterSectionInnerRef.current ||
          totalQuarters === 0 ||
          timelineStore.pagination.page === totalQuarters
        ) {
          return;
        }

        const element = quarterSectionInnerRef.current;
        if (element.scrollTop === 0 && !timelineStore.isLoadingTimeline) {
          fetchMoreTimeline();
        }
      };

      const element = quarterSectionInnerRef.current;
      if (element) {
        element.addEventListener('scroll', handleScroll);
      }

      if (
        totalQuarters === 0 ||
        timelineStore.pagination.page === totalQuarters ||
        !quarterSectionRef.current ||
        !quarterSectionInnerRef.current ||
        timelineStore.isLoadingTimeline
      ) {
        return;
      }

      const quarterSectionRefHeight = quarterSectionRef.current?.clientHeight;
      const quarterSectionInnerRefHeight =
        quarterSectionInnerRef.current?.clientHeight;
      if (quarterSectionRefHeight > quarterSectionInnerRefHeight) {
        fetchMoreTimeline();
      }

      return () => {
        if (element) {
          element.removeEventListener('scroll', handleScroll);
        }
      };
    }, [
      quarterSectionRef.current,
      quarterSectionInnerRef.current,
      totalQuarters,
      timelineStore.pagination.page,
      timelineStore.isLoadingTimeline,
      fetchMoreTimeline,
    ]);

    const renderLoadingPage = () => {
      return (
        <Flex className="sl-main-error-view" align="center">
          <LoadingState />
        </Flex>
      );
    };

    // const reloadQuarters = () => {
    //   reloadQuartersHook();
    //   refetch();
    // };
    const { isSuccess: isSuccessServiceCodes, data: serviceCodes } = useQuery({
      queryKey: ['FETCH_SERVICE_CODE_TO_TERMINATE_PSYCHOTHERAPY'],

      queryFn: async () => {
        if (!doctorProfile) {
          throw new Error('doctorProfile is mandatory');
        }
        const result = await ServiceBlockService.searchService(
          DatetimeUtil.now(),
          '88130-88131',
          doctorProfile,
          []
        );
        return result;
      },
      enabled: false,
    });

    const { billingKvHistoryId } = useKvBillingStore();
    const getErrors = async () => {
      if (!billingKvHistoryId) return;
      if (!patientId) return;
      const {
        data: { fileContentDetails, groupErrorByPatientDetailErrors },
      } = await getError({
        billingKvHistoryId: billingKvHistoryId,
        patientId: patientId,
      });
      patientFileActions.setListErrors(fileContentDetails || []);
      patientFileActions.setListHints(groupErrorByPatientDetailErrors || []);
    };

    const documentTerminalPsychotherapy = async (
      type: ServiceErrorCode,
      serviceEntry?: TimelineModel
    ) => {
      const treatmentDoctorId = doctorId;
      let query = '88130';
      if (type == ServiceErrorCode.ServiceErrorCode_Psychotherapy_88131) {
        query = '88131';
      }

      const servicesRes = await searchEbmsComposer({
        query,
        organizationId: await webWorkerServices.convertUkvToOkv(
          String(doctorProfile?.bsnr?.slice(0, 32))
        ),
        selectedDate: encounterDate || DatetimeUtil.now(),
      });
      const services = servicesRes?.data?.items || [];
      const service = services?.find((i) => i.code == query);
      const now = DatetimeUtil.dateToMoment();
      const schein = storeSchein?.list.find((s) =>
        (serviceEntry?.scheinIds || []).includes(s.scheinId)
      );
      if (!!service && !!schein && !!treatmentDoctorId && serviceEntry) {
        const scheins: ScheinWithMainGroup[] = [];
        const scheinIds: string[] = [];
        if (!schein.markedAsBilled) {
          scheins.push({
            scheinId: String(schein?.scheinId),
            group: schein?.scheinMainGroup,
          });
          scheinIds.push(schein.scheinId);
        }
        await document88130({
          timelineModel: {
            patientId: patientId,
            billingDoctorId: treatmentDoctorId,
            treatmentDoctorId: treatmentDoctorId,
            encounterCase: EncounterCase.AB,
            createdAt: now.toDate().getTime(),
            auditLogs: [],
            type: TimelineEntityType.TimelineEntityType_Service,
            encounterServiceTimeline: {
              command: 'L',
              isPreParticipate: false,
              freeText: `(${service.code}) ${service.description}`,
              code: String(service.code),
              description: String(service.description),
              referralDoctorInfo: {
                requiredBsnr: false,
                requiredLanr: false,
              },
              serviceMainGroup: MainGroup.KV,
              materialCosts: {
                required: false,
                materialCostsItemList: [],
              },
              scheins: scheins,
            },
            scheinIds: scheinIds,
            treatmentCase: TreatmentCase.TreatmentCaseCustodian,
            quarter: now.quarter(),
            year: now.year(),
            selectedDate: DatetimeUtil.getTimeUTC(
              now,
              DATE_TIME_TRANSFER_UTC
            ).valueOf(),
          },
          serviceEntryId: serviceEntry.id as string,
        });
        alertSuccessfully(
          tPsychotherapy('documentedTerminalPsychotherapy', {
            code: query,
          }),
          {
            timeout: TOASTER_TIMEOUT_CUSTOM,
            toaster: toasterRef?.current || undefined,
          }
        );
        setServiceEntry(undefined);
      }
    };

    const getQuartersTimeline = () => {
      if (!timelineStore.isHistoryMode) {
        return quarters
          .filter((quar) => quar.timelineModels.length > 0)
          .map((quarter) => {
            return {
              ...quarter,
              timelineModels: quarter.timelineModels.filter(
                (item) => item.auditLogs[0]?.actionType !== ActionType.Remove
              ),
            };
          });
      }

      return quarters;
    };

    const openConfirmPreParticipateServiceDialog = (
      preParticipateServiceId?: string
    ) => {
      setPreParticipateServiceId(preParticipateServiceId ?? '');
      setIsOpenConfirmPreParticipate(true);
    };

    const renderQuartersContent = () => {
      if (shouldShowPreParticipateServiceCode && isEmpty(quarters)) {
        const currentDate = DatetimeUtil.now();
        timelineActions.resetTimelineState({
          year: Number(DatetimeUtil.getYear(currentDate)),
          quarter: DatetimeUtil.getQuarter(currentDate),
          timelineModels: [],
        });
      }

      if (isEmpty(quarters)) {
        if (isSearching) {
          return (
            <Flex auto className={className} justify="center" align="center">
              <Flex column align="center" className="empty-entry">
                <BodyTextL>{t('noResult')}</BodyTextL>
              </Flex>
            </Flex>
          );
        }
        return (
          <Flex auto className={className} justify="center" align="center">
            <Flex column align="center" className="empty-entry">
              <H2 fontWeight="Bold">{t('timelineContent_emptyMessage')}</H2>
              <BodyTextL>{t('timelineContent_emptySuggestion')}</BodyTextL>
            </Flex>
          </Flex>
        );
      }

      const quartersData = getQuartersTimeline();
      return (
        <Flex className="quarter-section" ref={quarterSectionRef}>
          <div ref={quarterSectionInnerRef} className="quarter-section__inner">
            {quartersData.map((quarter) => (
              <TimelineQuarter
                keyword={highlightKeyword}
                key={quarter.year + '_' + quarter.quarter}
                quarter={quarter}
                diagnoseSuggestions={diagnoseSuggestions}
                sourceScreen={sourceScreen}
                onCreateTimelineItem={onCreateTimelineItem}
                onEditTimelineItem={onEditTimelineItem}
                onDeleteTimelineItem={onDeleteTimelineItem}
                reloadQuarters={reloadQuarters}
                fetchSuggestionList={fetchSuggestionList}
                showPreParticipateServiceCode={
                  shouldShowPreParticipateServiceCode
                }
                openCreateSchein={openCreateSchein}
                openEditSchein={openEditSchein}
              />
            ))}
            {shouldShowPreParticipateServiceCode && (
              <div className="timeline-content">
                <MessageBar
                  type="warning"
                  content={tEncounter(
                    isBVKJChargeSystem
                      ? 'KJP4a_warningMessage'
                      : 'UHU35_warningMessage'
                  )}
                  actionButtonGroup={
                    <Flex>
                      <Button
                        fill
                        intent={Intent.PRIMARY}
                        onClick={() => openConfirmPreParticipateServiceDialog()}
                      >
                        {tEncounter(
                          isBVKJChargeSystem
                            ? 'KJP4a_sendHpmRequest_buttonLabel'
                            : 'UHU35_sendHpmRequest_buttonLabel'
                        )}
                      </Button>
                    </Flex>
                  }
                />
              </div>
            )}
          </div>

          {isSuccessServiceCodes && (
            <InfoConfirmDialog
              type="primary"
              isOpen={!!selectTerminalPsychotherapy && !!serviceEntry}
              title={tPsychotherapy('documentService')}
              isShowIconTitle={false}
              onClose={() => {
                setSelectTerminalPsychotherapy(undefined);
                setServiceEntry(undefined);
              }}
              onConfirm={async () => {
                if (selectTerminalPsychotherapy) {
                  await documentTerminalPsychotherapy(
                    selectTerminalPsychotherapy as ServiceErrorCode,
                    serviceEntry
                  );
                }
                setSelectTerminalPsychotherapy(undefined);
                setServiceEntry(undefined);
              }}
            >
              <div>
                <RadioGroup
                  selectedValue={String(selectTerminalPsychotherapy)}
                  onChange={(e: React.FormEvent<HTMLInputElement>) => {
                    setSelectTerminalPsychotherapy(
                      e.currentTarget.value as ServiceErrorCode
                    );
                  }}
                >
                  {serviceCodes?.map((s, index) => (
                    <Radio
                      key={index}
                      label={`${s.code} - ${s.description}`}
                      value={
                        s.code == '88130'
                          ? ServiceErrorCode.ServiceErrorCode_Psychotherapy_88130
                          : ServiceErrorCode.ServiceErrorCode_Psychotherapy_88131
                      }
                    />
                  ))}
                </RadioGroup>
              </div>
            </InfoConfirmDialog>
          )}
        </Flex>
      );
    };

    const hasServiceAndDiagnoseEntries = useMemo(() => {
      return (
        storeSchein.isFocusMode &&
        quarters.some((quarter) => {
          return quarter.timelineModels.some(
            (entry) =>
              entry.type &&
              [
                TimelineEntityType.TimelineEntityType_Diagnose,
                TimelineEntityType.TimelineEntityType_Service,
                TimelineEntityType.TimelineEntityType_Service_GOA,
                TimelineEntityType.TimelineEntityType_Service_UV_GOA,
              ].includes(entry.type) &&
              entry.scheinIds?.[0] === storeSchein.activatedSchein?.scheinId
          );
        })
      );
    }, [storeSchein.isFocusMode, storeSchein.activatedSchein, quarters]);

    const handleScrollToEntries = useCallback(() => {
      const elements =
        quarterSectionInnerRef.current?.querySelectorAll('.focus-mode');
      const index = isJumpStart ? 0 : (elements?.length || 0) - 1;
      const element = elements?.[index];

      if (!element) {
        return;
      }

      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, [isJumpStart]);

    useImperativeHandle(
      ref,
      () => ({
        reloadQuarters,
      }),
      []
    );

    useEffect(() => {
      getErrors();
    }, [patientId]);

    useEffect(() => {
      return () => {
        timelineActions.setHistoryMode(false);
      };
    }, []);

    const callbackWithUseTimeLine = (timelineModel: TimelineModel) => {
      reloadQuarters({
        quarter: timelineModel.quarter,
        year: timelineModel.year,
      });
      fetchSuggestionList();
    };

    useListenTimelineCreate((data) => {
      if (patientId && data.patientId !== patientId) {
        return;
      }
      debounceSetModifiedId(data.timelineModel?.id);
      callbackWithUseTimeLine(data.timelineModel);
      getErrors();
    });

    useListenTimelineUpdate(async (data) => {
      if (patientId && data.patientId !== patientId) {
        return;
      }
      // support for eAB when email change status sent and exist schein actived
      if (
        isTimelineEabSent(data.timelineModel) &&
        storeSchein.activatedSchein
      ) {
        const resDocument = await handleAutoDocument(
          storeSchein.activatedSchein,
          patientId,
          globalData.userProfile?.bsnr as string
        );
        if (resDocument.error) {
          alertError(tCommon('autoDoucumentFailed'), { toaster });
        } else {
          if (resDocument.serviceCode) {
            alertSuccessfully(
              tCommon('autoDocumentSuccess', {
                code: resDocument.serviceCode,
              }),
              { toaster }
            );
          }
        }
      }

      if (timelineStore?.modifiedId) {
        debounceSetModifiedId(timelineStore.modifiedId);
      }

      callbackWithUseTimeLine(data.timelineModel);

      if (data.prescribeFormName) {
        timelineActions.setPrescribeFormName(data.prescribeFormName);
      }
      getErrors();
    });

    useListenTimelineRemove((data) => {
      if (patientId && data.patientId !== patientId) {
        return;
      }
      callbackWithUseTimeLine(data.timelineModel);
      getErrors();
    });

    useListenAutoAction((data) => {
      if (patientId && data.patientId !== patientId) {
        return;
      }
      alertSuccessfully(t(data.notificationCode as any), {
        toaster: toasterRef.current || undefined,
      });
    });

    const getTreatmentDoctor = useGetTreatmentDoctorWithBsnr();

    useEffect(() => {
      (async () => {
        // NOTE: skip when contract id is empty
        if (isEmpty(patientManagement?.selectedContractDoctor?.contractId)) {
          return;
        }

        const currentQuarter = quarters.find(
          (quarter) =>
            quarter.quarter === DatetimeUtil.getQuarter(DatetimeUtil.now())
        );
        const isExistIncludedDiagnosis = await isVerahProcessing(
          currentQuarter,
          serviceCode56565,
          patientManagement?.selectedContractDoctor?.contractId!
        );

        if (!isExistIncludedDiagnosis) return;

        const hasServiceCode56565 = currentQuarter?.timelineModels?.some(
          (item) =>
            item?.encounterServiceTimeline?.code === serviceCode56565 ||
            item?.encounterServiceChain?.services?.some(
              (service) => service.code === serviceCode56565
            )
        );

        if (hasServiceCode56565) {
          timelineActions.setIsShowVerahHint(true);
        } else if (commonSettingStore.displayHintVerah === 'true') {
          setIsShowVerahPopup(true);
        }
      })();
    }, [
      patientManagement?.selectedContractDoctor?.contractId,
      commonSettingStore.displayHintVerah,
      JSON.stringify(quarters),
    ]);
    const router = useRouter();

    usePatientPostAction(
      {
        action: PatientPostActionType.TIMELINE_VIEW_FORM,
        handler: async () => {
          try {
            const contractId = router.query.contractId as string;
            const patientId = router.query.patientId as string;
            const type = TimelineEntityType.TimelineEntityType_Form;
            const res = await findLatestTimelineEntry({
              patientId,
              contractId,
              type,
            });
            const entry = res.data.timelineModel;
            musterFormDialogActions.setCurrentEntryForm(entry.id || '');
            const currentDoctor = getTreatmentDoctor(
              entry.encounterForm?.prescribe?.treatmentDoctorId || '',
              entry.assignedToBsnrId || ''
            );
            const contract =
              patientFileActions.getAvailableContractById(contractId);
            if (entry.encounterForm && currentDoctor && contract) {
              musterFormDialogActions.viewForm(
                AccountManagementUtil.convertFormData(
                  entry.encounterForm,
                  currentDoctor
                ),
                entry.encounterForm?.encounterId || '',
                contract,
                entry.encounterForm.id,
                !!entry.encounterForm.prescribe.printedDate,
                false,
                false,
                entry.scheinIds?.[0]
              );
            }

            return true;
          } catch (err) {
            console.error('ERR ~ ', err);
            return false;
          }
        },
      },
      [router]
    );

    const resendPreParticipationService = () => {
      setIsOpenConfirmPreParticipate(false);
      const request = { serviceId: preParticipateServiceId as string };
      const chargeSystemId = awh01Participate
        ? awh01Participate.chargeSystemId
        : null;
      const isBVKJChargeSystem = chargeSystemId === 'AOK_BW_BVKJ';

      reSubmitPreParticipateService(request)
        .then(({ data }) => {
          const { status } = data;
          if (
            status === undefined ||
            status === 'PreParticipateServiceSubmissionStatusError'
          ) {
            alertError(
              tEncounter(
                isBVKJChargeSystem
                  ? 'KJP4a_sendHpmRequest_failedToasterMessage'
                  : 'UHU35_sendHpmRequest_failedToasterMessage'
              )
            );
          } else if (status === 'PreParticipateServiceSubmissionStatusOk') {
            alertSuccessfully(
              tEncounter(
                isBVKJChargeSystem
                  ? 'KJP4a_sendHpmRequest_successToasterMessage'
                  : 'UHU35_sendHpmRequest_successToasterMessage'
              )
            );
          }
        })
        .catch((error) => {
          console.error(error);
          alertError(
            tEncounter(
              isBVKJChargeSystem
                ? 'KJP4a_sendHpmRequest_failedToasterMessage'
                : 'UHU35_sendHpmRequest_failedToasterMessage'
            )
          );
        })
        .finally(() => {
          patientFileActions.schein.getScheinsOverview(
            patientManagement.patient?.id as string
          );
          // reloadQuarters();
        });
    };

    const sendPreParticipationService = () => {
      setIsOpenConfirmPreParticipate(false);

      const chargeSystemId = awh01Participate
        ? awh01Participate.chargeSystemId
        : null;
      const isBVKJChargeSystem = chargeSystemId === 'AOK_BW_BVKJ';
      const activatedSchein = storeSchein.activatedSchein;
      const commonPayload = {
        command: BaseComposerRowCommand.SERVICE,
        sources: Sources.Timeline,
        schein: {
          scheinId: activatedSchein?.scheinId,
          group: activatedSchein?.scheinMainGroup,
        },
        patientId: patientId,
        contractId: String(awh01Participate?.contractId),
        billingDoctorId: awh01Participate?.doctorId,
        encounterDate: DatetimeUtil.now(),
        participateId: awh01Participate?.id,
      } as PreParticipateServiceSubmissionRequest;

      const request = isBVKJChargeSystem
        ? {
          ...PreEnrollmentService.get('KJP4a'),
          ...commonPayload,
        }
        : {
          ...PreEnrollmentService.get('UHU35'),
          ...commonPayload,
        };

      submitPreParticipateService(request)
        .then(({ data }) => {
          const { status } = data;
          if (
            status === undefined ||
            status === 'PreParticipateServiceSubmissionStatusError'
          ) {
            alertError(
              tEncounter(
                isBVKJChargeSystem
                  ? 'KJP4a_sendHpmRequest_failedToasterMessage'
                  : 'UHU35_sendHpmRequest_failedToasterMessage'
              )
            );
          } else if (status === 'PreParticipateServiceSubmissionStatusOk') {
            alertSuccessfully(
              tEncounter(
                isBVKJChargeSystem
                  ? 'KJP4a_sendHpmRequest_successToasterMessage'
                  : 'UHU35_sendHpmRequest_successToasterMessage'
              )
            );
          }
        })
        .catch((error) => {
          console.error(error);
          alertError(
            tEncounter(
              isBVKJChargeSystem
                ? 'KJP4a_sendHpmRequest_failedToasterMessage'
                : 'UHU35_sendHpmRequest_failedToasterMessage'
            )
          );
        })
        .finally(() => {
          patientFileActions.schein.getScheinsOverview(
            patientManagement.patient?.id as string
          );
          // reloadQuarters();
        });
    };

    const closeConfirmPreParticipateServiceDialog = () => {
      setIsOpenConfirmPreParticipate(false);
    };

    return (
      <Flex auto column className={className}>
        <TimelineHeader
          patientId={patientId}
          isSortByCategory={settingStore.isSortByCategory}
          onFilterByTypes={setFilterTypes}
          onFilterByDateRange={(dateRange) => {
            setFilterDateRange([dateRange[0], dateRange[1]]);
          }}
          onFilterBySearch={(keyword) => {
            setFilterSearch(keyword);
          }}
        />
        {timelineStore.isLoadingTimeline && renderLoadingPage()}
        <Divider />
        <Flex column auto className="wrap">
          {(!timelineStore.isLoadingTimeline || quarters?.length > 0) && (
            <div className="timeline-content">{renderQuartersContent()}</div>
          )}
          {!timelineStore.isHistoryMode && (
            <ComposerContainer
              openCreateSchein={openCreateSchein}
              reloadQuarters={reloadQuarters}
            />
          )}
        </Flex>
        <GlobalStyleForPortalMenu />
        <ViewMailModal mailboxType={MailboxType.Kim} />
        <PreviewERP />
        {isShowVerahPopup && <VerahPopup />}
        {showPrintReview && <MedicationPrintPreview medicineType="kbv" />}
        {hasServiceAndDiagnoseEntries &&
          (isJumpStart ? (
            <Button
              data-test-id="btn-jump-start"
              className="btn-jump-start"
              intent={Intent.PRIMARY}
              small
              text={
                <Flex gap={8}>
                  <Svg src={JumpStartIcon} />
                  {t('jumpStart')}
                  <Svg src={JumpStartIcon} />
                </Flex>
              }
              onClick={() => {
                handleScrollToEntries();
                setJumpStart(false);
              }}
            />
          ) : (
            <Button
              data-test-id="btn-jump-end"
              className="btn-jump-end"
              intent={Intent.PRIMARY}
              small
              text={
                <Flex gap={8}>
                  <Svg src={JumpEndIcon} />
                  {t('jumpEnd')}
                  <Svg src={JumpEndIcon} />
                </Flex>
              }
              onClick={() => {
                handleScrollToEntries();
                setJumpStart(true);
              }}
            />
          ))}
        <OverlayToaster ref={toasterRef} position={Position.TOP} />

        <Alert
          cancelButtonText={tEncounter(
            'UHU35_sendHpmRequest_alertDialog_cancelButton'
          )}
          confirmButtonText={tEncounter(
            'UHU35_sendHpmRequest_alertDialog_confirmButton'
          )}
          intent={Intent.PRIMARY}
          isOpen={isOpenConfirmPreParticipate}
          onConfirm={
            preParticipateServiceId
              ? resendPreParticipationService
              : sendPreParticipationService
          }
          onCancel={closeConfirmPreParticipateServiceDialog}
        >
          <H2>
            {tEncounter(
              isBVKJChargeSystem
                ? 'KJP4a_sendHpmRequest_alertDialog_title'
                : 'UHU35_sendHpmRequest_alertDialog_title'
            )}
          </H2>
          <BodyTextM>
            {tEncounter(
              isBVKJChargeSystem
                ? 'KJP4a_sendHpmRequest_alertDialog_content'
                : 'UHU35_sendHpmRequest_alertDialog_content'
            )}
          </BodyTextM>
        </Alert>
      </Flex>
    );
  }
);

export default TimeLine;
