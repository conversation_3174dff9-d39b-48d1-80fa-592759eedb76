import { isEmpty, sortBy } from 'lodash';
import { proxy, useSnapshot } from 'valtio';

import {
  RemoveErezeptRequest,
  removeErezept,
} from '@tutum/hermes/bff/legacy/app_mvz_erezept';
import { getEntryHistory } from '@tutum/hermes/bff/legacy/app_mvz_audit_log';
import {
  GroupByQuarter,
  RestoreEntryHistoryRequest,
  restoreEntryHistory,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { HistoryDto } from '@tutum/hermes/bff/legacy/audit_log_common';
import { DiagnoseSuggestion } from '@tutum/hermes/bff/legacy/repo_encounter';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import { getUrlForm } from '../../patient-enrollment/patient-enrollment-form/print-review-form/PrintReviewForm.reducer';
import { PaginationRequest } from '@tutum/hermes/bff/legacy/common';
import { TimelineDocumentType } from '@tutum/hermes/bff/legacy/app_admin_timeline_document_type';
import { FormName } from '@tutum/hermes/bff/legacy/form_common';

interface ITimeLineStore {
  isShowVerahHint: boolean;
  isLoadingRemoveERezept: boolean;
  isHistoryMode: boolean;
  isOpenVersionHistoryDialog: boolean;
  viewingEntryHistory: TimelineModel | undefined;
  viewingEntryHistories: HistoryDto[] | undefined;
  isLoadingTimeline: boolean;
  isPendingFetchNewData: boolean;
  timelineState: GroupByQuarter[];
  timelineIdCreated: string | null;
  timelineModel: TimelineModel | null;
  diagnoseSuggestions: DiagnoseSuggestion[] | null;
  modifiedId?: string;
  isHasTimelineEntry: boolean;
  previewEnrollmentForm: {
    formName?: string;
    printUrl?: string;
  };
  pagination: PaginationRequest;
  matchedTokens: string[];
  documentTypes: TimelineDocumentType[];
  prescribeFromName: FormName | undefined;
}
interface IActions {
  setIsShowVerahHint: (payload: boolean) => void;
  removeERezept: (payload: RemoveErezeptRequest) => Promise<void>;
  setActiveHistoryMode: () => void;
  setVersionHistoryDialog: (isOpen: boolean) => void;
  setViewingEntryHistory: (payload: TimelineModel | undefined) => void;
  setLoadingTimeine: (isLoadingTimeline: boolean) => void;
  setPendingFetchNewData: (isPendingFetchNewData: boolean) => void;
  setTimelineState: (data: GroupByQuarter[]) => void;
  resetTimelineState: (data?: GroupByQuarter) => void;
  restoreEntryHistory: (value: RestoreEntryHistoryRequest) => void;
  setTimelineIdCreated: (value: string) => void;
  setTimelineModel: (payload: TimelineModel) => void;
  setDiagnoseSuggestions: (payload: DiagnoseSuggestion[] | null) => void;
  setHasTimelineEntry: (isHas: boolean) => void;
  setPreviewForm: (
    formName: string,
    enrollmentId: string,
    formType: string
  ) => void;
  setHistoryMode: (mode: boolean) => void;
  setPagination: (payload: PaginationRequest) => void;
  reloadTimeline: () => void;
  setMatchedTokens(_: string[]): void;
  setDocumentTypes: (documentTypes: TimelineDocumentType[]) => void;
  setPrescribeFormName: (prescribeFromName: FormName | undefined) => void;
}

const initPagination = {
  page: 1,
  pageSize: 2, // Todo: temporary for fixing performance issue. Next step: revert to 8 after complete the enhancement PRO-15680
  order: null!,
  sortBy: null!,
};

const initStore: ITimeLineStore = {
  isShowVerahHint: false,
  isLoadingRemoveERezept: false,
  isHistoryMode: false,
  isOpenVersionHistoryDialog: false,
  viewingEntryHistory: undefined,
  viewingEntryHistories: undefined,
  isLoadingTimeline: false,
  isPendingFetchNewData: false,
  timelineState: [],
  timelineIdCreated: null,
  timelineModel: null,
  diagnoseSuggestions: null,
  isHasTimelineEntry: true,
  previewEnrollmentForm: {},
  pagination: initPagination,
  matchedTokens: [],
  documentTypes: [],
  prescribeFromName: undefined,
};

export const store = proxy<ITimeLineStore>(initStore);

export const timelineActions: IActions = {
  setIsShowVerahHint: (value) => {
    store.isShowVerahHint = value;
  },
  removeERezept: async (payload) => {
    try {
      store.isLoadingRemoveERezept = true;
      await removeErezept(payload);
    } finally {
      store.isLoadingRemoveERezept = false;
    }
  },
  setActiveHistoryMode: () => {
    store.isHistoryMode = !store.isHistoryMode;
    store.pagination = initPagination;
    store.timelineState = [];
  },

  setHistoryMode: (mode: boolean) => {
    store.isHistoryMode = mode;
  },

  setVersionHistoryDialog: (isOpen) => {
    store.isOpenVersionHistoryDialog = isOpen;
    if (!isOpen) {
      store.viewingEntryHistory = undefined;
    }
  },

  setViewingEntryHistory: (value) => {
    store.viewingEntryHistory = value;
    // if true => also get histories of this entry to show on history dialog
    // if false => clean viewingEntryHistories
    if (value?.id != null) {
      getEntryHistory({
        timelineId: value?.id,
      }).then((response) => {
        store.viewingEntryHistories = response.data?.historyItems.sort(
          (a, b) => {
            const dateA = new Date(a.madeOn);
            const dateB = new Date(b.madeOn);
            return dateB.getTime() - dateA.getTime();
          }
        );
      });
    } else {
      store.viewingEntryHistories = undefined;
    }
  },

  setLoadingTimeine: (isLoadingTimeline: boolean) => {
    store.isLoadingTimeline = isLoadingTimeline;
  },

  setPendingFetchNewData: (isPendingFetchNewData: boolean) => {
    store.isPendingFetchNewData = isPendingFetchNewData;
  },
  resetTimelineState: (data) => {
    store.pagination = initPagination;
    if (data) {
      store.timelineState = [data];
      return;
    }
    store.timelineState = [];
  },
  setTimelineState: (data: GroupByQuarter[]) => {
    if (data.length === 0) {
      store.timelineState = [];
      return;
    }

    if (isEmpty(store.timelineState)) {
      store.timelineState = sortBy(data, ['year', 'quarter']);
      return;
    }

    for (const item of data) {
      const indexExistedGroup = store.timelineState.findIndex(
        (timeline) =>
          timeline.quarter === item.quarter && timeline.year === item.year
      );
      if (indexExistedGroup === -1) {
        store.timelineState.push(item);
        continue;
      }

      if (!item.timelineModels || item.timelineModels.length === 0) {
        store.timelineState.splice(indexExistedGroup, 1);
        continue;
      }

      store.timelineState[indexExistedGroup].timelineModels =
        item.timelineModels;
    }
    store.timelineState = sortBy(store.timelineState, ['year', 'quarter']);
  },
  restoreEntryHistory: (value: RestoreEntryHistoryRequest) => {
    restoreEntryHistory(value).then((response) => {
      console.error(response);
    });
  },

  setTimelineIdCreated: (payload) => {
    store.timelineIdCreated = payload;
    store.modifiedId = payload;
  },

  setTimelineModel: (payload) => {
    store.timelineModel = payload;
  },

  setDiagnoseSuggestions: (payload) => {
    store.diagnoseSuggestions = payload;
  },
  setHasTimelineEntry: (isHas: boolean) => {
    store.isHasTimelineEntry = isHas;
  },
  setPreviewForm: (
    formName: string,
    enrollmentId: string,
    formType: string
  ) => {
    store.previewEnrollmentForm.formName = formName;
    if (enrollmentId && formType) {
      store.previewEnrollmentForm.printUrl = decodeURIComponent(
        getUrlForm(enrollmentId, formType)
      );
    } else {
      store.previewEnrollmentForm.printUrl = undefined;
    }
  },
  setPagination: (payload) => {
    store.pagination = payload;
  },
  reloadTimeline: () => {
    store.pagination = {
      ...store.pagination,
      page: 1,
    };
  },
  setMatchedTokens: (matched: string[]) => {
    store.matchedTokens = matched;
  },
  setDocumentTypes: (documentTypes: TimelineDocumentType[]) => {
    store.documentTypes = documentTypes;
  },
  setPrescribeFormName: (prescribeFromName: FormName | undefined) => {
    store.prescribeFromName = prescribeFromName;
  },
};

export function useTimeLineStore() {
  return useSnapshot(store);
}
