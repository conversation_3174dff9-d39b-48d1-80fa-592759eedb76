
import React, { useState } from 'react';
import Theme, { IMvzTheme } from '@tutum/mvz/theme';
import I18n, {
  IFixedNamespaceTFunction,
  II18nFixedNamespace,
} from '@tutum/infrastructure/i18n';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import { FormName } from '@tutum/hermes/bff/form_common';
import { LabFormStatus } from '@tutum/hermes/bff/lab_common';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { labActions } from '@tutum/mvz/module_lab/lab-results/Lab.store';
import {
  Flex,
  BodyTextM,
  Svg,
  MessageBar,
} from '@tutum/design-system/components';
import {
  Menu,
  MenuItem,
  Popover,
  Button,
  Intent,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import DialogCancelConfirm from './DialogCancelConfirm';
import { COLOR } from '@tutum/design-system/themes/styles';
import ChangeItemDateMenuItem from '../ChangeItemDateMenuItem';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { cancelLabOrder } from '@tutum/hermes/bff/app_mvz_lab';
import { EncounterLab } from '@tutum/hermes/bff/repo_encounter';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import { formOverviewActions } from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

export interface ILabEntryProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  contract: IContractInfo;
  doctorIcon: JSX.Element;
  setItemToEditDate?: () => void;
}

const MoreIcon = '/images/more.svg';
const eyeOnIcon = '/images/eye-on.svg';
const cancelIcon = '/images/cancel.svg';

export const renderLabFormText = (
  t: IFixedNamespaceTFunction<keyof typeof LabI18n.Timeline>,
  form: EncounterLab
) => {
  return `${t('forms')} • ${form?.labForm?.labOrderNumber}`;
};

export const handleFormName = (formName: string) => {
  switch (formName) {
    case FormName.Muster_10:
      return 'M10';
    case FormName.Muster_10A:
      return 'M10A';
    case FormName.Muster_39A:
      return 'M39';
    default:
      return formName;
  }
};

const LabEntry = ({
  t,
  className,
  theme,
  entry,
  doctorIcon,
  setItemToEditDate,
}: ILabEntryProps & II18nFixedNamespace<keyof typeof LabI18n.Timeline>) => {
  const form = entry.encounterLab;
  const [isOpenCancelDialog, setOpenCancelDialog] = useState(false);
  const [isLoadingCancel, setIsLoadingCancel] = useState(false);
  const renderCancelOrderButton = () => {
    if (
      form.labForm.labFormStatus == LabFormStatus.HaveResult ||
      form.labForm.labFormStatus == LabFormStatus.Cancel ||
      form.labForm.labFormStatus == LabFormStatus.Save
    ) {
      return null;
    }
    return (
      <MenuItem
        key="cancel"
        text={t('cancelOrder')}
        onClick={() => {
          setOpenCancelDialog(true);
        }}
        icon={<Svg src={cancelIcon} />}
      />
    );
  };

  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });

  const handleViewForm = () => {
    musterFormDialogActions.viewFormLab(
      entry.id,
      entry.encounterLab.id,
      form.labForm,
      form?.labForm?.labFormStatus !== LabFormStatus.Save,
      false,
      {
        ...labActions,
        prescribe: () => formOverviewActions.prescribe(datetimeUtil.now()),
      },
      entry.scheinIds?.[0]
    );
  };

  const renderActionMenu = () => {
    return (
      <Menu className="action-menu">
        <ChangeItemDateMenuItem onClick={setItemToEditDate} />
        <MenuItem
          key="viewForm"
          text={t('viewFormLb')}
          onClick={handleViewForm}
          icon={
            <Svg className="timeline-entry_row_menu-icon" src={eyeOnIcon} />
          }
        />
        {renderCancelOrderButton()}
      </Menu>
    );
  };

  const renderLabel = () => {
    if (form.labForm.labFormStatus != LabFormStatus.Cancel) {
      return (
        <BodyTextM
          style={{ display: 'inline' }}
          color={COLOR.TEXT_SECONDARY_NAVAL}
          fontSize={13}
        >
          {renderLabFormText(t, form)}
        </BodyTextM>
      );
    }
    return (
      <BodyTextM
        style={{ display: 'inline' }}
        color={COLOR.TEXT_SECONDARY_NAVAL}
        fontSize={13}
      >
        {renderLabFormText(t, form)}{' '}
        <BodyTextM
          fontWeight="SemiBold"
          style={{ display: 'inline' }}
          color={COLOR.TAG_BACKGROUND_RED}
        >
          • {tCommon('Cancelled')}
        </BodyTextM>
      </BodyTextM>
    );
  };

  return (
    <Flex column className={className} onDoubleClick={handleViewForm}>
      <DialogCancelConfirm
        isOpen={isOpenCancelDialog}
        isLoadingCancelButton={isLoadingCancel}
        onClose={async (isCancel) => {
          if (isCancel) {
            setIsLoadingCancel(true);

            await cancelLabOrder({
              labOrderId: form.id,
              scheinId: form.labForm.scheinId,
            });

            setOpenCancelDialog(false);
            setIsLoadingCancel(false);
          } else {
            setOpenCancelDialog(false);
          }
        }}
      />
      <Flex justify="space-between">
        {renderLabel()}
        <Flex>
          <TimelineDeletionRemain entry={entry} />
          <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
          <TimelineActionHOC>
            <Flex className="actions-group" align="center">
              <Popover content={renderActionMenu()}>
                <Tooltip content={t('More')} position="top">
                  <Svg className="more-icon" src={MoreIcon} />
                </Tooltip>
              </Popover>
            </Flex>
          </TimelineActionHOC>
        </Flex>
      </Flex>
      <BodyTextM
        color={
          form.labForm.labFormStatus == LabFormStatus.Cancel
            ? COLOR.TAG_BACKGROUND_RED
            : ''
        }
      >
        {handleFormName(form?.labForm?.formName)} - {t('requestFormHint')}
      </BodyTextM>
      {form?.labForm?.labFormStatus === LabFormStatus.Save && (
        <MessageBar
          type="warning"
          content={t('saveHint')}
          actionButtonGroup={
            <Button
              fill
              intent={Intent.PRIMARY}
              onClick={() => {
                musterFormDialogActions.viewFormLab(
                  entry.id,
                  entry.encounterLab.id,
                  form.labForm,
                  false,
                  true,
                  {
                    ...labActions,
                    prescribe: () =>
                      formOverviewActions.prescribe(datetimeUtil.now()),
                  }
                );
              }}
            >
              {t('completeFormLb')}
            </Button>
          }
        />
      )}
    </Flex>
  );
};

export default I18n.withTranslation(LabEntry, {
  namespace: 'Lab',
  nestedTrans: 'Timeline',
});
