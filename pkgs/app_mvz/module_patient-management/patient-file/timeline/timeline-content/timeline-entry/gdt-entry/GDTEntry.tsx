import {
  BodyTextM,
  Box,
  Button,
  Flex,
  Svg,
  Tag,
} from '@tutum/design-system/components';
import {
  Menu,
  MenuItem,
  Popover,
  Tooltip,
} from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { TimelineActionHOC } from '@tutum/mvz/components/timeline-action/TimelineActions';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import { IMvzTheme } from '@tutum/mvz/theme';
import { memo, useCallback, useMemo, useState } from 'react';
import fileUtil from '@tutum/infrastructure/utils/file.util';
import {
  getPresignedGetURL,
  useQueryGetPresignedGetURL,
} from '@tutum/hermes/bff/legacy/app_mvz_file';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import Link from 'next/link';
import { PayLoadHistoryMode } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/util';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';

const editIcon = '/images/edit-3.svg';
const trashIcon = '/images/trash-bin-red.svg';
const moreIcon = '/images/more.svg';
const pdfIcon = '/images/pdf-icon.svg';
const textIcon = '/images/file-text.svg';
const downloadIcon = '/images/download.svg';
const chevronDownIcon = '/images/chevron-down-active.svg';
const chevronUpIcon = '/images/chevron-up-active.svg';

export interface GDTEntryProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  doctorIcon: JSX.Element;
  onRemoveEntry: (_: boolean, fn?: () => void) => void;
  isHistoryMode: boolean;
  payLoadHistoryMode: PayLoadHistoryMode;
  setEditInline?: () => void;
}

const downloadFile = async (
  bucketName: string,
  fileName: string,
  filePath: string
) => {
  const { data } = await getPresignedGetURL({
    bucketName,
    objectName: filePath,
    header: {
      'response-content-disposition': `attachment; filename="${fileName}"`,
    },
  });
  fileUtil.downloadFile(data.presignedURL, fileName);
};

const GDTEntry = memo(
  ({
    className,
    entry,
    onRemoveEntry,
    doctorIcon,
    setEditInline,
  }: GDTEntryProps) => {
    const { t } = I18n.useTranslation<
      keyof typeof PatientManagementI18n.Timeline
    >({
      namespace: 'PatientManagement',
      nestedTrans: 'Timeline',
    });
    const { t: tLab } = I18n.useTranslation<keyof typeof LabI18n.Timeline>({
      namespace: 'Lab',
      nestedTrans: 'Timeline',
    });
    const { t: tCommon } = I18n.useTranslation<Paths<typeof CommonI18n>>({
      namespace: 'Common',
    });
    const [isExpanded, setIsExpanded] = useState(false);

    // Handler to toggle expansion
    const toggleExpand = () => setIsExpanded((prev) => !prev);

    const { allowRemoveTimeline } = useSettingStore();
    const actionMenuItems = useMemo(() => {
      const items: React.ReactNode[] = [];
      items.push(
        <MenuItem
          key="edit"
          text={tCommon('Edit')}
          onClick={setEditInline}
          icon={<Svg className="timeline-entry_row_menu-icon" src={editIcon} />}
        />
      );
      if (allowRemoveTimeline) {
        items.push(
          <MenuItem
            key="remove"
            text={tCommon('ButtonActions.removeText')}
            onClick={() => onRemoveEntry(false)}
            icon={
              <Svg className="timeline-entry_row_menu-icon" src={trashIcon} />
            }
          />
        );
      }
      return items;
    }, [onRemoveEntry, t, allowRemoveTimeline]);

    const renderAction = useCallback(() => {
      if (actionMenuItems.length === 0) return null;
      const menu = <Menu className="action-menu">{actionMenuItems}</Menu>;

      return (
        <TimelineActionHOC>
          <Flex className="actions-group">
            <Popover content={menu}>
              <Tooltip content={tCommon('More')} position="top">
                <Svg className="more-icon" src={moreIcon} />
              </Tooltip>
            </Popover>
          </Flex>
        </TimelineActionHOC>
      );
    }, [actionMenuItems, t]);

    const fileName = entry.encounterGDT?.fileName ?? '';
    const bucketName = entry.encounterGDT?.bucketName ?? '';
    const extension = fileName?.split('.').pop();
    const filePath = entry.encounterGDT?.filePath ?? '';
    const fileIcon = extension === 'pdf' ? pdfIcon : textIcon;
    const { data } = useQueryGetPresignedGetURL(
      {
        bucketName,
        objectName: filePath,
        header: {
          'response-content-disposition': `attachment; filename="${fileName}"`,
        },
      },
      {
        enabled: Boolean(bucketName && filePath),
      }
    );

    const renderFiles = useCallback(
      () =>
        entry.encounterGDT?.archiveFiles?.map((file, index) => {
          const fileName = file.fileName;
          const bucketName = entry.encounterGDT?.bucketName;
          const extension = fileName.split('.').pop();
          const filePath = file.objectName;
          const fileIcon = extension === 'pdf' ? pdfIcon : textIcon;

          const download = (
            e: React.MouseEvent<HTMLAnchorElement, MouseEvent>
          ) => {
            e.preventDefault();
            e.stopPropagation();
            if (bucketName) {
              downloadFile(bucketName, fileName, filePath);
            }
          };

          return (
            <Link key={index} href={''} onClick={(e) => download(e)}>
              <Flex align="center" gap={4} px={4}>
                <Svg src={fileIcon} size={16} />
                {fileName}
                <Svg src={downloadIcon} size={16} />
              </Flex>
            </Link>
          );
        }),
      [entry]
    );

    if (!entry.encounterGDT) return null;

    return (
      <Flex className={className}>
        <Box className="flex-1" onClick={setEditInline}>
          <Flex justify="space-between" gap={8}>
            <Flex column className="gdt-content">
              <Flex gap={8} align="center">
                <BodyTextM>{entry.encounterGDT.command}</BodyTextM>
                <Tag slStyle="fill" slState="info">
                  {t('external')}
                </Tag>
                •
                <BodyTextM>{entry.encounterGDT.gdtImportSettingName}</BodyTextM>
                {!!entry.encounterGDT.labOrderId && (
                  <BodyTextM>
                    • {tLab('order')}: {entry.encounterGDT.labOrderId}
                  </BodyTextM>
                )}
              </Flex>

              <Flex column>
                <BodyTextM
                  className={getCssClass(
                    {
                      expanded: isExpanded,
                      collapsed: !isExpanded,
                    },
                    'expandable-text',
                    'entry-content'
                  )}
                >
                  {entry.encounterGDT.note}
                </BodyTextM>
                {entry.encounterGDT.note?.split('\n').length > 4 && (
                  <Button
                    minimal
                    intent="primary"
                    rightIcon={
                      <Svg src={isExpanded ? chevronUpIcon : chevronDownIcon} />
                    }
                    onClick={(e: React.MouseEvent<HTMLElement>) => {
                      e.stopPropagation();
                      toggleExpand();
                    }}
                    style={{ width: 'fit-content', padding: 0 }}
                  >
                    {isExpanded ? t('hide') : t('viewMore')}
                  </Button>
                )}
              </Flex>
            </Flex>
          </Flex>

          <Flex flexWrap>
            <Flex gap={4} align="center">
              <Svg src={fileIcon} />
              <Link
                href={data?.presignedURL ?? ''}
                onClick={(e) => e.stopPropagation()}
              >
                {fileName}
              </Link>
              <Button
                iconOnly
                small
                minimal
                onClick={(e: React.MouseEvent<HTMLElement>) => {
                  e.stopPropagation();
                  fileUtil.downloadFile(data?.presignedURL ?? '', fileName);
                }}
                style={{ marginBottom: -4 }}
              >
                <Svg src={downloadIcon} size={16} />
              </Button>
            </Flex>
            {renderFiles()}
          </Flex>
        </Box>

        <Flex>
          <TimelineDeletionRemain entry={entry} />
          <Flex>{doctorIcon}</Flex>
          {renderAction()}
        </Flex>
      </Flex>
    );
  }
);

export default GDTEntry;
