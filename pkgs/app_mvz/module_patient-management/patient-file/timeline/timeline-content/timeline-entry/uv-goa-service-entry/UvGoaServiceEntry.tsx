import { debounce, groupBy } from 'lodash';
import React, { memo, useCallback, useContext, useMemo, useState } from 'react';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';

import { BodyTextM, Button, Flex, Svg } from '@tutum/design-system/components';
import {
  Intent,
  Menu,
  MenuItem,
  Popover,
} from '@tutum/design-system/components/Core';
import {
  getCssClass,
  getUUID,
} from '@tutum/design-system/infrastructure/utils';
import {
  EditResponse,
  GroupByQuarter,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { takeOverDiagnosisWithScheinId } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { ActionType, TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { handleReduceErrors } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import ValidationMessage from '../service-entry/ValidationMessage';
import AdditionalInfoEntry from './AdditionalInfoEntry';
import HighlightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { MainGroup } from '@tutum/hermes/bff/common';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { TakeoverDiagnosisType } from '@tutum/hermes/bff/legacy/timeline_common';
import {
  AdditionalInfoParent,
  EncounterCase,
} from '@tutum/hermes/bff/repo_encounter';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  backgroundMainGroup,
  colorMainGroup,
  formatScheinName,
} from '@tutum/mvz/_utils/scheinFormat';
import SelectDiagnosisDialog from '@tutum/mvz/components/select-diagnosis-dialog/SelectDiagnosisDialog.styled';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import reloadSchein from '@tutum/mvz/module_patient-management/utils/reloadSchein';
import {
  PayLoadHistoryMode,
  renderUvGoaServiceText,
  customAdditionalInfos,
} from '../util';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

const trashIcon = '/images/trash-bin-red.svg';
const MoreIcon = '/images/more.svg';
const editIcon = '/images/edit-3.svg';

export interface IServiceEntryProps {
  className?: string;
  index?: number;
  theme?: IMvzTheme;
  entry?: TimelineModel;
  lastItem?: boolean;
  treatmentDoctorId?: string;
  patientId?: string;
  quarter?: GroupByQuarter;
  onEdit?: (isEditing: boolean) => void;
  isEditing?: boolean;
  doctorProfile?: IEmployeeProfile;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  patient: IPatientProfile;
  currentSchein?: ScheinItem;
  onRemoveEntry: (hardDelete?: boolean, cb?: Function) => void;
  setItemToEditDate?: () => void;
  setEditInline?: () => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  payLoadHistoryMode: PayLoadHistoryMode;
  isHistoryMode: boolean;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
}

const ServiceEntry = (props: IServiceEntryProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.ServiceEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'ServiceEntry',
  });

  const { t: tTimelineEncounter } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimelineEncounter
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimelineEncounter',
  });

  const {
    className,
    doctorIcon,
    patientId,
    currentSchein,
    openCreateSchein,
    setEditInline,
    hasBilled,
    entry,
  } = props;
  const encounterServiceTimeline = hasBilled
    ? {
      ...entry?.encounterUvGoaService!,
      errors: [],
    }
    : entry?.encounterUvGoaService!;

  const type = props?.entry?.type;

  const { getDoctorById } = GlobalContext.useContext();
  const { isHistoryMode, matchedTokens } = useTimeLineStore();
  const store = usePatientFileStore();

  const [cachedScheinId, setCachedScheinId] =
    useState<Nullable<string>>(undefined);
  const { patientManagement, setGetPatientParticipationResponse } = useContext(
    PatientManagementContext.instance
  );

  const { allowRemoveTimeline } = useSettingStore();

  const [isLoading] = useState(false);

  const renderActionMenu = () => {
    const actions: React.JSX.Element[] = [];
    if (!hasBilled) {
      const actionEdit = (
        <MenuItem
          key="edit_1"
          icon={<Svg src={editIcon} size={20} />}
          text={tTimelineEncounter('editEntry')}
          onClick={() => props.setEditInline?.()}
        />
      );
      actions.push(actionEdit);
    }
    if (!hasBilled && allowRemoveTimeline) {
      const actionRemove = (
        <MenuItem
          key="0"
          icon={<Svg src={trashIcon} />}
          text={t('removeEntry')}
          onClick={() => props.onRemoveEntry(false)}
        />
      );
      actions.push(actionRemove);
    }
    return actions;
  };

  const renderServiceContent = () => {
    const { entry } = props;
    if (!encounterServiceTimeline) {
      return;
    }

    return (
      <Flex w="100%" justify="flex-start">
        <BodyTextM
          className="with-icon"
          style={{
            whiteSpace: 'break-spaces',
            textDecoration:
              entry?.auditLogs[0]?.actionType === ActionType.Remove
                ? 'line-through'
                : 'none',
            color:
              entry?.auditLogs[0]?.actionType === ActionType.Remove
                ? 'red'
                : undefined,
          }}
        >
          {renderUvGoaServiceText(encounterServiceTimeline)}
        </BodyTextM>
      </Flex>
    );
  };

  const displayInfo = (
    info: Partial<AdditionalInfoParent>
  ): string | React.ReactNode => {
    const transformHHMM = () => {
      for (const error of encounterServiceTimeline?.errors || []) {
        if (error?.metaData?.field === '5006') {
          return info.value;
        }
      }
      const HH = info?.value?.slice(0, 2);
      const MM = info?.value?.slice(2);
      return `${HH}:${MM}`;
    };
    const mapByFK: Record<string, () => string | React.ReactNode> = {
      '5006': transformHHMM,
      '5900': transformHHMM,
    };
    const display = mapByFK[String(info.fK)];

    return display ? display() : info.value;
  };

  const renderAllAdditionalInfo = () => {
    const { entry } = props;
    const treatmentDoctorId = entry?.treatmentDoctorId;
    if (
      !encounterServiceTimeline?.additionalInfos?.length ||
      !treatmentDoctorId
    )
      return;

    const userProfile = getDoctorById(treatmentDoctorId);
    if (!userProfile) return;

    const additionalInfos = customAdditionalInfos(
      encounterServiceTimeline?.additionalInfos,
      userProfile
    );

    return (
      <span
        key={getUUID()}
        className="add-info__container"
        style={{
          textDecoration:
            entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'line-through'
              : 'none',
          color:
            entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'red'
              : undefined,
        }}
      >
        {additionalInfos.map((info, idx) => (
          <AdditionalInfoEntry
            key={getUUID()}
            info={info}
            isChild={false}
            isLast={additionalInfos?.length - 1 === idx}
            displayInfo={displayInfo}
          />
        ))}
      </span>
    );
  };

  const reloadScheinMod = async () => {
    const { scheins: newScheins, patientParticipation } =
      await reloadSchein(patientManagement);
    const activeScheins = newScheins.filter((schein) => !schein.markedAsBilled);
    patientFileActions.schein.setActivatedSchein(
      activeScheins[0],
      patientParticipation.participations
    );
    setGetPatientParticipationResponse(patientParticipation);
  };

  const listErrorNotShow = store?.skippedErrorKeyList || [];

  const renderMessageBar = useMemo(() => {
    const errors = encounterServiceTimeline?.errors ?? [];
    if (!errors?.length || entry?.isImported) {
      return null;
    }

    const groupErrors = handleReduceErrors(errors);
    const grouped = groupBy(groupErrors ?? [], 'type');
    return (
      <Flex column>
        {Object.keys(grouped ?? {}).map((key) => {
          return grouped[key].map((error, index: number) => {
            let actionButtonGroup: React.ReactNode = null;
            if (
              error.errorCode == ErrorCode.ErrorCode_Validation_Missing_ScheinId
            ) {
              actionButtonGroup = (
                <Button
                  intent={Intent.PRIMARY}
                  loading={isLoading}
                  disabled={isLoading}
                  onClick={async () => {
                    if (
                      ['88130', '88131'].includes(
                        props?.entry?.encounterServiceTimeline?.code!
                      )
                    ) {
                      patientFileActions.setTerminalId(entry?.id);
                    }

                    openCreateSchein();
                  }}
                >
                  {t('btnCreateNewSchein')}
                </Button>
              );
            }

            return (
              <ValidationMessage
                index={index}
                total={grouped[key]?.length}
                key={getUUID()}
                error={error}
                actionButtonGroup={actionButtonGroup}
              />
            );
          });
        })}
      </Flex>
    );
  }, [encounterServiceTimeline?.errors, isLoading, listErrorNotShow]);

  const handleTakeOver = async (
    data: TimelineModel[],
    mappingTreatmentRelevent: { [key: string]: boolean }
  ) => {
    await takeOverDiagnosisWithScheinId({
      scheinId: cachedScheinId!,
      timelineModelIds: data
        .filter((d) => d.id && Boolean(d['isAddTimeline']) === false)
        .map((d) => d.id!),
      newDiagnosis: data.filter((d) => !d.id),
      mappingTreatmentRelevent: mappingTreatmentRelevent,
    });
    setCachedScheinId(undefined);
  };

  const debounceOnCancel = debounce(async () => {
    setCachedScheinId(undefined);
    reloadScheinMod();
  }, 500);

  return (
    <React.Fragment>
      <Flex auto className={getCssClass(className)}>
        <Flex>
          <Flex auto>
            <Flex
              column
              className="service-info"
              onClick={() => {
                if (entry?.encounterCase === EncounterCase.PRE_ENROLLMENT) {
                  return;
                }
                setEditInline?.();
              }}
            >
              <Flex className="entry-type" gap={0} align="center">
                <HighlightWrapper matchedTokens={matchedTokens}>
                  <Flex className={`entry-text ${type?.toLowerCase()}`}>
                    {renderServiceContent()}
                  </Flex>
                </HighlightWrapper>
              </Flex>
            </Flex>
          </Flex>
          <Flex align="center">
            <TimelineDeletionRemain
              className="timeline-deletion-remain"
              entry={entry}
            />
            {!!currentSchein && (
              <div
                className="main-group"
                style={{
                  textDecoration:
                    entry?.auditLogs[0]?.actionType === ActionType.Remove
                      ? 'line-through'
                      : '',
                  color: colorMainGroup(currentSchein.scheinMainGroup),
                  backgroundColor: backgroundMainGroup(
                    currentSchein.scheinMainGroup
                  ),
                }}
              >
                {formatScheinName(currentSchein, t('pseudo'))}
              </div>
            )}
            <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
            {renderActionMenu().length > 0 && (
              <TimelineActionHOC
                entry={props?.entry}
                hasBilled={hasBilled}
                displayInfo={displayInfo}
              >
                <Flex className="actions-group">
                  <Popover
                    content={
                      <Menu className="action-menu">{renderActionMenu()}</Menu>
                    }
                  >
                    <Svg className="more-icon" src={MoreIcon} />
                  </Popover>
                </Flex>
              </TimelineActionHOC>
            )}
          </Flex>
        </Flex>
        {renderAllAdditionalInfo()}
        {!isHistoryMode ? <>{renderMessageBar}</> : null}
      </Flex>
      {cachedScheinId && props?.entry?.id && (
        <SelectDiagnosisDialog
          takeoverDiagnosisType={
            TakeoverDiagnosisType.TakeoverDiagnosisPsychotherapy
          }
          requiredAtLeast={1}
          patientId={patientId}
          onTakeover={(existed, newvalues = [], mappingTreatmentRelevent) => {
            handleTakeOver(
              [...existed, ...newvalues],
              mappingTreatmentRelevent!
            );
          }}
          scheinId={cachedScheinId}
          onCancel={debounceOnCancel}
          serviceId={entry?.id!}
        />
      )}
    </React.Fragment>
  );
};

export default memo(ServiceEntry);
