
import groupBy from 'lodash/groupBy';
import React, { memo, useMemo, useState } from 'react';

import { BodyTextM, Button, Flex, Svg } from '@tutum/design-system/components';
import {
  Intent,
  Menu,
  MenuItem,
  Popover,
} from '@tutum/design-system/components/Core';
import {
  getCssClass,
  getUUID,
  isEmpty,
} from '@tutum/design-system/infrastructure/utils';
import {
  EditResponse,
  GroupByQuarter,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { takeOverDiagnosisWithScheinId } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { ActionType, TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { handleReduceErrors } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import AdditionalInfoEntry from './AdditionalInfoEntry';
import ValidationMessage from '../service-entry/ValidationMessage';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { AdditionalInfoParent } from '@tutum/hermes/bff/repo_encounter';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { TakeoverDiagnosisType } from '@tutum/hermes/bff/legacy/timeline_common';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  backgroundMainGroup,
  colorMainGroup,
  formatScheinName,
} from '@tutum/mvz/_utils/scheinFormat';
import SelectDiagnosisDialog from '@tutum/mvz/components/select-diagnosis-dialog/SelectDiagnosisDialog.styled';
import { MainGroup } from '@tutum/hermes/bff/common';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { PayLoadHistoryMode } from '../util';
import HighlightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import { customAdditionalInfos, renderGoaServiceText } from '../util';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

const trashIcon = '/images/trash-bin-red.svg';
const editIcon = '/images/edit-3.svg';
const MoreIcon = '/images/more.svg';

export interface IServiceEntryProps {
  className?: string;
  index?: number;
  entry?: TimelineModel;
  treatmentDoctorId?: string;
  patientId?: string;
  quarter?: GroupByQuarter;
  onEdit?: (isEditing: boolean) => void;
  isEditing?: boolean;
  doctorProfile?: IEmployeeProfile;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  patient: IPatientProfile;
  currentSchein?: ScheinItem;
  keyword?: string;
  onRemoveEntry: (hardDelete?: boolean) => void;
  setItemToEditDate?: () => void;
  setEditInline?: () => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  payLoadHistoryMode: PayLoadHistoryMode;
  isHistoryMode: boolean;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
}

const ServiceEntry = (props: IServiceEntryProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.ServiceEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'ServiceEntry',
  });

  const { t: tTimelineEncounter } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimelineEncounter
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimelineEncounter',
  });

  const {
    className,
    doctorIcon,
    patientId,
    currentSchein,
    openCreateSchein,
    setEditInline,
    hasBilled,
    entry,
  } = props;
  //NOTE: hide errors for billed service code
  const encounterGoaService = hasBilled
    ? {
        ...entry.encounterGoaService,
        errors: [],
      }
    : entry.encounterGoaService;

  const errors = props?.entry?.errors;
  const type = props?.entry?.type;

  const { getDoctorById } = GlobalContext.useContext();
  const { isHistoryMode, matchedTokens } = useTimeLineStore();

  const [cachedScheinId, setCachedScheinId] =
    useState<Nullable<string>>(undefined);
  const { allowRemoveTimeline } = useSettingStore();
  const allowDirectEditor =
    errors &&
    errors.find(
      (error) =>
        error.type === 'error' &&
        isEmpty(error.akaFunction, true) &&
        error.errorCode === 'SERVICE_VALIDATION_ERROR'
    );

  const style = !allowDirectEditor ? '' : 'warning';

  const [isLoading] = useState(false);

  const renderActionMenu = () => {
    const actions: React.JSX.Element[] = [];

    if (!hasBilled) {
      const actionEdit = (
        <MenuItem
          key="edit_1"
          icon={<Svg src={editIcon} size={20} />}
          text={tTimelineEncounter('editEntry')}
          onClick={() => props.setEditInline()}
        />
      );
      actions.push(actionEdit);
    }

    if (!hasBilled && allowRemoveTimeline) {
      const actionRemove = (
        <MenuItem
          key="0"
          icon={<Svg src={trashIcon} />}
          text={t('removeEntry')}
          onClick={() => props.onRemoveEntry(false)}
        />
      );

      actions.push(actionRemove);
    }

    return actions;
  };

  const renderServiceContent = () => {
    const { entry } = props;
    if (!encounterGoaService) {
      return;
    }

    return (
      <Flex w="100%" justify="flex-start">
        <BodyTextM
          className="with-icon hover-text"
          style={{
            whiteSpace: 'break-spaces',
            textDecoration:
              entry?.auditLogs[0]?.actionType === ActionType.Remove
                ? 'line-through'
                : 'none',
            color:
              entry?.auditLogs[0]?.actionType === ActionType.Remove
                ? 'red'
                : undefined,
          }}
        >
          {renderGoaServiceText(entry.encounterGoaService)}
        </BodyTextM>
      </Flex>
    );
  };

  const displayInfo = (
    info: Partial<AdditionalInfoParent>
  ): string | React.ReactNode => {
    const transformHHMM = () => {
      for (const error of encounterGoaService?.errors || []) {
        if (error?.metaData?.field === '5006') {
          return info.value;
        }
      }
      const HH = info?.value?.slice(0, 2);
      const MM = info?.value?.slice(2);
      return `${HH}:${MM}`;
    };
    const mapByFK: Record<string, () => string | React.ReactNode> = {
      '5006': transformHHMM,
      '5900': transformHHMM,
    };
    const display = mapByFK[String(info.fK)];

    return display ? display() : info.value;
  };

  const renderAllAdditionalInfo = () => {
    const { entry } = props;
    const treatmentDoctorId = entry?.treatmentDoctorId;

    if (!treatmentDoctorId) return;

    const userProfile = getDoctorById(treatmentDoctorId);
    if (!userProfile) return;

    const additionalInfos = customAdditionalInfos(
      encounterGoaService?.additionalInfos,
      userProfile
    );

    const specialInfos: AdditionalInfoParent[] = [
      {
        fK: 'factor',
        value: encounterGoaService.factor.toString().replace('.', ','),
        children: null,
      },
      {
        fK: 'quantity',
        value: encounterGoaService.quantity.toString(),
        children: null,
      },
    ];

    let listRenderAdditionalInfo = encounterGoaService.isChangeDefault
      ? specialInfos
      : [];

    if (additionalInfos && additionalInfos.length > 0) {
      listRenderAdditionalInfo =
        listRenderAdditionalInfo.concat(additionalInfos);
    }

    return (
      <span
        key={getUUID()}
        className="add-info__container"
        style={{
          textDecoration:
            entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'line-through'
              : 'none',
          color:
            entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'red'
              : undefined,
        }}
      >
        {listRenderAdditionalInfo.map((info, idx) => (
          <AdditionalInfoEntry
            key={getUUID()}
            info={info}
            isChild={false}
            isLast={listRenderAdditionalInfo?.length - 1 === idx}
            displayInfo={displayInfo}
          />
        ))}
      </span>
    );
  };

  const renderMessageBar = useMemo(() => {
    const errors = encounterGoaService?.errors ?? [];
    if (!errors?.length) {
      return null;
    }

    const groupErrors = handleReduceErrors(errors);
    const grouped = groupBy(groupErrors ?? [], 'type');
    return (
      <Flex column>
        {Object.keys(grouped ?? {}).map((key) => {
          return grouped[key].map((error, index: number) => {
            let actionButtonGroup: React.ReactNode = null;

            if (
              error.errorCode == ErrorCode.ErrorCode_Validation_Missing_ScheinId
            ) {
              actionButtonGroup = (
                <Button
                  intent={Intent.PRIMARY}
                  loading={isLoading}
                  disabled={isLoading}
                  onClick={async () => {
                    patientFileActions.schein.setIssueDate(entry.selectedDate);
                    openCreateSchein(MainGroup.PRIVATE);
                  }}
                >
                  {t('btnCreateNewSchein')}
                </Button>
              );
            }

            return (
              <ValidationMessage
                index={index}
                total={grouped[key]?.length}
                key={getUUID()}
                error={error}
                actionButtonGroup={actionButtonGroup}
              />
            );
          });
        })}
      </Flex>
    );
  }, [encounterGoaService?.errors, isLoading]);

  const handleTakeOver = async (
    data: TimelineModel[],
    mappingTreatmentRelevent: { [key: string]: boolean }
  ) => {
    await takeOverDiagnosisWithScheinId({
      scheinId: cachedScheinId,
      timelineModelIds: data
        .filter((d) => d.id && Boolean(d['isAddTimeline']) === false)
        .map((d) => d.id),
      newDiagnosis: data.filter((d) => !d.id),
      mappingTreatmentRelevent: mappingTreatmentRelevent,
    });
    setCachedScheinId(undefined);
  };

  return (
    <React.Fragment>
      <Flex auto className={getCssClass(className, style)}>
        <Flex>
          <Flex auto basis={0} className="content">
            <Flex column className="service-info" onClick={setEditInline}>
              <Flex className="entry-type" gap={0} align="center">
                <Flex className={`entry-text ${type?.toLowerCase()}`}>
                  <HighlightWrapper matchedTokens={matchedTokens}>
                    {renderServiceContent()}
                  </HighlightWrapper>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
          <Flex align="center">
            <TimelineDeletionRemain
              className="timeline-deletion-remain"
              entry={entry}
            />
            {!!currentSchein && (
              <div
                className="main-group"
                style={{
                  textDecoration:
                    entry?.auditLogs[0]?.actionType === ActionType.Remove
                      ? 'line-through'
                      : '',
                  color: colorMainGroup(currentSchein.scheinMainGroup),
                  backgroundColor: backgroundMainGroup(
                    currentSchein.scheinMainGroup
                  ),
                }}
              >
                {formatScheinName(currentSchein, t('pseudo'))}
              </div>
            )}
            <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
            {renderActionMenu().length > 0 && (
              <TimelineActionHOC
                entry={props?.entry}
                hasBilled={hasBilled}
                displayInfo={displayInfo}
              >
                <Flex className="actions-group">
                  <Popover
                    content={
                      <Menu className="action-menu">{renderActionMenu()}</Menu>
                    }
                  >
                    <Svg className="more-icon" src={MoreIcon} />
                  </Popover>
                </Flex>
              </TimelineActionHOC>
            )}
          </Flex>
        </Flex>
        {renderAllAdditionalInfo()}
        {!isHistoryMode ? <>{renderMessageBar}</> : null}
      </Flex>

      {cachedScheinId && props?.entry?.id && (
        <SelectDiagnosisDialog
          takeoverDiagnosisType={
            TakeoverDiagnosisType.TakeoverDiagnosisPsychotherapy
          }
          requiredAtLeast={1}
          patientId={patientId}
          onTakeover={(existed, newvalues, mappingTreatmentRelevent) => {
            handleTakeOver(
              [...existed, ...newvalues],
              mappingTreatmentRelevent
            );
          }}
          scheinId={cachedScheinId}
          onCancel={() => {}}
          serviceId={entry.id}
        />
      )}
    </React.Fragment>
  );
};

export default memo(ServiceEntry);
