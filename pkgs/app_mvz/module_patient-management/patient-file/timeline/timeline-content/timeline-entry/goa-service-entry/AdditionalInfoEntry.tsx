import React from 'react';
import { AdditionalInfoParent } from '@tutum/hermes/bff/service_domains_patient_file';
import I18n from '@tutum/infrastructure/i18n';
import {
  getCssClass,
  getUUID,
} from '@tutum/design-system/infrastructure/utils';
import Theme from '@tutum/mvz/theme';
import { mixTypography } from '@tutum/design-system/themes/styles';
import ADDITIONAL_INFO_DATA from '@tutum/design-system/composer/assets/additional-info.json';
import { transformPrice } from '@tutum/infrastructure/shared/price-format';
// import AddInfoUtils from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-plugin/AdditionalInfoPlugin.util';

type MetaAdditionalInfo = {
  fK: string;
  inputType: string;
  dataSource: string;
  additionalInformations?: MetaAdditionalInfo[];
};

export interface AdditionalInfoEntryProps {
  className?: string;
  infoMeta?: MetaAdditionalInfo;
  info: Partial<AdditionalInfoParent>;
  isChild?: boolean;
  isLast?: boolean;
  displayInfo?(info: Partial<AdditionalInfoParent>): string | React.ReactNode;
}

const MATERIAL_COST_PRICE_FIELD_FK = '5012';
const TOTAL_AMOUNT = '5300';
const FACTOR = 'factor';

const OriginalAdditionalInfoEntry = ({
  className,
  infoMeta,
  info,
  isChild,
  isLast,
  displayInfo,
}: AdditionalInfoEntryProps) => {
  const { t: tAddtionalInfo } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });
  const { t: tDropDownItems } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'DropDownItems',
  });

  if (!info.value) return null;

  const infoMetaData: MetaAdditionalInfo =
    (infoMeta as MetaAdditionalInfo) ??
    ADDITIONAL_INFO_DATA.find((infoMeta) => infoMeta.fK === info.fK);
  const hasNoChildren = !info?.children?.length;

  function getLabel(fk?: string): string {
    return tAddtionalInfo(fk!);
  }

  const getValue = (
    meta: MetaAdditionalInfo,
    info: Partial<AdditionalInfoParent>
  ) => {
    if (meta && meta.inputType === 'DropDownList') {
      return tDropDownItems(`${meta.dataSource}.${info.value}`);
    }

    if (info.fK === MATERIAL_COST_PRICE_FIELD_FK) {
      return `${transformPrice(Number(info.value), {} as any)}`;
    }

    if (info.fK === TOTAL_AMOUNT) {
      return `${parseFloat(info.value!).toFixed(2).replace('.', ',')} €`;
    }

    return displayInfo ? displayInfo(info) : info.value;
  };

  if (hasNoChildren) {
    return (
      <span key={getUUID()} className={className}>
        {!isChild ? (
          <b>{getLabel(info.fK)}:</b>
        ) : (
          <span>{getLabel(info.fK)}:</span>
        )}
        <span>&nbsp;{getValue(infoMetaData, info)}</span>
        {!isLast && !isChild && (
          <span>{info.fK !== FACTOR ? ',' : null}&nbsp;</span>
        )}
      </span>
    );
  }

  return (
    <span key={getUUID()} className={className}>
      <b>{getLabel(info.fK)}:</b>
      <span>&nbsp;{getValue(infoMetaData, info)}</span>
      {info.children?.length && !!info.children[0].value ? (
        <span>&nbsp;-&nbsp;</span>
      ) : (
        ''
      )}
      {info.children
        ?.filter((additionalChild) => !!additionalChild.value)
        ?.map((child, idx) => {
          const isLastChild = idx === (info.children?.length || 0) - 1;
          return (
            <span key={getUUID()}>
              <OriginalAdditionalInfoEntry
                infoMeta={infoMetaData.additionalInformations?.find(
                  (meta) => meta.fK === child.fK
                )}
                info={child}
                isChild
              />
              {isLastChild || !info.children?.[idx + 1]?.value ? (
                <span>,&nbsp;</span>
              ) : (
                <span>&nbsp;-&nbsp;</span>
              )}
            </span>
          );
        })}
    </span>
  );
};

const styled = Theme.styled;

const AdditionalInfoEntry: React.ComponentType<AdditionalInfoEntryProps> = styled(
  OriginalAdditionalInfoEntry
).attrs(({ className }) => ({
  className: getCssClass('sl-AdditionalInfoEntry', className),
}))`
  & {
    ${mixTypography('bodyS')}
    display: flex;
    align-items: flex-end;
    b {
      font-weight: 600;
    }
  }
`;

export default AdditionalInfoEntry;
