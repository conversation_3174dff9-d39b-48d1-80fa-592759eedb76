import { useQuery } from '@tanstack/react-query';
import { cloneDeep, debounce, groupBy } from 'lodash';
import React, {
  memo,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';

import {
  BodyTextM,
  Button,
  Flex,
  LoadingState,
  MessageBar,
  Svg,
  TOASTER_TIMEOUT_CUSTOM,
  Tag,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Intent,
  Menu,
  MenuItem,
  Popover,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  convertToUTCMoment,
  getCssClass,
  getUUID,
  isEmpty as utilIsEmpty,
} from '@tutum/design-system/infrastructure/utils';
import {
  EditResponse,
  GroupByQuarter,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { MainGroup, ScheinWithMainGroup } from '@tutum/hermes/bff/common';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { FormName } from '@tutum/hermes/bff/form_common';
import { reSubmitPreParticipateService } from '@tutum/hermes/bff/legacy/app_mvz_billing';
import { useQueryGetBillingIcdByCode } from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdebm';
import {
  document88130,
  documentSuggestion,
  markAcceptedByKV,
  markNotApprovedPyschotherapy,
  rollbackDocumentTerminateService,
  takeOverDiagnosisWithScheinId,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { EncounterServiceTimeline } from '@tutum/hermes/bff/legacy/repo_encounter';
import { TakeoverDiagnosisType } from '@tutum/hermes/bff/legacy/timeline_common';
import {
  AdditionalInfoParent,
  EncounterCase,
  EncounterServiceTimelinePsychotherapyStatus,
  TreatmentCase,
} from '@tutum/hermes/bff/repo_encounter';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  ReferralDoctorInfo,
  Sources,
} from '@tutum/hermes/bff/service_domains_patient_file';
import { ServiceErrorCode } from '@tutum/hermes/bff/service_domains_validation_timeline';
import {
  ActionType,
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import stringUtil from '@tutum/infrastructure/utils/string.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import {
  backgroundMainGroup,
  colorMainGroup,
  formatScheinName,
} from '@tutum/mvz/_utils/scheinFormat';
import SelectDiagnosisDialog from '@tutum/mvz/components/select-diagnosis-dialog/SelectDiagnosisDialog.styled';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { useActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import { KEY_MAPPING_TIMELINE } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.service';
import ServiceBlockService from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-block/services/service-block.service';
import {
  PreEnrollmentService,
  serviceCode56565,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.const';
import { handleReduceErrors } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import EntryDismissBtn from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/entry-dismiss-btn/EntryDismissBtn.style';
import SingleServiceChainEntry from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/service-chain-entry/single-service-chain-entry/SingleServiceChainEntry';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import reloadSchein from '@tutum/mvz/module_patient-management/utils/reloadSchein';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import DoctorInfoInput from '../../doctor-info-input/DoctorInfoInput.styled';
import { customAdditionalInfos } from '../service-entry/utils';
import { PayLoadHistoryMode } from '../util';
import AdditionalInfoEntry from './AdditionalInfoEntry';
import { BillingServiceCodeDialog } from './BillingServiceCodeDialog.styled';
import ValidationMessage from './ValidationMessage';
import CareFacility from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/service-entry/care-facility/CareFacility.styled';
import { searchEbmsComposer } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { BaseComposerRowCommand } from '@tutum/design-system/composer/Composer.type';

const trashIcon = '/images/trash-bin-red.svg';
const MoreIcon = '/images/more.svg';
const deactivateIcon = '/images/x-circle.svg';
const successIcon = '/images/check-circle.svg';
const alertTriangle = '/images/resend.svg';
const editIcon = '/images/edit-value.svg';

enum TypeOfDocumentPsychoTherapy {
  DOCUMENT_NEW,
  RE_DOCUMENT,
}

export interface IServiceEntryProps {
  className?: string;
  index?: number;
  theme?: IMvzTheme;
  entry?: TimelineModel;
  lastItem?: boolean;
  treatmentDoctorId?: string;
  patientId?: string;
  quarter?: GroupByQuarter;
  onEdit?: (isEditing: boolean) => void;
  isEditing?: boolean;
  doctorProfile?: IEmployeeProfile;
  contract: IContractInfo;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  patient: IPatientProfile;
  currentSchein?: ScheinItem;
  onRemoveEntry: (hardDelete?: boolean, cb?: Function) => void;
  setItemToEditDate?: () => void;
  setEditInline?: () => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  reloadQuarters: ReloadQuarterFunc;
  payLoadHistoryMode: PayLoadHistoryMode;
  isHistoryMode: boolean;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
}

export interface BillingIcdDialogProps {
  serviceCode: string;
  encounterDate: number;
  kv: string;
  setOpen: (isOpen: boolean) => void;
}

const BillingIcdDialog = ({
  serviceCode,
  encounterDate,
  kv,
  setOpen,
}: BillingIcdDialogProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.ServiceEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'ServiceEntry',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { data, isSuccess, isFetching } = useQueryGetBillingIcdByCode({
    code: serviceCode,
    encounterDate: encounterDate || datetimeUtil.now(),
    organizationId: kv,
  });

  const renderContent = useMemo(() => {
    if (isFetching) {
      return (
        <Flex h={200}>
          <LoadingState />
        </Flex>
      );
    }

    return (
      <Flex column p={16}>
        {t('missICDCodeToBillingDialog', { code: serviceCode })}
        <Flex flexWrap px={8} py={16}>
          {isSuccess &&
            data?.data.map((code, idx) => {
              return (
                <BodyTextM key={idx} className="sl-service-code">
                  • {code}
                </BodyTextM>
              );
            })}
        </Flex>
      </Flex>
    );
  }, [serviceCode, data?.data, isSuccess, isFetching]);

  return (
    <BillingServiceCodeDialog
      isOpen
      title={t('missICDCodeToBillingDialogTitle', {
        code: serviceCode,
      })}
      actions={
        <>
          <Button
            minimal
            outlined
            intent="primary"
            large
            onClick={() => setOpen(false)}
          >
            {tButtonActions('close')}
          </Button>
        </>
      }
      onClose={() => setOpen(false)}
    >
      {renderContent}
    </BillingServiceCodeDialog>
  );
};

const ServiceChainEntry = ({
  className,
  doctorIcon,
  patientId,
  currentSchein,
  hasBilled,
  entry,
  quarter,
  openCreateSchein,
  setEditInline,
  reloadQuarters,
  onRemoveEntry,
  onEditTimelineItem,
  onEdit,
}: IServiceEntryProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.ServiceEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'ServiceEntry',
  });

  const { getDoctorById, useGetLoggedInUserProfile } =
    GlobalContext.useContext();
  const firstServiceTimeline = entry?.encounterServiceChain?.services[0];
  const services = entry?.encounterServiceChain?.services || [];
  const treatmentDoctorId = entry?.treatmentDoctorId;
  const userProfile = treatmentDoctorId
    ? getDoctorById(treatmentDoctorId)
    : null;

  const { isShowVerahHint, isHistoryMode } = useTimeLineStore();
  const store = usePatientFileStore();
  const { doctorId } = useActionBarStore();

  const noErrorFilter = (service: EncounterServiceTimeline) => {
    let additionalInfos = service.additionalInfos;

    if (userProfile) {
      additionalInfos = customAdditionalInfos(
        service.additionalInfos || [],
        userProfile
      );
    }

    const hasVerahService = isShowVerahHint && service.code === serviceCode56565;

    return !service.errors?.length && !additionalInfos?.length && !hasVerahService;
  };

  const noErrorNoAdditionalServices = services.filter(noErrorFilter);
  const restServices = services.filter((s) => !noErrorFilter(s));

  const errors = entry?.errors;
  const [selectTerminalPsychotherapy, setSelectTerminalPsychotherapy] =
    useState<Nullable<ServiceErrorCode>>(null);
  const [
    selectTerminalPsychotherapyIndex,
    setSelectTerminalPsychotherapyIndex,
  ] = useState<number | null>(null);

  const [cachedScheinId, setCachedScheinId] =
    useState<Nullable<string>>(undefined);
  const { patientManagement, setGetPatientParticipationResponse } = useContext(
    PatientManagementContext.instance
  );
  const [cachedTerminalServiceCode, setCachedTerminalServiceCode] =
    useState<Nullable<string>>(undefined);
  const [cachedTerminateServiceCode, setCachedTerminateServiceCode] =
    useState<Nullable<string>>(undefined);
  const currentUser = useGetLoggedInUserProfile();
  const { allowRemoveTimeline } = useSettingStore();
  const [typeOfDocumentTherapy, setTypeOfDocumentTherapy] =
    useState<Nullable<TypeOfDocumentPsychoTherapy>>(null);

  const { t: tEncounter } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimelineEncounter
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimelineEncounter',
  });

  const allowDirectEditor =
    errors &&
    errors.find(
      (error) =>
        error.type === 'error' &&
        utilIsEmpty(error.akaFunction, true) &&
        error.errorCode === 'SERVICE_VALIDATION_ERROR'
    );

  const style = !allowDirectEditor ? '' : 'warning';

  const doctorProfile = useMemo(
    () => getDoctorById(doctorId || ''),
    [doctorId]
  );

  const {
    isSuccess,
    data: serviceCodes,
    refetch,
  } = useQuery({
    queryKey: ['FETCH_SERVICE_CODE_TO_TERMINATE_PSYCHOTHERAPY'],
    queryFn: async () => {
      if (!currentUser) {
        throw new Error('currentUser is mandatory');
      }
      const result = await ServiceBlockService.searchService(
        entry?.createdAt ?? datetimeUtil.now(),
        '88130-88131',
        currentUser,
        []
      );
      return result;
    },
    enabled: false,
  });
  const [isDoctorEditing, setIsDoctorEditing] = useState(false);
  const [referralDoctorInfo, setReferralDoctorInfo] =
    useState<ReferralDoctorInfo>({
      lanr: undefined,
      bsnr: undefined,
      requiredBsnr: false,
      requiredLanr: false,
    });
  const [isLoading, setIsLoading] = useState(false);
  const [isOptionalCareFacility, setIsOpenCareFacility] = useState(false);

  useEffect(() => {
    if (entry?.contractId) {
      webWorkerServices
        .doesContractSupportFunctions(['ABRD1062'], entry.contractId)
        .then((resp) => {
          setIsOpenCareFacility(resp);
        });
    }
  }, [entry?.contractId]);

  useEffect(() => {
    if (firstServiceTimeline?.referralDoctorInfo) {
      setReferralDoctorInfo(firstServiceTimeline.referralDoctorInfo);
    }
  }, [firstServiceTimeline?.referralDoctorInfo]);

  const markNotApproval = async (id: string) => {
    await markNotApprovedPyschotherapy({
      timelineId: [id],
      patientId: patientId || '',
    });
    reloadQuarters({ quarter: entry?.quarter as number, year: entry?.year as number });
  };

  const renderActionMenu = () => {
    const actions: React.JSX.Element[] = [];
    // Split 2 check billed because make sure the order of showing icon
    if (!hasBilled) {
      const actionEdit = (
        <MenuItem
          key="2"
          icon={<Svg src={editIcon} />}
          text={tEncounter('editEntry')}
          onClick={setEditInline}
        />
      );
      actions.push(actionEdit);
    }

    if (
      entry?.encounterCase === EncounterCase.PRE_ENROLLMENT &&
      !firstServiceTimeline?.isSubmitPreParticipateSucsess &&
      PreEnrollmentService.has(firstServiceTimeline?.code || '')
    ) {
      const actionReSubmitPreEnrollment = (
        <MenuItem
          key="0"
          icon={<Svg src={alertTriangle} />}
          text={
            firstServiceTimeline?.code === 'KJP4a'
              ? tEncounter('KJP4a_resendHpmRequest_buttonLabel')
              : tEncounter('UHU35_resendHpmRequest_buttonLabel')
          }
          onClick={() => {
            if (entry?.id) {
              reSubmitPreParticipateService({
                serviceId: entry.id,
              });
            }
          }}
        />
      );
      actions.push(actionReSubmitPreEnrollment);
    }

    if (!hasBilled && allowRemoveTimeline) {
      const actionRemove = (
        <MenuItem
          key="2"
          icon={<Svg src={trashIcon} />}
          text={t('removeEntry')}
          onClick={() => onRemoveEntry(false)}
        />
      );
      actions.push(actionRemove);
    }

    if (
      hasBilled &&
      firstServiceTimeline?.approvalStatus &&
      [
        EncounterServiceTimelinePsychotherapyStatus.IsApproval,
        EncounterServiceTimelinePsychotherapyStatus.IsCompleted,
      ].includes(firstServiceTimeline.approvalStatus) &&
      !!entry?.id
    ) {
      const actionRemove = (
        <MenuItem
          key="0"
          icon={<Svg src={deactivateIcon} />}
          text={t('removeApproval')}
          onClick={() => markNotApproval(entry?.id as string)}
        />
      );
      actions.push(actionRemove);
    }

    if (
      firstServiceTimeline?.approvalStatus ===
      EncounterServiceTimelinePsychotherapyStatus.NotAcceptedByKV
    ) {
      const actionRemove = (
        <MenuItem
          key="0"
          icon={<Svg src={successIcon} />}
          text={t('markAsAcceptedByKV')}
          onClick={() => {
            if (entry?.id) {
              markAcceptedByKV({
                timelineId: entry.id,
              });
            }
          }}
        />
      );
      actions.push(actionRemove);
    }

    return actions;
  };

  const renderAllAdditionalInfo = (item: EncounterServiceTimeline) => {
    const treatmentDoctorId = entry?.treatmentDoctorId;
    if (!item?.additionalInfos?.length || !treatmentDoctorId) return;

    const userProfile = getDoctorById(treatmentDoctorId);
    if (!userProfile) return;

    const additionalInfos = customAdditionalInfos(
      item?.additionalInfos,
      userProfile
    );

    return (
      <span
        key={getUUID()}
        className="add-info__container"
        style={{
          textDecoration:
            entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'line-through'
              : 'none',
          color:
            entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'red'
              : undefined,
        }}
      >
        {additionalInfos.map((info, idx) => (
          <span className="add-info__item" key={idx}>
            <b>{'('}</b>
            <AdditionalInfoEntry
              key={getUUID()}
              info={info}
              isChild={false}
              isLast={true}
              displayInfo={displayInfo}
            />
            <b>{')'}</b>
          </span>
        ))}
      </span>
    );
  };

  const renderServiceContent = (item: EncounterServiceTimeline) => {
    if (!item) return;

    return (
      <Flex w="100%" justify="flex-start" gap={6}>
        <SingleServiceChainEntry
          item={item}
          type={entry?.auditLogs[0]?.actionType}
        />
        {renderAllAdditionalInfo(item)}
        {item.approvalStatus ===
          EncounterServiceTimelinePsychotherapyStatus.NotAcceptedByKV && (
            <Tag className="tag_not_accepted_by_kv" intent={Intent.WARNING}>
              {t('notAcceptedByKV')}
            </Tag>
          )}
      </Flex>
    );
  };

  const displayInfo = (
    info: Partial<AdditionalInfoParent>
  ): string | React.ReactNode => {
    const transformHHMM = () => {
      for (const error of firstServiceTimeline?.errors || []) {
        if (error?.metaData?.field === '5006') {
          return info.value;
        }
      }
      const HH = info?.value?.slice(0, 2);
      const MM = info?.value?.slice(2);
      return `${HH}:${MM}`;
    };
    const mapByFK: Record<string, () => string | React.ReactNode> = {
      '5006': transformHHMM,
      '5900': transformHHMM,
    };
    const display = mapByFK[String(info.fK)];

    return display ? display() : info.value;
  };

  const renderCareFacility = (idx: number) => {
    if (isOptionalCareFacility) {
      return null;
    }
    const serviceTimeline = entry?.encounterServiceChain?.services[idx];
    const careFacility = serviceTimeline?.careFacility;
    const shouldShowCareFacilityInfo = careFacility && careFacility.required;

    if (shouldShowCareFacilityInfo) {
      const name = serviceTimeline?.careFacility?.name;
      const ort = serviceTimeline?.careFacility?.ort;
      const saveCareFacilityInput = async (name: string, ort: string) => {
        const payload = cloneDeep(entry);

        onEditTimelineItem(
          {
            ...payload,
            auditLogs: payload?.auditLogs ?? [],
            quarter: Number(payload?.quarter),
            year: Number(payload?.year),
            [KEY_MAPPING_TIMELINE.SERVICE_CHAIN]: {
              ...payload?.[KEY_MAPPING_TIMELINE.SERVICE_CHAIN],
              services: (payload?.[
                KEY_MAPPING_TIMELINE.SERVICE_CHAIN
              ]?.services || []).map((service, index) => ({
                ...service,
                careFacility:
                  index === idx
                    ? { ...service.careFacility, name, ort }
                    : service.careFacility,
                sources: Sources.Timeline,
              })),
            },
            patientId,
          } as TimelineModel,
          () => {
            alertSuccessfully(t('saveCareFacilityToasterMessage'), {
              timeout: TOASTER_TIMEOUT_CUSTOM,
            });
          },
          () => { }
        );
      };
      return (
        <CareFacility
          name={name}
          ort={ort}
          isDisabled={hasBilled}
          onSave={saveCareFacilityInput}
        />
      );
    }
    return null;
  };

  const renderDoctorReferralInput = () => {
    const cancelDoctorReferralInput = () => {
      setIsDoctorEditing(false);
      if (onEdit) {
        onEdit(false);
      }
    };
    const saveDoctorReferralInput = async (lanr: string, bsnr: string) => {
      const payload = cloneDeep(entry);

      onEditTimelineItem(
        {
          ...payload,
          auditLogs: payload?.auditLogs ?? [],
          quarter: Number(payload?.quarter),
          year: Number(payload?.year),
          [KEY_MAPPING_TIMELINE.SERVICE]: {
            ...payload?.encounterServiceTimeline,
            code: String(payload?.encounterServiceTimeline?.code),
            description: String(payload?.encounterServiceTimeline?.description),
            freeText: String(payload?.encounterServiceTimeline?.freeText),
            command: String(payload?.encounterServiceTimeline?.command),
            referralDoctorInfo: {
              bsnr,
              lanr,
            },
            sources: Sources.Timeline,
          },
          patientId,
        } as TimelineModel,
        () => {
          setReferralDoctorInfo({
            ...referralDoctorInfo,
            lanr,
            bsnr,
          });
          setIsDoctorEditing(false);

          alertSuccessfully(t('saveReferrerInfoToasterMessage'), {
            timeout: TOASTER_TIMEOUT_CUSTOM,
          });
          if (onEdit) {
            onEdit(false);
          }
        },
        () => { }
      );
    };
    return (
      <DoctorInfoInput
        doctorBsnr={referralDoctorInfo.bsnr}
        doctorLanr={referralDoctorInfo.lanr}
        onSave={saveDoctorReferralInput}
        onCancel={cancelDoctorReferralInput}
      />
    );
  };

  const reloadScheinMod = async () => {
    const { scheins: newScheins, patientParticipation } =
      await reloadSchein(patientManagement);
    const activeScheins = newScheins.filter((schein) => !schein.markedAsBilled);
    patientFileActions.schein.setActivatedSchein(
      activeScheins[0],
      patientParticipation.participations
    );
    setGetPatientParticipationResponse(patientParticipation);
  };

  const documentTerminalPsychotherapy = async (
    type: ServiceErrorCode,
    index: number
  ) => {
    setIsLoading(true);
    const treatmentDoctorId = entry?.treatmentDoctorId;
    // const yearQuarter = datetimeUtil.toQuarterYear(datetimeUtil.now());
    let query = '88130';
    const is88131 =
      type == ServiceErrorCode.ServiceErrorCode_Psychotherapy_88131;
    if (is88131) {
      query = '88131';
    }
    setCachedTerminalServiceCode(query);

    const servicesRes = await searchEbmsComposer({
      query,
      organizationId: await webWorkerServices.convertUkvToOkv(
        String(currentUser?.bsnr?.slice(0, 32))
      ),
      selectedDate: datetimeUtil.now(),
    });

    const services = servicesRes.data?.items ?? [];
    const service = services?.find((i) => i.code == query);
    const now = datetimeUtil.dateToMoment();
    const schein = store?.schein?.list.find(
      (s) =>
        s.scheinMainGroup == MainGroup.KV &&
        !s.markedAsBilled &&
        s.g4101Year == now.year() &&
        s.g4101Quarter == now.quarter()
    );
    if (!!service && !!treatmentDoctorId) {
      const scheins: ScheinWithMainGroup[] = [];
      const scheinIds: string[] = [];
      const additionalInfos: AdditionalInfoParent[] = [];
      const doctor = getDoctorById(treatmentDoctorId);
      if (doctor) {
        if (doctor.bsnr) {
          additionalInfos.push({
            fK: '5098',
            value: doctor.bsnr,
            children: [],
          });
        }
        if (doctor.lanr || doctor.pseudoLanr) {
          additionalInfos.push({
            fK: '5099',
            value: doctor.lanr ?? doctor.pseudoLanr,
            children: [],
          });
        }
      }
      if (schein) {
        scheins.push({
          scheinId: schein.scheinId,
          group: schein.scheinMainGroup,
        });
        scheinIds.push(schein.scheinId);
      }
      const {
        data: { scheinId, serviceId },
      } = await document88130({
        timelineModel: {
          patientId: patientId as string,
          billingDoctorId: treatmentDoctorId,
          treatmentDoctorId: treatmentDoctorId,
          encounterCase: EncounterCase.AB,
          createdAt: now.toDate().getTime(),
          auditLogs: [],
          type: TimelineEntityType.TimelineEntityType_Service,
          encounterServiceTimeline: {
            command: 'L',
            isPreParticipate: false,
            freeText: `(${service.code}) ${service.description}`,
            code: String(service.code),
            description: String(service.description),
            referralDoctorInfo: {
              requiredBsnr: false,
              requiredLanr: false,
            },
            serviceMainGroup: MainGroup.KV,
            materialCosts: {
              required: false,
              materialCostsItemList: [],
            },
            additionalInfos: [...additionalInfos],
            scheins: scheins,
          },
          scheinIds: scheinIds,
          treatmentCase: TreatmentCase.TreatmentCaseCustodian,
          quarter: now.quarter(),
          year: now.year(),
          selectedDate: convertToUTCMoment(now).toDate().getTime(),
        },
        serviceEntryId: entry?.encounterServiceChain?.services[index].serviceId as string,
      });
      setCachedTerminateServiceCode(serviceId);
      if (scheinId) {
        setCachedScheinId(scheinId);
        setTypeOfDocumentTherapy(TypeOfDocumentPsychoTherapy.DOCUMENT_NEW);
      } else {
        alertSuccessfully(
          t('documentedTerminalPsychotherapy', { code: query }),
          {
            timeout: TOASTER_TIMEOUT_CUSTOM,
          }
        );
      }
      if (is88131) {
        reloadScheinMod();
      }
    }
    setIsLoading(false);
  };

  const renderExtraInfo = () => {
    if (isDoctorEditing) {
      return renderDoctorReferralInput();
    }
  };

  const handleClick = useCallback(
    async (serviceCode: string, index: number) => {
      if (!doctorProfile || !entry) return;
      try {
        setIsLoading(true);
        const result = await ServiceBlockService.searchService(
          entry?.createdAt ?? datetimeUtil.now(),
          serviceCode,
          doctorProfile,
          []
        );
        const serviceItem = result?.find((item) => item.code === serviceCode);
        await documentSuggestion({
          suggestionCode: serviceItem?.code ?? serviceCode,
          timelineId: entry.encounterServiceChain?.services[index].serviceId as string,
          suggestionData: {
            description: serviceItem?.description || '',
          },
        });

        alertSuccessfully(t('codeDocumented', { code: serviceCode }));
      } finally {
        setIsLoading(false);
      }
    },
    [doctorProfile, entry]
  );

  const [isOpenIcdDialog, setIsOpenIcdDialog] = useState(false);
  const listErrorNotShow = store?.skippedErrorKeyList || [];
  const setDismissError = (errorCode) => () => {
    const errList = cloneDeep(listErrorNotShow);
    errList.push({ entryId: entry?.id as string, errorCode });
    patientFileActions.saveSkippedErrorKeyList(errList);
  };
  const renderMessageBar = useCallback(
    (item: EncounterServiceTimeline) => {
      const errors = item?.errors ?? [];
      if (!errors?.length || entry?.isImported) {
        return null;
      }

      const groupErrors = handleReduceErrors(errors);
      const grouped = groupBy(groupErrors ?? [], 'type');
      return (
        <Flex column>
          {Object.keys(grouped ?? {}).map((key) => {
            return grouped[key].map((error, index: number) => {
              let actionButtonGroup: React.ReactNode = null;
              if (error?.errorCode === 'tss_surcharge_suggestion') {
                actionButtonGroup = (
                  <Button
                    intent="primary"
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={() => handleClick(error?.metaData?.code, index)}
                  >
                    {t('documentIt')}
                  </Button>
                );
              }

              if (
                error?.errorCode ===
                ServiceErrorCode.ServiceErrorCode_Psychotherapy_Missing_Diagnosis
              ) {
                actionButtonGroup = (
                  <Button
                    intent="primary"
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={() => {
                      setCachedScheinId(entry?.scheinIds?.[0]);
                      setTypeOfDocumentTherapy(
                        TypeOfDocumentPsychoTherapy.RE_DOCUMENT
                      );
                    }}
                  >
                    {t('documentIt')}
                  </Button>
                );
              }

              if (
                [
                  ServiceErrorCode.ServiceErrorCode_Psychotherapy_88130,
                  ServiceErrorCode.ServiceErrorCode_Psychotherapy_88131,
                ].includes(error.errorCode as ServiceErrorCode)
              ) {
                actionButtonGroup = (
                  <Button
                    intent={Intent.PRIMARY}
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={async () => {
                      const now = datetimeUtil.dateToMoment();
                      const schein = store?.schein?.list.find(
                        (s) =>
                          s.scheinMainGroup == MainGroup.KV &&
                          !s.markedAsBilled &&
                          s.g4101Year == now.year() &&
                          s.g4101Quarter == now.quarter()
                      );
                      if (!schein) {
                        await refetch();
                        setSelectTerminalPsychotherapy(
                          ServiceErrorCode.ServiceErrorCode_Psychotherapy_88130
                        );
                        setSelectTerminalPsychotherapyIndex(index);
                        return;
                      }
                      await documentTerminalPsychotherapy(
                        error.errorCode as ServiceErrorCode,
                        index
                      );
                    }}
                    style={{ height: 24, width: 'auto' }}
                  >
                    {t('documentIt')}
                  </Button>
                );
              }
              if (
                error.errorCode ==
                ServiceErrorCode.ServiceErrorCode_Psychotherapy
              ) {
                actionButtonGroup = (
                  <Button
                    intent={Intent.PRIMARY}
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={async () => {
                      refetch();
                      setSelectTerminalPsychotherapy(
                        ServiceErrorCode.ServiceErrorCode_Psychotherapy_88130
                      );
                      setSelectTerminalPsychotherapyIndex(index);
                    }}
                    style={{ height: 24, width: 'auto' }}
                  >
                    {t('documentIt')}
                  </Button>
                );
              }
              if (
                error.errorCode ==
                ErrorCode.ErrorCode_Validation_Missing_ScheinId
              ) {
                actionButtonGroup = (
                  <Button
                    intent={Intent.PRIMARY}
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={async () => {
                      if (
                        entry?.encounterServiceTimeline?.code &&
                        ['88130', '88131'].includes(
                          entry.encounterServiceTimeline.code
                        )
                      ) {
                        patientFileActions.setTerminalId(entry.id);
                      }

                      openCreateSchein();
                    }}
                  >
                    {t('btnCreateNewSchein')}
                  </Button>
                );
              }

              // NOTE: add a specific button
              // TODO: need a generic flow for fixing button (timeline validation)
              switch (error.errorCode) {
                case String(ErrorCode.ErrorCode_Missing_Referral_Doctor):
                case String(ErrorCode.ErrorCode_ValidationError_RequireLanr): // error code from contract definitions
                case String(ErrorCode.ErrorCode_ValidationError_RequireBsnr): // error code from contract definitions
                  return (
                    <ValidationMessage
                      index={index}
                      total={grouped[key]?.length}
                      key={getUUID()}
                      error={error}
                      actionButtonGroup={
                        <Flex>
                          <Button
                            intent={Intent.PRIMARY}
                            onClick={setEditInline}
                          >
                            {t('addLabel')}
                          </Button>
                        </Flex>
                      }
                    />
                  );
              }

              if (
                error.errorCode === ErrorCode.ErrorCode_MissICDCodeToBilling
              ) {
                return (
                  <>
                    <ValidationMessage
                      index={index}
                      total={grouped[key]?.length}
                      key={getUUID()}
                      error={error}
                      actionButtonGroup={
                        <Flex>
                          <Button
                            intent={Intent.PRIMARY}
                            loading={isLoading}
                            disabled={isLoading}
                            onClick={() => {
                              setIsOpenIcdDialog(true);
                            }}
                          >
                            {t('documentLabel')}
                          </Button>
                        </Flex>
                      }
                    />
                    {isOpenIcdDialog && (
                      <BillingIcdDialog
                        setOpen={setIsOpenIcdDialog}
                        serviceCode={item?.code}
                        kv={stringUtil.getKVRegion(doctorProfile?.bsnr || '')}
                        encounterDate={entry?.createdAt as number}
                      />
                    )}
                  </>
                );
              }

              return (
                <div key={index}>
                  {!listErrorNotShow.find(
                    (item) =>
                      item.entryId === entry?.id &&
                      item.errorCode === error.errorCode
                  ) && (
                      <ValidationMessage
                        index={index}
                        total={grouped[key]?.length}
                        key={getUUID()}
                        error={error}
                        subDescription={error?.metaData?.subDescription}
                        actionButtonGroup={
                          <>
                            {actionButtonGroup}
                            {error?.metaData?.isShowDismiss && (
                              <EntryDismissBtn
                                onClick={setDismissError(error.errorCode)}
                              />
                            )}
                          </>
                        }
                      />
                    )}
                </div>
              );
            });
          })}
        </Flex>
      );
    },
    [firstServiceTimeline?.errors, isLoading, isOpenIcdDialog, listErrorNotShow]
  );

  const renderMissingEmergencyPlanHint = () => {
    if (
      quarter?.quarter !== datetimeUtil.getQuarter(datetimeUtil.now()) ||
      firstServiceTimeline?.code !== '3740A'
    ) {
      return null;
    }
    const listEncounterForm = quarter?.timelineModels?.filter(
      (item) => item?.type === TimelineEntityType.TimelineEntityType_Form
    );
    const hasEmergencyPlanForm = listEncounterForm?.some(
      (form) =>
        form?.encounterForm?.prescribe?.formName ===
        FormName.BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1
    );
    if (hasEmergencyPlanForm) return null;
    return (
      <MessageBar
        showIcon
        type="warning"
        content={t('missingMusterEmergencyPlanHint')}
      />
    );
  };

  const renderVerahHint = (service: EncounterServiceTimeline) => {
    if (service?.code === serviceCode56565 && isShowVerahHint) {
      return (
        <MessageBar
          showIcon
          type="info"
          content="Die Leistung VERAH® TopVersorgt wurde bei diesem Versicherten dokumentiert."
        />
      );
    }
    return null;
  };

  const handleTakeOver = async (
    data: TimelineModel[],
    mappingTreatmentRelevent: { [key: string]: boolean } | undefined
  ) => {
    await takeOverDiagnosisWithScheinId({
      scheinId: cachedScheinId as string,
      timelineModelIds: data
        .filter((d) => d.id && Boolean(d['isAddTimeline']) === false)
        .map((d) => d.id as string),
      newDiagnosis: data.filter((d) => !d.id),
      mappingTreatmentRelevent: mappingTreatmentRelevent as { [key: string]: boolean },
    });
    setCachedScheinId(undefined);
    setTypeOfDocumentTherapy(undefined);
    reloadScheinMod();
    setCachedTerminateServiceCode(undefined);
    if (cachedTerminalServiceCode) {
      alertSuccessfully(
        t('documentAndTakeover', {
          code: cachedTerminalServiceCode,
        })
      );
      setCachedTerminalServiceCode(undefined);
    }
  };

  const debounceOnCancel = debounce(async () => {
    if (typeOfDocumentTherapy === TypeOfDocumentPsychoTherapy.DOCUMENT_NEW) {
      await rollbackDocumentTerminateService({
        tehcnicalScheinId: cachedScheinId as string,
        terminateServiceId: cachedTerminateServiceCode as string,
        patientId: patientId as string,
      });
    }
    setCachedScheinId(undefined);
    setTypeOfDocumentTherapy(undefined);
    reloadScheinMod();
    setCachedTerminateServiceCode(undefined);
  }, 500);

  return (
    <React.Fragment>
      <Flex auto className={getCssClass(className, style)}>
        <Flex>
          <Flex auto>
            <Flex
              column
              className="service-info"
              onClick={(_) => {
                if (entry?.encounterCase === EncounterCase.PRE_ENROLLMENT) {
                  return;
                }
                setEditInline?.();
              }}
            >
              <Flex className="entry-type" gap={16}>
                {/* {renderServiceContent()} */}
                <span className="name">
                  {BaseComposerRowCommand.SERVICE_CHAIN}
                </span>
                {noErrorNoAdditionalServices.map((item, idx) => {
                  return <SingleServiceChainEntry key={idx} item={item} />;
                })}
              </Flex>
            </Flex>
          </Flex>
          <Flex align="center">
            <TimelineDeletionRemain
              className="timeline-deletion-remain"
              entry={entry}
            />
            {!!currentSchein && (
              <div
                className="main-group"
                style={{
                  textDecoration:
                    entry?.auditLogs[0]?.actionType === ActionType.Remove
                      ? 'line-through'
                      : '',
                  color: colorMainGroup(currentSchein.scheinMainGroup),
                  backgroundColor: backgroundMainGroup(
                    currentSchein.scheinMainGroup
                  ),
                }}
              >
                {formatScheinName(currentSchein, t('pseudo'))}
              </div>
            )}
            <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
            {renderActionMenu().length > 0 && (
              <TimelineActionHOC
                entry={entry}
                hasBilled={hasBilled}
                displayInfo={displayInfo}
              >
                <Flex className="actions-group">
                  <Popover
                    content={
                      <Menu className="action-menu">{renderActionMenu()}</Menu>
                    }
                  >
                    <Svg className="more-icon" src={MoreIcon} />
                  </Popover>
                </Flex>
              </TimelineActionHOC>
            )}
          </Flex>
        </Flex>
        {/* {renderAllAdditionalInfo()} */}
        {restServices.map((e, idx) => {
          // TODO: RENDER EACH ITEM HERE...
          return (
            <div key={idx}>
              {renderServiceContent(e)}
              {!isHistoryMode ? (
                <>
                  {renderCareFacility(idx)}
                  {(referralDoctorInfo?.requiredBsnr ||
                    referralDoctorInfo?.requiredLanr) && (
                      <Flex className="extra-info">{renderExtraInfo()}</Flex>
                    )}
                  {renderMessageBar(e)}
                  {renderMissingEmergencyPlanHint()}
                  {renderVerahHint(e)}
                </>
              ) : null}
            </div>
          );
        })}
      </Flex>
      {isSuccess && (
        <InfoConfirmDialog
          type="primary"
          isOpen={!!selectTerminalPsychotherapy}
          title={t('documentService')}
          isShowIconTitle={false}
          onClose={() => {
            setSelectTerminalPsychotherapy(null);
            setSelectTerminalPsychotherapyIndex(null);
          }}
          onConfirm={async () => {
            if (selectTerminalPsychotherapy) {
              await documentTerminalPsychotherapy(
                selectTerminalPsychotherapy as ServiceErrorCode,
                selectTerminalPsychotherapyIndex || 0
              );
            }
            setSelectTerminalPsychotherapy(null);
          }}
        >
          <div>
            <RadioGroup
              selectedValue={String(selectTerminalPsychotherapy)}
              onChange={(e: React.FormEvent<HTMLInputElement>) => {
                setSelectTerminalPsychotherapy(
                  e.currentTarget.value as ServiceErrorCode
                );
              }}
            >
              {serviceCodes.map((s, index) => (
                <Radio
                  key={index}
                  label={`${s.code} - ${s.description}`}
                  id={`documentService_${s.code}`}
                  itemID={`documentService_${index}_itemId`}
                  value={
                    s.code == '88130'
                      ? ServiceErrorCode.ServiceErrorCode_Psychotherapy_88130
                      : ServiceErrorCode.ServiceErrorCode_Psychotherapy_88131
                  }
                />
              ))}
            </RadioGroup>
          </div>
        </InfoConfirmDialog>
      )}
      {cachedScheinId && entry?.id && (
        <SelectDiagnosisDialog
          takeoverDiagnosisType={
            TakeoverDiagnosisType.TakeoverDiagnosisPsychotherapy
          }
          requiredAtLeast={1}
          patientId={patientId}
          onTakeover={(existed, newvalues, mappingTreatmentRelevent) => {
            handleTakeOver(
              [...existed, ...(newvalues || [])],
              mappingTreatmentRelevent
            );
          }}
          scheinId={cachedScheinId}
          onCancel={debounceOnCancel}
          serviceId={entry.id}
        />
      )}
    </React.Fragment>
  );
};

export default memo(ServiceChainEntry);
