
import React from 'react';
import { EncounterItemError } from '@tutum/hermes/bff/repo_encounter';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { MessageBar } from '@tutum/design-system/components';
// import { ServiceErrorCode } from '@tutum/hermes/bff/service_domains_validation_timeline';
import { isEmpty, isNil } from 'lodash';
import { ServiceErrorCode } from '@tutum/hermes/bff/service_domains_validation_timeline';

export interface ErrorMessageProps {
  error: EncounterItemError;
  actionButtonGroup?: React.ReactNode;
  subDescription?: string;
  index: number;
  total: number;
}

// error.code is code from master data or somewhere else. e.g E881046 from AKA HZV
// error.errorCode is our defined custom error code can be used for translation
// else use error.message
const getErrorMessage = (
  error: EncounterItemError,
  t: IFixedNamespaceTFunction,
  tAdditionalInfo: IFixedNamespaceTFunction
): string => {
  // not sure when this case happen!
  if (isEmpty(error.errorCode)) {
    return t(error.message);
  }

  // NOTE: has additional info
  if (!isNil(error.metaData)) {
    // return t(`ErrorMessages.${error.errorCode}` as any);
    const fieldName = error.metaData['field']
      ? tAdditionalInfo(error.metaData['field'])
      : undefined;
    const fieldName1 = error.metaData['field1']
      ? tAdditionalInfo(error.metaData['field1'])
      : undefined;
    const fieldName2 = error.metaData['field2']
      ? tAdditionalInfo(error.metaData['field2'])
      : undefined;
    const childFieldName = error.metaData['childField']
      ? tAdditionalInfo(error.metaData['childField'])
      : undefined;
    const excludedCodes = error.metaData['excludedCodes']
      ? `(${error.metaData['excludedCodes']})`
      : undefined;
    const metaData = {
      ...error.metaData,
      fieldName,
      fieldName1,
      fieldName2,
      childFieldName,
      excludedCodes,
    };

    if (error.errorCode === ServiceErrorCode.ServiceErrorCode_OPS_5041) {
      const opsKeys = [];
      for (const [key, value] of Object.entries(metaData)) {
        if (key.startsWith('OPKey')) opsKeys.push(value);
      }
      metaData['opsKeys'] =
        opsKeys.length === 0
          ? ''
          : opsKeys.length === 1
            ? `${opsKeys}`
            : `[${opsKeys}]`;
    }

    return t(`ErrorMessages.${error.errorCode}` as any, metaData);
  }

  const translatedText = t(`ErrorMessages.${error.errorCode}`);
  // NOTE: specical case error message from Hzv/Fav SVD xml data and not support translation
  if (
    !isEmpty(error.message) &&
    translatedText.includes(`ErrorMessages.${error.errorCode}`)
  ) {
    return error.message;
  }
  return translatedText;
};

const getSubDescriptionMessage = (
  error: EncounterItemError,
  t: IFixedNamespaceTFunction,
  subDescription: string
): string => {
  const key = `ErrorMessages.${error.errorCode}_subDescription`;
  const translatedText = t(key);

  if (translatedText.includes(key)) {
    return subDescription;
  }

  return translatedText;
};

const ValidationMessage = ({
  error,
  index,
  total,
  actionButtonGroup,
  subDescription,
}: ErrorMessageProps) => {
  const { t } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'ServiceEntry',
  });
  const { t: tAdditionalInfo } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });
  const message = getErrorMessage(error, t, tAdditionalInfo);

  return (
    <MessageBar
      paddingTop={index === 0 ? '8px' : '0'}
      paddingBottom={index === total - 1 ? '8px' : '0'}
      showIcon={index === 0}
      type={error.type}
      content={message}
      actionButtonGroup={actionButtonGroup}
      hasBullet={total > 1}
      subDescription={getSubDescriptionMessage(error, t, subDescription)}
    />
  );
};

export default ValidationMessage;
