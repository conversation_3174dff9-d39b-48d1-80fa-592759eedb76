
import {
  Flex,
  Svg,
  MessageBar,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Menu,
  MenuItem,
  Popover,
  Tooltip,
} from '@tutum/design-system/components/Core';
import I18n, {
  IFixedNamespaceTFunction,
  II18nFixedNamespace,
} from '@tutum/infrastructure/i18n';
import type HimiI18n from '@tutum/mvz/locales/en/Himi.json';
import {
  IMusterPrescribe,
  musterFormDialogActions,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { memo, useMemo, useState } from 'react';
import { IContractInfo } from '../../../../../../types/contract.type';
import ChangeItemDateMenuItem from '../../ChangeItemDateMenuItem';
import HimiDialogRemoveConfirm from './DialogRemoveConfirm';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import {
  RemoveRequest,
  RemoveResponse,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { EncounterHimiPrescription } from '@tutum/hermes/bff/repo_encounter';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { Form } from '@tutum/hermes/bff/form_common';
import HighlightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import Guard from '@tutum/mvz/hooks/Guard';
import { UserType } from '@tutum/hermes/bff/legacy/common';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

const MoreIcon = '/images/more.svg';
const refillIcon = '/images/refill.svg';
const printerIcon = '/images/eye-on.svg';
const editIcon = '/images/content-editor-edit-3.svg';
const trashIcon = '/images/trash-bin-red.svg';

export interface ITimeLineFormDetailProps {
  form: EncounterHimiPrescription;
  contract?: IContractInfo;
  patientId: string;
  isControllable: boolean;
  doctorId: string;
  doctorIcon: JSX.Element;
  entry?: TimelineModel;
  listForms: Form[];
  setItemToEditDate: () => void;
  onDeleteTimelineItem: (
    data: RemoveRequest,
    handleSuccess: (result: RemoveResponse) => void,
    handleError: (error) => void
  ) => void;
  keyword?: string;
  onRemoveEntry: (hardDelete?: boolean) => void;
}

const convertFormToHimiPrescription = async (
  form: EncounterHimiPrescription,
  doctorId: string,
  t2: IFixedNamespaceTFunction<keyof typeof HimiI18n.TimePeriod>,
  bsnrId: string
): Promise<IMusterPrescribe> => {
  return {
    isViewForm: false,
    isRefill: false,
    isEditHimi: false,
    prescriptionDate: form.prescribeDate,
    quantity:
      form?.formInfo?.productInfo?.info?.quantity ||
      form?.formInfo?.productTypeInfo?.info?.quantity,
    timePeriod: {
      name: t2(
        (form?.formInfo?.productInfo?.info
          ?.timePeriod as keyof typeof HimiI18n.TimePeriod) ||
          (form?.formInfo?.productTypeInfo?.info
            ?.timePeriod as keyof typeof HimiI18n.TimePeriod)
      ),
      value:
        form?.formInfo?.productInfo?.info?.timePeriod ||
        form?.formInfo?.productTypeInfo?.info?.timePeriod,
    },
    doctor: {
      label: null,
      value: `${doctorId}-${bsnrId}`,
    },
    diagnosis: form?.diagnoseCode,
    secondaryDiagnosis: form?.secondaryDiagnore,
    product: form?.formInfo?.productInfo?.produkt,
    productType: form?.formInfo?.productTypeInfo?.productType,
    furtherInformation:
      form?.formInfo?.productInfo?.info?.furtherInformation ||
      form?.formInfo?.productTypeInfo?.info?.furtherInformation,
    currentFormSetting: JSON.parse(form?.formInfo?.formSetting),
    currentFormName: form?.formInfo?.currentFormType,
    listDiagnosis: [],
  } as IMusterPrescribe;
};

const getHimiDeviceName = (form: EncounterHimiPrescription) => {
  if (form?.formInfo?.productTypeInfo) {
    const productType = form?.formInfo?.productTypeInfo?.productType;

    if (productType.freetext) {
      return productType.freetext;
    }

    return `${productType.base.gruppe
      .toString()
      .padStart(2, '0')}.${productType.base.ort
      .toString()
      .padStart(2, '0')}.${productType.base.unter
      .toString()
      .padStart(2, '0')}.${productType.artId.toString()}-${
      productType.bezeichnung
    }`;
  }
  const product = form?.formInfo?.productInfo?.produkt;
  if (product) {
    return `${product.base.gruppe
      .toString()
      .padStart(2, '0')}.${product.base.ort
      .toString()
      .padStart(2, '0')}.${product.base.unter
      .toString()
      .padStart(2, '0')}.${product.artId
      .toString()
      .padStart(1, '0')}${product.produkt.toString().padStart(3, '0')}-${
      product.bezeichnung
    }.${product.hersteller}`;
  }
  return '';
};

const getQuantity = (form: EncounterHimiPrescription) => {
  if (form?.formInfo?.productInfo) {
    return form?.formInfo?.productInfo?.info?.quantity;
  }
  return form?.formInfo?.productTypeInfo?.info?.quantity;
};

type IHimiContentText = {
  isControllable: boolean;
  t: IFixedNamespaceTFunction<keyof typeof HimiI18n.Timeline>;
  form: EncounterHimiPrescription;
  contract: IContractInfo | null;
};

export const renderHimiContentText = ({
  isControllable,
  t,
  form,
}: IHimiContentText) => {
  const formSetting = JSON.parse(form.formInfo.formSetting);
  const deviceName =
    formSetting['textbox_himi_device_name'] ||
    formSetting['label_medication'] ||
    '';
  const diagnoseText = `${
    formSetting['textbox_diagnose_line1'] ||
    formSetting['textbox_diagnose_line1_0'] ||
    ''
  } ${
    formSetting['textbox_diagnose_line2'] ||
    formSetting['textbox_diagnose_line2_0'] ||
    ''
  }`.trim();

  let content = 'HIMI • ';
  if (isControllable) {
    content += t('ControllableFromTitle');
    return content;
  }

  content += t('NewHimi');
  if (deviceName) {
    content += ` - ${deviceName}`;
  }

  if (diagnoseText) {
    content += ` • ${diagnoseText}`;
  }

  return content;
};

function TimeLineFormDetailMemo(
  props: ITimeLineFormDetailProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof HimiI18n.Timeline>
) {
  const {
    entry,
    form,
    className,
    t,
    contract,
    isControllable,
    doctorIcon,
    listForms,
    setItemToEditDate,
    onDeleteTimelineItem,
  } = props;

  const [isOpenRemoveDialog, setOpenRemoveDialog] = useState(false);
  const [isRemoveLoading, setRemoveLoading] = useState(false);
  const { matchedTokens } = useTimeLineStore();
  const { t: t2 } = I18n.useTranslation<keyof typeof HimiI18n.TimePeriod>({
    namespace: 'Himi',
    nestedTrans: 'TimePeriod',
  });

  const hasRefill = useMemo(() => {
    return listForms?.find(
      (item) => item.id === form?.formInfo?.currentFormType
    )?.hasRefill;
  }, [form, listForms]);

  const handleViewForm = async () => {
    musterFormDialogActions.viewForm(
      await convertFormToHimiPrescription(
        form,
        props.doctorId,
        t2,
        entry?.assignedToBsnrId
      ),
      form.encounterId,
      contract,
      form.himiPrescriptionId,
      !!form?.printDate,
      false,
      isControllable,
      entry.scheinIds?.[0]
    );
  };

  const handleViewControllable = async () => {
    musterFormDialogActions.viewForm(
      await convertFormToHimiPrescription(
        form,
        props.doctorId,
        t2,
        entry?.assignedToBsnrId
      ),
      form.encounterId,
      contract,
      form.himiPrescriptionId,
      false,
      false,
      true
    );
  };

  const renderActionMenu = useMemo(() => {
    return (
      <Menu className="action-menu">
        {!isControllable && (
          <ChangeItemDateMenuItem
            onClick={() => {
              setItemToEditDate();
            }}
          />
        )}
        {hasRefill && !isControllable && (
          <MenuItem
            key="refillForm"
            text={t('RefillForm')}
            icon={<Svg src={refillIcon} />}
            onClick={async () => {
              musterFormDialogActions.viewForm(
                await convertFormToHimiPrescription(
                  form,
                  props.doctorId,
                  t2,
                  entry?.assignedToBsnrId
                ),
                form.encounterId,
                contract,
                form.himiPrescriptionId,
                false,
                true
              );
            }}
          />
        )}
        <MenuItem
          key="viewForm"
          text={t('ViewForm')}
          onClick={handleViewForm}
          icon={<Svg src={printerIcon} />}
        />
        {isControllable && (
          <MenuItem
            key="editForm"
            text={t('EditForm')}
            onClick={handleViewControllable}
            icon={<Svg src={editIcon} />}
          />
        )}
        {!isControllable && (
          <Guard roles={[UserType.MANAGER]} key="remove">
            <MenuItem
              key="remove"
              text={t('Remove')}
              onClick={() => {
                setOpenRemoveDialog(true);
              }}
              icon={<Svg src={trashIcon} />}
            />
          </Guard>
        )}
      </Menu>
    );
  }, [hasRefill, isControllable]);

  const renderAction = () => {
    return (
      <TimelineActionHOC>
        <Flex className="actions-group">
          <Popover content={renderActionMenu}>
            <Tooltip content={t('More')} position="top">
              <Svg className="more-icon" src={MoreIcon} />
            </Tooltip>
          </Popover>
        </Flex>
      </TimelineActionHOC>
    );
  };

  const renderHintPrintout = () => {
    if (form?.printDate) {
      return null;
    }
    return (
      <Flex align="center" mt={8}>
        <MessageBar
          hasBullet={false}
          type="warning"
          content={t('PrintOutHint')}
        />
      </Flex>
    );
  };

  return (
    <Flex column onDoubleClick={handleViewForm}>
      <div className={className}>
        <HimiDialogRemoveConfirm
          isLoading={isRemoveLoading}
          onClose={(isDeleted) => {
            if (isDeleted) {
              setRemoveLoading(true);
              onDeleteTimelineItem(
                {
                  timelineId: entry.id,
                },
                () => {
                  alertSuccessfully(t('DeleteSuccessfully'));
                  setRemoveLoading(false);
                },
                () => {
                  setRemoveLoading(false);
                }
              );
            } else {
              setOpenRemoveDialog(false);
            }
          }}
          isOpen={isOpenRemoveDialog}
          productName={`${getQuantity(form)} x ${getHimiDeviceName(form)}`}
        />
        <Flex flexWrap>
          <HighlightWrapper matchedTokens={matchedTokens}>
            <p className="himi-content hover-text">
              {renderHimiContentText({
                isControllable,
                t,
                form,
                contract,
              })}
            </p>
          </HighlightWrapper>
        </Flex>
        <Flex align="flex-start">
          <TimelineDeletionRemain entry={entry} />
          <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
          {renderAction()}
        </Flex>
      </div>
      {renderHintPrintout()}
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(TimeLineFormDetailMemo, {
    namespace: 'Himi',
    nestedTrans: 'Timeline',
  })
);
