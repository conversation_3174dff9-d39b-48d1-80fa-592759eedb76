import React, { memo } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type HimiI18n from '@tutum/mvz/locales/en/Himi.json';

import { IMvzThemeProps } from '@tutum/mvz/theme';
import { IContractInfo } from '../../../../../types/contract.type';
import { Flex } from '@tutum/design-system/components';
import TimeLineFormDetail from './time-line-form-detail/TimeLineFormDetail.styled';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import {
  RemoveRequest,
  RemoveResponse,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { Form } from '@tutum/hermes/bff/form_common';

export interface IHimiPrescriptionProps {
  entry: TimelineModel;
  lastItem?: boolean;
  patientId: string;
  contract?: IContractInfo;
  doctorIcon: JSX.Element;
  listForms: Form[];
  setItemToEditDate?: () => void;
  onDeleteTimelineItem: (
    data: RemoveRequest,
    handleSuccess: (result: RemoveResponse) => void,
    handleError: (error) => void
  ) => void;
  keyword?: string;
  onRemoveEntry: (hardDelete?: boolean) => void;
}

function HimiPrescriptionMemo(
  props: IHimiPrescriptionProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof HimiI18n.Timeline>
) {
  const {
    entry,
    className,
    contract,
    doctorIcon,
    listForms,
    setItemToEditDate,
    onDeleteTimelineItem,
    patientId,
    keyword,
    onRemoveEntry,
  } = props;

  const renderControllableHimiTimeLine = () => {
    if (!entry?.encounterHimiPrescription?.additionalForm) {
      return null;
    }
    return (
      <TimeLineFormDetail
        entry={entry}
        isControllable={true}
        contract={contract}
        patientId={patientId}
        doctorId={entry.treatmentDoctorId}
        form={entry.encounterHimiPrescription}
        doctorIcon={doctorIcon}
        listForms={listForms}
        setItemToEditDate={setItemToEditDate!}
        onDeleteTimelineItem={onDeleteTimelineItem}
        keyword={keyword}
        onRemoveEntry={onRemoveEntry}
      />
    );
  };

  return (
    <Flex column className={className}>
      <Flex className="entry-wrapper" column>
        <TimeLineFormDetail
          entry={entry}
          isControllable={false}
          contract={contract}
          patientId={patientId}
          doctorId={entry.treatmentDoctorId}
          form={entry.encounterHimiPrescription!}
          doctorIcon={doctorIcon}
          listForms={listForms}
          setItemToEditDate={setItemToEditDate!}
          onDeleteTimelineItem={onDeleteTimelineItem}
          keyword={keyword}
          onRemoveEntry={onRemoveEntry}
        />
        {renderControllableHimiTimeLine()}
      </Flex>
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(HimiPrescriptionMemo, {
    namespace: 'Himi',
    nestedTrans: 'Timeline',
  })
);
