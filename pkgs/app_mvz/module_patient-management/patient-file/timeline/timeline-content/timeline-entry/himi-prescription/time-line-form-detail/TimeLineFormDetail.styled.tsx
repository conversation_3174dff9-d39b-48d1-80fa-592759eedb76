
import { ComponentType } from 'react';
import Theme from '@tutum/mvz/theme';
import OriginalTimeLineFormDetailMemo, {
  ITimeLineFormDetailProps,
} from './TimeLineFormDetail';
import TimelineService from '../../../../Timeline.service';

const styled = Theme.styled;
const TimeLineFormDetail: ComponentType<ITimeLineFormDetailProps> = styled(
  OriginalTimeLineFormDetailMemo
)`
  & {
    display: flex;
    flex-direction: row;
    .sl-Flex:first-child {
      flex: 2;
    }
    .sl-Flex:nth-child(2) {
      .timeline-right-side-infos {
        align-items: center;
      }
    }
    .himi-content {
      color: ${(props) =>
        TimelineService.parseEntryColor(props.entry, props.theme.timelineTheme)
          ?.text};
    }
  }
`;

export default TimeLineFormDetail;
