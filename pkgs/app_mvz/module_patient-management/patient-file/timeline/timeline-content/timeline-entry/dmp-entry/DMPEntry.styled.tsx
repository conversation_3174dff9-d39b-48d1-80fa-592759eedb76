
import Theme from '@tutum/mvz/theme';

import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import OriginalDMPEntry from './DMPEntry';
import TimelineService from '../../../Timeline.service';

const styled = Theme.styled;

const StyledDMPEntry = styled(OriginalDMPEntry).attrs(({ className }) => ({
  className: getCssClass('sl-dmp-entry', className),
}))`
  gap: 8px;

  color: ${(props) => {
    return TimelineService.parseEntryColor(
      props.entry,
      props.theme.timelineTheme
    )?.text;
  }};

  .sl-icon {
    height: 16px;
    width: 16px;
  }

  &:not(:first-child) {
    margin-top: 16px;
  }

  .bp5-text-overflow-ellipsis.bp5-fill {
    font-size: 11px !important;
  }
`;

export default StyledDMPEntry;
