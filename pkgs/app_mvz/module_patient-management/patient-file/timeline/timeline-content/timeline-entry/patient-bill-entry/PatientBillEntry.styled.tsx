import Theme from '@tutum/mvz/theme';

import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import OriginalPatientBillEntry from './PatientBillEntry';
import TimelineService from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';

const styled = Theme.styled;

const StyledPatientBillEntry = styled(OriginalPatientBillEntry).attrs(
  ({ className }) => ({
    className: getCssClass('sl-patient-bill-entry', className),
  })
)`
  gap: 8px;

  .sl-icon {
    height: 16px;
    width: 16px;
  }

  .patient-bill-content {
    color: ${(props) => {
    return TimelineService.parseEntryColor(
      props.entry,
      props.theme.timelineTheme
    )?.text;
  }};
  }

  .bp5-text-overflow-ellipsis.bp5-fill {
    font-size: 11px !important;
  }
`;

export default StyledPatientBillEntry;
