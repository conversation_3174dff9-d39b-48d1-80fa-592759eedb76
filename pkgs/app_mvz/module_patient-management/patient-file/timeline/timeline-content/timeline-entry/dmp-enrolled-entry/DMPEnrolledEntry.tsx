
import {
  memo,
  useState,
  useMemo,
  useCallback,
  useEffect,
  useContext,
} from 'react';
import i18n from '@tutum/infrastructure/i18n';
import {
  BodyTextL,
  Flex,
  FormGroup2,
  ReactSelect,
  Svg,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Menu,
  Popover,
  Tooltip,
  MenuItem,
} from '@tutum/design-system/components/Core';
import { Tag } from '@tutum/design-system/components/Tag';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import type DMPI18n from '@tutum/mvz/locales/en/DMP.json';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import DMP_PROGRAMS from '@tutum/design-system/consts/DMP-programs';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import {
  musterFormDialogActions,
  musterFormDialogStore,
  useMusterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { printPlainPdf } from '@tutum/hermes/bff/app_mvz_form';
import PrinterService from '@tutum/mvz/services/printer.service';
import {
  EnrollmentInfo,
  ParticipationForm,
  ParticipationFormsStatus,
} from '@tutum/hermes/bff/edmp_common';
import { cloneDeep } from 'lodash';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { EditResponse } from '@tutum/hermes/bff/app_mvz_timeline';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';

export interface DMPEnrolledEntryProps {
  className?: string;
  doctorIcon: JSX.Element;
  entry: TimelineModel;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
}

const MoreIcon = '/images/more.svg';
const EyeIcon = '/images/eye-on.svg';

const DMPEnrolledEntry = ({
  className,
  doctorIcon,
  entry,
  onEditTimelineItem,
}: DMPEnrolledEntryProps) => {
  const { t } = i18n.useTranslation<keyof typeof DMPI18n.timelineEntry>({
    namespace: 'DMP',
    nestedTrans: 'timelineEntry',
  });
  const { t: tParticipationForms } = i18n.useTranslation<
    keyof typeof DMPI18n.enrollDialog.participationForms
  >({
    namespace: 'DMP',
    nestedTrans: 'enrollDialog.participationForms',
  });
  const { t: tCommon } = i18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });

  const {
    patientManagement: { patient },
  } = useContext(PatientManagementContext.instance);
  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  const store = useMusterFormDialogStore();

  const [isOpenParticipationForm, setOpenParticipationForm] =
    useState<boolean>(false);

  const matchedDMP = useMemo(() => {
    return DMP_PROGRAMS.getMatchedDMP(
      entry.eDMPEnrollmentInfo?.participationForm.dMPLabelingValue || ''
    ) as typeof DMP_PROGRAMS.DMP_LIST[0];
  }, [entry.eDMPEnrollmentInfo]);

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    entry.eDMPEnrollmentInfo?.treatmentDoctorId,
    entry.assignedToBsnrId
  );

  const textDMP = useMemo(() => {
    return `${t('command')} - ${matchedDMP.name}`;
  }, [matchedDMP]);

  const isSaved = useMemo(() => {
    return (
      entry.eDMPEnrollmentInfo?.participationForm.participationFormsStatus ===
      ParticipationFormsStatus.ParticipationFormsStatus_Save
    );
  }, [entry.eDMPEnrollmentInfo]);

  const isPrinted = useMemo(() => {
    return (
      entry.eDMPEnrollmentInfo?.participationForm.participationFormsStatus ===
      ParticipationFormsStatus.ParticipationFormsStatus_Print
    );
  }, [entry.eDMPEnrollmentInfo]);

  const onCustomActions = useCallback(
    async (
      formName: string,
      participationFormsStatus: ParticipationFormsStatus
    ) => {
      const cloneData = {
        ...(store.currentFormSetting || {}),
      };
      const currentFormSetting = JSON.stringify(cloneDeep(cloneData));
      const isPrinted = [
        ParticipationFormsStatus.ParticipationFormsStatus_Print,
      ].includes(participationFormsStatus);

      if (isPrinted) {
        musterFormDialogStore.isLoadingPrescribe = true;

        const resp = await printPlainPdf({
          formSetting: currentFormSetting,
          formName,
          treatmentDoctorId: entry.treatmentDoctorId,
        });

        PrinterService.initAndPrint(
          formName,
          async () => {
            return resp.formUrl;
          },
          {
            printSuccess: async () => {
              alertSuccessfully(tParticipationForms('printSuccess'));
              setOpenParticipationForm(false);
              musterFormDialogActions.clear();
              musterFormDialogStore.isLoadingPrescribe = false;
            },
            printFailure: () => {
              musterFormDialogStore.isLoadingPrescribe = false;
            },
          }
        );

        onEditTimelineItem(
          {
            ...entry,
            eDMPEnrollmentInfo: {
              ...entry.eDMPEnrollmentInfo,
              participationForm: {
                ...entry.eDMPEnrollmentInfo?.participationForm,
                participationFormsStatus:
                  ParticipationFormsStatus.ParticipationFormsStatus_Print,
              } as ParticipationForm,
            } as EnrollmentInfo,
          },
          () => {},
          () => {}
        );
      } else {
        setOpenParticipationForm(false);
      }
    },
    [store.currentFormSetting]
  );

  const handleViewForm = () => {
    setOpenParticipationForm(true);
    musterFormDialogActions.setCurrentFormName(matchedDMP.enrolFile);
    musterFormDialogActions.setViewForm(true);

    const formSetting = entry.eDMPEnrollmentInfo.participationForm.formSetting;
    if (formSetting) {
      musterFormDialogActions.setCurrentMusterFormSetting({
        ...JSON.parse(formSetting),
      });
    }
  };

  useEffect(() => {
    if (treatmentDoctor) {
      musterFormDialogActions.setCurrentTreatmentDoctor({
        value: `${treatmentDoctor.id}-${treatmentDoctor.bsnrId}`,
        label: `${treatmentDoctor.title || ''} ${
          treatmentDoctor.fullName
        }`.trim(),
        data: treatmentDoctor,
      });
    }
  }, [treatmentDoctor]);

  if (!entry.eDMPEnrollmentInfo) {
    return null;
  }

  return (
    <>
      <Flex className={className} column onDoubleClick={handleViewForm}>
        <Flex align="center">
          <Flex align="center" grow={1}>
            <BodyTextL margin="0 8px 0 0">{textDMP}</BodyTextL>
            <Tag slStyle="fill" slSize="small" slState="positive">
              {t('enrolled')}
            </Tag>
            {(isSaved || isPrinted) && (
              <Tag
                slStyle="fill"
                slSize="small"
                slState={isSaved ? 'info' : 'neutral'}
              >
                {tCommon(isSaved ? 'Saved' : 'Printed')}
              </Tag>
            )}
          </Flex>
          <TimelineDeletionRemain entry={entry} />
          {doctorIcon}
          <TimelineActionHOC>
            <Popover
              className="actions-group"
              content={
                <Menu className="action-menu">
                  <MenuItem
                    key="1"
                    icon={
                      <Svg
                        className="timeline-entry_row_menu-icon"
                        src={EyeIcon}
                      />
                    }
                    text={t('viewParticipationForm')}
                    onClick={handleViewForm}
                  />
                </Menu>
              }
            >
              <Tooltip content={tCommon('More')} position="top">
                <Svg className="more-icon" src={MoreIcon} />
              </Tooltip>
            </Popover>
          </TimelineActionHOC>
        </Flex>
      </Flex>
      <MusterFormDialog
        isDMPEnrollmentForm
        hasSaveButton={false}
        selectedContractDoctor={null}
        patient={patient}
        isOpen={isOpenParticipationForm}
        onClose={() => {
          setOpenParticipationForm(false);
          musterFormDialogActions.setCurrentFormName('');
        }}
        onCustomActions={onCustomActions}
      >
        <FormGroup2 label={tParticipationForms('doctor')}>
          <ReactSelect
            selectedValue={treatmentDoctor?.id}
            items={(treatmentDoctor ? [treatmentDoctor] : []).map((doctor) => ({
              label: `${doctor.title || ''} ${doctor.fullName}`.trim(),
              value: doctor.id,
            }))}
            isDisabled
          />
        </FormGroup2>
      </MusterFormDialog>
    </>
  );
};

export default memo(DMPEnrolledEntry);
