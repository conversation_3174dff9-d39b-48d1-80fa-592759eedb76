
import type { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type DoctorLetterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';

import { memo, useMemo, useCallback, useState, useContext } from 'react';
import i18n from '@tutum/infrastructure/i18n';
import {
  BodyTextL,
  Flex,
  Svg,
  MessageBar,
  alertError,
} from '@tutum/design-system/components';
import {
  Menu,
  Popover,
  Tooltip,
  MenuItem,
} from '@tutum/design-system/components/Core';
import { Tag } from '@tutum/design-system/components/Tag';
import ChangeItemDateMenuItem from '../ChangeItemDateMenuItem';
import { DEFAULT_LANGUAGES } from '@tutum/mvz/module_patient-management/create-patient-v2/g81EHICInfo/G81EHICInfo';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import {
  musterFormDialogActions,
  musterFormDialogStore,
  useMusterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import {
  EuropeanHealthInsuranceStatus,
  UserType,
} from '@tutum/hermes/bff/common';
import { cloneDeep } from 'lodash';
import { printPlainPdf } from '@tutum/hermes/bff/app_mvz_form';
import PrinterService from '@tutum/mvz/services/printer.service';
import { EditResponse } from '@tutum/hermes/bff/app_mvz_timeline';
import { musterFormActions } from '@tutum/mvz/module_form/muster-form/musterForm.store';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import PatientProfileCreation from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { updatePatientProfileV2 } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import Guard from '@tutum/mvz/hooks/Guard';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

export interface G81EHICEntryProps {
  className?: string;
  entry: TimelineModel;
  doctorIcon: JSX.Element;
  setItemToEditDate?: () => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  onRemoveEntry: (hardDelete?: boolean) => void;
}

const MoreIcon = '/images/more.svg';
const EyeIcon = '/images/eye-on.svg';
const Edit2Icon = '/images/edit-2.svg';
const TrashBinRedIcon = '/images/trash-bin-red.svg';

const G81EHICEntry = ({
  className,
  entry,
  doctorIcon,
  setItemToEditDate,
  onEditTimelineItem,
  onRemoveEntry,
}: G81EHICEntryProps) => {
  const { t } = i18n.useTranslation<
    keyof typeof PatientManagementI18n.Timeline
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Timeline',
  });
  const { t: tTimeLineFormDetail } = i18n.useTranslation<
    keyof typeof PatientManagementI18n.TimeLineFormDetail
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimeLineFormDetail',
  });
  const { t: tDoctorLetter } = i18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({
    namespace: 'DoctorLetter',
  });

  const { t: tG81EHICInformation } = i18n.useTranslation<
    keyof typeof PatientProfileCreation.G81EHICInformation
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'G81EHICInformation',
  });

  const store = useMusterFormDialogStore();
  const [isOpenForm, setOpenForm] = useState<boolean>(false);
  const { patientManagement, setPatient } = useContext(
    PatientManagementContext.instance
  );

  const encounterEHIC = entry.encounterEHIC;

  const isSaved = useMemo(() => {
    return (
      encounterEHIC?.status ===
      EuropeanHealthInsuranceStatus.EuropeanHealthInsuranceStatus_Saved
    );
  }, [encounterEHIC]);

  const handlePrescribe = useCallback(() => {
    if (!isSaved) {
      musterFormDialogActions.setViewForm(true);
    }

    musterFormDialogActions.setCurrentMusterFormSetting(
      JSON.parse(encounterEHIC.formSetting)
    );
    musterFormDialogActions.setCurrentFormName(
      `G81_EHIC_${encounterEHIC.language}`
    );
    setOpenForm(true);
  }, [encounterEHIC]);

  const onCustomActions = useCallback(
    async (
      language: string,
      status: EuropeanHealthInsuranceStatus,
      callback: () => void
    ) => {
      const formSetting = JSON.stringify(cloneDeep(store.currentFormSetting));
      const isPrinted = [
        EuropeanHealthInsuranceStatus.EuropeanHealthInsuranceStatus_Printed,
      ].includes(status);

      if (isSaved) {
        musterFormDialogStore.isLoadingPrescribe = true;

        onEditTimelineItem(
          {
            ...entry,
            encounterEHIC: {
              formSetting,
              language,
              status,
            },
          },
          async () => {
            const clonedPatient = cloneDeep(patientManagement.patient);
            clonedPatient.patientInfo.europeanHealthInsurance = {
              ...clonedPatient.patientInfo.europeanHealthInsurance,
              formSetting,
              status,
              language,
            };
            await updatePatientProfileV2({
              ...clonedPatient,
              isEditG81EHIC: true,
            });
            setPatient(clonedPatient);
            musterFormDialogActions.clear();
            musterFormDialogStore.isLoadingPrescribe = false;
            setOpenForm(false);
            callback();
          },
          (err) => {
            musterFormDialogStore.isLoadingPrescribe = false;
            alertError(err?.message);
          }
        );
      }

      if (isPrinted) {
        musterFormDialogStore.isLoadingPrescribe = true;

        const resp = await printPlainPdf({
          formSetting,
          formName: `G81_EHIC_${language}`,
          treatmentDoctorId: store.doctor?.data?.id,
        });

        PrinterService.initAndPrint(
          `G81_EHIC_${language}`,
          async () => {
            return resp.formUrl;
          },
          {
            printSuccess: async () => {
              musterFormDialogActions.clear();
              setOpenForm(false);
              callback?.();
              musterFormDialogStore.isLoadingPrescribe = false;
            },
            printFailure: () => {
              musterFormDialogStore.isLoadingPrescribe = false;
            },
          }
        ).finally(() => {
          musterFormDialogStore.isLoadingPrescribe = false;
        });
      }
    },
    [
      store.currentFormSetting,
      store.doctor,
      encounterEHIC.status,
      isSaved,
      patientManagement.patient,
    ]
  );

  if (!encounterEHIC) {
    return null;
  }

  return (
    <>
      <Flex className={className} column onDoubleClick={handlePrescribe}>
        <Flex align="center">
          <Flex align="center" grow={1}>
            <BodyTextL margin="0 8px 0 0">{t('g81EHICEntryText')}</BodyTextL>
            <Tag
              slStyle="fill"
              slSize="small"
              slState={isSaved ? 'info' : 'neutral'}
            >
              {tDoctorLetter(isSaved ? 'saved' : 'printed')}
            </Tag>
          </Flex>
          <TimelineDeletionRemain entry={entry} />
          {doctorIcon}
          <TimelineActionHOC>
            <Popover
              className="actions-group"
              content={
                <Menu className="action-menu">
                  <ChangeItemDateMenuItem
                    key="0"
                    text={t('editEntryDate')}
                    onClick={setItemToEditDate}
                  />
                  <MenuItem
                    key="1"
                    icon={
                      <Svg
                        className="timeline-entry_row_menu-icon"
                        src={isSaved ? Edit2Icon : EyeIcon}
                      />
                    }
                    text={tTimeLineFormDetail(
                      isSaved ? 'editForm' : 'viewForm'
                    )}
                    onClick={handlePrescribe}
                  />
                  <Guard roles={[UserType.MANAGER]} key="remove">
                    <MenuItem
                      key="2"
                      icon={
                        <Svg
                          className="timeline-entry_row_menu-icon"
                          src={TrashBinRedIcon}
                        />
                      }
                      text={tDoctorLetter('remove')}
                      onClick={() => onRemoveEntry(false)}
                    />
                  </Guard>
                </Menu>
              }
            >
              <Tooltip content={t('more')} position="top">
                <Svg className="more-icon" src={MoreIcon} />
              </Tooltip>
            </Popover>
          </TimelineActionHOC>
        </Flex>
        {isSaved && (
          <Flex align="center">
            <MessageBar
              hasBullet={false}
              type="warning"
              content={t('g81EHICEntryHint')}
            />
          </Flex>
        )}
        <MusterFormDialog
          languages={DEFAULT_LANGUAGES.map((language) => ({
            value: `G81_EHIC_${language}`,
            label:
              language === 'All' ? tG81EHICInformation('printAll') : language,
          }))}
          patient={null}
          selectedContractDoctor={null}
          isOpen={isOpenForm && !!store.currentFormName}
          onClose={() => {
            musterFormDialogActions.setCurrentFormName('');
          }}
          onChangeLanguage={(formName: string) => {
            musterFormActions.setHasSetFormAnnotation(false);
            musterFormDialogActions.setCurrentFormName(formName);
          }}
          onCustomActions={onCustomActions}
        />
      </Flex>
    </>
  );
};

export default memo(G81EHICEntry);
