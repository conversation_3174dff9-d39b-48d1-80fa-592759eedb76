
import React from 'react';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import Theme from '@tutum/mvz/theme';
import OriginalMedicineEntry, { IMedicineEntryProps } from './MedicineEntry';
import TimelineService from '../../../Timeline.service';

const styled = Theme.styled;
const MedicineEntry: React.ComponentType<IMedicineEntryProps> = styled(
  OriginalMedicineEntry
).attrs(({ className }) => ({
  className: getCssClass('sl-MedicineEntry', className),
}))`
  padding: 0 8px;

  .sl-medicine-text {
    color: ${(props) =>
      TimelineService.parseEntryColor(props.entry, props.theme.timelineTheme)
        ?.text};
  }
`;

export default MedicineEntry;
