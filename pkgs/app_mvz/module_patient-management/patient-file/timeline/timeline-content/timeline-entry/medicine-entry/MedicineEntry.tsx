import { memo, useMemo } from 'react';
import {
  Flex,
  BodyTextM,
  Tooltip,
  Svg,
  TagMedicine,
} from '@tutum/design-system/components';
import { Popover, Menu, MenuItem } from '@tutum/design-system/components/Core';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import { medicationUtil } from '@tutum/mvz/module_medication/utils/medication-util';
import { flatten } from '@tutum/design-system/infrastructure/utils';
import { FormType, Medicine } from '@tutum/hermes/bff/legacy/medicine_common';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import ChangeItemDateMenuItem from '../ChangeItemDateMenuItem';
import { MenuItemClasses } from '@tutum/design-system/components/Core/MenuItem';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import Guard from '@tutum/mvz/hooks/Guard';
import { UserType } from '@tutum/infrastructure/resource/AdminResource';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import HighlightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';

export interface IMedicineEntryProps {
  className?: string;
  entry: TimelineModel;
  doctorIcon: JSX.Element;
  setItemToEditDate?: () => void;
  onRemoveEntry: (hardDelete?: boolean, cb?: Function) => void;
}

const TrashIcon = '/images/trash-bin-red.svg';
const MoreIcon = '/images/more.svg';

export const getMedicineString = (medicine: Medicine) => {
  const componentsAndSubtants = medicationUtil.getComponentsAndSubstants(
    medicine?.drugInformation
  );

  const substantStr = flatten(
    componentsAndSubtants.map((item) => item.substances)
  ).join(', ');
  const packageSize = medicationUtil.getPackageSize(
    medicine?.packagingInformation
  );

  let str = `1 x ${medicine?.productInformation?.name
    } ${substantStr} ${medicationUtil.getDosageForm(
      medicine?.productInformation!
    )}`;

  if (packageSize.length > 0) {
    str = `${str} • ${packageSize}`;
  }

  return str;
};

const MedicineEntry = (props: IMedicineEntryProps) => {
  const { className, entry, doctorIcon, setItemToEditDate, onRemoveEntry } =
    props;
  const medicine = entry?.encounterMedicine;
  const { matchedTokens } = useTimeLineStore();
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimeLineFormDetail
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimeLineFormDetail',
  });
  const { t: tDoctorSample } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DoctorSample
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DoctorSample',
  });

  const medicineText = useMemo(() => {
    return getMedicineString(medicine!);
  }, [medicine]);

  const renderActionMenu = useMemo(() => {
    return (
      <Menu className="sl-action-menu">
        <ChangeItemDateMenuItem onClick={setItemToEditDate} />
        <Guard roles={[UserType.MANAGER]} key="remove">
          <MenuItem
            className={MenuItemClasses.Danger}
            key="removeForm"
            icon={<Svg src={TrashIcon} />}
            text={t('removeForm')}
            onClick={() => onRemoveEntry(false)}
          />
        </Guard>
      </Menu>
    );
  }, [setItemToEditDate, onRemoveEntry]);

  const renderFormLabel = useMemo(() => {
    if (medicine?.productInformation?.sampleProductFlag) {
      return (
        <TagMedicine className="sl-info">
          {tDoctorSample('doctorSample')}
        </TagMedicine>
      );
    }

    const formType = medicine?.productInformation?.formType;

    switch (formType) {
      case FormType.GREZ:
        return <TagMedicine className="sl-g-rez">{t('gRez')}</TagMedicine>;
      case FormType.KREZ:
        return <TagMedicine className="sl-k-rez">{t('kRez')}</TagMedicine>;
      case FormType.Private:
        return (
          <TagMedicine className="sl-private">{t('tagPrivate')}</TagMedicine>
        );
      case FormType.BTM:
        return <TagMedicine className="sl-btm-rez">{t('btm')}</TagMedicine>;
      default:
        return null;
    }
  }, [medicine?.productInformation]);

  return (
    <Flex className={className} column gap={4}>
      <Flex justify="space-between" align="center">
        {renderFormLabel}
        <Flex>
          <TimelineDeletionRemain entry={entry} />
          <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
          <TimelineActionHOC>
            <Flex className="actions-group">
              <Popover content={renderActionMenu}>
                <Tooltip content={t('more')} position="top">
                  <Svg className="more-icon" src={MoreIcon} />
                </Tooltip>
              </Popover>
            </Flex>
          </TimelineActionHOC>
        </Flex>
      </Flex>
      <Flex>
        <HighlightWrapper matchedTokens={matchedTokens}>
          <BodyTextM className="sl-medicine-text hover-text">
            {medicineText}
          </BodyTextM>
        </HighlightWrapper>
      </Flex>
    </Flex>
  );
};

export default memo(MedicineEntry);
