import cloneDeep from 'lodash/cloneDeep';
import React, { memo, useContext, useMemo, useState } from 'react';

import type EDocumentsI18n from '@tutum/mvz/locales/en/EDocuments.json';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';

import {
  alertError,
  alertSuccessfully,
  BodyTextS,
  Flex,
  Svg,
  Tooltip,
} from '@tutum/design-system/components';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import type { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  getCssClass,
  isNullUUID,
} from '@tutum/design-system/infrastructure/utils';
import type {
  CreateResponse,
  EditResponse,
  GroupByQuarter,
  RemoveRequest,
  RemoveResponse,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { ArribaStatus } from '@tutum/hermes/bff/arriba_common';
import { CodingRuleSuggestion } from '@tutum/hermes/bff/coding_rule_common';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  DocumentStatus,
  EnrollmentDocumentInfoModel,
} from '@tutum/hermes/bff/edmp_common';
import { Form } from '@tutum/hermes/bff/form_common';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { EncounterCase } from '@tutum/hermes/bff/service_domains_patient_file';
import {
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import I18n, { default as i18n } from '@tutum/infrastructure/i18n';
import { checkIsKvSchein } from '@tutum/mvz/_utils/scheinFormat';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import useToaster from '@tutum/mvz/hooks/useToaster';
import type { FORM_SECTION } from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import PatientManagementContext from '@tutum/mvz/module_patient-management//contexts/patient-management/PatientManagementContext';
import { ServiceChainCommand } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.const';
import { toComposerRowType } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.service';
import Composer from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.styled';
import type { IComposerRow } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.type';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import DocumentEntry from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/document-entry/DocumentEntry.styled';
import GDTEntry from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/gdt-entry/GDTEntry.styled';
import LDTEntry from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/ldt-entry/LDTEntry.styled';
import ServiceChainEntry from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/service-chain-entry/ServiceChainEntry.styled';
import { SourceScreen } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.hooks';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { getDataTimeline } from '@tutum/mvz/module_patient-management/patient-file/timeline/utils';
import type { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import CheckedIcon from '@tutum/mvz/public/images/check-circle-solid-success.svg';
import type { IMvzTheme } from '@tutum/mvz/theme';
import { AppointmentEntry } from './appointment-entry';
import ArribaEntry from './arriba-entry/ArribaEntry.styled';
import BillingDoctorInfo from './billing-doctor-info/BillingDoctorInfo.styled';
import CalendarEntry from './calendar-entry/CalendarEntry.styled';
import CustomizeEntry from './customize-entry/CustomizeEntry.styled';
import DiagnoseEntry from './diagnose-entry/DiagnoseEntry.styled';
import DigaEntry from './diga-entry/DigaEntry.styled';
import { DMPEnrolledEntry } from './dmp-enrolled-entry';
import { DMPEntry } from './dmp-entry';
import { DoctorLetterEntry } from './doctor-letter-entry';
import EntryStatus from './entry-status/EntryStatus.styled';
import FormEntry from './form-entry/FormEntry.styled';
import FreetextEntry from './freetext-entry/FreetextEntry.styled';
import { G81EHICEntry } from './g81-ehic-entry';
import GoaChainEntry from './goa-service-chain-entry/GoaServiceChainEntry.styled';
import GoaEntry from './goa-service-entry/GoaServiceEntry.styled';
import HeimiPrescription from './heimi-prescription/HeimiPrescription.styled';
import HimiPrescription from './himi-prescription/HimiPrescription.styled';
import LabEntry from './lab-entry/LabEntry.styled';
import { MailEntry } from './mail-entry';
import MedicationPlanHistoryEntry from './medication-plan-history-entry/MedicationPlanHistoryEntry.styled';
import MedicationPrescription from './medication-prescription/MedicationPrescription.styled';
import MedicineEntry from './medicine-entry';
import { PatientBillEntry } from './patient-bill-entry';
import Pyschotherapy from './psychotherapy-entry/Psychotherapy.styled';
import ServiceEntry from './service-entry/ServiceEntry.styled';
import {
  getBillingInfo,
  isCheckBilledPrescriptionMedicineWithHzvOrFav,
  PayLoadHistoryMode,
} from './util';
import UvGoaChainEntry from './uv-goa-service-chain-entry/UvGoaServiceChainEntry.styled';
import UvGoaEntry from './uv-goa-service-entry/UvGoaServiceEntry.styled';
import type { Paths } from '@tutum/infrastructure/i18n/i18n.context';

const editIcon = '/images/encounter/save-info.svg';
const ActionIcon = '/images/action-icon.svg';
const SendBase = '/images/encounter/send-base.svg';
const escIcon = '/images/esc-icon.svg';

export interface ITimelineEntryProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  treatmentDoctorId?: string;
  patientId?: string;
  contract: IContractInfo;
  quarter: GroupByQuarter;
  diagnoseSuggestions: CodingRuleSuggestion[];
  sourceScreen: SourceScreen;
  keyword?: string;
  setItemToEditDate?: () => void;
  date?: string;
  onCreateTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: CreateResponse) => void,
    handleError: (error) => void
  ) => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  onDeleteTimelineItem: (
    data: RemoveRequest,
    handleSuccess: (result: RemoveResponse) => void,
    handleError: (error) => void
  ) => void;
  reloadQuarters: ReloadQuarterFunc;
  payLoadHistoryMode: PayLoadHistoryMode;
  isHistoryMode: boolean;
  listForms: Form[];
  fetchSuggestionList: () => void;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
  openEditSchein: (scheinId?: string, scrollToSection?: FORM_SECTION) => void;
}

const disabledEntryTypesAfterBilled = [
  TimelineEntityType.TimelineEntityType_Diagnose,
  TimelineEntityType.TimelineEntityType_Diagnose_AD,
  TimelineEntityType.TimelineEntityType_Diagnose_DD,
  TimelineEntityType.TimelineEntityType_Service,
  TimelineEntityType.TimelineEntityType_Service_Chain,
  TimelineEntityType.TimelineEntityType_Service_GOA,
  TimelineEntityType.TimelineEntityType_Service_UV_GOA,
  TimelineEntityType.TimelineEntityType_Service_GOA_Chain,
  TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain,
];

export interface ITimelineEntryError {
  id: string;
  type: ITimelineEntryStatus;
  message: string;
}

export type ITimelineEntryStatus = 'error' | 'warning' | 'info';

interface IContentTooltip {
  text: string;
  src: string;
}

const ContentTooltip = (props: IContentTooltip) => {
  const { text, src } = props;
  return (
    <Flex gap={4} className="sl-content-tooltip">
      <BodyTextS>{text}</BodyTextS>
      <Svg src={src} />
    </Flex>
  );
};

const TimelineEntry = (props: ITimelineEntryProps) => {
  const { t: tTimeline } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Timeline
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Timeline',
  });
  const { t: t2 } = I18n.useTranslation<Paths<typeof ScheinI18n>>({
    namespace: 'Schein',
  });
  const {
    className,
    entry,
    date,
    contract,
    patientId,
    quarter,
    diagnoseSuggestions,
    listForms,
    keyword,
    setItemToEditDate,
    onEditTimelineItem,
    onDeleteTimelineItem,
    reloadQuarters,
    payLoadHistoryMode,
    openCreateSchein,
    openEditSchein,
  } = props;

  const entryID = entry?.id;
  const isChain =
    !!entry?.encounterGoaServiceChain ||
    !!entry?.encounterServiceChain ||
    !!entry?.encounterUvGoaServiceChain;
  const context = useContext(PatientManagementContext.instance);
  const { getDoctorName, getDoctorInitial, globalData } =
    GlobalContext.useContext();
  const { timelineDeletionPeriodDays } = useSettingStore();
  const [isOpenConfirmRemove, setIsOpenConfirmRemove] = useState(false);
  const [isEditInline, setIsEditInline] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoadingConfirmDialog, setLoadingConfirmDialog] = useState(false);
  const [cbFunction, setCbFunction] = useState<Nullable<Function>>(undefined);
  const [isERezept, setERezept] = useState(false);
  const [currentBlock, setCurrentBlock] =
    useState<Partial<IComposerRow> | null>(null);
  const currentUser = globalData.userProfile;

  const toast = useToaster();

  const billingInfo = getBillingInfo(
    patientFileStore.schein.originalList,
    entry,
    getDoctorName
  );

  const { isLoadingRemoveERezept, isHistoryMode } = useTimeLineStore();

  const { t: tConfirmDialog } = i18n.useTranslation<
    keyof typeof EDocumentsI18n.ConfirmDialog
  >({
    namespace: 'EDocuments',
    nestedTrans: 'ConfirmDialog',
  });

  const editModeCssClass = isEditing ? 'edit-mode' : '';
  const isSelected = false;

  const currentSchein = useMemo(() => {
    let schein = patientFileStore.schein.list.find(
      (schein) => schein.scheinId === entry.scheinIds?.[0]
    );
    // Get original to show in timeline entry
    if (!schein) {
      const temp = patientFileStore.schein.originalList.find(
        (schein) => schein.scheinId === entry.scheinIds?.[0]
      );
      if (temp?.isTechnicalSchein) {
        schein = temp;
      }
    }
    return schein;
  }, [
    patientFileStore.schein.list,
    patientFileStore.schein.originalList,
    entry,
  ]);

  const setEditInline = () => {
    const hasBilled = billingInfo?.billingSubmitted;
    if (
      !hasBilled &&
      !currentSchein?.isTechnicalSchein &&
      !isHistoryMode &&
      !entry?.isImported
    ) {
      setIsEditInline(true);
    }
  };

  const handleCloseEditInline = () => {
    setIsEditInline(false);
  };

  const toComposerDefaultValue = (
    timelineEntry: TimelineModel
  ): Partial<IComposerRow> => {
    // Keep original return type
    const entry = cloneDeep(timelineEntry);
    const data = getDataTimeline(entry);
    const newType = toComposerRowType(entry);

    if (data) {
      const freeText = data?.note || data?.code || '';
      let treatmentDoctorId = !isNullUUID(entry?.treatmentDoctorId)
        ? entry?.treatmentDoctorId
        : null;

      //if treatmentDoctorId is null we set it to current user
      //this is using for GDT and LDT during edit submit
      if (!treatmentDoctorId && currentUser && currentUser.id) {
        treatmentDoctorId = currentUser.id;
      }

      return {
        ...(data ?? {}),
        ...(data.services?.[0] ||
          data.goaServices?.[0] ||
          data.uvGoaServices?.[0] ||
          {}),
        id: data.id,
        type: newType,
        command: data.command ? data.command : ServiceChainCommand,
        freeText:
          data?.note ||
          `${freeText ? `(${freeText})` : ''} ${data?.description}`,
        auditLog: data.auditLog,
        encounterCase: entry?.encounterCase,
        encounterDate: entry?.selectedDate,
        treatmentDoctorId: treatmentDoctorId ?? undefined,
        bsnrId: entry?.assignedToBsnrId,
        createdAt: entry?.createdAt,
      };
    }
    return {};
  };

  const handleCloseModal = () => {
    setERezept(false);
    setIsOpenConfirmRemove(false);
  };

  const deleteEntry = (id: string, isChain = false, hasHardDelete = false) => {
    setLoadingConfirmDialog(true);
    onDeleteTimelineItem(
      { timelineId: id, hasHardDelete, isChain },
      () => {
        alertSuccessfully(tTimeline('entryRemove'), { toaster: toast });
        setLoadingConfirmDialog(false);
        handleCloseModal();
      },
      () => {
        alertError(tTimeline('entryRemoveFailed'), { toaster: toast });
      }
    );
  };
  // hardDelete: if true will call deleteEntry(entryID, true)
  // cb: the callback function will do later after choose delete on confirm model
  // if have cb function will call cb function, if not call deleteEntry(entryID)
  // ex: deletePrescription on TimeLineFormDetail.tsx

  const onRemoveEntry = (hardDelete = false, cb?: Function) => {
    if (!hardDelete) {
      setIsOpenConfirmRemove(true);
    }
    if (cb) {
      setCbFunction(
        cb.constructor.name !== 'SyntheticEvent' &&
          cb.constructor.name !== 'SyntheticBaseEvent'
          ? () => cb
          : null
      );
    }
  };

  const isBilledByPrescriptionOfHzvOrHav = useMemo(() => {
    return isCheckBilledPrescriptionMedicineWithHzvOrFav(
      patientFileStore.schein.originalList,
      entry
    );
  }, [patientFileStore.schein.originalList, entry, quarter]);

  const renderStatus = (isBilled = false) => {
    const auditLogs = [
      entry?.auditLogs[0],
      entry?.auditLogs[1] && entry?.auditLogs[entry?.auditLogs.length - 1],
    ];
    const treatmentDoctorName = getDoctorName(entry?.createdBy || '');
    let treatmentDoctorInitial = getDoctorInitial(entry?.createdBy || '');
    const billingInfo = getBillingInfo(
      patientFileStore.schein.originalList,
      entry,
      getDoctorName,
      true
    );

    let availableEdit = false;
    let isShowBillingInfo = false;

    if (entry.type) {
      availableEdit = [
        TimelineEntityType.TimelineEntityType_Diagnose,
        TimelineEntityType.TimelineEntityType_Service,
        TimelineEntityType.TimelineEntityType_Note,
        TimelineEntityType.TimelineEntityType_Service_GOA,
        TimelineEntityType.TimelineEntityType_Service_Chain,
        TimelineEntityType.TimelineEntityType_Service_GOA_Chain,
        TimelineEntityType.TimelineEntityType_Service_UV_GOA,
        TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain,
        TimelineEntityType.TimelineEntityType_Finding,
        TimelineEntityType.TimelineEntityType_Therapy,
        TimelineEntityType.TimelineEntityType_GDT,
        TimelineEntityType.TimelineEntityType_Customize,
        TimelineEntityType.TimelineEntityType_EDOKU_Document,
      ].includes(entry.type);
      isShowBillingInfo = [
        TimelineEntityType.TimelineEntityType_Diagnose,
        TimelineEntityType.TimelineEntityType_Service,
        TimelineEntityType.TimelineEntityType_Service_GOA,
        TimelineEntityType.TimelineEntityType_BillingPatient,
        TimelineEntityType.TimelineEntityType_Service_Chain,
        TimelineEntityType.TimelineEntityType_Service_GOA_Chain,
        TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain,
        TimelineEntityType.TimelineEntityType_Service_UV_GOA,
      ].includes(entry.type);
    }

    if (isNullUUID(entry.createdBy)) {
      treatmentDoctorInitial = 'E';
    }

    const caculateBilled = () => {
      if (isBilledByPrescriptionOfHzvOrHav) {
        return true;
      }

      if (
        billingInfo?.billingSubmitted &&
        entry.encounterMedicinePrescription
      ) {
        return false;
      }

      return !!billingInfo?.billingSubmitted;
    };

    return (
      <Flex className="entry-avatar">
        {isBilled ? (
          caculateBilled() ? (
            <EntryStatus
              doctorName={treatmentDoctorName}
              auditLogs={auditLogs}
              billingSubmittedUserName={billingInfo.billingSubmittedUserName}
              billingSubmittedDate={
                billingInfo.billingSubmittedDate || undefined
              }
              billingSubmitted
              billingSubmittedPrivate={billingInfo.billingSubmittedPrivate}
              showBillingIcon={true}
              isBilled
              treatmentDoctorInitial={treatmentDoctorInitial}
            />
          ) : availableEdit ? undefined : (
            <div className="non_editable"></div>
          )
        ) : (
          <EntryStatus
            doctorName={treatmentDoctorName}
            auditLogs={auditLogs}
            billingSubmittedUserName=""
            billingSubmitted={false}
            billingSubmittedPrivate={false}
            showBillingIcon={false}
            isBilled={false}
            treatmentDoctorInitial={treatmentDoctorInitial}
          />
        )}
        {!isBilled && isShowBillingInfo && (
          <BillingDoctorInfo entry={entry} schein={currentSchein} />
        )}
      </Flex>
    );
  };

  const onEdit = (isEditing: boolean) => setIsEditing(isEditing);

  const renderContent = (doctorIcon: JSX.Element, hasBilled: boolean) => {
    switch (entry?.type) {
      case TimelineEntityType.TimelineEntityType_Diagnose:
        return (
          <DiagnoseEntry
            keyword={keyword}
            entry={entry}
            onEdit={onEdit}
            patientId={patientId}
            isEditing={isEditing}
            hasBilled={hasBilled}
            contract={contract}
            doctorIcon={doctorIcon}
            quarter={quarter}
            diagnoseSuggestions={diagnoseSuggestions}
            currentSchein={currentSchein}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
            openCreateSchein={openCreateSchein}
            reloadQuarters={reloadQuarters}
            sourceScreen={props.sourceScreen}
          />
        );
      case TimelineEntityType.TimelineEntityType_Service:
        if (!context?.patientManagement?.patient) {
          return null;
        }
        return (
          <ServiceEntry
            entry={entry}
            onEdit={onEdit}
            patientId={patientId}
            isEditing={isEditing}
            contract={contract}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            quarter={quarter}
            patient={context.patientManagement.patient}
            currentSchein={currentSchein}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
            reloadQuarters={reloadQuarters}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
            openCreateSchein={openCreateSchein}
          />
        );
      case TimelineEntityType.TimelineEntityType_Service_GOA:
        if (!context?.patientManagement?.patient) {
          return null;
        }
        return (
          <GoaEntry
            keyword={keyword}
            entry={entry}
            onEdit={onEdit}
            patientId={patientId}
            isEditing={isEditing}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            quarter={quarter}
            patient={context?.patientManagement?.patient}
            currentSchein={currentSchein}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
            openCreateSchein={openCreateSchein}
          />
        );
      case TimelineEntityType.TimelineEntityType_Service_UV_GOA:
        if (!context?.patientManagement?.patient) {
          return null;
        }
        return (
          <UvGoaEntry
            entry={entry}
            onEdit={onEdit}
            patientId={patientId}
            isEditing={isEditing}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            quarter={quarter}
            patient={context?.patientManagement?.patient}
            currentSchein={currentSchein}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
            openCreateSchein={openCreateSchein}
          />
        );
      case TimelineEntityType.TimelineEntityType_Service_GOA_Chain:
        if (!context?.patientManagement?.patient) {
          return null;
        }
        return (
          <GoaChainEntry
            className="sl-GoaServiceChainEntry"
            keyword={keyword}
            entry={entry}
            onEdit={onEdit}
            patientId={patientId}
            isEditing={isEditing}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            quarter={quarter}
            patient={context.patientManagement?.patient}
            currentSchein={currentSchein}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
            openCreateSchein={openCreateSchein}
          />
        );
      case TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain:
        if (!context?.patientManagement?.patient) {
          return null;
        }
        return (
          <UvGoaChainEntry
            className="sl-UvGoaServiceChainEntry"
            keyword={keyword}
            entry={entry}
            onEdit={onEdit}
            patientId={patientId}
            isEditing={isEditing}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            quarter={quarter}
            patient={context.patientManagement?.patient}
            currentSchein={currentSchein}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
            openCreateSchein={openCreateSchein}
          />
        );
      case TimelineEntityType.TimelineEntityType_MedicinePlan:
        if (!patientId) {
          return null;
        }
        return (
          <MedicationPlanHistoryEntry
            entry={entry}
            contract={contract}
            patientId={patientId}
            doctorIcon={doctorIcon}
            setItemToEditDate={setItemToEditDate}
            keyword={keyword}
          />
        );
      case TimelineEntityType.TimelineEntityType_MedicinePrescription: {
        const eRezeptMedicine =
          entry.encounterMedicinePrescription?.formInfos[0]?.medicines?.[0] ??
          null;
        if (!patientId) {
          return null;
        }

        return eRezeptMedicine?.erezeptItemStatus &&
          eRezeptMedicine.isDeleted ? null : (
          <MedicationPrescription
            entry={entry}
            contract={contract}
            patientId={patientId}
            doctorIcon={doctorIcon}
            setItemToEditDate={setItemToEditDate}
            scheins={patientFileStore.schein.originalList}
            onRemoveEntry={onRemoveEntry}
            onEditTimelineItem={onEditTimelineItem}
            setERezept={setERezept}
            keyword={keyword}
          />
        );
      }

      case TimelineEntityType.TimelineEntityType_HimiPrescription:
        if (!patientId) {
          return null;
        }
        return (
          <HimiPrescription
            entry={entry}
            contract={contract}
            patientId={patientId}
            doctorIcon={doctorIcon}
            listForms={listForms}
            setItemToEditDate={setItemToEditDate}
            onDeleteTimelineItem={onDeleteTimelineItem}
            keyword={keyword}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_HeimiPrescription: {
        if (!patientId || !context.patientManagement.patient?.dateOfBirth) {
          return null;
        }
        return (
          <HeimiPrescription
            entry={entry}
            contract={contract}
            patientId={patientId}
            patientDateOfBirth={context.patientManagement.patient.dateOfBirth}
            doctorIcon={doctorIcon}
            listForms={listForms}
            setItemToEditDate={setItemToEditDate}
            onRemoveEntry={onRemoveEntry}
          />
        );
      }
      case TimelineEntityType.TimelineEntityType_Lab:
        return (
          <LabEntry
            contract={contract}
            entry={entry}
            doctorIcon={doctorIcon}
            setItemToEditDate={setItemToEditDate}
          />
        );
      case TimelineEntityType.TimelineEntityType_Form:
        return (
          <FormEntry
            entry={entry}
            contract={contract}
            patientId={patientId}
            doctorIcon={doctorIcon}
            listForms={listForms}
            onRemoveEntry={onRemoveEntry}
            onEditTimelineItem={onEditTimelineItem}
            setItemToEditDate={setItemToEditDate}
          />
        );
      case TimelineEntityType.TimelineEntityType_Room:
        return (
          <CalendarEntry
            entry={entry}
            onEditTimelineItem={onEditTimelineItem}
          />
        );
      case TimelineEntityType.TimelineEntityType_MailItem:
        return <MailEntry entry={entry} onRemoveEntry={onRemoveEntry} />;
      case TimelineEntityType.TimelineEntityType_DoctorLetter:
        return (
          <DoctorLetterEntry
            entry={entry}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_BillingPatient:
        return (
          <PatientBillEntry
            entry={entry}
            doctorIcon={doctorIcon}
            getDoctorName={getDoctorName}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_EHIC:
        return (
          <G81EHICEntry
            entry={entry}
            doctorIcon={doctorIcon}
            setItemToEditDate={setItemToEditDate}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_Psychotherapy: {
        if (!patientId) {
          return null;
        }
        return (
          <Pyschotherapy
            entry={entry}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onRemoveEntry={onRemoveEntry}
            patientId={patientId}
            openEditSchein={openEditSchein}
          />
        );
      }
      case TimelineEntityType.TimelineEntityType_EDMPEnrollment:
        return (
          <DMPEnrolledEntry
            entry={entry}
            doctorIcon={doctorIcon}
            onEditTimelineItem={onEditTimelineItem}
          />
        );
      case TimelineEntityType.TimelineEntityType_EDOKU_Document:
      case TimelineEntityType.TimelineEntityType_EDMPEnrollment_Document:
        return (
          <DMPEntry
            isEdoku={
              entry?.type ===
              TimelineEntityType.TimelineEntityType_EDOKU_Document
            }
            documentationOverview={
              {
                id: entry.documentationOverview?.documentationOverviewId,
                documentationOverview: entry.documentationOverview,
              } as EnrollmentDocumentInfoModel
            }
            entry={entry}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_Medicine:
        return (
          <MedicineEntry
            entry={entry}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
            setItemToEditDate={setItemToEditDate}
          />
        );
      case TimelineEntityType.TimelineEntityType_Note:
        return (
          <FreetextEntry
            keyword={keyword}
            entry={entry}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onRemoveEntry={onRemoveEntry}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
          />
        );
      case TimelineEntityType.TimelineEntityType_Calendar:
        return (
          <AppointmentEntry
            entry={entry}
            patientId={patientId}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_DocumentManagement:
        return <DocumentEntry entry={entry} doctorIcon={doctorIcon} />;
      case TimelineEntityType.TimelineEntityType_Diga:
        return (
          <DigaEntry
            entry={entry}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_Arriba:
        if (
          entry.encounterArriba &&
          (entry.encounterArriba.status === ArribaStatus.ArribaStatus_New ||
            entry.encounterArriba.status === ArribaStatus.ArribaStatus_Unsaved)
        ) {
          return null;
        }
        return (
          <ArribaEntry
            entry={entry}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
          />
        );
      case TimelineEntityType.TimelineEntityType_Service_Chain: {
        if (!context?.patientManagement?.patient) {
          return null;
        }
        return (
          <ServiceChainEntry
            entry={entry}
            onEdit={onEdit}
            patientId={patientId}
            isEditing={isEditing}
            contract={contract}
            hasBilled={hasBilled}
            doctorIcon={doctorIcon}
            quarter={quarter}
            patient={context?.patientManagement?.patient}
            currentSchein={currentSchein}
            setItemToEditDate={setItemToEditDate}
            setEditInline={setEditInline}
            onEditTimelineItem={onEditTimelineItem}
            onRemoveEntry={onRemoveEntry}
            reloadQuarters={reloadQuarters}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
            openCreateSchein={openCreateSchein}
          />
        );
      }
      case TimelineEntityType.TimelineEntityType_GDT:
        return (
          <GDTEntry
            entry={entry}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
            setEditInline={setEditInline}
            isHistoryMode={isHistoryMode}
            payLoadHistoryMode={payLoadHistoryMode}
          />
        );
      case TimelineEntityType.TimelineEntityType_LDT:
        return (
          <LDTEntry
            entry={entry}
            doctorIcon={doctorIcon}
            onRemoveEntry={onRemoveEntry}
            setEditInline={setEditInline}
            isHistoryMode={isHistoryMode}
            payLoadHistoryMode={payLoadHistoryMode}
          />
        );
      case TimelineEntityType.TimelineEntityType_Customize:
        return (
          <CustomizeEntry
            entry={entry}
            doctorIcon={doctorIcon}
            setEditInline={setEditInline}
            onRemoveEntry={onRemoveEntry}
            payLoadHistoryMode={payLoadHistoryMode}
            isHistoryMode={isHistoryMode}
          />
        );
      default:
        return null;
    }
  };

  const hasBilled = billingInfo?.billingSubmitted || false;

  const rendered = renderContent(renderStatus(), hasBilled);

  const hasFocusMode = useMemo(() => {
    if (
      !currentSchein ||
      !entry ||
      !entry.scheinIds ||
      entry.scheinIds.length === 0 ||
      !patientFileStore.schein ||
      !patientFileStore.schein.activatedSchein
    ) {
      return false;
    }

    const isSameSchein =
      patientFileStore.schein.activatedSchein.scheinId === entry.scheinIds[0];

    const isFocusMode = patientFileStore.schein.isFocusMode;

    const focusTypes = new Set([
      TimelineEntityType.TimelineEntityType_Service,
      TimelineEntityType.TimelineEntityType_Diagnose,
      TimelineEntityType.TimelineEntityType_Service_GOA,
      TimelineEntityType.TimelineEntityType_Service_GOA_Chain,
      TimelineEntityType.TimelineEntityType_Service_Chain,
      TimelineEntityType.TimelineEntityType_Service_UV_GOA,
      TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain,
    ]);

    return (
      isSameSchein && isFocusMode && entry.type && focusTypes.has(entry.type)
    );
  }, [
    patientFileStore.schein.isFocusMode,
    patientFileStore.schein.activatedSchein?.scheinId,
    entry.type,
    currentSchein,
  ]);

  const getScheinTitle = (scheinItem: ScheinItem) => {
    if (checkIsKvSchein(scheinItem)) {
      const kvTreatmentCase = t2(
        `generalInfo.kvTreatmentCaseValues.${scheinItem.kvTreatmentCase}` as any
      );
      return `${scheinItem.scheinMainGroup} - ${kvTreatmentCase} (${scheinItem.kvScheinSubGroup})`;
    }
    return t2(
      `generalInfo.kvTreatmentCaseValues.${scheinItem.scheinMainGroup}` as any
    );
  };

  const handleSubmit = () => {
    // type note
    let input = document.querySelector(
      '.sl-TimelineEntry div.sl-FreetextBlock'
    );

    // type service code/ diagnose
    if (!input) {
      input = document.querySelector(
        '.sl-TimelineEntry input.sl-InputSuggestion__input'
      );
    }

    // type service chain
    if (!input) {
      input = document.querySelector(
        '.sl-TimelineEntry input.sl-auto-resize__input'
      );
    }

    if (!input) {
      return;
    }

    const event = new KeyboardEvent('keydown', {
      key: 'Enter',
      code: 'Enter',
      keyCode: 13,
      charCode: 13,
      bubbles: true,
    });
    input.dispatchEvent(event);
  };

  const renderScheinName = useMemo(() => {
    if (!hasFocusMode) {
      return null;
    }

    if (!currentSchein) {
      return null;
    }
    return <div className="schein-name">{getScheinTitle(currentSchein)}</div>;
  }, [currentSchein, hasFocusMode]);

  if (!rendered) {
    return null;
  }

  return (
    <>
      <Flex
        auto
        className={getCssClass(className, editModeCssClass, entryID, {
          selected: isEditInline || isSelected,
          'non-editable':
            entry.encounterCase === EncounterCase.PRE_ENROLLMENT
              ? true
              : [
                  TimelineEntityType.TimelineEntityType_Form,
                  TimelineEntityType.TimelineEntityType_HeimiPrescription,
                  TimelineEntityType.TimelineEntityType_HimiPrescription,
                  TimelineEntityType.TimelineEntityType_Medicine,
                  TimelineEntityType.TimelineEntityType_MedicinePlan,
                  TimelineEntityType.TimelineEntityType_MedicinePrescription,
                ].includes(entry.type!),
        })}
        tabIndex={1}
      >
        {isEditInline && !isHistoryMode ? (
          <React.Fragment>
            <div
              className="edit_inline"
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  handleCloseEditInline();
                }
              }}
            >
              <Composer
                setCurrentBlock={setCurrentBlock}
                currentBlock={currentBlock}
                contract={contract}
                openCreateSchein={openCreateSchein}
                initEncounterId={entryID}
                defaultValue={toComposerDefaultValue(entry)}
                handleCloseEditInline={handleCloseEditInline}
                reloadQuarters={reloadQuarters}
              />
            </div>
            <Flex column>
              <Tooltip
                usePortal={false}
                popoverClassName="sl-content-tooltip"
                content={
                  <ContentTooltip
                    text={tTimeline('saveChanges')}
                    src={SendBase}
                  />
                }
              >
                <div
                  className="close_edit_inline"
                  onClick={() => handleSubmit()}
                >
                  <Svg src={editIcon} />
                </div>
              </Tooltip>
              <Tooltip
                usePortal={false}
                canEscapeKeyClose
                popoverClassName="sl-content-tooltip"
                content={
                  <ContentTooltip text={tTimeline('cancel')} src={escIcon} />
                }
              >
                <div
                  className="close_edit_inline"
                  onClick={handleCloseEditInline}
                >
                  <Svg src={ActionIcon} />
                </div>
              </Tooltip>
            </Flex>
          </React.Fragment>
        ) : (
          <React.Fragment>
            <div className="date">
              <p>{date}</p>
            </div>
            <Flex
              w="100%"
              className={`sl-main-timeline-entry ${getCssClass({
                'focus-mode': hasFocusMode,
              })}`}
            >
              {renderScheinName}
              {entry?.documentationOverview?.documentStatus ===
              DocumentStatus.DocumentStatus_Billed ? (
                <Flex className="entry-billed">
                  <CheckedIcon />
                </Flex>
              ) : (
                <div
                  className={getCssClass(
                    'billed_check',
                    entry &&
                      entry.type ===
                        TimelineEntityType.TimelineEntityType_MedicinePrescription
                      ? 'prescription'
                      : ''
                  )}
                >
                  {renderStatus(true)}
                </div>
              )}
              <Flex
                auto
                className={getCssClass(
                  'entry-content',
                  hasBilled &&
                    disabledEntryTypesAfterBilled.includes(entry.type!)
                    ? 'billed'
                    : ''
                )}
              >
                {rendered}
              </Flex>
            </Flex>
          </React.Fragment>
        )}
      </Flex>
      <DeleteConfirmDialog
        // className="confirm-dialog--hide-icon"
        isOpen={isOpenConfirmRemove}
        close={handleCloseModal}
        confirm={() => {
          if (cbFunction && cbFunction?.constructor?.name === 'Function') {
            cbFunction();
            setCbFunction(null);
          } else {
            if (entryID) {
              deleteEntry(entryID, isChain);
            }
          }
        }}
        isLoading={isLoadingConfirmDialog || isLoadingRemoveERezept}
        text={{
          btnCancel: tTimeline('confirmDeleteDialog_cancelButton'),
          btnOk: tTimeline('confirmDeleteDialog_confirmButton'),
          title: isERezept
            ? tConfirmDialog('deletedERezeptTitle')
            : tTimeline('confirmDeleteDialog_title'),
          message: isERezept
            ? tConfirmDialog('textContent')
            : tTimeline('confirmDeleteDialog_message', {
                duration: timelineDeletionPeriodDays,
              }),
        }}
      />
    </>
  );
};

export default memo(TimelineEntry);
