
import { useState } from 'react';

import type DocumentManagementI18n from '@tutum/mvz/locales/en/DocumentManagement.json';

import { Flex, Svg, Tag, Tooltip } from '@tutum/design-system/components';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import {
  deleteDocumentManagement,
  markReadDocumentManagement,
} from '@tutum/hermes/bff/legacy/app_mvz_document_management';
import { UserType } from '@tutum/hermes/bff/legacy/common';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import Guard from '@tutum/mvz/hooks/Guard';
import DocumentViewDialog from '@tutum/mvz/module_document-management/document-view-dialog/DocumentViewDialog.styled';
import { getSenderLabel } from '@tutum/mvz/module_document-management/sender-form-select/SenderFormSelect';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

type I18nKey = keyof typeof DocumentManagementI18n.timeline;

const moreIcon = '/images/more.svg';
const eyeOnIcon = '/images/eye-on-16px.svg';
const fileApprove = '/images/file-approve.svg';
const fileBlocked = '/images/file-blocked.svg';
const trashIcon = '/images/trash-2.svg';

export interface IDocumentEntryProps {
  className?: string;
  entry: TimelineModel;
  doctorIcon: JSX.Element;
}

const PairValue = ({ label, value }) => {
  if (!value) return null;
  return (
    <>
      <span className="label">{label}:</span>
      <span className="value">{value}</span>
    </>
  );
};
export const DocumentEntry = ({
  className,
  entry,
  doctorIcon,
}: IDocumentEntryProps) => {
  const { t } = I18n.useTranslation<I18nKey>({
    namespace: 'DocumentManagement',
    nestedTrans: 'timeline',
  });
  const document = entry.encounterDocumentManagementTimeline;
  const readStatus = document.readBy ? 'read' : 'new';
  const [editDocumentDialogOpen, setEditDocumentDialogOpen] = useState(false);
  const handleMakeReadDoc = () => {
    markReadDocumentManagement({
      id: document.id,
      patientId: entry.patientId,
    });
  };
  const handleRemoveDoc = () => {
    deleteDocumentManagement({
      id: document.id,
      patientId: entry.patientId,
    });
  };
  const handleViewDoc = () => {
    setEditDocumentDialogOpen(true);
  };
  const renderActions = () => {
    return (
      <TimelineActionHOC>
        <Flex className="actions-group" align="center">
          <Popover
            content={
              <Menu className="action-menu">
                <MenuItem
                  key="view"
                  text={t('viewDocument')}
                  onClick={handleViewDoc}
                  icon={<Svg src={eyeOnIcon} />}
                />
                {!entry.isImported && !document.readBy && (
                  <MenuItem
                    key="read"
                    text={t('markAsRead')}
                    onClick={handleMakeReadDoc}
                    icon={<Svg src={fileApprove} />}
                  />
                )}
                {document.readBy && (
                  <MenuItem
                    key="unread"
                    text={t('markAsUnread')}
                    onClick={handleMakeReadDoc}
                    icon={<Svg src={fileBlocked} />}
                  />
                )}
                <Guard roles={[UserType.MANAGER]} key="remove">
                  <MenuItem
                    key="remove"
                    text={t('remove')}
                    onClick={handleRemoveDoc}
                    icon={<Svg src={trashIcon} />}
                  />
                </Guard>
              </Menu>
            }
          >
            <Tooltip content={undefined} position="top">
              <Svg className="more-icon" src={moreIcon} />
            </Tooltip>
          </Popover>
        </Flex>
      </TimelineActionHOC>
    );
  };

  return (
    <Flex column className={className} onDoubleClick={handleViewDoc}>
      <Flex className="appointment-content" justify="space-between">
        <div className={className}>
          <div className="name">
            <span>{`DOK - <${document?.documentName}>`}</span>
            <Tag className={`tag ${readStatus}`}>{t(readStatus)}</Tag>
          </div>
          <div className="content">
            <PairValue
              label={t('documentType')}
              value={document?.documentType?.name}
            />
            <PairValue
              label={t('sender')}
              value={getSenderLabel(document?.sender, true)}
            />
            <PairValue label={t('description')} value={document?.description} />
            {document.readBy && (
              <>
                <PairValue label={t('readBy')} value={document.readBy.name} />
                <PairValue
                  label={t('readAt')}
                  value={datetimeUtil.dateTimeFormat(
                    document.readBy.readAt,
                    DATE_TIME_WITHOUT_SECONDS_FORMAT
                  )}
                />
              </>
            )}
          </div>
        </div>
        <Flex>
          <TimelineDeletionRemain
            className="timeline-deletion-remain"
            entry={entry}
          />
          <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
          {renderActions()}
        </Flex>
      </Flex>
      <DocumentViewDialog
        isOpen={editDocumentDialogOpen}
        setIsOpen={setEditDocumentDialogOpen}
        document={document as any}
        refetch={() => {}}
      />
    </Flex>
  );
};
