import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import Theme from '@tutum/mvz/theme';
import OriginalLDTEntry, { LDTEntryProps } from './LDTEntry';
import { FONT_FAMILY } from '@tutum/design-system/styles/typo/font-family';

const styled = Theme.styled;

const LDTEntry: React.ComponentType<LDTEntryProps> = styled(
  OriginalLDTEntry
).attrs(({ className }) => ({
  className: getCssClass('sl-LDTEntry', className),
}))`
  cursor: pointer;

  .entry-content {
    font-family: ${FONT_FAMILY.TYPE_FONT_SOURCE_CODE_PRO};
  }
  .expandable-text {
    overflow: hidden;
    transition: max-height 0.3s ease;
  }

  .expandable-text.collapsed {
    overflow: hidden;
    max-height: calc(20px * 4);
  }

  .expandable-text.expanded {
    max-height: unset;
  }
  .button-expand {
    width: fit-content;
    padding: 0;
  }
`;

export default LDTEntry;
