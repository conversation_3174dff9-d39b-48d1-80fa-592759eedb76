import React, { ComponentType, memo } from 'react';
import Theme from '@tutum/mvz/theme';
import I18n from '@tutum/infrastructure/i18n';
import {
  Menu,
  MenuItem,
  Popover,
  Tooltip,
} from '@tutum/design-system/components/Core';
import MoreIcon from '@tutum/mvz/public/images/more-no-color.svg';
import { Flex, Svg } from '@tutum/design-system/components';

const IconCalendar = '/images/calendar-default.svg';

interface IProps {
  text?: string;
  onClick?: () => void;
  className?: string;
}

function ChangeItemDateMenuItem({
  text,
  onClick,
  className,
}): React.FunctionComponentElement<IProps> {
  const { t } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry',
  });
  return (
    <MenuItem
      className={className}
      key="0"
      icon={<Svg src={IconCalendar} className="timeline-entry_row_menu-icon" />}
      text={text || t('editEntryDate')}
      onClick={onClick}
    />
  );
}

const styled = Theme.styled;

const StyledChangeItemDateMenuItem: ComponentType<IProps> = styled(
  ChangeItemDateMenuItem
)``;

export const ItemDateMenuOnly = ({
  text,
  onClick,
}): React.FunctionComponentElement<IProps> => {
  const { t } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry',
  });
  return (
    <Flex className="action-group">
      <Popover
        content={
          <Menu className="action-menu">
            <StyledChangeItemDateMenuItem
              className=""
              onClick={onClick}
              text={text}
            />
          </Menu>
        }
      >
        <Tooltip content={t('more')} position="top">
          <MoreIcon />
        </Tooltip>
      </Popover>
    </Flex>
  );
};

export default memo(StyledChangeItemDateMenuItem);
