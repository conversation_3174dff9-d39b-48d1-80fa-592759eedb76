
import { memo } from 'react';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type MailboxI18n from '@tutum/mvz/locales/en/Mailbox.json';

import i18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { BodyTextL, Flex, Svg, Tag } from '@tutum/design-system/components';
import {
  Menu,
  MenuItem,
  Popover,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { TabIds } from '@tutum/mvz/module_mailbox/Mailbox.type';
import { mailboxActions } from '@tutum/mvz/module_mailbox/Mailbox.store';
import { EABDocumentType } from '@tutum/hermes/bff/eab_common';
import { getPresignedGetURL } from '@tutum/hermes/bff/legacy/app_mvz_file';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { isEABMail } from '@tutum/mvz/_utils/mail.utils';
import { Attachment } from '@tutum/hermes/bff/mail_common';
import Guard from '@tutum/mvz/hooks/Guard';
import { UserType } from '@tutum/hermes/bff/legacy/common';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

export interface IMailEntryProps {
  className?: string;
  entry: TimelineModel;
  onRemoveEntry: (hardDelete?: boolean) => void;
}

interface IActionsMenuProps {
  t: IFixedNamespaceTFunction<keyof typeof MailboxI18n>;
  emailItem: TimelineModel['emailItem'];
  isMailEABReceive: boolean;
  onRemove: () => void;
  onRemoveEntry: (hardDelete?: boolean) => void;
}
const EAB_DOCUMENT = [
  EABDocumentType.EAB_PDF_SIGNED as string,
  EABDocumentType.EAB_XML as string,
];

const MoreIcon = '/images/more.svg';
const EyeIcon = '/images/eye-on.svg';
const AttachmentIcon = '/images/attachment.svg';
const TrashBinRedIcon = '/images/trash-bin-red.svg';

const ActionsMenu = ({
  emailItem,
  t,
  isMailEABReceive,
  onRemove,
  onRemoveEntry,
}: IActionsMenuProps) => {
  const { attachments } = emailItem;
  const eabPDF: Attachment | undefined =
    attachments &&
    attachments.find(
      (file) =>
        file.headers?.['Content-Description'] == EABDocumentType.EAB_PDF_SIGNED
    );
  return (
    <Menu>
      <MenuItem
        key="viewEmail"
        text={t('viewMail')}
        icon={<Svg className="timeline-entry_row_menu-icon" src={EyeIcon} />}
        onClick={() => {
          mailboxActions.setActiveTab(TabIds.Outbox);
          mailboxActions.setViewMailData(emailItem);
        }}
      />
      {isMailEABReceive && eabPDF && (
        <MenuItem
          key="viewEDoctorLetter"
          text={t('viewEDoctorLetter')}
          icon={<Svg className="timeline-entry_row_menu-icon" src={EyeIcon} />}
          onClick={async () => {
            const [bucketName, objectName] = eabPDF.url.split('/');
            const eabHeader = { ...eabPDF.headers };
            eabHeader[
              'response-content-disposition'
            ] = `inline; filename="${objectName}"`;
            eabHeader['response-content-type'] = 'application/pdf';
            const result = await getPresignedGetURL({
              bucketName: bucketName,
              objectName: objectName,
              header: eabHeader,
            });
            window.open(result.data.presignedURL, '_blank');
          }}
        />
      )}
      <Guard roles={[UserType.MANAGER]} key="removeMail">
        <MenuItem
          key="removeMail"
          text={t('remove')}
          icon={
            <Svg
              className="timeline-entry_row_menu-icon"
              src={TrashBinRedIcon}
            />
          }
          onClick={onRemove}
        />
      </Guard>
    </Menu>
  );
};

const MailEntry = (props: IMailEntryProps) => {
  const { className, entry, onRemoveEntry } = props;
  const { attachments, subject, category, from, to } = entry.emailItem || {};

  const { t } = i18n.useTranslation<
    keyof typeof PatientManagementI18n.Timeline
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Timeline',
  });
  const { t: tMailbox } = i18n.useTranslation<keyof typeof MailboxI18n>({
    namespace: 'Mailbox',
  });

  if (!entry.emailItem) return null;

  const isMailSent = () => {
    return !!entry.emailItem.isMailSent;
  };

  const isShowAttachmentIcon = (): boolean => {
    if (!attachments || (attachments && attachments.length === 0)) return false;

    if (isEABMail(entry.emailItem)) {
      return attachments.some(
        (attachment) =>
          !EAB_DOCUMENT.includes(attachment.headers?.['Content-Description'])
      );
    }
    return true;
  };

  const names = to.map((t) => {
    return t.name || t.address;
  });

  const onDoubleClick = () => {
    mailboxActions.setActiveTab(TabIds.Outbox);
    mailboxActions.setViewMailData(entry.emailItem);
  };

  return (
    <Flex className={className} column onDoubleClick={onDoubleClick}>
      <Flex justify="space-between">
        <Flex align="center">
          <BodyTextL>
            <span>{'Email - '}</span>
            <span className="sl-mail-entry__subject">{subject}</span>
            <span>
              {isMailSent()
                ? ` ${tMailbox('To').toLocaleLowerCase()} ${names.join(', ')}`
                : ` ${tMailbox('From').toLocaleLowerCase()} ${
                    from.name || from.address
                  }`}
            </span>
          </BodyTextL>
          {category && <Tag className="sl-mail-entry__tag">{category}</Tag>}
          {isEABMail(entry.emailItem) && !isMailSent() && (
            <Tag className="sl-mail-entry__tag" slStyle="fill" slState="info">
              {t('external')}
            </Tag>
          )}
        </Flex>
        <TimelineDeletionRemain entry={entry} />
        <TimelineActionHOC>
          <Flex align="center">
            {isShowAttachmentIcon() && (
              <Svg
                src={AttachmentIcon}
                className="icon-attachment"
                height={20}
                width={20}
              />
            )}
            <Popover
              className="sl-mail-entry__actions-group"
              content={
                <ActionsMenu
                  t={tMailbox}
                  emailItem={entry.emailItem}
                  isMailEABReceive={isEABMail(entry.emailItem) && !isMailSent()}
                  onRemove={onRemoveEntry}
                  onRemoveEntry={onRemoveEntry}
                />
              }
            >
              <Tooltip content={t('more')} position="top">
                <Svg width={24} height={24} src={MoreIcon} />
              </Tooltip>
            </Popover>
          </Flex>
        </TimelineActionHOC>
      </Flex>
    </Flex>
  );
};

export default memo(MailEntry);
