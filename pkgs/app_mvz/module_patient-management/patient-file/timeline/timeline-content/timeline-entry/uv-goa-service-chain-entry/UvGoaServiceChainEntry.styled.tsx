import type React from 'react';

import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { scaleSpacePx } from '@tutum/design-system/styles';
import Theme from '@tutum/mvz/theme';
import OriginalServiceEntry, {
  type IServiceEntryProps,
} from './UvGoaServiceChainEntry';
import { leftSidebarColor } from '../EntryCommon.styled';
import TimelineService from '../../../Timeline.service';

const styled = Theme.styled;

const ServiceEntry: React.ComponentType<IServiceEntryProps> = styled(
  OriginalServiceEntry
).attrs(({ className }) => ({
  className: getCssClass('sl-UvGoaServiceChainEntry', className),
}))`
 flex-direction: column;
   position: relative;
 
   &:before {
     ${leftSidebarColor('transparent')}
   }
 
   .service-info {
     cursor: pointer;
     flex: 1;
   }
 
   &:hover,
   &.is-opening-menu {
     .action-group {
       align-items: center;
       position: static;
 
       .sl-Svg {
         .svg {
           width: ${scaleSpacePx(4)};
           height: ${scaleSpacePx(4)};
         }
         margin-left: ${scaleSpacePx(4)};
 
         &:hover {
           cursor: pointer;
         }
       }
     }
   }
 
   .extra-info {
     flex: 1 1 auto;
     .doctor-info {
       &:hover {
         cursor: pointer;
       }
       .bp5-divider {
         margin-right: ${scaleSpacePx(2)};
         margin-bottom: 0;
         border-right: ${scaleSpacePx(1)} solid
           ${(props) => props.theme.background['01']};
         border-bottom: none;
       }
     }
   }
   .add-info__container {
     display: inline-flex;
     flex-wrap: wrap;
     gap: 4px;
     .add-info__item {
       display: flex;
     }
   }
 
   .entry-type {
     align-items: flex-start;
     .gap {
       padding: 0 ${scaleSpacePx(1)};
     }
   }
   .sl-Flex.entry-text.service_uv_goa_chain {
     width: 100%;
     gap: 16px;
     align-items: flex-end;
   }
   .with-icon {
     color: ${(props) => {
    if (props.isHistoryMode) {
      return props.payLoadHistoryMode.text;
    }
    return TimelineService.parseEntryColor(
      props.entry,
      props.theme.timelineTheme
    )?.text;
  }};
   }
`;

export default ServiceEntry;
