import { memo } from 'react';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';

import I18n from '@tutum/infrastructure/i18n';
import { Flex, Svg } from '@tutum/design-system/components';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import {
  Menu,
  Popover,
  Tooltip,
  MenuItem,
} from '@tutum/design-system/components/Core';
import { IMvzTheme } from '@tutum/mvz/theme';
import ChangeItemDateMenuItem from '../ChangeItemDateMenuItem';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { EncounterNoteTimeline } from '@tutum/hermes/bff/repo_encounter';
import { ENTRY_CAN_REMOVE } from '../../../Timeline.service';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { PayLoadHistoryMode } from '../util';
import HighlightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import { useTimeLineStore } from '../../../Timeline.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';

const MoreIcon = '/images/more.svg';
const trashIcon = '/images/trash-bin-red.svg';
const editIcon = '/images/edit-value.svg';

const defaultNote = 'N';

export const renderNoteText = (
  encounterNoteTimeline: EncounterNoteTimeline
) => {
  const { command, note } = encounterNoteTimeline;
  return `${isEmpty(command) ? defaultNote : command}${!isEmpty(note) ? ' - ' : ''
    }${note}`;
};
const EditInlineAction = ({ onClick, tTimelineEncounter }) => (
  <MenuItem
    key="edit_1"
    icon={<Svg src={editIcon} size={20} />}
    text={tTimelineEncounter('editEntry')}
    onClick={onClick}
  />
);

const EditDateAction = ({ onClick, t }) => (
  <ChangeItemDateMenuItem key="0" text={t('editEntryDate')} onClick={onClick} />
);

const RemoveAction = ({ onClick, t }) => (
  <MenuItem
    key="1"
    icon={<Svg src={trashIcon} />}
    text={t('actionRemove')}
    onClick={onClick}
  />
);

const ActionMenu = ({
  t,
  tTimelineEncounter,
  entry,
  canRemoveEntry,
  setEditInline,
  setItemToEditDate,
  onRemoveEntry,
}) => {
  const actions: JSX.Element[] = [];

  actions.push(
    <EditInlineAction
      key="edit_1"
      onClick={setEditInline}
      tTimelineEncounter={tTimelineEncounter}
    />
  );

  if (!entry.isImported) {
    actions.push(<EditDateAction key="0" onClick={setItemToEditDate} t={t} />);
  }

  if (canRemoveEntry) {
    actions.push(
      <RemoveAction key="1" onClick={() => onRemoveEntry(false)} t={t} />
    );
  }

  return actions;
};

export interface IFreetextEntryProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  keyword?: string;
  onRemoveEntry: (hardDelete?: boolean) => void;
  setItemToEditDate?: () => void;
  setEditInline?: () => void;
  payLoadHistoryMode: PayLoadHistoryMode;
  isHistoryMode: boolean;
}

const FreetextEntry = (props: IFreetextEntryProps) => {
  const {
    className,
    entry: { type, encounterNoteTimeline },
    doctorIcon,
    hasBilled,
    setEditInline,
    setItemToEditDate,
    onRemoveEntry,
    entry,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DiagnoseEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry',
  });
  const { t: tTimelineEncounter } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimelineEncounter
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimelineEncounter',
  });
  const { matchedTokens } = useTimeLineStore();
  const { allowRemoveTimeline } = useSettingStore();

  const canRemoveEntry =
    !hasBilled &&
    (ENTRY_CAN_REMOVE.includes(encounterNoteTimeline?.type!) ||
      ENTRY_CAN_REMOVE.includes(type!)) &&
    allowRemoveTimeline;

  return (
    <Flex auto className={`${className} sl-${type}Entry`}>
      <HighlightWrapper matchedTokens={matchedTokens}>
        <p className="freetext_content hover-text" onClick={setEditInline}>
          <span>{renderNoteText(entry.encounterNoteTimeline!)}</span>
        </p>
      </HighlightWrapper>
      <Flex>
        <TimelineDeletionRemain entry={entry} />
        <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>

        <TimelineActionHOC entry={entry} hasBilled={hasBilled}>
          <Flex className="actions-group">
            <Popover
              content={
                <Menu className="action-menu">
                  <ActionMenu
                    t={t}
                    tTimelineEncounter={tTimelineEncounter}
                    entry={entry}
                    canRemoveEntry={canRemoveEntry}
                    setEditInline={setEditInline}
                    setItemToEditDate={setItemToEditDate}
                    onRemoveEntry={onRemoveEntry}
                  />
                </Menu>
              }
            >
              <Tooltip content={t('more')} position="top">
                <Svg className="more-icon" src={MoreIcon} />
              </Tooltip>
            </Popover>
          </Flex>
        </TimelineActionHOC>
      </Flex>
    </Flex>
  );
};

export default memo(FreetextEntry);
