import {
  AppToaster,
  Flex,
  MessageBar,
  Svg,
  alertError,
  alertInProgress,
  alertWarning,
} from '@tutum/design-system/components';
import {
  Menu,
  MenuItem,
  Popover,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import {
  PrescribeType,
  getPrescribeById,
  validateFreeText,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { EncounterNoteTimeline } from '@tutum/hermes/bff/repo_encounter';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import useToaster from '@tutum/mvz/hooks/useToaster';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { mapFormNameToFormType } from '@tutum/mvz/module_diga/Diga.helper';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import { medicationActions } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import { medicationShoppingBagActions } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import { IMvzTheme } from '@tutum/mvz/theme';
import { memo, useContext } from 'react';

const MoreIcon = '/images/more.svg';
const trashIcon = '/images/trash-bin-red.svg';
const eyeOn = '/images/eye-on.svg';
const refillIcon = '/images/refill.svg';

const defaultNote = 'N';

export const renderNoteText = (
  encounterNoteTimeline: EncounterNoteTimeline
) => {
  const { command, note } = encounterNoteTimeline;
  return `${isEmpty(command) ? defaultNote : command}${!isEmpty(note) ? ' - ' : ''
    }${note}`;
};

export interface IDigaEntryProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  onRemoveEntry: (hardDelete: boolean) => void;
}

const DigaEntry = (props: IDigaEntryProps) => {
  const {
    className,
    entry: { type, digaPrescriptionTimeline },
    doctorIcon,
    hasBilled,
    onRemoveEntry,
    entry,
  } = props;

  if (!digaPrescriptionTimeline) {
    throw new Error('digaPrescriptionTimeline is undefined');
  }

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DigaEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DigaEntry',
  });
  const { t: tForm } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimeLineFormDetail
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimeLineFormDetail',
  });
  const medicationContext = useContext(MedicationContext);
  const toast = useToaster();

  const handleViewForm = () => {
    medicationActions.setRefillFormData(undefined);
    medicationContext.setViewFormTimeline({
      treatmentDoctorId: entry.treatmentDoctorId,
      assignedToBsnrId: entry.assignedToBsnrId!,
      formInfo: {
        id: digaPrescriptionTimeline.id!,
        formInfoResponse: [],
        formSetting: JSON.stringify(
          digaPrescriptionTimeline?.formInfo?.formSetting ?? {}
        ),
        currentFormType: mapFormNameToFormType(
          digaPrescriptionTimeline.formInfo.formName
        ),
        prescribeDate: new Date().getTime(),
        isNotPicked: false,
        isShowFavHint: false,
      },
      scheinId: digaPrescriptionTimeline.scheinId,
    });
    medicationShoppingBagActions.setDigaInfo(
      digaPrescriptionTimeline,
      digaPrescriptionTimeline.formInfo.formName,
      digaPrescriptionTimeline.type
    );
  };

  return (
    <>
      <Flex
        auto
        className={`${className} sl-${type}Entry`}
        onDoubleClick={handleViewForm}
      >
        <p className="freetext_content hover-text">
          <span>
            {t('diga')} -{' '}
            {digaPrescriptionTimeline.digaTitle ||
              digaPrescriptionTimeline.digaName}
          </span>
        </p>
        <Flex>
          <TimelineDeletionRemain entry={entry} />
          <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
          <TimelineActionHOC entry={entry} hasBilled={hasBilled}>
            <Flex className="actions-group">
              <Popover
                content={
                  <Menu className="action-menu">
                    <MenuItem
                      icon={<Svg src={eyeOn} />}
                      text={t('viewForm')}
                      onClick={handleViewForm}
                    />
                    {digaPrescriptionTimeline.printedDate ? (
                      <MenuItem
                        icon={<Svg src={refillIcon} />}
                        text={tForm('refillForm')}
                        onClick={async () => {
                          // Prescribe flow
                          if (
                            digaPrescriptionTimeline.type ===
                            PrescribeType.PRESCRIBED
                          ) {
                            alertInProgress(t('processingRefill'));
                            const prescribed = await getPrescribeById({
                              id: digaPrescriptionTimeline.id!,
                            });
                            AppToaster?.clear();
                            if (prescribed.data.prescribe.isExpired) {
                              alertError(t('invalidDiga'));
                              return;
                            }
                            patientFileActions.setDigaDetail(
                              prescribed.data.prescribe.diga!
                            );
                            patientFileActions.setActiveTabId(ID_TABS.DIGA);
                            return;
                          }
                          // Freetext flow
                          try {
                            await validateFreeText({
                              name: digaPrescriptionTimeline.digaName,
                              pzn: digaPrescriptionTimeline.digaPzn,
                            });
                          } catch (error) {
                            const serverError =
                              error?.response?.data?.serverError;
                            if (
                              serverError ===
                              ErrorCode.ErrorCode_Diga_PznAlreadyExists
                            ) {
                              alertWarning(t('pznExists'), { toaster: toast });
                              patientFileActions.setActiveTabId(ID_TABS.DIGA);
                              return;
                            }
                            throw error;
                          }
                          medicationContext.setShowPrintPreviewState(true);
                          medicationShoppingBagActions.setDigaInfo(
                            {
                              ...digaPrescriptionTimeline,
                              id: null!,
                              formInfo: {
                                ...digaPrescriptionTimeline.formInfo,
                                prescribeDate: null!,
                              },
                            },
                            digaPrescriptionTimeline.formInfo.formName,
                            digaPrescriptionTimeline.type
                          );
                        }}
                      />
                    ) : null}
                    {/* <Guard roles={[UserType.MANAGER]} key="remove"> */}
                    <MenuItem
                      icon={<Svg src={trashIcon} />}
                      text={t('actionRemove')}
                      onClick={() => onRemoveEntry(false)}
                    />
                    {/* </Guard> */}
                  </Menu>
                }
              >
                <Tooltip content={t('more')} position="top">
                  <Svg className="more-icon" src={MoreIcon} />
                </Tooltip>
              </Popover>
            </Flex>
          </TimelineActionHOC>
        </Flex>
      </Flex>

      {digaPrescriptionTimeline.printedDate === 0 ? (
        <Flex align="center">
          <MessageBar
            type="warning"
            hasBullet={false}
            content={tForm('printOutHint')}
          />
        </Flex>
      ) : null}
    </>
  );
};

export default memo(DigaEntry);
