import {
  BodyTextM,
  Flex,
  Svg,
  alertSuccessfully,
  MessageBar,
} from '@tutum/design-system/components';
import { Tooltip } from '@tutum/design-system/components/Core';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { heimiSelectionActions } from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { memo, useMemo } from 'react';

import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import { FormName } from '@tutum/hermes/bff/form_common';
import { EncounterHeimiPrescription } from '@tutum/hermes/bff/repo_encounter';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import type HeimiI18n from '@tutum/mvz/locales/en/Heimi.json';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { sortBy, get } from 'lodash';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import ChangeItemDateMenuItem from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/ChangeItemDateMenuItem';
import { deletePrescription } from '@tutum/hermes/bff/legacy/app_mvz_heimi';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { Form } from '@tutum/hermes/bff/form_common';
import Guard from '@tutum/mvz/hooks/Guard';
import { UserType } from '@tutum/hermes/bff/legacy/common';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';

const MoreIcon = '/images/more.svg';
const refillIcon = '/images/refill.svg';
const eyeOnIcon = '/images/eye-on.svg';
const trashIcon = '/images/trash-bin-red.svg';

export interface ITimeLineFormDetailProps {
  timelineModel: TimelineModel;
  contract?: IContractInfo;
  patientId: string;
  doctorId: string;
  patientDateOfBirth: number;
  doctorIcon: JSX.Element;
  listForms: Form[];
  setItemToEditDate?: () => void;
  onRemoveEntry: (hardDelete?: boolean, cb?: () => void) => void;
}

const convertFormToHeimiPrescription = (
  form: EncounterHeimiPrescription
): any => {
  return {
    isViewForm: false,
    isRefill: false,
    isEditHeimi: false,
    prescriptionDate: form.heimiForm.prescription.prescribeDate,
    currentFormName: FormName.Muster_13,
    heimiFormData: form.heimiForm,
  };
};

const fullName = (encounterHeimiPrescription) => {
  const { prescription } = encounterHeimiPrescription.heimiForm;
  const remedies = [
    ...sortBy(prescription.remedies, (remedy) => remedy.order),
    ...sortBy(prescription.complementaryRemedies, (remedy) => remedy.order),
  ];
  const remedyNames = !prescription.isStandardCombination
    ? remedies
      .map((r) => `${r.quantity ? `${r.quantity}x` : ''} ${r.name}`)
      .join('; ')
    : `${prescription.standardCombinationQuantity}x ${prescription.remediesTimeline ||
    `Standardisierte Heilmittelkombination (${remedies
      .map((compRemedy) => compRemedy.name)
      .join('; ')})`
    }`;

  return `(M 13) ${remedyNames} ${prescription.diagnose.code ? `• ${prescription.diagnose.code}` : ''
    }${prescription?.secondaryDiagnose?.code
      ? `; ${prescription?.secondaryDiagnose?.code}`
      : ''
    }${prescription?.therapyFrequency?.name
      ? ` • ${get(
        JSON.parse(prescription?.formSetting),
        'label_therapie_frequenz_0'
      )}`
      : ''
    }${prescription?.indicator?.label ? `• ${prescription?.indicator?.label}` : ''
    }`;
};

export const renderHeimiText = (encounterHeimiPrescription) => {
  return `HEIMI - ${fullName(encounterHeimiPrescription)}`;
};

function HeimiEntry(
  props: ITimeLineFormDetailProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof HeimiI18n.Timeline>
) {
  const {
    timelineModel,
    className,
    t,
    doctorIcon,
    listForms,
    setItemToEditDate,
    onRemoveEntry,
  } = props;

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    timelineModel.treatmentDoctorId,
    timelineModel.assignedToBsnrId
  );

  const hasRefill = useMemo(() => {
    return listForms.find(
      (item) =>
        item.id ===
        timelineModel.encounterHeimiPrescription?.heimiForm?.prescription
          ?.formName
    )?.hasRefill;
  }, [timelineModel, listForms]);

  const handleViewForm = () => {
    musterFormDialogActions.viewHeimiForm(
      convertFormToHeimiPrescription(timelineModel.encounterHeimiPrescription!),
      timelineModel.id,
      timelineModel.encounterHeimiPrescription?.id,
      !!timelineModel.encounterHeimiPrescription?.heimiForm.prescription
        .printDate,
      false,
      heimiSelectionActions,
      true, //hide edit button
      timelineModel.scheinIds?.[0],
      treatmentDoctor
    );
  };

  const renderActionMenu = () => {
    if (!timelineModel.encounterHeimiPrescription) {
      return <></>;
    }
    return (
      <Menu className="action-menu">
        {!timelineModel.isImported && (
          <ChangeItemDateMenuItem
            onClick={() => {
              setItemToEditDate?.();
            }}
          />
        )}
        {hasRefill && (
          <MenuItem
            key="refillForm"
            text={t('RefillForm')}
            icon={
              <Svg className="timeline-entry_row_menu-icon" src={refillIcon} />
            }
            onClick={() => {
              patientFileActions.setActiveTabId(ID_TABS.HEIMI);
              setTimeout(() => {
                heimiSelectionActions.refillFromTimeLine(
                  timelineModel.encounterHeimiPrescription!
                );
              }, 2000); //temp set timeout to wait page load data
            }}
          />
        )}
        <MenuItem
          key="viewForm"
          text={t('ViewForm')}
          onClick={handleViewForm}
          icon={
            <Svg className="timeline-entry_row_menu-icon" src={eyeOnIcon} />
          }
        />
        <Guard roles={[UserType.MANAGER]} key="remove">
          <MenuItem
            key="removeForm"
            text={t('RemoveForm')}
            onClick={() => {
              onRemoveEntry(false, async () => {
                await deletePrescription({
                  prescriptionId: timelineModel.id!,
                });
                alertSuccessfully(t('DeleteSuccessfully'));
              });
            }}
            icon={
              <Svg className="timeline-entry_row_menu-icon" src={trashIcon} />
            }
          />
        </Guard>
      </Menu>
    );
  };

  const renderAction = () => {
    return (
      <TimelineActionHOC>
        <Flex className="actions-group">
          <Popover content={renderActionMenu()}>
            <Tooltip content={t('More')} position="top">
              <Svg className="more-icon" src={MoreIcon} />
            </Tooltip>
          </Popover>
        </Flex>
      </TimelineActionHOC>
    );
  };

  const renderHintPrintout = () => {
    if (
      timelineModel.encounterHeimiPrescription?.heimiForm.prescription.printDate
    ) {
      return null;
    }
    return (
      <Flex align="center" mt={8}>
        <MessageBar
          hasBullet={false}
          type="warning"
          content={t('PrintOutHint')}
        />
      </Flex>
    );
  };

  if (!timelineModel.encounterHeimiPrescription) {
    return null;
  }

  return (
    <Flex column onDoubleClick={handleViewForm}>
      <div className={className}>
        <Flex flexWrap>
          <Flex>
            <BodyTextM as="span" className="heimi-content hover-text">
              {renderHeimiText(timelineModel?.encounterHeimiPrescription)}
            </BodyTextM>
          </Flex>
          <Flex align="flex-start">
            <TimelineDeletionRemain entry={timelineModel} />
            <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
            {renderAction()}
          </Flex>
        </Flex>
      </div>
      {renderHintPrintout()}
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(HeimiEntry, {
    namespace: 'Heimi',
    nestedTrans: 'Timeline',
  })
);
