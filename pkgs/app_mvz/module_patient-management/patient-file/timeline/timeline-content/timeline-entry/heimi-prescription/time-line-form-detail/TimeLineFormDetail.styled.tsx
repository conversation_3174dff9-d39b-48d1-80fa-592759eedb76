import Theme from '@tutum/mvz/theme';
import { ComponentType } from 'react';
import TimelineService from '../../../../Timeline.service';
import OriginalTimeLineFormDetailMemo, {
  ITimeLineFormDetailProps,
} from './TimeLineFormDetail';

const styled = Theme.styled;
const HeimiEntry: ComponentType<ITimeLineFormDetailProps> = styled(
  OriginalTimeLineFormDetailMemo
)`
  & {
    display: flex;
    flex-direction: row;
    .sl-Flex:first-child {
      flex: 2;
    }
    .sl-Flex:nth-child(2) {
      .timeline-right-side-infos {
        align-items: center;
      }
    }
    .heimi-content {
      color: ${(props) =>
    TimelineService.parseEntryColor(
      props.timelineModel,
      props.theme.timelineTheme
    )?.text};
    }
  }
`;

export default HeimiEntry;
