
import React, { memo } from 'react';

import { Flex, Svg } from '@tutum/design-system/components';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import { FORM_SECTION } from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import { IMvzTheme } from '@tutum/mvz/theme';
import { renderPsychotherapyText } from './util';

const MoreIcon = '/images/more.svg';
const deactivateIcon = '/images/trash-bin-red.svg';
const editIcon = '/images/edit-value.svg';

export interface IPsychotherapyProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  onRemoveEntry: (hardDelete?: boolean, cb?: Function) => void;
  setItemToEditDate?: () => void;
  setEditInline?: () => void;
  patientId: string;
  openEditSchein: (scheinId?: string, scrollToSection?: FORM_SECTION) => void;
}

const Pyschotherapy = (props: IPsychotherapyProps) => {
  const {
    className,
    entry: { type, encounterPsychotherapy },
    onRemoveEntry,
    openEditSchein,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DiagnoseEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry',
  });

  const { t: tTimelineEncounter } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimelineEncounter
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimelineEncounter',
  });

  const tSchein = I18n.useTranslation<keyof typeof ScheinI18n>({
    namespace: 'Schein',
  }).t;

  const patientFileStore = usePatientFileStore();
  const renderActionMenu = () => {
    const actions: React.JSX.Element[] = [];
    const actionRemove = (
      // <Guard roles={[UserType.MANAGER]} key="0">
      <MenuItem
        key="0"
        icon={<Svg src={deactivateIcon} />}
        text={t('actionRemove')}
        onClick={() => {
          onRemoveEntry(false);
        }}
      />
      // </Guard>
    );
    const actionEdit = (
      <MenuItem
        key="1"
        icon={<Svg src={editIcon} size={20} />}
        text={t('editSchein')}
        onClick={() => {
          openEditSchein(
            encounterPsychotherapy.scheinId,
            FORM_SECTION.PSYCHOTHERAPY
          );
        }}
      />
    );

    actions.push(actionEdit, actionRemove);
    return actions;
  };

  return (
    <Flex auto className={`${className} sl-${type}Entry`}>
      <div className="pyschotherapy_content hover-text">
        {encounterPsychotherapy
          ? renderPsychotherapyText(
              encounterPsychotherapy,
              patientFileStore.schein.originalList,
              tSchein,
              false
            )
          : ''}
      </div>
      <TimelineDeletionRemain entry={props.entry} />
      {renderActionMenu().length > 0 && (
        <TimelineActionHOC entry={props?.entry} hasBilled={props.hasBilled}>
          <Flex className="actions-group">
            <Popover
              content={
                <Menu className="action-menu">{renderActionMenu()}</Menu>
              }
            >
              <Svg className="more-icon" src={MoreIcon} />
            </Popover>
          </Flex>
        </TimelineActionHOC>
      )}
    </Flex>
  );
};

export default memo(Pyschotherapy);
