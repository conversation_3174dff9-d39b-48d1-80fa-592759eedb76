import { EncounterPsychotherapy } from '@tutum/hermes/bff/repo_encounter';
import { convertToUTCMoment } from '@tutum/design-system/infrastructure/utils';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import moment from 'moment';
import { BodyTextM, Box } from '@tutum/design-system/components';
import React from 'react';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { checkIsSvSchein } from '@tutum/mvz/_utils/scheinFormat';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';

const getScheinTitle = (
  t: IFixedNamespaceTFunction<any>,
  scheinItem?: ScheinItem
) => {
  if (checkIsSvSchein(scheinItem)) {
    return `${scheinItem?.scheinMainGroup}`;
  }

  const kvTreatmentCase = t(
    `generalInfo.kvTreatmentCaseValues.${scheinItem?.kvTreatmentCase}`
  );
  return `${kvTreatmentCase} (${scheinItem?.kvScheinSubGroup})`;
};

export const renderPsychotherapyText = (
  encounterPsychotherapy: EncounterPsychotherapy,
  originalList: ScheinItem[],
  t: IFixedNamespaceTFunction<Paths<typeof ScheinI18n>>,
  isReturnString: boolean
) => {
  const dateOfApproval = moment(encounterPsychotherapy.approvalDate),
    dateOfRequest = moment(encounterPsychotherapy.requestDate),
    date = datetimeUtil.date();
  date.setUTCMonth(4);
  date.setUTCDate(1);
  date.setUTCFullYear(2017);
  const isBefore =
    convertToUTCMoment(dateOfRequest).toDate().getTime() <
    moment(date).toDate().getTime();

  const schein = originalList.find(
    (s) => s.scheinId === encounterPsychotherapy.scheinId
  );
  if (!schein) return undefined;
  if (isReturnString) {
    const titleEntry = t('psychotherapy.titleEntry', {
      title: getScheinTitle(t, schein),
    });
    const requestDateText = dateOfRequest.isValid()
      ? `${t('psychotherapy.descriptionRequestDate')} ${dateOfRequest.format(
        'DD.MM.YYYY'
      )}, `
      : '';
    const approvalDateText = dateOfApproval.isValid()
      ? `${t('psychotherapy.descriptionApprovalDate')} ${dateOfApproval.format(
        'DD.MM.YYYY'
      )}${!isBefore ? ', ' : ''}`
      : '';
    const amountApprovalText = !isBefore
      ? `${t('psychotherapy.descriptionAmountEntry')} ${encounterPsychotherapy.amountApproval
      }`
      : '';

    return `${titleEntry}
${requestDateText}${approvalDateText}${amountApprovalText}`;
  }

  return (
    <Box className="content">
      <BodyTextM>
        {t('psychotherapy.titleEntry', { title: getScheinTitle(t, schein) })}
      </BodyTextM>
      <small>
        {dateOfRequest.isValid() && (
          <React.Fragment>
            <b>{t('psychotherapy.descriptionRequestDate')}</b>{' '}
            {dateOfRequest.format('DD.MM.YYYY')},{' '}
          </React.Fragment>
        )}
        {dateOfApproval.isValid() && (
          <React.Fragment>
            <b>{t('psychotherapy.descriptionApprovalDate')}</b>{' '}
            {dateOfApproval.format('DD.MM.YYYY')}
            {!isBefore && `, `}
          </React.Fragment>
        )}
        {!isBefore && (
          <React.Fragment>
            <b>{t('psychotherapy.descriptionAmountEntry')}</b>{' '}
            {encounterPsychotherapy.amountApproval}
          </React.Fragment>
        )}
      </small>
    </Box>
  );
};
