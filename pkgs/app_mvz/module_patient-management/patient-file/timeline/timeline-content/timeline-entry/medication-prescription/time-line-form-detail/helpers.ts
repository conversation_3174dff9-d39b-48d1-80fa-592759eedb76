
import { medicationUtil } from '@tutum/mvz/module_medication/utils/medication-util';
import {
  MedicineShoppingBagInfo,
  MedicineType,
} from '@tutum/hermes/bff/app_mvz_medicine';
import { flatten } from '@tutum/design-system/infrastructure/utils';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/admin/locales/en/PatientManagement.json';

export const handleGetMedicineString = (
  medicine: MedicineShoppingBagInfo,
  t: IFixedNamespaceTFunction<
    keyof typeof PatientManagementI18n.TimeLineFormDetail
  >
) => {
  const componentsAndSubtants = medicationUtil.getComponentsAndSubstants(
    medicine?.drugInformation
  );

  const substantStr = flatten(
    componentsAndSubtants.map((item) => item.substances)
  ).join(', ');
  const packageSize =
    medicine.type === MedicineType.FreeText
      ? ''
      : medicationUtil.getPackageSize(medicine?.packagingInformation);

  let str = `${medicine.quantity} x ${
    medicine.name
  } ${substantStr} ${medicationUtil.getDosageForm(
    medicine?.productInformation
  )}${medicine.drugFormInformation ? ` ${medicine.drugFormInformation}` : ''}`;

  if (packageSize.length > 0) {
    str = `${str} • ${packageSize}`;
  }

  if (medicine.furtherInformation && medicine.furtherInformation.length > 0) {
    str = `${str} • ${medicine.furtherInformation}`;
  }

  const { intakeInterval } = medicine;
  if (intakeInterval) {
    if (intakeInterval.dJ) {
      str = `${str} • ${t('dj')}`;
    } else {
      const morning = medicationUtil.getIntakeInterValue(
        intakeInterval,
        'MORNING'
      );
      const afternoon = medicationUtil.getIntakeInterValue(
        intakeInterval,
        'AFTERNOON'
      );
      const evening = medicationUtil.getIntakeInterValue(
        intakeInterval,
        'EVENING'
      );
      const night = medicationUtil.getIntakeInterValue(intakeInterval, 'NIGHT');
      if (
        morning !== null &&
        afternoon !== null &&
        evening !== null &&
        night !== null
      ) {
        const strIntake = `${morning || 0}-${afternoon || 0}-${evening || 0}-${
          night || 0
        }`;
        str = `${str} • ${strIntake}`;
      } else if (intakeInterval?.freetext) {
        str = `${str} • ${intakeInterval.freetext}`;
      }
    }
  }
  if (medicine.autIdem) {
    str = `${str} • ${t('autIdem')}`;
  }
  return str;
};
