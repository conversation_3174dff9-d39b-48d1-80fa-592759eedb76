
import { memo } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { IContractInfo } from '../../../../../types/contract.type';
import { Flex } from '@tutum/design-system/components';
import TimeLineFormDetail from './time-line-form-detail/TimeLineFormDetail.styled';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { EditResponse } from '@tutum/hermes/bff/app_mvz_timeline';
import {
  ScheinItem,
} from '@tutum/hermes/bff/legacy/schein_common';

export interface IMedicationPrescriptionProps {
  entry: TimelineModel;
  lastItem?: boolean;
  patientId: string;
  contract?: IContractInfo;
  doctorIcon: JSX.Element;
  scheins?: ScheinItem[];
  setItemToEditDate?: () => void;
  onRemoveEntry: (hardDelete?: boolean, cb?: Function) => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  setERezept: (isERezept: boolean) => void;
  keyword?: string;
}

function MedicationPrescriptionMemo(
  props: IMedicationPrescriptionProps &
    IMvzThemeProps &
    II18nFixedNamespace<
      keyof typeof PatientManagementI18n.MedicationPrescription
    >
) {
  const {
    entry,
    className,
    doctorIcon,
    setItemToEditDate,
    patientId,
    onEditTimelineItem,
    scheins,
    onRemoveEntry,
    setERezept,
    keyword,
  } = props;
  if (!entry.encounterMedicinePrescription?.formInfos) {
    return null;
  }

   return (
    <Flex column className={className}>
      <Flex className="entry-wrapper" column>
        {entry.encounterMedicinePrescription.formInfos.map((item) => (
          <TimeLineFormDetail
            form={item}
            encounterId={entry.id}
            patientId={patientId}
            scheins={scheins}
            key={item.id}
            doctorIcon={doctorIcon}
            setItemToEditDate={setItemToEditDate}
            onEditTimelineItem={onEditTimelineItem}
            timeline={entry}
            onRemoveEntry={onRemoveEntry}
            setERezept={setERezept}
            keyword={keyword}
          />
        ))}
      </Flex>
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(MedicationPrescriptionMemo, {
    namespace: 'PatientManagement',
    nestedTrans: 'MedicationPrescription',
  })
);
