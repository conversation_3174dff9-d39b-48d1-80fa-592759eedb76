import { REFERRAL_BLOCK } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import {
  ScheinStatus,
  ScheinItem,
} from '@tutum/hermes/bff/legacy/schein_common';
import { EncounterCase } from '@tutum/hermes/bff/legacy/service_domains_patient_file';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import {
  AdditionalInfoParent,
  EncounterGoaService,
  EncounterUvGoaService,
} from '@tutum/hermes/bff/repo_encounter';
import {
  checkIsBgSchein,
  checkIsFavSchein,
  checkIsHzvSchein,
  checkIsPrivateSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import { replaceHtmlEntites } from '@tutum/mvz/_utils/text';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';

// Moving this to BE for optimization to next PR
export const getBillingInfo = (
  scheins: ScheinItem[],
  entry: TimelineModel,
  getDoctorName: (doctorId: string) => string,
  onlyNeedDisableContent?: boolean
) => {
  // const scheins = patientFileStore.schein.originalList;
  const rs: {
    billingSubmittedUserName: string;
    billingSubmittedDate: number | null;
    billingSubmitted: boolean;
    billingSubmittedPrivate: boolean;
    showBillingIcon: boolean;
  } = {
    billingSubmittedUserName: '',
    billingSubmittedDate: null,
    billingSubmitted: false,
    billingSubmittedPrivate: false,
    showBillingIcon: false,
  };

  const isNotPrintedMedication =
    entry?.encounterMedicinePrescription &&
    !entry.encounterMedicinePrescription.formInfos[0]?.printDate;

  if (
    (!entry?.encounterDiagnoseTimeline &&
      !entry?.encounterServiceTimeline &&
      !entry?.encounterGoaService &&
      !entry?.encounterServiceChain &&
      !entry?.encounterUvGoaServiceChain &&
      !entry?.encounterGoaServiceChain &&
      !entry?.encounterMedicinePrescription &&
      !entry?.encounterUvGoaService) ||
    isNotPrintedMedication
  ) {
    return rs;
  }
  if (entry.encounterCase === EncounterCase.PRE_ENROLLMENT) {
    return {
      ...rs,
      billingSubmitted:
        entry?.encounterServiceTimeline?.isSubmitPreParticipateSucsess,
    };
  }

  const assignedScheins = entry?.scheinIds;

  if (assignedScheins?.[0]) {
    const schein = assignedScheins[0];

    const billedSchein = scheins.find((s) => {
      return (
        s.scheinId === schein &&
        (s.markedAsBilled ||
          [
            ScheinStatus.ScheinStatus_Printed,
            ScheinStatus.ScheinStatus_Billed,
            !onlyNeedDisableContent ? ScheinStatus.ScheinStatus_Canceled : null,
          ].includes(s?.scheinStatus!))
      );
    });

    if (billedSchein) {
      rs.billingSubmittedUserName = getDoctorName(billedSchein.updatedBy || '');
      rs.billingSubmittedDate = billedSchein.updatedAt as number;
      rs.billingSubmitted = true;
      rs.billingSubmittedPrivate =
        checkIsPrivateSchein(billedSchein) || checkIsBgSchein(billedSchein);
      rs.showBillingIcon = !isNotHzvandFavPrescriptionMedicineCheck(
        billedSchein,
        entry
      );
    }
  }

  return rs;
};

export const isNotHzvandFavPrescriptionMedicineCheck = (
  schein: ScheinItem,
  entry: TimelineModel
) => {
  if (!schein) return false;
  return (
    entry?.encounterMedicinePrescription &&
    !(checkIsHzvSchein(schein) || checkIsFavSchein(schein))
  );
};

export const isCheckBilledPrescriptionMedicineWithHzvOrFav = (
  scheins: ScheinItem[] | undefined,
  entry: TimelineModel
) => {
  if (!scheins || scheins.length === 0) return false;
  const schein = scheins.find(
    (schein) => schein.scheinId === entry?.scheinIds?.[0]
  );

  if (!schein) return false;
  return (
    entry?.encounterMedicinePrescription &&
    (checkIsHzvSchein(schein) || checkIsFavSchein(schein)) &&
    !!entry?.encounterMedicinePrescription.billingInfo
  );
};

export const renderUvGoaServiceText = (
  encounterServiceTimeline: EncounterUvGoaService
) => {
  return `${encounterServiceTimeline.command} - ${replaceHtmlEntites(
    encounterServiceTimeline.freeText
  )}`;
};

export const renderGoaServiceText = (
  encounterServiceTimeline: EncounterGoaService
) => {
  return `${encounterServiceTimeline.command} - ${replaceHtmlEntites(
    encounterServiceTimeline.freeText
  )}`;
};

export const customAdditionalInfos = (
  additionalInfos: AdditionalInfoParent[],
  userProfile: IEmployeeProfile
) => {
  return (additionalInfos || [])
    .filter((info) => {
      switch (info.fK) {
        case '5098':
          return userProfile.bsnr != info.value;
        case '5099':
          return userProfile.lanr != info.value;
        default:
          return info.fK !== REFERRAL_BLOCK;
      }
    })
    .map((info) => {
      if (info.fK !== '5012') {
        return info;
      }

      const value5012 = info.value;
      const item5011 = info.children.find((item) => item.fK === '5011');
      const children = info.children
        .map((item) => {
          const is5011 = item.fK === '5011';

          return {
            ...item,
            fK: is5011 ? '5012' : item.fK,
            value: is5011 ? value5012 : item.value,
          };
        })
        .sort((itemA) => {
          return itemA.fK === '5012' ? -1 : 0;
        });

      return {
        ...info,
        fK: '5011',
        children,
        value: item5011?.value!,
      };
    });
};

export type PayLoadHistoryMode = {
  bg: string;
  text: string;
  lineThrough?: boolean;
};
