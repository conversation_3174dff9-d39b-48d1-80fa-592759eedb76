import { REFERRAL_BLOCK } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import {
  AdditionalInfoParent,
  EncounterServiceTimeline,
} from '@tutum/hermes/bff/repo_encounter';
import {
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import { replaceHtmlEntites } from '@tutum/mvz/_utils/text';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';

export const renderServiceText = (
  encounterServiceTimeline: EncounterServiceTimeline
) => {
  return `${encounterServiceTimeline.command} - ${replaceHtmlEntites(
    encounterServiceTimeline.freeText
  )}`;
};

export const renderServiceChainText = (
  encounterServiceTimeline: EncounterServiceTimeline
) => {
  return `${encounterServiceTimeline.command} - ${replaceHtmlEntites(
    encounterServiceTimeline.freeText
  )} \n\n`;
};

export const customAdditionalInfos = (
  additionalInfos: AdditionalInfoParent[],
  userProfile: IEmployeeProfile
) => {
  return (additionalInfos || [])
    .filter((info) => {
      switch (info.fK) {
        case '5098':
          return userProfile.bsnr != info.value;
        case '5099':
          return userProfile.lanr != info.value;
        default:
          return info.fK !== REFERRAL_BLOCK; // don't show referral info
      }
    })
    .map((info) => {
      if (info.fK !== '5012' && info.fK !== '5077') {
        return info;
      }
      if (info.fK === '5077') { // HGNC-Gensymbol
        return {
          ...info,
          // if gen symbol is valid(!999999) , then don't show children(gen name)
          ...(info.value !== '999999' ? {children:[]} : {})
        }
      }

      // swap 5011, 5012
      const value5012 = info.value;
      const item5011 = info.children.find((item) => item.fK === '5011');
      const children = info.children
        .map((item) => {
          const is5011 = item.fK === '5011';

          return {
            ...item,
            fK: is5011 ? '5012' : item.fK,
            value: is5011 ? value5012 : item.value,
          };
        })
        .sort((itemA) => {
          return itemA.fK === '5012' ? -1 : 0;
        });

      return {
        ...info,
        fK: '5011',
        children,
        value: item5011?.value!,
      };
    });
};

export const prepareEntryForSubmit = (selectedEntries: TimelineModel[]) => {
  const entriesForSubmit: TimelineModel[] = [];
  selectedEntries.map((entry: TimelineModel) => {
    if (entry?.type === TimelineEntityType.TimelineEntityType_Service_Chain) {
      if (!entry?.encounterServiceChain?.services) return;
      for (const service of entry?.encounterServiceChain?.services) {
        entriesForSubmit.push({
          ...entry,
          id: service?.serviceId,
          encounterServiceChain: {
            ...entry?.encounterServiceChain,
            services: [service],
          },
        });
      }
    } else {
      entriesForSubmit.push(entry);
    }
  });
  return entriesForSubmit;
};
