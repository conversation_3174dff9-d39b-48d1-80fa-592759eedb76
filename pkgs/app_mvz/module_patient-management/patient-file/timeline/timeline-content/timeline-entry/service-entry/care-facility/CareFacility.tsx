
import React, { useState } from 'react';
import {
  Divider,
  InputGroup,
  Button,
  Intent,
} from '@tutum/design-system/components/Core';
import { Flex, BodyTextS, MessageBar } from '@tutum/design-system/components';
import { scaleSpacePx } from '@tutum/design-system/styles';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import Theme, { IMvzTheme } from '@tutum/mvz/theme';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface ICareFacilityProps {
  className?: string;
  theme?: IMvzTheme;
  onSave?: (name: string, ort: string) => void;
  onCancel?: () => void;
  name?: string;
  ort?: string;
  isDisabled: boolean;
}

const CareFacility = ({
  className,
  name,
  ort,
  isDisabled,
  onSave,
}: ICareFacilityProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Timeline
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Timeline',
  });
  const [isEditing, setIsEditing] = useState(false);
  const [nameValue, setNameValue] = useState<string>('');
  const [ortValue, setOrtValue] = useState<string>('');

  const renderNameInput = () => {
    return (
      <Flex className="name-input">
        <BodyTextS
          margin={`0 0 ${scaleSpacePx(1)} 0`}
          color={COLOR.TEXT_SECONDARY_NAVAL}
        >
          {t('name')}
        </BodyTextS>
        <InputGroup
          defaultValue={name}
          onChange={(event) => setNameValue(event.target.value)}
        />
      </Flex>
    );
  };

  const renderOrtInput = () => {
    return (
      <Flex className="ort-input">
        <BodyTextS
          margin={`0 0 ${scaleSpacePx(1)} 0`}
          color={COLOR.TEXT_SECONDARY_NAVAL}
        >
          {t('location')}
        </BodyTextS>
        <InputGroup
          defaultValue={ort}
          onChange={(event) => setOrtValue(event.target.value)}
        />
      </Flex>
    );
  };

  const renderButtonGroup = () => {
    const handleCancel = () => {
      setIsEditing(false);
    };

    const handleSave = () => {
      setIsEditing(false);
      onSave(nameValue || name, ortValue || ort);
    };

    return (
      <Flex className="button-group">
        <Button className="save" intent={Intent.PRIMARY} onClick={handleSave}>
          {t('save')}
        </Button>
        <Button className="cancel" onClick={handleCancel}>
          {t('cancel')}
        </Button>
      </Flex>
    );
  };

  const renderCareFacilityInput = () => {
    return (
      <Flex>
        <Divider />
        <Flex column>
          <BodyTextS
            fontWeight="SemiBold"
            margin={`${scaleSpacePx(1)} 0`}
            color={COLOR.TEXT_SECONDARY_NAVAL}
          >
            {t('timelineService_care_facility_details')}
          </BodyTextS>
          <Flex className="care-facility-input">
            {renderNameInput()}
            {renderOrtInput()}
            {renderButtonGroup()}
          </Flex>
        </Flex>
      </Flex>
    );
  };

  const renderCareFacilityDetails = () => {
    // const openCareFacilityInput = () => {
    //   if (isDisabled) {
    //     return;
    //   }

    //   setIsEditing(true);
    // };

    return (
      <Flex className="care-facility-info" auto>
        <Divider />
        {/* <Flex column auto onClick={openCareFacilityInput}> */}
        <Flex column auto>
          <BodyTextS fontWeight="SemiBold" margin={`${scaleSpacePx(1)} 0 0 0`}>
            {t('timelineService_careFacility_label')}
          </BodyTextS>
          <BodyTextS>
            {t('name')}: {name}
          </BodyTextS>
          <BodyTextS>
            {t('location')}: {ort}
          </BodyTextS>
        </Flex>
      </Flex>
    );
  };

  const renderErrorMessageBar = () => {
    const openCareFacilityInput = () => {
      setIsEditing(true);
    };

    return (
      <MessageBar
        type="warning"
        className="care-facility-missing-msg"
        content={t('timelineService_careFacility_missingMessage')}
        actionButtonGroup={
          <Flex>
            <Button intent={Intent.PRIMARY} onClick={openCareFacilityInput}>
              {t('add')}
            </Button>
          </Flex>
        }
      />
    );
  };

  const renderCareFacilityInfo = () => {
    return isEmpty(name) || isEmpty(ort)
      ? renderErrorMessageBar()
      : renderCareFacilityDetails();
  };

  return (
    <Flex className={className}>
      {isEditing ? renderCareFacilityInput() : renderCareFacilityInfo()}
    </Flex>
  );
};

export default CareFacility;
