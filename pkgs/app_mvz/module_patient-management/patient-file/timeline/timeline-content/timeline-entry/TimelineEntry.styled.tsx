import React from 'react';

import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { scaleSpacePx } from '@tutum/design-system/styles';
import { COLOR } from '@tutum/design-system/themes/styles';
import Theme from '@tutum/mvz/theme';
import TimelineService from '../../Timeline.service';
import OriginalTimelineEntry, { ITimelineEntryProps } from './TimelineEntry';

const styled = Theme.styled;
const TimelineEntry: React.ComponentType<ITimelineEntryProps> = styled(
  OriginalTimelineEntry
).attrs(({ className }) => ({
  className: getCssClass('sl-TimelineEntry', className),
}))`

  & {
    padding: 16px;
    box-shadow: inset 0px -1px 0px ${COLOR.BORDER_INPUT};
    gap: 8px;
    background: ${(props) => {
    if (props.isHistoryMode) {
      return props.payLoadHistoryMode.bg;
    }
    const entryColor = TimelineService.parseEntryColor(
      props.entry,
      props.theme.timelineTheme
    );
    if (!entryColor) {
      return 'none';
    }
    return entryColor?.bg;
  }};

    // apply color to all text in entry content except message bar and billed entries
    &:not(:hover):not(:has(div.billed)) .entry-content .sl-BodyText-M:not(.sl-message-bar-text),
    &:not(:hover):not(:has(div.billed)) .entry-text span:not(.bp5-fill,.sl-message-bar-text,.bp5-button-text),
    &:not(:hover):not(:has(div.billed)) .entry-content span:not(.bp5-fill,.sl-message-bar-text,.bp5-button-text) {
      color: ${(props) => {
    if (props.isHistoryMode) {
      return props.payLoadHistoryMode.text;
    }
    return TimelineService.parseEntryColor(
      props.entry,
      props.theme.timelineTheme
    )?.text;
  }};
    }

    .edit_inline {
      flex: 2;
    }
    .close_edit_inline {
      margin: 8px 8px 0 0;
      width: ${scaleSpacePx(6)};
      height: ${scaleSpacePx(6)};
      img {
        width: ${scaleSpacePx(6)};
        height: ${scaleSpacePx(6)};
      }
      &:hover {
        cursor: pointer;
      }
    }
    .action-group .bp5-popover-target > svg {
      width: 24px;
      height: 24px;
      fill: ${COLOR.TEXT_PRIMARY_BLACK};
    }
    .action-group {
      visibility: hidden;
    }
    &:not(:has(div.billed)):not(.non-editable):hover {
      .action-group {
        visibility: visible;
        cursor: pointer;
      }
      background-color: ${COLOR.BACKGROUND_HOVER};
      .date p,
      .hover-text {
        color: ${COLOR.TEXT_PRIMARY_BLACK};
        p {
          color: ${COLOR.TEXT_PRIMARY_BLACK};
        }
      }
      .sl-GDTEntry .gdt-content p {
        color: ${COLOR.TEXT_PRIMARY_BLACK};
      }
    }

    &.selected {
      background-color: ${COLOR.INFO_SECONDARY_PRESSED};
    }

    &.non-editable {
      cursor: default;
    }

    .entry-wrapper {
      position: relative;
    }

    .entry-avatar {
      margin-left: ${scaleSpacePx(2)};
      margin-right: ${scaleSpacePx(2)};
      height: 100%;
      > p,
      span {
        --font-size: 9px;
        font-weight: 600;
        font-size: var(--font-size) !important;
        line-height: 16px;
        color: ${COLOR.TEXT_PRIMARY_BLACK} !important;
      }

      .non_editable {
        height: 100%;
        margin-left: auto;
        width: ${scaleSpacePx(2)};
        background-color: ${COLOR.BACKGROUND_TERTIARY_DIM};
        border-radius: ${scaleSpacePx(1)};
        margin-top: ${scaleSpacePx(1)};
      }
    }

    .actions-group {
      margin-right: ${scaleSpacePx(2)};
      height: ${scaleSpacePx(7)};
      img.sl-Svg,
      img.sl-more-icon {
        width: ${scaleSpacePx(6)};
        height: ${scaleSpacePx(6)};
      }
    }

    > .bp5-divider {
      margin: ${scaleSpacePx(1)};
    }

    .date {
      width: ${scaleSpacePx(25)};
      margin-left: 8px;
      p {
        color: ${(props) =>
    TimelineService.parseEntryColor(
      props.entry,
      props.theme.timelineTheme
    )?.text};
        font-size: 16px;
      }
    }

    .billed_check {
      width: 40px;
      margin-right: ${scaleSpacePx(1)};
      margin-left: ${scaleSpacePx(-2)};
      margin-top: ${scaleSpacePx(-2)};
      &.prescription {
        transform: translateY(4px);
      }
    }

    .entry-billed {
      margin: 6px 8px 0 -8px;
    }

    .entry-content {
      flex-direction: column;
      flex: 2;
      white-space: break-spaces;

      .bp5-divider {
        margin-left: 0 !important;
        margin-bottom: 0 !important;
      }

      &.billed {
        p,
        span {
          color: ${COLOR.TEXT_TERTIARY_SILVER};
          cursor: no-drop;
        }
        .action-button {
          .bp5-button.sl-Button {
            span.bp5-button-text {
              cursor: pointer;
            }
          }
        }
        .actions-group {
          display: none;
          span.bp5-popover-target {
            display: none;
          }
          width: 24px;
        }
        .sl-ServiceEntry {
          .actions-group {
            display: flex;
            span.bp5-popover-target {
              // Allow to display popover for billed entries
              display: flex;
              cursor: pointer;
            }
            width: 24px;
          }
        }
      }
    }

    .focus-mode {
      position: relative;
      height: calc(100% + 16px);
      margin: -8px 0;
      padding: 8px 0;
      border: 2px solid ${COLOR.TAG_BACKGROUND_PURPLE};
    }

    .main-group {
      display: flex;
      white-space: nowrap;
      justify-content: center;
      align-items: center;
      min-width: 24px;
      margin-right: 4px;
      padding: 2px;
      border: 1px solid rgba(19, 50, 75, 0.1);
      border-radius: 4px;
      font-size: 11px;
      font-weight: 600;
      max-height: 30px;
      min-height: 24px;
    }

    .timeline-deletion-remain {
      margin-right: 8px;
    }

    .schein-name {
      position: absolute;
      top: -22px;
      padding: 2px;
      color: ${COLOR.TEXT_WHITE};
      background-color: ${COLOR.TAG_BACKGROUND_PURPLE};
      border-radius: 4px 4px 0 0;
    }

    .sl-content-tooltip {
      min-width: 70px;
      p {
        font-size: 11px !important;
      }
    }

    .bp5-tooltip .bp5-popover-content {
      width: max-content;
    }
  }
`;

export default TimelineEntry;
