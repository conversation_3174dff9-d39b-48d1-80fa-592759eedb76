
import groupBy from 'lodash/groupBy';
import React, { useCallback, useState } from 'react';

import { BodyTextM, Button, Flex, Svg } from '@tutum/design-system/components';
import {
  Intent,
  Menu,
  MenuItem,
  Popover,
} from '@tutum/design-system/components/Core';
import {
  getCssClass,
  isEmpty,
} from '@tutum/design-system/infrastructure/utils';
import type {
  EditResponse,
  GroupByQuarter,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { takeOverDiagnosisWithScheinId } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { ActionType, TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { handleReduceErrors } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import type { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import AdditionalInfoEntry from './AdditionalInfoEntry';
import ValidationMessage from '../service-entry/ValidationMessage';
import type { Nullable } from '@tutum/design-system/infrastructure/models';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import type { AdditionalInfoParent } from '@tutum/hermes/bff/repo_encounter';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { TakeoverDiagnosisType } from '@tutum/hermes/bff/legacy/timeline_common';
import type { ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  backgroundMainGroup,
  colorMainGroup,
  formatScheinName,
} from '@tutum/mvz/_utils/scheinFormat';
import SelectDiagnosisDialog from '@tutum/mvz/components/select-diagnosis-dialog/SelectDiagnosisDialog.styled';
import { MainGroup } from '@tutum/hermes/bff/common';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import type { PayLoadHistoryMode } from '../util';
import HighlightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import type { EncounterGoaService } from '@tutum/hermes/bff/legacy/repo_encounter';
import { customAdditionalInfos } from '../service-entry/utils';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import SingleServiceChainEntry from '../service-chain-entry/single-service-chain-entry/SingleServiceChainEntry';
import { BaseComposerRowCommand } from '@tutum/design-system/composer/Composer.type';

const trashIcon = '/images/trash-bin-red.svg';
const editIcon = '/images/edit-3.svg';
const MoreIcon = '/images/more.svg';

const isLeanService = (service: EncounterGoaService) => {
  return (
    service?.errors?.length === 0 &&
    service.additionalInfos.length === 0 &&
    !service.isChangeDefault
  );
};
export interface IServiceEntryProps {
  className?: string;
  index?: number;
  entry?: TimelineModel;
  treatmentDoctorId?: string;
  patientId?: string;
  quarter?: GroupByQuarter;
  onEdit?: (isEditing: boolean) => void;
  isEditing?: boolean;
  doctorProfile?: IEmployeeProfile;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  patient: IPatientProfile;
  currentSchein?: ScheinItem;
  keyword?: string;
  onRemoveEntry: (hardDelete?: boolean) => void;
  setItemToEditDate?: () => void;
  setEditInline?: () => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  payLoadHistoryMode: PayLoadHistoryMode;
  isHistoryMode: boolean;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
}

const GoaServiceChainEntry = (props: IServiceEntryProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.ServiceEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'ServiceEntry',
  });

  const { t: tTimelineEncounter } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimelineEncounter
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimelineEncounter',
  });
  const {
    className,
    doctorIcon,
    patientId,
    currentSchein,
    openCreateSchein,
    setEditInline,
    hasBilled,
    entry,
  } = props;

  const errors = props?.entry?.errors;
  const type = props?.entry?.type;
  const services = entry?.encounterGoaServiceChain.goaServices || [];
  const leanServices = services.filter(isLeanService);
  const restServices = services.filter((s) => !isLeanService(s));

  let firstServiceTimeline = services[0];

  //NOTE: hide errors for billed service code
  firstServiceTimeline = hasBilled
    ? {
        ...firstServiceTimeline,
        errors: [],
      }
    : firstServiceTimeline;

  const { isHistoryMode, matchedTokens } = useTimeLineStore();
  const { allowRemoveTimeline } = useSettingStore();
  const { getDoctorById } = GlobalContext.useContext();

  const [cachedScheinId, setCachedScheinId] = useState<Nullable<string>>(null);

  const allowDirectEditor = errors?.find(
    (error) =>
      error.type === 'error' &&
      isEmpty(error.akaFunction, true) &&
      error.errorCode === 'SERVICE_VALIDATION_ERROR'
  );

  const style = !allowDirectEditor ? '' : 'warning';

  const [isLoading] = useState(false);

  const renderActionMenu = () => {
    const actions: React.JSX.Element[] = [];

    if (!hasBilled) {
      const actionEdit = (
        <MenuItem
          key="edit_1"
          icon={<Svg src={editIcon} size={20} />}
          text={tTimelineEncounter('editEntry')}
          onClick={() => props.setEditInline()}
        />
      );
      actions.push(actionEdit);
    }

    if (!hasBilled && allowRemoveTimeline) {
      const actionRemove = (
        <MenuItem
          key="0"
          icon={<Svg src={trashIcon} />}
          text={t('removeEntry')}
          onClick={() => props.onRemoveEntry(false)}
        />
      );

      actions.push(actionRemove);
    }

    return actions;
  };

  const renderServiceContent = (item: EncounterGoaService) => {
    const { entry } = props;
    if (!item) return;

    return (
      <Flex w="100%" justify="flex-start" gap={6}>
        <SingleServiceChainEntry
          item={item}
          type={entry?.auditLogs[0]?.actionType}
        />
        {renderAllAdditionalInfo(item)}
      </Flex>
    );
  };

  const displayInfo = useCallback(
    (info: Partial<AdditionalInfoParent>): string | React.ReactNode => {
      const transformHHMM = () => {
        for (const error of firstServiceTimeline?.errors || []) {
          if (error?.metaData?.field === '5006') {
            return info.value;
          }
        }
        const HH = info?.value?.slice(0, 2);
        const MM = info?.value?.slice(2);
        return `${HH}:${MM}`;
      };
      const mapByFK: Record<string, () => string | React.ReactNode> = {
        '5006': transformHHMM,
        '5900': transformHHMM,
      };
      const display = mapByFK[String(info.fK)];

      return display ? display() : info.value;
    },
    [firstServiceTimeline]
  );

  const renderAllAdditionalInfo = (item: EncounterGoaService) => {
    const treatmentDoctorId = props.entry?.treatmentDoctorId;
    if (!treatmentDoctorId) return;

    const userProfile = getDoctorById(treatmentDoctorId);
    if (!userProfile) return;

    const specialInfos: AdditionalInfoParent[] = [
      {
        fK: 'factor',
        value: item.factor.toString().replace('.', ','),
        children: null,
      },
      {
        fK: 'quantity',
        value: item.quantity.toString(),
        children: null,
      },
    ];
    const additionalInfos = customAdditionalInfos(
      item?.additionalInfos || [],
      userProfile
    );
    let listRenderAdditionalInfo = item.isChangeDefault ? specialInfos : [];
    listRenderAdditionalInfo = listRenderAdditionalInfo.concat(additionalInfos);

    return (
      <span
        className="add-info__container"
        style={{
          marginBottom: 0,
          textDecoration:
            props.entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'line-through'
              : 'none',
          color:
            props.entry?.auditLogs[0]?.actionType === ActionType.Remove
              ? 'red'
              : undefined,
        }}
      >
        {listRenderAdditionalInfo.map((info, idx) => (
          <span key={`${info.fK}${info.value}`} className="add-info__item">
            <b>{'('}</b>
            <AdditionalInfoEntry
              info={info}
              isChild={false}
              isLast={listRenderAdditionalInfo?.length - 1 === idx}
              displayInfo={displayInfo}
            />
            <b>{')'}</b>
          </span>
        ))}
      </span>
    );
  };

  const renderMessageBar = useCallback(
    (item: EncounterGoaService) => {
      const errors = item?.errors ?? [];
      if (!errors.length) {
        return null;
      }

      const groupErrors = handleReduceErrors(errors);
      const grouped = groupBy(groupErrors ?? [], 'type');
      return (
        <Flex column>
          {Object.keys(grouped ?? {}).map((key) => {
            return grouped[key].map((error, index: number) => {
              let actionButtonGroup: React.ReactNode = null;

              if (
                error.errorCode ===
                ErrorCode.ErrorCode_Validation_Missing_ScheinId
              ) {
                actionButtonGroup = (
                  <Button
                    intent={Intent.PRIMARY}
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={async () => {
                      patientFileActions.schein.setIssueDate(
                        entry.selectedDate
                      );
                      openCreateSchein(MainGroup.PRIVATE);
                    }}
                  >
                    {t('btnCreateNewSchein')}
                  </Button>
                );
              }

              return (
                <ValidationMessage
                  key={`${key}${error.errorCode}`}
                  index={index}
                  total={grouped[key]?.length}
                  error={error}
                  actionButtonGroup={actionButtonGroup}
                />
              );
            });
          })}
        </Flex>
      );
    },
    [t, openCreateSchein, isLoading, entry.selectedDate]
  );

  const handleTakeOver = async (
    data: TimelineModel[],
    mappingTreatmentRelevent: { [key: string]: boolean }
  ) => {
    await takeOverDiagnosisWithScheinId({
      scheinId: cachedScheinId,
      timelineModelIds: data
        .filter((d) => d.id && Boolean(d['isAddTimeline']) === false)
        .map((d) => d.id),
      newDiagnosis: data.filter((d) => !d.id),
      mappingTreatmentRelevent: mappingTreatmentRelevent,
    });
    setCachedScheinId(undefined);
  };

  return (
    <React.Fragment>
      <Flex auto className={getCssClass(className, style)}>
        <Flex>
          <Flex auto>
            <Flex column className="service-info" onClick={setEditInline}>
              <Flex className="entry-type" gap={0} align="center">
                <Flex className={`entry-text ${type?.toLowerCase()}`}>
                  <HighlightWrapper matchedTokens={matchedTokens}>
                    <span className="name">
                      {BaseComposerRowCommand.SERVICE_CHAIN}
                    </span>
                    {leanServices.map((e) => {
                      return (
                        <div key={e.code}>
                          {renderServiceContent(e)}
                          {!isHistoryMode ? <>{renderMessageBar(e)}</> : null}
                        </div>
                      );
                    })}
                  </HighlightWrapper>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
          <Flex align="center">
            <TimelineDeletionRemain
              className="timeline-deletion-remain"
              entry={entry}
            />
            {!!currentSchein && (
              <div
                className="main-group"
                style={{
                  textDecoration:
                    entry?.auditLogs[0]?.actionType === ActionType.Remove
                      ? 'line-through'
                      : '',
                  color: colorMainGroup(currentSchein.scheinMainGroup),
                  backgroundColor: backgroundMainGroup(
                    currentSchein.scheinMainGroup
                  ),
                }}
              >
                {formatScheinName(currentSchein, t('pseudo'))}
              </div>
            )}
            <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
            {renderActionMenu().length > 0 && (
              <TimelineActionHOC
                entry={props?.entry}
                hasBilled={hasBilled}
                displayInfo={displayInfo}
              >
                <Flex className="actions-group">
                  <Popover
                    content={
                      <Menu className="action-menu">{renderActionMenu()}</Menu>
                    }
                  >
                    <Svg className="more-icon" src={MoreIcon} />
                  </Popover>
                </Flex>
              </TimelineActionHOC>
            )}
          </Flex>
        </Flex>
        <HighlightWrapper matchedTokens={matchedTokens}>
          {restServices.map((e) => {
            return (
              <div key={e.freeText}>
                {renderServiceContent(e)}
                {!isHistoryMode ? <>{renderMessageBar(e)}</> : null}
              </div>
            );
          })}
        </HighlightWrapper>
      </Flex>
      {cachedScheinId && props?.entry?.id && (
        <SelectDiagnosisDialog
          takeoverDiagnosisType={
            TakeoverDiagnosisType.TakeoverDiagnosisPsychotherapy
          }
          requiredAtLeast={1}
          patientId={patientId}
          onTakeover={(existed, newvalues, mappingTreatmentRelevent) => {
            handleTakeOver(
              [...existed, ...newvalues],
              mappingTreatmentRelevent
            );
          }}
          scheinId={cachedScheinId}
          onCancel={() => {}}
          serviceId={entry.id}
        />
      )}
    </React.Fragment>
  );
};

export default GoaServiceChainEntry;
