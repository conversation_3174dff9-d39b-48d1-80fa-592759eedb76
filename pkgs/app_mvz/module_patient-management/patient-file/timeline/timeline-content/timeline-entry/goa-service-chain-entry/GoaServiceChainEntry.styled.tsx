
import type React from 'react';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { scaleSpacePx } from '@tutum/design-system/styles';
import Theme from '@tutum/mvz/theme';
import OriginalServiceEntry, {
  type IServiceEntryProps,
} from './GoaServiceChainEntry';
import { leftSidebarColor } from '../EntryCommon.styled';
import TimelineService from '../../../Timeline.service';
import { COLOR } from '@tutum/design-system/themes/styles';

const styled = Theme.styled;

const ServiceEntry: React.ComponentType<IServiceEntryProps> = styled(
  OriginalServiceEntry
).attrs(({ className }) => ({
  className: getCssClass('sl-GoaServiceChainEntry', className),
}))`
  flex-direction: column;
  position: relative;

  &:before {
    ${leftSidebarColor('transparent')}
  }

  .service-info {
    cursor: pointer;
    flex: 1;
  }

  &:hover,
  &.is-opening-menu {
    .action-group {
      align-items: center;
      position: static;

      .sl-Svg {
        .svg {
          width: ${scaleSpacePx(4)};
          height: ${scaleSpacePx(4)};
        }
        margin-left: ${scaleSpacePx(4)};

        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .extra-info {
    flex: 1 1 auto;
    .doctor-info {
      &:hover {
        cursor: pointer;
      }
      .bp5-divider {
        margin-right: ${scaleSpacePx(2)};
        margin-bottom: 0;
        border-right: ${scaleSpacePx(1)} solid
          ${COLOR.BACKGROUND_TERTIARY_DIM};
        border-bottom: none;
      }
    }
  }
  .add-info__container {
    display: inline-flex;
    flex-wrap: wrap;
    gap: 4px;
    .add-info__item {
      display: flex;
      margin-top: 2px;
    }
  }

  .entry-type {
    align-items: flex-start;
    .gap {
      padding: 0 ${scaleSpacePx(1)};
    }
  }
  .sl-Flex.entry-text.service_goa_chain {
    width: 100%;
    gap: 16px;
    align-items: flex-end;
    .name {
      margin-bottom: 3px;
    }
    .tag_not_accepted_by_kv {
      border-radius: 12px;
      justify-content: center;
      font-weight: 600;
      font-size: 11px;
      line-height: 16px;
      letter-spacing: -0.2px;
      padding: 4px 8px;
      background-color: ${COLOR.TAG_BACKGROUND_RED_SUBTLE} !important;
      > span.bp5-text-overflow-ellipsis {
        font-weight: 600;
        font-size: 11px;
        line-height: 16px;
        color: ${COLOR.NEGATIVE_PRESSED} !important;
        margin: 0px 4px;
      }
    }
  }
  .with-icon {
    color: ${(props) => {
      if (props.isHistoryMode) {
        return props.payLoadHistoryMode.text;
      }
      return TimelineService.parseEntryColor(
        props.entry,
        props.theme.timelineTheme
      )?.text;
    }};
  }
`;

export default ServiceEntry;
