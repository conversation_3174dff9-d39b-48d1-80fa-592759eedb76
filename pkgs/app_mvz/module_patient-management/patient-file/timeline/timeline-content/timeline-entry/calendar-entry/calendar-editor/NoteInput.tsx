import React from 'react';
import { Formik, Field } from 'formik';
import {
  Button,
  Intent,
  InputGroup,
} from '@tutum/design-system/components/Core';
import { Flex, Svg } from '@tutum/design-system/components';
import { IMvzTheme } from '@tutum/mvz/theme';
import I18n from '@tutum/infrastructure/i18n';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';

const ActionIcon = '/images/action-icon.svg';
export interface INoteEditorProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  isLoading: boolean;
  onSave: (any) => void;
  onCancel: () => void;
}

const NoteEditor = React.memo(
  ({ entry, isLoading, onSave, onCancel }: INoteEditorProps) => {
    return (
      <Formik
        initialValues={{
          note: entry.encounterCalendarTimeline?.waitingRoomPatient?.note,
        }}
        onSubmit={onSave}
        render={({ isSubmitting }) => {
          return (
            <Flex align="center" w="100%" mb={15}>
              <Flex w="100%" pr={24}>
                <Field name="note">
                  {({ field }) => {
                    return (
                      <InputGroup
                        {...field}
                        autoFocus
                        fill
                        width="100%"
                        maxLength={60}
                        disabled={isSubmitting || isLoading}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            onSave({ note: e.currentTarget.value });
                          }
                          if (e.key === 'Escape') {
                            onCancel();
                          }
                        }}
                      />
                    );
                  }}
                </Field>
              </Flex>
              <Flex mr={6}>
                <div onClick={onCancel} className="close_edit_inline">
                  <Svg src={ActionIcon} />
                </div>
              </Flex>
            </Flex>
          );
        }}
      />
    );
  }
);

export default I18n.withTranslation(NoteEditor, {
  namespace: 'PatientManagement',
  nestedTrans: 'NoteEditor',
});
