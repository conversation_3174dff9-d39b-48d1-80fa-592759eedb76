
import React, { useContext, useMemo, useState } from 'react';
import {
  alertError,
  alertSuccessfully,
  Button,
  Flex,
  MessageBar,
  Svg,
} from '@tutum/design-system/components';
import { IMvzTheme } from '@tutum/mvz/theme';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { useCounterMinute } from '@tutum/mvz/hooks/useCounterMinute';
import {
  startTreatment,
  stopTreatment,
  useMutationUpdateNote,
} from '@tutum/hermes/bff/legacy/app_admin_waiting_room';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import NoteEditor from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/calendar-entry/calendar-editor/NoteInput';
import { EditResponse } from '@tutum/hermes/bff/app_mvz_timeline';
import { cloneDeep } from 'lodash';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { isNullUUID } from '@tutum/design-system/infrastructure/utils';

const SmileIcon = '/images/smile.svg';
const FrownIcon = '/images/frown.svg';

export enum StatusEntryCalendar {
  WAITING = 'waiting',
  TREATING = 'treating',
  COMPLETED = 'completed',
}
export interface ICalendarEntryProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  onEditTimelineItem?: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error: any) => void
  ) => void;
}

const CalendarEntry = ({
  className,
  entry: { type },
  entry,
  onEditTimelineItem,
}: ICalendarEntryProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.CalendarEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'CalendarEntry',
  });

  const { t: tTimeline } = I18n.useTranslation<keyof typeof FormI18n.Timeline>({
    namespace: 'Form',
    nestedTrans: 'Timeline',
  });

  const [loading, setLoading] = useState<boolean>(false);
  const [isLoadingNote, setLoadingNote] = useState<boolean>(false);

  const updateNote = useMutationUpdateNote({});

  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();

  const roomName = useMemo(() => {
    return entry.encounterCalendarTimeline.waitingRoomName;
  }, [entry]);

  const counter = useCounterMinute(
    entry.encounterCalendarTimeline.waitingRoomPatient?.startWaitingTime,
    entry.encounterCalendarTimeline.waitingRoomPatient?.endWaitingTime
  );

  const meansurementTime = useMemo(() => {
    return entry.encounterCalendarTimeline.acceptableWaitingTimeInMinutes || 15;
  }, [entry]);

  const isDeleted = useMemo(() => {
    const data = entry.encounterCalendarTimeline.waitingRoomPatient;
    return data?.startWaitingTime &&
      data?.endWaitingTime &&
      data?.startTreatmentTime &&
      data?.endTreatmentTime
      ? true // All tracking fields had data then this variable should be return true for not show counter and message bar
      : typeof data?.isDeleted == 'boolean'
        ? data?.isDeleted
        : true;
  }, [entry]);

  const isEditable = useMemo(() => {
    const data = entry.encounterCalendarTimeline.waitingRoomPatient;
    return !data?.startTreatmentTime;
  }, [entry]);

  const [contentMsg, buttonMsg] = useMemo(() => {
    const data = entry.encounterCalendarTimeline.waitingRoomPatient;
    if (data?.isDeleted) {
      return [undefined, undefined];
    }
    if (!data?.endWaitingTime) {
      return [t('startTreatmentToDocument'), t('startTreatment')];
    }
    return [undefined, undefined];
  }, [entry]);

  const isHappy = useMemo(() => {
    return counter <= meansurementTime;
  }, [counter, entry]);

  const note = useMemo(() => {
    return entry.encounterCalendarTimeline.waitingRoomPatient?.note;
  }, [entry]);

  const [isEdit, setIsEdit] = useState(false),
    activeTimeMeasurement = useMemo(() => {
      return entry.encounterCalendarTimeline.activeTimeMeasurement;
    }, [entry]);

  const classNameTimer = useMemo(() => {
    return isHappy ? 'happy' : 'unhappy';
  }, [isHappy]);

  const sourceTimer = useMemo(() => {
    return isHappy ? SmileIcon : FrownIcon;
  }, [isHappy]);

  const handleButtonMsg = async () => {
    try {
      setLoading(true);
      const data = entry.encounterCalendarTimeline.waitingRoomPatient;
      const roomId = data?.waitingRoom;
      const patientId = data?.patientId;

      if (!data?.endWaitingTime) {
        await startTreatment({ roomId, patientId });
      } else if (!data?.endTreatmentTime) {
        await stopTreatment({ roomId, patientId });
      }
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  return (
    <div className={className}>
      <Flex
        auto
        className={`sl-${type}Entry`}
        onClick={() => isEditable && setIsEdit(true)}
      >
        <p className="freetext_content hover-text">
          CAL - {t('patientPresentIn')}:&nbsp;
          {!!roomName && (
            <i>
              {roomName}
              {'. '}
            </i>
          )}
          {activeTimeMeasurement && (
            <>
              &nbsp;{t('waitingTimeIs')} &nbsp;
              <span className={`counter_time ${classNameTimer}`}>
                <Svg src={sourceTimer} width={16} height={16} />
                &nbsp;
                {counter} {t(`minute${counter > 1 ? 's' : ''}`)}
              </span>
            </>
          )}
        </p>
      </Flex>
      {isEdit ? (
        <NoteEditor
          className="note-editor"
          entry={entry}
          isLoading={isLoadingNote || updateNote.isPending}
          onSave={async (data) => {
            const updateEntry = cloneDeep(entry);

            updateEntry.encounterCalendarTimeline.waitingRoomPatient.note =
              data.note;

            // for skip validation rule, need to update logic later
            if (isNullUUID(updateEntry.treatmentDoctorId)) {
              updateEntry.treatmentDoctorId = currentLoggedinUser.id;
            }

            setLoadingNote(true);

            onEditTimelineItem(
              updateEntry,
              () => {
                alertSuccessfully(tTimeline('TimelineUpdatedSuccess'));
                setIsEdit(false);
                setLoadingNote(false);

                updateNote.mutate({
                  roomId:
                    entry.encounterCalendarTimeline.waitingRoomPatient
                      ?.waitingRoom || '',
                  patientId: entry.patientId,
                  note: data.note,
                });
              },
              () => {
                alertError(tTimeline('TimelineUpdatedFail'));
                setLoadingNote(false);
              }
            );
          }}
          onCancel={() => {
            setIsEdit(false);
          }}
        />
      ) : (
        note && (
          <p
            className="sl-Calendar-Note"
            onClick={() => isEditable && setIsEdit(true)}
          >
            <b>{t('note')}</b>:{' '}
            <span className="sl-Calednar-Note-Content">{note}</span>
          </p>
        )
      )}
      {!isDeleted && contentMsg && (
        <MessageBar
          type="info"
          hasBullet={false}
          content={contentMsg}
          hasAlign
          actionButtonGroup={
            buttonMsg && (
              <Button
                intent="primary"
                onClick={handleButtonMsg}
                loading={loading}
              >
                {buttonMsg}
              </Button>
            )
          }
        />
      )}
    </div>
  );
};

export default CalendarEntry;
