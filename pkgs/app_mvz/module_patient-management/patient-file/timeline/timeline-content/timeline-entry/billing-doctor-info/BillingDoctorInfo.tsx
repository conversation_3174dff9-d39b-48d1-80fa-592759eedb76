import React, { useContext, useMemo } from 'react';
import { Tooltip, PopoverPosition } from '@tutum/design-system/components/Core';
import { Avatar, BodyTextM, Flex, Svg } from '@tutum/design-system/components';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { ScheinItem } from '@tutum/hermes/bff/legacy/schein_common';
import {
  checkIsKvSchein,
  checkIsSvSchein,
} from '@tutum/mvz/_utils/scheinFormat';

const CurrencyEuroGreenIcon = '/images/currency-euro-green.svg';

export interface BillingDoctorInfoProps {
  className?: string;
  entry: TimelineModel;
  schein?: ScheinItem;
}

const BillingDoctorInfo = ({
  className,
  entry,
  schein,
}: BillingDoctorInfoProps) => {
  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  const patientFileStore = usePatientFileStore();
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Timeline
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Timeline',
  });
  const isSvSchein = checkIsSvSchein(schein);
  const isKvSchein = checkIsKvSchein(schein);

  const billingDoctorInfo = useMemo(() => {
    if (isKvSchein) {
      const treatmentDoctorId = entry.treatmentDoctorId;
      const treatmentDoctor = doctorList.find(
        (doctor) => doctor.id === treatmentDoctorId
      );
      return treatmentDoctor;
    }

    const scheinIds = entry?.scheinIds;
    if (!scheinIds?.length) {
      return null;
    }

    const billingDoctor = patientFileStore.schein.originalList.find(
      (schein) => schein.scheinId === scheinIds[0]
    );

    if (!billingDoctor || !billingDoctor?.doctorId) {
      return null;
    }

    const billingDoctorId = billingDoctor.doctorId;
    return doctorList.find((doctor) => doctor.id === billingDoctorId);
  }, [
    doctorList,
    patientFileStore.schein.originalList,
    entry.scheinIds,
    isKvSchein,
  ]);

  const renderTooltipContent = useMemo(() => {
    if (!billingDoctorInfo) {
      return undefined;
    }

    return (
      <Flex column>
        <BodyTextM fontWeight={600} margin="0 0 8px">
          {t('billingTo', {
            name: nameUtils.getDoctorName(billingDoctorInfo),
          })}
        </BodyTextM>
        <BodyTextM>
          {t('lanrBsnr', {
            lanr: billingDoctorInfo.lanr,
            bsnr: billingDoctorInfo.bsnr,
          })}
        </BodyTextM>
        {isSvSchein && !!billingDoctorInfo.havgId && (
          <BodyTextM>
            {t('havgID', {
              havgId: billingDoctorInfo.havgId,
              havgVpId: billingDoctorInfo.havgVpId,
            })}
          </BodyTextM>
        )}
        {isSvSchein && !!billingDoctorInfo.mediId && (
          <BodyTextM>
            {t('mediVPId', {
              mediId: billingDoctorInfo.mediId,
              mediVpId: billingDoctorInfo.mediVpId,
            })}
          </BodyTextM>
        )}
      </Flex>
    );
  }, [billingDoctorInfo, isSvSchein]);

  if (!billingDoctorInfo) {
    return null;
  }

  return (
    <Tooltip
      popoverClassName="sl-BillingDoctorInfo"
      content={renderTooltipContent}
      position={PopoverPosition.TOP}
    >
      <Flex className={className}>
        <Avatar initial={billingDoctorInfo.initial} />
        <Svg className="currency-euro-icon" src={CurrencyEuroGreenIcon} />
      </Flex>
    </Tooltip>
  );
};

export default BillingDoctorInfo;
