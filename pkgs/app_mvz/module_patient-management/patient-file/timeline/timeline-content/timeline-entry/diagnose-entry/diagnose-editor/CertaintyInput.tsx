import React, { useState } from 'react';
import {
  MenuItem,
  Button,
  Alignment,
  Icon,
} from '@tutum/design-system/components/Core';
import {
  ItemRendererProps,
  Select,
} from '@tutum/design-system/components/Select';
import I18n from '@tutum/infrastructure/i18n';
import FormI18n from '@tutum/mvz/locales/en/Form.json';

import { Flex, Box, IMenuItem } from '@tutum/design-system/components';
import { getCertaintyList } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/diagnose-block/DiagnosisNode.util';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';

export interface ICertaintyInputProps {
  className?: string;
  entry?: TimelineModel;
  onCertaintyChange: (certainty: string) => void;
}

const CertaintySelect = Select;

export default React.memo(
  ({
    className,
    entry,
    onCertaintyChange,
  }: ICertaintyInputProps) => {
    const { t } = I18n.useTranslation<
      keyof typeof FormI18n.AddNewDiagnosisDialog
    >({
      namespace: 'Form',
      nestedTrans: 'AddNewDiagnosisDialog',
    });
    const certaintyList = getCertaintyList(t);

    const initCertainty = certaintyList.find(
      (certainty) => certainty.value === entry?.encounterDiagnoseTimeline?.certainty
    );

    const [certainty, setCertainty] = useState<IMenuItem>(initCertainty!);

    const onCertaintySelect = (item: IMenuItem) => {
      setCertainty(item);
      onCertaintyChange(`${item?.value}`?.toUpperCase());
    };

    const certaintyListRenderer = (
      item: IMenuItem,
      { handleClick, modifiers }: ItemRendererProps
    ) => {
      return (
        <MenuItem
          key={item.value}
          onClick={handleClick}
          active={modifiers.active}
          disabled={modifiers.disabled}
          text={
            <Flex>
              <Box className="menuItemLeftValue">{item.value}</Box>
              <Box auto className="bp5-text-overflow-ellipsis bp5-fill">
                {item.label}
              </Box>
            </Flex>
          }
        />
      );
    };

    return (
      <Flex className={className} column auto>
        <CertaintySelect<IMenuItem>
          className={getCssClass('sl-select')}
          items={certaintyList}
          itemRenderer={certaintyListRenderer}
          onItemSelect={onCertaintySelect}
          popoverProps={{
            minimal: true,
            captureDismiss: true,
            usePortal: false,
          }}
          filterable={false}
          inputProps={{ fill: true }}
        >
          <Button
            fill
            text={certainty ? certainty.label : '-'}
            rightIcon={<Icon icon="caret-down" />}
            alignText={Alignment.LEFT}
            className="sl-certainty-select"
          />
        </CertaintySelect>
      </Flex>
    );
  }
);
