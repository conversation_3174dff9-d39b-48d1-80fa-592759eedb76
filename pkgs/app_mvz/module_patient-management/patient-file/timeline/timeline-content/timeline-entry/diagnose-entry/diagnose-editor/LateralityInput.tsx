
import React, { useState } from 'react';
import {
  MenuItem,
  Button,
  Icon,
  Alignment,
} from '@tutum/design-system/components/Core';
import {
  ItemRendererProps,
  Select,
} from '@tutum/design-system/components/Select';
import { Flex, Box, IMenuItem } from '@tutum/design-system/components';
import { getLateralityList } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/diagnose-block/DiagnosisNode.util';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';

export interface ILateralityInputProps {
  className?: string;
  entry?: TimelineModel;
  onLateralityChange: (laterality: string) => void;
}

const LateralitySelect = Select;

export default React.memo(
  ({
    className,
    entry: { encounterDiagnoseTimeline },
    onLateralityChange,
  }: ILateralityInputProps) => {
    const lateralityList = getLateralityList();

    const initLaterality = lateralityList.find(
      (laterality) => laterality.value === encounterDiagnoseTimeline.laterality
    );

    const [laterality, setLaterality] = useState<IMenuItem>(initLaterality);

    const onLateralitySelect = (item: IMenuItem) => {
      setLaterality(item);
      onLateralityChange(`${item?.value}`?.toUpperCase());
    };

    const lateralityListRenderer = (
      item: IMenuItem,
      { handleClick, modifiers }: ItemRendererProps
    ) => {
      return (
        <MenuItem
          key={item.value}
          onClick={handleClick}
          active={modifiers.active}
          disabled={modifiers.disabled}
          text={
            <Flex>
              <Box className="menuItemLeftValue">{item.value}</Box>
              <Box auto className="bp5-text-overflow-ellipsis bp5-fill">
                {item.label}
              </Box>
            </Flex>
          }
        />
      );
    };
    return (
      <Flex className={className} column auto>
        <LateralitySelect<IMenuItem>
          className={getCssClass('sl-select')}
          items={lateralityList}
          itemRenderer={lateralityListRenderer}
          onItemSelect={onLateralitySelect}
          popoverProps={{
            minimal: true,
            captureDismiss: true,
            usePortal: false,
          }}
          filterable={false}
          inputProps={{ fill: true }}
        >
          <Button
            fill
            text={laterality ? laterality.label : '-'}
            rightIcon={<Icon icon="caret-down" />}
            alignText={Alignment.LEFT}
            className="sl-laterality-select"
          />
        </LateralitySelect>
      </Flex>
    );
  }
);
