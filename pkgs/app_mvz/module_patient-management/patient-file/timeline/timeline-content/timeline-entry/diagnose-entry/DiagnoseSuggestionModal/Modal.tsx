import {
  BodyTextS,
  Button,
  <PERSON>lapse,
  Dialog,
  <PERSON>lex,
  MessageBar,
  ReactSelect,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Checkbox,
  Classes,
  Divider,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import {
  CreateResponse,
  EditResponse,
  RemoveRequest,
  RemoveResponse,
  ignoreSdkrwRule,
  updateSuggestionRuleApplied,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import {
  DiagnoseType,
  RunSdkrwEnum,
} from '@tutum/hermes/bff/legacy/repo_encounter';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import {
  Certainty,
  Diagnose,
  DiagnoseSuggestion,
  EncounterDiagnoseTimeline,
  Laterality,
  SuggestionType,
} from '@tutum/hermes/bff/repo_encounter';
import I18n from '@tutum/infrastructure/i18n';
import CommonI18n from '@tutum/mvz/locales/en/Common.json';
import FormI18n from '@tutum/mvz/locales/en/Form.json';
import PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import {
  KEY_MAPPING_TIMELINE,
  getDefaultFreeText,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.service';
import {
  getCertaintyList,
  getLateralityList,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/diagnose-block/DiagnosisNode.util';
import { Field, Form, Formik } from 'formik';
import { isEmpty } from 'lodash';
import { FC, memo, useCallback, useEffect, useState } from 'react';
import { timelineActions, useTimeLineStore } from '../../../../Timeline.store';
import { getCommandByType } from '../Diagnose.helper';
import {
  DONT_SHOW_INPUT_NAME,
  REMOVE_CURRENT_INPUT_NAME,
  REMOVE_LIST_INPUT_NAME,
  TITLE_MAPPING,
} from '../constants';
import SingleSuggestionModal from './SingleSuggestionModal.styled';
import FormUtils from '@tutum/infrastructure/utils/form.util';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';

export interface SuggestionModalProps {
  className?: string;
  timelineId?: string;
  timelineModel: TimelineModel;
  onCreateTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: CreateResponse) => void,
    handleError: (error) => void
  ) => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  onDeleteTimelineItem: (
    data: RemoveRequest,
    handleSuccess: (result: RemoveResponse) => void,
    handleError: (error) => void
  ) => void;
  reloadQuarters: ReloadQuarterFunc;
  fetchSuggestionList: () => void;
}

const SuggestionModal: FC<SuggestionModalProps> = (props) => {
  const {
    className,
    timelineModel: entry,
    fetchSuggestionList,
    onCreateTimelineItem,
    onDeleteTimelineItem,
    onEditTimelineItem,
    reloadQuarters,
  } = props;
  const { t: tDiagnoseEntry } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DiagnoseEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry',
  });
  const { t: tForm } = I18n.useTranslation<
    keyof typeof FormI18n.AddNewDiagnosisDialog
  >({
    namespace: 'Form',
    nestedTrans: 'AddNewDiagnosisDialog',
  });
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonI18n>({
    namespace: 'Common',
  });
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DiagnoseEntry.SuggestionModal
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry.SuggestionModal',
  });
  const [isVisibleModal, setVisibleModal] = useState<boolean>(false);
  const [ruleIdsFinished, setRuleIdsFinished] = useState<string[]>([]);
  const [ruleIdsNoLogerApplies, setRuleIdsNoLongerApplies] = useState<string[]>(
    []
  );
  const [certainties, setCertainties] = useState<{ [key: string]: Certainty }>(
    {}
  );
  const [isValidCertainty, setIsValidCertainty] = useState(true);
  const { diagnoseSuggestions } = useTimeLineStore();
  const { encounterDiagnoseTimeline } = entry;

  useEffect(() => {
    if (entry && diagnoseSuggestions?.length) {
      setVisibleModal(true);
    }
  }, [diagnoseSuggestions, entry]);

  const getPayload = (diagnose: Diagnose): TimelineModel => ({
    ...entry,
    [String(KEY_MAPPING_TIMELINE.DIAGNOSE)]: {
      ...encounterDiagnoseTimeline,
      type: encounterDiagnoseTimeline?.type as DiagnoseType,
      hib: !!encounterDiagnoseTimeline?.hib,
      mrsa: !!encounterDiagnoseTimeline?.mrsa,
      imported: !!encounterDiagnoseTimeline?.imported,
      command: getCommandByType(
        encounterDiagnoseTimeline?.type as DiagnoseType
      ),
      group: !!encounterDiagnoseTimeline?.group,
      exception: String(encounterDiagnoseTimeline?.exception),
      explanation: String(encounterDiagnoseTimeline?.explanation),
      sdvaRefs: encounterDiagnoseTimeline?.sdvaRefs ?? [],
      runSdkrw: encounterDiagnoseTimeline?.runSdkrw as RunSdkrwEnum,
      code: diagnose.code,
      certainty: diagnose.certainty,
      laterality: undefined,
      description: diagnose.description,
      freeText: getDefaultFreeText(diagnose),
      diagnoseSuggestions: [],
    },
    id: diagnose.timelineId || entry.id,
  });

  const hideSuggestionBar = (ruleId: string) => {
    if (!isEmpty(diagnoseSuggestions)) {
      fetchSuggestionList();

      return;
    }

    updateSuggestionRuleApplied({
      timelineId: entry?.id,
      ruleId: ruleId,
    });
  };

  const onSuccessSubmit = (ruleId: string) => {
    setRuleIdsFinished((prevState) => [...prevState, ruleId]);
    hideSuggestionBar(ruleId);
  };

  const updateNoLongerSuggestionApplies = (
    timelineId: string,
    ruleId: string
  ) => {
    if (timelineId === entry.id) {
      const rulesIds: string[] = [];
      diagnoseSuggestions?.forEach((diagnoseSuggestion) => {
        if (
          diagnoseSuggestion.ruleId !== ruleId &&
          !ruleIdsFinished.includes(diagnoseSuggestion.ruleId)
        ) {
          rulesIds.push(diagnoseSuggestion.ruleId);
        }
      });
      setRuleIdsNoLongerApplies(rulesIds);
    }
  };

  const removeSuggestionRuleHandler = (
    diagnoses: Diagnose[],
    ruleId: string
  ) => {
    let counter = diagnoses.length || 0;
    let succeed = 0;
    diagnoses.forEach(async (aDiagnose) => {
      onDeleteTimelineItem(
        {
          timelineId: aDiagnose.timelineId,
        },
        () => {
          counter--;
          succeed++;
          if (diagnoses.length <= 1) {
            alertSuccessfully(tDiagnoseEntry('msgDiagnoseRemoveSuccessful'));
          } else if (counter <= 0) {
            alertSuccessfully(
              tDiagnoseEntry('msgMultipleDiagnoseRemoveSuccessful', {
                number: succeed,
              })
            );
          }
        },
        (e) => {
          counter--;
          let msg: any = 'msgDiagnoseRemoveFailed';
          if (
            e.response?.data?.serverError ===
            ErrorCode.ErrorCode_Cannot_Delete_Timeline_Entry
          ) {
            msg = `ErrorMessages.${ErrorCode.ErrorCode_Cannot_Delete_Timeline_Entry}`;
          }

          alertError(tDiagnoseEntry(msg));
        }
      );
      updateNoLongerSuggestionApplies(aDiagnose.timelineId!, ruleId);
    });
    onSuccessSubmit(ruleId);
  };

  const replaceSuggestionRuleHandler = async (
    diagnose: Diagnose,
    ruleId: string
  ) => {
    onEditTimelineItem(
      getPayload(diagnose),
      () => {
        alertSuccessfully(tDiagnoseEntry('msgDiagnoseReplacedSuccessful'));
      },
      () => {
        alertError(tDiagnoseEntry('msgDiagnoseReplacedFailed'));
      }
    );
    onSuccessSubmit(ruleId);
    updateNoLongerSuggestionApplies(diagnose.timelineId!, ruleId);
  };

  const addSuggestionRuleHandler = (diagnoses: Diagnose[], ruleId: string) => {
    let counter = diagnoses.length || 0;

    diagnoses.forEach(async (aDiagnose: Diagnose) => {
      const payload = { ...getPayload(aDiagnose), id: undefined };
      onCreateTimelineItem(
        payload,
        () => {
          if (diagnoses.length <= 1) {
            alertSuccessfully(tDiagnoseEntry('msgDiagnoseAddedSuccessful'));
          } else if (counter <= 0) {
            alertSuccessfully(
              tDiagnoseEntry('msgMultipleDiagnoseAddedSuccessful', {
                number: diagnoses.length,
              })
            );
          }
        },
        () => {
          counter--;
          alertError(tDiagnoseEntry('msgDiagnoseAddedFailed'));
        }
      );
    });
    onSuccessSubmit(ruleId);
  };

  const saveIgnoreRule = async (ruleId: string) => {
    await ignoreSdkrwRule({
      patientId: entry?.patientId || '',
      ruleId: ruleId,
      encounterDate: Number(entry.createdAt),
    });
    reloadQuarters({ quarter: entry.quarter, year: entry.year });
    onSuccessSubmit(ruleId);
  };

  const onClose = () => {
    setVisibleModal(false);
    timelineActions.setTimelineIdCreated(null!);
    timelineActions.setTimelineModel(null!);
    timelineActions.setDiagnoseSuggestions(null);
  };

  const renderAltBlock = (diagnose: Diagnose) => {
    const mappedCertainty = {};
    getCertaintyList(tForm).forEach((certainty) => {
      mappedCertainty[certainty.value] = certainty.label;
    });
    const mappedLaterlity = {};
    getLateralityList().forEach((laterlity) => {
      mappedLaterlity[laterlity.value] = laterlity.label;
    });
    return (
      <div className="alts">
        {diagnose.certainty ? (
          <span>
            {t('lblCertainty')}: {mappedCertainty[diagnose.certainty]}
          </span>
        ) : null}
        {diagnose.certainty &&
          (diagnose as EncounterDiagnoseTimeline).laterality ? (
          <span className="separator">•</span>
        ) : null}
        {(diagnose as EncounterDiagnoseTimeline).laterality ? (
          <span>
            {t('lblLaterality')}:
            {
              mappedLaterlity[
              (diagnose as EncounterDiagnoseTimeline).laterality as Laterality
              ]
            }
          </span>
        ) : null}
      </div>
    );
  };

  const addContent = (suggestion: DiagnoseSuggestion, isDisabled = false) => {
    return (
      <>
        <div className="current-diagnose-item">
          <label>
            ({encounterDiagnoseTimeline?.code}){' '}
            {encounterDiagnoseTimeline?.description}
          </label>
          {renderAltBlock(encounterDiagnoseTimeline as Diagnose)}
        </div>
        <p>{suggestion.hint}</p>
        <br />
        <p className="description">{suggestion.proposal}</p>
        {suggestion.diagnoses.map((diagnose: Diagnose, index) => (
          <div className="options" key={`suggestion-diagnose-${index}`}>
            <Field name={diagnose.code.replace(/\./g, '_')}>
              {({ field }) => (
                <>
                  <Checkbox
                    disabled={isDisabled}
                    labelElement={
                      <>
                        ({diagnose.code}) {diagnose.description}
                        {renderAltBlock(diagnose)}
                      </>
                    }
                    {...field}
                    onChange={(...args) => {
                      field.onChange(...args);
                      setCertainties((prev) => {
                        const newVal = { ...prev };
                        delete newVal[String(field.name)];
                        return newVal;
                      });
                    }}
                  />
                  {String(diagnose.certainty) === '' && (
                    <ReactSelect
                      selectedValue={certainties[String(field.name)]}
                      styles={{
                        container: (base) => ({
                          ...base,
                          width: '50%',
                          marginBottom:
                            field.value && !certainties[String(field.name)]
                              ? 0
                              : 12,
                        }),
                      }}
                      isDisabled={
                        !field.value || String(diagnose.certainty) != ''
                      }
                      items={getCertaintyList(tForm)}
                      onItemSelect={(item) => {
                        setCertainties((prev) => {
                          const newVal = { ...prev };
                          newVal[String(field.name)] = item.value as Certainty;
                          return newVal;
                        });
                      }}
                    />
                  )}
                  {String(diagnose.certainty) === '' &&
                    field.value &&
                    !certainties[String(field.name)] && (
                      <BodyTextS style={{ margin: '12px 0px' }}>
                        {FormUtils.renderFormHelperText(
                          1,
                          !isValidCertainty,
                          tCommon(
                            'FormValidation.fieldRequired' as keyof typeof CommonI18n
                          )
                        )}
                      </BodyTextS>
                    )}
                </>
              )}
            </Field>
          </div>
        ))}
      </>
    );
  };

  const replaceContent = (
    suggestion: DiagnoseSuggestion,
    isDisabled = false
  ) => {
    if (!encounterDiagnoseTimeline) {
      return null!;
    }

    return (
      <>
        <div className="current-diagnose-item">
          <label>
            ({encounterDiagnoseTimeline.code}){' '}
            {encounterDiagnoseTimeline.description}
          </label>
          {renderAltBlock(encounterDiagnoseTimeline as Diagnose)}
        </div>
        <p>{suggestion.hint}</p>
        <br />
        <p className="description">{suggestion.proposal}</p>
        <div className="replace-with">
          {/* <p className="description">{t('lblReplaceWith')}</p> */}
          <Field name="replaceTo">
            {({ field }) => {
              return (
                <RadioGroup
                  onChange={undefined}
                  {...field}
                  disabled={isDisabled}
                >
                  <>
                    {suggestion.diagnoses.map((diagnose: Diagnose, index) => {
                      return (
                        <Radio
                          key={`suggestion-diagnose-replacement-${index}`}
                          labelElement={
                            <>
                              ({diagnose.code}) {diagnose.description}
                              {renderAltBlock(diagnose)}
                            </>
                          }
                          {...field}
                          value={diagnose.code}
                        />
                      );
                    })}
                  </>
                </RadioGroup>
              );
            }}
          </Field>
        </div>
      </>
    );
  };

  const removeContent = (
    suggestion: DiagnoseSuggestion,
    isDisabled = false
  ) => {
    const currentItem = (
      <Field name={REMOVE_CURRENT_INPUT_NAME}>
        {({ field }) => (
          <Checkbox
            {...field}
            disabled={isDisabled}
            labelElement={
              <>
                <div className="lbl">{t('lblRemove')}</div>
                <div className="lbl second">
                  ({encounterDiagnoseTimeline?.code}){' '}
                  {encounterDiagnoseTimeline?.description}
                  {renderAltBlock(encounterDiagnoseTimeline as Diagnose)}
                </div>
              </>
            }
          />
        )}
      </Field>
    );

    // const listitems = suggestion.diagnoses.map((diagnose: Diagnose, index) => {
    const listitems = (
      <Field name={REMOVE_LIST_INPUT_NAME}>
        {({ field }) => (
          <Checkbox
            disabled={isDisabled}
            labelElement={
              <>
                <div className="lbl">{t('lblRemoveConflict')}</div>
                {suggestion.diagnoses.map((diagnose: Diagnose) => (
                  <>
                    <div
                      className={getCssClass('lbl second', {
                        list: !!suggestion.diagnoses[1],
                      })}
                    >
                      {`${diagnose.code} ${diagnose.description}`}
                      {renderAltBlock(diagnose)}
                    </div>
                  </>
                ))}
              </>
            }
            {...field}
          />
        )}
      </Field>
    );

    return (
      <div className="remove-box">
        <div className="current-diagnose-item">
          <label>
            ({encounterDiagnoseTimeline?.code}){' '}
            {encounterDiagnoseTimeline?.description}
          </label>
          {renderAltBlock(encounterDiagnoseTimeline as Diagnose)}
        </div>
        <p>{suggestion.hint}</p>
        <br />
        <p className="description">{suggestion.proposal}</p>
        {currentItem}
        {listitems}
      </div>
    );
  };

  const valueProcess = useCallback(
    (
      values: Record<string, string>,
      suggestion: DiagnoseSuggestion,
      type: SuggestionType
    ) => {
      const suggestionItems = Object.keys(values)
        .filter(
          (key) =>
            values[key] &&
            key !== DONT_SHOW_INPUT_NAME &&
            ![REMOVE_CURRENT_INPUT_NAME, REMOVE_LIST_INPUT_NAME].includes(key)
        )
        .map((key) => {
          // NOTE: temporary solution
          const value =
            type === SuggestionType.SUGGESTIONTYPE_ADD ? key : values[key];

          const icdCode = value.replace(/_/g, '.');
          const result = suggestion?.diagnoses.find(
            (item) => item.code === icdCode
          );
          if (
            String(result?.certainty) == '' &&
            typeof certainties[value] !== 'undefined' &&
            String(certainties[value]) !== ''
          ) {
            return { ...result, certainty: certainties[value] };
          }
          return result;
        })
        .filter((item) => item !== undefined);
      const list: Diagnose[] = [];

      switch (suggestion?.suggestionType) {
        case SuggestionType.SUGGESTIONTYPE_ADD:
          return suggestionItems;
        case SuggestionType.SUGGESTIONTYPE_REPLACE:
          return suggestion?.diagnoses.find(
            (item) => item.code === values.replaceTo
          );
          return;
        case SuggestionType.SUGGESTIONTYPE_DELETE:
          if (values[REMOVE_LIST_INPUT_NAME]) {
            list.push(...suggestion.diagnoses);
          }
          if (values[REMOVE_CURRENT_INPUT_NAME]) {
            list.push({
              ...encounterDiagnoseTimeline,
              timelineId: entry.id!,
            } as Diagnose);
          }
          return list;
        default:
          return null;
      }
    },
    [certainties]
  );

  const validateData2Send = (data: Diagnose[] | Diagnose): boolean => {
    if (Array.isArray(data)) {
      for (const d of data) {
        if (String(d.certainty) === '') {
          return false;
        }
      }
    } else {
      return String(data?.certainty) !== '';
    }
    return true;
  };

  const onApply =
    (suggestion: DiagnoseSuggestion) =>
      async (values: Record<string, string>) => {
        if (values && Object.keys(values).length > 0) {
          const data2Send = valueProcess(
            values,
            suggestion,
            suggestion?.suggestionType
          ) as any;
          if (!validateData2Send(data2Send)) {
            setIsValidCertainty(false);
            return;
          }
          switch (suggestion?.suggestionType) {
            case SuggestionType.SUGGESTIONTYPE_ADD:
              addSuggestionRuleHandler(
                data2Send as Diagnose[],
                suggestion.ruleId
              );
              break;
            case SuggestionType.SUGGESTIONTYPE_REPLACE:
              if (data2Send) {
                replaceSuggestionRuleHandler(
                  data2Send as Diagnose,
                  suggestion.ruleId
                );
              }
              break;
            case SuggestionType.SUGGESTIONTYPE_DELETE:
              removeSuggestionRuleHandler(
                data2Send as Diagnose[],
                suggestion.ruleId
              );
              break;
            default:
              console.error('wrong SUGGESTIONTYPE');
              break;
          }
          if (values[DONT_SHOW_INPUT_NAME]) {
            saveIgnoreRule(suggestion?.ruleId);
          }
        }
        onClose();
      };

  return diagnoseSuggestions?.length === 1 ? (
    <SingleSuggestionModal
      className={className}
      addContent={addContent}
      removeContent={removeContent}
      replaceContent={replaceContent}
      suggestion={diagnoseSuggestions[0]}
      onSubmit={onApply}
      onClose={onClose}
    />
  ) : (
    <Dialog
      className={className}
      isOpen={isVisibleModal}
      title={t('titleModal')}
      onClose={onClose}
    >
      <Flex column justify="space-between" className={`${Classes.DIALOG_BODY}`}>
        <Flex column>
          {diagnoseSuggestions?.map((suggestion) => {
            if (!suggestion.diagnoses.length) {
              return null;
            }
            return (
              <Formik
                onSubmit={onApply(suggestion)}
                initialValues={{}}
                key={suggestion.ruleId}
              >
                {({ dirty, isSubmitting }) => {
                  const suggestionNoLongerApplies =
                    ruleIdsNoLogerApplies.includes(suggestion.ruleId);
                  const isDisabled =
                    ruleIdsFinished.includes(suggestion.ruleId) ||
                    (suggestionNoLongerApplies && !isValidCertainty);
                  return (
                    <Form>
                      <Flex column className="sl-modal-main">
                        <Collapse
                          title={t(
                            TITLE_MAPPING[suggestion?.suggestionType as any]
                          )}
                          className="sl-collapse"
                          isOpen={true}
                        >
                          <>
                            {suggestionNoLongerApplies && (
                              <MessageBar
                                type="warning"
                                content={t('suggestionNoLongerApplies')}
                              />
                            )}
                            <div>
                              {suggestion?.suggestionType ===
                                SuggestionType.SUGGESTIONTYPE_ADD &&
                                addContent(suggestion, isDisabled)}
                              {suggestion?.suggestionType ===
                                SuggestionType.SUGGESTIONTYPE_REPLACE &&
                                replaceContent(suggestion, isDisabled)}
                              {suggestion?.suggestionType ===
                                SuggestionType.SUGGESTIONTYPE_DELETE &&
                                removeContent(suggestion, isDisabled)}
                              <Divider />
                              <Field name={DONT_SHOW_INPUT_NAME}>
                                {({ field }) => (
                                  <Checkbox
                                    label={t('optionDontShow')}
                                    {...field}
                                    disabled={isDisabled}
                                  />
                                )}
                              </Field>
                            </div>
                            <Flex justify="space-between">
                              <Button
                                loading={isSubmitting}
                                disabled={!dirty || isDisabled}
                                outlined
                                intent="primary"
                                type="submit"
                              >
                                {tCommon(
                                  'ButtonActions.apply' as keyof typeof CommonI18n
                                )}
                              </Button>
                            </Flex>
                          </>
                        </Collapse>
                      </Flex>
                      <Divider />
                    </Form>
                  );
                }}
              </Formik>
            );
          })}
        </Flex>
        <Flex justify="flex-end" className={Classes.DIALOG_FOOTER}>
          <Button onClick={onClose} intent={'primary'} large>
            {tCommon('ButtonActions.close' as keyof typeof CommonI18n)}
          </Button>
        </Flex>
      </Flex>
    </Dialog>
  );
};

export default memo(SuggestionModal);
