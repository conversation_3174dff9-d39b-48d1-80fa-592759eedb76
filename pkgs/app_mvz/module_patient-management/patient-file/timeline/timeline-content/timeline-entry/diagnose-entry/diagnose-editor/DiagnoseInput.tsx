import React, { useState, useRef, useContext } from 'react';
import { MenuItem } from '@tutum/design-system/components/Core';
import {
  ItemRendererProps,
  Suggest,
} from '@tutum/design-system/components/Select';
import { Flex } from '@tutum/design-system/components';
import Theme, { IMvzTheme } from '@tutum/mvz/theme';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import DiagnosePopoverMenuItem from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/diagnose-block/DiagnosePopoverMenuItem';
import { IDiagnoseSearchResult } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/diagnose-block/DiagnoseBlock.type';
import DiagnoseBlockService from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/diagnose-block/DiagnoseBlock.service';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { isNotEmpty } from '@tutum/design-system/infrastructure/utils';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { EncounterDiagnoseTimeline } from '@tutum/hermes/bff/repo_encounter';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import { useTheme } from '@tutum/design-system/themes';
import { store as actionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
export interface IDiagnoseInputProps {
  className?: string;
  entry?: TimelineModel;
  onDiagnoseChange: (diagnose: EncounterDiagnoseTimeline) => void;
}

const DiagnoseSuggestion = Suggest.ofType<IDiagnoseSearchResult>();

const DiagnoseInput: React.ComponentType<IDiagnoseInputProps> = React.memo(
  ({
    className,
    entry,
    onDiagnoseChange,
    t,
  }: IDiagnoseInputProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.DiagnoseEditor>) => {
    const encounterDiagnoseTimeline = entry?.encounterDiagnoseTimeline;
    const [query, setQuery] = useState<string>(
      encounterDiagnoseTimeline?.freeText || ''
    );
    const { t: tCommon } = I18n.useTranslation<
      keyof typeof CommonI18n.HintError
    >({
      namespace: 'Common',
      nestedTrans: 'HintError',
    });
    const [openDiagnose, setOpenDiagnose] = useState<boolean>(false);
    const [currentDiagnose, setCurrentDiagnose] =
      useState<EncounterDiagnoseTimeline>(encounterDiagnoseTimeline!);
    const [diagnoseList, setDiagnoseList] = useState<IDiagnoseSearchResult[]>(
      []
    );
    const {
      patientManagement: { patient },
    } = useContext(PatientManagementContext.instance);
    const theme = useTheme() as IMvzTheme;

    const diagnoseInputRef = useRef<HTMLDivElement | null>(null);
    const bindInputRef = (ref: HTMLInputElement) => {
      diagnoseInputRef.current = ref;
    };

    const onDiagnoseSelect = (
      item: IDiagnoseSearchResult,
      event?: React.SyntheticEvent<HTMLSpanElement>
    ) => {
      if (!event || !openDiagnose) {
        return;
      }

      const inputData = `(${item.code}) ${item.description}`;
      diagnoseInputRef?.current?.setAttribute(
        'placeholder',
        t('searchDiagnosePlaceholder')
      );
      diagnoseInputRef?.current?.setAttribute('value', inputData);

      const updatedDiagnose = {
        ...encounterDiagnoseTimeline!,
        code: item.code!,
        description: item.description!,
        group: item.group!,
        freeText: inputData,
      };
      setQuery(inputData);
      setOpenDiagnose(false);
      setCurrentDiagnose(updatedDiagnose);
      onDiagnoseChange(updatedDiagnose);
    };

    const onQueryChange = async (inputData?: string) => {
      diagnoseInputRef?.current?.setAttribute(
        'placeholder',
        t('searchDiagnosePlaceholder')
      );
      diagnoseInputRef?.current?.setAttribute('value', inputData!);
      DiagnoseBlockService.searchDiagnose(
        patient!,
        entry?.createdAt || 0,
        inputData || '',
        actionBarStore,
        tCommon
      ).then((result) => {
        setOpenDiagnose(result.length > 0);
        setDiagnoseList(result);
      });
      onDiagnoseChange({ ...currentDiagnose, ...{ freeText: inputData! } });
    };

    const diagnoseListRenderer = (
      item: IDiagnoseSearchResult,
      { handleClick, modifiers, query, index }: ItemRendererProps
    ) => {
      return (
        <MenuItem
          className={item.group ? 'group' : ''}
          key={index}
          onClick={handleClick}
          active={modifiers.active}
          disabled={modifiers.disabled}
          text={
            <Flex auto>{DiagnosePopoverMenuItem({ item, query }, theme)}</Flex>
          }
        />
      );
    };

    return (
      <Flex className={className} column auto>
        <DiagnoseSuggestion
          disabled={isNotEmpty(encounterDiagnoseTimeline?.code)}
          inputValueRenderer={(diagnose) =>
            `(${diagnose.code}) ${diagnose.description}`
          }
          query={query}
          onQueryChange={onQueryChange}
          onItemSelect={onDiagnoseSelect}
          itemRenderer={diagnoseListRenderer}
          items={diagnoseList}
          popoverProps={{
            minimal: true,
            position: 'bottom-left',
            usePortal: false,
          }}
          inputProps={{
            onBlur: (item) => {
              setQuery(item.currentTarget.value);
            },
            onKeyDown: (event) => {
              if (event.key === 'ENTER') {
                event.preventDefault();
                event.stopPropagation();
              }
            },
            placeholder: t('searchDiagnosePlaceholder'),
            inputRef: bindInputRef,
          }}
        />
      </Flex>
    );
  }
);

export default I18n.withTranslation(DiagnoseInput, {
  namespace: 'PatientManagement',
  nestedTrans: 'DiagnoseEditor',
});
