import { cloneDeep, groupBy, isEmpty, isNil } from 'lodash';
import React, {
  ReactElement,
  memo,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';

import {
  BodyTextS,
  Button,
  Flex,
  H5,
  MessageBar,
  Svg,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Intent,
  Menu,
  MenuItem,
  Popover,
  Tooltip,
} from '@tutum/design-system/components/Core';
import HighLightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  getAge,
  getCssClass,
  toDateFormat,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { CodingRuleSuggestion } from '@tutum/hermes/bff/coding_rule_common';
import { MainGroup } from '@tutum/hermes/bff/common';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { FormName } from '@tutum/hermes/bff/form_common';
import {
  EditResponse,
  GroupByQuarter,
  useMutationMarkTreatmentRelevant,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import {
  DiagnoseType,
  EncounterDiagnoseTimeline,
  Sources,
  SuggestionType,
} from '@tutum/hermes/bff/repo_encounter';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { RunSdkrwEnum } from '@tutum/hermes/bff/service_domains_patient_file';
import { ActionType, TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import { getMainGroupNoSchein } from '@tutum/mvz/_utils/patientType';
import {
  backgroundMainGroup,
  colorMainGroup,
  formatScheinName,
} from '@tutum/mvz/_utils/scheinFormat';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { useFormOverviewStore } from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { KEY_MAPPING_TIMELINE } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/Composer.service';
import {
  DiagnoseFurtherInfor,
  UpdatingData,
} from '@tutum/mvz/module_patient-management/patient-file/terminate-permanent-diagnose-dialog/TerminatePermanentDiagnoseDialog';
import TerminatePermanentDiagnoseDialog from '@tutum/mvz/module_patient-management/patient-file/terminate-permanent-diagnose-dialog/TerminatePermanentDiagnoseDialog.styled';
import { SourceScreen } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.hooks';
import { handleReduceErrors } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import {
  timelineActions,
  useTimeLineStore,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import EntryDismissBtn from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/entry-dismiss-btn/EntryDismissBtn.style';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import { IMvzTheme } from '@tutum/mvz/theme';
import ChangeItemDateMenuItem from '../ChangeItemDateMenuItem';
import { ITimelineEntryStatus } from '../TimelineEntry';
import { PayLoadHistoryMode, getBillingInfo } from '../util';
import { getCommandByType } from './Diagnose.helper';
import {
  BKK_BOSCH_VAG_BW_Praeventionsverordnung_CONTRACT,
  DIAGNOSIS_CODE_BKK_BOSCH,
  DIAGNOSIS_CODE_BKK_BOSCH_AGE_RESTRICTED,
  DIAGNOSIS_CODE_BKK_BOSCH_VAG_BW_PRAEVENTIONSVERORDNUNG,
  IcdInfo,
  QUICK_INFO_PATIENT_ACCOMPANIMENT,
  contractPatientAccompanimentMapping,
  getValidDiagnose,
} from './constants';
import DiagnoseEditor from './diagnose-editor/DiagnoseEditor.styled';
import { checkExistedICDCode, renderDiagnoseText } from './helpers';

const EditIcon = '/images/edit-2.svg';
const editIcon = '/images/edit-3.svg';
const MoreIcon = '/images/more.svg';
const trashIcon = '/images/trash-bin-red.svg';
const MarkAcuteIcon = '/images/check-rounded.svg';
const MarkAnamnesticDiagnoseIcon = '/images/corner-up-right.svg';
const MarkPermanentDiagnoseIcon = '/images/refresh.svg';
const MarkTreatmentRelevantIcon = '/images/mark-treatment-relevant.svg';
const MarkedTreatmentRelevantIcon = '/images/marked-treatment-relevant.svg';
const UnmarkTreatmentRelevantIcon = '/images/unmark-treatment-relevant.svg';

export interface IDiagnoseEntryProps {
  className?: string;
  theme?: IMvzTheme;
  entry: TimelineModel;
  diagnoseSuggestions: CodingRuleSuggestion[];
  lastItem?: boolean;
  treatmentDoctorId?: string;
  patientId?: string;
  onEdit?: (isEditing: boolean) => void;
  isEditing?: boolean;
  contract: IContractInfo;
  doctorIcon: JSX.Element;
  hasBilled: boolean;
  quarter: GroupByQuarter;
  currentSchein: ScheinItem | undefined;
  keyword?: string;

  sourceScreen: SourceScreen;
  setItemToEditDate?: () => void;
  setEditInline?: () => void;
  onRemoveEntry: (_: boolean) => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  payLoadHistoryMode: PayLoadHistoryMode;
  isHistoryMode: boolean;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
  reloadQuarters: ReloadQuarterFunc;
}

const AGE_RESTRICTED = 70;

const DiagnoseEntry = (props: IDiagnoseEntryProps) => {
  const {
    className,
    doctorIcon,
    entry,
    hasBilled,
    diagnoseSuggestions: diagnoseSuggestionsProps,
    currentSchein,
    sourceScreen,
    quarter: timelinesByQuarter,
    setItemToEditDate,
    onEditTimelineItem,
    onRemoveEntry,
    setEditInline,
    reloadQuarters,
    openCreateSchein,
  } = props;
  const { id } = entry;

  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });

  // // NOTE: hide error, warning, hint for billed diagnose
  const encounterDiagnoseTimeline = hasBilled
    ? {
        ...entry.encounterDiagnoseTimeline,
        errors: [],
        diagnoseSuggestions: [],
      }
    : entry.encounterDiagnoseTimeline;

  const {
    patientManagement: { patient, selectedContractDoctor },
  } = useContext(PatientManagementContext.instance);

  const patientFileStore = usePatientFileStore();
  const timelineStore = useTimeLineStore();
  const settingStore = useSettingStore();
  const fomrOverviewStore = useFormOverviewStore();

  const [isEditing, setEditing] = useState<boolean>(false);
  const [openActionMenu, setOpenActionMenu] = useState<boolean>(false);
  const [openDiagnoseType, setOpenDiagnoseType] =
    useState<Nullable<DiagnoseFurtherInfor>>();
  const [isLoading, setLoading] = useState<boolean>(false);
  // NOTE: disabled timeline entry in case import from other system (e.g. BDT)
  const isDisabled = entry.isImported;

  const selectedSchein = useMemo(() => {
    return (patientFileStore.schein.originalList || []).find((schein) =>
      (entry.scheinIds || []).includes(schein.scheinId)
    );
  }, [patientFileStore.schein.originalList, entry.scheinIds]);

  const { isContractSupport } = useCheckContractSupport(
    QUICK_INFO_PATIENT_ACCOMPANIMENT,
    [selectedSchein?.hzvContractId]
  );

  const mainGroupNoSchein = useMemo(() => {
    return getMainGroupNoSchein(patient?.patientInfo.genericInfo.patientType);
  }, [patient]);

  useEffect(() => {
    if (
      !timelineStore.isPendingFetchNewData &&
      timelineStore.timelineIdCreated &&
      entry.id === timelineStore.timelineIdCreated &&
      !timelineStore.diagnoseSuggestions
    ) {
      if (isEmpty(entry.encounterDiagnoseTimeline?.diagnoseSuggestions)) {
        timelineActions.setDiagnoseSuggestions(null);
        return;
      }

      const suggestions = (
        entry.encounterDiagnoseTimeline?.diagnoseSuggestions || []
      ).filter((item) => item?.diagnoses?.length > 0);

      if (!suggestions.length) {
        timelineActions.setDiagnoseSuggestions(null);
        return;
      }

      timelineActions.setTimelineModel(entry);
      timelineActions.setDiagnoseSuggestions(suggestions);
    }
  }, [
    timelineStore.isPendingFetchNewData,
    timelineStore.timelineIdCreated,
    timelineStore.diagnoseSuggestions,
    entry,
  ]);

  const { t: tForm } = I18n.useTranslation<keyof typeof FormI18n.Forms>({
    namespace: 'Form',
    nestedTrans: 'Forms',
  });
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DiagnoseEntry
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry',
  });
  const { t: tTimelineEncounter } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.TimelineEncounter
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'TimelineEncounter',
  });

  // const style = !allowDirectEditor ? '' : 'warning';
  const style = '';
  const openMenuStyle = openActionMenu ? 'is-opening-menu' : '';

  const closeEditMode = () => {
    setEditing(false);
  };

  const onCloseDiagnoseDialog = () => {
    setOpenDiagnoseType(null);
  };

  const isMarkedTreatmentRelevant = useMemo(() => {
    return entry.encounterDiagnoseTimeline?.markedTreatmentRelevant;
  }, [entry.encounterDiagnoseTimeline]);

  const currentDiagnoseValue = useMemo(() => {
    return `${encounterDiagnoseTimeline?.code} ${
      encounterDiagnoseTimeline?.certainty
    } ${encounterDiagnoseTimeline?.laterality || ''}`.trim();
  }, [encounterDiagnoseTimeline]);

  const markAllTreatmentRelevant = useMutationMarkTreatmentRelevant({
    onSuccess: () => {
      const message = t('msgMarkAllTreatmentRelevantSuccess', {
        value: currentDiagnoseValue,
      });
      alertSuccessfully(message);
      reloadQuarters({ quarter: entry.quarter, year: entry.year });
    },
  });

  const onChangeDiagnoseType = async (diagnoseType: DiagnoseType) => {
    onEditTimelineItem(
      {
        ...entry,
        [String(KEY_MAPPING_TIMELINE.DIAGNOSE)]: {
          ...encounterDiagnoseTimeline,
          code: String(encounterDiagnoseTimeline?.code),
          description: String(encounterDiagnoseTimeline?.description),
          freeText: String(encounterDiagnoseTimeline?.freeText),
          group: !!encounterDiagnoseTimeline?.group,
          exception: String(encounterDiagnoseTimeline?.exception),
          explanation: String(encounterDiagnoseTimeline?.explanation),
          sdvaRefs: encounterDiagnoseTimeline?.sdvaRefs ?? [],
          runSdkrw: encounterDiagnoseTimeline?.runSdkrw as RunSdkrwEnum,
          hib: !!encounterDiagnoseTimeline?.hib,
          mrsa: !!encounterDiagnoseTimeline?.mrsa,
          imported: !!encounterDiagnoseTimeline?.imported,
          type: diagnoseType,
          sources: Sources.Timeline,
          command: getCommandByType(diagnoseType),
          markedTreatmentRelevant: getCommandByType(diagnoseType) === 'DD',
        },
      },
      () => {
        let message = '';

        if (diagnoseType === DiagnoseType.DIAGNOSETYPE_ACUTE) {
          message = t('msgAccuteSuccess');
        } else if (diagnoseType === DiagnoseType.DIAGNOSETYPE_ANAMNESTIC) {
          message = t('msgAnamsesticSuccess');
        } else if (diagnoseType === DiagnoseType.DIAGNOSETYPE_PERMANENT) {
          message = t('msgPermanentSuccess');
        }

        alertSuccessfully(message);
      },
      () => {}
    );
  };

  const onMarkTreatmentRelevant = async () => {
    onEditTimelineItem(
      {
        ...entry,
        [String(KEY_MAPPING_TIMELINE.DIAGNOSE)]: {
          ...encounterDiagnoseTimeline,
          markedTreatmentRelevant: !isMarkedTreatmentRelevant,
        },
      },
      () => {
        const message = isMarkedTreatmentRelevant
          ? t('msgUnmarkTreatmentRelevantSuccess')
          : t('msgMarkTreatmentRelevantSuccess');
        alertSuccessfully(message);
      },
      () => {}
    );
  };

  const onMarkAllTreatmentRelevant = async () => {
    markAllTreatmentRelevant.mutate({
      timelineId: entry.id as string,
    });
  };

  const terminateChronicDiagnose = async (diagnoseEndDate: UpdatingData) => {
    const editEncounterDiagnoseTimeline: Partial<EncounterDiagnoseTimeline> = {
      ...encounterDiagnoseTimeline,
      code: String(encounterDiagnoseTimeline?.code),
      description: String(encounterDiagnoseTimeline?.description),
      freeText: String(encounterDiagnoseTimeline?.freeText),
      group: !!encounterDiagnoseTimeline?.group,
      exception: String(encounterDiagnoseTimeline?.exception),
      explanation: String(encounterDiagnoseTimeline?.explanation),
      sdvaRefs: encounterDiagnoseTimeline?.sdvaRefs ?? [],
      runSdkrw: encounterDiagnoseTimeline?.runSdkrw as RunSdkrwEnum,
      hib: !!encounterDiagnoseTimeline?.hib,
      mrsa: !!encounterDiagnoseTimeline?.mrsa,
      imported: !!encounterDiagnoseTimeline?.imported,
      type: encounterDiagnoseTimeline?.type as DiagnoseType,
      command: String(encounterDiagnoseTimeline?.command),
    };
    let saveSuccessfulMessage = '';
    if (openDiagnoseType === DiagnoseFurtherInfor.EndDate) {
      editEncounterDiagnoseTimeline.validUntil =
        diagnoseEndDate.encounterDate.getTime();
      saveSuccessfulMessage = t('diagnoseEnddateSavedSuccessful');
    } else if (openDiagnoseType === DiagnoseFurtherInfor.Exception) {
      editEncounterDiagnoseTimeline.exception = diagnoseEndDate.exception;
      saveSuccessfulMessage = t('diagnoseExceptionSavedSuccessful');
    } else if (openDiagnoseType === DiagnoseFurtherInfor.Explanation) {
      editEncounterDiagnoseTimeline.explanation = diagnoseEndDate.explanation;
      saveSuccessfulMessage = t('diagnoseExplanationSavedSuccessful');
    }

    setLoading(true);

    onEditTimelineItem(
      {
        ...entry,
        [String(KEY_MAPPING_TIMELINE.DIAGNOSE)]: {
          ...editEncounterDiagnoseTimeline,
          sources: Sources.Timeline,
        },
      },
      () => {
        alertSuccessfully(saveSuccessfulMessage);
        setLoading(false);
        onCloseDiagnoseDialog();
      },
      () => {
        alertError(t('diagnoseInfoSavedFailed'));
        setLoading(false);
        onCloseDiagnoseDialog();
      }
    );
  };

  const cancelledSuggestionDiagnose = async () => {
    const editEncounterDiagnoseTimeline: Partial<EncounterDiagnoseTimeline> = {
      ...encounterDiagnoseTimeline,
      code: String(encounterDiagnoseTimeline?.code),
      description: String(encounterDiagnoseTimeline?.description),
      freeText: String(encounterDiagnoseTimeline?.freeText),
      group: !!encounterDiagnoseTimeline?.group,
      exception: String(encounterDiagnoseTimeline?.exception),
      explanation: String(encounterDiagnoseTimeline?.explanation),
      sdvaRefs: encounterDiagnoseTimeline?.sdvaRefs ?? [],
      hib: !!encounterDiagnoseTimeline?.hib,
      mrsa: !!encounterDiagnoseTimeline?.mrsa,
      imported: !!encounterDiagnoseTimeline?.imported,
      type: encounterDiagnoseTimeline?.type as DiagnoseType,
      command: String(encounterDiagnoseTimeline?.command),
      runSdkrw: RunSdkrwEnum.RUNSDKRWENUM_CANCELLED,
    };

    onEditTimelineItem(
      {
        ...entry,
        [String(KEY_MAPPING_TIMELINE.DIAGNOSE)]: {
          ...editEncounterDiagnoseTimeline,
          sources: Sources.Timeline,
        },
      },
      () => {
        alertSuccessfully(t('diagnoseCancelledSuggestionSavedSuccessful'));
        onCloseDiagnoseDialog();
      },
      () => {
        alertError(t('diagnoseCancelledSuggestionSavedFailed'));
        onCloseDiagnoseDialog();
      }
    );
  };

  const onDiagnoseEditorSave = async (values: any) => {
    const updatedDiagnose = {
      ...values.diagnoseInput,
      ...{ certainty: values.certaintyInput },
      ...{ laterality: values.lateralityInput },
    };
    onEditTimelineItem(
      {
        ...entry,
        ...updatedDiagnose,
        [KEY_MAPPING_TIMELINE.DIAGNOSE]: {
          ...encounterDiagnoseTimeline,
          code: String(encounterDiagnoseTimeline?.code),
          description: String(encounterDiagnoseTimeline?.description),
          group: !!encounterDiagnoseTimeline?.group,
          exception: String(encounterDiagnoseTimeline?.exception),
          explanation: String(encounterDiagnoseTimeline?.explanation),
          sdvaRefs: encounterDiagnoseTimeline?.sdvaRefs ?? [],
          hib: !!encounterDiagnoseTimeline?.hib,
          mrsa: !!encounterDiagnoseTimeline?.mrsa,
          imported: !!encounterDiagnoseTimeline?.imported,
          type: encounterDiagnoseTimeline?.type as DiagnoseType,
          // NewData
          freeText: String(encounterDiagnoseTimeline?.freeText),
          command: String(encounterDiagnoseTimeline?.command),
          sources: Sources.Timeline,
        },
      },
      () => {
        alertSuccessfully(t('diagnoseSavedSuccessful'));
        closeEditMode();
      },
      () => {
        alertError(t('diagnoseSavedFailed'));
        closeEditMode();
      }
    );
  };

  const renderActionMenu = (
    diagnose?: EncounterDiagnoseTimeline,
    isImported: boolean = false
  ): ReactElement | undefined => {
    if (!diagnose) return undefined;
    const actionEdit = (
      <MenuItem
        key="edit_1"
        icon={<Svg src={editIcon} size={20} />}
        text={tTimelineEncounter('editEntry')}
        onClick={() => props.setEditInline?.()}
      />
    );
    const actionEditDate = (
      <ChangeItemDateMenuItem
        key="0"
        text={t('editEntryDate')}
        onClick={() => setItemToEditDate?.()}
      />
    );
    const actionMarkAsPermanentDiagnose = (
      <MenuItem
        key="2"
        icon={
          <Svg
            className="timeline-entry_row_menu-icon"
            src={MarkPermanentDiagnoseIcon}
          />
        }
        text={t('actionMarkAsPermanentDiagnose')}
        onClick={() => {
          if (diagnose.code === 'Z01.7') {
            alertError(t('actionCannotMarkAsPermanentDiagnose'));
          } else {
            onChangeDiagnoseType(DiagnoseType.DIAGNOSETYPE_PERMANENT);
          }
        }}
      />
    );
    const actionMarkAsAcuteDiagnose = (
      <MenuItem
        key="3"
        icon={
          <Svg className="timeline-entry_row_menu-icon" src={MarkAcuteIcon} />
        }
        text={t('actionMarkAsAcuteDiagnose')}
        onClick={() => onChangeDiagnoseType(DiagnoseType.DIAGNOSETYPE_ACUTE)}
      />
    );
    const actionMarkAsAnamnesticDiagnose = (
      <MenuItem
        key="4"
        icon={
          <Svg
            className="timeline-entry_row_menu-icon"
            src={MarkAnamnesticDiagnoseIcon}
          />
        }
        text={t('actionMarkAsAnamnesticDiagnose')}
        onClick={() =>
          onChangeDiagnoseType(DiagnoseType.DIAGNOSETYPE_ANAMNESTIC)
        }
      />
    );
    const actionDocumentExceptionDiagnose = (
      <MenuItem
        key="5"
        icon={<Svg className="timeline-entry_row_menu-icon" src={EditIcon} />}
        text={t('actionDocumentExceptionDiagnose')}
        onClick={() => {
          setOpenDiagnoseType(DiagnoseFurtherInfor.Exception);
        }}
      />
    );
    const actionDocumentExplanationDiagnose = (
      <MenuItem
        key="6"
        icon={<Svg className="timeline-entry_row_menu-icon" src={EditIcon} />}
        text={t('actionDocumentExplanationDiagnose')}
        onClick={() => {
          setOpenDiagnoseType(DiagnoseFurtherInfor.Explanation);
        }}
      />
    );
    const actionMarkTreatmentRelevant = (
      <MenuItem
        key="7"
        icon={
          <Svg
            className="timeline-entry_row_menu-icon"
            src={
              isMarkedTreatmentRelevant
                ? UnmarkTreatmentRelevantIcon
                : MarkTreatmentRelevantIcon
            }
          />
        }
        text={t(
          isMarkedTreatmentRelevant
            ? 'actionUnMarkTreatmentRelevant'
            : 'actionMarkTreatmentRelevant'
        )}
        onClick={onMarkTreatmentRelevant}
      />
    );
    const actionAllMarkTreatmentRelevant = (
      <MenuItem
        key="8"
        icon={
          <Svg
            className="timeline-entry_row_menu-icon"
            src={MarkTreatmentRelevantIcon}
          />
        }
        text={t('actionMarkAllTreatmentRelevant', {
          value: currentDiagnoseValue,
        })}
        onClick={onMarkAllTreatmentRelevant}
      />
    );
    const actionRemove = (
      // <Guard roles={[UserType.MANAGER]} key="remove">
      <MenuItem
        key="1"
        icon={<Svg src={trashIcon} />}
        text={t('actionRemove')}
        onClick={() => {
          onRemoveEntry(false);
        }}
      />
      // </Guard>
    );
    const menuElements: React.ReactElement[] = [];
    if (isImported) {
      menuElements.push(actionRemove);
      return <Menu className="action-menu">{menuElements}</Menu>;
    }
    if (!hasBilled) {
      menuElements.push(actionEdit);
    }

    switch (diagnose.type) {
      case DiagnoseType.DIAGNOSETYPE_PERMANENT:
        if (!currentSchein?.isTechnicalSchein) {
          menuElements.push(actionEditDate);
        }
        if (diagnose.command === 'DD') {
          menuElements.push(
            ...[
              actionMarkAsAcuteDiagnose,
              actionMarkAsAnamnesticDiagnose,
              actionDocumentExceptionDiagnose,
              actionDocumentExplanationDiagnose,
            ]
          );
          if (!hasBilled) {
            menuElements.push(actionMarkTreatmentRelevant);
            menuElements.push(actionRemove);
          }
        } else {
          menuElements.push(...[actionMarkAsPermanentDiagnose]);
        }
        break;
      case DiagnoseType.DIAGNOSETYPE_ACUTE:
        if (!currentSchein?.isTechnicalSchein) {
          menuElements.push(actionEditDate);
        }
        menuElements.push(
          ...[
            actionMarkAsPermanentDiagnose,
            actionMarkAsAnamnesticDiagnose,
            actionDocumentExceptionDiagnose,
            actionDocumentExplanationDiagnose,
          ]
        );
        if (!hasBilled) {
          menuElements.push(actionRemove);
        }
        break;
      case DiagnoseType.DIAGNOSETYPE_ANAMNESTIC:
        if (!currentSchein?.isTechnicalSchein) {
          menuElements.push(actionEditDate);
        }
        menuElements.push(
          ...[
            actionMarkAsPermanentDiagnose,
            actionMarkAsAcuteDiagnose,
            actionDocumentExceptionDiagnose,
            actionDocumentExplanationDiagnose,
          ]
        );
        if (!hasBilled) {
          menuElements.push(actionAllMarkTreatmentRelevant);
          menuElements.push(actionMarkTreatmentRelevant);
          menuElements.push(actionRemove);
        }
        break;
      default:
        break;
    }

    return <Menu className="action-menu">{menuElements}</Menu>;
  };

  const renderSuggestMessageBar = (): Nullable<ReactElement> => {
    if (isNil(encounterDiagnoseTimeline)) {
      return;
    }

    const { diagnoseSuggestions, runSdkrw, scheins } =
      encounterDiagnoseTimeline || {};
    const diagnoseSuggestionsUse =
      sourceScreen === SourceScreen.PATIENT_MANAGEMENT
        ? diagnoseSuggestions
        : diagnoseSuggestionsProps?.find(
            (datum) => datum.diagnoseId === entry.id
          )?.diagnoseSuggestions;

    const buildActionButtonText = {
      [SuggestionType.SUGGESTIONTYPE_ADD]: t('btnAddDiagnose'),
      [SuggestionType.SUGGESTIONTYPE_REPLACE]: t('btnUpdateDiagnose'),
      [SuggestionType.SUGGESTIONTYPE_DELETE]: t('btnDeleteDiagnose'),
    };
    const kvScheins = new Map<string, boolean>();
    if (!!scheins && Number(scheins?.length) > 0) {
      scheins.forEach((schein) => {
        if (schein.group === MainGroup.KV) {
          kvScheins.set(schein.scheinId, false);
        }
      });
    }
    const isShowSuggestion =
      kvScheins.size > 0 && runSdkrw === RunSdkrwEnum.RUNSDKRWENUM_DONE;
    const runningRules =
      kvScheins.size > 0 && runSdkrw === RunSdkrwEnum.RUNSDKRWENUM_DEFAULT;

    return (
      <Flex column>
        {runningRules && (
          <MessageBar
            key={'info'}
            type={'info'}
            content={t('messageCancelled')}
            actionButtonGroup={
              <Button
                type="button"
                intent={Intent.PRIMARY}
                onClick={cancelledSuggestionDiagnose}
              >
                {t('btnCancelled')}
              </Button>
            }
          />
        )}
        {isShowSuggestion &&
          !timelineStore?.isHistoryMode &&
          diagnoseSuggestionsUse?.map(
            (suggestion, index) =>
              !suggestion.applied &&
              !!suggestion.diagnoses?.length && (
                <MessageBar
                  key={index}
                  type={'info'}
                  content={`${t('messageCorrectionProposal')} ${
                    suggestion.proposal
                  }`}
                  actionButtonGroup={
                    <Button
                      type="button"
                      intent={Intent.PRIMARY}
                      onClick={() => {
                        timelineActions.setTimelineModel(entry);
                        timelineActions.setDiagnoseSuggestions([suggestion]);
                      }}
                    >
                      {buildActionButtonText[suggestion.suggestionType]}
                    </Button>
                  }
                />
              )
          )}
      </Flex>
    );
  };

  const renderFurtherInfoDiagnoseDialog = (): Nullable<ReactElement> => {
    if (!openDiagnoseType) return null;

    let data: any = null;
    switch (openDiagnoseType as DiagnoseFurtherInfor) {
      case DiagnoseFurtherInfor.EndDate:
        data = {
          encounterDate: new Date(Number(entry.createdAt)),
          diagnoseText: encounterDiagnoseTimeline?.freeText,
        } as UpdatingData;
        break;
      case DiagnoseFurtherInfor.Exception:
        data = {
          exception: encounterDiagnoseTimeline?.exception,
        } as UpdatingData;
        break;
      case DiagnoseFurtherInfor.Explanation:
        data = {
          explanation: encounterDiagnoseTimeline?.explanation,
        } as UpdatingData;
        break;
      default:
        break;
    }

    return (
      <TerminatePermanentDiagnoseDialog
        updatingData={data}
        isOpen={!!openDiagnoseType}
        isLoading={isLoading}
        onClose={onCloseDiagnoseDialog}
        onSave={terminateChronicDiagnose}
        type={openDiagnoseType}
      />
    );
  };

  const getFormNamePatientAccompaniment = (): any => {
    let formName = '';

    for (const form in contractPatientAccompanimentMapping) {
      if (
        contractPatientAccompanimentMapping[form].includes(
          selectedContractDoctor.contractId
        )
      ) {
        formName = form;
        return formName;
      }
    }
    return formName;
  };

  const checkExistPrinterSetting = (formName: string): boolean => {
    const diagnoseEntry = entry.encounterDiagnoseTimeline;
    const savedOrPrinted = settingStore.printerSetting.settings.savedOrPrinted;
    const hasAnySavedOrPrintedBefore = savedOrPrinted.some((setting) => {
      if (
        setting.year !== timelinesByQuarter.year ||
        setting.quarter !== timelinesByQuarter.quarter
      ) {
        return false;
      }
      if (setting.formName !== formName) {
        return false;
      }
      if (setting.icdCode !== diagnoseEntry?.code) {
        return false;
      }
      if (
        !isNil(setting.certainty) &&
        setting.certainty !== diagnoseEntry?.certainty
      ) {
        return false;
      }

      return true;
    });

    return hasAnySavedOrPrintedBefore;
  };

  const openForm = (formName: FormName, icd: IcdInfo | null) => {
    musterFormDialogActions.setCurrentFormName(formName);
    musterFormDialogActions.setPrinterSettingData({
      patientId: entry.patientId || '',
      setting: {
        formName,
        icdCode: icd?.code as string,
        certainty: icd?.certainty as string,
        quarter: entry.quarter,
        year: entry.year,
      },
    });
  };

  const getRelatedIcdForAccompanimentContract = (
    patientAge: number
  ): IcdInfo | null => {
    const diagnoseEntry = entry.encounterDiagnoseTimeline;
    const formName = getFormNamePatientAccompaniment();
    const checkingDiagnose = {
      code: diagnoseEntry?.code || '',
      certainty: diagnoseEntry?.certainty,
    };

    if (checkExistPrinterSetting(formName)) {
      return null;
    }

    const isFirstDiagnosisCodeInQuarter = checkExistedICDCode(
      timelinesByQuarter.timelineModels,
      diagnoseEntry?.code || '',
      diagnoseEntry?.certainty || '',
      entry
    );
    if (!isFirstDiagnosisCodeInQuarter) {
      return null;
    }

    // NOTE: AC2 BKK_BOSCH (notion)
    if (
      [
        FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6,
        FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6,
      ].includes(formName)
    ) {
      if (patientAge < AGE_RESTRICTED) {
        return getValidDiagnose(DIAGNOSIS_CODE_BKK_BOSCH, checkingDiagnose);
      }

      return getValidDiagnose(
        [
          ...DIAGNOSIS_CODE_BKK_BOSCH,
          ...DIAGNOSIS_CODE_BKK_BOSCH_AGE_RESTRICTED,
        ],
        checkingDiagnose
      );
    }

    // NOTE: AC2 BKK_VAG_HE (notion)
    if (
      formName === FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4
    ) {
      // if (patientAge < AGE_RESTRICTED) {
      //   return getValidDiagnose(DIAGNOSIS_CODE_BKK_VAG_HE, checkingDiagnose);
      // }

      return getValidDiagnose(fomrOverviewStore.icdList, checkingDiagnose);
    }

    return null;
  };

  const getRelatedIcdForBKKVAGForm = (): IcdInfo | null => {
    const isBKKVAGForm =
      BKK_BOSCH_VAG_BW_Praeventionsverordnung_CONTRACT.includes(
        selectedContractDoctor.contractId || ''
      );

    if (!isBKKVAGForm) {
      return null;
    }

    const diagnoseEntry = entry.encounterDiagnoseTimeline;

    if (
      diagnoseEntry?.type !== DiagnoseType.DIAGNOSETYPE_PERMANENT ||
      checkExistPrinterSetting(
        FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1
      )
    ) {
      return null;
    }

    const listMatched = timelinesByQuarter.timelineModels.filter(
      (item) =>
        item.encounterDiagnoseTimeline?.type ===
          DiagnoseType.DIAGNOSETYPE_PERMANENT &&
        !getBillingInfo(patientFileStore.schein.originalList, item, () => '')
          ?.billingSubmitted &&
        getValidDiagnose(
          DIAGNOSIS_CODE_BKK_BOSCH_VAG_BW_PRAEVENTIONSVERORDNUNG,
          {
            code: item.encounterDiagnoseTimeline?.code || '',
            certainty: item.encounterDiagnoseTimeline?.certainty,
          }
        )
    );

    if (!listMatched[0] || listMatched[0].id !== entry.id) {
      return null;
    }

    return { code: diagnoseEntry.code, certainty: diagnoseEntry.certainty };
  };

  const renderCheckAlertPatientAccompaniment = () => {
    if (!isContractSupport) {
      return null;
    }

    const refIcd = getRelatedIcdForAccompanimentContract(
      getAge(new Date(patient?.dateOfBirth || ''))
    );
    if (isNil(refIcd)) {
      return null;
    }

    const formName = getFormNamePatientAccompaniment();
    return (
      <Flex align="center">
        <MessageBar
          showIcon={false}
          type="warning"
          content={tForm(formName)}
          actionButtonGroup={
            <Button intent="primary" onClick={() => openForm(formName, refIcd)}>
              {tCommon('View')}
            </Button>
          }
        />
      </Flex>
    );
  };

  const renderCheckAlertPatientAccompanimentForBKKVAGForm = () => {
    if (!isContractSupport || hasBilled) {
      return null;
    }

    const refIcd = getRelatedIcdForBKKVAGForm();
    if (isNil(refIcd)) {
      return null;
    }

    const doc = fomrOverviewStore.listForms?.find(
      (f) => f.id === FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1
    );
    return (
      <Flex align="center">
        <MessageBar
          showIcon={false}
          type="warning"
          content={doc?.title || ''}
          actionButtonGroup={
            <Button
              intent="primary"
              onClick={() =>
                openForm(
                  FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1,
                  refIcd
                )
              }
            >
              {tCommon('View')}
            </Button>
          }
        />
      </Flex>
    );
  };

  const listErrorNotShow = patientFileStore?.skippedErrorKeyList || [];

  const setDismissError = (errorCode) => () => {
    const errList = cloneDeep(listErrorNotShow);
    errList.push({ entryId: id || '', errorCode });
    patientFileActions.saveSkippedErrorKeyList(errList);
  };

  const renderMessageBar = (): Nullable<ReactElement> => {
    if (isNil(encounterDiagnoseTimeline) || entry.isImported) {
      return;
    }
    const { errors: errs = [] } = encounterDiagnoseTimeline || {};
    const groupErrors = handleReduceErrors(errs);
    const grouped = groupBy(groupErrors ?? [], 'type');
    return (
      <Flex column className="message-bar">
        {Object.keys(grouped ?? {}).map((key) => {
          const handledErrors = grouped[key];
          return handledErrors?.map((error, index: number) => {
            if (typeof error?.errorCode !== undefined && error?.errorCode) {
              return (
                <div key={index}>
                  {listErrorNotShow?.findIndex(
                    (item) =>
                      item?.entryId === id &&
                      item?.errorCode === error.errorCode
                  ) === -1 ? (
                    <>
                      <MessageBar
                        showIcon={index === 0}
                        hasBullet={handledErrors?.length > 1}
                        key={index}
                        type={error?.type as ITimelineEntryStatus}
                        content={t(
                          `ErrorMessages.${error.errorCode}` as any,
                          error?.metaData && {
                            minAge: error?.metaData?.minAge || '',
                            maxAge: error?.metaData?.maxAge || '',
                            minAgeUnit: error?.metaData?.unitMinAge || '',
                            maxAgeUnit: error?.metaData?.unitMaxAge || '',
                          }
                        )}
                        actionButtonGroup={
                          <>
                            {error?.metaData?.isShowDismiss && (
                              <EntryDismissBtn
                                onClick={setDismissError(error.errorCode)}
                              />
                            )}
                            {error?.errorCode ===
                              ErrorCode.ErrorCode_Validation_Missing_ScheinId && (
                              <Flex>
                                <Button
                                  intent={Intent.PRIMARY}
                                  onClick={() =>
                                    openCreateSchein(mainGroupNoSchein)
                                  }
                                >
                                  {t('btnCreateNewSchein')}
                                </Button>
                              </Flex>
                            )}
                          </>
                        }
                      />
                    </>
                  ) : null}
                </div>
              );
            }

            return (
              <MessageBar
                showIcon={index === 0}
                hasBullet={handledErrors?.length > 1}
                key={error?.message}
                type={error?.type.toLowerCase() as ITimelineEntryStatus}
                content={error.message}
              />
            );
          });
        })}
      </Flex>
    );
  };

  const renderExtraInfo = (
    diagnose?: EncounterDiagnoseTimeline
  ): Nullable<ReactElement> => {
    const hasNoDiagnose = isNil(diagnose);
    const hasNoExtraInfo =
      isNil(diagnose?.exception) &&
      isNil(diagnose?.explanation) &&
      isNil(diagnose?.validUntil);
    if (hasNoDiagnose || hasNoExtraInfo) {
      return null;
    }

    return (
      <Flex className="extra-info">
        <Flex column>
          <Flex auto>
            <Flex column auto className="extra-info-content-wrapper">
              {diagnose.validUntil && (
                <Flex className="extra-info-content">
                  <BodyTextS>{`${t('diagnoseEnddate')}: ${toDateFormat(
                    new Date(diagnose.validUntil),
                    {
                      dateFormat: 'dd.MM.yyyy',
                    }
                  )}`}</BodyTextS>
                </Flex>
              )}
              {diagnose.exception && (
                <Flex className="extra-info-content">
                  <BodyTextS>{`${t('exception')}: ${
                    diagnose.exception
                  }`}</BodyTextS>
                </Flex>
              )}
              {diagnose.explanation && (
                <Flex className="extra-info-content">
                  <BodyTextS>
                    {`${t('explanation')}: ${diagnose.explanation}`}
                  </BodyTextS>
                </Flex>
              )}
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    );
  };

  const alertPatientAccompaniment = renderCheckAlertPatientAccompaniment();
  const alertPatientAccompanimentForBKKVAGForm =
    renderCheckAlertPatientAccompanimentForBKKVAGForm();

  useEffect(() => {
    if (
      timelineStore.isPendingFetchNewData ||
      entry.id !== timelineStore.timelineIdCreated
    ) {
      return;
    }

    // NOTE: auto-open form when the diagnose includes form warnings
    if (alertPatientAccompaniment) {
      const refIcd = getRelatedIcdForAccompanimentContract(
        getAge(new Date(patient?.dateOfBirth || ''))
      );
      const formName = getFormNamePatientAccompaniment();
      openForm(formName, refIcd);
      return;
    }

    if (alertPatientAccompanimentForBKKVAGForm) {
      const refIcd = getRelatedIcdForBKKVAGForm();
      openForm(FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1, refIcd);
    }
  }, [
    timelineStore.isPendingFetchNewData,
    timelineStore.timelineIdCreated,
    isContractSupport,
    hasBilled,
  ]);

  return (
    <Flex
      auto
      className={getCssClass(
        className,
        style,
        openMenuStyle,
        encounterDiagnoseTimeline?.type?.toLowerCase()
      )}
    >
      <div className="info-wrapper">
        <Flex>
          {isEditing && (
            <DiagnoseEditor
              entry={entry}
              onSave={onDiagnoseEditorSave}
              onCancel={closeEditMode}
            />
          )}
          {!isEditing && (
            <p
              className={getCssClass('content hover-text', {
                disabled: isDisabled,
              })}
              style={{
                textDecoration:
                  entry?.auditLogs[0]?.actionType === ActionType.Remove
                    ? 'line-through'
                    : 'none',
                color:
                  entry?.auditLogs[0]?.actionType === ActionType.Remove
                    ? 'red'
                    : undefined,
              }}
              onClick={setEditInline}
            >
              {!isNil(encounterDiagnoseTimeline) && (
                <>
                  {encounterDiagnoseTimeline?.command} -{' '}
                  {isMarkedTreatmentRelevant && (
                    <Tooltip
                      content={
                        <>
                          <H5 color={COLOR.TEXT_WHITE}>
                            {t('permanentHeader')}
                          </H5>
                          <BodyTextS color={COLOR.TEXT_WHITE}>
                            {t('permanentContent')}
                          </BodyTextS>
                        </>
                      }
                    >
                      <Svg
                        className="timeline-entry_row_menu-icon"
                        src={MarkedTreatmentRelevantIcon}
                      />
                    </Tooltip>
                  )}
                  <HighLightWrapper matchedTokens={timelineStore.matchedTokens}>
                    {renderDiagnoseText(entry.encounterDiagnoseTimeline, false)}
                  </HighLightWrapper>
                </>
              )}
            </p>
          )}
        </Flex>
        <Flex>
          {!isEditing && !encounterDiagnoseTimeline?.validUntil && (
            <Flex align="center">
              <TimelineDeletionRemain
                className="timeline-deletion-remain"
                entry={entry}
              />
              {!!currentSchein && (
                <div
                  className="main-group"
                  style={{
                    textDecoration:
                      entry?.auditLogs[0]?.actionType === ActionType.Remove
                        ? 'line-through'
                        : '',
                    color:
                      entry?.auditLogs[0]?.actionType === ActionType.Remove
                        ? 'red'
                        : colorMainGroup(currentSchein.scheinMainGroup),
                    backgroundColor: backgroundMainGroup(
                      currentSchein.scheinMainGroup
                    ),
                  }}
                >
                  {formatScheinName(currentSchein, t('pseudo'))}
                </div>
              )}
              <Flex>{doctorIcon}</Flex>
              <TimelineActionHOC entry={entry} hasBilled={hasBilled}>
                <Popover
                  className="actions-group"
                  content={
                    isNil(encounterDiagnoseTimeline)
                      ? ''
                      : renderActionMenu(
                          encounterDiagnoseTimeline as EncounterDiagnoseTimeline,
                          isDisabled
                        )
                  }
                  onOpened={() => setOpenActionMenu(true)}
                  onClosed={() => setOpenActionMenu(false)}
                >
                  <Tooltip content={t('more')} position="top">
                    <Svg className="more-icon" src={MoreIcon} />
                  </Tooltip>
                </Popover>
              </TimelineActionHOC>
            </Flex>
          )}
        </Flex>
      </div>
      {!isEditing &&
        renderExtraInfo(encounterDiagnoseTimeline as EncounterDiagnoseTimeline)}
      {!timelineStore?.isHistoryMode && (
        <>
          {renderMessageBar()}
          {renderSuggestMessageBar()}
          {renderFurtherInfoDiagnoseDialog()}
          {alertPatientAccompaniment}
          {alertPatientAccompanimentForBKKVAGForm}
        </>
      )}
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(DiagnoseEntry, {
    namespace: 'PatientManagement',
    nestedTrans: 'DiagnoseEntry',
  })
);
