
import { ComponentType } from 'react';
import Theme from '@tutum/mvz/theme';
import OriginalMedicationPlanHistoryEntryMemo, {
  IMedicationPlanHistoryEntryProps,
} from './MedicationPlanHistoryEntry';
import { leftSidebarColor } from '../EntryCommon.styled';
import TimelineService from '../../../Timeline.service';
import { COLOR } from '@tutum/design-system/themes/styles';

const styled = Theme.styled;
const MedicationPlanHistoryEntry: ComponentType<IMedicationPlanHistoryEntryProps> = styled(
  OriginalMedicationPlanHistoryEntryMemo
)`
  position: relative;

  &:before {
    ${leftSidebarColor('transparent')}
  }

  .sl-red-color {
    color: ${COLOR.TEXT_NEGATIVE} !important;
  }
  .entry-wrapper {
    flex-direction: row;
    .medication_plan-content {
      flex: 2;

      color: ${(props) =>
        TimelineService.parseEntryColor(props.entry, props.theme.timelineTheme)
          ?.text};
    }
  }

  .updated-information {
    display: block;
    font-size: 13px;
    line-height: 20px;

    &:first-of-type {
      margin-top: 8px;
    }
  }

  .text-bold {
    font-weight: 600;
  }

  .sl-info {
    margin-left: 4px;
  }
`;

export default MedicationPlanHistoryEntry;
