
import React, { memo, useMemo } from 'react';
import I18n, {
  II18nFixedNamespace,
  IFixedNamespaceTFunction,
} from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { Menu, Popover } from '@tutum/design-system/components/Core';
import { IContractInfo } from '../../../../../types/contract.type';
import {
  Flex,
  Svg,
  Tooltip,
  TagMedicine,
} from '@tutum/design-system/components';
import { flatten } from '@tutum/design-system/infrastructure/utils';
import { MedicationPlanActionType } from '@tutum/hermes/bff/service_domains_patient_file';
import ChangeItemDateMenuItem from '../ChangeItemDateMenuItem';
import { medicationStore } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { MedicationInformation } from '@tutum/hermes/bff/repo_bmp_common';
import { EncounterMedicinePlanHistory } from '@tutum/hermes/bff/repo_encounter';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import { parseContentToRead } from '@tutum/mvz/module_medication/medication-plan/MedicationPlan.helper';
import HighlightWrapper from '@tutum/design-system/components/HighlightWrapper/HighlightWrapper';
import { useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';

const MoreIcon = '/images/more.svg';

export interface IMedicationPlanHistoryEntryProps {
  entry: TimelineModel;
  lastItem?: boolean;
  patientId: string;
  contract?: IContractInfo;
  doctorIcon: JSX.Element;
  setItemToEditDate?: () => void;
  keyword?: string;
}

const compareChanged = (
  t: IFixedNamespaceTFunction<
    keyof typeof PatientManagementI18n.MedicationPlanHistoryEntry
  >,
  old: MedicationInformation,
  current: MedicationInformation,
  units,
  forms
): Array<{ attributeName: string; old: any; new: any }> => {
  if (!(old || current)) {
    return [];
  }
  const fieldsChanged: Array<{ attributeName: string; old: any; new: any }> =
    [];
  if (old?.tradeName !== current?.tradeName) {
    fieldsChanged.push({
      attributeName: t('tradeName'),
      old: old?.tradeName,
      new: current?.tradeName,
    });
  }
  fieldsChanged.concat(
    old?.substances?.map((o, index) => {
      if (o?.name !== current[index]?.name) {
        return {
          attributeName: t('substance', { index }),
          old: o?.name,
          new: current[index]?.name,
        };
      }
    }) || []
  );
  if (old?.unit !== current?.unit) {
    const oldUnit = units.find((u) => u.code == old?.unit);
    const currentUnit = units.find((u) => u.code == current?.unit);
    fieldsChanged.push({
      attributeName: t('unit'),
      old: oldUnit?.description,
      new: currentUnit?.description,
    });
  }
  if (old?.drugForm !== current?.drugForm) {
    const oldDrugForm = forms.find((f) => f.code == old?.drugForm);
    const currentDrugForm = forms.find((f) => f.code == current?.drugForm);
    fieldsChanged.push({
      attributeName: t('drugForm'),
      old: oldDrugForm?.description,
      new: currentDrugForm?.description,
    });
  }

  const oldIntake = old?.intakeInterval;
  const curIntake = current?.intakeInterval;
  if (
    oldIntake?.freetext !== curIntake?.freetext ||
    oldIntake?.morning !== curIntake?.morning ||
    oldIntake?.afternoon !== curIntake?.afternoon ||
    oldIntake?.evening !== curIntake?.evening ||
    oldIntake?.night !== curIntake?.night
  ) {
    fieldsChanged.push({
      attributeName: t('intakeInterval'),
      old:
        oldIntake?.freetext ||
        (oldIntake?.morning ||
        oldIntake?.afternoon ||
        oldIntake?.evening ||
        oldIntake?.night
          ? `${oldIntake?.morning || ''} - ${oldIntake?.afternoon || ''} - ${
              oldIntake?.evening || ''
            } - ${oldIntake?.night || ''}`
          : undefined),
      new:
        curIntake?.freetext ||
        (curIntake?.morning ||
        curIntake?.afternoon ||
        curIntake?.evening ||
        curIntake?.night
          ? `${curIntake?.morning || ''} - ${curIntake?.afternoon || ''} - ${
              curIntake?.evening || ''
            } - ${curIntake?.night || ''}`
          : undefined),
    });
  }
  if (old?.hint !== current?.hint) {
    fieldsChanged.push({
      attributeName: t('hint'),
      old: parseContentToRead(old?.hint),
      new: parseContentToRead(current?.hint),
    });
  }
  if (old?.reason !== current?.reason) {
    fieldsChanged.push({
      attributeName: t('reason'),
      old: parseContentToRead(old?.reason),
      new: parseContentToRead(current?.reason),
    });
  }
  if (old?.additionalLine !== current?.additionalLine) {
    fieldsChanged.push({
      attributeName: t('additionalLine'),
      old: parseContentToRead(old?.additionalLine),
      new: parseContentToRead(current?.additionalLine),
    });
  }
  return fieldsChanged.filter((f) => Boolean(f.old || f.new));
};

export const handleCompare = (
  t: IFixedNamespaceTFunction<
    keyof typeof PatientManagementI18n.MedicationPlanHistoryEntry
  >,
  encounterMedicinePlanHistory: EncounterMedicinePlanHistory
) => {
  const { units, forms } = medicationStore;

  const compare =
    encounterMedicinePlanHistory.actionType === MedicationPlanActionType.Update
      ? compareChanged(
          t,
          encounterMedicinePlanHistory.beforeMedicineInfo,
          encounterMedicinePlanHistory.medicineInfo,
          units,
          forms
        )
      : [];
  const unit = units.find(
    (u) => u.code == encounterMedicinePlanHistory?.medicineInfo?.unit
  );
  const displayName = `${
    `${encounterMedicinePlanHistory?.medicineInfo?.tradeName} ` || ''
  }${unit?.description || ''}`.trim();
  const componentAndSubstances =
    encounterMedicinePlanHistory?.medicineInfo?.substances || [];
  const substanceName = flatten(
    componentAndSubstances.map((item) => `${item.name} ${item.concentration}`)
  ).join(', ');
  return {
    compare,
    displayName,
    substanceName,
  };
};

export const getActionType = (
  t: IFixedNamespaceTFunction<
    keyof typeof PatientManagementI18n.MedicationPlanHistoryEntry
  >,
  encounterMedicinePlanHistory: EncounterMedicinePlanHistory
) => {
  switch (encounterMedicinePlanHistory?.actionType) {
    case MedicationPlanActionType.Update:
      return t('updatedAMedication');
    case MedicationPlanActionType.Delete:
      return t('deleteMedication');
    case MedicationPlanActionType.Create:
      return t('addedAMedication');
    default:
      return t('addedAMedication');
  }
};

function MedicationPlanHistoryEntryMemo(
  props: IMedicationPlanHistoryEntryProps &
    IMvzThemeProps &
    II18nFixedNamespace<
      keyof typeof PatientManagementI18n.MedicationPlanHistoryEntry
    >
) {
  const { t: tDoctorSample } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.DoctorSample
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'DoctorSample',
  });

  const { className, t, entry, doctorIcon, setItemToEditDate } = props;
  const { matchedTokens } = useTimeLineStore();
  const isRemoved =
    entry?.encounterMedicinePlanHistory?.actionType ===
    MedicationPlanActionType.Delete;
  const isDoctorSample = Boolean(
    entry?.encounterMedicinePlanHistory?.medicineInfo?.productInformation
      ?.sampleProductFlag
  );

  const actiontType = useMemo(() => {
    return getActionType(t, entry?.encounterMedicinePlanHistory);
  }, [entry?.encounterMedicinePlanHistory?.actionType]);

  const renderDetailMedicineInfo = useMemo(() => {
    const { encounterMedicinePlanHistory } = entry;
    if (!encounterMedicinePlanHistory) {
      return null;
    }

    const { compare, displayName, substanceName } = handleCompare(
      t,
      encounterMedicinePlanHistory
    );

    return (
      <React.Fragment>
        <span className={`text-bold ${isRemoved ? 'sl-red-color' : ''}`}>
          &nbsp;-&nbsp;
          {displayName}
        </span>
        <span className={`${isRemoved ? 'sl-red-color' : ''}`}>
          &nbsp;-&nbsp;
          {substanceName}
        </span>
        {compare.map((c) => (
          <span
            key={c.attributeName}
            className={`updated-information ${isRemoved ? 'sl-red-color' : ''}`}
          >
            <b>{c.attributeName}:</b>&nbsp;{c.old}
            &nbsp;&rarr;&nbsp;
            {c.new}
          </span>
        ))}
      </React.Fragment>
    );
  }, [entry?.encounterMedicinePlanHistory?.id]);

  return (
    <Flex auto className={className} column>
      <Flex className="entry-wrapper" column auto>
        <p className="medication_plan-content hover-text">
          <span>{t('medicationPlan')}&nbsp;-&nbsp;</span>
          <span className={`text-bold ${isRemoved ? 'sl-red-color' : ''}`}>
            {actiontType}
          </span>
          <HighlightWrapper matchedTokens={matchedTokens}>
            {renderDetailMedicineInfo}
          </HighlightWrapper>
          {isDoctorSample && (
            <TagMedicine className="sl-info">
              {tDoctorSample('doctorSample')}
            </TagMedicine>
          )}
        </p>
        <Flex>
          <TimelineDeletionRemain entry={entry} />
          <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
          <TimelineActionHOC>
            <Flex className="actions-group">
              <Popover
                content={
                  <Menu className="action-menu">
                    <ChangeItemDateMenuItem
                      onClick={() => {
                        setItemToEditDate();
                      }}
                    />
                  </Menu>
                }
              >
                <Tooltip content={t('more')} position="top">
                  <Svg className="more-icon" src={MoreIcon} />
                </Tooltip>
              </Popover>
            </Flex>
          </TimelineActionHOC>
        </Flex>
      </Flex>
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(MedicationPlanHistoryEntryMemo, {
    namespace: 'PatientManagement',
    nestedTrans: 'MedicationPlanHistoryEntry',
  })
);
