import {
  BodyTextM,
  Flex,
  LoadingState,
  MessageBar,
  SLTagState,
  Svg,
  Tag,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Menu,
  MenuItem,
  Popover,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import { StatusText } from '@tutum/hermes/bff/eau_common';
import { FormName, Form as FormSetting } from '@tutum/hermes/bff/form_common';
import { printPlainPdf } from '@tutum/hermes/bff/legacy/app_mvz_form';
import { EditResponse } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { EnrollmentPrintFormStatus } from '@tutum/hermes/bff/legacy/repo_encounter';
import { DocumentStatus } from '@tutum/hermes/bff/qes_common';
import { Form } from '@tutum/hermes/bff/service_domains_patient_file';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import AccountManagementUtil from '@tutum/infrastructure/utils/form.util';
import { BASE_PATH_MVZ } from '@tutum/infrastructure/utils/string.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { EDocumentStatusTag } from '@tutum/mvz/components/e-document-status-tag';
import TimelineActionHOC from '@tutum/mvz/components/timeline-action/TimelineActions.styled';
import {
  FormTypeSetting,
  printSettingStore as printSettingsStore,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import { referralThroughTssActions } from '@tutum/mvz/hooks/useReferralThroughTss.store';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import {
  getFormIdDisplay,
  HANDOVER_LETTER_FORM,
} from '@tutum/mvz/module_form/form-overview/FormOverview.helper';
import {
  musterFormDialogActions,
  useMusterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { patientEnrollmentActions } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-form/PatientEnrollmentForm.store';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import PrinterService from '@tutum/mvz/services/printer.service';
import Theme, { IMvzTheme } from '@tutum/mvz/theme';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import ChangeItemDateMenuItem from '../ChangeItemDateMenuItem';
import { M6_HVZ_COVER_LETTER_FORM } from '@tutum/mvz/constant/form';
import cloneDeep from 'lodash/cloneDeep';
import { useQueryGetForm1450HandoverPatients } from '@tutum/hermes/bff/legacy/app_mvz_patient_overview';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import PrintPreviewContent from '@tutum/mvz/module_form/form-overview/HandoverLetterForm/PrintPreviewContent';
import { PrinterProfile } from '@tutum/hermes/bff/printer_common';
import Guard from '@tutum/mvz/hooks/Guard';
import { UserType } from '@tutum/hermes/bff/legacy/common';
import { TimelineDeletionRemain } from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/TimelineDeletionRemain';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';

export interface IFormEntryProps {
  className?: string;
  theme?: IMvzTheme;
  patientId?: string;
  entry: TimelineModel;
  contract: IContractInfo;
  doctorIcon: JSX.Element;
  listForms: FormSetting[];
  isCoverLetterForm?: boolean;
  onRemoveEntry: (hardDelete?: boolean) => void;
  setItemToEditDate?: () => void;
  onEditTimelineItem?: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error: any) => void
  ) => void;
}

const MoreIcon = '/images/more.svg';
const refillIcon = '/images/refill.svg';
const eyeOnIcon = '/images/eye-on.svg';
const editIcon = '/images/content-editor-edit-3.svg';
const trashIcon = '/images/trash-bin-red.svg';
const CornerRightUpIcon = '/images/corner-right-up.svg';
const CornerLeftDownIcon = '/images/corner-left-down.svg';

export const renderFormText = (t, formName) => {
  return `${t('Timeline.forms')} - (${getFormIdDisplay(formName)}) ${t(
    `Forms.${formName}`,
    null,
    {
      fallback: formName,
    }
  )}`;
};

const renderFormEnrollmentText = (t, formName) => {
  return `${t('Timeline.forms')} - ${formName}`;
};

const FormContent = ({
  form,
  isEnrollment,
  status,
  isCoverLetterForm,
}: {
  form?: Form;
  isEnrollment: boolean;
  status?: EnrollmentPrintFormStatus;
  isCoverLetterForm: boolean;
}) => {
  const { t } = I18n.useTranslation({
    namespace: 'Form',
  });

  if (isEnrollment) {
    const formTitle = form?.prescribe.formTitle;
    let slStateStatus: SLTagState = 'neutral';

    switch (status) {
      case EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Printed_Failed:
        slStateStatus = 'error';
        break;
      case EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Printed_Succesfully:
        slStateStatus = 'positive';
        break;
      default:
        slStateStatus = 'neutral';
        break;
    }

    return (
      <BodyTextM>
        {renderFormEnrollmentText(t, formTitle)}&nbsp;
        {!!status && (
          <>
            <Tag slStyle="fill" slState="">
              {t('Printed')}
            </Tag>
            <Tag slState={slStateStatus}>{t(status)}</Tag>
          </>
        )}
      </BodyTextM>
    );
  }

  if (isCoverLetterForm) {
    return `${t('Timeline.forms')} - ${t('Forms.CoverLetterForm')}`;
  }

  if (form?.prescribe.formTitle) {
    return `${t('Timeline.forms')} - ${form.prescribe.formTitle}`;
  }

  const formName = form?.prescribe.formName;

  if (formName === (HANDOVER_LETTER_FORM as FormName)) {
    return `${t('Timeline.forms')} - ${t(`Forms.${HANDOVER_LETTER_FORM}`)}`;
  }

  // "sent": for other cost unit OR special group 07 OR has EHIC
  if (
    form?.prescribe.eAUSetting?.statusText === StatusText.StatusText_Sent &&
    form.prescribe.eAUStatus === DocumentStatus.Status_NotDispatched
  ) {
    return (
      <Flex gap={8} align="center">
        <BodyTextM>{renderFormText(t, formName)}</BodyTextM>
        <EDocumentStatusTag status={DocumentStatus.Status_Sent} />
      </Flex>
    );
  }

  return (
    <Flex gap={8} align="center">
      <BodyTextM>{renderFormText(t, formName)}</BodyTextM>
      {form?.prescribe.eAUStatus && (
        <EDocumentStatusTag status={form.prescribe.eAUStatus} />
      )}
    </Flex>
  );
};

const TimelineEntry = ({
  className,
  entry,
  contract,
  doctorIcon,
  listForms,
  isCoverLetterForm,
  onRemoveEntry,
  setItemToEditDate,
  onEditTimelineItem,
}: IFormEntryProps) => {
  const form = entry.encounterForm;
  const [printUrl, setPrintUrl] = useState<string>('');
  const [VERT1181, setVERT1181] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [isOpenPrintPreview, setOpenPrintPreview] = useState<boolean>(false);
  const [printPreviewData, setPrintPreviewData] = useState<any[]>([]);

  const { data: lkkPatients } = useQueryGetForm1450HandoverPatients({
    enabled: false,
  });

  const { t } = I18n.useTranslation<keyof typeof FormI18n.Timeline>({
    namespace: 'Form',
    nestedTrans: 'Timeline',
  });
  const { t: tForms } = I18n.useTranslation({
    namespace: 'Form',
    nestedTrans: 'Forms',
  });
  const { t: tForm } = I18n.useTranslation({
    namespace: 'Form',
  });
  const { t: tCommonTable } = I18n.useTranslation<any>({
    namespace: 'Common',
    nestedTrans: 'Table',
  });

  const musterFormDialogStore = useMusterFormDialogStore();
  const patientFileStore = usePatientFileStore();
  const scheinInfo = useMemo(() => {
    return patientFileStore.schein.list.find(
      (schein) => schein.scheinId === entry.scheinIds?.[0]
    );
  }, [patientFileStore.schein.list, entry.scheinIds?.[0]]);
  const { isContractSupport: hasSupportForm1413 } = useCheckContractSupport(
    ['FORM1413'],
    [scheinInfo?.hzvContractId]
  );

  const {
    patientManagement: { patient, selectedContractDoctor },
  } = useContext(PatientManagementContext.instance);

  // AC 3 https://www.notion.so/silenteer/US-2-Forms-in-context-HzV-FaV-As-user-I-can-open-contract-specific-forms-63c4d0d9e64048818e698ca07c29b176
  const excludeFormPrinted = [FormName.Muster_52_0_V2, FormName.Muster_52_2_V3];

  const getHint = (hasSupportForm1413: boolean) => {
    const isPrinted = !!form?.prescribe.printedDate;

    switch (form?.prescribe.formName) {
      case FormName.Muster_52_0_V2:
      case FormName.Muster_52_2_V3:
        return hasSupportForm1413
          ? t('M52Hint')
          : !isPrinted
            ? t('PrintOutHint')
            : '';
      default:
        return t('PrintOutHint');
    }
  };

  const renderHintPrintout = (hasSupportForm1413: boolean) => {
    if (
      form?.prescribe.printedDate &&
      // if item include in this list, should render hint
      excludeFormPrinted.every((item) => item !== form.prescribe.formName)
    ) {
      return null;
    }
    const hint = getHint(hasSupportForm1413);

    if (!hint) {
      return null;
    }

    return (
      <Flex align="center">
        <MessageBar type="warning" content={hint} />
      </Flex>
    );
  };

  const matchedForm = useMemo(() => {
    return (listForms || []).find(
      (item) => item.id === form?.prescribe.formName
    );
  }, [form, listForms]);

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    form?.prescribe.treatmentDoctorId,
    entry.assignedToBsnrId
  );

  const isHandoverLetterForm = useMemo(() => {
    return form?.prescribe.formName === (HANDOVER_LETTER_FORM as FormName);
  }, [form?.prescribe.formName]);

  const isPrintFormOnly = useMemo(() => {
    return [
      FormName.Muster_PTV_3,
      FormName.Muster_PTV_10,
      FormName.AOK_FA_OC_BW_Antrag_AOK_Sports_V3,
    ].includes(form?.prescribe.formName as FormName);
  }, [form]);

  const isPrinted = !!form?.prescribe.printedDate;

  const isImportedMuster1 =
    entry.isImported && form?.prescribe.formName === FormName.Muster_1;

  const handleHandoverLetter = () => {
    const filteredPatients = (lkkPatients?.patientOverviews || []).filter(
      (item) => item.patientId === patient?.id
    );

    setPrintPreviewData(filteredPatients);
    setOpenPrintPreview(true);
  };

  const handlePrintOnly = () => {
    setPrintUrl(`${BASE_PATH_MVZ}/data/form/${form?.prescribe.formName}.pdf`);
  };

  const configureImportedForm = () => {
    musterFormDialogActions.setEnableValidation(false);
    musterFormDialogActions.setEAUDisable(
      form?.prescribe.formName === FormName.Muster_1
    );
  };

  const viewMainForm = () => {
    if (!form || !entry.id) {
      return;
    }
    const isReadOnly =
      isCoverLetterForm || (!!form.prescribe.printedDate && !isImportedMuster1);
    musterFormDialogActions.setCurrentEntryForm(entry.id);
    musterFormDialogActions.viewForm(
      AccountManagementUtil.convertFormData(form, treatmentDoctor),
      form.encounterId,
      contract,
      form.id,
      isReadOnly,
      false,
      isCoverLetterForm,
      entry.scheinIds?.[0]
    );
    musterFormDialogActions.setShowCoverLetter(!!isCoverLetterForm);
  };

  const handleViewForm = async () => {
    if (isHandoverLetterForm) {
      return handleHandoverLetter();
    }

    if (isPrintFormOnly) {
      return handlePrintOnly();
    }

    if (!form || !entry.id) {
      return;
    }

    if (entry.isImported) {
      configureImportedForm();
    }

    viewMainForm();
  };

  const handleViewCoverLetterForm = async () => {
    if (!form || !entry.id) {
      return;
    }

    musterFormDialogActions.setCurrentEntryForm(entry.id);
    musterFormDialogActions.viewForm(
      AccountManagementUtil.convertFormData(form, treatmentDoctor),
      form.encounterId,
      contract,
      form.id,
      false,
      false,
      true
    );
    musterFormDialogActions.setShowCoverLetter(true);
  };

  const handleChangeFormStatus = (status: EnrollmentPrintFormStatus) => {
    const updateEntry = cloneDeep(entry);

    if (!updateEntry.encounterForm) {
      return;
    }

    updateEntry.encounterForm.enrollmentPrintFormStatus = status;
    onEditTimelineItem?.(
      updateEntry,
      () => alertSuccessfully(t('TimelineUpdatedSuccess')),
      () => alertError(t('TimelineUpdatedFail'))
    );
  };

  useEffect(() => {
    if (!entry?.contractId) return;
    setLoading(true);
    webWorkerServices
      .doesContractSupportFunctions(['VERT1181'], entry.contractId)
      .then((result) => setVERT1181(result))
      .finally(() => setLoading(false));
  }, [entry?.contractId]);

  const renderActionMenu = useMemo(() => {
    return (
      <Menu className="action-menu">
        {loading && (
          <MenuItem
            key="editForm"
            disabled
            text={tCommonTable('loading')}
            icon={<LoadingState size={20} width={'20px'} border={'2px'} />}
          />
        )}
        {!loading && (
          <>
            {!form?.isEnrollmentForm && !isCoverLetterForm && (
              <>
                {!isPrinted && (
                  <ChangeItemDateMenuItem
                    text={t('editEntryDate')}
                    onClick={() => {
                      setItemToEditDate?.();
                    }}
                  />
                )}
                {!isPrintFormOnly && matchedForm?.hasRefill && (
                  <MenuItem
                    key="refillForm"
                    text={t('RefillForm')}
                    icon={<Svg src={refillIcon} />}
                    onClick={() => {
                      if (!form) {
                        return;
                      }

                      musterFormDialogActions.viewForm(
                        AccountManagementUtil.convertFormData(
                          form,
                          treatmentDoctor
                        ),
                        form.encounterId,
                        contract,
                        form.id,
                        false,
                        true
                      );
                    }}
                  />
                )}
              </>
            )}
            <MenuItem
              key="viewForm"
              text={t('ViewForm')}
              onClick={handleViewForm}
              icon={
                <Svg className="timeline-entry_row_menu-icon" src={eyeOnIcon} />
              }
            />
            {!isPrinted && isCoverLetterForm && (
              <MenuItem
                key="editForm"
                text={t('EditForm')}
                onClick={handleViewCoverLetterForm}
                icon={<Svg src={editIcon} />}
              />
            )}
            {VERT1181 &&
              form?.isEnrollmentForm &&
              form.enrollmentPrintFormStatus ===
                EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Printed_Created && (
                <MenuItem
                  key="handover"
                  icon={<Svg src={CornerRightUpIcon} />}
                  text={t('HandedOver')}
                  onClick={() =>
                    handleChangeFormStatus(
                      EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Handed_Over
                    )
                  }
                />
              )}
            {VERT1181 &&
              form?.isEnrollmentForm &&
              form.enrollmentPrintFormStatus ===
                EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Handed_Over && (
                <MenuItem
                  key="received"
                  icon={<Svg src={CornerLeftDownIcon} />}
                  text={t('Received')}
                  onClick={() =>
                    handleChangeFormStatus(
                      EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Received
                    )
                  }
                />
              )}
            {(!form?.isEnrollmentForm ||
              form.enrollmentPrintFormStatus !==
                EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Printed_Succesfully) &&
              !isCoverLetterForm && (
                <Guard roles={[UserType.MANAGER]} key="remove">
                  <MenuItem
                    key="removeForm"
                    icon={<Svg src={trashIcon} />}
                    text={t('Remove')}
                    onClick={() => onRemoveEntry(false)}
                  />
                </Guard>
              )}
          </>
        )}
      </Menu>
    );
  }, [
    loading,
    VERT1181,
    form,
    matchedForm,
    isCoverLetterForm,
    treatmentDoctor,
  ]);

  const renderActions = () => {
    return (
      <TimelineActionHOC>
        <Flex className="actions-group" align="center">
          <Popover content={renderActionMenu}>
            <Tooltip content={t('More')}>
              <Svg className="more-icon" src={MoreIcon} />
            </Tooltip>
          </Popover>
        </Flex>
      </TimelineActionHOC>
    );
  };

  const onClosePrintPreview = () => {
    setPrintUrl('');
  };

  const onClosePrintPreviewPdf = () => {
    setOpenPrintPreview(false);
    setPrintPreviewData([]);
  };

  const onPrintPlainForm = async (
    currentFormSetting: string,
    formName: string
  ) => {
    const isRemoveBackground = PrinterService.checkNeedRemoveBackground(
      formName as FormName
    );
    const resp = await printPlainPdf({
      formSetting: currentFormSetting,
      formName,
      treatmentDoctorId: entry.treatmentDoctorId,
      isRemoveBackground,
    });
    const getPdfUrl = async () => {
      return resp.data.formUrl;
    };
    const printSuccess = async () => {
      alertSuccessfully(tForm('formPrinted'));
      musterFormDialogActions.clear();
      referralThroughTssActions.clear();
    };
    const printFailure = () => {};

    await PrinterService.initAndPrint(
      isRemoveBackground ? formName : 'enrollment_document',
      getPdfUrl,
      {
        printSuccess,
        printFailure,
      }
    );
  };

  const formSetting = useMemo(() => {
    return printSettingsStore.formsSetting.find(
      (form) => form.formId === musterFormDialogStore.currentFormName
    );
  }, [musterFormDialogStore.currentFormName]);

  const handlePrintCustomPdf = useCallback(
    async (_: PrinterProfile | undefined, newFormSetting?: string) => {
      if (isPrinted) {
        return;
      }

      try {
        const updateEntry = cloneDeep(entry);

        if (!updateEntry.encounterForm) {
          return;
        }

        updateEntry.encounterForm.prescribe.printedDate = datetimeUtil.now();

        if (newFormSetting) {
          updateEntry.encounterForm.prescribe.payload = newFormSetting;
        }

        onEditTimelineItem?.(
          updateEntry,
          () => alertSuccessfully(t('TimelineUpdatedSuccess')),
          () => alertError(t('TimelineUpdatedFail'))
        );
      } catch (err) {
        console.error(err);
        alertError(t('TimelineUpdatedFail'));
      }
    },
    []
  );

  return (
    <Flex column className={className} onDoubleClick={handleViewForm}>
      <Flex className="form-content hover-text" justify="space-between">
        <FormContent
          form={form}
          isCoverLetterForm={!!isCoverLetterForm}
          isEnrollment={!!form?.isEnrollmentForm}
          status={form?.enrollmentPrintFormStatus}
        />
        <Flex>
          <TimelineDeletionRemain entry={entry} />
          <Flex className="timeline-right-side-infos">{doctorIcon}</Flex>
          {renderActions()}
        </Flex>
      </Flex>
      {renderHintPrintout(hasSupportForm1413)}
      {printUrl && (
        <PrintPreviewPdfDialog
          isShowCancelBtn={false}
          file={printUrl}
          onClose={onClosePrintPreview}
          formId={form?.prescribe.formName}
          titleText={`${getFormIdDisplay(form?.prescribe.formName || '')} - ${tForms(
            form?.prescribe.formName || ''
          )}`}
        />
      )}
      {musterFormDialogStore.currentEntryForm === entry.id &&
        formSetting?.type === FormTypeSetting.ENROLLMENT_FORM && (
          <MusterFormDialog
            className="enrollment-form"
            patient={patient}
            selectedContractDoctor={selectedContractDoctor}
            isOpen={!!musterFormDialogStore.currentFormName}
            onPrintPlainForm={onPrintPlainForm}
            onClose={() => {
              musterFormDialogActions.setProduct(undefined);
              musterFormDialogActions.setProductType(undefined);
            }}
            componentActions={{
              ...patientEnrollmentActions,
              prescribe: async (
                _: number,
                __: boolean,
                ___: string,
                currentFormSetting: string
              ) => {
                const isRemoveBackground =
                  PrinterService.checkNeedRemoveBackground(
                    musterFormDialogStore.currentFormName as FormName
                  );

                if (!isRemoveBackground) {
                  patientEnrollmentActions.prescribe();
                  return;
                }

                await onPrintPlainForm(
                  currentFormSetting,
                  musterFormDialogStore.currentFormName || ''
                );

                await handlePrintCustomPdf(undefined, currentFormSetting);
              },
            }}
          />
        )}

      {isOpenPrintPreview && !!form && (
        <PrintPreviewContent
          formValue={{
            ...JSON.parse(form.prescribe.payload),
            createdDate: form.prescribe.createdDate,
            prescribeDate: form.prescribe.prescribeDate,
          }}
          isOpen={isOpenPrintPreview}
          data={printPreviewData}
          isView
          handlePrintCustomPdf={handlePrintCustomPdf}
          onClosePrintPreview={onClosePrintPreviewPdf}
          setFieldValue={() => {}}
        />
      )}
    </Flex>
  );
};

const FormEntry = (props: IFormEntryProps) => {
  const form = props.entry.encounterForm;

  const currentFormSetting = useMemo(() => {
    return JSON.parse(form?.prescribe.payload || '{}');
  }, [form?.prescribe.payload]);

  const hasCoverLetterForm = useMemo(() => {
    return (
      form?.prescribe.formName === FormName.Muster_6 &&
      M6_HVZ_COVER_LETTER_FORM.includes(
        currentFormSetting['textbox_uberweisung'] as string
      )
    );
  }, [form?.prescribe.formName, currentFormSetting]);

  const renderCoverLetter = useMemo(() => {
    if (!hasCoverLetterForm) {
      return null;
    }

    return <TimelineEntry {...props} isCoverLetterForm />;
  }, [hasCoverLetterForm, props.entry]);

  return (
    <Flex column gap={8}>
      <TimelineEntry {...props} isCoverLetterForm={false} />
      {renderCoverLetter}
    </Flex>
  );
};

export default I18n.withTranslation(FormEntry, {
  namespace: 'Form',
  nestedTrans: 'Timeline',
});
