import { isNil } from 'lodash';
import moment from 'moment';
import React from 'react';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';

import { BodyTextL, BodyTextS, Flex } from '@tutum/design-system/components';
import { Divider } from '@tutum/design-system/components/Core';
import {
  CreateResponse,
  EditResponse,
  GroupByQuarter,
  RemoveRequest,
  RemoveResponse,
} from '@tutum/hermes/bff/app_mvz_timeline';
import { CodingRuleSuggestion } from '@tutum/hermes/bff/coding_rule_common';
import { MainGroup } from '@tutum/hermes/bff/common';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { DATE_FORMAT } from '@tutum/mvz/constant/dateTime';
import { IGlobalContext } from '@tutum/mvz/contexts/Global.context';
import { settingStore } from '@tutum/mvz/hooks/useSetting.store';
import { useFormOverviewStore } from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import type { FORM_SECTION } from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import { SourceScreen } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.hooks';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { IMvzTheme } from '@tutum/mvz/theme';
import { patientFileActions } from '../../../PatientFile.store';
import { useTimeLineStore } from '../../Timeline.store';
import TimelineEncounter from '../timeline-encounter/TimelineEncounter.styled';
import DiagnoseSuggestionModal from '../timeline-entry/diagnose-entry/DiagnoseSuggestionModal';
import { useTheme } from '@tutum/design-system/themes';

export interface ITimelineQuarterProps {
  className?: string;
  theme?: IMvzTheme;
  quarter: GroupByQuarter;
  diagnoseSuggestions: CodingRuleSuggestion[];
  sourceScreen: SourceScreen;
  showPreParticipateServiceCode?: boolean;
  keyword?: string;
  onCreateTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: CreateResponse) => void,
    handleError: (error) => void
  ) => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  onDeleteTimelineItem: (
    data: RemoveRequest,
    handleSuccess: (result: RemoveResponse) => void,
    handleError: (error) => void
  ) => void;
  reloadQuarters: ReloadQuarterFunc;
  fetchSuggestionList: () => void;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
  openEditSchein: (scheinId?: string, scrollToSection?: FORM_SECTION) => void;
}

const renderQuarterHeader = (quarter: GroupByQuarter) => {
  const text = `Q${quarter.quarter} ${quarter.year}`;
  return (
    <Flex className="quarter-header">
      <Flex auto column className="left-divider">
        <Divider />
      </Flex>
      <Flex auto column className="quarter_infor">
        <BodyTextS fontWeight="SemiBold">{text}</BodyTextS>
      </Flex>
      <Flex auto column className="right-divider">
        <Divider />
      </Flex>
    </Flex>
  );
};

const TimelineQuarter = React.memo(
  ({
    className,
    quarter,
    diagnoseSuggestions,
    sourceScreen,
    t,
    showPreParticipateServiceCode,
    keyword,
    onCreateTimelineItem,
    onEditTimelineItem,
    onDeleteTimelineItem,
    reloadQuarters,
    fetchSuggestionList,
    openCreateSchein,
    openEditSchein,
  }: ITimelineQuarterProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.Timeline> &
    IGlobalContext) => {
    const { timelineModel, isHistoryMode } = useTimeLineStore();
    const { listForms } = useFormOverviewStore();
    const { foreground } = useTheme();

    const renderTimeLineData = () => {
      if (!quarter.timelineModels[0]) {
        if (showPreParticipateServiceCode) {
          return (
            <TimelineEncounter
              keyword={keyword}
              createdDateString=""
              timelineItem={undefined}
              showPreParticipateServiceCode={showPreParticipateServiceCode}
              fontSetting={settingStore.timelineSetting.scaleNumber}
              quarter={quarter}
              diagnoseSuggestions={diagnoseSuggestions}
              sourceScreen={sourceScreen}
              isHistoryMode={isHistoryMode}
              listForms={listForms}
              onCreateTimelineItem={onCreateTimelineItem}
              onEditTimelineItem={onEditTimelineItem}
              onDeleteTimelineItem={onDeleteTimelineItem}
              contract={
                patientFileActions.getAvailableContractById(
                  'AWH_01'
                ) as IContractInfo
              }
              reloadQuarters={reloadQuarters}
              fetchSuggestionList={fetchSuggestionList}
              openCreateSchein={openCreateSchein}
              openEditSchein={openEditSchein}
            />
          );
        }
        return (
          <Flex className="empty-encounter">
            <BodyTextL color={foreground['03']}>
              {t('timelineContent_noResultFound')}
            </BodyTextL>
          </Flex>
        );
      }

      return quarter.timelineModels.map((encounterDetail, index) => (
        <TimelineEncounter
          keyword={keyword}
          key={encounterDetail.id}
          reloadQuarters={reloadQuarters}
          createdDateString={
            !index ||
              moment(new Date(encounterDetail.selectedDate)).format(
                DATE_FORMAT
              ) !==
              moment(
                new Date(quarter.timelineModels[index - 1].selectedDate)
              ).format(DATE_FORMAT)
              ? moment(new Date(encounterDetail.selectedDate)).format(
                DATE_FORMAT
              )
              : ''
          }
          timelineItem={encounterDetail}
          diagnoseSuggestions={diagnoseSuggestions}
          isHistoryMode={isHistoryMode}
          listForms={listForms}
          sourceScreen={sourceScreen}
          showPreParticipateServiceCode={
            showPreParticipateServiceCode && !index
          }
          fontSetting={settingStore.timelineSetting.scaleNumber}
          quarter={quarter}
          onCreateTimelineItem={onCreateTimelineItem}
          onEditTimelineItem={onEditTimelineItem}
          onDeleteTimelineItem={onDeleteTimelineItem}
          contract={patientFileActions.getAvailableContractById(
            encounterDetail.contractId
          )!}
          fetchSuggestionList={fetchSuggestionList}
          openCreateSchein={openCreateSchein}
          openEditSchein={openEditSchein}
        />
      ));
    };

    const showDiagnoseSuggestionModal =
      !isNil(timelineModel) &&
      timelineModel?.year === quarter.year &&
      timelineModel?.quarter === quarter.quarter;

    return (
      <Flex className={className}>
        {renderQuarterHeader(quarter)}
        {/* {quarter.errors &&
          quarter.errors.length > 0 &&
          renderErrorMessage(quarter)} TODO mapping later */}
        <Flex className="quarter-content">{renderTimeLineData()}</Flex>
        {showDiagnoseSuggestionModal && (
          <DiagnoseSuggestionModal
            fetchSuggestionList={fetchSuggestionList}
            onCreateTimelineItem={onCreateTimelineItem}
            onDeleteTimelineItem={onDeleteTimelineItem}
            onEditTimelineItem={onEditTimelineItem}
            reloadQuarters={reloadQuarters}
            timelineModel={timelineModel}
          />
        )}
      </Flex>
    );
  }
);

export default I18n.withTranslation(TimelineQuarter, {
  namespace: 'PatientManagement',
  nestedTrans: 'Timeline',
});
