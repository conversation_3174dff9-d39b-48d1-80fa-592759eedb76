import React from 'react';
import {
  Divider,
  InputGroup,
  Button,
  Intent,
  MenuItem,
  Alignment,
} from '@tutum/design-system/components/Core';
import { Select } from '@tutum/design-system/components/Select';
import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import {
  Flex,
  BodyTextS,
  BodyTextM,
  MessageBar,
} from '@tutum/design-system/components';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { scaleSpacePx } from '@tutum/design-system/styles';
import Theme, { IMvzTheme } from '@tutum/mvz/theme';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IDoctorInfoProps {
  className?: string;
  theme?: IMvzTheme;
  onSave?: (treatmentType: string, lanr: string, bsnr: string) => void;
  onCancel?: () => void;
  treatmentTypeKey?: string;
  lanr?: string;
  bsnr?: string;
}

export interface IDoctorInfoState {
  isEditing: boolean;
  lanr?: string;
  bsnr?: string;
  treatmentType?: ITreatmentType;
}

export interface ITreatmentType {
  key: string;
  description: string;
}

const TreatmentTypeSelect = Select;
const TREATMENT_TYPES: ITreatmentType[] = [
  //TODO: feed data later
  { key: '00', description: 'Ambulante Behandlung' },
  { key: '20', description: 'Selbstausstellung' },
  { key: '21', description: 'Auftragsleistungen' },
  { key: '23', description: 'Konsiliaruntersuchung' },
  { key: '24', description: 'Mit-/Weiterbehandlung' },
  {
    key: '26',
    description:
      'Stationäre Mitbehandlung,Vergütung nach ambulanten Grundsätzen',
  },
  {
    key: '27',
    description:
      'Überweisungs-/Abrechnungsschein für Laboratoriumsuntersuchungen als Auftragsleistung',
  },
  {
    key: '28',
    description:
      'Anforderungsschein für Laboratoriumsuntersuchungen bei Laborgemeinschaften',
  },
  { key: '30', description: 'Belegärztliche Behandlung' },
  { key: '31', description: 'Belegärztliche Mitbehandlung' },
  {
    key: '32',
    description:
      'Urlaubs- bzw. Krankheitsvertretung bei belegärztlicher Behandlung',
  },
  { key: '41', description: 'Ärztlicher Notfalldienst' },
];
class DoctorInfo extends React.PureComponent<
  IDoctorInfoProps &
  II18nFixedNamespace<keyof typeof PatientManagementI18n.Timeline>,
  IDoctorInfoState
> {
  lanrInputRef: HTMLInputElement | null = null;
  bsnrInputRef: HTMLInputElement | null = null;
  bindLanrInputRef = (ref: HTMLInputElement) => {
    this.lanrInputRef = ref;
  };
  bindBsnrInputRef = (ref: HTMLInputElement) => {
    this.bsnrInputRef = ref;
  };

  constructor(
    props: IDoctorInfoProps &
      II18nFixedNamespace<keyof typeof PatientManagementI18n.Timeline>
  ) {
    super(props);
    this.state = {
      isEditing: false,
      treatmentType:
        TREATMENT_TYPES.find(
          (treatmentType) => treatmentType.key === props.treatmentTypeKey
        ) || TREATMENT_TYPES[0],
      lanr: props.lanr,
      bsnr: props.bsnr,
    };
  }

  render() {
    const { className } = this.props;
    const { isEditing } = this.state;
    return (
      <Flex className={className}>
        {isEditing ? this.renderDoctorInfoInput() : this.renderDoctorInfo()}
      </Flex>
    );
  }

  renderDoctorInfoInput = () => {
    const {
      t,
    } = this.props;
    return (
      <Flex className="doctor-info-input">
        <Divider />
        <Flex column>
          <BodyTextS
            fontWeight="SemiBold"
            margin={`${scaleSpacePx(1)} 0`}
            color={COLOR.TEXT_SECONDARY_NAVAL}
          >
            {t('timelineContent_referrerInfo_label')}
          </BodyTextS>
          <Flex>
            {this.renderTreatmentTypeSelection()}
            {this.renderLanrInput()}
            {this.renderBsnrInput()}
            {this.renderButtonGroup()}
          </Flex>
        </Flex>
      </Flex>
    );
  };

  renderTreatmentTypeSelection = () => {
    const { treatmentType } = this.state;

    return (
      <Flex className="treatment-type-selection">
        <BodyTextS margin={`0 0 ${scaleSpacePx(1)} 0`} color={COLOR.TEXT_SECONDARY_NAVAL}>
          Treatment Type
        </BodyTextS>
        <TreatmentTypeSelect<ITreatmentType>
          className={getCssClass('sl-select')}
          items={TREATMENT_TYPES}
          itemRenderer={this.treatmentTypeRenderer}
          onItemSelect={this.onTreatmentTypeSelect}
          popoverProps={{
            minimal: true,
            captureDismiss: true,
          }}
          filterable={false}
          inputProps={{ fill: true }}
        >
          <Button
            fill
            text={treatmentType?.description}
            rightIcon="caret-down"
            alignText={Alignment.LEFT}
          />
        </TreatmentTypeSelect>
      </Flex>
    );
  };

  treatmentTypeRenderer = (
    item: ITreatmentType,
    { handleClick, modifiers }
  ) => {
    if (!modifiers.matchesPredicate) {
      return null;
    }
    return (
      <MenuItem key={item.key} onClick={handleClick} text={item.description} />
    );
  };

  onTreatmentTypeSelect = (item: ITreatmentType) => {
    this.setState({ treatmentType: item });
  };

  renderLanrInput = () => {
    const {
      lanr,
    } = this.props;
    return (
      <Flex className="lanr-input">
        <BodyTextS margin={`0 0 ${scaleSpacePx(1)} 0`} color={COLOR.TEXT_SECONDARY_NAVAL}>
          Lanr
        </BodyTextS>
        <InputGroup inputRef={this.bindLanrInputRef} defaultValue={lanr} />
      </Flex>
    );
  };

  renderBsnrInput = () => {
    const {
      bsnr,
    } = this.props;
    return (
      <Flex className="bsnr-input">
        <BodyTextS margin={`0 0 ${scaleSpacePx(1)} 0`} color={COLOR.TEXT_SECONDARY_NAVAL}>
          Bsnr
        </BodyTextS>
        <InputGroup inputRef={this.bindBsnrInputRef} defaultValue={bsnr} />
      </Flex>
    );
  };

  renderButtonGroup = () => {
    const { onSave } = this.props;
    const cancel = () => {
      this.setState({
        isEditing: false,
      });
    };
    const save = () => {
      this.setState({
        isEditing: false,
      });
      onSave?.(
        this.state.treatmentType?.key!,
        this.lanrInputRef?.value!,
        this.bsnrInputRef?.value!
      );
    };
    return (
      <Flex className="button-group">
        <Button className="save" intent={Intent.PRIMARY} onClick={() => save()}>
          Save
        </Button>
        <Button className="cancel" onClick={() => cancel()}>
          Cancel
        </Button>
      </Flex>
    );
  };

  renderDoctorInfo = () => {
    const { lanr, bsnr } = this.props;
    return isEmpty(lanr) || isEmpty(bsnr)
      ? this.renderErrorMessageBar()
      : this.renderDoctorInfoDetails();
  };

  renderDoctorInfoDetails = () => {
    const { treatmentTypeKey, lanr, bsnr } = this.props;
    const openDoctorInfoInput = () => {
      this.setState({
        isEditing: true,
      });
    };
    const treatment = TREATMENT_TYPES.find(
      (item) => item.key === treatmentTypeKey
    );
    return (
      <Flex className="doctor-info" auto>
        <Flex auto onClick={openDoctorInfoInput} align="center">
          <BodyTextS>Referral</BodyTextS>
          <BodyTextM fontWeight="SemiBold">
            {treatment && treatment.description}
          </BodyTextM>
          <BodyTextM>Lanr: {lanr}</BodyTextM>
          <BodyTextM>Bsnr: {bsnr}</BodyTextM>
        </Flex>
        <Divider />
      </Flex>
    );
  };

  renderErrorMessageBar = () => {
    const openDoctorInfoInput = () => {
      this.setState({
        isEditing: true,
      });
    };
    return (
      <MessageBar
        type="error"
        content={this.props.t('AKA_ABRD1544_errorMessage')}
        actionButtonGroup={
          <Flex>
            <Button intent={Intent.PRIMARY} onClick={openDoctorInfoInput}>
              Add
            </Button>
          </Flex>
        }
      />
    );
  };
}

export default I18n.withTranslation(DoctorInfo, {
  namespace: 'PatientManagement',
  nestedTrans: 'Timeline',
});
