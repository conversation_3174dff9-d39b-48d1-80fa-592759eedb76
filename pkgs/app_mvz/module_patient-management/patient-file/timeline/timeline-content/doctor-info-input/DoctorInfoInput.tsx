
import React from 'react';
import {
  Divider,
  InputGroup,
  Button,
  Intent,
  MenuItem,
  Alignment,
} from '@tutum/design-system/components/Core';
import { Select } from '@tutum/design-system/components/Select';
import { Flex, BodyTextS } from '@tutum/design-system/components';
import { scaleSpacePx } from '@tutum/design-system/styles';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import Theme, { IMvzTheme } from '@tutum/mvz/theme';

export interface IDoctorInfoInputProps {
  className?: string;
  theme?: IMvzTheme;
  onSave?: (doctorLanr: string, doctorBsnr: string) => void;
  onCancel?: () => void;
  doctorBsnr?: string;
  doctorLanr?: string;
}

export interface IDoctorInfoInputState {
  isLoading: boolean;
  treatmentType?: ITreatmentType;
}

export interface ITreatmentType {
  key: string;
  description: string;
}

const TreatmentTypeSelect = Select;
const TREATMENT_TYPES: ITreatmentType[] = [
  //TODO: feed data later
  { key: '00', description: 'Ambulante Behandlung' },
  { key: '20', description: 'Selbstausstellung' },
  { key: '21', description: 'Auftragsleistungen' },
  { key: '23', description: 'Konsiliaruntersuchung' },
  { key: '24', description: 'Mit-/Weiterbehandlung' },
  {
    key: '26',
    description:
      'Stationäre Mitbehandlung,Vergütung nach ambulanten Grundsätzen',
  },
  {
    key: '27',
    description:
      'Überweisungs-/Abrechnungsschein für Laboratoriumsuntersuchungen als Auftragsleistung',
  },
  {
    key: '28',
    description:
      'Anforderungsschein für Laboratoriumsuntersuchungen bei Laborgemeinschaften',
  },
  { key: '30', description: 'Belegärztliche Behandlung' },
  { key: '31', description: 'Belegärztliche Mitbehandlung' },
  {
    key: '32',
    description:
      'Urlaubs- bzw. Krankheitsvertretung bei belegärztlicher Behandlung',
  },
  { key: '41', description: 'Ärztlicher Notfalldienst' },
];

class DoctorInfoInput extends React.PureComponent<
  IDoctorInfoInputProps,
  IDoctorInfoInputState
> {
  lanrInputRef: HTMLInputElement = null;
  bsnrInputRef: HTMLInputElement = null;
  bindLanrInputRef = (ref: HTMLInputElement) => {
    this.lanrInputRef = ref;
  };
  bindBsnrInputRef = (ref: HTMLInputElement) => {
    this.bsnrInputRef = ref;
  };

  constructor(props: IDoctorInfoInputProps) {
    super(props);
    this.state = {
      isLoading: false,
      treatmentType: TREATMENT_TYPES[0],
    };
  }

  render() {
    const {
      className,
      theme: { foreground },
    } = this.props;
    return (
      <Flex className={className}>
        <Divider />
        <Flex column>
          <BodyTextS
            fontWeight="SemiBold"
            margin={`${scaleSpacePx(1)} 0`}
            color={foreground['02']}
          >
            Referrer Information
          </BodyTextS>
          <Flex className="doctor-info-input">
            {this.renderTreatmentTypeSelection()}
            {this.renderLanrInput()}
            {this.renderBsnrInput()}
            {this.renderButtonGroup()}
          </Flex>
        </Flex>
      </Flex>
    );
  }

  renderTreatmentTypeSelection = () => {
    const {
      theme: { foreground },
    } = this.props;
    const { treatmentType } = this.state;

    return (
      <Flex className="treatment-type-selection">
        <BodyTextS margin={`0 0 ${scaleSpacePx(1)} 0`} color={foreground['02']}>
          Treatment type
        </BodyTextS>
        <TreatmentTypeSelect<ITreatmentType>
          className={getCssClass('sl-select')}
          items={TREATMENT_TYPES}
          itemRenderer={this.treatmentTypeRenderer}
          onItemSelect={this.onTreatmentTypeSelect}
          popoverProps={{
            minimal: true,
            captureDismiss: true,
          }}
          filterable={false}
          inputProps={{ fill: true }}
        >
          <Button
            fill
            text={treatmentType.description}
            rightIcon="caret-down"
            alignText={Alignment.LEFT}
          />
        </TreatmentTypeSelect>
      </Flex>
    );
  };

  treatmentTypeRenderer = (
    item: ITreatmentType,
    { handleClick, modifiers }
  ) => {
    if (!modifiers.matchesPredicate) {
      return null;
    }
    return (
      <MenuItem key={item.key} onClick={handleClick} text={item.description} />
    );
  };

  onTreatmentTypeSelect = (item: ITreatmentType) => {
    this.setState({ treatmentType: item });
  };

  renderLanrInput = () => {
    const {
      doctorLanr,
      theme: { foreground },
    } = this.props;
    return (
      <Flex className="lanr-input">
        <BodyTextS margin={`0 0 ${scaleSpacePx(1)} 0`} color={foreground['02']}>
          LANR
        </BodyTextS>
        <InputGroup
          inputRef={this.bindLanrInputRef}
          defaultValue={doctorLanr}
        />
      </Flex>
    );
  };

  renderBsnrInput = () => {
    const {
      doctorBsnr,
      theme: { foreground },
    } = this.props;
    return (
      <Flex className="bsnr-input">
        <BodyTextS margin={`0 0 ${scaleSpacePx(1)} 0`} color={foreground['02']}>
          BSNR
        </BodyTextS>
        <InputGroup
          inputRef={this.bindBsnrInputRef}
          defaultValue={doctorBsnr}
        />
      </Flex>
    );
  };

  renderButtonGroup = () => {
    const { onSave, onCancel } = this.props;
    return (
      <Flex className="button-group">
        <Button
          className="save"
          intent={Intent.PRIMARY}
          onClick={() =>
            onSave(this.lanrInputRef.value, this.bsnrInputRef.value)
          }
        >
          Save
        </Button>
        <Button className="cancel" onClick={onCancel}>
          Cancel
        </Button>
      </Flex>
    );
  };
}

export default DoctorInfoInput;
