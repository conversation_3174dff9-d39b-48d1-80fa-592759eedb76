import { cloneDeep } from 'lodash';

import {
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import { edit, getById } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { IEntrySetting } from '@tutum/mvz/hooks/useSetting.store';
import { getDataTimeline } from './utils';
import {
  DiagnoseType,
  EncounterItemError,
  EncounterItemErrorType,
  EncounterNoteType,
} from '@tutum/hermes/bff/repo_encounter';
import { TimelineTheme } from '@tutum/mvz/theme';

export const onEditTimelineById = async (
  timelineId: string,
  updateFunc: (model: TimelineModel) => TimelineModel
) => {
  const { data: item } = await getById({ timelineId });

  if (!item?.timelineModel) {
    return;
  }

  const itemUpdated = updateFunc(item.timelineModel);
  const result = await edit({ timelineModel: itemUpdated });

  return result;
};

export const TIMELINE_SETTING = 'timeline-font-setting';

export const ENTRY_CAN_REMOVE = [
  EncounterNoteType.ANAMNESE,
  EncounterNoteType.FINDING,
  EncounterNoteType.THERAPY,
  DiagnoseType.DIAGNOSETYPE_ANAMNESTIC,
  TimelineEntityType.TimelineEntityType_Service,
  TimelineEntityType.TimelineEntityType_Note,
  TimelineEntityType.TimelineEntityType_Psychotherapy,
];

// entry type use same color
export const COLOR_ENTRY_MAPPER = {
  [TimelineEntityType.TimelineEntityType_Service_GOA]:
    TimelineEntityType.TimelineEntityType_Service,
  [TimelineEntityType.TimelineEntityType_Service_UV_GOA]:
    TimelineEntityType.TimelineEntityType_Service,
  [TimelineEntityType.TimelineEntityType_EHIC]:
    TimelineEntityType.TimelineEntityType_Form,
};

function parseEntryColor(
  entry: TimelineModel | undefined,
  timelineTheme: TimelineTheme | undefined
): IEntrySetting {
  if (!timelineTheme?.entryColor || !entry) {
    return {
      bg: '',
      text: '',
    };
  }
  const entryType = getEntryTypeForColor(entry);

  return timelineTheme.entryColor[entryType];
}

export const handleReduceErrors = (errs: EncounterItemError[]) => {
  const errors = cloneDeep(errs);
  return (
    errors?.reduce((acc: EncounterItemError[], curr) => {
      curr.type = curr.type.toLowerCase() as EncounterItemErrorType;
      return acc?.find(
        (item) => curr?.message && item?.message === curr?.message
      )
        ? acc
        : [...acc, curr];
    }, []) ?? []
  ).sort((a, b) => {
    if (a.type == EncounterItemErrorType.EncounterItemErrorType_error) {
      return -1;
    }
    if (a.type == EncounterItemErrorType.EncounterItemErrorType_warning) {
      if (b.type == EncounterItemErrorType.EncounterItemErrorType_error) {
        return 1;
      }
      return -1;
    }
    if (a.type == EncounterItemErrorType.EncounterItemErrorType_info) {
      return 1;
    }
    return 0;
  });
};

const getEntryTypeForColor = (entry: TimelineModel): string => {
  let entryType = getDataTimeline(entry)?.type;
  if (!entryType) {
    entryType = entry.type;
  }

  if (entry?.type === TimelineEntityType.TimelineEntityType_Diagnose) {
    return entryType === DiagnoseType.DIAGNOSETYPE_ACUTE
      ? TimelineEntityType.TimelineEntityType_Diagnose
      : TimelineEntityType.TimelineEntityType_Diagnose +
      '_' +
      entry.encounterDiagnoseTimeline?.command;
  }

  if (!!COLOR_ENTRY_MAPPER[entryType]) {
    entryType = COLOR_ENTRY_MAPPER[entryType];
  }

  if (entryType === TimelineEntityType.TimelineEntityType_DoctorLetter) {
    if (!entry.doctorLetter) {
      return entryType;
    }
    // Private Billing timeline have entity type DOCTOR_LETTER and entry.doctorLetter.privateInvoice
    if (entry.doctorLetter.privateInvoice) {
      return TimelineEntityType.TimelineEntityType_Private_Billing;
    }
    // BG Invoice have entity type DOCTOR_LETTER and entry.doctorLetter.bgInvoice
    if (entry.doctorLetter.bgInvoice) {
      return TimelineEntityType.TimelineEntityType_BG_Invoice;
    }
  }

  if (
    [
      TimelineEntityType.TimelineEntityType_Service,
      TimelineEntityType.TimelineEntityType_Service_GOA,
      TimelineEntityType.TimelineEntityType_Service_UV_GOA,
      TimelineEntityType.TimelineEntityType_Service_Chain,
      TimelineEntityType.TimelineEntityType_Service_GOA_Chain,
      TimelineEntityType.TimelineEntityType_Service_UV_GOA_Chain,
    ].includes(entryType)
  ) {
    return TimelineEntityType.TimelineEntityType_Service_Chain;
  }

  if (
    entryType === TimelineEntityType.TimelineEntityType_Customize &&
    !!entry.encounterCustomize
  ) {
    return entry.encounterCustomize.command;
  }

  return entryType;
};

export default {
  parseEntryColor,
};
