import { useEffect, useMemo, useState } from 'react';
import { isEmpty } from 'lodash';

import {
  CreateResponse,
  EditResponse,
  GroupByQuarterRequest,
  GroupByQuarterResponse,
  RemoveRequest,
  RemoveResponse,
  groupByQuarter,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';

import { useListenTimelineValidation } from '@tutum/hermes/bff/app_mvz_timeline';

import type { Toaster } from '@blueprintjs/core';
import { CodingRuleSuggestion } from '@tutum/hermes/bff/coding_rule_common';
import { validateCodingRuleByPatientId } from '@tutum/hermes/bff/legacy/app_mvz_coding_rule';
import { CheckTime } from '@tutum/hermes/bff/legacy/masterdata_common';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { default as datetimeUtil } from '@tutum/infrastructure/utils/datetime.util';
import stringUtil from '@tutum/infrastructure/utils/string.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { usePatientFileStore } from '../PatientFile.store';
import {
  createTimelineItem,
  deleteTimelineItem,
  editTimelineItem,
} from '../encounter-v2/composer/Composer.service';
import { timelineActions, useTimeLineStore } from './Timeline.store';
import { debounceSetModifiedId } from './Timeline.const';
import { alertSuccessfully } from '@tutum/design-system/components';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import { searchEbmsComposer } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { useActionBarStore } from '../encounter-v2/action-bar/ActionBar.store';

export interface ITimelineState {
  isLoading: boolean;
  filterKeyword?: string;
  filterTypes?: string[];
  filterStartDate?: Date;
  filterEndDate?: Date;
  latestTimeline?: string;
  version?: number;
}

export interface TimelineQuarterError {
  year: number;
  quarter: number;
  contractsError: ContractError[];
}

interface ContractError {
  contractId: string;
  doctorErrors: DoctorError[];
}

interface DoctorError {
  billingDoctorId: string;
  errors: string[];
  hints: string[];
}

export enum SourceScreen {
  CODING_RULE_OVERVIEW = 'CODING_RULE_OVERVIEW',
  KV_BILLING = 'KV_BILLING',
  PATIENT_MANAGEMENT = 'PATIENT_MANAGEMENT',
}

type RequestGroupByQuarterWithoutSchein = Omit<
  GroupByQuarterRequest,
  'scheinId'
>;

export function useTimeline(
  request: GroupByQuarterRequest,
  t: IFixedNamespaceTFunction,
  isCodingRules = false,
  sourceScreen: SourceScreen,
  bsnr?: string,
  billingQuarter?: Array<{ year: number; quarter: number }>
): {
  highlightKeyword: string;
  quartersErrors: TimelineQuarterError[];
  diagnoseSuggestions: CodingRuleSuggestion[];
  totalQuarters: number;
  reloadQuarters: ReloadQuarterFunc;
  fetchSuggestionList: () => void;
  onCreateTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: CreateResponse) => void,
    handleError: (error) => void
  ) => void;
  onEditTimelineItem: (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => void;
  onDeleteTimelineItem: (
    data: RemoveRequest,
    handleSuccess: (result: RemoveResponse) => void,
    handleError: (error) => void
  ) => void;
  fetchMoreTimeline: () => void;
} {
  usePatientFileStore();
  const toaster = useToaster();

  const [diagnoseSuggestions, setDiagnoseSuggestions] = useState<
    CodingRuleSuggestion[]
  >([]);
  const [totalQuarters, setTotalQuarters] = useState(0);
  const [quartersErrors] = useState<TimelineQuarterError[]>([]);
  const timelineStore = useTimeLineStore();

  // use to highlight keyword in timeline to prevent flickering effect when search (hightlight before have search result)
  const [highlightKeyword, setHighlightKeyword] = useState<string>('');
  const {encounterDate} = useActionBarStore()
  const requestTimeline: RequestGroupByQuarterWithoutSchein = useMemo(() => {
    return {
      isHistoryMode: request.isHistoryMode,
      patientId: request.patientId,
      keyword: request.keyword,
      isSortByCategory: request.isSortByCategory,
      fromDate: request.fromDate,
      quarter: request.quarter,
      timelineEntityTypes: request.timelineEntityTypes,
      toDate: request.toDate,
      year: request.year,
    };
  }, [JSON.stringify(request)]);

  const onCreateTimelineItem = async (
    data: TimelineModel,
    handleSuccess: (result: CreateResponse) => void,
    handleError: (error) => void
  ) => {
    try {
      const result = await createTimelineItem(data);

      if (!result) {
        throw new Error();
      }

      handleSuccess?.(result);
    } catch (error) {
      console.error(error);
      handleError?.(error);
    }
  };

  const onEditTimelineItem = async (
    data: TimelineModel,
    handleSuccess: (result: EditResponse) => void,
    handleError: (error) => void
  ) => {
    try {
      const result = await editTimelineItem(data);

      if (!result) {
        throw new Error();
      }

      handleSuccess?.(result);
    } catch (error) {
      console.error(error);
      handleError?.(error);
    }
  };

  const onDeleteTimelineItem = async (
    data: RemoveRequest,
    handleSuccess: (result: RemoveResponse) => void,
    handleError: (error) => void
  ) => {
    try {
      const result = await deleteTimelineItem(data);
      handleSuccess?.(result);
    } catch (error) {
      console.error(error);
      handleError?.(error);
    }
  };

  const fetchDataTimeline = async (
    request: GroupByQuarterRequest
  ): Promise<GroupByQuarterResponse> => {
    setHighlightKeyword('');
    const r = { ...request };
    const { data } = await groupByQuarter(r);
    setHighlightKeyword(r.keyword!);
    timelineActions.setPendingFetchNewData(false);
    const groupByQuarters = data?.groupByQuarters || [];
    timelineActions.setHasTimelineEntry(groupByQuarters.length > 0);
    timelineActions.setTimelineState(groupByQuarters);
    timelineActions.setMatchedTokens(data?.matchedTokens || []);
    return data;
  };

  const reloadQuarters: ReloadQuarterFunc = (yearQuarter) => {
    fetchDataTimeline({
      ...requestTimeline,
      ...yearQuarter,
    });
  };

  const fetchSuggestionList = async () => {
    if (sourceScreen === SourceScreen.PATIENT_MANAGEMENT) {
      return;
    }

    const now = datetimeUtil.date();
    const quarter = datetimeUtil.getQuarter(now);
    const year = +datetimeUtil.getYear(now);
    if (sourceScreen === SourceScreen.KV_BILLING) {
      if (isEmpty(billingQuarter)) {
        return;
      }

      const suggestions: CodingRuleSuggestion[] = [];
      for (const yearQuarter of billingQuarter!) {
        const resp = await validateCodingRuleByPatientId({
          patientId: request.patientId,
          year: yearQuarter.year,
          quarter: yearQuarter.quarter,
          checkTime: CheckTime.CheckTime_Billing,
        });
        if (isEmpty(resp.data.suggestions)) {
          continue;
        }

        suggestions.push(...resp.data.suggestions);
      }

      setDiagnoseSuggestions(suggestions);
      return;
    }
    if (sourceScreen === SourceScreen.CODING_RULE_OVERVIEW) {
      const resp = await validateCodingRuleByPatientId({
        patientId: request.patientId,
        year,
        quarter,
        checkTime: CheckTime.CheckTime_Selectable,
      });
      setDiagnoseSuggestions(resp.data.suggestions);
      return;
    }
  };

  useListenTimelineValidation(async (data) => {
    if (data.patientId !== request.patientId) {
      return;
    }
    await doReplaceService(data.timelineModels, toaster as Toaster, t);
  });

  async function doReplaceService(
    timelines: TimelineModel[],
    toaster: Toaster,
    t: IFixedNamespaceTFunction
  ) {
    if (!timelines?.length) {
      return;
    }
    const tlHasReplaceRule = timelines?.find((t) =>
      t.encounterServiceTimeline?.errors?.find(
        (e) => e?.metaData && e.metaData['replace']
      )
    );
    const replaceError =
      tlHasReplaceRule &&
      tlHasReplaceRule?.encounterServiceTimeline?.errors?.find(
        (err) => err?.metaData && err?.metaData['replace']
      );

    if (replaceError) {
      const replacedCode = replaceError.metaData['code'];
      const serviceCodesRes = await searchEbmsComposer({
        query: replacedCode,
        organizationId: await webWorkerServices.convertUkvToOkv(
          stringUtil.getKVRegion(bsnr)
        ),
        selectedDate: encounterDate!
      })
      const serviceCodes = serviceCodesRes?.data?.items ?? [];
      if (serviceCodes.length && tlHasReplaceRule?.encounterServiceTimeline) {
        const foundService = serviceCodes.find((s) => s.code === replacedCode);
        tlHasReplaceRule.encounterServiceTimeline.errors = [];
        tlHasReplaceRule.encounterServiceTimeline.code = foundService?.code!;
        tlHasReplaceRule.encounterServiceTimeline.description =
          foundService?.description!;
        tlHasReplaceRule.encounterServiceTimeline.freeText = `(${foundService?.code}) ${foundService?.description}`;
        await editTimelineItem(tlHasReplaceRule);
        if (timelineStore?.modifiedId) {
          debounceSetModifiedId(timelineStore.modifiedId);
        }
        t &&
          alertSuccessfully(
            t?.('ReplacedWithServiceCodeWhenBilling', {
              code: foundService?.code,
            }),
            { toaster }
          );
      }
    }
  }

  const fetchMoreTimeline = () => {
    timelineActions.setPagination({
      ...timelineStore.pagination,
      page: timelineStore.pagination.page + 1,
    });
  };

  useEffect(() => {
    timelineActions.setLoadingTimeine(true);
    fetchDataTimeline({
      ...requestTimeline,
      pagination: timelineStore.pagination,
    }).then((res) => {
      setTotalQuarters(res.totalPage);
      timelineActions.setLoadingTimeine(false);
    });
  }, [
    JSON.stringify(requestTimeline),
    JSON.stringify(timelineStore.pagination),
  ]);

  useEffect(() => {
    fetchSuggestionList();
  }, [JSON.stringify(request), isCodingRules]);

  return {
    highlightKeyword,
    quartersErrors,
    diagnoseSuggestions,
    totalQuarters,
    reloadQuarters,
    fetchSuggestionList,
    onCreateTimelineItem,
    onEditTimelineItem,
    onDeleteTimelineItem,
    fetchMoreTimeline,
  };
}

export default { useTimeline };
