import React, { memo, useEffect, useState } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import Theme, { IMvzThemeProps } from '@tutum/mvz/theme';
import {
  Dialog,
  Classes,
  Button,
  Intent,
  TextArea,
} from '@tutum/design-system/components/Core';
import { scaleSpacePx } from '@tutum/design-system/styles';
import { Flex } from '@tutum/design-system/components';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import { DateInput } from '@tutum/design-system/components/DateTime';
import MomentLocaleUtils from 'react-day-picker/moment';
import {
  DATE_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export enum DiagnoseFurtherInfor {
  EndDate,
  Exception,
  Explanation,
}

export interface UpdatingData {
  encounterDate: Date;
  diagnoseText: string;
  exception: string;
  explanation: string;
}

export interface ITerminatePermanentDiagnoseDialogProps {
  updatingData: UpdatingData;
  isOpen: boolean;
  isLoading: boolean;
  type?: DiagnoseFurtherInfor;
  onSave: (diagnoseEndDate: UpdatingData) => void;
  onClose: () => void;
}

const StyledDialog = Theme.styled(Dialog)`
  ${() => {
    return `
      & {
        .set-enddate-message {
          padding-bottom:  ${scaleSpacePx(4)};
        }

        .diagnose-date {
          padding-right:  ${scaleSpacePx(6)};
          min-width: ${scaleSpacePx(25)};
        }

        .calendar-title {
          padding-top:  ${scaleSpacePx(6)};
          padding-bottom:  ${scaleSpacePx(4)};
        }
      }
    `;
  }}
`;

function TerminatePermanentDiagnoseDialog(
  props: ITerminatePermanentDiagnoseDialogProps &
    IMvzThemeProps &
    II18nFixedNamespace<
      keyof typeof PatientManagementI18n.TerminatePermanentDiagnoseDialog
    >
) {
  const {
    updatingData,
    t,
    isOpen,
    isLoading,
    onClose,
    onSave,
    lang,
    type = DiagnoseFurtherInfor.EndDate,
  } = props;
  const [diagnoseUpdatingData, setDiagnoseUpdatingData] =
    useState(updatingData);

  const closeDiagnoseDialog = () => {
    onClose && onClose();
  };

  const saveDiagnoseEndDate = () => {
    onSave && onSave(diagnoseUpdatingData);
  };

  const setEndDate = (endDate: string) => {
    setDiagnoseUpdatingData({
      ...diagnoseUpdatingData,
      encounterDate: endDate ? new Date(+endDate) : null!,
    });
  };
  const setFurtherInfo = (content: string) => {
    if (type == DiagnoseFurtherInfor.Exception) {
      setDiagnoseUpdatingData({
        ...diagnoseUpdatingData,
        exception: content,
      });
    } else {
      setDiagnoseUpdatingData({
        ...diagnoseUpdatingData,
        explanation: content,
      });
    }
  };
  let title = t(
    'timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_title'
  );
  if (type === DiagnoseFurtherInfor.Exception) {
    title = t('timelineContent_diagnoseEntry_documentException_title');
  } else if (type === DiagnoseFurtherInfor.Explanation) {
    title = t('timelineContent_diagnoseEntry_documentExplanation_title');
  }

  useEffect(() => setDiagnoseUpdatingData(updatingData), [updatingData]);

  return (
    <StyledDialog
      title={title}
      isOpen={isOpen}
      onClose={closeDiagnoseDialog}
      canOutsideClickClose={false}
    >
      <Flex className={Classes.DIALOG_BODY}>
        {type == DiagnoseFurtherInfor.EndDate && (
          <Flex auto column>
            <Flex className="set-enddate-message">
              {t(
                'timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_message'
              )}
            </Flex>
            <Flex>
              <Flex className="diagnose-date">
                {toDateFormat(updatingData.encounterDate, {
                  dateFormat: 'dd.MM.yyyy',
                })}
              </Flex>
              <Flex className="diagnose-description">
                {updatingData.diagnoseText}
              </Flex>
            </Flex>
            <Flex className="calendar-title">
              {t(
                'timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_calendarTitle'
              )}
            </Flex>
            <DateInput
              inputProps={{
                leftIcon: 'calendar',
              }}
              popoverProps={{ usePortal: false }}
              value={
                diagnoseUpdatingData.encounterDate
                  ? datetimeUtil.dateTimeFormat(
                    diagnoseUpdatingData.encounterDate,
                    YEAR_MONTH_DAY_FORMAT
                  )
                  : null
              }
              placeholder={DATE_FORMAT}
              formatDate={(date) =>
                datetimeUtil.dateTimeFormat(date, DATE_FORMAT)
              }
              parseDate={(str) => datetimeUtil.strToDate(str, DATE_FORMAT)}
              locale={lang}
              localeUtils={MomentLocaleUtils}
              onChange={setEndDate}
            />
          </Flex>
        )}
        {(type == DiagnoseFurtherInfor.Exception ||
          type == DiagnoseFurtherInfor.Explanation) && (
            <Flex auto column>
              <Flex className="set-enddate-message">
                {t('timelineContent_diagnoseEntry_addFurtherInfoDialog_message')}
              </Flex>
              <TextArea
                className={`${Classes.FILL} diagnoseFurtherInfo__input`}
                growVertically={false}
                rows={4}
                maxLength={1500}
                onChange={(e) => {
                  const value = e.target.value;
                  setFurtherInfo(value);
                }}
                value={
                  type == DiagnoseFurtherInfor.Exception
                    ? diagnoseUpdatingData.exception
                    : diagnoseUpdatingData.explanation
                }
              />
            </Flex>
          )}
      </Flex>
      <Flex justify="space-between" className={Classes.DIALOG_FOOTER}>
        <Button
          fill
          loading={isLoading}
          onClick={saveDiagnoseEndDate}
          intent={Intent.PRIMARY}
          disabled={!diagnoseUpdatingData || isLoading}
        >
          {t(
            'timelineContent_diagnoseEntry_addPermanentDiagnoseEndDateDialog_ConfirmButton'
          )}
        </Button>
      </Flex>
    </StyledDialog>
  );
}

export default memo(
  I18n.withTranslation(TerminatePermanentDiagnoseDialog, {
    namespace: 'PatientManagement',
    nestedTrans: 'TerminatePermanentDiagnoseDialog',
  })
);
