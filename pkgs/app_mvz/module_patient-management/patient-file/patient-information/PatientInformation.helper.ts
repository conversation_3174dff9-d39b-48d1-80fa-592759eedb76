import cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';

import type PatientFileSidebarLocales from '@tutum/mvz/locales/en/PatientFileSidebar.json';

import {
  DateOfBirth,
  PatientInfo,
} from '@tutum/hermes/bff/patient_profile_common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import {
  getAgeInfo,
  toDateFormat,
} from '@tutum/design-system/infrastructure/utils';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export enum ROW_EDTTING {
  PERSONAL_INFO = 'PERSONAL_INFO',
  ADDRESS = 'ADDRESS',
  POST_OFFICE_BOX = 'POST_OFFICE_BOX',
  DOCTOR_INFO = 'DOCTOR_INFO',
  CONTACT_INFO = 'CONTACT_INFO',
  CONTACT_PERSON = 'CONTACT_PERSON',
}

export const renderTitle = (
  t: IFixedNamespaceTFunction<
    keyof typeof PatientFileSidebarLocales.PatientInformationSidebar
  >,
  rowEditing: Nullable<ROW_EDTTING>
) => {
  switch (rowEditing) {
    case ROW_EDTTING.PERSONAL_INFO:
      return t('personalInfo');
    case ROW_EDTTING.ADDRESS:
      return t('tooltipAddress');
    case ROW_EDTTING.POST_OFFICE_BOX:
      return t('tooltipPostOfficeBox');
    case ROW_EDTTING.DOCTOR_INFO:
      return t('tooltipDoctor');
    case ROW_EDTTING.CONTACT_INFO:
      return t('tooltipContactNumber');
    case ROW_EDTTING.CONTACT_PERSON:
      return t('tooltipContactPerson');
    default:
      return 'Title';
  }
};

export const valuesParser = (
  values: PatientInfo,
  rowEditing: Nullable<ROW_EDTTING>
) => {
  const cloneValues = cloneDeep(values);
  switch (rowEditing) {
    case ROW_EDTTING.PERSONAL_INFO:
      cloneValues.insuranceInfos = cloneValues.insuranceInfos.map(
        (insuranceInfo) => ({
          ...insuranceInfo,
          copaymentExemptionTillDate: insuranceInfo?.copaymentExemptionTillDate
            ? moment
              .utc(
                moment(insuranceInfo?.copaymentExemptionTillDate).format(
                  DATE_FORMAT
                ),
                DATE_FORMAT
              )
              .valueOf()
            : undefined,
        })
      );
      break;

    case ROW_EDTTING.CONTACT_INFO:
      cloneValues.contactInfo.furtherContactNumber =
        cloneValues?.contactInfo?.furtherContactNumber?.filter((item) =>
          Boolean(item)
        );
      break;

    case ROW_EDTTING.DOCTOR_INFO:
      cloneValues.doctorInfo.generalPractitionerDoctorId =
        (cloneValues.doctorInfo.generalPractitionerDoctorId || []).filter(
          (item) => !!item
        );
      cloneValues.doctorInfo.specialistDoctorId =
        (cloneValues.doctorInfo.specialistDoctorId || []).filter((item) => !!item);
      if (!cloneValues.doctorInfo.treatmentDoctorId) {
        delete cloneValues.doctorInfo.treatmentDoctorId;
      }
      break;
    default:
      break;
  }

  cloneValues.personalInfo.dateOfBirth = {
    date: Number(cloneValues?.personalInfo?.dateOfBirth?.date) || undefined,
    month: Number(cloneValues?.personalInfo?.dateOfBirth?.month) || undefined,
    year: Number(cloneValues.personalInfo.dateOfBirth.year) || undefined,
    isValidDOB: cloneValues.personalInfo.dateOfBirth.isValidDOB,
  };

  return cloneValues;
};

export const formatBirthday = (t, dateOfBirth: DateOfBirth) => {
  const { value } = getDateOfBirth(dateOfBirth);

  if (!dateOfBirth?.isValidDOB) {
    return `${value} (?)`;
  }

  const dob = new Date(+moment(value, DATE_FORMAT));
  const { years, months, days } = getAgeInfo(dob, datetimeUtil.date());
  const dobString = toDateFormat(dob, {
    dateFormat: 'dd.MM.yyyy',
  });
  if (years >= 2) {
    return `${dobString} (${years} ${t('age')})`;
  } else if (months >= 1) {
    return `${dobString} (${months} ${t('month')})`;
  } else {
    return `${dobString} (${days < 1 ? '< 1' : days} ${t('day')})`;
  }
};
