import React, { memo, useState } from 'react';
import { Fast<PERSON>ield, FieldArray } from 'formik';
import { Flex, Button, Svg } from '@tutum/design-system/components';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import { NumericInput, InputGroup } from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import PatientProfileLocales from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import { keyboardTriggerClick } from '@tutum/mvz/_utils/accessibility';
import { initContactPersonItem } from '@tutum/mvz//module_patient-management/create-patient-v2/CreatePatient.helper';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import { IFormProps } from '..';

const minusCircle = '/images/minus-circle.svg';

const ContactInfo = ({ values }: IFormProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientProfileLocales.ContactInformation
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'ContactInformation',
  });
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const [removeAction, setRemoveAction] = useState<(() => void) | undefined>(undefined);

  return (
    <Flex column style={{ flex: 1 }} pr={40}>
      <FieldArray
        name="contactInfo.contactPersons"
        render={(arrayHelpers) => (
          <>
            {values?.contactInfo?.contactPersons?.map((item, index) => (
              <Flex
                className="sl-box"
                key={`contactInfo_contactPersons_${index}`}
                column
                mb={16}
              >
                <Flex gap={16}>
                  <FormGroup2 label={t('name')}>
                    <FastField
                      name={`contactInfo.contactPersons.${index}.name`}
                    >
                      {({ field }) => {
                        return (
                          <InputGroup
                            {...field}
                            value={field.value || ''}
                            maxLength={60}
                            data-tab-id={field.name}
                            id={field.name}
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                  <FormGroup2 label={t('relationship')}>
                    <FastField
                      name={`contactInfo.contactPersons.${index}.relationship`}
                    >
                      {({ field }) => {
                        return (
                          <InputGroup
                            {...field}
                            value={field.value || ''}
                            maxLength={60}
                            data-tab-id={field.name}
                            id={field.name}
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                </Flex>
                <Flex gap={16}>
                  <FormGroup2 label={t('phoneNumber')}>
                    <FastField
                      name={`contactInfo.contactPersons.${index}.phoneNumber`}
                    >
                      {({ field, form }) => {
                        return (
                          <NumericInput
                            {...field}
                            name={field.name}
                            value={field.value || undefined}
                            buttonPosition="none"
                            fill
                            maxLength={60}
                            onValueChange={(
                              _: number,
                              valueAsString: string
                            ) => {
                              form.setFieldValue(field.name, valueAsString);
                            }}
                            data-tab-id={`contactPersons.${index}.phoneNumber`}
                            id={field.name}
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                  <FormGroup2 label={t('emailAddress')}>
                    <FastField
                      name={`contactInfo.contactPersons.${index}.email`}
                    >
                      {({ field }) => {
                        return (
                          <InputGroup
                            {...field}
                            value={field.value || ''}
                            type="email"
                            maxLength={60}
                            data-tab-id={field.name}
                            id={field.name}
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                </Flex>
                {index ? (
                  <Svg
                    className="sl-remove-person-contact"
                    onClick={() => {
                      const callback = () => {
                        arrayHelpers.remove(index);
                        setRemoveAction(undefined);
                      };
                      setRemoveAction(() => callback);
                    }}
                    onKeyDown={keyboardTriggerClick}
                    src={minusCircle}
                    tabIndex={0}
                    alt="minus-circle-icon"
                  />
                ) : null}
              </Flex>
            ))}
            <Flex pb={16}>
              <Button
                intent="primary"
                outlined
                minimal
                id="btn_add_new_contact_person"
                onClick={() => arrayHelpers.push(initContactPersonItem)}
              >
                {t('add')}
              </Button>
            </Flex>
          </>
        )}
      />
      {typeof removeAction === 'function' ? (
        <ConfirmDialog
          close={() => setRemoveAction(undefined)}
          confirm={removeAction}
          text={{
            btnCancel: tButtonActions('cancelText'),
            btnOk: tButtonActions('removeText'),
            title: t('removeContactPerson'),
            message: tCommon('weWillNotBeAbleToUndo'),
          }}
        />
      ) : null}
    </Flex>
  );
};

export default memo(ContactInfo);
