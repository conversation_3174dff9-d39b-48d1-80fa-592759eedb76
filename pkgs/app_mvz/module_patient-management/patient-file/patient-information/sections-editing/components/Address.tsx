
import React, { useState, useEffect, memo, useMemo, useCallback } from 'react';
import { Field } from 'formik';
import { isEmpty, debounce } from 'lodash';

import type PatientProfileLocales from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  Flex,
  ReactSelect,
  IMenuItem,
  FormGroup2,
} from '@tutum/design-system/components';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { InputGroup } from '@tutum/design-system/components/Core';
import {
  checkGermanyPostalCode,
  useMutationGetStatesFromPostalCode,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import I18n from '@tutum/infrastructure/i18n';
import countryType from '@tutum/mvz/constant/country.type';
import {
  MIN_LENGTH_POSTCODE,
  MAX_LENGTH_POSTCODE,
} from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import { IFormProps } from '..';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { IsReadCard } from '@tutum/mvz/_utils/cardReader';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { KTABValue } from '@tutum/hermes/bff/catalog_sdkt_common';
import { getInsuranceCardByQuarter } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';

const Address = ({
  values,
  errors,
  touched,
  submitCount,
  isDisabled,
  setFieldValue,
}: IFormProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientProfileLocales.AddressInformation
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'AddressInformation',
  });
  const [hasWarningPostCode, setHasWarningPostCode] = useState(false);
  const [isFirstFetchingStates, setFirstFetchingStates] =
    useState<boolean>(false);

  const isRequire =
    values?.genericInfo?.patientType === PatientType.PatientType_Private;

  const [inputValue, setInputValue] = useState<string>('');

  const isValidToValidatePostalCode = useMemo(() => {
    const cardInsurance = getInsuranceCardByQuarter(
      values?.insuranceInfos,
      datetimeUtil.now()
    );
    if (
      !IsReadCard(cardInsurance, datetimeUtil.now()) &&
      patientFileStore.schein.activatedSchein
    ) {
      const ktabScheinActive =
        patientFileStore.schein.activatedSchein?.scheinDetail?.g4106;
      if (ktabScheinActive === KTABValue.KTAB_00) {
        return true;
      }
    }
  }, [values.insuranceInfos, patientFileStore.schein.activatedSchein]);

  const debounceFunc = debounce(async (value, callback?) => {
    callback?.(value);
    if (
      value &&
      values.addressInfo.address.countryCode ===
        countryType.COUNTRY_DEFAULT_CODE &&
      isValidToValidatePostalCode
    ) {
      const { data } = await checkGermanyPostalCode({
        postalCode: value,
      });
      setHasWarningPostCode(!data?.isValid);
    } else {
      setHasWarningPostCode(false);
    }
  }, 500);

  const { data, isPending, isSuccess, mutate } =
    useMutationGetStatesFromPostalCode({
      onSuccess: (data) => {
        if (values?.addressInfo?.address.city) {
          setInputValue(values.addressInfo.address.city);
          return;
        }

        const value = data?.data?.states?.length ? data.data.states[0] : '';

        setFieldValue('addressInfo.address.city', value);
        setInputValue(value);
      },
    });

  const debouncedGetCity = useCallback(
    debounce(async (postalCode: string) => {
      mutate({
        postalCode,
      });
    }, 500),
    []
  );

  const cityList = useMemo(() => {
    if (
      !isSuccess ||
      isEmpty(data?.data?.states) ||
      !values.addressInfo.address.postCode
    ) {
      return [];
    }

    return data?.data?.states.map((state) => {
      return {
        label: state,
        value: state,
      };
    });
  }, [isSuccess, data?.data?.states, values.addressInfo.address.postCode]);

  useEffect(() => {
    debounceFunc(values?.addressInfo?.address?.postCode);
  }, [values?.addressInfo?.address?.countryCode]);

  useEffect(() => {
    if (
      !values.addressInfo.address.postCode ||
      isFirstFetchingStates ||
      isDisabled
    ) {
      return;
    }

    setFirstFetchingStates(true);
    debouncedGetCity(values.addressInfo.address.postCode);
  }, [
    isDisabled,
    isFirstFetchingStates,
    values.addressInfo.address.postCode,
    debouncedGetCity,
  ]);

  return (
    <Flex column>
      <Flex gap={16}>
        <FormGroup2
          name="addressInfo.address.street"
          label={t('street')}
          isRequired={isRequire}
          errors={errors}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name="addressInfo.address.street">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisabled}
                  value={field.value || ''}
                  maxLength={55}
                  id="street"
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          name="addressInfo.address.number"
          label={t('houseNumber')}
          isRequired={isRequire}
          errors={errors}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name="addressInfo.address.number">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisabled}
                  value={field.value || ''}
                  maxLength={55}
                  id="addressNumber"
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>
      <Flex gap={16}>
        <FormGroup2
          name="addressInfo.address.postCode"
          label={t('postalCode')}
          isRequired={
            values.addressInfo.address.countryCode ===
            countryType.COUNTRY_DEFAULT_CODE
          }
          errors={errors}
          touched={touched}
          submitCount={submitCount}
          warning={hasWarningPostCode ? t('sdplzInvalid') : null}
        >
          <Field name="addressInfo.address.postCode">
            {({ field, form }) => {
              const { setFieldValue } = form;
              const callback = (value: string) => {
                setFieldValue(field.name, value);
              };

              return (
                <InputGroup
                  id="postcode-address"
                  defaultValue={field.value}
                  disabled={isDisabled}
                  minLength={MIN_LENGTH_POSTCODE}
                  maxLength={MAX_LENGTH_POSTCODE}
                  onChange={(e) => {
                    const value = e.target.value;

                    debounceFunc(value, callback);

                    form.setFieldValue('addressInfo.address.city', '');

                    if (!value) {
                      setInputValue('');
                      return;
                    }

                    debouncedGetCity(value);
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          name="addressInfo.address.city"
          label={t('city')}
          isRequired={isRequire}
          errors={errors}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name="addressInfo.address.city">
            {({ field, form }) => {
              return (
                <ReactSelect
                  className="address-city"
                  id="address-city"
                  instanceId={'address-city'}
                  isSearchable
                  selectedValue={field.value}
                  isDisabled={isDisabled}
                  items={cityList}
                  isLoading={isPending}
                  inputValue={inputValue}
                  filterOption={() => {
                    // don't filter when search
                    return true;
                  }}
                  onInputChange={(value, { action, prevInputValue }) => {
                    switch (action) {
                      case 'input-change':
                        setInputValue(value);
                        return inputValue;
                      case 'input-blur':
                        form.setFieldValue(field.name, inputValue);
                        return inputValue;
                      default:
                        return prevInputValue;
                    }
                  }}
                  onItemSelect={(item: IMenuItem) => {
                    const value = (item?.value || '') as string;

                    form.setFieldValue(field.name, value);
                    setInputValue(value);
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          name="addressInfo.address.countryCode"
          label={t('country')}
          className="select-country"
          isRequired={isRequire}
          errors={errors}
          touched={touched}
          submitCount={submitCount}
        >
          <Field name="addressInfo.address.countryCode">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="addressInfo-country"
                  instanceId="addressInfo-country"
                  isSearchable={false}
                  selectedValue={field.value}
                  isDisabled={isDisabled}
                  items={countryType.COUNTRY_LIST}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>
      <FormGroup2 label={t('additionalAddressInfo')}>
        <Field name="addressInfo.address.additionalAddressInfo">
          {({ field }) => {
            return (
              <InputGroup
                {...field}
                value={field.value || ''}
                maxLength={45}
                data-tab-id={field.name}
                id="additionalAddressInfo"
                disabled={isDisabled}
              />
            );
          }}
        </Field>
      </FormGroup2>
      <FormGroup2 label={t('distance')}>
        <Field name="addressInfo.address.distance">
          {({ field, form }) => {
            return (
              <NumberInput
                defaultValue={field.value || ''}
                onValueChange={({ value }) =>
                  form.setFieldValue(field.name, +value || null)
                }
                maxLength={10}
                rightElement={
                  <span className="sl-input-right-element">{t('km')}</span>
                }
                data-tab-id={field.name}
                id="distance"
              />
            );
          }}
        </Field>
      </FormGroup2>
    </Flex>
  );
};

export default memo(Address);
