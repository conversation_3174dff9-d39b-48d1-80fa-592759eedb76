
import React from 'react';
import { <PERSON><PERSON><PERSON>, Field } from 'formik';

import type PatientProfileLocales from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  Flex,
  ReactSelect,
  IMenuItem,
  FormGroup2,
} from '@tutum/design-system/components';
import { InputGroup } from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import countryType from '@tutum/mvz/constant/country.type';
import {
  MIN_LENGTH_POSTCODE,
  MAX_LENGTH_POSTCODE,
} from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import { IFormProps } from '..';
import { scaleSpace } from '@tutum/design-system/styles';
import { useSettingStore } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.store';

const PostOfficeBox = ({
  values,
  errors,
  touched,
  submitCount,
  isDisabled,
}: IFormProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientProfileLocales.AddressInformation
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'AddressInformation',
  });
  const createPatientStore = useSettingStore();

  return (
    <Flex column mt={scaleSpace(4)} minWidth={480}>
      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="postOfficeBox.postCode"
          label={t('postalCode')}
          submitCount={submitCount}
          errors={errors}
          touched={touched}
          warning={
            !!values.postOfficeBox?.postCode?.length &&
            !createPatientStore.isGermanyPostOfficePostalCodeValid
              ? t('sdplzInvalid')
              : null
          }
        >
          <Field name="postOfficeBox.postCode">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  minLength={MIN_LENGTH_POSTCODE}
                  maxLength={MAX_LENGTH_POSTCODE}
                  disabled={isDisabled}
                  id="postOfficeBox-postcode"
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          name="postOfficeBox.placeOfResidence"
          label={t('placeOfResidence')}
        >
          <FastField name="postOfficeBox.placeOfResidence">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  minLength={2}
                  maxLength={40}
                  disabled={isDisabled}
                  id="placeOfResidence"
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
      <Flex gap={scaleSpace(4)}>
        <FormGroup2 name="postOfficeBox.officeBox" label={t('officeBox')}>
          <FastField name="postOfficeBox.officeBox">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  id="officeBox"
                  disabled={isDisabled}
                  maxLength={8}
                />
              );
            }}
          </FastField>
        </FormGroup2>
        <FormGroup2 name="postOfficeBox.countryCode" label={t('country')}>
          <FastField name="postOfficeBox.countryCode">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="postOfficeBox-country"
                  instanceId={'country'}
                  isSearchable={false}
                  selectedValue={field.value}
                  items={countryType.COUNTRY_LIST}
                  isDisabled={isDisabled}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
    </Flex>
  );
};

export default PostOfficeBox;
