import React, { memo, useState } from 'react';
import { <PERSON>Field, FieldArray } from 'formik';

import type PatientProfileLocales from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';

import { Flex, Button, Svg } from '@tutum/design-system/components';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import { NumericInput, InputGroup } from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { keyboardTriggerClick } from '@tutum/mvz/_utils/accessibility';
import { IFormProps } from '..';

const minusCircle = '/images/minus-circle.svg';

const ContactInfo = ({ values }: IFormProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientProfileLocales.ContactInformation
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'ContactInformation',
  });
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const [removeAction, setRemoveAction] = useState<(() => void) | undefined>(undefined);

  return (
    <Flex column style={{ flex: 1 }}>
      <FormGroup2 label={t('primaryContactNumber')}>
        <FastField name="contactInfo.primaryContactNumber">
          {({ field, form }) => {
            return (
              <NumericInput
                {...field}
                name={field.name}
                value={field.value || undefined}
                buttonPosition="none"
                fill
                maxLength={60}
                onValueChange={(_: number, valueAsString: string) => {
                  form.setFieldValue(field.name, valueAsString);
                }}
                data-tab-id="primaryContactNumber"
                id={field.name}
              />
            );
          }}
        </FastField>
      </FormGroup2>
      <FieldArray
        name="contactInfo.furtherContactNumber"
        render={(arrayHelpers) => (
          <Flex className="sl-box" column mb={15} style={{ paddingRight: 40 }}>
            {values?.contactInfo?.furtherContactNumber?.map((item, index) => (
              <FormGroup2
                label={t('furtherContactNumber')}
                key={index}
                style={{ width: '100%', position: 'relative' }}
              >
                <>
                  <FastField name={`contactInfo.furtherContactNumber.${index}`}>
                    {({ field, form }) => {
                      return (
                        <NumericInput
                          {...field}
                          name={field.name}
                          value={field.value || undefined}
                          buttonPosition="none"
                          fill
                          maxLength={60}
                          id={field.name}
                          onValueChange={(_: number, valueAsString: string) => {
                            form.setFieldValue(field.name, valueAsString);
                          }}
                          data-tab-id={`primaryContactNumber_${index}`}
                        />
                      );
                    }}
                  </FastField>
                  {index ? (
                    <Svg
                      className="sl-remove-further-contact"
                      onClick={() => {
                        const callback = () => {
                          arrayHelpers.remove(index);
                          setRemoveAction(undefined);
                        };
                        setRemoveAction(() => callback);
                      }}
                      onKeyDown={keyboardTriggerClick}
                      src={minusCircle}
                      tabIndex={0}
                      alt="minus-circle-icon"
                    />
                  ) : null}
                </>
              </FormGroup2>
            ))}
            <Flex mb={15}>
              <Button
                intent="primary"
                outlined
                minimal
                onClick={() => arrayHelpers.push('')}
              >
                {t('add')}
              </Button>
            </Flex>
          </Flex>
        )}
      />
      <FormGroup2 label={t('emailAddress')}>
        <FastField name="contactInfo.emailAddress">
          {({ field }) => {
            return (
              <InputGroup
                {...field}
                value={field.value || ''}
                type="email"
                maxLength={60}
                data-tab-id={field.name}
                id={field.name}
              />
            );
          }}
        </FastField>
      </FormGroup2>
      {typeof removeAction === 'function' ? (
        <ConfirmDialog
          close={() => setRemoveAction(undefined)}
          confirm={removeAction}
          text={{
            btnCancel: tButtonActions('cancelText'),
            btnOk: tButtonActions('removeText'),
            title: t('removeContactInfo'),
            message: tCommon('weWillNotBeAbleToUndo'),
          }}
        />
      ) : null}
    </Flex>
  );
};

export default memo(ContactInfo);
