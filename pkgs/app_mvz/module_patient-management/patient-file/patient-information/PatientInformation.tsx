import { FormikHelpers } from 'formik';
import cloneDeep from 'lodash/cloneDeep';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import isNil from 'lodash/isNil';
import moment from 'moment';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type PatientProfileLocales from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  BodyTextL,
  BodyTextM,
  Flex,
  Svg,
  Badge,
  alertSuccessfully,
  alertError,
} from '@tutum/design-system/components';
import {
  Collapse,
  Icon,
  Intent,
  Position,
  Tooltip,
} from '@tutum/design-system/components/Core';
import {
  getCssClass,
  isEmpty,
  isNullUUID,
  toShortCase,
} from '@tutum/design-system/infrastructure/utils';
import { updatePatientProfileV2 } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  CardReadInStatus,
  PatientInfo,
  PersonalInfo,
  ReadCardModel,
  InsuranceInfo,
  FromCardType,
  PatientType,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import I18n from '@tutum/infrastructure/i18n';
import PatientManagementUtil, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import { getCardReadInStatus } from '@tutum/mvz/_utils/cardReader';
import {
  employeeActions,
  useEmployeeStore,
} from '@tutum/mvz/hooks/useEmployee';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import {
  DoctorInfoExtend,
  handleShowPatientNumber,
  updateDoctorInfo,
} from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import PatientEnrollmentWidget from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/PatientEnrollmentWidget.styled';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import type { IMvzTheme } from '@tutum/mvz/theme';
import { IPatientProfile } from '../../types/profile.type';
import MedicalHistoryIcon from '@tutum/mvz/public/images/medical-history.svg';
import FileHeartBeatIcon from '@tutum/mvz/public/images/file-heartbeat.svg';
import ShieldCheckIcon from '@tutum/mvz/public/images/shield-check.svg';
import ReceiptIcon from '@tutum/mvz/public/images/receipt.svg';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { scaleSpace } from '@tutum/design-system/styles';
import {
  useListenCreateRemoveSchein,
  useListenScheinChanged,
} from '@tutum/hermes/bff/app_mvz_schein';
import {
  useListenTimelineCreate,
  useListenTimelineRemove,
  useListenTimelineUpdate,
} from '@tutum/hermes/bff/app_mvz_timeline';
import {
  WaitingRoomEventType,
  useListenWaitingRoomChanged,
} from '@tutum/hermes/bff/app_mvz_waiting_room';
import {
  checkExistWaitingRoom,
  createWaitingRoomForPatient,
  getWaitingRooms,
  useMutationUnAssignPatient,
} from '@tutum/hermes/bff/legacy/app_admin_waiting_room';
import {
  useQueryGetPreParticipationServiceCodes,
  useQueryGetTherapies,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { WaitingRoomView } from '@tutum/hermes/bff/waiting_room_common';
import {
  DATE_FORMAT,
  DATE_TIME_TRANSFER_UTC,
} from '@tutum/infrastructure/shared/date-format';
import {
  getCardModelInsurance,
  getCardInsurance,
} from '@tutum/mvz/_utils/cardReader';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import {
  PatientJobElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { DotSeparator } from '../DotSeparator';
import { useCurrentSchein } from '../hooks/useCurrentSchein.hook';
import AddPatientForm from './AddPatientForm';
import {
  ROW_EDTTING,
  formatBirthday,
  renderTitle,
  valuesParser,
} from './PatientInformation.helper';
import PopoverEditing from './popover-editing';
import SectionsEditing from './sections-editing';
import { onValidateForm } from './sections-editing/validation';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import PopoverAppointment from './popover-appointment';
import {
  InsuranceType,
  TodoTypeBasicInfo,
  getTodoTypes,
} from '@tutum/hermes/bff/legacy/app_external';
import { ContractType } from '@tutum/hermes/bff/common';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  useQueryGetAreaOfExpertises,
  useQueryGetSdavByIds,
} from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdav';
import { useQueryGetTotalScheins } from '@tutum/hermes/bff/legacy/app_mvz_schein';

export interface IPatientInformationProps {
  className?: string;
  theme?: IMvzTheme;
  patient: IPatientProfile;
  activeTabId: ID_TABS;
  isDisabled: boolean;
  isShowDMPEnrollDialog: boolean;
  onEdit: () => void;
  openCreateSchein: (scheinId?: string) => void;
  onReloadLoadListContracts?: () => void;
}

const editIcon = '/images/edit-2.svg';
const checkCircle = '/images/card-chip-mobile-active.svg';
const userIcon = '/images/user-icon.svg';
const homeIcon = '/images/home.svg';
const rulerIcon = '/images/ruler.svg';
const userNoteIcon = '/images/user-note.svg';
const phoneIcon = '/images/phone-mail.svg';
const emailIcon = '/images/email.svg';
const doctorIcon = '/images/doctor-icon.svg';
const deathIcon = '/images/death-alt3.svg';
const addWaitingRoomIcon = '/images/waiting-room-add.svg';
const removeWaitingRoomIcon = '/images/waiting-room-remove.svg';
const warningIcon = '/images/alert-circle-solid.svg';
const calendarDaysIcon = '/images/calendar-days.svg';
const readCardIcon = '/images/patient/selector/read-card-icon.svg';
const ContactConsent = '/images/phone-email-active.svg';

const PatientInformation = ({
  className,
  patient,
  activeTabId,
  isDisabled,
  isShowDMPEnrollDialog,
  onEdit,
  openCreateSchein,
  onReloadLoadListContracts,
}: IPatientInformationProps) => {
  const { t } = I18n.useTranslation({
    namespace: 'PatientFileSidebar',
    nestedTrans: 'PatientInformationSidebar',
  });
  const { t: tCreatePatient } = I18n.useTranslation<
    keyof typeof PatientProfileLocales.CreatePatient
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'CreatePatient',
  });
  const { t: tPatientFile } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'PatientFile',
  });
  const { t: tWaitingRoom } = I18n.useTranslation({
    namespace: 'WaitingRoom',
  });

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { setPatient } = useContext(PatientManagementContext.instance);

  const {
    addressInfo,
    doctorInfo,
    contactInfo,
    otherInfo,
    genericInfo,
    postOfficeBox,
  } = patient?.patientInfo || {};
  const { address } = addressInfo || {};
  const { generalPractitionerDoctorId, specialistDoctorId, treatmentDoctorId } =
    doctorInfo || {};
  const contactPersons = contactInfo?.contactPersons?.filter(
    (contact) => contact.relationship
  );

  const patientFileStore = usePatientFileStore();
  const {
    schein,
    appointmentNotifyCount,
    patient: { activeParticipations },
  } = patientFileStore;
  const employeeStore = useEmployeeStore();

  const isEmptyDoctor =
    !generalPractitionerDoctorId?.length &&
    !specialistDoctorId?.length &&
    !employeeStore?.employeeProfiles?.find(
      (employee) => employee.id === treatmentDoctorId
    );
  const isOneOfNoAgreement =
    !otherInfo?.haveMedicalHistoryFormCompleted ||
    !otherInfo?.isAgreeWithBillingViaPVS ||
    !otherInfo?.isLivingWillAvailable ||
    !otherInfo?.isPrivacyPolicySigned ||
    !contactInfo?.haveDeclarationOfAgreementToContact;

  const hasContact =
    !!contactInfo?.primaryContactNumber ||
    !!contactInfo?.emailAddress ||
    Number(
      contactInfo?.furtherContactNumber?.filter((item) => !!item)?.length
    ) > 0;

  const [expandItems, setExpandItems] = useState({
    address: false,
    contactNumber: false,
    contactPerson: false,
  });
  const [rowEditing, setRowEditing] = useState<Nullable<ROW_EDTTING>>(null);
  const [isAddPatient, setAddPatient] = useState<boolean>(false);
  const [isOpenConfirm, setIsOpenConfirm] = useState<boolean>(false);
  const [roomData, setRoomData] = useState<WaitingRoomView[]>([]);
  const [todoTypeData, setTodoTypeData] = useState<TodoTypeBasicInfo[]>([]);
  const [roomIdForCurrentPatient, setRoomIdForCurrentPatient] =
    useState<string>();
  const currentSchein = useCurrentSchein();
  const [isOpenPopoverAppointment, setIsOpenPopoverAppointment] =
    useState(false);
  const setting = useSettingStore();

  const {
    isSuccess: isGetTherapiesSuccess,
    data,
    refetch: refetchGetTherapies,
  } = useQueryGetTherapies(
    {
      patientId: patient.id,
      scheinId: currentSchein?.scheinId!,
    },
    { enabled: !!currentSchein?.scheinId }
  );

  const { data: areaOfExpertises = [] } = useQueryGetAreaOfExpertises({
    select: (data) => data.data.items || [],
  });

  const { data: sDAVInfoGP } = useQueryGetSdavByIds(
    {
      ids: generalPractitionerDoctorId,
    },
    {
      enabled: !isEmpty(generalPractitionerDoctorId),
    }
  );

  const { data: sDAVInfoSP } = useQueryGetSdavByIds(
    {
      ids: specialistDoctorId,
    },
    {
      enabled: !isEmpty(specialistDoctorId),
    }
  );

  const { refetch: refetchGetPreParticipationServiceCodes } =
    useQueryGetPreParticipationServiceCodes(
      {
        patientId: patient.id,
      },
      {
        enabled: false,
      }
    );

  const { refetch: refetchTotalScheins } = useQueryGetTotalScheins(
    {
      patientId: patient.id,
    },
    {
      enabled: false,
    }
  );
  const therapies = data?.pyschotherapies ?? [];

  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();

  const unAssignPatient = useMutationUnAssignPatient({
    onSuccess: () => {
      alertSuccessfully(tWaitingRoom('RemovePatient.successMessage'));
      handleFetchData();
      setIsOpenConfirm(false);
    },
  });

  const refetchDatas = () => {
    refetchGetTherapies();
    refetchGetPreParticipationServiceCodes();
  };

  useEffect(() => {
    if (patient.id && !isNil(currentSchein)) {
      refetchDatas();
    }
  }, [patient.id, currentSchein]);

  useListenTimelineUpdate((data) => {
    if (data.patientId !== patient.id) {
      return;
    }
    refetchDatas();
  });

  useListenCreateRemoveSchein((data) => {
    if (data?.data?.patientId !== patient.id) {
      return;
    }
    // Refetch total scheins useQuery to get latest count after schein changes
    refetchTotalScheins();
    refetchDatas();
  });

  useListenScheinChanged((data) => {
    if (data?.data?.patientId !== patient.id) {
      return;
    }
    refetchDatas();
  });

  useListenTimelineCreate((data) => {
    if (data.patientId !== patient.id) {
      return;
    }
    refetchDatas();
  });

  useListenTimelineRemove((data) => {
    if (data.patientId !== patient.id) {
      return;
    }
    refetchDatas();
  });

  useEffect(() => {
    employeeActions.loadListUserProfiles(doctorList);
  }, [doctorList]);

  const handleSetExpandItems = (event, name: string) => {
    event.stopPropagation();
    setExpandItems({
      ...expandItems,
      [name]: !expandItems[name],
    });
  };

  // mobile && kvk -> show read card date
  // egk -> online check date
  // private -> show card reading
  const handleShowCardStatus = (
    cardReadInStatus: string,
    isPrivateCard: boolean,
    ReadCardModel?: ReadCardModel
  ) => {
    const proofOfInsurance = ReadCardModel?.proofOfInsurance;
    const isReadMobileCard =
      ReadCardModel?.fromCardType === FromCardType.FromCardType_Mobile;

    const isReadCardKVK =
      ReadCardModel?.fromCardType === FromCardType.FromCardType_KVK;

    if (cardReadInStatus === CardReadInStatus.CardReadInStatus_CardReadin) {
      if (isReadMobileCard) {
        return {
          content: `${t(`cardReadinStatus.MobileCardReading`)} ${ReadCardModel.readCardDate
            ? datetimeUtil.dateTimeFormat(
              new Date(ReadCardModel.readCardDate),
              DATE_FORMAT
            )
            : ''
            }`,
          text: t(`cardReadinStatus.${cardReadInStatus}`),
          icon: checkCircle,
          color: COLOR.TAG_BACKGROUND_GREEN,
        };
      }

      if (isPrivateCard) {
        return {
          content: t('tooltipCardReadIn'),
          text: t(`cardReadinStatus.${cardReadInStatus}`),
          icon: checkCircle,
          color: COLOR.TAG_BACKGROUND_GREEN,
        };
      }

      if (isReadCardKVK) {
        return {
          content: `${t(
            'tooltipCardReadingDate'
          )}: ${datetimeUtil.dateTimeFormat(
            new Date(ReadCardModel.readCardDate),
            DATE_FORMAT
          )}`,
          text: t(`cardReadinStatus.${cardReadInStatus}`),
          icon: checkCircle,
          color: COLOR.TAG_BACKGROUND_GREEN,
        };
      }

      const egkToolTip: string = (() => {
        const pnTimeStamp = proofOfInsurance?.onlineCheckDate
          ? `${t('tooltipCardAuditEvidence')}: ${datetimeUtil.dateTimeFormat(
            new Date(proofOfInsurance.onlineCheckDate),
            DATE_FORMAT
          )}`
          : '';

        return `${proofOfInsurance?.resultCode ? t(
          `tooltipOnlineCheck.${proofOfInsurance.resultCode}`
        ) : t('tooltipOnlineCheck.default')} ${datetimeUtil.dateTimeFormat(
          new Date(ReadCardModel?.readCardDate!),
          DATE_FORMAT
        ) +
        '\n' +
        pnTimeStamp
          }`.trim();
      })();

      return {
        content: egkToolTip,
        text: t(`cardReadinStatus.${cardReadInStatus}`),
        icon: checkCircle,
        color: COLOR.TAG_BACKGROUND_GREEN,
      };
    }

    if (
      cardReadInStatus === CardReadInStatus.CardReadInStatus_OnlineCheckFail &&
      proofOfInsurance
    ) {
      return {
        content: `${proofOfInsurance?.resultCode ? t(
          `tooltipOnlineCheck.${proofOfInsurance.resultCode}`
        ) : t('tooltipOnlineCheck.default')} ${proofOfInsurance?.onlineCheckDate
          ? ` ${datetimeUtil.dateTimeFormat(
            new Date(proofOfInsurance.onlineCheckDate),
            DATE_FORMAT
          )}`
          : ''
          }`.trim(),
        text: t(`cardReadinStatus.${cardReadInStatus}`),
        icon: warningIcon,
        color: COLOR.TEXT_WARNING,
      };
    }

    return {
      content: t(`cardReadinStatus.${cardReadInStatus}`),
      text:
        cardReadInStatus === CardReadInStatus.CardReadInStatus_ManualEntry
          ? t('evTxt')
          : t(`cardReadinStatus.NotReadin`),
      icon: readCardIcon,
      color: COLOR.TEXT_WARNING,
    };
  };

  const GetCardStatus = (insurance?: InsuranceInfo) => {
    if (!insurance?.readCardDatas) {
      return getCardReadInStatus(undefined);
    }
    const cardModel = getCardModelInsurance(insurance);
    const cardStatusString = getCardReadInStatus(cardModel);
    return cardStatusString;
  };

  const getCardReadInStatusByInsurance = (insurance?: InsuranceInfo) => {
    const cardModel = getCardModelInsurance(insurance);
    const isPrivateCard = insurance?.insuranceType === TypeOfInsurance.Private;

    return handleShowCardStatus(
      GetCardStatus(insurance),
      isPrivateCard,
      cardModel
    );
  };

  const getCardReadInStatusRender = (() => {
    if (schein.activatedSchein) {
      const insuranceBySchein = patient.patientInfo.insuranceInfos?.find(
        (item) => item.id === schein.activatedSchein?.insuranceId
      );
      return getCardReadInStatusByInsurance(insuranceBySchein);
    }
    const activeInsurance = getActiveInsurance(
      patient.patientInfo.insuranceInfos
    );

    const currentPatientInfo =
      patientFileStore.patient.current?.patientInfo || patient.patientInfo;
    const cardInsurance = getCardInsurance(currentPatientInfo);

    // TODO: check Private insurance
    if (!activeInsurance) {
      const privateInsurance = patient.patientInfo.insuranceInfos?.find(
        (item) => item.insuranceType === TypeOfInsurance.Private
      );
      return getCardReadInStatusByInsurance(privateInsurance);
    }

    if (cardInsurance) return getCardReadInStatusByInsurance(cardInsurance);
    return getCardReadInStatusByInsurance(activeInsurance);
  })();

  const getFullName = (personalInfo: PersonalInfo) => {
    const { title, intendWord, lastName, firstName } = personalInfo;
    return PatientManagementUtil.getFullName(
      title,
      intendWord,
      lastName,
      firstName
    );
  };

  const dspAddress = [
    `${address?.street || ''} ${address?.number || ''}`.trim(),
    `${address?.postCode || ''} ${address?.city || ''}`.trim(),
  ].filter((str) => str);

  const postBoxInfo = [
    `${postOfficeBox.officeBox || ''}`.trim(),
    `${postOfficeBox.postCode || ''} ${postOfficeBox.placeOfResidence || ''
      }`.trim(),
  ].filter((str) => str);

  const isPublicPatient =
    genericInfo.patientType === PatientType.PatientType_Public;

  const treatmentDoctor = !isNullUUID(treatmentDoctorId)
    ? employeeStore?.employeeProfiles?.find(
      (employee) => employee.id === treatmentDoctorId
    )
    : ({} as IEmployeeProfile);

  const onSaveForm = async (
    values: PatientInfo,
    { setSubmitting }: FormikHelpers<PatientInfo>
  ) => {
    try {
      const { data: resp } = await updatePatientProfileV2({
        id: patient.id,
        patientMedicalData: patient.patientMedicalData,
        patientInfo: valuesParser(values, rowEditing),
      });
      const clonedPatient = cloneDeep(patient);
      clonedPatient.patientInfo = valuesParser(
        resp.patientInfo as PatientInfo,
        rowEditing
      );
      clonedPatient.dateOfBirth = resp.patientInfo.personalInfo.dOB;
      setPatient(clonedPatient);
      patientFileActions.patient.setCurrent(clonedPatient);
      setRowEditing(null);
      alertSuccessfully(tPatientFile('editSuccessLb'));
      setSubmitting(false);
      updateDoctorInfo(values.doctorInfo as DoctorInfoExtend);
    } catch (err) {
      console.error(err);
      alertError(err.message);
    }
  };

  const onAddPatient = () => {
    setAddPatient(true);
  };

  const handleCheckPatientExistRoom = useCallback(async () => {
    try {
      const res = await checkExistWaitingRoom({ patientId: patient.id });

      setRoomIdForCurrentPatient(res.data.roomId);
    } catch (error) {
      console.error(error);
      throw error;
    }
  }, []);

  const handleFetchData = useCallback(async () => {
    try {
      const resp = await getWaitingRooms({
        bsnrId: employeeStore.userProfile?.bsnrId,
      });

      setRoomData(resp.data.waitingRoomViews || []);
    } catch (error) {
      console.error(error);
      throw error;
    }
  }, [employeeStore.userProfile?.bsnrId]);

  const onSubmitRemovePatient = useCallback(async () => {
    unAssignPatient.mutate({
      roomId: roomIdForCurrentPatient!,
      patientId: patient.id,
    });
  }, [roomIdForCurrentPatient]);

  const onSubmitAddPatient = async (values, { setSubmitting }) => {
    try {
      await createWaitingRoomForPatient({
        waitingRoomId: values.roomId,
        patientId: patient.id,
        note: values.note,
        todoTypeId: values.todoTypeId,
      });

      alertSuccessfully(tWaitingRoom('AddPatient.successMessage'));
      setAddPatient(false);
      setSubmitting(false);
      handleFetchData();
    } catch (error) {
      console.error(error);
      throw error;
    }
  };

  useListenWaitingRoomChanged((res) => {
    if (
      [
        WaitingRoomEventType.WaitingRoomEventType_Create,
        WaitingRoomEventType.WaitingRoomEventType_Edit,
        WaitingRoomEventType.WaitingRoomEventType_Remove,
      ].includes(res.type)
    ) {
      handleFetchData();
    }
    if (patient.id === res.patientId) {
      handleCheckPatientExistRoom();
    }
  });

  useEffect(() => {
    handleCheckPatientExistRoom();
    handleFetchData();
  }, [handleCheckPatientExistRoom, handleFetchData]);

  useEffect(() => {
    const applicablePatients: InsuranceType[] = [];

    if (isPublicPatient) {
      applicablePatients.push(InsuranceType.InsuranceType_Public);
      const contractTypes = activeParticipations.map((p) => p.contractType);
      if (contractTypes.includes(ContractType.ContractType_HouseDoctorCare)) {
        applicablePatients.push(InsuranceType.InsuranceType_Hzv);
      }
      if (contractTypes.includes(ContractType.ContractType_SpecialistCare)) {
        applicablePatients.push(InsuranceType.InsuranceType_Fav);
      }
    } else {
      applicablePatients.push(InsuranceType.InsuranceType_Private);
    }

    getTodoTypes({ applicablePatients }).then((res) => {
      setTodoTypeData(res.data.data);
    });
  }, [JSON.stringify(activeParticipations), genericInfo.patientType]);

  return (
    <Flex className={className} column>
      <PopoverEditing
        isOpen={!!rowEditing}
        title={renderTitle(t, rowEditing)}
        placement="right"
        renderTarget={({ ref }) => (
          <div ref={ref} className="sl-transparent-wrapper" />
        )}
        onClose={() => setRowEditing(null)}
        initialValues={{
          ...patient.patientInfo,
          personalInfo: {
            ...patient.patientInfo.personalInfo,
            dateOfBirth: {
              ...patient.patientInfo.personalInfo.dateOfBirth,
              date: patient.patientInfo.personalInfo.dateOfBirth.date || '00',
              month: patient.patientInfo.personalInfo.dateOfBirth.month || '00',
              year: patient.patientInfo.personalInfo.dateOfBirth.year || '0000',
            },
          },
        }}
        onSaveForm={onSaveForm}
        onValidate={onValidateForm(tCreatePatient, rowEditing, isDisabled)}
        renderForm={({
          values,
          errors,
          touched,
          submitCount,
          setFieldValue,
        }) => (
          <SectionsEditing
            values={values}
            errors={errors}
            touched={touched}
            submitCount={submitCount}
            rowEditing={rowEditing}
            isDisabled={isDisabled}
            setFieldValue={setFieldValue}
          />
        )}
      />
      <PopoverEditing
        isOpen={isAddPatient}
        title={t('addPatientToWaitingRoom')}
        placement="right"
        initialValues={{
          note: '',
          roomId: '',
          taskType: '',
        }}
        renderTarget={({ ref }) => (
          <div ref={ref} className="sl-transparent-wrapper" />
        )}
        onClose={() => setAddPatient(false)}
        onSaveForm={onSubmitAddPatient}
        saveText="addText"
        needToCheckDirtyForm={false}
        onValidate={(values) => {
          const validateFields: ValidateField[] = [
            {
              fieldName: 'roomId',
              validateRule: () => !values.roomId,
              errorMessage: tWaitingRoom('AddPatient.requiredRoomId'),
            },
          ];

          const { errors } = PatientManagementUtil.validateForm(
            validateFields,
            undefined
          );

          return errors;
        }}
        renderForm={({ errors, touched }) => (
          <AddPatientForm
            errors={errors}
            touched={touched}
            roomData={roomData}
            todoTypeData={todoTypeData}
          />
        )}
      />
      {setting.enableCALFeature == 'true' && (
        <PopoverAppointment
          patient={patient}
          isOpen={isOpenPopoverAppointment}
          placement="right"
          renderTarget={({ ref }) => (
            <div ref={ref} className="sl-transparent-wrapper" />
          )}
          onClose={() => setIsOpenPopoverAppointment(false)}
          roomData={roomData}
          todoTypeData={todoTypeData}
        />
      )}
      <Flex justify="space-between">
        <Flex align="center">
          {!!roomIdForCurrentPatient && (
            <Tooltip
              className="sl-PatientInformation__patient-present"
              content={tWaitingRoom('patientPresent')}
            >
              <span className="sl-PatientInformation__patient-present__content" />
            </Tooltip>
          )}
          <BodyTextL fontWeight={600}>
            {getFullName(patient.patientInfo.personalInfo)}
          </BodyTextL>
        </Flex>
        <Flex align="flex-start">
          {setting.enableCALFeature == 'true' && (
            <Tooltip
              content={t('viewAppointment')}
              className="sl-tooltip-edit-icon"
            >
              <Badge content={appointmentNotifyCount}>
                <Svg
                  src={calendarDaysIcon}
                  width={16}
                  data-testid="calendarDaysBtn"
                  onClick={() => {
                    setIsOpenPopoverAppointment(true);
                  }}
                  className="sl-icon-edit"
                />
              </Badge>
            </Tooltip>
          )}
          <Tooltip
            content={
              roomIdForCurrentPatient
                ? tWaitingRoom('RemovePatient.popover')
                : t('addPatientToWaitingRoom')
            }
            className="sl-tooltip-edit-icon"
          >
            <Svg
              src={
                roomIdForCurrentPatient
                  ? removeWaitingRoomIcon
                  : addWaitingRoomIcon
              }
              width={16}
              data-testid="addWaitingRoomBtn"
              onClick={
                roomIdForCurrentPatient
                  ? () => setIsOpenConfirm(true)
                  : onAddPatient
              }
              className="sl-icon-edit"
            />
          </Tooltip>
          <Tooltip
            content={t('editPatientDetails')}
            className="sl-tooltip-edit-icon"
          >
            <Svg
              src={editIcon}
              width={16}
              data-testid="editPatientBtn"
              onClick={onEdit}
              className="sl-icon-edit"
              {...registerActionChainElementId(
                PatientJobElementId.DIALOG_TOGGLE_BUTTON
              )}
            />
          </Tooltip>
        </Flex>
      </Flex>
      <Flex flexWrap={true} align="center" mb={8}>
        {patient?.patientInfo?.personalInfo?.dateOfDeath && (
          <>
            <div className="sl-death-icon">
              <Tooltip
                content={`${datetimeUtil.dateTimeFormat(
                  new Date(patient.patientInfo.personalInfo.dateOfDeath),
                  DATE_FORMAT
                )}`}
              >
                <Svg src={deathIcon} width={20} />
              </Tooltip>
            </div>
            <DotSeparator />
          </>
        )}
        <Tooltip content={t(`${isPublicPatient ? 'public' : 'private'}`)}>
          <Flex
            className={`insurance_type ${isPublicPatient ? 'public' : 'private'
              }`}
          >
            <p>{toShortCase(`${isPublicPatient ? 'K' : 'P'}`)}</p>
          </Flex>
        </Tooltip>
        {!!getCardReadInStatusRender && (
          <React.Fragment>
            <BodyTextM>
              <DotSeparator />
            </BodyTextM>
            <Tooltip
              content={getCardReadInStatusRender.content}
              openOnTargetFocus={false}
              position={Position.BOTTOM_RIGHT}
              className="sl-popover-wrapper card-read-in-text"
            >
              <Flex align="center">
                <Svg
                  src={getCardReadInStatusRender.icon}
                  alt="icon"
                  style={{ width: 16 }}
                />
                <BodyTextM
                  fontWeight={600}
                  color={getCardReadInStatusRender.color}
                >
                  {getCardReadInStatusRender.text}
                </BodyTextM>
              </Flex>
            </Tooltip>
          </React.Fragment>
        )}
        {isGetTherapiesSuccess && therapies.length > 0 && (
          <React.Fragment>
            <BodyTextM>
              <DotSeparator />
            </BodyTextM>
            {therapies.flatMap((p, index) => {
              const total = p.timelineModel?.encounterPsychotherapy?.entries
                ? Object.keys(p.timelineModel.encounterPsychotherapy.entries)
                  .filter(
                    (k) =>
                      !p.timelineModel?.encounterPsychotherapy?.referenceServiceCodes?.includes(
                        k
                      )
                  )
                  .reduce((prev: number, next: string) => {
                    return (
                      prev +
                      Number(
                        p.timelineModel.encounterPsychotherapy?.entries[next]
                          .amountBilled ?? 0
                      )
                    );
                  }, 0)
                : 0;
              const dateOfRequest = moment(
                new Date(
                  p?.timelineModel?.encounterPsychotherapy?.requestDate ?? ''
                )
              ),
                dateOfApproval = moment(
                  new Date(
                    p?.timelineModel?.encounterPsychotherapy?.approvalDate ?? ''
                  )
                ),
                entries =
                  p?.timelineModel?.encounterPsychotherapy?.entries ?? {};
              if (
                moment
                  .utc(dateOfRequest.format(DATE_TIME_TRANSFER_UTC))
                  .toDate()
                  .getTime() < moment.utc('2017-04-01').toDate().getTime()
              ) {
                return Object.keys(entries).map((s) => {
                  return (
                    <Tooltip
                      key={index}
                      content={
                        <Flex style={{ flexDirection: 'column' }}>
                          {dateOfRequest.isValid() && (
                            <b>
                              {t('dateOfRequest')}:{' '}
                              {dateOfRequest.format('DD.MM.YYYY')}
                            </b>
                          )}
                          {dateOfApproval.isValid() && (
                            <b>
                              {t('dateOfApproval')}:{' '}
                              {dateOfApproval.format('DD.MM.YYYY')}
                            </b>
                          )}
                          <p>
                            {t('pyschotherapyContent', {
                              amountBilledTherapySessions:
                                entries?.[s].amountBilled,
                              amountApprovedTherapySessions:
                                entries?.[s].amountApproval,
                            })}
                          </p>
                          <ul>
                            <li>
                              {s}: {entries?.[s]?.amountBilled}
                            </li>
                          </ul>
                        </Flex>
                      }
                      openOnTargetFocus={false}
                      position={Position.BOTTOM_RIGHT}
                      className={getCssClass('sl-popover-wrapper')}
                    >
                      <span
                        id={`therapies_${entries?.[s].amountBilled}_${entries?.[s].amountApproval}`}
                        className={getCssClass('sl-tag therapy', {
                          'sl-popover-warning':
                            entries?.[s].amountBilled ==
                            entries?.[s].amountApproval,
                        })}
                      >
                        {entries?.[s].amountBilled}/
                        {entries?.[s].amountApproval}
                      </span>
                    </Tooltip>
                  );
                });
              }
              const contents = [
                <Tooltip
                  key={index}
                  content={
                    <Flex style={{ flexDirection: 'column' }}>
                      {dateOfRequest.isValid() && (
                        <b>
                          {t('dateOfRequest')}:{' '}
                          {dateOfRequest.format('DD.MM.YYYY')}
                        </b>
                      )}
                      {dateOfApproval.isValid() && (
                        <b>
                          {t('dateOfApproval')}:{' '}
                          {dateOfApproval.format('DD.MM.YYYY')}
                        </b>
                      )}
                      <p>
                        {t('pyschotherapyContent', {
                          amountBilledTherapySessions: total,
                          amountApprovedTherapySessions:
                            p?.timelineModel?.encounterPsychotherapy
                              ?.amountApproval,
                        })}
                      </p>
                      <ul>
                        {Object.keys(entries)
                          .filter(
                            (k) =>
                              !p.timelineModel?.encounterPsychotherapy?.referenceServiceCodes?.includes(
                                k
                              )
                          )
                          .map((s) => (
                            <li key={s}>
                              {s} ({entries[s].amountBilled})
                            </li>
                          ))}
                      </ul>
                    </Flex>
                  }
                  openOnTargetFocus={false}
                  position={Position.BOTTOM_RIGHT}
                  className={getCssClass('sl-popover-wrapper')}
                >
                  <span
                    id={`therapies_${total}_${p?.timelineModel?.encounterPsychotherapy?.amountApproval}`}
                    className={getCssClass('sl-tag therapy', {
                      'sl-popover-warning':
                        total ==
                        p?.timelineModel?.encounterPsychotherapy
                          ?.amountApproval,
                    })}
                  >
                    {total}/
                    {p?.timelineModel?.encounterPsychotherapy?.amountApproval}
                  </span>
                </Tooltip>,
              ];
              const referenceEntries = Object.keys(entries)
                .filter((k) =>
                  p.timelineModel?.encounterPsychotherapy?.referenceServiceCodes?.includes(
                    k
                  )
                )
                .map((k) => {
                  return entries[k];
                });
              const totalBilledReference = referenceEntries.reduce(
                (prev, next) => {
                  return prev + next.amountBilled;
                },
                0
              );
              const totalApprovalReference =
                p.timelineModel?.encounterPsychotherapy
                  ?.referenceAmountApproval ?? 0;
              if (totalApprovalReference) {
                contents.push(
                  <Tooltip
                    key={index}
                    content={
                      <Flex style={{ flexDirection: 'column' }}>
                        {dateOfRequest.isValid() && (
                          <b>
                            {t('dateOfRequest')}:{' '}
                            {dateOfRequest.format('DD.MM.YYYY')}
                          </b>
                        )}
                        {dateOfApproval.isValid() && (
                          <b>
                            {t('dateOfApproval')}:{' '}
                            {dateOfApproval.format('DD.MM.YYYY')}
                          </b>
                        )}
                        <p>
                          {t('pyschotherapyReferenceContent', {
                            amountBilledTherapySessions: totalBilledReference,
                            amountApprovedTherapySessions:
                              totalApprovalReference,
                          })}
                        </p>
                        <ul>
                          {Object.keys(entries)
                            .filter((k) =>
                              p?.timelineModel?.encounterPsychotherapy?.referenceServiceCodes.includes(
                                k
                              )
                            )
                            .map((s) => (
                              <li key={s}>
                                {s} ({entries[s].amountBilled})
                              </li>
                            ))}
                        </ul>
                      </Flex>
                    }
                    openOnTargetFocus={false}
                    position={Position.BOTTOM_RIGHT}
                    className={getCssClass('sl-popover-wrapper')}
                  >
                    <span
                      className={getCssClass('sl-tag therapy', {
                        'sl-popover-warning':
                          totalBilledReference == totalApprovalReference,
                      })}
                    >
                      {totalBilledReference}/{totalApprovalReference}
                    </span>
                  </Tooltip>
                );
              }
              return contents;
            })}
          </React.Fragment>
        )}
        {isOneOfNoAgreement && (
          <Flex className="agreement-icons" gap={scaleSpace(1)}>
            <BodyTextM>
              <DotSeparator />
            </BodyTextM>
            {!otherInfo?.haveMedicalHistoryFormCompleted ? (
              <Tooltip
                content={t('tooltipNoMedicalHistory')}
                openOnTargetFocus={false}
              >
                <MedicalHistoryIcon style={{ width: 16, marginRight: 4 }} />
              </Tooltip>
            ) : null}
            {!otherInfo?.isLivingWillAvailable ? (
              <Tooltip
                content={t('tooltipNoLivingWillAvailable')}
                openOnTargetFocus={false}
              >
                <FileHeartBeatIcon style={{ width: 16, marginRight: 4 }} />
              </Tooltip>
            ) : null}
            {!otherInfo?.isAgreeWithBillingViaPVS ? (
              <Tooltip
                content={t('tooltipNoAgreeWithBilling')}
                openOnTargetFocus={false}
              >
                <ReceiptIcon style={{ width: 16, marginRight: 4 }} />
              </Tooltip>
            ) : null}
            {!otherInfo?.isPrivacyPolicySigned ? (
              <Tooltip
                content={t('tooltipNoPrivacy')}
                openOnTargetFocus={false}
              >
                <ShieldCheckIcon style={{ width: 16 }} />
              </Tooltip>
            ) : null}
            {!contactInfo?.haveDeclarationOfAgreementToContact && (
              <Tooltip
                content={t('tooltipNoContact')}
                openOnTargetFocus={false}
              >
                <Svg src={ContactConsent} style={{ width: 17 }} />
              </Tooltip>
            )}
          </Flex>
        )}
      </Flex>
      <Flex
        align="center"
        className={`sl-row ${rowEditing === ROW_EDTTING.PERSONAL_INFO ? 'sl-active' : null
          }`}
        mb={2}
        onClick={() => setRowEditing(ROW_EDTTING.PERSONAL_INFO)}
      >
        <Tooltip
          className="icon-with-popover"
          content={t('tooltipDoBGender')}
          openOnTargetFocus={false}
        >
          <Svg
            src={userIcon}
            alt="icon"
            style={{ width: 16, marginRight: 8 }}
          />
        </Tooltip>
        <BodyTextM>
          {formatBirthday(t, patient.patientInfo.personalInfo.dateOfBirth)}
        </BodyTextM>
        <BodyTextM>
          <DotSeparator />
        </BodyTextM>
        <BodyTextM>{patient?.patientInfo?.personalInfo?.gender}</BodyTextM>
        <BodyTextM>
          <DotSeparator />
        </BodyTextM>
        <Tooltip content={t('tooltipPatientId')} openOnTargetFocus={false}>
          <BodyTextM>
            {t('ID')}:{' '}
            {handleShowPatientNumber(patient?.patientInfo?.patientNumber)}
          </BodyTextM>
        </Tooltip>
      </Flex>
      {activeTabId !== ID_TABS.MEDICATION && activeTabId !== ID_TABS.LAB && (
        <>
          {!!dspAddress[0] && (
            <Flex
              column
              className={`sl-row ${rowEditing === ROW_EDTTING.ADDRESS ? 'sl-active' : null
                }`}
              mb={2}
              onClick={() => setRowEditing(ROW_EDTTING.ADDRESS)}
            >
              <Flex align="center">
                <Tooltip
                  className="icon-with-popover"
                  content={t('tooltipAddress')}
                  openOnTargetFocus={false}
                >
                  <Svg
                    src={homeIcon}
                    alt="icon"
                    style={{ width: 16, marginRight: 8 }}
                  />
                </Tooltip>
                <BodyTextM>{dspAddress.join(', ')}</BodyTextM>
                <Icon
                  className="sl-expand-icon"
                  icon={expandItems.address ? 'chevron-up' : 'chevron-down'}
                  intent={Intent.NONE}
                  onClick={(e) => handleSetExpandItems(e, 'address')}
                />
              </Flex>
              <Collapse className="sl-collapse" isOpen={expandItems.address}>
                <Tooltip
                  content={t('tooltipDistance')}
                  openOnTargetFocus={false}
                >
                  <Svg
                    src={rulerIcon}
                    alt="icon"
                    style={{ width: 16, marginRight: 8 }}
                  />
                </Tooltip>
                <BodyTextM style={{ display: 'inline' }}>
                  {patient?.patientInfo?.addressInfo?.address?.distance || 0} km
                </BodyTextM>
              </Collapse>
            </Flex>
          )}
          {postBoxInfo.length > 0 && (
            <Flex
              column
              className={`sl-row ${rowEditing === ROW_EDTTING.POST_OFFICE_BOX ? 'sl-active' : null
                }`}
              mb={2}
              onClick={() => setRowEditing(ROW_EDTTING.POST_OFFICE_BOX)}
            >
              <Flex align="center">
                <Tooltip
                  className="icon-with-popover"
                  content={t('tooltipPostOfficeBox')}
                  openOnTargetFocus={false}
                >
                  <Svg
                    src={homeIcon}
                    alt="icon"
                    style={{ width: 16, marginRight: 8 }}
                  />
                </Tooltip>
                <BodyTextM>{postBoxInfo.join(', ')}</BodyTextM>
              </Flex>
            </Flex>
          )}
        </>
      )}
      {hasContact && (
        <Flex
          column
          className={`sl-row ${rowEditing === ROW_EDTTING.CONTACT_INFO ? 'sl-active' : null
            }`}
          mb={2}
          onClick={() => setRowEditing(ROW_EDTTING.CONTACT_INFO)}
        >
          <Flex align="center">
            <Tooltip
              className="icon-with-popover"
              content={t('tooltipContactNumber')}
              openOnTargetFocus={false}
            >
              <Svg
                src={phoneIcon}
                alt="icon"
                style={{ width: 16, marginRight: 8 }}
              />
            </Tooltip>
            <Tooltip
              content={t('tooltipPrimaryContactNumber')}
              openOnTargetFocus={false}
            >
              <BodyTextM>{contactInfo?.primaryContactNumber}</BodyTextM>
            </Tooltip>

            {(contactInfo?.emailAddress ||
              Number(
                contactInfo?.furtherContactNumber?.filter((item) => !!item)
                  ?.length
              ) > 0) && (
                <Icon
                  className="sl-expand-icon"
                  icon={expandItems.contactNumber ? 'chevron-up' : 'chevron-down'}
                  intent={Intent.NONE}
                  onClick={(e) => handleSetExpandItems(e, 'contactNumber')}
                />
              )}
          </Flex>
          <Collapse className="sl-collapse" isOpen={expandItems.contactNumber}>
            <Flex column>
              {contactInfo?.furtherContactNumber?.map((item, index) => (
                <Tooltip
                  key={index}
                  content={t('tooltipSecondaryContactNumber')}
                  openOnTargetFocus={false}
                >
                  <BodyTextM className="contact">{item}</BodyTextM>
                </Tooltip>
              ))}
            </Flex>

            <BodyTextM>{contactInfo?.emailAddress}</BodyTextM>
          </Collapse>
        </Flex>
      )}
      {activeTabId !== ID_TABS.MEDICATION &&
        activeTabId !== ID_TABS.LAB &&
        Number(contactPersons?.length) > 0 && (
          <Flex
            className={`sl-row ${rowEditing === ROW_EDTTING.CONTACT_PERSON ? 'sl-active' : null
              }`}
            mb={2}
            onClick={() => setRowEditing(ROW_EDTTING.CONTACT_PERSON)}
          >
            <Flex pt={2}>
              <Tooltip
                content={t('tooltipContactPerson')}
                openOnTargetFocus={false}
              >
                <Svg
                  src={userNoteIcon}
                  alt="icon"
                  style={{ width: 16, marginRight: 8 }}
                />
              </Tooltip>
            </Flex>
            {expandItems.contactPerson ? (
              <Flex column>
                {contactPersons?.map((item, index) => (
                  <Flex key={index} mb={4}>
                    <Flex w={104}>
                      <span className="sl-tag">{item.relationship}</span>
                    </Flex>
                    <Flex column>
                      <BodyTextM margin="0 0 4px 0">{item.name}</BodyTextM>
                      <Flex mb={4}>
                        <Tooltip
                          className="sl-popover-wrapper"
                          content={t('tooltipPhoneNumber')}
                          openOnTargetFocus={false}
                        >
                          <Svg
                            src={phoneIcon}
                            alt="icon"
                            style={{ width: 16, marginRight: 8 }}
                          />
                        </Tooltip>
                        <BodyTextM>{item.phoneNumber}</BodyTextM>
                      </Flex>
                      <Flex mb={4}>
                        <Tooltip
                          className="sl-popover-wrapper"
                          content={t('tooltipEmailAddress')}
                          openOnTargetFocus={false}
                        >
                          <Svg
                            src={emailIcon}
                            alt="icon"
                            style={{ width: 16, marginRight: 8 }}
                          />
                        </Tooltip>
                        <BodyTextM>{item.email}</BodyTextM>
                      </Flex>
                    </Flex>
                  </Flex>
                ))}
              </Flex>
            ) : (
              <Flex>
                {contactPersons?.map((item, index) => (
                  <span className="sl-tag" key={index}>
                    {item.relationship}
                  </span>
                ))}
              </Flex>
            )}
            {contactPersons?.length ? (
              <Icon
                className="sl-expand-icon"
                icon={expandItems.contactPerson ? 'chevron-up' : 'chevron-down'}
                intent={Intent.NONE}
                onClick={(e) => handleSetExpandItems(e, 'contactPerson')}
              />
            ) : null}
          </Flex>
        )}
      {!isEmptyDoctor && (
        <Flex
          className={`sl-row ${rowEditing === ROW_EDTTING.DOCTOR_INFO ? 'sl-active' : null
            }`}
          mb={2}
          onClick={() => setRowEditing(ROW_EDTTING.DOCTOR_INFO)}
        >
          <Flex pt={4}>
            <Tooltip content={t('tooltipDoctor')} openOnTargetFocus={false}>
              <Svg
                src={doctorIcon}
                alt="icon"
                style={{ width: 16, marginRight: 8 }}
              />
            </Tooltip>
          </Flex>
          <Flex column>
            <BodyTextM>
              {!!treatmentDoctorId && !isNullUUID(treatmentDoctorId) && (
                <>
                  <strong>{t('TreatmentDoctor')}:</strong>&nbsp;
                  <Tooltip
                    content={t('tooltipDoctor')}
                    openOnTargetFocus={false}
                  >
                    <span>{nameUtils.getDoctorName(treatmentDoctor)}</span>
                  </Tooltip>
                </>
              )}
            </BodyTextM>
            <BodyTextM>
              {!!generalPractitionerDoctorId?.length && (
                <>
                  <strong>{t('GP')}:</strong>&nbsp;
                </>
              )}
              {generalPractitionerDoctorId?.map((doctorId, index) => {
                const matchedDoctor = (doctorList || []).find(
                  (employee) => employee.id === doctorId
                );
                const doctor = (sDAVInfoGP?.items || []).find(
                  (employee) => employee.sdavId === doctorId
                );

                return (
                  <React.Fragment key={index}>
                    <Tooltip content={t('tooltipGP')} openOnTargetFocus={false}>
                      <React.Fragment>
                        {nameUtils.getDoctorName(
                          matchedDoctor || doctor?.doctorInfo
                        ) || doctor?.generalInfo.addressName}
                        {generalPractitionerDoctorId.length - 1 !== index &&
                          ','}
                      </React.Fragment>
                    </Tooltip>
                    {generalPractitionerDoctorId.length - 1 !== index && (
                      <span>&nbsp;</span>
                    )}
                  </React.Fragment>
                );
              })}
            </BodyTextM>
            <BodyTextM>
              {!!specialistDoctorId?.length && (
                <>
                  <strong>{t('Specialist')}:</strong>&nbsp;
                </>
              )}
              {specialistDoctorId?.map((doctorId, index) => {
                const matchedDoctor = (doctorList || []).find(
                  (employee) => employee.id === doctorId
                );
                const specialist = (sDAVInfoSP?.items || []).find(
                  (employee) => employee.sdavId === doctorId
                );
                const tooltipSpecialist = matchedDoctor
                  ? (matchedDoctor.areaOfExpertise || []).map((area) => {
                    const matchArea = areaOfExpertises.find(
                      (item) => item.v === area
                    );

                    return matchArea?.dN;
                  })
                  : (specialist?.doctorInfo.areaOfExpertises || []).map(
                    (item) => item.dN
                  );

                return (
                  <React.Fragment key={index}>
                    <Tooltip
                      content={tooltipSpecialist.join(', ')}
                      openOnTargetFocus={false}
                    >
                      <React.Fragment>
                        {nameUtils.getDoctorName(
                          matchedDoctor || specialist?.doctorInfo
                        ) || specialist?.generalInfo.addressName}
                        {specialistDoctorId.length - 1 !== index && ','}
                      </React.Fragment>
                    </Tooltip>
                    {specialistDoctorId.length - 1 !== index && (
                      <span>&nbsp;</span>
                    )}
                  </React.Fragment>
                );
              })}
            </BodyTextM>
          </Flex>
        </Flex>
      )}
      <PatientEnrollmentWidget
        className="enrollment"
        isShowDMPEnrollDialog={isShowDMPEnrollDialog}
        openCreateSchein={openCreateSchein}
        onReloadLoadListContracts={onReloadLoadListContracts ?? (() => { })}
      />
      <DeleteConfirmDialog
        className="confirm-dialog--hide-icon"
        isOpen={isOpenConfirm}
        isLoading={unAssignPatient.isPending}
        close={() => setIsOpenConfirm(false)}
        confirm={onSubmitRemovePatient}
        text={{
          btnCancel: tButtonActions('no'),
          btnOk: tButtonActions('yesRemove'),
          title: tWaitingRoom('RemovePatient.title'),
          message: tWaitingRoom('RemovePatient.description'),
        }}
      />
    </Flex>
  );
};

export default PatientInformation;
