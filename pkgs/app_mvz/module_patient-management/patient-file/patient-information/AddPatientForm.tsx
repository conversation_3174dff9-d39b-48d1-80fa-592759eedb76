import React, { useMemo } from 'react';
import { Field, FormikErrors, FormikTouched } from 'formik';
import {
  Flex,
  ReactSelect,
  IMenuItem,
  FormGroup2,
} from '@tutum/design-system/components';
import { InputGroup } from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import type WaitingRoomLang from '@tutum/mvz/locales/en/WaitingRoom.json';
import { WaitingRoomView } from '@tutum/hermes/bff/waiting_room_common';
import { TodoTypeBasicInfo } from '@tutum/hermes/bff/legacy/app_external';

export interface AddPatientInfo {
  note: string;
  roomId: string;
  todoTypeId: string;
}

interface AddPatientFormProps {
  errors: FormikErrors<AddPatientInfo>;
  touched: FormikTouched<AddPatientInfo>;
  roomData: WaitingRoomView[];
  todoTypeData?: TodoTypeBasicInfo[];
  disabledTodoType?: boolean;
}

const AddPatientForm = ({
  errors,
  touched,
  roomData,
  todoTypeData = [],
  disabledTodoType = false,
}: AddPatientFormProps) => {
  const { t } = I18n.useTranslation<keyof typeof WaitingRoomLang.AddPatient>({
    namespace: 'WaitingRoom',
    nestedTrans: 'AddPatient',
  });

  const roomOptions = useMemo(() => {
    return roomData.map((datum) => ({
      label: datum.name,
      value: datum.id!,
    }));
  }, [roomData]);

  const todoTypeOptions = useMemo(() => {
    return todoTypeData.map((task) => ({
      label: task.name,
      value: task.id,
    }));
  }, [todoTypeData]);

  return (
    <Flex column auto>
      <Flex gap={16}>
        <FormGroup2 label={t('note')} name="note">
          <Field name="note">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={60}
                  id="note"
                  autoFocus
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>
      <Flex gap={16}>
        <FormGroup2
          label={t('waitingRoom')}
          className="select-country"
          name="roomId"
          isRequired
          errors={errors}
          touched={touched}
        >
          <Field name="roomId">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="roomId"
                  instanceId="roomId"
                  selectedValue={field.value}
                  items={roomOptions}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item?.value || '');
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          label={t('taskType')}
          className="select-country"
          name="todoTypeId"
        >
          <Field name="todoTypeId">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="todoTypeId"
                  instanceId="todoTypeId"
                  selectedValue={field.value}
                  items={todoTypeOptions}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                  isDisabled={disabledTodoType}
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>
    </Flex>
  );
};

export default AddPatientForm;
