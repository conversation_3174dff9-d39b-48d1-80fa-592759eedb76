import { isEmpty, isNil } from 'lodash';
import React, { useEffect, useState } from 'react';

import { Flex, Svg } from '@tutum/design-system/components';
import { Text } from '@tutum/design-system/components/Core';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog/InfoConfirmDialog';
import {
  GetInvalidOmimGChainItem,
  getInvalidOmimGChain,
} from '@tutum/hermes/bff/legacy/app_mvz_text_module';
import {
  getSettings,
  saveSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import { getCurrentVersionByDocumentType } from '@tutum/hermes/bff/legacy/app_mvz_versioninfor';
import { MasterDataType } from '@tutum/hermes/bff/legacy/masterdata_common';
import I18n from '@tutum/infrastructure/i18n';

interface InvalidOmimGChainDialogProps {
  selectedDate: number;
}

const InvalidOmimGChainDialog = (props: InvalidOmimGChainDialogProps) => {
  const { selectedDate } = props;

  const { t } = I18n.useTranslation({
    namespace: 'Common',
    nestedTrans: 'TextModuleDialog',
  });
  const [isOpen, setIsOpen] = useState(false);
  const [invalidOmimGChains, setInvalidOmimGChains] = useState<
    GetInvalidOmimGChainItem[]
  >([]);

  const init = async () => {
    const { data: versionInfo } = await getCurrentVersionByDocumentType({
      masterDataType: MasterDataType.SDOMIM,
    });

    if (!isNil(versionInfo)) {
      const settingKey = `CHECK_INVALID_OMIM_G_CHAIN_v${versionInfo.versionNumber}_${versionInfo.quarter}_${versionInfo.year}`;
      const resp = await getSettings({ settings: [settingKey] });
      const settings = resp.data.settings || {};

      if (isNil(settings[settingKey])) {
        // NOTE: run validation
        const resp = await getInvalidOmimGChain({
          selectedDate,
        });
        const invalidChains = resp.data?.data || [];
        if (!isEmpty(invalidChains)) {
          setInvalidOmimGChains(invalidChains);
          setIsOpen(true);
        }

        // NOTE: set user_setting is validated
        await saveSettings({
          settings: {
            [settingKey]: 'VALIDATED',
            ...settings,
          },
        });
      }
    }
  };
  useEffect(() => {
    init();
  }, []);

  return (
    <InfoConfirmDialog
      headerIcon={
        <Svg src="/images/alert-circle.svg" style={{ marginRight: 8 }} />
      }
      type="primary"
      isOpen={isOpen}
      title={t('OmimGFileUpdatedTitle')}
      cancelText="Ok"
      isConfirmButtonShown={false}
      onClose={() => setIsOpen(false)}
      onConfirm={() => setIsOpen(false)}
      hasHeaderLine={false}
    >
      <Flex
        style={{
          maxHeight: '400px',
          flexDirection: 'column',
          overflowY: 'auto',
        }}
      >
        <Text style={{ marginBottom: 5 }}>{t('OmimGFileUpdatedHeader')}</Text>
        {invalidOmimGChains.map((textmodule) => (
          <Text key={textmodule.id}>- {textmodule.textShortcut}</Text>
        ))}
      </Flex>
    </InfoConfirmDialog>
  );
};

export default InvalidOmimGChainDialog;
