import { useState, useEffect } from 'react';
import { usePatientFileStore } from '../PatientFile.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
export function useCurrentPatient() {
  const [currentPatient, setCurrentPatient] = useState<IPatientProfile | null>(null);
  const patientFileStore = usePatientFileStore();
  useEffect(() => {
    setCurrentPatient(patientFileStore.patient.current!);
  }, [JSON.stringify(patientFileStore.patient.current)]);
  return currentPatient;
}
