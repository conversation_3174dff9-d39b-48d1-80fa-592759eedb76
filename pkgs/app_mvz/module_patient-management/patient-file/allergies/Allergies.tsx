import React, { useState, memo, useEffect } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import type PatientProfileCreationI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import { IPatientProfile } from '../../types/profile.type';
import { IMvzTheme } from '@tutum/mvz/theme';
import { alertSuccessfully, Flex, Svg } from '@tutum/design-system/components';
import { Divider, Tooltip, Intent } from '@tutum/design-system/components/Core';
import AllergiesInline, { onValidateForm } from './allergies-inline';
import { medicalHistoryActions } from '@tutum/mvz/module_patient-management/patient-file/medical-data/medical-history-dialog/MedicalHistory.store';
import {
  Allergy,
  PatientMedicalData,
} from '@tutum/hermes/bff/patient_profile_common';
import {
  EventPatientProfileChange,
  createPatientMedicalData,
  GetPatientMedicalDataResponse,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  EventName,
  useListenPatientProfileChange,
} from '@tutum/hermes/bff/app_mvz_patient_profile';
import PopoverEditing from '@tutum/mvz/module_patient-management/patient-file/patient-information/popover-editing';
import {
  AllergyElementId,
  actionChainActions,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';

export interface IAllergiesProps {
  theme?: IMvzTheme;
  className?: string;
  patient?: IPatientProfile;
  canEdit?: boolean;
  setMedicalData?: (medicalData: PatientMedicalData) => void;
  setMedicalDataUpdatedAt?: (medicalDataUpdatedAt: number) => void;
}

const HandIcon = '/images/hand-allergies.svg';
const PillIcon = '/images/pill.svg';

interface IInitValues {
  allergies: Allergy[];
}

export const initAllergy = {
  allergy: '',
  isPrescriptionRelated: false,
};

const Allergies = ({
  className,
  patient,
  canEdit = true,
  setMedicalData,
  setMedicalDataUpdatedAt,
}: IAllergiesProps) => {
  const { patientMedicalData } = patient || {};
  const allergies = patientMedicalData?.allergies || [];
  const id = patient.id;

  const { t } = I18n.useTranslation<
    keyof typeof PatientProfileCreationI18n.Medical
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'Medical',
  });
  const { t: tFormValidation } = I18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const [isOpenAllergies, setIsOpenAllergies] = useState(false);

  const successToaster = () => {
    alertSuccessfully(t('medicalRecordIsUpdated'));
  };

  const onCloseAllergiesInline = () => {
    setIsOpenAllergies(false);
  };

  const onSaveAllergiesInline = async (values: IInitValues) => {
    const filteredAllergies = values?.allergies?.filter((item) => item.allergy);
    const medicalData = {
      ...patientMedicalData,
      allergies: filteredAllergies || [],
    };
    await createPatientMedicalData({
      patientId: id,
      patientMedicalData: medicalData,
    });
    setMedicalData(medicalData);
    successToaster();
    onCloseAllergiesInline();
  };

  useListenPatientProfileChange((response: EventPatientProfileChange) => {
    if (
      response &&
      response.patientMedicalData &&
      response.medicalDataUpdatedAt &&
      response.eventName === EventName.EventName_UpdateMedicalData &&
      patient.id === response.patientId
    ) {
      setMedicalDataUpdatedAt(response?.medicalDataUpdatedAt);
      setMedicalData(response.patientMedicalData);
    }
    // updated but response.patientMedicalData === null
    else if (
      response &&
      response.patientMedicalData &&
      !response.medicalDataUpdatedAt &&
      response.eventName === EventName.EventName_UpdateMedicalData &&
      patient.id === response.patientId
    ) {
      // try to call the api to get medical data again
      // todo: for the backend side - fix return data @ useListenPatientProfileChange then remove this code.
      (
        medicalHistoryActions.getMedicalHistory(
          false
        ) as Promise<GetPatientMedicalDataResponse>
      )?.then((rs) => {
        if (rs.patientMedicalDatas?.length > 0) {
          const lastRecord = rs.patientMedicalDatas[0];
          setMedicalDataUpdatedAt(lastRecord.createdAt);
          setMedicalData(lastRecord.patientMedicalData);
        }
      });
    }
  });

  useEffect(() => {
    // NOTE: notify to all action chain subscribers that allergy-sidebar is mounted - ready
    actionChainActions.setMountedSectionsStatus({
      'allergy-sidebar': true,
    });
    return () => {
      // NOTE: notify to all action chain subscribers that allergy-sidebar is unmounted - not ready
      actionChainActions.setMountedSectionsStatus({
        'allergy-sidebar': false,
      });
    };
  }, []);

  return (
    <>
      <Flex column className={className}>
        <PopoverEditing
          title={t('editAllergy')}
          isOpen={isOpenAllergies}
          hasBackdrop
          placement="right"
          minimal
          initialValues={{
            allergies: allergies?.length ? allergies : [initAllergy],
          }}
          onClose={onCloseAllergiesInline}
          onSaveForm={onSaveAllergiesInline}
          onValidate={onValidateForm(tFormValidation)}
          renderForm={({ values, errors, touched, submitCount }) => (
            <AllergiesInline
              patient={patient}
              allergies={values?.allergies}
              errors={errors}
              touched={touched}
              submitCount={submitCount}
            />
          )}
          renderTarget={({ ref }) => (
            <div ref={ref} className="sl-transparent-wrapper" />
          )}
          saveButtonProps={
            {
              ...registerActionChainElementId(
                AllergyElementId.DIALOG_SAVE_BUTTON
              ),
            } as any
          }
          cancelButtonProps={
            {
              ...registerActionChainElementId(
                AllergyElementId.DIALOG_CANCEL_BUTTON
              ),
            } as any
          }
        />
        <Flex
          className={`sl-row sl-row-allergies ${isOpenAllergies ? 'sl-active' : null
            }`}
          onClick={canEdit ? () => setIsOpenAllergies(true) : null}
          {...registerActionChainElementId(
            AllergyElementId.DIALOG_TOGGLE_BUTTON
          )}
        >
          <span className="sl-icon-wrapper">
            <Tooltip content={t('allergiesTooltip')}>
              <Svg src={HandIcon} alt="hand-icon" className="allergy-icon" />
            </Tooltip>
          </span>
          <Flex className="sl-horizontal-scrollable">
            {allergies?.length
              ? allergies.map((allergy, index) => (
                <span className="sl-tag" key={index}>
                  {allergy.isPrescriptionRelated && (
                    <Svg src={PillIcon} alt="hand-icon" />
                  )}
                  {allergy.allergy}
                </span>
              ))
              : t('patientHasNoAllergies')}
          </Flex>
        </Flex>
      </Flex>
      <Divider className="side-bar__divider" />
    </>
  );
};

export default memo(Allergies);
