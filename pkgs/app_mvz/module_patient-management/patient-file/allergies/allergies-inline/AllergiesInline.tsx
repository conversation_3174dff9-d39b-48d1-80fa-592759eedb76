import React, { memo, useState, useMemo } from 'react';
import { Field, FieldArray, FormikErrors, FormikTouched } from 'formik';
import { Flex, Button, Svg } from '@tutum/design-system/components';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import {
  FormGroup,
  Checkbox,
  Tooltip,
  Position,
} from '@tutum/design-system/components/Core';
import useTranslation from 'next-translate/useTranslation';
import { Allergy } from '@tutum/hermes/bff/patient_profile_common';
import AllerySuggestor from '@tutum/mvz/components/select/allery';
import IconInfo from '@tutum/mvz/public/images/icon-info.svg';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { initAllergy } from '../Allergies';
import {
  AllergyElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import I18n from '@tutum/infrastructure/i18n';
import PatientProfileCreation from '@tutum/mvz/locales/en/PatientProfileCreation.json';

const minusCircle = '/images/minus-circle.svg';

export interface IAllergiesInlineProps {
  patient?: IPatientProfile;
  className?: string;
  allergies: Allergy[];
  is4205?: boolean;
  errors: FormikErrors<any>;
  touched: FormikTouched<any>;
  submitCount: number;
}

const AllergiesInline = ({
  className,
  patient,
  allergies,
  is4205,
  errors,
  touched,
  submitCount,
}: IAllergiesInlineProps) => {
  const originAllergies = patient?.patientMedicalData?.allergies || [];

  const { t } = useTranslation('PatientProfileCreation');

  const { t: tAutoFill4205Modal } = I18n.useTranslation<
    keyof typeof ScheinI18n.AutoFill4205Modal
  >({
    namespace: 'Schein',
    nestedTrans: 'AutoFill4205Modal',
  });

  const { t: tRemoveConfirmAllergies } = I18n.useTranslation<
    keyof typeof PatientProfileCreation.Medical.allergyDialog
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'Medical.allergyDialog',
  });

  const { t: tRemoveConfirm4205Modal } = I18n.useTranslation<
    keyof typeof ScheinI18n.AutoFill4205Modal.RemoveConfirm
  >({
    namespace: 'Schein',
    nestedTrans: 'AutoFill4205Modal.RemoveConfirm',
  });

  const [removeAction, setRemoveAction] = useState<(() => void) | null>(null);

  const removeConfirmText = useMemo(() => {
    return is4205 ? tRemoveConfirm4205Modal : tRemoveConfirmAllergies;
  }, [is4205]);

  const focusOnLastItem = (arr: Allergy[], item: Allergy, idx: number) => {
    if (arr.length - idx === 1 && item.allergy === '') {
      return registerActionChainElementId(AllergyElementId.FOCUS_FIELD);
    }
    return null;
  };

  const shouldAddNewItem = () => {
    if (originAllergies.length === 0) {
      return null;
    }
    return registerActionChainElementId(AllergyElementId.DIALOG_ADD_BUTTON);
  };

  return (
    <>
      <Flex
        column
        className={className}
        {...registerActionChainElementId(AllergyElementId.DIALOG)}
      >
        <FieldArray
          name="allergies"
          render={(arrayHelpers) => (
            <Flex column pb={16}>
              {allergies?.map((item, index) => (
                <Flex key={index} column className="sl-field-item">
                  <FormGroup2
                    name={`allergies.${index}.allergy`}
                    isRequired
                    errors={errors}
                    touched={touched}
                    submitCount={submitCount}
                  >
                    <Field name={`allergies.${index}.allergy`}>
                      {({ field, form }) => {
                        return (
                          <AllerySuggestor
                            {...field}
                            is4205={is4205}
                            exceptList={form.values?.allergies?.map(
                              (al) => al.allergy
                            )}
                            onValueChange={(
                              valueAsNumber: number,
                              valueAsString: string
                            ) => {
                              form.setFieldValue(field.name, valueAsString);
                            }}
                            data-tab-id={field.name}
                            {...focusOnLastItem(allergies, item, index)}
                          />
                        );
                      }}
                    </Field>
                  </FormGroup2>
                  <FormGroup className="form-group--prescription">
                    {!is4205 && (
                      <Field name={`allergies.${index}.isPrescriptionRelated`}>
                        {({ field, form }) => {
                          return (
                            <Checkbox
                              checked={field.value}
                              data-tab-id={field.name}
                              onChange={() => {
                                form.setFieldValue(field.name, !field.value);
                              }}
                              style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                              }}
                              labelElement={
                                <Flex align="center">
                                  <span style={{ marginRight: 8 }}>
                                    {t(
                                      'Medical.AllergiesInline.markAsPrescriptionRelated'
                                    )}
                                  </span>
                                  <Tooltip
                                    className="sl-mark-as-prescription-related-tooltip"
                                    content={t(
                                      'Medical.AllergiesInline.markAsPrescriptionRelatedTooltip'
                                    )}
                                    openOnTargetFocus={false}
                                    position={Position.BOTTOM}
                                  >
                                    <IconInfo />
                                  </Tooltip>
                                </Flex>
                              }
                            />
                          );
                        }}
                      </Field>
                    )}
                  </FormGroup>
                  {index ? (
                    <Svg
                      className="sl-remove-allergy"
                      onClick={() => {
                        if (
                          `${item?.allergy || ''}`.trim().length > 0 ||
                          item?.isPrescriptionRelated
                        ) {
                          setRemoveAction(() => () => {
                            arrayHelpers.remove(index);
                            setRemoveAction(null);
                          });
                        } else {
                          arrayHelpers.remove(index);
                        }
                      }}
                      src={minusCircle}
                      alt="minus-circle-icon"
                    />
                  ) : null}
                </Flex>
              ))}
              <Flex mt={8}>
                <Button
                  intent="primary"
                  outlined
                  minimal
                  {...shouldAddNewItem()}
                  onClick={() => arrayHelpers.push(initAllergy)}
                >
                  {is4205
                    ? tAutoFill4205Modal('addButton')
                    : t('Medical.AllergiesInline.addAllergy')}
                </Button>
              </Flex>
            </Flex>
          )}
        />
      </Flex>
      {typeof removeAction === 'function' ? (
        <ConfirmDialog
          close={() => setRemoveAction(null)}
          confirm={removeAction}
          text={{
            btnCancel: removeConfirmText('btnCancel'),
            btnOk: removeConfirmText('btnOk'),
            title: removeConfirmText('title'),
            message: removeConfirmText('message'),
          }}
        />
      ) : null}
    </>
  );
};

export default memo(AllergiesInline);
