import React, { memo, useContext, useMemo } from 'react';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientFileSidebar.json';

import I18n from '@tutum/infrastructure/i18n';
import { IPatientProfile } from '../../types/profile.type';
import { Flex, BodyTextL, Svg } from '@tutum/design-system/components';
import { Divider, Tooltip } from '@tutum/design-system/components/Core';

import { updatePatientProfileV2 } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import MvzTextmodule from '@tutum/design-system/textmodule';
import { AvoidLineBreakPlugin } from '@tutum/design-system/textmodule/plugins/AvoidLineBreak.plugin';
import { TextModuleUseFor } from '@tutum/hermes/bff/text_module_common';

export interface ICaveProps {
  className?: string;
  patient?: IPatientProfile;
  canEdit?: boolean;
  isBottomHeadMenu?: boolean;
}

const usedForList = [TextModuleUseFor.TextModuleUseFor_Cave];

const InfoIcon = '/images/icon-info.svg';

const Cave = ({
  className,
  patient,
  canEdit = true,
  isBottomHeadMenu = false,
}: ICaveProps) => {
  const { t } = I18n.useTranslation<keyof typeof PatientManagementI18n.Cave>({
    namespace: 'PatientFileSidebar',
    nestedTrans: 'Cave',
  });

  const { setPatient } = useContext(PatientManagementContext.instance);

  const { otherInfo } = patient?.patientInfo || {};

  const debouncedUpdateCave = useMemo(() => {
    return debounce(async (text: string) => {
      const clonedPatient = cloneDeep(patient)!;

      if (text !== clonedPatient.patientInfo.otherInfo.cave) {
        clonedPatient.patientInfo.otherInfo.cave = text;
        await updatePatientProfileV2(clonedPatient);
        setPatient(clonedPatient);
      }
    }, 1000);
  }, [patient]);

  return (
    <>
      <Flex column className={className}>
        <BodyTextL className="sl-cave-title" fontWeight={600}>
          {t('cave')}
        </BodyTextL>
        <Flex className="sl-cave-body">
          <Tooltip className="sl-info-icon">
            <Svg src={InfoIcon} alt="info-icon" style={{ width: 16 }} />
          </Tooltip>
          <MvzTextmodule
            isViewOnly={!canEdit}
            usedForList={usedForList}
            defaultValue={otherInfo?.cave}
            className="sl-editable-text"
            placeholder={t('cavePlaceholder')}
            onContentChange={({ text }) => debouncedUpdateCave(text)}
            isBottomHeadMenu={isBottomHeadMenu}
          >
            <AvoidLineBreakPlugin />
          </MvzTextmodule>
        </Flex>
      </Flex>
      <Divider className="side-bar__divider" />
    </>
  );
};

export default memo(Cave);
