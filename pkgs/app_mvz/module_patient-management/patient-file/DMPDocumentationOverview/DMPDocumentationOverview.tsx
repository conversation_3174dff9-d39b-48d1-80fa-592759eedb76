import React, {
  useState,
  useContext,
  useMemo,
  useEffect,
  useCallback,
  useRef,
} from 'react';
import {
  alertError,
  alertSuccessfully,
  BodyTextM,
  Button,
  Dialog,
  Flex,
  H2,
  InfoConfirmDialog,
  LoadingState,
  PDFDownloadFile,
  Svg,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import type DMPI18n from '@tutum/mvz/locales/en/DMP.json';
import PatientFileSidebarLocales from '@tutum/mvz/locales/en/PatientFileSidebar.json';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { handleShowPatientNumber } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import LeftPanel from './LeftPanel';
import RightPanel from './RightPanel';
import {
  patientFileActions,
  patientFileStore,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  DocumentStatus,
  DocumentType,
  DoctorRelationType,
  EnrollmentDocumentInfoModel,
  HeaderStatus,
  FieldValidationResult,
  FieldValidationResultType,
  ErrorType,
  DocumentationOverview,
  Field,
  FieldType,
  EnrollStatus,
  DMPBillingFile,
  Header,
  ParticipationFormsStatus,
} from '@tutum/hermes/bff/edmp_common';
import PatientManagementUtil from '@tutum/infrastructure/utils/form.util';
import {
  FilteredDocument,
  MAXIMUM_RETRY_CHECK_PLAUSIBILITY,
  convertDocumentValues,
  convertToFormValues,
  getFieldNameWithoutSpecialCharacter,
  handleScrollToFirstError,
  onPrintPHQ9Form,
  specialFields,
} from './helpers';
import PrintPreviewContent from './PrintPreviewContent';
import {
  PatientProfileResponse,
  getPatientFormProfile,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import useDMPData from '@tutum/mvz/hooks/useDMPData';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import {
  CheckPlausibilityResponse,
  useMutationCheckPlausibility,
  useQueryGetDMPDocument,
  useMutationCreateDocument,
  useMutationSaveDocumentationOverview,
  useMutationFinishDocumentationOverview,
  UpdateDocumentationOverviewRequest,
  CreateDocumentRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import { cloneDeep, debounce, groupBy, isEmpty } from 'lodash';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { ErrorField } from '@tutum/design-system/components/ErrorHelpText/component';
import NavigationPatients from '@tutum/mvz/module_mobile-card-reader/multi-patient-dialog/NavigationPatients';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import {
  DMPBillingFileWitFullURL,
  getDownloadUrlForDmpBillingFile,
} from '@tutum/mvz/module_dmp-billing/dmp.helper';
import { formatBirthday } from '@tutum/mvz/_utils/formatBirthday';
import { FormName } from '@tutum/hermes/bff/form_common';
import { FORM_SETTING_OBJECT } from '@tutum/mvz/constant/form';
import DMPPrograms from '@tutum/design-system/consts/DMP-programs';
import { MainGroup, ScheinStatus } from '@tutum/hermes/bff/schein_common';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  HiddenListByFieldName,
  radioBtnEdQuery,
  radioBtnFdQuery,
} from './constant';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { useMutationIsDMPCaseNumberExist } from '@tutum/hermes/bff/legacy/app_mvz_edmp';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';

enum ActionsType {
  CHECK = 'checkPlausibility',
  SAVE = 'save',
  PRINT = 'print',
  FINISH_PRINT = 'finishPrint',
  FINISH = 'finish',
}

export interface DMPDocumentationOverviewProps {
  className?: string;
  isOpen: boolean;
  isReadOnly?: boolean;
  isBilling?: boolean;
  isLoadingCheckValidation?: boolean;
  activePatientIdx?: number;
  totalReviewingPatients?: number;
  currentDmp: string[];
  currentDocumentType?: DocumentType;
  currentDocumentationOverview?: EnrollmentDocumentInfoModel;
  patientInfo?: PatientProfileResponse;
  validationErrors?: FieldValidationResult[];
  closeModal: () => void;
  onNextPatient?: () => void;
  onPrevPatient?: () => void;
  onFinishAll?: () => void;
}

export interface LeftPanelValues {
  dmpCaseNumber: string;
  createdAt: Date;
  selectedSchein: string;
  dmpValues: Array<{
    doctor?: string;
    doctorRelation?: DoctorRelationType;
    documentationType?: string;
    documentStatus?: string;
    name?: string;
  }>;
}

const COPD_Aktueller = 'Aktueller FEV1-Wert (alle sechs bis zwölf Monate)';
const COPD_Aktueller_2023 = 'Aktueller FEV1-Wert (alle 6 bis 12 Monate)';
const prozentValue = 'Prozent des Soll-Wertes';
const nichtValue = 'Nicht durchgeführt';
const COPD_Klinische =
  'Klinische Einschätzung des Osteoporoserisikos durchgeführt';

const DMPDocumentationOverview = ({
  className,
  isOpen,
  isReadOnly = false,
  isBilling = false,
  isLoadingCheckValidation,
  activePatientIdx,
  totalReviewingPatients = 0,
  currentDmp,
  currentDocumentType,
  currentDocumentationOverview,
  patientInfo,
  validationErrors,
  closeModal,
  onNextPatient,
  onPrevPatient,
  onFinishAll,
}: DMPDocumentationOverviewProps) => {
  const {
    patientManagement: { patient: patientContext },
  } = useContext(PatientManagementContext.instance);
  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();

  const { t } = I18n.useTranslation<keyof typeof DMPI18n.documentationOverview>(
    {
      namespace: 'DMP',
      nestedTrans: 'documentationOverview',
    }
  );

  const { t: tLeftPanel } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.leftPanel
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.leftPanel',
  });

  const { t: tActions } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.actions
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.actions',
  });

  const { t: tNotiEDDialog } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.notiEDDialog
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.notiEDDialog',
  });

  const { t: tPatientFile } = I18n.useTranslation<
    keyof typeof PatientFileSidebarLocales.PatientInformationSidebar
  >({
    namespace: 'PatientFileSidebar',
    nestedTrans: 'PatientInformationSidebar',
  });

  const { t: tPH9PrintPreviewPDF } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.printPreviewPDF.pHQ9
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.printPreviewPDF.pHQ9',
  });

  const { t: tErrorMessages } = I18n.useTranslation<
    keyof typeof DMPI18n.errorMessages
  >({
    namespace: 'DMP',
    nestedTrans: 'errorMessages',
  });

  const rightPanelRef = useRef<any>(null);

  const patient = useMemo(() => {
    return (
      isBilling ? patientInfo || {} : patientContext
    ) as PatientProfileResponse;
  }, [isBilling, patientInfo, patientContext]);

  const selectedContractDoctor = useMemo(() => {
    return {
      availableDoctor: doctorList,
      doctorId: isBilling
        ? currentDocumentationOverview?.documentationOverview?.doctorId
        : currentLoggedInUser.isDoctor ? currentLoggedInUser.id : doctorList[0]?.id!,
    } as ISelectedContractDoctor;
  }, [
    isBilling,
    currentDocumentationOverview,
    currentLoggedInUser.id,
    doctorList,
  ]);

  const store = usePatientFileStore();
  const { enrollmentList } = useDMPData({
    patient,
    doctorList,
    currentLoggedInUser,
  });

  const scheinData = patientFileStore.schein.activatedSchein;

  const [patientInfoMap, setPatientInfoMap] = useState<{
    [key: string]: string;
  }>({});
  const [leftPanelValues, setLeftPanelValues] = useState<LeftPanelValues>({
    dmpCaseNumber: '',
    createdAt: DatetimeUtil.date(),
    selectedSchein: '',
    dmpValues: [],
  });
  const [printPreviewData, setPrintPreviewData] = useState<
    DocumentationOverview[] | null
  >(null);
  const [initValueRightPanel, setInitValuesRightPanel] = useState<{
    [keyMapping: string]: string;
  }>({});
  const [headersError, setHeadersError] = useState<{
    [keyMapping: string]: FieldValidationResult[];
  }>({});
  const [fieldsError, setFieldsError] = useState<{
    [keyMapping: string]: ErrorField[] | null;
  }>({});
  const [fieldsWarning, setFieldsWarning] = useState<{
    [keyMapping: string]: ErrorField[] | null;
  }>({});
  const [schemasError, setSchemasError] = useState<FieldValidationResult[]>([]);
  const [fieldsErrorChangeField, setFieldsErrorChangeField] = useState<{
    [keyMapping: string]: ErrorField[];
  }>({});
  const [fieldsWarningChangeField, setFieldsWarningChangeField] = useState<{
    [keyMapping: string]: ErrorField[];
  }>({});
  const [fieldsWarningCustom, setFieldsWarningCustom] = useState<{
    [keyMapping: string]: ErrorField[] | null;
  }>({});
  const [isLoading, setLoading] = useState<boolean>(false);
  const [isOpenErrorLogDialog, setOpenErrorLogDialog] =
    useState<boolean>(false);
  const [errorLogs, setErrorLogs] = useState<DMPBillingFileWitFullURL[]>([]);
  const [currentDocumentDate, setCurrentDocumentDate] = useState<number>(
    currentDocumentationOverview
      ? currentDocumentationOverview.documentationOverview.documentDate
      : +DatetimeUtil.date()
  );
  const [
    currentDocumentationOverviewFinishED,
    setCurrentDocumentationOverviewFinishED,
  ] = useState<EnrollmentDocumentInfoModel | undefined>(undefined);
  const [formValue, setFormValue] = useState<FORM_SETTING_OBJECT>({});
  const [finishEDData, setFinishEDData] = useState<{
    action: ActionsType;
    payload: {
      documentationOverview: DocumentationOverview;
    };
  } | null>(null);
  const [hiddenFieldNames, setHiddenFieldNames] =
    useState<HiddenListByFieldName>({});
  const [hiddenHeaderNames, setHiddenHeaderNames] =
    useState<HiddenListByFieldName>({});
  const [isSavePHQ9Flow, setSavePHQ9Flow] = useState<boolean>(true);

  const currentSchein = useCurrentSchein();

  const {
    data: dMPDocument,
    isFetching: isLoadingDMPDocument,
    error: errorDMPDocument,
    isSuccess,
  } = useQueryGetDMPDocument(
    {
      dMPLabelingValues: currentDocumentationOverview
        ? [currentDocumentationOverview?.documentationOverview.dMPLabelingValue]
        : currentDmp,
      documentDate: DatetimeUtil.date(currentDocumentDate),
    },
    {
      select: (res) => res.data.dMPDocument,
      enabled: undefined,
    }
  );

  const checkPlausibility = useMutationCheckPlausibility();
  const createDocument = useMutationCreateDocument();
  const saveDocumentationOverview = useMutationSaveDocumentationOverview();
  const finishDocumentationOverview = useMutationFinishDocumentationOverview();

  const isLoadingCheckPlausibility = useMemo(() => {
    return (
      isLoading ||
      checkPlausibility.isPending ||
      createDocument.isPending ||
      saveDocumentationOverview.isPending ||
      finishDocumentationOverview.isPending
    );
  }, [
    isLoading,
    checkPlausibility.isPending,
    createDocument.isPending,
    saveDocumentationOverview.isPending,
    finishDocumentationOverview.isPending,
  ]);

  const isFinishED = useMemo(() => {
    if (!currentDocumentationOverviewFinishED) {
      return false;
    }

    const {
      documentationOverview: { documentType, documentStatus },
    } = currentDocumentationOverviewFinishED;

    return (
      documentType === DocumentType.DocumentType_ED &&
      !!documentStatus &&
      [
        DocumentStatus.DocumentStatus_Finished,
        DocumentStatus.DocumentStatus_Printed,
      ].includes(documentStatus)
    );
  }, [currentDocumentationOverviewFinishED]);

  const documentationActive = useMemo(() => {
    if (leftPanelValues.dmpValues.length === 1) {
      return leftPanelValues.dmpValues[0];
    }

    return leftPanelValues.dmpValues.find((item) => !!item.documentationType);
  }, [leftPanelValues.dmpValues]);

  const pHQ9FileName = useMemo(() => {
    // TODO get url by selected quarter
    return (
      currentDocumentationOverview?.documentationOverview?.pHQ9?.fileName ||
      'PHQ_9_Q3_2023'
    );
  }, [currentDocumentationOverview]);

  const isPHQ9Type = useMemo(() => {
    return [
      DocumentType.DocumentType_PHQ9_ED,
      DocumentType.DocumentType_PHQ9_FD,
    ].includes(documentationActive?.documentationType as DocumentType);
  }, [documentationActive]);

  const filteredDMPDocumentByType: FilteredDocument | null = useMemo(() => {
    if (!documentationActive?.documentationType || !isSuccess) {
      return null;
    }

    const matchedDocument = dMPDocument.find(
      (document) => document.dMPValue === documentationActive.name
    );

    const documentHeaders =
      documentationActive?.documentationType === DocumentType.DocumentType_ED
        ? cloneDeep(matchedDocument?.headersED)
        : documentationActive?.documentationType ===
          DocumentType.DocumentType_FD
          ? cloneDeep(matchedDocument?.headersFD)
          : documentationActive?.documentationType ===
            DocumentType.DocumentType_PD
            ? cloneDeep(matchedDocument?.headersPD)
            : null;

    if (!documentHeaders) {
      return null;
    }

    // custom document for Depression
    const isPHQ = documentationActive.name === '08';
    let tempFields: Header | undefined = undefined;

    if (isPHQ) {
      const cloneData = cloneDeep(documentHeaders);
      tempFields = cloneData.find(
        (document) => document.name === 'Anamnese- und Befunddaten'
      );

      if (tempFields) {
        tempFields.dMPs = tempFields.dMPs.map((dMP) => {
          if (dMP.dMPLabelingValue === '08') {
            dMP.fields = dMP.fields.filter((field) =>
              specialFields.includes(field.name)
            );
          }

          return dMP;
        });
      }
    }

    const activeHeaders = documentHeaders.reduce(
      (activeHeaders: Header[], header) => {
        let dMPs = header.dMPs.filter(
          (dMP) => dMP.dMPLabelingValue === documentationActive.name
        );

        if (dMPs[0]) {
          if (isPHQ) {
            dMPs = dMPs.map((dMP) => {
              dMP.fields = dMP.fields.filter(
                (field) => !specialFields.includes(field.name)
              );
              return dMP;
            });
          }

          activeHeaders.push({
            ...header,
            dMPs,
          });
        }

        return activeHeaders;
      },
      []
    );

    if (isPHQ && tempFields) {
      const index = documentHeaders.findIndex(
        (document) => document.name === 'Behandlungsplanung'
      );
      activeHeaders.splice(index, 0, tempFields);
    }

    const hiddenFieldNamesValues = Object.values(hiddenFieldNames)?.flat();
    const hiddenHeaderNamesValues = Object.values(hiddenHeaderNames)?.flat();

    const filteredData = activeHeaders.map((header) => {
      if (hiddenHeaderNamesValues.includes(header.name)) {
        return {
          name: header.name,
          dMPs: [],
        };
      }

      const filteredDMPs = header.dMPs.map((dMP) => {
        const filteredFields =
          dMP.fields?.filter(
            (field) =>
              field.documentType === documentationActive.documentationType &&
              !hiddenFieldNamesValues.includes(field.name)
          ) || [];
        let filteredFieldsValues: Field[] = [];
        let headerStatus = HeaderStatus.HeaderStatus_NotFilled;
        const documentOverviewData =
          currentDocumentationOverviewFinishED || currentDocumentationOverview;

        if (documentOverviewData) {
          filteredFieldsValues = (
            documentOverviewData.documentationOverview.fields || []
          ).filter((item) => {
            if (item.displayHeader && dMP.displayHeader) {
              return (
                item.displayHeader === dMP.displayHeader &&
                item.documentType === documentationActive?.documentationType
              );
            }
            return (
              item.header === dMP.headerName &&
              item.documentType === documentationActive?.documentationType
            );
          });

          if (filteredFieldsValues[0]) {
            const requiredFields = filteredFields.filter(
              (field) => field.isRequire
            );
            const requiredFieldsValues = filteredFieldsValues.filter(
              (field) => field.isRequire
            );
            const headerStatusComplete =
              filteredFields.length === filteredFieldsValues.length ||
              (requiredFields[0] &&
                requiredFields.length === requiredFieldsValues.length);

            headerStatus = headerStatusComplete
              ? HeaderStatus.HeaderStatus_Completed
              : HeaderStatus.HeaderStatus_Incomplete;
          }
        }

        return {
          ...dMP,
          headerStatus,
          fields: filteredFields,
        };
      });

      return {
        ...header,
        dMPs: filteredDMPs.filter((dMP) => !!dMP.fields?.[0]),
      };
    });

    return {
      headers: filteredData,
    };
  }, [
    dMPDocument,
    documentationActive,
    currentDocumentationOverview,
    currentDocumentationOverviewFinishED,
    rightPanelRef,
    hiddenFieldNames,
    hiddenHeaderNames,
  ]);

  const validScheins = useMemo(() => {
    const scheinList = isBilling
      ? store.schein.originalList
      : store.schein.originalList.filter(
        (s) =>
          !s?.isTechnicalSchein &&
          !s?.markedAsBilled &&
          s?.scheinStatus !== ScheinStatus.ScheinStatus_Canceled &&
          s?.scheinStatus !== ScheinStatus.ScheinStatus_Billed
      );
    const time = DatetimeUtil.dateToMoment(currentDocumentDate);
    const quarter = time.quarter();
    const year = time.year();

    return [...scheinList].filter(
      (schein) => schein.g4101Quarter === quarter && schein.g4101Year === year
    );
  }, [isBilling, currentDocumentDate, store.schein.originalList]);

  const defaultSelectSchein = useMemo(() => {
    if (currentDocumentationOverview) {
      return currentDocumentationOverview.documentationOverview.scheinId;
    }

    if (currentSchein) {
      return currentSchein.scheinId;
    }

    return '';
  }, [currentDocumentationOverview, currentSchein]);

  const isDMPCaseNumberExist = useMutationIsDMPCaseNumberExist();

  const onCheckExistedCaseNumber = useMemo(() => {
    return debounce((caseNumber: string) => {
      isDMPCaseNumberExist.mutate({
        caseNumber,
      });
    }, 500);
  }, []);

  const dMPCaseNumberError = useMemo(() => {
    return leftPanelValues.dmpCaseNumber
      ? isDMPCaseNumberExist.data?.data?.isExist
        ? tLeftPanel('caseNumberExisted')
        : ''
      : tLeftPanel('caseNumberRequired');
  }, [leftPanelValues.dmpCaseNumber, isDMPCaseNumberExist.data?.data?.isExist]);

  const hasValidSchein = useMemo(() => {
    return !!validScheins.length;
  }, [validScheins]);

  const isDisabledFinishPrint = useMemo(() => {
    return (
      (!isPHQ9Type && !filteredDMPDocumentByType) ||
      !hasValidSchein ||
      !!dMPCaseNumberError
    );
  }, [
    isPHQ9Type,
    filteredDMPDocumentByType,
    hasValidSchein,
    dMPCaseNumberError,
  ]);

  const isDisabledAction = useMemo(() => {
    return isDisabledFinishPrint || isReadOnly || isFinishED;
  }, [isDisabledFinishPrint, isReadOnly, isFinishED]);

  const matchedEnrollement = useMemo(() => {
    if (!documentationActive) {
      return undefined;
    }

    return enrollmentList.find(
      (item) =>
        item.enrollmentInfoModel.enrollmentInfo.participationForm
          .dMPLabelingValue === documentationActive.name
    );
  }, [enrollmentList, documentationActive]);

  const title = useMemo(() => {
    if (isEmpty(patient)) {
      return null;
    }

    const patientName = PatientManagementUtil.getFullName(
      patient.patientInfo?.personalInfo?.title || '',
      patient.patientInfo?.personalInfo?.intendWord || '',
      patient.patientInfo?.personalInfo?.lastName || '',
      patient.patientInfo?.personalInfo?.firstName || ''
    );
    const activeInsurance = getActiveInsurance(
      patient.patientInfo.insuranceInfos
    );
    const ikNumberText = activeInsurance
      ? `${t('ikNumber')} ${activeInsurance.ikNumber}`
      : '';
    const birthday = formatBirthday(
      tPatientFile,
      patient.patientInfo?.personalInfo?.dateOfBirth
    );
    const gender = patient?.patientInfo?.personalInfo?.gender;
    const idNumber = `${tPatientFile('ID')}: ${handleShowPatientNumber(
      patient?.patientInfo?.patientNumber
    )}`;
    const contactNumber =
      patient.patientInfo?.contactInfo?.primaryContactNumber;
    const address = patient.patientInfo?.addressInfo?.address;
    const dspAddress = [
      `${address?.street} ${address?.number}`.trim(),
      `${address?.postCode} ${address?.city}`.trim(),
    ]
      .filter((str) => str)
      .join(', ');
    const patientInfo = [
      ikNumberText,
      birthday,
      gender,
      idNumber,
      contactNumber,
      dspAddress,
    ]
      .filter((value) => value)
      .join(' • ');

    return (
      <Flex justify="space-between">
        <Flex column>
          <H2>
            {t('title')} {patientName}
          </H2>
          <BodyTextM>{patientInfo}</BodyTextM>
        </Flex>
        {isBilling && (
          <NavigationPatients
            activePatientIdx={activePatientIdx}
            totalReviewingPatients={totalReviewingPatients}
            onNextPatient={onNextPatient}
            onPrevPatient={onPrevPatient}
          />
        )}
      </Flex>
    );
  }, [
    patient,
    isBilling,
    activePatientIdx,
    totalReviewingPatients,
    onNextPatient,
    onPrevPatient,
  ]);

  const selectedDoctorInfo = useMemo(() => {
    return selectedContractDoctor.availableDoctor.find(
      (doctor) => doctor.id === selectedContractDoctor.doctorId
    );
  }, [selectedContractDoctor.availableDoctor, selectedContractDoctor.doctorId]);

  const doctorRelationList = useMemo(() => {
    return [
      {
        label: tLeftPanel('treatingDoctor'),
        value: DoctorRelationType.DoctorRelationType_Treatment,
      },
      {
        label: tLeftPanel('deputyDoctor'),
        value: DoctorRelationType.DoctorRelationType_Deputy,
      },
      {
        label: tLeftPanel('doctorChange'),
        value: DoctorRelationType.DoctorRelationType_DoctorCharge,
      },
    ];
  }, []);

  const COPDDocument = useMemo(() => {
    const COPDDocument = enrollmentList?.find(
      (enrollment) =>
        enrollment.enrollmentInfoModel.enrollmentInfo.participationForm
          .dMPLabelingValue === '06'
    );

    if (!COPDDocument || !COPDDocument.enrollmentDocumentInfoModel?.[0]) {
      return {};
    }

    const documentFinished = COPDDocument.enrollmentDocumentInfoModel
      .filter(
        (document) =>
          document.documentationOverview.documentStatus !==
          DocumentStatus.DocumentStatus_Saved
      )
      .reverse();
    const documentedED = documentFinished.find(
      (document) =>
        document.documentationOverview.documentType ===
        DocumentType.DocumentType_ED
    );
    const documentedFD = documentFinished.find(
      (document) =>
        document.documentationOverview.documentType ===
        DocumentType.DocumentType_FD
    );

    return {
      documentFinished,
      documentedED,
      documentedFD,
    };
  }, [enrollmentList]);

  const fieldsWarningShow = useMemo(() => {
    if (isEmpty(fieldsWarningCustom)) {
      return fieldsWarning;
    }

    const warning = { ...fieldsWarning };

    Object.keys(fieldsWarningCustom).forEach((key) => {
      const value = fieldsWarningCustom[key];

      if (warning[key]) {
        warning[key] = [...(warning[key] || []), ...(value || [])];
      } else {
        warning[key] = value;
      }
    });

    return warning;
  }, [fieldsWarning, fieldsWarningCustom]);

  const getDMPVersion = (dateInMilisec: number) => {
    const date = new Date(dateInMilisec);
    const year = date.getFullYear();
    const quarter = Math.floor(date.getMonth() / 3) + 1;
    return `${year}.${quarter}.0`;
  };

  const handleCheckPlausibilityOnField = useCallback(
    debounce(
      async (
        payLoad,
        callback: (resp: CheckPlausibilityResponse) => void,
        retryCheckPlaus: number
      ) => {
        try {
          const resp = await checkPlausibility.mutateAsync({
            ...payLoad,
            doctorId: documentationActive?.doctor,
            patientId: patient.id,
            dMPCaseNumber: leftPanelValues.dmpCaseNumber,
            scheinId:
              currentDocumentationOverview?.documentationOverview?.scheinId ||
              scheinData?.scheinId,
            dMPVersion: getDMPVersion(payLoad.documentDate),
          });
          callback(resp?.data);
        } catch (err) {
          const { status, headers } = err?.response || {};
          if (
            status === 400 &&
            headers['content-type'] === 'text/html' &&
            retryCheckPlaus < MAXIMUM_RETRY_CHECK_PLAUSIBILITY
          ) {
            console.log('retry check plausibility');
            handleCheckPlausibilityOnField(
              payLoad,
              callback,
              retryCheckPlaus + 1
            );
          } else {
            throw err;
          }
        }
      },
      200
    ),
    [
      patient,
      leftPanelValues.dmpCaseNumber,
      documentationActive,
      currentDocumentationOverview,
    ]
  );

  const handleError = useCallback(
    (fieldValidationResults: FieldValidationResult[], name = '') => {
      const errorsByType = fieldValidationResults.reduce(
        (errors, error) => {
          const isFieldError = !!error.fieldName;
          const isHeaderError =
            !!error.headerName &&
            filteredDMPDocumentByType?.headers?.some(
              (header) => header.name === error.headerName
            ) &&
            error.errorType !== ErrorType.ErrorType_Schema;

          if (isFieldError) {
            const headerName = filteredDMPDocumentByType?.headers.find(
              (header) => header.name === error.headerName
            );
            let matchedField;

            headerName?.dMPs.forEach((dmp) => {
              matchedField = dmp.fields.find(
                (field) => field.name === error.fieldName
              );
            });

            const fieldName = getFieldNameWithoutSpecialCharacter(
              matchedField?.fieldType === FieldType.FieldType_Nested
                ? error.fieldName.replace('/', '_') // Handle for case nested field
                : error.fieldName
            );
            const fieldError = {
              errorCode: error.errorCode,
              errorMessage:
                error.fieldValidationResultType ===
                  FieldValidationResultType.FieldValidationResultType_SelfCheck &&
                  error.errorType === ErrorType.ErrorType_Error
                  ? tErrorMessages(
                    error.errorCode as keyof typeof DMPI18n.errorMessages
                  )
                  : error.errorMessage,
              errorContent: error?.script?.content,
            };

            const keyMapping =
              error.errorType === ErrorType.ErrorType_Warning
                ? 'fieldsWarning'
                : 'fieldsError';

            if (errors[keyMapping][fieldName]) {
              errors[keyMapping][fieldName].push(fieldError);
            } else {
              errors[keyMapping][fieldName] = [fieldError];
            }
          } else if (isHeaderError) {
            errors.headersError.push(error);
          } else {
            errors.schemasError.push(error);
          }

          return errors;
        },
        {
          fieldsError: {} as {
            [keyMapping: string]: ErrorField[];
          },
          fieldsWarning: {} as {
            [keyMapping: string]: ErrorField[];
          },
          headersError: [] as FieldValidationResult[],
          schemasError: [] as FieldValidationResult[],
        }
      );

      if (name) {
        setFieldsError((prevValue) => {
          if (!Object.keys(prevValue)[0]) {
            return errorsByType.fieldsError;
          }

          if (Object.keys(errorsByType.fieldsError)[0]) {
            return {
              ...prevValue,
              ...errorsByType.fieldsError,
            };
          }

          const newValue = Object.keys(prevValue).reduce(
            (errors, fieldName) => {
              let value = prevValue[fieldName];

              if (name === fieldName || fieldsErrorChangeField[fieldName]) {
                value = null;
              }

              return {
                ...errors,
                [fieldName]: value,
              };
            },
            prevValue
          );

          return newValue;
        });
        setFieldsWarning((prevValue) => {
          if (!Object.keys(prevValue)[0]) {
            return errorsByType.fieldsWarning;
          }

          if (Object.keys(errorsByType.fieldsWarning)[0]) {
            return {
              ...prevValue,
              ...errorsByType.fieldsWarning,
            };
          }

          const newValue = Object.keys(prevValue).reduce(
            (warnings, fieldName) => {
              let value = prevValue[fieldName];

              if (name === fieldName || fieldsWarningChangeField[fieldName]) {
                value = null;
              }

              return {
                ...warnings,
                [fieldName]: value,
              };
            },
            prevValue
          );

          return newValue;
        });
        setFieldsErrorChangeField(errorsByType.fieldsError);
        setFieldsWarningChangeField(errorsByType.fieldsWarning);

        return;
      }

      setFieldsError(errorsByType.fieldsError);
      setFieldsWarning(errorsByType.fieldsWarning);
      setHeadersError(groupBy(errorsByType.headersError, 'headerName'));
      setSchemasError(errorsByType.schemasError);
      handleScrollToFirstError();
    },
    [
      filteredDMPDocumentByType,
      fieldsErrorChangeField,
      fieldsWarningChangeField,
    ]
  );

  const handlePlausibilityError = useCallback(
    async (resp: CheckPlausibilityResponse) => {
      handleError(resp.fieldValidationResults);

      const errorLogs: DMPBillingFile[] = [];

      if (resp.billingFile) {
        errorLogs.push(resp.billingFile);
      }

      if (resp.xPMPFile) {
        errorLogs.push(resp.xPMPFile);
      }

      const errorLogsUrl = await getDownloadUrlForDmpBillingFile(errorLogs);

      setErrorLogs(errorLogsUrl);
      setOpenErrorLogDialog(true);
    },
    [handleError]
  );

  const handleFinishDocument = async (
    payload: any,
    shouldCloseModal = true
  ) => {
    let response;

    if (currentDocumentationOverview?.id) {
      response = await finishDocumentationOverview.mutateAsync({
        ...currentDocumentationOverview,
        ...payload,
      });
    } else {
      response = await createDocument.mutateAsync(payload);
    }

    const checkPlausibilityResponse = response.data?.checkPlausibilityResponse;

    if (checkPlausibilityResponse && !checkPlausibilityResponse?.isPlausible) {
      alertError(tActions('checkFailed'));

      handlePlausibilityError(checkPlausibilityResponse);

      return;
    }

    alertSuccessfully(tActions('documentFinished'));
    shouldCloseModal && closeModal();
  };

  const handlePrintPHQ9Form = useCallback(
    (formData: FORM_SETTING_OBJECT) => {
      if (!documentationActive) {
        return;
      }

      const printFailure = (err: Error) => {
        setLoading(false);
        throw err;
      };

      const printSuccess = async () => {
        alertSuccessfully(tActions('printSuccessfull'));
        setLoading(false);
      };

      const request = {
        doctorId: documentationActive.doctor as string,
        patientId: patient.id,
        scheinId: leftPanelValues.selectedSchein,
        data: formData,
      };

      onPrintPHQ9Form(
        FormName[pHQ9FileName],
        request,
        printSuccess,
        printFailure
      );
    },
    [pHQ9FileName, documentationActive, patient, leftPanelValues]
  );

  const handleActionsPHQ9 = useCallback(
    async (action: ActionsType) => {
      setLoading(true);

      const formData = action === ActionsType.FINISH_PRINT ? formValue : {};

      handlePrintPHQ9Form(formData);
    },
    [formValue]
  );

  const handleFinish = useCallback(
    (
      action: ActionsType | undefined,
      payload:
        | {
          documentationOverview: DocumentationOverview;
        }
        | undefined
    ) => {
      if (action === ActionsType.FINISH) {
        handleFinishDocument(payload);

        return;
      }

      if (action === ActionsType.FINISH_PRINT && payload) {
        setPrintPreviewData([
          {
            ...payload.documentationOverview,
          },
        ]);
      }
    },
    []
  );

  const handleChangePHQ9To = useCallback(() => {
    const documentActiveType =
      documentationActive?.documentationType as DocumentType;
    if (documentActiveType === DocumentType.DocumentType_PHQ9_ED) {
      (document.querySelector(radioBtnEdQuery) as HTMLElement).click();
    }

    if (documentActiveType === DocumentType.DocumentType_PHQ9_FD) {
      (document.querySelector(radioBtnFdQuery) as HTMLElement).click();
    }
  }, [documentationActive]);

  const handleActions = useCallback(
    async (action: ActionsType, callback?: () => void) => {
      if (isPHQ9Type && action === ActionsType.PRINT) {
        handleActionsPHQ9(action);
      }

      try {
        if (!leftPanelValues.selectedSchein) {
          handleScrollToFirstError();
          return;
        }

        const _rightPanelRef = rightPanelRef?.current;
        const rightPanelValues = _rightPanelRef?.values;
        const value = !isPHQ9Type
          ? convertDocumentValues(
            filteredDMPDocumentByType as FilteredDocument,
            rightPanelValues || {}
          )
          : undefined;

        const payload = {
          documentationOverview: {
            enrollmentId: matchedEnrollement?.enrollmentInfoModel?.id || '',
            bsnrCode: undefined,
            bsnrId: undefined,
            documentType:
              documentationActive?.documentationType as DocumentType,
            scheinId: leftPanelValues.selectedSchein,
            dMPCaseNumber: leftPanelValues.dmpCaseNumber,
            dMPLabelingValue: documentationActive?.name || '',
            treatmentDoctorId: documentationActive?.doctor || '',
            doctorId: selectedContractDoctor.doctorId || '',
            doctorRelationType:
              documentationActive?.doctorRelation as DoctorRelationType,
            documentStatus: '' as DocumentStatus,
            enrollStatus: '' as EnrollStatus,
            patientId: patient.id,
            fields: value,
            documentDate: isPHQ9Type
              ? +DatetimeUtil.date()
              : +rightPanelValues?.documentDate,
            dMPBillingFile: currentDocumentationOverview
              ? currentDocumentationOverview.documentationOverview
                .dMPBillingFile
              : ({} as DMPBillingFile),
            pHQ9: isPHQ9Type
              ? {
                fileName: pHQ9FileName,
                scores: Object.keys(formValue)
                  .sort()
                  .reduce((scores: number[], key) => {
                    if (!key.includes('question_')) {
                      return scores;
                    }

                    const value = (formValue[key] as number) || -1;

                    return [...scores, value];
                  }, []),
                totals: [
                  +formValue['label_total'],
                  +formValue['label_total1'],
                  +formValue['label_total2'],
                  +formValue['label_total3'],
                ],
              }
              : undefined,
          },
        };

        if (action === ActionsType.SAVE) {
          payload.documentationOverview.documentStatus =
            DocumentStatus.DocumentStatus_Saved;

          if (currentDocumentationOverview?.id) {
            await saveDocumentationOverview.mutateAsync({
              ...currentDocumentationOverview,
              ...payload,
            });
          } else {
            await createDocument.mutateAsync(payload);
          }

          alertSuccessfully(tActions('documentSaved'));

          if (callback) {
            callback();
            return;
          }

          closeModal();

          return;
        }

        if (isPHQ9Type) {
          if ([ActionsType.FINISH, ActionsType.FINISH_PRINT].includes(action)) {
            const status =
              action === ActionsType.FINISH
                ? DocumentStatus.DocumentStatus_Finished
                : DocumentStatus.DocumentStatus_Printed;

            payload.documentationOverview.documentStatus = status;
            const shouldCloseModal = action !== ActionsType.FINISH;
            setSavePHQ9Flow(false);
            await handleFinishDocument(payload, shouldCloseModal);
          }

          if (action === ActionsType.FINISH_PRINT) {
            handleActionsPHQ9(action);
          }

          if (action === ActionsType.FINISH) {
            handleChangePHQ9To();
          }

          return;
        }

        _rightPanelRef?.handleSubmit();

        // waiting for check validation in right panel
        await new Promise((resolve) => setTimeout(resolve));

        if (!(rightPanelRef?.current as any)?.isValid) {
          handleScrollToFirstError();
          return;
        }

        if ([ActionsType.FINISH_PRINT].includes(action) && isDisabledAction) {
          handleFinish(action, payload);
          return;
        }

        await handleCheckPlausibilityOnField(
          {
            relatedFields: convertDocumentValues(
              filteredDMPDocumentByType as FilteredDocument,
              rightPanelValues || {},
              true
            ),
            field: null,
            documentType:
              documentationActive?.documentationType as DocumentType,
            dMPLabelingValue: documentationActive?.name,
            doctorRelationType: documentationActive?.doctorRelation,
            documentDate: +rightPanelValues.documentDate,
            enrollmentId: matchedEnrollement?.enrollmentInfoModel?.id || '',
            ...(currentDocumentationOverview?.id && {
              documentId: currentDocumentationOverview.id,
            }),
          },
          async (resp: CheckPlausibilityResponse) => {
            if (!resp.isPlausible) {
              alertError(tActions('checkFailed'));

              handlePlausibilityError(resp);

              return;
            }

            const headersError = resp.fieldValidationResults.filter(
              (error) =>
                !error.fieldName &&
                error.errorType === ErrorType.ErrorType_Warning
            );

            handleError(resp.fieldValidationResults);
            setHeadersError(groupBy(headersError, 'headerName'));

            if (action == ActionsType.CHECK) {
              alertSuccessfully(tActions('checkSuccessful'));
            }

            if (
              [ActionsType.FINISH, ActionsType.FINISH_PRINT].includes(action)
            ) {
              payload.documentationOverview.documentStatus =
                action === ActionsType.FINISH_PRINT
                  ? DocumentStatus.DocumentStatus_Printed
                  : DocumentStatus.DocumentStatus_Finished;

              if (
                documentationActive &&
                documentationActive.documentationType ===
                DocumentType.DocumentType_ED &&
                matchedEnrollement?.enrollmentInfoModel.enrollmentInfo
                  .participationForm.participationFormsStatus !==
                ParticipationFormsStatus.ParticipationFormsStatus_Print &&
                !isDisabledAction
              ) {
                setFinishEDData({
                  action,
                  payload,
                });
                return;
              }

              handleFinish(action, payload);

              return;
            }
          },
          1
        );
      } catch (err) {
        const isExistedEDType =
          err?.serverError === ErrorCode.ErrorCode_EDMP_ED_Exist;
        const message = tActions(
          isExistedEDType ? 'existedEDTypeError' : 'checkFailed'
        );

        if (
          err?.validationErrors?.some((error) => error.field === 'DocumentDate')
        ) {
          rightPanelRef.current.setFieldError(
            'documentDate',
            tActions('documentationDateRequired')
          );
          rightPanelRef.current.setFieldTouched('documentDate', true);

          const element = document.querySelector(`label[name='documentDate']`);

          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }

          return;
        }

        console.error(err);
        alertError(message);
      }
    },
    [
      isReadOnly,
      isPHQ9Type,
      leftPanelValues,
      documentationActive,
      selectedContractDoctor.doctorId,
      patient.id,
      matchedEnrollement,
      filteredDMPDocumentByType,
      formValue,
      handleCheckPlausibilityOnField,
      handlePlausibilityError,
      handleFinishDocument,
      handleActionsPHQ9,
      handleChangePHQ9To,
    ]
  );

  const handlePrintCustomPdf = useCallback(async () => {
    if (isDisabledAction) {
      closeModal();
      return;
    }

    const printData = (printPreviewData?.[0] || {}) as DocumentationOverview;

    printData.documentStatus = DocumentStatus.DocumentStatus_Printed;

    handleFinishDocument({
      documentationOverview: {
        ...printData,
      },
    });
  }, [
    isDisabledAction,
    printPreviewData,
    currentDocumentationOverview,
    handleFinishDocument,
  ]);

  const onClosePrintPreview = useCallback(() => {
    setPrintPreviewData(null);
  }, []);

  const fetchPatientInfo = useCallback(async () => {
    if (!scheinData || !patient || !selectedDoctorInfo) {
      return;
    }

    const resp = await getPatientFormProfile({
      patientID: patient.id,
      doctorID: selectedDoctorInfo.id as string,
      scheinId: scheinData?.scheinId,
    });

    setPatientInfoMap(resp.data.profile);
  }, [patient, selectedDoctorInfo, scheinData]);

  const handleBindDefaultValueForDepression = useCallback(() => {
    if (!matchedEnrollement?.enrollmentDocumentInfoModel) {
      return;
    }

    const cloneData = cloneDeep(
      matchedEnrollement.enrollmentDocumentInfoModel
    ).reverse();
    const currentDocumentedPHQ9 = cloneData.find((document) =>
      [
        DocumentType.DocumentType_PHQ9_ED,
        DocumentType.DocumentType_PHQ9_FD,
      ].includes(document.documentationOverview.documentType)
    );

    if (!currentDocumentedPHQ9) {
      return;
    }

    if (
      !currentDocumentationOverview &&
      [
        DocumentType.DocumentType_PHQ9_ED,
        DocumentType.DocumentType_PHQ9_FD,
      ].includes(documentationActive?.documentationType as DocumentType)
    ) {
      const previousScore = currentDocumentedPHQ9.documentationOverview.pHQ9
        ?.totals[0] as number;

      setFormValue((prevValues) => ({
        ...prevValues,
        label_total: '0',
        label_total1: '0',
        label_total2: '0',
        label_total3: '0',
        label_previous_score: previousScore,
        label_previous_total: tPH9PrintPreviewPDF('previousScore', {
          value: previousScore,
        }),
      }));
    }

    if (
      ![DocumentType.DocumentType_ED, DocumentType.DocumentType_FD].includes(
        documentationActive?.documentationType as DocumentType
      )
    ) {
      return;
    }

    setInitValuesRightPanel((prevValues) => ({
      ...prevValues,
      label_previous_score: String(
        currentDocumentedPHQ9.documentationOverview.pHQ9?.previousScore
      ),
      'Aktuelle Symptomatik - PHQ-9 Summenwert': String(
        currentDocumentedPHQ9.documentationOverview.pHQ9?.totals[0]
      ),
    }));
  }, [matchedEnrollement, documentationActive, currentDocumentationOverview]);

  const handleNewData = useCallback(() => {
    const dmpValues = currentDmp.map((dmp, index) => {
      return {
        doctor: selectedContractDoctor.doctorId,
        doctorRelation: DoctorRelationType.DoctorRelationType_Treatment,
        documentationType: (!index && currentDocumentType) || '',
        documentStatus: '',
        name: dmp,
      };
    });

    setLeftPanelValues((prevValues) => ({
      ...prevValues,
      dmpValues,
    }));
  }, [selectedContractDoctor, JSON.stringify(currentDmp), currentDocumentType]);

  const handleExistedData = useCallback(
    async (documentOverviewData: EnrollmentDocumentInfoModel) => {
      const dmpValue = {
        doctor: documentOverviewData?.documentationOverview.doctorId,
        doctorRelation:
          documentOverviewData?.documentationOverview.doctorRelationType ||
          DoctorRelationType.DoctorRelationType_Treatment,
        documentationType:
          documentOverviewData?.documentationOverview.documentType,
        documentStatus:
          documentOverviewData?.documentationOverview.documentStatus,
        name: documentOverviewData?.documentationOverview.dMPLabelingValue,
      };
      setLeftPanelValues((prevValues) => {
        const dmpValues = prevValues.dmpValues[1]
          ? prevValues.dmpValues.map((value) => {
            if (
              value.name ===
              documentOverviewData?.documentationOverview
                .dMPLabelingValue &&
              value.documentationType ===
              documentOverviewData?.documentationOverview.documentType
            ) {
              return dmpValue;
            }
            return value;
          })
          : [dmpValue];

        return {
          ...prevValues,
          createdAt: documentOverviewData?.createdAt
            ? new Date(documentOverviewData.createdAt)
            : DatetimeUtil.date(),
          selectedSchein:
            documentOverviewData?.documentationOverview.scheinId || '',
          dmpValues,
        };
      });

      if (documentOverviewData?.documentationOverview?.pHQ9) {
        return;
      }

      const { documentDate } =
        documentOverviewData?.documentationOverview || {};

      let formValues = convertToFormValues(
        documentOverviewData?.documentationOverview.fields || []
      );

      const f = (documentOverviewData.documentationOverview.fields || []).find(
        (f) => f.name === 'Körpergröße'
      );

      if (f && f.values && f.values[0].value && f.unit === 'm') {
        const heightString = Math.round(
          Number(f.values[0].value) * 100
        ).toString();
        formValues['Körpergröße'] = heightString;
      }

      formValues = {
        ...formValues,
        documentDate: documentDate ? new Date(documentDate) : null,
      };
      setInitValuesRightPanel(formValues);
    },
    []
  );

  const handleCheckCOPDAktuellerField = (field: Field, values) => {
    const value = values[field.name];
    const documentDate = values.documentDate || 0;
    const { documentFinished, documentedED, documentedFD } = COPDDocument;
    const isProzentValue = value === prozentValue;
    const documentHasProzentValue = documentFinished?.find(
      (document) =>
        document.documentationOverview.documentDate <= documentDate &&
        document.documentationOverview?.fields?.find(
          (field) =>
            [COPD_Aktueller, COPD_Aktueller_2023].includes(field.name) &&
            field.values?.[0].value !== nichtValue
        )
    );

    if (isProzentValue) {
      if (!documentHasProzentValue) {
        setFieldsWarningCustom((prevValues) => {
          return {
            ...prevValues,
            [field.name]: [
              {
                errorCode: 'Rule_COPD_Aktueller_No_Previous_Document_Warning',
                errorMessage: tErrorMessages(
                  'Rule_COPD_Aktueller_No_Previous_Document_Warning'
                ),
              },
            ],
          };
        });

        return;
      }

      if (documentedED && !documentedFD) {
        setFieldsWarningCustom((prevValues) => {
          return {
            ...prevValues,
            [field.name]: [
              {
                errorCode: 'Rule_COPD_Aktueller_First_FD_Warning',
                errorMessage: tErrorMessages(
                  'Rule_COPD_Aktueller_First_FD_Warning',
                  {
                    documentDate: DatetimeUtil.dateTimeFormat(
                      documentedED.documentationOverview.documentDate,
                      DATE_FORMAT
                    ),
                    value: (
                      documentedED.documentationOverview.fields || []
                    ).find((field) =>
                      [COPD_Aktueller, COPD_Aktueller_2023].includes(field.name)
                    )?.values?.[0].value,
                  }
                ),
              },
            ],
          };
        });

        return;
      }

      if (documentedFD) {
        setFieldsWarningCustom((prevValues) => {
          return {
            ...prevValues,
            [field.name]: [
              {
                errorCode: 'Rule_COPD_Aktueller_After_First_FD_Warning',
                errorMessage: tErrorMessages(
                  'Rule_COPD_Aktueller_After_First_FD_Warning',
                  {
                    documentDate: DatetimeUtil.dateTimeFormat(
                      documentedFD.documentationOverview.documentDate,
                      DATE_FORMAT
                    ),
                    value: (
                      documentedFD.documentationOverview.fields || []
                    ).find((field) =>
                      [COPD_Aktueller, COPD_Aktueller_2023].includes(field.name)
                    )?.values?.[0].value,
                  }
                ),
              },
            ],
          };
        });

        return;
      }

      return;
    }

    const isNichtValue = value === nichtValue;

    if (isNichtValue) {
      if (!documentHasProzentValue) {
        setFieldsWarningCustom((prevValues) => {
          return {
            ...prevValues,
            [field.name]: [
              {
                errorCode: 'Rule_COPD_Aktueller_Never_Select_Value_Warning',
                errorMessage: tErrorMessages(
                  'Rule_COPD_Aktueller_Never_Select_Value_Warning'
                ),
              },
            ],
          };
        });

        return;
      }

      const countMonth = DatetimeUtil.diff(
        documentDate,
        documentHasProzentValue.documentationOverview.documentDate,
        'months'
      );

      if (countMonth > 9) {
        setFieldsWarningCustom((prevValues) => {
          return {
            ...prevValues,
            [field.name]: [
              {
                errorCode: 'Rule_COPD_Aktueller_Nicht_Option_Warning',
                errorMessage: tErrorMessages(
                  'Rule_COPD_Aktueller_Nicht_Option_Warning',
                  {
                    value: countMonth,
                  }
                ),
              },
            ],
          };
        });
      }
    }
  };

  const handleCheckCOPDKlinischeField = (field: Field, values) => {
    const value = values[field.name];
    const documentDate = values.documentDate || 0;
    const { documentFinished } = COPDDocument;
    const isJa = value === 'Ja';

    if (isJa) {
      setFieldsWarningCustom((prevValues) => {
        return {
          ...prevValues,
          [COPD_Klinische]: null,
        };
      });

      return;
    }

    const documentHasJaValue = documentFinished?.find(
      (document) =>
        document.documentationOverview.documentDate <= documentDate &&
        document.documentationOverview?.fields?.find(
          (field) =>
            field.name === COPD_Klinische && field.values?.[0].value === 'Ja'
        )
    );

    if (documentHasJaValue) {
      const countMonth = DatetimeUtil.diff(
        documentDate,
        documentHasJaValue.documentationOverview.documentDate,
        'months'
      );

      if (countMonth > 9) {
        setFieldsWarningCustom((prevValues) => {
          return {
            ...prevValues,
            [COPD_Klinische]: [
              {
                errorCode: 'Rule_COPD_Klinische_Nicht_Option_Warning',
                errorMessage: tErrorMessages(
                  'Rule_COPD_Klinische_Nicht_Option_Warning',
                  {
                    value: countMonth,
                  }
                ),
              },
            ],
          };
        });
      }

      return;
    }

    setFieldsWarningCustom((prevValues) => {
      return {
        ...prevValues,
        [COPD_Klinische]: [
          {
            errorCode: 'Rule_COPD_Klinische_Never_Option_Warning',
            errorMessage: tErrorMessages(
              'Rule_COPD_Klinische_Never_Option_Warning'
            ),
          },
        ],
      };
    });
  };

  const onChangeField = async (field: Field, values) => {
    if ([COPD_Aktueller, COPD_Aktueller_2023].includes(field.name)) {
      handleCheckCOPDAktuellerField(field, values);
      return;
    }

    if (field.name === COPD_Klinische) {
      handleCheckCOPDKlinischeField(field, values);
      return;
    }

    await handleCheckPlausibilityOnField(
      {
        relatedFields: convertDocumentValues(
          filteredDMPDocumentByType as FilteredDocument,
          values || {},
          true
        ),
        field,
        documentType: documentationActive?.documentationType as DocumentType,
        dMPLabelingValue: documentationActive?.name,
        doctorRelationType: documentationActive?.doctorRelation,
        documentDate: +values.documentDate,
        enrollmentId: matchedEnrollement?.enrollmentInfoModel?.id || '',
        ...(currentDocumentationOverview?.id && {
          documentId: currentDocumentationOverview.id,
        }),
      },
      async (resp: CheckPlausibilityResponse) => {
        handleError(resp.fieldValidationResults, field.name);
      },
      3
    );
  };

  useEffect(() => {
    fetchPatientInfo();
  }, [fetchPatientInfo]);

  useEffect(() => {
    if (currentDocumentationOverview) {
      handleExistedData(currentDocumentationOverview);

      return;
    }

    handleNewData();
  }, [currentDocumentationOverview, handleExistedData, handleNewData]);

  useEffect(() => {
    if (currentDocumentationOverviewFinishED) {
      setCurrentDocumentDate(
        currentDocumentationOverviewFinishED.documentationOverview.documentDate
      );
      handleExistedData(currentDocumentationOverviewFinishED);
    }
  }, [currentDocumentationOverviewFinishED, handleExistedData]);

  useEffect(() => {
    if (isBilling && patientInfo) {
      patientFileActions.schein.getScheinsOverview(patientInfo?.id);
    }
  }, [isBilling, patientInfo]);

  useEffect(() => {
    if (validationErrors) {
      handleError(validationErrors);
    }
  }, [validationErrors]);

  useEffect(() => {
    if (
      !documentationActive ||
      !DMPPrograms.checkAllowPHQ9Type(documentationActive.name)
    ) {
      return;
    }

    handleBindDefaultValueForDepression();
  }, [documentationActive, handleBindDefaultValueForDepression]);

  return (
    <Dialog
      className={className}
      isOpen={isOpen}
      title={title}
      size="full"
      actions={
        <>
          {!isPHQ9Type && (
            <Button
              minimal
              outlined
              intent="primary"
              large
              loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
              disabled={isDisabledAction}
              onClick={() => handleActions(ActionsType.CHECK)}
            >
              {tActions('checkPlausibility')}
            </Button>
          )}
          <Button
            minimal
            outlined
            intent="primary"
            large
            loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
            disabled={isDisabledAction}
            onClick={() => handleActions(ActionsType.SAVE)}
          >
            {tActions(currentDocumentationOverview?.id ? 'update' : 'save')}
          </Button>
          {isPHQ9Type && (
            <Button
              minimal
              outlined
              intent="primary"
              large
              loading={isLoadingCheckPlausibility}
              disabled={isLoadingCheckPlausibility || isDisabledAction}
              onClick={() => handleActions(ActionsType.PRINT)}
            >
              {tActions('print')}
            </Button>
          )}
          <Button
            minimal
            outlined
            intent="primary"
            large
            loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
            disabled={isDisabledFinishPrint}
            onClick={() => handleActions(ActionsType.FINISH_PRINT)}
          >
            {tActions('finishPrint')}
          </Button>
          <Button
            minimal={isBilling}
            outlined={isBilling}
            intent="primary"
            large
            loading={isLoadingCheckPlausibility || isLoadingCheckValidation}
            disabled={isDisabledAction}
            onClick={() => handleActions(ActionsType.FINISH)}
          >
            {tActions('finish')}
          </Button>
          {isBilling && (
            <Button
              intent="primary"
              large
              loading={isLoadingCheckValidation}
              onClick={onFinishAll}
            >
              {tActions('finishAll')}
            </Button>
          )}
        </>
      }
      onClose={closeModal}
    >
      <Flex h="100%">
        <LeftPanel
          rightPanelRef={rightPanelRef}
          isReadOnly={isReadOnly || isBilling}
          isFinishED={!!isFinishED}
          scheinList={validScheins}
          doctorList={selectedContractDoctor.availableDoctor}
          doctorRelationList={doctorRelationList}
          leftPanelValues={leftPanelValues}
          enrollmentList={enrollmentList}
          patient={patient}
          matchedEnrollement={matchedEnrollement}
          dataMapping={filteredDMPDocumentByType}
          defaultSelectSchein={defaultSelectSchein}
          dMPCaseNumberError={dMPCaseNumberError}
          documentationActive={documentationActive}
          currentDocumentDate={currentDocumentDate}
          isCreate={!currentDocumentationOverview}
          isLoading={isLoadingCheckPlausibility}
          isSavePHQ9Flow={isSavePHQ9Flow}
          setSavePHQ9Flow={setSavePHQ9Flow}
          setLeftPanelValues={(newValue) => {
            setLeftPanelValues((prevValues) => ({
              ...prevValues,
              ...newValue,
            }));
          }}
          setCurrentDocumentationOverviewFinishED={
            setCurrentDocumentationOverviewFinishED
          }
          handleResetState={() => {
            setInitValuesRightPanel({});
            setFormValue({});
            setHeadersError({});
            setFieldsWarning({});
            setFieldsError({});
            setSchemasError([]);
            setFieldsWarningChangeField({});
            setFieldsWarningCustom({});
            setFieldsErrorChangeField({});
          }}
          handleChangePHQ9To={handleChangePHQ9To}
          onCheckExistedCaseNumber={onCheckExistedCaseNumber}
          onSavePHQ9={(callback: () => void) =>
            handleActions(ActionsType.SAVE, callback)
          }
        />
        <RightPanel
          isLoading={isLoadingDMPDocument}
          isReadOnly={isReadOnly || isFinishED}
          initValues={initValueRightPanel}
          contentRef={rightPanelRef}
          dataMapping={filteredDMPDocumentByType}
          headersError={headersError}
          fieldsError={fieldsError}
          fieldsWarning={fieldsWarningShow}
          schemasError={schemasError}
          currentDocumentDate={currentDocumentDate}
          documentationActive={documentationActive}
          isPHQ9Type={isPHQ9Type}
          fileName={pHQ9FileName}
          pHQ9Data={
            currentDocumentationOverview?.documentationOverview?.pHQ9 || null
          }
          formValue={formValue}
          errorDMPDocument={(errorDMPDocument as any)?.response?.data}
          onChangeFormValue={(formValue) => {
            setFormValue((prevValues) => ({
              ...prevValues,
              ...formValue,
            }));
          }}
          setCurrentDocumentDate={setCurrentDocumentDate}
          setHiddenFieldNames={setHiddenFieldNames}
          setHiddenHeaderNames={setHiddenHeaderNames}
          handleBindDefaultValueForDepression={
            handleBindDefaultValueForDepression
          }
          hasValidSchein={hasValidSchein}
          onChangeField={onChangeField}
        />
        {printPreviewData && (
          <PrintPreviewContent
            isOpen
            data={printPreviewData}
            dmpCaseNumber={leftPanelValues.dmpCaseNumber}
            doctorInfo={selectedDoctorInfo as IEmployeeProfile}
            patientInfoMap={patientInfoMap}
            handlePrintCustomPdf={handlePrintCustomPdf}
            onClosePrintPreview={onClosePrintPreview}
          />
        )}
        {isLoadingDMPDocument && <LoadingState />}
        <InfoConfirmDialog
          type="primary"
          isOpen={isOpenErrorLogDialog}
          title={tActions('checkFailed')}
          cancelText={tActions('close')}
          isCloseButtonShown={false}
          isConfirmButtonShown={false}
          onClose={() => setOpenErrorLogDialog(false)}
        >
          <Flex column>
            {tActions('viewErrorLog')}

            <Flex column mt={18} gap={8}>
              {errorLogs.map((file, index) => {
                return (
                  <PDFDownloadFile
                    key={index}
                    url={file.url}
                    name={file.fileName}
                  />
                );
              })}
            </Flex>
          </Flex>
        </InfoConfirmDialog>
        <InfoConfirmDialog
          type="primary"
          isOpen={!!finishEDData}
          title={tNotiEDDialog('title')}
          cancelText={tNotiEDDialog('confirmBtn')}
          isCloseButtonShown={false}
          isConfirmButtonShown={false}
          onClose={() => {
            setFinishEDData(null);
            handleFinish(finishEDData?.action, finishEDData?.payload);
          }}
        >
          <Flex column>{tNotiEDDialog('description')}</Flex>
        </InfoConfirmDialog>
      </Flex>
    </Dialog>
  );
};

export default DMPDocumentationOverview;
