import React, {
  useMemo,
  useState,
  useCallback,
  useEffect,
  ChangeEvent,
} from 'react';
import {
  Flex,
  FormGroup2,
  BodyTextS,
  ReactSelect,
  BodyTextL,
  InfoConfirmDialog,
  BodyTextM,
  PDFDownloadFile,
  IMenuItem,
  Svg,
  Button,
} from '@tutum/design-system/components';
import { DateInput } from '@tutum/design-system/components/DateTime';
import {
  DATE_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import I18n from '@tutum/infrastructure/i18n';
import type DMPI18n from '@tutum/mvz/locales/en/DMP.json';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import ContentEditorCopy from '@tutum/mvz/public/images/content-editor-copy.svg';
import HelpCircle from '@tutum/mvz/public/images/help-circle.svg';
import { COLOR } from '@tutum/design-system/themes/styles';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  Divider,
  InputGroup,
  Intent,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import { LeftPanelValues } from './DMPDocumentationOverview';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { isNil } from 'lodash';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import TransferDataDialog from './TransferDataDialog';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import {
  DocumentType,
  DocumentStatus,
  EnrollmentWithDocumentModel,
  DoctorRelationType,
  EnrollStatus,
  EnrollmentDocumentInfoModel,
} from '@tutum/hermes/bff/edmp_common';
import DMP_PROGRAMS from '@tutum/design-system/consts/DMP-programs';
import { FilteredDocument } from './helpers';
import { DMP_VALUE, radioBtnEdId, radioBtnFdId } from './constant';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  checkIsBgSchein,
  checkIsPrivateSchein,
  checkIsSvSchein,
  mappingPrivatecheinLabel,
} from '@tutum/mvz/_utils/scheinFormat';
import HelpText from '@tutum/design-system/components/ErrorHelpText';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';

const CalendarIcon = '/images/calendar-default.svg';
const CheckCircle = '/images/check.svg';
const WarningIcon = '/images/alert-circle-solid.svg';
const ErrorIcon = '/images/alert-triangle-solid.svg';

interface LeftPanelProps {
  rightPanelRef: any;
  isReadOnly: boolean;
  isFinishED: boolean;
  scheinList: ScheinItem[];
  doctorList: IEmployeeProfile[];
  doctorRelationList: IMenuItem[];
  leftPanelValues: LeftPanelValues;
  enrollmentList: EnrollmentWithDocumentModel[];
  patient: IPatientProfile;
  matchedEnrollement?: EnrollmentWithDocumentModel;
  dataMapping: FilteredDocument | null;
  defaultSelectSchein: string;
  dMPCaseNumberError: string;
  documentationActive?: {
    doctor?: string;
    doctorRelation?: DoctorRelationType;
    documentationType?: string;
    documentStatus?: string;
    name?: string;
  };
  currentDocumentDate: number;
  isCreate: boolean;
  isLoading: boolean;
  isSavePHQ9Flow: boolean;
  setSavePHQ9Flow: React.Dispatch<React.SetStateAction<boolean>>;
  setLeftPanelValues: (leftPanelValues: LeftPanelValues) => void;
  setCurrentDocumentationOverviewFinishED: (
    currentDocumentationOverview: EnrollmentDocumentInfoModel | undefined
  ) => void;
  handleResetState: () => void;
  handleChangePHQ9To: () => void;
  onCheckExistedCaseNumber: (dmpCaseNumber: string) => void;
  onSavePHQ9: (callback: () => void) => void;
}

const LeftPanel = ({
  rightPanelRef,
  isReadOnly,
  isFinishED,
  scheinList,
  doctorList,
  doctorRelationList,
  leftPanelValues,
  enrollmentList,
  patient,
  matchedEnrollement,
  dataMapping,
  defaultSelectSchein,
  dMPCaseNumberError,
  documentationActive,
  currentDocumentDate,
  isCreate,
  isLoading,
  isSavePHQ9Flow,
  setSavePHQ9Flow,
  setLeftPanelValues,
  setCurrentDocumentationOverviewFinishED,
  handleResetState,
  handleChangePHQ9To,
  onCheckExistedCaseNumber,
  onSavePHQ9,
}: LeftPanelProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.leftPanel
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.leftPanel',
  });

  const { t: tFollowupDialog } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.leftPanel.followupDialog
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.leftPanel.followupDialog',
  });

  const { t: tConfirmSavePHQ9Dialog } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.leftPanel.confirmSavePHQ9Dialog
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.leftPanel.confirmSavePHQ9Dialog',
  });

  const { t: tPostoperativeDialog } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.leftPanel.postoperativeDialog
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.leftPanel.postoperativeDialog',
  });

  const { t: tGuideDialog } = I18n.useTranslation<
    keyof typeof DMPI18n.documentationOverview.leftPanel.guideDialog
  >({
    namespace: 'DMP',
    nestedTrans: 'documentationOverview.leftPanel.guideDialog',
  });

  const { t: tCreateSchein } = I18n.useTranslation<
    keyof typeof ScheinI18n.createSchein
  >({
    namespace: 'Schein',
    nestedTrans: 'createSchein',
  });

  const { t: tKvTreatmentCaseValues } = I18n.useTranslation<
    keyof typeof ScheinI18n.generalInfo.kvTreatmentCaseValues
  >({
    namespace: 'Schein',
    nestedTrans: 'generalInfo.kvTreatmentCaseValues',
  });

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [currentIndexDMP, setCurrentIndexDMP] = useState<number | null>(null);
  const [isConfirmSavePHQ9Dialog, setConfirmSavePHQ9Dialog] =
    useState<boolean>(false);
  const [isOpenTransferDataDialog, setOpenTransferDataDialog] =
    useState<boolean>(false);
  const [isOpenGuideDialog, setOpenGuideDialog] = useState<boolean>(false);
  const [currentDocumentType, setCurrentDocumentType] = useState<
    DocumentType | ''
  >('');

  const getScheinTitle = (scheinItem: ScheinItem) => {
    if (checkIsSvSchein(scheinItem)) {
      return `${scheinItem.scheinMainGroup}`;
    }

    const kvTreatmentCase = tKvTreatmentCaseValues(
      scheinItem?.kvTreatmentCase as keyof typeof ScheinI18n.generalInfo.kvTreatmentCaseValues
    );
    return `${kvTreatmentCase} (${scheinItem?.kvScheinSubGroup})`;
  };

  const getQuarterString = (quarter: number, year: number) => {
    return `Q${quarter}/${year}`;
  };

  const sortedScheinList = useMemo(() => {
    return scheinList
      .filter(
        (schein) => !checkIsPrivateSchein(schein) && !checkIsBgSchein(schein)
      )
      .reverse()
      .map((schein: ScheinItem) => {
        const scheinQuarter = getQuarterString(
          schein.g4101Quarter || 0,
          schein.g4101Year || 0
        );
        const label = `${getScheinTitle(schein)} • ${scheinQuarter}`;

        return {
          label,
          value: schein.scheinId,
        };
      }, []);
  }, [scheinList]);

  const handleResetForm = useCallback(() => {
    handleResetState();

    if (!rightPanelRef?.current) {
      return;
    }

    rightPanelRef.current.resetForm();
    rightPanelRef.current.setValues({});
  }, []);

  const renderDMPList = useMemo(() => {
    return leftPanelValues.dmpValues.map((dmp, index) => {
      const matchedEnrollmentList = enrollmentList.find(
        (item) =>
          item.enrollmentInfoModel.enrollmentInfo.participationForm
            .dMPLabelingValue === dmp.name
      );
      const activeDocumentation =
        matchedEnrollmentList?.enrollmentDocumentInfoModel?.filter(
          (document) =>
            document.documentationOverview.enrollStatus ===
            EnrollStatus.StatusActivated
        );
      const hasDocumented = !!activeDocumentation?.[0];
      let isFinishedED = false;
      let isValidFinishedEDInQuarter = false;
      let isMissingED =
        hasDocumented &&
        activeDocumentation?.every(
          (document) =>
            document.documentationOverview.documentType !==
            DocumentType.DocumentType_ED
        );
      let isIncompleteED = false;
      let isFinishedFD = false;
      let isMissingFD =
        hasDocumented &&
        activeDocumentation?.every(
          (document) =>
            document.documentationOverview.documentType !==
            DocumentType.DocumentType_FD
        );
      const hasPDType = DMP_PROGRAMS.checkAllowPDType(dmp.name || '');
      const hasPHQ9Type = DMP_PROGRAMS.checkAllowPHQ9Type(dmp.name || '');
      let isIncompleteFD = false;
      let isBilled = false;
      let isIncompletePD = false;
      let isFinishedPD = false;
      let isMissingPD =
        hasDocumented &&
        activeDocumentation?.every(
          (document) =>
            document.documentationOverview.documentType !==
            DocumentType.DocumentType_PD
        );
      let isIncompletePHQ9ED = false;
      let isFinishedPHQ9ED = false;
      let isMissingPHQ9ED =
        hasDocumented &&
        activeDocumentation?.every(
          (document) =>
            document.documentationOverview.documentType !==
            DocumentType.DocumentType_PHQ9_ED
        );
      let isIncompletePHQ9FD = false;
      let isFinishedPHQ9FD = false;
      let isMissingPHQ9FD =
        hasDocumented &&
        activeDocumentation?.every(
          (document) =>
            document.documentationOverview.documentType !==
            DocumentType.DocumentType_PHQ9_FD
        );

      if (hasDocumented) {
        activeDocumentation?.forEach((document) => {
          const { documentStatus, documentType } =
            document.documentationOverview;
          const isED = documentType === DocumentType.DocumentType_ED;
          const isFD = documentType === DocumentType.DocumentType_FD;
          const isPD = documentType === DocumentType.DocumentType_PD;
          const isPHQ9ED = documentType === DocumentType.DocumentType_PHQ9_ED;
          const isPHQ9FD = documentType === DocumentType.DocumentType_PHQ9_FD;

          if (documentStatus === DocumentStatus.DocumentStatus_Billed) {
            isBilled = true;

            return;
          }

          if (isED) {
            isMissingED = false;

            switch (documentStatus) {
              case DocumentStatus.DocumentStatus_Finished:
              case DocumentStatus.DocumentStatus_Printed:
                isFinishedED = true;
                isValidFinishedEDInQuarter = true;
                break;
              case DocumentStatus.DocumentStatus_Saved:
                isIncompleteED = true;
                break;
              default:
                break;
            }
          } else if (isFD) {
            isMissingFD = false;

            switch (documentStatus) {
              case DocumentStatus.DocumentStatus_Finished:
              case DocumentStatus.DocumentStatus_Printed:
                isFinishedFD = true;
                break;
              case DocumentStatus.DocumentStatus_Saved:
                isIncompleteFD = true;
                break;
              default:
                break;
            }
          } else if (isPD) {
            isMissingPD = false;

            switch (documentStatus) {
              case DocumentStatus.DocumentStatus_Finished:
              case DocumentStatus.DocumentStatus_Printed:
                isFinishedPD = true;
                break;
              case DocumentStatus.DocumentStatus_Saved:
                isIncompletePD = true;
                break;
              default:
                break;
            }
          } else if (isPHQ9ED) {
            isMissingPHQ9ED = false;

            switch (documentStatus) {
              case DocumentStatus.DocumentStatus_Finished:
              case DocumentStatus.DocumentStatus_Printed:
                isFinishedPHQ9ED = true;
                break;
              case DocumentStatus.DocumentStatus_Saved:
                isIncompletePHQ9ED = true;
                break;
              default:
                break;
            }
          } else if (isPHQ9FD) {
            isMissingPHQ9FD = false;

            switch (documentStatus) {
              case DocumentStatus.DocumentStatus_Finished:
              case DocumentStatus.DocumentStatus_Printed:
                isFinishedPHQ9FD = true;
                break;
              case DocumentStatus.DocumentStatus_Saved:
                isIncompletePHQ9FD = true;
                break;
              default:
                break;
            }
          }
        });
      }

      return (
        <Flex key={index} column>
          <Flex className="sl-dmp-documentation-overview__left-panel__background">
            <BodyTextS
              margin="8px 16px"
              fontWeight={600}
              color={COLOR.TEXT_SECONDARY_NAVAL}
            >
              {DMP_PROGRAMS.getMatchedDMP(dmp.name || '')?.name || ''}
            </BodyTextS>
          </Flex>
          <Flex
            className="sl-dmp-documentation-overview__left-panel__doctor-group"
            mt={16}
            gap={16}
          >
            <FormGroup2 label={t('doctor')}>
              <ReactSelect
                id="dmp-doctor-select"
                instanceId="dmp-doctor-select"
                selectedValue={dmp.doctor}
                items={doctorList.map((doctor) => ({
                  label: `${doctor.title || ''} ${doctor.fullName}`.trim(),
                  value: doctor.id || '',
                }))}
                isDisabled={isReadOnly || isFinishED}
                onItemSelect={(item) => {
                  if (item?.value) {
                    const values = [...leftPanelValues.dmpValues];
                    values[index].doctor = item?.value as string;

                    setLeftPanelValues({
                      ...leftPanelValues,
                      dmpValues: values,
                    });
                  }
                }}
              />
            </FormGroup2>
            <FormGroup2 label={t('doctorRelation')}>
              <ReactSelect
                id="dmp-doctor-relation-select"
                instanceId="dmp-doctor-relation-select"
                selectedValue={dmp.doctorRelation}
                items={doctorRelationList}
                isDisabled={isReadOnly || isFinishED}
                onItemSelect={(item) => {
                  if (item?.value) {
                    const values = [...leftPanelValues.dmpValues];
                    values[index].doctorRelation =
                      item?.value as DoctorRelationType;

                    setLeftPanelValues({
                      ...leftPanelValues,
                      dmpValues: values,
                    });
                  }
                }}
              />
            </FormGroup2>
          </Flex>
          <Flex column mb={16}>
            <RadioGroup
              selectedValue={dmp.documentationType}
              onChange={(e: any) => {
                if (isBilled) {
                  return;
                }

                const value = e.target.value;
                const isSameDMPType = documentationActive?.name === dmp.name;
                const isPHQ9_EDToED =
                  documentationActive?.documentationType ===
                  DocumentType.DocumentType_PHQ9_ED &&
                  value === DocumentType.DocumentType_ED;
                const isPHQ9_FDToFD =
                  documentationActive?.documentationType ===
                  DocumentType.DocumentType_PHQ9_FD &&
                  value === DocumentType.DocumentType_FD;

                if (
                  [
                    DocumentType.DocumentType_PHQ9_ED,
                    DocumentType.DocumentType_PHQ9_FD,
                  ].includes(value)
                ) {
                  setSavePHQ9Flow(true);
                }

                if (
                  isSameDMPType &&
                  isSavePHQ9Flow &&
                  !isConfirmSavePHQ9Dialog &&
                  isCreate &&
                  (isPHQ9_EDToED || isPHQ9_FDToFD)
                ) {
                  setConfirmSavePHQ9Dialog(true);
                  return;
                }

                if (
                  [
                    DocumentType.DocumentType_FD,
                    DocumentType.DocumentType_PD,
                  ].includes(value) &&
                  (!hasDocumented || isMissingED)
                ) {
                  setCurrentIndexDMP(index);
                  setCurrentDocumentType(value);
                  return;
                }

                const values = [...leftPanelValues.dmpValues];

                setLeftPanelValues({
                  ...leftPanelValues,
                  dmpValues: values.map((item, idx) => {
                    if (idx === index) {
                      return {
                        ...item,
                        documentationType: value,
                      };
                    }

                    return {
                      ...item,
                      documentationType: '',
                    };
                  }),
                });
                handleResetForm();

                if (
                  value === DocumentType.DocumentType_ED &&
                  !isIncompleteED &&
                  isFinishedED
                ) {
                  const matchedEnrollement = enrollmentList.find(
                    (item) =>
                      item.enrollmentInfoModel.enrollmentInfo.participationForm
                        .dMPLabelingValue === dmp.name
                  );
                  setCurrentDocumentationOverviewFinishED(
                    matchedEnrollement?.enrollmentDocumentInfoModel.find(
                      (document) =>
                        document.documentationOverview.documentType ===
                        DocumentType.DocumentType_ED
                    ) as EnrollmentDocumentInfoModel
                  );
                } else {
                  setCurrentDocumentationOverviewFinishED(undefined);
                }
              }}
            >
              {hasPHQ9Type && (
                <Radio
                  className={getCssClass(
                    'sl-dmp-documentation-overview__left-panel__radio',
                    {
                      finished: !isIncompletePHQ9ED && isFinishedPHQ9ED,
                      incomplete: isIncompletePHQ9ED,
                      missing: isMissingPHQ9ED,
                      disabled: isBilled || isReadOnly,
                    }
                  )}
                  labelElement={
                    <Flex align="center" justify="space-between" w="100%">
                      {t('phq9DocumentationED')}
                      {!isIncompletePHQ9ED && isFinishedPHQ9ED && (
                        <Svg
                          src={CheckCircle}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                      {isIncompletePHQ9ED && (
                        <Svg
                          src={WarningIcon}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                      {isMissingPHQ9ED && (
                        <Svg
                          src={ErrorIcon}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                    </Flex>
                  }
                  value={DocumentType.DocumentType_PHQ9_ED}
                />
              )}
              <Radio
                className={getCssClass(
                  'sl-dmp-documentation-overview__left-panel__radio',
                  {
                    finished: !isIncompleteED && isFinishedED,
                    incomplete: isIncompleteED,
                    missing: isMissingED,
                    disabled: isBilled || isReadOnly,
                  }
                )}
                labelElement={
                  <Flex align="center" justify="space-between" w="100%">
                    {t('initDocumentation')}
                    {!isIncompleteED && isFinishedED && (
                      <Svg
                        src={CheckCircle}
                        className="sl-dmp-documentation-overview__left-panel__radio-icon"
                      />
                    )}
                    {isIncompleteED && (
                      <Svg
                        src={WarningIcon}
                        className="sl-dmp-documentation-overview__left-panel__radio-icon"
                      />
                    )}
                    {isMissingED && (
                      <Svg
                        src={ErrorIcon}
                        className="sl-dmp-documentation-overview__left-panel__radio-icon"
                      />
                    )}
                  </Flex>
                }
                value={DocumentType.DocumentType_ED}
                data-id={dmp.name === DMP_VALUE.Depression ? radioBtnEdId : ''}
              />
              {hasPDType && (
                <Radio
                  className={getCssClass(
                    'sl-dmp-documentation-overview__left-panel__radio',
                    {
                      finished: !isIncompletePD && isFinishedPD,
                      incomplete: !isValidFinishedEDInQuarter && isIncompletePD,
                      missing: !isValidFinishedEDInQuarter && isMissingPD,
                      disabled: isBilled || isReadOnly,
                    }
                  )}
                  labelElement={
                    <Flex align="center" justify="space-between" w="100%">
                      {t('postoPerativeDocumentation')}
                      {!isIncompletePD && isFinishedPD && (
                        <Svg
                          src={CheckCircle}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                      {!isValidFinishedEDInQuarter && isIncompletePD && (
                        <Svg
                          src={WarningIcon}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                      {!isValidFinishedEDInQuarter && isMissingPD && (
                        <Svg
                          src={ErrorIcon}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                    </Flex>
                  }
                  value={DocumentType.DocumentType_PD}
                />
              )}
              {hasPHQ9Type && <Divider style={{ margin: '0 0 10px' }} />}
              {hasPHQ9Type && (
                <Radio
                  className={getCssClass(
                    'sl-dmp-documentation-overview__left-panel__radio',
                    {
                      finished: !isIncompletePHQ9FD && isFinishedPHQ9FD,
                      incomplete: isIncompletePHQ9FD,
                      missing: isMissingPHQ9FD,
                      disabled: isBilled || isReadOnly,
                    }
                  )}
                  labelElement={
                    <Flex align="center" justify="space-between" w="100%">
                      {t('phq9DocumentationFD')}
                      {!isIncompletePHQ9FD && isFinishedPHQ9FD && (
                        <Svg
                          src={CheckCircle}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                      {isIncompletePHQ9FD && (
                        <Svg
                          src={WarningIcon}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                      {isMissingPHQ9FD && (
                        <Svg
                          src={ErrorIcon}
                          className="sl-dmp-documentation-overview__left-panel__radio-icon"
                        />
                      )}
                    </Flex>
                  }
                  value={DocumentType.DocumentType_PHQ9_FD}
                />
              )}
              <Radio
                className={getCssClass(
                  'sl-dmp-documentation-overview__left-panel__radio',
                  {
                    finished: !isIncompleteFD && isFinishedFD,
                    incomplete: !isValidFinishedEDInQuarter && isIncompleteFD,
                    missing: !isValidFinishedEDInQuarter && isMissingFD,
                    disabled: isBilled || isReadOnly,
                  }
                )}
                labelElement={
                  <Flex align="center" justify="space-between" w="100%">
                    {t('followDocumentation')}
                    {!isIncompleteFD && isFinishedFD && (
                      <Svg
                        src={CheckCircle}
                        className="sl-dmp-documentation-overview__left-panel__radio-icon"
                      />
                    )}
                    {!isValidFinishedEDInQuarter && isIncompleteFD && (
                      <Svg
                        src={WarningIcon}
                        className="sl-dmp-documentation-overview__left-panel__radio-icon"
                      />
                    )}
                    {!isValidFinishedEDInQuarter && isMissingFD && (
                      <Svg
                        src={ErrorIcon}
                        className="sl-dmp-documentation-overview__left-panel__radio-icon"
                      />
                    )}
                  </Flex>
                }
                value={DocumentType.DocumentType_FD}
                data-id={dmp.name === DMP_VALUE.Depression ? radioBtnFdId : ''}
              />
              {!!dmp.documentationType && (
                <BodyTextM
                  className={getCssClass(
                    'sl-dmp-documentation-overview__left-panel__clear-selection',
                    {
                      'sl-dmp-documentation-overview__left-panel__text':
                        !isReadOnly,
                    }
                  )}
                  fontWeight={600}
                  color={
                    isReadOnly
                      ? COLOR.NEUTRAL_DISABLED
                      : COLOR.BACKGROUND_SELECTED_STRONG
                  }
                  onClick={() => {
                    if (isReadOnly) {
                      return;
                    }

                    const values = [...leftPanelValues.dmpValues];

                    values[index].documentationType = '';

                    setLeftPanelValues({
                      ...leftPanelValues,
                      dmpValues: values,
                    });
                  }}
                >
                  {t('clearSelection')}
                </BodyTextM>
              )}
            </RadioGroup>
          </Flex>
        </Flex>
      );
    });
  }, [
    leftPanelValues,
    doctorList,
    doctorRelationList,
    enrollmentList,
    isReadOnly,
    isFinishED,
    matchedEnrollement,
    documentationActive,
    isSavePHQ9Flow,
    isConfirmSavePHQ9Dialog,
    isCreate,
    handleResetForm,
  ]);

  useEffect(() => {
    const matchedSchein = sortedScheinList.find(
      (schein) => schein.value === defaultSelectSchein
    );

    setLeftPanelValues({
      selectedSchein: matchedSchein
        ? matchedSchein.value
        : sortedScheinList[0]?.value,
      dmpCaseNumber:
        matchedEnrollement?.enrollmentInfoModel?.enrollmentInfo?.dMPCaseNumber,
    } as LeftPanelValues);
  }, [sortedScheinList, matchedEnrollement, defaultSelectSchein]);

  return (
    <Flex className="sl-dmp-documentation-overview__left-panel" column>
      <FormGroup2
        label={t('caseNumber')}
        name="dMPCaseNumber"
        errors={{
          dMPCaseNumber: dMPCaseNumberError,
        }}
        touched={{
          dMPCaseNumber: !!matchedEnrollement,
        }}
        isRequired
      >
        <InputGroup
          id="dmp-document-case-number"
          data-tab-id="dmp-document-case-number"
          disabled={isReadOnly}
          value={leftPanelValues.dmpCaseNumber}
          maxLength={7}
          onChange={(event: ChangeEvent<HTMLInputElement>) => {
            const dmpCaseNumber = event.target.value;

            setLeftPanelValues({
              ...leftPanelValues,
              dmpCaseNumber,
            });

            onCheckExistedCaseNumber(dmpCaseNumber);
          }}
        />
      </FormGroup2>
      <FormGroup2 label={t('date')}>
        <>
          <DateInput
            disabled
            formatDate={(date) =>
              datetimeUtil.dateTimeFormat(date, DATE_FORMAT)
            }
            parseDate={(str) => datetimeUtil.strToDate(str, DATE_FORMAT)}
            inputProps={{
              leftElement: <Svg src={CalendarIcon} />,
            }}
            popoverProps={{ usePortal: false }}
            placeholder={DATE_FORMAT}
            value={
              leftPanelValues.createdAt
                ? datetimeUtil.dateTimeFormat(
                  leftPanelValues.createdAt,
                  YEAR_MONTH_DAY_FORMAT
                )
                : null
            }
            onChange={() => { }}
          />
          <BodyTextS margin="4px 0 0" color={COLOR.TEXT_SECONDARY_NAVAL}>
            {t('tooltip')}
          </BodyTextS>
        </>
      </FormGroup2>
      <FormGroup2
        label={t('schein')}
        name="schein"
        errors={{
          schein: !sortedScheinList[0] ? t('noExistedSchein') : '',
        }}
        touched={{
          schein: true,
        }}
        customError={(err, defaultContent) => {
          if (err !== t('noExistedSchein')) {
            return defaultContent;
          }
          return (
            <div className="bp5-form-helper-text">
              <Flex align="center">
                <HelpText submitCount={0} touched err={err} />
                <Button
                  minimal
                  intent={Intent.PRIMARY}
                  small
                  onClick={() => {
                    patientFileActions.setIsOpenSchein(true);
                    patientFileActions.setDefaultDocumentDate(
                      currentDocumentDate
                    );
                  }}
                >
                  {tCreateSchein('createScheinBtn')}
                </Button>
              </Flex>
            </div>
          );
        }}
      >
        <ReactSelect
          id="dmp-schein-select"
          instanceId="dmp-schein-select"
          selectedValue={leftPanelValues.selectedSchein}
          items={sortedScheinList}
          isDisabled={isReadOnly || isFinishED}
          onItemSelect={(item) => {
            if (item?.value) {
              setLeftPanelValues({
                ...leftPanelValues,
                selectedSchein: item.value as string,
              });
            }
          }}
        />
      </FormGroup2>
      {renderDMPList}
      <div className="sl-dmp-documentation-overview__left-panel__background" />
      <Flex align="center" my={8}>
        <ContentEditorCopy className="sl-dmp-documentation-overview__left-panel__icon" />
        <BodyTextL
          className="sl-dmp-documentation-overview__left-panel__text"
          fontWeight={600}
          color={COLOR.BACKGROUND_SELECTED_STRONG}
          onClick={() => setOpenTransferDataDialog(true)}
        >
          {t('transferData')}
        </BodyTextL>
      </Flex>
      <Flex align="center" my={8}>
        <HelpCircle className="sl-dmp-documentation-overview__left-panel__icon" />
        <BodyTextL
          className="sl-dmp-documentation-overview__left-panel__text"
          fontWeight={600}
          color={COLOR.BACKGROUND_SELECTED_STRONG}
          onClick={() => setOpenGuideDialog(true)}
        >
          {t('guide')}
        </BodyTextL>
      </Flex>

      <InfoConfirmDialog
        type="primary"
        isOpen={!isConfirmSavePHQ9Dialog && !isNil(currentIndexDMP)}
        title={
          currentDocumentType !== DocumentType.DocumentType_PD
            ? tFollowupDialog('title')
            : ''
        }
        confirmText={
          currentDocumentType !== DocumentType.DocumentType_PD
            ? tButtonActions('yes')
            : tButtonActions('okText')
        }
        cancelText={tButtonActions('no')}
        isShowIconTitle={false}
        isCloseButtonShown={false}
        isCancelButtonShown={
          currentDocumentType !== DocumentType.DocumentType_PD
        }
        onClose={() => {
          setCurrentIndexDMP(null);
          setCurrentDocumentType('');
        }}
        onConfirm={() => {
          const values = [...leftPanelValues.dmpValues];

          setLeftPanelValues({
            ...leftPanelValues,
            dmpValues: values.map((item, idx) => {
              if (idx === currentIndexDMP) {
                const documentationType =
                  currentDocumentType !== DocumentType.DocumentType_PD
                    ? currentDocumentType
                    : DocumentType.DocumentType_ED;

                return {
                  ...item,
                  documentationType,
                };
              }

              return {
                ...item,
                documentationType: '',
              };
            }),
          });
          setCurrentIndexDMP(null);
          setCurrentDocumentType('');
          handleResetForm();
        }}
      >
        {currentDocumentType !== DocumentType.DocumentType_PD
          ? tFollowupDialog('description')
          : tPostoperativeDialog('description')}
      </InfoConfirmDialog>

      <TransferDataDialog
        isOpen={isOpenTransferDataDialog}
        enrollmentList={enrollmentList}
        patient={patient}
        rightPanelRef={rightPanelRef}
        matchedEnrollement={matchedEnrollement}
        dMPLabelingValue={
          leftPanelValues.dmpValues.find((dmp) => !!dmp.documentationType)
            ?.name as string
        }
        dataMapping={dataMapping}
        closeModal={() => setOpenTransferDataDialog(false)}
      />

      <InfoConfirmDialog
        type="primary"
        isOpen={isOpenGuideDialog}
        title={tGuideDialog('title')}
        cancelText={tButtonActions('close')}
        isShowIconTitle={false}
        isCloseButtonShown={false}
        isConfirmButtonShown={false}
        onClose={() => setOpenGuideDialog(false)}
      >
        <Flex column>
          {tGuideDialog('description')}

          <Flex column mt={18} gap={8}>
            {DMP_PROGRAMS.DMP_GUIDE_FILES.map((file, index) => {
              return (
                <PDFDownloadFile key={index} url={file.url} name={file.name} />
              );
            })}
          </Flex>
        </Flex>
      </InfoConfirmDialog>

      <InfoConfirmDialog
        type="primary"
        isOpen={isConfirmSavePHQ9Dialog}
        title={tConfirmSavePHQ9Dialog('title')}
        cancelText={tButtonActions('cancelText')}
        confirmText={tButtonActions('yes')}
        isShowIconTitle={false}
        isCloseButtonShown={false}
        isLoading={isLoading}
        onClose={() => {
          handleChangePHQ9To();
          setConfirmSavePHQ9Dialog(false);
        }}
        onConfirm={() => {
          onSavePHQ9(() => {
            handleChangePHQ9To();
            setConfirmSavePHQ9Dialog(false);
          });
        }}
      >
        <Flex column>{tConfirmSavePHQ9Dialog('description')}</Flex>
      </InfoConfirmDialog>
    </Flex>
  );
};

export default LeftPanel;
