import { InsuranceInfo } from '@tutum/hermes/bff/patient_profile_common';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';

import { ICustomInsuranceInfo } from '../create-patient-v2/CreatePatient.helper';
export interface PatientFileState {
  isLoadingPatient: boolean | null;
}

export interface IDropDownData {
  code?: string;
  description?: string;
  group?: boolean;
  evaluation?: number;
  unit?: string;
  isBlankService?: boolean;
  isActive?: boolean;
  price?: number;
}

export enum ID_TABS {
  TIMELINE = '#timeline',
  MEDICATION = '#medication',
  HIMI = '#himi',
  HEIMI = '#heimi',
  DIGA = '#diga',
  FORMS = '#forms',
  LAB = '#lab',
}

export type QuarterGroupKey = {
  quarter: number;
  year: number;
};

export type QuarterGroup = {
  groupKey: QuarterGroupKey;
  groupValue: GroupScheinItem[];
};

export type GroupScheinItem = {
  scheinItem: ScheinItem;
  insurance: InsuranceInfo;
};

export type PatientInsuranceListInitialValue = {
  insuranceInfos: ICustomInsuranceInfo[];
};
