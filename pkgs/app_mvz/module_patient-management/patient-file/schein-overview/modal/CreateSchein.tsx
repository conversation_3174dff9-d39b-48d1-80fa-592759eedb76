
/* eslint-disable no-unused-vars */
import React, { memo } from 'react';
import { IMvzTheme } from '@tutum/mvz/theme';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import { Dialog } from '@blueprintjs/core';

import { Button, Classes, Intent } from '@tutum/design-system/components/Core';

export interface ICreateScheinDialogProps {
  className?: string;
  theme?: IMvzTheme;
  show: boolean | null;
  onClose: () => void;
  openCreateSchein: () => void;
}

function CreateScheinDialog(
  props: ICreateScheinDialogProps &
    II18nFixedNamespace<keyof typeof ScheinI18n.createScheinModal>
): React.FunctionComponentElement<ICreateScheinDialogProps> {
  const { show, onClose, t, className, openCreateSchein } = props;
  if (show === null || show === false) {
    return null;
  }
  const createSchein = () => {
    onClose();
    openCreateSchein();
  };

  const confirmModal = (
    <Dialog
      className={className}
      isOpen={show}
      title={t('title')}
      onClose={onClose}
      canOutsideClickClose={false}
    >
      <div className={Classes.DIALOG_BODY}>
        <div>{t('content')}</div>
      </div>
      <div className={Classes.DIALOG_FOOTER}>
        <div
          className={`${Classes.DIALOG_FOOTER_ACTIONS} confirmation-modal__footer`}
        >
          <Button
            className="confirmation-modal__footer__button"
            onClick={() => onClose()}
            intent={Intent.NONE}
          >
            {t('btnNo')}
          </Button>
          <Button
            className="confirmation-modal__footer__button"
            onClick={() => createSchein()}
            intent={Intent.PRIMARY}
          >
            {t('btnYes')}
          </Button>
        </div>
      </div>
    </Dialog>
  );
  return confirmModal;
}

export default memo(
  I18n.withTranslation(CreateScheinDialog, {
    namespace: 'Schein',
    nestedTrans: 'createScheinModal',
  })
);
