
/* eslint-disable no-unused-vars */
import React, { memo } from 'react';
import { Dialog } from '@blueprintjs/core';

import { Flex, Box } from '@tutum/design-system/components';
import { IMvzTheme } from '@tutum/mvz/theme';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import { Button, Classes } from '@tutum/design-system/components/Core';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';

export interface IConfirmMarkNotBilledDialogProps {
  className?: string;
  theme?: IMvzTheme;
  scheinItem?: ScheinItem;
  onConfirm: (scheinItem: ScheinItem) => void;
  onClose: () => void;
}

function ConfirmMarkNotBilledDialog(
  props: IConfirmMarkNotBilledDialogProps &
    II18nFixedNamespace<keyof typeof ScheinI18n.deleteScheinModal>
): React.FunctionComponentElement<IConfirmMarkNotBilledDialogProps> {
  const { onClose, t, onConfirm, className, scheinItem } = props;

  return (
    <Dialog
      title={t('title')}
      isOpen={Boolean(scheinItem?.scheinId)}
      onClose={onClose}
      className={className}
      canOutsideClickClose={false}
    >
      <Flex className={Classes.DIALOG_BODY}>{t('content')}</Flex>
      <Flex justify="space-between" className={Classes.DIALOG_FOOTER}>
        <Box w="46%">
          <Button fill onClick={onClose}>
            {t('btnNo')}
          </Button>
        </Box>
        <Box w="46%">
          <Button
            fill
            onClick={() => {
              onConfirm(scheinItem);
              onClose();
            }}
            intent="primary"
          >
            {t('btnYes')}
          </Button>
        </Box>
      </Flex>
    </Dialog>
  );
}

export default memo(
  I18n.withTranslation(ConfirmMarkNotBilledDialog, {
    namespace: 'Schein',
    nestedTrans: 'confirmMarkNotBilled',
  })
);
