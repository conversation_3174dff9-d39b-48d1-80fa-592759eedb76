import React, { useMemo, useState } from 'react';
import { Field } from 'formik';
import get from 'lodash/get';
import debounce from 'lodash/debounce';
import MomentLocaleUtils from 'react-day-picker/moment';

import type patientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  SearchType as SearchTypeBGInsurance,
  searchBGInsurance,
} from '@tutum/hermes/bff/legacy/app_mvz_catalog_bg_insurance';
import { InputGroup } from '@tutum/design-system/components/Core';
import {
  BodyTextM,
  FormGroup2,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import AddIkNumberDialog from '@tutum/mvz/module_sdkt/add-ik-number-dialog';
import { isReadByEGKCard, isReadByTICard } from '@tutum/mvz/_utils/cardReader';
import { DateInput } from '@tutum/design-system/components/DateTime';
import FormUtil from '@tutum/infrastructure/utils/form.util';
import InsuranceInfoUtils from '@tutum/mvz/module_patient-management/create-patient-v2/insurance-info/InsuranceInfo.util';
import InsuranceNameInput from '@tutum/mvz/module_patient-management/create-patient-v2/insurance-info/insurance-name-input';
import { SdktCatalog, SearchType } from '@tutum/hermes/bff/catalog_sdkt_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { FieldError } from '@tutum/hermes/bff/common';
import { useEmployeeStore } from '@tutum/mvz/hooks/useEmployee';
import {
  CreatePatientProfileErrorCode,
  PatientInfo,
} from '@tutum/hermes/bff/patient_profile_common';
import { ICustomInsuranceInfo } from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.helper';
import { DATE_FORMAT } from '@tutum/mvz/constant/dateTime';
import { SdikCatalogStatus } from '@tutum/hermes/bff/catalog_sdik_common';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';

export interface BGInsuranceProps {
  className?: string;
  serverErrors: Record<string, FieldError>;
  t: any;
  insurance: ICustomInsuranceInfo;
  index: number;
  submitCount: number;
  errors?: any;
  touched?: any;
  patientInfo: PatientInfo;
  setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
}

const calendarIcon = '/images/calendar-default.svg';

const BGInsurance = ({
  t,
  className,
  touched,
  index,
  insurance,
  serverErrors,
  errors,
  submitCount,
}: BGInsuranceProps) => {
  const { userProfile } = useEmployeeStore();
  const ukv = userProfile?.bsnr?.substring(0, 2);
  const isDisabled = isReadByTICard(insurance, datetimeUtil.now());

  const [showAddIKDialog, setShowAddIKDialog] = useState<boolean>(false);
  const [isRefetch, setRefetch] = useState<boolean>(false);
  const [preFilledFormValues, setPreFilledFormValues] =
    useState<Partial<SdktCatalog>>();

  const fieldErrorInsuranceCompanyId =
    serverErrors?.[`insuranceInfos.${index}.insuranceCompanyId`];
  const fieldWarningIKNumber =
    serverErrors?.[`insuranceInfos.${index}.ikNumber`];

  const insuranceNameServerWarning =
    fieldErrorInsuranceCompanyId?.errorCode ===
      CreatePatientProfileErrorCode.CostUnitHasExpired ||
      insurance?.isExpiredCostUnit
      ? t(
        'Error.CostUnitHasExpired' as keyof typeof patientManagementI18n.InsuranceInformation
      )
      : null;
  const ikNumberServerWarning =
    fieldWarningIKNumber?.errorCode ===
      CreatePatientProfileErrorCode.IKNumberHasExpired ||
      insurance?.isExpiredCostUnit
      ? t(
        `Error.${fieldWarningIKNumber?.errorCode}` as keyof typeof patientManagementI18n.InsuranceInformation
      )
      : null;

  const debouncedQueryChangeHandler = useMemo(
    () =>
      debounce(
        async (_query, _form, _searchType, _setInsurances, _isRefetch) => {
          if (_query?.length === 1) return;
          try {
            if (!_query) {
              _setInsurances([]);
              return;
            }
            const res = await searchBGInsurance({
              value: _query,
              searchType: _searchType
                ? SearchTypeBGInsurance.SearchType_IkNumber
                : SearchTypeBGInsurance.SearchType_SearchName,
              selectedDate: new Date().getTime(),
            });
            const { items } = res.data;
            _setInsurances(items);
            setPreFilledFormValues(
              InsuranceInfoUtils.parseQueryToCostUnitFormValues(
                _query,
                _searchType?.value as SearchType
              )
            );
          } catch (error) {
            console.error(error);
            throw error;
          } finally {
            if (_isRefetch) {
              setRefetch(false);
            }
          }
        },
        500
      ),
    [ukv]
  );

  return (
    <div className={className}>
      <InsuranceNameInput
        showRequireAsterisk
        disabled={isDisabled}
        isRefetch={isRefetch}
        touched={false}
        label={t('insuranceCompany')}
        submitCount={submitCount}
        categories={[SearchType.SearchType_IkNumbers]}
        fieldName={`insuranceInfos.${index}.insuranceCompanyName`}
        errorText={errors[`insuranceInfos.${index}.insuranceCompanyName`]}
        warningText={insuranceNameServerWarning}
        onItemSelect={(item: any, _form, _field, _setSearchType) => {
          _form.setFieldValue(_field.name, item.insuranceName || '');
          _form.setFieldValue(
            `insuranceInfos.${index}.ikNumber`,
            +item.ikNumber
          );
          _setSearchType(null!);
        }}
        beforeQueryChange={(_form) => {
          if (insurance.isTerminated) {
            _form.setFieldValue(`insuranceInfos.${index}.isTerminated`, false);
          }
        }}
        onQueryChange={debouncedQueryChangeHandler}
        isUsedForPrivateCostUnit={true}
      />

      <Flex justify="space-between" gap={18}>
        <FormGroup2
          style={{ flex: 1 }}
          errors={errors}
          touched={touched}
          submitCount={submitCount}
          label={t('insuranceNumber')}
          name={`insuranceInfos.${index}.insuranceNumber`}
        >
          <Field name={`insuranceInfos.${index}.insuranceNumber`}>
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisabled}
                  value={field.value || ''}
                  id="insuranceNumber"
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          style={{ flex: 1 }}
          helperText={FormUtil.renderFormHelperText(
            submitCount,
            touched.insuranceInfos?.[index]?.ikNumber,
            errors[`insuranceInfos.${index}.ikNumber`],
            ikNumberServerWarning
          )}
          className={FormUtil.renderFormClass(
            submitCount,
            touched.insuranceInfos?.[index]?.ikNumber,
            errors[`insuranceInfos.${index}.ikNumber`],
            ikNumberServerWarning
          )}
          label={t('insuranceIkNumber')}
        >
          <Field name={`insuranceInfos.${index}.ikNumber`}>
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisabled}
                  value={field.value || ''}
                  id="ikNumber"
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>
      <Flex gap={16} className="calendar-input-group date">
        <FormGroup2
          helperText={FormUtil.renderFormHelperText(
            submitCount,
            touched.insuranceInfos?.[index]?.startDate,
            errors[`insuranceInfos.${index}.startDate`]
          )}
          className={FormUtil.renderFormClass(
            submitCount,
            touched.insuranceInfos?.[index]?.startDate,
            errors[`insuranceInfos.${index}.startDate`]
          )}
          style={{ flex: 1 }}
          label={t('insuranceStartDate')}
        >
          <Field name={`insuranceInfos.${index}.startDate`}>
            {({ field, form }) => {
              const { setFieldValue, initialValues } = form;
              const setDatetimeValue = (dateValue: string) => {
                setFieldValue(
                  field.name,
                  dateValue ? new Date(+dateValue) : null
                );
              };
              let isDisableStartDate = false;
              if (
                isReadByEGKCard(
                  initialValues.insuranceInfos[index],
                  datetimeUtil.now()
                )
              ) {
                isDisableStartDate = true;
              }
              return (
                <DateInput
                  inputProps={{
                    leftElement: (
                      <Svg src={calendarIcon} className="calendar-icon" />
                    ),
                    className: FormUtil.renderFormClass(
                      submitCount,
                      touched.insuranceInfos?.[index]?.startDate,
                      errors[`insuranceInfos.${index}.startDate`]
                    ),
                    id: field.name,
                  }}
                  invalidDateMessage={t('invalidDateErrorMessage')}
                  outOfRangeMessage={t('outOfRangeErrorMessage')}
                  localeUtils={MomentLocaleUtils}
                  maxDate={new Date(2100, 1, 1)}
                  minDate={new Date(1900, 1, 1)}
                  formatDate={(date) =>
                    datetimeUtil.dateTimeFormat(date, DATE_FORMAT)
                  }
                  parseDate={(str) => datetimeUtil.strToDate(str, DATE_FORMAT)}
                  popoverProps={{ usePortal: false }}
                  placeholder={DATE_FORMAT}
                  data-tab-id={field.name}
                  value={
                    field.value
                      ? datetimeUtil.dateTimeFormat(
                        field.value,
                        YEAR_MONTH_DAY_FORMAT
                      )
                      : null
                  }
                  disabled={isDisableStartDate}
                  showActionsBar
                  onChange={setDatetimeValue}
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          helperText={FormUtil.renderFormHelperText(
            submitCount,
            touched.insuranceInfos?.[index]?.endDate,
            errors[`insuranceInfos.${index}.endDate`]
          )}
          className={FormUtil.renderFormClass(
            submitCount,
            touched.insuranceInfos?.[index]?.endDate,
            errors[`insuranceInfos.${index}.endDate`]
          )}
          style={{ flex: 1 }}
          label={t('insuranceEndDate')}
        >
          <Field name={`insuranceInfos.${index}.endDate`}>
            {({ field, form }) => {
              const { setFieldValue, initialValues } = form;
              const startDate = get(
                form.values,
                `insuranceInfos.${index}.startDate`
              );
              const setDatetimeValue = (dateValue: string) => {
                setFieldValue(
                  field.name,
                  dateValue ? new Date(+dateValue) : null
                );
              };
              const defaultEndDate = initialValues.insuranceInfos[index]
                ?.validity?.toDate
                ? new Date(
                  initialValues.insuranceInfos[index]?.validity?.toDate
                )
                : null;
              const dateValue = field.value || defaultEndDate;
              let isDisableEndDate = false;
              if (
                defaultEndDate?.getTime() &&
                isReadByEGKCard(
                  initialValues.insuranceInfos[index],
                  datetimeUtil.now()
                )
              ) {
                isDisableEndDate = true;
              }

              return (
                <DateInput
                  inputProps={{
                    leftElement: (
                      <Svg src={calendarIcon} className="calendar-icon" />
                    ),
                    className: FormUtil.renderFormClass(
                      submitCount,
                      touched.insuranceInfos?.[index]?.endDate,
                      errors[`insuranceInfos.${index}.endDate`]
                    ),
                    id: field.name,
                  }}
                  disabled={isDisableEndDate}
                  invalidDateMessage={t('invalidDateErrorMessage')}
                  outOfRangeMessage={t('outOfRangeErrorMessage')}
                  localeUtils={MomentLocaleUtils}
                  maxDate={new Date(2100, 1, 1)}
                  minDate={startDate || new Date(1900, 1, 1)}
                  initialMonth={form.values?.insuranceInfos?.[index]?.startDate}
                  formatDate={(date) =>
                    datetimeUtil.dateTimeFormat(date, DATE_FORMAT)
                  }
                  parseDate={(str) => datetimeUtil.strToDate(str, DATE_FORMAT)}
                  popoverProps={{
                    usePortal: false,
                  }}
                  placeholder={DATE_FORMAT}
                  data-tab-id={field.name}
                  value={
                    dateValue
                      ? datetimeUtil.dateTimeFormat(
                        dateValue,
                        YEAR_MONTH_DAY_FORMAT
                      )
                      : null
                  }
                  showActionsBar
                  onChange={setDatetimeValue}
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>

      {showAddIKDialog && (
        <AddIkNumberDialog
          isOpen
          onAddConfirm={async () => { }}
          onClose={() => setShowAddIKDialog(false)}
          initialValues={{
            ikNumber: preFilledFormValues?.iKNumbers?.[0]?.value,
          }}
        />
      )}
    </div>
  );
};
export default BGInsurance;
