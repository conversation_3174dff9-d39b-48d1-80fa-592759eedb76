
import { FieldArray, Form, Formik, FormikProps } from 'formik';
import { debounce, isEmpty, valuesIn } from 'lodash';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import type SidebarI18n from '@tutum/mvz/locales/en/Sidebar.json';
import type { FieldArrayRenderProps } from 'formik';

import {
  alertError,
  BodyTextL,
  BodyTextM,
  Box,
  Flex,
  InfoConfirmDialog,
  LeaveConfirmModal,
  LoadingState,
} from '@tutum/design-system/components';
import {
  Button,
  Callout,
  Checkbox,
  Collapse,
  Dialog,
  Intent,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import {
  GetNullUUID,
  isNullUUID,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { FieldError, ValidationType } from '@tutum/hermes/bff/common';
import {
  isValid,
  useMutationDeleteInsurances,
  useMutationUpdateInsurances,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  useMutationSaveSetting,
  useQueryGetSetting,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  InsuranceInfo,
  TypeOfInsurance,
} from '@tutum/hermes/bff/legacy/patient_profile_common';
import { PrivateScheinItem } from '@tutum/hermes/bff/legacy/private_schein_common';
import {
  DateOfBirth,
  PatientInfo,
  PatientType,
} from '@tutum/hermes/bff/patient_profile_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  getStartDateEndDate,
  isReadByTICard,
} from '@tutum/mvz/_utils/cardReader';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { InsuranceDetail } from '@tutum/mvz/components/schein-component';
import { useCopaymentData } from '@tutum/mvz/hooks/useCopaymentData';
import { ScheinInfo } from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';
import { useLHMStore } from '@tutum/mvz/module_patient-management/create-patient-v2/insurance-info/longterm-heimi-approval/store';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  initInsuranceItem,
  initInsurancePrivate,
} from '../../../PatientFile.helper';
import { ICustomInsuranceInfo } from '../../ScheinOverview.types';
import {
  checkHaveAnyActiveInsuranceInQuarter,
  onValidateInsuranceForm,
  parseInitValue,
  parseInitValues,
  scrollToFirstError,
  transformDate,
} from '../../ScheinOverviewUtils';
import NotFoundInsurance from './NotFoundInsurance';
import PrivateInsuranceContent from './PrivateInsuranceContent.styled';
import PublicInsuranceContent from './PublicInsuranceContent.styled';
import { ScheinInfo as ScheinInfoSVSchein } from '@tutum/mvz/module_kv_hzv_schein/CreateSVSchein/helpers';
import BGInsuranceContent from './BGInsuranceContent.styled';
import { BgScheinItem } from '@tutum/hermes/bff/schein_common';
import { initInsuranceBG } from '@tutum/mvz/module_insurance/ListInsurance.helper';
import { getInsuranceType } from '@tutum/mvz/_utils/patientType';

export interface ListInsurancesModalProps {
  show: boolean;
  className?: string;
  isDisable: boolean;
  temporaryInsurances: { insuranceInfos: ICustomInsuranceInfo[] };
  saveTemporaryInsurances?: (
    items: ICustomInsuranceInfo[],
    removeItems?: ICustomInsuranceInfo[],
    newSelectInsurance?: number
  ) => void;
  onClose: () => void;
  setChangeDOB?: (isChangeDOB: boolean) => void;
  isEdit?: boolean;
  patientInfo?: PatientInfo;
  selectedPatient?: PatientInfo;
  cardInsuranceInfo?: InsuranceInfo;
  isDisablePublic?: boolean;
  isDisablePrivate?: boolean;
  isDisableBG?: boolean;
  isNotFromSchein?: boolean;
  currentSchein?:
    | ScheinInfo
    | PrivateScheinItem
    | BgScheinItem
    | ScheinInfoSVSchein;
  isLoading?: boolean;
  focusCreate?: boolean;
  focusInsuranceId?: string;
  markAsActive: (id: string) => void;
  isPrivate: boolean;
  isBGSchein?: boolean;
}

export type ListInsuranceForm = {
  insuranceInfos: Array<ICustomInsuranceInfo & { tempActive?: boolean }>;
  indexSelectInsurance?: number;
};

const ListInsurancesModal = ({
  className,
  show,
  isDisablePublic,
  isDisablePrivate,
  isDisableBG,
  temporaryInsurances,
  saveTemporaryInsurances,
  onClose,
  isEdit: isEditPatient = true,
  patientInfo,
  cardInsuranceInfo,
  setChangeDOB,
  isNotFromSchein,
  currentSchein,
  markAsActive,
  isLoading,
  focusCreate,
  focusInsuranceId = '',
  isPrivate,
  selectedPatient,
  isBGSchein,
}: ListInsurancesModalProps): React.FunctionComponentElement<ListInsurancesModalProps> => {
  const dateOfBirth: DateOfBirth = patientInfo?.personalInfo.dateOfBirth || {
    isValidDOB: false,
  };

  const lhmStore = useLHMStore();
  const { patient, schein } = usePatientFileStore();
  const patientProfile = patient.current;
  const [activeInsuranceId, setActiveInsuranceId] = useState<string>('');

  const updateInsurance = useMutationUpdateInsurances({
    onSuccess: (resp) => {
      /**
       * After updating insurance successfully
       * Re-run function set active insurance to make sure active insurance in patient store alway update to date
       */
      patientFileActions.reloadPatientProfile();
      patientFileActions.schein.groupSchein(
        schein.originalList,
        resp.data.insuranceInfos
      );
    },
  });
  const deleteInsurance = useMutationDeleteInsurances({
    onSuccess: () => {
      patientFileActions.reloadPatientProfile();
    },
  });

  const { data: dataSetting, refetch: refetchSetting } = useQueryGetSetting();
  const { mutate: saveSetting } = useMutationSaveSetting({
    onSuccess: () => {
      refetchSetting();
    },
  });

  const { t } = I18n.useTranslation<any>({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'InsuranceInformation',
  });
  const { t: tValidateInsurance } = I18n.useTranslation<any>({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'CreatePatient',
  });
  const { t: tButtonActions } = I18n.useTranslation<any>({
    namespace: 'Common',
  });
  const { t: tSpecialGroup9 } = I18n.useTranslation<
    keyof typeof SidebarI18n.SpecialGroup09Dialog
  >({
    namespace: 'Sidebar',
    nestedTrans: 'SpecialGroup09Dialog',
  });

  const [clickAdd, setClickAdd] = useState(false);
  const [calloutError, setCalloutError] = useState<string>('');
  const [insuranceToDelete, setInsuranceToDelete] = useState<string>('');
  const [errorIndex, setErrorIndex] = useState<number | undefined>(undefined);
  const [removeAction, setRemoveAction] = useState<() => void>(null);
  const [showHintSpecialGroup09, setShowHintSpecialGroup09] =
    useState<boolean>(false);
  const [collapseItems, setCollapseItems] = useState([]);
  const [serverErrors, setServerErrors] = useState<{
    [key: string]: FieldError;
  } | null>(null);
  const [preventShowMessageAgain, setPreventShowMessageAgain] =
    useState<boolean>(false);
  const formikRef = useRef<FormikProps<ListInsuranceForm>>(null);
  const addNewBtnRef = useRef<HTMLButtonElement>(null);

  const copaymentData = useCopaymentData({
    patientInfo,
    prevPatientInfo: selectedPatient,
    isCreatePatient: !isEditPatient,
  });

  const insuranceInfos = useMemo(() => {
    // case for creation
    const { insuranceInfos } = temporaryInsurances;

    if (copaymentData) {
      const newInsuranceInfos = (insuranceInfos ?? []).map((insurance) => {
        return {
          ...insurance,
          haveCoPaymentExemptionTill: copaymentData.isUnder18,
          copaymentExemptionTillDate: copaymentData.copaymentExemptionTillDate,
        };
      });

      return newInsuranceInfos;
    }
    return insuranceInfos;
  }, [copaymentData, temporaryInsurances?.insuranceInfos]);

  const getListInsurances = () => {
    if (insuranceInfos?.length > 0) {
      return insuranceInfos;
    }
    if (temporaryInsurancesCheckDOB.insuranceInfos.length) {
      return temporaryInsurancesCheckDOB.insuranceInfos;
    }
    return [];
  };

  const containerRef = useCallback(
    (node: HTMLDivElement) => {
      if (!node) return;
      if (!getListInsurances().length) {
        //  If don't have any insurance, force to click add new
        setClickAdd(true);
        return;
      }
      //  If not focus on create new, should focus the existing insurance ?
      if (!focusCreate) {
        if (focusInsuranceId) {
          const existInsurance = getListInsurances().findIndex(
            (item) => item.id === focusInsuranceId
          );
          handleSetCollapse(existInsurance);
        }
        return;
      }
      if (isEditPatient && !clickAdd) {
        addNewBtnRef.current.click();
        setClickAdd(true);
      }
    },
    [insuranceInfos, focusInsuranceId, focusCreate]
  );

  const isDisabledSelectPublicInsurance = useMemo(() => {
    if (isNotFromSchein) {
      return (
        patientInfo?.genericInfo?.patientType ===
        PatientType.PatientType_Private
      );
    }
    return isDisablePublic;
  }, [isNotFromSchein, isDisablePublic, patientInfo?.genericInfo?.patientType]);

  const temporaryInsurancesCheckDOB = useMemo(() => {
    if (copaymentData) {
      const newInsuranceInfos = (temporaryInsurances.insuranceInfos ?? []).map(
        (insurance) => {
          return {
            ...insurance,
            haveCoPaymentExemptionTill: copaymentData.isUnder18,
            copaymentExemptionTillDate:
              copaymentData.copaymentExemptionTillDate,
          };
        }
      );

      return {
        insuranceInfos: newInsuranceInfos,
      };
    }
    return temporaryInsurances;
  }, [temporaryInsurances, copaymentData]);

  const handleValidationResponse = (serverError: {
    [key: string]: FieldError;
  }) => {
    const errorInsurance = serverError?.['insuranceInfos'];
    if (!errorInsurance) return <></>;
    setCalloutError(t(errorInsurance.errorCode));
    const sectionEl = document.getElementById('callout-error');
    sectionEl?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
    return <></>;
  };

  // useEffect(() => {
  //   if (!activeIndexId) return;
  //   const sectionEl = document.getElementById(activeIndexId);
  //   sectionEl?.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
  // }, [activeIndexId]);

  useEffect(() => {
    setCollapseItems(
      (insuranceInfos || []).map(
        (_, index: number) => collapseItems[index] || false
      )
    );

    if (insuranceInfos?.length > 0) {
      const activeInsurance = insuranceInfos.find((item) => item.isActive);
      setActiveInsuranceId(activeInsurance?.id);
    }
  }, [insuranceInfos]);

  const saveTemporaryInsuranceInfo = (values: ListInsuranceForm) => {
    saveTemporaryInsurances(
      values.insuranceInfos,
      null,
      values.indexSelectInsurance
    );
  };

  const handleValidationOnSaveForm = async (
    transformedInsurances: ICustomInsuranceInfo[]
  ) => {
    const isValidResp = await isValid({
      patientId: patientInfo?.patientId,
      patientType: patientInfo?.genericInfo.patientType,
      insuranceInfos: transformDate(transformedInsurances),
    });

    if (!isEmpty(isValidResp?.data?.errors)) {
      handleValidationResponse(isValidResp.data.errors);
      setServerErrors(isValidResp.data.errors);
    }
    return isValidResp?.data?.errors || [];
  };

  const onSaveCreateForm = async (values: ListInsuranceForm) => {
    const lastPublicInsurance = values.insuranceInfos
      .filter((insurance) => insurance.insuranceType === TypeOfInsurance.Public)
      ?.at(-1);

    // The latest public insurance always active

    const listInsuranceInfo: ICustomInsuranceInfo[] = [];

    let existActiveInsurance = false;
    for (let i = values.insuranceInfos.length; i > 0; i--) {
      const item = values.insuranceInfos[i - 1];
      if (item.insuranceType === TypeOfInsurance.Private) {
        listInsuranceInfo.unshift({ ...item, isActive: false });
      }
      if (item.insuranceType === TypeOfInsurance.Public) {
        if (!existActiveInsurance) {
          listInsuranceInfo.unshift({ ...item, isActive: true });
          existActiveInsurance = true;
        } else {
          listInsuranceInfo.unshift({ ...item, isActive: false });
        }
      }
    }

    let indexNewSelected = listInsuranceInfo.findIndex((item) =>
      isNullUUID(item.id)
    );
    if (indexNewSelected === -1) {
      indexNewSelected = 0;
    }
    const errMap = await handleValidationOnSaveForm(listInsuranceInfo);
    const hasError = valuesIn(errMap).some(
      (err) => err.validationType === ValidationType.ValidationType_Error
    );
    if (hasError) {
      return;
    }
    if (!isPrivate && !lastPublicInsurance) {
      alertError(t('NotFoundActiveInsurance'));
      return;
    }
    saveTemporaryInsuranceInfo({
      insuranceInfos: listInsuranceInfo,
      indexSelectInsurance: indexNewSelected,
    });
    return onClose();
  };

  const onSaveEditForm = async (values: ListInsuranceForm) => {
    const listPublicInsurance = values.insuranceInfos.filter(
      (insurance) => insurance.insuranceType === TypeOfInsurance.Public
    );

    // The latest public insurance always active
    const transformedInsurances: ICustomInsuranceInfo[] =
      listPublicInsurance.length > 0
        ? values.insuranceInfos
        : values.insuranceInfos.map((item, i) => {
            if (item.isActive) {
              return item;
            }
            return {
              ...item,
              isActive: false,
              tempActive: false,
            };
          });
    const insuranceInfos = transformDate(transformedInsurances);
    let indexSelectInsurance = transformedInsurances.findIndex((item) =>
      isNullUUID(item.id)
    );
    if (indexSelectInsurance === -1) {
      indexSelectInsurance = 0;
    }

    saveTemporaryInsuranceInfo({
      insuranceInfos: insuranceInfos,
      indexSelectInsurance,
    });
    onClose();
  };

  const onSaveForm = (values: ListInsuranceForm) => {
    if (values.insuranceInfos.length == 0 && !isPrivate) {
      alertError(t('NotFoundActiveInsurance'));
      return;
    }
    // NOTE: update patient's insurances
    if (isEditPatient) {
      return onSaveEditForm(values);
    }

    // NOTE: create patient
    return onSaveCreateForm(values);
  };

  const handleSetCollapse = (index: number) => {
    setCollapseItems((prevCollapseItems) => {
      const collapseItemsTemp = [...prevCollapseItems];

      // Always open the new insurance when click `Add insurance`
      if (collapseItemsTemp.length <= index) {
        return [...collapseItemsTemp, true];
      }

      collapseItemsTemp[index] = !collapseItemsTemp[index];

      if (index === errorIndex) {
        setErrorIndex(undefined);
      }
      return collapseItemsTemp;
    });
  };

  const getInitInsuranceType = (isEditPatient: boolean) => {
    if (isBGSchein) {
      return TypeOfInsurance.BG;
    }

    if (isEditPatient) {
      if (isDisabledSelectPublicInsurance) {
        return TypeOfInsurance.Private;
      }
      return TypeOfInsurance.Public;
    }
    return getInsuranceType(patientInfo?.genericInfo.patientType);
  };

  const initFormData = (): InsuranceInfo[] => {
    const listInsurance = getListInsurances();
    if (listInsurance.length) return listInsurance;
    return [
      {
        ...initInsuranceItem,
        insuranceNumber: isEditPatient
          ? ''
          : patientInfo.insuranceInfos?.[0]?.insuranceNumber || '',
        haveCoPaymentExemptionTill: copaymentData?.isUnder18,
        copaymentExemptionTillDate: copaymentData?.copaymentExemptionTillDate,
        startDate: datetimeUtil
          .getStartOfQuarter(datetimeUtil.date())
          .toDate()
          .getTime(),
        insuranceType: getInitInsuranceType(isEditPatient),
      },
    ];
  };

  // this function is used to get init value for insurance item when click public/private radio button
  const getInitInsuraneItem = (
    index: number,
    insuranceType: TypeOfInsurance
  ) => {
    if (
      insuranceInfos.at(index) &&
      insuranceInfos.at(index).insuranceType === insuranceType
    ) {
      return parseInitValue(insuranceInfos.at(index));
    }
    if (
      temporaryInsurancesCheckDOB.insuranceInfos.at(index) &&
      temporaryInsurancesCheckDOB.insuranceInfos.at(index).insuranceType ===
        insuranceType
    ) {
      return parseInitValue(
        temporaryInsurancesCheckDOB.insuranceInfos.at(index)
      );
    }
    const initItem = parseInitValue({ ...initInsuranceItem, insuranceType });

    if (insuranceInfos.at(index) && insuranceInfos.at(index).id) {
      initItem.id = insuranceInfos.at(index).id;
    }

    return initItem;
  };

  const markInsuranceActive = async (id: string) => {
    markAsActive(id);
    setActiveInsuranceId(id);
  };

  const onChangeInsType = (e: any, setFieldValue: any, index: number) => {
    const initItem = getInitInsuraneItem(
      index,
      e.currentTarget?.value as TypeOfInsurance
    );
    setFieldValue(`insuranceInfos.${index}`, initItem);
  };

  const handleRemoveInsurance = (
    insurance: ICustomInsuranceInfo,
    index: number,
    arrayHelper: FieldArrayRenderProps
  ) => {
    const isActiveInsurance = insurance.id === activeInsuranceId;
    if (isActiveInsurance && isEditPatient) {
      alertError(t('ErrorCode_Cannot_Delete_Activated_Insurance'));
      return;
    }

    const { readCardDatas } = insurance;
    if (!readCardDatas?.length) {
      setInsuranceToDelete(insurance.id);
      setRemoveAction(() => () => {
        arrayHelper.remove(index);
        setRemoveAction(null);
      });
      return;
    }

    const now = datetimeUtil.now();
    const [startDate, endDate] = getStartDateEndDate(now);
    const readCardModel = readCardDatas.filter(
      (r) => startDate <= r.readCardDate && r.readCardDate <= endDate
    );

    if (readCardModel.length) {
      alertError(t('ErrorCode_CardReader_Cannot_Delete_Insurance'));
      return;
    }

    setInsuranceToDelete(insurance.id);
    setRemoveAction(() => () => {
      arrayHelper.remove(index);
      setRemoveAction(null);
    });
  };

  const [isOpenConfirm, setIsOpenConfirm] = useState(false);

  function closeConfirmDialog() {
    if (formikRef.current?.dirty) {
      return setIsOpenConfirm(true);
    }
    onClose();
  }

  const focusInsuranceCompany = debounce((inputField: HTMLElement) => {
    inputField.focus();
  }, 500);

  const handleFocusInsuranceCompany = (lastInsuranceField: HTMLElement) => {
    if (clickAdd) {
      setClickAdd(false);
      focusInsuranceCompany(lastInsuranceField);
    }
  };

  if (isLoading) {
    return (
      <Dialog
        className={className}
        title={t('InsuranceTitle')}
        enforceFocus={true}
        isOpen={show}
        canOutsideClickClose={false}
        onClose={() => onClose()}
        style={{
          position: 'relative',
          width: '790px',
          overflowY: 'hidden',
          height: 'calc(100vh - 90px)',
        }}
      >
        <LoadingState />
      </Dialog>
    );
  }

  return (
    <>
      <Dialog
        className={className}
        title={t('InsuranceTitle')}
        enforceFocus={true}
        isOpen={show}
        canOutsideClickClose={false}
        onClose={closeConfirmDialog}
        style={{
          position: 'relative',
          width: '790px',
          overflowY: 'hidden',
          height: 'calc(100vh - 90px)',
        }}
      >
        <Flex auto column className="list-insurance-container">
          <Formik<ListInsuranceForm>
            innerRef={formikRef}
            initialValues={{
              insuranceInfos: parseInitValues(initFormData()),
            }}
            validate={onValidateInsuranceForm(
              tValidateInsurance,
              lhmStore,
              dateOfBirth
            )}
            onSubmit={(v) =>
              onSaveForm(v).catch((err) => {
                let msg = err.message;
                if (err.response?.data?.serverError) {
                  msg = t(err.response.data.serverError);
                }
                alertError(msg);
                throw err;
              })
            }
            isInitialValid
            enableReinitialize
          >
            {({
              isValid,
              submitCount,
              touched,
              errors,
              values: formikValues,
              setFieldValue,
              isSubmitting,
            }) => {
              if (!isValid) {
                const errorIndex = Object.keys(errors)[0].split('.')[1];
                setErrorIndex(+errorIndex);
                scrollToFirstError();
              } else if (errorIndex !== undefined) {
                setErrorIndex(undefined);
              }
              const currentDate = datetimeUtil.now();

              const listInsusrances = formikValues.insuranceInfos;

              let defaultNewInsuranceStartdate =
                checkHaveAnyActiveInsuranceInQuarter(
                  listInsusrances,
                  currentDate
                )
                  ? datetimeUtil.date()
                  : datetimeUtil
                      .getStartOfQuarter(datetimeUtil.date())
                      .toDate();

              const hasActiveInsurance =
                getActiveInsurance(listInsusrances) !== null;

              //  if no active insurance => first day of the current quarter
              if (hasActiveInsurance) {
                defaultNewInsuranceStartdate = datetimeUtil
                  .getStartOfQuarter(datetimeUtil.date())
                  .toDate();
              } else {
                defaultNewInsuranceStartdate = datetimeUtil
                  .getStartOfQuarter(datetimeUtil.date())
                  .toDate();
              }

              return (
                <Form style={{ height: '100%' }}>
                  <Flex column h="100%">
                    <Flex>
                      {!!listInsusrances?.length && (
                        <Box w="240px" className="menu-container">
                          <Flex
                            column
                            gap={20}
                            style={{ position: 'fixed', width: 'inherit' }}
                          >
                            {listInsusrances.map((item, index) => (
                              <Flex key={item.id + index} align="center">
                                <BodyTextL
                                  lineHeight={1.2}
                                  style={{ cursor: 'pointer' }}
                                  fontWeight={
                                    item.isActive ? 'SemiBold' : 'Normal'
                                  }
                                >
                                  {item.insuranceCompanyName}
                                </BodyTextL>
                              </Flex>
                            ))}
                          </Flex>
                        </Box>
                      )}
                      <Flex auto column={true} w="390px" ref={containerRef}>
                        {calloutError && (
                          <Box id="callout-error">
                            <Callout
                              intent={Intent.DANGER}
                              className="remedy-indicator"
                            >
                              <BodyTextM
                                fontWeight={500}
                                color={COLOR.TAG_BACKGROUND_RED}
                              >
                                {calloutError}
                              </BodyTextM>
                            </Callout>
                            <br />
                          </Box>
                        )}
                        <FieldArray
                          name="insuranceInfos"
                          render={(arrayHelper) => {
                            if (!listInsusrances.length && !isPrivate)
                              return (
                                <NotFoundInsurance
                                  t={t}
                                  cb={() => {
                                    handleSetCollapse(1);
                                    arrayHelper.push({
                                      ...(!isDisabledSelectPublicInsurance
                                        ? initInsuranceItem
                                        : isBGSchein
                                          ? initInsuranceBG
                                          : initInsurancePrivate),
                                      startDate: defaultNewInsuranceStartdate,
                                      haveCoPaymentExemptionTill:
                                        copaymentData?.isUnder18,
                                      copaymentExemptionTillDate:
                                        copaymentData?.copaymentExemptionTillDate,
                                    });
                                  }}
                                />
                              );
                            const lastInsuranceField =
                              document.querySelector<HTMLInputElement>(
                                `[name="insuranceInfos.${
                                  listInsusrances.length - 1
                                }.insuranceCompanyName"]`
                              );
                            if (lastInsuranceField) {
                              handleFocusInsuranceCompany(lastInsuranceField);
                            }
                            return (
                              <>
                                {listInsusrances.map((insur, index) => {
                                  const disabledInsuranceType = isReadByTICard(
                                    insur,
                                    datetimeUtil.now()
                                  );
                                  const isPublic =
                                    insur.insuranceType ===
                                    TypeOfInsurance.Public;
                                  const isPrivate =
                                    insur.insuranceType ===
                                    TypeOfInsurance.Private;
                                  const isBG =
                                    insur.insuranceType === TypeOfInsurance.BG;

                                  return (
                                    <Flex
                                      id={index + ''}
                                      className="insurance-item"
                                      column
                                      key={insur.id + index}
                                    >
                                      <Box w="100%">
                                        <InsuranceDetail
                                          index={index}
                                          isReadOnlyMode={!collapseItems[index]}
                                          isInitInsurance={
                                            insur.id === GetNullUUID()
                                          }
                                          insurance={insur}
                                          setRemoveAction={(index) => {
                                            handleRemoveInsurance(
                                              insur,
                                              index,
                                              arrayHelper
                                            );
                                          }}
                                          setCollapse={handleSetCollapse}
                                          isActive={
                                            activeInsuranceId === insur.id
                                          }
                                          isLoadingActiveInsurance={
                                            updateInsurance.isPending
                                          }
                                          onMarkActiveInsurance={(id) => {
                                            markInsuranceActive(id);
                                          }}
                                        />
                                      </Box>
                                      <Collapse
                                        isOpen={
                                          (index === errorIndex &&
                                            touched.insuranceInfos?.[index]
                                              ?.id) ||
                                          collapseItems[index] ||
                                          insur.id === GetNullUUID()
                                        }
                                      >
                                        {isPublic &&
                                          !!insur.ikNumber &&
                                          insur.id !== GetNullUUID() && (
                                            <Button
                                              className="markActive-btn"
                                              outlined
                                              minimal
                                              disabled={
                                                activeInsuranceId === insur.id
                                              }
                                              intent="primary"
                                              onClick={() =>
                                                markInsuranceActive(insur.id)
                                              }
                                            >
                                              {t('markAsActive')}
                                            </Button>
                                          )}
                                        <Box mt="-5px" mb="6px">
                                          <RadioGroup
                                            disabled={disabledInsuranceType}
                                            inline={true}
                                            className="blank-form-dialog__radio-group"
                                            label={t('insuranceType')}
                                            onChange={(e) =>
                                              onChangeInsType(
                                                e,
                                                setFieldValue,
                                                index
                                              )
                                            }
                                            selectedValue={
                                              insur.insuranceType ??
                                              TypeOfInsurance.Private
                                            }
                                          >
                                            <Radio
                                              className="blank-form-dialog__radio"
                                              label={t('publicLb')}
                                              value={TypeOfInsurance.Public}
                                              disabled={
                                                isDisabledSelectPublicInsurance
                                              }
                                              data-tab-id="typeOfIsurance_public"
                                            />
                                            <Radio
                                              className="blank-form-dialog__radio"
                                              label={t('privateLb')}
                                              value={TypeOfInsurance.Private}
                                              disabled={isDisablePrivate}
                                              data-tab-id="typeOfIsurance_private"
                                            />
                                            <Radio
                                              className="blank-form-dialog__radio"
                                              label={t('bgLb')}
                                              value={TypeOfInsurance.BG}
                                              disabled={isDisableBG}
                                              data-tab-id="typeOfIsurance_bg"
                                            />
                                          </RadioGroup>
                                        </Box>
                                        {isPublic ? (
                                          <PublicInsuranceContent
                                            className="sl-PublicInsuranceContent"
                                            t={t}
                                            index={index}
                                            errors={errors}
                                            insurance={insur}
                                            patient={patientProfile}
                                            values={
                                              listInsusrances?.length
                                                ? formikValues
                                                : temporaryInsurancesCheckDOB
                                            }
                                            touched={touched}
                                            serverErrors={serverErrors}
                                            submitCount={submitCount}
                                            dataSetting={dataSetting}
                                            setShowHintSpecialGroup09={
                                              setShowHintSpecialGroup09
                                            }
                                            setFieldValue={setFieldValue}
                                            setServerErrors={setServerErrors}
                                            setChangeDOB={setChangeDOB}
                                            cardInsuranceInfo={
                                              cardInsuranceInfo
                                            }
                                          />
                                        ) : isPrivate ? (
                                          <PrivateInsuranceContent
                                            className="sl-PrivateInsuranceContent"
                                            t={t}
                                            index={index}
                                            errors={errors}
                                            submitCount={submitCount}
                                            touched={touched}
                                            insurance={insur}
                                            serverErrors={serverErrors}
                                            setFieldValue={setFieldValue}
                                            patientInfo={patientInfo}
                                          />
                                        ) : isBG ? (
                                          <BGInsuranceContent
                                            className="sl-BGInsuranceContent"
                                            t={t}
                                            index={index}
                                            errors={errors}
                                            submitCount={submitCount}
                                            touched={touched}
                                            insurance={insur}
                                            serverErrors={serverErrors}
                                            patientInfo={patientInfo}
                                            setFieldValue={setFieldValue}
                                          />
                                        ) : null}
                                      </Collapse>
                                    </Flex>
                                  );
                                })}
                                <Flex w="100%" justify="center">
                                  <Button
                                    className="add-new-insurance"
                                    outlined
                                    intent="primary"
                                    minimal
                                    style={{ borderRadius: 4 }}
                                    onClick={() => {
                                      handleSetCollapse(
                                        listInsusrances.length + 1
                                      );
                                      const values =
                                        arrayHelper.form.values?.insuranceInfos?.map(
                                          (i) => ({ ...i, isActive: false })
                                        );

                                      const newInsurance = {
                                        ...(!isDisabledSelectPublicInsurance
                                          ? initInsuranceItem
                                          : isBGSchein
                                            ? initInsuranceBG
                                            : initInsurancePrivate),
                                        insuranceType:
                                          !isDisabledSelectPublicInsurance
                                            ? TypeOfInsurance.Public
                                            : TypeOfInsurance.Private,
                                        startDate: defaultNewInsuranceStartdate,
                                        haveCoPaymentExemptionTill:
                                          copaymentData?.isUnder18,
                                        copaymentExemptionTillDate:
                                          copaymentData?.copaymentExemptionTillDate,
                                        isActive: true,
                                      };
                                      values.push(newInsurance);
                                      arrayHelper.form.setValues({
                                        insuranceInfos: values,
                                      });
                                    }}
                                    ref={addNewBtnRef}
                                  >
                                    <span style={{ fontWeight: 600 }}>
                                      {t('addInsurance')}
                                    </span>
                                  </Button>
                                </Flex>
                                <br />
                              </>
                            );
                          }}
                        />
                        {typeof removeAction === 'function' ? (
                          <ConfirmDialog
                            close={() => setRemoveAction(null)}
                            confirm={() => removeAction()}
                            text={{
                              btnCancel: t('cancel'),
                              btnOk: t('remove'),
                              title: t('removeThisInsuranceInfo'),
                              message: t('weWillNotBeAbleToUndo'),
                            }}
                          />
                        ) : null}

                        {showHintSpecialGroup09 && (
                          <InfoConfirmDialog
                            type="primary"
                            isOpen
                            title={tSpecialGroup9('title')}
                            cancelText={tSpecialGroup9('cancelText')}
                            isCloseButtonShown={false}
                            isConfirmButtonShown={false}
                            onClose={() => {
                              if (preventShowMessageAgain) {
                                saveSetting({
                                  ...dataSetting,
                                  showHintSpecialGroup09: false,
                                });
                              }

                              setShowHintSpecialGroup09(false);
                            }}
                          >
                            <Flex column gap={16}>
                              {tSpecialGroup9('description')}
                              <Checkbox
                                id="prevent-show-message-again"
                                name="prevent-show-message-again"
                                label={tSpecialGroup9('noted')}
                                checked={preventShowMessageAgain}
                                onChange={() =>
                                  setPreventShowMessageAgain(
                                    !preventShowMessageAgain
                                  )
                                }
                              />
                            </Flex>
                          </InfoConfirmDialog>
                        )}
                      </Flex>
                    </Flex>
                    <Flex
                      className="footer-actions"
                      justify="space-between"
                      align="baseline"
                      mt="auto"
                    >
                      <span>
                        {tButtonActions(
                          'PrintPreviewDialog.keyboardNavigationHint'
                        )}
                      </span>
                      <Flex>
                        <Button
                          intent="primary"
                          className="cancel-btn"
                          outlined
                          minimal
                          disabled={updateInsurance.isPending}
                          onClick={() => {
                            if (listInsusrances.length === 0 && isEditPatient) {
                              saveTemporaryInsuranceInfo({
                                insuranceInfos: listInsusrances,
                              });
                            }
                            closeConfirmDialog();
                          }}
                        >
                          {tButtonActions('ButtonActions.cancelText')}
                        </Button>
                        <Button
                          className="submit-insurances"
                          type="submit"
                          intent="primary"
                          loading={isSubmitting || isLoading}
                          disabled={
                            !touched ||
                            (!isPrivate && !listInsusrances?.length) ||
                            updateInsurance.isPending
                          }
                        >
                          {tButtonActions('ButtonActions.updatedText')}
                        </Button>
                      </Flex>
                    </Flex>
                  </Flex>
                </Form>
              );
            }}
          </Formik>
        </Flex>
      </Dialog>
      <LeaveConfirmModal
        isOpen={isOpenConfirm}
        onConfirm={() => {
          setIsOpenConfirm(false);
          onClose();
        }}
        onClose={() => setIsOpenConfirm(false)}
      />
    </>
  );
};

export default ListInsurancesModal;
