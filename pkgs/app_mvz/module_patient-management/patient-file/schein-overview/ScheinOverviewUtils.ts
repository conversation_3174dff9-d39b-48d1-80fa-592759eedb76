import moment from 'moment';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  DateOfBirth,
  InsuranceInfo,
  SpecialGroupDescription,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import DMP_PROGRAMS from '@tutum/design-system/consts/DMP-programs';
import {
  getUUID,
  isEmpty,
  isNullUUID,
} from '@tutum/design-system/infrastructure/utils';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { ICustomInsuranceInfo } from '../../create-patient-v2/CreatePatient.helper';
import { ListInsuranceForm } from './modal/ListInsuranceModal/ListInsurancesModal';
import { convertToMMYYYYString } from '../../create-patient-v2/insurance-info/InsuranceInfo.util';
import FormUtils, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import InsuranceType from '../../types/insurance.type';
import { getStartDateEndDate } from '@tutum/mvz/_utils/cardReader';

export const getColor = (mainGroup: string) => {
  switch (mainGroup) {
    case MainGroup.KV:
      return '#C2D2F2';
    case MainGroup.FAV:
      return '#C3F3D3';
    case MainGroup.HZV:
      return '#F2C2F2';
    case MainGroup.BG:
      return '#FDD8CC';
    case MainGroup.PRIVATE:
      return '#FFF1CC';
  }
  return '';
};

export const scrollToFirstError = () => {
  const element = document.querySelector('.msg-error');
  const errorMsg = document.querySelector('.bp5-intent-danger');
  if (!element && !errorMsg) {
    return;
  }
  return (element || errorMsg)?.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
  });
};

export const transformFormikData = (
  data: ICustomInsuranceInfo
): InsuranceInfo => {
  const rsData: InsuranceInfo = {
    ...data,
    startDate: data.startDate
      ? moment
        .utc(moment(data.startDate).format(DATE_FORMAT), DATE_FORMAT)
        .valueOf()
      : undefined,
    endDate: data.endDate
      ? moment
        .utc(moment(data.endDate).format(DATE_FORMAT), DATE_FORMAT)
        .valueOf()
      : undefined,
    copaymentExemptionTillDate: data.copaymentExemptionTillDate
      ? moment
        .utc(
          moment(data.copaymentExemptionTillDate).format(DATE_FORMAT),
          DATE_FORMAT
        )
        .valueOf()
      : undefined,
    insuranceNumber: data.insuranceNumber ?? '',
  };

  return rsData;
};

export const onValidateInsuranceForm =
  (t, lhmStore, dateOfBirth: DateOfBirth) =>
    async (values: ListInsuranceForm) => {
      const validateFields: ValidateField[] = [];
      const { insuranceInfos } = values;

      insuranceInfos?.forEach((item, index) => {
        const insurance = document.getElementsByName(
          `insuranceInfos.${index}.insuranceCompanyName`
        )[0];

        validateFields.push({
          fieldName: `insuranceInfos.${index}.insuranceCompanyName`,
          validateRule: () =>
            !item.insuranceCompanyName ||
            isEmpty(item.insuranceCompanyName, true),
          errorMessage: insurance
            ? !!insurance.getAttribute('value')?.length
              ? t('errInsuranceCompanyMustSelect')
              : t('errInsuranceCompany')
            : '',
        });
        validateFields.push({
          fieldName: `insuranceInfos.${index}.startDate`,
          validateRule: () =>
            !!item.startDate &&
            !!item.endDate &&
            moment(item.endDate).startOf('d').unix() <
            moment(item.startDate).endOf('d').unix(),
          errorMessage: t('errInvalidDateRange'),
        });
        validateFields.push({
          fieldName: `insuranceInfos.${index}.startDate`,
          validateRule: () => {
            if (!item.startDate) {
              return false;
            }

            const startDate = moment(item.startDate).startOf('d');
            const { year: dobYear, month: dobMonth, date: dobDate } = dateOfBirth;
            const date = startDate.date();
            const month = startDate.month() + 1;
            const year = startDate.year();

            if (dobYear! > year) {
              return true;
            }

            if (dobYear === year) {
              if (!dobMonth || dobMonth > month) {
                return true;
              }

              if (dobMonth === month) {
                if (!dobDate || dobDate > date) {
                  return true;
                }
              }
            }

            return false;
          },
          errorMessage: t('errStartDateAfterBirthdate'),
        });

        // validate for private insurance
        if (item.insuranceType === TypeOfInsurance.Private) {
          validateFields.push({
            fieldName: `insuranceInfos.${index}.ikNumber`,
            validateRule: () =>
              !item.startDate &&
              !item.endDate &&
              !item.ikNumber &&
              !item.insuranceNumber,
            errorMessage: t('errAtleast1FieldRequired'),
          });
          return;
        }

        // validate for public insurance
        validateFields.push({
          fieldName: `insuranceInfos.${index}.insuranceCompanyName`,
          validateRule: () => Boolean(item?.isTerminated),
          errorMessage: t('errCostUnitTerminated'),
        });
        validateFields.push({
          fieldName: `insuranceInfos.${index}.ikNumber`,
          validateRule: () => !item.ikNumber || isEmpty(item.ikNumber, true),
          errorMessage: t('errIkNumber'),
        });
        validateFields.push({
          fieldName: `insuranceInfos.${index}.insuranceStatus`,
          validateRule: () =>
            !item.insuranceStatus || isEmpty(item.insuranceStatus, true),
          errorMessage: t('errInsuranceStatus'),
        });

        const insuranceCompanyId: string = item.insuranceCompanyId;
        const fromCardType = null;
        const isPublic = InsuranceType.isPublicCostUnit(
          insuranceCompanyId,
          fromCardType
        );
        const pattern = isPublic
          ? InsuranceType.INSURANCE_NUMBER_PATTERN_PUBLIC_COST_UNIT
          : InsuranceType.INSURANCE_NUMBER_PATTERN_OTHER_COST_UNITS;
        const insuranceFromCard = item?.readCardDatas;

        validateFields.push({
          fieldName: `insuranceInfos.${index}.insuranceNumber`,
          validateRule: () =>
            !!item.insuranceNumber &&
            !pattern.test(item.insuranceNumber) &&
            !insuranceFromCard,
          errorMessage: t(
            isPublic
              ? 'errInsuranceNumberPublicCostUnit'
              : 'errInsuranceNumberOtherCostUnits'
          ),
        });
        validateFields.push({
          fieldName: `insuranceInfos.${index}.insuranceNumber`,
          validateRule: () => item.isInValidInsuranceNumber,
          errorMessage: t('errInsuranceNumberExisted'),
        });

        if (item.haveHeimiLongTermApproval) {
          item.lHMs?.forEach((lhm, lhmIndex) => {
            const errFnc = () => {
              const lhmPrimaryRemediesPositionLength =
                lhm.primaryRemediesPosition?.length || 0;
              const lhmComplementaryRemediesPositionLength =
                lhm.complementaryRemediesPosition?.length || 0;
              // has standard combination and...
              if (
                !!lhmStore.hasStandardCombination[lhm.diagnosisGroupCode] &&
                lhm.isStandardCombination
              ) {
                if (
                  !lhmComplementaryRemediesPositionLength &&
                  !lhmPrimaryRemediesPositionLength
                ) {
                  // rule 1
                  //pass
                } else if (
                  !lhmComplementaryRemediesPositionLength &&
                  lhmPrimaryRemediesPositionLength < 3
                ) {
                  // rule 2
                  return t('lhm.atLeast3PrimaryRemedies');
                } else if (
                  !lhmPrimaryRemediesPositionLength &&
                  lhmComplementaryRemediesPositionLength < 3
                ) {
                  //rule 3
                  return t('lhm.atLeast3SupplementaryRemedies');
                } else {
                  // rule 4
                  if (
                    lhmPrimaryRemediesPositionLength +
                    lhmComplementaryRemediesPositionLength <
                    3
                  ) {
                    return t('lhm.atLeast3Remedies');
                  }
                }
              }
              return '';
            };
            // set the error if any
            validateFields.push({
              fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.primaryRemediesPosition`,
              validateRule: () => !!errFnc(),
              errorMessage: errFnc(),
            });
            validateFields.push({
              fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.complementaryRemediesPosition`,
              validateRule: () => !!errFnc(),
              errorMessage: errFnc(),
            });

            validateFields.push({
              fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.firstICDCode`,
              validateRule: () =>
                !lhm?.firstICDCode || isEmpty(lhm?.firstICDCode, true),
              errorMessage: t('errFirstRequired'),
            });
            validateFields.push({
              fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.diagnosisGroupCode`,
              validateRule: () =>
                !lhm?.diagnosisGroupCode ||
                isEmpty(lhm?.diagnosisGroupCode, true),
              errorMessage: t('errDiagnosisGroupRequired'),
            });
          });
        }
      });
      const { errors } = FormUtils.validateForm(validateFields, null);
      return errors;
    };

export const parseInitValue = (item: InsuranceInfo) => {
  return {
    ...item,
    specialGroup: item.specialGroup
      ? item.specialGroup
      : item.insuranceType === TypeOfInsurance.Private
        ? null
        : SpecialGroupDescription.SpecialGroup_00,
    dMPLabeling: item.dMPLabeling || DMP_PROGRAMS.DMP_DEFAULT_VALUE,
    isDetail: true,
    isExpiredCostUnit: false,
    isInValidInsuranceNumber: false,
    error: null!,
    startDate: item.startDate ? new Date(item.startDate) : null!,
    endDate: item.endDate ? new Date(item.endDate) : null!,
    validTo: convertToMMYYYYString(
      item.validUntil?.month!,
      item.validUntil?.year!
    ),
    insuranceType: item.insuranceType,
  };
};

export const parseInitValues = (
  data: InsuranceInfo[]
): ICustomInsuranceInfo[] => {
  const rsData = data || [];
  return rsData.map(
    (item: InsuranceInfo) =>
      parseInitValue(item) as unknown as ICustomInsuranceInfo
  );
};

export const transformDate = (
  customInsurances: ICustomInsuranceInfo[]
): ICustomInsuranceInfo[] => {
  return customInsurances.map((insuranceInfo) => ({
    ...insuranceInfo,
    id: isNullUUID(insuranceInfo.id) ? getUUID() : insuranceInfo.id,
    insuranceNumber: insuranceInfo.insuranceNumber
      ? insuranceInfo.insuranceNumber
      : '',
    startDate: insuranceInfo?.startDate
      ? moment
        .utc(
          moment(insuranceInfo?.startDate).format(DATE_FORMAT),
          DATE_FORMAT
        )
        .valueOf()
      : undefined,
    endDate: insuranceInfo?.endDate
      ? moment
        .utc(moment(insuranceInfo?.endDate).format(DATE_FORMAT), DATE_FORMAT)
        .valueOf()
      : undefined,
    copaymentExemptionTillDate: insuranceInfo?.copaymentExemptionTillDate
      ? moment
        .utc(
          moment(insuranceInfo?.copaymentExemptionTillDate).format(
            DATE_FORMAT
          ),
          DATE_FORMAT
        )
        .valueOf()
      : undefined,
  }));
};

export const checkHaveAnyActiveInsuranceInQuarter = (
  insurances: ICustomInsuranceInfo[],
  currentDate: number
): boolean => {
  const startQuarter = getStartDateEndDate(currentDate)[0];

  const haveActiveInsurance = insurances?.some((item) => {
    if (item.startDate) {
      if (!item.endDate || item.endDate >= startQuarter) {
        return true;
      } else {
        return false;
      }
    } else {
      if (item.endDate && item.endDate >= startQuarter) {
        return true;
      } else {
        return false;
      }
    }
  });

  return haveActiveInsurance;
};
