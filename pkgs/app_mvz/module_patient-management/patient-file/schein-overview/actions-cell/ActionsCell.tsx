import { Svg, alertSuccessfully } from '@tutum/design-system/components';
import {
  Intent,
  Menu,
  MenuItem,
  Popover,
  PopoverPosition,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { SdavCatalog } from '@tutum/hermes/bff/catalog_sdav_common';
import {
  useMutationMarkAsReferral,
  useMutationRemoveReferral,
  useMutationTakeOverDiagnosisByScheinId,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { MainGroup } from '@tutum/hermes/bff/legacy/common';
import { PatientType } from '@tutum/hermes/bff/legacy/patient_profile_common';
import {
  TakeoverDiagnosisType,
  TimelineModel,
} from '@tutum/hermes/bff/legacy/timeline_common';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import I18n from '@tutum/infrastructure/i18n';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import SelectDiagnosisDialog from '@tutum/mvz/components/select-diagnosis-dialog/SelectDiagnosisDialog.styled';
import useConfirm from '@tutum/mvz/hooks/useConfirm';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import SelectFavReferralDoctorDialog from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/fav-referral-doctor/SelectFavReferralDoctorDialog.styled';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import React, { memo, useEffect, useMemo, useState } from 'react';

const moreIcon = '/images/more-vertical.svg';
const editIcon = '/images/edit-value.svg';
const trashIcon = '/images/trash-bin-red.svg';
const stethoscopeIcon = '/images/stethoscope.svg';
const usersIcon = '/images/users.svg';
const usersRedIcon = '/images/users-red.svg';

export interface IActionsCellProps {
  className?: string;
  scheinItem: ScheinItem;
  isAutoOpenTakeoverDiagnose: boolean;
  edit: ((scheinItem: ScheinItem) => void) | null;
  remove: ((scheinItem: ScheinItem) => void) | null;
  openInsuranceList: (insuranceId: string, isPrivateSchein: boolean) => void;
  reloadQuarters?: ReloadQuarterFunc;
  onSaveAutoSetting: () => void;
}

const ActionsCell: React.ComponentType<IActionsCellProps> = (props) => {
  const {
    className,
    scheinItem,
    isAutoOpenTakeoverDiagnose,
    edit,
    remove,
    reloadQuarters,
    openInsuranceList,
    onSaveAutoSetting,
  } = props;
  const { t } = I18n.useTranslation<keyof typeof ScheinI18n.scheinOverview>({
    namespace: 'Schein',
    nestedTrans: 'scheinOverview',
  });
  const { t: tButton } = I18n.useTranslation({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { patient } = usePatientFileStore();

  const isEditableSchein = useMemo(() => {
    if (!patient.current || !patient.current.patientInfo) {
      return false;
    }

    const patientInfo = patient.current.patientInfo;
    if (
      scheinItem.scheinMainGroup === MainGroup.KV &&
      patientInfo.genericInfo.patientType === PatientType.PatientType_Private
    ) {
      return false;
    }

    return true;
  }, [patient.current, scheinItem.scheinMainGroup]);

  const [referralDoctorDialogOpen, setReferralDoctorDialogOpen] =
    useState(false);
  const [isOpenTakeOverDiagnosis, setIsOpenTakeOverDiagnosis] =
    useState<boolean>(false);

  const { mutate: mutateDiagnosisTakeOver, isPending } =
    useMutationTakeOverDiagnosisByScheinId({
      onSuccess: () => {
        alertSuccessfully(t('diagnosisTookOver'));
        setIsOpenTakeOverDiagnosis(false);
      },
    });

  const mutationMarkAsReferral = useMutationMarkAsReferral({
    onSuccess: () => {
      alertSuccessfully(t('referralDoctorMarked'));
      reloadQuarters?.({
        quarter: scheinItem.g4101Quarter,
        year: scheinItem.g4101Year,
      });
      setReferralDoctorDialogOpen(false);
    },
  });

  const mutationRemoveReferral = useMutationRemoveReferral({
    onSuccess: () => {
      alertSuccessfully(t('referralDoctorRemoved'));
      reloadQuarters?.({
        quarter: scheinItem.g4101Quarter,
        year: scheinItem.g4101Year,
      });
    },
  });

  const handleMarkAsReferral = (data: SdavCatalog) => {
    mutationMarkAsReferral.mutate({
      scheinId: scheinItem.scheinId,
      referralDoctor: {
        lanr: data?.doctorInfo?.lanr,
        bsnr: data?.generalInfo?.bsnr,
      },
    });
  };
  const handleRemoveReferral = async () => {
    const confirmed = await askConfirmation();
    if (!confirmed) return;

    mutationRemoveReferral.mutate({
      scheinId: scheinItem.scheinId,
    });
  };
  const getMenuElement = (currentSchein: ScheinItem) => {
    const isPrivateSchein = currentSchein.scheinMainGroup === MainGroup.PRIVATE;

    return (
      <Menu key="menu-more">
        {edit ? (
          <MenuItem
            icon={<Svg src={editIcon} />}
            text={isPrivateSchein ? t('editPrivateSchein') : t('edit')}
            onClick={() => edit(scheinItem)}
          />
        ) : null}
        {scheinItem.scheinMainGroup !== MainGroup.BG ? (
          <MenuItem
            icon={<Svg src={editIcon} />}
            text={t('editInsurance')}
            onClick={() =>
              openInsuranceList(
                scheinItem.insuranceId,
                checkIsPrivateSchein(scheinItem)
              )
            }
          />
        ) : null}
        <MenuItem
          icon={<Svg src={stethoscopeIcon} />}
          text={t('diagnosisTakeover')}
          onClick={() => {
            setIsOpenTakeOverDiagnosis(true);
          }}
        />
        {scheinItem?.scheinMainGroup === MainGroup.FAV ? (
          <>
            {!scheinItem.referralDoctor && (
              <MenuItem
                icon={<Svg src={usersIcon} />}
                text={t('makeAsReferral')}
                onClick={() => {
                  setReferralDoctorDialogOpen(true);
                }}
              />
            )}
            {scheinItem.referralDoctor && (
              <MenuItem
                className="action-cell__remove-item"
                icon={<Svg src={usersRedIcon} />}
                text={t('removeReferral')}
                onClick={handleRemoveReferral}
              />
            )}
          </>
        ) : null}
        {remove ? (
          <MenuItem
            icon={<Svg src={trashIcon} />}
            text={t('remove')}
            onClick={() => remove(scheinItem)}
          />
        ) : null}
      </Menu>
    );
  };
  const { ConfirmationDialog, askConfirmation } = useConfirm({
    title: t('removeReferralDoctorTitle'),
    content: t('removeReferralDoctorMessage'),
    intent: Intent.DANGER,
    cancelButton: tButton('cancelText'),
    confirmButton: tButton('yesRemove'),
  });

  useEffect(() => {
    if (isAutoOpenTakeoverDiagnose) {
      setIsOpenTakeOverDiagnosis(true);
    }
  }, [isAutoOpenTakeoverDiagnose]);

  return (
    <>
      {isEditableSchein && (
        <Tooltip className={className} content={t('more')}>
          <Popover
            usePortal={true}
            position={PopoverPosition.TOP}
            content={getMenuElement(scheinItem)}
            minimal={true}
          >
            <Svg className="actions-cell__icon" src={moreIcon} />
          </Popover>
        </Tooltip>
      )}
      {isOpenTakeOverDiagnosis ? (
        <SelectDiagnosisDialog
          isLoading={isPending}
          patientId={patient.current?.id}
          scheinId={scheinItem.scheinId}
          takeoverDiagnosisType={TakeoverDiagnosisType.TakeoverDiagnosisSchein}
          quarter={scheinItem.g4101Quarter!}
          isAutoOpen={isAutoOpenTakeoverDiagnose}
          year={scheinItem.g4101Year!}
          onCancel={() => {
            setIsOpenTakeOverDiagnosis(false);
          }}
          isOpen={isOpenTakeOverDiagnosis}
          onSaveAutoSetting={onSaveAutoSetting}
          onTakeover={(
            existedDiagnosis,
            newDiagnosis,
            mappingTreatmentRelevent
          ) => {
            const takeOverDiagnoseIds = existedDiagnosis.map(
              (item) => item.id!
            );
            mutateDiagnosisTakeOver({
              scheinId: scheinItem.scheinId,
              takeOverDiagnoseInfos: takeOverDiagnoseIds.map((id) => ({
                id,
                isTreatmentRelevant: !!mappingTreatmentRelevent?.[id],
              })),
              newTakeOverDiagnosis: newDiagnosis as unknown as TimelineModel[],
            });
          }}
        />
      ) : null}
      <ConfirmationDialog />
      {referralDoctorDialogOpen ? (
        <SelectFavReferralDoctorDialog
          isOpen={referralDoctorDialogOpen}
          isSubmitting={mutationMarkAsReferral.isPending}
          setIsOpen={setReferralDoctorDialogOpen}
          onSubmit={handleMarkAsReferral}
        />
      ) : null}
    </>
  );
};

export default memo(ActionsCell);
