import {
  BodyTextM,
  Flex,
  InfoConfirmDialog,
  Svg,
  alertError,
} from '@tutum/design-system/components';
import {
  formatValueDateOfBirth,
  getAge,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MainGroup } from '@tutum/hermes/bff/common';
import { DoctorLetter } from '@tutum/hermes/bff/doctor_letter_common';
import {
  CreateDoctorLetterRequest,
  createDoctorLetter,
  getPdfPresignedUrl,
  useMutationHandleBgInvoice,
  useMutationHandlePrivateInvoice,
} from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import { useQueryGetOmimLicense } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { useQueryGetPointValueByYear } from '@tutum/hermes/bff/legacy/app_mvz_point_value';
import { useQueryGetById } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { useQueryGetBgBillingByScheinId } from '@tutum/hermes/bff/legacy/bg_billing';
import { useQueryGetPrivateBillingByScheinId } from '@tutum/hermes/bff/legacy/private_billing';
import {
  PrivateBillingItem,
  PrivateBillingStatus,
} from '@tutum/hermes/bff/legacy/private_billing_common';
import { ScheinStatus } from '@tutum/hermes/bff/legacy/schein_common';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import {
  checkIsBgSchein,
  checkIsPrivateSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import { printPreviewPdfActions } from '@tutum/mvz/hooks/usePrintPreviewPdf.store';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type EHKSI18n from '@tutum/mvz/locales/en/EHKS.json';
import {
  CommonActionChainElementId,
  actionChainActions,
  composerActionChainActions,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import { DoctorLetterCreateEditDialog } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog';
import {
  submitLetterForBgBilling,
  submitLetterForPrivateBilling,
} from '@tutum/mvz/module_doctor-letter/DoctorLetter.helper';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import EHKSDocumentationOverview from '@tutum/mvz/module_patient-management/patient-file/EHKSDocumentationOverview/EHKSDocumentationOverview.styled';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import PrivateBillingDialogContent from '@tutum/mvz/module_private_billing/components/PrivateBillingDialog/PrivateBillingDialogContent';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import { shouldCreatePrivateSchein } from '../PatientFile.helper';
import { patientFileActions, usePatientFileStore } from '../PatientFile.store';
import { timelineActions } from '../timeline/Timeline.store';
import { useActionBarStore } from './action-bar/ActionBar.store';
import Composer from './composer';
import {
  DiagnoseCommands,
  FreeTextCommands,
  ServiceChainCommand,
  AllCommands,
} from './composer/Composer.const';
import { BaseComposerRowType, IComposerRow } from './composer/Composer.type';
import { AdditionalContractsEnum } from '@tutum/hermes/bff/legacy/edmp_common';

const MODE_CREATE = 'create';
const MODE_SAVE = 'save';

const SendBaseIcon = '/images/encounter/send-base.svg';
interface PrivBillingEntryPayload {
  activeBilling: PrivateBillingItem;
  activeTimelineEntry: TimelineModel;
  invoiceEntryId?: string;
}

interface IComposerContainerProps {
  openCreateSchein: (scheinMaingRoup?: MainGroup) => void;
  reloadQuarters: ReloadQuarterFunc;
}

const ShortcutRenderer = ({ command, tComposer, isShowHintSubmit }) => {
  if (
    (!AllCommands.includes(command) || FreeTextCommands.includes(command)) &&
    !isShowHintSubmit
  ) {
    return (
      <Flex justify="flex-end" p={'0 16px'}>
        <BodyTextM fontWeight="SemiBold">
          {tComposer('controlSpace')}&nbsp;
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {tComposer('forTextmodule')}
        </BodyTextM>
      </Flex>
    );
  }

  if (
    [ServiceChainCommand, ...DiagnoseCommands].includes(
      command
    ) ||
    isShowHintSubmit
  ) {
    return (
      <Flex justify="flex-end" p={'0 16px'}>
        <Svg src={SendBaseIcon} />
        &nbsp;
        <BodyTextM fontWeight="SemiBold">
          {tComposer('controlEnter')}&nbsp;
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {tComposer('toSubmit')}
        </BodyTextM>
      </Flex>
    );
  }

  return null;
};
const ComposerContainer: React.FC<IComposerContainerProps> = ({
  openCreateSchein,
  reloadQuarters,
}) => {
  const { t: tDoctorLetter } = I18n.useTranslation({
    namespace: 'DoctorLetter',
  });
  const { t: tComposer } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: tConfirmDialog } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein.ConfirmDialog',
  });
  const { t: tEHKS } = I18n.useTranslation<keyof typeof EHKSI18n.confirmDialog>(
    {
      namespace: 'EHKS',
      nestedTrans: 'confirmDialog',
    }
  );

  const router = useRouter();

  const {
    patientManagement: { patient },
  } = React.useContext(PatientManagementContext.instance);

  const patientFileStore = usePatientFileStore();
  const actionBarStore = useActionBarStore();

  const isCurrentScheinPrivate = checkIsPrivateSchein(
    patientFileStore.schein?.activatedSchein
  );

  const isCurrentScheinBg = checkIsBgSchein(
    patientFileStore.schein?.activatedSchein
  );

  const [isOpenDoctorLetter, setOpenDoctorLetter] = useState<boolean>(false);
  const [isLoadedOmimLicense, setLoadedOmimLicense] = useState<boolean>(false);
  const [isShowHintSubmit, setShowHintSubmit] = useState<boolean>(false);
  const [isOpenCreateScheinDialog, setOpenCreateScheinDialog] =
    useState<boolean>(false);
  const [isOpenEHKSDocumentationOverview, setOpenEHKSDocumentationOverview] =
    useState<boolean>(false);
  const [isOpenConfirmEHKSDialog, setOpenConfirmEHKSDialog] =
    useState<boolean>(false);

  const [currentBlock, setCurrentBlock] =
    useState<Partial<IComposerRow> | null>(null);

  const privBilling = useQueryGetPrivateBillingByScheinId(
    {
      scheinId: patientFileStore.schein?.activatedSchein?.scheinId!,
    },
    {
      enabled: isOpenDoctorLetter && isCurrentScheinPrivate,
    }
  );

  const bgBilling = useQueryGetBgBillingByScheinId(
    {
      scheinId: patientFileStore.schein?.activatedSchein?.scheinId!,
    },
    {
      enabled: isOpenDoctorLetter && isCurrentScheinBg,
    }
  );

  const privTimelineEntry = useQueryGetById(
    {
      timelineId: privBilling.data?.item.currentInvoiceTimelineId!,
    },
    {
      enabled:
        isOpenDoctorLetter &&
        privBilling.data?.item.currentInvoiceTimelineId !== undefined,
    }
  );

  const HandlePrivateInvoice = useMutationHandlePrivateInvoice({ retry: 2 });
  const HandleBgInvoice = useMutationHandleBgInvoice({ retry: 2 });

  const onDoctorLetterDialogClose = () => {
    setOpenDoctorLetter(false);
  };

  const isPatientAgeUnder35 = useMemo(() => {
    return (
      (patient?.patientInfo?.personalInfo.dateOfBirth && getAge(
        formatValueDateOfBirth(patient?.patientInfo?.personalInfo.dateOfBirth)
      ) || -1) < 35
    );
  }, [patient?.patientInfo?.personalInfo.dateOfBirth]);

  const additionalContracts = useMemo(() => {
    return isPatientAgeUnder35
      ? AdditionalContractsEnum.AdditionalContractsEnum_Yes
      : AdditionalContractsEnum.AdditionalContractsEnum_No;
  }, [isPatientAgeUnder35]);

  const activatedSchein = useMemo(
    () => patientFileStore.schein.activatedSchein,
    [patientFileStore.schein.activatedSchein]
  );
  const contractByScheinOnSidebar = useMemo(
    () =>
      patientFileActions.getAvailableContractById(
        activatedSchein?.hzvContractId
      ),
    [activatedSchein?.hzvContractId, patientFileStore.availableContracts]
  );

  const {
    data: resOmimLicense,
    isSuccess,
    refetch,
  } = useQueryGetOmimLicense(
    {
      selectedDate: actionBarStore.encounterDate!,
      patientId: patient?.id!,
    },
    {
      enabled:
        !isLoadedOmimLicense &&
        !!actionBarStore.encounterDate &&
        !!patientFileStore.schein?.activatedSchein &&
        !isCurrentScheinPrivate,
    }
  );

  const year = actionBarStore?.encounterDate
    ? new Date(actionBarStore.encounterDate).getFullYear()
    : undefined!;

  const { data, isSuccess: isSuccessGetPointValue } =
    useQueryGetPointValueByYear(
      {
        year,
      },
      {
        enabled: !!year,
        select: (res) => res.data.data,
      }
    );

  useEffect(() => {
    if (isSuccess) {
      setLoadedOmimLicense(true);
    }
  }, [isSuccess]);

  useEffect(() => {
    if (isSuccessGetPointValue) {
      patientFileActions.setPointValue(data);
    }
  }, [isSuccessGetPointValue, data]);

  useEffect(() => {
    composerActionChainActions.setContract(contractByScheinOnSidebar);
  }, [contractByScheinOnSidebar]);

  useEffect(() => {
    // NOTE: notify to all action chain subscribers that composer is mounted - ready
    actionChainActions.setMountedSectionsStatus({ 'timline-composer': true });
    return () => {
      // NOTE: notify to all action chain subscribers that composer is unmounted - not ready
      actionChainActions.setMountedSectionsStatus({
        'timline-composer': false,
      });
    };
  }, []);

  if (
    (activatedSchein && activatedSchein?.markedAsBilled === true) ||
    patientFileStore.schein.originalList.length === 0
  ) {
    return null;
  }

  const onCreateDoctorLetter = async (payload: CreateDoctorLetterRequest) => {
    const resp = await createDoctorLetter(payload);
    if (
      !resp?.data?.timelineModel?.id ||
      !resp?.data?.timelineModel?.doctorLetter
    ) {
      alertError(tDoctorLetter('printFailure'));
      return null;
    }
    return resp.data.timelineModel;
  };

  const onCreatePrivateDoctorLetter = async (
    payload: CreateDoctorLetterRequest
  ) => {
    payload = {
      ...payload,
      patientId: patient?.id!,
      treatmentDoctorId: actionBarStore.doctorId!,
    };
    const timelineModel = await onCreateDoctorLetter(payload);
    return timelineModel;
  };

  const onCreatePublicDoctorLetter = async (values: DoctorLetter) => {
    const payload: CreateDoctorLetterRequest = {
      patientId: patient?.id!,
      doctorLetter: values,
      treatmentDoctorId: actionBarStore.doctorId!,
      scheinId: activatedSchein?.scheinId,
    };
    const timelineModel = await onCreateDoctorLetter(payload);
    if (timelineModel) {
      timelineActions.setTimelineIdCreated(timelineModel.id!);
    }
    return timelineModel;
  };

  const onSaveCreateDoctorLetter = async (doctorLetterValues: DoctorLetter) => {
    if (isCurrentScheinPrivate) {
      const updatedTimelineEntry = await submitLetterForPrivateBilling(
        activatedSchein?.scheinId!,
        patient?.id!,
        doctorLetterValues,
        HandlePrivateInvoice,
        MODE_SAVE,
        doctorLetterValues.type
      );
      return updatedTimelineEntry || undefined;
    }
    if (isCurrentScheinBg) {
      const updatedTimelineEntry = await submitLetterForBgBilling(
        activatedSchein?.scheinId!,
        patient?.id!,
        doctorLetterValues,
        HandleBgInvoice,
        MODE_SAVE,
        bgBilling?.data?.item.id
      );
      return updatedTimelineEntry || undefined;
    }
    return await onCreatePublicDoctorLetter(doctorLetterValues) || undefined;
  };

  const onPrintCreateDoctorLetter = async (
    editedTimelineModel?: TimelineModel
  ) => {
    if (!editedTimelineModel?.id || !editedTimelineModel?.doctorLetter?.id) {
      alertError(tDoctorLetter('printFailure'));
      return null;
    }
    const { data } = await getPdfPresignedUrl({
      doctorLetterId: editedTimelineModel.doctorLetter.id,
    });

    if (!data?.url) {
      alertError(tDoctorLetter('openPrintFailure'));
      return null;
    }

    // NOTE: open print preview dialog
    printPreviewPdfActions.setPrintingDoctorLetter({
      timelineModelId: editedTimelineModel.id,
      doctorLetter: editedTimelineModel.doctorLetter,
    });
    printPreviewPdfActions.setOpenPrintPreviewDialog(data.url);
  };

  const renderDoctorLetterDialog = (
    privateBilling: PrivateBillingItem,
    timelineEntry: TimelineModel
  ) => {
    if (!isOpenDoctorLetter) return <></>;

    const billingStatus = privBilling?.data?.item.status!;
    if (
      [
        PrivateBillingStatus.PrivBillingStatus_NoInvoice,
        PrivateBillingStatus.PrivBillingStatus_UnPaid,
        PrivateBillingStatus.PrivBillingStatus_1stReminder,
        PrivateBillingStatus.PrivBillingStatus_2ndReminder,
        PrivateBillingStatus.PrivBillingStatus_3rdReminder,
      ].includes(billingStatus)
    ) {
      //  Payload 1/2/3 reminder is same with NoInvoice
      const payload: PrivBillingEntryPayload = {
        activeBilling: privateBilling,
        activeTimelineEntry: timelineEntry,
        invoiceEntryId: timelineEntry?.id,
      };

      return (
        <PrivateBillingDialogContent
          t={tDoctorLetter}
          status={billingStatus}
          payload={payload}
          onClose={onDoctorLetterDialogClose}
          onSubmitDoctorLetter={onCreatePrivateDoctorLetter}
        />
      );
    }
  };

  const shouldShowCreateScheinDialog =
    isEmpty(activatedSchein) ||
    activatedSchein.scheinStatus === ScheinStatus.ScheinStatus_Billed ||
    activatedSchein.scheinStatus === ScheinStatus.ScheinStatus_Canceled;

  const getExcludedComposerCommands = (): string[] => {
    let res: string[] = [];
    if (router.pathname === ROUTING.BILLING_KV) {
      res = res.concat([
        BaseComposerRowType.DOCTOR_LETTER,
        BaseComposerRowType.DMP,
        BaseComposerRowType.EHKS,
        BaseComposerRowType.ACTION_CHAIN,
      ]);
    }
    return res;
  };

  return (
    <div
      className="sl-composer-container"
      {...registerActionChainElementId(CommonActionChainElementId.COMPOSER)}
    >
      <Composer
        openCreateSchein={openCreateSchein}
        contract={contractByScheinOnSidebar || undefined}
        excludedComposerCommands={getExcludedComposerCommands()}
        onSelectCommand={(cmd, clear) => {
          if (cmd === BaseComposerRowType.DOCTOR_LETTER) {
            if (shouldShowCreateScheinDialog) {
              setOpenCreateScheinDialog(true);
              return;
            }
            setOpenDoctorLetter(true);
            clear();
            return 'stop-propagate';
          }

          if (cmd === BaseComposerRowType.EHKS) {
            if (isPatientAgeUnder35) {
              setOpenConfirmEHKSDialog(true);
            } else {
              setOpenEHKSDocumentationOverview(true);
            }

            clear();
            return 'stop-propagate';
          }
          return 'continue';
        }}
        reloadQuarters={reloadQuarters}
        currentBlock={currentBlock}
        setCurrentBlock={setCurrentBlock}
        setShowHintSubmit={setShowHintSubmit}
      />
      {currentBlock?.command && (
        <ShortcutRenderer
          command={currentBlock?.command}
          tComposer={tComposer}
          isShowHintSubmit={isShowHintSubmit}
        />
      )}

      {/* DOCTOR LETTER CREATE DIALOG */}
      {isOpenDoctorLetter &&
        privBilling.data?.item &&
        !privTimelineEntry.isFetching &&
        renderDoctorLetterDialog(
          privBilling.data.item,
          privTimelineEntry.data?.timelineModel!
        )}

      {isOpenDoctorLetter && patient?.id && !isCurrentScheinPrivate && (
        <DoctorLetterCreateEditDialog
          mode={MODE_CREATE}
          activatedSchein={{
            type: 'public',
            scheinId: activatedSchein?.scheinId,
          }}
          successToastMessage={tDoctorLetter('saveSuccess')}
          patientId={patient.id}
          onSubmit={async (doctorLetterValues) => {
            return onSaveCreateDoctorLetter(doctorLetterValues);
          }}
          onPrint={async (editedTimelineModel) => {
            onPrintCreateDoctorLetter(editedTimelineModel);
          }}
          onClose={onDoctorLetterDialogClose}
        />
      )}
      {isSuccess && resOmimLicense && (
        <InfoConfirmDialog
          type="primary"
          isOpen={true}
          title={tComposer('titlePopupShowLicense')}
          isShowIconTitle
          onClose={() => { }}
          onConfirm={refetch}
          isCloseButtonShown={false}
          isCancelButtonShown={false}
          className="sl-OmimLicenseDialog"
          confirmText={tButtonActions('okText')}
        >
          {resOmimLicense.data.version}
        </InfoConfirmDialog>
      )}
      {isOpenCreateScheinDialog && (
        <InfoConfirmDialog
          type="primary"
          isOpen={isOpenCreateScheinDialog}
          title={tConfirmDialog('titleForDoctorLetter')}
          cancelText={tConfirmDialog('btnNo')}
          confirmText={tConfirmDialog('btnYes')}
          isShowIconTitle={false}
          isCloseButtonShown={true}
          isCancelButtonShown={false}
          onConfirm={() => {
            setOpenCreateScheinDialog(false);
            openCreateSchein(shouldCreatePrivateSchein(activatedSchein)!);
          }}
          onClose={() => {
            setOpenCreateScheinDialog(false);
          }}
        >
          <Flex column>{tConfirmDialog('descriptionForDoctorLetter')}</Flex>
        </InfoConfirmDialog>
      )}

      {isOpenEHKSDocumentationOverview && (
        <EHKSDocumentationOverview
          isOpen
          additionalContracts={additionalContracts}
          closeModal={() => setOpenEHKSDocumentationOverview(false)}
        />
      )}

      {isOpenConfirmEHKSDialog && (
        <InfoConfirmDialog
          type="primary"
          isOpen
          title={tEHKS('title')}
          cancelText={tButtonActions('no')}
          confirmText={tButtonActions('yesContinue')}
          isShowIconTitle={false}
          isCloseButtonShown={false}
          onConfirm={() => {
            setOpenConfirmEHKSDialog(false);
            setOpenEHKSDocumentationOverview(true);
          }}
          onClose={() => {
            setOpenConfirmEHKSDialog(false);
          }}
        >
          <Flex column>{tEHKS('description')}</Flex>
        </InfoConfirmDialog>
      )}
    </div>
  );
};

export default React.memo(ComposerContainer);
