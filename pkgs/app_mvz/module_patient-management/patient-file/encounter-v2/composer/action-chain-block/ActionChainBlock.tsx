import {
  BodyTextM,
  BodyTextS,
  Flex,
  InputSuggestion,
  Svg,
  coreComponents,
} from '@tutum/design-system/components';
import {
  DEFAULT_SELECT_COMPONENT_CONFIG,
  DEFAULT_SELECT_STYLE_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import ActionChainTriggerDialog from '@tutum/mvz/module_action-chain/action-chain-trigger-dialog';
import React, { memo, useRef, useState, useEffect, useMemo } from 'react';
import StyledActionChainWrapper, {
  StyledOption,
} from './ActionChainBlock.styled';
const EYE_ON_SQUARE_ICON = '/images/eye-on-square.svg';

import { Spinner } from '@tutum/design-system/components/Core';
import {
  ActionChainCategory,
  ServiceCategory,
} from '@tutum/hermes/bff/legacy/action_chain_common';
import {
  <PERSON><PERSON>hain,
  GetActionChainsRequest,
  getActionChains,
} from '@tutum/hermes/bff/legacy/app_admin_action_chain';
import { MainGroup } from '@tutum/hermes/bff/legacy/common';
import { ScheinItem } from '@tutum/hermes/bff/legacy/schein_common';
import I18n from '@tutum/infrastructure/i18n';
import ComposerCommandI18n from '@tutum/mvz/locales/en/ComposerCommand.json';
import PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import {
  actionChainActions,
  isStepInvalid,
  useActionChainStore,
} from '@tutum/mvz/module_action-chain';
import useCheckActionValidity from '@tutum/mvz/module_action-chain/action-chain-trigger-dialog/useCheckActionValidity';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import debounce from 'lodash/debounce';
import { useEmployeeStore } from '@tutum/mvz/hooks/useEmployee';

interface ActionChainBlockProps {
  scheinsFilteredByDate: ScheinItem[];
  autoFocus?: boolean;
  onClear: () => void;
  placeholder: string;
}

const CustomOption = memo(({ t, onClear, ...props }: any) => {
  const {
    data: { data },
  } = props;
  return (
    <coreComponents.Option {...props}>
      <StyledOption>
        <Flex justify="space-between" className="action-chain__block-wrapper">
          <div>
            <BodyTextM>{data.name}</BodyTextM>
            <BodyTextS>{`${t('description')}: ${data.description}`}</BodyTextS>
          </div>

          {props.isFocused && (
            <div
              className="sl-icon"
              onClick={(e) => {
                e.stopPropagation();
                actionChainActions.setSelectionActionChain(data);
                actionChainActions.setIsOpenActionChainTriggerDialog(true);
              }}
            >
              <Svg
                src={EYE_ON_SQUARE_ICON}
                alt="eye-on"
                width="22px"
                height="22px"
              />
            </div>
          )}
        </Flex>
      </StyledOption>
    </coreComponents.Option>
  );
});

const actionChainListParser = (actionChains: ActionChain[]) => {
  return (
    actionChains?.map((actionChain) => ({
      value: actionChain.id,
      label: actionChain.name,
      data: actionChain,
    })) ?? []
  );
};

const ActionChainBlock = (props: ActionChainBlockProps) => {
  const { scheinsFilteredByDate, autoFocus, onClear, placeholder } = props;
  const { schein } = usePatientFileStore();
  const scheinOnSidebar = schein.activatedSchein;
  const actionChainStore = useActionChainStore();
  const currentProcessActionChain = actionChainStore.currentProcessActionChain;
  const isOpenActionChainTriggerDialog =
    actionChainStore.isOpenActionChainTriggerDialog;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.ActionChainTrigger
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'ActionChainTrigger',
  });
  const { t: commandTrans } = I18n.useTranslation<
    keyof typeof ComposerCommandI18n
  >({
    namespace: 'ComposerCommand',
  });

  const { diagnosis, serviceCodes, goaServiceCodes } = useMemo(() => {
    const actionChainSteps = currentProcessActionChain?.steps ?? [];
    const diagnosis: string[] = [];
    const serviceCodes: string[] = [];
    const goaServiceCodes: string[] = [];

    for (const step of actionChainSteps) {
      switch (step.stepCategory) {
        case ActionChainCategory.ActionChainCategory_Service:
          if (step.service?.service?.code) {
            serviceCodes.push(step.service.service.code);
          }
          if (step.service?.goaService?.code) {
            goaServiceCodes.push(step.service.goaService.code);
          }
          break;
        case ActionChainCategory.ActionChainCategory_AcuteDiagnose:
        case ActionChainCategory.ActionChainCategory_AnamnesticDiagnose:
        case ActionChainCategory.ActionChainCategory_PermanentDiagnose:
          if (step.diagnose?.diagnose?.code) {
            diagnosis.push(step.diagnose.diagnose.code);
          }
          break;
        case ActionChainCategory.ActionChainCategory_ServiceChain:
          if (
            step.serviceChain.serviceCategory ===
            ServiceCategory.Service_SelectiveContract ||
            step.serviceChain.serviceCategory === ServiceCategory.Service_EBM
          ) {
            if (step.serviceChain?.services?.length) {
              serviceCodes.push(
                ...step.serviceChain.services.map((service) => service.code)
              );
            }
          } else {
            if (step.serviceChain?.services?.length) {
              goaServiceCodes.push(
                ...step.serviceChain.services.map((service) => service.code)
              );
            }
          }
          break;
        default:
          break;
      }
    }
    return { diagnosis, serviceCodes, goaServiceCodes };
  }, [currentProcessActionChain]);

  const currentSchein: ScheinItem | undefined = useMemo(() => {
    const scheinInSidebarId = scheinOnSidebar?.scheinId ?? null;
    return (
      (scheinInSidebarId != null &&
        scheinsFilteredByDate.find(
          (sch) => sch.scheinId === scheinInSidebarId
        )) ||
      scheinOnSidebar
    );
  }, [scheinsFilteredByDate, scheinOnSidebar]);

  const {
    restrictedSteps,
    invalidSteps,
    isLoadingValidation,
    setRestrictedStep,
  } = useCheckActionValidity({
    activatedSchein: currentSchein!,
    diagnosis,
    serviceCodes,
    goaServiceCodes,
  });

  const [isShow, setIsShow] = useState(false);
  const actionChainSelectRef = useRef<any>(null);
  const [defaultList, setDefaultList] = useState<
    { value: string | undefined; label: string; data: ActionChain }[]
  >([]);
  const { userProfile } = useEmployeeStore();
  const setting = useSettingStore();

  useEffect(() => {
    (async () => {
      if (!userProfile?.bsnrId) {
        return;
      }
      const request: GetActionChainsRequest = {
        query: '',
        page: 1,
        pageSize: 30,
        bsnrId: userProfile?.bsnrId,
      };
      const response = await getActionChains(request);
      const parsedActionList = actionChainListParser(
        response?.data?.actionChains ?? []
      );
      setDefaultList(parsedActionList);
      setIsShow(Boolean(parsedActionList?.length));
    })();
    return () => {
      actionChainActions.setSelectionActionChain(null);
    };
  }, [userProfile?.bsnrId]);

  const searchActionChain = debounce(
    async (query: string, cb: (actionChains: ActionChain[]) => void) => {
      const request: GetActionChainsRequest = {
        query,
        page: 1,
        pageSize: 10,
        bsnrId: userProfile?.bsnrId,
      };
      const response = await getActionChains(request);
      cb(response.data.actionChains);
    },
    300
  );

  const runActionChain = (data: ActionChain) => {
    const actionChain = { ...data };
    actionChain.steps = actionChain.steps.filter(
      (step) =>
        !restrictedSteps[step['id']] && !isStepInvalid(step, invalidSteps)
    );

    actionChainActions.handleRunActionChain(commandTrans, actionChain);
    onClear();
  };

  const onChange = (selected) => {
    if (!selected) {
      return;
    }
    actionChainActions.setSelectionActionChain(selected.data);
  };

  const shouldOpenDialog = [
    MainGroup.PRIVATE,
    MainGroup.FAV,
    MainGroup.HZV,
  ].includes(currentSchein?.scheinMainGroup!)
    ? false
    : goaServiceCodes.length > 0 ||
    serviceCodes.length > 0 ||
    diagnosis.length > 0;

  useEffect(() => {
    if (!currentProcessActionChain) {
      return;
    }
    if (isLoadingValidation || isOpenActionChainTriggerDialog) {
      return;
    }
    if (!shouldOpenDialog) {
      runActionChain(currentProcessActionChain);
      return;
    }
    actionChainActions.setIsOpenActionChainTriggerDialog(true);
  }, [
    currentProcessActionChain,
    currentSchein?.scheinMainGroup,
    isLoadingValidation,
    shouldOpenDialog,
    isOpenActionChainTriggerDialog,
    restrictedSteps,
    invalidSteps,
  ]);

  if (isLoadingValidation) {
    return <Spinner size={22} />;
  }

  return (
    <StyledActionChainWrapper column auto>
      <InputSuggestion
        tabSelectsValue={false}
        menuIsOpen={isShow}
        isClearable
        isSearchable
        classNamePrefix="sl-action-chain-block"
        autoFocus={autoFocus}
        menuPortalTarget={document.body}
        styles={{
          ...DEFAULT_SELECT_STYLE_CONFIG(setting.timelineSetting.scaleNumber),
          menu: (base) => ({
            ...base,
            maxWidth: '704px',
            maxHeight: '400px',
          }),
        }}
        placeholder={placeholder}
        components={{
          ...DEFAULT_SELECT_COMPONENT_CONFIG,
          Option: (props) => (
            <CustomOption {...props} onClear={onClear} t={t} />
          ),
        }}
        defaultOptions={defaultList}
        loadOptions={(query, setOptions) => {
          searchActionChain(query, (actionChains) => {
            setIsShow(Boolean(actionChains?.length));
            setOptions(actionChainListParser(actionChains));
          });
        }}
        keyCode="label"
        onChange={onChange}
        ref={actionChainSelectRef}
        onKeyDown={(event) => {
          if (event.key === 'Escape') {
            setIsShow(false);
          }
          if (
            event.key === 'Backspace' &&
            !actionChainSelectRef?.current?.inputRef?.value
          ) {
            actionChainActions.setSelectionActionChain(null);
            onClear();
          }
          if (event.key === 'Tab') {
            const _data: ActionChain =
              actionChainSelectRef?.current?.state?.focusedOption?.data;
            if (_data != null) {
              event.preventDefault();
              setIsShow(false);
              actionChainActions.setSelectionActionChain(_data);
              onClear();
            }
          }
        }}
        onBlur={() => setIsShow(false)}
      />
      <ActionChainTriggerDialog
        stepValidation={{
          restrictedSteps,
          invalidSteps,
          isLoadingValidation,
          setRestrictedStep,
          onClear,
        }}
      />
    </StyledActionChainWrapper>
  );
};

export default memo(ActionChainBlock);
