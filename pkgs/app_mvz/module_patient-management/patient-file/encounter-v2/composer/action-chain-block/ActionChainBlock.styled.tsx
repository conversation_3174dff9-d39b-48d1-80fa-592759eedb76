import { styled } from '@tutum/design-system/models';
import { Flex } from '@tutum/design-system/components/Flexbox';
import { COLOR, mixTypography } from '@tutum/design-system/themes/styles';
import ComposerUtil from '@tutum/design-system/composer/Composer.util';
import Theme from '@tutum/mvz/theme';

const StyledActionChainWrapper = Theme.styled(Flex)`
  ${mixTypography('composer')};
  caret-color: ${COLOR.BACKGROUND_SELECTED_STRONG};
  .sl-row {
    display: flex;
    align-items: center;
    height: 24px;
    padding: 0px;
  }
  p,
  .sl-InputSuggestion__input {
    font-size: ${(props) =>
    ComposerUtil.parseCustomFontsize(
      props.theme.timelineTheme!.scaleNumber
    )} !important;
  }

  .sl-dropdown {
    gap: 4px;
  }
  .sl-action-chain-block__input {
    opacity: 1 !important;
  }
  .sl-action-chain-block__input-container {
    &::after {
      display: none;
    }
  }
`;

const StyledOption = styled('div')`
  display: flex;
  color: ${COLOR.TEXT_PRIMARY_BLACK};
  padding: 0 8px;
  .sl-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px;
  }
  .action-chain__block-wrapper {
    width: 100%;
  }
`;
export { StyledOption };
export default StyledActionChainWrapper;
