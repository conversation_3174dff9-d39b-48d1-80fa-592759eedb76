import React, { useRef, useState, useEffect } from 'react';
import { Flex, Box } from '@tutum/design-system/components';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { IMvzTheme } from '@tutum/mvz/theme';
import PopoverContentEditable from '../popover-content-editable/PopoverContentEditable.styled';
import RowTypeBlockService, { IRowTypeList } from './RowTypeBlock.service';
export interface IRowTypeBlockProps {
  className?: string;
  theme?: IMvzTheme;
  disabled?: boolean;
  command: string;
  onRowTypeChange: (rowType: string, command: string) => void;
}

const OriginRowTypeBlock = React.memo(
  ({
    t,
    className,
    command,
    onRowTypeChange,
    disabled,
  }: IRowTypeBlockProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.Composer>) => {
    const [value, setValue] = useState<string>(command);

    useEffect(() => {
      setValue(command);
    }, [command]);
    const [openRowType, setOpenRowType] = useState<boolean>(false);
    const rowTypeEl = useRef<any>(null);

    const _onKeyDown = () => {
      setOpenRowType(true);
    };

    const _onInput = (event?: React.FormEvent<HTMLSpanElement>, t?: string) => {
      event?.preventDefault();
      setValue(t!);
      setOpenRowType(true);
    };

    const _onBlur = (
      _event: React.FocusEvent<HTMLSpanElement>,
      inputData = ''
    ) => {
      setValue(inputData || command);
      setOpenRowType(false);
    };

    const onTab = () => {
      setOpenRowType(false);
    };

    const handleOnRowTypeSelect = (
      item: IRowTypeList,
      event?: React.SyntheticEvent<HTMLElement>
    ) => {
      if (!event) {
        return;
      }
      onRowTypeChange(item.code!, item.commands[0]!);
      setOpenRowType(false);
    };

    return (
      <PopoverContentEditable
        className={className}
        inputRef={rowTypeEl}
        popOverProps={{ isOpen: openRowType, canEscapeKeyClose: true }}
        listProps={{
          menuItemRenderer: ({ item }: any) => (
            <Flex>
              <Box
                auto
                className="bp5-text-overflow-ellipsis bp5-fill menuItemLeftValue"
              >
                {item.title}
              </Box>
              <Box className="bp5-menu-item-label">
                {item.commands.join(', ')}
              </Box>
            </Flex>
          ),
          items: RowTypeBlockService.filterRowTypeList(value),
          onItemSelect: handleOnRowTypeSelect,
        }}
        inputProps={{
          className: `rowType`,
          onInput: _onInput,
          onKeyDown: _onKeyDown,
          onBlur: _onBlur,
          disabled,
        }}
        placeholder={t('command')}
        value={value}
        onTab={onTab}
      />
    );
  }
);

export default I18n.withTranslation(OriginRowTypeBlock, {
  namespace: 'PatientManagement',
  nestedTrans: 'Composer',
});
