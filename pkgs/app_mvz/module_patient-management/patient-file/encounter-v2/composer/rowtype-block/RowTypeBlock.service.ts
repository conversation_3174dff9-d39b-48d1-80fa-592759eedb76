import { isEmpty } from '@tutum/design-system/infrastructure/utils';
import { IDropDownData } from '../../../PatientFile.type';
import { BaseComposerRowType, ComposerRowCommand } from '../Composer.type';

export interface IRowTypeList extends IDropDownData {
  commands: ComposerRowCommand[];
}

const getRowTypeList = (): IRowTypeList[] => [
  {
    description: 'Anamnese',
    code: BaseComposerRowType.ANAMNESE,
    commands: ['A'],
  },
  {
    description: 'Anamnestische Diagnose',
    code: BaseComposerRowType.ANAMNESTIC_DIAGNOSE,
    commands: ['AD'],
  },
  {
    description: 'Befund',
    code: BaseComposerRowType.FINDINGS,
    commands: ['B'],
  },
  {
    description: 'DMP',
    code: BaseComposerRowType.DMP,
    commands: ['DMP'],
  },
  {
    description: 'Akutdiagnose',
    code: BaseComposerRowType.ACUTE_DIAGNOSE,
    commands: ['D'],
  },
  {
    description: 'Dauerdiagnose',
    code: BaseComposerRowType.PERMANENT_DIAGNOSE,
    commands: ['DD'],
  },
  {
    description: 'Leistung',
    code: BaseComposerRowType.SERVICE,
    commands: ['L'],
  },
  {
    description: 'Therapie',
    code: BaseComposerRowType.THERAPY,
    commands: ['T'],
  },
  {
    description: 'Notiz',
    code: BaseComposerRowType.NOTES,
    commands: ['N'],
  },
];

const getRowTypeFromCommand = (command = '') => {
  return (
    getRowTypeList().find((rowType) => {
      return (
        rowType.commands.findIndex(
          (cmd: string) => command.toLowerCase() === cmd.toLowerCase()
        ) !== -1
      );
    }) || ({} as IRowTypeList)
  );
};

const filterRowTypeList = (query = '') => {
  const rowTypeList = getRowTypeList();
  if (isEmpty(query)) {
    return rowTypeList;
  }

  return rowTypeList.filter((item) => {
    const normalizedDesc = item.description!.toLowerCase();
    const normalizedQuery = query.toLowerCase();
    return (
      item.commands.find((command) =>
        command.toLowerCase().includes(normalizedQuery)
      ) !== undefined || normalizedDesc === normalizedQuery
    );
  });
};

export default { getRowTypeFromCommand, filterRowTypeList };
