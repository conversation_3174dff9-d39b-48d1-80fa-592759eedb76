import React, {
  useState,
  useEffect,
  useMemo,
  useContext,
  useCallback,
} from 'react';
import debounce from 'lodash/debounce';
import isEmpty from 'lodash/isEmpty';
import Router from 'next/router';

import {
  BodyTextL,
  BodyTextM,
  Button,
  Flex,
  InfoConfirmDialog,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import type { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import type { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import type { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import ServiceBlockService from '../service-block/services/service-block.service';
import ServiceMetaService from '../service-block/services/service-meta.service';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { parseToScheinMainGroup } from '../Composer.util';
import GoaServiceNodeComponent from '@tutum/design-system/composer/goa-service-node-component';
import AdditionalInfoAutoFillDefaultDataPlugin from '../lexical/plugins/additional-info-auto-fill-default-data-plugin';
import ScheinBlock from '@tutum/mvz/components/schein-block';
import { Dialog, Classes } from '@tutum/design-system/components/Core';
import CreateMaterialCostDialog from '@tutum/mvz/module_sdebm/material-cost-dialog/MaterialCostDialog.styled';
import { ROUTING } from '@tutum/mvz/types/route.type';
import type { EncounterGoaService } from '@tutum/hermes/bff/repo_encounter';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { AdditionalInfoAutoTransformListNodePlugin } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-auto-transform-list-node-plugin';
import { ASV_KEY } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import MaterialCostService from '@tutum/infrastructure/web-worker-services/material-cost.service';
import HospitalsPracticesDialog from '@tutum/mvz/module_kv_hzv_schein/hospitals-practices-dialog';
import { CreateExternalAddressDialog } from '@tutum/mvz/module_external-address/external-address-overview/CreateExternalAddressDialog';
import CreateJusitficationModal from './justification-modal';
import type { IMenuItemWithData } from '@tutum/design-system/components';
import { useActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import { groupQuarterbySchein } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  saveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '../Composer.const';
import { useComposerActionChainStore } from '@tutum/mvz/module_action-chain';

export interface IGoaServiceBlockProps {
  className?: string;
  initEncounterId?: string;
  encounterDate: number;
  data: EncounterGoaService;
  doctorProfile?: IEmployeeProfile;
  defaultContract?: IContractInfo;
  patient?: IPatientProfile;
  disabled?: boolean;
  onChange: (data: EncounterGoaService) => Promise<void>;
  scheinsFilteredByDate: ScheinItem[];
  onSubmit(payload: EncounterGoaService): Promise<void>;
  onClear: () => void;
  openCreateSchein: (scheinMaingRoup?: MainGroup) => void;
}

interface IConfirmOpenCreateDialogProps {
  isOpen: boolean;
  onClose(): void;
  onConfirmClick(): void;
}

const ConfirmOpenCreateDialog: React.FC<IConfirmOpenCreateDialogProps> = (
  props
) => {
  const { t: tSdebm } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'MaterialCostDialog',
  });

  return (
    <Dialog
      isOpen={props.isOpen}
      onClose={props.onClose}
      style={{ borderRadius: 4 }}
      canOutsideClickClose={false}
    >
      <Flex className={Classes.DIALOG_BODY} column gap={24}>
        <BodyTextL fontWeight={'Bold'} fontSize={20}>
          {tSdebm('createMaterialCostTitle')}
        </BodyTextL>
        {tSdebm('leaveCreateMaterialCostContent')}
        <Flex gap={16}>
          <Button
            onClick={props.onClose}
            minimal
            outlined
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('cancelCreate')}
          </Button>
          <Button
            fill
            onClick={props.onConfirmClick}
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('confirmCreateMaterialCost')}
          </Button>
        </Flex>
      </Flex>
    </Dialog>
  );
};

const GoaServiceBlock: React.FC<IGoaServiceBlockProps> = (props) => {
  const {
    initEncounterId,
    data,
    patient,
    defaultContract,
    onSubmit,
    disabled,
    onChange,
    className,
    encounterDate,
    doctorProfile,
    scheinsFilteredByDate,
    onClear,
    openCreateSchein,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tConfirmDialog } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein.ConfirmDialog',
  });

  const { data: listSettingJustification, refetch } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });
  const composerActionChainStore = useComposerActionChainStore();
  const actionBarStore = useActionBarStore();
  const globalContext = useContext(GlobalContext.instance);
  const documentedDoctor = globalContext.getDoctorById(actionBarStore.doctorId!);

  const { useGetDoctorList } = useContext(GlobalContext.instance);
  const doctorLists = useGetDoctorList();

  const patientFileStore = usePatientFileStore();
  const scheinOnSidebar = patientFileStore?.schein?.activatedSchein;
  const {
    patientManagement: { selectedContractDoctor },
  } = useContext(PatientManagementContext.instance);

  const [filteredAdditionalInfos, setFilteredAdditionalInfos] = useState<
    AdditionalInfoField[]
  >([]);
  const [isOpenCreateMaterialCost, setIsOpenCreateMaterialCostDialog] =
    useState(false);
  const [isOpenCreateJustification, setIsOpenCreateJustification] =
    useState(false);
  const [isOpenConfirmDialog, setIsOpenConfirmDialog] = useState(false);
  const [currentContract] = useState<IContractInfo | null>(
    defaultContract ?? null
  );
  const [isOpenHospitalsPractices, setIsOpenHospitalsPractices] =
    useState(false);
  const [isOpenCreateDoctor, setIsOpenCreateDoctor] = useState(false);

  const setUpdatedService = (updatedService: EncounterGoaService) => {
    const _service = { ...updatedService };
    onChange(_service);
    return _service;
  };

  const [selectedSchein, setSelectedSchein] = useState<ScheinItem | undefined>(
    undefined
  );

  const [isOpenConfirmScheinDialog, setOpenConfirmScheinDialog] =
    useState<boolean>(false);
  const [currentPayload, setCurrentPayload] =
    useState<EncounterGoaService | null>(null);

  const currentSchein = useMemo(() => {
    const singleScheinId = patientFileStore.schein.activatedSchein?.scheinId; // NOTE: only single Schein select on service code
    return (
      (singleScheinId != null &&
        scheinsFilteredByDate.find((sch) => sch.scheinId === singleScheinId)) ||
      scheinOnSidebar
    );
  }, [
    patientFileStore.schein.activatedSchein,
    scheinsFilteredByDate,
    scheinOnSidebar,
  ]);

  const onServiceSelect = async (item: IContractData) => {
    const resultAfterValidate =
      await ServiceMetaService.getServiceMetaVersionGoa(item);
    return resultAfterValidate;
  };

  const onSetScheinIds = (scheinItems: ScheinItem[] = []) => {
    const payload: EncounterGoaService = {
      ...data,
      scheins: parseToScheinMainGroup(scheinItems),
    };
    setSelectedSchein(scheinItems[0]);
    setUpdatedService(payload);
  };

  const searchServiceData = useMemo(
    () =>
      debounce(async (query: string, cb: (result: IContractData[]) => void) => {
        let isPrivate =
          patient?.patientInfo.genericInfo.patientType ===
          PatientType.PatientType_Private;
        if (patientFileStore.schein.activatedSchein) {
          isPrivate = checkIsPrivateSchein(
            patientFileStore.schein.activatedSchein
          );
        }
        const result = await ServiceBlockService.searchService(
          encounterDate,
          query,
          doctorProfile,
          [],
          currentContract,
          selectedContractDoctor,
          isPrivate,
          selectedSchein?.scheinMainGroup
        );

        cb(result.slice(0, 20));
      }, 500),
    [
      patient,
      doctorProfile,
      encounterDate,
      selectedContractDoctor,
      selectedSchein,
    ]
  );

  const NoResults = () => {
    return (
      <Flex className="sl-no-results" p="8px 16px" justify="center" gap={8}>
        <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
          {t('noResultsFound')}
        </BodyTextM>
      </Flex>
    );
  };

  const { t: translator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const memoizedDoctorASVNumbers = useMemo(
    () =>
      doctorLists.reduce((asvNumbers, doctor) => {
        if (doctor.teamNumbers?.length) {
          return asvNumbers.concat(doctor.teamNumbers);
        }
        return asvNumbers;
      }, [] as string[]),
    [doctorLists]
  );

  useEffect(() => {
    catalogOverviewActions
      .getAdditionalInfos(
        currentSchein?.scheinMainGroup!,
        currentSchein?.kvTreatmentCase!
      )
      .then((fields) => {
        const infors = fields.map((info) => {
          info.label = translator(info.fK);
          return info;
        });
        setFilteredAdditionalInfos(infors);
      });
  }, [currentSchein]);

  const filterScheinFunc = useCallback(
    (_option: ScheinItem) =>
      currentSchein?.scheinMainGroup === _option?.scheinMainGroup,
    [currentSchein]
  );

  const autoFillInfos = useMemo(() => {
    // service codes of KV patient
    if (['03008', '04008'].includes(data?.code)) {
      return [
        {
          fK: '5003',
          value: doctorProfile?.bsnr!,
        },
      ];
    }
    return [];
  }, [data?.code, doctorProfile]);

  const onCloseHospitalsPractices = useCallback(() => {
    setIsOpenHospitalsPractices(false);
  }, []);

  const createListInsuranceMapBySchein = useMemo(() => {
    const listInsurance =
      patientFileStore.patient.current?.patientInfo.insuranceInfos ?? [];

    const listScheinId = scheinsFilteredByDate.map(
      (schein) => schein.insuranceId
    );

    const res = listInsurance?.filter((insur) =>
      listScheinId.includes(insur.id)
    );
    const groupQuarterBySchein = groupQuarterbySchein(
      scheinsFilteredByDate,
      res
    );

    return groupQuarterBySchein;
  }, [scheinsFilteredByDate]);

  useEffect(() => {
    const defaultSelectedScheinId = initEncounterId
      ? data.scheins?.[0]?.scheinId
      : patientFileStore?.schein?.activatedSchein?.scheinId;

    // with edit encounters entry no schein we do not auto bind schein valid
    const scheinValid =
      scheinsFilteredByDate.find(
        (schein) => schein.scheinId === defaultSelectedScheinId
      ) ||
      (scheinsFilteredByDate?.length === 1 && !!data.scheins?.[0]?.scheinId
        ? scheinsFilteredByDate[0]
        : ({} as ScheinItem));

    setSelectedSchein(scheinValid);

    // TODO: Check logic auto bind first schein valid in quarter
    onChange({
      ...data,
      scheins:
        typeof scheinValid !== 'undefined'
          ? [
            {
              scheinId: scheinValid.scheinId,
              group: scheinValid.scheinMainGroup,
            },
          ]
          : [],
    });
  }, [
    initEncounterId,
    JSON.stringify(scheinsFilteredByDate),
    patientFileStore?.schein?.activatedSchein,
  ]);

  const onHandleSubmit = async (payload: EncounterGoaService) => {
    if (!scheinsFilteredByDate[0] || isEmpty(selectedSchein)) {
      setCurrentPayload(payload);
      setOpenConfirmScheinDialog(true);
      return;
    }
    // for flow Action chain
    let additionalInfosRaw = payload.additionalInfosRaw
      ? JSON.parse(payload.additionalInfosRaw)
      : null;
    let additionalInfos = payload.additionalInfos ?? [];

    if (composerActionChainStore.currentBlock) {
      const {
        additionalInfosRaw: additionalInfosRawAC,
        additionalInfos: additionalInfosAC,
      } = composerActionChainStore.currentBlock;

      if (additionalInfosRawAC) {
        additionalInfosRaw = { ...JSON.parse(additionalInfosRawAC) };
      }
      if (!isEmpty(additionalInfosAC)) {
        additionalInfos = [...additionalInfosAC!];
      }
      if (additionalInfosRaw) {
        additionalInfosRaw = {
          ...additionalInfosRaw,
          root: {
            ...additionalInfosRaw.root,
            children: additionalInfosRaw.root.children.map((child) => {
              child.children = child.children.map((item) => item);
              return child;
            }),
          },
        };
      }
    }

    onSubmit(
      setUpdatedService({
        ...payload,
        additionalInfos,
        additionalInfosRaw: additionalInfosRaw
          ? JSON.stringify(additionalInfosRaw)
          : undefined,
      })
    );
  };

  return (
    <Flex className={className} column gap={0}>
      <GoaServiceNodeComponent
        id="service_node_composer"
        placeholder={t('typeService')}
        additionalInfoEditorplaceholder={t('typeParenthesis', {
          key: '(',
        })}
        disabled={disabled}
        data={data}
        onChange={onChange}
        onSubmit={onHandleSubmit}
        onClear={onClear}
        additionalFields={filteredAdditionalInfos}
        searchService={searchServiceData as any}
        onServiceSelect={onServiceSelect}
        // OPTIONALS PROPS
        encounterDate={encounterDate}
        noResultsComponent={<NoResults />}
        rightElement={
          data?.freeText &&
            data?.code ? (
            <ScheinBlock
              selectedSchein={selectedSchein}
              openCreateSchein={() => openCreateSchein(MainGroup.PRIVATE)}
              onSetScheinIds={onSetScheinIds}
              scheinFilter={createListInsuranceMapBySchein}
              filterOption={filterScheinFunc}
            />
          ) : undefined
        }
        pointValue={patientFileStore.pointValue}
        scheinId={selectedSchein?.scheinId!}
      >
        {/* LEXICAL PLUGINS AS CHILDREN */}
        <AdditionalInfoAutoFillDefaultDataPlugin
          schein={currentSchein}
          autoFillInfos={autoFillInfos}
        />
        <AdditionalInfoMaterialCostPlugin
          onSearch={async (query, setData) => {
            const _searchQuery = query || '*';
            const results =
              (await MaterialCostService.searchMaterialCost(_searchQuery)) ??
              [];
            setData(results);
            return results;
          }}
          onCreateMaterialCostClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenConfirmDialog(true);
              return;
            }
            setIsOpenCreateMaterialCostDialog(true);
          }}
          onViewAllMaterialCostClick={() =>
            Router.push(ROUTING.MATERIAL_COST_CATALOG)
          }
        >
          {({ onNewMaterialCostAdded }) => (
            <>
              {isOpenCreateMaterialCost && (
                <CreateMaterialCostDialog
                  isOpen={isOpenCreateMaterialCost}
                  onClose={() => {
                    setIsOpenCreateMaterialCostDialog(false);
                  }}
                  onCreateSuccess={onNewMaterialCostAdded}
                />
              )}
            </>
          )}
        </AdditionalInfoMaterialCostPlugin>

        <AdditionalInfoJusification
          onSearch={(query, setData) => {
            const _searchQuery = query || '*';
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const results = listJustification.filter((item) =>
              item.includes(_searchQuery)
            );
            setData(results);
            return results;
          }}
          onCreateJustificationClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenCreateJustification(true);
              return;
            }
            setIsOpenCreateJustification(true);
          }}
          onDeletedJustificationClick={async (value: string) => {
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const newListJustification = listJustification.filter(
              (v) => v !== value
            );
            await saveSettings({
              settings: {
                [SETTING_KEY_ALLERGIES_FOR_5009]:
                  JSON.stringify(newListJustification),
              },
            });
            await refetch();
          }}
        >
          {({ onNewJustificationAdded }) => (
            <>
              {isOpenCreateJustification && (
                <CreateJusitficationModal
                  isOpen={isOpenCreateJustification}
                  className="stage-modal-justification"
                  onCreateSuccess={async (ws) => {
                    onNewJustificationAdded(ws);
                    const listJustification: string[] = JSON.parse(
                      listSettingJustification?.settings?.[
                      SETTING_KEY_ALLERGIES_FOR_5009
                      ] || '[]'
                    );
                    listJustification.push(ws!);
                    await saveSettings({
                      settings: {
                        [SETTING_KEY_ALLERGIES_FOR_5009]:
                          JSON.stringify(listJustification),
                      },
                    });
                    refetch();
                    setIsOpenCreateJustification(false);
                  }}
                  onClose={() => {
                    setIsOpenCreateJustification(false);
                  }}
                  t={t}
                />
              )}
            </>
          )}
        </AdditionalInfoJusification>
        {/* NOTE: auto-transform ASV teamnumber add.info block to list mode */}
        <AdditionalInfoAutoTransformListNodePlugin<string>
          targetFk={ASV_KEY}
          documentedDoctor={documentedDoctor}
          onQuery={async (query, setData) => {
            const menuItems = memoizedDoctorASVNumbers
              .filter((asv) => asv.includes(query))
              .map(
                (asv) =>
                  ({
                    id: DatetimeUtil.now(),
                    label: asv,
                    value: asv,
                    data: asv,
                  }) as IMenuItemWithData<string>
              );
            setData(menuItems);
            return menuItems;
          }}
        />
      </GoaServiceNodeComponent>

      {/* CONFIRM ADD NEW MATERIAL COST DIALOG */}
      <ConfirmOpenCreateDialog
        isOpen={isOpenConfirmDialog}
        onClose={() => {
          setIsOpenConfirmDialog(false);
          // _focusOnFirstInput();
        }}
        onConfirmClick={() => {
          setIsOpenConfirmDialog(false);
          setIsOpenCreateMaterialCostDialog(true);
        }}
      />
      {isOpenHospitalsPractices && (
        <HospitalsPracticesDialog
          isOpen={isOpenHospitalsPractices}
          onClose={onCloseHospitalsPractices}
          onSelect={() => {
            onCloseHospitalsPractices();
          }}
        />
      )}
      {isOpenCreateDoctor && (
        <CreateExternalAddressDialog
          isOpen={isOpenCreateDoctor}
          onClose={() => {
            setIsOpenCreateDoctor(false);
          }}
          onSuccess={() => null}
          onError={() => null}
        />
      )}
      <InfoConfirmDialog
        type="primary"
        isOpen={isOpenConfirmScheinDialog}
        title={tConfirmDialog('title')}
        cancelText={tConfirmDialog('btnNo')}
        confirmText={tConfirmDialog('btnYes')}
        isShowIconTitle={false}
        onConfirm={() => {
          setOpenConfirmScheinDialog(false);
          openCreateSchein(MainGroup.PRIVATE);
          setCurrentPayload(null);
        }}
        onClose={(closeAndSubmit) => {
          setOpenConfirmScheinDialog(false);
          if (closeAndSubmit) {
            onSubmit(currentPayload!);
            patientFileActions.schein.setIssueDate(DatetimeUtil.now());
            setCurrentPayload(null);
          }
        }}
      >
        <Flex column>{tConfirmDialog('description')}</Flex>
      </InfoConfirmDialog>
    </Flex>
  );
};

export default React.memo(GoaServiceBlock);
