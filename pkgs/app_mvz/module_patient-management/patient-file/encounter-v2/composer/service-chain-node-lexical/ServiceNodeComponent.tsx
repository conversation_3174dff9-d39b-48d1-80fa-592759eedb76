import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import type AdminComposerCommandI18n from '@tutum/admin/locales/en/ComposerCommand.json';
import {
  BodyTextM,
  Flex,
  Tooltip,
  coreComponents,
} from '@tutum/design-system/components';
import { StyledAutosizeInput } from '@tutum/design-system/components/AutoExpandInput';
import { Spinner } from '@tutum/design-system/components/Core';
import { InputSuggestion } from '@tutum/design-system/components/InputSuggestion/InputSuggestion.styled';
import {
  CareFacilityBlock,
  ServicePopoverMenuItem,
} from '@tutum/design-system/composer/service-node-component';
import {
  DEFAULT_COMPOSER_SELECT_STYLE_CONFIG,
  DEFAULT_SELECT_COMPONENT_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  useMutationGetGoaFactorValue,
  useQueryGetGoaFactorValue,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  CareFacility,
  EncounterServiceTimeline,
} from '@tutum/hermes/bff/repo_encounter';
import I18n from '@tutum/infrastructure/i18n';
import { BASE_PATH_ADMIN } from '@tutum/infrastructure/utils/string.util';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import type PraxisComposerCommandI18n from '@tutum/mvz/locales/en/ComposerCommand.json';
import {
  EncounterChainAbleService,
  ON_CREATE_SELECTOR_NODE,
  ON_DELETE_SERVICE_COMMAND,
  ON_SELECT_SERVICE_COMMAND,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/ServiceChainCommands';
import { ServiceChainContext } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/ServiceChainHooks';
import { parseServiceChainFromLexicalState } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/utils';
import { useCallback, useContext, useEffect, useRef } from 'react';
import type { SelectInstance, StylesConfig } from 'react-select';
import { updateFactorValue } from './ServiceNodeComponent.helper';
import { isEmpty } from 'lodash';

interface Iprops {
  encounterService?: EncounterChainAbleService;
  nodeKey: string;
  className?: string;
}
const STYLE_CONFIG: StylesConfig = {
  ...DEFAULT_COMPOSER_SELECT_STYLE_CONFIG(),
  container: (base) => ({
    ...base,
    height: 20,
    padding: 0,
    margin: 0,
  }),
};

const ServiceNodeComponent = ({
  encounterService,
  nodeKey,
  className,
}: Iprops) => {
  const inputSuggestionEl = useRef<SelectInstance<any, boolean> | null>(null);
  const {
    id,
    searchService,
    onServiceSelect,
    onSubmit,
    additionalFields,
    noResultsComponent,
    usedFor,
    scheinId,
    hasError,
    factorMap,
    editable,
    onCheckError,
  } = useContext(ServiceChainContext);
  const [editor] = useLexicalComposerContext();
  const { t: tPraxis } = I18n.useTranslation<
    keyof typeof PraxisComposerCommandI18n
  >({
    namespace: 'ComposerCommand',
  });
  const { t: tAdmin } = I18n.useTranslation<
    keyof typeof AdminComposerCommandI18n
  >({
    namespace: 'ComposerCommand',
  });

  const isInAdminApp = window.location.pathname.includes(BASE_PATH_ADMIN);
  const t = isInAdminApp ? tAdmin : tPraxis;
  const isGoaService = usedFor === 'GOA_SERVICE';
  const isUVGoaService = usedFor === 'UV_GOA_SERVICE';

  const {
    isFetching: isFetchingFactor,
    data: dataFactor,
    isSuccess: isSuccessGetFactor,
  } = useQueryGetGoaFactorValue(
    {
      scheinId: scheinId!,
      goaNumber: encounterService?.code!,
    },
    {
      enabled:
        !!scheinId && !!encounterService?.code && isGoaService,
    }
  );
  const getGoaFactorValue = useMutationGetGoaFactorValue({
    onSuccess: (data) => {
      updateFactorValue(editor, nodeKey, data.data, true);
    },
  });

  const isServiceNode = ['SERVICE'].includes(usedFor || '');
  const encounterServiceTimeline = encounterService as EncounterServiceTimeline;

  useEffect(() => {
    if (isFetchingFactor || !isSuccessGetFactor || !encounterService) return;
    if (isGoaService && factorMap) {
      factorMap.set(`${scheinId}_${encounterService.code}`, dataFactor.value);
    }
  }, [isFetchingFactor, encounterService, isSuccessGetFactor, dataFactor, isGoaService, factorMap]);

  useEffect(() => {
    if (!isGoaService || isFetchingFactor) {
      return;
    }

    updateFactorValue(editor, nodeKey, dataFactor, false);
    
    if (!scheinId && encounterService?.code && !dataFactor) {
      onCheckError?.('factor', 0);
    }
  }, [isFetchingFactor, nodeKey, scheinId, encounterService?.code, dataFactor, isGoaService]);

  const setUpdatedService = (updatedService: EncounterChainAbleService) => {
    const _service = { ...updatedService };
    editor.dispatchCommand(ON_SELECT_SERVICE_COMMAND, {
      encounterService: updatedService,
      serviceNodeKey: nodeKey,
      additionalFields,
      cb: onChangeServiceCode,
    });
    return _service;
  };

  const onChangeServiceCode = async (encounterService: any) => {
    if (isGoaService && scheinId) {
      getGoaFactorValue.mutate({
        scheinId,
        goaNumber: encounterService.code,
      });
    }
  };

  // create additional info selector node
  useEffect(() => {
    if (encounterService && editable) {
      editor.dispatchCommand(ON_CREATE_SELECTOR_NODE, {
        additionalFields,
        serviceNodeKey: nodeKey,
        isGoaServiceNode: isGoaService,
        isUvGoaServiceNode: isUVGoaService,
      });
    }
  }, [!!encounterService, additionalFields, editable, isUVGoaService, isGoaService]);

  const handleSelectService = async (item: IContractData) => {
    if (item.isBlankService && !item.isActive) {
      return;
    }
    const inputData = `${item.code ? `(${item.code})` : ''} ${
      item.description
    }`.trim();
    let result = await onServiceSelect(item);
    if (isUVGoaService) {
      result = await onServiceSelect<'UV_GOA_SERVICE'>(item);
      setUpdatedService({
        ...result,
        freeText: inputData,
        price: item.price,
        isGeneral: result.isGeneral,
      });
    } else {
      setUpdatedService({ ...result, freeText: inputData });
    }
  };

  const handleSubmit = () => {
    if (!inputSuggestionEl?.current?.props?.menuIsOpen) {
      const editorState = editor.getEditorState();
      const data = parseServiceChainFromLexicalState(
        editorState,
        isGoaService,
        isUVGoaService,
        factorMap
      );

      // Block submission while factor is still being resolved
      if (isGoaService && isFetchingFactor) {
        return;
      }

      // Extra guard: for GOA_SERVICE ensure every service has valid factor & quantity (>0)
      if (isGoaService) {
        const goaData = data as {
          goaServices?: Array<{ factor?: number; quantity?: number }>;
        };
        const goaServices = goaData.goaServices || [];
        const hasInvalidService = goaServices.some(
          (service) =>
            !service.factor ||
            service.factor <= 0 ||
            !service.quantity ||
            service.quantity <= 0
        );
        if (hasInvalidService) {
          console.warn(
            'Submission blocked: GOA service factor/quantity not ready'
          );
          return;
        }
      }

      if (
        isEmpty(hasError) ||
        (!hasError.errFactor && !hasError.errQuantity)
      ) {
        onSubmit(data);
      }
    }
  };

  const onCareFacilityChange = (careFacility: CareFacility) => {
    if (encounterServiceTimeline?.careFacility === careFacility) {
      return;
    }

    setUpdatedService({
      ...encounterService,
      careFacility,
    } as EncounterChainAbleService);
  };

  const Input = useCallback(
    (props) => {
      const input = (
        <StyledAutosizeInput
          autoFocus={editable}
          type="text"
          inputClassName="sl-auto-resize__input"
          autoComplete="off"
          placeholder={t('newService')}
          id={props.inputId}
          inputRef={(ref) => {
            props.innerRef(ref);
          }}
          value={props.value}
          onChange={props.onChange}
        />
      );
      if (encounterService && props.value) {
        return (
          <Tooltip position="top" content={encounterService?.description}>
            {input}
          </Tooltip>
        );
      }
      return input;
    },
    [encounterService?.description]
  );

  return (
    <Flex gap={2} className={className}>
      {!encounterService && (
        <BodyTextM color={COLOR.TEXT_INFO} fontWeight="SemiBold">
          #
        </BodyTextM>
      )}
      <InputSuggestion
        id={id}
        keyCode="code"
        styles={STYLE_CONFIG}
        openMenuOnClick={false}
        components={{
          ...DEFAULT_SELECT_COMPONENT_CONFIG,
          Option: (props) => {
            const {
              data,
              selectProps: { inputValue },
            } = props;

            return (
              <coreComponents.Option {...props} isSelected={false}>
                <ServicePopoverMenuItem
                  item={data}
                  query={inputValue}
                  pointValue={undefined}
                />
              </coreComponents.Option>
            );
          },
          Input,
        }}
        defaultInputValue={encounterService?.code}
        loadOptions={
          ((query: string, setDatasource: any) => {
            searchService(query, (_result) => {
              setDatasource(_result);
            });
          }) as any
        }
        onChange={handleSelectService}
        noOptionsMessage={({ inputValue }) => {
          if (!inputValue) {
            return null;
          }
          return noResultsComponent;
        }}
        ref={inputSuggestionEl}
        onKeyDown={(e) => {
          switch (e.key) {
            case 'Backspace':
              if (!inputSuggestionEl?.current?.inputRef?.value) {
                editor.dispatchCommand(ON_DELETE_SERVICE_COMMAND, {
                  nodeKey,
                });
              }
              break;

            case 'Enter':
              handleSubmit();
              break;
          }
        }}
      />

      {isServiceNode && (
        <Flex column>
          <CareFacilityBlock
            isInServiceChain
            data={encounterServiceTimeline?.careFacility}
            onChange={onCareFacilityChange}
            onKeyDown={handleSubmit}
          />
        </Flex>
      )}

      {isFetchingFactor && (
        <Spinner className="sl-loading" intent="primary" size={20} />
      )}
    </Flex>
  );
};

export default ServiceNodeComponent;
