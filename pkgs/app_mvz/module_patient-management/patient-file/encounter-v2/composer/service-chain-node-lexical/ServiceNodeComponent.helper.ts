import type { AdditionalInfoBlockNode } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/AdditionalInfoBlock.node';
import type { GetGoaFactorValueResponse } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  $focusServiceNode,
  ServiceNode,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/ServiceNode';
import {
  $createNodeSelection,
  $getNodeByKey,
  $setSelection,
  LexicalEditor,
} from 'lexical';

// Should update when:
// 1. in create mode
// 2. in update mode, but for new code only
const updateFactorValue = (
  editor: LexicalEditor,
  nodeKey: string,
  dataFactor: GetGoaFactorValueResponse | undefined,
  isServiceCodeChanged: boolean
) => {
  editor.update(() => {
    const serviceNode = $getNodeByKey<ServiceNode>(nodeKey);
    const factorNode = serviceNode?.getNextSibling<AdditionalInfoBlockNode>();

    if (factorNode?.__info && factorNode.__info.fK === 'factor') {
      factorNode.setInfoValue(dataFactor ? dataFactor.value.toString().replace('.', ',') : '');

      if (isServiceCodeChanged) {
        const quantityNode: AdditionalInfoBlockNode =
          factorNode.getNextSibling()!;
        quantityNode.setInfoValue('1');
      }

      const next = factorNode.getNextSibling();
      if (next) {
        const nodeSelection = $createNodeSelection();
        nodeSelection.add(next.getKey());
        $setSelection(nodeSelection);
        $focusServiceNode(editor, next as any);
      }
    }
  });
};

export { updateFactorValue };
