import { $getNearestInput } from '@tutum/design-system/composer/service-node-lexical/utils/input';

import ServiceNodeComponent from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/ServiceNodeComponent.styled';
import {
  DecoratorNode,
  LexicalEditor,
  LexicalNode,
  NodeKey,
  SerializedLexicalNode,
  Spread,
} from 'lexical';
import React, { ReactNode } from 'react';
import type { EncounterChainAbleService } from './ServiceChainCommands';

const SERVICE_NODE_TYPE = 'sl-service-node';

export type SerializedServiceNode = Spread<
  {
    encounterService: EncounterChainAbleService;
  },
  SerializedLexicalNode
>;

export class ServiceNode extends DecoratorNode<ReactNode> {
  __encounterService: EncounterChainAbleService;

  static getType(): string {
    return SERVICE_NODE_TYPE;
  }

  static clone(node: ServiceNode): ServiceNode {
    return new ServiceNode(node.__encounterService, node.__key);
  }

  static importJSON(jsonNode: SerializedServiceNode): ServiceNode {
    return $createServiceNode(jsonNode.encounterService);
  }

  exportJSON(): SerializedServiceNode {
    const self = this.getLatest();
    return {
      type: SERVICE_NODE_TYPE,
      version: 1,
      encounterService: self.__encounterService,
    };
  }

  constructor(encounterService: EncounterChainAbleService, key?: NodeKey) {
    super(key);
    this.__encounterService = encounterService;
  }

  createDOM(): HTMLElement {
    const div = document.createElement('div');
    div.style.display = 'inline-block';
    return div;
  }

  updateDOM(): false {
    return false;
  }

  decorate(): ReactNode {
    const self = this.getLatest();
    return (
      <ServiceNodeComponent
        encounterService={self.__encounterService}
        nodeKey={self.__key}
      />
    );
  }

  setEncounterService(encounterService: EncounterChainAbleService) {
    const writable = this.getWritable();
    writable.__encounterService = encounterService;
  }
}

export function $createServiceNode(
  encounterService: EncounterChainAbleService
): ServiceNode {
  return new ServiceNode(encounterService);
}

export function $isServiceNode(
  node: LexicalNode | null | undefined
): node is ServiceNode {
  return node instanceof ServiceNode;
}

export function $focusServiceNode(editor: LexicalEditor, node: ServiceNode) {
  $getNearestInput(editor, node)?.focus();
}
