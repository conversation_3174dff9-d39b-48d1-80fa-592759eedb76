import { useState, useEffect, useMemo, useContext, useCallback } from 'react';
import Router from 'next/router';
import { isEmpty, debounce } from 'lodash';

import type React from 'react';
import type { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import type { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import type { EncounterGoaServiceChain } from '@tutum/hermes/bff/legacy/timeline_common';
import type {
  IProps,
  ServiceReturnType,
  SubmitReturnType,
} from '../service-chain-node-lexical/ServiceChainHooks';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { MainGroup } from '@tutum/hermes/bff/common';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import type { ScheinItem } from '@tutum/hermes/bff/schein_common';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';

import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import {
  alertSuccessfully,
  BodyTextL,
  BodyTextM,
  Button,
  Flex,
  InfoConfirmDialog,
  Link,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { Classes, Dialog } from '@tutum/design-system/components/Core';
import ADDITIONAL_INFO_FULL_LIST from '@tutum/design-system/composer/assets/additional-info.json';
import ScheinBlock from '@tutum/mvz/components/schein-block';
import CreateMaterialCostDialog from '@tutum/mvz/module_sdebm/material-cost-dialog/MaterialCostDialog.styled';
import { ASV_KEY } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { COLOR } from '@tutum/design-system/themes/styles';
import { useComposerActionChainStore } from '@tutum/mvz/module_action-chain';
import {
  saveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import I18n from '@tutum/infrastructure/i18n';
import {
  checkIsKvSchein,
  checkIsPrivateSchein,
  checkIsSvSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { groupQuarterbySchein } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '../Composer.const';
import { parseToScheinMainGroup } from '../Composer.util';
import ServiceBlockService from '../service-block/services/service-block.service';
import ServiceMetaService from '../service-block/services/service-meta.service';
import ServiceChainTextEditor from '../service-chain-node-lexical/ServiceChainTextEditor';
import { PatientType } from '@tutum/hermes/bff/legacy/patient_profile_common';
import MaterialCostService from '@tutum/infrastructure/web-worker-services/material-cost.service';
import CreateJusitficationModal from '../goa-service-block/justification-modal/CreateJustification.styled';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { $$getAllNodes } from '@tutum/design-system/lexical/utils';
import {
  $focusServiceNode,
  $isServiceNode,
  ServiceNode,
} from '../service-chain-node-lexical/ServiceNode';
import CreateGoaDialog from '@tutum/mvz/module_sdgoa/create-goa-dialog/CreateGoaDialog.styled';
import { useQueryGetGoaChapters } from '@tutum/hermes/bff/legacy/app_mvz_catalog_goa';
import type SdebmI18n from '@tutum/mvz/locales/en/Sdebm.json';

export interface IServiceChainBlockProps {
  className?: string;
  initEncounterId?: string;
  encounterDate?: number;
  data: EncounterGoaServiceChain;
  doctorProfile?: IEmployeeProfile;
  defaultContract?: IContractInfo;
  patient?: IPatientProfile;
  disabled?: boolean;
  scheinsFilteredByDate: ScheinItem[];
  onSubmit(payload: EncounterGoaServiceChain | null): Promise<void>;
  onClear: () => void;
  openCreateSchein: (scheinMaingRoup?: MainGroup) => void;
}

interface IConfirmOpenCreateDialogProps {
  isOpen: boolean;
  onClose(): void;
  onConfirmClick(): void;
}

const ConfirmOpenCreateDialog: React.FC<IConfirmOpenCreateDialogProps> = (
  props
) => {
  const { t: tSdebm } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'MaterialCostDialog',
  });

  return (
    <Dialog
      isOpen={props.isOpen}
      onClose={props.onClose}
      style={{ borderRadius: 4 }}
      canOutsideClickClose={false}
    >
      <Flex className={Classes.DIALOG_BODY} column gap={24}>
        <BodyTextL fontWeight={'Bold'} fontSize={20}>
          {tSdebm('createMaterialCostTitle')}
        </BodyTextL>
        {tSdebm('leaveCreateMaterialCostContent')}
        <Flex gap={16}>
          <Button
            onClick={props.onClose}
            minimal
            outlined
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('cancelCreate')}
          </Button>
          <Button
            fill
            onClick={props.onConfirmClick}
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('confirmCreateMaterialCost')}
          </Button>
        </Flex>
      </Flex>
    </Dialog>
  );
};

const ASV_ADD_INFO = ADDITIONAL_INFO_FULL_LIST.find(
  (addInfo) => addInfo.fK === ASV_KEY
);

const GoaServiceChainBlock: React.FC<IServiceChainBlockProps> = (props) => {
  const {
    initEncounterId,
    data,
    patient,
    onSubmit,
    disabled = false,
    className,
    encounterDate,
    doctorProfile,
    scheinsFilteredByDate,
    onClear,
    openCreateSchein,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tConfirmDialog } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein.ConfirmDialog',
  });

  const { t: translator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const { t: tSdebmOverview } = I18n.useTranslation<keyof typeof SdebmI18n.SdebmOverview>({
    namespace: 'Sdebm',
    nestedTrans: 'SdebmOverview',
  });

  const { useGetDoctorList } = useContext(GlobalContext.instance);
  const composerActionChainStore = useComposerActionChainStore();
  const getChapters = useQueryGetGoaChapters({});

  const doctorLists = useGetDoctorList();
  const patientFileStore = usePatientFileStore();
  const scheinOnSidebar = patientFileStore?.schein?.activatedSchein;
  const {
    patientManagement: { selectedContractDoctor },
  } = useContext(PatientManagementContext.instance);

  const [isOpenCreateServiceDialog, setIsOpenCreateServiceDialog] = useState<boolean>(false);
  const [codeNotExist, setCodeNotExist] = useState('');
  const [filteredAdditionalInfos, setFilteredAdditionalInfos] = useState<
    AdditionalInfoField[]
  >([]);
  const [isOpenCreateMaterialCost, setIsOpenCreateMaterialCostDialog] =
    useState(false);

  const [isOpenConfirmDialog, setIsOpenConfirmDialog] = useState(false);
  const [isOpenCreateJustification, setIsOpenCreateJustification] =
    useState(false);

  const { data: listSettingJustification, refetch } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });

  const [selectedSchein, setSelectedSchein] = useState<ScheinItem | undefined>(
    undefined
  );

  const [isOpenConfirmScheinDialog, setOpenConfirmScheinDialog] =
    useState<boolean>(false);
  const [currentPayload, setCurrentPayload] =
    useState<EncounterGoaServiceChain | null>(null);

  const memoizedDoctorASVNumbers = useMemo(
    () =>
      doctorLists.reduce((asvNumbers, doctor) => {
        if (doctor.teamNumbers?.length) {
          return asvNumbers.concat(doctor.teamNumbers);
        }
        return asvNumbers;
      }, [] as string[]),
    [doctorLists]
  );

  const currentSchein = useMemo(() => {
    if (selectedSchein) {
      return selectedSchein;
    }

    const singleScheinId = patientFileStore.schein.activatedSchein?.scheinId; // NOTE: only single Schein select on service code
    return (
      (singleScheinId != null &&
        scheinsFilteredByDate.find((sch) => sch.scheinId === singleScheinId)) ||
      scheinOnSidebar
    );
  }, [
    selectedSchein,
    patientFileStore.schein.activatedSchein,
    scheinsFilteredByDate,
    scheinOnSidebar,
  ]);

  const currentContract = useMemo(() => {
    if (!currentSchein) return;
    let scheinKey = '';
    if (checkIsSvSchein(currentSchein)) {
      scheinKey = currentSchein.hzvContractId || '';
    } else if (currentSchein.kvTreatmentCase) {
      scheinKey = currentSchein.kvTreatmentCase || '';
    }

    catalogOverviewActions
      .getAdditionalInfos(currentSchein.scheinMainGroup, scheinKey)
      .then((fields) => {
        const infors = fields.map((info) => {
          info.label = translator(info.fK);
          return info;
        });
        if (
          memoizedDoctorASVNumbers?.length &&
          checkIsKvSchein(currentSchein)
        ) {
          infors.push({
            ...ASV_ADD_INFO,
            label: translator(ASV_ADD_INFO?.fK || ''),
          } as AdditionalInfoField);
        }
        setFilteredAdditionalInfos(infors);
      });
    const newContract = patientFileActions.getAvailableContractById(
      currentSchein.hzvContractId
    );
    return newContract;
  }, [translator, currentSchein, memoizedDoctorASVNumbers]);

  const onServiceSelect = async <T extends IProps['usedFor']>(
    item: IContractData
  ): Promise<ServiceReturnType<T>> => {
    const resultAfterValidate =
      await ServiceMetaService.getServiceMetaVersionGoa(item);
    if (!resultAfterValidate) {
      throw new Error('Service metadata retrieval failed.');
    }

    resultAfterValidate.scheins = parseToScheinMainGroup([currentSchein]);

    return resultAfterValidate as ServiceReturnType<T>;
  };

  const onSetScheinIds = (scheinItems: ScheinItem[] = []) => {
    setSelectedSchein(scheinItems[0]);
  };

  const searchServiceData = useMemo(
    () =>
      debounce(async (query: string, cb: (result: IContractData[]) => void) => {
        setCodeNotExist(query);

        let isPrivate =
          patient?.patientInfo.genericInfo.patientType ===
          PatientType.PatientType_Private;

        const activeSchein =
          selectedSchein ?? patientFileStore.schein.activatedSchein;
        if (activeSchein) {
          isPrivate = checkIsPrivateSchein(activeSchein);
        }

        const result = await ServiceBlockService.searchService(
          encounterDate as number,
          query,
          doctorProfile,
          [],
          currentContract,
          selectedContractDoctor,
          isPrivate,
          selectedSchein?.scheinMainGroup
        );

        cb(result.slice(0, 20));
      }, 500),
    [
      patient,
      doctorProfile,
      encounterDate,
      selectedContractDoctor,
      selectedSchein,
      currentContract,
    ]
  );

  const handleClickNoResults = () => {
    setIsOpenCreateServiceDialog(true);
  };

  const hasFavSchein = useMemo(() => {
    return patientFileStore.schein.originalList?.some((schein) =>
      checkIsSvSchein(schein)
    );
  }, [patientFileStore.schein.originalList]);

  const isShowSuggestChangeToFavSchein = useMemo(() => {
    return hasFavSchein && !currentSchein?.hzvContractId;
  }, [hasFavSchein, currentSchein]);

  const NoResults = () => {
    return (
      <>
        <Flex className="sl-no-results" p="8px 16px" justify="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
            {`${t('noResultsFound')}.`}
          </BodyTextM>
          <Link onClick={handleClickNoResults}>{t('CreateServiceCode')}</Link>
        </Flex>
        {isShowSuggestChangeToFavSchein && (
          <BodyTextM>{t('changeToFavSchein')}</BodyTextM>
        )}
      </>
    );
  };

  const filterScheinFunc = useCallback(
    (_option: ScheinItem) =>
      currentSchein?.scheinMainGroup === _option?.scheinMainGroup,
    [currentSchein]
  );

  const createListInsuranceMapBySchein = useMemo(() => {
    const listInsurance =
      patientFileStore.patient.current?.patientInfo.insuranceInfos || [];

    const listScheinId = scheinsFilteredByDate.map(
      (schein) => schein.insuranceId
    );

    const res = listInsurance.filter((insur) =>
      listScheinId.includes(insur.id)
    );
    const groupQuarterBySchein = groupQuarterbySchein(
      scheinsFilteredByDate,
      res
    );

    return groupQuarterBySchein;
  }, [scheinsFilteredByDate]);

  useEffect(() => {
    const firstSchein = data.goaServices?.[0]?.scheins?.[0]?.scheinId;
    const defaultSelectedScheinId = initEncounterId
      ? firstSchein
      : patientFileStore?.schein?.activatedSchein?.scheinId;
    const scheinValid =
      scheinsFilteredByDate.find(
        (schein) => schein.scheinId === defaultSelectedScheinId
      ) ||
      (scheinsFilteredByDate?.length === 1 && !!firstSchein
        ? scheinsFilteredByDate[0]
        : undefined);
    setSelectedSchein(scheinValid);

    // TODO: Check logic auto bind first schein valid in quarter
    if (data.goaServices) {
      for (const service of data.goaServices) {
        const updatedService = {
          ...service,
          scheins:
            typeof scheinValid !== 'undefined'
              ? [
                {
                  scheinId: scheinValid.scheinId,
                  group: scheinValid.scheinMainGroup,
                },
              ]
              : [],
        };
        const index = data.goaServices.indexOf(service);
        data.goaServices[index] = updatedService;
      }
    }
  }, [
    initEncounterId,
    JSON.stringify(scheinsFilteredByDate),
    patientFileStore?.schein?.activatedSchein,
  ]);

  useEffect(() => {
    return () => {
      searchServiceData.cancel?.();
    };
  }, [searchServiceData]);

  const handleSubmit = <T extends IProps['usedFor']>(
    payload: SubmitReturnType<T>,
    usedFor: IProps['usedFor']
  ) => {
    if (usedFor === 'GOA_SERVICE') {
      const goaPayload = payload as EncounterGoaServiceChain;
      const updatedPayload: EncounterGoaServiceChain = {
        ...goaPayload,
        encounterGoaServiceChainRaw:
          composerActionChainStore.currentBlock?.encounterGoaServiceChainRaw ||
          goaPayload.encounterGoaServiceChainRaw ||
          '',
        goaServices: (payload as EncounterGoaServiceChain).goaServices.map(
          (service, index) => ({
            ...service,
            scheins: selectedSchein ? [{
              scheinId: selectedSchein.scheinId,
              group: selectedSchein.scheinMainGroup,
            }] : service.scheins,
            additionalInfos:
              composerActionChainStore.currentBlock
                ?.additionalInfosServiceChain?.[index] ||
              service.additionalInfos ||
              [],
          })
        ),
      };
      if (!scheinsFilteredByDate[0] || isEmpty(selectedSchein)) {
        setCurrentPayload(updatedPayload);
        setOpenConfirmScheinDialog(true);
        return;
      }
      onSubmit(updatedPayload);
    }
  };

  const onSaveSuccessGoa = () => {
    alertSuccessfully(
      tSdebmOverview('createSuccessEbm'),
      { timeout: TOASTER_TIMEOUT_CUSTOM }
    );
    setIsOpenCreateServiceDialog(false);
  };

  return (
    <Flex className={className} column gap={0}>
      <ServiceChainTextEditor
        id="goa_service_chain_text_editor"
        usedFor="GOA_SERVICE"
        scheinId={selectedSchein?.scheinId}
        disabled={disabled}
        factorMap={new Map()}
        initialEditorState={data?.encounterGoaServiceChainRaw}
        onSubmit={(payload) => handleSubmit(payload, 'GOA_SERVICE')}
        className="sl-ServiceChainTextEditor"
        encounterDate={encounterDate}
        searchService={searchServiceData}
        onServiceSelect={onServiceSelect}
        pointValue={patientFileStore.pointValue}
        additionalFields={filteredAdditionalInfos}
        noResultsComponent={<NoResults />}
        onClear={onClear}
        rightElement={
          <ScheinBlock
            selectedSchein={selectedSchein}
            openCreateSchein={() => openCreateSchein(MainGroup.PRIVATE)}
            onSetScheinIds={onSetScheinIds}
            scheinFilter={createListInsuranceMapBySchein}
            filterOption={filterScheinFunc}
          />
        }
      >
        {/* Plugin for selecting additional info nodes */}
        <AdditionalInfoMaterialCostPlugin
          onSearch={async (query, setData) => {
            const _searchQuery = query || '*';
            const results =
              (await MaterialCostService.searchMaterialCost(_searchQuery)) ??
              [];
            setData(results);
            return results;
          }}
          onCreateMaterialCostClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenConfirmDialog(true);
              return;
            }
            setIsOpenCreateMaterialCostDialog(true);
          }}
          onViewAllMaterialCostClick={() =>
            Router.push(ROUTING.MATERIAL_COST_CATALOG)
          }
          callback={(editor) => {
            const last = $$getAllNodes($isServiceNode).pop();
            $focusServiceNode(editor, last as ServiceNode);
          }}
        >
          {({ onNewMaterialCostAdded }) => (
            <>
              {isOpenCreateMaterialCost && (
                <CreateMaterialCostDialog
                  isOpen={isOpenCreateMaterialCost}
                  onClose={() => {
                    setIsOpenCreateMaterialCostDialog(false);
                  }}
                  onCreateSuccess={onNewMaterialCostAdded}
                />
              )}
            </>
          )}
        </AdditionalInfoMaterialCostPlugin>
        <AdditionalInfoJusification
          onSearch={(query, setData) => {
            const _searchQuery = query || '*';
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const results = listJustification.filter((item) =>
              item.includes(_searchQuery)
            );
            setData(results);
            return results;
          }}
          onCreateJustificationClick={() => setIsOpenCreateJustification(true)}
          onDeletedJustificationClick={async (value: string) => {
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const newListJustification = listJustification.filter(
              (v) => v !== value
            );
            await saveSettings({
              settings: {
                [SETTING_KEY_ALLERGIES_FOR_5009]:
                  JSON.stringify(newListJustification),
              },
            });
            refetch();
          }}
        >
          {({ onNewJustificationAdded }) => (
            <>
              {isOpenCreateJustification && (
                <CreateJusitficationModal
                  t={t}
                  isOpen={isOpenCreateJustification}
                  className="stage-modal-justification"
                  onCreateSuccess={async (ws: string) => {
                    onNewJustificationAdded(ws);
                    const listJustification: string[] = JSON.parse(
                      listSettingJustification?.settings?.[
                      SETTING_KEY_ALLERGIES_FOR_5009
                      ] || '[]'
                    );
                    listJustification.push(ws);
                    await saveSettings({
                      settings: {
                        [SETTING_KEY_ALLERGIES_FOR_5009]:
                          JSON.stringify(listJustification),
                      },
                    });
                    refetch();
                    setIsOpenCreateJustification(false);
                  }}
                  onClose={() => setIsOpenCreateJustification(false)}
                />
              )}
            </>
          )}
        </AdditionalInfoJusification>
      </ServiceChainTextEditor>

      <ConfirmOpenCreateDialog
        isOpen={isOpenConfirmDialog}
        onClose={() => setIsOpenConfirmDialog(false)}
        onConfirmClick={() => {
          setIsOpenConfirmDialog(false);
          setIsOpenCreateMaterialCostDialog(true);
        }}
      />

      {isOpenConfirmScheinDialog && (
        <InfoConfirmDialog
          type="primary"
          isOpen={isOpenConfirmScheinDialog}
          title={tConfirmDialog('title')}
          cancelText={tConfirmDialog('btnNo')}
          confirmText={tConfirmDialog('btnYes')}
          isShowIconTitle={false}
          onConfirm={() => {
            setOpenConfirmScheinDialog(false);
            openCreateSchein(MainGroup.PRIVATE);
            setCurrentPayload(null);
          }}
          onClose={(closeAndSubmit) => {
            setOpenConfirmScheinDialog(false);
            if (closeAndSubmit) {
              const newPayload = {
                ...currentPayload,
                goaServices: currentPayload?.goaServices?.map((service) => ({
                  ...service,
                  scheins: [],
                })),
              } as EncounterGoaServiceChain;
              onSubmit(newPayload);
              patientFileActions.schein.setIssueDate(DatetimeUtil.now());
              setCurrentPayload(null);
            }
          }}
        >
          <Flex column>{tConfirmDialog('description')}</Flex>
        </InfoConfirmDialog>
      )}

      {isOpenCreateServiceDialog && (
        <CreateGoaDialog
          createGoaDefaultValue={codeNotExist}
          chaptersList={getChapters.data?.items || []}
          isOpen
          onClose={() => {
            setIsOpenCreateServiceDialog(false);
          }}
          onSaveSuccess={onSaveSuccessGoa}
        />
      )}
    </Flex>
  );
};

export default GoaServiceChainBlock;
