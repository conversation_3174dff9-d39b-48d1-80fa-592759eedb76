import { searchHgnc } from "@tutum/hermes/bff/legacy/app_mvz_patient_encounter";
import datetimeUtil from "@tutum/infrastructure/utils/datetime.util";

export const checkValidHgncSymbol = async (hgncSymbol: string[]): Promise<boolean> => {
  try{
    const now = datetimeUtil.now();
    const searchHgncPromise = hgncSymbol.map(symbol => searchHgnc({ query: symbol, selectedDate: now }))
    const searchHgncResponse = await Promise.all(searchHgncPromise)
    const searchHgncResult = searchHgncResponse.flatMap(response => response.data.items)
    const validSymbol = searchHgncResult.map(item => item.description)
    return hgncSymbol.every(symbol => validSymbol.includes(symbol))
  } catch (error) {
    console.error( error)
    return false;
  }
}