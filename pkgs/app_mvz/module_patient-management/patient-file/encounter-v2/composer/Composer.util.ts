import type { IMenuItem } from '@tutum/design-system/components';

import ComposerUtils from '@tutum/design-system/composer/Composer.util';
import { TimelineDocumentType } from '@tutum/hermes/bff/legacy/app_admin_timeline_document_type';
import {
  MainGroup,
  ScheinItem,
  ScheinStatus,
} from '@tutum/hermes/bff/schein_common';
import { DiagnoseType } from '@tutum/hermes/bff/service_domains_patient_file';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { checkIsBgSchein } from '@tutum/mvz/_utils/scheinFormat';
import {
  BaseComposerRowCommand,
  BaseComposerRowType,
  ComposerRowCommand,
  IComposerRow,
} from './Composer.type';

const statusBlockSchein = [
  ScheinStatus.ScheinStatus_Billed,
  ScheinStatus.ScheinStatus_Canceled,
  ScheinStatus.ScheinStatus_Printed,
];

function filterScheinByDate(scheinList: ScheinItem[], date: Date) {
  const quarterCurrent = Math.floor(date.getMonth() / 3 + 1);
  const scheinListFilter = scheinList.filter((item) => {
    const { isPrivate } = ComposerUtils.getTypeOfSchein(item);
    // NOTE: filter valid schein for Private case
    const isBgSchein = checkIsBgSchein(item)
    if (isPrivate || isBgSchein) {
      let isValidItem = true
      if (!isBgSchein) {
        isValidItem = ComposerUtils.compareDate(
          item.issueDate ? +DatetimeUtil.startOf(item.issueDate, 'day') : 0,
          date.getTime()
        )
      } else {
        isValidItem = ComposerUtils.isInDateRange(
          item.bgScheinDetail?.createdOn ? +DatetimeUtil.startOf(item.bgScheinDetail.createdOn, 'day') : 0,
          item.bgScheinDetail?.endDate || 0,
          date.getTime()
        )
      }
      if (
        isValidItem &&
        !statusBlockSchein.includes(item.scheinStatus as ScheinStatus)
      ) {
        return true;
      }
      return false;
    }

    // NOTE: filter valid schein for KV or HzV case
    if (
      item.g4101Quarter === quarterCurrent &&
      item.g4101Year === date.getFullYear() &&
      !item.markedAsBilled
    ) {
      return true;
    }
    return false;
  });
  return scheinListFilter;
}

function getYearQuarterOfEncounterDateAndNow(encounterDate: number) {
  const quarterOfEncounterDate = DatetimeUtil.getQuarter(encounterDate);
  const yearOfEncounterDate = DatetimeUtil.getYear(encounterDate);
  const currentQuarter = Math.floor(DatetimeUtil.date().getMonth() / 3) + 1;
  const currentYear = DatetimeUtil.date().getFullYear();
  return {
    currentYear,
    currentQuarter,
    yearOfEncounterDate,
    quarterOfEncounterDate,
  };
}

export type ComposerCommandOption = IMenuItem & {
  keywords?: string[];
  tooltipDescription?: string;
};
const parseCommandType = (abbreviation: string) => {
  switch (abbreviation) {
    case 'A':
      return BaseComposerRowType.ANAMNESE;
    case 'AD':
      return BaseComposerRowType.ANAMNESTIC_DIAGNOSE;
    case 'B':
      return BaseComposerRowType.FINDINGS;
    case 'BR':
      return BaseComposerRowType.DOCTOR_LETTER;
    case 'D':
      return BaseComposerRowType.ACUTE_DIAGNOSE;
    case 'DD':
      return BaseComposerRowType.PERMANENT_DIAGNOSE;
    case 'L':
      return BaseComposerRowType.SERVICE;
    // case 'LK':
    //   return BaseComposerRowType.SERVICE;
    case 'T':
      return BaseComposerRowType.THERAPY;
    case 'N':
      return BaseComposerRowType.NOTES;
    case 'AK':
      return BaseComposerRowType.ACTION_CHAIN;
    case 'DMP':
      return BaseComposerRowType.DMP;
    case 'EHKS':
      return BaseComposerRowType.EHKS;
    default:
      return BaseComposerRowType.CUSTOMIZE;
  }
};
const COMPOSER_DEFAULT_COMMAND_LIST = (
  customizes: TimelineDocumentType[],
  excluded: string[] = []
) => {
  const result = [
    ...(customizes ?? []).map((c) => ({
      label: c.name,
      value: c.isCustom
        ? BaseComposerRowType.CUSTOMIZE
        : parseCommandType(c.abbreviation),
      keywords: [c.abbreviation],
    })),
  ].filter((item) => excluded.indexOf(item.value) === -1);

  return result;
};

type Func = (rowType: string, searchCommandText: ComposerRowCommand) => void;

const parseCommand = (
  commandText: string,
  excluded: string[],
  onRowTypeChange: Func,
  onSelectBrief: () => void,
  customizeDocumentTypes: TimelineDocumentType[]
) => {
  const searchCommandText = commandText?.toUpperCase();
  const validSearchCommands = COMPOSER_DEFAULT_COMMAND_LIST(
    customizeDocumentTypes,
    excluded
  );
  if (
    !validSearchCommands.flatMap((c) => c.keywords).includes(searchCommandText)
  ) {
    return;
  }

  switch (searchCommandText) {
    case BaseComposerRowCommand.THERAPY: {
      onRowTypeChange(BaseComposerRowType.THERAPY, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.NOTES: {
      onRowTypeChange(BaseComposerRowType.NOTES, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.ACUTE_DIAGNOSE: {
      onRowTypeChange(BaseComposerRowType.ACUTE_DIAGNOSE, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.ANAMNESTIC_DIAGNOSE: {
      onRowTypeChange(
        BaseComposerRowType.ANAMNESTIC_DIAGNOSE,
        searchCommandText
      );
      break;
    }
    case BaseComposerRowCommand.PERMANENT_DIAGNOSE: {
      onRowTypeChange(
        BaseComposerRowType.PERMANENT_DIAGNOSE,
        searchCommandText
      );
      break;
    }
    case BaseComposerRowCommand.EHKS: {
      onRowTypeChange(BaseComposerRowType.EHKS, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.SERVICE: {
      onRowTypeChange(BaseComposerRowType.SERVICE, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.SERVICE_CHAIN: {
      onRowTypeChange(BaseComposerRowType.SERVICE_CHAIN, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.FINDINGS: {
      onRowTypeChange(BaseComposerRowType.FINDINGS, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.ANAMNESE: {
      onRowTypeChange(BaseComposerRowType.ANAMNESE, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.ACTION_CHAIN: {
      onRowTypeChange(BaseComposerRowType.ACTION_CHAIN, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.DMP: {
      onRowTypeChange(BaseComposerRowType.DMP, searchCommandText);
      break;
    }
    case BaseComposerRowCommand.DOCTOR_LETTER: {
      onSelectBrief();
      break;
    }
    default:
      const customizeDocumentType = customizeDocumentTypes.find(
        (c) => c.abbreviation === searchCommandText
      );
      if (customizeDocumentType) {
        onRowTypeChange(
          BaseComposerRowType.NOTES,
          // TODO: onRowTypeChange into Composer.tsx receive rowType as string, but it should be ComposerRowCommand and extends with customize document type
          customizeDocumentType.abbreviation as unknown as ComposerRowCommand
        );
        break;
      }
      onRowTypeChange(BaseComposerRowType.NOTES, 'N');
      break;
  }
};

// Follow by timestamp in microseconds with 16 digits.
// in case of realtime, sort order may be duplicated, then addedDate should be in place
function generateSortOrder(currentIndex: number, rows?: IComposerRow[]) {
  //first or last row
  if (!rows || rows.length === 0 || !rows[currentIndex + 1]) {
    // timestamp in seconds (10 digits) + 6 zero padding at end
    return Number(`${DatetimeUtil.nowInUnix()}`.padEnd(16, `0`));
  }
  // row in between
  const min = rows[currentIndex].sortOrder;
  const max = rows[currentIndex + 1].sortOrder;
  return Math.random() * (max - min) + min;
}

const parseToScheinMainGroup = (scheinItems: (ScheinItem | undefined)[]) => {
  return (
    scheinItems?.map((item) => ({
      scheinId: item?.scheinId as string,
      group: item?.scheinMainGroup as MainGroup,
    })) || []
  );
};

export const DIAGNOSE_MAPPING = {
  [BaseComposerRowType.ACUTE_DIAGNOSE]: DiagnoseType.DIAGNOSETYPE_ACUTE,
  [BaseComposerRowType.PERMANENT_DIAGNOSE]: DiagnoseType.DIAGNOSETYPE_PERMANENT,
  [BaseComposerRowType.ANAMNESTIC_DIAGNOSE]:
    DiagnoseType.DIAGNOSETYPE_ANAMNESTIC,
};

const FREETEXT_TYPE_TO_COMPOSER_TYPE: Record<string, string> = {
  A: BaseComposerRowType.ANAMNESE,
  B: BaseComposerRowType.FINDINGS,
  T: BaseComposerRowType.THERAPY,
  N: BaseComposerRowType.NOTES,
};

export function convertFreetextCommandToComposerRowType(
  freeTextCommand: string
): string {
  return (
    FREETEXT_TYPE_TO_COMPOSER_TYPE[freeTextCommand] ??
    BaseComposerRowType.UNKNOWN
  );
}

export {
  COMPOSER_DEFAULT_COMMAND_LIST,
  filterScheinByDate,
  generateSortOrder,
  getYearQuarterOfEncounterDateAndNow,
  parseCommand,
  parseToScheinMainGroup,
};
