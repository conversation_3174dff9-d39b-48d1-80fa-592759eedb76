import { debounce, isEmpty, isNil } from 'lodash';
import Router from 'next/router';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import {
  BodyTextL,
  BodyTextM,
  Button,
  Flex,
  IMenuItemWithData,
  InfoConfirmDialog,
  Link,
  TOASTER_TIMEOUT_CUSTOM,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { Classes, Dialog } from '@tutum/design-system/components/Core';
import { BaseComposerRowCommand } from '@tutum/design-system/composer/Composer.type';
import ADDITIONAL_INFO_FULL_LIST from '@tutum/design-system/composer/assets/additional-info.json';
import { AdditionalInfoAutoTransformListNodePlugin } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-auto-transform-list-node-plugin';
import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import AdditionalInfoOmimGChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-omimg-chain-plugins';
import { ASV_KEY } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { $$getAllNodes } from '@tutum/design-system/lexical/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  OmimGChain,
  searchOmimGChain,
} from '@tutum/hermes/bff/legacy/app_catalog_omimg_chain';
import { getBlankServices } from '@tutum/hermes/bff/legacy/app_mvz_blank_service';
import {
  saveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import { UnitStatistiks } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import { PatientParticipation } from '@tutum/hermes/bff/legacy/service_domains_patient_participation';
import type { EncounterServiceTimeline } from '@tutum/hermes/bff/repo_encounter';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { EncounterServiceChain } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import MaterialCostService from '@tutum/infrastructure/web-worker-services/material-cost.service';
import {
  checkIsBgSchein,
  checkIsKvSchein,
  checkIsPrivateSchein,
  checkIsSvSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import ScheinBlock from '@tutum/mvz/components/schein-block';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import useToaster from '@tutum/mvz/hooks/useToaster';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { useComposerActionChainStore } from '@tutum/mvz/module_action-chain';
import { CreateExternalAddressDialog } from '@tutum/mvz/module_external-address/external-address-overview/CreateExternalAddressDialog';
import HospitalsPracticesDialog from '@tutum/mvz/module_kv_hzv_schein/hospitals-practices-dialog';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { groupQuarterbySchein } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  IActionBarStore,
  useActionBarStore,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import ServiceChainTextEditor from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-node-lexical/ServiceChainTextEditor';
import OmimGChainOverview from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/OmimGChainOverView';
import OmimGCreateDialog from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/create-dialog/OmimGCreateDialog.styled';
import type { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import type { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import CreateEbmDialog from '@tutum/mvz/module_sdebm/create-ebm-dialog/CreateEbmDialog.styled';
import CreateMaterialCostDialog from '@tutum/mvz/module_sdebm/material-cost-dialog/MaterialCostDialog.styled';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { NodeKey } from 'lexical';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '../Composer.const';
import { parseToScheinMainGroup } from '../Composer.util';
import CreateJusitficationModal from '../goa-service-block/justification-modal';
import {
  IProps,
  ServiceReturnType,
  SubmitReturnType,
} from '../service-chain-node-lexical/ServiceChainHooks';
import {
  $focusServiceNode,
  $isServiceNode,
  ServiceNode,
} from '../service-chain-node-lexical/ServiceNode';
import ServiceBlockService from './services/service-block.service';
import ServiceMetaService from './services/service-meta.service';
import AdditionalInfoHgncChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-hgnc-chain-plugins';
import HgncCreateDialog from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/create-dialog/HgncCreateDialog.styled';
import HgncChainOverview from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/HgncChainOverView';
import { searchHgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import { HgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
export interface IServiceChainBlockProps {
  className?: string;
  initEncounterId?: string;
  encounterDate?: number;
  data: EncounterServiceChain;
  doctorProfile?: IEmployeeProfile;
  defaultContract?: IContractInfo;
  patient?: IPatientProfile;
  disabled?: boolean;
  scheinsFilteredByDate: ScheinItem[];
  onSubmit(payload: EncounterServiceChain | null): Promise<void>;
  onClear: () => void;
  openCreateSchein: (scheinMaingRoup?: MainGroup) => void;
  handleSetActionBarValues: (key: keyof IActionBarStore, value: any) => void;
  onChange: (
    data: EncounterServiceChain & {
      scheins: {
        scheinId: string;
        group: MainGroup;
      }[];
    }
  ) => Promise<void>;
}

interface IConfirmOpenCreateDialogProps {
  isOpen: boolean;
  onClose(): void;
  onConfirmClick(): void;
}

const ConfirmOpenCreateDialog: React.FC<IConfirmOpenCreateDialogProps> = (
  props
) => {
  const { t: tSdebm } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'MaterialCostDialog',
  });

  return (
    <Dialog
      isOpen={props.isOpen}
      onClose={props.onClose}
      style={{ borderRadius: 4 }}
      canOutsideClickClose={false}
    >
      <Flex className={Classes.DIALOG_BODY} column gap={24}>
        <BodyTextL fontWeight={'Bold'} fontSize={20}>
          {tSdebm('createMaterialCostTitle')}
        </BodyTextL>
        {tSdebm('leaveCreateMaterialCostContent')}
        <Flex gap={16}>
          <Button
            onClick={props.onClose}
            minimal
            outlined
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('cancelCreate')}
          </Button>
          <Button
            fill
            onClick={props.onConfirmClick}
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('confirmCreateMaterialCost')}
          </Button>
        </Flex>
      </Flex>
    </Dialog>
  );
};

const ASV_ADD_INFO = ADDITIONAL_INFO_FULL_LIST.find(
  (addInfo) => addInfo.fK === ASV_KEY
);

function syncEditorData(
  editorRaw: string,
  services: EncounterServiceTimeline[]
): string | null {
  if (!editorRaw || !services.length) {
    return null;
  }
  const stateJson = JSON.parse(editorRaw);
  const children: any[] = stateJson.root.children;
  const serviceNodes = children.filter(
    (node) => node.type === 'sl-service-node' && !!node.encounterService
  );
  // update service code
  if (serviceNodes.length === services.length) {
    for (let i = 0; i < serviceNodes.length; i++) {
      serviceNodes[i].encounterService.code = services[i].code;
      serviceNodes[i].encounterService.description = services[i].description;
    }
  }
  // auto document service code
  const serviceSelectorNode = children.pop();
  let n = serviceNodes.length;
  while (n < services.length) {
    children.push({
      type: 'sl-service-node',
      version: 1,
      encounterService: services[n],
    });
    n++;
  }
  children.push(serviceSelectorNode);
  return JSON.stringify(stateJson);
}

const ServiceChainBlock: React.FC<IServiceChainBlockProps> = (props) => {
  const {
    initEncounterId,
    data,
    onSubmit,
    disabled,
    className,
    encounterDate,
    doctorProfile,
    scheinsFilteredByDate: scheinsFilteredByDateProp,
    onClear,
    openCreateSchein,
    onChange,
  } = props;
  const encounterDateObj = new Date(encounterDate || datetimeUtil.now())
  const hgncAvailableDate = new Date("2025-07-01T00:00:00")
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tConfirmDialog } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein.ConfirmDialog',
  });

  const { t: translator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const composerActionChainStore = useComposerActionChainStore();

  const actionBarStore = useActionBarStore();
  const { useGetDoctorList, getDoctorById } = useContext(
    GlobalContext.instance
  );
  const documentedDoctor = getDoctorById(actionBarStore.doctorId || '');

  const doctorLists = useGetDoctorList();

  const toast = useToaster();
  const patientFileStore = usePatientFileStore();
  const scheinOnSidebar = patientFileStore?.schein?.activatedSchein;
  const {
    patientManagement: { selectedContractDoctor },
    patientManagement,
  } = useContext(PatientManagementContext.instance);

  const initialStateLexical = useMemo(
    () => syncEditorData(data.encounterServiceChainRaw, data.services),
    [data]
  );

  const [isOpenEbmDialog, setIsOpenEbmDialog] = useState<boolean>(false);
  const [codeNotExist, setCodeNotExist] = useState('');
  const [filteredAdditionalInfos, setFilteredAdditionalInfos] = useState<
    AdditionalInfoField[]
  >([]);
  const [isOpenCreateMaterialCost, setIsOpenCreateMaterialCostDialog] =
    useState(false);
  const [createOmimGChainDialogState, setCreateOmimGChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: OmimGChain | undefined;
    }>({
      isOpen: false,
      defaultValue: undefined,
    });
  const [createHgncChainDialogState, setCreateHgncChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: HgncChain | undefined;
    }>({
      isOpen: false,
      defaultValue: undefined,
    });
  const [listChainDialogState, setListChainDialogState] = useState<{
    isOpen: boolean;
    query: string | null;
    additionalInfoNodeKey: NodeKey | null;
  }>({
    isOpen: false,
    query: '',
    additionalInfoNodeKey: null,
  });

  const [isOpenConfirmDialog, setIsOpenConfirmDialog] = useState(false);
  const [isOpenHospitalsPractices, setIsOpenHospitalsPractices] =
    useState(false);
  const [isOpenCreateDoctor, setIsOpenCreateDoctor] = useState(false);
  const [isOpenCreateJustification, setIsOpenCreateJustification] =
    useState(false);

  const { data: listSettingJustification, refetch } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });

  const patientParticipations: PatientParticipation[] =
    patientManagement?.getPatientParticipationResponse?.participations ?? [];

  const [selectedSchein, setSelectedSchein] = useState<ScheinItem | undefined>(
    undefined
  );

  const [isOpenConfirmScheinDialog, setOpenConfirmScheinDialog] =
    useState<boolean>(false);
  const [currentPayload, setCurrentPayload] =
    useState<EncounterServiceChain | null>(null);

  const memoizedDoctorASVNumbers = useMemo(
    () =>
      doctorLists.reduce((asvNumbers, doctor) => {
        if (doctor.teamNumbers?.length) {
          return asvNumbers.concat(doctor.teamNumbers);
        }
        return asvNumbers;
      }, [] as string[]),
    [doctorLists]
  );

  const currentSchein = useMemo(() => {
    if (selectedSchein) return selectedSchein;

    const singleScheinId = patientFileStore.schein.activatedSchein?.scheinId; // NOTE: only single Schein select on service code
    return (
      (singleScheinId != null &&
        scheinsFilteredByDateProp.find(
          (sch) => sch.scheinId === singleScheinId
        )) ||
      scheinOnSidebar
    );
  }, [
    selectedSchein,
    patientFileStore.schein.activatedSchein,
    scheinsFilteredByDateProp,
    scheinOnSidebar,
  ]);

  const scheinsFilteredByDate = useMemo(() => {
    return scheinsFilteredByDateProp.filter((schein) => {
      if (checkIsSvSchein(currentSchein)) {
        return checkIsSvSchein(schein);
      }

      return checkIsKvSchein(schein);
    });
  }, [scheinsFilteredByDateProp, currentSchein]);

  const currentContract = useMemo(() => {
    if (!currentSchein) return null;
    let scheinKey = '';
    if (checkIsSvSchein(currentSchein)) {
      scheinKey = currentSchein.hzvContractId!;
    } else if (currentSchein.kvTreatmentCase) {
      scheinKey = currentSchein.kvTreatmentCase;
    }

    catalogOverviewActions
      .getAdditionalInfos(
        currentSchein.scheinMainGroup,
        scheinKey,
        encounterDate
      )
      .then((fields) => {
        const infors = fields.map((info) => {
          info.label = translator(info.fK);
          return info;
        });
        if (
          memoizedDoctorASVNumbers?.length &&
          checkIsKvSchein(currentSchein)
        ) {
          infors.push({
            ...ASV_ADD_INFO,
            label: translator(ASV_ADD_INFO?.fK || ''),
          } as AdditionalInfoField);
        }
        setFilteredAdditionalInfos(infors);
      });
    const newContract = patientFileActions.getAvailableContractById(
      currentSchein.hzvContractId
    );
    return newContract;
  }, [currentSchein, memoizedDoctorASVNumbers, encounterDate]);

  const onServiceSelect = async <T extends IProps['usedFor']>(
    item: IContractData
  ): Promise<ServiceReturnType<T>> => {
    const resultAfterValidate = await ServiceMetaService.getServiceMeta(
      currentContract!,
      item,
      patientFileStore.pointValue!
    );
    if (!resultAfterValidate) {
      throw new Error('Service metadata retrieval failed.');
    }
    resultAfterValidate.scheins = parseToScheinMainGroup([currentSchein]);

    const updatedResult = {
      ...resultAfterValidate,
      serviceMainGroup: item.mainGroup ?? '',
      chargeSystemId: item.chargeSystemId ?? '',
    };

    return updatedResult as ServiceReturnType<T>;
  };

  const onSetScheinIds = (scheinItems: ScheinItem[] = []) => {
    setSelectedSchein(scheinItems[0]);
  };

  const searchServiceData = useMemo(
    () =>
      debounce(async (query: string, cb: (result: IContractData[]) => void) => {
        const contractBySchein = selectedSchein?.hzvContractId;
        setCodeNotExist(query);
        const pariticipation = patientParticipations?.find(
          (p) => p.contractId == contractBySchein
        );
        let currentContract: IContractInfo | null = null;
        if (pariticipation) {
          currentContract = {
            id: contractBySchein!,
            chargeSystemId: pariticipation.chargeSystemId,
            role: pariticipation.doctorFunctionType,
            type: pariticipation.contractType,
            status: pariticipation.status,
            doctorId: pariticipation.doctorId,
          };
        }
        const result = await ServiceBlockService.searchService(
          encounterDate!,
          query,
          doctorProfile!,
          [],
          currentContract,
          selectedContractDoctor,
          checkIsPrivateSchein(selectedSchein),
          selectedSchein?.scheinMainGroup,
          {
            isBgSchein: checkIsBgSchein(selectedSchein),
            isGeneral: !!selectedSchein?.isGeneral,
          }
        );

        if (currentContract) {
          const blankServicesResp = await getBlankServices({
            contractId: currentContract.id,
          });

          const overridedActiveBlankServices = result.map((s) => {
            const blankService = blankServicesResp.data.blankServices.find(
              (blank) => blank.code === s.code
            );
            if (isNil(blankService)) {
              return { ...s };
            }

            return {
              ...s,
              isActive: true,
              description: blankService.description,
              evaluation: blankService.price,
              unit: UnitStatistiks.UnitStatistiks_Euros,
            };
          });
          const excludingInactiveBlankService =
            overridedActiveBlankServices.filter(
              (s) => !s.isBlankService || s.isActive
            );
          cb(excludingInactiveBlankService.slice(0, 20));
        }

        cb(result.slice(0, 20));
      }, 500),
    [
      doctorProfile,
      encounterDate,
      selectedContractDoctor,
      selectedSchein,
      patientParticipations,
    ]
  );

  const handleClickNoResults = () => {
    setIsOpenEbmDialog(true);
  };

  const hasFavSchein = useMemo(() => {
    return patientFileStore.schein.originalList?.some((schein) =>
      checkIsSvSchein(schein)
    );
  }, [patientFileStore.schein.originalList]);

  const isShowSuggestChangeToFavSchein = useMemo(() => {
    return hasFavSchein && !currentSchein?.hzvContractId;
  }, [hasFavSchein, currentSchein]);

  const NoResults = () => {
    return (
      <>
        <Flex className="sl-no-results" p="8px 16px" justify="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
            {t('noResultsFound') + '.'}
          </BodyTextM>
          <Link onClick={handleClickNoResults}>{t('CreateServiceCode')}</Link>
        </Flex>
        {isShowSuggestChangeToFavSchein && (
          <BodyTextM>{t('changeToFavSchein')}</BodyTextM>
        )}
      </>
    );
  };

  const onSaveSuccessEbm = () => {
    alertSuccessfully(t('createSuccessEbm'), {
      timeout: TOASTER_TIMEOUT_CUSTOM,
      toaster: toast,
    });
    setIsOpenEbmDialog(false);
  };

  const memoizedEncounterDate = useMemo(() => encounterDate, [encounterDate]);

  const filterScheinFunc = useCallback(
    (_option: ScheinItem) =>
      currentSchein?.scheinMainGroup === _option?.scheinMainGroup,
    [currentSchein]
  );

  const onCloseHospitalsPractices = useCallback(() => {
    setIsOpenHospitalsPractices(false);
  }, []);

  const createListInsuranceMapBySchein = useMemo(() => {
    const listInsurance =
      patientFileStore.patient.current?.patientInfo.insuranceInfos || [];

    const listScheinId = scheinsFilteredByDate.map(
      (schein) => schein.insuranceId
    );

    const res = listInsurance.filter((insur) =>
      listScheinId.includes(insur.id)
    );
    const groupQuarterBySchein = groupQuarterbySchein(
      scheinsFilteredByDate,
      res
    );

    return groupQuarterBySchein;
  }, [scheinsFilteredByDate]);

  useEffect(() => {
    const firstSchein = data.services?.[0]?.scheins?.[0]?.scheinId;
    const defaultSelectedScheinId = initEncounterId
      ? firstSchein
      : patientFileStore?.schein?.activatedSchein?.scheinId;

    const scheinValid =
      scheinsFilteredByDate.find(
        (schein) => schein.scheinId === defaultSelectedScheinId
      ) ||
      (scheinsFilteredByDate?.length === 1 && !!firstSchein
        ? scheinsFilteredByDate[0]
        : (null as unknown as ScheinItem));

    setSelectedSchein(scheinValid);
    // TODO: Check logic auto bind first schein valid in quarter
    const scheins = scheinValid
      ? [
        {
          scheinId: scheinValid.scheinId,
          group: scheinValid.scheinMainGroup,
        },
      ]
      : [];
    data.services?.forEach((service) => {
      service = {
        ...service,
        scheins,
      };
    });
    onChange({ ...data, scheins });
  }, [
    initEncounterId,
    JSON.stringify(scheinsFilteredByDate),
    patientFileStore?.schein?.activatedSchein,
  ]);

  const handleSubmit = <T extends IProps['usedFor']>(
    payload: SubmitReturnType<T>,
    usedFor: IProps['usedFor']
  ) => {
    if (usedFor === 'SERVICE') {
      const updatedPayload: EncounterServiceChain = {
        ...(payload as EncounterServiceChain),
      };
      if (composerActionChainStore.currentBlock) {
        updatedPayload.encounterServiceChainRaw =
          composerActionChainStore.currentBlock.encounterServiceChainRaw;
        updatedPayload.services = updatedPayload.services.map(
          (service, index) => {
            service.additionalInfos =
              composerActionChainStore.currentBlock?.additionalInfosServiceChain?.[
              index
              ];
            return service;
          }
        );
      }
      if (!scheinsFilteredByDate.length || isEmpty(selectedSchein)) {
        setCurrentPayload({
          ...updatedPayload,
          services: updatedPayload.services.map((service) => ({
            ...service,
            // no schein
            scheins: [],
          })),
        });
        setOpenConfirmScheinDialog(true);
        return;
      }
      //inject command code to service
      updatedPayload.services.forEach((service) => {
        service.command = BaseComposerRowCommand.SERVICE;
        service.scheins = parseToScheinMainGroup([currentSchein]);
      });
      onSubmit(updatedPayload);
    }
  };

  return (
    <Flex className={className} column gap={0}>
      {/* lexical editor here... */}
      <ServiceChainTextEditor
        id="service_chain_text_editor"
        usedFor="SERVICE"
        disabled={disabled}
        scheinId={selectedSchein?.scheinId}
        initialEditorState={initialStateLexical || ''}
        onSubmit={(payload) => handleSubmit(payload, 'SERVICE')}
        className="sl-ServiceChainTextEditor"
        encounterDate={memoizedEncounterDate}
        searchService={searchServiceData}
        onServiceSelect={onServiceSelect}
        pointValue={patientFileStore.pointValue}
        additionalFields={filteredAdditionalInfos}
        noResultsComponent={<NoResults />}
        onClear={onClear}
        rightElement={
          <ScheinBlock
            selectedSchein={selectedSchein}
            openCreateSchein={() => openCreateSchein()}
            onSetScheinIds={onSetScheinIds}
            scheinFilter={createListInsuranceMapBySchein}
            filterOption={filterScheinFunc}
          />
        }
      >
        {/* Plugin for selecting additional info nodes */}
        {encounterDateObj < hgncAvailableDate ? <>
          <AdditionalInfoOmimGChainPlugin
            setAdditionalInfoNodeKey={(nodeKey) => {
              setListChainDialogState((prev) => ({
                ...prev,
                additionalInfoNodeKey: nodeKey,
              }));
            }}
            isServiceChain
            onSearch={async (query) => {
              const response = await searchOmimGChain({
                name: query,
                pagination: {
                  pageSize: 20,
                  page: 1,
                  sortBy: null!,
                  order: null!,
                },
              });
              return response.data.chains;
            }}
            onCreate={() => {
              setCreateOmimGChainDialogState({
                isOpen: true,
                defaultValue: undefined,
              });
            }}
            onViewAll={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                isOpen: true,
                query: '',
              }));
            }}
          />
          <OmimGChainOverview
            isOpen={listChainDialogState.isOpen}
            query={listChainDialogState.query!}
            additionalInfoNodeKey={
              listChainDialogState.additionalInfoNodeKey!
            }
            setQuery={(newQuery) => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: newQuery,
              }));
            }}
            onEdit={(item) => {
              setCreateOmimGChainDialogState({
                isOpen: true,
                defaultValue: item,
              });
            }}
            onClose={() => {
              setListChainDialogState({
                isOpen: false,
                query: '',
                additionalInfoNodeKey: null,
              });
            }}
            onCreate={() => {
              setCreateOmimGChainDialogState({
                isOpen: true,
                defaultValue: undefined,
              });
            }}
          />
        </> : <>
          <AdditionalInfoHgncChainPlugin
            setAdditionalInfoNodeKey={(nodeKey) => {
              setListChainDialogState((prev) => ({
                ...prev,
                additionalInfoNodeKey: nodeKey,
              }));
            }}
            isServiceChain
            onSearch={async (query) => {
              const response = await searchHgncChain({
                name: query,
                paginationRequest: {
                  pageSize: 20,
                  page: 1,
                  sortBy: null!,
                  order: null!,
                },
              });
              return response.data.hgncChains;
            }}
            onCreate={() => {
              setCreateHgncChainDialogState({
                isOpen: true,
                defaultValue: undefined,
              });
            }}
            onViewAll={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                isOpen: true,
                query: '',
              }));
            }}
          />
          <HgncChainOverview
            isOpen={listChainDialogState.isOpen}
            query={listChainDialogState.query!}
            additionalInfoNodeKey={
              listChainDialogState.additionalInfoNodeKey!
            }
            setQuery={(newQuery) => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: newQuery,
              }));
            }}
            onEdit={(item) => {
              setCreateHgncChainDialogState({
                isOpen: true,
                defaultValue: item,
              });
            }}
            onClose={() => {
              setListChainDialogState({
                isOpen: false,
                query: '',
                additionalInfoNodeKey: null,
              });
            }}
            onCreate={() => {
              setCreateHgncChainDialogState({
                isOpen: true,
                defaultValue: undefined,
              });
            }}
          />
        </>
        }
        {createOmimGChainDialogState.isOpen && (
          <OmimGCreateDialog
            isOpen
            defaultValue={createOmimGChainDialogState.defaultValue}
            onSuccess={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: prev.query === '' ? null : '',
              }));
            }}
            onClose={() => {
              setCreateOmimGChainDialogState({
                isOpen: false,
                defaultValue: undefined,
              });
            }}
          />
        )}
        {createHgncChainDialogState.isOpen && (
          <HgncCreateDialog
            isOpen
            defaultValue={createHgncChainDialogState.defaultValue}
            onSuccess={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: prev.query === '' ? null : '',
              }));
            }}
            onClose={() => {
              setCreateHgncChainDialogState({
                isOpen: false,
                defaultValue: undefined,
              });
            }}
          />
        )}

        <AdditionalInfoMaterialCostPlugin
          // onIndexing={startIndexingMaterialCost}
          onSearch={async (query, setData) => {
            const _searchQuery = query || '*';
            const results =
              (await MaterialCostService.searchMaterialCost(_searchQuery)) ??
              [];
            setData(results);
            return results;
          }}
          onCreateMaterialCostClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenConfirmDialog(true);
              return;
            }
            setIsOpenCreateMaterialCostDialog(true);
          }}
          onViewAllMaterialCostClick={() =>
            Router.push(ROUTING.MATERIAL_COST_CATALOG)
          }
          callback={(editor) => {
            const last = $$getAllNodes($isServiceNode).pop();
            $focusServiceNode(editor, last as ServiceNode);
          }}
        >
          {({ onNewMaterialCostAdded }) => (
            <>
              {isOpenCreateMaterialCost && (
                <CreateMaterialCostDialog
                  isOpen={isOpenCreateMaterialCost}
                  onClose={() => {
                    setIsOpenCreateMaterialCostDialog(false);
                    // _focusOnFirstInput();
                  }}
                  onCreateSuccess={onNewMaterialCostAdded}
                />
              )}
            </>
          )}
        </AdditionalInfoMaterialCostPlugin>
        {/* NOTE: auto-transform ASV teamnumber add.info block to list mode */}
        <AdditionalInfoAutoTransformListNodePlugin<string>
          targetFk={ASV_KEY}
          documentedDoctor={documentedDoctor}
          onQuery={async (query, setData) => {
            const menuItems = memoizedDoctorASVNumbers
              .filter((asv) => asv.includes(query))
              .map(
                (asv) =>
                ({
                  id: DatetimeUtil.now(),
                  label: asv,
                  value: asv,
                  data: asv,
                } as IMenuItemWithData<string>)
              );
            setData(menuItems);
            return menuItems;
          }}
        />
        <AdditionalInfoJusification
          onSearch={(query, setData) => {
            const _searchQuery = query || '*';
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const results = listJustification.filter((item) =>
              item.includes(_searchQuery)
            );
            setData(results);
            return results;
          }}
          onCreateJustificationClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenCreateJustification(true);
              return;
            }
            setIsOpenCreateJustification(true);
          }}
          onDeletedJustificationClick={async (value: string) => {
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const newListJustification = listJustification.filter(
              (v) => v != value
            );
            await saveSettings({
              settings: {
                [SETTING_KEY_ALLERGIES_FOR_5009]:
                  JSON.stringify(newListJustification),
              },
            });
            await refetch();
          }}
        >
          {({ onNewJustificationAdded }) => (
            <>
              {isOpenCreateJustification && (
                <CreateJusitficationModal
                  isOpen={isOpenCreateJustification}
                  className="stage-modal-justification"
                  onCreateSuccess={async (ws) => {
                    onNewJustificationAdded(ws);
                    const listJustification: string[] = JSON.parse(
                      listSettingJustification?.settings?.[
                      SETTING_KEY_ALLERGIES_FOR_5009
                      ] || '[]'
                    );
                    listJustification.push(ws!);
                    await saveSettings({
                      settings: {
                        [SETTING_KEY_ALLERGIES_FOR_5009]:
                          JSON.stringify(listJustification),
                      },
                    });
                    refetch();
                    setIsOpenCreateJustification(false);
                  }}
                  onClose={() => {
                    setIsOpenCreateJustification(false);
                  }}
                  t={t}
                />
              )}
            </>
          )}
        </AdditionalInfoJusification>
      </ServiceChainTextEditor>
      {/* CREATE SERVICE CODE DIALOG */}
      {isOpenEbmDialog && (
        <CreateEbmDialog
          createEbmDefaultValue={codeNotExist}
          isOpen={isOpenEbmDialog}
          onClose={() => setIsOpenEbmDialog(false)}
          onSaveSuccess={onSaveSuccessEbm}
        />
      )}
      {/* CONFIRM ADD NEW MATERIAL COST DIALOG */}
      <ConfirmOpenCreateDialog
        isOpen={isOpenConfirmDialog}
        onClose={() => {
          setIsOpenConfirmDialog(false);
          // _focusOnFirstInput();
        }}
        onConfirmClick={() => {
          setIsOpenConfirmDialog(false);
          setIsOpenCreateMaterialCostDialog(true);
        }}
      />
      {isOpenHospitalsPractices && (
        <HospitalsPracticesDialog
          isOpen={isOpenHospitalsPractices}
          onClose={onCloseHospitalsPractices}
          onSelect={() => {
            onCloseHospitalsPractices();
          }}
        />
      )}
      {isOpenCreateDoctor && (
        <CreateExternalAddressDialog
          isOpen={isOpenCreateDoctor}
          onClose={() => {
            setIsOpenCreateDoctor(false);
          }}
          onSuccess={() => null}
          onError={() => null}
        />
      )}
      {isOpenConfirmScheinDialog && (
        <InfoConfirmDialog
          type="primary"
          isOpen={isOpenConfirmScheinDialog}
          title={tConfirmDialog('title')}
          cancelText={tConfirmDialog('btnNo')}
          confirmText={tConfirmDialog('btnYes')}
          isShowIconTitle={false}
          onConfirm={() => {
            setOpenConfirmScheinDialog(false);
            openCreateSchein();
            setCurrentPayload(null);
          }}
          onClose={(closeAndSubmit) => {
            setOpenConfirmScheinDialog(false);
            if (closeAndSubmit) {
              onSubmit(currentPayload);
              setCurrentPayload(null);
            }
          }}
        >
          <Flex column>{tConfirmDialog('description')}</Flex>
        </InfoConfirmDialog>
      )}
    </Flex>
  );
};

export default React.memo(ServiceChainBlock);
