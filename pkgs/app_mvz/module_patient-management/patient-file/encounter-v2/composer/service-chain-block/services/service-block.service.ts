import { Nullable } from '@tutum/design-system/infrastructure/models';
import { generateRandomStr, isNumber } from '@tutum/design-system/infrastructure/utils';
import { searchGoaItems } from '@tutum/hermes/bff/legacy/app_mvz_catalog_goa';
import { ContractData } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import {
  AdditionalInfoParent,
  EncounterServiceTimeline,
} from '@tutum/hermes/bff/repo_encounter';
import { MainGroup, ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  default as DatetimeUtil,
  default as datetimeUtil,
} from '@tutum/infrastructure/utils/datetime.util';
import stringUtil from '@tutum/infrastructure/utils/string.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { cloneDeep } from 'lodash';
import { IComposerRow } from '../../Composer.type';
import { parseToScheinMainGroup } from '../../Composer.util';
import OKVService from './okv.service';
import ServiceMetaService from './service-meta.service';
import { DoctorFunctionType } from '@tutum/hermes/bff/legacy/service_domains_patient_participation';
import { searchUvGoaItems } from '@tutum/hermes/bff/legacy/app_mvz_catalog_uv_goa';
import { UnitStatistiks } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import { searchEbmsComposer } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';

const KV_SUB_GROUP_28 = '28';

async function findValidServiceCodes(
  date: number,
  query: string,
  doctorProfile: IEmployeeProfile,
  _data: any,
  contract?: Nullable<IContractInfo>,
  selectedContractDoctor?: ISelectedContractDoctor,
  isPrivateSchein?: boolean,
  bgData?: { isBgSchein: boolean; isGeneral: boolean },
  scheinMainGroup?: MainGroup
): Promise<IContractData[]> {
  // NOTE: HzV / FaV
  if (contract != null) {
    const tasks: Promise<ContractData[]>[] = [];
    const hzvService = catalogOverviewActions
      .searchContract(query, contract.id, contract.chargeSystemId)
      .then((serviceList) => {
        return serviceList.filter((item) => {
          return (
            filterServiceCodeWithValidDates(item, date, contract) &&
            isServiceCodeWithValidOkv(item, date, doctorProfile) &&
            isServiceCodeWithValidIkGroups(item, date)
          );
        });
      });
    tasks.push(hzvService);

    if (
      selectedContractDoctor?.moduleChargeSystemId &&
      selectedContractDoctor.moduleChargeSystemId !== contract.chargeSystemId
    ) {
      const moduleHzvService = catalogOverviewActions
        .searchContract(
          query,
          contract.id,
          selectedContractDoctor.moduleChargeSystemId
        )
        .then((moduleServiceList) =>
          moduleServiceList.filter((item) => {
            return (
              filterServiceCodeWithValidDates(item, date, contract) &&
              isServiceCodeWithValidOkv(item, date, doctorProfile) &&
              isServiceCodeWithValidIkGroups(item, date)
            );
          })
        );
      tasks.push(moduleHzvService);
    }

    return Promise.all(tasks).then((results) =>
      results.flat()?.map((hzvItem: any) => {
        hzvItem.mainGroup = MainGroup.HZV;
        return hzvItem;
      })
    );
  }

  // NOTE: Pg / Ig
  if (isPrivateSchein) {
    return searchGoaItems({
      value: (query || '').trim(),
      selectedDate: date,
    }).then((items) => {
      return items.data.items.map((item, index) => {
        return {
          ...item,
          price: item.price,
          factor: item.factor,
          id: index,
          code: item.goaNumber,
          mainGroup: scheinMainGroup ? scheinMainGroup : MainGroup.PRIVATE,
          isBlankService: false,
          isActive: true,
          validFrom: 1,
          highlight: item.highlight,
          isSelfCreated: item.isSelfCreated,
          unit: UnitStatistiks.UnitStatistiks_Euros,
        } as IContractData;
      });
    });
  }

  if (bgData && bgData.isBgSchein) {
    return searchUvGoaItems({
      value: query,
      selectedDate: date,
      isGeneral: bgData.isGeneral,
    }).then((items) => {
      return items.data.items.map((item, index) => {
        return {
          ...item,
          id: index,
          mainGroup: MainGroup.BG,
          isBlankService: false,
          isActive: true,
          validFrom: 1,
          isSelfCreated: item.isSelfCreated,
          price: item.evaluation,
          highlight: item.highlight,
          unit: UnitStatistiks.UnitStatistiks_Euros,
        } as IContractData;
      });
    });
  }

  // NOTE: kV
  const isQueryNumber = query.length === 1 && isNumber(query);
  const searchable = isQueryNumber || query.length >= 2;
  if (!searchable) {
    return Promise.resolve([]);
  }
  return searchEbmsComposer({
    selectedDate: date,
    query,
    organizationId: await webWorkerServices.convertUkvToOkv(
      stringUtil.getKVRegion(doctorProfile?.bsnr)
    ),
  }).then((embRes) => {
    return (embRes?.data?.items || []).map((item) =>
    ({
      code: item.code,
      description: item.description,
      highlight: item.highlight,
      evaluation: item.evaluation,
      unit: item.unit,
      isSelfCreated: item.isSelfCreated,
      mainGroup: MainGroup.KV,
    } as IContractData))
  });
}

function searchService(
  date: number,
  query: string,
  doctorProfile: IEmployeeProfile,
  data: any,
  contract?: IContractInfo | null,
  selectedContractDoctor?: ISelectedContractDoctor,
  isPrivate?: boolean,
  scheinMainGroup?: MainGroup,
  bgData?: { isBgSchein: boolean; isGeneral: boolean }
): Promise<IContractData[]> {
  if (!query) {
    return Promise.resolve([]);
  }

  return findValidServiceCodes(
    date,
    query,
    doctorProfile,
    data,
    contract,
    selectedContractDoctor,
    isPrivate,
    bgData,
    scheinMainGroup
  );
}


async function parseCombineServices(
  row: IComposerRow,
  doctorProfile: IEmployeeProfile,
  contract: IContractInfo,
  selectedContractDoctor: ISelectedContractDoctor,
  _patient: IPatientProfile,
  encounterDate: number,
  freeText: string,
  scheins: ScheinItem[],
  data: any
): Promise<IComposerRow[]> {
  const serviceList = await findValidServiceCodes(
    encounterDate,
    freeText,
    doctorProfile,
    data,
    contract,
    selectedContractDoctor,
    false
  );
  const selectedServices = freeText.split('-').filter((result) => {
    return !!result;
  });
  const arrService: Promise<IComposerRow>[] = selectedServices.map(
    async (code, index) => {
      const selectedService =
        serviceList.find((item) => item.code === code.trim()) ||
        ({} as IContractData);

      if (selectedService) {
        const result = await ServiceMetaService.getServiceMeta(
          contract,
          selectedService
        );
        const serviceTmp: EncounterServiceTimeline = result;
        const scheinFilter = scheins.filter((item) => {
          if (item.scheinMainGroup == selectedService.mainGroup) {
            return item;
          }
        });
        serviceTmp.scheins = parseToScheinMainGroup(scheinFilter);
        serviceTmp.serviceMainGroup = selectedService.mainGroup;
        const freeText = selectedService.code
          ? `(${selectedService.code}) ${selectedService.description}`
          : `(${code}) `;
        const composerRow: IComposerRow = {
          ...row,
          id: index === 0 ? row.id : undefined,
          key: generateRandomStr(),
          freeText: freeText,
          meta: {
            service: serviceTmp,
          },
        };
        return composerRow;
      }
      const composerRow: IComposerRow = {
        ...row,
        id: index === 0 ? row.id : undefined,
        key: row.key,
        freeText: code.trim(),
      };
      return composerRow;
    }
  );
  return Promise.all(arrService);
}

// For `Custodian` contract function:
// - Show custodian service only
//
// For `Deputy` contract function:
// - Show both custodian and deputy services
// - Show error for custodian service
//
// ABRD605/ABRD606 AKA rule
function filterServiceCodeByContractParticipation(
  data: ContractData,
  contract?: IContractInfo
): boolean {
  if (!data.rules || !data.rules.doctorFunctionTypes || !contract) {
    return true;
  }

  const doctorFunctionType = data.rules.doctorFunctionTypes.find((role) => {
    const isCustodian = role === DoctorFunctionType.DoctorFunctionTypeCustodian;
    const isDeputy = role === DoctorFunctionType.DoctorFunctionTypeDeputy;

    if (contract.role === DoctorFunctionType.DoctorFunctionTypeCustodian)
      return isCustodian;

    if (contract.role === DoctorFunctionType.DoctorFunctionTypeDeputy)
      return isCustodian || isDeputy;

    return false;
  });

  // Return true if a matching doctor function type is found, otherwise false
  return doctorFunctionType !== undefined;
}

//ABRD602 - allow only documentation of contract specific services
function filterServiceCodeWithValidDates(
  data: ContractData,
  date: number,
  contract?: IContractInfo
) {
  return (
    !contract?.id ||
    DatetimeUtil.isBetween(
      DatetimeUtil.utc(date),
      data.validFrom,
      data.validTo,
      'D',
      '[]'
    )
  );
}

//ABRD830/ABRD603 - determination of contract specific services depending on IK allocation
function isServiceCodeWithValidOkv(
  data: ContractData,
  date: number,
  doctorProfile: IEmployeeProfile
) {
  if (!data.conditions || !data.conditions.kvRegionInclusions?.length) {
    return true;
  }
  const okv = OKVService.getOkvByBsnr(String(doctorProfile?.bsnr));
  return (
    data.conditions.kvRegionInclusions.find(
      (item) =>
        item.okv === okv &&
        !(
          date < item.validFrom ||
          (Boolean(item.validTo) && date > item.validTo)
        )
    ) != undefined
  );
}

//ABRD830/ABRD603 - determination of contract specific services depending on IK allocation
function isServiceCodeWithValidIkGroups(data: ContractData, date: number) {
  if (!data.conditions || !data.conditions.iKGroupInclusion) {
    return true;
  }
  return data.conditions.iKGroupInclusion.some((item) => {
    const ikNumber = patientFileStore.activeInsurance?.ikNumber;
    return (
      item.iKs.includes(ikNumber) &&
      DatetimeUtil.isBetween(
        DatetimeUtil.utc(date),
        item.validFrom,
        item.validTo,
        'D',
        '[]'
      )
    );
  });
}

function handleFk5098and5099ForKvPatient(
  additionalInfos: AdditionalInfoParent[],
  doctorProfile: IEmployeeProfile,
  activatedSchein: ScheinItem
): AdditionalInfoParent[] {
  if (!activatedSchein) {
    return [];
  }

  const fk = {
    bnsr: '5098',
    lanr: '5099',
  };

  const fixedValues = [
    {
      fK: fk.bnsr,
      value: doctorProfile?.bsnr,
    },
  ];

  if (doctorProfile.markAsBillingDoctor) {
    fixedValues.push({
      fK: fk.lanr,
      value: doctorProfile?.lanr,
    });
  }

  const isSubGroup28 = activatedSchein?.kvScheinSubGroup === KV_SUB_GROUP_28;
  const scheinDetail = activatedSchein?.scheinDetail;
  const tempData = cloneDeep(additionalInfos);

  fixedValues.forEach((fixedValue) => {
    const existedAdditionalInfo = tempData?.find(
      (item) => item.fK == fixedValue.fK
    );

    if (existedAdditionalInfo) {
      if (!isSubGroup28) {
        return;
      }

      const index = tempData?.findIndex((item) => item.fK == fixedValue.fK);

      if (fixedValue.fK === fk.bnsr) {
        tempData[index] = {
          ...tempData[index],
          value: scheinDetail['re4218'],
        };
      }
      if (fixedValue.fK === fk.lanr) {
        tempData[index] = {
          ...tempData[index],
          value: scheinDetail['re4242'],
        };
      }
    } else {
      let value = fixedValue.value;

      if (isSubGroup28) {
        value =
          fixedValue.fK === fk.bnsr
            ? scheinDetail['re4218']
            : fixedValue.fK === fk.lanr
              ? scheinDetail['re4242']
              : fixedValue.value;
      }

      tempData.push({
        ...fixedValue,
        value,
        children: null,
      });
    }
  });

  return tempData;
}

const handleFk5300 = (
  additionalInfos: AdditionalInfoParent[]
): AdditionalInfoParent[] => {
  if (!additionalInfos || additionalInfos?.length === 0) return [];
  const tempData = additionalInfos.map((additional) => {
    if (additional.fK === '5300') {
      return {
        ...additional,
        value: additional.value
          ? parseFloat(additional.value).toFixed(2)
          : additional.value,
      };
    }
    return additional;
  });

  return tempData;
};

export default {
  searchService,
  parseCombineServices,
  filterServiceCodeByContractParticipation,
  filterServiceCodeWithValidDates,
  isServiceCodeWithValidOkv,
  isServiceCodeWithValidIkGroups,
  handleFk5098and5099ForKvPatient,
  handleFk5300,
};
