import React, { DOMAttributes } from 'react';
import {
  Popover,
  PopoverPosition,
  MenuItem,
  Keys,
  PopoverProps,
  Classes,
} from '@tutum/design-system/components/Core';
import {
  QueryList,
  QueryListRendererProps,
  ItemRendererProps,
} from '@tutum/design-system/components/Select';
import { ItemPredicate } from '@tutum/design-system/components/Select';
import { Flex, Box, BodyTextM } from '@tutum/design-system/components';
import Theme, { IMvzTheme } from '@tutum/mvz/theme';
import { IDropDownData } from '../../../PatientFile.type';
import ContentEditable, {
  getData,
  setData,
  IInputProps,
} from './ContentEditable';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IPopoverContentEditableProps {
  className?: string;
  theme?: IMvzTheme;
  inputRef?: React.MutableRefObject<HTMLSpanElement>;
  inputProps: DOMAttributes<HTMLSpanElement> & IInputProps;
  popOverProps?: PopoverProps;
  listProps?: IListItemsProps;
  placeholder?: string;
  value?: string;
  onTab?: () => void;
  onBackspaceOrLeftArrow?: (
    event: React.KeyboardEvent<HTMLSpanElement>
  ) => void;
  isService?: boolean;
  renderNoResults?: (onMouseEnter, onMouseLeave) => React.ReactNode;
}

export interface IPopoverContentEditableStates {
  insideMenuItem?: boolean;
}

export interface IListItemsProps {
  menuItemRenderer?: (props: IMenuItemRenderer) => React.ReactNode;
  items: IDropDownData[];
  query?: string;
  itemPredicate?: ItemPredicate<any>;
  onItemSelect: (
    item: IDropDownData,
    event?: React.SyntheticEvent<HTMLElement>
  ) => void;
}

export interface IMenuItemRenderer {
  item: IDropDownData;
  query: string;
}

const TypedQueryList = QueryList.ofType<IDropDownData>();
const EMPTY_ARRAY: any[] = [];
const EMPTY_OBJECT: any = {};

class PopoverContentEditable extends React.PureComponent<
  IPopoverContentEditableProps & II18nFixedNamespace<any>,
  IPopoverContentEditableStates
> {
  constructor(props: IPopoverContentEditableProps & II18nFixedNamespace<any>) {
    super(props);
    this.state = {
      insideMenuItem: false,
    };
  }

  render() {
    const {
      t,
      popOverProps = EMPTY_OBJECT as PopoverProps,
      listProps = { items: EMPTY_ARRAY } as IListItemsProps,
      className,
      inputRef,
      inputProps,
      placeholder,
      value,
      isService,
      renderNoResults,
    } = this.props;
    const itemsLength = listProps.items.length;

    return (
      <div className={className}>
        <TypedQueryList
          noResults={
            renderNoResults
              ? renderNoResults(this.onMouseEnter, this.onMouseLeave)
              : null
          }
          itemRenderer={(item: IDropDownData, itemProps: ItemRendererProps) => {
            const isLastItem = itemsLength - 1 === itemProps.index;
            const customItemClick = (
              event: React.MouseEvent<HTMLElement, MouseEvent>
            ) => {
              setData(inputRef, this.props.value);
              itemProps.handleClick(event);
            };
            return (
              <React.Fragment key={`${item.code}_${itemProps.index}`}>
                <MenuItem
                  onMouseEnter={this.onMouseEnter}
                  onMouseLeave={this.onMouseLeave}
                  className={item.group ? 'group' : ''}
                  active={itemProps.modifiers.active}
                  disabled={itemProps.modifiers.disabled}
                  onClick={customItemClick}
                  text={
                    listProps.menuItemRenderer ? (
                      listProps.menuItemRenderer({
                        item,
                        query: getData(inputRef),
                      })
                    ) : (
                      <Flex>
                        <Box
                          className={`${Classes.MENU_ITEM_LABEL} menuItemLeftValue`}
                        >
                          {item.code}
                        </Box>
                        <Box
                          auto
                          className={`${Classes.TEXT_OVERFLOW_ELLIPSIS} ${Classes.FILL}`}
                        >
                          {item.description}
                        </Box>
                      </Flex>
                    )
                  }
                />
                {isLastItem && (
                  <MenuItem
                    active={false}
                    className="lastMenuItem"
                    text={
                      <Flex>
                        <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER}>
                          {t('listNavigation')}
                        </BodyTextM>
                      </Flex>
                    }
                  />
                )}
              </React.Fragment>
            );
          }}
          renderer={(rendererProps: QueryListRendererProps<IDropDownData>) => (
            <Popover
              minimal
              autoFocus={false}
              enforceFocus={false}
              canEscapeKeyClose
              usePortal={false}
              fill
              position={PopoverPosition.BOTTOM_LEFT}
              // boundary="window"
              {...popOverProps}
            >
              <ContentEditable
                inputRef={inputRef}
                placeholder={placeholder}
                className={inputProps.className}
                onInput={this.onInput}
                onBlur={this.onBlur}
                onFocus={this.onFocus}
                onKeyDown={(event, data) =>
                  this.onKeyDown(event, data, rendererProps)
                }
                isService={isService}
                onKeyUp={(event) => this.onKeyUp(event, rendererProps)}
                value={value}
                disabled={inputProps.disabled}
              />
              <div
                onKeyDown={rendererProps.handleKeyDown}
                onKeyUp={rendererProps.handleKeyUp}
              >
                {rendererProps.itemList}
              </div>
            </Popover>
          )}
          {...listProps}
        />
      </div>
    );
  }

  onFocus = (event: React.FocusEvent<HTMLSpanElement>, data?: string) => {
    const { onFocus } = this.props.inputProps;
    onFocus && onFocus(event, data);
  };

  onMouseEnter = () => {
    this.setState({
      insideMenuItem: true,
    });
  };
  onMouseLeave = () => {
    this.setState({
      insideMenuItem: false,
    });
  };

  onBlur = (event: React.FocusEvent<HTMLSpanElement>, data?: string) => {
    const { inputProps } = this.props;
    const { insideMenuItem } = this.state;
    if (insideMenuItem) {
      return;
    }
    inputProps.onBlur && inputProps.onBlur(event, data);
  };

  onInput = (event: React.FormEvent<HTMLSpanElement>, data?: string) => {
    const { onInput } = this.props.inputProps;
    onInput && onInput(event, data);
  };

  onKeyUp = (
    event: React.KeyboardEvent<HTMLSpanElement>,
    rendererProps: QueryListRendererProps<IDropDownData>
  ) => {
    rendererProps.handleKeyUp(event);
  };

  onKeyDown = (
    event: React.KeyboardEvent<HTMLSpanElement>,
    data: string,
    rendererProps: QueryListRendererProps<IDropDownData>
  ) => {
    const {
      popOverProps = EMPTY_OBJECT,
      inputProps,
      inputRef,
      onTab,
      listProps = EMPTY_OBJECT,
      onBackspaceOrLeftArrow,
      isService,
    } = this.props;
    const regex = /[0-9a-zA-Z]/;
    if (popOverProps.isOpen && (listProps.items || []).length > 0) {
      if (event.keyCode === Keys.ENTER) {
        setData(inputRef, this.props.value);
        event.preventDefault();
      }
      if (
        event.keyCode === Keys.BACKSPACE ||
        event.keyCode === Keys.ARROW_LEFT
      ) {
        onBackspaceOrLeftArrow && onBackspaceOrLeftArrow(event);
      }
      if (event.keyCode === Keys.TAB) {
        onTab && onTab();
      }
      if (regex.test(event.key) && isService) {
        inputProps.onKeyDown && inputProps.onKeyDown(event, data);
      }
      rendererProps.handleKeyDown(event);
    } else {
      inputProps.onKeyDown && inputProps.onKeyDown(event, data);
    }
  };
}

export default I18n.withTranslation(PopoverContentEditable, {
  namespace: 'Common',
  nestedTrans: 'NavigationSuggest',
});
