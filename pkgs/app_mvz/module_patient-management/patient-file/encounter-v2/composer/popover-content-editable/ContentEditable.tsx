import React, { useEffect, useState } from 'react';
import { Keys, Classes } from '@tutum/design-system/components/Core';
import {
  getCssClass,
  isNotEmpty,
  stripHTMLtags,
} from '@tutum/design-system/infrastructure/utils';
import { setCaretPositionAtEnd } from '@tutum/mvz/components/select-diagnosis-dialog/ComposerDiagnosis/helpers';

const INPUT_MAX_LENGTH = 500;

export interface IContentEditableProps {
  value: string;
  placeholder?: string;
  inputRef: React.MutableRefObject<HTMLSpanElement>;
  className?: string;
  disabled?: boolean;
  isService?: boolean;
}

export interface IInputProps {
  className?: string;
  disabled?: boolean;
  onInput?: (event?: React.FormEvent<HTMLSpanElement>, data?: string) => void;
  onKeyDown?: (
    event: React.KeyboardEvent<HTMLSpanElement>,
    data?: string
  ) => void;
  onKeyUp?: (
    event: React.KeyboardEvent<HTMLSpanElement>,
    data?: string
  ) => void;
  onBlur?: (event: React.FocusEvent<HTMLSpanElement>, data?: string) => void;
  onFocus?: (event: React.FocusEvent<HTMLSpanElement>, data?: string) => void;
}

const ALLOWED_TAGS = '<i><b><br><strong><p>';

export const getData = (
  inputRef: React.MutableRefObject<HTMLSpanElement>
): string => {
  if (!inputRef || !inputRef.current) {
    return '';
  }
  return stripHTMLtags(inputRef.current.innerHTML, ALLOWED_TAGS);
};

export const setData = (
  inputRef: React.MutableRefObject<HTMLSpanElement>,
  data: string,
  isFocused = true
) => {
  if (!inputRef) {
    return;
  }
  const allowedData = data?.substring(0, INPUT_MAX_LENGTH);
  inputRef.current.innerHTML = stripHTMLtags(allowedData, ALLOWED_TAGS);
  if (isNotEmpty(allowedData) && inputRef.current && isFocused) {
    setCaretPositionAtEnd(inputRef.current);
  }
  return;
};

const isSupportedKeyEvent = ({
  keyCode,
}: React.KeyboardEvent<HTMLSpanElement>) => {
  return (
    keyCode === Keys.BACKSPACE ||
    keyCode === Keys.ENTER ||
    keyCode === Keys.ARROW_DOWN ||
    keyCode === Keys.ARROW_UP ||
    keyCode === Keys.ARROW_LEFT ||
    keyCode === Keys.ARROW_RIGHT ||
    keyCode === Keys.TAB
  );
};

export default React.memo(
  ({
    value,
    inputRef,
    onInput,
    onKeyDown,
    onFocus,
    onKeyUp,
    onBlur,
    className,
    placeholder,
    disabled,
    isService,
  }: IContentEditableProps & IInputProps) => {
    const [isFocused, setIsFocused] = useState<boolean>(false);
    const _onKeyDown = (event: React.KeyboardEvent<HTMLSpanElement>) => {
      if (
        inputRef?.current?.textContent!.length >= INPUT_MAX_LENGTH &&
        !isSupportedKeyEvent(event) &&
        !event.metaKey &&
        !event.ctrlKey
      ) {
        event.preventDefault();
        event.stopPropagation();
      }
      if (isSupportedKeyEvent(event) || isService) {
        onKeyDown?.(event, getData(inputRef));
      }
    };

    const _onKeyUp = (event: React.KeyboardEvent<HTMLSpanElement>) => {
      if (isSupportedKeyEvent(event)) {
        onKeyUp?.(event, getData(inputRef));
      } else if (inputRef?.current?.textContent!.length >= INPUT_MAX_LENGTH) {
        event.preventDefault();
        event.stopPropagation();
      }
    };

    const _onInput = (event: React.FormEvent<HTMLSpanElement>) => {
      onInput && onInput(event, getData(inputRef));
    };

    const _onBlur = (event: React.FocusEvent<HTMLSpanElement>) => {
      setIsFocused(false);
      onBlur?.(event, getData(inputRef));
    };

    const _onPaste = (event: React.ClipboardEvent<HTMLSpanElement>) => {
      event.preventDefault();
      const data = event.clipboardData.getData('text');
      setData(inputRef, data);
      onInput && onInput(event, getData(inputRef));
    };

    const _onFocus = (event: React.FocusEvent<HTMLSpanElement>) => {
      setIsFocused(true);
      onFocus && onFocus(event, getData(inputRef));
    };

    useEffect(() => {
      setData(inputRef, value, isFocused);
    }, [inputRef, value]);

    return (
      <span
        ref={inputRef}
        contentEditable={!disabled}
        placeholder={placeholder}
        onInput={_onInput}
        onKeyDown={_onKeyDown}
        onKeyUp={_onKeyUp}
        onBlur={_onBlur}
        onPaste={_onPaste}
        onFocus={_onFocus}
        className={getCssClass(Classes.FILL, className)}
      />
    );
  }
);
