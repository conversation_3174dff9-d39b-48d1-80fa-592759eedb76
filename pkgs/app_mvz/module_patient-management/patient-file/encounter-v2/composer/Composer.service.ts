import {
  create,
  edit,
  remove,
  RemoveRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { DiagnoseType, Diagnose } from '@tutum/hermes/bff/repo_encounter';
import { BaseComposerRowType } from './Composer.type';

const DIAGNOSE_TYPE_TO_COMPOSER_TYPE: Record<DiagnoseType, string> = {
  [DiagnoseType.DIAGNOSETYPE_PERMANENT]: BaseComposerRowType.PERMANENT_DIAGNOSE,
  [DiagnoseType.DIAGNOSETYPE_ACUTE]: BaseComposerRowType.ACUTE_DIAGNOSE,
  [DiagnoseType.DIAGNOSETYPE_ANAMNESTIC]:
    BaseComposerRowType.ANAMNESTIC_DIAGNOSE,
};

const ENCOUNTER_TYPE_TO_COMPOSER_TYPE: Record<string, string> = {
  [BaseComposerRowType.ANAMNESE]: BaseComposerRowType.ANAMNESE,
  [BaseComposerRowType.FINDINGS]: BaseComposerRowType.FINDINGS,
  [BaseComposerRowType.THERAPY]: BaseComposerRowType.THERAPY,
  [BaseComposerRowType.NOTES]: BaseComposerRowType.NOTES,
};

export enum KEY_MAPPING_TIMELINE {
  DIAGNOSE = 'encounterDiagnoseTimeline',
  SERVICE = 'encounterServiceTimeline',
  GOASERVICE = 'encounterGoaService',
  UVGOASERVICE = 'encounterUvGoaService',
  NOTE = 'encounterNoteTimeline',
  MEDICINE_PLAN_HISTORY = 'encounterMedicinePlanHistory',
  MEDICINE_PRESCRIPTION = 'encounterMedicinePrescription',
  HIMI_PRESCRIPTION = 'encounterHimiPrescription',
  PATIENT_MEDICAL_DATA = 'encounterPatientMedicalData',
  HEIMI_PRESCRIPTION = 'encounterHeimiPrescription',
  LAB = 'encounterLab',
  FORM = 'encounterForm',
  SERVICE_CHAIN = 'encounterServiceChain',
  GOA_SERVICE_CHAIN = 'encounterGoaServiceChain',
  UV_GOA_SERVICE_CHAIN = 'encounterUvGoaServiceChain',
  GDT = 'encounterGDT',
  LDT = 'encounterLDT',
  CUSTOMIZE = 'encounterCustomize',
}

export const getKeyTimelineMapping = (type: string): KEY_MAPPING_TIMELINE => {
  switch (type) {
    case BaseComposerRowType.PERMANENT_DIAGNOSE:
    case BaseComposerRowType.ACUTE_DIAGNOSE:
    case BaseComposerRowType.ANAMNESTIC_DIAGNOSE:
    case DiagnoseType.DIAGNOSETYPE_ACUTE:
    case DiagnoseType.DIAGNOSETYPE_PERMANENT:
    case DiagnoseType.DIAGNOSETYPE_ANAMNESTIC:
      return KEY_MAPPING_TIMELINE.DIAGNOSE;
    case BaseComposerRowType.SERVICE:
      return KEY_MAPPING_TIMELINE.SERVICE;
    case BaseComposerRowType.GOASERVICE:
      return KEY_MAPPING_TIMELINE.GOASERVICE;
    case BaseComposerRowType.SERVICE_CHAIN:
      return KEY_MAPPING_TIMELINE.SERVICE_CHAIN;
    case BaseComposerRowType.GOA_SERVICE_CHAIN:
      return KEY_MAPPING_TIMELINE.GOA_SERVICE_CHAIN;
    case BaseComposerRowType.GDT:
      return KEY_MAPPING_TIMELINE.GDT;
    case BaseComposerRowType.LDT:
      return KEY_MAPPING_TIMELINE.LDT;
    case BaseComposerRowType.UV_GOA_SERVICE:
      return KEY_MAPPING_TIMELINE.UVGOASERVICE;
    case BaseComposerRowType.CUSTOMIZE:
      return KEY_MAPPING_TIMELINE.CUSTOMIZE;
    case BaseComposerRowType.UV_GOA_SERVICE_CHAIN:
      return KEY_MAPPING_TIMELINE.UV_GOA_SERVICE_CHAIN;
    default:
      return KEY_MAPPING_TIMELINE.NOTE;
  }
};

export const needScheinIds = (keyMapping: KEY_MAPPING_TIMELINE) => {
  return [KEY_MAPPING_TIMELINE.DIAGNOSE, KEY_MAPPING_TIMELINE.SERVICE].includes(
    keyMapping
  );
};

export const getDefaultFreeText = (diagnose: Diagnose) =>
  `(${diagnose.code}) ${diagnose.description}`;

export const toComposerRowType = (entry: TimelineModel): string => {
  const typeDiagnose = entry?.encounterDiagnoseTimeline?.type;

  if (typeDiagnose) {
    return DIAGNOSE_TYPE_TO_COMPOSER_TYPE[typeDiagnose];
  }
  if (entry?.encounterUvGoaServiceChain) {
    return BaseComposerRowType.UV_GOA_SERVICE_CHAIN;
  }
  if (entry?.encounterServiceChain) {
    return BaseComposerRowType.SERVICE_CHAIN;
  }
  if (entry?.encounterGoaServiceChain) {
    return BaseComposerRowType.GOA_SERVICE_CHAIN;
  }

  if (entry?.encounterServiceTimeline) {
    return BaseComposerRowType.SERVICE;
  }

  if (entry?.encounterGoaService) {
    return BaseComposerRowType.GOASERVICE;
  }

  if (entry?.encounterGDT) {
    return BaseComposerRowType.GDT;
  }

  if (entry?.encounterLDT) {
    return BaseComposerRowType.LDT;
  }

  if (entry?.encounterUvGoaService) {
    return BaseComposerRowType.UV_GOA_SERVICE;
  }
  if (entry?.encounterCustomize) {
    return BaseComposerRowType.CUSTOMIZE;
  }

  const typeNote = entry?.encounterNoteTimeline?.type;

  if (ENCOUNTER_TYPE_TO_COMPOSER_TYPE[typeNote]) {
    return ENCOUNTER_TYPE_TO_COMPOSER_TYPE[typeNote];
  }

  return BaseComposerRowType.UNKNOWN;
};

export const createTimelineItem = async (payload: TimelineModel) => {
  const convertedPayload = {
    timelineModel: payload,
  };
  const data = await create(convertedPayload);

  return data?.data;
};

export const editTimelineItem = async (payload: TimelineModel) => {
  const convertedPayload = {
    timelineModel: payload,
  };

  const data = await edit(convertedPayload);

  return data?.data;
};

export const deleteTimelineItem = async (payload: RemoveRequest) => {
  const data = await remove(payload);
  return data?.data;
};
