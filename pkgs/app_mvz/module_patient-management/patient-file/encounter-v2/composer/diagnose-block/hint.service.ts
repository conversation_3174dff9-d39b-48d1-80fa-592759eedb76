import { Gender } from '@tutum/hermes/bff/patient_profile_common';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { Diagnose, Meta } from '@tutum/infrastructure/masterdata-service/type';

function getHints(
  item: Diagnose,
  pateintProfile: IPatientProfile,
  t: IFixedNamespaceTFunction<keyof typeof CommonI18n.HintError>,
  command: string
): string[] {
  const { meta, noPermanent } = item;
  const genderHint = getGenderHint(meta, pateintProfile, t);
  const ageHint = getAgeHint(meta, pateintProfile, t);
  const hints = new Array<string>();
  if (genderHint) {
    hints.push(genderHint);
  }

  if (ageHint) {
    hints.push(ageHint);
  }

  if (noPermanent && command == 'DD') hints.push(getPermanent(t));

  return hints;
}

export default { getHints };

// -------------------------------------------- //

function greaterThanLowerDayLimit(
  now: Date,
  currentDob: Date,
  lowerDayLimit: number
): boolean {
  const daysOld = Math.floor(
    (now.getTime() - currentDob.getTime()) / (24 * 3600 * 1000)
  );

  return daysOld >= lowerDayLimit;
}

function greaterThanLowerYearLimit(
  now: Date,
  currentDob: Date,
  lowerYearLimit: number
): boolean {
  const newYearOfBirth = currentDob.getFullYear() + lowerYearLimit;
  const newDob = currentDob.setFullYear(newYearOfBirth);

  return now.getTime() >= newDob;
}

function lessThanUpperDayLimit(
  now: Date,
  currentDob: Date,
  upperDayLimit: number
): boolean {
  const daysOld = Math.floor(
    (now.getTime() - currentDob.getTime()) / (24 * 3600 * 1000)
  );

  return daysOld <= upperDayLimit;
}

function lessThanUpperYearLimit(
  now: Date,
  currentDob: Date,
  lowerYearLimit: number
): boolean {
  const newYearOfBirth = currentDob.getFullYear() + lowerYearLimit;
  const newDob = currentDob.setFullYear(newYearOfBirth);

  return now.getTime() <= newDob;
}

function generateAgeHintText(
  gt: boolean,
  lowerAgeUnit: string,
  lowerAgeValue: number,
  lt: boolean,
  upperAgeUnit: string,
  upperAgeValue: number,
  reject: string,
  t: IFixedNamespaceTFunction<keyof typeof CommonI18n.HintError>
): string {
  if (gt && lt) {
    return '';
  }

  if (reject !== 'k' && reject !== 'm') {
    return '';
  }

  if (lowerAgeUnit === 'Jahr' && upperAgeUnit === 'Jahr') {
    if (upperAgeValue >= 124) {
      return lowerAgeValue === 1
        ? t('hintMaxAgeFrom124MinAge1', { age: lowerAgeValue })
        : t('hintMaxAgeFrom124WithoutMinAge1', { age: lowerAgeValue });
    }

    return t('hintMaxAgeTo124', {
      ageMin: lowerAgeValue,
      ageMax: upperAgeValue,
    });
  }

  if (lowerAgeUnit === 'Tag' && upperAgeUnit === 'Jahr') {
    if (lowerAgeValue >= 28 && upperAgeValue >= 124) {
      return t('hintMaxAgeFrom124MinAge28', { age: lowerAgeValue });
    }

    return t('hintAge', { age: upperAgeValue + 1 });
  }

  return '';
}

function getAgeHint(
  meta: Meta,
  pateintProfile: IPatientProfile,
  t: IFixedNamespaceTFunction<keyof typeof CommonI18n.HintError>
): string {
  if (!meta || !meta.age) {
    return '';
  }
  const { ageLow, ageHigh, reject } = meta.age;

  const now = datetimeUtil.date();
  const dob = pateintProfile.dateOfBirth;
  const lowerAgeUnit = ageLow?.unit;
  const lowerAgeValue = parseInt(ageLow?.value || '0');

  const gt =
    lowerAgeUnit === 'Tag'
      ? greaterThanLowerDayLimit(now, new Date(dob), lowerAgeValue)
      : lowerAgeUnit === 'Jahr'
        ? greaterThanLowerYearLimit(now, new Date(dob), lowerAgeValue)
        : true;

  const upperAgeUnit = ageHigh?.unit;
  const upperAgeValue = parseInt(ageHigh?.value || '0');

  const lt =
    upperAgeUnit === 'Tag'
      ? lessThanUpperDayLimit(now, new Date(dob), upperAgeValue)
      : upperAgeUnit === 'Jahr'
        ? lessThanUpperYearLimit(now, new Date(dob), upperAgeValue)
        : true;

  return generateAgeHintText(
    gt,
    lowerAgeUnit || '',
    lowerAgeValue,
    lt,
    upperAgeUnit || '',
    upperAgeValue,
    reject,
    t
  );
}

function getGenderHint(
  meta: Meta,
  patientProfile: IPatientProfile,
  t: IFixedNamespaceTFunction<keyof typeof CommonI18n.HintError>
): string {
  if (!meta || !meta.gender) {
    return '';
  }

  const { value, reject } = meta.gender;
  const gender = patientProfile.patientInfo.personalInfo.gender;

  if (value === 'm' && gender !== Gender.M) {
    return reject === 'm'
      ? t('hintGenderMRejectM')
      : t('hintGenderMRejectNotM');
  }

  if (value === 'w' && gender !== Gender.W) {
    return reject === 'm'
      ? t('hintGenderWRejectM')
      : t('hintGenderWRejectNotM');
  }

  return '';
}

function getPermanent(
  t: IFixedNamespaceTFunction<keyof typeof CommonI18n.HintError>
) {
  return t('hintPermanentDiagnosis');
}
