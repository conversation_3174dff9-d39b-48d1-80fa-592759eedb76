import React from 'react';
import { Flex, Box, BodyTextS } from '@tutum/design-system/components';
import { Classes } from '@tutum/design-system/components/Core';
import { getPositionsToHighlight } from '@tutum/infrastructure/utils/match';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IMenuItemRenderer } from '../popover-content-editable/PopoverContentEditable';
import { IDiagnoseSearchResult } from './DiagnoseBlock.type';

export default ({ item, query }: IMenuItemRenderer, theme: IMvzTheme) => {
  const createCode = (item: IDiagnoseSearchResult) => {
    return (
      <Box className={`${Classes.MENU_ITEM_LABEL} menuItemLeftValue`}>
        {item.displayCode && item.code}
      </Box>
    );
  };

  const createHints = (hints: string[]) => {
    const { background } = theme;
    return (
      <Box className={Classes.MENU_ITEM_LABEL}>
        {hints.map((hint, index) => {
          return (
            <BodyTextS key={index} color={background.warn.base}>
              {hint}
            </BodyTextS>
          );
        })}
      </Box>
    );
  };

  const createTitle = (query: string, item: IDiagnoseSearchResult) => {
    const styleTexts = getPositionsToHighlight(item.description!, query);
    if (styleTexts.length == 0) {
      return (
        <Box
          auto
          className={`${Classes.TEXT_OVERFLOW_ELLIPSIS} ${Classes.FILL}`}
        >
          {item.description}
        </Box>
      );
    }
    return (
      <Box auto className={`${Classes.TEXT_OVERFLOW_ELLIPSIS} ${Classes.FILL}`}>
        {styleTexts.map((itemSub, index) => {
          return itemSub.highlight ? (
            itemSub.value
          ) : (
            <strong key={index}>{itemSub.value}</strong>
          );
        })}
        {item.displayHints && createHints(item.hints!)}
      </Box>
    );
  };

  return (
    <Flex>
      {createCode(item)}
      {createTitle(query, item)}
    </Flex>
  );
};
