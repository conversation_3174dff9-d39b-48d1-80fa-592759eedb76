import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import {
  Certainty,
  Laterality,
} from '@tutum/hermes/bff/service_domains_patient_file';
import type { IMenuItem } from '@tutum/design-system/components';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

export const getLateralityList = (): Array<IMenuItem<Laterality>> => [
  {
    label: 'Links',
    value: Laterality.L,
  },
  {
    label: 'Rechts',
    value: Laterality.R,
  },
  {
    label: 'Beidseitig',
    value: Laterality.B,
  },
];

export const getCertaintyList = (
  t?: IFixedNamespaceTFunction<keyof typeof FormI18n.AddNewDiagnosisDialog>
): Array<IMenuItem<Certainty>> => {
  return [
    {
      label: t('gesichert'),
      value: Certainty.G,
    },
    {
      label: t('verdacht'),
      value: Certainty.V,
    },
    {
      label: t('zustandNach'),
      value: Certainty.Z,
    },
    {
      label: t('ausgeschlossen'),
      value: Certainty.A,
    },
  ];
};

export function findCertainty(
  value: Certainty
): IMenuItem<Certainty> | undefined {
  return getCertaintyList().find((c) => c.value === value);
}

export function findLaterality(
  value: Laterality
): IMenuItem<Laterality> | undefined {
  return getLateralityList().find((l) => l.value === value);
}
