import React, {
  useState,
  useContext,
  useEffect,
  useCallback,
  useMemo,
} from 'react';
import isEmpty from 'lodash/isEmpty';
import debounce from 'lodash/debounce';

import {
  BodyTextM,
  Flex,
  InfoConfirmDialog,
  Svg,
  Tooltip,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import Button from '@tutum/design-system/components/Button/Button';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import {
  Certainty,
  Laterality,
} from '@tutum/hermes/bff/service_domains_patient_file';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import doctorSpecialistGroups from '@tutum/mvz/public/data/icd/doctor-specialist-groups.json';
import type { IDiagnoseSearchResult } from './DiagnoseBlock.type';
import ScheinBlock from '@tutum/mvz/components/schein-block';
import { parseToScheinMainGroup } from '../Composer.util';
import DiagnoseBlockService from './DiagnoseBlock.service';
import SdvaDetailsDialog from '@tutum/mvz/components/sdva-dialog';
import DiagnosisNodeComponent from '@tutum/design-system/composer/diagnosis-node-component';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { EncounterDiagnoseTimeline } from '@tutum/hermes/bff/repo_encounter';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import {
  removeDiagnoseCodeFromText,
  renderDiagnoseLabel,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/diagnose-entry/helpers';
import { groupQuarterbySchein } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import { IcdSearchCatalog } from '@tutum/infrastructure/masterdata-service/type';
import { MainGroup } from '@tutum/hermes/bff/common';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';
import { getMainGroupNoSchein } from '@tutum/mvz/_utils/patientType';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export interface IDiagnoseBlockProps {
  initEncounterId: string;
  disabled?: boolean;
  data?: EncounterDiagnoseTimeline;
  scheinsFilteredByDate: ScheinItem[];
  actionBarValues: IActionBarStore;
  handleSetCatalog: (value: string) => void;
  openCreateSchein: (scheinMainGroup?: MainGroup) => void;
  onChange(data: EncounterDiagnoseTimeline): Promise<void>;
  onSubmit: () => Promise<void>;
  onClear: () => void;
  command?: string;
}

const DiagnoseBlock: React.FC<IDiagnoseBlockProps> = (props) => {
  const {
    initEncounterId,
    disabled,
    data,
    scheinsFilteredByDate,
    actionBarValues,
    handleSetCatalog,
    onChange,
    onSubmit,
    onClear,
    openCreateSchein,
    command,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonI18n.HintError>(
    {
      namespace: 'Common',
      nestedTrans: 'HintError',
    }
  );

  const { t: tConfirmDialog } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein.ConfirmDialog',
  });

  const {
    patientManagement: { patient },
  } = useContext(PatientManagementContext.instance);

  const patientFileStore = usePatientFileStore();
  const setting = useSettingStore();

  const [isSdvaDetailsOpen, setIsSdvaDetailsOpen] = useState(false);

  const [selectedSchein, setSelectedSchein] = useState<ScheinItem | undefined>(
    undefined
  );

  const [isOpenConfirmScheinDialog, setOpenConfirmScheinDialog] =
    useState<boolean>(false);

  const [isOpenScheinModal, setIsOpenScheinModal] = useState<boolean>(false);

  const [key, setKey] = useState<string>(actionBarValues.catalog);

  const mainGroupNoSchein = useMemo(() => {
    return getMainGroupNoSchein(patient?.patientInfo.genericInfo.patientType);
  }, [patient]);

  useEffect(() => {
    if (
      actionBarValues.catalog &&
      actionBarValues.catalog !== key &&
      data?.freeText &&
      data?.freeText !== 'undefined'
    ) {
      setKey(actionBarValues.catalog);
    }
  }, [actionBarValues.catalog, data?.freeText]);

  const handleSearchDiagnose = async (
    keyWord: string,
    actionBarValuesParams: IActionBarStore,
    cb?: (result: IDiagnoseSearchResult[]) => void
  ) => {
    const result = await DiagnoseBlockService.searchDiagnose(
      patient!,
      actionBarValues?.encounterDate || datetimeUtil.now(),
      keyWord,
      actionBarValuesParams || actionBarValues,
      tCommon,
      command
    );
    cb?.(result);
  };

  const onICDChange = async (diagnoseItem: EncounterDiagnoseTimeline) => {
    const updatedDiagnose: EncounterDiagnoseTimeline = {
      ...data!,
      code: diagnoseItem.code,
      description: diagnoseItem.description,
      freeText: `${diagnoseItem.code ? `(${diagnoseItem.code}) ` : ''}${
        diagnoseItem.description
      }`,
      group: diagnoseItem.group,
      scheins: data?.scheins?.length
        ? data.scheins
        : parseToScheinMainGroup([patientFileStore.schein.activatedSchein]),
      certainty:
        DiagnoseBlockService.getDefaultCertainty(diagnoseItem.code) ??
        data?.certainty,
      sdvaRefs: diagnoseItem.sdvaRefs,
    };
    await onChange(updatedDiagnose);
  };

  const onCertaintyChange = async (_data: Certainty) => {
    if (data?.certainty === _data) {
      return;
    }

    await onChange({
      ...data!,
      certainty: _data,
    });
  };

  const onLateralityChange = async (_data: Laterality) => {
    if (data?.laterality === _data) {
      return;
    }

    await onChange({
      ...data!,
      laterality: _data,
    });
  };

  const onCloseSdvaDialog = () => setIsSdvaDetailsOpen(!isSdvaDetailsOpen);

  const onSdvaInfoClick = () => {
    setIsSdvaDetailsOpen(true);
  };

  const handleClickNoResults = () => {
    handleSetCatalog(IcdSearchCatalog.SystematicAndAlphabetical);
  };

  const NoResults = () => {
    const isSystematicAndAlphabet =
      actionBarValues?.catalog === IcdSearchCatalog.SystematicAndAlphabetical;

    const isGeneralPractitioner =
      actionBarValues?.catalog === IcdSearchCatalog.GeneralPractitioner;
    const isSpecialistGroup =
      actionBarValues?.catalog === IcdSearchCatalog.SpecialistGroup;
    const isSystematic =
      actionBarValues?.catalog === IcdSearchCatalog.Systematic;

    let catalogDescribe = t('generalPractitionerText2');
    if (isSpecialistGroup) {
      catalogDescribe = t('specialistGroupText3');
    } else if (isSystematic) {
      catalogDescribe = t('systematicText3');
    }

    const isShowNoResult =
      isGeneralPractitioner || isSpecialistGroup || isSystematic;

    if (!actionBarValues?.catalog || isSystematicAndAlphabet) {
      return <>{t('noResultsFound')}</>;
    }

    if (!isShowNoResult) return null;

    return (
      <Flex className="sl-no-results" align="flex-start" column>
        <span>
          {isGeneralPractitioner ? (
            <BodyTextM>{t('generalPractitionerText1')}</BodyTextM>
          ) : isSystematic ? (
            <BodyTextM>{t('systematicText2')}</BodyTextM>
          ) : (
            `${t('specialistGroupNoResult', {
              specialListGroup:
                doctorSpecialistGroups.find(
                  (item) => item.key === actionBarValues?.doctorSpecialist
                )?.value || '',
            })}`
          )}
        </span>
        <span className="sl-action-link">
          <Button
            style={{ paddingLeft: 0 }}
            minimal
            intent="primary"
            onClick={handleClickNoResults}
          >
            <strong>{catalogDescribe}</strong>
          </Button>
        </span>
      </Flex>
    );
  };

  const onSetScheinIds = useCallback(
    (scheinItems: ScheinItem[]) => {
      const items = parseToScheinMainGroup(scheinItems);
      setSelectedSchein(scheinItems[0]);
      onChange({
        ...data!,
        scheins: items,
      });
    },
    [onChange, data, scheinsFilteredByDate]
  );

  const isDisplayInfoIcon =
    data?.freeText &&
    data?.freeText.length > 2 &&
    data?.sdvaRefs != undefined &&
    data?.sdvaRefs.length > 0;

  const isShowScheinBlock = Boolean(data?.code);

  const icdDefaultValue = data?.description
    ? {
        id: '',
        label: `${data?.code != '' ? `(${data?.code}) ` : ''}${
          data?.description
        }`,
        value: data?.code,
        data: data,
      }
    : undefined;

  const createListInsuranceMapBySchein = () => {
    const listInsurance =
      patientFileStore.patient.current?.patientInfo.insuranceInfos || [];

    const listScheinId = scheinsFilteredByDate.map(
      (schein) => schein.insuranceId
    );
    const res = listInsurance.filter((insur) =>
      listScheinId.includes(insur.id)
    );
    const groupQuarterBySchein = groupQuarterbySchein(
      scheinsFilteredByDate,
      res
    );
    return groupQuarterBySchein;
  };

  useEffect(() => {
    const defaultSelectedScheinId = initEncounterId
      ? data?.scheins?.[0]?.scheinId
      : patientFileStore?.schein?.activatedSchein?.scheinId;
    const scheinValid =
      scheinsFilteredByDate.find(
        (schein) => schein.scheinId === defaultSelectedScheinId
      ) ||
      (scheinsFilteredByDate?.length === 1 && !!data?.scheins?.[0]?.scheinId
        ? scheinsFilteredByDate[0]
        : ({} as ScheinItem));
    setSelectedSchein(scheinValid);
    onChange({
      ...data!,
      scheins: scheinValid ? parseToScheinMainGroup([scheinValid]) : [],
    });
  }, [
    initEncounterId,
    JSON.stringify(scheinsFilteredByDate),
    patientFileStore?.schein?.activatedSchein,
  ]);

  return (
    <Flex align="flex-start" auto>
      {isDisplayInfoIcon && (
        <Flex gap={4} pt={2}>
          <Tooltip position="top" content={t('encryptionTooltip')}>
            <Svg
              className="info-icon"
              src="/images/icon-info.svg"
              onClick={onSdvaInfoClick}
              style={{ cursor: 'pointer' }}
            />
          </Tooltip>
          <SdvaDetailsDialog
            isOpen={isSdvaDetailsOpen}
            onClose={onCloseSdvaDialog}
            icdCode={data?.code}
            sdvaRefs={data?.sdvaRefs}
            encounterDate={actionBarValues?.encounterDate || datetimeUtil.now()}
            t={t}
          />
        </Flex>
      )}
      {!isDisplayInfoIcon && <div className="padder"></div>}
      <Flex auto>
        <DiagnosisNodeComponent
          autoFocus
          onClear={onClear}
          onSubmit={async () => {
            if (!scheinsFilteredByDate[0] || isEmpty(selectedSchein)) {
              setOpenConfirmScheinDialog(true);
              return;
            }
            onSubmit();
          }}
          disabled={disabled}
          placeholder={t('typeDiagnose')}
          defaultIcdValue={icdDefaultValue}
          formatIcdLabel={(opt) => renderDiagnoseLabel(opt.data)}
          onIcdCodeSelect={(item) =>
            onICDChange(item?.data as EncounterDiagnoseTimeline)
          }
          onSearchIcdList={debounce(async (query: string, setICDOptions) => {
            await onICDChange({
              ...data!,
              description: removeDiagnoseCodeFromText(query, data?.code!),
            });
            await handleSearchDiagnose(query, actionBarValues, (results) =>
              setICDOptions(
                results.map((rs) => ({
                  label: renderDiagnoseLabel(rs),
                  value: rs.code,
                  data: rs,
                }))
              )
            );
          }, 100)}
          certaintyLabel={t('certainty')}
          onCertaintyChange={(item: Certainty) => onCertaintyChange(item)}
          defaultCertaintyValue={data?.certainty}
          lateralityLabel={t('laterality')}
          onLateralityChange={(item: Laterality) => onLateralityChange(item)}
          onFocusOpenSchein={() => setIsOpenScheinModal(true)}
          defaultLateralityValue={data?.laterality}
          renderNoResults={<NoResults />}
          freeText={data?.freeText || ''}
          scaleNumber={setting?.timelineSetting?.scaleNumber}
          actionBarValues={actionBarValues}
        />
      </Flex>
      {isShowScheinBlock && (
        <ScheinBlock
          isOpenScheinModal={isOpenScheinModal}
          selectedSchein={selectedSchein}
          onSetScheinIds={onSetScheinIds}
          openCreateSchein={() => openCreateSchein(mainGroupNoSchein)}
          scheinFilter={createListInsuranceMapBySchein()}
        />
      )}
      <InfoConfirmDialog
        type="primary"
        isOpen={isOpenConfirmScheinDialog}
        title={tConfirmDialog('title')}
        cancelText={tConfirmDialog('btnNo')}
        confirmText={tConfirmDialog('btnYes')}
        isShowIconTitle={false}
        onConfirm={() => {
          setOpenConfirmScheinDialog(false);
          openCreateSchein(mainGroupNoSchein);
        }}
        onClose={(closeAndSubmit) => {
          setOpenConfirmScheinDialog(false);
          if (closeAndSubmit) {
            onSubmit();
          }
        }}
      >
        <Flex column>{tConfirmDialog('description')}</Flex>
      </InfoConfirmDialog>
    </Flex>
  );
};

export default React.memo(DiagnoseBlock);
