import {
  generateRandomStr,
  isNotEmpty,
} from '@tutum/design-system/infrastructure/utils';
import {
  Certainty,
  Laterality,
} from '@tutum/hermes/bff/service_domains_patient_file';
import { IPatientProfile } from '../../../../types/profile.type';
import { IComposerRow } from '../Composer.type';
import HintService from './hint.service';
import { IDiagnoseBlock, IDiagnoseSearchResult } from './DiagnoseBlock.type';
import { ScheinWithMainGroup } from '@tutum/hermes/bff/common';
import { extendedIcdRules } from './icdRules.extended';
import { IActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { searchDiagnosis } from '@tutum/infrastructure/masterdata-service';

function searchDiagnose(
  selectedPatient: IPatientProfile,
  encounterDate: number,
  query: string,
  actionBarValues: IActionBarStore,
  t?: any,
  command?: string
): Promise<IDiagnoseSearchResult[]> {
  if (!query) {
    return Promise.resolve([]);
  }

  return new Promise<IDiagnoseSearchResult[]>((resolve, reject) => {
    searchDiagnosis({
      query,
      selectedDate: encounterDate || datetimeUtil.now(),
      catalog: actionBarValues.catalog,
      doctorSpecialistType: actionBarValues.doctorSpecialist,
    })
      .then((data) => {
        const groupCodes = new Map<string, IDiagnoseSearchResult[]>();
        data.forEach((item) => {
          const value = item.code;
          const title = item.description;
          const group = item.group;
          let displayCode = true;
          let displayHints = true;
          const hints = HintService.getHints(item, selectedPatient, t, command!);

          if (groupCodes.has(value)) {
            displayCode = false;
            displayHints = false;
          } else {
            groupCodes.set(value, new Array<IDiagnoseSearchResult>());
          }

          const result: IDiagnoseSearchResult = {
            description: title,
            code: value,
            sdvaRefs: item.sdvaRefs,
            group: group,
            displayCode: displayCode,
            displayHints: displayHints,
            hints: hints,
          };

          groupCodes?.get(value)?.push(result);
        });

        let results = new Array<IDiagnoseSearchResult>();

        Array.from(groupCodes.values()).forEach((groupCode) => {
          results = results.concat(groupCode);
        });

        resolve(results);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

async function parseCombineDiagnoses(
  row: Partial<IComposerRow> | null = {},
  encounterDate: number | undefined,
  data: string[],
  scheinList: ScheinWithMainGroup[],
  actionBarValues?: IActionBarStore
): Promise<IComposerRow[]> {
  const extractedDiagnoseInputs = data
    .map((code) => code.trim())
    .filter((code) => isNotEmpty(code))
    .map((code) => {
      const pttern =
        /^([A-Z][0-9]{2}\.[0-9]{1,2})\s{0,}([GVZA]\s{0,}[ULRB]?)?$/gi;

      const defaultParsedResult = {
        input: code,
      } as RegExpExecArray;

      return pttern.exec(code) || defaultParsedResult;
    });
  const extractedIcdCodes = extractedDiagnoseInputs.map((extractedInput) => {
    return extractedInput[1] || extractedInput.input;
  });

  const diagnoseList = await searchDiagnosis({
    query: extractedIcdCodes.join('-'),
    selectedDate: encounterDate || datetimeUtil.date().getTime(),
    catalog: actionBarValues?.catalog,
    doctorSpecialistType: actionBarValues?.doctorSpecialist,
  });

  return Promise.resolve(
    extractedDiagnoseInputs.map((extractedInput, index) => {
      const codeInput = extractedInput[1] || extractedInput.input;
      const metaInput = extractedInput[2];

      const certainty = metaInput && metaInput.replace(/\s/gi, '').charAt(0);
      const laterality = metaInput && metaInput.replace(/\s/gi, '').charAt(1);

      const diagnose: IDiagnoseBlock = {
        certainty: undefined,
        laterality: undefined,
        code: undefined,
        description: undefined,
        errors: undefined,
        group: undefined,
        scheins: scheinList ? scheinList : [],
      };

      if (certainty) {
        diagnose.certainty = certainty as Certainty;
      }

      if (laterality) {
        diagnose.laterality = laterality as Laterality;
      }
      const foundDiagnose = diagnoseList.find((item) => {
        return (
          item.code.replace('-', '').toUpperCase() === codeInput.toUpperCase()
        );
      });
      let inputData = '';
      let key = '';
      if (foundDiagnose) {
        diagnose.code = foundDiagnose.code;
        diagnose.description = foundDiagnose.description;
        diagnose.group = foundDiagnose.group;
        inputData = `(${foundDiagnose.code}) ${foundDiagnose.description}`;
        key = generateRandomStr();
      } else {
        inputData = codeInput;
        key = row?.code!;
      }

      const composerRow = {
        ...row,
        id: index === 0 ? row?.id : undefined,
        key,
        freeText: inputData,
        meta: {
          diagnose,
        },
      };

      return composerRow as IComposerRow;
    })
  );
}

function getDefaultCertainty(itemCode: string): Certainty {
  const rule = extendedIcdRules.find((r) => r.code === itemCode);
  if (rule !== undefined && rule.defaultCertainty !== null) {
    return rule.defaultCertainty!;
  }
  return null!;
}

export default {
  searchDiagnose,
  parseCombineDiagnoses,
  getDefaultCertainty,
};
