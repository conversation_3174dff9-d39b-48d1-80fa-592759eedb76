import { isNil, debounce, isEmpty } from 'lodash';
import Router from 'next/router';
import React, {
  useState,
  useEffect,
  useMemo,
  useContext,
  useCallback,
} from 'react';

import {
  BodyTextL,
  BodyTextM,
  Button,
  Flex,
  InfoConfirmDialog,
  Link,
  TOASTER_TIMEOUT_CUSTOM,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { IMenuItemWithData } from '@tutum/design-system/components';
import { Classes, Dialog } from '@tutum/design-system/components/Core';
import ADDITIONAL_INFO_FULL_LIST from '@tutum/design-system/composer/assets/additional-info.json';
import ServiceNodeComponent from '@tutum/design-system/composer/service-node-component';
import { AdditionalInfoAutoTransformListNodePlugin } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-auto-transform-list-node-plugin';
import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import AdditionalInfoOmimGChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-omimg-chain-plugins';
import AdditionalInfoHgncChainPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-hgnc-chain-plugins';
import {
  ASV_KEY,
  // PSEUDO_GNR,
} from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  OmimGChain,
  searchOmimGChain,
} from '@tutum/hermes/bff/legacy/app_catalog_omimg_chain';
import { getBlankServices } from '@tutum/hermes/bff/legacy/app_mvz_blank_service';
import {
  saveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import { UnitStatistiks } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import { ScheinWithMainGroup } from '@tutum/hermes/bff/legacy/common';
import { PatientParticipation } from '@tutum/hermes/bff/legacy/service_domains_patient_participation';
import type { EncounterServiceTimeline } from '@tutum/hermes/bff/repo_encounter';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import I18n from '@tutum/infrastructure/i18n';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import MaterialCostService from '@tutum/infrastructure/web-worker-services/material-cost.service';
import {
  checkIsKvSchein,
  checkIsSvSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import ScheinBlock from '@tutum/mvz/components/schein-block';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import useToaster from '@tutum/mvz/hooks/useToaster';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { CreateExternalAddressDialog } from '@tutum/mvz/module_external-address/external-address-overview/CreateExternalAddressDialog';
import HospitalsPracticesDialog from '@tutum/mvz/module_kv_hzv_schein/hospitals-practices-dialog';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { groupQuarterbySchein } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  IActionBarStore,
  useActionBarStore,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import OmimGChainOverview from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/OmimGChainOverView';
import OmimGCreateDialog from '@tutum/mvz/module_patient-management/patient-file/omim-g-chain/create-dialog/OmimGCreateDialog.styled';
import type { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import type { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import CreateEbmDialog from '@tutum/mvz/module_sdebm/create-ebm-dialog/CreateEbmDialog.styled';
import CreateMaterialCostDialog from '@tutum/mvz/module_sdebm/material-cost-dialog/MaterialCostDialog.styled';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '../Composer.const';
import { parseToScheinMainGroup } from '../Composer.util';
import CreateJusitficationModal from '../goa-service-block/justification-modal';
import AdditionalInfoAutoFillDefaultDataPlugin from '../lexical/plugins/additional-info-auto-fill-default-data-plugin';
import ServiceBlockService from './services/service-block.service';
import ServiceMetaService from './services/service-meta.service';
import { NodeKey } from 'lexical';
import { searchHgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import { HgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import HgncCreateDialog from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/create-dialog/HgncCreateDialog.styled';
import HgncChainOverview from '@tutum/mvz/module_patient-management/patient-file/hgnc-chain/HgncChainOverView';

export interface IServiceBlockProps {
  className?: string;
  initEncounterId?: string;
  encounterDate?: number;
  data: EncounterServiceTimeline;
  doctorProfile?: IEmployeeProfile;
  defaultContract?: IContractInfo;
  patient?: IPatientProfile;
  disabled?: boolean;
  currentSchein?: ScheinItem;
  onChange: (data: EncounterServiceTimeline) => Promise<void>;
  scheinsFilteredByDate: ScheinItem[];
  onSubmit(payload: EncounterServiceTimeline): Promise<void>;
  onClear: () => void;
  openCreateSchein: (scheinMaingRoup?: MainGroup) => void;
  handleSetActionBarValues: (key: keyof IActionBarStore, value: any) => void;
}

interface IConfirmOpenCreateDialogProps {
  isOpen: boolean;
  onClose(): void;
  onConfirmClick(): void;
}

const ConfirmOpenCreateDialog: React.FC<IConfirmOpenCreateDialogProps> = (
  props
) => {
  const { t: tSdebm } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'MaterialCostDialog',
  });

  return (
    <Dialog
      isOpen={props.isOpen}
      onClose={props.onClose}
      style={{ borderRadius: 4 }}
      canOutsideClickClose={false}
    >
      <Flex className={Classes.DIALOG_BODY} column gap={24}>
        <BodyTextL fontWeight={'Bold'} fontSize={20}>
          {tSdebm('createMaterialCostTitle')}
        </BodyTextL>
        {tSdebm('leaveCreateMaterialCostContent')}
        <Flex gap={16}>
          <Button
            onClick={props.onClose}
            minimal
            outlined
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('cancelCreate')}
          </Button>
          <Button
            fill
            onClick={props.onConfirmClick}
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('confirmCreateMaterialCost')}
          </Button>
        </Flex>
      </Flex>
    </Dialog>
  );
};

const ASV_ADD_INFO = ADDITIONAL_INFO_FULL_LIST.find(
  (addInfo) => addInfo.fK === ASV_KEY
);

const ServiceBlock: React.FC<IServiceBlockProps> = (props) => {
  const {
    initEncounterId,
    data,
    patient,
    onSubmit,
    disabled,
    onChange,
    className,
    encounterDate,
    doctorProfile,
    scheinsFilteredByDate,
    currentSchein,
    onClear,
    openCreateSchein,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });
  const encounterDateObj = new Date(encounterDate || datetimeUtil.now())
  const hgncAvailableDate = new Date("2025-07-01T00:00:00")

  const { t: tConfirmDialog } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein.ConfirmDialog',
  });

  const { t: translator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const actionBarStore = useActionBarStore();
  const globalContext = useContext(GlobalContext.instance);
  const documentedDoctor = globalContext.getDoctorById(
    actionBarStore?.doctorId!
  );

  const { useGetDoctorList } = useContext(GlobalContext.instance);
  const doctorLists = useGetDoctorList();

  const toast = useToaster();
  const patientFileStore = usePatientFileStore();
  const {
    patientManagement: { selectedContractDoctor },
    patientManagement,
  } = useContext(PatientManagementContext.instance);

  const [isOpenEbmDialog, setIsOpenEbmDialog] = useState<boolean>(false);
  const [codeNotExist, setCodeNotExist] = useState('');
  const [filteredAdditionalInfos, setFilteredAdditionalInfos] = useState<
    AdditionalInfoField[]
  >([]);
  const [isOpenCreateMaterialCost, setIsOpenCreateMaterialCostDialog] =
    useState(false);
  const [createOmimGChainDialogState, setCreateOmimGChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: OmimGChain;
    }>({
      isOpen: false,
      defaultValue: null!,
    });
  const [createHgncChainDialogState, setCreateHgncChainDialogState] =
    useState<{
      isOpen: boolean;
      defaultValue: HgncChain;
    }>({
      isOpen: false,
      defaultValue: null!,
    });
  const [listChainDialogState, setListChainDialogState] = useState<{
    isOpen: boolean;
    query: string;
    additionalInfoNodeKey: NodeKey;
  }>({
    isOpen: false,
    query: '',
    additionalInfoNodeKey: null!,
  });
  const [isOpenConfirmDialog, setIsOpenConfirmDialog] = useState(false);
  const [isOpenHospitalsPractices, setIsOpenHospitalsPractices] =
    useState(false);
  const [isOpenCreateDoctor, setIsOpenCreateDoctor] = useState(false);
  const [isOpenCreateJustification, setIsOpenCreateJustification] =
    useState(false);

  const { data: listSettingJustification, refetch } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });

  const patientParticipations: PatientParticipation[] =
    patientManagement?.getPatientParticipationResponse?.participations ?? [];

  const setUpdatedService = (updatedService: EncounterServiceTimeline) => {
    const _service = { ...updatedService };
    onChange(_service);
    return _service;
  };

  const [selectedSchein, setSelectedSchein] = useState<ScheinItem | undefined>(
    undefined
  );

  const [isOpenConfirmScheinDialog, setOpenConfirmScheinDialog] =
    useState<boolean>(false);
  const [currentPayload, setCurrentPayload] =
    useState<EncounterServiceTimeline | null>(null);

  const memoizedDoctorASVNumbers = useMemo(
    () =>
      doctorLists.reduce((asvNumbers, doctor) => {
        if (doctor.teamNumbers?.length) {
          return asvNumbers.concat(doctor.teamNumbers);
        }
        return asvNumbers;
      }, [] as string[]),
    [doctorLists]
  );
  const currentContract = useMemo(() => {
    if (!currentSchein) return null;
    let scheinKey = '';
    if (checkIsSvSchein(currentSchein)) {
      scheinKey = currentSchein.hzvContractId!;
    } else if (currentSchein.kvTreatmentCase) {
      scheinKey = currentSchein.kvTreatmentCase;
    }

    catalogOverviewActions
      .getAdditionalInfos(
        currentSchein.scheinMainGroup,
        scheinKey,
        encounterDate
      )
      .then((fields) => {
        const infors = fields.map((info) => {
          info.label = translator(info.fK);
          return info;
        });
        if (
          memoizedDoctorASVNumbers?.length &&
          checkIsKvSchein(currentSchein)
        ) {
          infors.push({
            ...ASV_ADD_INFO,
            label: translator(ASV_ADD_INFO?.fK!),
          } as AdditionalInfoField);
        }
        setFilteredAdditionalInfos(infors);
      });
    const newContract = patientFileActions.getAvailableContractById(
      currentSchein.hzvContractId
    );
    return newContract;
  }, [currentSchein, memoizedDoctorASVNumbers, encounterDate]);

  const onServiceSelect = async (item: IContractData) => {
    const resultAfterValidate = await ServiceMetaService.getServiceMeta(
      currentContract!,
      item
    );

    resultAfterValidate.scheins = parseToScheinMainGroup([currentSchein]);

    resultAfterValidate.serviceMainGroup = item.mainGroup;
    resultAfterValidate.chargeSystemId = item.chargeSystemId;
    return resultAfterValidate;
  };

  const onSetScheinIds = (scheinItems: ScheinItem[] = []) => {
    const payload: EncounterServiceTimeline = {
      ...data,
      scheins: parseToScheinMainGroup(scheinItems),
    };
    setSelectedSchein(scheinItems[0]);
    setUpdatedService(payload);
  };

  const searchServiceData = useMemo(
    () =>
      debounce(async (query: string, cb: (result: IContractData[]) => void) => {
        const contractBySchein = (
          currentSchein || patientFileStore.schein.activatedSchein
        )?.hzvContractId;
        setCodeNotExist(query);
        const pariticipation = patientParticipations?.find(
          (p) => p.contractId == contractBySchein
        );
        let currentContract: IContractInfo | null = null;
        if (pariticipation) {
          currentContract = {
            id: contractBySchein!,
            chargeSystemId: pariticipation.chargeSystemId,
            role: pariticipation.doctorFunctionType,
            type: pariticipation.contractType,
            status: pariticipation.status,
            doctorId: pariticipation.doctorId,
          };
        }
        const result = await ServiceBlockService.searchService(
          encounterDate!,
          query,
          doctorProfile,
          [],
          currentContract,
          selectedContractDoctor
        );

        if (currentContract) {
          const blankServicesResp = await getBlankServices({
            contractId: currentContract.id,
          });

          const overridedActiveBlankServices = result.map((s) => {
            const blankService = blankServicesResp.data.blankServices.find(
              (blank) => blank.code === s.code
            );
            if (isNil(blankService)) {
              return { ...s };
            }

            return {
              ...s,
              isActive: true,
              description: blankService.description,
              evaluation: blankService.price,
              unit: UnitStatistiks.UnitStatistiks_Euros,
            };
          });
          const excludingInactiveBlankService =
            overridedActiveBlankServices.filter(
              (s) => !s.isBlankService || s.isActive
            );
          cb(excludingInactiveBlankService.slice(0, 20));
        }

        cb(result.slice(0, 20));
      }, 500),
    [
      patient,
      doctorProfile,
      encounterDate,
      selectedContractDoctor,
      patientFileStore.schein.activatedSchein?.scheinId,
      patientParticipations,
      currentSchein,
    ]
  );

  const handleClickNoResults = () => {
    setIsOpenEbmDialog(true);
  };

  const hasFavSchein = useMemo(() => {
    return patientFileStore.schein.originalList?.some((schein) =>
      checkIsSvSchein(schein)
    );
  }, [patientFileStore.schein.originalList]);

  const isShowSuggestChangeToFavSchein = useMemo(() => {
    return hasFavSchein && !currentSchein?.hzvContractId;
  }, [hasFavSchein, currentSchein]);

  const NoResults = () => {
    if (props.defaultContract) {
      return (
        <Flex className="sl-no-results" p="8px 16px" justify="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
            {t('noResultsFound')}
          </BodyTextM>
        </Flex>
      );
    }
    return (
      <>
        <Flex className="sl-no-results" p="8px 16px" justify="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
            {t('noResultsFound') + '.'}
          </BodyTextM>
          <Link onClick={handleClickNoResults}>{t('CreateServiceCode')}</Link>
        </Flex>
        {isShowSuggestChangeToFavSchein && (
          <BodyTextM>{t('changeToFavSchein')}</BodyTextM>
        )}
      </>
    );
  };

  const onSaveSuccessEbm = () => {
    alertSuccessfully(t('createSuccessEbm'), {
      timeout: TOASTER_TIMEOUT_CUSTOM,
      toaster: toast,
    });
    setIsOpenEbmDialog(false);
  };

  const memoizedEncounterDate = useMemo(() => encounterDate, [encounterDate]);

  const filterScheinFunc = useCallback(
    (_option: ScheinItem) =>
      currentSchein?.scheinMainGroup === _option?.scheinMainGroup,
    [currentSchein]
  );

  const autoFillInfos = useMemo(() => {
    // service codes of KV patient
    if (['03008', '04008'].includes(data?.code)) {
      return [
        {
          fK: '5003',
          value: doctorProfile?.bsnr!,
        },
      ];
    }
    return [];
  }, [data?.code, doctorProfile]);

  const onCloseHospitalsPractices = useCallback(() => {
    setIsOpenHospitalsPractices(false);
  }, []);

  const createListInsuranceMapBySchein = useMemo(() => {
    const listInsurance =
      patientFileStore.patient.current?.patientInfo.insuranceInfos || [];

    const listScheinId = scheinsFilteredByDate.map(
      (schein) => schein.insuranceId
    );

    const res = listInsurance.filter((insur) =>
      listScheinId.includes(insur.id)
    );
    const groupQuarterBySchein = groupQuarterbySchein(
      scheinsFilteredByDate,
      res
    );

    return groupQuarterBySchein;
  }, [scheinsFilteredByDate]);

  useEffect(() => {
    const defaultSelectedScheinId = initEncounterId
      ? data.scheins?.[0]?.scheinId
      : patientFileStore?.schein?.activatedSchein?.scheinId;
    const scheinValid =
      scheinsFilteredByDate.find(
        (schein) => schein.scheinId === defaultSelectedScheinId
      ) ||
      (scheinsFilteredByDate?.length === 1 && !!data.scheins?.[0]?.scheinId
        ? scheinsFilteredByDate[0]
        : ({} as ScheinItem));
    setSelectedSchein(scheinValid);
    onChange({
      ...data,
      scheins:
        typeof scheinValid != 'undefined'
          ? [
            {
              scheinId: scheinValid.scheinId,
              group: scheinValid.scheinMainGroup,
            },
          ]
          : [],
    });
  }, [
    initEncounterId,
    JSON.stringify(scheinsFilteredByDate),
    patientFileStore?.schein?.activatedSchein,
  ]);
  return (
    <Flex className={className} column gap={0}>
      <ServiceNodeComponent
        id="service_node_composer"
        className="sl-ServiceNodeComponent"
        placeholder={t('typeService')}
        disabled={disabled}
        data={data}
        onChange={onChange}
        onSubmit={async (payload) => {
          if (!scheinsFilteredByDate[0] || isEmpty(selectedSchein)) {
            setCurrentPayload(payload);
            setOpenConfirmScheinDialog(true);
            return;
          }

          if (isEmpty(payload.scheins)) {
            const defaultSchein = scheinsFilteredByDate.map(
              (schein): ScheinWithMainGroup => ({
                scheinId: schein.scheinId,
                group: schein.scheinMainGroup,
              })
            )[0];
            payload.scheins = [defaultSchein];
          }

          onSubmit(payload);
        }}
        onClear={onClear}
        additionalFields={filteredAdditionalInfos}
        searchService={searchServiceData}
        onServiceSelect={onServiceSelect}
        // OPTIONALS PROPS
        encounterDate={memoizedEncounterDate}
        noResultsComponent={<NoResults />}
        rightElement={
          !!data?.freeText && !!data?.code ? (
            <ScheinBlock
              selectedSchein={selectedSchein}
              openCreateSchein={() => openCreateSchein()}
              onSetScheinIds={onSetScheinIds}
              scheinFilter={createListInsuranceMapBySchein}
              filterOption={filterScheinFunc}
            />
          ) : undefined
        }
        pointValue={patientFileStore.pointValue}
      >
        {/* LEXICAL PLUGINS AS CHILDREN */}
        <AdditionalInfoAutoFillDefaultDataPlugin
          schein={currentSchein}
          autoFillInfos={autoFillInfos}
        />
        {encounterDateObj < hgncAvailableDate ? <>
          <AdditionalInfoOmimGChainPlugin
            setAdditionalInfoNodeKey={(nodeKey) => {
              setListChainDialogState((prev) => ({
                ...prev,
                additionalInfoNodeKey: nodeKey,
              }));
            }}
            onSearch={async (query) => {
              const response = await searchOmimGChain({
                name: query,
                pagination: {
                  pageSize: 20,
                  page: 1,
                  sortBy: null!,
                  order: null!,
                },
              });
              return response.data.chains;
            }}
            onCreate={() => {
              setCreateOmimGChainDialogState({
                isOpen: true,
                defaultValue: null!,
              });
            }}
            onViewAll={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                isOpen: true,
                query: '',
              }));
            }}
          />
          <OmimGChainOverview
            isOpen={listChainDialogState.isOpen}
            query={listChainDialogState.query}
            additionalInfoNodeKey={
              listChainDialogState.additionalInfoNodeKey
            }
            setQuery={(newQuery) => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: newQuery,
              }));
            }}
            onEdit={(item) => {
              setCreateOmimGChainDialogState({
                isOpen: true,
                defaultValue: item,
              });
            }}
            onClose={() => {
              setListChainDialogState({
                isOpen: false,
                query: '',
                additionalInfoNodeKey: null!,
              });
            }}
            onCreate={() => {
              setCreateOmimGChainDialogState({
                isOpen: true,
                defaultValue: null!,
              });
            }}
          />
        </> : <>
          <AdditionalInfoHgncChainPlugin
            setAdditionalInfoNodeKey={(nodeKey) => {
              setListChainDialogState((prev) => ({
                ...prev,
                additionalInfoNodeKey: nodeKey,
              }));
            }}
            onSearch={async (query) => {
              const response = await searchHgncChain({
                name: query,
                paginationRequest: {
                  pageSize: 20,
                  page: 1,
                  sortBy: null!,
                  order: null!,
                },
              });
              return response.data.hgncChains;
            }}
            onCreate={() => {
              setCreateHgncChainDialogState({
                isOpen: true,
                defaultValue: null!,
              });
            }}
            onViewAll={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                isOpen: true,
                query: '',
              }));
            }}
          />
          <HgncChainOverview
            isOpen={listChainDialogState.isOpen}
            query={listChainDialogState.query}
            additionalInfoNodeKey={
              listChainDialogState.additionalInfoNodeKey
            }
            setQuery={(newQuery) => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: newQuery,
              }));
            }}
            onEdit={(item) => {
              setCreateHgncChainDialogState({
                isOpen: true,
                defaultValue: item,
              });
            }}
            onClose={() => {
              setListChainDialogState({
                isOpen: false,
                query: '',
                additionalInfoNodeKey: null!,
              });
            }}
            onCreate={() => {
              setCreateHgncChainDialogState({
                isOpen: true,
                defaultValue: null!,
              });
            }}
          />
        </>
        }
        {createOmimGChainDialogState.isOpen && (
          <OmimGCreateDialog
            isOpen
            defaultValue={createOmimGChainDialogState.defaultValue}
            onSuccess={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: prev.query === '' ? null! : '',
              }));
            }}
            onClose={() => {
              setCreateOmimGChainDialogState({
                isOpen: false,
                defaultValue: null!,
              });
            }}
          />
        )}
        {createHgncChainDialogState.isOpen && (
          <HgncCreateDialog
            isOpen
            defaultValue={createHgncChainDialogState.defaultValue}
            onSuccess={() => {
              setListChainDialogState((prev) => ({
                ...prev,
                query: prev.query === '' ? null! : '',
              }));
            }}
            onClose={() => {
              setCreateHgncChainDialogState({
                isOpen: false,
                defaultValue: null!,
              });
            }}
          />
        )}

        <AdditionalInfoMaterialCostPlugin
          // onIndexing={startIndexingMaterialCost}
          onSearch={async (query, setData) => {
            const _searchQuery = query || '*';
            const results =
              (await MaterialCostService.searchMaterialCost(_searchQuery)) ??
              [];
            setData(results);
            return results;
          }}
          onCreateMaterialCostClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenConfirmDialog(true);
              return;
            }
            setIsOpenCreateMaterialCostDialog(true);
          }}
          onViewAllMaterialCostClick={() =>
            Router.push(ROUTING.MATERIAL_COST_CATALOG)
          }
        >
          {({ onNewMaterialCostAdded }) => (
            <>
              {isOpenCreateMaterialCost && (
                <CreateMaterialCostDialog
                  isOpen={isOpenCreateMaterialCost}
                  onClose={() => {
                    setIsOpenCreateMaterialCostDialog(false);
                    // _focusOnFirstInput();
                  }}
                  onCreateSuccess={onNewMaterialCostAdded}
                />
              )}
            </>
          )}
        </AdditionalInfoMaterialCostPlugin>
        {/* NOTE: auto-transform ASV teamnumber add.info block to list mode */}
        <AdditionalInfoAutoTransformListNodePlugin<string>
          targetFk={ASV_KEY}
          documentedDoctor={documentedDoctor}
          onQuery={async (query, setData) => {
            const menuItems = memoizedDoctorASVNumbers
              .filter((asv) => asv.includes(query))
              .map(
                (asv) =>
                ({
                  id: DatetimeUtil.now(),
                  label: asv,
                  value: asv,
                  data: asv,
                } as IMenuItemWithData<string>)
              );
            setData(menuItems);
            return menuItems;
          }}
        />
        <AdditionalInfoJusification
          onSearch={(query, setData) => {
            const _searchQuery = query || '*';
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const results = listJustification.filter((item) =>
              item.includes(_searchQuery)
            );
            setData(results);
            return results;
          }}
          onCreateJustificationClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenCreateJustification(true);
              return;
            }
            setIsOpenCreateJustification(true);
          }}
          onDeletedJustificationClick={async (value: string) => {
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const newListJustification = listJustification.filter(
              (v) => v != value
            );
            await saveSettings({
              settings: {
                [SETTING_KEY_ALLERGIES_FOR_5009]:
                  JSON.stringify(newListJustification),
              },
            });
            await refetch();
          }}
        >
          {({ onNewJustificationAdded }) => (
            <>
              {isOpenCreateJustification && (
                <CreateJusitficationModal
                  isOpen={isOpenCreateJustification}
                  className="stage-modal-justification"
                  onCreateSuccess={async (ws) => {
                    onNewJustificationAdded(ws);
                    const listJustification: string[] = JSON.parse(
                      listSettingJustification?.settings?.[
                      SETTING_KEY_ALLERGIES_FOR_5009
                      ] || '[]'
                    );
                    listJustification.push(ws!);
                    await saveSettings({
                      settings: {
                        [SETTING_KEY_ALLERGIES_FOR_5009]:
                          JSON.stringify(listJustification),
                      },
                    });
                    refetch();
                    setIsOpenCreateJustification(false);
                  }}
                  onClose={() => {
                    setIsOpenCreateJustification(false);
                  }}
                  t={t}
                />
              )}
            </>
          )}
        </AdditionalInfoJusification>
      </ServiceNodeComponent>
      {/* CREATE SERVICE CODE DIALOG */}
      {isOpenEbmDialog && (
        <CreateEbmDialog
          createEbmDefaultValue={codeNotExist}
          isOpen={isOpenEbmDialog}
          onClose={() => setIsOpenEbmDialog(false)}
          onSaveSuccess={onSaveSuccessEbm}
        />
      )}
      {/* CONFIRM ADD NEW MATERIAL COST DIALOG */}
      <ConfirmOpenCreateDialog
        isOpen={isOpenConfirmDialog}
        onClose={() => {
          setIsOpenConfirmDialog(false);
          // _focusOnFirstInput();
        }}
        onConfirmClick={() => {
          setIsOpenConfirmDialog(false);
          setIsOpenCreateMaterialCostDialog(true);
        }}
      />
      {isOpenHospitalsPractices && (
        <HospitalsPracticesDialog
          isOpen={isOpenHospitalsPractices}
          onClose={onCloseHospitalsPractices}
          onSelect={() => {
            onCloseHospitalsPractices();
          }}
        />
      )}
      {isOpenCreateDoctor && (
        <CreateExternalAddressDialog
          isOpen={isOpenCreateDoctor}
          onClose={() => {
            setIsOpenCreateDoctor(false);
          }}
          onSuccess={() => null}
          onError={() => null}
        />
      )}
      {isOpenConfirmScheinDialog && (
        <InfoConfirmDialog
          type="primary"
          isOpen={isOpenConfirmScheinDialog}
          title={tConfirmDialog('title')}
          cancelText={tConfirmDialog('btnNo')}
          confirmText={tConfirmDialog('btnYes')}
          isShowIconTitle={false}
          onConfirm={() => {
            setOpenConfirmScheinDialog(false);
            openCreateSchein();
            setCurrentPayload(null);
          }}
          onClose={(closeAndSubmit) => {
            setOpenConfirmScheinDialog(false);
            if (closeAndSubmit) {
              onSubmit(currentPayload!);
              setCurrentPayload(null);
            }
          }}
        >
          <Flex column>{tConfirmDialog('description')}</Flex>
        </InfoConfirmDialog>
      )}
    </Flex>
  );
};

export default React.memo(ServiceBlock);
