import {
  ReferralDoctorInfo,
} from '@tutum/hermes/bff/service_domains_patient_file';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { ContractType } from '@tutum/hermes/bff/common';
import {
  EncounterGoaService,
  EncounterServiceTimeline,
  EncounterUvGoaService,
} from '@tutum/hermes/bff/repo_encounter';

async function getServiceMeta(
  contract: IContractInfo,
  data: IContractData
): Promise<EncounterServiceTimeline> {
  return {
    isPreParticipate: null!,
    freeText: null!,
    command: null!,
    code: data.code!,
    description: data.description!,
    referralDoctorInfo: getReferralInfo(data, contract),
    careFacility: getCareFacility(contract, data),
    serviceMainGroup: data.mainGroup,
    materialCosts: {
      required: false,
      materialCostsItemList: [],
    },
  };
}

async function getServiceMetaVersionGoa(
  data: IContractData
): Promise<EncounterGoaService> {
  return {
    factor: null!,
    quantity: null!,
    freeText: null!,
    command: null!,
    price: data?.price,
    code: data.code!,
    description: data.description!,
    isChangeDefault: false,
    materialCosts: {
      required: false,
      materialCostsItemList: [],
    },
  };
}

async function getServiceMetaVersionUvGoa(
  data: IContractData
): Promise<EncounterUvGoaService> {
  return {
    isGeneral: false,
    freeText: null!,
    command: null!,
    price: data?.price,
    code: data.code!,
    description: data.description!,
    materialCosts: {
      required: false,
      materialCostsItemList: [],
    },
  };
}
export default {
  getServiceMeta,
  getServiceMetaVersionGoa,
  getServiceMetaVersionUvGoa,
};

// ---------------------------------------------------- //

function getReferralInfo(_data: IContractData, _contract: IContractInfo) {
  const referralInfo: ReferralDoctorInfo = {
    lanr: undefined,
    bsnr: undefined,
    requiredBsnr: false,
    requiredLanr: false,
  };

  return referralInfo;
}

function getCareFacility(contract: IContractInfo, data: IContractData) {
  return {
    required:
      contract &&
      contract.type === ContractType.ContractType_HouseDoctorCare &&
      data.code === '0008',
    name: undefined!,
    ort: undefined!,
  };
}
