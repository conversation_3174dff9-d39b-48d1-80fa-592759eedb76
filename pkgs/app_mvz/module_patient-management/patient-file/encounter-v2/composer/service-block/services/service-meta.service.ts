import {
  ReferralDoctorInfo,
} from '@tutum/hermes/bff/service_domains_patient_file';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { ContractType } from '@tutum/hermes/bff/common';
import {
  EncounterGoaService,
  EncounterServiceTimeline,
  EncounterUvGoaService,
} from '@tutum/hermes/bff/repo_encounter';

async function getServiceMeta(
  contract: IContractInfo,
  data: IContractData
): Promise<EncounterServiceTimeline> {
  return {
    isPreParticipate: false,
    freeText: '',
    command: '',
    code: data.code!,
    description: data.description!,
    referralDoctorInfo: getReferralInfo(data, contract),
    careFacility: getCareFacility(contract, data),
    serviceMainGroup: data.mainGroup,
    materialCosts: {
      required: false,
      materialCostsItemList: [],
    },
  };
}

async function getServiceMetaVersionGoa(
  data: IContractData
): Promise<EncounterGoaService> {
  return {
    factor: 1,
    quantity: 1,
    freeText: '',
    command: '',
    price: data?.price,
    code: data.code!,
    description: data.description!,
    isChangeDefault: false,
    materialCosts: {
      required: false,
      materialCostsItemList: [],
    },
  };
}

async function getServiceMetaVersionUvGoa(
  data: IContractData
): Promise<EncounterUvGoaService> {
  return {
    isGeneral: false,
    freeText: '',
    command: '',
    price: data?.price,
    code: data.code!,
    description: data.description!,
    materialCosts: {
      required: false,
      materialCostsItemList: [],
    },
  };
}
export default {
  getServiceMeta,
  getServiceMetaVersionGoa,
  getServiceMetaVersionUvGoa,
};

// ---------------------------------------------------- //

function getReferralInfo(_data: IContractData, _contract: IContractInfo) {
  const referralInfo: ReferralDoctorInfo = {
    lanr: undefined,
    bsnr: undefined,
    requiredBsnr: false,
    requiredLanr: false,
  };

  // AKA_ABRD1544 for FAV contract: check if FAV contract then required lanr and bsnr.
  // if (contract?.type === ContractType.ContractType_SpecialistCare) {
  //   referralInfo.requiredBsnr = true;
  //   referralInfo.requiredLanr = true;
  //   return referralInfo;
  // }

  // // other rules by contract
  // if (!data || !data.rules || !data.rules.requiredInformation) {
  //   return referralInfo;
  // }

  // data?.rules?.requiredInformation?.forEach?.((item) => {
  //   if (item.requiredBsnr) {
  //     referralInfo.requiredBsnr = true;
  //   }
  //   if (item.requiredLanr) {
  //     referralInfo.requiredLanr = true;
  //   }
  // });

  return referralInfo;
}

function getCareFacility(contract: IContractInfo, data: IContractData) {
  return {
    required:
      contract &&
      contract.type === ContractType.ContractType_HouseDoctorCare &&
      data.code === '0008',
    name: '',
    ort: '',
  };
}
