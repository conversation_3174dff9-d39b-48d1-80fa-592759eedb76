import { debounce, isEmpty } from 'lodash';
import Router from 'next/router';
import React, { useState, useEffect, useMemo, useCallback } from 'react';

import {
  alertSuccessfully,
  BodyTextL,
  BodyTextM,
  Button,
  Flex,
  InfoConfirmDialog,
  Link,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import { Classes, Dialog } from '@tutum/design-system/components/Core';
import AdditionalInfoJusification from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-justification';
import AdditionalInfoMaterialCostPlugin from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-material-cost-plugin';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  saveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import type { Field as AdditionalInfoField } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';
import type { EncounterUvGoaService } from '@tutum/hermes/bff/repo_encounter';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { EncounterUvGoaServiceChain } from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import type { IContractData } from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.type';
import MaterialCostService from '@tutum/infrastructure/web-worker-services/material-cost.service';
import ScheinBlock from '@tutum/mvz/components/schein-block';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { useComposerActionChainStore } from '@tutum/mvz/module_action-chain';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { groupQuarterbySchein } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import CreateMaterialCostDialog from '@tutum/mvz/module_sdebm/material-cost-dialog/MaterialCostDialog.styled';
import UvGoaServiceChainTextEditor from '../service-chain-node-lexical/ServiceChainTextEditor';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { SETTING_KEY_ALLERGIES_FOR_5009 } from '../Composer.const';
import { parseToScheinMainGroup } from '../Composer.util';
import CreateJusitficationModal from '../goa-service-block/justification-modal';
import ServiceBlockService from '../service-chain-block/services/service-block.service';
import ServiceMetaService from '../service-chain-block/services/service-meta.service';
import {
  IProps,
  ServiceReturnType,
  SubmitReturnType,
} from '../service-chain-node-lexical/ServiceChainHooks';
import CreateGoaDialog from '@tutum/mvz/module_sduvgoa/create-uv-goa-dialog/CreateUVGoaDialog.styled';
import type SdebmI18n from '@tutum/mvz/locales/en/Sdebm.json';
import { checkIsSvSchein } from '@tutum/mvz/_utils/scheinFormat';

export interface IServiceChainBlockProps {
  className?: string;
  initEncounterId?: string;
  encounterDate?: number;
  data: EncounterUvGoaServiceChain;
  doctorProfile?: IEmployeeProfile;
  disabled?: boolean;
  scheinsFilteredByDate: ScheinItem[];
  onSubmit(payload: EncounterUvGoaServiceChain): Promise<void>;
  onClear: () => void;
  openCreateSchein: (scheinMaingRoup?: MainGroup) => void;
}

interface IConfirmOpenCreateDialogProps {
  isOpen: boolean;
  onClose(): void;
  onConfirmClick(): void;
}

const ConfirmOpenCreateDialog: React.FC<IConfirmOpenCreateDialogProps> = (
  props
) => {
  const { t: tSdebm } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'MaterialCostDialog',
  });

  return (
    <Dialog
      isOpen={props.isOpen}
      onClose={props.onClose}
      style={{ borderRadius: 4 }}
      canOutsideClickClose={false}
    >
      <Flex className={Classes.DIALOG_BODY} column gap={24}>
        <BodyTextL fontWeight={'Bold'} fontSize={20}>
          {tSdebm('createMaterialCostTitle')}
        </BodyTextL>
        {tSdebm('leaveCreateMaterialCostContent')}
        <Flex gap={16}>
          <Button
            onClick={props.onClose}
            minimal
            outlined
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('cancelCreate')}
          </Button>
          <Button
            fill
            onClick={props.onConfirmClick}
            intent="primary"
            style={{ flex: 1 }}
          >
            {tSdebm('confirmCreateMaterialCost')}
          </Button>
        </Flex>
      </Flex>
    </Dialog>
  );
};

function syncEditorData(
  editorRaw: string,
  services: EncounterUvGoaService[]
) {
  if (!editorRaw || !services.length) {
    return undefined;
  }
  const stateJson = JSON.parse(editorRaw);
  const children = stateJson.root.children;
  let i = 0;
  for (let j = 0; j < services.length; j++) {
    while (i < children.length && !children[i].encounterService) {
      i++;
    }
    children[i].encounterService.code = services[j].code;
    children[i].encounterService.description = services[j].description;
    i++;
  }
  return JSON.stringify(stateJson);
}

const ServiceChainBlock: React.FC<IServiceChainBlockProps> = (props) => {
  const {
    initEncounterId,
    data,
    onSubmit,
    disabled,
    className,
    encounterDate,
    doctorProfile,
    scheinsFilteredByDate,
    onClear,
    openCreateSchein,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tConfirmDialog } = I18n.useTranslation({
    namespace: 'Schein',
    nestedTrans: 'createSchein.ConfirmDialog',
  });

  const { t: translator } = I18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });

  const { t: tSdebmOverview } = I18n.useTranslation<keyof typeof SdebmI18n.SdebmOverview>({
    namespace: 'Sdebm',
    nestedTrans: 'SdebmOverview',
  });

  const composerActionChainStore = useComposerActionChainStore();
  const patientFileStore = usePatientFileStore();

  const scheinOnSidebar = patientFileStore?.schein?.activatedSchein;

  const initialStateLexical = useMemo(
    () =>
      syncEditorData(data.encounterUvGoaServiceChainRaw, data.uvGoaServices),
    [data]
  );

  const [isOpenCreateServiceDialog, setIsOpenCreateServiceDialog] = useState<boolean>(false);
  const [codeNotExist, setCodeNotExist] = useState('');
  const [filteredAdditionalInfos, setFilteredAdditionalInfos] = useState<
    AdditionalInfoField[]
  >([]);
  const [isOpenCreateMaterialCost, setIsOpenCreateMaterialCostDialog] =
    useState(false);
  const [isOpenConfirmDialog, setIsOpenConfirmDialog] = useState(false);
  const [isOpenCreateJustification, setIsOpenCreateJustification] =
    useState(false);

  const { data: listSettingJustification, refetch } = useQueryGetSettings({
    settings: [SETTING_KEY_ALLERGIES_FOR_5009],
  });

  const [selectedSchein, setSelectedSchein] = useState<ScheinItem | undefined>(
    undefined
  );

  const [isOpenConfirmScheinDialog, setOpenConfirmScheinDialog] =
    useState<boolean>(false);
  const [currentPayload, setCurrentPayload] =
    useState<EncounterUvGoaServiceChain | undefined>(undefined);

  const currentSchein = useMemo(() => {
    const singleScheinId = patientFileStore.schein.activatedSchein?.scheinId;
    return (
      (singleScheinId != null &&
        scheinsFilteredByDate.find((sch) => sch.scheinId === singleScheinId)) ||
      scheinOnSidebar
    );
  }, [
    patientFileStore.schein.activatedSchein,
    scheinsFilteredByDate,
    scheinOnSidebar,
  ]);

  const onServiceSelect = async <T extends IProps['usedFor']>(
    item: IContractData & { usedFor: T }
  ): Promise<ServiceReturnType<T>> => {
    const resultAfterValidate =
      await ServiceMetaService.getServiceMetaVersionUvGoa(item);
    if (!resultAfterValidate) {
      throw new Error('Service metadata retrieval failed.');
    }
    resultAfterValidate.scheins = parseToScheinMainGroup([currentSchein]);
    const updatedResult = {
      ...resultAfterValidate,
      serviceMainGroup: item.mainGroup ?? '',
      chargeSystemId: item.chargeSystemId ?? '',
    };

    return updatedResult as unknown as ServiceReturnType<T>;
  };

  const onSetScheinIds = (scheinItems: ScheinItem[] = []) => {
    setSelectedSchein(scheinItems[0]);
  };

  const searchServiceData = useMemo(
    () =>
      debounce(async (query: string, cb: (result: IContractData[]) => void) => {
        setCodeNotExist(query);

        const result = await ServiceBlockService.searchService(
          encounterDate!,
          query,
          doctorProfile!,
          [],
          undefined,
          undefined,
          false,
          MainGroup.BG,
          { isBgSchein: true, isGeneral: !!selectedSchein?.isGeneral }
        );

        cb(result.slice(0, 20));
      }, 500),
    [doctorProfile, encounterDate, selectedSchein]
  );

  useEffect(() => {
    const scheinKey = currentSchein?.kvTreatmentCase;
    catalogOverviewActions
      .getAdditionalInfos(currentSchein?.scheinMainGroup!, scheinKey!)
      .then((fields) => {
        const infors = fields.map((info) => {
          info.label = translator(info.fK);
          return info;
        });
        setFilteredAdditionalInfos(infors);
      });
  }, [currentSchein]);

  const hasFavSchein = useMemo(() => {
    return patientFileStore.schein.originalList?.some((schein) =>
      checkIsSvSchein(schein)
    );
  }, [patientFileStore.schein.originalList]);

  const isShowSuggestChangeToFavSchein = useMemo(() => {
    return hasFavSchein && !currentSchein?.hzvContractId;
  }, [hasFavSchein, currentSchein]);

  const NoResults = () => {
    return (
      <>
        <Flex className="sl-no-results" p="8px 16px" justify="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PLACEHOLDER}>
            {`${t('noResultsFound')}.`}
          </BodyTextM>
          <Link onClick={() => setIsOpenCreateServiceDialog(true)}>{t('CreateServiceCode')}</Link>
        </Flex>
        {isShowSuggestChangeToFavSchein && (
          <BodyTextM>{t('changeToFavSchein')}</BodyTextM>
        )}
      </>
    );
  };

  const memoizedEncounterDate = useMemo(() => encounterDate, [encounterDate]);

  const filterScheinFunc = useCallback(
    (_option: ScheinItem) =>
      currentSchein?.scheinMainGroup === _option?.scheinMainGroup,
    [currentSchein]
  );

  const createListInsuranceMapBySchein = useMemo(() => {
    const listInsurance =
      patientFileStore.patient.current?.patientInfo.insuranceInfos || [];

    const listScheinId = scheinsFilteredByDate.map(
      (schein) => schein.insuranceId
    );

    const res = listInsurance.filter((insur) =>
      listScheinId.includes(insur.id)
    );
    const groupQuarterBySchein = groupQuarterbySchein(
      scheinsFilteredByDate,
      res
    );

    return groupQuarterBySchein;
  }, [scheinsFilteredByDate]);

  useEffect(() => {
    const firstSchein = data.uvGoaServices?.[0]?.scheins?.[0]?.scheinId;
    const defaultSelectedScheinId = initEncounterId
      ? firstSchein
      : patientFileStore?.schein?.activatedSchein?.scheinId;
    const scheinValid =
      scheinsFilteredByDate.find(
        (schein) => schein.scheinId === defaultSelectedScheinId
      ) ||
      (scheinsFilteredByDate?.length === 1 && !!firstSchein
        ? scheinsFilteredByDate[0]
        : undefined);
    setSelectedSchein(scheinValid);
    data.uvGoaServices?.forEach((service) => {
      service = {
        ...service,
        scheins:
          typeof scheinValid != 'undefined'
            ? [
              {
                scheinId: scheinValid.scheinId,
                group: scheinValid.scheinMainGroup,
              },
            ]
            : [],
      };
    });
  }, [
    initEncounterId,
    JSON.stringify(scheinsFilteredByDate),
    patientFileStore?.schein?.activatedSchein,
  ]);

  const handleSubmit = <T extends IProps['usedFor']>(
    payload: SubmitReturnType<T>,
    usedFor: IProps['usedFor']
  ) => {
    if (usedFor === 'UV_GOA_SERVICE') {
      const updatedPayload: EncounterUvGoaServiceChain = {
        ...(payload as EncounterUvGoaServiceChain),
      };
      if (composerActionChainStore.currentBlock) {
        updatedPayload.encounterUvGoaServiceChainRaw =
          composerActionChainStore.currentBlock.encounterUvGoaServiceChainRaw;
        updatedPayload.uvGoaServices = updatedPayload.uvGoaServices.map(
          (service, index) => {
            service.additionalInfos =
              composerActionChainStore.currentBlock
                ?.additionalInfosServiceChain?.[index] || service.additionalInfos;

            return service;
          }
        );
      }
      if (!scheinsFilteredByDate.length || isEmpty(selectedSchein)) {
        setCurrentPayload({
          ...updatedPayload, uvGoaServices: updatedPayload.uvGoaServices.map(service => ({
            ...service,
            // no schein
            scheins: []
          }))
        });
        setOpenConfirmScheinDialog(true);
        return;
      }
      onSubmit(updatedPayload);
    }
  };

  const onSaveSuccessGoa = () => {
    alertSuccessfully(
      tSdebmOverview('createSuccessEbm'),
      { timeout: TOASTER_TIMEOUT_CUSTOM }
    );
    setIsOpenCreateServiceDialog(false);
  };

  return (
    <Flex className={className} column gap={0}>
      <UvGoaServiceChainTextEditor
        id="service_chain_text_editor"
        usedFor="UV_GOA_SERVICE"
        disabled={disabled}
        scheinId={selectedSchein?.scheinId}
        initialEditorState={initialStateLexical!}
        onSubmit={(payload) => handleSubmit(payload, 'UV_GOA_SERVICE')}
        className="sl-ServiceChainTextEditor"
        encounterDate={memoizedEncounterDate}
        searchService={searchServiceData}
        onServiceSelect={onServiceSelect}
        pointValue={patientFileStore.pointValue}
        additionalFields={filteredAdditionalInfos}
        noResultsComponent={<NoResults />}
        onClear={onClear}
        rightElement={
          <ScheinBlock
            selectedSchein={selectedSchein}
            openCreateSchein={() => openCreateSchein(MainGroup.BG)}
            onSetScheinIds={onSetScheinIds}
            scheinFilter={createListInsuranceMapBySchein}
            filterOption={filterScheinFunc}
          />
        }
      >
        <AdditionalInfoMaterialCostPlugin
          onSearch={async (query, setData) => {
            const _searchQuery = query || '*';
            const results =
              (await MaterialCostService.searchMaterialCost(_searchQuery)) ??
              [];
            setData(results);
            return results;
          }}
          onCreateMaterialCostClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenConfirmDialog(true);
              return;
            }
            setIsOpenCreateMaterialCostDialog(true);
          }}
          onViewAllMaterialCostClick={() =>
            Router.push(ROUTING.MATERIAL_COST_CATALOG)
          }
        >
          {({ onNewMaterialCostAdded }) => (
            <>
              {isOpenCreateMaterialCost && (
                <CreateMaterialCostDialog
                  isOpen={isOpenCreateMaterialCost}
                  onClose={() => {
                    setIsOpenCreateMaterialCostDialog(false);
                    // _focusOnFirstInput();
                  }}
                  onCreateSuccess={onNewMaterialCostAdded}
                />
              )}
            </>
          )}
        </AdditionalInfoMaterialCostPlugin>
        <AdditionalInfoJusification
          onSearch={(query, setData) => {
            const _searchQuery = query || '*';
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const results = listJustification.filter((item) =>
              item.includes(_searchQuery)
            );
            setData(results);
            return results;
          }}
          onCreateJustificationClick={(isNoResult) => {
            if (isNoResult) {
              setIsOpenCreateJustification(true);
              return;
            }
            setIsOpenCreateJustification(true);
          }}
          onDeletedJustificationClick={async (value: string) => {
            const listJustification: string[] = JSON.parse(
              listSettingJustification?.settings?.[
              SETTING_KEY_ALLERGIES_FOR_5009
              ] || '[]'
            );
            const newListJustification = listJustification.filter(
              (v) => v != value
            );
            await saveSettings({
              settings: {
                [SETTING_KEY_ALLERGIES_FOR_5009]:
                  JSON.stringify(newListJustification),
              },
            });
            await refetch();
          }}
        >
          {({ onNewJustificationAdded }) => (
            <>
              {isOpenCreateJustification && (
                <CreateJusitficationModal
                  isOpen={isOpenCreateJustification}
                  className="stage-modal-justification"
                  onCreateSuccess={async (ws) => {
                    onNewJustificationAdded(ws);
                    const listJustification: string[] = JSON.parse(
                      listSettingJustification?.settings?.[
                      SETTING_KEY_ALLERGIES_FOR_5009
                      ] || '[]'
                    );
                    listJustification.push(ws!);
                    await saveSettings({
                      settings: {
                        [SETTING_KEY_ALLERGIES_FOR_5009]:
                          JSON.stringify(listJustification),
                      },
                    });
                    refetch();
                    setIsOpenCreateJustification(false);
                  }}
                  onClose={() => {
                    setIsOpenCreateJustification(false);
                  }}
                  t={t}
                />
              )}
            </>
          )}
        </AdditionalInfoJusification>
      </UvGoaServiceChainTextEditor>
      {/* CONFIRM ADD NEW MATERIAL COST DIALOG */}
      <ConfirmOpenCreateDialog
        isOpen={isOpenConfirmDialog}
        onClose={() => {
          setIsOpenConfirmDialog(false);
        }}
        onConfirmClick={() => {
          setIsOpenConfirmDialog(false);
          setIsOpenCreateMaterialCostDialog(true);
        }}
      />

      {isOpenConfirmScheinDialog && (
        <InfoConfirmDialog
          type="primary"
          isOpen={isOpenConfirmScheinDialog}
          title={tConfirmDialog('title')}
          cancelText={tConfirmDialog('btnNo')}
          confirmText={tConfirmDialog('btnYes')}
          isShowIconTitle={false}
          onConfirm={() => {
            setOpenConfirmScheinDialog(false);
            openCreateSchein(MainGroup.BG);
            setCurrentPayload(undefined);
          }}
          onClose={(closeAndSubmit) => {
            setOpenConfirmScheinDialog(false);
            if (closeAndSubmit) {
              onSubmit(currentPayload!);
              setCurrentPayload(undefined);
            }
          }}
        >
          <Flex column>{tConfirmDialog('description')}</Flex>
        </InfoConfirmDialog>
      )}

      {isOpenCreateServiceDialog && (
        <CreateGoaDialog
          createUVGoaDefaultValue={codeNotExist}
          isOpen
          onClose={() => {
            setIsOpenCreateServiceDialog(false);
          }}
          onSaveSuccess={onSaveSuccessGoa}
        />
      )}
    </Flex>
  );
};

export default React.memo(ServiceChainBlock);
