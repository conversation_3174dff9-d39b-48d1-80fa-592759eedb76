import React from 'react';
import { isFunction } from 'lodash';

import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { styled } from '@tutum/design-system/models';
import OriginalComposer, { IComposerProps } from './Composer';
import { COLOR } from '@tutum/design-system/themes/styles';
import ComposerUtil from '@tutum/design-system/composer/Composer.util';
import Theme from '@tutum/mvz/theme';

const _styled = Theme.styled;
const StyledComposer: React.ComponentType<IComposerProps> = _styled(
  OriginalComposer
).attrs(({ className }) => ({
  className: getCssClass('sl-Composer-s2', className),
}))`

    &:focus-within {
      border-color: ${COLOR.BACKGROUND_SELECTED_STRONG};
    }

    position: relative;
    display: flex;
    flex-direction: column;
    padding: 8px;
    margin: 8px;
    border:${(props) =>
    isFunction(props.handleCloseEditInline)
      ? `1px solid ${COLOR.BACKGROUND_SELECTED_STRONG}`
      : `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`};
    border-radius: 4px;
    gap: 8px;
    caret-color: ${COLOR.BACKGROUND_SELECTED_STRONG};
    background-color: ${COLOR.BACKGROUND_PRIMARY_WHITE};

    .sl-blocks-wrapper {
      position: relative;
      .sl-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .rowType {
      font-size: ${(props) =>
    ComposerUtil.parseCustomFontsize(props.theme.timelineTheme?.scaleNumber)}
    }

    .sl-ScheinBlock::-webkit-scrollbar {
      display: none; /* Safari and Chrome */
    }
    .input-justification {
      width: 100%
    }

`;

const StyledCommandOption = styled('div')`
  display: flex;
  align-items: center;
  width: 100%;
  color: ${COLOR.TEXT_PRIMARY_BLACK};
  .sl-spacer {
    flex: 1;
  }
  .sl-tooltip {
    margin-left: 4px;
  }
`;

const StyledTooltipContent = styled('div')`
  max-width: 268px;
`;

export { StyledCommandOption, StyledTooltipContent };
export default StyledComposer;
