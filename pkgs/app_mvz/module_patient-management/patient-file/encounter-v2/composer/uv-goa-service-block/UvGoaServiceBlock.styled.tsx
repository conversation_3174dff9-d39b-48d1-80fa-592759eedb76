import React from 'react';

import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR, mixTypography } from '@tutum/design-system/themes/styles';
import { scaleSpacePx, SPACE } from '@tutum/design-system/styles';
import Theme from '@tutum/mvz/theme';
import ComposerUtil from '@tutum/design-system/composer/Composer.util';
import OriginalUvGoaServiceBlock, {
  IUvGoaServiceBlockProps,
} from './UvGoaServiceBlock';

const UvGoaServiceBlock: React.ComponentType<IUvGoaServiceBlockProps> = Theme.styled(
  OriginalUvGoaServiceBlock
).attrs(({ className }) => ({
  className: getCssClass('sl-UvGoaServiceBlock', className),
}))`

   width: 99%;
    padding-left: ${scaleSpacePx(2)};

    .label-mid-additional {
      font-weight: 600;
    }

    .sl-ServiceNodeComponent {
      &>.sl-Flex {
        align-items: flex-start;
      }
    }

    .label {
      padding: ${SPACE.SPACE_XS} 0;
      margin-right:${SPACE.SPACE_S};
      min-width: ${scaleSpacePx(15)};
      color: ${COLOR.TEXT_TERTIARY_SILVER};
    }

    .bp5-popover {
      width: 100%;
      .sl-no-results {
        background-color: ${COLOR.INFO_SECONDARY_PRESSED};
      }
    }
    .sl-InputSuggestion__input {
      font-size: ${(props) =>
    ComposerUtil.parseCustomFontsize(
      props.theme.timelineTheme?.scaleNumber
    )} !important;
    }

    .sl-PopoverContentEditable {
      .rowData {
        ${mixTypography('bodyM')}
        text-align: left;
        padding-bottom: 0px;
      }
    }

    .sl-composer-paragraph {
      span, .react-select__single-value, #text, * {
        font-size: ${(props) =>
    ComposerUtil.parseCustomFontsize(
      props.theme.timelineTheme?.scaleNumber,
      true
    )};
        font-weight: 600;
      }
    }

    .bp5-popover-content {
      .bp5-menu {
        max-height: ${scaleSpacePx(90)};
        overflow: auto;
        border: 1px solid ${COLOR.TEXT_TERTIARY_SILVER};
      }
    }

    .bp5-popover-content {
        .bp5-menu {
          max-height: ${scaleSpacePx(90)};
          overflow: auto;
          border: 1px solid ${COLOR.TEXT_TERTIARY_SILVER};
        }
    }

    .bp5-text-overflow-no-ellipsis.bp5-fill {
      overflow: initial;
      text-overflow: unset;
      white-space: normal;
    }


`;

export default UvGoaServiceBlock;
