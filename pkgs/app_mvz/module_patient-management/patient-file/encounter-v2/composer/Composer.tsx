import { isNil, maxBy } from 'lodash';
import moment from 'moment';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Select from 'react-select';

import type ComposerCommandI18n from '@tutum/mvz/locales/en/ComposerCommand.json';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import type {
  ActionMeta,
  GroupBase,
  SelectComponentsConfig,
  SelectInstance,
} from 'react-select';
import type { IComposerRow } from './Composer.type';

import {
  BodyTextM,
  Box,
  Button,
  Flex,
  IMenuItemWithData,
  InfoConfirmDialog,
  Svg,
  alertError,
  alertSuccessfully,
  coreComponents,
} from '@tutum/design-system/components';
import { <PERSON><PERSON>, <PERSON>lt<PERSON> } from '@tutum/design-system/components/Core';
import ComposerUtil from '@tutum/design-system/composer/Composer.util';
import { DEFAULT_SELECT_COMPONENT_CONFIG } from '@tutum/design-system/consts/react-select-config';
import { COLOR } from '@tutum/design-system/themes/styles';
import { MainGroup } from '@tutum/hermes/bff/common';
import { FormName } from '@tutum/hermes/bff/form_common';
import { useQueryGetContractById } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import {
  getPatientParticipation,
  useQueryCheckPatientParticipation,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_participation';
import { useMutationCreateSvScheinAutomaticly } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { useMutationUpdateEncounterCaseForServiceEntries } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import {
  EncounterGoaService,
  EncounterServiceTimeline,
  EncounterUvGoaService,
} from '@tutum/hermes/bff/legacy/repo_encounter';
import { CheckPatientParticipationRequest } from '@tutum/hermes/bff/legacy/service_domains_patient_participation';
import {
  EncounterGoaServiceChain,
  TimelineModel,
} from '@tutum/hermes/bff/legacy/timeline_common';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';
import {
  AdditionalInfoParent,
  EncounterDiagnoseTimeline,
  TreatmentCase,
} from '@tutum/hermes/bff/repo_encounter';
import type { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { PatientEnrollmentInformationStatus } from '@tutum/hermes/bff/service_domains_enrollment';
import {
  EncounterCase,
  RunSdkrwEnum,
} from '@tutum/hermes/bff/service_domains_patient_file';
import {
  EncounterServiceChain,
  EncounterUvGoaServiceChain,
} from '@tutum/hermes/bff/timeline_common';
import I18n from '@tutum/infrastructure/i18n';
import { IcdSearchCatalog } from '@tutum/infrastructure/masterdata-service/type';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import {
  checkIsBgSchein,
  checkIsPrivateSchein,
  checkIsSvSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import {
  CommonActionChainElementId,
  registerActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import GoaServiceBlock from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/goa-service-block';
import ServiceBlock from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-block';
import ServiceChainBlock from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/service-chain-block/ServiceChainBlock.styled';
import UvGoaServiceBlock from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/composer/uv-goa-service-block';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import reloadSchein from '@tutum/mvz/module_patient-management/utils/reloadSchein';
import type { StylesConfig } from 'react-select';
import {
  patientFileActions,
  usePatientFileStore,
} from '../../PatientFile.store';
import {
  timelineActions,
  useTimeLineStore,
} from '../../timeline/Timeline.store';
import ActionBar from '../action-bar';
import { IDefaultCatalog } from '../action-bar/ActionBar';
import {
  IActionBarStore,
  actionBarActions,
  initStore as initActionBarStore,
} from '../action-bar/ActionBar.store';
import {
  ActionChainCommands,
  DEFAULT_COMMAND_SELECT_STYLE_CONFIG,
  DiagnoseCommands,
  DmpCommands,
  ServiceChainCommand,
} from './Composer.const';
import {
  KEY_MAPPING_TIMELINE,
  createTimelineItem,
  editTimelineItem,
  getKeyTimelineMapping,
} from './Composer.service';
import { StyledCommandOption, StyledTooltipContent } from './Composer.styled';
import {
  BaseComposerRowType,
  SERVICECOMMANDS,
  TStagerCancel,
} from './Composer.type';
import {
  COMPOSER_DEFAULT_COMMAND_LIST,
  ComposerCommandOption,
  DIAGNOSE_MAPPING,
  filterScheinByDate,
  getYearQuarterOfEncounterDateAndNow,
  parseCommand,
} from './Composer.util';
import ActionChainBlock from './action-chain-block';
import CustomizeInComposer from './customize-block';
import DiagnoseBlock from './diagnose-block/DiagnoseBlock';
import DiagnoseBlockService from './diagnose-block/DiagnoseBlock.service';
import DMPBlock from './dmp-block';
import FreetextBlockInComposer from './freetext-block';
import GoaServiceChainBlock from './goa-service-chain-block/GoaServiceChainBlock.styled';
import RowTypeBlock from './rowtype-block/RowTypeBlock.styled';
import ServiceBlockService from './service-block/services/service-block.service';
import UvGoaServiceChainBlock from './uv-goa-service-chain-block/UvGoaServiceChainBlock.styled';
import { checkValidHgncSymbol } from './utils';

const InfoIcon = '/images/icon-info.svg';
const AddIcon = '/images/plus-circle.svg';
const ReturnKeyIcon = '/images/return-key.svg';

export interface IComposerProps {
  className?: string;
  contract?: IContractInfo;
  initEncounterId?: string; // NOTE: inline edit must have id (encounter id)
  defaultValue?: Partial<IComposerRow>;
  openCreateSchein: (scheinMaingRoup?: MainGroup) => void;
  onSelectCommand?: (
    cmd: string,
    clearComposer: () => void
  ) => 'continue' | 'stop-propagate' | undefined;
  handleCloseEditInline?: () => void;
  excludedComposerCommands?: string[];
  reloadQuarters: ReloadQuarterFunc;
  currentBlock: Partial<IComposerRow> | null;
  setCurrentBlock: React.Dispatch<
    React.SetStateAction<Partial<IComposerRow> | null>
  >;
  setShowHintSubmit?: React.Dispatch<React.SetStateAction<boolean>>;
}

const DEFAULT_COMMAND_SELECT_COMPONENT_CONFIG: Partial<
  SelectComponentsConfig<
    ComposerCommandOption,
    false,
    GroupBase<ComposerCommandOption>
  >
> = {
  ...DEFAULT_SELECT_COMPONENT_CONFIG,
  Input: (props) => (
    <coreComponents.Input
      {...props}
      {...registerActionChainElementId(
        CommonActionChainElementId.COMPOSER_COMMAND_INPUT
      )}
    />
  ),
  Option: (props) => {
    const { label, keywords, tooltipDescription } =
      props.data as ComposerCommandOption;
    return (
      <coreComponents.Option {...props}>
        <StyledCommandOption>
          <span className="sl-label">{label}</span>
          {!!tooltipDescription && (
            <Tooltip
              className="sl-tooltip"
              content={
                <StyledTooltipContent>
                  {tooltipDescription}
                </StyledTooltipContent>
              }
              position="top"
            >
              <Flex align="center">
                <Svg src={InfoIcon} />
              </Flex>
            </Tooltip>
          )}
          <span className="sl-spacer" />
          <BodyTextM
            fontWeight={600}
            fontSize={11}
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            style={{ marginRight: 7 }}
            className="sl-commands"
          >
            {keywords?.join(', ')}
          </BodyTextM>
          {props.isFocused ? (
            <Svg src={ReturnKeyIcon} size={16} />
          ) : (
            <Box h={16} w={16} />
          )}
        </StyledCommandOption>
      </coreComponents.Option>
    );
  },
};

type SelectRefType<T> = SelectInstance<
  IMenuItemWithData<T>,
  false,
  GroupBase<IMenuItemWithData<T>>
>;

const Composer: React.FC<IComposerProps> = (props) => {
  const {
    className,
    contract,
    initEncounterId,
    defaultValue,
    openCreateSchein,
    handleCloseEditInline,
    onSelectCommand,
    reloadQuarters,
    excludedComposerCommands = [],
    currentBlock,
    setCurrentBlock,
    setShowHintSubmit = () => { },
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });
  const { t: commandTrans } = I18n.useTranslation<
    keyof typeof ComposerCommandI18n
  >({
    namespace: 'ComposerCommand',
  });
  const { t: createSvScheinTrans } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.CreateSvSchein
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'CreateSvSchein',
  });
  const refComposer = useRef<SelectRefType<ComposerCommandOption[]>>(null);
  const settingStore = useSettingStore();
  const timelineStore = useTimeLineStore();

  const customizeDocumentTypes = timelineStore.documentTypes;
  const { getDoctorById, globalData } = useContext(GlobalContext.instance);
  const {
    patientManagement: {
      patient,
      selectedContractDoctor,
      getPatientParticipationResponse,
    },
    patientManagement,
    setGetPatientParticipationResponse,
  } = useContext(PatientManagementContext.instance);
  const patientFileStore = usePatientFileStore();
  const doctorList = selectedContractDoctor?.availableDoctor || [];
  const defaultSetting: IDefaultCatalog = {
    defaultCatalog: patientFileStore.defaultCatalog || '',
    defaultDoctorSpecialist: patientFileStore.defaultDoctorSpecialist || '',
  };

  const [actionBarState, setActionBarState] = useState<IActionBarStore>({
    ...initActionBarStore,
    encounterDate: datetimeUtil.now(),
    doctorId: defaultValue?.treatmentDoctorId,
    bsnrId: defaultValue?.bsnrId,
  });
  const [stageCancel, setStageCancel] = useState<TStagerCancel>('default');
  const [isLoading, setLoading] = useState(false);
  const [availableCheckParticipation, setAvailableCheckParticipation] =
    useState<{ [key: string]: boolean }>({});

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    actionBarState.doctorId,
    actionBarState.bsnrId
  );

  // NOTE: [filter Scheins by quarter]
  const scheinsFilteredByDate = useMemo<ScheinItem[]>(() => {
    const encounterTime = actionBarState?.encounterDate
      ? new Date(actionBarState.encounterDate)
      : datetimeUtil.date();

    const listScheins = filterScheinByDate(
      patientFileStore.schein.originalList,
      encounterTime
    );
    return listScheins?.filter((item) => !!item) || [];
  }, [patientFileStore.schein.originalList, actionBarState?.encounterDate]);

  const isPrivate = useMemo(() => {
    if (defaultValue?.scheins?.[0]?.group) {
      return [MainGroup.IGEL, MainGroup.PRIVATE].includes(
        defaultValue?.scheins?.[0]?.group
      );
    }
    if (
      patientFileStore.schein.activatedSchein &&
      scheinsFilteredByDate.length > 0
    ) {
      return checkIsPrivateSchein(patientFileStore.schein.activatedSchein);
    }

    // NOTE: In case no schein is base on patient type
    const genericInfo =
      patientFileStore.patient.current?.patientInfo?.genericInfo;
    return (
      genericInfo && genericInfo.patientType === PatientType.PatientType_Private
    );
  }, [
    defaultValue,
    patientFileStore.schein.activatedSchein,
    scheinsFilteredByDate,
  ]);
  useEffect(() => {
    if (!actionBarState?.encounterDate) return;
    getPatientParticipation({
      checkDate: actionBarState?.encounterDate,
      patientId: patientManagement.patient?.id || '',
    }).then(async ({ data: resp }) => {
      setGetPatientParticipationResponse(resp);
    });
  }, [actionBarState?.encounterDate]);

  const isBg = useMemo(() => {
    if (defaultValue?.scheins?.[0]?.group) {
      return [MainGroup.BG].includes(defaultValue?.scheins?.[0]?.group);
    }
    if (
      patientFileStore.schein.activatedSchein &&
      scheinsFilteredByDate.length > 0
    ) {
      return checkIsBgSchein(patientFileStore.schein.activatedSchein);
    }
    return false;
  }, [
    defaultValue,
    patientFileStore.schein.activatedSchein,
    scheinsFilteredByDate,
  ]);

  const { isSuccess: isSuccessContract } = useQueryGetContractById(
    {
      contractId: patientFileStore.schein.activatedSchein?.hzvContractId || '',
      selectedDate: actionBarState?.encounterDate || 0,
    },
    {
      enabled: Boolean(
        patientFileStore.schein.activatedSchein?.hzvContractId &&
        actionBarState?.encounterDate
      ),
    }
  );

  const [isAutoFocusOnCommand, setIsAutoFocusOnCommand] = useState(false);
  const [searchCommandText, setSearchCommandText] = useState('');

  const DMPList = useMemo(() => {
    return doctorList.reduce((dMPList: string[], doctor) => {
      doctor?.dmpPrograms?.forEach((dmp) => {
        if (!dMPList.includes(dmp)) {
          dMPList.push(dmp);
        }
      });

      return dMPList;
    }, []);
  }, [doctorList]);

  const isPrivatePatient = useMemo(() => {
    return (
      patient &&
      patient.patientInfo.genericInfo.patientType ===
      PatientType.PatientType_Private
    );
  }, [patient]);

  const hasDMPButton = useMemo(() => {
    return !isPrivatePatient && !!DMPList.length;
  }, [isPrivatePatient, DMPList]);

  const hasEdokuCommand = useMemo(() => {
    return (
      !checkIsPrivateSchein(patientFileStore.schein.activatedSchein) &&
      !checkIsBgSchein(patientFileStore.schein.activatedSchein)
    );
  }, [patientFileStore.schein.activatedSchein]);

  const excludedComposerCommandsCustom = useMemo(() => {
    return [
      ...(excludedComposerCommands || []),
      !hasDMPButton ? 'DMP' : '',
      !hasEdokuCommand || isNil(treatmentDoctor?.eHKSType) ? 'EHKS' : '',
    ].filter((item) => item);
  }, [
    excludedComposerCommands,
    hasDMPButton,
    hasEdokuCommand,
    treatmentDoctor?.eHKSType,
  ]);

  const options = COMPOSER_DEFAULT_COMMAND_LIST(
    customizeDocumentTypes,
    excludedComposerCommandsCustom
  );

  const [isOpenConfirmCreateSvSchein, setIsOpenConfirmCreateSvSchein] =
    useState(false);

  const [isCommandMenuOpen, setIsCommandMenuOpen] = useState(false);

  // NOTE: [sync doctorProfile]
  const doctorProfile = useMemo(
    () =>
      getDoctorById(
        actionBarState?.doctorId || globalData.userProfile?.id || ''
      ),
    [actionBarState?.doctorId, globalData.userProfile?.id]
  );

  const selectedScheinInfo = useMemo(() => {
    if (!currentBlock || !scheinsFilteredByDate[0]) {
      return null;
    }

    const selectedSchein = currentBlock.scheins?.[0];

    return scheinsFilteredByDate.find(
      (scheinItem) => scheinItem.scheinId === selectedSchein?.scheinId
    );
  }, [currentBlock, scheinsFilteredByDate]);

  const isDisabledEncounterCase = useMemo(() => {
    if (
      !currentBlock?.command ||
      ![ServiceChainCommand].includes(currentBlock.command) ||
      !selectedScheinInfo ||
      !checkIsSvSchein(selectedScheinInfo)
    ) {
      return false;
    }

    const entries = timelineStore.timelineState.find(
      (item) =>
        item.quarter === selectedScheinInfo.g4101Quarter &&
        item.year === selectedScheinInfo.g4101Year
    )?.timelineModels;
    const hasServiceAccidentEntry = entries?.some(
      (entry) =>
        (!!entry.encounterServiceTimeline || !!entry.encounterServiceChain) &&
        entry.scheinIds?.[0] === selectedScheinInfo.scheinId &&
        entry.encounterCase === EncounterCase.NOT
    );

    return hasServiceAccidentEntry;
  }, [currentBlock, timelineStore.timelineState, selectedScheinInfo]);

  const {
    mutate,
    isPending: isLoadingCreateSvSchein,
    isSuccess,
    reset,
  } = useMutationCreateSvScheinAutomaticly({
    async onSuccess(resp) {
      const { scheins, patientParticipation } = await reloadSchein(
        patientManagement,
        false
      );
      const newActiveSchein = scheins.find((s) =>
        resp.data.scheinIds.includes(s.scheinId)
      );
      if (!newActiveSchein) {
        return;
      }
      patientFileActions.schein.setActivatedSchein(
        newActiveSchein,
        patientParticipation.participations
      );
      setIsOpenConfirmCreateSvSchein(false);
      setTimeout(() => {
        if (currentBlock?.command) {
          triggerBindEncounterDate({
            activeSchein: newActiveSchein,
            encounterDate: actionBarState.encounterDate || 0,
            command: currentBlock?.command,
            originalList: patientFileStore.schein.originalList,
            initEncounterId: initEncounterId || '',
          });
        }
      });
    },
  });

  useEffect(() => {
    if (isDisabledEncounterCase) {
      handleSetActionBarValues('encounterCase', EncounterCase.NOT, false);
    }
  }, [isDisabledEncounterCase]);

  useEffect(() => {
    if (patientFileStore.activeTabId === ID_TABS.TIMELINE) {
      refComposer?.current?.focus();
    }
  }, [patientFileStore.activeTabId]);

  useEffect(() => {
    if (!initEncounterId) {
      setCurrentBlock(null);
    }
  }, [initEncounterId, patientFileStore.schein.activatedSchein?.scheinId]);

  const checkParticipationReq: CheckPatientParticipationRequest =
    useMemo(() => {
      const contractId =
        patientFileStore.schein.activatedSchein?.hzvContractId || '';
      const doctorId = getPatientParticipationResponse?.participations?.find(
        (participation) => participation.contractId === contractId
      )?.doctorId;
      const req = {
        patientId: patient?.id || '',
        doctorId: doctorId || '',
        contractId: contractId || '',
        checkDate: datetimeUtil.date(actionBarState?.encounterDate),
      };
      if (Object.keys(req).some((key) => !req[key] || req[key] === ''))
        return {
          patientId: '',
          doctorId: '',
          contractId: '',
          checkDate: new Date(),
        } as unknown as CheckPatientParticipationRequest;
      return req;
    }, [
      patient,
      patientFileStore.schein.activatedSchein?.hzvContractId,
      actionBarState?.encounterDate,
      getPatientParticipationResponse?.participations,
    ]);

  const { data: checkParticipationRes } = useQueryCheckPatientParticipation(
    checkParticipationReq,
    {
      enabled: checkParticipationReq.patientId !== '',
    }
  );

  useEffect(() => {
    setAvailableCheckParticipation((prev) => {
      prev[
        moment(datetimeUtil.date(actionBarState?.encounterDate)).format(
          DATE_FORMAT
        )
      ] = Boolean(checkParticipationRes?.isAvailable);
      return prev;
    });
  }, [checkParticipationRes]);

  useEffect(() => {
    if (!patientFileStore.schein.activatedSchein) {
      return;
    }
    if (
      !patientFileStore.schein.activatedSchein?.hzvContractId &&
      currentBlock?.command
    ) {
      triggerBindEncounterDate({
        activeSchein: patientFileStore.schein.activatedSchein,
        encounterDate: actionBarState.encounterDate || 0,
        command: currentBlock?.command,
        originalList: patientFileStore.schein.originalList,
        initEncounterId: initEncounterId || '',
      });
      return;
    }
    const selectedDate = moment(new Date(actionBarState?.encounterDate || 0)),
      participation =
        patientManagement?.getPatientParticipationResponse?.participations?.find(
          (p) =>
            p.contractId ===
            patientFileStore.schein.activatedSchein?.hzvContractId
        ),
      filteredSvSchein = patientFileStore.schein.originalList.filter(
        (s) => s.hzvContractId
      );
    if (
      selectedDate.isValid() &&
      filteredSvSchein.length > 0 &&
      [...DiagnoseCommands].includes(currentBlock?.command || '') &&
      checkIsSvSchein(patientFileStore.schein.activatedSchein) &&
      !isOpenConfirmCreateSvSchein &&
      participation &&
      isSuccessContract &&
      !isSuccess
    ) {
      const selectedYear = selectedDate.year(),
        selectedQuarter = selectedDate.quarter();

      const filteredSchein = filteredSvSchein.filter((s) => {
        const { year, quarter } =
          ComposerUtil.getValidQuarterAndYearInSchein(s);
        return year === selectedYear && quarter === selectedQuarter;
      });
      if (filteredSchein.length === 0) {
      }
      if (filteredSchein.length > 0) {
        triggerBindEncounterDate({
          activeSchein: patientFileStore.schein.activatedSchein,
          encounterDate: actionBarState.encounterDate || 0,
          command: currentBlock?.command || '',
          originalList: patientFileStore.schein.originalList,
          initEncounterId: initEncounterId || '',
        });
      }
      setIsOpenConfirmCreateSvSchein(filteredSchein.length == 0);
    }
  }, [
    patientFileStore.schein.originalList,
    patientFileStore.schein.activatedSchein,
    actionBarState?.encounterDate,
    currentBlock?.command,
    patientManagement?.getPatientParticipationResponse?.participations,
    isSuccessContract,
  ]);

  useEffect(() => {
    bindEncounterDateBySelectedSchein(
      patientFileStore.schein.activatedSchein,
      actionBarState?.encounterDate || 0
    );
  }, [JSON.stringify(patientFileStore.schein.activatedSchein)]);

  // NOTE: [set default catalog to action bar]
  useEffect(() => {
    setActionBarState((prevState) => ({
      ...prevState,
      catalog: defaultSetting?.defaultCatalog as IcdSearchCatalog,
      doctorSpecialist: defaultSetting?.defaultDoctorSpecialist,
    }));
  }, [defaultSetting?.defaultCatalog, defaultSetting?.defaultDoctorSpecialist]);

  useEffect(() => {
    if (!defaultValue) {
      const treatmentDoctor = doctorList.find(
        (doctor) =>
          doctor.id === patientFileStore?.schein?.activatedSchein?.doctorId
      );
      const doctorId = treatmentDoctor?.id || selectedContractDoctor.doctorId;
      const bsnrId = treatmentDoctor?.bsnrId || selectedContractDoctor.bsnrId;

      setActionBarState((prevState) => ({
        ...prevState,
        doctorId,
        bsnrId,
      }));
    }
  }, [
    selectedContractDoctor.doctorId,
    selectedContractDoctor.bsnrId,
    Boolean(defaultValue),
    patientFileStore?.schein?.activatedSchein,
    doctorList,
  ]);

  // NOTE: if has id, reuse it (update) | else create new encounter (create)
  useEffect(() => {
    if (defaultValue) {
      setCurrentBlock({ ...defaultValue });
      setActionBarState((prevState) => ({
        ...prevState,
        encounterCase: defaultValue?.encounterCase,
        encounterDate: defaultValue?.encounterDate,
        doctorId: defaultValue?.treatmentDoctorId || '',
        catalog: IcdSearchCatalog.SystematicAndAlphabetical,
        bsnrId: defaultValue.bsnrId,
      }));
    }
  }, [JSON.stringify(defaultValue)]);

  useEffect(() => {
    actionBarActions.setActionBarStore(actionBarState);
  }, [actionBarState]);

  const handleSetActionBarValues = useCallback(
    (
      key: keyof IActionBarStore,
      value: string | number,
      isPrivate: boolean
    ) => {
      setActionBarState((prevState) => {
        if (key === 'catalog') {
          prevState.doctorSpecialist = undefined;
        }
        if (key === 'encounterDate' && isPrivate) {
          return prevState;
        }
        return {
          ...prevState,
          [key]: value,
        };
      });
      if (key === 'encounterDate' && !isPrivate) {
        const ms = Number(`${value}`.padEnd(16, `0`));
        setCurrentBlock((prev) => ({
          ...prev,
          sortOrder: ms,
        }));
      }
    },
    [isPrivate]
  );

  const triggerBindEncounterDate = ({
    activeSchein,
    originalList,
    encounterDate,
    command,
    initEncounterId,
  }: {
    activeSchein: ScheinItem;
    originalList: ScheinItem[];
    encounterDate: number;
    command: string;
    initEncounterId: string;
  }) => {
    const {
      quarterOfEncounterDate,
      yearOfEncounterDate,
      currentQuarter,
      currentYear,
    } = getYearQuarterOfEncounterDateAndNow(encounterDate);
    if (
      ![...DiagnoseCommands, ServiceChainCommand].includes(command) ||
      !activeSchein ||
      originalList.filter((item) => !item?.markedAsBilled).length === 0
    ) {
      return;
    }

    const { quarter, year } =
      ComposerUtil.getValidQuarterAndYearInSchein(activeSchein);
    if (
      !initEncounterId &&
      quarter &&
      year &&
      (quarterOfEncounterDate !== quarter || yearOfEncounterDate !== year) &&
      (currentQuarter !== quarter || currentYear !== year)
    ) {
      const lastDayOfQuarter = +datetimeUtil.getEndOfSelectedQuarter(
        quarter,
        year
      );
      handleSetActionBarValues('encounterDate', lastDayOfQuarter, !!isPrivate);
    }
    reset();
  };

  const bindEncounterDateBySelectedSchein = (
    activeSchein: ScheinItem | undefined,
    encounterDate: number
  ) => {
    if (!activeSchein) return;
    const { quarter, year } =
      ComposerUtil.getValidQuarterAndYearInSchein(activeSchein);
    const {
      quarterOfEncounterDate,
      yearOfEncounterDate,
      currentQuarter,
      currentYear,
    } = getYearQuarterOfEncounterDateAndNow(encounterDate);
    if (
      !initEncounterId &&
      (quarterOfEncounterDate !== quarter || yearOfEncounterDate !== year) &&
      (currentQuarter === quarter || currentYear === year)
    ) {
      const currentDate = datetimeUtil.date();
      handleSetActionBarValues(
        'encounterDate',
        currentDate.getTime(),
        !!isPrivate
      );
    }
  };

  async function onDiagnoseChange(data: EncounterDiagnoseTimeline) {
    const freeText = `${data?.code ? `(${data.code}) ` : ''}${data?.description || ''
      }`;

    const type = DIAGNOSE_MAPPING[data.type] || data.type;

    // NOTE: if diagnose has code & certainty, reset runSdkrw status on every changes
    if (data.code && data.certainty) {
      data.runSdkrw = RunSdkrwEnum.RUNSDKRWENUM_DEFAULT;
    }

    if (
      !data?.code &&
      data?.description &&
      data?.description?.indexOf?.('-') !== -1 &&
      patientFileStore.schein.activatedSchein?.scheinId &&
      patientFileStore.schein.activatedSchein?.scheinMainGroup
    ) {
      const scheinMainGroup = [
        {
          scheinId: patientFileStore.schein.activatedSchein?.scheinId,
          group: patientFileStore.schein.activatedSchein?.scheinMainGroup,
        },
      ];

      // TODO: this case only occur on multi-row context, likely to change in the future
      const results = await DiagnoseBlockService.parseCombineDiagnoses(
        currentBlock,
        actionBarState?.encounterDate,
        data.description.split('-'),
        scheinMainGroup,
        actionBarState
      );
      const result = results?.[0];
      setCurrentBlock((prev) => ({ ...prev, ...result, type, freeText }));
      return;
    }

    setCurrentBlock((prev) => ({
      ...prev,
      ...data,
      freeText,
      type,
    }));
  }

  const getDefaultLanrAndBsnrForFavService = (scheinId: string) => {
    const { timelineState } = timelineStore;
    const now = datetimeUtil.date();
    const year = datetimeUtil.getYear(now);
    const quarter = datetimeUtil.getQuarter(now);

    const currentQuarterTimelines = timelineState.find(
      (state) => state.quarter === quarter && state.year === year
    )?.timelineModels;
    const timelinesWithSpecificSchien = (currentQuarterTimelines || []).filter(
      (timeline) => {
        if (isNil(timeline.encounterServiceTimeline)) {
          return false;
        }

        const scheins = timeline.encounterServiceTimeline.scheins;
        const hasSpecificSchein = (scheins || []).some(
          (schein) => schein.scheinId === scheinId
        );

        return hasSpecificSchein;
      }
    );
    if (timelinesWithSpecificSchien.length === 0) {
      return { lanr: '', bsnr: '' };
    }

    const lastDocumentedFavTimeline = maxBy(
      timelinesWithSpecificSchien,
      (timeline) => timeline.auditLogs[timeline.auditLogs.length - 1].date
    );
    const referralDoctorInfo =
      lastDocumentedFavTimeline?.encounterServiceTimeline?.referralDoctorInfo ||
      {};

    return { lanr: referralDoctorInfo.lanr, bsnr: referralDoctorInfo.bsnr };
  };

  async function onServiceChainChange(data: EncounterServiceChain) {
    setCurrentBlock((prev) => ({
      ...prev,
      ...data,
    }));
  }

  async function onServiceChange(data: EncounterServiceTimeline) {
    // NOTE: add default lanr & bsnr when CREATING a FAV service code
    const favSchein = data.scheins?.find(
      (schein) => schein.group === MainGroup.FAV
    );
    if (isNil(initEncounterId) && !isNil(favSchein)) {
      const { lanr, bsnr } = getDefaultLanrAndBsnrForFavService(
        favSchein.scheinId
      );

      setCurrentBlock((prev) => ({
        ...prev,
        ...data,
        referralDoctorInfo: {
          ...data.referralDoctorInfo,
          lanr: data?.referralDoctorInfo?.lanr || lanr,
          bsnr: data?.referralDoctorInfo?.bsnr || bsnr,
        },
      }));

      return;
    }

    setCurrentBlock((prev) => ({
      ...prev,
      ...data,
    }));
  }

  async function onGoaServiceChange(data: EncounterGoaService) {
    const freeText = data.freeText;
    setCurrentBlock((prev) => ({
      ...prev,
      ...data,
      freeText,
    }));
  }

  async function onUvGoaServiceChange(data: EncounterUvGoaService) {
    setCurrentBlock((prev) => ({
      ...prev,
      ...data,
    }));
  }

  const onRowTypeChange = (rowType: string, command: string) => {
    const isCommandService = SERVICECOMMANDS.includes(command);
    let type = rowType;
    if (isCommandService) {
      if (isPrivate) {
        type = BaseComposerRowType.GOASERVICE;
      } else if (isBg) {
        type = BaseComposerRowType.UV_GOA_SERVICE;
      }
    }
    deFocusCommandSelect();
    setCurrentBlock((prev) => ({
      ...prev,
      type,
      command,
    }));
  };

  const updateEncounterCaseForServiceEntries =
    useMutationUpdateEncounterCaseForServiceEntries();

  const handleUpdateEncounterCase = async (entry: TimelineModel) => {
    if (!entry?.id) {
      return;
    }

    const encounterCase = entry.encounterCase;
    const selectedSchein = scheinsFilteredByDate.find(
      (schein) => schein.scheinId === entry.scheinIds?.[0]
    );

    if (
      !selectedSchein ||
      !encounterCase ||
      !checkIsSvSchein(selectedSchein) ||
      ![EncounterCase.NOT].includes(encounterCase)
    ) {
      return;
    }

    updateEncounterCaseForServiceEntries.mutate({
      timelineId: entry.id || '',
    });
    reloadQuarters?.({
      year: entry.year,
      quarter: entry.quarter,
    });
  };

  async function onSubmit(_nextBlock?: IComposerRow) {
    const patientId = patient?.id;

    if (!patientId || isLoading) return;

    const now = datetimeUtil.date();

    if (
      !isNil(actionBarState?.encounterDate) &&
      actionBarState?.encounterDate > now.getTime()
    ) {
      return;
    }
    try {
      setLoading(true);
      const date = actionBarState.encounterDate
        ? new Date(actionBarState.encounterDate)
        : datetimeUtil.date();
      const year = date.getFullYear();
      const quarter = datetimeUtil.getQuarter(date);
      const payload = _nextBlock ?? currentBlock;
      if (!payload) {
        throw new Error('Payload is required');
      }

      const keyMapping = getKeyTimelineMapping(payload?.type);
      const scheins = payload?.scheins ?? [];
      const scheinIds = scheins
        .filter((s) => s.scheinId)
        .map((s) => s.scheinId);

      payload!.freeText = `${payload?.code ? `(${payload?.code}) ` : ''}${payload?.description
        }`;

      if (!initEncounterId && keyMapping === KEY_MAPPING_TIMELINE.DIAGNOSE) {
        payload.markedTreatmentRelevant = payload.command === 'DD';
      }
      const schein = (patientFileStore.schein.originalList ?? []).find(
        (s) => s.scheinId == scheinIds[0]
      );

      const commonPayload: TimelineModel = {
        patientId,
        billingDoctorId: actionBarState?.doctorId,
        contractId: schein?.hzvContractId,
        treatmentDoctorId: actionBarState?.doctorId || '',
        encounterCase: actionBarState?.encounterCase,
        createdAt: payload.createdAt || datetimeUtil.now(),
        auditLogs: [],
        [keyMapping]: payload,
        scheinIds,
        treatmentCase: TreatmentCase.TreatmentCaseCustodian, // TODO: check how to get current treatment case
        year,
        quarter,
        selectedDate: actionBarState?.encounterDate || 0,
        assignedToBsnrId: actionBarState?.bsnrId,
      };

      // PRO-12243
      if (
        commonPayload?.contractId &&
        ((commonPayload.encounterServiceChain?.services || []).some(
          (s) => s.code === 'PTZ5'
        ) ||
          commonPayload?.encounterServiceTimeline?.code === 'PTZ5')
      ) {
        const isTerminatedContract =
          patientFileStore.patient.currentContract?.status ===
          PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Terminated;

        if (!isTerminatedContract) {
          const selectedDate = +datetimeUtil.endOfSelectedDay(
            new Date(actionBarState?.encounterDate || 0)
          );

          musterFormDialogActions.setCurrentFormName(
            FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5
          );
          musterFormDialogActions.setCurrentMusterFormSetting({
            prevent_create_ptz5: true,
            disable_date_prescribe: true,
            date_label_custom_abmel: selectedDate,
            disable_date_label_custom_abmel: true,
          });
        }
      }

      if (keyMapping === KEY_MAPPING_TIMELINE.DIAGNOSE) {
        if (
          payload?.code == 'Z01.7' &&
          (payload?.command == 'DD' || payload?.command == 'DA')
        ) {
          alertError(t('actionCannotMarkAsPermanentDiagnose'));
          return;
        }
      }

      timelineActions.setPendingFetchNewData(true);
      if (initEncounterId) {
        if (!isNil(payload)) {
          commonPayload.billingDoctorId = undefined; // do not edit billdingDoctorId
          const result = await editTimelineItem({
            ...commonPayload,
            id: initEncounterId,
          });
          timelineActions.setTimelineIdCreated(result.timelineModel.id || '');
          handleUpdateEncounterCase(result.timelineModel);
          alertSuccessfully(t('timelineUpdated'));
        }
      } else {
        const result = await createTimelineItem({
          ...commonPayload,
        });
        timelineActions.setTimelineIdCreated(result.timelineModel.id || '');
        handleUpdateEncounterCase(result.timelineModel);
        alertSuccessfully(t('timelineCreated'));
      }

      setCurrentBlock(null);
      if (initEncounterId) {
        handleCloseEditInline?.();
      } else {
        focusOnCommandSelect();
      }
    } catch (e) {
      console.error(e);
      throw e;
    } finally {
      setLoading(false);
    }
  }

  const onClear = () => {
    // not allow clear type in edit mode
    if (defaultValue) {
      return;
    }
    setCurrentBlock(null);
    focusOnCommandSelect();
  };

  let isNoteType = false;

  function renderBlocks(command?: string, type?: string) {
    if (type === BaseComposerRowType.GOASERVICE) {
      return (
        <GoaServiceBlock
          disabled={false}
          patient={patient}
          defaultContract={contract}
          doctorProfile={doctorProfile || undefined}
          scheinsFilteredByDate={scheinsFilteredByDate.filter((schein) =>
            checkIsPrivateSchein(schein)
          )}
          data={currentBlock as EncounterGoaService}
          initEncounterId={initEncounterId}
          encounterDate={actionBarState?.encounterDate || 0}
          onChange={onGoaServiceChange}
          onSubmit={async (newServiceData) => {
            const additionalInfos = ServiceBlockService.handleFk5300(
              newServiceData?.additionalInfos ?? []
            );
            const latestCurrentBlock = {
              ...currentBlock,
              ...newServiceData,
              additionalInfos,
              type: BaseComposerRowType.GOASERVICE,
            } as IComposerRow;
            await onSubmit(latestCurrentBlock);
          }}
          openCreateSchein={(scheinMainGroup) => {
            openCreateSchein(scheinMainGroup);
            patientFileActions.schein.setIssueDate(
              actionBarState.encounterDate || 0
            );
          }}
          onClear={onClear}
        />
      );
    }
    if (type === BaseComposerRowType.UV_GOA_SERVICE) {
      return (
        <UvGoaServiceBlock
          disabled={false}
          patient={patient}
          defaultContract={contract}
          doctorProfile={doctorProfile || undefined}
          scheinsFilteredByDate={scheinsFilteredByDate.filter((schein) =>
            checkIsBgSchein(schein)
          )}
          data={currentBlock as EncounterUvGoaService}
          initEncounterId={initEncounterId}
          encounterDate={actionBarState?.encounterDate}
          onChange={onUvGoaServiceChange}
          onSubmit={async (newServiceData) => {
            const additionalInfos = ServiceBlockService.handleFk5300(
              newServiceData?.additionalInfos ?? []
            );
            const latestCurrentBlock = {
              ...currentBlock,
              ...newServiceData,
              additionalInfos,
              type: BaseComposerRowType.UV_GOA_SERVICE,
            } as IComposerRow;
            await onSubmit(latestCurrentBlock);
          }}
          openCreateSchein={(scheinMainGroup) => {
            openCreateSchein(scheinMainGroup);
          }}
          onClear={onClear}
        />
      );
    }
    if (type === BaseComposerRowType.SERVICE) {
      return (
        <ServiceBlock
          disabled={false}
          patient={patient}
          defaultContract={contract}
          doctorProfile={doctorProfile || undefined}
          scheinsFilteredByDate={scheinsFilteredByDate.filter(
            (schein) =>
              !checkIsPrivateSchein(schein) && !checkIsBgSchein(schein)
          )}
          currentSchein={selectedScheinInfo || undefined}
          data={currentBlock as EncounterServiceTimeline}
          initEncounterId={initEncounterId}
          encounterDate={actionBarState?.encounterDate}
          onChange={onServiceChange}
          onSubmit={async (newServiceData) => {
            const selectedSchein = patientFileStore.schein.list.find(
              (item) => item.scheinId === newServiceData.scheins?.[0]?.scheinId
            );
            const additionalInfos =
              ServiceBlockService.handleFk5098and5099ForKvPatient(
                newServiceData?.additionalInfos ?? [],
                treatmentDoctor,
                selectedSchein
              );
            const latestCurrentBlock = {
              ...currentBlock,
              ...newServiceData,
              additionalInfos,
            } as IComposerRow;
            await onSubmit(latestCurrentBlock);
          }}
          handleSetActionBarValues={(key: keyof IActionBarStore, value) =>
            handleSetActionBarValues(key, value, !!isPrivate)
          }
          openCreateSchein={openCreateSchein}
          onClear={onClear}
        />
      );
    }
    if (command === ServiceChainCommand) {
      if (isPrivate) {
        return (
          <GoaServiceChainBlock
            patient={patient}
            defaultContract={contract}
            doctorProfile={doctorProfile || undefined}
            scheinsFilteredByDate={scheinsFilteredByDate.filter((schein) =>
              checkIsPrivateSchein(schein)
            )}
            data={currentBlock as EncounterGoaServiceChain}
            initEncounterId={initEncounterId}
            encounterDate={actionBarState?.encounterDate}
            onSubmit={async (newServiceData) => {
              if (!newServiceData?.goaServices?.length) return;

              const firstService = newServiceData.goaServices[0];
              for (const goaService of newServiceData.goaServices) {
                const additionalInfos = ServiceBlockService.handleFk5300(
                  goaService?.additionalInfos ?? []
                );
                goaService.additionalInfos = additionalInfos;
              }
              const latestCurrentBlock = {
                ...newServiceData,
                createdAt: currentBlock?.createdAt,
                scheins: [
                  {
                    scheinId: firstService.scheins?.[0]?.scheinId,
                    group: firstService.scheins?.[0]?.group,
                  },
                ],
                type: BaseComposerRowType.GOA_SERVICE_CHAIN,
              } as IComposerRow;
              await onSubmit(latestCurrentBlock);
            }}
            openCreateSchein={openCreateSchein}
            onClear={onClear}
          />
        );
      }
      if (isBg) {
        return (
          <UvGoaServiceChainBlock
            doctorProfile={doctorProfile || undefined}
            scheinsFilteredByDate={scheinsFilteredByDate.filter((schein) =>
              checkIsBgSchein(schein)
            )}
            data={currentBlock as EncounterUvGoaServiceChain}
            initEncounterId={initEncounterId}
            encounterDate={actionBarState?.encounterDate}
            onSubmit={async (newServiceData) => {
              if (!newServiceData.uvGoaServices?.length) return;

              const firstService = newServiceData.uvGoaServices[0];
              for (const goaService of newServiceData.uvGoaServices) {
                const additionalInfos = ServiceBlockService.handleFk5300(
                  goaService?.additionalInfos ?? []
                );
                goaService.additionalInfos = additionalInfos;
              }
              const latestCurrentBlock = {
                ...newServiceData,
                createdAt: currentBlock?.createdAt,
                scheins: [
                  {
                    scheinId: firstService.scheins?.[0]?.scheinId,
                    group: firstService.scheins?.[0]?.group,
                  },
                ],
                type: BaseComposerRowType.UV_GOA_SERVICE_CHAIN,
              } as IComposerRow;
              await onSubmit(latestCurrentBlock);
            }}
            openCreateSchein={openCreateSchein}
            onClear={onClear}
          />
        );
      }
      return (
        <ServiceChainBlock
          disabled={false}
          patient={patient}
          defaultContract={contract}
          doctorProfile={doctorProfile || undefined}
          scheinsFilteredByDate={scheinsFilteredByDate.filter(
            (schein) =>
              !checkIsPrivateSchein(schein) && !checkIsBgSchein(schein)
          )}
          onChange={onServiceChainChange}
          data={currentBlock as EncounterServiceChain}
          initEncounterId={initEncounterId}
          encounterDate={actionBarState?.encounterDate}
          onSubmit={async (newServiceData) => {
            if (!newServiceData || !newServiceData.services?.length) {
              return;
            }
            const firstService = newServiceData.services[0];
            const selectedSchein = patientFileStore.schein.list.find(
              (item) => item.scheinId === firstService.scheins?.[0]?.scheinId
            );
            for (const service of newServiceData.services) {
              const additionalInfos =
                ServiceBlockService.handleFk5098and5099ForKvPatient(
                  service?.additionalInfos ?? [],
                  treatmentDoctor,
                  selectedSchein
                );
              service.additionalInfos = additionalInfos;
            }
            const isExistHgnc = newServiceData.services.some(service => service?.additionalInfos?.some(info => info.fK === '5077'));
            if (isExistHgnc) {
              const hgncSymbol = newServiceData.services.flatMap(service => !service?.additionalInfos?.length ? [] : service?.additionalInfos?.reduce((acc: string[], info: AdditionalInfoParent) => {
                if (info.fK === '5077') { // HGNC-Gensymbol
                  acc.push(info.value);
                }
                return acc;
              }, []))
              const isValidHgncSymbol = await checkValidHgncSymbol(hgncSymbol)
              if (!isValidHgncSymbol) {
                alertError(t('invalidHgncGenSymbol'));
                return;
              }
              const newServices: EncounterServiceTimeline[] = []
              for (const service of newServiceData.services) {
                const newAdditionalInfos = service.additionalInfos?.map((info) => {
                  if (info.fK != '5077') return info;
                  if (info.value != '999999') {
                    info.children = []
                  }
                  return info
                })
                service.additionalInfos = newAdditionalInfos
                newServices.push(service)
              }
              newServiceData.services = newServices

            }
            const latestCurrentBlock = {
              ...newServiceData,
              createdAt: currentBlock?.createdAt,
              scheins: [
                {
                  scheinId: firstService.scheins?.[0]?.scheinId,
                  group: firstService.scheins?.[0]?.group,
                },
              ],
              type: BaseComposerRowType.SERVICE_CHAIN,
            } as IComposerRow;
            await onSubmit(latestCurrentBlock);
          }}
          handleSetActionBarValues={(key: keyof IActionBarStore, value) =>
            handleSetActionBarValues(key, value, !!isPrivate)
          }
          openCreateSchein={openCreateSchein}
          onClear={onClear}
        />
      );
    }
    if (!command) {
      return null;
    }
    if (DiagnoseCommands.includes(command)) {
      return (
        <DiagnoseBlock
          initEncounterId={initEncounterId || ''}
          data={currentBlock as EncounterDiagnoseTimeline}
          scheinsFilteredByDate={scheinsFilteredByDate}
          actionBarValues={actionBarState}
          onSubmit={onSubmit}
          onClear={onClear}
          onChange={onDiagnoseChange}
          openCreateSchein={openCreateSchein}
          handleSetCatalog={(value) =>
            handleSetActionBarValues('catalog', value, !!isPrivate)
          }
          command={command}
        />
      );
    }
    if (ActionChainCommands.includes(command)) {
      return (
        <ActionChainBlock
          placeholder={t('typeActionChain')}
          scheinsFilteredByDate={scheinsFilteredByDate}
          onClear={onClear}
          autoFocus={true}
        />
      );
    }
    if (DmpCommands.includes(command)) {
      return <DMPBlock patient={patient} autoFocus onClear={onClear} />;
    }

    if (
      customizeDocumentTypes.some(
        (t) => t.isCustom && t.abbreviation === command
      ) &&
      currentBlock?.type !== BaseComposerRowType.GDT
    ) {
      return (
        <CustomizeInComposer
          command={command}
          fontsizeSetting={settingStore.timelineSetting.scaleNumber}
          defaultFreeText={currentBlock?.freeText}
          customizeDocumentTypes={customizeDocumentTypes}
          onSubmit={(freeText) => {
            const submitData = {
              ...currentBlock,
              type: BaseComposerRowType.CUSTOMIZE,
              description: freeText,
              command: command,
            } as IComposerRow;
            onSubmit(submitData);
          }}
          onClear={onClear}
        />
      );
    }

    isNoteType = true;
    return (
      <>
        <FreetextBlockInComposer
          command={command}
          fontsizeSetting={settingStore.timelineSetting.scaleNumber}
          defaultFreeText={currentBlock?.freeText}
          onSubmit={async (freeText) => {
            const submitData = {
              ...currentBlock,
              note: freeText,
            } as IComposerRow;
            await onSubmit(submitData);
            setShowHintSubmit(false);
          }}
          onClear={onClear}
          setShowHintSubmit={setShowHintSubmit}
        />
      </>
    );
  }

  function focusOnCommandSelect() {
    refComposer.current?.blurInput();
    refComposer.current?.focusInput();
    setIsAutoFocusOnCommand(true);
  }

  function deFocusCommandSelect() {
    refComposer.current?.blurInput();
    setIsAutoFocusOnCommand(false);
  }

  const onSelectBrief = () => {
    onSelectCommand?.(BaseComposerRowType.DOCTOR_LETTER, onClear);
  };

  const onChangeSelectCommand = (
    opt: ComposerCommandOption,
    actionMeta: ActionMeta<ComposerCommandOption>
  ) => {
    if (!opt) return;
    if (
      actionMeta.action === 'select-option' &&
      opt?.value &&
      typeof onSelectCommand !== 'undefined'
    ) {
      const shouldStop = onSelectCommand(opt.value.toString(), onClear);

      if (shouldStop === 'stop-propagate') {
        return;
      }
    }
    onRowTypeChange(opt.value.toString(), opt?.keywords?.[0] ?? '');
  };

  const block = renderBlocks(
    currentBlock?.command,
    // only render service block when edit
    defaultValue ? currentBlock?.type : null
  );

  if (timelineStore.isHistoryMode) {
    return null;
  }

  return (
    <Flex className={className} column>
      <Flex
        className="sl-blocks-wrapper"
        align="center"
        w={isNoteType ? '75%' : '100%'}
      >
        {currentBlock?.command ? (
          <Flex gap={4} align="flex-start" w="100%">
            <Flex style={{ minWidth: 'fit-content' }}>
              <RowTypeBlock
                command={currentBlock?.command}
                onRowTypeChange={onRowTypeChange}
              />
            </Flex>
            {block}
          </Flex>
        ) : (
          <>
            <Tooltip
              content={
                <StyledTooltipContent>
                  {commandTrans('showComposerCommands')}
                </StyledTooltipContent>
              }
              position="top"
            >
              <Button
                icon={<Svg src={AddIcon} size={16} />}
                small
                outlined
                style={{ marginRight: 2, border: 'none', outline: 'none' }}
                onClick={() => {
                  setIsCommandMenuOpen((prev) => {
                    if (!prev) {
                      refComposer.current?.focus();
                    }
                    return true;
                  });
                }}
              />
            </Tooltip>
            <Select<ComposerCommandOption>
              inputId="composer-id"
              autoFocus={isAutoFocusOnCommand}
              onBlur={() => {
                setIsCommandMenuOpen(false);
              }}
              ref={refComposer as any}
              value={
                currentBlock?.command
                  ? {
                    label: currentBlock?.command,
                    value: currentBlock?.command,
                    keywords: [],
                  }
                  : null
              }
              isMulti={false}
              openMenuOnFocus={false}
              openMenuOnClick={false}
              menuPlacement="top"
              placeholder={t('typeCommand')}
              options={options}
              menuIsOpen={isCommandMenuOpen}
              filterOption={(filter, query) => {
                const normalizedQuery = query.toLowerCase();
                const label = filter.label.toLowerCase();
                const exist =
                  filter.data.keywords?.find((command) =>
                    command.toLowerCase().includes(normalizedQuery)
                  ) !== undefined;
                return label.includes(normalizedQuery) || exist;
              }}
              onInputChange={(newValue) => {
                if (newValue) {
                  setIsCommandMenuOpen(true);
                }
                setSearchCommandText(newValue);
              }}
              noOptionsMessage={() => t('noResultsFound')}
              onChange={onChangeSelectCommand}
              styles={
                DEFAULT_COMMAND_SELECT_STYLE_CONFIG as StylesConfig<
                  ComposerCommandOption,
                  false,
                  GroupBase<ComposerCommandOption>
                >
              }
              components={{
                ...DEFAULT_COMMAND_SELECT_COMPONENT_CONFIG,
              }}
              onKeyDown={(e) => {
                if (
                  (e.key === 'Backspace' || e.key === 'Escape') &&
                  !searchCommandText
                ) {
                  setIsCommandMenuOpen(false);
                  return;
                }
                if (e.key === '+' && !searchCommandText) {
                  setIsCommandMenuOpen(true);
                  e.preventDefault();
                  return;
                }
                if (e.key === ' ') {
                  parseCommand(
                    searchCommandText,
                    excludedComposerCommands,
                    onRowTypeChange,
                    onSelectBrief,
                    customizeDocumentTypes
                  );
                  e.preventDefault();
                }
              }}
            />
          </>
        )}
        {isLoading && (
          <Spinner className="sl-loading" intent="primary" size={20} />
        )}
      </Flex>

      <ActionBar
        actionBarValues={actionBarState}
        doctorList={doctorList}
        command={currentBlock?.command}
        defaultSetting={defaultSetting}
        isDisabledEncounterCase={isDisabledEncounterCase || false}
        selectedScheinInfo={selectedScheinInfo || undefined}
        handleSetActionBarValues={(key: keyof IActionBarStore, value) =>
          handleSetActionBarValues(key, value, false)
        }
        onClear={onClear}
        stageCancel={stageCancel}
        setStageCancel={setStageCancel}
        isEditStage={!!defaultValue}
      />

      <InfoConfirmDialog
        title={createSvScheinTrans('title')}
        cancelText={createSvScheinTrans('cancelButton')}
        confirmText={createSvScheinTrans('confirmButton')}
        isOpen={isOpenConfirmCreateSvSchein}
        isShowIconTitle={false}
        isCloseButtonShown={false}
        onClose={() => {
          setIsOpenConfirmCreateSvSchein(false);
        }}
        disableConfirm={isLoadingCreateSvSchein}
        isLoading={isLoadingCreateSvSchein}
        onConfirm={async () => {
          const selectedDate = moment(
            new Date(actionBarState?.encounterDate || 0)
          );
          const participation =
            patientManagement?.getPatientParticipationResponse?.participations?.find(
              (p) =>
                p.contractId ===
                patientFileStore.schein.activatedSchein?.hzvContractId
            );
          if (
            await webWorkerServices.doesContractSupportFunctions(
              ['ABRG1007'],
              patientFileStore.schein.activatedSchein?.hzvContractId || ''
            )
          ) {
            if (
              patientFileStore.schein.activatedSchein?.hzvContractId !==
              'MEDI_FA_PT_BW' &&
              !availableCheckParticipation[selectedDate.format(DATE_FORMAT)]
            ) {
              openCreateSchein();
              return;
            }
          } else {
            if (
              typeof participation?.startDate === 'number' &&
              typeof actionBarState?.encounterDate === 'number' &&
              participation.startDate > actionBarState.encounterDate
            ) {
              openCreateSchein();
              return;
            }
          }
          mutate({
            referenceScheinIds: [
              patientFileStore.schein.activatedSchein?.scheinId || '',
            ],
            selectedDate: moment(new Date(actionBarState.encounterDate || 0))
              .toDate()
              .getTime(),
          });
        }}
      >
        {createSvScheinTrans('description')}
      </InfoConfirmDialog>
    </Flex>
  );
};

export default Composer;
