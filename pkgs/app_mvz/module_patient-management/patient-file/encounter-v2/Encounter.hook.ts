import { useState, useEffect } from 'react';
// import { EncounterDetailsResponse } from '@tutum/hermes/bff/service_domains_patient_file';
// import {
//   getSettings,
//   SettingsResponse,
// } from '@tutum/hermes/bff/app_mvz_user_settings';
// import { setEndOfDate } from '@tutum/design-system/infrastructure/utils';
import {
  // getEncounterDetail,
  getContractDoctorGroup,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { getPatientParticipation } from '@tutum/hermes/bff/legacy/app_mvz_patient_participation';
import { GetPatientParticipationRequest } from '@tutum/hermes/bff/service_domains_patient_participation';
import { IContractInfo } from '../../types/contract.type';
import {
  GetGroupByContractResponse,
  DoctorParticipateStatus,
} from '@tutum/hermes/bff/service_domains_doctor_participate';
import { Nullable } from '@tutum/design-system/infrastructure/models';
export class GetGroupContractDoctorRequest {
  contractIds: string[];
  encounterDate: number;
}

export const EncounterHook = {
  // useGetEncounterDetail: (
  //   request: GetEncounterDetailRequest
  // ): EncounterDetailsResponse => {
  //   const [result, setResult] = useState<EncounterDetailsResponse>(null);
  //   useEffect(() => {
  //     if (!request) {
  //       return;
  //     }
  //     const requestData: GetEncounterDetailRequest = {
  //       contractId: request.contractId,
  //       treatmentDoctorId: request.treatmentDoctorId,
  //       patientId: request.patientId,
  //       encounterDate: setEndOfDate(request.encounterDate).valueOf(),
  //       encounterCase: request.encounterCase,
  //     };
  //     getEncounterDetail(requestData).then((res) => {
  //       setResult({
  //         encounter: res.encounterDetail?.encounter ?? null,
  //         dataRows: res.encounterDetail?.dataRows ?? [],
  //       });
  //     });
  //   }, [request]);

  //   return result;
  // },
  useGetContractDoctorGroup: (
    request: GetGroupContractDoctorRequest | undefined
  ): GetGroupByContractResponse => {
    const [getGroupByContractResponse, setGetGroupByContractResponse] =
      useState<GetGroupByContractResponse>({
        doctorParticipateContracts: [],
      });

    useEffect(() => {
      if (!request?.contractIds || request.contractIds.length === 0) {
        setGetGroupByContractResponse({
          doctorParticipateContracts: [],
        });
        return;
      }
      getContractDoctorGroup({
        contractIds: [...request?.contractIds],
        doctorIds: [],
        statuses: [DoctorParticipateStatus.Active],
        time: request?.encounterDate,
      }).then((result) => {
        setGetGroupByContractResponse({ ...result.data });
      });
    }, [request]);

    return getGroupByContractResponse;
  },

  useGetContracts: (
    request: Nullable<GetPatientParticipationRequest>,
    triggered?: boolean
  ): [boolean, IContractInfo[]] => {
    const [loading, setLoading] = useState<boolean>(false);
    const [result, setResult] = useState<IContractInfo[]>([]);

    const [prevRequest, setPrevRequest] =
      useState<Nullable<GetPatientParticipationRequest>>(null);

    useEffect(() => {
      if (!request || !request.checkDate || !request.patientId) {
        return;
      }
      if (
        prevRequest?.checkDate === request.checkDate &&
        prevRequest?.patientId === request.patientId
      ) {
        return;
      }
      setPrevRequest(request);
      setLoading(true);
      getPatientParticipation({
        checkDate: request.checkDate,
        patientId: request.patientId,
      })
        .then(({ data: resp }) => {
          const newResult = resp.participations.map((participation) => {
            return {
              doctorId: String(participation.doctorId),
              id: participation.contractId,
              chargeSystemId: participation.chargeSystemId,
              role: participation.doctorFunctionType,
              status: participation.status,
              type: participation.contractType,
            };
          });

          // Only update the result if it has changed
          if (JSON.stringify(newResult) !== JSON.stringify(result)) {
            setResult(newResult);
          }

          setLoading(false);
        })
        .catch((error) => {
          console.error(error);
          setLoading(false);
        });
    }, [triggered, request?.patientId, request?.checkDate]);

    return [loading, result];
  },
};
