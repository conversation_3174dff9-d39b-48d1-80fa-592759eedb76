import { isEmpty, orderBy } from 'lodash';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type ScheinI18n from '@tutum/mvz/locales/en/Schein.json';

import {
  ButtonGroup,
  Flex,
  Svg,
  Tooltip,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Button,
  Divider,
  Intent,
  Popover,
} from '@tutum/design-system/components/Core';
import { useListenScheinChanged } from '@tutum/hermes/bff/app_mvz_schein';
import { ContractType } from '@tutum/hermes/bff/common';
import { ResponseType } from '@tutum/hermes/bff/legacy/api_client';
import { useQueryGetContractInformationFromMvz } from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import {
  useMutationUpdateInsurances,
  useQueryGetInsurancesByPatientId,
  useQueryGetPatientProfileById,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  useMutationSaveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_settings';
import {
  markNotBilled,
  useQueryGetTotalScheins,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { useQueryGetPsychotherapyBefore2020 } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';
import { MainGroup, ScheinItem } from '@tutum/hermes/bff/schein_common';
import { PatientEnrollmentInformationStatus } from '@tutum/hermes/bff/service_domains_enrollment';
import { PatientParticipation } from '@tutum/hermes/bff/service_domains_patient_participation';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { handleAutoDocument } from '@tutum/mvz/_utils/autoDocument';
import {
  ICustomInsuranceInfo,
  stageSelectedInsurance,
} from '@tutum/mvz/_utils/checkInsurance';
import {
  backgroundMainGroup,
  checkIsFavSchein,
  checkIsKvSchein,
  checkIsSvSchein,
  colorMainGroup,
} from '@tutum/mvz/_utils/scheinFormat';
import ScheinDetail from '@tutum/mvz/components/schein-component';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useErrorCodeI18n } from '@tutum/mvz/hooks/useErrorCode';
import { FFWrapper } from '@tutum/mvz/hooks/useFeatureFlag';
import useToaster from '@tutum/mvz/hooks/useToaster';
import ListInsurancesModal from '@tutum/mvz/module_insurance/ListInsurance.styled';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import {
  patientFileActions,
  patientFileStore,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import ActionsCell from '@tutum/mvz/module_patient-management/patient-file/schein-overview/actions-cell/ActionsCell.styled';
import ConfirmMarkNotBilled from '@tutum/mvz/module_patient-management/patient-file/schein-overview/modal/ConfirmMarkNotBilled.styled';
import DeleteScheinModal from '@tutum/mvz/module_patient-management/patient-file/schein-overview/modal/Delete.styled';
import type { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import type { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import reloadSchein from '../../utils/reloadSchein';
import { TypeOfInsurance } from '@tutum/hermes/bff/legacy/patient_profile_common';
import { InsuranceActionEnum } from '@tutum/mvz/module_insurance/ListInsurance.type';

const medicalData = '/images/patient/selector/shield-medical.svg';
const maximizeIcon = '/images/patient/selector/maximize-icon.svg';
const minimizeIcon = '/images/patient/selector/minimize-icon.svg';
const chevronDownIcon = '/images/chevron-down.svg';

export interface IScheinSelectorProps {
  className?: string;
  patient: IPatientProfile;
  openCreateSchein: (scheinId?: string) => void;
  patientParticipation?: PatientParticipation[];
  isBillingValidation?: boolean;
  reloadQuarters: ReloadQuarterFunc;
  setShowCreateSchein?: (scheinType: MainGroup, scheinId?: string) => void;
}

const ScheinSelector: React.ComponentType<IScheinSelectorProps> = (props) => {
  const {
    className,
    patient,
    openCreateSchein,
    patientParticipation,
    reloadQuarters,
    setShowCreateSchein,
  } = props;
  const shouldFocusCreateRef = useRef(false);

  const [scheinIdToDelete, setScheinIdToDelete] = useState<string | null>(null);
  const [showListInsurances, setshowListInsurances] = useState<boolean>(false);
  const [showFullSchein, setShowFullSchein] = useState<boolean>(false);
  const [openListSchein, setOpenListSchein] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [scheinMarkNotBilled, setScheinMarkNotBilled] = useState<ScheinItem>();
  const [initInsurances, setInitInsurances] = useState<ICustomInsuranceInfo[]>(
    []
  );
  const [insuranceAction, setInsuranceAction] = useState<InsuranceActionEnum>(
    InsuranceActionEnum.Create
  );
  const [isLoaded, setLoaded] = useState<boolean>(false);
  const currentSchein = useCurrentSchein();

  const { isContractSupport: hasSupportABRD609 } = useCheckContractSupport(
    ['ABRD609'],
    [currentSchein?.hzvContractId]
  );

  const { data: dataSettings, refetch: refetchSetting } = useQueryGetSettings({
    patientId: patient.id,
  });
  const { mutate: saveSetting } = useMutationSaveSettings({
    onSuccess: () => {
      refetchSetting();
    },
  });

  const { data: dataTotalScheins, isSuccess: isSuccessGetTotalSchein } =
    useQueryGetTotalScheins(
      {
        patientId: patient.id,
      },
      {
        enabled: !!patient.id,
      }
    );

  const matchedSetting = useMemo(() => {
    const year = currentSchein?.g4101Year;
    const quarter = currentSchein?.g4101Quarter;

    return (dataSettings?.settings || []).find(
      (setting) => setting.quarter === quarter && setting.year === year
    );
  }, [currentSchein, dataSettings?.settings]);

  const stageSettingInsurance = useMemo(() => {
    switch (patient?.patientInfo?.genericInfo?.patientType) {
      case PatientType.PatientType_Public:
        return stageSelectedInsurance.Public_patientProfile;
      case PatientType.PatientType_Private:
        return stageSelectedInsurance.Private_patientProfile;
      default:
        return undefined;
    }
  }, [patient?.patientInfo?.genericInfo?.patientType]);

  const toaster = useToaster();

  const { t } = I18n.useTranslation<keyof typeof ScheinI18n.scheinBlock>({
    namespace: 'Schein',
    nestedTrans: 'scheinBlock',
  });

  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });
  const tError = useErrorCodeI18n();

  const {
    patientManagement,
    setGetPatientParticipationResponse,
    reloadSelectedContractDoctor,
  } = useContext(PatientManagementContext.instance);

  const globalContext = useContext(GlobalContext.instance);

  const store = usePatientFileStore();

  const { refetch } = useQueryGetPsychotherapyBefore2020(
    {
      patientId: patient.id,
    },
    {
      enabled: false,
    }
  );

  const { data: patientProfile, refetch: refetchProfile } =
    useQueryGetPatientProfileById(
      {
        id: patient.id,
      },
      {
        enabled: !!patient?.id && showListInsurances,
      }
    );

  const { isSuccess, refetch: refetchInsurance } =
    useQueryGetInsurancesByPatientId(
      {
        patientId: patient?.id,
      },
      {
        enabled: Boolean(patient?.id),
        select: (data) => data?.data?.insuranceInfos ?? [],
      }
    );

  const { mutate: updateInsurance } = useMutationUpdateInsurances({
    onSuccess: () => {
      closeShowListInsurancesModal();
      refetchProfile();
      refetchInsurance();
      alertSuccessfully(t('updateInsuranceSuccess'));
    },
    onError: (error) => {
      alertError(tError(error.response.data.serverError as any));
    },
    throwOnError: false,
  });

  const {
    data: dataGetContractInformationHzv,
    refetch: refetchGetContractInformationHzv,
  } = useQueryGetContractInformationFromMvz(
    {
      contractType: ContractType.ContractType_HouseDoctorCare,
      patientId: patient?.id,
    },
    {
      enabled: !!patient?.id && !isEmpty(patientFileStore.activeInsurance),
      select: (res) => {
        return res.data.contractInformations[0];
      },
    }
  );

  const {
    data: dataGetContractInformationFav,
    refetch: refetchGetContractInformationFav,
  } = useQueryGetContractInformationFromMvz(
    {
      contractType: ContractType.ContractType_SpecialistCare,
      patientId: patient?.id,
    },
    {
      enabled: !!patient?.id && !isEmpty(patientFileStore.activeInsurance),
      select: (res) => {
        return res.data.contractInformations?.length > 0
          ? res.data.contractInformations[0]
          : undefined;
      },
    }
  );

  const {
    schein: { loading: loadingStore, activatedSchein },
  } = store;

  useEffect(() => {
    if (!isEmpty(patientFileStore.activeInsurance)) {
      refetchGetContractInformationHzv();
      refetchGetContractInformationFav();
    }
  }, [
    patientFileStore.activeInsurance?.ikNumber,
    patientFileStore.activeInsurance?.insuranceNumber,
  ]);

  useEffect(() => {
    setLoading(loadingStore);
  }, [loadingStore]);

  useListenScheinChanged(async (response) => {
    if (!response) {
      return;
    }
    if (patient.id === response.data?.patientId) {
      setLoading(true);
      const { patientParticipation } = await reloadSchein(patientManagement);
      setGetPatientParticipationResponse(patientParticipation);
      reloadQuarters({
        quarter: activatedSchein?.g4101Quarter as number,
        year: activatedSchein?.g4101Year as number,
      });
      refetch();
      setLoading(false);
    }
  });

  const getScheinsOverview = async () => {
    await patientFileActions.schein.getScheinsOverview(patient.id);
  };

  useEffect(() => {
    getScheinsOverview();
  }, [patient.id]);

  const onOpenCreateSchein = (type: MainGroup, scheinId?: string) => {
    setShowCreateSchein?.(type, scheinId);
  };

  const doctorList = globalContext.useGetDoctorList();

  const setDefaultSchein = async (scheins: ScheinItem[]) => {
    const lastSelectedScheinSetting = store.lastSelectedScheinSetting;
    const lastSelectedScheinId =
      lastSelectedScheinSetting && lastSelectedScheinSetting[patient.id];

    let activeSchein = scheins.find(
      (item) => item.scheinId === lastSelectedScheinId && !item.markedAsBilled
    );

    if (!activeSchein) {
      const scheinsSort = orderBy(
        scheins,
        ['g4101Year', 'g4101Quarter', 'createdTime'],
        ['desc', 'desc', 'desc']
      );
      activeSchein = scheinsSort.find((item) => !item.markedAsBilled);
    }

    patientFileActions.schein.setActivatedSchein(
      activeSchein,
      patientParticipation
    );

    const resDocument = await handleAutoDocument(
      activeSchein!,
      patient.id,
      globalContext.globalData.userProfile?.bsnr!
    );

    if (resDocument.error) {
      alertError(tCommon('autoDoucumentFailed'), { toaster });
    } else {
      if (!!resDocument.serviceCode) {
        alertSuccessfully(
          tCommon('autoDocumentSuccess', {
            code: resDocument.serviceCode,
          }),
          { toaster }
        );
        reloadQuarters({
          quarter: activeSchein?.g4101Quarter as number,
          year: activeSchein?.g4101Year as number,
        });
      }
    }

    reloadSelectedContractDoctor(
      patient.id,
      datetimeUtil.date(),
      activeSchein!,
      doctorList
    );
  };

  const onSelectCreateSchein = (type: string, scheinId?: string) => {
    setOpenListSchein(false);
    switch (type) {
      case MainGroup.PRIVATE:
        onOpenCreateSchein(MainGroup.PRIVATE, scheinId);
        break;
      case MainGroup.IGEL:
        onOpenCreateSchein(MainGroup.IGEL, scheinId);
        break;
      case MainGroup.BG:
        onOpenCreateSchein(MainGroup.BG, scheinId);
        break;
      case MainGroup.HZV:
        onOpenCreateSchein(MainGroup.HZV, scheinId);
        break;
      case MainGroup.FAV:
        onOpenCreateSchein(MainGroup.FAV, scheinId);
        break;
      default:
        break;
    }
  };

  const getListSchein = () => {
    const listSchein = store.schein?.originalList.filter(
      (schein: ScheinItem) => {
        if (checkIsSvSchein(schein)) {
          return (patientParticipation || []).some(
            (participation: PatientParticipation) =>
              participation.contractId === schein.hzvContractId
          );
        }
        return true;
      }
    );
    return listSchein;
  };

  useEffect(() => {
    if (store.schein?.originalList?.length > 0) {
      const scheins = getListSchein();
      patientFileActions.schein.setScheins(scheins);
      if (!store.schein.activatedSchein) {
        setDefaultSchein(scheins);
      }
    } else {
      patientFileActions.schein.setScheins([]);
    }
  }, [
    store.schein?.originalList.length,
    patientParticipation,
    store.schein.activatedSchein,
  ]);

  const closeShowListInsurancesModal = () => {
    setshowListInsurances(false);
    setOpenListSchein(false);
  };

  const closeDeleteScheinModal = () => {
    setScheinIdToDelete(null);
  };

  const onSuccessDeleteSchein = () => {
    setLoading(true);
    reloadSchein(patientManagement).then(({ scheins }) => {
      const activeScheins = scheins.find((s) => !s.markedAsBilled);
      patientFileActions.schein.setActivatedSchein(
        activeScheins,
        patientParticipation
      );
      setLoading(false);
    });
  };

  const onSaveNewInsurances = async (
    items: ICustomInsuranceInfo[],
    insuranceDeleted: ICustomInsuranceInfo[]
  ) => {
    setInitInsurances(items);
    updateInsurance({
      patientId: patient.id,
      insuranceInfos: items,
      insuranceInfosDeleted: insuranceDeleted,
    });
  };

  const closeConfirmMarkNotBilled = () => {
    setScheinMarkNotBilled(undefined);
  };

  const onSaveAutoSetting = () => {
    saveSetting({
      patientId: patient.id,
      settings: [
        {
          ...(matchedSetting || {
            favContractOnlineChecked: [],
            year: currentSchein?.g4101Year as number,
            quarter: currentSchein?.g4101Quarter as number,
          }),
          openedTakeoverDiagnose: true,
        },
      ],
    });
  };

  const renderActionCells = (selectedSchein: ScheinItem) => {
    const { scheinMainGroup, scheinId } = selectedSchein || {};
    const isSVSchein = checkIsSvSchein(selectedSchein);
    const isKVSchein = checkIsKvSchein(selectedSchein);
    const svScheins = store.schein?.originalList?.filter((schein) =>
      checkIsSvSchein(schein)
    );
    const hasOnlyOneSVSchein = svScheins?.length === 1;
    const getEditFunc = () => {
      if (isSVSchein)
        return () => onOpenCreateSchein(scheinMainGroup, scheinId);
      if (isKVSchein) return () => openCreateSchein(scheinId);
      return () => onSelectCreateSchein(scheinMainGroup, scheinId);
    };

    const getRemoveFunc = () => {
      if (isSVSchein && hasOnlyOneSVSchein) return null;
      return () => setScheinIdToDelete(scheinId);
    };

    return (
      <ActionsCell
        className="option"
        scheinItem={selectedSchein}
        isAutoOpenTakeoverDiagnose={
          !isLoaded &&
          hasSupportABRD609 &&
          !matchedSetting?.openedTakeoverDiagnose &&
          selectedSchein.scheinId === currentSchein?.scheinId
        }
        edit={getEditFunc()}
        remove={getRemoveFunc()}
        reloadQuarters={reloadQuarters}
        openInsuranceList={(_, isPrivateSchein: boolean) =>
          handleShowListInsurance(true, isPrivateSchein)
        }
        onSaveAutoSetting={onSaveAutoSetting}
      />
    );
  };

  const onConfirmMarkNotBilled = async (scheinItem: ScheinItem) => {
    const res: ResponseType<any> = await markNotBilled({
      scheinId: scheinItem.scheinId,
    });

    if (res.status === 200) {
      alertSuccessfully(t('scheinReopenSuccess'));
      const scheins = await patientFileActions.schein.getScheinsOverview(
        patient.id
      );
      // This is to refresh the quarter list for get the mecine prescription from entry fetch by quarter
      if (checkIsFavSchein(scheinItem) || checkIsSvSchein(scheinItem)) {
        reloadQuarters({
          quarter: scheinItem?.g4101Quarter as number,
          year: scheinItem?.g4101Year as number,
        });
      }

      patientFileActions.schein.setActivatedSchein(
        scheins.find((s) => s.scheinId == scheinItem.scheinId),
        patientParticipation
      );
    }
  };

  const onSelectSchein = async (selectedSchein: ScheinItem) => {
    patientFileActions.schein.setActivatedSchein(selectedSchein);
    setLoaded(true);
    await handleAutoDocument(
      selectedSchein,
      patient.id,
      globalContext.globalData.userProfile?.bsnr!
    );
  };

  const onClickOpenCreateSchein = () => {
    const patientType = patient.patientInfo.genericInfo.patientType;
    if (patientType === PatientType.PatientType_Private) {
      onOpenCreateSchein(MainGroup.PRIVATE);
      return;
    }
    openCreateSchein();
  };

  const handleShowListInsurance = (
    isOpenEdit: boolean,
    isPrivateSchein: boolean
  ) => {
    shouldFocusCreateRef.current = !isOpenEdit;

    const isPrivatePatient =
      patient?.patientInfo?.genericInfo?.patientType ===
      PatientType.PatientType_Private;
    if (stageSettingInsurance) {
      stageSettingInsurance.isDisablePublic =
        isPrivatePatient || (isOpenEdit && isPrivateSchein);
    }
    setshowListInsurances(true);
    setInsuranceAction(
      isOpenEdit ? InsuranceActionEnum.Edit : InsuranceActionEnum.Create
    );
    setInitInsurances(
      patientProfile?.patientInfo?.insuranceInfos.map(
        (i) => ({ ...i } as ICustomInsuranceInfo)
      ) || []
    );
  };

  const contentFullScheinPopover = () => {
    return (
      <Flex column className="wrapper-pop-up">
        <Flex className="container-schein">
          <Flex className="section-title-schein">
            <div className="title-schein-quarter">
              {t('titleScheinHistory')}
            </div>
            <div className="total-quarter">
              {t('numberQuarter', {
                totalQuarter: isSuccessGetTotalSchein
                  ? dataTotalScheins.totalScheins
                  : 0,
              })}
            </div>
          </Flex>
          <div className="group-icon">
            <Tooltip content={t('tooltipCloseFullSchein')}>
              <Svg
                src={minimizeIcon}
                style={{ cursor: 'pointer' }}
                height={16}
                width={16}
                onClick={() => setShowFullSchein(false)}
              />
            </Tooltip>
          </div>
        </Flex>
        <ScheinDetail
          className="schein-detail"
          listScheinQuarters={store.schein.scheinGroupByQuarter}
          isNeedActiveSchein={false}
          menuItem={renderActionCells}
          openCreateSchein={onClickOpenCreateSchein}
          setScheinIdToDelete={setScheinIdToDelete}
          onSelectSchein={onSelectSchein}
          onConfirmMarkNotBilled={onConfirmMarkNotBilled}
          onCloseMarkNotBilled={closeConfirmMarkNotBilled}
          scheinSelected={store.schein.activatedSchein as ScheinItem}
          onOpenInsuranceModal={handleShowListInsurance}
        />
      </Flex>
    );
  };

  const elementSelectListSchein = () => {
    return (
      <Popover
        data-test-id="pop-up-list-schein"
        isOpen={openListSchein}
        content={
          <Flex column p="8px 16px" gap={8}>
            {patient.patientInfo.genericInfo.patientType ===
              PatientType.PatientType_Public && (
              <Flex
                gap={8}
                pb={8}
                className="cursor-pointer"
                onClick={() => {
                  setOpenListSchein(false);
                  openCreateSchein();
                }}
                data-test-id="btn-create-schein-kv"
                align="center"
              >
                <Flex
                  p={2}
                  className="main-group"
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '4px',
                    width: '24px',
                    height: '24px',
                    fontSize: '11px',
                    lineHeight: '16px',
                    backgroundColor: backgroundMainGroup(MainGroup.KV),
                    color: colorMainGroup(MainGroup.KV),
                  }}
                >
                  KV
                </Flex>
                <div className="text-lbl">{t('lblNewKvSchein')}</div>
              </Flex>
            )}
            <Flex
              gap={8}
              pb={8}
              className="cursor-pointer"
              onClick={() => onSelectCreateSchein(MainGroup.PRIVATE)}
              data-test-id="btn-create-schein-private"
              align="center"
            >
              <Flex
                p={2}
                className="main-group"
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: '4px',
                  width: '24px',
                  height: '24px',
                  fontSize: '11px',
                  lineHeight: '16px',
                  backgroundColor: backgroundMainGroup(MainGroup.PRIVATE),
                  color: colorMainGroup(MainGroup.PRIVATE),
                }}
              >
                P
              </Flex>
              <div className="text-lbl">{t('lblNewPrivateSchein')}</div>
            </Flex>
            <Flex
              gap={8}
              pb={8}
              className="cursor-pointer"
              onClick={() => onSelectCreateSchein(MainGroup.IGEL)}
              data-test-id="btn-create-schein-igel"
              align="center"
            >
              <Flex
                p={2}
                className="main-group"
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: '4px',
                  width: '24px',
                  height: '24px',
                  fontSize: '11px',
                  lineHeight: '16px',
                  backgroundColor: backgroundMainGroup(MainGroup.IGEL),
                  color: colorMainGroup(MainGroup.IGEL),
                }}
              >
                IG
              </Flex>
              <div className="text-lbl">{t('lblNewIgelSchein')}</div>
            </Flex>

            {dataGetContractInformationHzv?.status ===
              PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Enrolled && (
              <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_SV}>
                <Flex
                  gap={4}
                  pb={8}
                  className="cursor-pointer"
                  onClick={() => onSelectCreateSchein(MainGroup.HZV)}
                  data-test-id="btn-create-schein-hzv"
                >
                  <Flex
                    p={2}
                    className="main-group"
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: '4px',
                      width: '28px',
                      fontSize: '11px',
                      lineHeight: '16px',
                      backgroundColor: backgroundMainGroup(MainGroup.HZV),
                      color: colorMainGroup(MainGroup.HZV),
                    }}
                  >
                    HZV
                  </Flex>
                  <div className="text-lbl">{t('lblNewHzvSchein')}</div>
                </Flex>
              </FFWrapper>
            )}

            {dataGetContractInformationFav?.status ===
              PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Enrolled && (
              <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_SV}>
                <Flex
                  gap={4}
                  pb={8}
                  className="cursor-pointer"
                  onClick={() => onSelectCreateSchein(MainGroup.FAV)}
                  data-test-id="btn-create-schein-fav"
                >
                  <Flex
                    p={2}
                    className="main-group"
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: '4px',
                      width: '28px',
                      fontSize: '11px',
                      lineHeight: '16px',
                      backgroundColor: backgroundMainGroup(MainGroup.FAV),
                      color: colorMainGroup(MainGroup.FAV),
                    }}
                  >
                    FaV
                  </Flex>
                  <div className="text-lbl">{t('lblNewFavSchein')}</div>
                </Flex>
              </FFWrapper>
            )}

            <Flex
              gap={8}
              style={{ cursor: 'pointer' }}
              onClick={() => onSelectCreateSchein(MainGroup.BG)}
              data-test-id="btn-create-schein-bg"
              align="center"
            >
              <Flex
                className="main-group"
                p={2}
                style={{
                  justifyContent: 'center',
                  borderRadius: '4px',
                  alignItems: 'center',
                  width: '24px',
                  height: '24px',
                  fontSize: '11px',
                  lineHeight: '16px',
                  backgroundColor: backgroundMainGroup(MainGroup.BG),
                  color: colorMainGroup(MainGroup.BG),
                }}
              >
                {MainGroup.BG}
              </Flex>
              <div className="text-lbl">{t('lblNewBgSchein')}</div>
            </Flex>

            <Divider />
            <Flex
              gap={8}
              className="cursor-pointer"
              onClick={() => handleShowListInsurance(false, false)}
              data-test-id="btn-create-insurance"
            >
              <Flex
                align="center"
                justify="center"
                style={{
                  width: '24px',
                }}
              >
                <Svg
                  src={medicalData}
                  height={20}
                  width={20}
                  style={{ cursor: 'pointer' }}
                />
              </Flex>
              <div className="text-lbl">{t('lblNewInsurance')}</div>
            </Flex>
          </Flex>
        }
        enforceFocus={false}
        onClose={() => setOpenListSchein(false)}
        canEscapeKeyClose
      >
        <Svg
          data-test-id="btn-open-list-schein"
          className="icon-list-schein"
          src={chevronDownIcon}
          style={{ padding: '4px' }}
          onClick={() => setOpenListSchein(true)}
        />
      </Popover>
    );
  };

  return (
    <>
      <div className={className}>
        {loading && (
          <Button
            loading
            large
            minimal
            intent={Intent.NONE}
            className="action-button"
            style={{
              marginLeft: '0px',
              marginRight: '0px',
              backgroundColor: 'transparent',
            }}
          />
        )}
        <div className="wrapper">
          <Flex column>
            <Flex className="container-schein">
              <Flex className="section-title-schein">
                <div className="title-schein-quarter">
                  {t('titleScheinHistory')}
                </div>
                <div className="total-quarter">
                  {t('numberQuarter', {
                    totalQuarter: isSuccessGetTotalSchein
                      ? dataTotalScheins.totalScheins
                      : 0,
                  })}
                </div>
              </Flex>
              <div className="group-icon">
                <Popover
                  className="full-schein-popover"
                  isOpen={showFullSchein}
                  position="right"
                  usePortal={false}
                  autoFocus={false}
                  enforceFocus={false}
                  minimal
                  onClose={() => setShowFullSchein(false)}
                  canEscapeKeyClose
                  content={contentFullScheinPopover()}
                >
                  <Tooltip content={t('toolTipFullSchein')}>
                    <Svg
                      data-test-id="btn-show-full-schein"
                      src={maximizeIcon}
                      style={{ cursor: 'pointer' }}
                      height={16}
                      width={16}
                      onClick={() => setShowFullSchein(true)}
                    />
                  </Tooltip>
                </Popover>
                <ButtonGroup
                  data-test-id="lbl-new-schein-default"
                  content={t('lblNew')}
                  className="bp5-small wrapper-list-schein"
                  elementRight={elementSelectListSchein()}
                  onClickButton={onClickOpenCreateSchein}
                />
              </div>
            </Flex>
            <ScheinDetail
              className="schein-detail"
              listScheinQuarters={store.schein.activeScheinGroupByQuarter}
              isNeedActiveSchein={true}
              menuItem={renderActionCells}
              openCreateSchein={onClickOpenCreateSchein}
              setScheinIdToDelete={setScheinIdToDelete}
              onSelectSchein={onSelectSchein}
              scheinSelected={store.schein.activatedSchein as ScheinItem}
              onOpenInsuranceModal={handleShowListInsurance}
            />
          </Flex>
        </div>
      </div>

      <DeleteScheinModal
        id={scheinIdToDelete}
        patientId={patient.id}
        onClose={closeDeleteScheinModal}
        onSuccess={onSuccessDeleteSchein}
      />

      <ConfirmMarkNotBilled
        scheinItem={scheinMarkNotBilled}
        onConfirm={onConfirmMarkNotBilled}
        onClose={closeConfirmMarkNotBilled}
      />

      {isSuccess && showListInsurances && (
        <ListInsurancesModal
          className="sl-ListInsurancesModal"
          show={showListInsurances}
          onClose={closeShowListInsurancesModal}
          patient={patient}
          initInsurance={initInsurances}
          onSubmit={onSaveNewInsurances}
          stateSelectInsurance={stageSettingInsurance!}
          scenario={{
            action: insuranceAction,
            focusInsuranceId: store.schein?.activatedSchein?.insuranceId || '',
          }}
        />
      )}
    </>
  );
};

export default ScheinSelector;
