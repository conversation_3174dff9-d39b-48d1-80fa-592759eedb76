import { debounce } from 'lodash';
import moment from 'moment';
import { useEffect } from 'react';
import { proxy, useSnapshot } from 'valtio';
import { proxyWithComputed } from 'valtio/utils';

import type { MaterialCostCatalog } from '@tutum/hermes/bff/legacy/catalog_material_cost_common';
import type {
  CreateHistoryRequest,
  GetLatestDoctorViewRequest,
} from '@tutum/hermes/bff/app_mvz_patient_log';
import type { EntryHistory } from '@tutum/hermes/bff/patient_log_common';

import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  FileContentDetail,
  GroupErrorByPatientDetailError,
} from '@tutum/hermes/bff/billing_kv_common';
import { useQueryGetPatientProfileById } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { getScheinsOverview } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { TherapiesResponse } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import {
  SettingsKey,
  getSettings,
  saveSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_user_settings';
import { PatientInfo } from '@tutum/hermes/bff/legacy/patient_profile_common';
import { PointValueModel } from '@tutum/hermes/bff/legacy/point_value_common';
import {
  GenericInfo,
  InsuranceInfo,
} from '@tutum/hermes/bff/patient_profile_common';
import { ScheinStatus, ScheinItem } from '@tutum/hermes/bff/schein_common';
import { ContractInformation } from '@tutum/hermes/bff/service_domains_enrollment';
import { PatientParticipation } from '@tutum/hermes/bff/service_domains_patient_participation';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  createHistory,
  getLatestDoctorView,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_log';
import { startIndexingMaterialCost } from '@tutum/infrastructure/web-worker-services/service';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { checkIsSvSchein } from '@tutum/mvz/_utils/scheinFormat';
import { ICustomInsuranceInfo } from '../create-patient-v2/CreatePatient.helper';
import { IContractInfo } from '../types/contract.type';
import { IPatientProfile } from '../types/profile.type';
import { catalogOverviewActions } from './CatalogsOverview.store';
import { groupQuarterbySchein } from './PatientFile.helper';
import { ID_TABS, QuarterGroup } from './PatientFile.type';
import { DigaDetail } from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { timelineActions } from './timeline/Timeline.store';
import { IcdSearchCatalog } from '@tutum/infrastructure/masterdata-service/type';
import { EbmSearchItem } from '@tutum/hermes/bff/legacy/catalog_sdebm_common';

export interface IErrorCode {
  entryId: string;
  errorCode: string;
}
export interface IPatientFileStore {
  preventRedirectCreatePatient: boolean;
  createPatient: PatientInfo | null;
  activeTabId: ID_TABS;
  params: { [key: string]: string };
  patient: {
    isReadingCard: boolean;
    current: IPatientProfile | null;
    activeParticipations: PatientParticipation[];
    currentContract: ContractInformation | null;
  };
  schein: IScheinPatientFileStore;
  isSvPatient: boolean;
  serviceCodeData: Omit<EbmSearchItem, 'id'>[] | null;
  materialCosts: {
    list: MaterialCostCatalog[];
  };
  isEditingProfile: boolean | string;
  // TODO: duplicate data to maintainence. remove and use from encounter store
  currentPatientGenericInfo?: GenericInfo;
  // NOTE: patient available contracts
  availableContracts: IContractInfo[];
  insurances: ICustomInsuranceInfo[];
  activeInsurance?: InsuranceInfo;
  barcodeMedicationPlanContent: string;
  sessionId: string;
  isSdvaIndexed: boolean;
  currentQuarter: string;
  continuePatient: string;
  therapies: TherapiesResponse[];
  terminalId?: string;
  listErrors: FileContentDetail[];
  listHints: GroupErrorByPatientDetailError[];
  pointValue: PointValueModel | undefined;
  lastSelectedScheinSetting: { [key: string]: string } | null;
  skippedErrorKeyList: IErrorCode[];
  defaultCatalog: IcdSearchCatalog;
  defaultDoctorSpecialist?: string;
  appointmentNotifyCount: number;
  digaDetail: DigaDetail | undefined;
  history: {
    latestViewer: Nullable<EntryHistory>;
    listHistory: Nullable<EntryHistory[]>;
  };
  isOpenSchein: boolean;
  defaultDocumentDate: number | null;
}

export interface IPatientFileActions {
  setPreventRedirectCreatePatient: (isPrevent: boolean) => void;
  setCreatePatient: (createPatient: PatientInfo | null) => void;
  setActiveTabId: (tabId: ID_TABS) => void;
  params: {
    set: (key: string, value: string) => void;
    get: (key: string) => string;
  };
  patient: {
    setReadingCard: (isReadingCard: boolean) => void;
    setCurrent: (patient: IPatientProfile) => void;
    setActiveParticipations: (
      activeParticipations: PatientParticipation[]
    ) => void;
    setPatientInfo: (patientInfo: PatientInfo) => void;
    setCurrentContract: (contract: ContractInformation) => void;
  };
  schein: {
    getListScheins: () => ScheinItem[];
    setActivatedSchein: (
      shein: ScheinItem | undefined,
      participations?: PatientParticipation[],
      patientId?: string
    ) => void;
    groupSchein: (
      scheins: ScheinItem[],
      insuranceInfo: InsuranceInfo[]
    ) => void;
    getScheinsOverview: (patientId: string) => Promise<ScheinItem[]>;
    setScheins: (scheins: ScheinItem[]) => void;
    setOriginalList: (scheins: ScheinItem[]) => void;
    setLoading: (data: boolean) => void;
    resetSchein: () => void;
    setFocusMode: (isFocusMode: boolean) => void;
    setIssueDate: (issueDate: number) => void;
  };
  resetStore: () => void;
  materialCosts: {
    getMaterialCosts: () => void;
  };
  setIsEditPatientProfile: (isEdit: boolean | string) => void;
  // NOTE: contracts
  setAvailableContracts: (contracts: IContractInfo[]) => void;
  getAvailableContractById: (
    contractId: string | undefined
  ) => IContractInfo | null;
  loadUserSettings: () => void;
  setBarcodeMedicationPlanContent: (
    barcodeContent: string,
    sessionId: string,
    continuePatient: string
  ) => void;
  setSdvaIndexed: (isIndexed: boolean) => void;
  setTerminalId: (id?: string) => void;
  setListErrors: (errors: FileContentDetail[]) => void;
  setListHints: (hints: GroupErrorByPatientDetailError[]) => void;
  setPointValue: (payload: PointValueModel) => void;
  setIsOpenSchein: (isOpenSchein: boolean) => void;
  setDefaultDocumentDate: (documentDate: number | null) => void;
  reloadPatientProfile: () => void;
  saveSkippedErrorKeyList: (errorKeyList: IErrorCode[]) => void;
  saveDefaultCatalog: (
    catalog: IcdSearchCatalog,
    doctorSpecialist?: string
  ) => void;
  setAppointmentNotifyCount: (count: number) => void;
  setDigaDetail: (id: DigaDetail | undefined) => void;
  resetStateWhenChangePatient: () => void;
  setHistoryViewPatient: (payload: CreateHistoryRequest) => void;
  getLatestView: (payload: GetLatestDoctorViewRequest) => Promise<void>;
}
export interface IScheinPatientFileStore {
  insuranceInfos: InsuranceInfo[];
  activatedSchein?: ScheinItem;
  isActivatedSvSchein: boolean;
  isFocusMode: boolean;
  list: ScheinItem[];
  originalList: ScheinItem[];
  loading: boolean;
  scheinGroupByQuarter: QuarterGroup[];
  activeScheinGroupByQuarter: QuarterGroup[];
  issueDateDefault: number;
}

const initStore: IPatientFileStore = {
  preventRedirectCreatePatient: false,
  createPatient: null,
  params: {},
  activeTabId: ID_TABS.TIMELINE,
  schein: {
    insuranceInfos: [],
    activatedSchein: undefined,
    isActivatedSvSchein: false,
    isFocusMode: false,
    list: [],
    originalList: [],
    loading: false,
    scheinGroupByQuarter: [],
    activeScheinGroupByQuarter: [],
    issueDateDefault: datetimeUtil.now(),
  },
  patient: {
    isReadingCard: false,
    current: null,
    activeParticipations: [],
    currentContract: null,
  },
  serviceCodeData: null,
  materialCosts: {
    list: [],
  },
  isSvPatient: false,
  isEditingProfile: false,
  availableContracts: [],
  insurances: [],
  barcodeMedicationPlanContent: '',
  isSdvaIndexed: false,
  sessionId: '',
  currentQuarter: '',
  continuePatient: '',
  therapies: [],
  listErrors: [],
  listHints: [],
  pointValue: undefined,
  lastSelectedScheinSetting: null,
  skippedErrorKeyList: [],
  defaultCatalog: IcdSearchCatalog.SystematicAndAlphabetical,
  defaultDoctorSpecialist: undefined,
  appointmentNotifyCount: 0,
  digaDetail: undefined,
  history: {
    latestViewer: null,
    listHistory: [],
  },
  isOpenSchein: false,
  defaultDocumentDate: null,
};

export const patientFileStore = proxy<IPatientFileStore>(initStore);
proxyWithComputed(patientFileStore, {
  currentPatientGenericInfo: (store) => {
    return store?.patient?.current?.patientInfo?.genericInfo;
  },
  activeInsurance: (store) => {
    const activeInsurance = getActiveInsurance(
      store?.patient?.current?.patientInfo?.insuranceInfos || []
    );
    return activeInsurance;
  },
});

export const patientFileActions: IPatientFileActions = {
  setPreventRedirectCreatePatient: (isPrevent: boolean) => {
    patientFileStore.preventRedirectCreatePatient = isPrevent;
  },

  setCreatePatient: (createPatient: PatientInfo | null) => {
    patientFileStore.createPatient = createPatient;
  },

  setActiveTabId: (tabId: ID_TABS) => {
    patientFileStore.activeTabId = tabId;
  },

  resetStore() {
    patientFileStore.patient.current = null;
    patientFileStore.schein.activatedSchein = undefined;
    patientFileStore.schein.isActivatedSvSchein = false;
    patientFileStore.schein.list = [];
    patientFileStore.schein.originalList = [];
    patientFileStore.isSvPatient = false;
  },
  schein: {
    getListScheins: function (): ScheinItem[] {
      return patientFileStore.schein.list;
    },
    setActivatedSchein: debounce(function (
      schein?: ScheinItem,
      participations?: PatientParticipation[],
      patientId?: string
    ): void {
      patientFileStore.schein.activatedSchein = schein;
      const patientIdSetting =
        patientId || patientFileStore?.patient?.current?.id || '';
      if (!schein) {
        patientFileStore.schein.isActivatedSvSchein = false;
        saveSettings({
          settings: {
            [SettingsKey.SettingsKey_LastSelectedSchein]: JSON.stringify({
              ...patientFileStore.lastSelectedScheinSetting,
              [patientFileStore?.patient?.current?.id || '']: null,
            }),
          },
        });
      } else {
        patientFileStore.isSvPatient = checkIsSvSchein(schein);

        const scheinMatchContract = participations?.findIndex(
          (p: PatientParticipation) => p.contractId === schein.hzvContractId
        );

        patientFileStore.schein.isActivatedSvSchein =
          typeof scheinMatchContract !== 'undefined' &&
          scheinMatchContract !== -1;
        saveSettings({
          settings: {
            [SettingsKey.SettingsKey_LastSelectedSchein]: JSON.stringify({
              ...patientFileStore.lastSelectedScheinSetting,
              [patientIdSetting]: schein.scheinId,
            }),
          },
        });
      }
      if (patientFileStore.lastSelectedScheinSetting) {
        patientFileStore.lastSelectedScheinSetting[patientIdSetting] =
          schein?.scheinId || '';
      }
    },
      300),
    groupSchein: (scheins: ScheinItem[], insuranceInfo: InsuranceInfo[]) => {
      const normalizeRes = groupQuarterbySchein(
        scheins.filter((s) => !s?.isTechnicalSchein),
        insuranceInfo
      );
      const scheinActiveFilter = groupQuarterbySchein(
        scheins.filter(
          (s) =>
            !s?.isTechnicalSchein &&
            !s?.markedAsBilled &&
            s?.scheinStatus !== ScheinStatus.ScheinStatus_Canceled &&
            s?.scheinStatus !== ScheinStatus.ScheinStatus_Billed
        ),
        insuranceInfo
      );

      patientFileStore.schein.scheinGroupByQuarter = normalizeRes;
      patientFileStore.schein.activeScheinGroupByQuarter = scheinActiveFilter;
    },
    getScheinsOverview: async function (
      patientId: string
    ): Promise<ScheinItem[]> {
      patientFileStore.schein.loading = true;
      try {
        const rs = await getScheinsOverview({ patientId });
        const scheins = rs.data.scheinItems || [];
        await Promise.all(
          scheins.map(async (s) => {
            if (checkIsSvSchein(s)) {
              const contractMeta = await catalogOverviewActions.getContractMeta(
                moment(`${s.g4101Quarter}/${s.g4101Year}`, 'Q/Y')
                  .add(1, 'months')
                  .unix() * 1000,
                s?.hzvContractId || ''
              );
              if (contractMeta) {
                s['contractName'] = contractMeta?.contractName;
              }
            }
          })
        );

        patientFileStore.schein.originalList = scheins;
        patientFileStore.schein.insuranceInfos = rs.data.insuranceInfo;
        patientFileActions.schein.groupSchein(scheins, rs.data.insuranceInfo);
        if (scheins?.length === 0)
          patientFileActions.setActiveTabId(ID_TABS.TIMELINE);
        patientFileStore.schein.list = scheins.filter(
          (s) => !s?.isTechnicalSchein
        );
        return scheins;
      } finally {
        patientFileStore.schein.loading = false;
      }
    },
    setScheins: (scheins: ScheinItem[]) => {
      patientFileStore.schein.list = scheins.filter(
        (s) => !s?.isTechnicalSchein
      );
    },
    setOriginalList: (scheins: ScheinItem[]) => {
      patientFileStore.schein.originalList = scheins;
    },
    setLoading: (data: boolean) => [(patientFileStore.schein.loading = data)],
    resetSchein: () => {
      patientFileStore.schein = {
        insuranceInfos: [],
        activatedSchein: undefined,
        isActivatedSvSchein: false,
        isFocusMode: false,
        list: [],
        originalList: [],
        loading: false,
        scheinGroupByQuarter: [],
        activeScheinGroupByQuarter: [],
        issueDateDefault: datetimeUtil.now(),
      };
      patientFileStore.isSvPatient = false;
    },
    setIssueDate: (issueDate: number) => {
      patientFileStore.schein.issueDateDefault = issueDate;
    },
    setFocusMode: (isFocusMode: boolean) => {
      patientFileStore.schein.isFocusMode = isFocusMode;
    },
  },
  patient: {
    setCurrent: function (patient: IPatientProfile): void {
      patientFileStore.patient.current = patient;
    },
    setActiveParticipations: function (
      activeParticipations: PatientParticipation[]
    ): void {
      patientFileStore.patient.activeParticipations = activeParticipations;
    },
    setReadingCard: (isReadingCard: boolean) => {
      patientFileStore.patient.isReadingCard = isReadingCard;
    },
    setPatientInfo: (patientInfo: PatientInfo) => {
      if (patientFileStore.patient.current) {
        patientFileStore.patient.current.patientInfo = patientInfo;
      }
    },
    setCurrentContract: (contract: ContractInformation) => {
      patientFileStore.patient.currentContract = contract;
    },
  },
  materialCosts: {
    getMaterialCosts: async function () {
      patientFileStore.materialCosts.list = await startIndexingMaterialCost();
    },
  },
  setIsEditPatientProfile(isEdit: boolean | string): void {
    patientFileStore.isEditingProfile = isEdit;
  },
  params: {
    set: function (key: string, value: string): void {
      patientFileStore.params[key] = value;
    },
    get: function (key: string): string {
      const rs = patientFileStore.params[key];
      patientFileStore.params[key] = '';
      return rs;
    },
  },
  // NOTE: contracts
  setAvailableContracts: (contracts: IContractInfo[]) => {
    patientFileStore.availableContracts = [...contracts];
  },
  getAvailableContractById: (contractId: string | undefined) => {
    if (!contractId) {
      return null;
    }
    return (
      patientFileStore.availableContracts.find(
        (contract) => contract.id === contractId
      ) || null
    );
  },
  loadUserSettings: async () => {
    const { data } = await getSettings({
      settings: [
        SettingsKey.SettingsKey_LastSelectedSchein,
        SettingsKey.SettingsKey_ErrorKeyList,
        SettingsKey.SettingsKey_DefaultCatalog,
        SettingsKey.SettingsKey_DefaultDoctorSpecialist,
      ],
    });

    patientFileStore.lastSelectedScheinSetting = JSON.parse(
      data?.settings?.[SettingsKey.SettingsKey_LastSelectedSchein] || '{}'
    );

    patientFileStore.skippedErrorKeyList = JSON.parse(
      data?.settings?.[SettingsKey.SettingsKey_ErrorKeyList] || '[]'
    );

    patientFileStore.defaultCatalog =
      (data?.settings?.[
        SettingsKey.SettingsKey_DefaultCatalog
      ] as IcdSearchCatalog) || IcdSearchCatalog.SystematicAndAlphabetical;

    patientFileStore.defaultDoctorSpecialist =
      data?.settings?.[SettingsKey.SettingsKey_DefaultDoctorSpecialist];
  },

  setBarcodeMedicationPlanContent: (
    barcodeContent: string,
    sessionId: string,
    patientId: string
  ) => {
    patientFileStore.continuePatient = patientId;
    patientFileStore.barcodeMedicationPlanContent = barcodeContent;
    patientFileStore.sessionId = sessionId;
  },
  setSdvaIndexed: (isIndexed) => {
    patientFileStore.isSdvaIndexed = isIndexed;
  },
  setTerminalId: (id?: string) => {
    patientFileStore.terminalId = id;
  },
  setListErrors: (errors: FileContentDetail[]) => {
    patientFileStore.listErrors = errors;
  },
  setListHints: (hints: GroupErrorByPatientDetailError[]) => {
    patientFileStore.listHints = hints;
  },
  setPointValue: (payload) => {
    patientFileStore.pointValue = payload;
  },
  setIsOpenSchein: (isOpenSchein: boolean) => {
    patientFileStore.isOpenSchein = isOpenSchein;
  },
  setDefaultDocumentDate: (documentDate: number | null) => {
    patientFileStore.defaultDocumentDate = documentDate;
  },
  reloadPatientProfile: async () => { },
  saveSkippedErrorKeyList: (skippedErrorKeyList: IErrorCode[]) => {
    patientFileStore.skippedErrorKeyList = skippedErrorKeyList;
    saveSettings({
      settings: {
        [SettingsKey.SettingsKey_ErrorKeyList]:
          JSON.stringify(skippedErrorKeyList),
      },
    });
  },
  saveDefaultCatalog: (
    catalog: IcdSearchCatalog,
    doctorSpecialist?: string
  ) => {
    patientFileStore.defaultCatalog = catalog;
    patientFileStore.defaultDoctorSpecialist = doctorSpecialist;
    saveSettings({
      settings: {
        [SettingsKey.SettingsKey_DefaultCatalog]: catalog,
        [SettingsKey.SettingsKey_DefaultDoctorSpecialist]:
          doctorSpecialist as string,
      },
    });
  },
  setAppointmentNotifyCount: (count: number) => {
    patientFileStore.appointmentNotifyCount = count;
  },
  setDigaDetail: (digaDetail: DigaDetail | undefined) => {
    patientFileStore.digaDetail = digaDetail;
  },

  resetStateWhenChangePatient: () => {
    patientFileActions.setActiveTabId(ID_TABS.TIMELINE);
    timelineActions.resetTimelineState();
    timelineActions.setHistoryMode(false);
  },
  setHistoryViewPatient: async (payload: CreateHistoryRequest) => {
    await createHistory(payload);
  },

  getLatestView: async (payload: GetLatestDoctorViewRequest) => {
    try {
      const res = await getLatestDoctorView(payload);
      if (res.data && res.data.historyItem) {
        patientFileStore.history.latestViewer = res.data.historyItem;
      }
    } catch (err) {
      throw err;
    }
  },
};

export function usePatientFileStore() {
  const { isSuccess, data, refetch } = useQueryGetPatientProfileById(
    {
      id: patientFileStore.patient?.current?.id as string,
    },
    {
      enabled: false,
    }
  );

  useEffect(() => {
    if (isSuccess && data) {
      patientFileStore.patient.current = data;
    }
  }, [isSuccess, data]);

  useEffect(() => {
    if (!patientFileStore.patient?.current?.id) return;

    patientFileActions.reloadPatientProfile = refetch;
  }, [refetch]);

  return useSnapshot(patientFileStore);
}
