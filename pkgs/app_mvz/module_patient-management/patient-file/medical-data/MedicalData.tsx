import {
  alertSuccessfully,
  BodyTextL,
  BodyTextM,
  BodyTextS,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import {
  Classes,
  Collapse,
  Divider,
  Icon,
  Tooltip,
} from '@tutum/design-system/components/Core';
import {
  EventName,
  EventPatientProfileChange,
  PatientMedicalData as PatientProfilePatientMedicalData,
  createPatientMedicalData,
  updatePatientMedicalHistoryData,
  UpdatePatientMedicalHistoryDataRequest,
  GetPatientMedicalDataResponse,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  Gender,
  PatientMedicalData,
} from '@tutum/hermes/bff/patient_profile_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import MedicalDialog from '@tutum/mvz/module_patient-management/patient-file/medical-data/medical-dialog/MedicalDialog.styled';
import PregnantIcon from '@tutum/mvz/public/images/pregnant.svg';
import { isEmpty } from 'lodash';
import moment from 'moment';
import React, { memo, useEffect, useState } from 'react';
import { IPatientProfile } from '../../types/profile.type';
import { medicalHistoryActions } from './medical-history-dialog/MedicalHistory.store';
import MedicalHistoryDialog from './medical-history-dialog/MedicalHistoryDialog.styled';
import FormUtils from '@tutum/infrastructure/utils/form.util';
import {
  DATE_FORMAT,
  DATE_TIME_WITHOUT_SECONDS_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import useToaster from '../../../hooks/useToaster';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import {
  registerActionChainElementId,
  VitalParameterActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import { useListenPatientProfileChange } from '@tutum/hermes/bff/app_mvz_patient_profile';

const editIcon = '/images/edit-2.svg';

export interface IMedicalDataProps {
  className?: string;
  patient?: IPatientProfile;
  isBillingValidation?: boolean;
  canEdit?: boolean;
  setMedicalData?: (medicalData: PatientMedicalData) => void;
  setMedicalDataUpdatedAt?: (medicalDataUpdatedAt: number) => void;
}

function MedicalData({
  className,
  patient,
  isBillingValidation,
  canEdit = true,
  setMedicalData,
  t,
  setMedicalDataUpdatedAt,
}: IMedicalDataProps &
  II18nFixedNamespace<any>): React.FunctionComponentElement<IMedicalDataProps> {
  const [isExpand, setExpand] = useState(true);
  const [medicalDialogState, setMedicalDialogState] = useState<{
    isOpen: boolean;
    isAdd: boolean;
    isUpdate: boolean;
    isUpdateHistory: boolean;
    editingMedicalRecord?: PatientProfilePatientMedicalData;
    isSubmitting: boolean;
    initData?: PatientMedicalData;
  }>({
    isOpen: false,
    isAdd: false,
    isUpdate: false,
    isUpdateHistory: false,
    isSubmitting: false,
  });
  const [isMedicalHistoryDialogOpen, setMedicalHistoryDialogOpen] =
    useState(false);
  useEffect(() => {
    medicalHistoryActions.setCurrentPatient(patient);
  }, []);
  useListenPatientProfileChange((response: EventPatientProfileChange) => {
    if (
      response &&
      response.patientMedicalData &&
      response.medicalDataUpdatedAt &&
      response.eventName === EventName.EventName_UpdateMedicalData &&
      patient?.id === response.patientId
    ) {
      setMedicalDataUpdatedAt?.(response?.medicalDataUpdatedAt);
      setMedicalData?.(response.patientMedicalData);
    }
    // updated but response.patientMedicalData === null
    else if (
      response &&
      response.patientMedicalData &&
      !response.medicalDataUpdatedAt &&
      response.eventName === EventName.EventName_UpdateMedicalData &&
      patient?.id === response.patientId
    ) {
      // try to call the api to get medical data again
      // todo: for the backend side - fix return data @ useListenPatientProfileChange then remove this code.
      (
        medicalHistoryActions.getMedicalHistory(
          false
        ) as Promise<GetPatientMedicalDataResponse>
      )?.then((rs) => {
        if (rs.patientMedicalDatas?.length > 0) {
          const lastRecord = rs.patientMedicalDatas[0];
          setMedicalDataUpdatedAt?.(lastRecord.createdAt);
          setMedicalData?.(lastRecord.patientMedicalData);
        }
      });
    }
  });

  if (isEmpty(patient)) {
    return null;
  }

  const { id, patientMedicalData, medicalDataUpdatedAt, patientInfo } = patient;
  const weight = patientMedicalData?.weight || 0;
  const height = patientMedicalData?.height || 0;
  const bloodPressure = patientMedicalData?.bloodPressure;
  const heartFrequency = patientMedicalData?.heartFrequency;
  const creatinine = patientMedicalData?.creatinine;
  const amountOfChildren = patientMedicalData?.amountOfChildren;
  const amountOfPregnancies = patientMedicalData?.amountOfPregnancies;
  const careLevel = patientMedicalData?.careLevel;
  const amountOfBirth = patientMedicalData?.amountOfBirth;
  const dateOfPlannedBirth = patientMedicalData?.dateOfPlannedBirth
    ? moment(patientMedicalData?.dateOfPlannedBirth).format(DATE_FORMAT)
    : '';

  const bmi = FormUtils.calculateBMI({ height, weight });
  const successToaster = () => {
    alertSuccessfully(t('medicalRecordIsUpdated'));
  };

  const isMedicalDataEmpty = Object.values(patientMedicalData ?? {}).every(
    (v) => !v || (Array.isArray(v) && v.length === 0)
  );

  const saveMedicalData = (
    medicalData: PatientMedicalData,
    close?: Function
  ) => {
    setMedicalDialogState((prev) => ({
      ...prev,
      isSubmitting: true,
    }));
    if (medicalDialogState.isUpdateHistory) {
      const request: UpdatePatientMedicalHistoryDataRequest = {
        id: medicalDialogState.editingMedicalRecord?.id,
        patientId: patient.id,
        patientMedicalData: medicalData,
      };
      //Update then listen event from useListenPatientProfileChange to update patient data in UI
      updatePatientMedicalHistoryData(request).then(() => {
        if (setMedicalData) {
          setMedicalData(medicalData);
        }
        medicalHistoryActions.getMedicalHistory();
        setMedicalDialogState((prev) => ({
          ...prev,
          isSubmitting: false,
        }));
        if (close) {
          close();
        }
        successToaster();
      });
    } else {
      createPatientMedicalData({
        patientId: id,
        patientMedicalData: medicalData,
      }).then(() => {
        setMedicalData?.(medicalData);
        if (medicalDialogState.isAdd) {
          medicalHistoryActions.getMedicalHistory();
        }
        setMedicalDialogState((prev) => ({
          ...prev,
          isSubmitting: false,
        }));
        if (close) {
          close();
        }
        successToaster();
      });
    }
  };

  return (
    <div className={className}>
      <div className="sl-header-container">
        <BodyTextL className="sl-medical-data__title" fontWeight={600}>
          {t('medicalData')}
        </BodyTextL>
        {canEdit && !isBillingValidation && (
          <Tooltip
            className="sl-tooltip-edit-icon"
            content={t('editMedicalRecordToolTip')}
            openOnTargetFocus={false}
          >
            <span
              className="sl-edit-icon"
              onClick={() =>
                setMedicalDialogState({
                  isOpen: true,
                  isAdd: false,
                  isUpdate: true,
                  isUpdateHistory: false,
                  isSubmitting: false,
                })
              }
              {...registerActionChainElementId(
                VitalParameterActionChainElementId.DIALOG_TOGGLE_BUTTON
              )}
            >
              <Svg src={editIcon} width={16} />
            </span>
          </Tooltip>
        )}
        {canEdit && !isMedicalDataEmpty && (
          <Icon
            className="expand-icon"
            icon={isExpand ? 'chevron-up' : 'chevron-down'}
            onClick={() => setExpand(!isExpand)}
          />
        )}
      </div>
      <Collapse isOpen={isExpand} className="medical__content">
        <Flex column>
          {patientMedicalData?.isPregnant ||
            patientMedicalData?.isBreastfeeding ||
            patientMedicalData?.isSmoker ? (
            <Flex className="sl-row" align="center">
              {!patientMedicalData?.isSmoker && (
                <span className="sl-icon-wrapper">
                  <PregnantIcon />
                </span>
              )}
              <Flex flexWrap>
                {patientMedicalData?.isPregnant && (
                  <span className="sl-tag">{t('pregnant')}</span>
                )}
                {patientMedicalData?.isBreastfeeding && (
                  <span className="sl-tag">{t('breastfeeding')}</span>
                )}
                {patientMedicalData?.isSmoker && (
                  <span className="sl-tag">{t('patientIsSmoker')}</span>
                )}
              </Flex>
            </Flex>
          ) : null}
          {Boolean(weight) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('weight')}</BodyTextS>
              <BodyTextS className="sl-description">
                {`${medicationUtil.transformFormatNumber(weight, 0)} ${t(
                  'kg'
                )}`}
              </BodyTextS>
            </Flex>
          )}
          {Boolean(height) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('height')}</BodyTextS>
              <BodyTextS className="sl-description">
                {`${medicationUtil.transformFormatNumber(height, 0)} ${t(
                  'cm'
                )}`}
              </BodyTextS>
            </Flex>
          )}
          {Boolean(bmi) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('bmi')}</BodyTextS>
              <BodyTextS className="sl-description">
                {medicationUtil.transformFormatNumber(bmi, 0)}
              </BodyTextS>
            </Flex>
          )}
          {Boolean(bloodPressure) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('bloodPressure')}</BodyTextS>
              <BodyTextS className="sl-description">
                {`${bloodPressure} ${t('mmHg')}`}
              </BodyTextS>
            </Flex>
          )}
          {Boolean(heartFrequency) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('heartFrequency')}</BodyTextS>
              <BodyTextS className="sl-description">
                {`${medicationUtil.transformFormatNumber(
                  heartFrequency,
                  0
                )} ${t('bpm')}`}
              </BodyTextS>
            </Flex>
          )}
          {Boolean(creatinine) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('creatinine')}</BodyTextS>
              <BodyTextS className="sl-description">
                {`${medicationUtil.transformFormatNumber(creatinine, 0)} ${t(
                  'mgdl'
                )}`}
              </BodyTextS>
            </Flex>
          )}
          {Boolean(amountOfChildren) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">
                {t('amountOfChildren')}
              </BodyTextS>
              <BodyTextS className="sl-description">
                {amountOfChildren}
              </BodyTextS>
            </Flex>
          )}
          {patientInfo?.personalInfo?.gender !== Gender.M && (
            <>
              {Boolean(dateOfPlannedBirth) && (
                <Flex className="sl-row">
                  <BodyTextS className="sl-title">
                    {t('dateOfPlannedBirth')}
                  </BodyTextS>
                  <BodyTextS className="sl-description">
                    {dateOfPlannedBirth}
                  </BodyTextS>
                </Flex>
              )}
              {Boolean(amountOfBirth) && (
                <Flex className="sl-row">
                  <BodyTextS className="sl-title">
                    {t('amountOfBirths')}
                  </BodyTextS>
                  <BodyTextS className="sl-description">
                    {amountOfBirth}
                  </BodyTextS>
                </Flex>
              )}
              {Boolean(amountOfPregnancies) && (
                <Flex className="sl-row">
                  <BodyTextS className="sl-title">
                    {t('amountOfPregnancies')}
                  </BodyTextS>
                  <BodyTextS className="sl-description">
                    {amountOfPregnancies}
                  </BodyTextS>
                </Flex>
              )}
            </>
          )}
          {Boolean(
            patientMedicalData?.isSmoker && patientMedicalData?.cigarettesPerDay
          ) && (
              <Flex className="sl-row">
                <BodyTextS className="sl-title">
                  {t('cigarettesPerDay')}
                </BodyTextS>
                <BodyTextS className="sl-description">
                  {patientMedicalData.cigarettesPerDay}
                </BodyTextS>
              </Flex>
            )}
          {Boolean(careLevel) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('careLevel')}</BodyTextS>
              <BodyTextS className="sl-description">{careLevel}</BodyTextS>
            </Flex>
          )}
          {Boolean(patientMedicalData?.bodyTemperature) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('bodyTemperature')}</BodyTextS>
              <BodyTextS className="sl-description">
                {medicationUtil.transformFormatNumber(
                  patientMedicalData.bodyTemperature,
                  0
                )}{' '}
                °C
              </BodyTextS>
            </Flex>
          )}
          {Boolean(patientMedicalData?.pulseOxiMetric) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('pulseOxiMetric')}</BodyTextS>
              <BodyTextS className="sl-description">
                {medicationUtil.transformFormatNumber(
                  patientMedicalData.pulseOxiMetric,
                  0
                )}{' '}
                %
              </BodyTextS>
            </Flex>
          )}
          {Boolean(patientMedicalData?.pulse) && (
            <Flex className="sl-row">
              <BodyTextS className="sl-title">{t('pulse')}</BodyTextS>
              <BodyTextS className="sl-description">
                {t(patientMedicalData.pulse)}
              </BodyTextS>
            </Flex>
          )}
          <Flex column className="sl-row">
            <Flex w="100%" justify="space-between">
              <BodyTextM className="sl-created-on" margin="0 0 0 8px">
                {isMedicalDataEmpty
                  ? t('noMedicalData')
                  : Boolean(medicalDataUpdatedAt)
                    ? t('lastUpdatedTime', {
                      date: moment(medicalDataUpdatedAt).format(
                        DATE_TIME_WITHOUT_SECONDS_FORMAT
                      ),
                    })
                    : ''}
              </BodyTextM>
              {canEdit && !isMedicalDataEmpty && (
                <BodyTextM
                  className="sl-view-all-records"
                  onClick={() => setMedicalHistoryDialogOpen(true)}
                >
                  {t('viewAllRecords')}
                </BodyTextM>
              )}
            </Flex>
          </Flex>
        </Flex>
      </Collapse>

      {isMedicalHistoryDialogOpen && (
        <MedicalHistoryDialog
          isOpen={isMedicalHistoryDialogOpen}
          close={() => setMedicalHistoryDialogOpen(false)}
          patient={patient}
          setMedicalDialogState={setMedicalDialogState}
        />
      )}

      {medicalDialogState.isOpen && (
        <MedicalDialog
          isOpen={medicalDialogState.isOpen}
          close={() =>
            setMedicalDialogState({
              isOpen: false,
              isAdd: true,
              isUpdate: true,
              isUpdateHistory: false,
              editingMedicalRecord: null,
              isSubmitting: false,
            })
          }
          save={saveMedicalData}
          patient={patient}
          medicalDialogState={medicalDialogState}
        />
      )}
    </div>
  );
}

export default memo(
  I18n.withTranslation(MedicalData, {
    namespace: 'PatientProfileCreation',
    nestedTrans: 'Medical',
  })
);
