import { Flex, Svg } from '@tutum/design-system/components';
import {
  Ali<PERSON><PERSON>,
  Button,
  Dialog,
  Position,
  Tooltip,
} from '@tutum/design-system/components/Core';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { PatientMedicalData } from '@tutum/hermes/bff/app_mvz_patient_profile';
import {
  DATE_FORMAT,
  DATE_TIME_WITHOUT_SECONDS_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import FormUtils from '@tutum/infrastructure/utils/form.util';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { Translate } from 'next-translate';
import useTranslation from 'next-translate/useTranslation';
import React, { useEffect } from 'react';
import {
  medicalHistoryActions,
  useMedicalHistoryStore,
} from './MedicalHistory.store';

const EditIcon = '/images/edit-2.svg';

export interface IMedicalHistoryDialogProps {
  className?: string;
  isOpen: boolean;
  close: () => void;
  patient: IPatientProfile;
  setMedicalDialogState: Function;
}

const TRANSLATION_NAMESPACE = 'PatientProfileCreation';

type MedicalHistoryTableColumnType = (
  t: Translate,
  setMedicalDialogState: Function
) => IDataTableColumn<PatientMedicalData>[];
const getMedicalHistoryTableColumn: MedicalHistoryTableColumnType = (
  t,
  setMedicalDialogState
) => {
  const columns: IDataTableColumn<PatientMedicalData>[] = [
    {
      id: 'createdAt',
      name: t('Medical.createdOn'),
      minWidth: '140px',
      maxWidth: '140px',
      sortable: true,
      grow: 2,
      selector: (row) => row.createdAt,
      format: (row: PatientMedicalData) =>
        row.createdAt
          ? moment(row.createdAt).format(DATE_TIME_WITHOUT_SECONDS_FORMAT)
          : '',
    },
    {
      selector: (row) => row.patientMedicalData.weight!,
      name: t('Medical.weight'),
      minWidth: '100px',
      maxWidth: '100px',
      cell: (row: PatientMedicalData) => {
        return row.patientMedicalData.weight ? (
          <>
            {Intl.NumberFormat('de').format(row.patientMedicalData.weight)}{' '}
            {t('Medical.kg')}
          </>
        ) : (
          <></>
        );
      },
    },
    {
      selector: (row) => row.patientMedicalData.height!,
      name: t('Medical.height'),
      minWidth: '85px',
      maxWidth: '85px',
      cell: (row: PatientMedicalData) => {
        return row.patientMedicalData.height ? (
          <>
            {Intl.NumberFormat('de').format(row.patientMedicalData.height)}{' '}
            {t('Medical.cm')}
          </>
        ) : (
          <></>
        );
      },
    },
    {
      id: 'bmi',
      name: t('Medical.bmi'),
      minWidth: '80px',
      maxWidth: '80px',
      cell: (row: PatientMedicalData) => {
        const bmi = FormUtils.calculateBMI({
          height: row.patientMedicalData.height!,
          weight: row.patientMedicalData.weight!,
        });
        if (!bmi) return null;
        const bmiFloat = Number.parseFloat(bmi);
        return <>{Intl.NumberFormat('de').format(bmiFloat)}</>;
      },
    },
    {
      name: t('Medical.bloodPressure'),
      minWidth: '140px',
      maxWidth: '140px',
      selector: (row) => row.patientMedicalData.bloodPressure!,
      cell: (row: PatientMedicalData) => {
        return row.patientMedicalData.bloodPressure ? (
          <>
            {row.patientMedicalData.bloodPressure} {t('Medical.mmHg')}
          </>
        ) : (
          <></>
        );
      },
    },
    {
      name: t('Medical.heartFrequency'),
      minWidth: '140px',
      maxWidth: '140px',
      selector: (row) => row.patientMedicalData.heartFrequency!,
      cell: (row: PatientMedicalData) => {
        return row.patientMedicalData.heartFrequency ? (
          <>
            {Intl.NumberFormat('de').format(
              row.patientMedicalData.heartFrequency
            )}{' '}
            {t('Medical.bpm')}
          </>
        ) : (
          <></>
        );
      },
    },
    {
      name: t('Medical.creatinine'),
      minWidth: '140px',
      maxWidth: '140px',
      selector: (row) => row.patientMedicalData.creatinine!,
      cell: (row: PatientMedicalData) => {
        return row.patientMedicalData.creatinine ? (
          <>
            {Intl.NumberFormat('de').format(row.patientMedicalData.creatinine)}{' '}
            {t('Medical.mgdl')}
          </>
        ) : (
          <></>
        );
      },
    },
    {
      name: t('Medical.dateOfPlannedBirth'),
      minWidth: '110px',
      maxWidth: '110px',
      selector: (row) => row.patientMedicalData.dateOfPlannedBirth!,
      format: (row: PatientMedicalData) =>
        row.patientMedicalData.dateOfPlannedBirth
          ? moment(row.patientMedicalData.dateOfPlannedBirth).format(
            DATE_FORMAT
          )
          : '',
    },
    {
      name: t('Medical.amountOfPregnancies'),
      minWidth: '140px',
      maxWidth: '140px',
      selector: (row) => row.patientMedicalData.amountOfPregnancies!,
    },
    {
      name: t('Medical.amountOfBirths'),
      minWidth: '110px',
      maxWidth: '110px',
      selector: (row) => row.patientMedicalData.amountOfBirth!,
    },
    {
      name: t('Medical.amountOfChildren'),
      minWidth: '90px',
      maxWidth: '90px',
      selector: (row) => row.patientMedicalData.amountOfChildren!,
    },
    {
      name: t('Medical.cigarettesPerDay'),
      minWidth: '90px',
      maxWidth: '90px',
      selector: (row) => row.patientMedicalData.cigarettesPerDay!,
    },
    {
      name: t('Medical.careLevel'),
      minWidth: '80px',
      maxWidth: '80px',
      selector: (row) => row.patientMedicalData.careLevel!,
      cell: (row: PatientMedicalData) => {
        const { careLevel } = row.patientMedicalData;
        return <>{careLevel || ''}</>;
      },
    },
    {
      name: t('Medical.bodyTemperature'),
      minWidth: '140px',
      maxWidth: '140px',
      selector: (row) => row.patientMedicalData.bodyTemperature!,
      cell: (row: PatientMedicalData) => {
        const { bodyTemperature } = row.patientMedicalData;
        return `${bodyTemperature
            ? Intl.NumberFormat('de').format(bodyTemperature) + ' °C'
            : ''
          }`;
      },
    },
    {
      name: t('Medical.pulseOxiMetricCol'),
      minWidth: '120px',
      maxWidth: '120px',
      selector: (row) => row.patientMedicalData.pulseOxiMetric!,
      cell: (row: PatientMedicalData) => {
        const { pulseOxiMetric } = row.patientMedicalData;
        return `${pulseOxiMetric
            ? Intl.NumberFormat('de').format(pulseOxiMetric) + ' %'
            : ''
          }`;
      },
    },
    {
      name: t('Medical.pulse'),
      minWidth: '120px',
      maxWidth: '120px',
      selector: (row) => row.patientMedicalData.pulse!,
      cell: (row: PatientMedicalData) => {
        const { pulse } = row.patientMedicalData;
        return <>{pulse ? t(`Medical.${pulse}`) : ''}</>;
      },
    },
    {
      name: t('Medical.lastEditedOn'),
      selector: (row) => row.updatedAt!,
      minWidth: '150px',
      maxWidth: '150px',
      sortable: true,
      format: (row: PatientMedicalData) =>
        row.updatedAt
          ? moment(row.updatedAt).format(DATE_TIME_WITHOUT_SECONDS_FORMAT)
          : '',
    },
    {
      name: t('Medical.lastEditedBy'),
      minWidth: '150px',
      maxWidth: '150px',
      selector: (row) => row.updatedByName,
    },
    {
      name: t('Medical.createdBy'),
      minWidth: '170px',
      maxWidth: '170px',
      selector: (row) => row.createdByName,
    },
    {
      id: 'actions',
      name: '',
      minWidth: '30px',
      maxWidth: '30px',
      cell: (row: PatientMedicalData) => {
        return (
          <div className="edit-hover">
            <Tooltip
              content={t('Medical.editMedicalRecordToolTip')}
              position={Position.TOP}
            >
              <Svg
                src={EditIcon}
                style={{ cursor: 'pointer' }}
                onClick={() =>
                  setMedicalDialogState({
                    isOpen: true,
                    isAdd: false,
                    isUpdate: false,
                    isUpdateHistory: true,
                    editingMedicalRecord: row,
                  })
                }
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return columns;
};

const MedicalDialog = React.memo((props: IMedicalHistoryDialogProps) => {
  const { className, isOpen, close, patient, setMedicalDialogState } = props;
  const { t } = useTranslation(TRANSLATION_NAMESPACE);
  const medicalHistoryStore = useMedicalHistoryStore();
  useEffect(() => {
    medicalHistoryActions.setCurrentPatient(patient);
    medicalHistoryActions.getMedicalHistory();
  }, []);

  const handleAddMedicalRecord = () => {
    setMedicalDialogState({
      isOpen: true,
      isAdd: true,
      isUpdate: false,
      isUpdateHistory: false,
      initData: medicalHistoryStore?.medicalHistoryData?.[0],
    });
  };
  const dataTable = cloneDeep(medicalHistoryStore.medicalHistoryData) || [];

  const isAllRowNull = (selector) => {
    return dataTable.every((row) => !selector?.(row));
  };
  const columns = getMedicalHistoryTableColumn(t, setMedicalDialogState);

  const filteredColumns = columns.filter((col) => {
    return (
      !isAllRowNull(col.selector) || ['actions'].includes(col.id as string)
    );
  });

  return (
    <Dialog
      className={`${className} bp5-dialog-fullscreen`}
      title={t('Medical.medicalDataRecord')}
      isOpen={isOpen}
      onClose={close}
      canOutsideClickClose={false}
    // portalClassName={`second-layer-right_wo-backdrop`}
    >
      <Flex column={true} className="sl-medical-history__container">
        <Flex align="center" justify="flex-end">
          <Button
            large
            icon="plus"
            alignText={Alignment.LEFT}
            intent="primary"
            onClick={handleAddMedicalRecord}
            className="sl-medical-history__add-button"
          />
        </Flex>

        <div className="sl-medical-history__table">
          <Table
            title={false}
            stickyLastColumn
            columns={filteredColumns}
            data={dataTable}
            customStyles={{
              headRow: {
                style: {
                  textTransform: 'uppercase',
                },
              },
              pagination: {
                style: {
                  justifyContent: 'flex-start',
                },
              },
            }}
            noHeader={true}
            selectableRowsNoSelectAll={true}
            highlightOnHover={true}
            selectableRowsHighlight={true}
            // responsive={false}
            paginationRowsPerPageOptions={[10, 20, 30]}
            paginationTotalRows={medicalHistoryStore.totalRecords}
            onChangePage={medicalHistoryActions.handlePageChange}
            pagination
            paginationServer
            paginationDefaultPage={medicalHistoryStore.page}
            paginationPerPage={medicalHistoryStore.pageSize}
            paginationResetDefaultPage
            onChangeRowsPerPage={medicalHistoryActions.handleChangeRowsPerPage}
            onSort={(column, direction) => {
              medicalHistoryActions.setSortOption(
                String(column.selector),
                direction
              );
            }}
            // defaultSortField="createdOn"
            defaultSortAsc={false}
          />
        </div>
      </Flex>
    </Dialog>
  );
});

export default MedicalDialog;
