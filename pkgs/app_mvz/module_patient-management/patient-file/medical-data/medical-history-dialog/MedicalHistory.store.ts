import {
  GetPatientMedicalDataRequest,
  GetPatientMedicalDataResponse,
  PatientMedicalData,
  getPatientMedicalData,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { Order, SortBy } from '@tutum/hermes/bff/common';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { useEffect } from 'react';
import { proxy, useSnapshot } from 'valtio';
import { patientFileStore } from '../../PatientFile.store';

interface IMedicalHistoryStore {
  medicalHistoryData: PatientMedicalData[];
  totalRecords: number;
  totalPage: number;
  page: number;
  pageSize: number;
  currentPatient?: IPatientProfile;
  currentSortBy: SortBy;
  currentOrderBy: Order;
}

interface IMedicalHistoryActions {
  getMedicalHistory: (
    self?: boolean
  ) => Promise<GetPatientMedicalDataResponse | undefined>;
  handlePageChange: (page: number) => void;
  handleChangeRowsPerPage(currentRowsPerPage: number): void;
  setCurrentPatient(patient?: IPatientProfile): void;
  setSortOption(columnSelector: string, direction: string);
}

const initStore: IMedicalHistoryStore = {
  medicalHistoryData: [],
  totalRecords: 0,
  totalPage: 0,
  page: 1,
  pageSize: 10,
  currentPatient: undefined,
  currentSortBy: SortBy.SortBy_MedicalData_CreatedAt,
  currentOrderBy: Order.DESC,
};

export let medicalHistoryStore = proxy<IMedicalHistoryStore>(initStore);

export const medicalHistoryActions: IMedicalHistoryActions = {
  async getMedicalHistory(self = true) {
    const requestBody: GetPatientMedicalDataRequest = {
      patientID:
        medicalHistoryStore?.currentPatient?.id! ||
        patientFileStore?.patient?.current?.id!,
      paginationRequest: {
        page: medicalHistoryStore.page,
        pageSize: medicalHistoryStore.pageSize,
        sortBy: medicalHistoryStore.currentSortBy,
        order: medicalHistoryStore.currentOrderBy,
      },
    };
    if (requestBody.patientID) {
      const request = getPatientMedicalData(requestBody);
      if (self) {
        const { data } = await request;
        const { paginationResponse, patientMedicalDatas } = data;
        medicalHistoryStore.medicalHistoryData = patientMedicalDatas;
        medicalHistoryStore.totalRecords = paginationResponse.total;
        medicalHistoryStore.totalPage = paginationResponse.totalPage;
      } else {
        return request.then((response) => response.data);
      }
    }
  },

  handlePageChange(page: number) {
    medicalHistoryStore.page = page;
    medicalHistoryActions.getMedicalHistory();
  },

  handleChangeRowsPerPage(currentRowsPerPage: number) {
    medicalHistoryStore.pageSize = currentRowsPerPage;
    medicalHistoryActions.getMedicalHistory();
  },

  setCurrentPatient(patient?: IPatientProfile) {
    medicalHistoryStore.currentPatient = patient;
  },

  setSortOption(columnSelector: string, direction: string) {
    medicalHistoryStore.currentSortBy =
      columnSelector === 'createdOn'
        ? SortBy.SortBy_MedicalData_CreatedAt
        : SortBy.SortBy_MedicalData_UpdatedAt;
    medicalHistoryStore.currentOrderBy =
      direction === 'asc' ? Order.ASC : Order.DESC;
    medicalHistoryStore.page = 1;
    medicalHistoryActions.getMedicalHistory();
  },
};

export function useMedicalHistoryStore() {
  useEffect(() => {
    return () => {
      medicalHistoryStore = proxy<IMedicalHistoryStore>(initStore);
    };
  }, []);
  return useSnapshot(medicalHistoryStore);
}
