
import {
  BodyTextS,
  Flex,
  Svg,
  ReactSelect,
  IMenuItem,
  Button,
  Intent,
  LeaveConfirmModal,
} from '@tutum/design-system/components';
import {
  Classes,
  Dialog,
  H5,
  InputGroup,
  Label,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import NumberInput from '@tutum/design-system/components/NumberInput';
import {
  Allergy,
  Gender,
  PatientMedicalData,
  Pulse,
} from '@tutum/hermes/bff/patient_profile_common';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import ValidationFormGroup from '@tutum/mvz/components/validation-form-group/ValidationFormGroup';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import {
  Field,
  Form,
  Formik,
  FormikErrors,
  FastField,
  FormikProps,
} from 'formik';
import React, { useMemo, useRef, useState } from 'react';
import PregnantInfo from './PregnantInfo';
import FormUtils from '@tutum/infrastructure/utils/form.util';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import {
  registerActionChainElementId,
  setAttributeActionChainElementId,
  VitalParameterActionChainElementId,
} from '@tutum/mvz/module_action-chain';
import SmokerInfo from './SmokerInfo';

const MAX_LIMIT = 1000;
export interface IMedicalDialogProps {
  className?: string;
  isOpen: boolean;
  close: () => void;
  patient: IPatientProfile;
  save: (medicalData: PatientMedicalData) => void;
  medicalDialogState: Object;
}

const TRANSLATION_NAMESPACE = 'PatientProfileCreation';

const renderMedicalForm = (
  props: IMedicalDialogProps,
  errors,
  values: PatientMedicalData,
  t: IFixedNamespaceTFunction<any>,
  medicalDialogState,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setRemoveAction: (fnc: () => void) => void
) => {
  const { close, patient } = props;
  const femaleRows =
    patient.patientInfo.personalInfo.gender === Gender.W ? (
      <>
        <div className="row">
          <div className="col-sm-12">
            <PregnantInfo
              i18n={{
                namespace: TRANSLATION_NAMESPACE,
              }}
              errors={errors}
              values={values}
            />
          </div>
        </div>
      </>
    ) : null;
  const { weight, height } = values;
  const bmi = FormUtils.calculateBMI({ height, weight });
  const renderSmoker = (
    <div className="row">
      <div className="col-sm-12">
        <SmokerInfo values={values} />
      </div>
    </div>
  );

  return (
    <Form>
      <div className={`${Classes.DIALOG_BODY} medical-dialog__body`}>
        <div className="row">
          <div className="col-sm-6">
            <ValidationFormGroup errors={errors} fieldName="weight">
              <Label name="weight">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('weight')}
                </BodyTextS>
              </Label>
              <Field name="weight">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      isFloat
                      placeholder="0"
                      defaultValue={field.value}
                      maxLimit={MAX_LIMIT}
                      decimalScale={3}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('kg')}
                        </span>
                      }
                      {...registerActionChainElementId(
                        VitalParameterActionChainElementId.WEIGHT_INPUT
                      )}
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
          <div className="col-sm-6">
            <ValidationFormGroup errors={errors} fieldName="height">
              <Label name="height">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('height')}
                </BodyTextS>
              </Label>
              <Field name="height">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      isFloat
                      placeholder="0"
                      maxLimit={MAX_LIMIT}
                      decimalScale={1}
                      defaultValue={field.value}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('cm')}
                        </span>
                      }
                      {...registerActionChainElementId(
                        VitalParameterActionChainElementId.HEIGHT_INPUT
                      )}
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
        </div>
        {(!!values.weight || !!values.height) && (
          <div className="row bmi-row">
            <div className="col-sm-12">
              <Label name="bmi">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('bmi')}
                </BodyTextS>
              </Label>
              <Field name="bmi">
                {({ field }) => {
                  return (
                    <InputGroup
                      placeholder="0,00"
                      type="number"
                      {...field}
                      value={bmi}
                      disabled
                    />
                  );
                }}
              </Field>
            </div>
          </div>
        )}
        <div className="row">
          <div className="col-sm-6">
            <ValidationFormGroup errors={errors} fieldName="bloodPressure">
              <Label name="bloodPressure">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('bloodPressure')}
                </BodyTextS>
              </Label>
              <Field name="bloodPressure">
                {({ field }) => {
                  return (
                    <InputGroup
                      placeholder="0"
                      {...field}
                      value={field?.value ?? undefined}
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('mmHg')}
                        </span>
                      }
                      {...registerActionChainElementId(
                        VitalParameterActionChainElementId.BLOOD_PRESSURE_INPUT
                      )}
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
          <div className="col-sm-6">
            <ValidationFormGroup errors={errors} fieldName="heartFrequency">
              <Label name="heartFrequency">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('heartFrequency')}
                </BodyTextS>
              </Label>
              <Field name="heartFrequency">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      placeholder="0"
                      defaultValue={field.value}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      maxLength={3}
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('bpm')}
                        </span>
                      }
                      {...registerActionChainElementId(
                        VitalParameterActionChainElementId.HEART_FREQUENCY_INPUT
                      )}
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
        </div>

        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="pulse">
              <Label name="pulse">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('pulse')}
                </BodyTextS>
              </Label>
              <Field name="pulse">
                {({ field, form }) => (
                  <RadioGroup
                    inline
                    onChange={(e) => {
                      form.setFieldValue(field.name, e.currentTarget.value);
                    }}
                    selectedValue={field.value}
                  >
                    <Radio
                      label={t('Pulse_Rhythmic')}
                      value={Pulse.Pulse_Rhythmic}
                    />
                    <Radio
                      label={t('Pulse_Irregular')}
                      value={Pulse.Pulse_Irregular}
                    />
                  </RadioGroup>
                )}
              </Field>
            </ValidationFormGroup>
          </div>
        </div>

        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="pulseOxiMetric">
              <Label name="pulseOxiMetric">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('pulseOxiMetric')}
                </BodyTextS>
              </Label>
              <FastField name="pulseOxiMetric">
                {({ field, form }) => (
                  <NumberInput
                    isFloat
                    placeholder="0,00"
                    decimalScale={3}
                    maxLimit={MAX_LIMIT}
                    data-test-id="pulse-oximetry"
                    defaultValue={field.value}
                    onValueChange={({ value }) =>
                      form.setFieldValue(field.name, +value || null)
                    }
                    rightElement={
                      <span className="sl-input-right-element">%</span>
                    }
                  />
                )}
              </FastField>
            </ValidationFormGroup>
          </div>
        </div>

        {renderSmoker}

        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="creatinine">
              <Label name="creatinine">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('creatinine')}
                </BodyTextS>
              </Label>
              <FastField name="creatinine">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      isFloat
                      placeholder="0,000"
                      decimalScale={3}
                      maxLimit={MAX_LIMIT}
                      defaultValue={field.value}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      rightElement={
                        <span className="sl-input-right-element">
                          {t('mgdl')}
                        </span>
                      }
                      {...registerActionChainElementId(
                        VitalParameterActionChainElementId.CREATININE_INPUT
                      )}
                    />
                  );
                }}
              </FastField>
            </ValidationFormGroup>
          </div>
        </div>

        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="careLevel">
              <Label name="careLevel">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('careLevel')}
                </BodyTextS>
              </Label>
              <Field name="careLevel">
                {({ field, form }) => {
                  return (
                    <ReactSelect
                      {...field}
                      items={[0, 1, 2, 3, 4, 5].map((item) => ({
                        value: item,
                        label: item == 0 ? ' ' : item.toString(),
                      }))}
                      customProps={{
                        'data-test-id': 'care-level',
                      }}
                      isSearchable={false}
                      onItemSelect={(item: IMenuItem) => {
                        form.setFieldValue(field.name, item.value);
                      }}
                      selectedValue={field.value}
                      styles={{
                        option: (provided) => ({
                          ...provided,
                          minHeight: 32,
                        }),
                      }}
                    ></ReactSelect>
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
        </div>

        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="bodyTemperature">
              <Label name="bodyTemperature">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('bodyTemperature')}
                </BodyTextS>
              </Label>
              <FastField name="bodyTemperature">
                {({ field, form }) => (
                  <NumberInput
                    isFloat
                    placeholder="0"
                    data-test-id="body-temperature"
                    decimalScale={3}
                    maxLimit={MAX_LIMIT}
                    defaultValue={field.value}
                    onValueChange={({ value }) =>
                      form.setFieldValue(field.name, +value || null)
                    }
                    rightElement={
                      <span className="sl-input-right-element">°C</span>
                    }
                  />
                )}
              </FastField>
            </ValidationFormGroup>
          </div>
        </div>

        <div className="row">
          <div className="col-sm-12">
            <ValidationFormGroup errors={errors} fieldName="amountOfChildren">
              <Label name="amountOfChildren">
                <BodyTextS fontWeight={500} textTransform="uppercase">
                  {t('amountOfChildren')}
                </BodyTextS>
              </Label>
              <Field name="amountOfChildren">
                {({ field, form }) => {
                  return (
                    <NumberInput
                      placeholder="0"
                      defaultValue={field.value}
                      onValueChange={({ value }) =>
                        form.setFieldValue(field.name, +value || null)
                      }
                      {...registerActionChainElementId(
                        VitalParameterActionChainElementId.AMOUNT_OF_CHIDREN_INPUT
                      )}
                    />
                  );
                }}
              </Field>
            </ValidationFormGroup>
          </div>
        </div>

        {femaleRows}
      </div>
      <div className={`${Classes.DIALOG_FOOTER} medical-dialog__footer`}>
        <div
          className={`${Classes.DIALOG_FOOTER_ACTIONS} medical-dialog__footer-actions`}
        >
          <Button
            style={{
              marginRight: 8,
            }}
            text={t('cancel')}
            intent={Intent.PRIMARY}
            large
            outlined
            minimal
            {...registerActionChainElementId(
              VitalParameterActionChainElementId.DIALOG_CANCEL_BUTTON
            )}
            onClick={close}
          />
          <Button
            style={{
              marginRight: 8,
            }}
            text={t('save')}
            type="submit"
            large
            intent={Intent.PRIMARY}
            loading={medicalDialogState.isSubmitting}
            {...registerActionChainElementId(
              VitalParameterActionChainElementId.DIALOG_SAVE_BUTTON
            )}
          />
        </div>
      </div>
    </Form>
  );
};

const MedicalDialog = (props) => {
  const { className, isOpen, close, save, patient, medicalDialogState } = props;
  const [removeAction, setRemoveAction] = useState<() => void>(null);
  const { t } = I18n.useTranslation({
    namespace: TRANSLATION_NAMESPACE,
    nestedTrans: 'Medical',
  });
  const medicalData = patient.patientMedicalData || {};
  const initialValues = useMemo(() => {
    let initialValues: PatientMedicalData = {};
    if (
      medicalDialogState.isUpdateHistory &&
      medicalDialogState.editingMedicalRecord?.patientMedicalData
    ) {
      initialValues = {
        ...medicalDialogState.editingMedicalRecord.patientMedicalData,
        careLevel:
          medicalDialogState.editingMedicalRecord.patientMedicalData
            .careLevel ?? 0,
      };
    } else if (medicalDialogState.isAdd) {
      //add new medical record
      initialValues = medicalDialogState.initData?.patientMedicalData || {
        careLevel: 0,
      };
    } else {
      // edit medical record in sidebar
      initialValues = {
        ...medicalData,
        allergies: medicalData?.allergies?.length
          ? medicalData.allergies
          : Array<Allergy>({ allergy: '', isPrescriptionRelated: false }),
        careLevel: medicalData.careLevel ?? 0,
      };
    }

    return initialValues;
  }, [
    medicalDialogState.isUpdateHistory,
    medicalDialogState.editingMedicalRecord?.patientMedicalData,
    medicalDialogState.isAdd,
    medicalDialogState.initData?.patientMedicalData,
    medicalData,
  ]);

  const submit = (formValues) => {
    const medicalData: PatientMedicalData = {
      allergies: formValues.allergies?.filter((item) => !!item.allergy) || [],
      bloodPressure: formValues.bloodPressure
        ? formValues.bloodPressure.toString()
        : null,
      heartFrequency: formValues.heartFrequency
        ? parseInt(formValues.heartFrequency)
        : null,
      height: formValues.height || null,
      weight: formValues.weight || null,
      additionalNote: formValues.additionalNote ?? null,

      // pregnant
      isBreastfeeding: formValues.isBreastfeeding || null,
      isPregnant: formValues.isPregnant || null,
      dateOfPlannedBirth: formValues.dateOfPlannedBirth
        ? Number(formValues.dateOfPlannedBirth)
        : null,
      amountOfBirth: formValues.amountOfBirth
        ? Number(formValues.amountOfBirth)
        : null,
      amountOfPregnancies: formValues.amountOfPregnancies
        ? Number(formValues.amountOfPregnancies)
        : null,
      amountOfChildren: formValues.amountOfChildren,
      isSmoker: formValues.isSmoker,
      cigarettesPerDay: formValues.isSmoker
        ? formValues.cigarettesPerDay
        : null,
      careLevel: formValues.careLevel ?? undefined,
      creatinine:
        formValues.creatinine === null
          ? formValues.creatinine
          : +formValues.creatinine,
      bodyTemperature: formValues.bodyTemperature ?? null,
      pulse: formValues.pulse ?? null,
      pulseOxiMetric: formValues.pulseOxiMetric ?? null,
    };
    save(medicalData, close);
  };

  const formInstantValidating = (values: PatientMedicalData) => {
    const errors = {} as FormikErrors<PatientMedicalData>;
    if (values.amountOfBirth && Number(values.amountOfBirth) < 0) {
      errors.amountOfBirth = t('validate.generic', {
        field: t('amountOfBirth'),
      });
    }
    if (values.amountOfPregnancies && Number(values.amountOfPregnancies) < 0) {
      errors.amountOfPregnancies = t('validate.generic', {
        field: t('amountOfPregnancies'),
      });
    }

    if (values.pulseOxiMetric && +values.pulseOxiMetric > 100) {
      errors.pulseOxiMetric = t('validate.pulseOxiMetricMaximum');
    }

    return errors;
  };

  const formikRef = useRef<FormikProps<typeof initialValues>>(null);
  const [isOpenConfirm, setIsOpenConfirm] = useState(false);

  function closeConfirmDialog() {
    if (formikRef.current?.dirty) {
      return setIsOpenConfirm(true);
    }
    close();
  }

  return (
    <>
      <Dialog
        canEscapeKeyClose={true}
        isCloseButtonShown={false}
        className={`${className} dialog-right`}
        title={
          <Flex auto>
            <H5>
              {medicalDialogState.isAdd
                ? t('addMedicalData')
                : t('editMedicalData')}
            </H5>
            <Svg
              style={{ cursor: 'pointer' }}
              src="/images/close.svg"
              onClick={closeConfirmDialog}
              {...registerActionChainElementId(
                VitalParameterActionChainElementId.DIALOG_X_BUTTON
              )}
            />
          </Flex>
        }
        isOpen={isOpen}
        onClose={closeConfirmDialog}
        canOutsideClickClose={false}
        backdropProps={{
          hidden: true,
        }}
        containerRef={setAttributeActionChainElementId(
          VitalParameterActionChainElementId.DIALOG
        )}
      >
        <Formik
          onSubmit={submit}
          innerRef={formikRef}
          initialValues={initialValues}
          validate={formInstantValidating}
          render={({ errors, values }) =>
            renderMedicalForm(
              { ...props, close: closeConfirmDialog },
              errors,
              values,
              t,
              medicalDialogState,
              setRemoveAction
            )
          }
        />
      </Dialog>
      {typeof removeAction === 'function' ? (
        <ConfirmDialog
          close={() => setRemoveAction(null)}
          confirm={removeAction}
          text={{
            btnCancel: t('allergyDialog.btnCancel'),
            btnOk: t('allergyDialog.btnOk'),
            title: t('allergyDialog.title'),
            message: t('allergyDialog.message'),
          }}
        />
      ) : null}
      <LeaveConfirmModal
        isOpen={isOpenConfirm}
        onConfirm={() => {
          setIsOpenConfirm(false);
          close();
        }}
        onClose={() => setIsOpenConfirm(false)}
      />
    </>
  );
};

export default MedicalDialog;
