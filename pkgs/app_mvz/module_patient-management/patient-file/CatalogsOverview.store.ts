import { REFERRAL_BLOCK } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-selection-plugin/components/additional-info-block';
import { useListenBlankServiceCode } from '@tutum/hermes/bff/app_mvz_blank_service';
import { Field } from '@tutum/hermes/bff/catalog_sdebm_common';
import { MainGroup } from '@tutum/hermes/bff/common';
import {
  ContractData,
  ContractMetaData,
  getContracts,
} from '@tutum/hermes/bff/legacy/app_mvz_contract';
import {
  getAdditionalInfoFields,
  getAdditionalInfoFieldsKv,
  getAdditionalInfoFieldsSelectiveContract,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import ContractSearchWebWorker from '@tutum/infrastructure/web-worker-services/contract-search-mvz/contract-search.main';
import { proxy, useSnapshot } from 'valtio';

export type ICatalogOverviewStore = {
  contractsMeta: { [key: string]: boolean };
  additionalInfos: { [key: string]: Field[] };
  loading: boolean;
};

const initStore: ICatalogOverviewStore = {
  contractsMeta: {},
  additionalInfos: {},
  loading: false,
};

const catalogsOverviewStore = proxy<ICatalogOverviewStore>(initStore);

type ICatalogOverviewActions = {
  getContractMeta: (
    selectedDate: number,
    contractId: string
  ) => Promise<ContractMetaData>;
  searchContract: (
    query: string,
    contractId: string,
    chargeSystemId?: string
  ) => Promise<ContractData[]>;
  doesContractSupportFunctions: (
    functions: string[],
    contractId: string,
    chargeSystemId?: string
  ) => Promise<boolean>;
  getAdditionalInfos: (
    scheinMainGroup: MainGroup,
    scheinKey: string,
    selectedDate?: number
  ) => Promise<Field[]>;
};

function getQuarterYearKey(selectedDate: number): string {
  const selectedDateMoment = datetimeUtil.unixToMoment(selectedDate);
  const quarterYearKey = `${datetimeUtil.getYear(
    selectedDateMoment
  )}-${datetimeUtil.getQuarter(selectedDateMoment)}`;
  return quarterYearKey;
}

const wait = (ms) => new Promise((r) => setTimeout(r, ms));

const retryOperation = (operation, delay, retries) =>
  new Promise((resolve, reject) => {
    return operation()
      .then(resolve)
      .catch((reason) => {
        if (retries > 0) {
          return wait(delay)
            .then(retryOperation.bind(null, operation, delay, retries - 1))
            .then(resolve)
            .catch(reject);
        }
        return reject(reason);
      });
  });

const loadContractsInternal = (selectedDate: number): Promise<void> => {
  return new Promise(async (resolve, reject) => {
    const quarterYearKey = getQuarterYearKey(selectedDate);
    if (catalogsOverviewStore.contractsMeta[quarterYearKey] !== true) {
      if (catalogsOverviewStore.loading === true) {
        reject('is loading');
        return;
      }
      catalogsOverviewStore.loading = true;
      const contractsResp = await getContracts({
        selectedDate,
      });
      // todo addContractsMeta also need to have perquarter cache key internally
      await ContractSearchWebWorker.addContractsMeta(
        quarterYearKey,
        contractsResp.data.contracts
      );
      catalogsOverviewStore.contractsMeta[quarterYearKey] = true;
      catalogsOverviewStore.loading = false;
    }
    resolve();
  });
};

const loadContracts = async (selectedDate: number): Promise<void> => {
  await retryOperation(() => loadContractsInternal(selectedDate), 200, 5).catch(
    (reason) => {
      if (reason !== 'is loading') {
        throw reason;
      }
    }
  );
};

export const catalogOverviewActions: ICatalogOverviewActions = {
  getContractMeta: async (selectedDate: number, contractId: string) => {
    await loadContracts(selectedDate);
    return ContractSearchWebWorker.getContractMeta(selectedDate, contractId);
  },
  searchContract: async (
    query: string,
    contractId: string,
    chargeSystemId?: string
  ) => {
    return ContractSearchWebWorker.searchContract(
      query,
      contractId,
      chargeSystemId
    );
  },
  doesContractSupportFunctions: async (
    functions: string[],
    contractId: string,
    chargeSystemId?: string
  ) => {
    return ContractSearchWebWorker.doesContractSupportFunctions(
      functions,
      contractId,
      chargeSystemId
    );
  },
  getAdditionalInfos: (
    scheinMainGroup: MainGroup,
    scheinKey: string,
    selectedDate: number
  ) => {
    const key = selectedDate ? `${scheinKey}-${selectedDate}` : scheinKey;
    if (catalogsOverviewStore.additionalInfos[key]) {
      return Promise.resolve(catalogsOverviewStore.additionalInfos[key]);
    }

    if (
      scheinMainGroup === MainGroup.HZV ||
      scheinMainGroup === MainGroup.FAV
    ) {
      return getAdditionalInfoFieldsSelectiveContract({
        contractId: scheinKey,
      }).then((fieldsResp) => {
        catalogsOverviewStore.additionalInfos[key] = fieldsResp.data.fields;
        return fieldsResp.data.fields;
      });
    }

    if (scheinMainGroup === MainGroup.KV) {
      return getAdditionalInfoFieldsKv({
        treatmentCase: scheinKey,
        selectedDate: selectedDate ?? datetimeUtil.now(),
      }).then((fieldsResp) => {
        const fields = fieldsResp.data.fields.filter(
          (field) => field.fK !== REFERRAL_BLOCK
        );
        catalogsOverviewStore.additionalInfos[key] = fields;
        return fields;
      });
    }
    return getAdditionalInfoFields().then((fieldsResp) => {
      const fields = fieldsResp.data.fields.filter(
        (field) => field.fK !== REFERRAL_BLOCK
      );
      catalogsOverviewStore.additionalInfos[key] = fields;
      return fields;
    });
  },
};

export function useCatalogOverviewStore(contract) {
  useListenBlankServiceCode(() => {
    catalogOverviewActions.doesContractSupportFunctions(
      [],
      contract.id,
      contract.chargeSystemId
    );
  });
  return useSnapshot(catalogsOverviewStore);
}
