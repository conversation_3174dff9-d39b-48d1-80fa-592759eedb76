import { isEmpty } from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import React, { useCallback, useContext, useEffect, useState } from 'react';

import { getTypeOfLetterTemplate } from '@tutum/admin/module_doctor_letter/DoctorLetter.helper';
import { LetterTemplateType } from '@tutum/admin/module_doctor_letter/DoctorLetter.type';
import {
  Flex,
  H1,
  LoadingState,
  Svg,
  TOASTER_TIMEOUT_CUSTOM,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { OverlayToaster } from '@tutum/design-system/components/Core';
import { useListenReadCard } from '@tutum/hermes/bff/app_mvz_cardservice';
import { MainGroup } from '@tutum/hermes/bff/common';
import { DoctorLetter } from '@tutum/hermes/bff/doctor_letter_common';
import {
  getPdfPresignedUrl,
  updateDoctorLetter,
  useMutationHandleBgInvoice,
  useMutationHandlePrivateInvoice,
} from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import {
  getByDoctorLetterId,
  updateStatusEAB,
} from '@tutum/hermes/bff/legacy/app_mvz_eab';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import { DocumentStatus } from '@tutum/hermes/bff/qes_common';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import I18n, {
  IFixedNamespaceTFunction,
} from '@tutum/infrastructure/i18n';
import { DATE_TIME_TRANSFER_UTC } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil, { date } from '@tutum/infrastructure/utils/datetime.util';
import { isReadByTICard } from '@tutum/mvz/_utils/cardReader';
import {
  checkIsEabTemplate,
  checkIsKvSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { bsnrActions } from '@tutum/mvz/hooks/useBsnr.store';
import { formTranslationActions } from '@tutum/mvz/hooks/useFormTranslation';
import { useLocationHash } from '@tutum/mvz/hooks/useLocationHash';
import {
  printPreviewPdfActions,
  usePrintPreviewPdfStore,
} from '@tutum/mvz/hooks/usePrintPreviewPdf.store';
import useToaster from '@tutum/mvz/hooks/useToaster';
import type CardReaderI18n from '@tutum/mvz/locales/en/CardReader.json';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { actionChainActions } from '@tutum/mvz/module_action-chain/use-action-chain';
import {
  submitLetterForBgBilling,
  submitLetterForPrivateBilling,
} from '@tutum/mvz/module_doctor-letter/DoctorLetter.helper';
import {
  doctorLetterTimelineActions,
  useDoctorLetterTimelineStore,
} from '@tutum/mvz/module_doctor-letter/DoctorLetterTimeline.store';
import { DoctorLetterCreateEditDialog } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog';
import { IActiveSchein } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog/types';
import { useHimiPrescriptionStore } from '@tutum/mvz/module_himi/himi-prescrible/himiPrescrible.store';
import type { FORM_SECTION } from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  ID_TABS,
  PatientFileState,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import PatientPage from '@tutum/mvz/module_patient-management/patient-file/PatientPage';
import { IMvzTheme } from '@tutum/mvz/theme';
import { settingActions } from '../../hooks/useSetting.store';
import PatientManagementContext from '../contexts/patient-management/PatientManagementContext';
import CreatePatient from '../create-patient-v2/CreatePatient.styled';
import CheckInformColonoscopyInfo from '../create-patient-v2/colonoscopy-info/CheckInformColonoscopyInfo';
import { useActionBarStore } from './encounter-v2/action-bar/ActionBar.store';
import { editTimelineItem } from './encounter-v2/composer/Composer.service';
import { useCurrentSchein } from './hooks/useCurrentSchein.hook';
import { useTimeLineStore } from './timeline/Timeline.store';
import InvalidOmimGChainDialog from '@tutum/mvz/module_patient-management/patient-file/InvalidOmimGChainDialog';

export interface IPatientFileProps {
  className?: string;
  theme?: IMvzTheme;
  viewPatientId?: string;
  billingQuarter?: Array<{ year: number; quarter: number }>;
}

export const TIMELINE_RESTORE_LEGEND_HEIGHT = 36;

export const toasterRef = React.createRef<OverlayToaster>();

const PatientFile: React.FC<IPatientFileProps> = (props) => {
  const { className, viewPatientId, billingQuarter } = props;

  const router = useRouter();
  const { t } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'PatientFile',
  });
  const globalContext = useContext(GlobalContext.instance);

  const { t: tForm } = I18n.useTranslation({
    namespace: 'Form',
  });

  const { t: CardReaderTranslation } = I18n.useTranslation<
    keyof typeof CardReaderI18n.CardReader.VSDStatus
  >({
    namespace: 'CardReader',
    nestedTrans: 'CardReader.VSDStatus',
  });

  const { t: tDoctorLetter } = I18n.useTranslation({
    namespace: 'DoctorLetter',
  });

  const patientFileStore = usePatientFileStore();
  const timelineStore = useTimeLineStore();
  const himiPrescriptionStore = useHimiPrescriptionStore();
  const printPreviewPdfStore = usePrintPreviewPdfStore();
  const doctorLetterTimelineStore = useDoctorLetterTimelineStore();
  const { encounterDate, doctorId } = useActionBarStore();

  const {
    activeTabId,
    schein: { activatedSchein },
  } = patientFileStore;
  const { hash, setHash } = useLocationHash();
  const toast = useToaster();
  const {
    patientManagement,
    setPatientId,
    setMedicalData,
    setMedicalDataUpdatedAt,
    reloadSelectedContractDoctor,
  } = useContext(PatientManagementContext.instance);
  const [showCreateSchein, setShowCreateSchein] = useState<MainGroup | null>(
    null
  );
  const [scheinIdToEdit, setScheinIdToEdit] = useState<string | undefined>(
    undefined
  );
  const [scrollToSection, setScrollToSection] = useState<FORM_SECTION>();

  const HandlePrivateInvoice = useMutationHandlePrivateInvoice({ retry: 2 });
  const HandleBgInvoice = useMutationHandleBgInvoice({ retry: 2 });

  const isReadTICard = isReadByTICard(patientFileStore.activeInsurance);

  let patientId = router.query?.patientId?.toString();

  const isActivatedSvSchein = patientFileStore.schein?.isActivatedSvSchein;
  const isKvSchein = checkIsKvSchein(patientFileStore.schein?.activatedSchein);
  const scheinList = patientFileStore.schein?.list;

  if (viewPatientId) {
    patientId = viewPatientId;
  }

  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();
  const currentSchein = useCurrentSchein();
  const [modelState, setModelState] = useState<PatientFileState>({
    isLoadingPatient: false,
  });

  useListenReadCard((data) => {
    if (data.currentUserId == currentLoggedinUser.id && data.isPerformed) {
      alertSuccessfully(CardReaderTranslation('VSDMPerformed'), {
        toaster: toast,
      });
    }
  });

  useEffect(() => {
    if (patientId) {
      patientFileActions.schein.resetSchein();
      actionChainActions.setProgressInfo({
        patientId,
        doctorId: globalContext?.globalData?.userProfile?.id || '',
      });
      formTranslationActions.setTranslate(tForm);
      patientFileActions.loadUserSettings();
      bsnrActions.getListBsnr();
    }
  }, [patientId]);

  useEffect(() => {
    if (patientFileStore.patient.current?.id && !isEmpty(currentLoggedinUser)) {
      patientFileActions.setHistoryViewPatient({
        patientId: patientFileStore.patient.current.id,
        doctorId: currentLoggedinUser.id || '',
      });
    }
  }, [patientFileStore.patient.current?.id, currentLoggedinUser.id]);

  const doctorList = globalContext.useGetDoctorList();

  useEffect(() => {
    if (patientId) {
      reloadSelectedContractDoctor(
        patientId,
        date(),
        currentSchein!,
        doctorList
      );
    }
  }, [patientId, currentSchein, JSON.stringify(doctorList)]);

  useEffect(() => {
    setHash(activeTabId);
  }, [activeTabId]);

  useEffect(() => {
    if (hash) {
      if (hash === '#schein') {
        patientFileActions.setIsOpenSchein(true);
      } else if (hash === '#private') {
        setShowCreateSchein(MainGroup.PRIVATE);
      } else if (hash !== activeTabId) {
        patientFileActions.setActiveTabId(hash as ID_TABS);
      }
    }
  }, [hash]);

  useEffect(() => {
    const edit = Boolean(router?.query.edit);
    if (edit) {
      patientFileActions.setIsEditPatientProfile(true);
    }

    patientFileActions.materialCosts.getMaterialCosts();

    settingActions.setPatientForPrinterSetting(patientId || null);

    return () => {
      settingActions.setPatientForPrinterSetting(null);
      patientFileActions.resetStore();
    };
  }, []);

  useEffect(() => {
    if (himiPrescriptionStore.isEditHimi) {
      patientFileActions.setActiveTabId(ID_TABS.HIMI);
    }
  }, [himiPrescriptionStore.isEditHimi]);

  useEffect(() => {
    if (!patientId) {
      setModelState({ isLoadingPatient: false });
      return;
    }
    setModelState({ isLoadingPatient: true });
    setPatientId(patientId);
  }, [patientId]);

  useEffect(() => {
    setModelState((modelState) => ({
      ...modelState,
      isLoadingPatient: patientManagement.loadingPatient,
    }));
  }, [patientManagement.loadingPatient]);

  const toggleOpenCreateSchein = useCallback(
    (scheinId?: string, scrollToSection?: FORM_SECTION) => {
      if (
        !patientFileStore.isOpenSchein &&
        scheinId &&
        typeof scheinId == 'string'
      ) {
        setScheinIdToEdit(scheinId);
        setScrollToSection(scrollToSection);
      } else {
        setScheinIdToEdit(undefined);
      }

      patientFileActions.setIsOpenSchein(!patientFileStore.isOpenSchein);
      patientFileActions.setDefaultDocumentDate(null);
    },
    [patientFileStore.isOpenSchein]
  );

  const toggleOpenScheinBySelect = (typeSchein: string, scheinId?: string) => {
    if (!showCreateSchein && scheinId && typeof scheinId == 'string') {
      setScheinIdToEdit(scheinId);
    } else {
      setScheinIdToEdit(undefined);
    }
    setShowCreateSchein(typeSchein as MainGroup);
  };

  const openEditPatientWithScroll = (sectionId: string) => {
    patientFileActions.setIsEditPatientProfile(sectionId);
  };

  const onSaveSuccess = () => {
    alertSuccessfully(t('editSuccessLb'), { timeout: TOASTER_TIMEOUT_CUSTOM });
    patientFileActions.setIsEditPatientProfile(false);
  };

  const patientPageProps = {
    patientManagement,
    viewTimelineFromOutside: viewPatientId !== undefined,
    billingQuarter: billingQuarter!,
    t,
    activeTabId,
    setActiveTabId: patientFileActions.setActiveTabId,
    toggleOpenSchein: toggleOpenCreateSchein,
    setMedicalData,
    setMedicalDataUpdatedAt,
    setIsEditingProfile: patientFileActions.setIsEditPatientProfile,
    openEditPatientWithScroll,
    scheinIdToEdit,
    currentSchein,
    isActivatedSvSchein: isActivatedSvSchein,
    activatedSchein: Boolean(activatedSchein),
    isReadTICard,
    isHistoryMode: timelineStore.isHistoryMode,
    scrollToSection,
    showCreateSchein,
    toggleOpenScheinBySelect,
    setScheinIdToEdit,
  };

  const defineScheinUseTemplate = (
    scheins: ScheinItem[],
    timelineItem: TimelineModel,
    activatedSchein?: { scheinId: string }
  ): IActiveSchein => {
    const { doctorLetter } = timelineItem;
    if (!isEmpty(doctorLetter?.privateInvoice)) {
      return {
        type: 'private',
        scheinId: doctorLetter.scheinId,
      };
    }
    if (!isEmpty(doctorLetter?.bgInvoice)) {
      return {
        type: 'bg',
        scheinId: doctorLetter.scheinId,
      };
    }
    const selectedScheinPrivate = scheins.find(
      (s) => s.invoiceNumber === doctorLetter?.privateInvoice?.invoiceNumber
    );
    if (selectedScheinPrivate) {
      return {
        type: 'private',
        scheinId: selectedScheinPrivate.scheinId,
      };
    }

    const selectedScheinBg = scheins.find(
      (s) => s.invoiceNumber === doctorLetter?.bgInvoice?.invoiceNumber
    );

    if (selectedScheinBg) {
      return {
        type: 'bg',
        scheinId: selectedScheinBg.scheinId,
      };
    }
    return {
      type: 'public',
      scheinId: activatedSchein?.scheinId,
    };
  };

  const handleSubmitDoctorLetter = async (
    newDoctorLetterValues: DoctorLetter
  ) => {
    const { id, doctorLetter, patientId, treatmentDoctorId } =
      doctorLetterTimelineStore.activeTimelineModel!;

    const newDoctorLetter = {
      ...doctorLetter,
      ...newDoctorLetterValues,
      isPrinted: false,
    };

    const privBillingId = doctorLetter?.privateInvoice?.privateBillingId!;

    const bgBillingId = doctorLetter?.bgInvoice?.billingId!;

    if (privBillingId) {
      try {
        return (await submitLetterForPrivateBilling(
          null!,
          patientManagement.patient?.id!,
          { ...newDoctorLetterValues, id: doctorLetter?.id! },
          HandlePrivateInvoice,
          'save',
          privBillingId
        )) || undefined;
      } catch (error) {
        console.error(error);
        alertError(tDoctorLetter('submitPrivateBillingFailure'));
        return undefined;
      }
    }

    if (bgBillingId) {
      try {
        return (await submitLetterForBgBilling(
          null!,
          patientManagement.patient?.id!,
          { ...newDoctorLetterValues, id: doctorLetter?.id! },
          HandleBgInvoice,
          'save',
          bgBillingId
        )) || undefined;
      } catch (error) {
        console.error(error);
        alertError(tDoctorLetter('submitBgBillingFailure'));
        return undefined;
      }
    }

    if (!id || !newDoctorLetter) {
      alertError(tDoctorLetter('editFailure'));
      return undefined;
    }

    try {
      const payload: TimelineModel = {
        id,
        auditLogs: [],
        doctorLetter: newDoctorLetter,
        year:
          doctorLetterTimelineStore.activeTimelineModel?.year ||
          datetimeUtil.date().getFullYear(),
        quarter:
          doctorLetterTimelineStore.activeTimelineModel?.quarter ||
          Math.floor((datetimeUtil.date().getMonth() + 3) / 3),
        treatmentDoctorId,
        patientId,
        selectedDate:
          doctorLetterTimelineStore.activeTimelineModel?.selectedDate! ??
          moment
            .utc(moment(datetimeUtil.date()).format(DATE_TIME_TRANSFER_UTC))
            .toDate()
            .getTime(),
      };
      const resp = await updateDoctorLetter({
        timelineModel: payload,
      });
      if (resp && resp.data && resp.data.timelineModel) {
        return resp.data.timelineModel;
      }
      return {
        ...doctorLetterTimelineStore.activeTimelineModel!,
        doctorLetter: newDoctorLetter,
      };
    } catch (error) {
      console.error(error);
      alertError(tDoctorLetter('editFailure'));
      return undefined;
    }
  };

  const handlePrintDoctorLetter = async (
    editedTimelineModel: TimelineModel,
    isPrintOnly: boolean
  ) => {
    const { id, doctorLetter } =
      editedTimelineModel ?? doctorLetterTimelineStore.activeTimelineModel;

    if (!doctorLetter?.id) {
      alertError(tDoctorLetter('printFailure'));
      return;
    }

    try {
      const data = await getPdfPresignedUrl({
        doctorLetterId: doctorLetter.id,
      });

      if (!data?.data?.url) {
        alertError(tDoctorLetter('printFailure'));
        return;
      }

      const { url } = data.data;

      printPreviewPdfActions.setPrintingDoctorLetter({
        timelineModelId: id!,
        doctorLetter,
        isPrintOnly,
      });

      printPreviewPdfActions.setOpenPrintPreviewDialog(url);
    } catch (error) {
      console.error(error);
      alertError(tDoctorLetter('openPrintPreviewFailure'));
    }
  };

  // at patient detail page (patientId)
  // check if patientId is valid from api response
  if (!patientFileStore.patient?.current?.id && patientId) {
    return <LoadingState />;
  }

  return (
    <Flex column className={className} style={{ position: 'relative' }}>
      <InvalidOmimGChainDialog
        selectedDate={encounterDate || datetimeUtil.now()}
      />
      <Flex auto>
        <PatientPage {...patientPageProps} />
        {modelState.isLoadingPatient === null && renderWelcomeScreen(t)}
      </Flex>
      {patientFileStore.isEditingProfile ? (
        <CreatePatient
          onClose={() => patientFileActions.setIsEditPatientProfile(false)}
          isOpen={patientFileStore.isEditingProfile}
          selectedPatient={patientManagement?.patient}
          onSaveSuccess={onSaveSuccess}
        />
      ) : null}
      {/* PRINTING DOCTOR LETTER */}
      {printPreviewPdfStore.fileUrl &&
        printPreviewPdfStore.printingDoctorLetter?.doctorLetter && (
          <PrintPreviewPdfDialog
            patient={patientManagement?.patient}
            timelineId={
              printPreviewPdfStore.printingDoctorLetter.doctorLetter.id
            }
            isAllowSendKim={
              checkIsEabTemplate(
                printPreviewPdfStore.printingDoctorLetter.doctorLetter
              ) && isKvSchein
            }
            formId={isKvSchein ? 'Doctor_Letter' : 'Private_Invoice'}
            file={printPreviewPdfStore.fileUrl}
            onPrintSuccess={async () => {
              const list = timelineStore.timelineState;
              const { timelineModelId, doctorLetter, isPrintOnly } =
                printPreviewPdfStore.printingDoctorLetter!;
              if (
                !isPrintOnly &&
                doctorLetter?.privateInvoice?.privateBillingId! &&
                getTypeOfLetterTemplate(doctorLetter?.type!) ===
                LetterTemplateType.Private
              ) {
                await submitLetterForPrivateBilling(
                  null!,
                  patientManagement.patient?.id!,
                  doctorLetter,
                  HandlePrivateInvoice,
                  'print',
                  doctorLetter.privateInvoice.privateBillingId
                );
                patientFileActions.schein.getScheinsOverview(
                  patientManagement.patient?.id!
                );
                alertSuccessfully(tDoctorLetter('printSuccess'));
                return;
              }
              if (doctorLetter?.bgInvoice?.billingId) {
                await submitLetterForBgBilling(
                  null!,
                  patientManagement.patient?.id!,
                  doctorLetter,
                  HandleBgInvoice,
                  'print',
                  doctorLetter.bgInvoice.billingId
                );
                patientFileActions.schein.getScheinsOverview(
                  patientManagement.patient?.id!
                );
                alertSuccessfully(tDoctorLetter('printSuccess'));
                return;
              }
              //  Below is case current schein is not private schein
              const selectedTimelineModel = list.find((t) => {
                const models = t.timelineModels;
                const selectedTimelineModel = models.find(
                  (model) => model.id === timelineModelId
                );
                return selectedTimelineModel;
              });
              if (selectedTimelineModel) {
                await editTimelineItem({
                  id: timelineModelId,
                  auditLogs: [],
                  doctorLetter: { ...doctorLetter!, isPrinted: true },
                  year: selectedTimelineModel.year,
                  quarter: selectedTimelineModel.quarter,
                  treatmentDoctorId: doctorId!,
                  patientId: patientId!,
                  selectedDate: moment
                    .utc(moment(datetimeUtil.date()))
                    .toDate()
                    .getTime(),
                });
                alertSuccessfully(tDoctorLetter('printSuccess'));
              }
              if (
                checkIsEabTemplate(
                  printPreviewPdfStore.printingDoctorLetter?.doctorLetter
                )
              ) {
                try {
                  const eabItem = await getByDoctorLetterId({
                    doctorLetterId: doctorLetter?.id!,
                  });
                  if (eabItem && eabItem.data.data) {
                    await updateStatusEAB({
                      id: eabItem.data.data.id as string,
                      status: DocumentStatus.Status_Printed,
                    });
                  }
                } catch (err) {
                  throw err;
                }
              }
            }}
            onClose={() => {
              printPreviewPdfActions.setOpenPrintPreviewDialog(null);
              doctorLetterTimelineActions.setActiveTimelineModel(null);
            }}
            titleText={tForm('preview')}
          />
        )}
      {/* OPEN FROM TIMELINE ENTRIES */}
      {doctorLetterTimelineStore.openDoctorLetterFromTimeline &&
        doctorLetterTimelineStore.activeTimelineModel?.doctorLetter != null &&
        patientManagement?.patient?.id && (
          <DoctorLetterCreateEditDialog
            activatedSchein={defineScheinUseTemplate(
              scheinList,
              doctorLetterTimelineStore.activeTimelineModel,
              activatedSchein
            )}
            successToastMessage={tDoctorLetter('saveSuccess')}
            patientId={patientManagement.patient.id}
            mode={doctorLetterTimelineStore.dialogMode}
            defaultDoctorLetterValue={
              doctorLetterTimelineStore.activeTimelineModel.doctorLetter
            }
            onClose={() =>
              doctorLetterTimelineActions.setOpenDoctorLetterFromTimeline(false)
            }
            onPrint={handlePrintDoctorLetter}
            onSubmit={handleSubmitDoctorLetter}
          />
        )}
      {!!patientManagement.patient && (
        <CheckInformColonoscopyInfo
          patient={patientManagement.patient}
          contracts={patientManagement.availableHzvContracts}
        />
      )}
    </Flex>
  );
};

function renderWelcomeScreen(
  t: IFixedNamespaceTFunction<keyof typeof PatientManagementI18n.PatientFile>
) {
  return (
    <Flex auto column className="welcome-screen" align="center">
      <H1
        fontSize="48"
        lineHeight="48px"
        fontWeight="Normal"
        margin="0 0 24px 0"
      >
        {t('WELCOME_TO_TUTUM')}
      </H1>
      <H1 fontWeight="Normal" margin="0 0 64px 0">
        {t('SEARCH_OR_ADD_PATIENT')}
      </H1>

      <Svg size="auto" src="/images/homescreen.png" />
    </Flex>
  );
}

export default PatientFile;
