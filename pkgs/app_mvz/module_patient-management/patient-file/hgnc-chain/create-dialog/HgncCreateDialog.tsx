
import { Field, FieldArray, Formik } from 'formik';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';

import {
  Button,
  Dialog,
  Flex,
  FormGroup2,
  Svg,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { InputGroup } from '@tutum/design-system/components/Core';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import {
  CreateHgncChainRequest,
  useMutationCreateHgncChain,
  useMutationUpdateHgncChain,
} from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import i18n from '@tutum/infrastructure/i18n';
import HgncSelect from './HgncSelect';
import { HgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import { HgncItem } from '@tutum/hermes/bff/legacy/hgnc_common';

const minusCircle = '/images/minus-circle.svg';

export interface IHgncCreateProps {
  className?: string;
  isOpen: boolean;
  onClose: () => void;
  defaultValue?: HgncChain;
  onSuccess: () => void;
}

const INIT_OMIM_CODES: HgncItem = {
  code: '',
  description: '',
};

const INIT_CHAIN: HgncItem[] = Array(4)
  .fill(null)
  .map(() => ({
    code: '',
    description: '',
  }));

const HgncCreateDialog = ({
  className,
  isOpen,
  onClose,
  defaultValue,
  onSuccess,
}: IHgncCreateProps) => {
  const { t } = i18n.useTranslation<
    keyof typeof PatientManagementI18n.HgncChain
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'HgncChain',
  });

  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { t: tForm } = i18n.useTranslation<
    keyof typeof CommonLocales.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const { mutate: createHgncChain, isPending: isCreating } =
    useMutationCreateHgncChain({
      onSuccess: () => {
        alertSuccessfully(t('createHgncChainSuccess'));
        onSuccess();
        onClose();
      },
    });

  const { mutate: updateHgncChain, isPending: isUpdating } =
    useMutationUpdateHgncChain({
      onSuccess: () => {
        alertSuccessfully(t('updateHgncChainSuccess'));
        onSuccess();
        onClose();
      },
    });

  let initialValues = null;
  if (defaultValue) {
    initialValues = {
      ...defaultValue,
      chain: (defaultValue?.hgncItems || []).map((item) => ({
        ...item,
        label: item.code + ': ' + item.description,
        value: item.code,
      })),
    };
  }

  return (
    <Dialog
      className={getCssClass(
        'bp5-dialog-halfscreen',
        'bp5-dialog-content-scrollable',
        className
      )}
      title={t(defaultValue ? 'edit' : 'add')}
      isOpen={isOpen}
      onClose={onClose}
    >
      <Formik<CreateHgncChainRequest>
        initialValues={
          initialValues ?? {
            name: '',
            chain: INIT_CHAIN,
          }
        }
        validate={(values) => {
          const errors = {};

          if (!values['name']) {
            errors['name'] = tForm('fieldRequired');
          }

          if (!values['chain'][0].code) {
            errors['chain.0'] = tForm('fieldRequired');
          }

          return errors;
        }}
        onSubmit={(values) => {
          const chains = values.chain.filter((chain) => !!chain?.code);
          if (defaultValue) {
            updateHgncChain({
              ...values,
              hgncItems: chains,
              id: defaultValue.id,
            });
          } else {
            createHgncChain({
              ...values,
              hgncItems: chains,
            });
          }
        }}
      >
        {({ submitForm, errors, touched, submitCount, values }) => {
          return (
            <Flex column gap={20} p={16}>
              <FormGroup2
                name="name"
                isRequired
                errors={errors}
                touched={touched}
                label={t('name')}
              >
                <Field name="name">
                  {({ field }) => <InputGroup {...field} />}
                </Field>
              </FormGroup2>
              <FormGroup2
                isRequired
                touched={touched}
                name="chain"
                errors={errors}
              >
                <FieldArray
                  name="chain"
                  render={({ remove, push }) => {
                    return (
                      <Flex column pb={16}>
                        {values &&
                          values.chain &&
                          values.chain.map((_, idx) => {
                            return (
                              <Flex key={idx} mb={15} gap={12}>
                                <Flex gap={16} mb={10} w="100%">
                                  <FormGroup2
                                    isRequired={idx === 0}
                                    label={t('hgncCodes', { code: idx + 1 })}
                                    submitCount={submitCount}
                                    errors={errors}
                                    touched={touched}
                                    name={`chain.${idx}`}
                                  >
                                    <Field name={`chain.${idx}`}>
                                      {({ field, form }) => {
                                        return (
                                          <HgncSelect
                                            field={field}
                                            form={form}
                                          />
                                        );
                                      }}
                                    </Field>
                                  </FormGroup2>
                                </Flex>
                                {idx > 0 && (
                                  <Svg
                                    className="sl-remove"
                                    src={minusCircle}
                                    onClick={() => remove(idx)}
                                  />
                                )}
                              </Flex>
                            );
                          })}
                        <Flex mt={8}>
                          <Button
                            intent="primary"
                            outlined
                            minimal
                            onClick={() => push(INIT_OMIM_CODES)}
                          >
                            {t('addCode')}
                          </Button>
                        </Flex>
                      </Flex>
                    );
                  }}
                />
              </FormGroup2>
              <Flex justify="flex-end" gap={10}>
                <Button
                  outlined
                  intent="primary"
                  text={tButtonActions('cancelText')}
                  onClick={onClose}
                />
                <Button
                  intent="primary"
                  text={tButtonActions(defaultValue ? 'updatedText' : 'create')}
                  loading={isCreating || isUpdating}
                  disabled={isCreating || isUpdating}
                  type="submit"
                  onClick={submitForm}
                />
              </Flex>
            </Flex>
          );
        }}
      </Formik>
    </Dialog>
  );
};

export default HgncCreateDialog;
