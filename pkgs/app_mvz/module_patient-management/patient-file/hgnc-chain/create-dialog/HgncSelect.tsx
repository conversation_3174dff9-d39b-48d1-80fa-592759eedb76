import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { ReactSelect } from '@tutum/design-system/components';
import { searchHgnc } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { HgncItem } from '@tutum/hermes/bff/legacy/hgnc_common';

export interface IHgncSelect {
  field: any;
  form: any;
}
const HgncSelect = (props: IHgncSelect) => {
  const { field, form } = props;
  const now = datetimeUtil.now();
  const [inputValue, setInputValue] = useState('');

  const { isLoading, data } = useQuery({
    queryKey: [inputValue],
    queryFn: () => searchHgnc({ query: inputValue, selectedDate: now }),
    select: (res) => {
      return res.data.items.map((e: HgncItem) => ({
        ...e,
        label: e.code + ': ' + e.description,
        value: e.code,
      }));
    },
    enabled: !!inputValue,
  });

  return (
    <ReactSelect
      inputValue={inputValue}
      onInputChange={(newValue) => {
        setInputValue(newValue);
      }}
      selectedValue={field.value.label}
      onItemSelect={(item) => {
        form.setFieldValue(field.name, item);
      }}
      items={data ?? []}
      isLoading={isLoading}
    />
  );
};

export default HgncSelect;
