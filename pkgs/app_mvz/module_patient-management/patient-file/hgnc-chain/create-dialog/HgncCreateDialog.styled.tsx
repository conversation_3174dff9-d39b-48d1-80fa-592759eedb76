import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import Theme from '@tutum/mvz/theme';
import { scaleSpacePx } from '@tutum/design-system/styles';
import HgncCreateDialog from './HgncCreateDialog';
import { IHgncCreateProps } from './HgncCreateDialog';

const styled = Theme.styled;
const HgncCreateDialogStyled: React.ComponentType<IHgncCreateProps> = styled(
  HgncCreateDialog
).attrs(({ className }) => ({
  className: getCssClass('sl-Omim-Create-Dialog', className),
}))`
  & {
    .bp5-dialog {
      width: 768px;
    }

    .bp5-form-group {
      width: 100%;
    }

    .sl-remove {
      width: 22px;
      cursor: pointer;
    }

    .search-input {
      max-width: 220px;
      padding: 0 16px;
      .bp5-input-left-container {
        top: ${scaleSpacePx(3)};
        left: ${scaleSpacePx(7)};
      }
    }
  }
`;

export default HgncCreateDialogStyled;
