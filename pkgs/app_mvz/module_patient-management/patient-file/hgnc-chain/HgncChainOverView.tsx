import { Dialog, Box } from '@tutum/design-system/components';
import { OmimGChain } from '@tutum/hermes/bff/legacy/app_catalog_omimg_chain';
import i18n from '@tutum/infrastructure/i18n';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { NodeKey } from 'lexical';
import HgncChainTable from './HgncChainTable';
import { HgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';

export interface IProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (_: HgncChain) => void;
  onCreate: () => void;
  query: string;
  setQuery: (_: string) => void;
  additionalInfoNodeKey: NodeKey;
}

const HgncChainOverview = ({
  isOpen,
  onClose,
  onEdit,
  onCreate,
  query,
  setQuery,
  additionalInfoNodeKey,
}: IProps) => {
  const { t } = i18n.useTranslation<
    keyof typeof PatientManagementI18n.HgncChain
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'HgncChain',
  });

  return (
    <Dialog title={t('overview')} isOpen={isOpen} onClose={onClose}>
      <Box p={20}>
        <HgncChainTable
          onClose={onClose}
          onCreate={onCreate}
          onEdit={onEdit}
          query={query}
          setQuery={setQuery}
          additionalInfoNodeKey={additionalInfoNodeKey}
        />
      </Box>
    </Dialog>
  );
};

export default HgncChainOverview;
