import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  alertSuccessfully,
  BodyTextM,
  BodyTextS,
  Button,
  Flex,
  Svg,
  Tooltip,
} from '@tutum/design-system/components';
import {
  InputGroup,
  Menu,
  MenuItem,
  Popover,
  Spinner,
} from '@tutum/design-system/components/Core';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { PAGINATION_DEFAULT } from '@tutum/design-system/consts/table';
import {
  useMutationDeleteHgncChain,
  useQuerySearchHgncChain,
} from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import I18n from '@tutum/infrastructure/i18n';
import { useState } from 'react';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { NodeKey } from 'lexical';
import { HgncChain } from '@tutum/hermes/bff/legacy/app_catalog_hgnc_chain';
import { ON_SELECT_HGNC_CHAIN } from '@tutum/design-system/composer/service-node-lexical/plugins/additional-info-hgnc-chain-plugins';

const plusIcon = '/images/plus-white.svg';
const moreIcon = '/images/more-vertical.svg';
const editIcon = '/images/edit-value.svg';
const trashIcon = '/images/delete-value.svg';

interface IProps {
  onClose: () => void;
  onCreate: () => void;
  onEdit: (_: HgncChain) => void;
  query: string;
  setQuery: (_: string) => void;
  additionalInfoNodeKey: NodeKey;
}

const HgncChainTable = ({
  onClose,
  onEdit,
  query,
  setQuery,
  onCreate,
  additionalInfoNodeKey: addtionalInfoNodeKey,
}: IProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.HgncChain
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'HgncChain',
  });

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [pagination, setPagination] = useState(PAGINATION_DEFAULT);
  const [selectedRow, setSelectedRow] = useState<HgncChain | undefined>(undefined);
  const [currentRow, setCurrentRow] = useState<HgncChain | undefined>(undefined);
  const [editor] = useLexicalComposerContext();

  const { data, isFetching, refetch } = useQuerySearchHgncChain({
    name: query,
    paginationRequest: pagination,
  });

  const onChangePage = (page: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      page,
    }));
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      pageSize: currentRowsPerPage,
    }));
  };

  const deleteChain = useMutationDeleteHgncChain({
    onSuccess: () => {
      refetch();
      setCurrentRow(undefined);
      alertSuccessfully(t('titleConfirmSuccess'));
    },
  });

  const isLoading = isFetching || deleteChain.isPending;

  return (
    <>
      <Flex justify="space-between">
        <InputGroup
          style={{ width: 250 }}
          value={query}
          placeholder={t('placeholder')}
          onValueChange={(value) => setQuery(value)}
        />
        <Tooltip content={t('add')}>
          <Button
            intent="primary"
            icon={<Svg src={plusIcon} />}
            onClick={() => onCreate()}
          >
            {t('createChain')}
          </Button>
        </Tooltip>
      </Flex>
      <Table
        customStyles={{
          table: {
            style: {
              marginTop: 10,
              minHeight: 300,
            },
          },
          cells: {
            style: {
              padding: '4px 12px',
            },
          },
          rows: {
            style: {
              minHeight: 'auto',
            },
          },
        }}
        columns={genColumns(t, setCurrentRow, onEdit)}
        data={data?.hgncChains || []}
        pagination={data?.pagination.total! > PAGINATION_DEFAULT.pageSize}
        progressPending={isLoading}
        paginationTotalRows={data?.pagination?.total || 0}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
        selectableRowsSingle
        selectableRows
        onSelectedRowsChange={(state) => {
          if (state.selectedRows.length === 1) {
            setSelectedRow(state.selectedRows[0] as HgncChain);
          } else {
            setSelectedRow(undefined);
          }
        }}
        footer={
          <Flex justify="flex-end">
            <Button
              intent="primary"
              disabled={!selectedRow}
              onClick={() => {
                editor.dispatchCommand(ON_SELECT_HGNC_CHAIN, {
                  chain: selectedRow?.hgncItems!,
                  nodeKeyToReplace: addtionalInfoNodeKey,
                });
                onClose();
              }}
            >
              {isLoading ? <Spinner size={20} /> : t('select')}
            </Button>
          </Flex>
        }
      />
      <ConfirmDialog
        isOpen={!!currentRow}
        text={{
          btnCancel: tButtonActions('no'),
          btnOk: tButtonActions('yesRemove'),
          title: t('titleConfirmRemove'),
          message: t('titleConfirmDescription'),
        }}
        isLoading={deleteChain.isPending}
        close={() => {
          setCurrentRow(undefined);
        }}
        confirm={() => {
          deleteChain.mutate({ id: currentRow?.id! });
        }}
      />
    </>
  );
};

function genColumns(
  t: IFixedNamespaceTFunction<keyof typeof PatientManagementI18n.HgncChain>,
  onDelete: (_: HgncChain) => void,
  onEdit: (_: HgncChain) => void
): IDataTableColumn<HgncChain>[] {
  return [
    {
      name: t('name'),
      cell: (row) => row.name,
    },
    {
      name: t('hgncChain'),
      width: '400px',
      cell: (row) => {
        return (
          <Flex column>
            {(row?.hgncItems || []).map((e, idx) => {
              return (
                <Flex key={idx} gap={5} align="center">
                  <BodyTextS fontWeight="SemiBold">{e.code + ':'}</BodyTextS>
                  <BodyTextM>{e.description}</BodyTextM>
                </Flex>
              );
            })}
          </Flex>
        );
      },
    },
    {
      name: '',
      width: '52px',
      cell: (row) => (
        <Popover
          minimal
          content={
            <Menu key="menu-more">
              <MenuItem
                icon={<Svg src={editIcon} />}
                text={t('edit')}
                onClick={() => onEdit(row)}
              />
              <MenuItem
                icon={<Svg src={trashIcon} />}
                text={t('remove')}
                onClick={() => onDelete(row)}
              />
            </Menu>
          }
        >
          <Button iconOnly minimal icon={<Svg src={moreIcon} />} />
        </Popover>
      ),
    },
  ];
}

export default HgncChainTable;
