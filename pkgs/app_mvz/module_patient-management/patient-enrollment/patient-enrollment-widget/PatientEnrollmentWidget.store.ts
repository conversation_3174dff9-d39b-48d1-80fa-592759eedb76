import { PatientEnrollmentResponse } from '@tutum/hermes/bff/service_domains_enrollment';
import { proxy, useSnapshot } from 'valtio';
import { GroupEnrollmentState } from './fav-button/FavButton.model';

interface IEnrollmentStore {
  favEnrollmentState: GroupEnrollmentState | undefined;
}

const initStore: IEnrollmentStore = {
  favEnrollmentState: undefined,
};

const store = proxy<IEnrollmentStore>(initStore);

interface IEnrollmentActions {
  setFavEnrollmentState: (favEnrollmentState: GroupEnrollmentState) => void;
  setCurrentFavEnrollment: (
    currentFavEnrollment: PatientEnrollmentResponse
  ) => void;
}

export const enrollmentActions: IEnrollmentActions = {
  setFavEnrollmentState: (favEnrollmentState) => {
    store.favEnrollmentState = favEnrollmentState;
  },
  setCurrentFavEnrollment: (currentFavEnrollment) => {
    if (!store.favEnrollmentState) {
      store.favEnrollmentState = new GroupEnrollmentState();
    }
    store.favEnrollmentState.currentEnrollment = currentFavEnrollment;
  },
};

export const useEnrollmentStore = () => useSnapshot(store);
