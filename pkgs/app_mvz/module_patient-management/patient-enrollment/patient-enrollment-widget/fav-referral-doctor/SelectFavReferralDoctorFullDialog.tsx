import { Button, Dialog } from '@tutum/design-system/components';
import { SdavCatalog } from '@tutum/hermes/bff/catalog_sdav_common';
import ExternalAddressOverview from '@tutum/mvz/module_external-address/external-address-overview';
import { useState } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import ExternalI18nKey from '@tutum/mvz/locales/en/ExternalAddress.json';

export interface SelectFavReferralDoctorFullDialogProps {
  className?: string;
  isOpen?: boolean;
  setIsOpen?: (open: boolean) => void;
  onSave?: (data?: SdavCatalog) => void;
}
export const SelectFavReferralDoctorFullDialog = ({
  className,
  isOpen,
  onSave,
  setIsOpen,
}: SelectFavReferralDoctorFullDialogProps) => {
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: tExternalAddress } = I18n.useTranslation<
    keyof typeof ExternalI18nKey.externalAddress
  >({
    namespace: 'ExternalAddress',
    nestedTrans: 'externalAddress',
  });
  const [selectedRow, setSelectedRow] = useState<SdavCatalog | undefined>(undefined);
  const onSelectedRowsChange = (data: any) => {
    setSelectedRow(data);
  };
  return (
    <Dialog
      className={className}
      isOpen={!!isOpen}
      onClose={() => setIsOpen?.(false)}
    >
      <div className="table">
        <ExternalAddressOverview onSelectedRowsChange={onSelectedRowsChange} />
      </div>
      <div className="action">
        <Button intent="primary" outlined onClick={() => setIsOpen?.(false)}>
          {tButtonActions('cancelText')}
        </Button>
        <Button
          intent="primary"
          onClick={() => {
            setIsOpen?.(false);
            onSave?.(selectedRow);
          }}
        >
          {tExternalAddress('btnSelect')}
        </Button>
      </div>
    </Dialog>
  );
};
