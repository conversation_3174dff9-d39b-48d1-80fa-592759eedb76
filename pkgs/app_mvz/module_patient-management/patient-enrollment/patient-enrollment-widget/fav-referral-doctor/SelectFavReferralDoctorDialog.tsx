import {
  BodyTextM,
  coreComponents,
  Dialog,
  Button,
  DropdownIndicator,
} from '@tutum/design-system/components';
import { debounce } from '@tutum/design-system/infrastructure/utils';
import { Order } from '@tutum/hermes/bff/common';
import { useQueryGetSdav } from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdav';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { useState } from 'react';
import ReactSelect from 'react-select';
import FormGroup2 from '@tutum/design-system/components/FormGroup2';
import SelectFavReferralDoctorFullDialog from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/fav-referral-doctor/SelectFavReferralDoctorFullDialog.styled';
import { SdavCatalog } from '@tutum/hermes/bff/legacy/catalog_sdav_common';
import I18n from '@tutum/infrastructure/i18n';
import ScheinI18n from '@tutum/mvz/locales/en/Schein.json';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import ExternalI18nKey from '@tutum/mvz/locales/en/ExternalAddress.json';

const PairValue = ({ label, value }) => {
  return (
    <div className="pair-value">
      <BodyTextM>{label}</BodyTextM>
      <BodyTextM>{value}</BodyTextM>
    </div>
  );
};

const getLabel = (option: any, showLanrBsnr?: boolean) => {
  const doctorLabel = [
    option?.doctorInfo?.title,
    option?.doctorInfo?.firstName,
    option?.doctorInfo?.lastName,
  ]
    .filter((label) => Boolean(label))
    .join(' ');

  const lanr = option?.doctorInfo?.lanr;
  const bsnr = option?.generalInfo?.bsnr;
  const lanrBsnrLabel = `LANR: ${lanr} | BSNR: ${bsnr}`;

  return `${doctorLabel} ${showLanrBsnr ? lanrBsnrLabel : ''}`;
};
const ReferralOptionRender = ({ option }) => {
  return (
    <div>
      <div className="sender-info">
        <BodyTextM>{getLabel(option, false)}</BodyTextM>
        <BodyTextM>{`LANR: ${option?.doctorInfo?.lanr} | BSNR: ${option?.generalInfo?.bsnr}`}</BodyTextM>
      </div>
    </div>
  );
};

export interface SelectFavReferralDoctorDialogProps {
  className?: string;
  isOpen?: boolean;
  isSubmitting: boolean;
  setIsOpen?: (open: boolean) => void;
  onSubmit?: (data: SdavCatalog) => void;
}

export const SelectFavReferralDoctorDialog = ({
  className,
  isOpen = false,
  isSubmitting,
  setIsOpen,
  onSubmit,
}: SelectFavReferralDoctorDialogProps) => {
  const { t: tExternalAddress } = I18n.useTranslation<
    keyof typeof ExternalI18nKey.externalAddress
  >({
    namespace: 'ExternalAddress',
    nestedTrans: 'externalAddress',
  });
  const { t } = I18n.useTranslation<keyof typeof ScheinI18n.scheinOverview>({
    namespace: 'Schein',
    nestedTrans: 'scheinOverview',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [fullDialogOpen, setFullDialogOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<SdavCatalog | undefined>(undefined);
  const [query, setQuery] = useState('');

  const { data, isLoading } = useQueryGetSdav({
    pagination: {
      page: 1,
      pageSize: 50,
      sortBy: 'bsnr',
      order: Order.ASC,
    },
    query,
  });

  const handleSearch = debounce(300, (query: string) => setQuery(query));

  return (
    <Dialog
      portalClassName={className}
      isOpen={isOpen}
      title={t('makeAsReferral')}
      onClose={() => setIsOpen?.(false)}
    >
      <FormGroup2 label={t('referralDoctor')} isRequired>
        <ReactSelect
          placeholder={t('referralDoctorSelect')}
          isSearchable
          isLoading={isLoading}
          onInputChange={handleSearch}
          className="form-select"
          classNamePrefix="form-select"
          options={data?.items}
          components={{
            DropdownIndicator,
            Option: (item: any) => {
              return (
                <coreComponents.Option {...item}>
                  <ReferralOptionRender option={item?.data} />
                </coreComponents.Option>
              );
            },
            Menu: (menuProps) => {
              return (
                <coreComponents.Menu {...menuProps}>
                  {menuProps.children}
                  <div
                    className="view-all"
                    onClick={() => setFullDialogOpen(true)}
                  >
                    {tButtonActions('viewAll')}
                  </div>
                </coreComponents.Menu>
              );
            },
          }}
          getOptionLabel={(option: SdavCatalog) => getLabel(option, true)}
          getOptionValue={(option: SdavCatalog) =>
            option?.doctorInfo?.lanr + '-' + option?.generalInfo?.bsnr
          }
          onChange={(item) => setSelectedValue(item || undefined)}
          value={selectedValue}
        />
      </FormGroup2>
      {selectedValue && (
        <div className="doctor-information">
          {selectedValue?.generalInfo?.bsnr && (
            <PairValue label="BSNR:" value={selectedValue?.generalInfo?.bsnr} />
          )}
          {selectedValue?.doctorInfo?.lanr && (
            <PairValue label="LANR:" value={selectedValue?.doctorInfo?.lanr} />
          )}
          {selectedValue?.doctorInfo?.areaOfExpertises && (
            <PairValue
              label={tExternalAddress('expertise')}
              value={selectedValue?.doctorInfo?.areaOfExpertises}
            />
          )}
        </div>
      )}
      <SelectFavReferralDoctorFullDialog
        isOpen={fullDialogOpen}
        setIsOpen={setFullDialogOpen}
        onSave={(data) => setSelectedValue(data)}
      />
      <div className="action">
        <Button
          intent="primary"
          outlined
          disabled={isSubmitting}
          onClick={() => setIsOpen?.(false)}
        >
          {tButtonActions('cancelText')}
        </Button>
        <Button
          intent="primary"
          disabled={!selectedValue}
          loading={isSubmitting}
          onClick={() => {
            onSubmit?.(selectedValue!);
          }}
        >
          {tButtonActions('saveText')}
        </Button>
      </div>
    </Dialog>
  );
};
