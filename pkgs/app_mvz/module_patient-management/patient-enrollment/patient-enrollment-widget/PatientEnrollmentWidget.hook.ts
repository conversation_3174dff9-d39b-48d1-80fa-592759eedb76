import { useState, useEffect } from 'react';
import { CheckHpmServiceConnectionResponse } from '@tutum/hermes/bff/service_domains_enrollment';
import { checkHpmServiceConnection } from '@tutum/hermes/bff/legacy/app_mvz_enrollment';

function useCheckHpmServiceConnection(
  patientId: string
) {
  const [
    checkHpmServiceConnectionResponse,
    setCheckHpmServiceConnectionResponse,
  ] = useState<CheckHpmServiceConnectionResponse | undefined>(undefined);
  useEffect(() => {
    if (!patientId) {
      return;
    }
    checkHpmServiceConnection({
      patientId: patientId,
    })
      .then((res) => {
        setCheckHpmServiceConnectionResponse({ ...res.data });
      })
      .catch((err) => {
        setCheckHpmServiceConnectionResponse({
          ok: false,
          hpmInformation: [],
        });
        console.trace('err', err);
      });
  }, [patientId]);
  return checkHpmServiceConnectionResponse;
}

export const PatientEnrollmentWidgetHook = {
  useCheckHpmServiceConnection,
};
