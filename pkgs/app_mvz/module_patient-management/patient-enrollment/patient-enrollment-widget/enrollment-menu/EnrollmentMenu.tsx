import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { memo, useCallback, useEffect, useRef, useState } from 'react';

import {
  BodyTextS,
  Flex,
  LoadingState,
  // LoadingState,
  Svg,
} from '@tutum/design-system/components';
import {
  Intent,
  Menu,
  MenuDivider,
  MenuItem,
  OverlayToaster,
  Tag,
  Tooltip,
} from '@tutum/design-system/components/Core';
import {
  getCssClass,
  parseDate,
  toDateFormat,
} from '@tutum/design-system/infrastructure/utils';
import { MainGroup } from '@tutum/hermes/bff/legacy/common';
import { PatientEnrollmentResponse } from '@tutum/hermes/bff/legacy/service_domains_enrollment';
import {
  PatientParticipation,
  PatientParticipationStatus,
} from '@tutum/hermes/bff/legacy/service_domains_patient_participation';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import { PatientEnrollmentInformationAction } from '@tutum/hermes/bff/service_domains_enrollment';
import { HpmInformationStatus } from '@tutum/hermes/bff/service_domains_profile';
import AccountManagementUtil from '@tutum/infrastructure/utils/form.util';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import {
  filterHpmAction,
  filterNotHpmAction,
} from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/enrollment-menu/EnrollmentMenu.helper';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import ProfileService from '@tutum/mvz/services/profile.service';
import { IPatientProfile } from '../../../types/profile.type';
import {
  HpmViewInformation,
  IEnrollmentMenuActionItem,
  ParticipationAction,
  ParticipationButtonAction,
} from '../participation/Participation.model';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { Form } from '@tutum/hermes/bff/service_domains_patient_file';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';

export interface IEnrollmentMenuProps {
  className?: string;
  patient?: IPatientProfile;
  enrollmentActionItems: IEnrollmentMenuActionItem[] | null;
  participationActionItems: IEnrollmentMenuActionItem[] | null;
  textTopMenuDivider?: string;

  currentParticipation: PatientParticipation | undefined;
  hpmInformation: HpmViewInformation;
  terminateLogInformation: string;

  showHpmInformation?: boolean;
  // showCheckParticipation?: boolean;
  onClick?: (action: ParticipationAction) => void;
  type: MainGroup.HZV | MainGroup.FAV;
  formEntry?: TimelineModel;
  enrollment?: PatientEnrollmentResponse;
}

class EnrollmentMenuMemoState {
  // hpmInformation: HpmInformation;
  hpmToolTipContent: string;
}
class EnrollmentMenuLogState {
  actionInformations: Map<ParticipationAction, string>;
}

const HpmInformationClass = {
  [HpmInformationStatus.HpmInformationStatus_Active]: 'hpm-text-status-active',
  [HpmInformationStatus.HpmInformationStatus_InActive]:
    'hpm-text-status-inActive',
  [HpmInformationStatus.HpmInformationStatus_Error]: 'hpm-text-status-error',
};
function EnrollmentMenuMemo(
  props: IEnrollmentMenuProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.EnrollmentMenu>
) {
  const {
    t,
    terminateLogInformation,
    showHpmInformation,
    // showCheckParticipation = true,
    formEntry,
  } = props;

  const toasterRef = useRef<OverlayToaster>(null);

  const [modelState, setModelState] = useState<EnrollmentMenuMemoState>({
    hpmToolTipContent: '',
  });

  const [modelStateLog, setModelStateLog] = useState<EnrollmentMenuLogState>({
    actionInformations: new Map(),
  });

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    formEntry?.encounterForm?.prescribe?.treatmentDoctorId,
    formEntry?.assignedToBsnrId
  );
  const loadToolTip = useCallback(async () => {
    if (!props.hpmInformation) {
      return;
    }
    let hpmToolTipContent = '';
    switch (props.hpmInformation?.status) {
      case HpmInformationStatus.HpmInformationStatus_InActive: {
        hpmToolTipContent = t('hpmInactiveToolTip');
        break;
      }
      case HpmInformationStatus.HpmInformationStatus_Active: {
        hpmToolTipContent = t('hpmActive');
        if (!props.currentParticipation?.status) {
          hpmToolTipContent = t('hpmActiveWithMvzNotEnrollment');
        } else if (
          props.currentParticipation?.status ===
          PatientParticipationStatus.PatientParticipation_Terminated
        ) {
          let employee: IEmployeeProfile[] | null = null;
          if (props.currentParticipation.updatedBy) {
            employee = await ProfileService.getEmployeeProfilesByIds([
              props.currentParticipation.updatedBy,
            ]).then((e) => {
              return e;
            });
          }
          const updatedDate = props.currentParticipation.updatedDate
            ? parseDate(props.currentParticipation.updatedDate)
            : null;

          hpmToolTipContent = t('hpmActiveWithMvzTerminated', {
            updatedBy: employee?.length ? employee[0].fullName : '',
            updatedDate: updatedDate
              ? toDateFormat(updatedDate, {
                dateFormat: 'dd.MM.yyyy',
              })
              : '',
          });
        }
        break;
      }
      default: {
        hpmToolTipContent = t('hpmConnectionFail');
      }
    }

    setModelState((state) => ({
      ...state,
      hpmToolTipContent: hpmToolTipContent,
    }));
  }, [props.hpmInformation, props.currentParticipation]);

  useEffect(() => {
    loadToolTip();
  }, [props.hpmInformation, props.currentParticipation]);

  useEffect(() => {
    setModelStateLog((state) => {
      const logs = new Map(state.actionInformations);
      if (
        !props.hpmInformation?.checkedTime &&
        logs.has(
          ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation
        )
      ) {
        logs.delete(
          ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation
        );
      } else if (props.hpmInformation?.checkedTime) {
        const checkDate = parseDate(props.hpmInformation.checkedTime) as Date;
        const content = t('lastCheckHpmParticipation', {
          checkedDate: toDateFormat(checkDate, {
            dateFormat: 'dd.MM.yyyy',
          }),
          checkedTime: toDateFormat(checkDate, {
            timeFormat: 'hh:mm',
            showAMPM: false,
          }),
        });
        logs.set(
          ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation,
          content
        );
      }
      return {
        ...state,
        actionInformations: logs,
      };
    });
  }, [props.hpmInformation]);

  useEffect(() => {
    setModelStateLog((state) => {
      const informations = new Map(state.actionInformations);
      const action =
        props.type === MainGroup.HZV
          ? PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation
          : PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateFavGroupContract;

      if (terminateLogInformation) {
        informations.set(action, terminateLogInformation);
      } else {
        informations.delete(action);
      }

      return {
        ...state,
        actionInformations: informations,
      };
    });
  }, [terminateLogInformation]);

  const hasHpmCheckOnlineParticipation =
    props.participationActionItems?.some(filterHpmAction);

  const renderParticipationActionItems = (item) => {
    return (
      <MenuItem
        shouldDismissPopover={!item.needPopoverForm}
        key={item.text}
        text={
          <>
            <span>{item.text}</span>
            {modelStateLog.actionInformations?.has(item.action) && (
              <BodyTextS className="menu-footer-item-message">
                {modelStateLog.actionInformations.get(item.action)}
              </BodyTextS>
            )}
          </>
        }
        className={getCssClass(
          item.className,
          modelStateLog.actionInformations?.has(item.action)
            ? 'enrollment-menu-item-has-footer'
            : ''
        )}
        onClick={() => {
          props.onClick ? props.onClick(item.action) : {};
        }}
        icon={
          item.icon ? (
            <Svg
              className={'svg-information-icon'}
              src={`/images/${item.icon}`}
            />
          ) : (
            false
          )
        }
      />
    );
  };

  return (
    <Flex className={props.className}>
      <Menu>
        <>
          {props.textTopMenuDivider && (
            <MenuDivider
              className="top-divider"
              title={
                <BodyTextS className={'top-menu-divider-text'}>
                  {props.textTopMenuDivider}
                </BodyTextS>
              }
            />
          )}
          {!!props.enrollmentActionItems?.length && (
            <>
              {props.enrollmentActionItems.map((item) => {
                // Edge case for print enrollment form
                if (
                  item.action ===
                  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ParticipationForm &&
                  formEntry
                ) {
                  return (
                    <MenuItem
                      shouldDismissPopover={true}
                      key={item.text}
                      text={
                        <span>
                          {item.text}{' '}
                          <Tag round intent={Intent.NONE}>
                            Printed
                          </Tag>
                        </span>
                      }
                      className={getCssClass(
                        item.className,
                        modelStateLog.actionInformations?.has(item.action)
                          ? 'enrollment-menu-item-has-footer'
                          : ''
                      )}
                      icon={
                        item.icon ? (
                          <Svg
                            className={'svg-information-icon'}
                            src={`/images/${item.icon}`}
                          />
                        ) : (
                          false
                        )
                      }
                      onClick={() => {
                        musterFormDialogActions.setCurrentEntryForm(
                          formEntry.id || ''
                        );
                        musterFormDialogActions.viewForm(
                          AccountManagementUtil.convertFormData(
                            formEntry.encounterForm as Form,
                            treatmentDoctor
                          ),
                          formEntry.encounterForm?.encounterId || '',
                          patientFileActions.getAvailableContractById(
                            props?.enrollment?.contractId
                          ) as IContractInfo,
                          formEntry.encounterForm?.id as string,
                          !!formEntry.encounterForm?.prescribe.printedDate,
                          false,
                          false,
                          formEntry.scheinIds?.[0]
                        );
                      }}
                    />
                  );
                }
                return (
                  <MenuItem
                    shouldDismissPopover={!item.needPopoverForm}
                    key={item.text}
                    text={item.text}
                    className={getCssClass(
                      item.className,
                      modelStateLog.actionInformations?.has(item.action as ParticipationAction)
                        ? 'enrollment-menu-item-has-footer'
                        : ''
                    )}
                    onClick={() => {
                      props.onClick ? props.onClick(item.action as ParticipationAction) : {};
                    }}
                    icon={
                      item.icon ? (
                        <Svg
                          className={'svg-information-icon'}
                          src={`/images/${item.icon}`}
                        />
                      ) : (
                        false
                      )
                    }
                  />
                );
              })}
            </>
          )}
          {props.participationActionItems &&
            props.participationActionItems
              .filter(filterNotHpmAction)
              .map(renderParticipationActionItems)}
          {hasHpmCheckOnlineParticipation && (
            <>
              <MenuDivider
                key={props?.hpmInformation?.checkedTime?.toString()}
                title={
                  <Flex>
                    <Flex>
                      <BodyTextS
                        className={getCssClass(
                          'hpm-text-status',
                          HpmInformationClass[props.hpmInformation?.status || '']
                        )}
                      >
                        {'HPM'}
                      </BodyTextS>
                    </Flex>
                    <Flex>
                      <Tooltip
                        className="hpm-tool-tip-content"
                        content={modelState.hpmToolTipContent}
                        position={'top-right'}
                        autoFocus={false}
                        openOnTargetFocus={false}
                        portalClassName={props.className}
                      >
                        <Svg
                          className={'svg-information-icon'}
                          src={'/images/hpm-information-icon.svg'}
                        />
                      </Tooltip>
                    </Flex>
                  </Flex>
                }
                className={'menu-item-hpm-status'}
              />
              {(props.participationActionItems || [])
                .filter(filterHpmAction)
                .map(renderParticipationActionItems)}
            </>
          )}
          {showHpmInformation && (
            <>
              <MenuDivider
                key={props?.hpmInformation?.checkedTime?.toString()}
                title={
                  <Flex>
                    <Flex>
                      <BodyTextS
                        className={getCssClass(
                          'hpm-text-status',
                          HpmInformationClass[props.hpmInformation?.status || '']
                        )}
                      >
                        {'HPM'}
                      </BodyTextS>
                    </Flex>
                    <Flex>
                      <Tooltip
                        content={modelState.hpmToolTipContent}
                        position={'top-right'}
                        autoFocus={false}
                        //https://github.com/palantir/blueprint/issues/3514
                        openOnTargetFocus={false}
                        portalClassName={props.className}
                      >
                        <Svg
                          className={'svg-information-icon'}
                          src={'/images/hpm-information-icon.svg'}
                        />
                      </Tooltip>
                    </Flex>
                  </Flex>
                }
                className={'menu-item-hpm-status'}
              />
              <MenuItem
                shouldDismissPopover={false}
                key={
                  ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation
                }
                text={
                  <>
                    <span>
                      {props.hpmInformation?.isLoading
                        ? t('checkingHpmParticipationOnline')
                        : t('checkHpmParticipationOnline')}
                    </span>
                    {modelStateLog.actionInformations?.has(
                      ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation
                    ) && (
                        <BodyTextS className="menu-footer-item-message">
                          {modelStateLog.actionInformations.get(
                            ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation
                          )}
                        </BodyTextS>
                      )}
                  </>
                }
                className={getCssClass(
                  modelStateLog.actionInformations?.has(
                    ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation
                  )
                    ? 'enrollment-menu-item-has-footer'
                    : ''
                )}
                onClick={() => {
                  if (props.hpmInformation.isLoading) return;

                  props?.onClick?.(
                    ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation
                  );
                }}
                icon={
                  <Flex className={'svg-information-icon'}>
                    {props.hpmInformation?.isLoading ? (
                      <LoadingState />
                    ) : (
                      <Svg src={'/images/check-hpm-icon.svg'} />
                    )}
                  </Flex>
                }
              />
            </>
          )}
        </>
      </Menu>
      <OverlayToaster ref={toasterRef} usePortal={false} />
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(EnrollmentMenuMemo, {
    namespace: 'PatientManagement',
    nestedTrans: 'EnrollmentMenu',
  })
);
