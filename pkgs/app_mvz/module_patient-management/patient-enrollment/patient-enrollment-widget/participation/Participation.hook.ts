import {
  isNotEmpty,
  isNotNullOrEmpty,
  isNullOrEmpty,
  parseDate,
  toDateFormat,
} from '@tutum/design-system/infrastructure/utils';
import {
  GetContractInformationFromMvzRequest,
  getContractInformationFromMvz,
  getContractsInformationFromHpmService,
} from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { EnrollmentType } from '@tutum/hermes/bff/legacy/enrollment_common';
import { PatientParticipation } from '@tutum/hermes/bff/legacy/service_domains_patient_participation';
import {
  GetContractInformationFromMvzResponse,
  GetContractsInformationFromHpmRequest,
  GetContractsInformationFromHpmResponse,
  GroupContractAction,
  PatientEnrollmentInformationAction,
  PatientEnrollmentInformationStatus,
} from '@tutum/hermes/bff/service_domains_enrollment';
import { PatientParticipationStatus } from '@tutum/hermes/bff/service_domains_patient_participation';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtil from '@tutum/infrastructure/utils/name.utils';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { ContractService } from '@tutum/mvz/services/contract.service';
import ProfileService from '@tutum/mvz/services/profile.service';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { IContractDoctors } from '../../../types/contract.type';
import { PatientEnrollmentUtils } from '../../patient-enrollment';
import {
  ContractsInformation,
  HpmViewInformation,
  IContractInformation,
  IContractMetaItem,
  IEnrollmentMenuActionItem,
  MenuInformation,
  ParticipationAction,
  getActionItemInformation,
  mapOrderEnrollmentActions,
  mapOrderParticipationActions,
} from './Participation.model';
import { ParticipationService } from './Participation.service';

class ContentMapContractToolTip {
  contractId: string | null;
  contentLines: string[];
}
class ContentMap {
  printedDate = '';
  printedTime = '';
  appliedDate = '';
  appliedTime = '';
  doctorName = '';
  startDate = '';
  updatedDate = '';
  endDate = '';
  contractName = '';
  updatedBy = '';
  chargeSystemId = '';
}

function useCheckContractFromHpm(
  request: GetContractsInformationFromHpmRequest | undefined
): GetContractsInformationFromHpmResponse | null {
  const [result, setResult] =
    useState<GetContractsInformationFromHpmResponse | null>(null);
  useEffect(() => {
    let contractIds: string[] = [];
    if (request?.contractIds) {
      contractIds = request.contractIds.filter((id) => isNotEmpty(id));
    }
    if (!request?.patientId || isNullOrEmpty(contractIds)) {
      setResult(null);
      return;
    }
    getContractsInformationFromHpmService({
      contractIds: contractIds,
      patientId: request.patientId,
      forceUpdate: request.forceUpdate,
      doctorIds: request.doctorIds,
      saveHistory: request.saveHistory,
      checkDate: request.checkDate || datetimeUtil.date(),
    })
      .then((res) => setResult({ ...res.data }))
      .catch((err) => {
        setResult({
          patientId: request.patientId,
          hpmResponses: null,
          hpmErrorMessages: [err],
        } as unknown as GetContractsInformationFromHpmResponse);
      });
  }, [JSON.stringify(request)]);

  return result;
}

function useGetContractInformationFromMvz(
  request: GetContractInformationFromMvzRequest | null,
  isLoading: boolean
): GetContractInformationFromMvzResponse | null {
  const [result, setResult] =
    useState<GetContractInformationFromMvzResponse | null>(null);

  useEffect(() => {
    if (!request?.patientId || isLoading) {
      setResult(null);
      return;
    }
    getContractInformationFromMvz(request).then(({ data: res }) => {
      setResult({
        ...res,
      });
    });
  }, [request, isLoading]);
  return result;
}

function useCheckIsContractConflictWithHpm(
  informations: Array<{
    contractInformation: IContractInformation | undefined;
    hpmInformation: HpmViewInformation;
  }>,
  isIgnoreConflict = false
): boolean {
  const [haveConflict, setHaveConflict] = useState<boolean>(false);
  useEffect(() => {
    if (isIgnoreConflict) {
      setHaveConflict(false);
      return;
    }
    if (!informations || informations.length == 0) {
      setHaveConflict(false);
      return;
    }
    let haveAnyConflict = false;
    for (let index = 0; index < informations.length; index++) {
      const information = informations[index];

      if (!information.hpmInformation) {
        continue;
      }
      const isParticipationActive: boolean = information?.contractInformation
        ?.participation
        ? PatientEnrollmentUtils.isParticipationActive(
          information?.contractInformation?.participation
        )
        : false;
      haveAnyConflict =
        isParticipationActive !==
        (information?.hpmInformation?.isActive ?? false);
      if (haveAnyConflict) {
        break;
      }
    }
    setHaveConflict(haveAnyConflict);
  }, [informations, isIgnoreConflict]);
  return haveConflict;
}

function useGetAllowedShowingEnrollmentActions(
  actionItems: IEnrollmentMenuActionItem[] | undefined,
  contractStatus: PatientEnrollmentInformationStatus | undefined,
  hpmInformation: HpmViewInformation
): IEnrollmentMenuActionItem[] | null {
  const [resultMenu, setResultMenu] = useState<
    IEnrollmentMenuActionItem[] | null
  >(null);
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();

  const isHpmActiveAtCurrent: boolean =
    hpmInformation && hpmInformation.isActive && true;
  ParticipationService.isHpmCheckDateValid(hpmInformation);
  useMemo(() => {
    if (
      !actionItems ||
      !contractStatus ||
      !currentLoggedinUser.hasHzvContracts
    ) {
      setResultMenu(
        actionItems?.filter((item) => {
          return [
            PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_PerformHpmCheckOnlineParticipation,
            PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ShowHpmCheckHistoryDialog,
          ].includes(item.action as any);
        }) || null
      );
      return;
    }

    const showedActionItems = actionItems?.filter((item) => {
      // const isNotCreateActiveParticipation =
      //   item.action !==
      //   PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation;
      const isNotTreatAsDeputy =
        item.action !==
        PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy;

      const isNotEnrollYet =
        contractStatus !==
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet;

      return isNotTreatAsDeputy || isNotEnrollYet || isHpmActiveAtCurrent;
    });
    setResultMenu(showedActionItems);
  }, [actionItems, contractStatus, isHpmActiveAtCurrent]);

  return resultMenu;
}

function useGetContractInformation(
  request: GetContractInformationFromMvzRequest | null,
  isLoading = false
): ContractsInformation | null {
  const contractsInformationResponse = useGetContractInformationFromMvz(
    request,
    isLoading
  );

  const [result, setResult] = useState<ContractsInformation | null>(null);

  useEffect(() => {
    if (!contractsInformationResponse) {
      setResult(null);
      return;
    }
    const listContractsInformation: IContractInformation[] = [];

    contractsInformationResponse?.contractInformations?.forEach(
      (contractResponse) => {
        const contractInformation: IContractInformation = {
          patientId: contractsInformationResponse.patientId,
          ...contractResponse,
          hasHpmError: null,
          contractAbbreviation: ContractService.getContractAbbreviations(
            contractResponse.contractId,
            contractResponse.participation?.contractType ??
            contractResponse.enrollment?.contractType ??
            null
          ),
          hpmErrorMessages: null,
          enrollmentActions: contractResponse?.actions
            ?.filter((a) => {
              return mapOrderEnrollmentActions.has(a);
            })
            .sort(
              (a, b) =>
                (mapOrderEnrollmentActions.get(a) || 0) -
                (mapOrderEnrollmentActions.get(b) || 0)
            ),
          participationActions: contractResponse?.actions
            ?.filter((a) => {
              return mapOrderParticipationActions.has(a);
            })
            .sort(
              (a, b) =>
                (mapOrderParticipationActions.get(a) || 0) -
                (mapOrderParticipationActions.get(b) || 0)
            ),
        };
        listContractsInformation.push(contractInformation);
      }
    );

    const listContractActionGroups: GroupContractAction[] =
      contractsInformationResponse.groupContractActions
        ? [...contractsInformationResponse.groupContractActions]
        : [];

    setResult({
      contractInformations: listContractsInformation.sort((c1, c2) =>
        c1.contractAbbreviation < c2.contractAbbreviation
          ? -1
          : c1.contractAbbreviation > c2.contractAbbreviation
            ? 1
            : 0
      ),
      contractGroupActions: listContractActionGroups.sort(
        (action1, action2) =>
          (mapOrderParticipationActions.get(action1.action) || 0) -
          (mapOrderEnrollmentActions.get(action2.action) || 0)
      ),
    });
  }, [contractsInformationResponse]);

  return result;
}

function isI18nKeyNotExisting(key = '', content = '') {
  // next translate return {Namespace:key} when the key is missing in current namespace
  return content.endsWith(key);
}

function useGenerateContractToolTip(
  contractInformation: IContractInformation | undefined,
  currentIkNumber: number | undefined,
  isHpmActive: boolean,
  prefixKeyComponent: 'hzvButton' | 'favContract',
  t: IFixedNamespaceTFunction,
  missingInsuranceNumber: boolean,
  isContractActiveWithoutAnyParticipation?: boolean,
  available = true,
  previousParticipation?: PatientParticipation
): ContentMapContractToolTip {
  const doctorList = useContext(GlobalContext.instance).useGetDoctorList();
  const [toolTipContent, setToolTipContent] =
    useState<ContentMapContractToolTip>({
      contractId: null,
      contentLines: [],
    });
  const asyncGenerateToolTip = useCallback(async () => {
    // generate hint content

    if (!available) {
      setToolTipContent({
        contentLines: [t(prefixKeyComponent + '_NotAvailable_Line1')],
        contractId: null,
      });
      return;
    }

    if (
      contractInformation &&
      contractInformation.iKChanged &&
      contractInformation.status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet
    ) {
      setToolTipContent({
        contentLines: [t('hzv_iKChanged')],
        contractId: contractInformation.contractId,
      });

      return;
    }

    if (
      isContractActiveWithoutAnyParticipation &&
      contractInformation &&
      contractInformation.status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet
    ) {
      setToolTipContent({
        contentLines: [t(prefixKeyComponent + '_Enrolled_Line1')],
        contractId: contractInformation.contractId,
      });

      return;
    }

    let status: string = contractInformation?.status || '';
    const hpmStatusKey: string = !isHpmActive ? '' : 'HpmActive';

    const hintContents: string[] = [];
    const contentMap: ContentMap = new ContentMap();
    if (
      contractInformation?.participation &&
      contractInformation.participation.status ===
      PatientParticipationStatus.PatientParticipation_Terminated
    ) {
      if (currentIkNumber !== contractInformation?.participation?.ikNumber) {
        status = `${status}_ChangedIknumber`;
      } else {
        const endDate = parseDate(contractInformation.participation.endDate);
        const doctorInfo = doctorList.find(
          (doctor) =>
            doctor.id === (contractInformation.participation?.updatedBy || '')
        );

        setToolTipContent({
          contractId: contractInformation.contractId,
          contentLines: [
            t('button_Terminated_Line1', {
              userName: nameUtil.getDoctorName(doctorInfo),
              endDate:
                endDate &&
                toDateFormat(endDate, {
                  dateFormat: 'dd.MM.yyyy',
                }),
            }),
          ],
        });

        return;
      }
    }

    let isOffline = false;
    if (contractInformation && contractInformation.enrollment) {
      isOffline =
        contractInformation.enrollment.type ===
        EnrollmentType.EnrollmentType_OFFLINE;
      if (contractInformation.enrollment.printedDate) {
        const printedDate = parseDate(
          contractInformation.enrollment.printedDate
        );
        contentMap.printedDate =
          (printedDate &&
            toDateFormat(printedDate, {
              dateFormat: 'dd.MM.yyyy',
            })) ||
          '';
        contentMap.printedTime =
          (printedDate &&
            toDateFormat(printedDate, {
              timeFormat: 'hh:mm',
              showAMPM: false,
            })) ||
          '';
      }
      if (contractInformation.enrollment.appliedDate) {
        const appliedDate = parseDate(
          contractInformation.enrollment.appliedDate
        );
        contentMap.appliedDate =
          (appliedDate &&
            toDateFormat(appliedDate, {
              dateFormat: 'dd.MM.yyyy',
            })) ||
          '';
        contentMap.appliedTime =
          (appliedDate &&
            toDateFormat(appliedDate, {
              timeFormat: 'hh:mm',
              showAMPM: false,
            })) ||
          '';
      }
    }
    if (contractInformation && contractInformation.participation) {
      if (
        contractInformation.participation.status ===
        PatientParticipationStatus.PatientParticipation_Requested
      ) {
        isOffline = !contractInformation.participation.isTransmittedHpm;
        // when status is Requested, the start date is the requested date
        const startDate = parseDate(
          contractInformation.participation.startDate
        );

        contentMap.chargeSystemId =
          contractInformation.participation.chargeSystemId;

        contentMap.appliedDate =
          (startDate &&
            toDateFormat(startDate, {
              dateFormat: 'dd.MM.yyyy',
            })) ||
          '';
        // Date only!
        // contentMap.appliedTime =
        //   (startDate &&
        //     toDateFormat(startDate, {
        //       timeFormat: 'hh:mm',
        //       showAMPM: false,
        //     })) ||
        //   '';
      }
      if (contractInformation.participation?.doctorId) {
        const doctors = doctorList.filter(
          (doctor) =>
            doctor.id === (contractInformation.participation?.doctorId || '')
        );
        if (isNotNullOrEmpty(doctors)) {
          contentMap.doctorName = doctors[0].fullName;
        }
      }
      if (contractInformation.participation?.updatedBy) {
        const users = doctorList.filter(
          (doctor) =>
            doctor.id === (contractInformation.participation?.updatedBy || '')
        );
        if (isNotNullOrEmpty(users)) {
          contentMap.updatedBy = users[0].fullName;
        }
      }
      if (contractInformation.participation.startDate) {
        const startDate = parseDate(
          contractInformation.participation.startDate
        );
        contentMap.startDate =
          (startDate &&
            toDateFormat(startDate, {
              dateFormat: 'dd.MM.yyyy',
            })) ||
          '';
      }
      if (contractInformation.participation.updatedDate) {
        const updatedDate = parseDate(
          contractInformation.participation.updatedDate
        );
        contentMap.updatedDate =
          (updatedDate &&
            toDateFormat(updatedDate, {
              dateFormat: 'dd.MM.yyyy',
            })) ||
          '';
      }
      if (contractInformation.participation.endDate) {
        const updatedDate = parseDate(
          contractInformation.participation.endDate
        );
        contentMap.endDate =
          (updatedDate &&
            toDateFormat(updatedDate, {
              dateFormat: 'dd.MM.yyyy',
            })) ||
          '';
      }
    }
    if (contractInformation && contractInformation.contractId) {
      const contractMeta = await catalogOverviewActions.getContractMeta(
        datetimeUtil.now(),
        contractInformation.contractId
      );
      if (contractMeta) {
        contentMap.contractName = contractMeta?.contractName;
      }
    }

    let index = 1;
    let mappedKey = '';
    const isDeputy =
      status != 'Deputy' &&
      contractInformation?.participation?.doctorFunctionType === 'Deputy';
    const isPending = status === 'Pending';
    // module contract AOK_BW_IV_P of AWH_01
    const isIVP = contentMap.chargeSystemId === 'AOK_BW_IV_P';
    const isPendingIVP = isPending && isIVP;
    const isChangingDoctor =
      !!contractInformation &&
      contractInformation.status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Pending &&
      contractInformation.participation?.isChangingDoctor;

    const prefixKey =
      `${prefixKeyComponent}` +
      `_${status}` +
      `${isDeputy ? 'Deputy' : ''}` +
      `${isChangingDoctor ? 'ChangingDoctor' : ''}` +
      `${isPendingIVP ? '_IVP' : ''}`;
    const key = isOffline ? 'Offline' : 'Online';
    // eslint-disable-next-line no-constant-condition
    // TODO: fixed hard maxIndex to avoid infinite loops
    let maxIndex = 10;
    if (
      status === 'Created' &&
      previousParticipation?.status ===
      PatientParticipationStatus.PatientParticipation_Active
    )
      maxIndex = 1;

    while (index <= maxIndex) {
      if (index === maxIndex) {
        console.error('loop to max index');
      }
      // hzvButton_Active_HpmActive_Offline_index
      // hzvButton_Active_Offline_index
      // hzvButton_Active_index
      const lineIndex = `Line${index}`;

      if (mappedKey === '') {
        mappedKey = `${prefixKey}${hpmStatusKey !== '' ? '_' + hpmStatusKey : ''
          }_${key}_${lineIndex}`;
        let checkedContent: string = t(mappedKey, contentMap as any);

        if (isI18nKeyNotExisting(mappedKey, checkedContent)) {
          mappedKey = `${prefixKey}${hpmStatusKey !== '' ? '_' + hpmStatusKey : ''
            }_${lineIndex}`;
          checkedContent = t(mappedKey, contentMap as any);
        }

        if (isI18nKeyNotExisting(mappedKey, checkedContent)) {
          mappedKey = `${prefixKey}_${key}_${lineIndex}`;
          checkedContent = t(mappedKey, contentMap as any);
        }

        if (isI18nKeyNotExisting(mappedKey, checkedContent)) {
          mappedKey = `${prefixKey}_${lineIndex}`;
          checkedContent = t(mappedKey, contentMap as any);
        }

        if (!isI18nKeyNotExisting(mappedKey, checkedContent)) {
          mappedKey = mappedKey.slice(
            0,
            mappedKey.length - `_${lineIndex}`.length
          );
        }
      }

      const i18nKey = `${mappedKey}_${lineIndex}`;
      const content: string = t(i18nKey, contentMap as any);
      if (isI18nKeyNotExisting(i18nKey, content)) {
        break;
      }
      hintContents.push(content);
      index++;
    }
    setToolTipContent({
      contentLines: [...hintContents],
      contractId: contractInformation?.contractId || '',
    });
  }, [
    contractInformation,
    currentIkNumber,
    isHpmActive,
    isContractActiveWithoutAnyParticipation,
    previousParticipation,
  ]);

  useEffect(() => {
    if (missingInsuranceNumber) {
      setToolTipContent({
        contentLines: t('MissingInsuranceNumber').split('\n'),
        contractId: contractInformation?.contractId || '',
      });
      return;
    }
    if (!contractInformation) {
      return;
    }
    asyncGenerateToolTip();
  }, [contractInformation, missingInsuranceNumber, asyncGenerateToolTip]);
  return toolTipContent;
}

function useGetTerminateLog(
  contractInformation: IContractInformation | undefined,
  keyContent: string,
  t: IFixedNamespaceTFunction
): string {
  const [information, setInformation] = useState<string>('');

  const asyncGenerateInformation = async () => {
    let content = '';
    const patientParticipation = contractInformation?.participation;
    if (!patientParticipation) {
      setInformation('');
      return;
    }
    if (
      patientParticipation?.status ===
      PatientParticipationStatus.PatientParticipation_Terminated
    ) {
      const contentMap: any = {};
      const employee = await ProfileService.getEmployeeProfilesByIds([
        patientParticipation.updatedBy as string,
      ]).then((e) => {
        return e;
      });
      contentMap.updatedBy =
        employee && employee.length > 0 ? employee[0].fullName : '';
      if (patientParticipation.updatedDate) {
        const updatedDate = parseDate(patientParticipation.updatedDate) as Date;
        contentMap.updatedDate = toDateFormat(updatedDate, {
          dateFormat: 'dd.MM.yyyy',
        });
        contentMap.updatedTime = toDateFormat(updatedDate, {
          timeFormat: 'hh:mm',
          showAMPM: false,
        });
      }
      content = t(keyContent, contentMap as any);
    }
    return setInformation(content);
  };

  useEffect(() => {
    if (!contractInformation?.participation) {
      return;
    }
    asyncGenerateInformation();
  }, [contractInformation]);

  return information;
}

function useLoadContractMetaItems(
  contracts: IContractDoctors[]
): IContractMetaItem[] | null {
  const [result, setResult] = useState<IContractMetaItem[] | null>(null);

  const loadContractMeta = useCallback(() => {
    const listSelectContract: IContractMetaItem[] = [];
    Promise.all(
      contracts?.map(async (item) => {
        const contractMeta = await catalogOverviewActions.getContractMeta(
          datetimeUtil.now(),
          item.contractId
        );
        const contractItem: IContractMetaItem = {
          contractId: item.contractId,
          doctorIds: item.doctorIds,
          contractName: contractMeta?.contractName,
        };
        contractItem.doctorIds = item.doctorIds;
        listSelectContract.push(contractItem);
      })
    ).then(() => {
      setResult(listSelectContract);
    });
  }, [contracts]);
  useEffect(() => {
    if (!contracts) {
      setResult(null);
      return;
    }
    loadContractMeta();
  }, [contracts]);
  return result;
}

function useLoadParticipationMenuItems(
  contractInformation: IContractInformation | undefined,
  t: IFixedNamespaceTFunction,
  isAllowCreateParticipation = true
): MenuInformation {
  const initData: MenuInformation = {
    enrollmentActions: [],
    particpationActions: [],
  };
  const [result, setResult] = useState<MenuInformation>(initData);
  useEffect(() => {
    if (!contractInformation) {
      setResult(initData);
      return;
    }
    const menuResult: MenuInformation = {
      enrollmentActions: [],
      particpationActions: [],
    };

    contractInformation.enrollmentActions?.forEach(
      (enrollmentAction: ParticipationAction) => {
        const menuItem = getActionItemInformation(
          enrollmentAction,
          contractInformation
        );
        if (menuItem) {
          menuItem.text = t(menuItem.text || '');
          menuResult.enrollmentActions.push(menuItem);
        }
      }
    );
    contractInformation.participationActions.forEach(
      (participationAction: ParticipationAction) => {
        if (
          !isAllowCreateParticipation &&
          (participationAction ===
            PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation ||
            participationAction ===
            PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveFavGroupContract)
        ) {
          return;
        }
        const menuItem = getActionItemInformation(
          participationAction,
          contractInformation
        );
        if (menuItem) {
          menuItem.text = t(menuItem.text || '');
          menuResult.particpationActions.push(menuItem);
        }
      }
    );
    setResult(menuResult);
  }, [contractInformation, t, isAllowCreateParticipation]);
  return result;
}

export const ParticipationHook = {
  useGetContractInformation,
  useCheckIsContractConflictWithHpm,
  useCheckContractFromHpm,
  useGenerateContractToolTip,
  useGetTerminateLog,
  useLoadContractMetaItems,
  useGetAllowedShowingEnrollmentActions,
  useLoadParticipationMenuItems,
};
