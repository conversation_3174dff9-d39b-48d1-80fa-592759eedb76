import {
  alertError,
  alertSuccessfully,
  alertWarning,
  BodyTextM,
  BodyTextS,
  Box,
  Button,
  Flex,
  H2,
  Svg,
} from '@tutum/design-system/components';
import {
  Alert,
  Classes,
  Dialog,
  Intent,
  Popover,
  PopoverInteractionKind,
  PopoverPosition,
  Position,
  Tooltip,
} from '@tutum/design-system/components/Core';
import AlertTriangleSolid from '@tutum/design-system/components/Icons/AlertTriangleSolid';
import {
  getCssClass,
  isNotEmpty,
} from '@tutum/design-system/infrastructure/utils';
import { useListenPatientParticipationChange } from '@tutum/hermes/bff/app_mvz_patient_participation';
import { HpmFunctionType } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import {
  useMutationCheckPotentialVerah,
  useQueryGetContractInformationFromMvz,
} from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { getContractDoctorGroup } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import {
  takeOverScheinDiagnosis,
  useQueryGetScheinDetailById,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  reRunValidateService,
  useQueryGetTakeOverDiagnosis,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { ContractType } from '@tutum/hermes/bff/legacy/common';
import { DoctorParticipateStatus } from '@tutum/hermes/bff/legacy/service_domains_doctor_participate';
import {
  GetContractsInformationFromHpmResponse,
  HpmErrorMessage,
} from '@tutum/hermes/bff/legacy/service_domains_enrollment';
import { DiagnoseType } from '@tutum/hermes/bff/legacy/service_domains_patient_file';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';
import { MainGroup, ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  PatientEnrollmentInformationAction,
  PatientEnrollmentInformationStatus,
} from '@tutum/hermes/bff/service_domains_enrollment';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import SVSelectDiagnosisDialog from '@tutum/mvz/components/select-diagnosis-dialog/sv-select-diagnosis-dialog/SVSelectDiagnosisDialog.styled';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import useConfirm from '@tutum/mvz/hooks/useConfirm';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { IPatientManagement } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import PatientEnrollmentForm from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-form/PatientEnrollmentForm.styled';
import HpmCheckHistoryDialog from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/hpm-check-history/HpmCheckHistoryDialog.styled';
import { HpmViewInformation } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/participation/Participation.model';
import { ParticipationService } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/participation/Participation.service';
import { MissingInsuranceNumber_Error } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/PatientEnrollmentWidget.constant';
import {
  PatientPostActionType,
  usePatientPostAction,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.hook';
import {
  patientFileActions,
  patientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { timelineActions } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { IContractDoctors } from '@tutum/mvz/module_patient-management/types/contract.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import reloadSchein from '@tutum/mvz/module_patient-management/utils/reloadSchein';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import debounce from 'lodash/debounce';
import {
  memo,
  Reducer,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import ActiveContractParticipation from '../active-contract-participation/ActiveContractParticipation.styled';
import DatePickerForm from '../date-picker-form/DatePickerForm.styled';
import EnrollmentMenu from '../enrollment-menu/EnrollmentMenu.styled';
import { FavButtonHook } from '../fav-button/FavButton.hook';
import { ParticipationHook } from '../participation/Participation.hook';
import SelectCustodianDoctorDialog from '../select-custodian-doctor/SelectCustodianDoctorDialog.styled';
import TreatAsDeputy from '../treat-as-deputy/TreatAsDeputy.styled';
import { HzvButtonHook } from './HzvButton.hook';
import {
  HzvButtonAction,
  HzvButtonReducer,
  HzvButtonState,
} from './HzvButton.reducer';
import { StyledParticipationToolTipContent } from './HzvButton.styled';
import { useQueryGetFileUrl } from '@tutum/hermes/bff/legacy/app_mvz_form';
import { useFormOverviewStore } from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import { FormType } from '@tutum/hermes/bff/form_common';

const ArrowDownGrayIcon = '/images/chevron-down.svg';
const ArrowDownIcon = '/images/chevron-down-white.svg';
const CheckCircleSolidSuccess = '/images/check-circle-solid-success.svg';
const CloseSolid = '/images/close-solid-orange.svg';

interface reloadScheinAndActivateHzvScheinProps {
  patientManagement: IPatientManagement;
  create: boolean;
  newDoctorId?: string;
  triggerSelectCustodian?: boolean;
  startDate?: number;
  endDate?: number;
}
export class IHzvButtonProps {
  className?: string;
  isHpmConnectionOk: boolean;
  patient: IPatientProfile;
  availableHzvContracts: IContractDoctors[];
  availableFavContracts: IContractDoctors[];
  disabled?: boolean;
  onHzvInformationChanged?: (
    contractStatus: PatientEnrollmentInformationStatus | undefined,
    isHpmActive: boolean
  ) => void;
  openCreateSchein: () => void;
  onReloadLoadListContracts: () => void;
}

function HzvButtonMemo(
  props: IHzvButtonProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.HzvButton>
) {
  const {
    t,
    className,
    patient,
    availableHzvContracts,
    disabled = false,
    openCreateSchein,
    onReloadLoadListContracts,
    availableFavContracts,
  } = props;
  const missingInsuranceNumber =
    !patientFileStore.activeInsurance?.insuranceNumber;

  const { t: tVerah } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Verah
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Verah',
  });

  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();
  const doctors = globalContext.useGetDoctorList();

  const [modelState, dispatch] = useReducer<
    Reducer<HzvButtonState, HzvButtonAction>
  >(HzvButtonReducer, {
    currentLoggedInUser: undefined,
    patient: patient,
    loading: false,
    hzvAvailableContracts: null,
    hzvAvailableDoctors: null,
    hzvRequest: null,
    hzvInformation: null,
    hzvPopoverButtonIsOpen: false,
    hzvTooltipButtonIsOpen: false,
    actionHzvContract: null,
    currentActionProcess: null,
    currentSubmittingActionProcess: null,
    datePickerProps: null,
    toastInfo: null,
    hzvHintContentLines: null,
    isChangingDoctor: false,
    hzvMenu: null,
    checkHpmConnectionRequest: undefined,
    hpmInformation: {
      status: undefined,
      checkedTime: undefined,
      hasError: false,
      isActive: false,
      isLoading: false,
    },
    t,
    showHpmHistoryDialog: false,
  });

  const modelStateRef = useRef<HzvButtonState>(modelState);

  useEffect(() => {
    modelStateRef.current = modelState;
  }, [modelState]);

  const { ConfirmationDialog, askConfirmation } = useConfirm();

  const {
    reloadPatient,
    patientManagement,
    setGetPatientParticipationResponse,
    refetchPatientProfile,
  } = useContext(PatientManagementContext.instance);
  const [isSupportHpmOnlineCheck, setIsSupportHpmOnlineCheck] = useState(true);
  const [
    isContractActiveWithoutAnyParticipation,
    setIsContractActiveWithoutAnyParticipation,
  ] = useState(false);
  const [openSelectCustodianDoctorDialog, setOpenSelectCustodianDoctorDialog] =
    useState<boolean>(false);

  const fomrOverviewStore = useFormOverviewStore();
  const [verahUrl, setverahUrl] = useState('');
  const { data: getFileUrlResponse } = useQueryGetFileUrl(
    {
      fileName: verahUrl,
    },
    {
      enabled: !!verahUrl,
    }
  );

  useEffect(() => {
    const doc = fomrOverviewStore.listForms?.find((f) =>
      f.id.endsWith('VERAH_TopVersorgt')
    );
    if (!doc) {
      return;
    }
    const docType =
      doc.formType === FormType.FormType_public_document
        ? 'Dokumente'
        : 'Vertragstexte';
    const fileName = `${docType}/${doc.fileNameWithVersion}.pdf`;
    setverahUrl(fileName);
  }, [fomrOverviewStore.listForms]);

  useEffect(() => {
    if (props.onHzvInformationChanged) {
      props.onHzvInformationChanged(
        modelState.hzvInformation?.status,
        modelState.hpmInformation?.isActive
      );
    }
  }, [modelState.hzvInformation, modelState.hpmInformation]);

  const { refetch: refetchGetContractInformationHzv } =
    useQueryGetContractInformationFromMvz(
      {
        contractType: ContractType.ContractType_HouseDoctorCare,
        patientId: patient?.id,
      },
      {
        enabled: false,
      }
    );

  useEffect(() => {
    if (!currentLoggedInUser.id) return;
    dispatch({
      type: 'loadingCurrentLoggedInUser',
      payload: { currentLoggedInUser },
    });
  }, [currentLoggedInUser]);

  const available = !!modelStateRef.current.hzvAvailableContracts?.length;

  const currentHzvSchein = useMemo(() => {
    return patientFileStore.schein.list
      .reverse()
      .find((schein: ScheinItem) => schein.scheinMainGroup === MainGroup.HZV);
  }, [patientFileStore.schein.list]);

  const getScheinDetailById = useQueryGetScheinDetailById(
    {
      scheinId: currentHzvSchein?.scheinId as string,
    },
    {
      enabled: !!currentHzvSchein,
    }
  );

  const isTerminated = useMemo(() => {
    const selectedInsurance =
      patientFileStore.patient?.current?.patientInfo.insuranceInfos?.find(
        (item) => item.id === currentHzvSchein?.insuranceId
      );

    let minEndDate = selectedInsurance?.endDate;

    if (getScheinDetailById.data?.svScheinDetail?.endDate) {
      minEndDate = Math.min(
        selectedInsurance?.endDate || 0,
        getScheinDetailById.data?.svScheinDetail?.endDate || 0
      );
    }

    return !!minEndDate && minEndDate < datetimeUtil.now();
  }, [
    patientFileStore.patient?.current?.patientInfo.insuranceInfos,
    getScheinDetailById.data?.svScheinDetail?.endDate,
  ]);

  //#region uses
  const hzvInformationRequest = useMemo(() => {
    if (!modelState.hzvRequest) {
      return null;
    }

    return {
      ...modelState.hzvRequest,
    };
  }, [modelState.hzvRequest, isTerminated]);
  const hzvInformationResponse = ParticipationHook.useGetContractInformation(
    hzvInformationRequest,
    getScheinDetailById.isFetching
  );

  const showActivatedVerah = async () => {
    const isOpen = await askConfirmation({
      title: t('VERAHActivated'),
      content: t('VERAHActivatedDescription'),
      intent: Intent.SUCCESS,
      confirmButton: tVerah('openGuidelines'),
      cancelButton: tVerah('cancelText'),
      isShowIconTitle: true,
      headerIcon: (
        <Svg
          className="sl-type-header-icon"
          src={CheckCircleSolidSuccess}
          style={{ width: 32, height: 32, marginRight: 8 }}
        />
      ),
    });

    if (isOpen && getFileUrlResponse?.fileUrl) {
      window.open(getFileUrlResponse?.fileUrl);
    }
  };
  const showEligibleVerah = async () => {
    const isOpen = await askConfirmation({
      title: t('VERAHEligible'),
      content: t('VERAHEligibleDescription'),
      intent: Intent.WARNING,
      confirmButton: tVerah('openGuidelines'),
      cancelButton: tVerah('cancelText'),
      isShowIconTitle: true,
      headerIcon: undefined,
    });

    if (isOpen && getFileUrlResponse?.fileUrl) {
      window.open(getFileUrlResponse?.fileUrl);
    }
  };
  const showNotEligibleVerah = () => {
    askConfirmation({
      title: t('VERAHNotEligible'),
      content: t('VERAHNotEligibleDescription'),
      intent: Intent.DANGER,
      confirmButton: undefined,
      cancelButton: t('closeBtn'),
      isShowIconTitle: true,
      headerIcon: (
        <Svg
          className="sl-type-header-icon"
          src={CloseSolid}
          style={{ width: 32, height: 32, marginRight: 8 }}
        />
      ),
    });
  };

  const { mutate: checkPotentialVerah } = useMutationCheckPotentialVerah({
    onSuccess: (res) => {
      const data = res.data;
      if (data.hasVeraService) showActivatedVerah();
      else if (data.hasRelatedVerahDiagnose) showEligibleVerah();
      else showNotEligibleVerah();
    },
  });

  useEffect(() => {
    if (!modelState.checkPotentialVerahRequest) return;
    checkPotentialVerah(modelState.checkPotentialVerahRequest);
  }, [modelState.checkPotentialVerahRequest]);

  const allowedShowingEnrollmentMenuItems =
    ParticipationHook.useGetAllowedShowingEnrollmentActions(
      modelState.hzvMenu?.enrollmentActionItems,
      modelState.hzvInformation?.status,
      modelState.hpmInformation
    );

  const allowedShowingParticipationMenuItems =
    ParticipationHook.useGetAllowedShowingEnrollmentActions(
      modelState.hzvMenu?.participationActionItems,
      modelState.hzvInformation?.status,
      modelState.hpmInformation
    );

  const hzvToolTipContentResponse =
    ParticipationHook.useGenerateContractToolTip(
      hzvInformationResponse?.contractInformations?.flat()[0],
      patientFileStore.activeInsurance?.ikNumber,
      modelState?.hpmInformation?.isActive,
      'hzvButton',
      t,
      missingInsuranceNumber,
      isContractActiveWithoutAnyParticipation,
      available,
      modelState.hzvInformation?.previousParticipation
    );

  const actionHzvContractResponse = HzvButtonHook.useActionOnHzvContract(
    modelState.actionHzvContract
  );

  const isHzvContractConflictWithHpm =
    ParticipationHook.useCheckIsContractConflictWithHpm(
      [
        {
          contractInformation: modelState.hzvInformation || undefined,
          hpmInformation: modelState.hpmInformation,
        },
      ],
      !isSupportHpmOnlineCheck || isContractActiveWithoutAnyParticipation
    );
  const hpmParticipationResponse = ParticipationHook.useCheckContractFromHpm(
    modelState.getHzvContractFromHpmRequest
  );

  const terminateLogInformation = ParticipationHook.useGetTerminateLog(
    modelState.hzvInformation || undefined,
    'menuFooterHzvTerminated',
    t
  );

  const contractMetaItems = ParticipationHook.useLoadContractMetaItems(
    availableHzvContracts
  );
  const [takeOverDiagnosisDialogOpen, setTakeOverDiagnosisDialogOpen] =
    useState(false);

  const {
    isSuccess,
    data: takeOverDiagnosisData,
    refetch,
  } = useQueryGetTakeOverDiagnosis(
    {
      patientId: patient.id,
      scheinId: currentHzvSchein?.scheinId,
    },
    {
      enabled: Boolean(patient.id && currentHzvSchein?.scheinId),
    }
  );

  const hasPermanentDiagnosis = useMemo(() => {
    if (!isSuccess) return false;

    const res = (takeOverDiagnosisData.takeOverDiagnosisGroup || []).find(
      (item) => item.diagnoseType === DiagnoseType.DIAGNOSETYPE_PERMANENT
    );

    return !!res && res.timelineModels.length > 0;
  }, [isSuccess, takeOverDiagnosisData]);

  const handleTakeOver = (
    existedDiagnosis: TimelineModel[],
    mappingTreatmentRelevent
  ) => {
    const scheinId = patientFileStore.schein.list.find(
      (schein: ScheinItem) => schein.scheinMainGroup === MainGroup.HZV
    )?.scheinId;
    setTakeOverDiagnosisDialogOpen(false);
    if (!scheinId) return;

    const takeOverDiagnoseInfos: any = existedDiagnosis.map((item) => ({
      ...item,
      isTreatmentRelevant: mappingTreatmentRelevent[item.id || ''],
    }));
    takeOverScheinDiagnosis({
      scheinId: scheinId,
      takeOverDiagnoseInfos: takeOverDiagnoseInfos,
    });
  };

  const handleShowTakeOverDiagnosisDialog = async (activateSchein) => {
    const isSupport = await webWorkerServices.doesContractSupportFunctions(
      ['ABRD609'],
      activateSchein.hzvContractId,
      activateSchein?.chargeSystemId
    );
    refetch();
    setTakeOverDiagnosisDialogOpen(hasPermanentDiagnosis && isSupport);
  };

  ////#endregion uses
  const reloadScheinAndActivateHzvSchein = async ({
    patientManagement,
    create,
    newDoctorId,
    triggerSelectCustodian,
    startDate,
    endDate,
  }: reloadScheinAndActivateHzvScheinProps) => {
    if (!newDoctorId && !patientManagement.selectedContractDoctor.doctorId) {
      return;
    }

    const { scheins, patientParticipation, newScheins, isDeputy } =
      await reloadSchein(patientManagement, create, newDoctorId, startDate, endDate);
    setGetPatientParticipationResponse(patientParticipation);

    const activatedSchein = scheins.find((schein: ScheinItem) =>
      create
        ? schein.scheinMainGroup === MainGroup.HZV
        : schein.scheinMainGroup !== MainGroup.HZV
    );

    if (!activatedSchein) return;

    patientFileActions.schein.setActivatedSchein(
      activatedSchein,
      patientParticipation.participations
    );

    const existNewHzvSchein = (newScheins || []).some(
      (schein) => schein.scheinMainGroup === MainGroup.HZV
    );
    if (triggerSelectCustodian && existNewHzvSchein && !isDeputy) {
      // PRO-14478: remove this feature
      setOpenSelectCustodianDoctorDialog(false);
    }
  };

  const onSelectCustodianDoctorClose = () => {
    setOpenSelectCustodianDoctorDialog(false);
    handleShowTakeOverDiagnosisDialog(currentHzvSchein);
  };

  const activeContractParticipation = async (
    hpmParticipationResponse: GetContractsInformationFromHpmResponse,
    hpmInformation: HpmViewInformation
  ) => {
    if (hpmInformation?.isLoading) return;
    const activeContracts = hpmParticipationResponse?.hpmResponses?.filter(
      (resp) => resp.status === 'Active'
    );
    if (!activeContracts || activeContracts.length <= 0) return;
    const contractId = activeContracts[0].contractId;
    const isDeputyActive = activeContracts[0].isDeputy;
    const isNotEnroll =
      modelState?.hzvInformation?.status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet;
    const isFirsTimeInQuarter =
      ParticipationService.isHpmCheckDateValid(hpmInformation);
    const isForceUpdate = modelState.getHzvContractFromHpmRequest?.forceUpdate;

    if (isNotEnroll && (isFirsTimeInQuarter || isForceUpdate)) {
      if (
        !(await webWorkerServices.doesContractSupportFunctions(
          ['VERT833'],
          contractId
        ))
      ) {
        return;
      }

      const { data } = await getContractDoctorGroup({
        contractIds: [contractId],
        doctorIds: doctors.map((d) => d.id as string),
        statuses: [DoctorParticipateStatus.Active],
        time: datetimeUtil.now(),
      });
      const groupDoctorContracts = data?.doctorParticipateContracts;
      const type = 'OnSubmitAction';
      const groupDoctorContract = groupDoctorContracts?.find(
        (g) => g.contractID === contractId
      );
      if (!groupDoctorContract) {
        return;
      }
      if (groupDoctorContract.doctors.length == 0) {
        return;
      }
      // Priority to get currentLoggedInUser
      let doctorId = groupDoctorContract.doctors.find(
        (d) => d.doctorId === currentLoggedInUser.id
      )?.doctorId;
      if (!doctorId) {
        doctorId = groupDoctorContract.doctors[0].doctorId;
      }
      if (!doctorId && availableFavContracts?.length > 0) {
        setIsContractActiveWithoutAnyParticipation(true);
        return;
      }
      const payload = {
        action: isDeputyActive
          ? PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy
          : PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation,
        contractId,
        doctorId,
        date: isDeputyActive ? datetimeUtil.startOf(datetimeUtil.now(), 'day').toDate() : datetimeUtil.getStartOfQuarter(datetimeUtil.now()),
        triggerSelectCustodian: !isDeputyActive,
      };
      dispatch({ type, payload });
    } else {
      const startDate = +datetimeUtil.getStartOfQuarter(
        hpmInformation.checkedTime
      );

      reloadScheinAndActivateHzvSchein({
        patientManagement,
        create: true,
        triggerSelectCustodian: false,
        startDate,
      });
    }
  };

  const showToasterHPM = debounce((hpmErrorMessages: HpmErrorMessage[]) => {
    hpmErrorMessages = hpmErrorMessages.filter(
      (err) => err.code !== MissingInsuranceNumber_Error
    );
    if (hpmErrorMessages.length === 0) return;

    const defaultErrorContent = t('checkHpmParticipationFail');
    hpmErrorMessages = hpmErrorMessages.filter((hpmError) =>
      isNotEmpty(hpmError?.code)
    );
    const messageKey = hpmErrorMessages?.map((m) => m.code).join(',');
    const message =
      hpmErrorMessages.filter((m) => isNotEmpty(m.message))?.length > 0 ? (
        <>
          {hpmErrorMessages.map((log) => log.message)}
          <br />
        </>
      ) : (
        defaultErrorContent
      );
    alertError(message, { key: messageKey });
  }, 2000);

  const buttonStyle = HzvButtonHook.useClassHzvButton(
    modelState.hzvInformation != null ? modelState.hzvInformation.status : null,
    isContractActiveWithoutAnyParticipation,
    modelState.hzvInformation?.previousParticipation,
    hzvInformationResponse?.contractInformations?.flat()[0]
  );

  const triggerReRunValidation = (contractId) =>
    reRunValidateService({
      patientId: patient.id,
      contractId: contractId,
    }).then(() => {
      timelineActions.reloadTimeline();
    });

  //#region useEffectHook
  useEffect(() => {
    if (!modelState?.hzvAvailableContracts) return;
    const contractIds = modelState.hzvAvailableContracts.map(
      (contract) => contract.contractId
    );
    Promise.all(
      contractIds.map((contractId) =>
        webWorkerServices.doesContractSupportHpmFunctions(
          [HpmFunctionType.HpmFunktionSimpleTyp_PRUEFE_TEILNAHMEN_LISTE],
          contractId
        )
      )
    ).then((res) => {
      setIsSupportHpmOnlineCheck(
        res.every((isSupportHpmOnlineCheck) => isSupportHpmOnlineCheck)
      );
    });
  }, [modelState?.hzvAvailableContracts]);
  useEffect(() => {
    if (!hpmParticipationResponse) return;

    if (hpmParticipationResponse.hpmErrorMessages?.length > 0) {
      showToasterHPM(hpmParticipationResponse.hpmErrorMessages);
    } else if (
      hpmParticipationResponse.hpmResponses?.every((r) => r.status === 'Active')
    ) {
      const isDeputy = hpmParticipationResponse.hpmResponses.every(
        (r) => r.isDeputy
      );
      const message = t(
        `checkHpmParticipationSuccessfully${isDeputy ? 'Deputy' : ''}`
      );
      alertSuccessfully(message, { key: message });
    } else {
      const message = t('checkHpmParticipationNoActiveParticipation');
      alertWarning(message, { key: message });
    }
    refetchPatientProfile();
    dispatch({
      type: 'OnGetHzvContractFromHpmServiceLoaded',
      payload: hpmParticipationResponse,
    });
    activeContractParticipation(
      hpmParticipationResponse,
      modelState?.hpmInformation
    );
  }, [hpmParticipationResponse, modelState?.hpmInformation?.isLoading]);

  useEffect(() => {
    const isNotEnroll =
      modelState?.hzvInformation?.status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet;
    const isHpmActive = modelState?.hpmInformation?.isActive;
    const hasParticipationAction =
      !!allowedShowingParticipationMenuItems?.length;
    const isActiveByFav = !!availableFavContracts?.length;
    if (
      isHpmActive &&
      isNotEnroll &&
      !hasParticipationAction &&
      isActiveByFav
    ) {
      setIsContractActiveWithoutAnyParticipation(true);
    }
  }, [
    modelState?.hpmInformation?.isActive,
    modelState?.hzvInformation?.status,
    availableFavContracts,
    allowedShowingParticipationMenuItems,
  ]);

  useEffect(() => {
    if (
      !contractMetaItems ||
      (contractMetaItems?.length == 0 &&
        modelState.hzvAvailableContracts?.length === 0)
    ) {
      return;
    }
    dispatch({
      type: 'OnListHZVContractsLoaded',
      payload: {
        hzvContractList: contractMetaItems,
      },
    });
  }, [contractMetaItems]);

  useEffect(() => {
    if (!modelState.hzvAvailableContracts?.length || !modelState.patient) {
      return;
    }
    dispatch({
      type: 'LoadCurrentHpmInformation',
      payload: {
        isHpmConnectionOk: props.isHpmConnectionOk,
      },
    });
  }, [
    modelState.hzvAvailableContracts,
    modelState.patient,
    props.isHpmConnectionOk,
  ]);

  useEffect(() => {
    dispatch({
      type: 'CheckHpmServiceConnection',
      payload: {
        patientId: patient.id,
      },
    });
  }, [patient.id]);

  useEffect(() => {
    if (!modelState.hzvRequest && !modelState.actionHzvContract) {
      return;
    }
    dispatch({
      type: 'setLoadingState',
      payload: {
        loading: true,
      },
    });
  }, [
    JSON.stringify(modelState.hzvRequest),
    JSON.stringify(modelState.actionHzvContract),
  ]);

  useEffect(() => {
    if (!hzvInformationResponse) {
      return;
    }
    dispatch({
      type: 'OnHZVContractResponsed',
      payload: {
        hzvInformation: {
          ...hzvInformationResponse?.contractInformations?.flat()[0],
        },
      },
    });
    dispatch({
      type: 'setLoadingState',
      payload: {
        loading: false,
      },
    });
  }, [JSON.stringify(hzvInformationResponse)]);

  useEffect(() => {
    if (!hzvToolTipContentResponse) {
      return;
    }
    dispatch({
      type: 'OnHzvToolTipChanged',
      payload: {
        toolTipContents: hzvToolTipContentResponse,
      },
    });
  }, [hzvToolTipContentResponse]);

  useEffect(() => {
    if (!patient) {
      return;
    } else {
      dispatch({
        type: 'PatientChange',
        payload: {
          patient: patient,
        },
      });
    }
  }, [patient]);

  useEffect(() => {
    if (!modelState.currentPatientId) {
      return;
    }
    dispatch({
      type: 'LoadHZVInformation',
      payload: null,
    });
  }, [
    patientFileStore.activeInsurance?.ikNumber,
    patientFileStore.activeInsurance?.insuranceNumber,
    modelState.currentPatientId,
  ]);

  useEffect(() => {
    if (!modelState.toastInfo) {
      return;
    }
    const message = t(modelState.toastInfo.message as any);
    if (modelState.toastInfo.intent === Intent.DANGER) {
      alertError(message);
    }
    if (modelState.toastInfo.intent === Intent.SUCCESS) {
      alertSuccessfully(message);
    }
  }, [modelState.toastInfo]);

  useEffect(() => {
    if (!actionHzvContractResponse) {
      return;
    }
    dispatch({
      type: 'OnActionHzvContractResponse',
      payload: {
        actionResult: { ...actionHzvContractResponse },
      },
    });
    switch (actionHzvContractResponse.action) {
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation:
        reloadPatient();
        reloadScheinAndActivateHzvSchein({
          patientManagement,
          create: true,
          triggerSelectCustodian: false,
          endDate: 0,
        });
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy:
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation:
        reloadPatient();
        reloadScheinAndActivateHzvSchein({
          patientManagement,
          create: true,
          triggerSelectCustodian: false,
        });
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation:
        reloadPatient();
        reloadScheinAndActivateHzvSchein({
          patientManagement,
          create: true,
          newDoctorId: modelState.actionHzvContract?.doctorId,
          triggerSelectCustodian:
            actionHzvContractResponse?.triggerSelectCustodian,
          startDate: modelState.actionHzvContract?.startDate,
        });
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate:
        triggerReRunValidation(modelState.actionHzvContract?.contractId);
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation:
        reloadPatient();
        reloadScheinAndActivateHzvSchein({
          patientManagement,
          create: true,
          triggerSelectCustodian: false,
          endDate: modelState.actionHzvContract?.endDate,
        });
        setIsContractActiveWithoutAnyParticipation(false);
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelParticipation:
        reloadPatient();
        reloadScheinAndActivateHzvSchein({
          patientManagement,
          create: false,
          triggerSelectCustodian: false,
        });
        setIsContractActiveWithoutAnyParticipation(false);
        break;
    }

    refetchGetContractInformationHzv();
  }, [actionHzvContractResponse, refetchGetContractInformationHzv]);

  // #endregion useEffectHook

  // #region useListen
  useListenPatientParticipationChange((response) => {
    if (!response) {
      return;
    }

    if (props.patient?.id === response.patientId) {
      dispatch({
        type: 'OnPatientParticipationChanged',
        payload: null,
      });
    }
  });
  // #endregion useListen

  const handleHzvActionClick = (action: PatientEnrollmentInformationAction) => {
    dispatch({
      type: 'OnHzvActionClicked',
      payload: {
        action: action,
      },
    });
  };
  const startEnrollment = () => {
    handleHzvActionClick(
      PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment
    );
    return true;
  };

  const continueEnrollment = () => {
    handleHzvActionClick(
      PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ContinueEnrollment
    );
    return true;
  };

  usePatientPostAction(
    {
      contractId: modelState.actionHzvContract?.contractId,
      action: PatientPostActionType.START_ENROLLMENT,
      handler: startEnrollment,
    },
    []
  );

  usePatientPostAction(
    {
      contractId: modelState.actionHzvContract?.contractId,
      action: PatientPostActionType.CONTINUE_ENROLLMENT,
      handler: continueEnrollment,
    },
    []
  );
  const isOpenPatientEnrollmentForm =
    modelState.currentActionProcess ===
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment ||
    modelState.currentActionProcess ===
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ContinueEnrollment;

  if (
    patient.patientInfo.genericInfo.patientType ===
    PatientType.PatientType_Private
  ) {
    return null;
  }

  return (
    <Flex className={`${className}`}>
      <Flex>
        <Popover
          isOpen={
            disabled ||
              modelState.loading ||
              modelState.hzvInformation?.status ===
              PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_InvalidIkNumber ||
              modelState.hzvInformation?.status ===
              PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotSupportedIkNumber
              ? false
              : undefined
          }
          onClose={() => {
            dispatch({
              type: 'OnPopOverHzvClosed',
              payload: {
                currentActionProcess: modelState.currentActionProcess,
              },
            });
          }}
          enforceFocus={false}
          position={PopoverPosition.BOTTOM_LEFT}
          transitionDuration={0}
          content={
            <>
              {modelState.currentActionProcess &&
                modelState.datePickerProps && (
                  <Flex column auto className={'popover-form-container'}>
                    <DatePickerForm
                      dateShortcut={modelState.datePickerProps.dateShortcut}
                      title={modelState.datePickerProps.title}
                      cancelLabel={modelState.datePickerProps.cancelLabel}
                      submitLabel={modelState.datePickerProps.submitLabel}
                      defaultValue={modelState.datePickerProps.defaultValue}
                      minDate={modelState.datePickerProps.minDate}
                      maxDate={modelState.datePickerProps.maxDate}
                      dayPickerProps={modelState.datePickerProps.dayPickerProps}
                      className={'date-picker-form'}
                      onCancel={() => {
                        dispatch({
                          type: 'OnCancelOrCloseAction',
                          payload: {
                            action: modelState.currentActionProcess,
                          },
                        });
                      }}
                      onSubmit={(date: Date) => {
                        dispatch({
                          type: 'OnSubmitAction',
                          payload: {
                            action: modelState.currentActionProcess,
                            date: date,
                            participationIds:
                              modelState.hzvInformation?.participationIds,
                          },
                        });
                      }}
                      isSubmitting={
                        modelState.currentSubmittingActionProcess != null
                      }
                      intentSubmit={modelState.datePickerProps.intentSubmit}
                      disableDays={modelState.datePickerProps.disableDays}
                    />
                  </Flex>
                )}

              {(allowedShowingEnrollmentMenuItems ||
                allowedShowingParticipationMenuItems) &&
                !modelState.currentActionProcess && (
                  <EnrollmentMenu
                    className={''}
                    enrollmentActionItems={
                      // modelState.hzvMenu.enrollmentActionItems
                      allowedShowingEnrollmentMenuItems
                    }
                    participationActionItems={
                      allowedShowingParticipationMenuItems
                    }
                    currentParticipation={
                      modelState.hzvInformation?.participation
                    }
                    terminateLogInformation={terminateLogInformation}
                    onClick={handleHzvActionClick}
                    hpmInformation={{
                      ...modelState.hpmInformation,
                      hasError:
                        !props.isHpmConnectionOk ||
                        modelState.hpmInformation.hasError,
                    }}
                    patient={patient}
                    showHpmInformation={false}
                    // footerMessage={modelState.hzvMenu.footerMessage}
                    type={MainGroup.HZV}
                  />
                )}
            </>
          }
        >
          <Tooltip
            content={
              <StyledParticipationToolTipContent
                className="hzv-tooltip-content"
                contentLines={modelState.hzvHintContentLines}
                isLoading={
                  modelState.loading || !modelState.hzvHintContentLines
                }
              />
            }
            position={Position.TOP}
            interactionKind={PopoverInteractionKind.HOVER}
            disabled={
              !modelState?.hzvHintContentLines ||
              modelState.hzvHintContentLines?.length === 0
            }
          >
            <Button
              className={getCssClass(
                'hzv-button',
                'widget-button',
                buttonStyle
              )}
              small
              disabled={
                modelState.loading ||
                disabled ||
                !available ||
                buttonStyle.includes(Classes.DISABLED)
              }
              loading={modelState.loading}
              rightIcon={
                <>
                  <Svg
                    src={
                      buttonStyle.includes('gray-button')
                        ? ArrowDownGrayIcon
                        : ArrowDownIcon
                    }
                    size={16}
                  />
                </>
              }
            >
              <Flex className="text-wrapper">
                <BodyTextS
                  fontWeight={600}
                  color={FavButtonHook.intentIconMapping(buttonStyle)?.[0]}
                >
                  {t('contractTypeHZV')}
                </BodyTextS>
                {isHzvContractConflictWithHpm && (
                  <Flex className="hpm-icon-conflict">
                    <AlertTriangleSolid
                      fill={FavButtonHook.intentIconMapping(buttonStyle)?.[2]}
                    />
                  </Flex>
                )}
              </Flex>
            </Button>
          </Tooltip>
        </Popover>
      </Flex>
      {isOpenPatientEnrollmentForm && (
        <PatientEnrollmentForm
          listContractDoctors={props.availableHzvContracts}
          isOpen={isOpenPatientEnrollmentForm}
          isChangingDoctor={modelState.isChangingDoctor}
          enrollment={
            modelState.currentActionProcess ===
              PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ContinueEnrollment &&
              modelState.hzvInformation &&
              modelState.hzvInformation.enrollment
              ? modelState.hzvInformation.enrollment
              : undefined
          }
          onClose={() => {
            dispatch({
              type: 'OnCancelOrCloseAction',
              payload: {
                type: modelState.currentActionProcess,
                needToReload: true,
              },
            });
          }}
          isHzvButton
          onCreateSchein={() => {
            dispatch({
              type: 'OnCancelOrCloseAction',
              payload: {
                type: modelState.currentActionProcess,
                needToReload: true,
              },
            });
            openCreateSchein();
          }}
          onReloadLoadListContracts={onReloadLoadListContracts}
        />
      )}
      <Dialog
        title={t('createActiveCustodianParticipationLabelDialog')}
        isOpen={
          modelState.currentActionProcess ===
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation &&
          !modelState.datePickerProps
        }
        onClose={() => {
          dispatch({
            type: 'OnCancelOrCloseAction',
            payload: {
              type: PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation,
            },
          });
        }}
        canOutsideClickClose={false}
      >
        <Box className={getCssClass(Classes.DIALOG_BODY, className)}>
          <Flex auto column>
            <ActiveContractParticipation
              listContractDoctors={props.availableHzvContracts}
              className={'form' + modelState.currentActionProcess}
              ikNumber={patientFileStore.activeInsurance?.ikNumber}
              insuranceNumber={
                patientFileStore.activeInsurance?.insuranceNumber
              }
              patientDob={modelState.patient.dateOfBirth}
              isSubmitting={
                modelState.currentSubmittingActionProcess ===
                PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation
              }
              showHzvWarning={
                modelState.hzvInformation?.status ===
                PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet
              }
              onSubmit={(
                contractId: string,
                doctorId: string,
                startDate: Date
              ) => {
                dispatch({
                  type: 'OnSubmitAction',
                  payload: {
                    action: modelState.currentActionProcess,
                    contractId: contractId,
                    doctorId: doctorId,
                    date: startDate,
                  },
                });
              }}
              onCancelActivateParticipation={() => {
                dispatch({
                  type: 'OnCancelOrCloseAction',
                  payload: {
                    action: modelState.currentActionProcess,
                  },
                });
              }}
            />
          </Flex>
        </Box>
      </Dialog>
      <Dialog
        title={t('treatAsDeputyTitle')}
        isOpen={
          modelState.currentActionProcess ===
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy
        }
        onClose={() => {
          dispatch({
            type: 'OnCancelOrCloseAction',
            payload: {
              type: PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy,
            },
          });
        }}
        canOutsideClickClose={false}
      >
        <Box className={getCssClass(Classes.DIALOG_BODY, className)}>
          <Flex auto column>
            <TreatAsDeputy
              ikNumber={patientFileStore.activeInsurance?.ikNumber}
              insuranceNumber={
                patientFileStore.activeInsurance?.insuranceNumber
              }
              onCancel={() => {
                dispatch({
                  type: 'OnCancelOrCloseAction',
                  payload: {
                    action: modelState.currentActionProcess,
                  },
                });
              }}
              onSubmit={(
                contractId: string,
                doctorId: string,
                startDate: Date
              ) => {
                dispatch({
                  type: 'OnSubmitAction',
                  payload: {
                    doctorId,
                    action: modelState.currentActionProcess,
                    contractId: contractId,
                    date: startDate,
                  },
                });
              }}
              isLoadingContract={!modelState.hzvAvailableContracts}
              listContracts={modelState.hzvAvailableContracts}
            />
          </Flex>
        </Box>
      </Dialog>
      <Alert
        cancelButtonText={
          modelState.confirmAction ? modelState.confirmAction.cancelButton : ''
        }
        confirmButtonText={
          modelState.confirmAction ? modelState.confirmAction.confirmButton : ''
        }
        intent={Intent.DANGER}
        isOpen={
          modelState.currentActionProcess ===
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelEnrollment ||
          modelState.currentActionProcess ===
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation ||
          modelState.currentActionProcess ===
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelParticipation
        }
        onConfirm={() => {
          dispatch({
            type: 'OnSubmitAction',
            payload: {
              action: modelState.currentActionProcess,
            },
          });
        }}
        onCancel={() => {
          dispatch({
            type: 'OnCancelOrCloseAction',
            payload: {
              action: modelState.currentActionProcess,
            },
          });
        }}
      >
        <H2>
          {modelState.confirmAction ? modelState.confirmAction.title : ''}
        </H2>
        <BodyTextM>
          {modelState.confirmAction ? modelState.confirmAction.content : ''}
        </BodyTextM>
      </Alert>{' '}
      {takeOverDiagnosisDialogOpen && (
        <SVSelectDiagnosisDialog
          patientId={patient.id}
          open
          currentSchein={currentHzvSchein}
          onCancel={() => setTakeOverDiagnosisDialogOpen(false)}
          onTakeover={handleTakeOver}
        />
      )}
      <HpmCheckHistoryDialog
        patientId={patient.id}
        contractIds={modelState.hzvAvailableContracts?.map((c) => c.contractId)}
        isHzv
        open={modelState.showHpmHistoryDialog}
        onClose={() => {
          handleHzvActionClick(
            PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ShowHpmCheckHistoryDialog
          );
        }}
        contractType={ContractType.ContractType_HouseDoctorCare}
      />
      {openSelectCustodianDoctorDialog && currentHzvSchein && (
        <SelectCustodianDoctorDialog
          isOpen
          schein={currentHzvSchein}
          doctors={doctors}
          onClose={onSelectCustodianDoctorClose}
        />
      )}
      <ConfirmationDialog />
    </Flex>
  );
}

export default memo(
  I18n.withTranslation(HzvButtonMemo, {
    namespace: 'PatientManagement',
    nestedTrans: 'HzvButton',
  })
);
