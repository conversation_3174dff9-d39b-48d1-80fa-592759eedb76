import { Intent, Tooltip } from '@tutum/design-system/components/Core';
import {
  getUTCMilliseconds,
  parseDate,
  setEndOfDate,
  utcToLocal,
} from '@tutum/design-system/infrastructure/utils';
import { PatientProfileResponse } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  ActionOnContractRequest,
  ActionOnContractResponse,
  CheckHpmServiceConnectionRequest,
  GetContractsInformationFromHpmRequest,
  GetContractsInformationFromHpmResponse,
  PatientEnrollmentInformationAction,
} from '@tutum/hermes/bff/service_domains_enrollment';
import {
  EmployeeProfileResponse,
  HpmInformationStatus,
} from '@tutum/hermes/bff/service_domains_profile';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { IPatientProfile } from '../../../types/profile.type';
import { IDatePickerFormProps } from '../date-picker-form/DatePickerForm';

import { GetContractInformationFromMvzRequest } from '@tutum/hermes/bff/app_mvz_enrollment';
import { ContractType } from '@tutum/hermes/bff/common';
import { CheckPotentialVerahRequest } from '@tutum/hermes/bff/legacy/service_domains_enrollment';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { SingleDateWrapper } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/hzv-button/HzvButton.styled';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import {
  HpmViewInformation,
  IContractInformation,
  IContractMetaItem,
  IParticipationActionProps,
  ParticipationAction,
  ParticipationButtonAction,
  getActionItemInformation,
} from '../participation/Participation.model';
import { ParticipationService } from '../participation/Participation.service';
export interface HzvButtonState {
  currentLoggedInUser?: IEmployeeProfile;
  hzvHintContentLines: string[] | null;
  patient: IPatientProfile;
  currentPatientId?: string;
  isChangingDoctor: boolean;
  loading: boolean;
  //HZV
  hzvAvailableContracts: IContractMetaItem[] | null;
  hzvAvailableDoctors: EmployeeProfileResponse[] | null;
  // hzvInformationResponse: IContractInformation;
  hzvPopoverButtonIsOpen: boolean;
  hzvTooltipButtonIsOpen: boolean;
  currentActionProcess: ParticipationAction | null;
  currentSubmittingActionProcess: PatientEnrollmentInformationAction | null;
  datePickerProps: IDatePickerFormProps | null;
  // request, response hooks
  hzvRequest: GetContractInformationFromMvzRequest | null;
  hzvInformation: IContractInformation | null;
  actionHzvContract: ActionOnContractRequest | null;
  confirmAction?: IConfirmActionProps;

  // Shows message after actions
  toastInfo: {
    message: string;
    intent: Intent;
  } | null;
  // EnrollmentMenuProps
  hpmInformation: HpmViewInformation;
  hzvMenu: IParticipationActionProps | null;

  //HPM
  checkHpmConnectionRequest?: CheckHpmServiceConnectionRequest;
  getHzvContractFromHpmRequest?: GetContractsInformationFromHpmRequest;
  // getHzvContractFromHpmResponse: GetContractsInformationFromHpmResponse;
  t: IFixedNamespaceTFunction;
  showHpmHistoryDialog: boolean;

  checkPotentialVerahRequest?: CheckPotentialVerahRequest;
}

export interface IConfirmActionProps {
  intent: Intent;
  title: string;
  content: string;
  cancelButton: string;
  confirmButton: string;
  onConfirm: (() => void) | null;
  onCancel: (() => void) | null;
}

export interface HzvButtonAction {
  type: string;
  payload: any;
}

const messageInforWithActionMap = new Map<
  PatientEnrollmentInformationAction,
  string
>([
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment,
    'createEnrollmentSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation,
    'activateParticipationSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation,
    'createActiveCustodianParticipationSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelEnrollment,
    'cancelEnrollmentSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelParticipation,
    'cancelContractSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeDoctor,
    'changeDoctorSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateDeputy,
    'terminateParticipationSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation,
    'terminateParticipationSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate,
    'changeParticipationStartDateSuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation,
    'undoTerminatedContractSucessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy,
    'treatAsDeputySuccessful',
  ],
  [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveCustodianTreatment,
    'activateCustodianTreatmentSucessful',
  ],
]);

export const HzvButtonReducer = (
  state: HzvButtonState,
  action: HzvButtonAction
) => {
  const t = state.t;

  switch (action.type) {
    case 'loadingCurrentLoggedInUser': {
      state.currentLoggedInUser = {
        ...action.payload.currentLoggedInUser,
      } as IEmployeeProfile;
      break;
    }
    case 'setLoadingState': {
      state.loading = action.payload.loading;
      break;
    }
    case 'PatientChange': {
      const patient = { ...action.payload.patient } as PatientProfileResponse;
      state.patient = { ...patient };
      state.currentPatientId = state.patient.id;
      break;
    }
    case 'OnListHZVContractsLoaded': {
      state.hzvAvailableContracts = [...action.payload.hzvContractList];
      break;
    }
    case 'LoadHZVInformation': {
      state.hzvRequest = {
        patientId: state.patient.id,
        contractType: ContractType.ContractType_HouseDoctorCare,
      };
      state.hzvInformation = null;
      state.hzvHintContentLines = null;
      break;
    }
    case 'OnHZVContractResponsed': {
      const hzvInformation: IContractInformation = {
        ...action.payload.hzvInformation,
      };
      const hzvMenu: IParticipationActionProps = {
        enrollmentActionItems: [],
        participationActionItems: [],
      };

      hzvInformation?.participationActions?.forEach(
        (item: ParticipationAction) => {
          const hzvItem = getActionItemInformation(item, hzvInformation);
          if (hzvItem) {
            hzvItem.text = t(hzvItem.text || '');
            hzvMenu.participationActionItems.push(hzvItem);
          }
        }
      );
      hzvInformation?.enrollmentActions?.forEach(
        (item: ParticipationAction) => {
          const hzvItem = getActionItemInformation(item, hzvInformation);
          if (hzvItem) {
            hzvItem.text = t(hzvItem.text || '');
            hzvMenu.enrollmentActionItems.push(hzvItem);
          }
        }
      );
      state.hzvInformation = hzvInformation;
      state.hzvMenu = hzvMenu;

      if (hzvInformation.hasHpmError && hzvInformation.hpmErrorMessages) {
        state.toastInfo = {
          message: hzvInformation.hpmErrorMessages
            .map((x) => x.message)
            .join(', '),
          intent: Intent.DANGER,
        };
      }
      break;
    }
    case 'OnHzvActionClicked': {
      const actionItem: ParticipationAction = action.payload.action;

      state.currentActionProcess = actionItem;
      state.datePickerProps = null;
      state.isChangingDoctor = false;

      switch (actionItem) {
        case ParticipationButtonAction.ParticipationButtonAction_ViewOnlineParticipationHistory:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ShowHpmCheckHistoryDialog: {
          state.currentActionProcess = null;
          state.showHpmHistoryDialog = !state.showHpmHistoryDialog;
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment: {
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation: {
          const endDate = state.hzvInformation?.participation?.updatedDate;
          const minDate = endDate
            ? datetimeUtil.dateToMoment(endDate).toDate()
            : datetimeUtil
                .getPreviousQuarters(datetimeUtil.dateToMoment(), 4)
                .startOf('quarter')
                .toDate();

          const maxDate = datetimeUtil.endOf(datetimeUtil.date(), 'day');

          const disableDays = (date: Date) => {
            if (+date <= +maxDate) return false;
            return !datetimeUtil.isFirstDayOfQuarter(date);
          };

          const minDateShortcut = datetimeUtil.date();
          const maxDateShortcut = datetimeUtil.date();
          minDateShortcut.setMonth(maxDateShortcut.getMonth() - 18);
          const dateShortcuts = ParticipationService.generateQuarterShortcut(
            minDateShortcut,
            maxDateShortcut,
            t,
            false
          );
          const dayPickerProps = {
            renderDay: (date) => {
              const tooltipContent = datetimeUtil.isBefore(date, minDate)
                ? t('earliestDateWarning')
                : undefined;
              return (
                <Tooltip placement="right" content={tooltipContent}>
                  <SingleDateWrapper> {date.getDate()}</SingleDateWrapper>
                </Tooltip>
              );
            },
          };

          if (state.hzvInformation?.participationIds?.length) {
            state.datePickerProps =
              ParticipationService.generateDatePickerProps(
                t('createActiveCustodianParticipationLabelDialog'),
                t('cancelButton'),
                t('activateButton'),
                null,
                null,
                new Date('12/31/2050'),
                undefined,
                dateShortcuts,
                dayPickerProps,
                disableDays
              );
          }
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation: {
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelEnrollment:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelParticipation:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation: {
          state.confirmAction = ParticipationService.generateConfirmActionProps(
            actionItem,
            t
          );
          state.getHzvContractFromHpmRequest = undefined;
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeDoctor: {
          state.currentActionProcess =
            PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment;
          state.isChangingDoctor = true;
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation: {
          const today = datetimeUtil.date();
          const maxDate = datetimeUtil.getNextQuarters(today, 4).toDate();
          let minDate: Date | null = null;
          if (state.hzvInformation?.participation?.startDate) {
            minDate = utcToLocal(
              parseDate(state.hzvInformation.participation.startDate) as Date
            );
          }
          if (!minDate) {
            minDate = datetimeUtil.getPreviousQuarters(today, 4).toDate();
          }
          const dateShortcuts = ParticipationService.generateQuarterShortcut(
            minDate,
            maxDate,
            t
          );

          state.datePickerProps = ParticipationService.generateDatePickerProps(
            t('teminateContractParticipationTitle'),
            t('cancelButton'),
            t('terminateContractParticipationSubmit'),
            null,
            minDate,
            maxDate,
            Intent.DANGER,
            dateShortcuts
          );
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate: {
          const endDate = state.hzvInformation?.previousParticipation?.endDate;
          const minDate = endDate
            ? datetimeUtil.dateToMoment(endDate).add(1, 'd').toDate()
            : datetimeUtil
                .getPreviousQuarters(datetimeUtil.dateToMoment(), 4)
                .startOf('quarter')
                .toDate();
          const maxDate = datetimeUtil.date();

          const disableDays = (date: Date) => {
            if (date >= minDate && date <= maxDate) return false;
            return !datetimeUtil.isFirstDayOfQuarter(date);
          };

          const minDateShortcut = datetimeUtil.date();
          const maxDateShortcut = datetimeUtil.date();
          minDateShortcut.setMonth(maxDateShortcut.getMonth() - 18);
          state.datePickerProps = ParticipationService.generateDatePickerProps(
            t('changeContractStartDateTitle'),
            t('changeContractStartDateCancel'),
            t('changeContractStartDateSubmit'),
            null,
            null,
            new Date('12/31/2050'),
            undefined,
            ParticipationService.generateQuarterShortcut(
              minDateShortcut,
              maxDateShortcut,
              t,
              false
            ),
            undefined,
            disableDays
          );
          break;
        }

        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateDeputy: {
          const maxDate = datetimeUtil.date();
          const minDate = datetimeUtil.getPreviousQuarters(maxDate, 4).toDate();
          const dateShortcuts = ParticipationService.generateQuarterShortcut(
            minDate,
            maxDate,
            t
          );

          state.datePickerProps = ParticipationService.generateDatePickerProps(
            t('teminateContractParticipationTitle'),
            t('cancelButton'),
            t('terminateContractParticipationSubmit'),
            null,
            minDate,
            maxDate,
            Intent.DANGER,
            dateShortcuts
          );
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveCustodianTreatment: {
          const maxDate = datetimeUtil.date();
          const endDate = state.hzvInformation?.previousParticipation?.endDate;
          const minDate = endDate
            ? datetimeUtil.dateToMoment(endDate).add(1, 'd').toDate()
            : datetimeUtil
                .getPreviousQuarters(datetimeUtil.dateToMoment(), 4)
                .startOf('quarter')
                .toDate();
          const dateShortcuts = ParticipationService.generateQuarterShortcut(
            minDate,
            maxDate,
            t
          );

          state.datePickerProps = ParticipationService.generateDatePickerProps(
            t('activateCustodianTitle'),
            t('activateCustodianCancelButton'),
            t('activateCustodianSubmitButton'),
            null,
            minDate,
            maxDate,
            undefined,
            dateShortcuts
          );
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CheckPotentialVERAH: {
          state.currentActionProcess = null;
          state.checkPotentialVerahRequest = {
            patientId: state.patient.id,
            contractId: state.hzvInformation?.contractId as string,
          };
          break;
        }

        case ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_PerformHpmCheckOnlineParticipation: {
          const contractIds = (state.hzvAvailableContracts || []).map(
            (c) => c.contractId
          );
          let doctorIds = state.hzvAvailableContracts
            ?.map((c) => c.doctorIds)
            .flat();
          if (
            state.currentLoggedInUser?.id &&
            (doctorIds || []).includes(state.currentLoggedInUser.id)
          )
            doctorIds = [state.currentLoggedInUser.id];

          state.getHzvContractFromHpmRequest = {
            patientId: state.patient.id,
            contractIds,
            forceUpdate: true,
            doctorIds: doctorIds,
            saveHistory: true,
          };
          state.hpmInformation = {
            ...state.hpmInformation,
            isLoading: true,
          };
          state.currentActionProcess = null;
          break;
        }
      }
      state.currentSubmittingActionProcess = null;
      break;
    }
    case 'OnActionHzvContractResponse': {
      const response: ActionOnContractResponse = action.payload.actionResult;
      state.toastInfo = null;
      const messageInfo = messageInforWithActionMap.get(response.action);
      const intent: Intent = Intent.SUCCESS;

      if (messageInfo) {
        state.toastInfo = {
          message: messageInfo,
          intent: intent,
        };
      }

      // reset data if the action is successful
      state.currentActionProcess = null;
      state.currentSubmittingActionProcess = null;
      state.confirmAction = undefined;
      // Reload HzvInformation.
      state.hzvRequest = {
        patientId: state.patient.id,
        contractType: ContractType.ContractType_HouseDoctorCare,
      };
      state.hzvInformation = null;
      break;
    }
    case 'OnCancelOrCloseAction': {
      state.confirmAction = undefined;
      state.currentActionProcess = null;
      state.currentSubmittingActionProcess = null;
      state.datePickerProps = null;
      if (action.payload.needToReload) {
        // Reload HzvInformation.
        state.hzvRequest = {
          patientId: state.patient.id,
          contractType: ContractType.ContractType_HouseDoctorCare,
        };
      }
      break;
    }
    case 'OnSubmitAction': {
      const actionType = action.payload.action;
      state.actionHzvContract = null;
      state.currentSubmittingActionProcess = actionType;
      switch (actionType) {
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation: {
          const contractId: string = action.payload.contractId;
          const doctorId: string = action.payload.doctorId;
          const startDate: Date = action.payload.date;
          const epochStartDate: number =
            datetimeUtil.startOf(startDate, 'day').unix() * 1000;
          state.actionHzvContract = {
            action:
              PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation,
            patientId: state.patient.id,
            enrollmentId: undefined,
            patientParticipationIds: undefined,
            doctorId: doctorId,
            contractId: contractId,
            startDate: epochStartDate,
            endDate: undefined,
            triggerSelectCustodian: action.payload.triggerSelectCustodian,
          };
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation: {
          const startDate: Date = action.payload.date;
          state.actionHzvContract = {
            action:
              PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation,
            patientId: state.patient.id,
            patientParticipationIds: state.hzvInformation?.participationIds,
            doctorId: undefined,
            contractId: undefined,
            startDate: datetimeUtil.startOf(startDate, 'day').unix() * 1000,
            endDate: undefined,
          };
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelEnrollment: {
          state.actionHzvContract = {
            action: actionType,
            patientId: state.patient.id,
            contractId: state.hzvInformation?.contractId,
          };
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelParticipation: {
          state.actionHzvContract = {
            action: actionType,
            patientId: state.patient.id,
            contractId: state.hzvInformation?.contractId,
          };
          state.getHzvContractFromHpmRequest = undefined;
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeDoctor: {
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation: {
          const date: Date = action.payload.date;
          const endDate = setEndOfDate(date);
          if (date) {
            state.actionHzvContract = {
              action: actionType,
              patientId: state.patient.id,
              contractId: state.hzvInformation?.contractId,
              endDate: getUTCMilliseconds(endDate),
            };
          }
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateDeputy: {
          const date: Date = action.payload.date;
          if (date) {
            state.actionHzvContract = {
              action: actionType,
              patientId: state.patient.id,
              contractId: state.hzvInformation?.contractId,
              endDate: getUTCMilliseconds(date),
            };
          }
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate: {
          const date: Date = action.payload.date;
          if (date) {
            state.actionHzvContract = {
              action: actionType,
              patientId: state.patient.id,
              enrollmentId: undefined,
              patientParticipationIds: action.payload.participationIds,
              doctorId: undefined,
              contractId: undefined,
              startDate: getUTCMilliseconds(date),
              endDate: undefined,
            };
          }
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation: {
          state.actionHzvContract = {
            action: actionType,
            patientId: state.patient.id,
            contractId: state.hzvInformation?.contractId,
          };
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy: {
          const date: Date = action.payload.date;
          if (date) {
            state.actionHzvContract = {
              doctorId: action.payload.doctorId,
              action: actionType,
              patientId: state.patient.id,
              patientParticipationIds: state.hzvInformation?.participationIds,
              contractId: action.payload.contractId,
              startDate: getUTCMilliseconds(date),
            };
          }
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveCustodianTreatment: {
          const date: Date = action.payload.date;
          if (date) {
            state.actionHzvContract = {
              action: actionType,
              patientId: state.patient.id,
              enrollmentId: state.hzvInformation?.enrollmentId,
              patientParticipationIds: state.hzvInformation?.participationIds,
              startDate: getUTCMilliseconds(date),
            };
          }
          break;
        }
      }
      break;
    }
    case 'OnHzvToolTipChanged': {
      state.hzvHintContentLines = action.payload.toolTipContents
        ? action.payload.toolTipContents.contentLines
        : [];
      break;
    }
    case 'CheckHpmServiceConnection': {
      const patientId = action.payload.patientId;
      state.checkHpmConnectionRequest = {
        patientId: patientId,
      };
      break;
    }
    case 'OnPopOverHzvClosed': {
      const currentActionProcess: PatientEnrollmentInformationAction =
        action.payload.currentActionProcess;
      switch (currentActionProcess) {
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate: {
          state.currentActionProcess = null;
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation ||
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateDeputy: {
          state.currentActionProcess = null;
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveCustodianTreatment: {
          state.currentActionProcess = null;
          state.datePickerProps = null;
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation: {
          state.currentActionProcess = null;
          state.datePickerProps = null;
          break;
        }
      }
      break;
    }
    case 'LoadCurrentHpmInformation': {
      const isHpmConnectionOk = action.payload.isHpmConnectionOk;
      const hpmInformation = state.patient.listHpmInformation?.find(
        (i) =>
          (state.hzvAvailableContracts || []).findIndex(
            (c) => c.contractId === i.contractId
          ) >= 0
      );
      state.hpmInformation.status = hpmInformation?.status;
      state.hpmInformation = !!hpmInformation
        ? {
            ...state.hpmInformation,
            checkedTime: parseDate(hpmInformation.checkedDate),
            isLoading: false,
            isActive:
              hpmInformation.status ===
              HpmInformationStatus.HpmInformationStatus_Active,
          }
        : { ...state.hpmInformation };
      if (
        isHpmConnectionOk &&
        !ParticipationService.isHpmCheckDateValid(state.hpmInformation)
      ) {
        state.hpmInformation = {
          ...state.hpmInformation,
          isLoading: true,
          checkedTime: datetimeUtil.date(datetimeUtil.now()),
        };
        const contractIds = (state.hzvAvailableContracts || []).map(
          (c) => c.contractId
        );
        const doctorIds = state.hzvAvailableContracts
          ?.map((c) => c.doctorIds)
          .flat();
        state.getHzvContractFromHpmRequest = {
          patientId: state.patient.id,
          contractIds,
          forceUpdate: false,
          doctorIds,
          saveHistory: true,
        };
      }
      break;
    }
    case 'OnGetHzvContractFromHpmServiceLoaded': {
      const hpmParticipationResponse =
        action.payload as GetContractsInformationFromHpmResponse;
      const hpmResult =
        hpmParticipationResponse.hpmResponses?.length > 0
          ? hpmParticipationResponse.hpmResponses[0]
          : null;
      const hasError = hpmParticipationResponse.hpmErrorMessages?.length > 0;
      let isActive = state.hpmInformation?.isActive ?? false;
      if (hpmResult != null) {
        isActive =
          hpmResult.status === HpmInformationStatus.HpmInformationStatus_Active;
      }
      state.hpmInformation = {
        ...state.hpmInformation,
        checkedTime: hpmResult?.checkedDate
          ? parseDate(hpmResult?.checkedDate)
          : state.hpmInformation?.checkedTime,
        hasError: hasError,
        isActive: isActive,
        isLoading: false,
      };
      break;
    }
    case 'OnPatientParticipationChanged': {
      state.hzvRequest = {
        patientId: state.patient.id,
        contractType: ContractType.ContractType_HouseDoctorCare,
      };
      break;
    }
    default: {
      return state;
    }
  }
  return { ...state };
};
