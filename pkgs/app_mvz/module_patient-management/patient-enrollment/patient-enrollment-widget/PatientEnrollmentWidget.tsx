import { BodyTextS, Flex } from '@tutum/design-system/components';
import AlertTriangleSolid from '@tutum/design-system/components/Icons/AlertTriangleSolid';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { IMvzTheme } from '@tutum/mvz/theme';
import React, { ReactNode, useContext, useEffect, useReducer } from 'react';

import {
  PopoverInteractionKind,
  Position,
  Tooltip,
} from '@tutum/design-system/components/Core';
import {
  parseDate,
  toDateFormat,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { HpmFunctionType } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';
import { CheckHpmServiceConnectionResponse } from '@tutum/hermes/bff/service_domains_enrollment';
import { HpmInformationStatus } from '@tutum/hermes/bff/service_domains_profile';
import { FFWrapper } from '@tutum/mvz/hooks/useFeatureFlag';
import { HpmFunctionWrapper } from '@tutum/mvz/hooks/useHpmFunction';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { PatientEnrollmentWidgetReducer } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/PatientEnrollmentWidget.reducer';
import GDTButton from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/gdt-button/GDTButton.styled';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { usePatientEnrollmentFormStore } from '../patient-enrollment-form/PatientEnrollmentForm.store';
import { PatientEnrollmentWidgetHook } from './PatientEnrollmentWidget.hook';
import ArribaButton from './arriba-button/ArribaButton.styled';
import DMPButton from './dmp-button/DMPButton.styled';
import FavButton from './fav-button/FavButton.styled';
import HzvButton from './hzv-button/HzvButton.styled';

export interface IPatientEnrollmentWidgetProps {
  className?: string;
  theme?: IMvzTheme;
  isShowDMPEnrollDialog: boolean;
  openCreateSchein: (scheinId?: string) => void;
  onReloadLoadListContracts: () => void;
}

const PatientEnrollmentWidget = React.memo(
  ({
    className,
    isShowDMPEnrollDialog,
    openCreateSchein,
    onReloadLoadListContracts,
    t,
  }: IPatientEnrollmentWidgetProps &
    II18nFixedNamespace<
      keyof typeof PatientManagementI18n.PatientEnrollmentWidget
    >) => {
    const [modelState, dispatch] = useReducer(
      PatientEnrollmentWidgetReducer,
      {
        hpmConnectionInformation: {
          hpmInformation: [],
          ok: true,
        },
        currentStage: {
          value: 'IDLE',
        },
        hpmErrorInformation: '',
        t,
      },
      (data) => data
    );

    const {
      patientManagement: {
        patient,
        availableHzvContracts,
        availableFavContracts,
        isShowHzvButton,
      },
    } = useContext(PatientManagementContext.instance);

    const { activeInsurance } = patientFileStore;
    const isDisabledEnrollment = !activeInsurance?.insuranceNumber;
    const enrollmentStore = usePatientEnrollmentFormStore();

    const checkHpmServiceConnectionResponse =
      PatientEnrollmentWidgetHook.useCheckHpmServiceConnection(
        patient?.id || ''
      );

    useEffect(() => {
      if (!checkHpmServiceConnectionResponse) {
        return;
      }
      dispatch({
        type: 'OnCheckHpmServiceConnectionResponsed',
        payload: {
          ...checkHpmServiceConnectionResponse,
        } as CheckHpmServiceConnectionResponse,
      });
    }, [checkHpmServiceConnectionResponse]);

    // const [hzvInformation, setHzvInformation] = useState<{
    //   isActiveOnMvz: boolean;
    //   isActiveOnHpm: boolean;
    // }>({
    //   isActiveOnHpm: false,
    //   isActiveOnMvz: false,
    // });

    const renderTooltipcontent = (modelState, store): ReactNode => {
      let content: any = null;

      if (store.enrollmentHpmError) {
        content = <BodyTextS>{t('hpmEnrollmentFail')}</BodyTextS>;
      } else if (
        !modelState.hpmConnectionInformation?.ok &&
        modelState.hpmConnectionInformation.hpmInformation?.length
      ) {
        content = (
          <>
            {modelState.hpmConnectionInformation.hpmInformation?.map(
              (information, index) => {
                return (
                  <BodyTextS key={`lastHpmInformation-${index}`}>
                    {t('lastHpmInformation', {
                      checkedDate: toDateFormat(
                        parseDate(information.checkedDate) || new Date(),
                        { dateFormat: 'dd.MM.yyyy' }
                      ),
                      checkedTime: toDateFormat(
                        parseDate(information.checkedDate) || new Date(),
                        { timeFormat: 'hh:mm' }
                      ),
                      contractId: information.contractId,
                      status:
                        information.status ===
                        HpmInformationStatus.HpmInformationStatus_Active
                          ? t('active')
                          : t('inActive'),
                    })}
                  </BodyTextS>
                );
              }
            )}
            <BodyTextS className={className + '-line'}>
              {t('hpmConnectionFail')}
            </BodyTextS>
          </>
        );
      }

      return content ? (
        <Flex column className={'warning-hpm-connection'}>
          <Tooltip
            className="hpm-information-tooltip"
            placement="bottom"
            content={content}
            usePortal={false}
            position={Position.TOP}
            interactionKind={PopoverInteractionKind.HOVER}
          >
            <AlertTriangleSolid
              fill={COLOR.TEXT_NEGATIVE}
              width={16}
              height={16}
            />
          </Tooltip>
        </Flex>
      ) : null;
    };

    if (!patient) return null;

    return (
      <Flex className={className}>
        {renderTooltipcontent(modelState, enrollmentStore)}
        <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_SV}>
          {(availableHzvContracts.length > 0 || isShowHzvButton) && (
            <HzvButton
              disabled={isDisabledEnrollment}
              isHpmConnectionOk={modelState.hpmConnectionInformation?.ok}
              patient={patient}
              availableHzvContracts={availableHzvContracts}
              availableFavContracts={availableFavContracts}
              onHzvInformationChanged={() => {}}
              // onHzvInformationChanged={(contractStatus, isHpmActive) => {
              //   setHzvInformation({
              //     isActiveOnHpm: isHpmActive,
              //     isActiveOnMvz:
              //       (contractStatus ===
              //         PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Enrolled ||
              //         contractStatus ===
              //           PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Deputy) ??
              //       false,
              //   });
              // }}
              className="btn-hzv flex-shrink-0"
              openCreateSchein={openCreateSchein}
              onReloadLoadListContracts={onReloadLoadListContracts}
            />
          )}
        </FFWrapper>

        {availableFavContracts.length > 0 && (
          <FavButton
            className="flex-shrink-0"
            disabled={isDisabledEnrollment}
            patient={patient}
            openCreateSchein={openCreateSchein}
            isHpmConnectionOk={modelState.hpmConnectionInformation?.ok}
            availableFavContracts={availableFavContracts}
          />
        )}
        <DMPButton
          className="flex-shrink-0"
          patient={patient}
          isShowDMPEnrollDialog={isShowDMPEnrollDialog}
          openCreateSchein={openCreateSchein}
        />
        <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_SV}>
          {(availableHzvContracts.length > 0 || isShowHzvButton) && (
            <HpmFunctionWrapper
              hpmFunction={
                HpmFunctionType.HpmFunktionSimpleTyp_SENDE_ARRIBA_DATEN
              }
            >
              <ArribaButton patient={patient} className="flex-shrink-0" />
            </HpmFunctionWrapper>
          )}
        </FFWrapper>
        <GDTButton patient={patient} />
      </Flex>
    );
  }
);

export default I18n.withTranslation(PatientEnrollmentWidget, {
  namespace: 'PatientManagement',
  nestedTrans: 'PatientEnrollmentWidget',
});
