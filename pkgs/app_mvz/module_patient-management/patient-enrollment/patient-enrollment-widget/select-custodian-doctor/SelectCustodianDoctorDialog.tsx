import React, { useMemo, useRef } from 'react';

import {
  BodyTextM,
  BodyTextS,
  Button,
  Dialog,
  Flex,
  FormGroup2,
  IMenuItem,
  ReactSelect,
} from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import { Field, Form, Formik, FormikProps } from 'formik';
import { Classes, Intent } from '@tutum/design-system/components/Core';
import { COLOR } from '@tutum/design-system/themes/styles';
import { ScheinItem } from '@tutum/hermes/bff/legacy/schein_common';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { useMutationUpdateScheinDoctor } from '@tutum/hermes/bff/legacy/app_mvz_enrollment';

export interface SelectCustodianDoctorDialogProps {
  className?: string;
  isOpen: boolean;
  doctors: IEmployeeProfile[];
  schein: ScheinItem;
  onClose: () => void;
}

const SelectCustodianDoctorDialog = ({
  className,
  schein,
  isOpen,
  doctors,
  onClose,
}: SelectCustodianDoctorDialogProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.HzvSelectCustodianDoctor
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'HzvSelectCustodianDoctor',
  });

  const { t: tHandoverLetter } = I18n.useTranslation<
    keyof typeof FormI18n.HandoverLetter
  >({
    namespace: 'Form',
    nestedTrans: 'HandoverLetter',
  });

  const formRef = useRef(null);

  const updateScheinDoctor = useMutationUpdateScheinDoctor({
    onSuccess: () => {
      onClose();
    },
  });

  const doctorsHzv = useMemo(() => {
    return doctors.filter((doctor) =>
      (doctor.hzvContracts || []).some(
        (contract) => contract.contractId === schein.hzvContractId
      )
    );
  }, [doctors, schein]);

  const checkValidation = (values) => {
    const errors = {};

    if (!values.doctor) {
      errors['doctor'] = t('doctorRequired');
    }

    return errors;
  };

  const onSubmitForm = (values) => {
    updateScheinDoctor.mutate({
      scheinId: schein.scheinId,
      doctorId: values.doctor.id,
    });
  };

  const renderForm = ({
    submitCount,
    errors,
    touched,
    dirty,
    values,
  }: FormikProps<any>) => {
    return (
      <Form>
        <Flex className={Classes.DIALOG_BODY}>
          <Flex p={16} w="100%" gap={4} column>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
              {t('description')}
            </BodyTextM>
            <Flex align="center" gap={8} mb={12}>
              <Flex minWidth={64} w={64}>
                <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                  {t('contract')}
                </BodyTextS>
              </Flex>
              <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={500}>
                {schein.hzvContractId}
              </BodyTextM>
            </Flex>

            <FormGroup2
              label={t('doctor')}
              isRequired
              name="doctor"
              submitCount={submitCount}
              errors={errors}
              touched={touched}
            >
              <>
                <Field name="doctor">
                  {({ field, form }) => (
                    <ReactSelect
                      selectedValue={field.value?.id}
                      items={doctorsHzv.map((d) => ({
                        label: nameUtils.getDoctorName(d),
                        value: d.id!,
                        data: d,
                      }))}
                      onItemSelect={(item: IMenuItem) => {
                        form.setFieldValue(field.name, item.data);
                      }}
                    />
                  )}
                </Field>
                {!!values.doctor && (
                  <Flex
                    column
                    gap={8}
                    p="8px 16px"
                    mt="8px"
                    w="100%"
                    style={{
                      backgroundColor: COLOR.NEUTRAL_SECONDARY_HOVER,
                    }}
                  >
                    <Flex align="center" gap={8}>
                      <Flex minWidth="64px" w="64px">
                        <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                          {tHandoverLetter('bsnr')}:
                        </BodyTextS>
                      </Flex>
                      <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                        {values.doctor.bsnr}
                      </BodyTextM>
                    </Flex>
                    <Flex align="center" gap={8}>
                      <Flex minWidth="64px" w="64px">
                        <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                          {tHandoverLetter('lanr')}:
                        </BodyTextS>
                      </Flex>
                      <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                        {values.doctor.lanr}
                      </BodyTextM>
                    </Flex>
                    <Flex gap={8}>
                      <Flex minWidth="64px" w="64px">
                        <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                          {tHandoverLetter('expertise')}:
                        </BodyTextS>
                      </Flex>
                      <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                        {(values.doctor.areaOfExpertises || [])
                          .map((item) => item.dN)
                          .join(', ')}
                      </BodyTextM>
                    </Flex>
                  </Flex>
                )}
              </>
            </FormGroup2>
          </Flex>
        </Flex>
        <Flex className={Classes.DIALOG_FOOTER}>
          <Flex className={Classes.DIALOG_FOOTER_ACTIONS}>
            <Button
              intent={Intent.PRIMARY}
              minimal
              outlined
              large
              disabled={updateScheinDoctor.isPending}
              onClick={onClose}
            >
              {t('btnSkip')}
            </Button>
            <Button
              type="submit"
              intent={Intent.PRIMARY}
              large
              loading={updateScheinDoctor.isPending}
              disabled={updateScheinDoctor.isPending || !dirty}
            >
              {t('btnUpdate')}
            </Button>
          </Flex>
        </Flex>
      </Form>
    );
  };

  return (
    <>
      <Dialog
        title={t('title')}
        className={className}
        isCloseButtonShown
        isOpen={isOpen}
        onClose={onClose}
      >
        <Formik
          innerRef={formRef}
          initialValues={{
            doctor: doctorsHzv.find((doctor) => doctor.id === schein.doctorId),
          }}
          enableReinitialize
          validate={checkValidation}
          onSubmit={onSubmitForm}
        >
          {({ ...props }) => {
            return <>{renderForm(props)}</>;
          }}
        </Formik>
      </Dialog>
    </>
  );
};

export default SelectCustodianDoctorDialog;
