import { DateUtils } from 'react-day-picker';
import { parseDate } from '@tutum/design-system/infrastructure/utils/parsers';
import { PatientParticipationStatus } from '@tutum/hermes/bff/legacy/service_domains_patient_participation';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  HpmViewInformation,
  IContractInformation,
} from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/participation/Participation.model';
import { useState, useEffect } from 'react';

export const useCheckIsContractConflictWithHpm = (
  informations: Array<{
    contractInformation: IContractInformation;
    hpmInformation: HpmViewInformation;
  }>,
  isSupportHpmOnlineCheck = true
): boolean => {
  const [haveConflict, setHaveConflict] = useState<boolean>(false);
  useEffect(() => {
    if (!isSupportHpmOnlineCheck) {
      setHaveConflict(false);
      return;
    }
    if (!informations || informations.length == 0) {
      setHaveConflict(false);
      return;
    }
    let haveAnyConflict: boolean | null = null;
    for (let index = 0; index < informations.length; index++) {
      const information = informations[index];

      if (!information.hpmInformation) {
        continue;
      }
      const participation = information?.contractInformation?.participation;
      const isParticipationActive: boolean = information?.contractInformation
        ?.participation
        ? participation?.status ===
        PatientParticipationStatus.PatientParticipation_Active ||
        (participation?.status ===
          PatientParticipationStatus.PatientParticipation_Terminated &&
          DateUtils.isDayBetween(
            datetimeUtil.date(),
            parseDate(participation.startDate)!,
            parseDate(participation.endDate)!
          ))
        : false;
      haveAnyConflict =
        isParticipationActive !==
        (information?.hpmInformation?.isActive ?? false);
      if (haveAnyConflict) {
        break;
      }
    }
    setHaveConflict(!!haveAnyConflict);
  }, [informations]);
  return haveConflict;
};
