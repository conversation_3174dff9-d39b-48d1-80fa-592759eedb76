import { Classes } from '@tutum/design-system/components/Core';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  GetContractInformationFromMvzRequest,
  actionOnFavContract,
  actionOnFavContractGroup,
  getFavContractInformationFromMvz,
} from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import {
  ActionOnContractRequest,
  ActionOnFavContractGroupRequest,
  GetContractInformationFromMvzResponse,
  GroupContractAction,
  PatientEnrollmentInformationAction,
  PatientEnrollmentInformationStatus,
} from '@tutum/hermes/bff/service_domains_enrollment';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { date } from '@tutum/infrastructure/utils/datetime.util';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { useContext, useEffect, useState } from 'react';
import {
  HpmViewInformation,
  IContractInformation,
  IEnrollmentMenuActionItem,
  getActionItemInformation,
  mapFavStatusStyle,
} from '../participation/Participation.model';
import { ParticipationService } from '../participation/Participation.service';
import { GroupParticipationActionsState } from './FavButton.model';

function useHookGetFavPatientEnrollmentInformation(
  request: GetContractInformationFromMvzRequest
) {
  const [favInformationResponse, setFavInformationResponse] =
    useState<GetContractInformationFromMvzResponse | undefined>(undefined);

  useEffect(() => {
    if (!request) {
      return;
    }
    getFavContractInformationFromMvz({
      patientId: request.patientId,
    })
      .then((result) => {
        setFavInformationResponse(result.data);
      })
      .catch((err) => {
        throw err;
      });
  }, [request]);
  return favInformationResponse;
}
function useHookLoadButtonStyle(
  listContractInformations: IContractInformation[],
  isHzvActive: boolean,
  t: IFixedNamespaceTFunction,
  isStyleChange?: boolean
) {
  // Since the logic was changed back and forth frequently, will set status of hzv by hardcode without removing this parameter as describe on notion.
  isHzvActive = true;
  const [state, setState] = useState({
    numberContracts: 0,
    style: Classes.DISABLED,
    status: null,
    toolTipContent: null,
  });

  useEffect(() => {
    if (!listContractInformations) {
      return;
    }

    const contractInformations = [...listContractInformations];
    const defaultFavButtonStatus = isHzvActive
      ? PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotSupportedIkNumber
      : PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_CannotEnrollYet;
    const defaultFavButtonStyle = Classes.DISABLED;

    const sortedContractInformations =
      ParticipationService.sortFavInformations(contractInformations);
    const participation =
      sortedContractInformations.length > 0
        ? sortedContractInformations[0]
        : null;

    const favButtonStatus = getFavButtonStatus(
      participation,
      isHzvActive,
      defaultFavButtonStatus
    );
    const favButtonStyle = getFavButtonStyle(
      participation,
      favButtonStatus,
      defaultFavButtonStyle
    );
    const toolTipContent = getToolTipContent(favButtonStatus, participation, t);

    const numberContracts = contractInformations.filter(
      (c) =>
        c.status !==
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_InvalidIkNumber &&
        c.status !==
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotSupportedIkNumber
    ).length;

    setState({
      numberContracts,
      style: favButtonStyle,
      status: favButtonStatus,
      toolTipContent,
    });
  }, [listContractInformations, isHzvActive, t, isStyleChange]);

  return state;
}

function getFavButtonStatus(
  participation,
  isHzvActive,
  defaultFavButtonStatus
) {
  return participation &&
    (isHzvActive ||
      participation.status !==
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet)
    ? participation.status
    : defaultFavButtonStatus;
}

function getFavButtonStyle(
  participation,
  favButtonStatus,
  defaultFavButtonStyle
) {
  return participation
    ? mapFavStatusStyle.get(favButtonStatus)?.style ?? defaultFavButtonStyle
    : defaultFavButtonStyle;
}

export function getToolTipContent(favButtonStatus, participation, t) {
  let toolTipContent = t(`favButton_${favButtonStatus}`);

  const isNotEnrollYet =
    favButtonStatus ===
    PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet;

  const isAllowCreateActiveParticipation = [
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation,
    PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveFavGroupContract,
  ].some((action) => participation?.participationActions?.includes(action));

  if (isNotEnrollYet && !isAllowCreateActiveParticipation) {
    toolTipContent = null;
  }

  return toolTipContent?.split('\n');
}

const contractGroupActions: PatientEnrollmentInformationAction[] = [
  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelFavGroupContract,
  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeFavGroupContractStartDate,
  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateFavGroupContract,
  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveFavGroupContract,
  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateFavGroupContract,
  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy,
  PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveCustodianTreatment,
];

export class ActionOnFavContractResponse {
  done: boolean;
  action: PatientEnrollmentInformationAction;
}

function useActionOnFavContract(
  request: ActionOnContractRequest,
  doctorList: IEmployeeProfile[]
  // insuranceInfo: InsuranceInfo
) {
  const [result, setResult] = useState<ActionOnFavContractResponse | undefined>(undefined);
  const { reloadSelectedContractDoctor } = useContext(
    PatientManagementContext.instance
  );
  useEffect(() => {
    if (!request) {
      setResult(undefined);
      return;
    }
    if (
      request.action ===
      PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateFavGroupContract
    ) {
      setResult({
        done: true,
        action: request.action,
      });
      return;
    }
    setResult({
      done: false,
      action: request.action,
    });
    const isGroupAction = contractGroupActions.indexOf(request.action) >= 0;
    if (!isGroupAction) {
      actionOnFavContract(request).then((response) => {
        setResult({
          done: true,
          action: response.data.action,
        });
      });
    } else {
      const groupActionRequest: ActionOnFavContractGroupRequest = {
        ...request,
        doctorIds: [request.doctorId!],
        contractId: request.contractId!,
      };
      actionOnFavContractGroup(groupActionRequest).then(async (response) => {
        if (
          request.action ===
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveFavGroupContract
        ) {
          const currentSchein = patientFileStore?.schein?.activatedSchein;
          await reloadSelectedContractDoctor(
            request.patientId,
            date(),
            currentSchein!,
            doctorList
          );
        }
        setResult({
          done: true,
          action: response.data.action,
        });
      });
    }
  }, [request, JSON.stringify(doctorList)]);
  return result;
}

function useLoadFavGroupParticipationActionState(
  informations: Array<{
    contractInformation: IContractInformation;
    hpmInformation: HpmViewInformation;
  }>,
  contractGroupActions: GroupContractAction[],
  isHzvContractActiveOnHpm: boolean,
  t: IFixedNamespaceTFunction
): GroupParticipationActionsState[] {
  const [result, setResult] = useState<GroupParticipationActionsState[]>([]);
  // https://www.notion.so/silenteer/Not-enrolled-04a76326c1af4d63a85925dd460d9719
  // Since the logic was changed back and forth frequently, will set status of hzv by hardcode without removing this parameter as describe on notion.
  isHzvContractActiveOnHpm = true;
  useEffect(() => {
    if (!informations || !t || !contractGroupActions) {
      setResult([]);
      return;
    }

    const isFavActiveOnHpm =
      informations.filter((i) => i.hpmInformation?.isActive)[0]?.hpmInformation
        ?.isActive ?? false;

    const groupActions: GroupParticipationActionsState[] = [];
    contractGroupActions.forEach((groupAction) => {
      if (
        groupAction.action ===
        PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveFavGroupContract &&
        !isFavActiveOnHpm &&
        !isHzvContractActiveOnHpm
      ) {
        return;
      }
      const actionInfo = getActionItemInformation(
        groupAction.action,
        undefined
      ) as GroupParticipationActionsState;
      groupActions.push({
        ...actionInfo!,
        text: t(actionInfo.text!),
        contractId: groupAction.contractId!,
      });
    });
    setResult(groupActions);
  }, [informations, contractGroupActions, isHzvContractActiveOnHpm, t]);

  return result;
}

function intentIconMapping(buttonStyle: string) {
  //  string[]: text - HPMConflictIcon - chevronDown
  const mapper: Record<string, string[]> = {
    'not-enrolled-yet-button': [
      COLOR.TEXT_PRIMARY_BLACK,
      COLOR.TEXT_SECONDARY_NAVAL,
      COLOR.TEXT_SECONDARY_NAVAL,
    ],
    'bp5-disabled': [
      COLOR.TEXT_SECONDARY_NAVAL2,
      COLOR.TEXT_SECONDARY_NAVAL2,
      COLOR.TEXT_SECONDARY_NAVAL2,
    ],
    'gray-button': [
      COLOR.TEXT_PRIMARY_BLACK,
      COLOR.TEXT_SECONDARY_NAVAL,
      COLOR.TEXT_SECONDARY_NAVAL,
    ],
    [Classes.INTENT_SUCCESS]: [
      COLOR.TEXT_WHITE,
      COLOR.TEXT_WHITE,
      COLOR.TEXT_WHITE,
    ],
    [Classes.INTENT_WARNING]: [
      COLOR.TEXT_WARNING,
      COLOR.TEXT_WARNING,
      COLOR.TEXT_WARNING,
    ],
    [Classes.INTENT_DANGER]: [
      COLOR.TEXT_WHITE,
      COLOR.TEXT_WHITE,
      COLOR.TEXT_WHITE,
    ],
    [`${Classes.INTENT_SUCCESS} secondary`]: [
      COLOR.TEXT_POSITIVE,
      COLOR.TEXT_POSITIVE,
      COLOR.TEXT_POSITIVE,
    ],
    'fav-faulty': [
      COLOR.TEXT_PRIMARY_BLACK,
      COLOR.TEXT_SECONDARY_NAVAL,
      COLOR.TEXT_SECONDARY_NAVAL,
    ],
  };
  return mapper[buttonStyle];
}

export const FavButtonHook = {
  useHookGetFavPatientEnrollmentInformation,
  useHookLoadButtonStyle,
  useActionOnFavContract,
  useLoadFavGroupParticipationActionState,
  intentIconMapping,
};
