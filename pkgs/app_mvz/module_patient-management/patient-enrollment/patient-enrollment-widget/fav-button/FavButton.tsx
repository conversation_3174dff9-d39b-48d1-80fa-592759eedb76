import {
  Intent,
  Menu,
  MenuDivider,
  MenuItem,
  Popover,
  PopoverInteractionKind,
  PopoverPosition,
  Position,
} from '@tutum/design-system/components/Core';
import {
  alertError,
  alertSuccessfully,
  alertWarning,
} from '@tutum/design-system/components/Toaster';
import {
  actionOnFavContract,
  useQueryGetContractInformationFromMvz,
  useQueryGetContractsInformationFromHpmService,
} from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { ContractType, MainGroup } from '@tutum/hermes/bff/legacy/common';

import {
  BodyTextS,
  Flex,
  HpmErrorMessage,
  Svg,
  Tag,
  Tooltip,
} from '@tutum/design-system/components';
import Button from '@tutum/design-system/components/Button/Button';
import {
  getCssClass,
  isNotEmpty,
} from '@tutum/design-system/infrastructure/utils';
import { useListenPatientParticipationChange } from '@tutum/hermes/bff/app_mvz_patient_participation';
import { FormName } from '@tutum/hermes/bff/form_common';
import { actionOnFavContractGroup } from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { getPatientParticipation } from '@tutum/hermes/bff/legacy/app_mvz_patient_participation';
import { takeOverScheinDiagnosis } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  useQueryGetTakeOverDiagnosis,
  useQueryGetTimelineByEnrollmentId,
} from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { DiagnoseType } from '@tutum/hermes/bff/legacy/repo_encounter';
import { ScheinItem } from '@tutum/hermes/bff/legacy/schein_common';
import {
  ActionOnFavContractGroupRequest,
  PatientEnrollmentInformationAction,
  PatientEnrollmentInformationStatus,
} from '@tutum/hermes/bff/legacy/service_domains_enrollment';
import I18n from '@tutum/infrastructure/i18n';
import { default as datetimeUtil } from '@tutum/infrastructure/utils/datetime.util';
import AccountManagementUtil from '@tutum/infrastructure/utils/form.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import { ComposerDiagnosisType } from '@tutum/mvz/components/select-diagnosis-dialog/helpers';
import SVSelectDiagnosisDialog from '@tutum/mvz/components/select-diagnosis-dialog/sv-select-diagnosis-dialog/SVSelectDiagnosisDialog.styled';
import useConfirm from '@tutum/mvz/hooks/useConfirm';
import useDatePicker from '@tutum/mvz/hooks/useDatePicker';
import { useTreatmentDoctorWithBsnr } from '@tutum/mvz/hooks/useTreatmentDoctorWithBsnr';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import PatientEnrollmentForm from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-form/PatientEnrollmentForm.styled';
import { MissingInsuranceNumber_Error } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/PatientEnrollmentWidget.constant';
import { enrollmentActions } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/PatientEnrollmentWidget.store';
import {
  favActionMap,
  favActionOrder,
  hpmActionsList,
} from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/fav-button/FavButton.mapper';
import { GroupEnrollmentState } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/fav-button/FavButton.model';
import FavPopoverContent from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/fav-button/FavPopoverContent.styled';
import HpmCheckHistoryDialog from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/hpm-check-history/HpmCheckHistoryDialog.styled';
import { ParticipationToolTipContent } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/participation/Participation';
import { ParticipationHook } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/participation/Participation.hook';
import { ParticipationService } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/participation/Participation.service';
import {
  PatientPostActionType,
  usePatientPostAction,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.hook';
import {
  patientFileActions,
  patientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { timelineActions } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import reloadSchein from '@tutum/mvz/module_patient-management/utils/reloadSchein';
import { ContractService } from '@tutum/mvz/services/contract.service';
import { debounce } from 'lodash';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

const ArrowDownGrayIcon = '/images/chevron-down.svg';
const ArrowDownIcon = '/images/chevron-down-white.svg';

export interface FavButtonProps {
  className?: string;
  patient: IPatientProfile;
  [key: string]: any;
}
const mergeClasses = (...classes: string[]) =>
  classes.filter(Boolean).join(' ');

enum ButtonContent {
  ENROLLMENT = 'ENROLLMENT',
  DATE_PICKER = 'DATE_PICKER',
}
const FavButton = ({
  className,
  disabled,
  patient,
  availableFavContracts,
  isHpmConnectionOk,
  openCreateSchein,
}: FavButtonProps) => {
  const patientId = patient?.id;
  const { t } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'FavButton',
  });
  const { t: translator } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'FavContractItem',
  });
  const { t: tEnrollment } = I18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'EnrollmentMenu',
  });
  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonLocales>({
    namespace: 'Common',
  });
  const {
    data: contractInformation,
    isLoading,
    isFetching,
    refetch: getContractInformationFromMvz,
  } = useQueryGetContractInformationFromMvz(
    {
      patientId: patientId,
      contractType: ContractType.ContractType_SpecialistCare,
    },
    {
      select: (data) => {
        data.data.contractInformations = data.data.contractInformations.map(
          (contractResponse) => {
            return {
              ...contractResponse,
              contractAbbreviation: ContractService.getContractAbbreviations(
                contractResponse.contractId,
                contractResponse.participation?.contractType ??
                contractResponse.enrollment?.contractType ??
                null
              ),
              hpmErrorMessages: null,
            };
          }
        );
        return data.data;
      },
    }
  );
  const [buttonContent, setButtonContent] = useState<ButtonContent | undefined>(undefined);
  const [onlineCheckConfig, setOnlineCheckConfig] = useState<{
    forceUpdate: boolean;
    saveHistory: boolean;
  }>({
    forceUpdate: false,
    saveHistory: false,
  });
  const shouldPushAlertRef = useRef<boolean>(false);
  const [enrollmentFormOpen, setEnrollmentFormOpen] = useState(false);
  const [takeOverDiagnosisDialogOpen, setTakeOverDiagnosisDialogOpen] =
    useState(false);
  const [hpmCheckHistoryDialogOpen, setHpmCheckHistoryDialogOpen] =
    useState(false);
  const [hpmInformation, setHpmInformation] = useState<any>(null);
  const [
    isContractActiveWithoutAnyParticipation,
    setIsContractActiveWithoutAnyParticipation,
  ] = useState(false);

  const hpmCheckHistoryDialogHandleToggle = () =>
    setHpmCheckHistoryDialogOpen(!hpmCheckHistoryDialogOpen);

  const showHPMErrorToast = debounce((hpmErrorMessages: HpmErrorMessage[]) => {
    hpmErrorMessages = hpmErrorMessages.filter(
      (err) => err.code !== MissingInsuranceNumber_Error
    );
    if (hpmErrorMessages.length === 0) return;
    const defaultErrorContent = t('checkHpmParticipationFail');
    alertError(
      hpmErrorMessages.filter((m) => isNotEmpty(m.message))?.length > 0 ? (
        <>{hpmErrorMessages.map((log) => log.message)}</>
      ) : (
        defaultErrorContent
      )
    );
  }, 2000);

  const excludedContractIds = contractInformation?.contractInformations
    ?.filter((contract) => {
      contract.status !==
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Enrolled;
    })
    .map((contract) => contract.contractId);
  // Currently we support single contract only, so we can directly get first contract
  // TODO: need UI to select contract
  const contract = contractInformation?.contractInformations?.[0];

  const enrollment = contract?.enrollment;
  const contractId = contract?.contractId;
  const actions = contract?.actions;
  const status = contract?.status;
  const buttonStyle = mergeClasses(
    'button',
    status!,
    isContractActiveWithoutAnyParticipation ? 'ActiveByHzv' : ''
  );
  const ikNumber = patientFileStore?.activeInsurance?.ikNumber;

  const terminateLogInformation = ParticipationHook.useGetTerminateLog(
    contract as any,
    'menuFooterFavTerminated',
    translator
  );

  const tooltipContentObj = ParticipationHook.useGenerateContractToolTip(
    contract as any,
    ikNumber,
    false,
    'favContract',
    translator,
    disabled,
    isContractActiveWithoutAnyParticipation
  );
  const toolTipContent = tooltipContentObj?.contentLines;
  const { patientManagement, setGetPatientParticipationResponse } = useContext(
    PatientManagementContext.instance
  );

  const availableFavDoctorIds = patientManagement?.availableFavContracts?.find(
    (contract) => contract.contractId === contractId
  )?.doctorIds;
  const {
    data: contractInformationFromHpm,
    refetch: checkOnlineParticipation,
  } = useQueryGetContractsInformationFromHpmService(
    {
      patientId: patientId,
      contractIds: [contractId!],
      forceUpdate: onlineCheckConfig.forceUpdate,
      doctorIds: availableFavDoctorIds,
      saveHistory: onlineCheckConfig.saveHistory,
    },
    {
      enabled: !!contractId && contractId !== 'MEDI_FA_PT_BW',
    }
  );

  const currentFavSchein = useMemo(() => {
    return patientFileStore.schein.list
      ?.reverse()
      .find((schein) => schein.scheinMainGroup === MainGroup.FAV);
  }, [patientFileStore.schein.list]);

  const {
    isSuccess,
    data: takeOverDiagnosisData,
    refetch,
  } = useQueryGetTakeOverDiagnosis(
    {
      patientId: patient.id,
      scheinId: currentFavSchein?.scheinId,
    },
    {
      enabled: Boolean(patient.id && currentFavSchein?.scheinId),
    }
  );

  const hasPermanentDiagnosis = useMemo(() => {
    if (!isSuccess) return false;

    const res = (takeOverDiagnosisData.takeOverDiagnosisGroup || []).find(
      (item) => item.diagnoseType === DiagnoseType.DIAGNOSETYPE_PERMANENT
    );

    return res && res.timelineModels.length > 0;
  }, [isSuccess, takeOverDiagnosisData]);

  const handleTakeOverDiagnosisDialogOpen = async (isCreateActive: boolean) => {
    const supportABRD609 = await webWorkerServices.doesContractSupportFunctions(
      ['ABRD609'],
      contractId!
    );
    refetch();
    setTakeOverDiagnosisDialogOpen(
      isCreateActive && !!hasPermanentDiagnosis && !!supportABRD609
    );
  };
  const reloadAndActivateSchein = async (patientManagement, create) => {
    const { scheins, patientParticipation } = await reloadSchein(
      patientManagement,
      create
    );
    setGetPatientParticipationResponse(patientParticipation);
    const activateSchein = scheins.filter((schein: ScheinItem) => {
      if (create) return schein.scheinMainGroup === MainGroup.FAV;
      else return schein.scheinMainGroup !== MainGroup.FAV;
    })[0];
    patientFileActions.schein.setActivatedSchein(
      activateSchein,
      patientParticipation.participations
    );

    handleTakeOverDiagnosisDialogOpen(create);
  };

  const handleTakeOver = (
    existedDiagnosis: ComposerDiagnosisType[],
    mappingTreatmentRelevent
  ) => {
    const scheinId = patientFileStore.schein.list.find(
      (schein: ScheinItem) => schein.scheinMainGroup === MainGroup.FAV
    )?.scheinId;
    setTakeOverDiagnosisDialogOpen(false);
    if (!scheinId) return;

    const takeOverDiagnoseInfos = existedDiagnosis.map((item) => ({
      id: item.id!,
      isTreatmentRelevant: mappingTreatmentRelevent[item.id!],
    }));
    takeOverScheinDiagnosis({
      scheinId: scheinId,
      takeOverDiagnoseInfos: takeOverDiagnoseInfos,
    });
  };

  const { ConfirmationDialog, askConfirmation } = useConfirm();
  const maxDate = datetimeUtil.date();
  const minDate = datetimeUtil.getPreviousQuarters(maxDate, 6).toDate();
  const dateShortcuts = ParticipationService.generateQuarterShortcut(
    minDate,
    maxDate,
    t
  );
  const { DatePickerPopover, askDate } = useDatePicker({
    title: t('changeGroupContractStartDateTitle'),
    cancelLabel: t('cancelButton'),
    submitLabel: t('changeContractStartDateSubmit'),
    defaultValue: null,
    minDate: minDate,
    maxDate: maxDate,
    intentSubmit: Intent.PRIMARY,
    dateShortcut: dateShortcuts,
  });

  const { data: enrollmentForm } = useQueryGetTimelineByEnrollmentId(
    {
      enrollmentId: enrollment?.id!,
      patientId: patientId,
    },
    {
      enabled: Boolean(enrollment?.id && patientId),
    }
  );

  const treatmentDoctor = useTreatmentDoctorWithBsnr(
    enrollmentForm?.timelineModel?.encounterForm?.prescribe?.treatmentDoctorId,
    enrollmentForm?.timelineModel?.assignedToBsnrId
  );

  const mapHpmInfo = (hpmInformation) => {
    if (!hpmInformation) {
      return;
    }

    let hpmToolTipContent = '';
    const classes = ['hpm-text-status'];
    if (hpmInformation?.hasError) {
      classes.push('hpm-text-status-error');
      hpmToolTipContent = tEnrollment('hpmConnectionFail');
    } else if (hpmInformation?.status === 'Active') {
      classes.push('hpm-text-status-active');
      hpmToolTipContent = tEnrollment('hpmActive');
      if (!status)
        hpmToolTipContent = tEnrollment('hpmActiveWithMvzNotEnrollment');
    } else if (hpmInformation?.status === 'InActive') {
      hpmToolTipContent = tEnrollment('hpmInactiveToolTip');
      classes.push('hpm-text-status-inActive');
    }
    return {
      ...hpmInformation,
      tooltip: hpmToolTipContent,
      className: getCssClass(...classes),
    };
  };
  useEffect(() => {
    if (
      [
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet,
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Created,
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Printed,
      ].includes(status!)
    ) {
      enrollmentActions.setFavEnrollmentState({
        enrollmentActions: actions,
        currentEnrollment: enrollment,
      } as GroupEnrollmentState);
      return;
    }
    enrollmentActions.setFavEnrollmentState(undefined!);
  }, [actions, enrollment, status]);

  const checkOnlineFavContract = useCallback(
    async (isPushAlert: boolean) => {
      shouldPushAlertRef.current = isPushAlert;
      setOnlineCheckConfig({
        forceUpdate: true,
        saveHistory: true,
      });
      await checkOnlineParticipation();
      if (!isContractActiveWithoutAnyParticipation) {
        await getContractInformationFromMvz();
        await reloadAndActivateSchein(patientManagement, true);
      }
      setOnlineCheckConfig({
        forceUpdate: false,
        saveHistory: false,
      });
    },
    [
      checkOnlineParticipation,
      isContractActiveWithoutAnyParticipation,
      getContractInformationFromMvz,
      reloadAndActivateSchein,
      patientManagement,
    ]
  );

  const performContractAction = async (
    request: ActionOnFavContractGroupRequest,
    event?: any
  ) => {
    setButtonContent(undefined);
    switch (request.action) {
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment:
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ContinueEnrollment:
        setEnrollmentFormOpen(true);
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_PerformHpmCheckOnlineParticipation:
        await checkOnlineFavContract(true);
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ShowHpmCheckHistoryDialog:
        hpmCheckHistoryDialogHandleToggle();
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveFavGroupContract:
        await actionOnFavContractGroup(request);
        await getContractInformationFromMvz();
        await reloadAndActivateSchein(patientManagement, true);
        break;
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelFavGroupContract: {
        const isYesConfirmed = await askConfirmation({
          title: t('cancelContractConfirmTitle'),
          content: t('cancelContractConfirmContent'),
          intent: Intent.DANGER,
          cancelButton: t('cancelButton'),
          confirmButton: t('cancelContractConfirmButton'),
        });
        if (!isYesConfirmed) break;
        await actionOnFavContractGroup(request);
        await getContractInformationFromMvz();
        await reloadAndActivateSchein(patientManagement, false);
        break;
      }
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate:
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeFavGroupContractStartDate: {
        event?.stopPropagation();
        setButtonContent(ButtonContent.DATE_PICKER);
        const selectedDate = await askDate({});
        if (!selectedDate) break;
        await actionOnFavContractGroup({
          ...request,
          startDate: selectedDate.startDate,
          endDate: selectedDate.endDate,
        });
        await getContractInformationFromMvz();
        setButtonContent(undefined);
        const { data } = await getPatientParticipation({
          patientId: patient.id,
          checkDate: datetimeUtil.now(),
        });
        setGetPatientParticipationResponse(data);
        break;
      }
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelEnrollment: {
        const isYesConfirmed = await askConfirmation({
          title: t('cancelEnrollmentConfirmTitle'),
          content: t('cancelEnrollmentConfirmContent'),
          intent: Intent.DANGER,
          cancelButton: t('cancelEnrollmentCancelButton'),
          confirmButton: t('cancelEnrollmentConfirmButton'),
        });
        if (!isYesConfirmed) break;
        await actionOnFavContract(request);
        await getContractInformationFromMvz();
        await reloadAndActivateSchein(patientManagement, false);
        break;
      }
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ParticipationForm: {
        const formEntry = enrollmentForm?.timelineModel;
        musterFormDialogActions.setCurrentEntryForm(formEntry?.id!);
        musterFormDialogActions.viewForm(
          AccountManagementUtil.convertFormData(
            formEntry?.encounterForm!,
            treatmentDoctor
          ),
          formEntry?.encounterForm?.encounterId!,
          patientFileActions.getAvailableContractById(contractId)!,
          formEntry?.encounterForm?.id!,
          !!formEntry?.encounterForm?.prescribe.printedDate,
          false,
          false,
          formEntry?.scheinIds?.[0]
        );
        break;
      }
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation:
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateFavGroupContract: {
        const supportFORM1687 =
          await webWorkerServices.doesContractSupportFunctions(
            ['FORM1687'],
            contractId!
          );

        if (supportFORM1687) {
          musterFormDialogActions.setCurrentFormName(
            FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5
          );
          musterFormDialogActions.setPrescribeContractId(contractId!);
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_abmel: +datetimeUtil.endOfSelectedDay(),
            disable_date_prescribe: true,
            disable_date_label_custom_abmel: true,
          });
        }
        break;
      }
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation:
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateFavGroupContract: {
        const isYesConfirmed = await askConfirmation({
          title: t('undoTerminatedGroupContractTitle'),
          content: t('undoTerminatedGroupContractContent'),
          intent: Intent.DANGER,
          cancelButton: t('cancelButton'),
          confirmButton: t('undoTerminatedContractConfirmButton'),
        });
        if (!isYesConfirmed) break;
        await actionOnFavContractGroup(request);
        await getContractInformationFromMvz();
        timelineActions.reloadTimeline();
        break;
      }
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveCustodianTreatment:
      case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TreatAsDeputy:
        await actionOnFavContractGroup(request);
        await getContractInformationFromMvz();
        break;
      default:
        break;
    }
  };

  const activateContract = (contractResponseFromHpm) => {
    const contractIds = contractResponseFromHpm?.hpmResponses
      ?.filter((resp) => resp.status === 'Active')
      ?.map((resp) => resp.contractId);
    if (!contractIds || contractIds.length <= 0) return;

    // Now we only support one contract
    // TODO: need UI to select contract
    const contractId = contractIds?.[0];
    if (
      ![
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Deputy,
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Enrolled,
        PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Terminated,
      ].includes(status!)
    ) {
      performContractAction({
        action:
          PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveFavGroupContract,
        patientId: patient.id,
        startDate: datetimeUtil.now(),
        endDate: datetimeUtil.now(),
        contractId: contractId,
        doctorIds: [],
      });
    } else {
      reloadAndActivateSchein(patientManagement, true);
    }
  };

  const handleHpmResponseStatus = (responses) => {
    if (!shouldPushAlertRef.current) return;
    if (responses?.every((response) => response.status === 'Active')) {
      const isDeputy = responses?.some((r) => r.isDeputy);
      const message = t(
        `checkHpmParticipationSuccessfully${isDeputy ? 'Deputy' : ''}`
      );
      alertSuccessfully(message, { key: message });
    } else {
      alertWarning(t('checkHpmParticipationNoActiveParticipation'));
    }
    shouldPushAlertRef.current = false;
  };

  const getLastCheck = (listHpmInformation, contractId) => {
    return listHpmInformation
      .sort((a, b) => a.checkedDate - b.checkedDate)
      .filter((hpmInfo) => hpmInfo?.checkedDate <= datetimeUtil.now())
      .find(
        (hpmInfo) => hpmInfo.contractId === contractId && hpmInfo.checkedDate
      );
  };

  useEffect(() => {
    if (!contractInformationFromHpm) return;

    const { hpmErrorMessages, hpmResponses } = contractInformationFromHpm;

    if (hpmErrorMessages?.length > 0) {
      const filteredErrors = hpmErrorMessages.filter((error) =>
        isNotEmpty(error?.code)
      );
      showHPMErrorToast(filteredErrors);
      setHpmInformation(mapHpmInfo({ ...hpmInformation, hasError: true }));
    } else {
      handleHpmResponseStatus(hpmResponses);
    }
  }, [contractInformationFromHpm]);

  useEffect(() => {
    if (!contractInformationFromHpm) return;
    if (!availableFavContracts) return;
    const isNotEnroll =
      status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet;
    const isHpmActive = contractInformationFromHpm?.hpmResponses
      ?.filter((resp) => resp.status === 'Active')
      ?.some((resp) => resp.contractId);
    const isActiveByHzv =
      availableFavContracts?.filter((c) => c.isImplicitParticipation)?.length >
      0;
    if (isHpmActive && isNotEnroll && isActiveByHzv) {
      setIsContractActiveWithoutAnyParticipation(true);
    } else {
      activateContract(contractInformationFromHpm);
    }
  }, [contractInformationFromHpm, availableFavContracts]);

  // Contract != MEDI_FA_PT_BW
  // First time in quarter, HPM online check for the contract
  useEffect(() => {
    if (!contractId || contractId === 'MEDI_FA_PT_BW') return;
    if (patient && !patient.listHpmInformation) {
      checkOnlineFavContract(true);
      return;
    }

    const lastCheck = getLastCheck(patient.listHpmInformation, contractId);
    setHpmInformation(
      mapHpmInfo({ ...lastCheck, hasError: !isHpmConnectionOk })
    );
    const checkDateQuarter = datetimeUtil.getQuarter(
      datetimeUtil.unixToMoment(lastCheck?.checkedDate)
    );
    const currentQuarter = datetimeUtil.getQuarter(datetimeUtil.now());
    const isSameQuarter = checkDateQuarter === currentQuarter;
    if (!isSameQuarter || !lastCheck) checkOnlineParticipation();
  }, [patient?.listHpmInformation, contractId]);

  // Contract == MEDI_FA_PT_BW
  // if status is enrolled and there isn't any schein in the current quarter
  // Created new schein automatically
  useEffect(() => {
    const isEnrolled =
      status ===
      PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_Enrolled;

    if (!contractId || contractId !== 'MEDI_FA_PT_BW') return;
    if (!isEnrolled) return;

    reloadAndActivateSchein(patientManagement, true);
  }, [status, contractId]);

  useEffect(() => {
    patientFileActions.patient.setCurrentContract(contract!);
  }, [contract]);

  const renderItem = (action) => {
    const favMappedAction = favActionMap[action];
    const favAction = favMappedAction?.action || action;
    const i18nKey = favMappedAction?.i18nKey;
    const color = favMappedAction?.color;
    const style = favMappedAction?.style;
    const icon = favMappedAction?.icon;
    const isPrintedForm =
      action ===
      PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ParticipationForm;
    const terminateLog = [
      PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateFavGroupContract,
      PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation,
    ].includes(action)
      ? terminateLogInformation
      : null;

    return (
      <>
        <MenuItem
          key={action}
          style={style}
          text={
            <span style={{ color }}>
              {t(i18nKey)}
              {isPrintedForm && (
                <Tag round intent={Intent.NONE}>
                  {tCommon('Printed')}
                </Tag>
              )}
              {terminateLog && (
                <BodyTextS className="menu-footer-item-message">
                  {terminateLog}
                </BodyTextS>
              )}
            </span>
          }
          icon={<Svg src={`/images/${icon}`} />}
          onClick={(event) => {
            performContractAction(
              {
                action: favAction,
                patientId,
                contractId: contractId!,
              },
              event
            );
          }}
        />
      </>
    );
  };

  useListenPatientParticipationChange((response) => {
    if (!response || patientId !== response.patientId) return;
    getContractInformationFromMvz();
  });

  const startEnrollment = () => {
    performContractAction({
      action:
        PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment,
      contractId: contractId!,
      patientId,
    });
    return true;
  };

  const continueEnrollment = () => {
    performContractAction({
      action:
        PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ContinueEnrollment,
      contractId: contractId!,
      patientId,
    });
    return true;
  };

  usePatientPostAction(
    {
      contractId: contractId,
      action: PatientPostActionType.START_ENROLLMENT,
      handler: startEnrollment,
    },
    []
  );

  usePatientPostAction(
    {
      contractId: contractId,
      action: PatientPostActionType.CONTINUE_ENROLLMENT,
      handler: continueEnrollment,
    },
    []
  );

  const renderMenu = (actions: PatientEnrollmentInformationAction[]) => {
    const sortedActions = actions?.sort((a, b) => {
      return favActionOrder.indexOf(a) - favActionOrder.indexOf(b);
    });

    const nonHpmActions = sortedActions?.filter(
      (action) => !hpmActionsList.includes(action)
    );

    const hpmActions = sortedActions?.filter((action) =>
      hpmActionsList.includes(action)
    );

    return (
      <Menu>
        {!isContractActiveWithoutAnyParticipation && (
          <>{nonHpmActions?.map((action) => renderItem(action))}</>
        )}
        {hpmActions?.length > 0 && (
          <>
            <MenuDivider
              title={
                <Flex>
                  <Flex>
                    <BodyTextS className={hpmInformation?.className}>
                      {'HPM'}
                    </BodyTextS>
                  </Flex>
                  <Flex>
                    <Tooltip
                      className="hpm-tool-tip-content"
                      content={hpmInformation?.tooltip}
                      position={'top-right'}
                      autoFocus={false}
                      openOnTargetFocus={false}
                      portalClassName={'FavButton'}
                    >
                      <Svg
                        className={'svg-information-icon'}
                        src={'/images/hpm-information-icon.svg'}
                      />
                    </Tooltip>
                  </Flex>
                </Flex>
              }
              className={'menu-item-hpm-status'}
            />
            {hpmActions?.map((action) => renderItem(action))}
          </>
        )}
      </Menu>
    );
  };

  return (
    <div className={className}>
      <Flex>
        <Popover
          position={PopoverPosition.BOTTOM_LEFT}
          hasBackdrop={true}
          popoverClassName="fav-popover"
          isOpen={buttonContent !== undefined}
          backdropProps={{
            onClick: () => setButtonContent(undefined),
          }}
          content={
            <FavPopoverContent>
              {buttonContent === ButtonContent.DATE_PICKER && (
                <DatePickerPopover />
              )}
              {buttonContent === ButtonContent.ENROLLMENT &&
                renderMenu(actions!)}
            </FavPopoverContent>
          }
        >
          <Tooltip
            content={
              <ParticipationToolTipContent
                className="fav-tooltip-content"
                contentLines={toolTipContent ?? []}
                isLoading={false}
              />
            }
            position={Position.TOP}
            interactionKind={PopoverInteractionKind.HOVER}
            disabled={!toolTipContent || toolTipContent?.length === 0}
          >
            <Button
              className={buttonStyle}
              small
              loading={isLoading || isFetching}
              disabled={disabled}
              onClick={() => setButtonContent(ButtonContent.ENROLLMENT)}
            >
              {status ===
                PatientEnrollmentInformationStatus.PatientEnrollmentInformationStatus_NotEnrollYet ? (
                <BodyTextS fontWeight={600} color="inherit">
                  {t('contractTypeFaV')}
                </BodyTextS>
              ) : (
                t('contractTypeFaV')
              )}
              <Svg
                src={
                  !['Enrolled', 'ActiveByHzv'].some((value) =>
                    buttonStyle.includes(value)
                  )
                    ? ArrowDownGrayIcon
                    : ArrowDownIcon
                }
                size={16}
              />
            </Button>
          </Tooltip>
        </Popover>
      </Flex>
      <HpmCheckHistoryDialog
        patientId={patientId}
        contractIds={[contractId!]}
        open={hpmCheckHistoryDialogOpen}
        onClose={hpmCheckHistoryDialogHandleToggle}
        contractType={ContractType.ContractType_SpecialistCare}
      />
      {enrollmentFormOpen && (
        <PatientEnrollmentForm
          listContractDoctors={availableFavContracts}
          isOpen={enrollmentFormOpen}
          isChangingDoctor={false}
          excludedContractIds={excludedContractIds}
          enrollment={enrollment}
          onClose={() => setEnrollmentFormOpen(false)}
          onCreateSchein={() => {
            openCreateSchein();
            setEnrollmentFormOpen(false);
          }}
          submitEnrollmentCallback={getContractInformationFromMvz}
          onReloadLoadListContracts={() => {
            reloadAndActivateSchein(patientManagement, true);
          }}
        />
      )}
      <ConfirmationDialog />
      {!!takeOverDiagnosisDialogOpen && (
        <SVSelectDiagnosisDialog
          patientId={patient.id}
          open={takeOverDiagnosisDialogOpen}
          currentSchein={currentFavSchein}
          onCancel={() => setTakeOverDiagnosisDialogOpen(false)}
          onTakeover={handleTakeOver}
        />
      )}
    </div>
  );
};
export default FavButton;
