import {
  IContractInformation,
  ParticipationAction,
  HpmViewInformation,
  ParticipationButtonAction,
} from '../../participation/Participation.model';
import {
  GetContractsInformationFromHpmResponse,
  PatientEnrollmentInformationAction,
  ActionOnContractRequest,
} from '@tutum/hermes/bff/service_domains_enrollment';
import { Intent } from '@tutum/design-system/components/Core';
import {
  parseDate,
  getUTCMilliseconds,
  utcToLocal,
} from '@tutum/design-system/infrastructure/utils';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { PatientProfileResponse } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { IDatePickerFormProps } from '../../date-picker-form/DatePickerForm';
import { ParticipationService } from '../../participation/Participation.service';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

interface ToastInfo {
  message: string;
  intent: Intent;
}

export type FavButtonAction =
  | {
    type: 'OnContractInformationChanged';
    payload: IContractInformation;
  }
  | {
    type: 'OnActionClicked';
    payload: {
      action: ParticipationAction;
    };
  }
  | {
    type: 'OnHpmInformationChanged';
    payload: HpmViewInformation;
  }
  | {
    type: 'OnPatientProfileChanged';
    payload: PatientProfileResponse | undefined;
  }
  | {
    type: 'OnUseCheckContractFromHpmResponsed';
    payload: GetContractsInformationFromHpmResponse;
  }
  | {
    type: 'OnCancelOrCloseAction';
    payload: ParticipationAction | undefined;
  }
  | {
    type: 'OnRequestedActionContract';
  }
  | {
    type: 'OnDatePickerSubmit';
    payload: {
      currentAction: ParticipationAction;
      date: Date;
      participationId: string;
      enrollmentId: string;
      applyContractGroup: boolean;
    };
  };
// | {
//     type: 'OnConfirmedAction';
//     payload: ParticipationAction;
//   };

export interface FavContractItemState {
  stage:
  | {
    value: 'IDLE';
  }
  | {
    value: 'InlineDatePicker';
    currentProcess: ParticipationAction;
    datePickerProps: IDatePickerFormProps | undefined;
    isSubmitting: boolean;
  }
  | {
    value: 'RequestActionContract';
    request: ActionOnContractRequest;
  };
  contractInformation: IContractInformation | undefined;
  patient: PatientProfileResponse | undefined;
  toastInfo: ToastInfo | undefined;
  // actionMenu: IParticipationActionProps;

  hpmInformation: {
    hpmViewInformation: HpmViewInformation;
  };
  t: IFixedNamespaceTFunction;
  //request
}

export const initFavContractItemState = (
  t: IFixedNamespaceTFunction
): FavContractItemState => {
  return {
    contractInformation: undefined,
    patient: undefined,
    stage: {
      value: 'IDLE',
    },
    toastInfo: undefined,
    hpmInformation: {
      hpmViewInformation: {
        status: undefined,
        checkedTime: undefined,
        hasError: false,
        isActive: false,
        isLoading: false,
      },
    },
    t,
  };
};

export const FavContractItemReducer = (
  state: FavContractItemState,
  action: FavButtonAction
): FavContractItemState => {
  const t = state.t;
  switch (action.type) {
    case 'OnContractInformationChanged': {
      const contractInformation = action.payload;
      let toastInfo: ToastInfo | undefined = undefined;
      if (
        contractInformation.hasHpmError &&
        contractInformation.hpmErrorMessages
      ) {
        toastInfo = {
          message: contractInformation.hpmErrorMessages
            .map((x) => x.message)
            .join(', '),
          intent: Intent.DANGER,
        };
      }

      state.toastInfo = toastInfo;
      state.contractInformation = { ...contractInformation };
      break;
    }
    case 'OnPatientProfileChanged': {
      state.patient = { ...action.payload! };
      break;
    }
    case 'OnHpmInformationChanged': {
      const hpmInformation = action.payload;
      state.hpmInformation = {
        hpmViewInformation: { ...hpmInformation },
      };
      break;
    }
    case 'OnActionClicked': {
      const menuAction = action.payload.action;
      let currentStage = state.stage;
      switch (menuAction) {
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation: {
          currentStage = {
            value: 'InlineDatePicker',
            currentProcess: menuAction,
            datePickerProps: undefined,
            isSubmitting: false,
          };
          const endDate =
            state.contractInformation?.previousParticipation?.endDate;
          const minDate = endDate
            ? DatetimeUtil.dateToMoment(endDate).add(1, 'd').toDate()
            : DatetimeUtil.getPreviousQuarters(DatetimeUtil.dateToMoment(), 4)
              .startOf('quarter')
              .toDate();

          const maxDate = DatetimeUtil.date();
          const dateShortcuts = ParticipationService.generateQuarterShortcut(
            minDate,
            maxDate,
            t
          );
          if (
            state.contractInformation?.enrollmentId ||
            state.contractInformation?.participationIds
          ) {
            currentStage.datePickerProps =
              ParticipationService.generateDatePickerProps(
                t('createActiveCustodianParticipationLabelDialog'),
                t('cancelButton'),
                t('activateButton'),
                null,
                minDate,
                DatetimeUtil.date(),
                undefined,
                dateShortcuts
              );
          } else {
            currentStage = {
              value: 'IDLE',
            };
          }
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate: {
          currentStage = {
            value: 'InlineDatePicker',
            currentProcess: menuAction,
            datePickerProps: undefined,
            isSubmitting: false,
          };
          const endDate =
            state.contractInformation?.previousParticipation?.endDate;
          const minDate = endDate
            ? DatetimeUtil.dateToMoment(endDate).add(1, 'd').toDate()
            : DatetimeUtil.getPreviousQuarters(DatetimeUtil.dateToMoment(), 4)
              .startOf('quarter')
              .toDate();
          const maxDate = DatetimeUtil.date();
          currentStage.datePickerProps =
            ParticipationService.generateDatePickerProps(
              t('changeContractStartDateTitle'),
              t('changeContractStartDateCancel'),
              t('changeContractStartDateSubmit'),
              null,
              minDate,
              maxDate,
              undefined,
              ParticipationService.generateQuarterShortcut(minDate, maxDate, t)
            );
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation: {
          currentStage = {
            value: 'InlineDatePicker',
            currentProcess: menuAction,
            datePickerProps: undefined,
            isSubmitting: false,
          };
          const today = DatetimeUtil.date();
          const maxDate = DatetimeUtil.getNextQuarters(today, 4).toDate();
          let minDate: Date | undefined = undefined;
          if (!!state.contractInformation?.participation?.startDate) {
            minDate = utcToLocal(
              parseDate(state.contractInformation.participation.startDate)!
            );
          }
          if (!minDate) {
            minDate = DatetimeUtil.getPreviousQuarters(today, 4).toDate();
          }
          const dateShortcuts = ParticipationService.generateQuarterShortcut(
            minDate,
            today,
            t
          );

          currentStage.datePickerProps =
            ParticipationService.generateDatePickerProps(
              t('teminateContractParticipationTitle'),
              t('cancelButton'),
              t('terminateContractParticipationSubmit'),
              null,
              minDate,
              maxDate,
              Intent.DANGER,
              dateShortcuts
            );
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelEnrollment:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CancelParticipation:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_UndoTerminateParticipation: {
          currentStage = {
            value: 'RequestActionContract',
            request: {
              action: menuAction,
              patientId: state.patient?.id!,
              contractId: state.contractInformation?.contractId,
            },
          };
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateEnrollment:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ContinueEnrollment:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_CreateActiveParticipation: {
          currentStage = {
            value: 'RequestActionContract',
            request: {
              action: menuAction,
              patientId: state.patient?.id!,
              contractId: state.contractInformation?.contractId,
            },
          };
          break;
        }
        case ParticipationButtonAction.ParticipationButtonAction_CheckOnlineParticipation: {
          state.hpmInformation = {
            hpmViewInformation: {
              ...state.hpmInformation?.hpmViewInformation,
              isLoading: true,
            },
          };
          break;
        }
      }

      state.stage = {
        ...currentStage,
      };
      break;
    }
    case 'OnRequestedActionContract':
    case 'OnCancelOrCloseAction': {
      state.stage.value = 'IDLE';
      break;
    }
    case 'OnDatePickerSubmit': {
      const payload = action.payload;
      const request: ActionOnContractRequest = {
        action: payload.currentAction as PatientEnrollmentInformationAction,
        patientId: state.patient?.id!,
      };
      switch (payload.currentAction) {
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ChangeParticipationStartDate:
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_ActiveParticipation: {
          request.patientParticipationIds = [payload.participationId];
          request.enrollmentId = payload.enrollmentId;
          request.contractId = state.contractInformation?.contractId;
          request.startDate = getUTCMilliseconds(payload.date);
          break;
        }
        case PatientEnrollmentInformationAction.PatientEnrollmentInformationAction_TerminateParticipation: {
          request.contractId = state.contractInformation?.contractId;
          request.endDate = getUTCMilliseconds(payload.date);
          break;
        }
      }
      state.stage = {
        value: 'RequestActionContract',
        request: request,
      };
      break;
    }
  }
  return { ...state };
};
