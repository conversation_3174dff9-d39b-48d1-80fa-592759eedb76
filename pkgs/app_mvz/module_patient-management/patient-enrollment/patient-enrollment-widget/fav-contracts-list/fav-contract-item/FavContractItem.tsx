import React, {
  memo,
  useReducer,
  useEffect,
  useContext,
  useState,
} from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import {
  MenuItem,
  PopoverInteractionKind,
  Position,
  Tooltip,
} from '@tutum/design-system/components/Core';
import {
  IContractInformation,
  ParticipationAction,
  HpmViewInformation,
} from '../../participation/Participation.model';
import {
  FavContractItemReducer,
  initFavContractItemState,
} from './FavContractItem.reducer';
import EnrollmentMenu from '../../enrollment-menu/EnrollmentMenu.styled';
import PatientManagementContext from '../../../../contexts/patient-management/PatientManagementContext';
import { ParticipationHook } from '../../participation/Participation.hook';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { Flex, Svg } from '@tutum/design-system/components';
import DatePickerForm from '../../date-picker-form/DatePickerForm.styled';
import { ActionOnContractRequest } from '@tutum/hermes/bff/service_domains_enrollment';
import { ParticipationToolTipContent } from '../../participation/Participation';
import { patientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { PatientSettings } from '@tutum/hermes/bff/legacy/app_mvz_patient_settings';
// import GlobalContext from '@tutum/mvz/contexts/Global.context';
// import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
// import moment from 'moment';
import { MainGroup } from '@tutum/hermes/bff/legacy/common';

export interface IFavContractItemProps {
  contractInformation: IContractInformation;
  hpmInformation: HpmViewInformation;
  onActionContract: (request: ActionOnContractRequest) => void;
  onRequestHpm: (contractIds: string[]) => void;
  showEnrollmentActions?: false;
  isSamePendingGroup?: boolean;
  HzvContractInformation: {
    isActiveOnMvz: boolean;
    isActiveOnHpm: boolean;
  };
  settings: PatientSettings[];
}

function FavContractItemMemo(
  props: IFavContractItemProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.FavContractItem>
) {
  const AlertHpmConflict = '/images/alert-conflict-hpm-status.svg';

  const { contractInformation, t } = props;
  const {
    patientManagement: { patient },
  } = useContext(PatientManagementContext.instance);

  const missingInsuranceNumber = !patientFileStore.activeInsurance?.ikNumber;

  const [modelState, dispatch] = useReducer(
    FavContractItemReducer,
    initFavContractItemState(t)
  );

  const isConflict = ParticipationHook.useCheckIsContractConflictWithHpm([
    {
      contractInformation: modelState.contractInformation,
      hpmInformation: modelState.hpmInformation?.hpmViewInformation,
    },
  ]);

  const contractMenu = ParticipationHook.useLoadParticipationMenuItems(
    modelState.contractInformation,
    t,
    // https://www.notion.so/silenteer/Not-enrolled-04a76326c1af4d63a85925dd460d9719
    // Since the logic was changed back and forth frequently, will set status of hzv by hardcode without removing this parameter as describe on notion.
    // props.HzvContractInformation?.isActiveOnHpm ||
    // props.HzvContractInformation?.isActiveOnMvz
    true
  );

  const tooltipContent = ParticipationHook.useGenerateContractToolTip(
    modelState.contractInformation,
    patientFileStore.activeInsurance?.ikNumber,
    modelState.hpmInformation?.hpmViewInformation.isActive,
    'favContract',
    t,
    missingInsuranceNumber
  );

  const terminateLogInformation = ParticipationHook.useGetTerminateLog(
    modelState.contractInformation,
    'menuFooterFavTerminated',
    t
  );

  useEffect(() => {
    switch (modelState.stage.value) {
      case 'RequestActionContract':
        {
          props.onActionContract({
            ...modelState.stage.request,
          });
        }
        dispatch({
          type: 'OnRequestedActionContract',
        });
    }
  }, [modelState.stage.value]);

  useEffect(() => {
    if (!modelState.contractInformation) {
      return;
    }
    if (
      modelState.hpmInformation?.hpmViewInformation?.isLoading &&
      modelState.hpmInformation?.hpmViewInformation?.isLoading !==
      props.hpmInformation?.isLoading
    ) {
      props.onRequestHpm([modelState.contractInformation?.contractId]);
    }
  }, [modelState.hpmInformation]);

  useEffect(() => {
    if (!contractInformation) {
      return;
    }
    dispatch({
      type: 'OnContractInformationChanged',
      payload: contractInformation,
    });
  }, [contractInformation]);

  useEffect(() => {
    dispatch({
      type: 'OnPatientProfileChanged',
      payload: patient,
    });
  }, [patient]);

  useEffect(() => {
    dispatch({
      type: 'OnHpmInformationChanged',
      payload: { ...props.hpmInformation },
    });
  }, [props.hpmInformation, modelState.contractInformation]);

  const [displayToolTip, setDisplayToolTip] = useState<boolean>(false);
  // const globalContext = useContext(GlobalContext.instance);
  // const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();
  // const now = moment(datetimeUtil.now());
  // const isHideCheckOnline = Boolean(
  //   settings.find(
  //     (s) =>
  //       typeof s.favContractOnlineChecked.find(
  //         (f) =>
  //           f.doctorId == currentLoggedinUser.id &&
  //           contractInformation.contractId === f.contractId
  //       ) != 'undefined' &&
  //       s.year === now.year() &&
  //       s.quarter === now.quarter()
  //   ) || contractInformation.contractId === 'MEDI_FA_PT_BW'
  // );

  return (
    modelState.contractInformation && (
      <Flex className={props.className}>
        <Tooltip
          position={Position.TOP}
          interactionKind={PopoverInteractionKind.HOVER}
          content={
            <ParticipationToolTipContent
              className="fav-tooltip-content"
              contentLines={tooltipContent?.contentLines}
              isLoading={modelState.stage.value !== 'IDLE'}
            />
          }
          className="popover-wrapper-menu-item"
          isOpen={displayToolTip}
        >
          <MenuItem
            onMouseEnter={() => {
              setDisplayToolTip(true);
            }}
            onMouseLeave={() => {
              setDisplayToolTip(false);
            }}
            labelElement={
              isConflict && (
                <Flex className="hpm-icon-conflict">
                  <Svg className="sl-icon-error" src={AlertHpmConflict} />
                </Flex>
              )
            }
            text={
              <span
                className={getCssClass(
                  `fav-item-text`,
                  `fav-item-text-${contractInformation.status.toLocaleLowerCase()}`,
                  `${props.isSamePendingGroup && 'fav-item-pending-group'}`
                )}
              >
                {modelState.contractInformation.contractAbbreviation ??
                  modelState.contractInformation.contractId}
              </span>
            }
            className={getCssClass(
              `fav-item fav-item-${contractInformation.status.toLocaleLowerCase()}`
            )}
            key={modelState.contractInformation.contractId}
            popoverProps={{
              interactionKind: PopoverInteractionKind.CLICK,
            }}
          >
            {modelState.stage.value === 'IDLE' && contractMenu && (
              <EnrollmentMenu
                enrollmentActionItems={
                  props.showEnrollmentActions
                    ? contractMenu.enrollmentActions
                    : []
                }
                participationActionItems={contractMenu.particpationActions}
                currentParticipation={
                  modelState.contractInformation?.participation
                }
                terminateLogInformation={terminateLogInformation}
                onClick={(action: ParticipationAction) => {
                  dispatch({
                    type: 'OnActionClicked',
                    payload: {
                      action: action,
                    },
                  });
                }}
                hpmInformation={modelState.hpmInformation?.hpmViewInformation}
                patient={patient}
                // showCheckParticipation={!isHideCheckOnline}
                // showHpmInformation={
                //   contractInformation.contractId !== 'MEDI_FA_PT_BW'
                // }
                // footerMessage={modelState.hzvMenu.footerMessage}
                type={MainGroup.FAV}
              />
            )}
            {modelState.stage.value === 'InlineDatePicker' &&
              modelState.stage.datePickerProps && (
                <>
                  <Flex column auto className={'popover-form-container'}>
                    <DatePickerForm
                      dateShortcut={
                        modelState.stage.datePickerProps.dateShortcut
                      }
                      title={modelState.stage.datePickerProps.title}
                      cancelLabel={modelState.stage.datePickerProps.cancelLabel}
                      submitLabel={modelState.stage.datePickerProps.submitLabel}
                      defaultValue={
                        modelState.stage.datePickerProps.defaultValue
                      }
                      minDate={modelState.stage.datePickerProps.minDate}
                      maxDate={modelState.stage.datePickerProps.maxDate}
                      className={'date-picker-form'}
                      showFavGroupCheckBox={false}
                      onCancel={() => {
                        dispatch({
                          type: 'OnCancelOrCloseAction',
                          payload:
                            modelState.stage.value === 'InlineDatePicker'
                              ? modelState.stage.currentProcess
                              : undefined,
                        });
                      }}
                      onSubmit={(date: Date, applyContractGroup: boolean) => {
                        dispatch({
                          type: 'OnDatePickerSubmit',
                          payload: {
                            currentAction:
                              modelState.stage.value === 'InlineDatePicker'
                                ? modelState.stage.currentProcess
                                : null!,
                            date: date,
                            participationId:
                              modelState.contractInformation
                                ?.participationIds?.[0]!,
                            enrollmentId:
                              modelState.contractInformation?.enrollmentId!,
                            applyContractGroup: applyContractGroup,
                          },
                        });
                      }}
                      isSubmitting={modelState.stage.isSubmitting}
                      intentSubmit={
                        modelState.stage.datePickerProps.intentSubmit
                      }
                    />
                  </Flex>
                </>
              )}
          </MenuItem>
        </Tooltip>
      </Flex>
    )
  );
}

export default memo(
  I18n.withTranslation(FavContractItemMemo, {
    namespace: 'PatientManagement',
    nestedTrans: 'FavContractItem',
  })
);
