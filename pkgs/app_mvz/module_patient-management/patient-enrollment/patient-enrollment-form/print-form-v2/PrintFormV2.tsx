import {
  alertError,
  alertSuccessfully,
  BodyTextM,
  Button,
  Flex,
  H3,
  Svg,
} from '@tutum/design-system/components';
import {
  ChangeDoctorReason,
  FormType,
} from '@tutum/hermes/bff/service_domains_enrollment';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { useCallback, useEffect, useMemo, useReducer } from 'react';
import CircleNumber from '../CircleNumber';
import PatientEnrollmentFormHook, {
  HookGetListContractsResponse,
} from '../PatientEnrollmentForm.hook';
import {
  handleGroupFormsByContractId,
  handleSortedGroupForms,
} from '../PatientEnrollmentForm.service';
import { patientEnrollmentActions } from '../PatientEnrollmentForm.store';
import printFormReducer, {
  IPrintFormState,
} from '../print-form/PrintForm.reducer';
import { IPrintForm } from '../print-review-form/PrintReviewForm';
import { musterFormActions } from '@tutum/mvz/module_form/muster-form/musterForm.store';
import { Divider, Intent } from '@tutum/design-system/components/Core';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';

export interface PrintFormV2Props {
  forms: IPrintForm[] | null;
  displayStageNumber: number;
  showForm?: boolean;
  isHzvButton: boolean;
  changeDoctorReason?: ChangeDoctorReason;
  contractId: string;
  doctorId: string;
  isOnlineEnrollment: boolean;
  setFormPrint?: React.Dispatch<React.SetStateAction<boolean>>;
}

function init(data: IPrintFormState): IPrintFormState {
  return {
    forms:
      data && data.forms
        ? Array.from(data.forms, (f) => {
          return { ...f };
        })
        : null,
    printingFormsQueue: [],
    printingForm: null,
    isReviewOpen: false,
  };
}

const PrintFormV2 = ({
  forms,
  displayStageNumber,
  showForm = true,
  changeDoctorReason,
  contractId,
  isOnlineEnrollment,
  setFormPrint,
}: PrintFormV2Props) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.PrintForm
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'PrintForm',
  });

  const groupFormsByContractId = useMemo(() => {
    return handleGroupFormsByContractId(forms);
  }, [forms]);

  const memoizedListContractId = useMemo(() => {
    return forms?.map((item) => ({
      contractId: item.contractId,
      contractType: item.contractType,
    }));
  }, [forms]);

  const listContractGroups = PatientEnrollmentFormHook.useHookGetListContracts(
    memoizedListContractId
  );

  const sortedGroupForms = useMemo(() => {
    if (!groupFormsByContractId) return null;
    return handleSortedGroupForms(groupFormsByContractId);
  }, [groupFormsByContractId]);

  const { isContractSupport: hasSupportVERE466 } = useCheckContractSupport(
    ['VERE466'],
    [contractId]
  );

  const showContractName = useCallback(
    (
      listContractGroups: HookGetListContractsResponse,
      currentContract: string
    ) => {
      const { listContracts, listGroups } = listContractGroups;

      let contractName = listContracts?.find(
        (c) => c.contractId === currentContract
      )?.contractName;

      if (!listGroups || !listGroups.patientParticipationContracts?.length) {
        return contractName;
      }

      for (
        let i = 0;
        i < listGroups!.patientParticipationContracts.length;
        i++
      ) {
        const contractIds = listGroups!.patientParticipationContracts[i];

        const isSubContractExistInGroup = contractIds.includes(currentContract);
        if (isSubContractExistInGroup) {
          contractName = listGroups.designation;
          break;
        }
      }
      return contractName;
    },
    [listContractGroups]
  );

  const handleLabel = (form: IPrintForm) => {
    let label = '';
    switch (form.type) {
      case FormType.FormType_Registration:
        label += t('registrationDocument');
        break;
      case FormType.FormType_CareManagement1:
        label += t('careManagement1');
        break;
      case FormType.FormType_CareManagement2:
        label += t('careManagement2');
        break;
      default:
        label += form.templateName;
        break;
    }
    if (form.printed) label += ` ${t('tagPrintedForm')}`;
    return label;
  };

  const handlePrintForm = (form: IPrintForm, isFormPrint: boolean) => {
    if (!form) return;

    setFormPrint?.(isFormPrint);
    patientEnrollmentActions.setCurrentForm(form);
    const formName = form.templatePath.split('/').pop()?.split('.')[0] || '';

    musterFormDialogActions.setCurrentFormName(formName);

    if (changeDoctorReason && formName) {
      musterFormActions.setFormName(formName, undefined, undefined, () => {
        musterFormDialogActions.setCurrentMusterFormSetting({
          checkbox_arztwechsel_0: true,
          textbox_reason_0: changeDoctorReason,
          checkbox_arztwechsel_1: true,
          textbox_reason_1: changeDoctorReason,
          checkbox_arztwechsel_2: true,
          textbox_reason_2: changeDoctorReason,
          checkbox_arztwechsel_3: true,
          textbox_reason_3: changeDoctorReason,
        });
      });
    }
  };

  const [_, dispatch] = useReducer(
    printFormReducer,
    {
      forms,
      printingFormsQueue: [],
      isReviewOpen: false,
      printingForm: null,
    },
    init
  );

  useEffect(() => {
    patientEnrollmentActions.setPrintFormDispatch(dispatch);
  }, []);

  return (
    <Flex className="print-form-v2" auto column>
      <H3 className="label-section-form">
        {!!displayStageNumber && (
          <CircleNumber showedText={displayStageNumber.toString()} />
        )}
        {forms && forms.length <= 1
          ? t('printFormLabel')
          : t('printFormsLabel')}
      </H3>
      {showForm && forms && (
        <>
          <Flex column gap={8}>
            {sortedGroupForms?.map((contractId) => (
              <Flex key={contractId} column gap={8}>
                <BodyTextM textTransform="uppercase">
                  {showContractName(listContractGroups, contractId)}
                </BodyTextM>
                <div className={'print-form-v2__list'}>
                  {groupFormsByContractId[contractId].map(
                    (form: IPrintForm) => {
                      // const isRegistrationForm = form.type === FormType.FormType_Registration;
                      const hasBlankForm = !isOnlineEnrollment && hasSupportVERE466;

                      return (
                        <div
                          key={form.formId}
                          className={`print-form-v2__item ${form.printed ? 'printed' : ''
                            }`}
                        >
                          <div className="print-form-v2__form-name flex-1">
                            <Svg src={'/images/file-text.svg'} />
                            <BodyTextM fontWeight={600}>
                              {handleLabel(form)}
                            </BodyTextM>
                          </div>
                          <div className="print-form-v2__print-btn-group">
                            {hasBlankForm && (
                              <>
                                <Button
                                  minimal
                                  intent={Intent.PRIMARY}
                                  onClick={() => handlePrintForm(form, false)}
                                >
                                  {t('printBlankForm')}
                                </Button>
                                <Divider />
                              </>
                            )}
                            <Button
                              minimal
                              intent={Intent.PRIMARY}
                              onClick={() =>
                                handlePrintForm(
                                  form,
                                  hasBlankForm
                                )
                              }
                            >
                              {form.printed ? t('printAgain') : t('print')}
                            </Button>
                          </div>
                        </div>
                      );
                    }
                  )}
                </div>
              </Flex>
            ))}
          </Flex>
        </>
      )}
    </Flex>
  );
};

export default PrintFormV2;
