import {
  HpmError,
  IErrorData,
} from '@tutum/design-system/components/ErrorBoundary/ErrorBoundaryModel';
import {
  createPatientEnrollment,
  getPatientEnrollment,
  sendParticipation,
  saveOfflineEnrollment,
  getPatientContractGroups,
  getPreviewFormUrl,
  printForm,
} from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { ContractType } from '@tutum/hermes/bff/common';
import { ContractMetaData } from '@tutum/hermes/bff/legacy/app_mvz_contract';
import { InsuranceInfo } from '@tutum/hermes/bff/patient_profile_common';
import { MainGroup, ScheinItem } from '@tutum/hermes/bff/schein_common';
import {
  ContractGroup,
  CreatePatientEnrollmentRequest,
  GetPreviewFormUrlRequest,
  GetPreviewFormUrlResponse,
  PatientEnrollmentResponse,
  PrintFormRequest,
  PrintFormResponse,
  SendParticipationRequest,
} from '@tutum/hermes/bff/service_domains_enrollment';
import { GetPatientParticipationResponse } from '@tutum/hermes/bff/service_domains_patient_participation';
import { default as datetimeUtil } from '@tutum/infrastructure/utils/datetime.util';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { useEffect, useState } from 'react';
import { IPatientManagement } from '../../contexts/patient-management/PatientManagementContext.type';
import reloadSchein from '../../utils/reloadSchein';
import {
  EnrollmentErrorCode,
  ISendParticipationResponse,
} from './PatientEnrollmentForm.reducer';
import {
  PatientEnrollmentStatus,
  SaveOfflineEnrollmentRequest,
} from '@tutum/hermes/bff/legacy/service_domains_enrollment';

function useHookCreatePatientEnrollemnt(
  request: CreatePatientEnrollmentRequest | undefined
): PatientEnrollmentResponse | null {
  const [createEnrollmentResponse, setCreateEnrollmentResponse] =
    useState<PatientEnrollmentResponse | null>(null);

  useEffect(() => {
    if (
      !request ||
      !request.patientId ||
      !request.contractId ||
      !request.doctorId ||
      request.enrollmentId
    ) {
      return;
    }
    createPatientEnrollment(request).then((res) => {
      if (res?.data) {
        setCreateEnrollmentResponse(res.data);
      }
    });
  }, [request]);
  return createEnrollmentResponse;
}

function useHookGetPatientEnrollemnt(
  request: CreatePatientEnrollmentRequest | undefined
): PatientEnrollmentResponse | null {
  const [enrollmentResponse, setEnrollmentResponse] =
    useState<PatientEnrollmentResponse | null>(null);

  useEffect(() => {
    if (!request || !request.enrollmentId) {
      return;
    }
    getPatientEnrollment(request).then((res) => {
      if (res?.data) {
        setEnrollmentResponse(res.data);
      }
    });
  }, [request]);
  return enrollmentResponse;
}

function useHookSendParticipation(
  request: SendParticipationRequest | undefined,
  insuranceInfo: InsuranceInfo | undefined,
  patientManagement: IPatientManagement,
  setGetPatientParticipationResponse: (
    res: GetPatientParticipationResponse
  ) => void,
  onReloadLoadListContracts?: () => void
): ISendParticipationResponse | null {
  const [sendParticipationResponse, setSendParticipationResponse] =
    useState<ISendParticipationResponse | null>(null);

  useEffect(() => {
    if (!request || !request.forms.length) {
      return;
    }

    sendParticipation(request)
      .then(async (res) => {
        const errorMessages: string[] = [];
        const status = res.data.status;
        if (
          ![
            PatientEnrollmentStatus.PatientEnrollmentStatus_Active,
            PatientEnrollmentStatus.PatientEnrollmentStatus_Requested,
          ].includes(status)
        ) {
          errorMessages.push(...res.data.messages);
        }
        setSendParticipationResponse({
          patientEnrollment: res.data,
          errorMessages: errorMessages,
        });

        reloadSchein(patientManagement, true).then(
          ({ scheins, patientParticipation }) => {
            setGetPatientParticipationResponse(patientParticipation);
            patientFileActions.schein.setActivatedSchein(
              scheins.filter(
                (schein: ScheinItem) => schein.scheinMainGroup !== MainGroup.FAV
              )[0],
              patientParticipation.participations
            );
            onReloadLoadListContracts?.();
          }
        );
      })
      .catch((errorData: IErrorData) => {
        if (errorData.serverError == EnrollmentErrorCode.DefaultHpmError) {
          const hpmError = errorData as unknown as HpmError;
          const messages = hpmError.serverErrorParam
            ? hpmError.serverErrorParam.map((x) => {
                return x.message.length > 0 ? x.message : x.code;
              })
            : undefined;
          setSendParticipationResponse({
            patientEnrollment: undefined,
            errorMessages: messages,
          });
        } else {
          // there will have timeout exeption from nats-server which related to requesting to HPM service. So still need to cover this case.
          setSendParticipationResponse({
            patientEnrollment: undefined,
            errorMessages: undefined,
          });
        }
      });
  }, [request, insuranceInfo, patientManagement.patientId?.value]);

  return sendParticipationResponse;
}

function useHookSaveOfflineEnrollment(
  request: SaveOfflineEnrollmentRequest | undefined,
  insuranceInfo: InsuranceInfo | undefined,
  patientManagement: IPatientManagement,
  setGetPatientParticipationResponse: (
    res: GetPatientParticipationResponse
  ) => void,
  onReloadLoadListContracts?: () => void
): ISendParticipationResponse | null {
  const [saveOfflineEnrollmentResponse, setSaveOfflineEnrollmentResponse] =
    useState<ISendParticipationResponse | null>(null);

  useEffect(() => {
    if (!request) return;

    saveOfflineEnrollment(request)
      .then(async (res) => {
        const errorMessages: string[] = [];
        const status = res.data.status;
        if (
          ![PatientEnrollmentStatus.PatientEnrollmentStatus_Printed].includes(
            status
          )
        ) {
          errorMessages.push(...res.data.messages);
        }
        setSaveOfflineEnrollmentResponse({
          patientEnrollment: res.data,
          errorMessages: errorMessages,
        });

        reloadSchein(patientManagement, true).then(
          ({ scheins, patientParticipation }) => {
            setGetPatientParticipationResponse(patientParticipation);
            patientFileActions.schein.setActivatedSchein(
              scheins.filter(
                (schein: ScheinItem) => schein.scheinMainGroup !== MainGroup.FAV
              )[0],
              patientParticipation.participations
            );
            onReloadLoadListContracts?.();
          }
        );
      })
      .catch((errorData: IErrorData) => {
        if (errorData.serverError == EnrollmentErrorCode.DefaultHpmError) {
          const hpmError = errorData as unknown as HpmError;
          const messages = hpmError.serverErrorParam
            ? hpmError.serverErrorParam.map((x) => {
                return x.message.length > 0 ? x.message : x.code;
              })
            : undefined;
          setSaveOfflineEnrollmentResponse({
            patientEnrollment: undefined,
            errorMessages: messages,
          });
        } else {
          // there will have timeout exeption from nats-server which related to requesting to HPM service. So still need to cover this case.
          setSaveOfflineEnrollmentResponse({
            patientEnrollment: undefined,
            errorMessages: undefined,
          });
        }
      });
  }, [request, insuranceInfo, patientManagement.patientId?.value]);

  return saveOfflineEnrollmentResponse;
}

function useHookPrintForm(request: PrintFormRequest | undefined): PrintFormResponse | null {
  const [printFormResponse, setPrintFormResponse] =
    useState<PrintFormResponse | null>(null);

  useEffect(() => {
    if (!request) {
      return;
    }
    printForm(request).then((res) => {
      setPrintFormResponse({
        printedAll: res?.data?.printedAll,
      });
    });
  }, [request]);
  return printFormResponse;
}

function useHookGetPreviewFormUrl(
  request: GetPreviewFormUrlRequest | undefined
): GetPreviewFormUrlResponse | null {
  const [getPreviewFormUrlResponse, setGetPreviewFormUrlResponse] =
    useState<GetPreviewFormUrlResponse | null>(null);

  useEffect(() => {
    if (request) {
      getPreviewFormUrl(request).then((res) => {
        setGetPreviewFormUrlResponse({
          filePath: res?.data?.filePath,
        });
      });
    }
  }, [request]);
  return getPreviewFormUrlResponse;
}

export interface HookGetListContractsResponse {
  listContracts: ContractMetaData[] | null;
  listGroups: ContractGroup | null;
}

function useHookGetListContracts(
  listGroupForms?: Array<{ contractId: string; contractType: ContractType }>
): HookGetListContractsResponse {
  const [listContracts, setListContracts] = useState<ContractMetaData[] | null>(
    null
  );
  const [listGroups, setListGroups] = useState<ContractGroup | null>(null);

  useEffect(() => {
    if (listGroupForms?.length) {
      Promise.all(
        listGroupForms?.map((form) =>
          catalogOverviewActions.getContractMeta(
            datetimeUtil.now(),
            form.contractId
          )
        )
      ).then((res) => {
        setListContracts(res);
        const favForm = listGroupForms.find(
          (f) => f.contractType === ContractType.ContractType_SpecialistCare
        );

        if (favForm?.contractId) {
          getPatientContractGroups({ contractId: favForm.contractId }).then(
            (r) => {
              setListGroups(r?.data?.contractGroups?.[0] ?? null);
            }
          );
        }
      });
    }
  }, [JSON.stringify(listGroupForms)]);

  return { listContracts, listGroups };
}

export default {
  useHookCreatePatientEnrollemnt,
  useHookGetPatientEnrollemnt,
  useHookSendParticipation,
  useHookSaveOfflineEnrollment,
  useHookPrintForm,
  useHookGetPreviewFormUrl,
  useHookGetListContracts,
};
