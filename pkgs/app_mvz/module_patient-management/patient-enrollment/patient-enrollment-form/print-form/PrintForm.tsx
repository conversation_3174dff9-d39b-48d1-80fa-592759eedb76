import { BodyTextM, Box, Flex, H3 } from '@tutum/design-system/components';
import {
  Button,
  Checkbox,
  FormGroup,
  Intent,
} from '@tutum/design-system/components/Core';
import { ContractType } from '@tutum/hermes/bff/common';
import { FormType } from '@tutum/hermes/bff/service_domains_enrollment';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { memo, useCallback, useEffect, useMemo, useReducer } from 'react';
import CircleNumber from '../CircleNumber';
import PatientEnrollmentFormHook, {
  HookGetListContractsResponse,
} from '../PatientEnrollmentForm.hook';
import {
  handleGroupFormsByContractId,
  handleSortedGroupForms,
} from '../PatientEnrollmentForm.service';
import {
  patientEnrollmentActions,
  usePatientEnrollmentFormStore,
} from '../PatientEnrollmentForm.store';
import { IPrintForm } from '../print-review-form/PrintReviewForm';
import printFormReducer, { IPrintFormState } from './PrintForm.reducer';

export interface IPrintFormViewProps {
  forms: IPrintForm[];
  displayStageNumber: number;
  showForm?: boolean;
  isHzvButton: boolean;
}

export function sortForms(forms: IPrintForm[]) {
  forms.sort((a, b) => {
    const indexA =
      a.type === FormType.FormType_Registration
        ? 0
        : FormType.FormType_Participation
          ? 1
          : FormType.FormType_CareManagement1
            ? 2
            : 3; // FormType_CareManagement2
    const indexB =
      b.type === FormType.FormType_Registration
        ? 0
        : FormType.FormType_Participation
          ? 1
          : FormType.FormType_CareManagement1
            ? 2
            : 3; // FormType_CareManagement2
    return indexA <= indexB ? -1 : 1;
  });
}

function init(data: IPrintFormState): IPrintFormState {
  return {
    forms:
      data && data.forms
        ? Array.from(data.forms, (f) => {
          return { ...f };
        })
        : null,
    printingFormsQueue: [],
    printingForm: null,
    isReviewOpen: false,
  };
}

const PrintForm = ({
  forms,
  displayStageNumber,
  showForm = true,
  isHzvButton,
}: IPrintFormViewProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.PrintForm
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'PrintForm',
  });

  const store = usePatientEnrollmentFormStore();

  const memoizedListContractId = useMemo(() => {
    return forms?.map((item) => ({
      contractId: item.contractId,
      contractType: item.contractType,
    }));
  }, [forms]);

  const listContractGroups = PatientEnrollmentFormHook.useHookGetListContracts(
    memoizedListContractId
  );

  const [modelState, dispatch] = useReducer(
    printFormReducer,
    {
      forms,
      printingFormsQueue: [],
      isReviewOpen: false,
      printingForm: null,
    },
    init
  );

  useEffect(() => {
    patientEnrollmentActions.setPrintFormDispatch(dispatch);
  }, []);

  useEffect(() => {
    dispatch({
      type: 'ReloadForms',
      payload: {
        forms: forms,
        sortForms,
      },
    });
  }, [forms]);

  const groupFormsByContractId = useMemo(() => {
    return handleGroupFormsByContractId(modelState.forms);
  }, [modelState.forms]);

  const sortedGroupForms = useMemo(() => {
    if (!groupFormsByContractId) return null;
    return handleSortedGroupForms(groupFormsByContractId);
  }, [groupFormsByContractId]);

  const handleLabel = useCallback(
    (formType: FormType, isFormPrinted: boolean) => {
      let label = isFormPrinted ? `${t('tagPrintedForm')} ` : '';
      switch (formType) {
        case FormType.FormType_Registration:
          label += t('registrationDocument');
          break;
        case FormType.FormType_Participation:
          label += t('declareParticipation');
          break;
        case FormType.FormType_CareManagement1:
          label += t('careManagement1');
          break;
        case FormType.FormType_CareManagement2:
          label += t('careManagement2');
          break;
        default:
          break;
      }
      return label;
    },
    []
  );

  const isDisableCheckbox = useCallback(
    (_model, form) => {
      if (!isHzvButton) return false;
      const isExtraFAV =
        form?.contractType === ContractType.ContractType_SpecialistCare;

      return isHzvButton && isExtraFAV && !store.isEnrollExtraFAV;
    },
    [store.formsToPrint]
  );

  const isEnableCheckbox = useCallback(
    (model, form) => {
      if (!isHzvButton) {
        return (
          model?.printingFormsQueue.findIndex((f) => f.formId === form?.formId) >=
          0
        );
      }
      const formToPrint = store.formsToPrint;
      const formExistInQueue = model?.printingFormsQueue.find(
        (f) => f.formId === form?.formId
      );
      if (!formExistInQueue) return false;
      const shouldPrintForm =
        formToPrint.findIndex(
          (form) => form.formId === formExistInQueue?.formId
        ) !== -1;

      if (!shouldPrintForm) {
        dispatch({
          type: 'OnCheckboxFormsChanged',
          payload: {
            formId: form?.formId,
            checked: false,
          },
        });
      }

      return shouldPrintForm;
    },
    [store.formsToPrint]
  );

  const renderCheckboxes = useCallback(
    (forms: IPrintForm[]) => {
      return (
        <>
          {forms?.map((form) => (
            <>
              <Checkbox
                key={form.formId}
                value={form.formId}
                disabled={isDisableCheckbox(modelState, form)}
                checked={isEnableCheckbox(modelState, form)}
                label={handleLabel(form.type, form.printed)}
                className={form.printed ? 'form-printed-checkbox' : ''}
                onChange={(event) => {
                  dispatch({
                    type: 'OnCheckboxFormsChanged',
                    payload: {
                      formId: form.formId,
                      checked: event.currentTarget.checked,
                    },
                  });
                }}
              />
            </>
          ))}
        </>
      );
    },
    [modelState.printingFormsQueue, store.formsToPrint]
  );

  const showContractName = useCallback(
    (
      listContractGroups: HookGetListContractsResponse,
      currentContract: string
    ) => {
      const { listContracts, listGroups } = listContractGroups;

      let contractName = listContracts?.find(
        (c) => c.contractId === currentContract
      )?.contractName;

      if (!listGroups || !listGroups.patientParticipationContracts?.length) {
        return contractName;
      }

      for (
        let i = 0;
        i < listGroups!.patientParticipationContracts.length;
        i++
      ) {
        const contractIds = listGroups!.patientParticipationContracts[i];

        const isSubContractExistInGroup = contractIds.includes(currentContract);
        if (isSubContractExistInGroup) {
          contractName = listGroups.designation;
          break;
        }
      }
      return contractName;
    },
    [listContractGroups]
  );

  return (
    <>
      <Flex className="print-form" auto column>
        <H3 className="label-section-form">
          <CircleNumber showedText={displayStageNumber.toString()} />
          {modelState.forms && modelState.forms.length <= 1
            ? t('printFormLabel')
            : t('printFormsLabel')}
        </H3>
        {showForm && modelState.forms && (
          <>
            <Flex column gap={8}>
              {sortedGroupForms?.map((contractId) => (
                <Flex key={contractId} column gap={8}>
                  <BodyTextM textTransform="uppercase">
                    {showContractName(listContractGroups, contractId)}
                  </BodyTextM>
                  {renderCheckboxes(groupFormsByContractId[contractId])}
                </Flex>
              ))}
            </Flex>
            <Box w="100%">
              <FormGroup>
                <Button
                  type="button"
                  intent={Intent.PRIMARY}
                  fill
                  disabled={
                    !modelState.printingFormsQueue ||
                    modelState.printingFormsQueue.length === 0
                  }
                  onClick={() => {
                    const forms = modelState.printingFormsQueue;
                    sortForms(forms);
                    const form = forms[0];
                    if (!form) {
                      return;
                    }
                    patientEnrollmentActions.setCurrentForm(form);
                    const formName = form.templatePath
                      .split('/')
                      .pop()
                      ?.split('.')[0]!;
                    musterFormDialogActions.setCurrentFormName(formName);
                  }}
                >
                  {t('printButtonLabel')}
                </Button>
                <Button
                  type="button"
                  intent={Intent.PRIMARY}
                  fill
                  disabled={
                    !modelState.printingFormsQueue ||
                    modelState.printingFormsQueue.length === 0
                  }
                  onClick={() => {
                    const forms = modelState.printingFormsQueue;
                    sortForms(forms);
                    const form = forms[1];
                    if (!form) {
                      return;
                    }
                    patientEnrollmentActions.setCurrentForm(form);
                    const formName = form.templatePath
                      .split('/')
                      .pop()
                      ?.split('.')[0]!;
                    musterFormDialogActions.setCurrentFormName(formName);
                  }}
                >
                  {t('printButtonLabel')}
                </Button>
              </FormGroup>
            </Box>
          </>
        )}
      </Flex>
    </>
  );
};

export default memo(PrintForm);
