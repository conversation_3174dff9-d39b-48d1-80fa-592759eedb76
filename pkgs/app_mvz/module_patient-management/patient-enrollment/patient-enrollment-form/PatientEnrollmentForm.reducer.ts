import {
  getUUID,
  isNotNullOrEmpty,
} from '@tutum/design-system/infrastructure/utils';
import { ContractType } from '@tutum/hermes/bff/common';
import { EnrollmentType } from '@tutum/hermes/bff/legacy/enrollment_common';
import { SaveOfflineEnrollmentRequest } from '@tutum/hermes/bff/legacy/service_domains_enrollment';
import {
  ChangeDoctorReason,
  CreatePatientEnrollmentRequest,
  ErrorMessageValue,
  Form,
  FormType,
  ParticipationStatus,
  PatientEnrollmentResponse,
  PatientEnrollmentStatus,
  PrintFormRequest,
  SendParticipationRequest,
} from '@tutum/hermes/bff/service_domains_enrollment';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { FORM_SETTING_OBJECT } from '@tutum/mvz/constant/form';
import { IPatientSignaturesForm } from './patient-signature-form/PatientSignatureForm';
import { handleUpdatePatientEnrollment } from './PatientEnrollmentForm.service';
import { IPrintForm } from './print-review-form/PrintReviewForm';
import { ISelectContractDoctorMessage } from './select-contract-doctor-form/SelectContractDoctorForm.reducer';
import { IRequiredTeCodeForm } from './te-code-form/TeCodeForm.reducer';

enum EnrollmentProgressFormStage {
  SelectContractDoctor = 1,
  SelectContractDoctorRequestCreateEnrollment = 2,
  PrintForms = 3,
  GetSignatures = 4,
  InputTeCode = 5,
}

export enum EnrollmentErrorCode {
  DefaultHpmError = 'HPM_SERVICE_ERROR',
}

enum EnrollmentFormStage {
  LoadingEnrollmentDetail = 1,
  OpenedForm = 2,
  Submitting = 3,
  OnConfirmingCloseForm = 4,
  ClosedForm = 5,
}

export interface IEnrollmentFormAction {
  type: string;
  payload?: IEnrollmentFormActionPayload;
}

export interface IEnrollmentFormActionPayload {
  patientId?: string;
  ikNumber?: number;
  patientDOB?: number;
  insuranceNumber?: string;

  contractId?: string;
  doctorId?: string;
  isChangingDoctor?: boolean;
  changeDoctorReason?: ChangeDoctorReason;
  checkParticipationStatus?: ParticipationStatus;
  formId?: string;
  isPrintedAll?: boolean;
  inputtedTeCode?: string;
  signatureName?: string;
  isSignatureChecked?: boolean;

  isOpen?: boolean;
  confirmCloseForm?: boolean;
  enrollmentProgressStage?: EnrollmentProgressFormStage;

  createPatientEnrollmentRequest?: CreatePatientEnrollmentRequest;
  enrollmentResponse?: PatientEnrollmentResponse;
  sendParticipationResult?: ISendParticipationResponse;
  extraEnrollResponse?: PatientEnrollmentResponse;
  enrollmentId?: string;
  requiredSignatureForms?: IPatientSignaturesForm[];
  doubleEnrollmentResponse?: {
    hzvResponse: ISendParticipationResponse;
    favResponse: ISendParticipationResponse;
  };
  isEnrollExtraFAV?: boolean;
  formSettings?: FORM_SETTING_OBJECT;
  errorMessage?: string;

  onShowToastSuccess?: (msg: string) => void;
  onShowToastFail?: (msg: string) => void;
}

export interface ISendParticipationResponse {
  patientEnrollment?: PatientEnrollmentResponse;
  errorMessages?: string[];
}

export interface IPatientEnrollmentInformation {
  patientId: string;
  ikNumber: number;
  patientDOB: number;
  insuranceNumber: string;
  enrollmentId?: string;
  isChangingDoctor: boolean;
  changeDoctorReason?: ChangeDoctorReason;
  contractId?: string;
  doctorId?: string;
  contractType?: ContractType;
  forms: IContractForm[];
  requiredTeCodeForms: IRequiredTeCodeForm[];
  printForms: IPrintForm[];
  requiredSignatureForms: IPatientSignaturesForm[];
  messages: string[];
  status: PatientEnrollmentStatus;
  hasExtraStatus: boolean;
  extraData: { [key: string]: string };
  type: EnrollmentType;
}

export interface IContractForm extends Form {
  isPrinted: boolean;
  signature1: boolean;
  signature1Required: boolean;
  signature1Showed: boolean;
  signature2: boolean;
  signature2Required: boolean;
  signature2Showed: boolean;
  teCode: string;
  teCodeRequired: boolean;
  teId: string;
  templateId: string;
  templateName: string;
  templatePath: string;
  type: FormType;
  inputtedTeCode?: string;
  formId: string;
}

export interface IToastMessage {
  message: string;
  intent: 'success' | 'danger';
  icon: 'info-sign' | 'error' | 'tick-circle';
}

export interface IEnrollmentModelState {
  patientEnrollmentInformation: IPatientEnrollmentInformation;
  extraEnrollmentInformation?: IPatientEnrollmentInformation;
  toastMessage: IToastMessage;

  hpmMessages: string[];

  currentProgressStage: EnrollmentProgressFormStage;
  savedEnrollmentProgressStage: EnrollmentProgressFormStage;
  currentFormStage: EnrollmentFormStage;
  latestResultCheckParticipation?: ParticipationStatus;
  requiredSignatureForms: IPatientSignaturesForm[];

  // handle request
  requestCreatePatientEnrollment: CreatePatientEnrollmentRequest;
  requestPrintForm: PrintFormRequest;
  requestSendParticipation: SendParticipationRequest;
  requestSaveOfflineEnrollment: SaveOfflineEnrollmentRequest;
  requestSendParticipationExtraEnroll?: SendParticipationRequest;
  contractDoctorErrorMessages: ISelectContractDoctorMessage;
  isFinishEnrollmentForm: boolean;
  t: IFixedNamespaceTFunction;
}

const handleTextToast = (
  contractType: ContractType,
  t: IFixedNamespaceTFunction
) => {
  switch (contractType) {
    case ContractType.ContractType_HouseDoctorCare:
      return t('hzvOnlineEnrollmentSubmitted');
    case ContractType.ContractType_SpecialistCare:
      return t('favOnlineEnrollmentSubmitted');
    default:
      return;
  }
};

const handleShowError = (
  formType: FormType,
  contractType: ContractType,
  t: IFixedNamespaceTFunction
) => {
  if (formType === FormType.FormType_Registration) {
    return t('errorRegistration');
  }
  if (formType === FormType.FormType_Participation) {
    if (contractType === ContractType.ContractType_HouseDoctorCare) {
      return t('errorSignatureParticipationHzV');
    }
    if (contractType === ContractType.ContractType_SpecialistCare) {
      return t('errorSignatureParticipationFAV');
    }
  }
};

const handleMapError = (
  requiredSignatureForms: IPatientSignaturesForm[],
  form: IContractForm,
  keyError: string,
  t: IFixedNamespaceTFunction
) => {
  return (
    requiredSignatureForms?.map((f) => {
      if (f.formId === form.formId) {
        return {
          ...f,
          [keyError]: handleShowError(f.type, f.contractType, t),
        };
      }
      return f;
    }) ?? []
  );
};

function reducer(state: IEnrollmentModelState, action: IEnrollmentFormAction) {
  const t = state.t;
  switch (action.type) {
    case 'ClearRequestCreatePatientEnrollment': {
      state.requestCreatePatientEnrollment = undefined!;
      state.patientEnrollmentInformation = {
        ...state.patientEnrollmentInformation,
        enrollmentId: undefined,
      };
      break;
    }
    case 'OnEnrollmentFormChanged': {
      const defaultEnrollment =
        action.payload?.enrollmentResponse;

      const isOpen = action.payload?.isOpen;
      const isChangingDoctor = action.payload?.isChangingDoctor;

      if (isOpen) {
        state.currentFormStage = EnrollmentFormStage.OpenedForm;
        state.patientEnrollmentInformation = {
          ...state.patientEnrollmentInformation,
          doctorId: undefined,
          contractId: undefined,
          changeDoctorReason: undefined,
          printForms: undefined!,
          forms: undefined!,
          requiredSignatureForms: undefined!,
          requiredTeCodeForms: undefined!,
          isChangingDoctor: !!isChangingDoctor,
        };
        if (defaultEnrollment) {
          state.currentFormStage = EnrollmentFormStage.LoadingEnrollmentDetail;
          state.patientEnrollmentInformation.enrollmentId =
            defaultEnrollment.id;

          // state.loadingDetailEnrollment = true;
          state.patientEnrollmentInformation = {
            ...state.patientEnrollmentInformation,
            ...defaultEnrollment,
            forms: undefined!,
            extraData: defaultEnrollment.extraData
              ? { ...defaultEnrollment.extraData }
              : undefined!,
            messages: defaultEnrollment.messages
              ? [...defaultEnrollment.messages]
              : [],
          };

          const data = initEnrollmentData(defaultEnrollment);
          state.patientEnrollmentInformation.forms = data[0];
          state.patientEnrollmentInformation.requiredTeCodeForms = data[1];
          state.patientEnrollmentInformation.requiredSignatureForms = data[2];
          state.patientEnrollmentInformation.printForms = data[3];

          // get current stage number of enrollment's detail.
          state.savedEnrollmentProgressStage = data[4];
          state.isFinishEnrollmentForm = data[1].every((form) => {
            return form.teCode === form.inputtedTeCode;
          });
        } else {
          state.currentProgressStage =
            EnrollmentProgressFormStage.SelectContractDoctor;
        }
      } else {
        state.currentFormStage = EnrollmentFormStage.ClosedForm;
      }
      break;
    }
    case 'OnPatientChanged': {
      const patientId = action.payload?.patientId;
      const patientDob = action.payload?.patientDOB;
      const ikNumber = action.payload?.ikNumber;
      const insuranceNumber = action.payload?.insuranceNumber;
      state.patientEnrollmentInformation = {
        ...state.patientEnrollmentInformation,
        patientId: patientId!,
        patientDOB: patientDob!,
        ikNumber: ikNumber!,
        insuranceNumber: insuranceNumber!,
      };
      break;
    }
    case 'OnSelectedContractChanged': {
      const selectedContractId = action.payload?.contractId;

      if (
        state.currentFormStage === EnrollmentFormStage.LoadingEnrollmentDetail
      ) {
        if (
          state.patientEnrollmentInformation.contractId !== selectedContractId
        ) {
          state.currentProgressStage =
            EnrollmentProgressFormStage.SelectContractDoctor;
          state.currentFormStage = EnrollmentFormStage.OpenedForm;
        }
      } else {
        state.currentProgressStage =
          EnrollmentProgressFormStage.SelectContractDoctor;
      }
      state.patientEnrollmentInformation = {
        ...state.patientEnrollmentInformation,
        contractId: selectedContractId,
      };
      break;
    }
    case 'OnSelectedDoctorChanged': {
      const selectedDoctorId = action.payload?.doctorId;
      state.patientEnrollmentInformation = {
        ...state.patientEnrollmentInformation,
        doctorId: selectedDoctorId,
      };

      if (
        state.currentFormStage ===
          EnrollmentFormStage.LoadingEnrollmentDetail &&
        state.patientEnrollmentInformation.doctorId !== selectedDoctorId
      ) {
        state.currentProgressStage =
          EnrollmentProgressFormStage.SelectContractDoctor;
        state.currentFormStage = EnrollmentFormStage.OpenedForm;
      }

      if (
        state.currentFormStage !== EnrollmentFormStage.LoadingEnrollmentDetail
      ) {
        state.patientEnrollmentInformation = {
          ...state.patientEnrollmentInformation,
          printForms: null!,
          forms: null!,
          requiredSignatureForms: null!,
          requiredTeCodeForms: null!,
        };
        state.currentProgressStage =
          EnrollmentProgressFormStage.SelectContractDoctor;
      }
      break;
    }

    case 'OnChangeDoctorReasonChanged': {
      const selectedChangeDoctorReason = action.payload?.changeDoctorReason;
      if (
        state.currentFormStage === EnrollmentFormStage.LoadingEnrollmentDetail
      ) {
        if (
          state.patientEnrollmentInformation.changeDoctorReason !==
          selectedChangeDoctorReason
        ) {
          state.currentProgressStage =
            EnrollmentProgressFormStage.SelectContractDoctor;
          state.currentFormStage = EnrollmentFormStage.OpenedForm;
        }
      }
      state.patientEnrollmentInformation.changeDoctorReason =
        selectedChangeDoctorReason;

      if (
        state.currentFormStage !==
          EnrollmentFormStage.LoadingEnrollmentDetail &&
        (state.patientEnrollmentInformation?.enrollmentId ||
          state.latestResultCheckParticipation ===
            ParticipationStatus.ParticipateStatus_EligibleToEnroll)
      ) {
        state.currentProgressStage =
          EnrollmentProgressFormStage.SelectContractDoctorRequestCreateEnrollment;
      }
      break;
    }
    case 'OnRequestingCreatePatientEnrollment': {
      const request = action.payload?.createPatientEnrollmentRequest;
      if (
        !request?.contractId ||
        !request.doctorId ||
        (request.isChangingDoctor && !request.changeDoctorReason)
      ) {
        state.currentProgressStage =
          EnrollmentProgressFormStage.SelectContractDoctor;
      } else {
        state.requestCreatePatientEnrollment = {
          ...request,
        };
      }
      break;
    }
    case 'OnCheckParticipationResponsed': {
      const checkParticipationStatus = action.payload?.checkParticipationStatus;
      if (
        checkParticipationStatus ===
        ParticipationStatus.ParticipateStatus_EligibleToEnroll
      ) {
        if (
          state.currentFormStage === EnrollmentFormStage.LoadingEnrollmentDetail
        ) {
          state.currentProgressStage = state.savedEnrollmentProgressStage;
          if (
            state.currentProgressStage ===
            EnrollmentProgressFormStage.GetSignatures
          ) {
            state.currentFormStage = EnrollmentFormStage.OpenedForm;
          }
        } else {
          state.currentProgressStage =
            EnrollmentProgressFormStage.SelectContractDoctorRequestCreateEnrollment;
        }
      }
      state.latestResultCheckParticipation = checkParticipationStatus;
      break;
    }
    case 'OnExtraEnrollResponse': {
      const result =
        action.payload?.extraEnrollResponse;
      if (!result) {
        state.extraEnrollmentInformation = undefined;
        break;
      }
      state.extraEnrollmentInformation = {
        ...state.extraEnrollmentInformation!,
        forms: [],
      };

      if (
        !state.extraEnrollmentInformation ||
        !state.extraEnrollmentInformation.enrollmentId
      ) {
        state.extraEnrollmentInformation = {
          ...state.extraEnrollmentInformation,
          contractId: result.contractId,
          doctorId: result.doctorId,
          patientId: result.patientId,
          enrollmentId: result.id,
          type: result.type,
        };
      }

      if (
        result.status ==
          PatientEnrollmentStatus.PatientEnrollmentStatus_Created ||
        result.status == PatientEnrollmentStatus.PatientEnrollmentStatus_Printed
      ) {
        const data = initEnrollmentData(result);
        state.extraEnrollmentInformation = {
          ...state.extraEnrollmentInformation,
          forms: data[0],
          requiredTeCodeForms: data[1],
          requiredSignatureForms: data[2],
          printForms: data[3],
        };
        // state.currentProgressStage = data[4];
      }
      break;
    }
    case 'OnCreatePatientEnrollmentResponsed': {
      if (
        state.currentProgressStage === EnrollmentProgressFormStage.GetSignatures
      ) {
        break;
      }

      state.patientEnrollmentInformation = {
        ...state.patientEnrollmentInformation,
        forms: [],
      };
      state.contractDoctorErrorMessages = {
        ...state.contractDoctorErrorMessages,
        contract: undefined,
        doctor: undefined,
      };
      const result =
        action.payload?.enrollmentResponse;

      state.currentProgressStage =
        EnrollmentProgressFormStage.SelectContractDoctor;

      if (
        !state.patientEnrollmentInformation ||
        !state.patientEnrollmentInformation.enrollmentId
      ) {
        state.patientEnrollmentInformation = {
          ...state.patientEnrollmentInformation,
          enrollmentId: result?.id,
          type: result?.type!,
        };
      }

      if (
        result?.status == PatientEnrollmentStatus.PatientEnrollmentStatus_Created
      ) {
        const data = initEnrollmentData(result!);
        state.patientEnrollmentInformation = {
          ...state.patientEnrollmentInformation,
          type: result?.type!,
          forms: data[0],
          requiredTeCodeForms: data[1],
          requiredSignatureForms: data[2],
          printForms: data[3],
        };
        state.currentProgressStage = data[4];
      } else if (
        result?.status == PatientEnrollmentStatus.PatientEnrollmentStatus_Faulty
      ) {
        if (isNotNullOrEmpty(result.messages)) {
          result.messages.forEach((value) => {
            if (
              value ==
              ErrorMessageValue.Error_PatientAlreadyParticipatingOnSameContract
            ) {
              state?.contractDoctorErrorMessages?.contract?.push(
                t('errorMessageAlreadyParticipating')
              );
            } else {
              state?.contractDoctorErrorMessages?.contract?.push(value);
            }
          });
        }
      }
      break;
    }
    case 'OnGetPatientEnrollmentResponsed': {
      if (
        state.currentProgressStage === EnrollmentProgressFormStage.GetSignatures
      ) {
        break;
      }
      const result =
        action.payload?.enrollmentResponse;
      const data = initEnrollmentData(result!);

      state.patientEnrollmentInformation = {
        ...state.patientEnrollmentInformation,
        forms: data[0],
        requiredTeCodeForms: data[1],
        requiredSignatureForms: data[2],
        printForms: data[3],
        type: result?.type!,
      };

      state.currentProgressStage =
        result?.status ===
        PatientEnrollmentStatus?.PatientEnrollmentStatus_Created
          ? EnrollmentProgressFormStage.PrintForms
          : EnrollmentProgressFormStage.GetSignatures;
      const finished = state.patientEnrollmentInformation.forms.every(
        (f) => f.teCodeRequired && f.inputtedTeCode === f.teCode
      );
      state.isFinishEnrollmentForm = finished;

      // if (
      //   state.currentProgressStage ===
      //     EnrollmentProgressFormStage.GetSignatures &&
      //   finished
      // ) {
      //   state.currentProgressStage = EnrollmentProgressFormStage.InputTeCode;
      // }

      break;
    }
    case 'OnFormPrinted': {
      state.patientEnrollmentInformation.printForms =
        state.patientEnrollmentInformation.printForms?.map((form) => {
          const printed = form.formId === action.payload?.formId || form.printed;
          return {
            ...form,
            printed,
          };
        });
      const formId = action.payload?.formId;
      const formSettings = action.payload?.formSettings;
      const indexPatientEnrollForm =
        state.patientEnrollmentInformation.forms.findIndex(
          (x) => x.formId === formId
        );
      if (indexPatientEnrollForm !== -1) {
        state.patientEnrollmentInformation.forms[indexPatientEnrollForm] = {
          ...state.patientEnrollmentInformation.forms[indexPatientEnrollForm],
          isPrinted: true,
          payload: JSON.stringify(formSettings),
        };
        state.patientEnrollmentInformation.forms = [
          ...state.patientEnrollmentInformation.forms,
        ];
        state.requestPrintForm = {
          enrollmentId: action.payload?.enrollmentId!,
          formType:
            state.patientEnrollmentInformation.forms[indexPatientEnrollForm]
              ?.type,
        };
      }

      if (state.extraEnrollmentInformation?.forms?.length) {
        const indexExtraEnrollForm =
          state.extraEnrollmentInformation?.forms?.findIndex(
            (x) => x.formId === formId
          );
        if (indexExtraEnrollForm !== -1) {
          state.extraEnrollmentInformation.forms[indexExtraEnrollForm] = {
            ...state.extraEnrollmentInformation.forms[indexExtraEnrollForm],
            isPrinted: true,
          };
          state.extraEnrollmentInformation.printForms =
            state.extraEnrollmentInformation.printForms?.map((form) => {
              const printed =
                form.formId === action.payload?.formId || form.printed;
              return { ...form, printed: printed };
            });
          state.extraEnrollmentInformation.forms = [
            ...state.extraEnrollmentInformation.forms,
          ];
          state.requestPrintForm = {
            enrollmentId: action.payload?.enrollmentId!,
            formType:
              state.extraEnrollmentInformation.forms[indexExtraEnrollForm]
                ?.type,
          };
        }
      }
      break;
    }
    case 'OnPrintFormResponsed': {
      const isPrintedAll = action.payload?.isPrintedAll;
      if (isPrintedAll) {
        state.currentProgressStage = EnrollmentProgressFormStage.GetSignatures;

        if (state.requiredSignatureForms.length > 0) {
          const isAllRequiredSignatureSigned =
            state.requiredSignatureForms.every((form) => {
              return (
                (!form.isSignature1Required || form.signature1) &&
                (!form.isSignature2Required || form.signature2)
              );
            });
          if (isAllRequiredSignatureSigned) {
            state.currentProgressStage =
              EnrollmentProgressFormStage.InputTeCode;
          }
        }
      }
      break;
    }
    case 'OnTeCodeChanged': {
      const formId = action.payload?.formId;
      const teCode = action.payload?.inputtedTeCode;
      const errorMessage = action.payload?.errorMessage;

      state.patientEnrollmentInformation.forms =
        state.patientEnrollmentInformation.forms?.map((item) => {
          if (item.formId === formId) {
            return {
              ...item,
              inputtedTeCode: teCode,
              errorMessage: errorMessage,
            };
          }
          return item;
        });

      const unfinished = state.patientEnrollmentInformation.forms.find(
        (f) => f.teCodeRequired && f.inputtedTeCode !== f.teCode
      );
      state.isFinishEnrollmentForm = !unfinished;

      // if (unfinished) {
      //   state.currentProgressStage = EnrollmentProgressFormStage.GetSignatures;
      // } else {
      //   state.currentProgressStage = EnrollmentProgressFormStage.InputTeCode;
      // }
      handleUpdatePatientEnrollment({
        enrollmentId: state.patientEnrollmentInformation.enrollmentId!,
        forms: state.patientEnrollmentInformation.forms,
      });
      break;
    }
    case 'OnSignatureChecked': {
      const formId = action.payload?.formId;
      const signatureName = action.payload?.signatureName;
      const checked = action.payload?.isSignatureChecked;

      const indexRequiredForm = state.requiredSignatureForms.findIndex(
        (x) => x.formId === formId
      );
      const index = state.patientEnrollmentInformation.forms.findIndex(
        (x) => x.formId === formId
      );
      const indexExtra =
        state.extraEnrollmentInformation?.forms?.findIndex(
          (x) => x.formId === formId
        ) ?? -1;

      state.requiredSignatureForms[indexRequiredForm] = {
        ...state.requiredSignatureForms[indexRequiredForm],
      };
      state.requiredSignatureForms = [...state.requiredSignatureForms];
      if (signatureName === 'signature1') {
        if (index !== -1) {
          state.patientEnrollmentInformation.forms[index].signature1 = !!checked;
        } else if (indexExtra !== -1 && state.extraEnrollmentInformation) {
          state.extraEnrollmentInformation.forms[indexExtra].signature1 =
            !!checked;
        }
        state.requiredSignatureForms[indexRequiredForm].signature1 = !!checked;
        if (checked) {
          state.requiredSignatureForms[
            indexRequiredForm
          ].errorMessageSignature1 = undefined!;
        }
      } else if (signatureName === 'signature2') {
        if (index !== -1) {
          state.patientEnrollmentInformation.forms[index].signature2 = !!checked;
        } else if (indexExtra !== -1 && state.extraEnrollmentInformation) {
          state.extraEnrollmentInformation.forms[indexExtra].signature2 =
            !!checked;
        }
        state.requiredSignatureForms[indexRequiredForm].signature2 = !!checked;
        if (checked) {
          state.requiredSignatureForms[
            indexRequiredForm
          ].errorMessageSignature2 = undefined!;
        }
      }
      const isAllRequiredSignatureSigned = state.requiredSignatureForms.every(
        (form) => {
          if (form.isSignature1Required && !form.signature1) return false;
          if (form.isSignature2Required && !form.signature2) return false;
          return true;
        }
      );
      if (isAllRequiredSignatureSigned)
        state.currentProgressStage = EnrollmentProgressFormStage.InputTeCode;
      else
        state.currentProgressStage = EnrollmentProgressFormStage.GetSignatures;

      handleUpdatePatientEnrollment({
        enrollmentId: state.patientEnrollmentInformation?.enrollmentId!,
        forms: state.patientEnrollmentInformation.forms,
      });
      break;
    }
    case 'SendParticipation': {
      // const isEnrollExtraFAV = action.payload?.isEnrollExtraFAV;
      let isAllSigned = true;

      const combineForms = state?.patientEnrollmentInformation?.forms ?? [];
      // if (isEnrollExtraFAV) {
      //   combineForms = uniqBy(
      //     combineForms.concat(state.extraEnrollmentInformation?.forms ?? []),
      //     ['teId', 'templateId', 'type']
      //   );
      // }

      // Validate signature
      combineForms.forEach((form) => {
        if (form.signature1Required && !form.signature1) {
          state.requiredSignatureForms = handleMapError(
            state.requiredSignatureForms,
            form,
            'errorMessageSignature1',
            t
          );
          isAllSigned = false;
        }
        if (form.signature2Required && !form.signature2) {
          state.requiredSignatureForms = handleMapError(
            state.requiredSignatureForms,
            form,
            'errorMessageSignature2',
            t
          );
          isAllSigned = false;
        }
      });

      if (!isAllSigned) {
        break;
      }

      state.currentFormStage = EnrollmentFormStage.Submitting;
      state.requestSendParticipation = {
        contractId: state.patientEnrollmentInformation.contractId!,
        doctorId: state.patientEnrollmentInformation.doctorId!,
        enrollmentId: state.patientEnrollmentInformation.enrollmentId!,
        patientId: state.patientEnrollmentInformation.patientId,
        forms: state.patientEnrollmentInformation.forms,
      };

      const hasExtraEnrollment =
        state.extraEnrollmentInformation?.contractId !=
          state.patientEnrollmentInformation?.contractId &&
        state.extraEnrollmentInformation;
      if (hasExtraEnrollment) {
        state.requestSendParticipationExtraEnroll = {
          contractId: state.extraEnrollmentInformation?.contractId!,
          doctorId: state.extraEnrollmentInformation?.doctorId!,
          enrollmentId: state.extraEnrollmentInformation?.enrollmentId!,
          patientId: state.extraEnrollmentInformation?.patientId!,
          forms: state.extraEnrollmentInformation?.forms!,
        };
      }

      break;
    }
    case 'SaveOfflineEnrollment': {
      state.currentFormStage = EnrollmentFormStage.Submitting;
      state.requestSaveOfflineEnrollment = {
        contractId: state.patientEnrollmentInformation?.contractId!,
        doctorId: state.patientEnrollmentInformation?.doctorId!,
        enrollmentId: state.patientEnrollmentInformation?.enrollmentId!,
        patientId: state.patientEnrollmentInformation.patientId,
      };
      break;
    }
    case 'OnSendParticipationResponsed': {
      const {
        sendParticipationResult: response,
        onShowToastSuccess,
        onShowToastFail,
      } = action.payload!;

      state.currentFormStage = EnrollmentFormStage.OpenedForm;
      const status = response?.patientEnrollment?.status;
      switch (status) {
        case PatientEnrollmentStatus.PatientEnrollmentStatus_Active:
        case PatientEnrollmentStatus.PatientEnrollmentStatus_Requested:
          const message = handleTextToast(
            response?.patientEnrollment?.contractType!,
            t
          );
          onShowToastSuccess?.(message!);
          break;
        case PatientEnrollmentStatus.PatientEnrollmentStatus_Printed:
          if (
            state.patientEnrollmentInformation.type ===
            EnrollmentType.EnrollmentType_OFFLINE
          ) {
            onShowToastSuccess?.(t('hzvOfflineEnrollmentSaved'));
          }
          break;
        default:
          if (isNotNullOrEmpty(response?.errorMessages!)) {
            const errorMessages = (response?.errorMessages || []).map((message) =>
              message != EnrollmentErrorCode.DefaultHpmError
                ? message
                : t('defaultMessageHpmError')
            );
            onShowToastFail?.(errorMessages.join(', '));
          } else {
            onShowToastFail?.(t('enrollError'));
          }
      }
      state.currentFormStage = EnrollmentFormStage.ClosedForm;
      break;
    }
    case 'OnDoubleEnrollmentResponsed': {
      const { doubleEnrollmentResponse, onShowToastSuccess, onShowToastFail } =
        action.payload!;
      const { hzvResponse, favResponse } = doubleEnrollmentResponse!;

      state.currentFormStage = EnrollmentFormStage.OpenedForm;
      let hasError = false;

      const handleResponse = (response: ISendParticipationResponse) => {
        if (
          response?.patientEnrollment?.status ===
          PatientEnrollmentStatus.PatientEnrollmentStatus_Requested
        ) {
          const message = handleTextToast(
            response?.patientEnrollment?.contractType,
            t
          );
          onShowToastSuccess?.(message!);
        } else {
          hasError = true;
          if (isNotNullOrEmpty(response?.errorMessages!)) {
            const errorMessages = (response.errorMessages || []).map((message) =>
              message != EnrollmentErrorCode.DefaultHpmError
                ? message
                : t('defaultMessageHpmError')
            );
            onShowToastFail?.(errorMessages.join(', '));
          } else {
            onShowToastFail?.(t('enrollError'));
          }
        }
      };
      handleResponse(hzvResponse);
      handleResponse(favResponse);

      if (hasError) {
        state.currentProgressStage = EnrollmentProgressFormStage.GetSignatures;
        state.currentFormStage = EnrollmentFormStage.ClosedForm;
        break;
      }
      state.currentFormStage = EnrollmentFormStage.ClosedForm;

      break;
    }
    case 'OnCloseForm': {
      state.currentFormStage = EnrollmentFormStage.OnConfirmingCloseForm;
      break;
    }
    case 'OnCloseFormConfirmed': {
      if (action.payload?.confirmCloseForm) {
        state.currentFormStage = EnrollmentFormStage.ClosedForm;
      } else {
        state.currentFormStage = EnrollmentFormStage.OpenedForm;
      }
      break;
    }
    case 'OnCurrentEnrollmentProgressStageChanged': {
      if (
        state.currentFormStage === EnrollmentFormStage.LoadingEnrollmentDetail
      ) {
        const currentStage = action.payload?.enrollmentProgressStage;
        if (
          IsEqualOrLargerThan(currentStage!, state.savedEnrollmentProgressStage)
        ) {
          state.currentFormStage = EnrollmentFormStage.OpenedForm;
        }
      }
      break;
    }
    case 'OnSetRequiredSignatureForms': {
      const { requiredSignatureForms } = action.payload!;
      state.requiredSignatureForms = requiredSignatureForms!;
      break;
    }
    case 'ResetHPMErrorMessage': {
      state.hpmMessages = null!;
      break;
    }
  }
  return { ...state };
}

function IsEqualOrLargerThan(
  currentStage: EnrollmentProgressFormStage,
  compareStage: EnrollmentProgressFormStage
): boolean {
  if (!currentStage || !compareStage) {
    return false;
  }
  return currentStage.valueOf() >= compareStage.valueOf();
}

export default {
  EnrollmentFormStage,
  EnrollmentProgressFormStage,
  reducer,
  IsEqualOrLargerThan,
};

// ---------------------------------------------------------- //

function initEnrollmentData(
  enrollment: PatientEnrollmentResponse
): [
  IContractForm[],
  IRequiredTeCodeForm[],
  IPatientSignaturesForm[],
  IPrintForm[],
  EnrollmentProgressFormStage,
] {
  const forms: IContractForm[] = [];
  const requiredTeCodeForms: IRequiredTeCodeForm[] = [];
  const requiredSignatureForms: IPatientSignaturesForm[] = [];
  const printForms: IPrintForm[] = [];
  let currentStage = EnrollmentProgressFormStage.SelectContractDoctor;

  enrollment.forms?.forEach((form) => {
    const newUUID: string = getUUID();
    const formView: IContractForm = {
      ...form,
      formId: newUUID,
    };
    forms.push(formView);
    // Repare data for teCode form
    if (form.teCodeRequired) {
      requiredTeCodeForms.push({
        formId: newUUID,
        errorMessage: form.errorMessage!,
        teCode: form.teCode,
        templateId: form.templateId,
        inputtedTeCode: form.inputtedTeCode,
      });
    }
    // repare data for get signature forms.
    if (form.signature1Required || form.signature2Required) {
      requiredSignatureForms.push({
        formId: newUUID,
        templateId: form.templateId,
        isSignature1Required: form.signature1Required,
        isSignature2Required: form.signature2Required,
        signature1: form.signature1,
        errorMessageSignature1: '',
        signature2: form.signature2,
        errorMessageSignature2: '',
        contractType: enrollment.contractType,
        type: form.type,
        contractId: enrollment.contractId,
        isSignature2Showed: form.signature2Showed,
      });
    }
    //Check if form has print template.
    if (form.templatePath) {
      printForms.push({
        formId: newUUID,
        templateId: form.templateId,
        templateName: form.templateName,
        templatePath: form.templatePath,
        type: form.type,
        printed: form.isPrinted,
        enrollmentId: enrollment.id,
        contractType: enrollment.contractType,
        contractId: enrollment.contractId,
        teId: undefined!,
      });
    }
  });

  switch (enrollment.status) {
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Created: {
      currentStage = EnrollmentProgressFormStage.PrintForms;
      break;
    }
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Printed: {
      if (enrollment.type === EnrollmentType.EnrollmentType_OFFLINE) {
        currentStage = EnrollmentProgressFormStage.GetSignatures;
      } else if (requiredSignatureForms.length > 0) {
        const isAllRequiredSignatureSigned = requiredSignatureForms.every(
          (form) => {
            return (
              (!form.isSignature1Required || form.signature1) &&
              (!form.isSignature2Required || form.signature2)
            );
          }
        );
        if (isAllRequiredSignatureSigned)
          currentStage = EnrollmentProgressFormStage.InputTeCode;
        else currentStage = EnrollmentProgressFormStage.GetSignatures;
      }
      break;
    }
    // Faulty only happen when submitting to HPM has error
    case PatientEnrollmentStatus.PatientEnrollmentStatus_Faulty: {
      currentStage = EnrollmentProgressFormStage.GetSignatures;
      break;
    }
  }

  return [
    forms,
    requiredTeCodeForms,
    requiredSignatureForms,
    printForms,
    currentStage,
  ];
}
