import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';

export interface IRequiredTeCodeForm {
  formId: string;
  teCode: string;
  inputtedTeCode?: string;
  templateId: string;
  errorMessage: string;
}

export interface ITeCodeFormState {
  forms: IRequiredTeCodeForm[] | undefined;
  isShowed: boolean;
  t: IFixedNamespaceTFunction;
}

export interface ITeCodeFormAction {
  type: string;
  payload: any;
}

export default function reducer(
  modelState: ITeCodeFormState,
  action: ITeCodeFormAction
) {
  const t = modelState.t;

  switch (action.type) {
    case 'OnFormsChanged': {
      modelState.forms = action.payload;
      break;
    }
    case 'TECodeInputChanged': {
      const formId: string = action.payload.formId;
      const inputTeCode: string = action.payload.teCode;
      const form = modelState.forms?.find((f) => f.formId === formId)!;
      form.inputtedTeCode = inputTeCode;
      if (inputTeCode != form.teCode) {
        form.errorMessage = t('incorrectInputtedTeCode');
      } else {
        form.errorMessage = '';
      }
      action.payload.onInputTeCodesChanged(
        formId,
        inputTeCode,
        form.errorMessage
      );
      break;
    }
    case 'ToggleForm': {
      modelState.isShowed = action.payload;
    }
  }
  return { ...modelState };
}
