import React, { useReducer, useEffect } from 'react';
import {
  FormGroup,
  Intent,
  Classes,
  NumericInput,
  Label,
} from '@tutum/design-system/components/Core';
import { Flex, Box, H3 } from '@tutum/design-system/components';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { isEmpty } from 'lodash';
import CircleNumber from '../CircleNumber';
import teCodeFormReducer, { ITeCodeFormState } from './TeCodeForm.reducer';

export interface ITeCodeFormProps {
  contractForms: RequiredTeCodeForm[];
  displayStageNumber: number;
  isShowed: boolean;
  isShowForm?: boolean;
  onInputTeCodesChanged: (
    formId: string,
    teCode: string,
    errorMessage: string
  ) => void;
}
export interface RequiredTeCodeForm {
  formId: string;
  teCode: string;
  inputtedTeCode?: string;
  templateId: string;
  errorMessage: string;
}

const TeCodeForm: React.ComponentType<ITeCodeFormProps> = React.memo(
  ({
    isShowed,
    isShowForm = true,
    displayStageNumber,
    contractForms,
    onInputTeCodesChanged,
    t,
  }: ITeCodeFormProps &
    II18nFixedNamespace<keyof typeof PatientManagementI18n.TeCodeForm>) => {
    const initState: ITeCodeFormState = {
      forms: undefined,
      isShowed: isShowed,
      t,
    };
    const [modelState, dispatch] = useReducer(teCodeFormReducer, initState);

    useEffect(() => {
      if (!contractForms) {
        return;
      }
      dispatch({
        type: 'OnFormsChanged',
        payload: contractForms,
      });
    }, [contractForms]);

    useEffect(() => {
      dispatch({
        type: 'ToggleForm',
        payload: isShowed,
      });
    }, [isShowed]);

    return (
      <>
        {modelState.isShowed && (
          <Flex auto column className="panel-tecode">
            <H3 className="label-section-form">
              <CircleNumber showedText={displayStageNumber.toString()} />
              {t('enterTeCodeLabel')}
            </H3>
            {isShowForm &&
              modelState.forms != null &&
              modelState.forms.map((form) => {
                return (
                  <Box key={form.formId} auto align={'center'}>
                    <FormGroup
                      helperText={form.errorMessage}
                      className={
                        form.errorMessage != '' ? Classes.INTENT_DANGER : ''
                      }
                    >
                      <NumericInput
                        value={form.inputtedTeCode ? form.inputtedTeCode : ''}
                        intent={
                          !form.errorMessage ? Intent.NONE : Intent.DANGER
                        }
                        className="tecode-input"
                        maxLength={4}
                        placeholder="_ _ _ _"
                        allowNumericCharactersOnly={true}
                        fill
                        buttonPosition={'none'}
                        onValueChange={(_: number, valueAsString: string) => {
                          dispatch({
                            type: 'TECodeInputChanged',
                            payload: {
                              formId: form.formId,
                              teCode: valueAsString,
                              onInputTeCodesChanged,
                            },
                          });
                        }}
                      />
                    </FormGroup>
                  </Box>
                );
              })}
            {isShowForm && !isEmpty(modelState.forms) && (
              <Label>{t('teCodeContent')}</Label>
            )}
          </Flex>
        )}
      </>
    );
  }
);

export default I18n.withTranslation(TeCodeForm, {
  namespace: 'PatientManagement',
  nestedTrans: 'TeCodeForm',
});
