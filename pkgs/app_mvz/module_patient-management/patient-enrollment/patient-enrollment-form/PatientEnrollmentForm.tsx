import {
  BodyTextM,
  Box,
  Flex,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import {
  Button,
  Classes,
  Dialog,
  FormGroup,
  Intent,
} from '@tutum/design-system/components/Core';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import {
  getCssClass,
  isNotNullOrEmpty,
} from '@tutum/design-system/infrastructure/utils';
import { ContractType } from '@tutum/hermes/bff/common';
import { prescribe } from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { printPlainPdf } from '@tutum/hermes/bff/legacy/app_mvz_form';
import {
  PatientProfileResponse,
  useQueryGetPatientFormProfile,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { EnrollmentType } from '@tutum/hermes/bff/legacy/enrollment_common';
import { EnrollmentPrintFormStatus } from '@tutum/hermes/bff/legacy/repo_encounter';
import { SaveOfflineEnrollmentRequest } from '@tutum/hermes/bff/legacy/service_domains_enrollment';
import { EncounterCase } from '@tutum/hermes/bff/repo_encounter';
import {
  ChangeDoctorReason,
  CreatePatientEnrollmentRequest,
  FormType,
  ParticipationStatus,
  PatientEnrollmentResponse,
  PreviewFormRequest,
  PrintFormRequest,
  SendParticipationRequest,
} from '@tutum/hermes/bff/service_domains_enrollment';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import formUtil from '@tutum/infrastructure/utils/form.util';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';
import {
  FORM_SETTING_OBJECT,
  FORM_SETTING_TYPE,
} from '@tutum/mvz/constant/form';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import {
  FormTypeSetting,
  printSettingStore as printSettingsStore,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import { useMusterFormDialogStore } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { patientHeaderKey } from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import PrintFormV2 from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-form/print-form-v2/PrintFormV2';
import SaveOfflineForm from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-form/save-offline-form/SaveOfflineForm';
import PrinterService from '@tutum/mvz/services/printer.service';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import React, {
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';
import { patientFileStore } from '../../patient-file/PatientFile.store';
import { timelineActions } from '../../patient-file/timeline/Timeline.store';
import { IContractDoctors } from '../../types/contract.type';
import {
  enrollmentActions,
  useEnrollmentStore,
} from '../patient-enrollment-widget/PatientEnrollmentWidget.store';
import ErrorMessageList from './ErrorMessageList';
import PatientEnrollmentFormHook from './PatientEnrollmentForm.hook';
import PatientEnrollmentFormReducer, {
  IEnrollmentModelState,
  IPatientEnrollmentInformation,
} from './PatientEnrollmentForm.reducer';
import {
  PatientEnrollmentFormAction,
  patientEnrollmentActions,
  usePatientEnrollmentFormStore,
} from './PatientEnrollmentForm.store';
import PatientSignaturesForm from './patient-signature-form/PatientSignatureForm';
import { IPrintForm } from './print-review-form/PrintReviewForm';
import SelectContractDoctorForm from './select-contract-doctor-form/SelectContractDoctorForm.styled';
import TeCodeForm from './te-code-form/TeCodeForm';
import { DateOfBirth } from '@tutum/hermes/bff/legacy/patient_profile_common';

export interface IPatientEnrollmentFormProps {
  className?: string;
  listContractDoctors: IContractDoctors[];
  excludedContractIds?: string[];
  defaultSelectedContractId?: string;
  userProfile?: IEmployeeProfile;
  isOpen: boolean;
  isChangingDoctor?: boolean;
  enrollment?: PatientEnrollmentResponse;
  isHzvButton?: boolean;
  onClose: () => void;
  onCreateSchein?: () => void;
  onReloadLoadListContracts?: () => void;
  submitEnrollmentCallback?: () => void;
}

const PatientEnrollmentForm = React.memo(
  ({
    listContractDoctors,

    className,
    isOpen,
    onClose,
    isChangingDoctor,
    enrollment,
    defaultSelectedContractId,
    excludedContractIds,
    isHzvButton,
    t,
    onCreateSchein,
    onReloadLoadListContracts,
    submitEnrollmentCallback,
  }: IPatientEnrollmentFormProps &
    II18nFixedNamespace<
      keyof typeof PatientManagementI18n.PatientEnrollmentForm
    >) => {
    const {
      patientManagement: { patient },
      patientManagement,
      setGetPatientParticipationResponse,
    } = useContext(PatientManagementContext.instance);
    const musterFormDialogStore = useMusterFormDialogStore();
    const globalContext = useContext(GlobalContext.instance);
    const doctorList = globalContext.useGetDoctorList();

    const store = usePatientEnrollmentFormStore();

    const { favEnrollmentState } = useEnrollmentStore();
    const [modelState, dispatch] = useReducer(
      PatientEnrollmentFormReducer.reducer,
      {
        patientEnrollmentInformation: {} as IPatientEnrollmentInformation,
        hpmMessages: undefined!,
        currentFormStage: undefined!,
        savedEnrollmentProgressStage: undefined!,
        currentProgressStage: undefined!,
        requestCreatePatientEnrollment: undefined!,
        requestPrintForm: undefined!,
        requestSendParticipation: undefined!,
        requestSaveOfflineEnrollment: undefined!,
        toastMessage: undefined!,
        requiredSignatureForms: [],
        contractDoctorErrorMessages: {
          changeDoctor: undefined,
          contract: undefined,
          doctor: undefined,
          noAvailableContracts: undefined,
        },
        isFinishEnrollmentForm: false,
        t,
      },
      init
    );

    //#region use state
    const [
      createPatientEnrollmentModelRequest,
      setCreatePatientEnrollmentModelRequest,
    ] = useState<CreatePatientEnrollmentRequest | undefined>(undefined);
    const [
      getPatientEnrollmentModelRequest,
      setGetPatientEnrollmentModelRequest,
    ] = useState<CreatePatientEnrollmentRequest | undefined>(undefined);
    const [extraEnrollRequest, setExtraEnroll] = useState<
      CreatePatientEnrollmentRequest | undefined
    >(undefined);

    const [sendParticipationRequest, setSendParticipationRequest] = useState<
      SendParticipationRequest | undefined
    >(undefined);
    const [saveOfflineEnrollmentRequest, setSaveOfflineEnrollmentRequest] =
      useState<SaveOfflineEnrollmentRequest | undefined>(undefined);
    const [
      sendParticipationExtraEnrollRequest,
      setSendParticipationExtraEnrollRequest,
    ] = useState<SendParticipationRequest | undefined>(undefined);
    const [isFormPrint, setFormPrint] = useState<boolean>(false);

    //#region use hook call apis
    const createPatientEnrollmentResponse =
      PatientEnrollmentFormHook.useHookCreatePatientEnrollemnt(
        createPatientEnrollmentModelRequest
      );

    const extraEnrollResponse =
      PatientEnrollmentFormHook.useHookCreatePatientEnrollemnt(
        extraEnrollRequest
      );

    const getPatientEnrollmentResponse =
      PatientEnrollmentFormHook.useHookGetPatientEnrollemnt(
        getPatientEnrollmentModelRequest
      );

    const sendParticipationResponse =
      PatientEnrollmentFormHook.useHookSendParticipation(
        sendParticipationRequest,
        patientFileStore.activeInsurance,
        patientManagement,
        setGetPatientParticipationResponse,
        onReloadLoadListContracts
      );

    const sendParticipationExtraEnrollResponse =
      PatientEnrollmentFormHook.useHookSendParticipation(
        sendParticipationExtraEnrollRequest,
        patientFileStore.activeInsurance,
        patientManagement,
        setGetPatientParticipationResponse,
        onReloadLoadListContracts
      );

    const saveOfflineEnrollmentResponse =
      PatientEnrollmentFormHook.useHookSaveOfflineEnrollment(
        saveOfflineEnrollmentRequest,
        patientFileStore.activeInsurance,
        patientManagement,
        setGetPatientParticipationResponse,
        onReloadLoadListContracts
      );

    const [printFormModelRequest, setPrintFormModelRequest] = useState<
      PrintFormRequest | undefined
    >(undefined);

    const printFormResponse = PatientEnrollmentFormHook.useHookPrintForm(
      printFormModelRequest
    );

    const { data: dataPatientHeader } = useQueryGetPatientFormProfile(
      {
        patientID: patient?.id!,
        doctorID: modelState.patientEnrollmentInformation?.doctorId!,
        scheinId: patientFileStore.schein.activatedSchein?.scheinId,
      },
      {
        select: (res) => res.data.profile,
        enabled: !!modelState.patientEnrollmentInformation?.doctorId,
      }
    );

    //#region handler
    const handlePrescribe = async (
      enrollmentId: string,
      formType: string,
      status: EnrollmentPrintFormStatus,
      formSettings: Nullable<FORM_SETTING_OBJECT>,
      formName: string,
      formTitle: string
    ) => {
      await prescribe({
        enrollmentId: enrollmentId,
        formType: formType as FormType,
        printedStatus: status,
        payloadForm: formSettings ? JSON.stringify(formSettings) : '{}',
        formName,
        formTitle,
      });
      timelineActions.setHasTimelineEntry(true);
    };

    const handlePrint = async (
      previewFormRequest: PreviewFormRequest,
      formSettings: FORM_SETTING_OBJECT,
      formPath: string,
      formTitle: string
    ) => {
      let cloneData = cloneDeep(formSettings);

      cloneData = Object.keys(cloneData).reduce(
        (
          newData: {
            [x: string]: FORM_SETTING_TYPE;
          },
          key
        ) => {
          const matchedKey = patientHeaderKey.find((item) =>
            key.includes(item)
          );

          if (matchedKey) {
            const valueMapping =
              musterFormDialogStore.patientInfoMap?.[matchedKey] ||
              dataPatientHeader?.[matchedKey] ||
              cloneData[matchedKey];
            newData = {
              ...newData,
              [key]: valueMapping,
            };
          } else {
            const value = cloneData[key];
            newData = {
              ...newData,
              [key]:
                newData[key] || (key.includes('date_prescribe') && !value)
                  ? +datetimeUtil.date()
                  : value,
            };
          }

          return newData;
        },
        {}
      );

      // handle print te code
      const insuranceNumber =
        modelState.patientEnrollmentInformation.insuranceNumber?.slice(3, 7);
      const lastDigit = +insuranceNumber.slice(-1);
      const teCode = isOnlineEnrollment
        ? ((+lastDigit % 2 === 0 ? +insuranceNumber : +insuranceNumber + 1) / 2)
            .toString()
            .padStart(4, '0')
        : '';
      cloneData['textbox_te_code'] = teCode;
      cloneData['textbox_te_code_0'] = teCode;
      cloneData['textbox_te_code_1'] = teCode;
      cloneData['textbox_te_code_2'] = teCode;
      cloneData['textbox_te_code_3'] = teCode;
      cloneData['textbox_te_code_5'] = teCode;

      // handle qr data
      const contract = await webWorkerServices.getContractById(
        modelState.patientEnrollmentInformation.contractId!
      );
      cloneData['qrdata'] = getQRString(
        patient,
        selectedDoctor,
        !!cloneData['checkbox_kranken'],
        contract?.identification || ''
      );

      const currentFormSetting = JSON.stringify(cloneDeep(cloneData));
      const formName = formPath
        .split(/Formulare\/|Dokumente\//)[1]
        .split('.pdf')[0]; // get form name to print
      const resp = await printPlainPdf({
        formSetting: currentFormSetting,
        formName,
        treatmentDoctorId: selectedDoctor?.id,
        contractId: modelState.patientEnrollmentInformation.contractId,
        isRemoveBackground: isFormPrint,
      });
      const getPdfUrl = async () => {
        return resp.data.formUrl;
      };
      const printSuccess = async () => {
        alertSuccessfully(t('printRequestSent'));
        await handlePrescribe(
          previewFormRequest.enrollmentId,
          previewFormRequest.formType,
          EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Printed_Created,
          cloneData,
          formName,
          formTitle
        );
      };
      const printFailure = () => {
        handlePrescribe(
          previewFormRequest.enrollmentId,
          previewFormRequest.formType,
          EnrollmentPrintFormStatus.EnrollmentPrintFormStatus_Printed_Failed,
          cloneData,
          formName,
          formTitle
        );
      };

      const callBackError = () => {
        printFailure();
      };

      await PrinterService.initAndPrint(
        'enrollment_document',
        getPdfUrl,
        {
          printSuccess,
          printFailure,
        },
        undefined,
        callBackError
      );
    };

    //#region Handle callback function for reducer.
    useEffect(() => {
      patientEnrollmentActions.setPatientEnrollmentDispatch(dispatch);
    }, []);

    useEffect(() => {
      dispatch({
        type: 'OnPatientChanged',
        payload: {
          patientId: patient?.id,
          ikNumber: patientFileStore.activeInsurance?.ikNumber,
          insuranceNumber: patientFileStore.activeInsurance?.insuranceNumber,
          patientDOB: patient?.dateOfBirth,
        },
      });
    }, [
      patient?.dateOfBirth,
      patient?.id,
      patientFileStore.activeInsurance?.ikNumber,
      patientFileStore.activeInsurance?.insuranceNumber,
    ]);

    useEffect(() => {
      if (!createPatientEnrollmentResponse) {
        return;
      }

      dispatch({
        type: 'OnCreatePatientEnrollmentResponsed',
        payload: {
          enrollmentResponse: createPatientEnrollmentResponse,
        },
      });
      const firstExtraEnrollments =
        createPatientEnrollmentResponse?.extraEnrollments?.[0];
      if (
        !isHzvButton ||
        !store.isEnrollExtraFAV ||
        Boolean(extraEnrollRequest) ||
        firstExtraEnrollments?.type !== ContractType.ContractType_SpecialistCare
      ) {
        return;
      }
      setExtraEnroll({
        ...modelState.requestCreatePatientEnrollment,
        contractId: firstExtraEnrollments?.contractId,
      });
    }, [createPatientEnrollmentResponse, store.isEnrollExtraFAV, isHzvButton]);

    useEffect(() => {
      if (!getPatientEnrollmentResponse) {
        return;
      }
      dispatch({
        type: 'OnGetPatientEnrollmentResponsed',
        payload: {
          enrollmentResponse: getPatientEnrollmentResponse,
        },
      });
    }, [getPatientEnrollmentResponse, store.isEnrollExtraFAV, isHzvButton]);

    useEffect(() => {
      if (!extraEnrollResponse) {
        return;
      }
      enrollmentActions.setCurrentFavEnrollment(extraEnrollResponse);
    }, [extraEnrollResponse]);

    useEffect(() => {
      PatientEnrollmentFormAction.setIsEnrollExtraFAV(
        Boolean(favEnrollmentState?.currentEnrollment)
      );
      if (isOpen) {
        dispatch({
          type: 'OnExtraEnrollResponse',
          payload: {
            extraEnrollResponse: favEnrollmentState?.currentEnrollment,
          },
        });
      }
    }, [favEnrollmentState?.currentEnrollment, isOpen]);

    useEffect(() => {
      if (isOpen) {
        dispatch({
          type: 'OnEnrollmentFormChanged',
          payload: {
            isOpen: isOpen,
            enrollmentResponse: enrollment,
            isChangingDoctor: isChangingDoctor,
          },
        });
      }
    }, [enrollment, isChangingDoctor, isOpen]);

    useEffect(() => {
      if (!modelState.requestSendParticipation) {
        return;
      }
      setSendParticipationRequest({
        ...modelState.requestSendParticipation,
      });
    }, [modelState.requestSendParticipation]);

    useEffect(() => {
      if (!modelState.requestSaveOfflineEnrollment) {
        return;
      }
      setSaveOfflineEnrollmentRequest({
        ...modelState.requestSaveOfflineEnrollment,
      });
    }, [modelState.requestSaveOfflineEnrollment]);

    useEffect(() => {
      if (
        !modelState.requestSendParticipationExtraEnroll ||
        !store.isEnrollExtraFAV
      ) {
        return;
      }
      setSendParticipationExtraEnrollRequest({
        ...modelState.requestSendParticipationExtraEnroll,
      });
    }, [
      modelState.requestSendParticipationExtraEnroll,
      store.isEnrollExtraFAV,
    ]);

    useEffect(() => {
      if (!saveOfflineEnrollmentResponse) return;

      dispatch({
        type: 'OnSendParticipationResponsed',
        payload: {
          sendParticipationResult: saveOfflineEnrollmentResponse,
          onShowToastSuccess: (msg) => alertSuccessfully(msg),
          onShowToastFail: (msg) => alertError(msg),
        },
      });
    }, [saveOfflineEnrollmentResponse]);

    useEffect(() => {
      if (!sendParticipationResponse) {
        return;
      }
      if (
        !sendParticipationExtraEnrollRequest ||
        !sendParticipationExtraEnrollRequest.forms.length
      ) {
        dispatch({
          type: 'OnSendParticipationResponsed',
          payload: {
            sendParticipationResult: sendParticipationResponse,
            onShowToastSuccess: (msg) => alertSuccessfully(msg),
            onShowToastFail: (msg) => alertError(msg),
          },
        });
        submitEnrollmentCallback?.();
        return;
      }
      if (!sendParticipationExtraEnrollResponse) {
        return;
      }
      dispatch({
        type: 'OnDoubleEnrollmentResponsed',
        payload: {
          doubleEnrollmentResponse: {
            hzvResponse: sendParticipationResponse,
            favResponse: sendParticipationExtraEnrollResponse,
          },
          onShowToastSuccess: (msg) => alertSuccessfully(msg),
          onShowToastFail: (msg) => alertError(msg),
        },
      });
      if (
        sendParticipationResponse.errorMessages ||
        sendParticipationExtraEnrollResponse.errorMessages
      ) {
        PatientEnrollmentFormAction.setEnrollmentHpmError(
          sendParticipationResponse.errorMessages?.[0] ||
            sendParticipationExtraEnrollResponse.errorMessages?.[0] ||
            '',
          t
        );
      }
    }, [
      sendParticipationResponse,
      sendParticipationExtraEnrollRequest,
      sendParticipationExtraEnrollResponse,
    ]);

    useEffect(() => {
      if (!modelState.requestPrintForm) {
        return;
      }
      setPrintFormModelRequest({
        ...modelState.requestPrintForm,
      });
    }, [modelState.requestPrintForm]);

    useEffect(() => {
      if (!printFormResponse) {
        return;
      }
      dispatch({
        type: 'OnPrintFormResponsed',
        payload: {
          isPrintedAll: printFormResponse.printedAll,
        },
      });
    }, [printFormResponse]);

    useEffect(() => {
      if (!modelState.requestCreatePatientEnrollment) return;
      if (!modelState.requestCreatePatientEnrollment.enrollmentId) {
        setCreatePatientEnrollmentModelRequest({
          ...modelState.requestCreatePatientEnrollment,
        });
      } else {
        setGetPatientEnrollmentModelRequest({
          ...modelState.requestCreatePatientEnrollment,
        });
      }
    }, [modelState.requestCreatePatientEnrollment]);

    useEffect(() => {
      dispatch({
        type: 'OnCurrentEnrollmentProgressStageChanged',
        payload: {
          enrollmentProgressStage: modelState.currentProgressStage,
        },
      });

      if (
        modelState.currentProgressStage ===
        PatientEnrollmentFormReducer.EnrollmentProgressFormStage
          .SelectContractDoctorRequestCreateEnrollment
      ) {
        dispatch({
          type: 'OnRequestingCreatePatientEnrollment',
          payload: {
            createPatientEnrollmentRequest: {
              enrollmentId:
                modelState.patientEnrollmentInformation.enrollmentId,
              patientId: modelState.patientEnrollmentInformation.patientId,
              doctorId: modelState.patientEnrollmentInformation.doctorId!,
              ikNumber: modelState.patientEnrollmentInformation.ikNumber,
              contractId: modelState.patientEnrollmentInformation.contractId!,
              insuranceNumber:
                modelState.patientEnrollmentInformation.insuranceNumber,
              timeZoneLocation:
                Intl.DateTimeFormat().resolvedOptions().timeZone,
              isChangingDoctor:
                modelState.patientEnrollmentInformation.isChangingDoctor,
              changeDoctorReason:
                modelState.patientEnrollmentInformation.changeDoctorReason,
            },
          },
        });
      }
    }, [modelState.currentProgressStage]);

    useEffect(() => {
      if (!modelState || !modelState.toastMessage) {
        return;
      }
      const message = modelState.toastMessage.message;
      if (modelState.toastMessage.intent === 'danger') {
        alertError(message);
      }

      if (modelState.toastMessage.intent === 'success') {
        alertSuccessfully(message);
      }
    }, [modelState.toastMessage]);

    useEffect(() => {
      if (
        modelState.currentFormStage ===
        PatientEnrollmentFormReducer.EnrollmentFormStage.ClosedForm
      ) {
        onClose();
      }
    }, [modelState.currentFormStage]);

    useEffect(() => {
      const patientEnrollSignatureForms =
        modelState.patientEnrollmentInformation?.requiredSignatureForms ?? [];

      const getExtraEnrollSignatureForms = () => {
        if (!isHzvButton) return [];
        const printedExtraIds =
          modelState.extraEnrollmentInformation?.printForms
            ?.filter((form) => form.printed)
            ?.map((form) => form.formId);

        return (
          modelState.extraEnrollmentInformation?.requiredSignatureForms?.filter(
            (form) => printedExtraIds?.includes(form.formId)
          ) ?? []
        );
      };
      const extraEnrollSignatureForms = getExtraEnrollSignatureForms();

      dispatch({
        type: 'OnSetRequiredSignatureForms',
        payload: {
          requiredSignatureForms: [
            ...patientEnrollSignatureForms,
            ...extraEnrollSignatureForms,
          ],
        },
      });
    }, [
      modelState.patientEnrollmentInformation?.requiredSignatureForms,
      modelState.extraEnrollmentInformation?.requiredSignatureForms,
      modelState.extraEnrollmentInformation?.printForms,
    ]);

    useEffect(() => {
      if (!enrollment) {
        dispatch({
          type: 'ClearRequestCreatePatientEnrollment',
        });
        setGetPatientEnrollmentModelRequest(undefined);
        setCreatePatientEnrollmentModelRequest(undefined);
      }
    }, [enrollment]);

    //#endregion

    // TODO: implement call print with QZ tray here
    async function onPrintedForm(
      formInfo: IPrintForm,
      formSettings?: FORM_SETTING_OBJECT
    ) {
      const { formId, enrollmentId, type, templatePath, templateName } =
        formInfo;

      // NOTE trigger printing flow
      handlePrint(
        {
          enrollmentId,
          formType: type,
        },
        formSettings!,
        templatePath,
        templateName
      );

      dispatch({
        type: 'OnFormPrinted',
        payload: {
          formId,
          enrollmentId,
          formSettings,
        },
      });
    }

    const printForms = useMemo(() => {
      const patientEnrollPrintForms =
        modelState.patientEnrollmentInformation?.printForms ?? [];
      if (!isHzvButton)
        return patientEnrollPrintForms.map((form) => {
          const teId =
            modelState.patientEnrollmentInformation?.forms?.find(
              (f) => f.formId === form.formId
            )?.teId || '';

          return {
            ...form,
            teId,
          };
        });
      const extraEnrollPrintForms =
        modelState.extraEnrollmentInformation?.printForms ?? [];

      return [...patientEnrollPrintForms, ...extraEnrollPrintForms].map(
        (form) => {
          const teId =
            modelState.patientEnrollmentInformation?.forms?.find(
              (f) => f.formId === form.formId
            )?.teId || '';

          return {
            ...form,
            teId,
          };
        }
      );
    }, [
      modelState.patientEnrollmentInformation?.printForms,
      modelState.extraEnrollmentInformation?.printForms,
      modelState.patientEnrollmentInformation?.forms,
    ]);

    useEffect(() => {
      PatientEnrollmentFormAction.setFormsToPrint(printForms ?? []);
    }, [printForms]);

    const isShowFavButton = Boolean(
      favEnrollmentState?.enrollmentActions?.length
    );

    const contractForms =
      modelState.patientEnrollmentInformation?.requiredTeCodeForms;

    // const shouldAllowSubmit = (state: IEnrollmentModelState) => {
    //   const isPrintMainContract = state.patientEnrollmentInformation?.forms
    //     ?.filter((form) => form.teCodeRequired)
    //     ?.every((f) => f.isPrinted);

    //   if (store.isEnrollExtraFAV) {
    //     const isPrintExtraContract = state.extraEnrollmentInformation
    //       ? state.extraEnrollmentInformation?.forms?.every((f) => f.isPrinted)
    //       : true;
    //     return isPrintExtraContract && isPrintMainContract;
    //   }
    //   return isPrintMainContract;
    // };

    const shouldAllowSubmit = (state: IEnrollmentModelState) => {
      if (isOnlineEnrollment) return state.isFinishEnrollmentForm;

      return PatientEnrollmentFormReducer.IsEqualOrLargerThan(
        state.currentProgressStage,
        PatientEnrollmentFormReducer.EnrollmentProgressFormStage.GetSignatures
      );
    };

    const onHandleSetCheckFAV = (isCheck: boolean) => {
      PatientEnrollmentFormAction.setIsEnrollExtraFAV(isCheck);
    };

    const formSetting = useMemo(() => {
      return printSettingsStore.formsSetting.find(
        (form) => form.formId === musterFormDialogStore.currentFormName
      );
    }, [musterFormDialogStore.currentFormName]);

    const selectedDoctor = useMemo(() => {
      return doctorList.find(
        (doctor) =>
          doctor.id === modelState.patientEnrollmentInformation?.doctorId
      );
    }, [doctorList, modelState.patientEnrollmentInformation?.doctorId]);

    const progressSaveOn =
      modelState.patientEnrollmentInformation?.['updatedDate'] ||
      modelState.patientEnrollmentInformation?.['createdDate'];

    const isOnlineEnrollment =
      modelState.patientEnrollmentInformation.type ===
      EnrollmentType.EnrollmentType_ONLINE;

    const handleSubmitEnrollment = () => {
      if (
        modelState.currentFormStage ===
        PatientEnrollmentFormReducer.EnrollmentFormStage.Submitting
      ) {
        return;
      }
      dispatch({
        type: 'SendParticipation',
        payload: {
          isEnrollExtraFAV: store.isEnrollExtraFAV,
        },
      });
    };
    const handleSaveOfflineEnrollment = () => {
      dispatch({
        type: 'SaveOfflineEnrollment',
      });
    };

    const handleSubmitButtonClick = () => {
      if (isOnlineEnrollment) handleSubmitEnrollment();
      else handleSaveOfflineEnrollment();
    };

    return (
      <>
        <Dialog
          className={getCssClass(
            'bp5-dialog-fullscreen',
            'bp5-dialog-content-scrollable',
            className
          )}
          title={formUtil.getFullName(
            patient?.patientInfo.personalInfo.title,
            patient?.patientInfo.personalInfo.intendWord,
            patient?.patientInfo.personalInfo.lastName || '',
            patient?.patientInfo.personalInfo.firstName || ''
          )}
          isOpen={isOpen}
          onClose={() => {
            dispatch({
              type: 'OnCloseForm',
              payload: undefined,
            });
          }}
          canOutsideClickClose={false}
        >
          <Box className={Classes.DIALOG_BODY}>
            <Flex auto column>
              <SelectContractDoctorForm
                listContractDoctors={listContractDoctors}
                displayStageNumber={1}
                ikNumber={modelState.patientEnrollmentInformation.ikNumber}
                dob={modelState.patientEnrollmentInformation.patientDOB}
                insuranceNumber={
                  modelState.patientEnrollmentInformation.insuranceNumber
                }
                loadingFooterMessage={
                  modelState.currentProgressStage ===
                  PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                    .SelectContractDoctorRequestCreateEnrollment
                    ? {
                        showLoadingIcon: true,
                        message: t('prepareContractForms'),
                      }
                    : undefined
                }
                isChangingDoctor={
                  modelState.patientEnrollmentInformation
                    ? modelState.patientEnrollmentInformation.isChangingDoctor
                    : false
                }
                disableForms={
                  modelState.currentFormStage ===
                    PatientEnrollmentFormReducer.EnrollmentFormStage
                      .LoadingEnrollmentDetail ||
                  modelState.currentProgressStage ===
                    PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                      .SelectContractDoctorRequestCreateEnrollment ||
                  modelState.currentFormStage ===
                    PatientEnrollmentFormReducer.EnrollmentFormStage.Submitting
                }
                changeDoctorReason={
                  modelState.patientEnrollmentInformation.changeDoctorReason
                }
                excludedContractIds={excludedContractIds}
                selectedDoctorId={
                  modelState.patientEnrollmentInformation
                    ? modelState.patientEnrollmentInformation.doctorId
                    : undefined
                }
                selectedContractId={
                  modelState.patientEnrollmentInformation?.contractId ??
                  defaultSelectedContractId ??
                  undefined
                }
                errorMessages={modelState.contractDoctorErrorMessages}
                onContractChanged={(contractId: string) => {
                  dispatch({
                    type: 'OnSelectedContractChanged',
                    payload: {
                      contractId: contractId,
                    },
                  });
                }}
                onDoctorChanged={(doctorId: string) => {
                  if (!doctorId) return;
                  dispatch({
                    type: 'OnSelectedDoctorChanged',
                    payload: {
                      doctorId,
                    },
                  });
                }}
                onChangeDoctorReasonChanged={(reason: ChangeDoctorReason) => {
                  if (!reason) {
                    return;
                  }
                  dispatch({
                    type: 'OnChangeDoctorReasonChanged',
                    payload: {
                      changeDoctorReason: reason,
                    },
                  });
                }}
                onCheckedParticipation={(status: ParticipationStatus) => {
                  dispatch({
                    type: 'OnCheckParticipationResponsed',
                    payload: {
                      checkParticipationStatus: status,
                    },
                  });
                }}
                onResetHpmMsg={() => {
                  dispatch({
                    type: 'ResetHPMErrorMessage',
                  });
                }}
                isShowFavButton={isShowFavButton}
                isHzvButton={!!isHzvButton}
                checkedFAV={store.isEnrollExtraFAV}
                setCheckedFAV={(isChecked) => onHandleSetCheckFAV(isChecked)}
              />
              <PrintFormV2
                forms={printForms ?? []}
                displayStageNumber={2}
                showForm={PatientEnrollmentFormReducer.IsEqualOrLargerThan(
                  modelState.currentProgressStage,
                  PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                    .PrintForms
                )}
                changeDoctorReason={
                  modelState.patientEnrollmentInformation.changeDoctorReason
                }
                contractId={modelState.patientEnrollmentInformation.contractId!}
                doctorId={modelState.patientEnrollmentInformation.doctorId!}
                isHzvButton={!!isHzvButton}
                isOnlineEnrollment={isOnlineEnrollment}
                setFormPrint={setFormPrint}
              />

              {isOnlineEnrollment && (
                <PatientSignaturesForm
                  displayStageNumber={3}
                  isShowForm={PatientEnrollmentFormReducer.IsEqualOrLargerThan(
                    modelState.currentProgressStage,
                    PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                      .GetSignatures
                  )}
                  hasRequiredSignature={
                    isNotNullOrEmpty(modelState.requiredSignatureForms) ||
                    isOnlineEnrollment
                  }
                  patientSignaturesForms={modelState.requiredSignatureForms}
                  isHzvButton={!!isHzvButton}
                  onSignatureSelect={(
                    formId: string,
                    signatureName: string,
                    checked: boolean
                  ) => {
                    dispatch({
                      type: 'OnSignatureChecked',
                      payload: {
                        formId: formId,
                        signatureName: signatureName,
                        isSignatureChecked: checked,
                      },
                    });
                  }}
                />
              )}
              {isOnlineEnrollment && (
                <TeCodeForm
                  displayStageNumber={4}
                  isShowed={
                    modelState.patientEnrollmentInformation &&
                    isNotNullOrEmpty(contractForms)
                  }
                  isShowForm={PatientEnrollmentFormReducer.IsEqualOrLargerThan(
                    modelState.currentProgressStage,
                    PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                      .InputTeCode
                  )}
                  contractForms={contractForms}
                  onInputTeCodesChanged={(
                    formId: string,
                    teCode: string,
                    errorMessage: string
                  ) => {
                    dispatch({
                      type: 'OnTeCodeChanged',
                      payload: {
                        formId: formId,
                        inputtedTeCode: teCode,
                        errorMessage: errorMessage,
                      },
                    });
                  }}
                />
              )}

              {!isOnlineEnrollment && (
                <SaveOfflineForm
                  displayStageNumber={3}
                  isShowForm={PatientEnrollmentFormReducer.IsEqualOrLargerThan(
                    modelState.currentProgressStage,
                    PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                      .GetSignatures
                  )}
                />
              )}
              <Box w="100%">
                <FormGroup
                  helperText={
                    isNotNullOrEmpty(modelState.hpmMessages) ? (
                      <ErrorMessageList messages={modelState.hpmMessages} />
                    ) : (
                      ''
                    )
                  }
                  className={
                    isNotNullOrEmpty(modelState.hpmMessages)
                      ? Classes.INTENT_DANGER
                      : ''
                  }
                >
                  {PatientEnrollmentFormReducer.IsEqualOrLargerThan(
                    modelState.currentProgressStage,
                    isOnlineEnrollment
                      ? PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                          .InputTeCode
                      : PatientEnrollmentFormReducer.EnrollmentProgressFormStage
                          .GetSignatures
                  ) && (
                    <Button
                      disabled={!shouldAllowSubmit(modelState)}
                      type="button"
                      intent={Intent.PRIMARY}
                      loading={
                        modelState.currentFormStage ===
                          PatientEnrollmentFormReducer.EnrollmentFormStage
                            .Submitting ||
                        modelState.currentFormStage ===
                          PatientEnrollmentFormReducer.EnrollmentFormStage
                            .LoadingEnrollmentDetail
                      }
                      fill
                      onClick={handleSubmitButtonClick}
                    >
                      {isOnlineEnrollment
                        ? t('submitOnlineButtonLabel')
                        : t('submitOfflineButtonLabel')}
                    </Button>
                  )}
                </FormGroup>
              </Box>
              {progressSaveOn && (
                <Box className="progress-save-on" w="100%">
                  {t('progressSaveOn', {
                    time: moment(progressSaveOn).format('DD.MM.YYYY HH:mm:ss'),
                  })}
                </Box>
              )}
            </Flex>
          </Box>
        </Dialog>
        <InfoConfirmDialog
          isOpen={
            modelState.currentFormStage ===
            PatientEnrollmentFormReducer.EnrollmentFormStage
              .OnConfirmingCloseForm
          }
          onClose={() => {
            dispatch({
              type: 'OnCloseFormConfirmed',
              payload: {
                confirmCloseForm: false,
              },
            });
          }}
          onConfirm={() => {
            dispatch({
              type: 'OnCloseFormConfirmed',
              payload: {
                confirmCloseForm: true,
              },
            });
            submitEnrollmentCallback?.();
          }}
          title={t('titleConfirmCloseAlert')}
          cancelText={t('cancelButtonCloseFormAlert')}
          confirmText={t('confirmButtonCloseFormAlert')}
          type="secondary"
          isCloseButtonShown={false}
        >
          <BodyTextM>{t('contentConfirmCloseAlert')}</BodyTextM>
        </InfoConfirmDialog>
        {!musterFormDialogStore.isViewForm &&
          formSetting?.type === FormTypeSetting.ENROLLMENT_FORM && (
            <MusterFormDialog
              className="enrollment-form"
              patient={patient}
              selectedContractDoctor={
                selectedDoctor
                  ? {
                      doctorId: selectedDoctor.id,
                      bsnrId: selectedDoctor.bsnrId,
                      availableDoctor: [selectedDoctor],
                      contractId:
                        modelState.patientEnrollmentInformation?.contractId,
                      chargeSystemId:
                        modelState.patientEnrollmentInformation?.contractId,
                      encounterCase: EncounterCase.AB,
                      isEnrollment: true,
                    }
                  : null
              }
              isOpen={!!musterFormDialogStore.currentFormName}
              isFormPrint={isFormPrint}
              onClose={() => {
                dispatch({
                  type: 'OnCloseFormConfirmed',
                  payload: {
                    confirmCloseForm: false,
                  },
                });
              }}
              onActions={async (formSettings) => {
                if (!store.currentForm) {
                  return;
                }
                await onPrintedForm(store.currentForm, formSettings);
              }}
              componentActions={patientEnrollmentActions}
              onCreateSchein={onCreateSchein}
            />
          )}
      </>
    );
  }
);

function init(data: IEnrollmentModelState): IEnrollmentModelState {
  return {
    patientEnrollmentInformation: { ...data.patientEnrollmentInformation },
    hpmMessages: data.hpmMessages ? [...data.hpmMessages] : [],
    requestCreatePatientEnrollment: null!,
    toastMessage: data.toastMessage,
    requestPrintForm: data.requestPrintForm,
    requestSaveOfflineEnrollment: data.requestSaveOfflineEnrollment,
    requestSendParticipation: data.requestSendParticipation,
    currentFormStage: data.currentFormStage,
    savedEnrollmentProgressStage: data.savedEnrollmentProgressStage,
    currentProgressStage: data.currentProgressStage,
    contractDoctorErrorMessages: data.contractDoctorErrorMessages,
    requiredSignatureForms: [],
    isFinishEnrollmentForm: false,
    t: data.t,
  };
}

export default I18n.withTranslation(PatientEnrollmentForm, {
  namespace: 'PatientManagement',
  nestedTrans: 'PatientEnrollmentForm',
});

const getQRString = (
  patient: PatientProfileResponse | undefined,
  doctor: IEmployeeProfile | undefined,
  doctorChange: boolean,
  identification: string
) => {
  if (!patient) return '';

  const data = [
    patient.patientInfo.personalInfo.lastName,
    patient.patientInfo.personalInfo.firstName,
    formatDob(patient.patientInfo.personalInfo.dateOfBirth),
    patient.patientInfo.insuranceInfos[0].ikNumber,
    patient.patientInfo.insuranceInfos[0].insuranceNumber,
    doctor?.bsnr,
    doctor?.lanr,
    datetimeUtil.dateTimeFormat(datetimeUtil.now(), 'DD.MM.YY'),
    doctorChange ? '1' : '0',
    identification,
  ];
  return data.join(';');
};

const formatDob = (dob: DateOfBirth) => {
  return (
    `${dob.date}`.padStart(2, '0') +
    '.' +
    `${dob.month}`.padStart(2, '0') +
    '.' +
    `${dob.year}`.toString().slice(-2)
  );
};
