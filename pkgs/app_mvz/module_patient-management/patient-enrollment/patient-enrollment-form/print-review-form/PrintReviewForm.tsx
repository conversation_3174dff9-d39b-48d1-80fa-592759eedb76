import React, { useReducer, useContext, useEffect, useMemo } from 'react';
import {
  Intent,
  Dialog,
  Classes,
  Spinner,
  Label,
} from '@tutum/design-system/components/Core';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import {
  Box,
  Flex,
  Svg,
  H1,
  H3,
  BodyTextM,
  Button,
} from '@tutum/design-system/components';
import { scaleSpace } from '@tutum/design-system/styles';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { FormType } from '@tutum/hermes/bff/service_domains_enrollment';
import PDFFrame from '@tutum/mvz/components/pdf-frame';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import printReviewFormReducer, {
  IPrintPreviewFormState,
} from './PrintReviewForm.reducer';
import { ContractType } from '@tutum/hermes/bff/common';

export interface IPrintReviewFormProps {
  className?: string;
  isOpen: boolean;
  form: IPrintForm;
  onClose: () => void;
  onSaveAndPrint: () => void;
}

export interface IPrintForm {
  formId: string;
  templateId: string;
  templateName: string;
  templatePath: string;
  printed: boolean;
  type: FormType;
  enrollmentId: string;
  contractType: ContractType;
  contractId: string;
  teId: string;
}

const ArrowLeft = '/images/arrow-left.svg';

const PrintReviewFormMemo = React.memo(
  ({
    className,
    isOpen,
    form,
    onClose,
    onSaveAndPrint,
    t,
  }: IPrintReviewFormProps &
    II18nFixedNamespace<
      keyof typeof PatientManagementI18n.PrintReviewForm
    >) => {
    const { t: tPrintForm } = I18n.useTranslation<
      keyof typeof PatientManagementI18n.PrintForm
    >({
      namespace: 'PatientManagement',
      nestedTrans: 'PrintForm',
    });

    const {
      patientManagement: { patient },
    } = useContext(PatientManagementContext.instance);

    function init(data: IPrintPreviewFormState): IPrintPreviewFormState {
      return {
        loadingPDF: data.loadingPDF,
        pdfPath: data.pdfPath,
      };
    }

    const [modelState, dispatch] = useReducer(
      printReviewFormReducer,
      {
        loadingPDF: true,
        pdfPath: '',
      },
      init
    );

    useEffect(() => {
      if (!form) {
        return;
      }
      dispatch({
        type: 'LoadForm',
        payload: {
          formType: form?.type,
          enrollmentId: form?.enrollmentId,
        },
      });
    }, [form]);

    const formTypeLabel = useMemo(() => {
      switch (form?.type) {
        case FormType.FormType_Registration:
          return tPrintForm('registrationDocument');
        case FormType.FormType_Participation:
          return tPrintForm('declareParticipation');
        case FormType.FormType_CareManagement1:
          return tPrintForm('careManagement1');
        case FormType.FormType_CareManagement2:
          return tPrintForm('careManagement2');
        default:
          return '';
      }
    }, [form?.type]);

    return (
      <>
        <Dialog
          className={getCssClass(
            'bp5-dialog-fullscreen',
            'bp5-dialog-content-scrollable'
          )}
          onOpening={() => {
            dispatch({
              type: 'SetLoadingPDF',
              payload: null,
            });
          }}
          isOpen={isOpen}
          onClose={onClose}
          canOutsideClickClose={false}
        >
          <Flex className={getCssClass(Classes.DIALOG_HEADER)}>
            <Button
              className={'btn-back-header'}
              onClick={onClose}
              minimal={true}
              text={undefined}
              large={true}
            >
              <Svg src={ArrowLeft} />
            </Button>

            <H1>{`${patient?.lastName}, ${patient?.firstName}`}</H1>
          </Flex>
          <Flex
            className={getCssClass(
              Classes.DIALOG_BODY,
              className,
              'print-review-container'
            )}
          >
            <Flex column className={getCssClass('pdf-review')}>
              <Box className="spinner-wrapper" hidden={!modelState.loadingPDF}>
                <Box className={getCssClass('loading-pdf-spinner')}>
                  <Spinner size={scaleSpace(6)} intent={Intent.PRIMARY} />
                  <Label className="loading-pdf-message">
                    {modelState.loadingPDF ? t('loadingPDFReview') : ''}
                  </Label>
                </Box>
              </Box>
              <Box className="pdf-wrapper">
                <PDFFrame
                  id="test"
                  url={`/scripts/pdf-editor/viewer.html?file=${modelState.pdfPath}`}
                  width="100%"
                  onPagesRendered={() => {
                    dispatch({
                      type: 'OnLoadedPDF',
                      payload: null,
                    });
                  }}
                />
              </Box>
            </Flex>
            <Flex column className="pdf-review-settings">
              <Box className="settings-container">
                {/**waiting for the requirement print controls **/}
                <Box className="forms-container">
                  <H3>{t('formDetails')}</H3>
                  <BodyTextM>{formTypeLabel}</BodyTextM>
                </Box>
                <Box className="print-container">
                  <Button
                    intent={Intent.PRIMARY}
                    loading={modelState.loadingPDF}
                    onClick={() => {
                      if (modelState.loadingPDF) {
                        return;
                      }
                      onSaveAndPrint();
                    }}
                  >
                    {t('btnPrint')}
                  </Button>
                </Box>
              </Box>
            </Flex>
          </Flex>
        </Dialog>
      </>
    );
  }
);

export default I18n.withTranslation(PrintReviewFormMemo, {
  namespace: 'PatientManagement',
  nestedTrans: 'PrintReviewForm',
});
