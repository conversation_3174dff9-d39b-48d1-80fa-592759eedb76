import React, { useEffect, useState, useMemo, useCallback, memo } from 'react';
import {
  FormGroup,
  Classes,
  Label,
  Checkbox,
} from '@tutum/design-system/components/Core';
import { Flex, Box, H3, BodyTextM } from '@tutum/design-system/components';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import CircleNumber from '../CircleNumber';
import { ContractType } from '@tutum/hermes/bff/common';
import { FormType } from '@tutum/hermes/bff/service_domains_enrollment';
import PatientEnrollmentFormHook, {
  HookGetListContractsResponse,
} from '../PatientEnrollmentForm.hook';
import {
  handleGroupFormsByContractId,
  handleSortedGroupForms,
} from '../PatientEnrollmentForm.service';
import { usePatientEnrollmentFormStore } from '../PatientEnrollmentForm.store';

export interface IPatientSignaturesForm {
  formId: string;
  templateId: string;
  isSignature1Required: boolean;
  signature1: boolean;
  errorMessageSignature1: string;
  isSignature2Required: boolean;
  signature2: boolean;
  errorMessageSignature2: string;
  isSignature2Showed: boolean;
  contractType: ContractType;
  type: FormType;
  contractId: string;
}

export interface IPatientSignaturesFormProp {
  patientSignaturesForms: IPatientSignaturesForm[];
  hasRequiredSignature: boolean;
  displayStageNumber: number;
  isHzvButton: boolean;
  isShowForm?: boolean;
  onSignatureSelect: (
    formId: string,
    signatureName: string,
    checked: boolean
  ) => void;
}

export interface IPatientSignaturesFormState {
  forms: IPatientSignaturesForm[] | undefined;
}

const handleCheckboxLabel = (
  formType: FormType,
  t: IFixedNamespaceTFunction<
    keyof typeof PatientManagementI18n.PatientSignatureForm
  >
) => {
  switch (formType) {
    case FormType.FormType_Registration:
      return t('signatureRegisterLabel');
    case FormType.FormType_Participation:
      return t('signatureParticipationLabel');
    default:
      return t('signature1Label');
  }
};

const renderCheckboxes = (
  forms: IPatientSignaturesForm[],
  isRequireExtraForm: boolean,
  isHzvButton: boolean,
  signatureChecked: (
    formId: string,
    signatureName: string,
    checked: boolean
  ) => void,
  t: IFixedNamespaceTFunction<
    keyof typeof PatientManagementI18n.PatientSignatureForm
  >
) => {
  const formToSubmit =
    isRequireExtraForm || !isHzvButton
      ? forms
      : forms.filter(
        (f) => f.contractType === ContractType.ContractType_HouseDoctorCare
      );

  return (
    formToSubmit?.map((form, index) => (
      <React.Fragment key={index}>
        {form.isSignature1Required && (
          <FormGroup
            helperText={form.errorMessageSignature1}
            className={
              form.errorMessageSignature1 != '' ? Classes.INTENT_DANGER : ''
            }
            key={form.formId}
            style={{ marginBottom: 0 }}
          >
            <Checkbox
              label={handleCheckboxLabel(form?.type, t)}
              checked={form.signature1}
              onChange={(event: React.FormEvent<HTMLInputElement>) => {
                signatureChecked(
                  'signature1',
                  form.formId,
                  event.currentTarget.checked
                );
              }}
            />
          </FormGroup>
        )}
        {(form.isSignature2Showed ||
          (form.isSignature2Required && isRequireExtraForm)) && (
            <FormGroup
              helperText={form.errorMessageSignature2}
              className={
                form.errorMessageSignature2 != '' ? Classes.INTENT_DANGER : ''
              }
              key={form.formId}
              style={{ marginBottom: 0 }}
            >
              <Checkbox
                label={t('signature2Label')}
                checked={form.signature2}
                onChange={(event: React.FormEvent<HTMLInputElement>) => {
                  signatureChecked(
                    'signature2',
                    form.formId,
                    event.currentTarget.checked
                  );
                }}
              />
            </FormGroup>
          )}
      </React.Fragment>
    )) ?? null
  );
};

const PatientSignaturesForm = ({
  patientSignaturesForms,
  isShowForm = true,
  hasRequiredSignature,
  displayStageNumber,
  isHzvButton,
  onSignatureSelect,
}: IPatientSignaturesFormProp) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.PatientSignatureForm
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'PatientSignatureForm',
  });

  const store = usePatientEnrollmentFormStore();

  const memoizedListContractId = useMemo(() => {
    return patientSignaturesForms?.map((item) => ({
      contractId: item.contractId,
      contractType: item.contractType,
    }));
  }, [patientSignaturesForms]);
  const listContractGroups = PatientEnrollmentFormHook.useHookGetListContracts(
    memoizedListContractId
  );

  const [modelState, setModelState] = useState<IPatientSignaturesFormState>({
    forms: undefined,
  });

  useEffect(() => {
    setModelState({
      forms: patientSignaturesForms
        ? Array.from(patientSignaturesForms, (item) => {
          return { ...item };
        })
        : [],
    });
  }, [patientSignaturesForms]);

  const signatureChecked = useCallback(
    (signatureName: string, formId: string, checked: boolean) => {
      const index = (modelState.forms || []).findIndex(
        (form) => form.formId === formId
      );

      if (modelState.forms) {
        modelState.forms[index] = { ...modelState.forms[index] };
      }

      if (signatureName == 'signature1') {
        if (modelState.forms?.[index]) {
          modelState.forms[index].signature1 = checked;
        }
        if (checked) {
          if (modelState.forms?.[index]) {
            modelState.forms[index].errorMessageSignature1 = '';
          }
        }
      } else if (signatureName == 'signature2') {
        if (modelState.forms?.[index]) {
          modelState.forms[index].signature2 = checked;
        }
        if (checked) {
          if (modelState.forms?.[index]) {
            modelState.forms[index].errorMessageSignature2 = '';
          }
        }
      }
      onSignatureSelect(modelState.forms?.[index]?.formId!, signatureName, checked);
    },
    [modelState.forms, onSignatureSelect]
  );

  const groupFormsByContractId = useMemo(() => {
    return handleGroupFormsByContractId(modelState.forms);
  }, [modelState.forms]);

  const sortedGroupForms = useMemo(() => {
    if (!groupFormsByContractId) return null;
    return handleSortedGroupForms(groupFormsByContractId);
  }, [groupFormsByContractId]);

  const showContractName = useCallback(
    (
      listContractGroups: HookGetListContractsResponse,
      currentContract: string
    ) => {
      const { listContracts, listGroups } = listContractGroups;

      let contractName = listContracts?.find(
        (c) => c.contractId === currentContract
      )?.contractName;

      if (!listGroups || !listGroups.patientParticipationContracts?.length) {
        return contractName;
      }
      for (
        let i = 0;
        i < listGroups!.patientParticipationContracts.length;
        i++
      ) {
        const contractIds = listGroups!.patientParticipationContracts[i];

        const isSubContractExistInGroup = contractIds.includes(currentContract);
        if (isSubContractExistInGroup) {
          contractName = listGroups.designation;
          break;
        }
      }
      return contractName;
    },
    [listContractGroups]
  );

  return (
    <Flex auto column className="panel-patient-signature">
      <H3 className="label-section-form">
        <CircleNumber showedText={displayStageNumber.toString()} />
        {t('getPatientSignature')}
      </H3>
      {isShowForm && (
        <>
          <Box mb={8}>
            {!hasRequiredSignature || !modelState.forms ? (
              <Label>{t('getPatientSignatureContent')}</Label>
            ) : (
              <Flex column gap={8}>
                {sortedGroupForms?.map((contractId) => (
                  <Flex key={contractId} column>
                    <BodyTextM textTransform="uppercase">
                      {showContractName(listContractGroups, contractId)}
                    </BodyTextM>
                    {renderCheckboxes(
                      groupFormsByContractId[contractId],
                      store.isEnrollExtraFAV,
                      isHzvButton,
                      signatureChecked,
                      t
                    )}
                  </Flex>
                ))}
              </Flex>
            )}
          </Box>
        </>
      )}
    </Flex>
  );
};

export default memo(PatientSignaturesForm);
