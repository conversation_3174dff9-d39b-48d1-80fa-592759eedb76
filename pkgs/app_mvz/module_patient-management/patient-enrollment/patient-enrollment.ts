import {
  PatientParticipation,
  PatientParticipationStatus,
} from '@tutum/hermes/bff/service_domains_patient_participation';
import { DateUtils } from 'react-day-picker';
import { parseDate } from '@tutum/design-system/infrastructure/utils';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

const isParticipationActive = (
  participation: PatientParticipation
): boolean => {
  if (!participation) {
    return false;
  }
  return (
    participation.status ===
    PatientParticipationStatus.PatientParticipation_Active ||
    (participation.status ===
      PatientParticipationStatus.PatientParticipation_Terminated &&
      DateUtils.isDayBetween(
        datetimeUtil.date(),
        parseDate(participation.startDate)!,
        parseDate(participation.endDate)!
      ))
  );
};

export const PatientEnrollmentUtils = {
  isParticipationActive,
};
