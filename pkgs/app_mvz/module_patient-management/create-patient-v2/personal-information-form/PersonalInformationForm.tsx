import React, { useEffect } from 'react';
import { Field, FastField } from 'formik';

import {
  Flex,
  MultiSelect,
  ReactSelect,
  IMenuItem,
  FormGroup2,
  Svg,
} from '@tutum/design-system/components';
import {
  InputGroup,
  Radio,
  RadioGroup,
} from '@tutum/design-system/components/Core';
import { AdditionalName } from '@tutum/hermes/bff/patient_profile_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { Gender } from '@tutum/infrastructure/resource/PatientProfileResource';
import CreatePatientUtil from '@tutum/infrastructure/utils/form.util';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import { IMvzTheme } from '@tutum/mvz/theme';
import { IntendWord } from '@tutum/hermes/bff/patient_profile_common';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { backspace2ClearSelectBox } from '@tutum/mvz/_utils/accessibility';
import { scaleSpace } from '@tutum/design-system/styles';
import PopoverDateInput from '@tutum/design-system/components/DateTime/PopoverDateInput';
import {
  getMaxDateOfBirth,
  MIN_DATE_OF_BIRTH,
  getDateOfBirth,
} from '../CreatePatient.helper';
import { convertDateValueToObject } from '@tutum/design-system/components/DateTime/utils';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { AccountTitleSelect } from '@tutum/design-system/account-form';
import type ExternalAddressI18n from '@tutum/mvz/locales/en/ExternalAddress.json';

export interface IPatientInformationFormProps {
  className?: string;
  theme?: IMvzTheme;
  submitCount?: number;
  errors?: any;
  touched?: any;
  setFieldError?: any;
  isCreatePatient?: boolean;
  isEdit: boolean;
  isDisable?: boolean;
  isReadCardEGK?: boolean;
  isMobileCard?: boolean;
  isSubmitting: boolean;
  setChangeDOB?: (isChangeDOB: boolean) => void;
  setOpenSection: () => void;
}

export interface IPatientInformationFormState {
  isSubmissionConfirmed: false;
  errors?: any;
}

const MAX_ADDITIONAL_NAMES = 2;

const VALIDATE_FIELDS = [
  'personalInfo.firstName',
  'personalInfo.lastName',
  'personalInfo.title',
  'personalInfo.intendWord',
  'personalInfo.gender',
  'personalInfo.dateOfBirth',
  'personalInfo.dateOfDeath',
];
const calendarIcon = '/images/calendar-default.svg';

const PersonalInformationForm = ({
  t,
  submitCount,
  className,
  errors,
  touched,
  isDisable,
  isReadCardEGK,
  isMobileCard,
  isSubmitting,
  isEdit,
  setChangeDOB,
  setOpenSection,
}: IPatientInformationFormProps &
  II18nFixedNamespace<keyof typeof PatientManagementI18n.PersonalInformation> &
  IPatientInformationFormState) => {
  const { t: tExternalAddress } = I18n.useTranslation<
    keyof typeof ExternalAddressI18n['externalAddress']
  >({
    namespace: 'ExternalAddress',
    nestedTrans: 'externalAddress',
  });

  const setDatetimeValue = (dateValue: string, field, form) => {
    const { setFieldValue } = form;
    const dateOfBirth = convertDateValueToObject(dateValue);

    setFieldValue(field.name, dateOfBirth);
    setChangeDOB?.(true);
  };

  useEffect(() => {
    if (!isSubmitting) {
      return;
    }

    const hasErrorInSection = VALIDATE_FIELDS.some((field) => !!errors[field]);

    if (hasErrorInSection) {
      setOpenSection();
    }
  }, [errors, isSubmitting]);

  return (
    <Flex column className={className}>
      {/*first name, last name*/}
      <Flex className="row-content-grow" gap={scaleSpace(4)}>
        <FormGroup2
          name="personalInfo.firstName"
          label={t('firstName')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
          isRequired
        >
          <Field name="personalInfo.firstName">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisable}
                  intent={CreatePatientUtil.getFormInputIntent(
                    submitCount,
                    touched.personalInfo?.firstName,
                    errors['personalInfo.firstName']
                  )}
                  maxLength={45}
                  data-tab-id={field.name}
                  id={'firstName'}
                  fill
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          name="personalInfo.lastName"
          label={t('lastName')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
          isRequired
        >
          <Field name="personalInfo.lastName">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisable}
                  intent={CreatePatientUtil.getFormInputIntent(
                    submitCount,
                    touched.personalInfo?.lastName,
                    errors['personalInfo.lastName']
                  )}
                  maxLength={45}
                  data-tab-id={field.name}
                  id={'lastName'}
                  fill
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>

      {/*title, intend word*/}
      <Flex className="row-content-grow" gap={scaleSpace(4)}>
        <FormGroup2
          name="personalInfo.title"
          label={t('title')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
          style={{ flex: 1 }}
        >
          <Field name="personalInfo.title">
            {({ field, form }) => {
              return (
                <AccountTitleSelect
                  id={field.name}
                  data-test-id={field.name}
                  instanceId={field.name}
                  t={tExternalAddress}
                  isDisabled={isDisable}
                  selectedValue={field.value}
                  onInputChange={(inputValue: string) =>
                    form.setFieldValue(field.name, inputValue)
                  }
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldTouched(field.name, true);
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          name="personalInfo.intendWord"
          label={t('intendWord')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
          style={{ flex: 1 }}
        >
          <FastField name="personalInfo.intendWord">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="intendWord"
                  instanceId={'intendWord'}
                  isSearchable={false}
                  onKeyDown={(evt) =>
                    backspace2ClearSelectBox(evt, () =>
                      form.setFieldValue(field.name, undefined)
                    )
                  }
                  selectedValue={field.value}
                  isDisabled={isDisable}
                  items={Object.values(IntendWord).map(
                    (item): IMenuItem => ({
                      value: item,
                      label: item,
                    })
                  )}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>

      {/*additional name*/}
      <FormGroup2
        name="personalInfo.additionalNames"
        label={t('additionalName')}
      >
        <FastField name="personalInfo.additionalNames">
          {({ field, form }) => {
            const additionalNamesOptions = Object.values(AdditionalName).map(
              (item): IMenuItem => ({
                value: item,
                label: item,
                isDisabled:
                  !!field.value?.[0] &&
                  field.value?.length === MAX_ADDITIONAL_NAMES &&
                  !field.value?.find((value) => value === item),
              })
            );

            return (
              <MultiSelect
                id="additionalNames"
                instanceId="additionalNames"
                isSearchable={false}
                isDisabled={isDisable}
                value={additionalNamesOptions.filter((option) =>
                  field.value?.includes(option.value)
                )}
                options={additionalNamesOptions}
                onChange={(newValue: IMenuItem[]) => {
                  form.setFieldValue(
                    field.name,
                    newValue.map((item) => item.value)
                  );
                }}
              />
            );
          }}
        </FastField>
      </FormGroup2>

      {/* gender */}
      <FormGroup2
        name="personalInfo.gender"
        label={t('gender')}
        submitCount={submitCount}
        touched={{
          ...touched,
          'personalInfo.gender': isMobileCard || touched.personalInfo?.gender,
        }}
        errors={errors}
        isRequired
      >
        <Field name="personalInfo.gender">
          {({ field, form }) => {
            const { setFieldValue } = form;
            const onGenderChange = (
              event: React.FormEvent<HTMLInputElement>
            ) => {
              const value = event.currentTarget.value;
              setFieldValue(field.name, value);
            };
            return (
              <RadioGroup
                onChange={onGenderChange}
                inline
                className="gender-group"
                selectedValue={field.value}
                disabled={isReadCardEGK && isDisable}
              >
                <Radio label={t('genderM')} value={Gender.M} data-test-id="gender-m" />
                <Radio label={t('genderW')} value={Gender.W} data-test-id="gender-w" />
                <Radio label={t('genderX')} value={Gender.X} data-test-id="gender-x" />
                <Radio label={t('genderD')} value={Gender.D} data-test-id="gender-d" />
                <Radio label={t('genderU')} value={Gender.U} data-test-id="gender-u" />
              </RadioGroup>
            );
          }}
        </Field>
      </FormGroup2>
      <Flex className="row-content-grow" gap={scaleSpace(4)}>
        <FormGroup2
          name="personalInfo.dateOfBirth"
          label={t('dateOfBirth')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
          isRequired
          style={{ flex: 1 }}
        >
          <Field name="personalInfo.dateOfBirth">
            {({ field, form }) => {
              const { value } = getDateOfBirth(field.value, false);

              return (
                <PopoverDateInput
                  dataTabId="dateOfBirth"
                  className={CreatePatientUtil.renderFormClass(
                    submitCount,
                    touched.dateOfBirth,
                    errors.dateOfBirth
                  )}
                  name={field.name}
                  placeholder={DATE_FORMAT}
                  formatDate={DATE_FORMAT}
                  minDate={MIN_DATE_OF_BIRTH}
                  maxDate={getMaxDateOfBirth()}
                  disabled={isDisable}
                  value={value}
                  onChange={(dateValue) => {
                    form.setFieldTouched(field.name);
                    setDatetimeValue(dateValue, field, form);
                  }}
                  leftElement={
                    <Svg src={calendarIcon} className="calendar-icon" />
                  }
                />
              );
            }}
          </Field>
        </FormGroup2>
        {isEdit && (
          <FormGroup2
            name="personalInfo.dateOfDeath"
            label={t('dateOfDeath')}
            submitCount={submitCount}
            touched={touched}
            errors={errors}
            style={{ flex: 1 }}
          >
            <Field name={`personalInfo.dateOfDeath`}>
              {({ field, form }) => {
                const { setFieldValue, setFieldTouched } = form;
                const value = field?.value
                  ? datetimeUtil.dateTimeNumberFormat(field.value, DATE_FORMAT)
                  : '';
                const setDateValue = (dateString: string) => {
                  setFieldTouched(field.name);
                  const date = datetimeUtil.strToDate(dateString, DATE_FORMAT);
                  setFieldValue(field.name, date?.getTime());
                };
                return (
                  <PopoverDateInput
                    dataTabId="dateOfDeath"
                    className={CreatePatientUtil.renderFormClass(
                      submitCount,
                      touched.dateOfDeath,
                      errors.dateOfDeath
                    )}
                    name={field.name}
                    placeholder={DATE_FORMAT}
                    formatDate={DATE_FORMAT}
                    maxDate={datetimeUtil.date()}
                    minDate={new Date(1900, 1, 1)}
                    disabled={isDisable}
                    value={value}
                    onChange={setDateValue}
                    leftElement={
                      <Svg src={calendarIcon} className="calendar-icon" />
                    }
                  />
                );
              }}
            </Field>
          </FormGroup2>
        )}
      </Flex>
    </Flex>
  );
};

export default I18n.withTranslation(PersonalInformationForm, {
  namespace: 'PatientProfileCreation',
  nestedTrans: 'PersonalInformation',
});
