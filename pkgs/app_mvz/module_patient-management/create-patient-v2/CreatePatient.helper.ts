import { FormikErrors } from 'formik';
import { isNil } from 'lodash';
import moment from 'moment';

import {
  InsuranceInfo,
  SpecialGroupDescription,
  TypeOfInsurance,
  ContactPerson,
  InsuranceStatus,
  WorkActivity1,
  WorkActivity2,
  GenericInfo,
  PersonalInfo,
  DoctorInfo,
  AddressInfo,
  OtherInfo,
  EmploymentInfo,
  ContactInfo,
  PatientMedicalData,
  PostOfficeBox,
  DateOfBirth,
  PatientType,
  LHM,
} from '@tutum/hermes/bff/patient_profile_common';
import { FieldError } from '@tutum/hermes/bff/common';
import DMP_PROGRAMS from '@tutum/design-system/consts/DMP-programs';
import {
  GetNullUUID,
  isEmpty,
} from '@tutum/design-system/infrastructure/utils';
import FormUtils, {
  ValidateField,
} from '@tutum/infrastructure/utils/form.util';
import { IPatientProfile } from '../types/profile.type';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import DateTimeUtils from '@tutum/infrastructure/utils/datetime.util';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { FeeCatalogue } from '@tutum/hermes/bff/catalog_sdkt_common';
import { convertToMMYYYYString } from './insurance-info/InsuranceInfo.util';
import { PatientInfo } from '@tutum/hermes/bff/legacy/patient_profile_common';
import { Visible } from './CreatePatientContent';
import countryType from '@tutum/mvz/constant/country.type';
import {
  editSdav,
  getSdavById,
} from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdav';
import { employeeStore } from '@tutum/mvz/hooks/useEmployee';
import { AreaOfExpertise } from '@tutum/hermes/bff/catalog_sdav_common';

export interface ICustomInsuranceInfo extends InsuranceInfo {
  isInValidInsuranceNumber: boolean;
  isDetail: boolean;
  error: FieldError | null;
  isExpiredCostUnit: boolean;
  isTerminated?: boolean;
  insuranceType: TypeOfInsurance;
  validTo?: string;
}

export const initDefaultLHMItem: LHM = {
  firstICDCode: '',
  secondICDCode: '',
  diagnosisGroupCode: '',
  isStandardCombination: undefined!,
  primaryRemediesPosition: [],
  complementaryRemediesPosition: [],
  validUntilDate: undefined,
  note: '',
};

export interface ICustomGenericInfo extends GenericInfo {
  patientId?: string;
  cardReadInStatus?: string;
  lastReadingDayOfCard?: string;
}

export const initInsuranceItem: ICustomInsuranceInfo = {
  id: GetNullUUID(),
  insuranceCompanyId: '',
  insuranceCompanyName: '',
  ikNumber: 0,
  insuranceStatus: InsuranceStatus.Mitglied,
  insuranceNumber: '',
  startDate: undefined,
  endDate: undefined,
  specialGroup: SpecialGroupDescription.SpecialGroup_00,
  dMPLabeling: DMP_PROGRAMS.DMP_DEFAULT_VALUE,
  haveCoPaymentExemptionTill: false,
  copaymentExemptionTillDate: undefined,
  havePatientReceipt: false,
  haveHeimiLongTermApproval: false,
  isInValidInsuranceNumber: false,
  wop: '',
  isDetail: false,
  lHMs: [initDefaultLHMItem],
  error: null,
  isExpiredCostUnit: false,
  locationNames: [],
  validity: undefined!,
  feeSchedule: 0,
  feeCatalogue: FeeCatalogue.FeeCatalogue_0,
  insuranceType: TypeOfInsurance.Public,
  validUntil: undefined,
  validTo: '',
  isActive: false,
};

export const initContactPersonItem: ContactPerson = {
  name: '',
  phoneNumber: '',
  email: '',
  relationship: '',
};

export const initPatientMedicalData: PatientMedicalData = {
  weight: undefined,
  height: undefined,
  bloodPressure: '',
  heartFrequency: undefined,
  allergies: [],
  creatinine: undefined,
  amountOfChildren: undefined,
  isPregnant: false,
  dateOfPlannedBirth: undefined,
  amountOfBirth: undefined,
  amountOfPregnancies: undefined,
  isBreastfeeding: false,
  careLevel: 0,
  additionalNote: '',
  bodyTemperature: undefined,
  pulseOxiMetric: undefined,
  pulse: undefined,
};

export interface IInitialValues {
  genericInfo: ICustomGenericInfo;
  contactInfo: ContactInfo;
  insuranceInfos: ICustomInsuranceInfo[];
  personalInfo: PersonalInfo;
  doctorInfo: DoctorInfo;
  addressInfo: AddressInfo;
  otherInfo: OtherInfo;
  employmentInfo: EmploymentInfo;
  patientMedicalData: PatientMedicalData;
  postOfficeBox: PostOfficeBox;
}

export interface DoctorInfoExtend extends DoctorInfo {
  generalPractitionerDoctor_phoneNumber: string[];
  generalPractitionerDoctor_email: string[];
  specialistDoctor_exptertise: string[] | AreaOfExpertise[];
  specialistDoctor_phoneNumber: string[];
  specialistDoctor_email: string[];
}

export const DEFAULT_COUNTRY_CODE = 'D';

export const MIN_LENGTH_POSTCODE = 5;
export const MAX_LENGTH_POSTCODE = 10;

export const MIN_DATE_OF_BIRTH = new Date(1900, 1, 1);
export const getMaxDateOfBirth = () => {
  return DateTimeUtils.date();
};

export const getDateOfBirth = (dateOfBirth: DateOfBirth, isEdit = true) => {
  const { date, month, year } = dateOfBirth || {};
  const textDate = !isNil(date)
    ? +date > 9
      ? `${date}`
      : `0${+date}`
    : isEdit
      ? '00'
      : '';
  const textMonth = !isNil(month)
    ? +month > 9
      ? `${month}`
      : `0${+month}`
    : isEdit
      ? '00'
      : '';
  const textYear = !isNil(year) ? `${year}` : isEdit ? '0000' : '';
  const isCorrectDate = textDate.length === 2;
  const isCorrectMonth = textMonth.length === 2;
  const isCorrectYear = textYear.length === 4;
  const isCorrectFormat = isCorrectDate && isCorrectMonth && isCorrectYear;
  const value = [textDate, textMonth, textYear].join(
    isCorrectFormat ? '.' : ''
  );

  return {
    textDate,
    textMonth,
    textYear,
    value,
  };
};

export const getCopaymentExemptionTillDateUnder18y = (dateOfBirth) => {
  const { value } = getDateOfBirth(dateOfBirth, false);
  const date = moment(value, DATE_FORMAT).add(18, 'y');
  const beginingDate = DateTimeUtils.getStartOfQuarter(date);

  return +beginingDate;
};

export const getCopaymentExemptionTillDateOver18y = (year: number) => {
  const date = moment(`31.12.${year}`, DATE_FORMAT);

  return date.toDate();
};

export const initialValues: IInitialValues = {
  genericInfo: {
    patientType: PatientType.PatientType_Public,
    patientId: '',
    cardReadInStatus: '',
    lastReadingDayOfCard: '',
  },
  contactInfo: {
    primaryContactNumber: '',
    furtherContactNumber: [''],
    emailAddress: '',
    haveDeclarationOfAgreementToContact: false,
    contactPersons: [initContactPersonItem],
    contactAgreementFile: {
      fileName: '',
      fileUrl: '',
    },
  },
  insuranceInfos: [initInsuranceItem],
  personalInfo: {
    firstName: '',
    lastName: '',
    title: '',
    additionalNames: undefined,
    intendWord: undefined,
    gender: null!,
    dOB: undefined!,
    dateOfBirth: {
      date: undefined,
      month: undefined,
      year: undefined,
      isValidDOB: false,
    },
  },
  doctorInfo: {
    generalPractitionerDoctorId: [],
    specialistDoctorId: [],
  },
  addressInfo: {
    address: {
      street: '',
      number: '',
      postCode: '',
      city: '',
      countryCode: DEFAULT_COUNTRY_CODE,
      distance: undefined,
      additionalAddressInfo: '',
    },
    billingAddress: {
      street: '',
      number: '',
      postCode: '',
      city: '',
      countryCode: DEFAULT_COUNTRY_CODE,
      firstName: '',
      lastName: '',
    },
  },
  otherInfo: {
    cave: '',
    haveMedicalHistoryFormCompleted: false,
    isLivingWillAvailable: false,
    isAgreeWithBillingViaPVS: false,
    isPrivacyPolicySigned: false,
    medicalHistoryFileUrl: {
      fileName: '',
      fileUrl: '',
    },
    livingWillFileUrl: {
      fileName: '',
      fileUrl: '',
    },
    billingFileUrl: {
      fileName: '',
      fileUrl: '',
    },
    privacyPolicyFileUrl: {
      fileName: '',
      fileUrl: '',
    },
  },
  employmentInfo: {
    isEmployed: false,
    occupation: '',
    specialProblemAtWork: '',
    workingHourInWeek: 0,
    workActivity1: '' as WorkActivity1,
    workActivity2: '' as WorkActivity2,
    companyAddress: {},
  },
  patientMedicalData: {
    weight: undefined,
    height: undefined,
    bloodPressure: '',
    heartFrequency: undefined,
    allergies: [],
    creatinine: undefined,
    amountOfChildren: undefined,
    isPregnant: false,
    dateOfPlannedBirth: undefined,
    amountOfBirth: undefined,
    amountOfPregnancies: undefined,
    isBreastfeeding: false,
    careLevel: 1,
    additionalNote: '',
    bodyTemperature: undefined,
    pulse: undefined,
    pulseOxiMetric: undefined,
  },
  postOfficeBox: {
    postCode: '',
    placeOfResidence: '',
    officeBox: '',
    countryCode: DEFAULT_COUNTRY_CODE,
  },
};
export const handleShowPatientNumber = (patientNumber: number) => {
  return patientNumber.toString().padStart(6, '0');
};

export const parseInitValues = (selectedPatient: IPatientProfile) => ({
  ...selectedPatient.patientInfo,
  genericInfo: {
    ...selectedPatient.patientInfo?.genericInfo,
    patientType: selectedPatient.patientInfo?.genericInfo.patientType,
    patientId: handleShowPatientNumber(
      selectedPatient.patientInfo?.patientNumber
    ),
    lastCardReadinDate:
      selectedPatient.patientInfo.genericInfo?.lastCardReadinDate,
  },
  personalInfo: {
    ...selectedPatient.patientInfo?.personalInfo,
    dOB: selectedPatient.patientInfo?.personalInfo?.dOB
      ? moment(selectedPatient.patientInfo.personalInfo.dOB).format(DATE_FORMAT)
      : null,
    dateOfBirth: {
      ...selectedPatient.patientInfo.personalInfo.dateOfBirth,
      date: selectedPatient.patientInfo.personalInfo.dateOfBirth.date || '00',
      month: selectedPatient.patientInfo.personalInfo.dateOfBirth.month || '00',
      year: selectedPatient.patientInfo.personalInfo.dateOfBirth.year || '0000',
    },
    dateOfDeath: selectedPatient.patientInfo?.personalInfo?.dateOfDeath
      ? new Date(selectedPatient.patientInfo?.personalInfo?.dateOfDeath)
      : null,
  },
  insuranceInfos: selectedPatient.patientInfo?.insuranceInfos?.map((item) => ({
    ...item,
    specialGroup: item?.specialGroup
      ? item.specialGroup
      : item.insuranceType === TypeOfInsurance.Private
        ? null
        : SpecialGroupDescription.SpecialGroup_00,
    dMPLabeling: item.dMPLabeling || DMP_PROGRAMS.DMP_DEFAULT_VALUE,
    startDate: item.startDate ? new Date(item.startDate) : null,
    endDate: item.endDate ? new Date(item.endDate) : null,
    copaymentExemptionTillDate: item.copaymentExemptionTillDate
      ? new Date(item.copaymentExemptionTillDate)
      : null,
    isDetail: true,
    validTo: convertToMMYYYYString(
      item.validUntil?.month!,
      item.validUntil?.year!
    ),
  })),
  patientMedicalData: {
    ...selectedPatient.patientMedicalData,
    careLevel: selectedPatient.patientMedicalData?.careLevel || 0,
  },
});

export const onValidateForm =
  (t, lhmStore, isDisable) => async (values: IInitialValues) => {
    const {
      insuranceInfos,
      personalInfo,
      addressInfo,
      postOfficeBox,
      employmentInfo,
    } = values;
    const errorFunc = () => {
      const { date, month, year } = personalInfo.dateOfBirth;
      if (personalInfo.dateOfBirth?.year && personalInfo.dateOfDeath) {
        const doB = new Date(`${year}-${month}-${date}`);
        const dateOfDeath = new Date(personalInfo.dateOfDeath);
        if (doB.getTime() >= dateOfDeath.getTime()) {
          return t('invalidDobWithDod');
        }
      }
      return t('invalidDob');
    };
    const validateFields: ValidateField[] = [];
    validateFields.push({
      fieldName: 'personalInfo.firstName',
      validateRule: () =>
        !personalInfo.firstName || isEmpty(personalInfo.firstName, true),
      errorMessage: t('errFirstNameEmpty'),
    });
    validateFields.push({
      fieldName: 'personalInfo.dateOfBirth',
      validateRule: () => {
        const { date, month, year } = personalInfo.dateOfBirth;
        const { value } = getDateOfBirth(personalInfo.dateOfBirth, false);
        // check correct format DD.MM.YYYY
        if (value.length < 8) {
          return true;
        }
        if (!+date! && !+month! && !+year!) {
          return false;
        }
        if (!+date!) {
          return (
            +year! < MIN_DATE_OF_BIRTH.getFullYear() ||
            +year! > getMaxDateOfBirth().getFullYear()
          );
        }
        const formatDate = moment(value, DATE_FORMAT);

        return (
          !formatDate.isValid() ||
          +formatDate < +MIN_DATE_OF_BIRTH ||
          +formatDate > +getMaxDateOfBirth()
        );
      },
      errorMessage: t('invalidDob'),
    });
    validateFields.push({
      fieldName: 'personalInfo.dateOfDeath',
      validateRule: () => {
        const { date, month, year } = personalInfo.dateOfBirth;

        if (personalInfo.dateOfBirth?.year && personalInfo.dateOfDeath) {
          const doB = new Date(`${year}-${month}-${date}`);
          const dateOfDeath = new Date(personalInfo.dateOfDeath);
          if (doB.getTime() >= dateOfDeath.getTime()) {
            return true;
          }
        }

        return false;
      },
      errorMessage: errorFunc(),
    });
    validateFields.push({
      fieldName: 'personalInfo.dateOfBirth',
      validateRule: () => {
        const { date, month, year } = personalInfo.dateOfBirth;

        return !(date || month || year);
      },
      errorMessage: t('errDob'),
    });
    validateFields.push({
      fieldName: 'personalInfo.gender',
      validateRule: () =>
        !personalInfo.gender ||
        isEmpty(personalInfo.gender, true) ||
        personalInfo?.gender.toString() === '',
      errorMessage: t('errGender'),
    });
    validateFields.push({
      fieldName: 'personalInfo.lastName',
      validateRule: () =>
        !personalInfo.lastName || isEmpty(personalInfo.lastName, true),
      errorMessage: t('errLastNameEmpty'),
    });
    const isInvalidPostCodeOffice =
      !postOfficeBox.postCode || isEmpty(postOfficeBox.postCode, true);
    const isInvalidPostCodeAddress =
      !addressInfo.address.postCode ||
      isEmpty(addressInfo.address.postCode, true);
    const isInvalidPostCode =
      !isDisable &&
      isInvalidPostCodeOffice &&
      isInvalidPostCodeAddress &&
      addressInfo.address.countryCode === countryType.COUNTRY_DEFAULT_CODE;
    validateFields.push({
      fieldName: 'postOfficeBox.postCode',
      validateRule: () => isInvalidPostCode,
      errorMessage: t('errPostCodeRequired'),
    });
    validateFields.push({
      fieldName: 'addressInfo.address.postCode',
      validateRule: () => isInvalidPostCode,
      errorMessage: t('errPostCodeRequired'),
    });

    validateFields.push({
      fieldName: 'addressInfo.address.postCode',
      validateRule: () =>
        !isEmpty(addressInfo?.address?.postCode, true) &&
        addressInfo.address.countryCode !== countryType.COUNTRY_DEFAULT_CODE &&
        (addressInfo.address.postCode.length > MAX_LENGTH_POSTCODE ||
          addressInfo.address.postCode.length < MIN_LENGTH_POSTCODE),
      errorMessage: t('errPostCodeInvalid'),
    });

    // with Private patient Address information must have required information
    if (values.genericInfo.patientType === PatientType.PatientType_Private) {
      validateFields.push({
        fieldName: 'addressInfo.address.street',
        validateRule: () => !addressInfo.address.street,
        errorMessage: t('errStreetRequired'),
      });
      validateFields.push({
        fieldName: 'addressInfo.address.city',
        validateRule: () => !addressInfo.address.city,
        errorMessage: t('errCityRequired'),
      });
      validateFields.push({
        fieldName: 'addressInfo.address.number',
        validateRule: () => !addressInfo.address.number,
        errorMessage: t('errNumberRequired'),
      });
    }

    (insuranceInfos || []).forEach((item, index) => {
      if (!item.haveHeimiLongTermApproval) {
        return;
      }

      (item.lHMs || []).forEach((lhm, lhmIndex) => {
        const errFnc = () => {
          const lhmPrimaryRemediesPositionLength =
            lhm.primaryRemediesPosition?.length || 0;
          const lhmComplementaryRemediesPositionLength =
            lhm.complementaryRemediesPosition?.length || 0;
          // has standard combination and...
          if (
            !!lhmStore.hasStandardCombination[lhm.diagnosisGroupCode] &&
            lhm.isStandardCombination
          ) {
            if (
              !lhmComplementaryRemediesPositionLength &&
              !lhmPrimaryRemediesPositionLength
            ) {
              // rule 1
              //pass
            } else if (
              !lhmComplementaryRemediesPositionLength &&
              lhmPrimaryRemediesPositionLength < 3
            ) {
              // rule 2
              return t('lhm.atLeast3PrimaryRemedies');
            } else if (
              !lhmPrimaryRemediesPositionLength &&
              lhmComplementaryRemediesPositionLength < 3
            ) {
              //rule 3
              return t('lhm.atLeast3SupplementaryRemedies');
            } else {
              // rule 4
              if (
                lhmPrimaryRemediesPositionLength +
                lhmComplementaryRemediesPositionLength <
                3
              ) {
                return t('lhm.atLeast3Remedies');
              }
            }
          }
          return '';
        };
        // set the error if any
        validateFields.push({
          fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.primaryRemediesPosition`,
          validateRule: () => !!errFnc(),
          errorMessage: errFnc(),
        });
        validateFields.push({
          fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.complementaryRemediesPosition`,
          validateRule: () => !!errFnc(),
          errorMessage: errFnc(),
        });

        validateFields.push({
          fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.firstICDCode`,
          validateRule: () =>
            !lhm?.firstICDCode || isEmpty(lhm?.firstICDCode, true),
          errorMessage: t('errFirstRequired'),
        });
        validateFields.push({
          fieldName: `insuranceInfos.${index}.lHMs.${lhmIndex}.diagnosisGroupCode`,
          validateRule: () =>
            !lhm?.diagnosisGroupCode || isEmpty(lhm?.diagnosisGroupCode, true),
          errorMessage: t('errDiagnosisGroupRequired'),
        });
      });
    });

    validateFields.push({
      fieldName: 'employmentInfo.workingHourInWeek',
      validateRule: () =>
        !!employmentInfo.isEmployed &&
        !!employmentInfo.workingHourInWeek &&
        !(
          0 <= employmentInfo.workingHourInWeek &&
          employmentInfo.workingHourInWeek < 100
        ),
      errorMessage: t('errWorkingHourInWeek'),
    });

    const { errors } = FormUtils.validateForm(validateFields, null);

    return errors;
  };

export const medicalDataTransform = (
  medicalData: Nullable<PatientMedicalData>,
  isWoman: boolean
): PatientMedicalData => ({
  ...medicalData,
  allergies: medicalData?.allergies?.filter((item) => !!item.allergy) || [],
  dateOfPlannedBirth: isWoman
    ? medicalData?.dateOfPlannedBirth ?? undefined
    : undefined,
  amountOfBirth: isWoman ? medicalData?.amountOfBirth : undefined,
  amountOfPregnancies: isWoman ? medicalData?.amountOfPregnancies : undefined,
  isPregnant: isWoman ? medicalData?.isPregnant : false,
  isBreastfeeding: isWoman ? medicalData?.isBreastfeeding : false,
});

export const transformInsurances = (
  customInsurances: ICustomInsuranceInfo[]
): InsuranceInfo[] => {
  return customInsurances.map((insuranceInfo) => ({
    ...insuranceInfo,
    insuranceNumber: insuranceInfo.insuranceNumber
      ? insuranceInfo.insuranceNumber
      : '',
    startDate: insuranceInfo?.startDate
      ? moment
        .utc(
          moment(insuranceInfo?.startDate).format(DATE_FORMAT),
          DATE_FORMAT
        )
        .valueOf()
      : undefined,
    endDate: insuranceInfo?.endDate
      ? moment
        .utc(moment(insuranceInfo?.endDate).format(DATE_FORMAT), DATE_FORMAT)
        .valueOf()
      : undefined,
    copaymentExemptionTillDate: insuranceInfo?.copaymentExemptionTillDate
      ? moment
        .utc(
          moment(insuranceInfo?.copaymentExemptionTillDate).format(
            DATE_FORMAT
          ),
          DATE_FORMAT
        )
        .valueOf()
      : undefined,
  }));
};

export const CARE_LEVEL_LIST = [1, 2, 3, 4, 5];

export interface IFormikRef {
  dirty: () => boolean;
  getValue: () => PatientInfo;
  onSaveFormRef: (patientInfo?: PatientInfo) => Promise<void>;
  onValidateRef: () => Promise<FormikErrors<IInitialValues> | undefined>;
  setFieldValue: (key: string, value: any) => void;
  triggerDirty: () => void;
}

export const PatientDeletionTabId = 'patientDeletion';

export const getTabOptions = (
  t: any,
  hiddenIds: { [key: string]: boolean }
): Visible[] => {
  return [
    {
      id: 'genericInfo',
      position: 1,
      label: t('menuGenericInfo'),
    },
    {
      id: 'personalInfo',
      position: 2,
      label: t('menuPersonalInfo'),
    },
    {
      id: 'addressInfo',
      position: 3,
      label: t('menuAddressInfo'),
    },
    {
      id: 'contactInfo',
      position: 4,
      label: t('menuContactInfo'),
    },
    {
      id: 'insuranceInfo',
      position: 5,
      label: t('menuInsuranceInfo'),
    },
    {
      id: 'g81EHICInfo',
      position: 6,
      label: t('menuG81EHIC'),
    },
    {
      id: 'doctorInfo',
      position: 7,
      label: t('menuDoctorInfo'),
    },
    {
      id: 'otherInfo',
      position: 8,
      label: t('menuOtherInfo'),
    },
    {
      id: 'visitInfo',
      position: 9,
      label: t('menuVisitInfo'),
    },
    {
      id: 'medicalInfo',
      position: 10,
      label: t('menuMedicalInfo'),
      isEdit: true,
    },
    {
      id: 'employmentInfo',
      position: 11,
      label: t('menuEmploymentInfo'),
      isEdit: true,
    },
    {
      id: PatientDeletionTabId,
      position: 12,
      label: t('menuPatientDeletion'),
      isEdit: true,
    },
  ].filter((item) => !hiddenIds[item.id]);
};

export const getMenuSections = (container: HTMLDivElement | null) => {
  const navList = container?.querySelectorAll<HTMLElement>(
    '.create-patient__menu-item'
  );
  return Array.from(navList || []);
};

export const fillCustomPatientInfoTemplateFileName = (patientId: string) => {
  return `CUSTOM_PATIENT_INFO_TEMPLATE_${patientId}.pdf`;
};

export const updateDoctorInfo = (doctorInfo: DoctorInfoExtend) => {
  if (!doctorInfo) return;
  if (doctorInfo.generalPractitionerDoctorId) {
    doctorInfo.generalPractitionerDoctorId.forEach(async (doctorId, index) => {
      const internalTmp = (employeeStore.employeeProfiles || []).find(
        (item) => item.id === doctorId
      );

      if (!internalTmp && doctorId) {
        const sdavInfo = await getSdavById({
          id: doctorId,
        });

        if (sdavInfo.data?.data) {
          editSdav({
            sdavCatalog: {
              ...sdavInfo.data.data,
              sdavId: doctorId,
              contactInfo: {
                ...sdavInfo.data.data.contactInfo,
                phoneNumber: !isNil(
                  doctorInfo.generalPractitionerDoctor_phoneNumber?.[index]
                )
                  ? doctorInfo.generalPractitionerDoctor_phoneNumber[index]
                  : sdavInfo.data.data.contactInfo.phoneNumber,
                email: !isNil(
                  doctorInfo.generalPractitionerDoctor_email?.[index]
                )
                  ? doctorInfo.generalPractitionerDoctor_email[index]
                  : sdavInfo.data.data.contactInfo.email,
              },
            },
          });
        }
      }
    });
  }
  if (doctorInfo.specialistDoctorId) {
    doctorInfo.specialistDoctorId.forEach(async (doctorId, index) => {
      const internalTmp = (employeeStore.employeeProfiles || []).find(
        (item) => item.id === doctorId
      );

      if (!internalTmp && doctorId) {
        const sdavInfo = await getSdavById({
          id: doctorId,
        });

        if (sdavInfo.data?.data) {
          editSdav({
            sdavCatalog: {
              ...sdavInfo.data.data,
              sdavId: doctorId,
              doctorInfo: {
                ...sdavInfo.data.data.doctorInfo,
                areaOfExpertises: (!isNil(
                  doctorInfo.specialistDoctor_exptertise?.[index]
                )
                  ? doctorInfo.specialistDoctor_exptertise[index]
                  : sdavInfo.data.data.doctorInfo
                    .areaOfExpertises) as unknown as AreaOfExpertise[],
              },
              contactInfo: {
                ...sdavInfo.data.data.contactInfo,
                phoneNumber: !isNil(
                  doctorInfo.specialistDoctor_phoneNumber?.[index]
                )
                  ? doctorInfo.specialistDoctor_phoneNumber[index]
                  : sdavInfo.data.data.contactInfo.phoneNumber,
                email: !isNil(doctorInfo.specialistDoctor_email?.[index])
                  ? doctorInfo.specialistDoctor_email[index]
                  : sdavInfo.data.data.contactInfo.email,
              },
            },
          });
        }
      }
    });
  }
};
