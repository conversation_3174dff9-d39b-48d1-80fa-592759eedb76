import { <PERSON>Field, Field } from 'formik';
import React, { memo, useCallback, useEffect, useState, useMemo } from 'react';
import { isEmpty, debounce } from 'lodash';

import type patientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  Flex,
  ReactSelect,
  IMenuItem,
  FormGroup2,
  MultiSelect,
} from '@tutum/design-system/components';
import {
  Collapse,
  Icon,
  InputGroup,
} from '@tutum/design-system/components/Core';
import { scaleSpace } from '@tutum/design-system/styles';
import {
  checkGermanyPostalCode,
  useMutationGetStatesFromPostalCode,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import {
  AddressInfo,
  PostOfficeBox,
} from '@tutum/hermes/bff/patient_profile_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import CreatePatientUtil from '@tutum/infrastructure/utils/form.util';
import { IMvzTheme } from '@tutum/mvz/theme';
import { keyboardTriggerClick } from '@tutum/mvz/_utils/accessibility';
import countryType from '@tutum/mvz/constant/country.type';
import {
  MAX_LENGTH_POSTCODE,
  MIN_LENGTH_POSTCODE,
} from '../CreatePatient.helper';
import {
  settingActions,
  useSettingStore,
} from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.store';
import { AdditionalName } from '@tutum/hermes/bff/patient_profile_common';
import { IntendWord } from '@tutum/hermes/bff/patient_profile_common';
import { backspace2ClearSelectBox } from '@tutum/mvz/_utils/accessibility';

const MAX_ADDITIONAL_NAMES = 2;

export interface IAddressInformationFormProps {
  className?: string;
  theme?: IMvzTheme;
  submitCount?: number;
  errors?: any;
  touched?: any;
  addressInfo: AddressInfo;
  postOfficeBox: PostOfficeBox;
  isDisable?: boolean;
  isValidToValidatePostalCode: boolean;
  isNeedRequiredAddress: boolean;
  isSubmitting: boolean;
  setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
  setOpenSection: () => void;
}

const VALIDATE_FIELDS_ADDRESS = [
  'addressInfo.address.street',
  'addressInfo.address.number',
  'addressInfo.address.postCode',
  'addressInfo.address.city',
  'addressInfo.address.countryCode',
];

const VALIDATE_FIELDS_POST_OFFICE_BOX = ['postOfficeBox.postCode'];

const VALIDATE_FIELDS_BILLING_ADDRESS = ['addressInfo.billingAddress.postCode'];

const AddressInformationForm = (
  props: IAddressInformationFormProps &
    II18nFixedNamespace<keyof typeof patientManagementI18n.AddressInformation>
) => {
  const {
    className,
    submitCount,
    errors,
    touched,
    t,
    isDisable,
    addressInfo,
    postOfficeBox,
    isValidToValidatePostalCode = true,
    isNeedRequiredAddress,
    isSubmitting,
    setFieldValue,
    setOpenSection,
  } = props;

  const { t: tPI } = I18n.useTranslation<
    keyof typeof patientManagementI18n.PersonalInformation
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'PersonalInformation',
  });
  const [isExpandAddress, setExpandAddress] = useState(true);
  const [isExpandPostOffice, setExpandPostOffice] = useState(false);
  const [isExpandBillingAddress, setExpandBillingAddress] = useState(false);
  const [isFirstFetchingStates, setFirstFetchingStates] =
    useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');

  const createPatientStore = useSettingStore();

  const { data, isPending, isSuccess, mutate } =
    useMutationGetStatesFromPostalCode({
      onSuccess: (data) => {
        if (addressInfo?.address.city) {
          setInputValue(addressInfo?.address.city);
          return;
        }

        const value = data?.data?.states?.length ? data.data.states[0] : '';

        setFieldValue('addressInfo.address.city', value);
        setInputValue(value);
      },
    });

  const cityList = useMemo(() => {
    if (
      !isSuccess ||
      isEmpty(data?.data?.states) ||
      !addressInfo.address.postCode
    ) {
      return [];
    }

    return data?.data?.states.map((state) => {
      return {
        label: state,
        value: state,
      };
    });
  }, [isSuccess, data?.data?.states, addressInfo.address.postCode]);

  const debouncedFetchData = useCallback(
    debounce(
      async (
        postalCode: string,
        callback: (data: { isValid: boolean }) => void
      ) => {
        const { data } = await checkGermanyPostalCode({
          postalCode,
        });
        callback({ isValid: data.isValid });
      },
      500
    ),
    []
  );

  const debouncedGetCity = useCallback(
    debounce(async (postalCode: string) => {
      mutate({
        postalCode,
      });
    }, 500),
    []
  );

  useEffect(() => {
    if (
      addressInfo.address.postCode &&
      addressInfo.address.countryCode === countryType.COUNTRY_DEFAULT_CODE &&
      isValidToValidatePostalCode
    ) {
      debouncedFetchData(addressInfo.address.postCode, ({ isValid }) => {
        settingActions.setGermanyPostalCodeValid(isValid);
      });
    } else {
      settingActions.setGermanyPostalCodeValid(true);
    }
  }, [
    addressInfo.address.postCode,
    addressInfo.address.countryCode,
    isValidToValidatePostalCode,
  ]);

  useEffect(() => {
    if (
      postOfficeBox?.postCode &&
      postOfficeBox?.countryCode === countryType.COUNTRY_DEFAULT_CODE &&
      isValidToValidatePostalCode
    ) {
      debouncedFetchData(postOfficeBox?.postCode, ({ isValid }) => {
        settingActions.setGermanyPostOfficePostalCodeValid(isValid);
      });
    } else {
      settingActions.setGermanyPostOfficePostalCodeValid(true);
    }
  }, [
    postOfficeBox.postCode,
    postOfficeBox.countryCode,
    isValidToValidatePostalCode,
  ]);

  useEffect(() => {
    if (!addressInfo.address.postCode || isFirstFetchingStates || isDisable) {
      return;
    }

    setFirstFetchingStates(true);
    debouncedGetCity(addressInfo.address.postCode);
  }, [
    isDisable,
    isFirstFetchingStates,
    addressInfo.address.postCode,
    debouncedGetCity,
  ]);

  useEffect(() => {
    if (!isSubmitting) {
      return;
    }

    const hasErrorAddress = VALIDATE_FIELDS_ADDRESS.some(
      (field) => !!errors[field]
    );
    const hasErrorPostOfficeBox = VALIDATE_FIELDS_POST_OFFICE_BOX.some(
      (field) => !!errors[field]
    );
    const hasErrorBillingAddress = VALIDATE_FIELDS_BILLING_ADDRESS.some(
      (field) => !!errors[field]
    );

    if (hasErrorAddress || hasErrorPostOfficeBox || hasErrorBillingAddress) {
      setOpenSection();
    }

    if (hasErrorAddress) {
      setExpandAddress(true);
    }

    if (hasErrorPostOfficeBox) {
      setExpandPostOffice(true);
    }

    if (hasErrorBillingAddress) {
      setExpandBillingAddress(true);
    }
  }, [errors, isSubmitting]);

  const gpAddress = (
    <Flex column mt={scaleSpace(4)}>
      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          submitCount={submitCount}
          errors={errors}
          touched={touched}
          name="addressInfo.address.street"
          label={t('street')}
          isRequired={isNeedRequiredAddress}
        >
          <Field name="addressInfo.address.street">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  data-tab-id={field.name}
                  disabled={isDisable}
                  value={field.value || ''}
                  maxLength={55}
                  minLength={2}
                  id="street"
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          submitCount={submitCount}
          errors={errors}
          touched={touched}
          name="addressInfo.address.number"
          label={t('houseNumber')}
          isRequired={isNeedRequiredAddress}
        >
          <Field name="addressInfo.address.number">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisable}
                  value={field.value || ''}
                  maxLength={55}
                  minLength={1}
                  id="addressNumber"
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>

      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="addressInfo.address.postCode"
          label={t('postalCode')}
          submitCount={submitCount}
          errors={errors}
          touched={touched}
          warning={
            !!addressInfo.address.postCode?.length &&
              !createPatientStore.isGermanyPostalCodeValid
              ? t('sdplzInvalid')
              : null
          }
          isRequired={
            addressInfo.address.countryCode === countryType.COUNTRY_DEFAULT_CODE
          }
        >
          <Field name="addressInfo.address.postCode">
            {({ field, form }) => {
              return (
                <InputGroup
                  {...field}
                  disabled={isDisable}
                  value={field.value || ''}
                  minLength={MIN_LENGTH_POSTCODE}
                  maxLength={MAX_LENGTH_POSTCODE}
                  id="postcode"
                  onChange={(e) => {
                    const value = e.target.value;

                    form.setFieldValue(field.name, value);
                    form.setFieldValue('addressInfo.address.city', '');

                    if (!value) {
                      setInputValue('');
                      return;
                    }

                    debouncedGetCity(value);
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          errors={errors}
          touched={touched}
          submitCount={submitCount}
          name="addressInfo.address.city"
          label={t('city')}
          isRequired={isNeedRequiredAddress}
        >
          <Field name="addressInfo.address.city">
            {({ field, form }) => {
              return (
                <ReactSelect
                  className="address-city"
                  id="address-city"
                  instanceId={'address-city'}
                  isSearchable
                  selectedValue={field.value}
                  isDisabled={isDisable}
                  items={cityList}
                  isLoading={isPending}
                  inputValue={inputValue}
                  filterOption={() => {
                    // don't filter when search
                    return true;
                  }}
                  onInputChange={(value, { action, prevInputValue }) => {
                    switch (action) {
                      case 'input-change':
                        setInputValue(value);
                        return inputValue;
                      case 'input-blur':
                        form.setFieldValue(field.name, inputValue);
                        return inputValue;
                      default:
                        return prevInputValue;
                    }
                  }}
                  onItemSelect={(item: IMenuItem) => {
                    const value = (item?.value || '') as string;

                    form.setFieldValue(field.name, value);
                    setInputValue(value);
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          className="select-country"
          errors={errors}
          touched={touched}
          submitCount={submitCount}
          name="addressInfo.address.countryCode"
          label={t('country')}
          isRequired={isNeedRequiredAddress}
        >
          <Field name="addressInfo.address.countryCode">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="addressInfo-country"
                  instanceId={'country'}
                  isSearchable={false}
                  selectedValue={field.value}
                  isDisabled={isDisable}
                  items={countryType.COUNTRY_LIST}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>

      <Flex>
        <FormGroup2
          name="addressInfo.address.additionalAddressInfo"
          label={t('additionalAddressInfo')}
        >
          <Field name="addressInfo.address.additionalAddressInfo">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={45}
                  data-tab-id={field.name}
                  disabled={isDisable}
                  id="additionalAddressInfo"
                />
              );
            }}
          </Field>
        </FormGroup2>
      </Flex>
      <Flex>
        <FormGroup2 name="addressInfo.address.distance" label={t('distance')}>
          <FastField name="addressInfo.address.distance">
            {({ field }) => {
              return (
                <div className="input-txt-overlay">
                  <InputGroup
                    {...field}
                    value={field.value || ''}
                    maxLength={45}
                    data-tab-id={field.name}
                    id="distance"
                  />
                  <span>{t('km')}</span>
                </div>
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
    </Flex>
  );

  const gpPostOffice = (
    <Flex column mt={scaleSpace(4)}>
      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="postOfficeBox.postCode"
          label={t('postalCode')}
          submitCount={submitCount}
          errors={errors}
          touched={touched}
          warning={
            !!postOfficeBox?.postCode?.length &&
              !createPatientStore.isGermanyPostOfficePostalCodeValid
              ? t('sdplzInvalid')
              : null
          }
        >
          <Field name="postOfficeBox.postCode">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  minLength={MIN_LENGTH_POSTCODE}
                  maxLength={MAX_LENGTH_POSTCODE}
                  disabled={isDisable}
                  id="postOfficeBox-postcode"
                />
              );
            }}
          </Field>
        </FormGroup2>
        <FormGroup2
          name="postOfficeBox.placeOfResidence"
          label={t('placeOfResidence')}
        >
          <FastField name="postOfficeBox.placeOfResidence">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  minLength={2}
                  maxLength={40}
                  disabled={isDisable}
                  id="placeOfResidence"
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
      <Flex gap={scaleSpace(4)}>
        <FormGroup2 name="postOfficeBox.officeBox" label={t('officeBox')}>
          <FastField name="postOfficeBox.officeBox">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  id="officeBox"
                  disabled={isDisable}
                  maxLength={8}
                />
              );
            }}
          </FastField>
        </FormGroup2>
        <FormGroup2 name="postOfficeBox.countryCode" label={t('country')}>
          <FastField name="postOfficeBox.countryCode">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="postOfficeBox-country"
                  instanceId={'country'}
                  isSearchable={false}
                  selectedValue={field.value}
                  items={countryType.COUNTRY_LIST}
                  isDisabled={isDisable}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
    </Flex>
  );

  const gpBillingAddress = (
    <Flex mt={scaleSpace(4)} column>
      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="addressInfo.billingAddress.firstName"
          label={tPI('firstName')}
        >
          <FastField name="addressInfo.billingAddress.firstName">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={45}
                  fill
                  id="billingAddressFirstName"
                />
              );
            }}
          </FastField>
        </FormGroup2>
        <FormGroup2
          name="addressInfo.billingAddress.lastName"
          label={tPI('lastName')}
        >
          <FastField name="addressInfo.billingAddress.lastName">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={45}
                  fill
                  id="billingAddressLastName"
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>

      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="addressInfo.billingAddress.title"
          label={tPI('title')}
        >
          <FastField name="addressInfo.billingAddress.title">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={45}
                  id="billingAddressTitle"
                />
              );
            }}
          </FastField>
        </FormGroup2>

        <FormGroup2
          name="addressInfo.billingAddress.intendWord"
          label={tPI('intendWord')}
        >
          <FastField name="addressInfo.billingAddress.intendWord">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="billingAddressIntendWord"
                  instanceId={'billingAddressIntendWord'}
                  isSearchable={false}
                  onKeyDown={(evt) =>
                    backspace2ClearSelectBox(evt, () =>
                      form.setFieldValue(field.name, undefined)
                    )
                  }
                  selectedValue={field.value}
                  items={Object.values(IntendWord).map(
                    (item): IMenuItem => ({
                      value: item,
                      label: item,
                    })
                  )}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>

      <Flex>
        <FormGroup2
          name="addressInfo.billingAddress.additionalName"
          label={tPI('additionalName')}
        >
          <FastField name="addressInfo.billingAddress.additionalNames">
            {({ field, form }) => {
              const additionalNamesOptions = Object.values(AdditionalName).map(
                (item): IMenuItem => ({
                  value: item,
                  label: item,
                  isDisabled:
                    !!field.value?.[0] &&
                    field.value?.length === MAX_ADDITIONAL_NAMES &&
                    !field.value?.find((value) => value === item),
                })
              );

              return (
                <MultiSelect
                  menuPosition="absolute"
                  id="billingAddressAdditionalNames"
                  instanceId="billingAddressAdditionalNames"
                  isSearchable={false}
                  value={additionalNamesOptions.filter((option) =>
                    field.value?.includes(option.value)
                  )}
                  options={additionalNamesOptions}
                  onChange={(newValue: IMenuItem[]) => {
                    form.setFieldValue(
                      field.name,
                      newValue.map((item) => item.value)
                    );
                  }}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>

      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="addressInfo.billingAddress.street"
          label={t('street')}
        >
          <FastField name="addressInfo.billingAddress.street">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={55}
                  minLength={2}
                  id="billingAddressStreet"
                />
              );
            }}
          </FastField>
        </FormGroup2>

        <FormGroup2
          name="addressInfo.billingAddress.number"
          label={t('houseNumber')}
        >
          <FastField name="addressInfo.billingAddress.number">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={55}
                  minLength={1}
                  id="billingAddressNumber"
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>

      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="addressInfo.billingAddress.postCode"
          label={t('postalCode')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
        >
          <FastField name="addressInfo.billingAddress.postCode">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  minLength={MIN_LENGTH_POSTCODE}
                  maxLength={MAX_LENGTH_POSTCODE}
                  intent={CreatePatientUtil.getFormInputIntent(
                    submitCount,
                    touched.addressInfo?.billingAddress?.postCode,
                    errors['addressInfo.billingAddress.postCode']
                  )}
                  id="billingAddressPostcode"
                />
              );
            }}
          </FastField>
        </FormGroup2>

        <FormGroup2 name="addressInfo.billingAddress.city" label={t('city')}>
          <FastField name="addressInfo.billingAddress.city">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  minLength={2}
                  maxLength={40}
                  id="billingAddressCity"
                />
              );
            }}
          </FastField>
        </FormGroup2>

        <FormGroup2
          className="select-country"
          name="addressInfo.billingAddress.countryCode"
          label={t('country')}
        >
          <FastField name="addressInfo.billingAddress.countryCode">
            {({ field, form }) => {
              return (
                <ReactSelect
                  id="billingAddressCountry"
                  instanceId={'billingAddressCountry'}
                  isSearchable={false}
                  selectedValue={field.value}
                  items={countryType.COUNTRY_LIST}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
    </Flex>
  );

  return (
    <Flex column className={className}>
      <Flex column className="group">
        <h3
          className="label-section-patient-form"
          onClick={() => setExpandAddress(!isExpandAddress)}
        >
          {t('address')}
          <Icon icon={isExpandAddress ? 'chevron-up' : 'chevron-down'} />
        </h3>
        <Collapse keepChildrenMounted={true} isOpen={isExpandAddress}>
          {gpAddress}
        </Collapse>
      </Flex>
      <Flex column className="group">
        <h3
          id="section_post_office"
          className="label-section-patient-form"
          onClick={() => setExpandPostOffice(!isExpandPostOffice)}
        >
          {t('postOffice')}
          <Icon icon={isExpandPostOffice ? 'chevron-up' : 'chevron-down'} />
        </h3>
        <Collapse keepChildrenMounted={true} isOpen={isExpandPostOffice}>
          {gpPostOffice}
        </Collapse>
      </Flex>
      <Flex column className="group">
        <h3
          className="label-section-patient-form"
          id="section_billing_address"
          onClick={() => setExpandBillingAddress(!isExpandBillingAddress)}
          onKeyDown={keyboardTriggerClick}
        >
          {t('billingAddress')}
          <Icon icon={isExpandBillingAddress ? 'chevron-up' : 'chevron-down'} />
        </h3>
        <Collapse keepChildrenMounted={true} isOpen={isExpandBillingAddress}>
          {gpBillingAddress}
        </Collapse>
      </Flex>
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(AddressInformationForm, {
    namespace: 'PatientProfileCreation',
    nestedTrans: 'AddressInformation',
  })
);
