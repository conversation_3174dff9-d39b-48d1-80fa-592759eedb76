import { FastField } from 'formik';
import React, { useMemo, useState, memo, useEffect } from 'react';

import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  Box,
  Flex,
  Svg,
  ReactSelect,
  IMenuItem,
  FormGroup2,
  MessageBar,
  H3,
} from '@tutum/design-system/components';
import {
  Collapse,
  Icon,
  InputGroup,
  Radio,
  RadioGroup,
  Switch,
} from '@tutum/design-system/components/Core';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { IMvzTheme } from '@tutum/mvz/theme';
import CreatePatientUtil from '@tutum/infrastructure/utils/form.util';
import {
  JobStatus,
  WorkActivity1,
  WorkActivity2,
} from '@tutum/hermes/bff/patient_profile_common';
import countryType from '@tutum/mvz/constant/country.type';
import { IPatientProfile } from '../../types/profile.type';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { backspace2ClearSelectBox } from '@tutum/mvz/_utils/accessibility';
import { scaleSpace } from '@tutum/design-system/styles';
import {
  MAX_LENGTH_POSTCODE,
  MIN_LENGTH_POSTCODE,
} from '../CreatePatient.helper';
import { registerActionChainElementId } from '@tutum/mvz/module_action-chain';
import { PatientJobElementId } from '@tutum/mvz/module_action-chain/actions/patientJob/type';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';

const alert_circle_solid = '/images/alert-circle-solid.svg';

const VALIDATE_FIELDS = ['employmentInfo.companyAddress.postCode'];

export interface IEmploymentInfoFormProps {
  className?: string;
  theme?: IMvzTheme;
  submitCount?: number;
  values?: any;
  errors?: any;
  touched?: any;
  setFieldTouched?: any;
  setFieldError?: any;
  setErrMessage?: any;
  isCreatePatient?: boolean;
  selectedPatient?: IPatientProfile;
  isEmployed: boolean;
  isSubmitting: boolean;
  isInForm?: boolean;
  setOpenSection: () => void;
}

export interface IEmploymentInfoFormState {
  isSubmissionConfirmed: false;
  errors?: any;
}

const EmploymentInfoForm = ({
  t,
  submitCount,
  className,
  values,
  touched,
  errors,
  selectedPatient,
  isEmployed,
  isSubmitting,
  isInForm,
  setOpenSection,
}: IEmploymentInfoFormProps &
  II18nFixedNamespace<
    keyof typeof PatientManagementI18n.EmploymentInformation
  > &
  IEmploymentInfoFormState) => {
  const [isExpand, setExpand] = useState(true);

  const JobStatusList = Object.keys(JobStatus).map(
    (jsKey: keyof typeof PatientManagementI18n.EmploymentInformation) => {
      const value = JobStatus[jsKey];
      return {
        label: t(jsKey),
        value,
      };
    }
  );

  const renderEmploymentInfo = (
    <Box>
      <Flex>
        <FormGroup2 name="jobStatus" label={t('jobStatus')}>
          <FastField name="employmentInfo.jobStatus">
            {({ field, form }) => {
              return (
                <ReactSelect
                  isSearchable={false}
                  selectedValue={field.value}
                  onKeyDown={(evt) =>
                    backspace2ClearSelectBox(evt, () =>
                      form.setFieldValue(field.name, undefined)
                    )
                  }
                  items={JobStatusList}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>

      <Box>
        <FormGroup2 name="occupation" label={t('occupation')}>
          <FastField name="employmentInfo.occupation">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  data-tab-id={field.name}
                  {...registerActionChainElementId(
                    PatientJobElementId.FOCUS_FIELD
                  )}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Box>
      <Box>
        <FormGroup2
          name="employmentInfo.workingHourInWeek"
          label={t('workingHourInWeek')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
        >
          <FastField name="employmentInfo.workingHourInWeek">
            {({ field, form }) => {
              return (
                <NumberInput
                  {...field}
                  isFloat
                  maxLength={5}
                  defaultValue={field.value}
                  onChange={(v) => {
                    const value = +v.target.value.replace(',', '.');

                    form.setFieldValue(field.name, value);
                  }}
                  data-tab-id={field.name}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Box>

      <div>
        <div className="activity-profile">{t('activityProfile')}</div>
        <Box>
          <Flex auto column>
            <FormGroup2 name="workActivity1" label={t('workActivity1')}>
              <FastField name="employmentInfo.workActivity1">
                {({ field, form }) => {
                  const { setFieldValue } = form;
                  const onGenderChange = (
                    event: React.FormEvent<HTMLInputElement>
                  ) => {
                    const value = event.currentTarget.value;
                    setFieldValue(field.name, value);
                  };
                  return (
                    <RadioGroup
                      onChange={onGenderChange}
                      inline
                      className="gender-group"
                      selectedValue={field.value}
                    >
                      <Radio
                        label={t('workActivityPhysical')}
                        value={WorkActivity1.Physical}
                      />
                      <Radio
                        label={t('workActivityMental')}
                        value={WorkActivity1.Mental}
                      />
                    </RadioGroup>
                  );
                }}
              </FastField>
            </FormGroup2>
          </Flex>
        </Box>
        <Box>
          <Flex auto column>
            <FormGroup2 name="workActivity2" label={t('workActivity2')}>
              <FastField name="employmentInfo.workActivity2">
                {({ field, form }) => {
                  const { setFieldValue } = form;
                  const onGenderChange = (
                    event: React.FormEvent<HTMLInputElement>
                  ) => {
                    const value = event.currentTarget.value;
                    setFieldValue(field.name, value);
                  };
                  return (
                    <RadioGroup
                      onChange={onGenderChange}
                      inline
                      className="gender-group"
                      selectedValue={field.value}
                    >
                      <Radio
                        label={t('workActivityStanding')}
                        value={WorkActivity2.Standing}
                      />
                      <Radio
                        label={t('workActivitySitting')}
                        value={WorkActivity2.Sitting}
                      />
                    </RadioGroup>
                  );
                }}
              </FastField>
            </FormGroup2>
          </Flex>
        </Box>
      </div>
    </Box>
  );

  const renderCompanyInfo = (
    <>
      <Flex>
        <FormGroup2 name="employer" label={t('employer')}>
          <FastField name="employmentInfo.companyAddress.employer">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  data-tab-id={field.name}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="employmentInfo.companyAddress.street"
          label={t('street')}
        >
          <FastField name="employmentInfo.companyAddress.street">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={55}
                  minLength={2}
                />
              );
            }}
          </FastField>
        </FormGroup2>
        <FormGroup2
          name="employmentInfo.companyAddress.number"
          label={t('houseNumber')}
        >
          <FastField name="employmentInfo.companyAddress.number">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={55}
                  minLength={2}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>

      <Flex gap={scaleSpace(4)}>
        <FormGroup2
          name="employmentInfo.companyAddress.postCode"
          label={t('postalCode')}
          submitCount={submitCount}
          touched={touched}
          errors={errors}
        >
          <FastField name="employmentInfo.companyAddress.postCode">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  minLength={MIN_LENGTH_POSTCODE}
                  maxLength={MAX_LENGTH_POSTCODE}
                  intent={CreatePatientUtil.getFormInputIntent(
                    submitCount,
                    touched.employmentInfo?.companyAddress?.postCode,
                    errors['employmentInfo.companyAddress.postCode']
                  )}
                />
              );
            }}
          </FastField>
        </FormGroup2>
        <FormGroup2 name="employmentInfo.companyAddress.city" label={t('city')}>
          <FastField name="employmentInfo.companyAddress.city">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  minLength={2}
                  maxLength={40}
                />
              );
            }}
          </FastField>
        </FormGroup2>
        <FormGroup2
          className="select-country"
          name="employmentInfo.companyAddress.countryCode"
          label={t('country')}
        >
          <FastField name="employmentInfo.companyAddress.countryCode">
            {({ field, form }) => {
              return (
                <ReactSelect
                  isSearchable={false}
                  selectedValue={field.value}
                  items={countryType.COUNTRY_LIST}
                  onItemSelect={(item: IMenuItem) => {
                    form.setFieldValue(field.name, item.value);
                  }}
                  menuPlacement="top"
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Flex>
    </>
  );

  const renderPatientEmploymentOutOfDate = useMemo(() => {
    if (selectedPatient && selectedPatient.employmentInfoUpdatedAt) {
      const now = DatetimeUtil.date();
      const lastUpdated = new Date(selectedPatient.employmentInfoUpdatedAt);
      const durationDate = DatetimeUtil.getDayDuration(
        lastUpdated,
        now
      ).asDays();
      if (durationDate >= 365) {
        return (
          <Flex className="call-out-date">
            <div className="bp5-callout bp5-intent-warning">
              <Flex>
                <div className="call-out-date__icon">
                  <Svg src={alert_circle_solid} />
                </div>
                <div className="call-out-date__content">
                  <p className="bp5-heading">{t('employmentOutOfDate')}</p>
                  <p>{t('employmentOutOfDateContentPleaseUpdate')}</p>
                </div>
              </Flex>
            </div>
          </Flex>
        );
      }
    }

    return null;
  }, [JSON.stringify(selectedPatient)]);

  useEffect(() => {
    if (!isSubmitting) {
      return;
    }

    const hasErrorInSection = VALIDATE_FIELDS.some((field) => !!errors[field]);

    if (hasErrorInSection) {
      setOpenSection();
    }
  }, [errors, isSubmitting]);

  return (
    <Flex column className={className}>
      {isInForm ? (
        !!values?.employmentInfo?.specialProblemAtWork && (
          <Flex mb={24}>
            <MessageBar
              type="info"
              content={values?.employmentInfo?.specialProblemAtWork}
              hasBullet={false}
            />
          </Flex>
        )
      ) : (
        <>
          <Flex>
            <FormGroup2
              name="specialProblemAtWork"
              label={t('specialProblemAtWork')}
            >
              <FastField name="employmentInfo.specialProblemAtWork">
                {({ field }) => {
                  return (
                    <InputGroup
                      {...field}
                      value={field.value || ''}
                      data-tab-id={field.name}
                    />
                  );
                }}
              </FastField>
            </FormGroup2>
          </Flex>
          <Flex
            {...registerActionChainElementId(
              PatientJobElementId.CHECKBOX_FIELD
            )}
          >
            <FormGroup2 name="employmentInfo.isEmployed">
              <FastField name="employmentInfo.isEmployed">
                {({ field, form }) => {
                  return (
                    <Switch
                      name={field.name}
                      checked={field.value}
                      label={t('isEmployed')}
                      onChange={() => {
                        form.setFieldValue(field.name, !isEmployed);
                      }}
                    />
                  );
                }}
              </FastField>
            </FormGroup2>
          </Flex>
        </>
      )}
      {isEmployed && (
        <>
          {!isInForm && renderPatientEmploymentOutOfDate}
          <Flex
            column
            className={getCssClass({
              group: !isInForm,
            })}
          >
            {!isInForm ? (
              <>
                <H3 onClick={() => setExpand(!isExpand)}>
                  {t('employmentInfo')}
                  <Icon icon={isExpand ? 'chevron-up' : 'chevron-down'} />
                </H3>
                <Collapse keepChildrenMounted={true} isOpen={isExpand}>
                  {renderEmploymentInfo}
                </Collapse>
              </>
            ) : (
              <>
                <H3
                  className="label-section-patient-form"
                  onClick={() => setExpand(!isExpand)}
                >
                  {t('employmentInfo')}
                </H3>
                {renderEmploymentInfo}
              </>
            )}
          </Flex>
          {!isInForm && (
            <Flex column className="group">
              <h3
                className="label-section-patient-form"
                onClick={() => setExpand(!isExpand)}
              >
                {t('companyInfo')}
                <Icon icon={isExpand ? 'chevron-up' : 'chevron-down'} />
              </h3>
              <Collapse keepChildrenMounted={true} isOpen={isExpand}>
                {renderCompanyInfo}
              </Collapse>
            </Flex>
          )}
        </>
      )}
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(EmploymentInfoForm, {
    namespace: 'PatientProfileCreation',
    nestedTrans: 'EmploymentInformation',
  })
);
