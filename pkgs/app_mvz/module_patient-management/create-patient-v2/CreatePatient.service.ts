import moment from 'moment';
import cloneDeep from 'lodash/cloneDeep';

import { getUTCMilliseconds } from '@tutum/design-system/infrastructure/utils';
import {
  createPatientProfileV2,
  CreatePatientProfileV2Request,
  CreatePatientProfileV2Response,
  updatePatientProfileV2,
  UpdatePatientProfileV2Request,
  UpdatePatientProfileV2Response,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { transformInsurances } from './CreatePatient.helper';

const handleNumberValue = (value: string | number) => {
  if (value) return +value;
  return 0;
};

export const fomikDataTransform = (
  formikData
): CreatePatientProfileV2Request => {
  const rsData: CreatePatientProfileV2Request = {
    patientInfo: {
      ...cloneDeep(formikData),
    },
  };
  const { patientInfo } = rsData;

  patientInfo.insuranceInfos =
    formikData?.insuranceInfos && formikData?.insuranceInfos.length
      ? transformInsurances(formikData.insuranceInfos)
      : [];

  patientInfo.genericInfo = {
    patientType: patientInfo.genericInfo.patientType,
    lastCardReadinDate: patientInfo.genericInfo.lastCardReadinDate
      ? new Date(patientInfo.genericInfo.lastCardReadinDate).getTime()
      : undefined,
  };
  patientInfo.personalInfo.dOB = patientInfo.personalInfo.dOB
    ? getUTCMilliseconds(
      moment(patientInfo.personalInfo.dOB, DATE_FORMAT).toDate()
    )
    : 0;
  patientInfo.doctorInfo.specialistDoctorId =
    patientInfo.doctorInfo.specialistDoctorId?.filter(
      (item) => item?.length > 0
    );
  patientInfo.doctorInfo.generalPractitionerDoctorId =
    patientInfo.doctorInfo.generalPractitionerDoctorId?.filter(
      (item) => item?.length > 0
    );

  patientInfo.addressInfo.address.distance = handleNumberValue(
    patientInfo?.addressInfo?.address?.distance || ''
  );

  patientInfo.personalInfo.dOB = new Date(
    patientInfo.personalInfo.dOB
  ).getTime();

  const { date, month, year, isValidDOB } =
    patientInfo.personalInfo.dateOfBirth;

  patientInfo.personalInfo.dateOfBirth = {
    isValidDOB: isValidDOB,
    date: +date ? +date : undefined,
    month: +month ? +month : undefined,
    year: +year ? +year : undefined,
  };
  return rsData;
};

export const createPatient = async (
  patient: CreatePatientProfileV2Request
): Promise<CreatePatientProfileV2Response> => {
  const result = await createPatientProfileV2(patient);
  return result.data;
};

export const updatePatient = async (
  patient: UpdatePatientProfileV2Request
): Promise<UpdatePatientProfileV2Response> => {
  const result = await updatePatientProfileV2(patient);
  return result.data;
};

export default {
  createPatient,
  updatePatient,
  fomikDataTransform,
};
