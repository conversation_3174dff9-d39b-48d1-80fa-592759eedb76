import { useState, useEffect, useContext } from 'react';

import type CreatePatientI18N from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';

import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { IContractDoctors } from '@tutum/mvz/module_patient-management/types/contract.type';
import I18n from '@tutum/infrastructure/i18n';
import { BodyTextM, Flex } from '@tutum/design-system/components';
import { useMutationCreate } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import { TimelineModel } from '@tutum/hermes/bff/legacy/timeline_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import {
  formatValueDateOfBirth,
  getAge,
} from '@tutum/design-system/infrastructure/utils';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import InfoConfirmDialog from '@tutum/design-system/components/Modal/info-confirm-dialog/InfoConfirmDialog';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import {
  useMutationSaveSettings,
  useQueryGetSettings,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_settings';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';

interface Props {
  patient: IPatientProfile;
  contracts: IContractDoctors[];
}

const CheckInformColonoscopyInfo = (props: Props) => {
  const { patient, contracts = [] } = props;

  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();

  const { t } = I18n.useTranslation<
    keyof typeof CreatePatientI18N.CreatePatient
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'CreatePatient',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const currentSchein = useCurrentSchein();
  const { isContractSupport } = useCheckContractSupport(
    ['VSST1390'],
    [currentSchein?.hzvContractId]
  );

  const [isSupportVSST1390, setIsSupportVSST1390] = useState<boolean>(false);
  const { mutate } = useMutationCreate();
  const { mutate: saveSetting } = useMutationSaveSettings();
  const { data } = useQueryGetSettings({ patientId: patient.id });

  const checkSupportVSST1390 = async (patient: IPatientProfile) => {
    if (!data?.settings) {
      return;
    }

    const isAlreadyInformed = (data?.settings ?? []).find(
      (s) => !!s.hzvInformed?.['VSST1390']
    );
    const patientAge = getAge(
      formatValueDateOfBirth(patient.patientInfo.personalInfo.dateOfBirth)
    );
    const isAgeMatch = patientAge >= 51 && patientAge < 65;

    setIsSupportVSST1390(isContractSupport && !isAlreadyInformed && isAgeMatch);
  };

  useEffect(() => {
    if (patient.patientInfo?.personalInfo) {
      checkSupportVSST1390(patient);
    }
  }, [data, isContractSupport]);

  const onCreateTimeline = (content: string) => {
    const now = datetimeUtil.now();
    const year = Number(datetimeUtil.getYear(now));
    const quarter = datetimeUtil.getQuarter(now);

    const payload: TimelineModel = {
      patientId: patient.id,
      createdAt: now,
      auditLogs: [],
      treatmentDoctorId: currentLoggedinUser.id,
      quarter,
      year,
      encounterNoteTimeline: {
        sortOrder: 0,
        note: content,
        command: 'N',
      },
      selectedDate: now,
    };
    mutate({ timelineModel: payload });
    return { year, quarter };
  };

  const onClose = () => setIsSupportVSST1390(false);

  const onUpdateSetting = (isConfirm: boolean) => {
    const { year, quarter } = onCreateTimeline(
      t(isConfirm ? 'entryInformed' : 'entryNotInformed')
    );
    saveSetting({
      patientId: patient.id,
      settings: [
        {
          favContractOnlineChecked: [],
          year,
          quarter,
          hzvInformed: {
            VSST1390: true,
          },
        },
      ],
    });
  };

  const onHandleCancel = () => {
    onUpdateSetting(false);
    onClose();
  };

  const onHandleInform = () => {
    onUpdateSetting(true);
    onClose();
  };

  return (
    <InfoConfirmDialog
      isOpen={isSupportVSST1390}
      onClose={onHandleCancel}
      onConfirm={onHandleInform}
      isShowIconTitle={false}
      isCloseButtonShown={false}
      confirmText={tButtonActions('yesInform')}
      title={t('titleColonoscopy')}
    >
      <Flex column>
        <BodyTextM margin={'24px 0 0 0'}>
          {t('contentColonoscopy')}
          <br />
          <span>
            <a href="http://www.medi-arztsuche.de">www.medi-arztsuche.de</a>
          </span>
        </BodyTextM>
        <br />
      </Flex>
    </InfoConfirmDialog>
  );
};

export default CheckInformColonoscopyInfo;
