import React, { useEffect, memo, useContext } from 'react';
import {
  Box,
  Flex,
  H3,
  FormGroup2,
  ReactSelect,
  IMenuItem,
} from '@tutum/design-system/components';
import {
  employeeActions,
  useEmployeeStore,
} from '@tutum/mvz/hooks/useEmployee';
import { IMvzTheme } from '@tutum/mvz/theme';
import { FastField, Field } from 'formik';
import {
  Suggest,
} from '@tutum/design-system/components/Select';
import { InputGroup } from '@tutum/design-system/components/Core';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { IEmployeeProfile, UserType } from '@tutum/mvz/types/profile.type';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { GetNullUUID } from '@tutum/design-system/infrastructure/utils';

export interface IVisitInformationFormProps {
  className?: string;
  theme?: IMvzTheme;
  submitCount?: number;
  errors?: any;
  touched?: any;
  setFieldTouched?: any;
  setFieldError?: any;
  setErrMessage?: any;
  isCreatePatient?: boolean;
}

export interface IVisitInformationFormState {
  isSubmissionConfirmed: false;
  errors?: any;
}
const SearchDoctor = Suggest.ofType<IEmployeeProfile>();

const VisitInformationForm = ({
  t,
  className,
}: IVisitInformationFormProps &
  II18nFixedNamespace<any> &
  IVisitInformationFormState) => {
  const employeeStore = useEmployeeStore();
  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  useEffect(() => {
    employeeActions.loadListUserProfiles(doctorList);
  }, [doctorList]);
  return (
    <Flex column className={className}>
      <Flex column className="sl-group">
        <Flex column gap={16}>
          <H3>{t('treatmentDoctor')}</H3>
          <FormGroup2>
            <Field
              name={`visitInfo.treatmentDoctorId`}
              key={`visitInfo.treatmentDoctorId`}
            >
              {({ field, form }) => {
                return (
                  <Box className="box">
                    <Flex auto justify="space-between">
                      <ReactSelect
                        instanceId={`visitInfo.treatmentDoctorId`}
                        {...field}
                        items={employeeStore.employeeProfiles
                          .filter(
                            (doc) =>
                              doc.types.includes(UserType.DOCTOR) ||
                              doc.lanr?.length > 0
                          )
                          .map((item): IMenuItem => {
                            return {
                              value: item.id!,
                              label: nameUtils.getDoctorName(item),
                            };
                          })}
                        placeholder={t('select')}
                        isSearchable
                        onItemSelect={(item: IMenuItem) => {
                          form.setFieldValue(field.name, item.value);
                        }}
                        selectedValue={
                          field.value !== GetNullUUID() ? field.value : null
                        }
                      />
                    </Flex>
                  </Box>
                );
              }}
            </Field>
          </FormGroup2>
        </Flex>
      </Flex>
      <Box className="additional">
        <FormGroup2 label={t('additionalVisitInfo')}>
          <FastField name="visitInfo.additionalVisitInfo">
            {({ field }) => {
              return (
                <InputGroup
                  {...field}
                  value={field.value || ''}
                  maxLength={45}
                  data-tab-id={field.name}
                  id={field.name}
                />
              );
            }}
          </FastField>
        </FormGroup2>
      </Box>
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(VisitInformationForm, {
    namespace: 'PatientProfileCreation',
    nestedTrans: 'VisitInformation',
  })
);
