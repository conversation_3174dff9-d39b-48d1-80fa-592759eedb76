import React, { useEffect, useState, memo, useContext, useMemo } from 'react';
import { Field, FieldArray } from 'formik';

import type patientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  Intent,
  Icon,
  Collapse,
  InputGroup,
  Label,
} from '@tutum/design-system/components/Core';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import {
  Flex,
  Box,
  Button,
  H3,
  ReactSelect,
  IMenuItem,
  BodyTextM,
} from '@tutum/design-system/components';
import { IMvzTheme } from '@tutum/mvz/theme';
import {
  employeeActions,
  useEmployeeStore,
} from '@tutum/mvz/hooks/useEmployee';
import { UserType } from '@tutum/mvz/types/profile.type';
import { DoctorInfo } from '@tutum/hermes/bff/patient_profile_common';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import RemoveIcon from '@tutum/mvz/public/images/minus-circle-nocolor.svg';
import { scaleSpace } from '@tutum/design-system/styles';
import { GetNullUUID } from '@tutum/design-system/infrastructure/utils';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import HospitalsPracticesDialog from '@tutum/mvz/module_kv_hzv_schein/hospitals-practices-dialog';
import {
  useQueryGetAreaOfExpertises,
  useQueryGetSdavByIds,
} from '@tutum/hermes/bff/legacy/app_mvz_catalog_sdav';
import { isEmpty, isNil, uniq } from 'lodash';
import { SdavCatalog } from '@tutum/hermes/bff/catalog_sdav_common';
import { OptionProps, components } from 'react-select';
import { AccountAreaOfExpertiseSelect } from '@tutum/design-system/account-form';

export interface IDoctorInformationFormProps {
  className?: string;
  theme?: IMvzTheme;
  doctorInfo?: DoctorInfo;
  submitCount?: number;
  errors?: any;
  touched?: any;
  setFieldValue?: (field: string, value: any, shouldValidate?: boolean) => void;
}
type i18nKeys =
  | keyof typeof patientManagementI18n.DoctorInformation
  | `confirmDialog.${keyof typeof patientManagementI18n.DoctorInformation.confirmDialog}`;

const DoctorInformationForm = ({
  t,
  className,
  doctorInfo,
  setFieldValue,
}: IDoctorInformationFormProps & II18nFixedNamespace<i18nKeys>) => {
  const employeeStore = useEmployeeStore();
  const [collapseGPItems, setCollapseGPItems] = useState({});
  const [collapseSPItems, setCollapseSPItems] = useState({});
  const [removeAction, setRemoveAction] = useState<(() => void) | undefined>(undefined);
  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  const [isOpenSDAVDialog, setOpenSDAVDialog] = useState<boolean>(false);
  const [currentType, setCurrentType] = useState<'GP' | 'SP' | undefined>(undefined);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [selectedGPDoctors, setSelectedGPDoctors] = useState<string[]>([]);
  const [selectedSPDoctors, setSelectedSPDoctors] = useState<string[]>([]);

  const { data: sDAVInfoGP, refetch: refetchSDAVInfoGP } = useQueryGetSdavByIds(
    {
      ids: selectedGPDoctors,
    },
    {
      enabled: !isEmpty(selectedGPDoctors),
    }
  );

  const { data: sDAVInfoSP, refetch: refetchSDAVInfoSP } = useQueryGetSdavByIds(
    {
      ids: selectedSPDoctors,
    },
    {
      enabled: !isEmpty(selectedSPDoctors),
    }
  );

  const { data: areaOfExpertises = [] } = useQueryGetAreaOfExpertises({
    select: (data) => data.data.items || [],
  });

  useEffect(() => {
    employeeActions.loadListUserProfiles(doctorList);
  }, [doctorList]);

  useEffect(() => {
    if (!doctorInfo?.generalPractitionerDoctorId) return;
    setSelectedGPDoctors((prevValues) => {
      return uniq([
        ...prevValues,
        ...doctorInfo.generalPractitionerDoctorId,
      ]).filter((value) => value);
    });
  }, [doctorInfo?.generalPractitionerDoctorId]);

  useEffect(() => {
    if (!doctorInfo?.specialistDoctorId) return;
    setSelectedSPDoctors((prevValues) => {
      return uniq([...prevValues, ...doctorInfo.specialistDoctorId]).filter(
        (value) => value
      );
    });
  }, [doctorInfo?.specialistDoctorId]);

  const collapseGPSectionHandler = (index: number, item) => {
    if (!item) {
      return;
    }
    const tmp = { ...collapseGPItems };
    tmp[index] = !tmp[index];
    setCollapseGPItems(tmp);
  };
  const collapseSPSectionHandler = (index: number, item) => {
    if (!item) {
      return;
    }
    const tmp = { ...collapseSPItems };
    tmp[index] = !tmp[index];
    setCollapseSPItems(tmp);
  };

  const fieldName = useMemo(() => {
    return currentType === 'GP'
      ? `doctorInfo.generalPractitionerDoctorId.${currentIndex}`
      : `doctorInfo.specialistDoctorId.${currentIndex}`;
  }, [currentType, currentIndex]);

  const gpForm = (index, arr) => (
    <Field
      name={`doctorInfo.generalPractitionerDoctorId.${index}`}
      key={`doctorInfo.generalPractitionerDoctorId.${index}`}
    >
      {({ field, form }) => {
        const internalTmp = (employeeStore.employeeProfiles || []).find(
          (item) => item.id === field.value
        );
        const tmp = (sDAVInfoGP?.items || []).find(
          (item) => item.sdavId === field.value
        );
        const dlst = [
          ...(employeeStore.employeeProfiles || []).map((item): IMenuItem => {
            return {
              value: item.id!,
              label: nameUtils.getDoctorName(item),
            };
          }),
          ...(sDAVInfoGP?.items || []).map((item: SdavCatalog): IMenuItem => {
            return {
              value: item.sdavId,
              label:
                nameUtils.getDoctorName(item.doctorInfo) ||
                item.generalInfo.addressName!,
            };
          }),
          {
            label: t('selectDoctor'),
            value: 'new',
          },
        ];
        const isInternalDoctor = !!internalTmp;

        return (
          <Box>
            <Flex auto justify="space-between">
              <ReactSelect
                instanceId={`doctorInfo.generalPractitionerDoctorId.${index}`}
                {...field}
                items={dlst}
                placeholder={t('select')}
                isSearchable={false}
                components={{
                  Option: (props: any) => {
                    if (props.data.value === 'new') {
                      return (
                        <Flex
                          id={`react-select_option_value_${props.data.value}`}
                          className="select-doctor__option-new"
                          gap={8}
                          p="8px 12px"
                          style={{
                            borderTop: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
                            cursor: 'pointer',
                          }}
                          onClick={() => {
                            setCurrentType('GP');
                            setCurrentIndex(index);
                            setOpenSDAVDialog(true);
                          }}
                        >
                          <BodyTextM
                            fontWeight={600}
                            color={COLOR.BACKGROUND_SELECTED_STRONG}
                          >
                            {props.data.label}
                          </BodyTextM>
                        </Flex>
                      );
                    }

                    return (
                      <div
                        id={`react-select_option_value_${String(
                          props.data.value
                        ).replace('.', '_')}`}
                      >
                        <components.Option {...props} />
                      </div>
                    );
                  },
                }}
                filterOption={(option, inputValue) => {
                  return (
                    option.value === 'new' ||
                    option.label
                      .toLowerCase()
                      .includes(inputValue.toLowerCase())
                  );
                }}
                onItemSelect={(item: IMenuItem) => {
                  form.setFieldValue(field.name, item.value);

                  // reset value
                  form.setFieldValue(
                    `doctorInfo.generalPractitionerDoctor_phoneNUmber.${index}`,
                    null
                  );
                  form.setFieldValue(
                    `doctorInfo.generalPractitionerDoctor_email.${index}`,
                    null
                  );
                }}
                selectedValue={
                  field.value !== GetNullUUID() ? field.value : null
                }
              />
              <Icon
                className={
                  'expand-icon' + `${internalTmp || tmp ? '' : ' inactive'}`
                }
                icon={collapseGPItems[index] ? 'chevron-up' : 'chevron-down'}
                intent={Intent.NONE}
                onClick={() =>
                  collapseGPSectionHandler(index, internalTmp || tmp)
                }
                style={{
                  color: COLOR.TEXT_TERTIARY_SILVER,
                }}
              />
              <RemoveIcon
                className={`remove-doctor${index === 0 ? ' inactive' : ''}`}
                onClick={() => {
                  if (field.value) {
                    return setRemoveAction(() => () => {
                      index !== 0 && arr.remove(index);
                      setRemoveAction(undefined);
                    });
                  }
                  return index !== 0 && arr.remove(index);
                }}
              />
            </Flex>
            <Collapse isOpen={collapseGPItems[index]}>
              {(!!internalTmp || !!tmp) && (
                <div className="collapse-wrapper">
                  <Flex gap={scaleSpace(4)}>
                    <Flex auto className="half-row">
                      <Label>{t('lanr')}</Label>
                      <InputGroup
                        value={internalTmp?.lanr || tmp?.doctorInfo.lanr || ''}
                        disabled
                      />
                    </Flex>
                    <Flex auto className="half-row">
                      <Label>{t('bsnr')}</Label>
                      <InputGroup
                        value={internalTmp?.bsnr || tmp?.generalInfo.bsnr || ''}
                        disabled
                      />
                    </Flex>
                  </Flex>
                  <Flex gap={scaleSpace(4)}>
                    <Flex auto className="half-row">
                      <Label>{t('phoneNumber')}</Label>
                      <Field
                        name={`doctorInfo.generalPractitionerDoctor_phoneNumber.${index}`}
                        key={`doctorInfo.generalPractitionerDoctor_phoneNumber.${index}`}
                      >
                        {({ field, form }) => {
                          return (
                            <InputGroup
                              value={
                                !isNil(field.value)
                                  ? field.value
                                  : internalTmp?.phone ||
                                  tmp?.contactInfo.phoneNumber
                              }
                              disabled={isInternalDoctor}
                              onChange={(e) =>
                                form.setFieldValue(field.name, e.target.value)
                              }
                            />
                          );
                        }}
                      </Field>
                    </Flex>
                    <Flex auto className="half-row">
                      <Label>{t('email')}</Label>
                      <Field
                        name={`doctorInfo.generalPractitionerDoctor_email.${index}`}
                        key={`doctorInfo.generalPractitionerDoctor_email.${index}`}
                      >
                        {({ field, form }) => {
                          return (
                            <InputGroup
                              value={
                                !isNil(field.value)
                                  ? field.value
                                  : internalTmp?.email || tmp?.contactInfo.email
                              }
                              disabled={isInternalDoctor}
                              onChange={(e) =>
                                form.setFieldValue(field.name, e.target.value)
                              }
                            />
                          );
                        }}
                      </Field>
                    </Flex>
                  </Flex>
                </div>
              )}
            </Collapse>
          </Box>
        );
      }}
    </Field>
  );
  const gpGeneralPractitioner = (
    <FieldArray
      name="doctorInfo.generalPractitionerDoctorId"
      render={(arr) => {
        return (
          <>
            {!!doctorInfo?.generalPractitionerDoctorId?.length &&
              doctorInfo?.generalPractitionerDoctorId.map((_, index) =>
                gpForm(index, arr)
              )}
            <Flex mt={16}>
              <Button
                intent="primary"
                outlined
                minimal
                onClick={() => {
                  arr.push('');
                  collapseGPSectionHandler(
                    doctorInfo?.generalPractitionerDoctorId?.length || 0,
                    {}
                  );
                }}
              >
                {t('addDoctor')}
              </Button>
            </Flex>
          </>
        );
      }}
    />
  );

  const gpSpecialist = (
    <FieldArray
      name="doctorInfo.specialistDoctorId"
      render={(arr) => {
        return (
          <>
            {!!doctorInfo?.specialistDoctorId?.length &&
              doctorInfo?.specialistDoctorId.map((_, index) => {
                return (
                  <Field
                    name={`doctorInfo.specialistDoctorId.${index}`}
                    key={`doctorInfo.specialistDoctorId.${index}`}
                  >
                    {({ field, form }) => {
                      const internalTmp = (
                        employeeStore.employeeProfiles || []
                      ).find((item) => item.id === field.value);
                      const tmp = (sDAVInfoSP?.items || []).find(
                        (item) => item.sdavId === field.value
                      );
                      const dlst = [
                        ...(employeeStore.employeeProfiles || []).map(
                          (item): IMenuItem => {
                            return {
                              value: item.id!,
                              label: nameUtils.getDoctorName(item),
                            };
                          }
                        ),
                        ...(sDAVInfoSP?.items || []).map(
                          (item: SdavCatalog): IMenuItem => {
                            return {
                              value: item.sdavId,
                              label:
                                nameUtils.getDoctorName(item.doctorInfo) ||
                                item.generalInfo.addressName!,
                            };
                          }
                        ),
                        {
                          label: t('selectDoctor'),
                          value: 'new',
                        },
                      ];
                      const isInternalDoctor = !!internalTmp;

                      return (
                        <Box>
                          <Flex auto justify="space-between">
                            <ReactSelect
                              instanceId={`doctorInfo.specialistDoctorId.${index}`}
                              {...field}
                              items={dlst}
                              placeholder={t('select')}
                              isSearchable={false}
                              components={{
                                Option: (
                                  props: any
                                ) => {
                                  if (props.data.value === 'new') {
                                    return (
                                      <Flex
                                        id={`react-select_option_value_${props.data.value}`}
                                        className="select-doctor__option-new"
                                        gap={8}
                                        p="8px 12px"
                                        style={{
                                          borderTop: `1px solid ${COLOR.BACKGROUND_TERTIARY_DIM}`,
                                          cursor: 'pointer',
                                        }}
                                        onClick={() => {
                                          setCurrentType('SP');
                                          setCurrentIndex(index);
                                          setOpenSDAVDialog(true);
                                        }}
                                      >
                                        <BodyTextM
                                          fontWeight={600}
                                          color={
                                            COLOR.BACKGROUND_SELECTED_STRONG
                                          }
                                        >
                                          {props.data.label}
                                        </BodyTextM>
                                      </Flex>
                                    );
                                  }

                                  return (
                                    <div
                                      id={`react-select_option_value_${String(
                                        props.data.value
                                      ).replace('.', '_')}`}
                                    >
                                      <components.Option {...props} />
                                    </div>
                                  );
                                },
                              }}
                              filterOption={(option, inputValue) => {
                                return (
                                  option.value === 'new' ||
                                  option.label
                                    .toLowerCase()
                                    .includes(inputValue.toLowerCase())
                                );
                              }}
                              onItemSelect={(item: IMenuItem) => {
                                form.setFieldValue(field.name, item.value);

                                // reset value
                                form.setFieldValue(
                                  `doctorInfo.specialistDoctor_exptertise.${index}`,
                                  null
                                );
                                form.setFieldValue(
                                  `doctorInfo.specialistDoctor_phoneNUmber.${index}`,
                                  null
                                );
                                form.setFieldValue(
                                  `doctorInfo.specialistDoctor_email.${index}`,
                                  null
                                );
                              }}
                              selectedValue={
                                field.value !== GetNullUUID()
                                  ? field.value
                                  : null
                              }
                            />
                            <Icon
                              className={
                                'expand-icon' +
                                `${internalTmp || tmp ? '' : ' inactive'}`
                              }
                              icon={
                                collapseSPItems[index]
                                  ? 'chevron-up'
                                  : 'chevron-down'
                              }
                              intent={Intent.NONE}
                              onClick={() =>
                                collapseSPSectionHandler(
                                  index,
                                  internalTmp || tmp
                                )
                              }
                              style={{
                                color: COLOR.TEXT_TERTIARY_SILVER,
                              }}
                            />
                            <RemoveIcon
                              className={`remove-doctor${index === 0 ? ' inactive' : ''
                                }`}
                              onClick={() => {
                                if (field.value) {
                                  return setRemoveAction(() => () => {
                                    index !== 0 && arr.remove(index);
                                    setRemoveAction(undefined);
                                  });
                                }
                                return index !== 0 && arr.remove(index);
                              }}
                            />
                          </Flex>
                          <Collapse isOpen={collapseSPItems[index]}>
                            {(!!internalTmp || !!tmp) && (
                              <div className="collapse-wrapper">
                                <Flex column className="half-row" w={374}>
                                  <Label>{t('areaOfExpertise')}</Label>
                                  <Field
                                    name={`doctorInfo.specialistDoctor_exptertise.${index}`}
                                    key={`doctorInfo.specialistDoctor_exptertise.${index}`}
                                  >
                                    {({ field, form }) => {
                                      if (!areaOfExpertises.length) {
                                        return null;
                                      }

                                      return (
                                        <AccountAreaOfExpertiseSelect
                                          menuPlacement="top"
                                          menuPosition="absolute"
                                          placeholder={t(
                                            'areaOfExpertisePlaceholder'
                                          )}
                                          options={areaOfExpertises.map(
                                            (item) => ({
                                              label: item.dN,
                                              value: item.v,
                                            })
                                          )}
                                          value={(!isNil(field.value)
                                            ? field.value
                                            : internalTmp?.areaOfExpertise ||
                                            tmp?.doctorInfo
                                              .areaOfExpertises ||
                                            []
                                          ).map((value) => {
                                            const matchedItem =
                                              areaOfExpertises.find((item) =>
                                                typeof value === 'string'
                                                  ? item.v === value
                                                  : item.v === value.v
                                              );
                                            return {
                                              value:
                                                matchedItem?.v ||
                                                (value as string),
                                              label:
                                                matchedItem?.dN ||
                                                (value as string),
                                            };
                                          })}
                                          isDisabled={isInternalDoctor}
                                          onChange={(newValueList: any) => {
                                            form.setFieldValue(
                                              field.name,
                                              newValueList.map((item) => {
                                                if (!!internalTmp) {
                                                  return item.value;
                                                }

                                                return {
                                                  v: item.value,
                                                  dN: item.label,
                                                };
                                              })
                                            );
                                          }}
                                        />
                                      );
                                    }}
                                  </Field>
                                </Flex>
                                <Flex gap={scaleSpace(4)}>
                                  <Flex auto className="half-row">
                                    <Label>{t('lanr')}</Label>
                                    <InputGroup
                                      value={
                                        internalTmp?.lanr ||
                                        tmp?.doctorInfo.lanr
                                      }
                                      disabled
                                    />
                                  </Flex>
                                  <Flex auto className="half-row">
                                    <Label>{t('bsnr')}</Label>
                                    <InputGroup
                                      value={
                                        internalTmp?.bsnr ||
                                        tmp?.generalInfo.bsnr
                                      }
                                      disabled
                                    />
                                  </Flex>
                                </Flex>
                                <Flex gap={scaleSpace(4)}>
                                  <Flex auto className="half-row">
                                    <Label>{t('phoneNumber')}</Label>
                                    <Field
                                      name={`doctorInfo.specialistDoctor_phoneNumber.${index}`}
                                      key={`doctorInfo.specialistDoctor_phoneNumber.${index}`}
                                    >
                                      {({ field, form }) => {
                                        return (
                                          <InputGroup
                                            value={
                                              !isNil(field.value)
                                                ? field.value
                                                : internalTmp?.phone ||
                                                tmp?.contactInfo.phoneNumber
                                            }
                                            disabled={isInternalDoctor}
                                            onChange={(e) => {
                                              form.setFieldValue(
                                                field.name,
                                                e.target.value
                                              );
                                            }}
                                          />
                                        );
                                      }}
                                    </Field>
                                  </Flex>
                                  <Flex auto className="half-row">
                                    <Label>{t('email')}</Label>
                                    <Field
                                      name={`doctorInfo.specialistDoctor_email.${index}`}
                                      key={`doctorInfo.specialistDoctor_email.${index}`}
                                    >
                                      {({ field, form }) => {
                                        return (
                                          <InputGroup
                                            value={
                                              !isNil(field.value)
                                                ? field.value
                                                : internalTmp?.email ||
                                                tmp?.contactInfo.email
                                            }
                                            disabled={isInternalDoctor}
                                            onChange={(e) => {
                                              form.setFieldValue(
                                                field.name,
                                                e.target.value
                                              );
                                            }}
                                          />
                                        );
                                      }}
                                    </Field>
                                  </Flex>
                                </Flex>
                              </div>
                            )}
                          </Collapse>
                        </Box>
                      );
                    }}
                  </Field>
                );
              })}
            <Flex mt={16}>
              <Button
                intent="primary"
                outlined
                minimal
                onClick={() => {
                  arr.push('');
                  collapseSPSectionHandler(
                    doctorInfo?.specialistDoctorId?.length || 0,
                    {}
                  );
                }}
              >
                {t('addDoctor')}
              </Button>
            </Flex>
          </>
        );
      }}
    />
  );

  const gpTreatmentDoctor = (
    <Field
      name={`doctorInfo.treatmentDoctorId`}
      key={`doctorInfo.treatmentDoctorId`}
    >
      {({ field, form }) => {
        const dlst =
          employeeStore.employeeProfiles
            .filter(
              (item) =>
                ((doctorInfo?.treatmentDoctorId || ['']).indexOf(item.id!) ===
                  -1 ||
                  item.id === field.value) &&
                (item.types.includes(UserType.DOCTOR) || !!item.lanr?.length)
            )
            .map((item): IMenuItem => {
              return {
                value: item.id!,
                label: nameUtils.getDoctorName(item),
              };
            }) || [];
        return (
          <Box>
            <Flex auto justify="space-between">
              <ReactSelect
                instanceId={`doctorInfo.treatmentDoctorId`}
                {...field}
                items={dlst}
                placeholder={t('select')}
                isSearchable={false}
                onItemSelect={(item: IMenuItem) => {
                  const value = item.value || null;

                  form.setFieldValue(field.name, value);
                  form.setFieldValue('visitInfo.treatmentDoctorId', value);
                }}
                selectedValue={
                  field.value !== GetNullUUID() ? field.value : null
                }
              />
            </Flex>
          </Box>
        );
      }}
    </Field>
  );

  return (
    <>
      <Flex column className={className}>
        <Flex column className="sl-group">
          <H3>{t('treatmentDoctor')}</H3>
          {gpTreatmentDoctor}
        </Flex>
        <Flex column className="sl-group">
          <H3>{t('generalPractitioner')}</H3>
          {gpGeneralPractitioner}
        </Flex>
        <Flex column className="sl-group">
          <H3>{t('specialist')}</H3>
          {gpSpecialist}
        </Flex>
      </Flex>
      {typeof removeAction === 'function' ? (
        <ConfirmDialog
          close={() => setRemoveAction(undefined)}
          confirm={removeAction}
          text={{
            btnCancel: t('confirmDialog.btnCancel'),
            btnOk: t('confirmDialog.btnRemove'),
            title: t('confirmDialog.title'),
            message: t('confirmDialog.message'),
          }}
        />
      ) : null}
      {isOpenSDAVDialog && (
        <HospitalsPracticesDialog
          isOpen
          needDoctorInfo
          onClose={() => {
            setOpenSDAVDialog(false);
            setCurrentType(undefined);
          }}
          onSelect={(data) => {
            if (currentType === 'GP' && !selectedGPDoctors.includes(data.sdavId)) {
              setSelectedGPDoctors((prevValues) => [...prevValues, data.sdavId]);
            } else if (!selectedSPDoctors.includes(data.sdavId)) {
              setSelectedSPDoctors((prevValues) => [...prevValues, data.sdavId]);
            }

            setOpenSDAVDialog(false);
            setCurrentType(undefined);
            setFieldValue?.(fieldName, data.sdavId);
            refetchSDAVInfoGP();
            refetchSDAVInfoSP();
          }}
        />
      )}
    </>
  );
};
export default memo(
  I18n.withTranslation(DoctorInformationForm, {
    namespace: 'PatientProfileCreation',
    nestedTrans: 'DoctorInformation',
  })
);
