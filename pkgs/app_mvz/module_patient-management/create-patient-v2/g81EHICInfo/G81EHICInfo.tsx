import { FastField } from 'formik';
import { cloneDeep } from 'lodash';
import { useCallback, useContext, useEffect } from 'react';

import type PatientProfileCreation from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import {
  alertError,
  BodyTextM,
  Button,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import { Checkbox, FormGroup } from '@tutum/design-system/components/Core';
import { COLOR } from '@tutum/design-system/themes/styles';
import { EuropeanHealthInsuranceStatus } from '@tutum/hermes/bff/common';
import { Gender, PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { useEmployeeStore } from '@tutum/mvz/hooks/useEmployee';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import {
  musterFormDialogActions,
  musterFormDialogStore,
  useMusterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { convertDataToStringValue } from '@tutum/mvz/module_form/muster-form/MusterForm.helper';
import { musterFormActions } from '@tutum/mvz/module_form/muster-form/musterForm.store';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import PrinterService from '@tutum/mvz/services/printer.service';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import countryType from '@tutum/mvz/constant/country.type';
import { getDateOfBirth } from '../CreatePatient.helper';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { getDefaultBsnrIdOfDoctor } from '@tutum/mvz/_utils/bsnr';
import { printPlainPdf } from '@tutum/hermes/bff/legacy/app_mvz_form';

export interface G81EHICInfoProps {
  className?: string;
  values: PatientInfo;
  isEdit: boolean;
  setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
}

const CheckedIcon = '/images/check-circle-solid-2.svg';
const DEFAULT_LANGUAGE = 'G81_EHIC_Englisch';

export const DEFAULT_LANGUAGES = [
  'Bulgarisch',
  'Danisch',
  'Englisch',
  'Estnisch',
  'Finnisch',
  'Franzosisch',
  'Griechisch',
  'Italienisch',
  'Kroatisch',
  'Lettisch',
  'Litauisch',
  'Niederlandisch',
  'Polnisch',
  'Portugiesisch',
  'Rumanisch',
  'Schwedisch',
  'Slowakisch',
  'Slowenisch',
  'Spanisch',
  'Tschechisch',
  'Ungarisch',
  'All',
];

const G81EHICInfo = ({
  className,
  values,
  isEdit,
  setFieldValue,
}: G81EHICInfoProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientProfileCreation.G81EHICInformation
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'G81EHICInformation',
  });

  const store = useMusterFormDialogStore();
  const { patient } = usePatientFileStore();
  const { userProfile } = useEmployeeStore();
  const europeanHealthInsurance =
    patient?.current?.patientInfo?.europeanHealthInsurance;
  const { patientManagement } = useContext(PatientManagementContext.instance);
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();

  const handlePrescribe = useCallback(() => {
    musterFormDialogActions.setCurrentFormName(DEFAULT_LANGUAGE);
    if (!isEdit) {
      musterFormDialogActions.setCurrentTreatmentDoctor({
        value: `${currentLoggedInUser.id}-${currentLoggedInUser.bsnrId}`,
        label: currentLoggedInUser.firstName,
        data: currentLoggedInUser,
      });
    }
  }, [isEdit, currentLoggedInUser]);

  const handleRePrescribe = useCallback(() => {
    musterFormDialogActions.setCurrentMusterFormSetting(
      JSON.parse(values.europeanHealthInsurance?.formSetting || '{}')
    );
    musterFormDialogActions.setCurrentFormName(
      `G81_EHIC_${values.europeanHealthInsurance?.language}`
    );
  }, [values.europeanHealthInsurance]);

  const onCustomActions = useCallback(
    async (
      language: string,
      status: EuropeanHealthInsuranceStatus,
      callback: () => void
    ) => {
      musterFormDialogStore.isLoadingPrescribe = true;

      const currentFormSetting = JSON.stringify(
        cloneDeep(store.currentFormSetting)
      );
      const isPrinted = [
        EuropeanHealthInsuranceStatus.EuropeanHealthInsuranceStatus_Printed,
      ].includes(status);

      setFieldValue('europeanHealthInsurance.formSetting', currentFormSetting);
      setFieldValue('europeanHealthInsurance.language', language);
      setFieldValue('europeanHealthInsurance.status', status);
      setFieldValue(
        'europeanHealthInsurance.treatmentDoctorId',
        patientManagement.selectedContractDoctor.doctorId ||
        currentLoggedInUser.id
      );

      if (isPrinted) {
        const resp = await printPlainPdf({
          formSetting: currentFormSetting,
          formName: `G81_EHIC_${language}`,
          treatmentDoctorId: store.doctor?.data?.id,
        });

        PrinterService.initAndPrint(
          `G81_EHIC_${language}`,
          async () => {
            return resp.data.formUrl;
          },
          {
            printSuccess: async () => {
              musterFormDialogActions.clear();
              musterFormDialogStore.isLoadingPrescribe = false;
              callback?.();
            },
            printFailure: () => {
              musterFormDialogStore.isLoadingPrescribe = false;
            },
          }
        );
      } else {
        musterFormDialogActions.clear();
        musterFormDialogStore.isLoadingPrescribe = false;
      }
    },
    [
      store.currentFormSetting,
      patientManagement.selectedContractDoctor,
      currentLoggedInUser.id,
    ]
  );

  useEffect(() => {
    if (!values.europeanHealthInsurance?.status && store.currentFormName) {
      const prescribedDate = +datetimeUtil.date();
      const { insuranceInfos, personalInfo, addressInfo } = values;
      const activeInsurance = getActiveInsurance(insuranceInfos);
      const countryName =
        countryType.COUNTRY_LIST.find(
          (country) => country.value === addressInfo?.address?.countryCode
        )?.label || '';
      const { value: birthDateValue } = getDateOfBirth(
        personalInfo.dateOfBirth,
        false
      );

      musterFormDialogActions.setCurrentMusterFormSetting({
        checkbox_confirmation: true,
        textbox_insuranceName: activeInsurance?.insuranceCompanyName || '',
        textbox_treatingDoctor: convertDataToStringValue([
          userProfile?.lastName,
          userProfile?.firstName,
        ]),
        textbox_patientName: convertDataToStringValue([
          personalInfo?.firstName,
          personalInfo?.lastName,
        ]),
        checkbox_male: personalInfo?.gender === Gender.M,
        checkbox_other: personalInfo?.gender === Gender.D,
        checkbox_unspecified: personalInfo?.gender === Gender.X,
        checkbox_female: personalInfo?.gender === Gender.W,
        textbox_strabe: convertDataToStringValue([
          addressInfo?.address?.street,
          addressInfo?.address?.number,
        ]),
        textbox_plz: convertDataToStringValue([
          addressInfo?.address?.postCode,
          addressInfo?.address?.city,
        ]),
        textbox_land: countryName,
        date_datumBirth: birthDateValue,
        date_datumCurrent: prescribedDate,
        date_datumSignature: prescribedDate,
        // Need to send this field so BE can fill latest doctor stamp
        label_doctor_stamp: '',
      });
    }
  }, [values, userProfile, Boolean(store.currentFormName)]);

  return (
    <Flex column className={className}>
      <FormGroup>
        <FastField
          name={`europeanHealthInsurance.hasEuropeanHealthInsuranceCard`}
        >
          {({ field, form }) => {
            return (
              <Checkbox
                checked={field.value}
                onChange={() => {
                  const currentValue = field.value;

                  form.setFieldValue(
                    'europeanHealthInsurance.formSetting',
                    (isEdit &&
                      !currentValue &&
                      europeanHealthInsurance?.formSetting) ||
                    ''
                  );
                  form.setFieldValue(
                    'europeanHealthInsurance.language',
                    (isEdit &&
                      !currentValue &&
                      europeanHealthInsurance?.language) ||
                    ''
                  );
                  form.setFieldValue(
                    'europeanHealthInsurance.status',
                    (isEdit &&
                      !currentValue &&
                      europeanHealthInsurance?.status) ||
                    ''
                  );
                  form.setFieldValue(field.name, !currentValue);
                }}
              >
                <span>{t('content')}</span>
              </Checkbox>
            );
          }}
        </FastField>
        <BodyTextM padding="0 0 0 24px" color={COLOR.TEXT_PLACEHOLDER}>
          {t('description')}
        </BodyTextM>
        <Flex align="center" m="8px 0 0 24px">
          <Button
            icon={
              values?.europeanHealthInsurance?.formSetting ? (
                <Svg src={CheckedIcon} />
              ) : null
            }
            intent="primary"
            className="sl-g81-ehic-info__prescribe-btn"
            text={t(
              values?.europeanHealthInsurance?.formSetting
                ? 'prescribedBtn'
                : 'prescribeBtn'
            )}
            disabled={
              !values?.europeanHealthInsurance
                ?.hasEuropeanHealthInsuranceCard ||
              !!values?.europeanHealthInsurance?.formSetting
            }
            onClick={handlePrescribe}
          />
          {!!values?.europeanHealthInsurance?.formSetting && (
            <BodyTextM
              className="sl-g81-ehic-info__re-prescribe-btn"
              margin="0 0 0 8px"
              fontWeight={600}
              color={COLOR.BACKGROUND_SELECTED_STRONG}
              onClick={handleRePrescribe}
            >
              {t('represcribeBtn')}
            </BodyTextM>
          )}
        </Flex>
      </FormGroup>
      <MusterFormDialog
        languages={DEFAULT_LANGUAGES.map((language) => ({
          value: `G81_EHIC_${language}`,
          label: language === 'All' ? t('printAll') : language,
        }))}
        patient={isEdit && patient?.current || undefined}
        selectedContractDoctor={
          patientManagement.selectedContractDoctor?.doctorId
            ? patientManagement.selectedContractDoctor
            : {
              doctorId: currentLoggedInUser.id,
              bsnrId: currentLoggedInUser.bsnrId,
              contractId: '',
              chargeSystemId: '',
              availableDoctor: [currentLoggedInUser],
            }
        }
        isOpen={!!store.currentFormName}
        onClose={() => {
          musterFormDialogActions.clear();
        }}
        onChangeLanguage={(formName: string) => {
          musterFormActions.setHasSetFormAnnotation(false);
          musterFormDialogActions.setCurrentFormName(formName);
        }}
        onCustomActions={onCustomActions}
      />
    </Flex>
  );
};

export default G81EHICInfo;
