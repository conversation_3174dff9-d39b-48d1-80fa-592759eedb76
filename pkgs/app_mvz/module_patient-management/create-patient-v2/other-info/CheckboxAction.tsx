import { useRef, useState } from 'react';
import get from 'lodash/get';
import {
  Box,
  Flex,
  Svg,
  Tooltip,
  Button,
  BodyTextS,
} from '@tutum/design-system/components';
import { alertSuccessfully, alertError } from '@tutum/design-system/components';
import { Checkbox, Spinner } from '@tutum/design-system/components/Core';
import { FastField, Field } from 'formik';
import { COLOR } from '@tutum/design-system/themes/styles';
import { PatientInfoTemplateType } from '@tutum/hermes/bff/legacy/patient_info_template_common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import {
  getPresignedURL,
  useMutationGetPresignedGetURL,
} from '@tutum/hermes/bff/legacy/app_mvz_file';
import { getUUID } from '@tutum/design-system/infrastructure/utils';
import { TEXT } from '@tutum/design-system/themes/styles/color/text';
import { fillCustomPatientInfoTemplateFileName } from '../CreatePatient.helper';

const PrinterIcon = '/images/printer.svg';
const DisabledPrinterIcon = '/images/disabled-printer.svg';
const UploadIcon = '/images/upload.svg';
const CloseIcon = '/images/close-solid-gray.svg';

type CheckBoxActionProps = {
  t: IFixedNamespaceTFunction<any>;
  fileMap: Record<PatientInfoTemplateType, string>;
  type: PatientInfoTemplateType;
  fieldName: string;
  isReadCard: boolean;
};
const CheckBoxAction = ({
  t,
  type,
  fileMap,
  fieldName,
  isReadCard,
}: CheckBoxActionProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const { mutate: getPresignedGetURL, isPending } =
    useMutationGetPresignedGetURL({
      onSuccess: (result) => {
        window.open(result.data.presignedURL, '_blank');
      },
    });

  const [isUploading, setIsUploading] = useState(false);
  const [placehodler, setPlaceholder] = useState('');

  const hasFile = fileMap[type];

  const otherFieldTitle = (type: PatientInfoTemplateType) => {
    return {
      [PatientInfoTemplateType.MedicalHistory]: t('medicalHistoryComplete'),
      [PatientInfoTemplateType.LivingWill]: t('livingWillAvailable'),
      [PatientInfoTemplateType.Billing]: t('agreeBilling'),
      [PatientInfoTemplateType.Privacy]: t('privacyPolicySigned'),
      [PatientInfoTemplateType.ContactConsent]: t('declarationOfAgreement'),
    }[type];
  };
  const handlePrint = (type: PatientInfoTemplateType) => {
    if (fileMap[type]) {
      window.open(fileMap[type], '_blank');
    }
  };

  const mapFileTypeToFieldName = (type: PatientInfoTemplateType) => {
    return {
      [PatientInfoTemplateType.MedicalHistory]:
        'otherInfo.medicalHistoryFileUrl',
      [PatientInfoTemplateType.LivingWill]: 'otherInfo.livingWillFileUrl',
      [PatientInfoTemplateType.Billing]: 'otherInfo.billingFileUrl',
      [PatientInfoTemplateType.Privacy]: 'otherInfo.privacyPolicyFileUrl',
      [PatientInfoTemplateType.ContactConsent]:
        'contactInfo.contactAgreementFile',
    }[type];
  };

  const getFileInfo = (form: any, fieldName: 'fileName' | 'fileUrl') => {
    return get(form, `values.${mapFileTypeToFieldName(type)}.${fieldName}`);
  };

  const handleUpload = async () => {
    try {
      const files = inputRef.current?.files;
      if (!files?.length) return;
      const pdfFile = files[0];
      const fileName = fillCustomPatientInfoTemplateFileName(getUUID());
      const payload = {
        bucketName: 'bucket-doctor-letter',
        objectName: fileName,
        header: {
          'response-content-type': 'application/pdf',
        },
      };
      setIsUploading(true);
      const { data } = await getPresignedURL(payload);
      if (!data?.presignedURL) {
        throw new Error('Failed to upload');
      }
      const presignedURL = data.presignedURL;
      await fetch(`${presignedURL}`, { method: 'PUT', body: pdfFile });
      if (formRef.current) {
        formRef.current.setFieldValue(
          `${mapFileTypeToFieldName(type)}.fileName`,
          pdfFile.name
        );
        formRef.current.setFieldValue(
          `${mapFileTypeToFieldName(type)}.fileUrl`,
          fileName
        );
        formRef.current.setFieldValue(fieldName, true); //  auto check checkbox when upload file
      }
      alertSuccessfully(t('fileUploaded'));
    } catch (error) {
      alertError(error.message);
    } finally {
      setIsUploading(false);
    }
  };

  const noTemplateFoundContent = (
    <Box>
      <BodyTextS>{t('noTemeplate')}</BodyTextS>
      <BodyTextS color={COLOR.TEXT_PRIMARY_BLACK}>
        {t('setupTemplate')}
      </BodyTextS>
    </Box>
  );

  const onOpenFile = (form) => {
    getPresignedGetURL({
      bucketName: 'bucket-doctor-letter',
      objectName: getFileInfo(form, 'fileUrl'),
      header: { 'response-content-type': 'application/pdf' },
    });
  };

  const PlaceHolder = ({ text }: { text: string }) => {
    return <BodyTextS className="placeholder">{text}</BodyTextS>;
  };

  const subContent = (form, field) => {
    if (isPending)
      return (
        <>
          &nbsp;
          <Spinner size={16} />
        </>
      );
    if (getFileInfo(form, 'fileName')) {
      return (
        <>
          &nbsp;
          <Svg
            src={CloseIcon}
            style={{ width: 16, cursor: 'pointer' }}
            onClick={() => {
              if (inputRef.current) {
                inputRef.current.value = null!;
              }
              form.setFieldValue(field.name, {
                fileName: '',
                fileUrl: '',
              });
            }}
          />
        </>
      );
    }
    if (isReadCard) {
      return <></>;
    }
    return <PlaceHolder text={placehodler} />;
  };

  return (
    <Flex w="100%" justify="space-between">
      <Box>
        <FastField name={fieldName}>
          {({ field, form }) => {
            (formRef as any).current = form;
            return (
              <Box>
                <Checkbox
                  id={field.name}
                  checked={field.value}
                  onChange={() => {
                    form.setFieldValue(field.name, !field.value);
                    if (!field.value && !getFileInfo(form, 'fileName')) {
                      setPlaceholder(t('NoFileUploaded'));
                    } else {
                      setPlaceholder('');
                    }
                  }}
                >
                  <span style={{ color: TEXT.TEXT_PRIMARY_BLACK }}>
                    {otherFieldTitle(type)}
                  </span>
                </Checkbox>
              </Box>
            );
          }}
        </FastField>

        <Field name={mapFileTypeToFieldName(type)}>
          {({ field, form }) => {
            return (
              <Flex>
                <BodyTextS
                  color={COLOR.TEXT_INFO}
                  style={{ margin: '3px 0 0 24px', cursor: 'pointer' }}
                  onClick={() => onOpenFile(form)}
                >
                  {getFileInfo(form, 'fileName') ?? (
                    <PlaceHolder text={placehodler} />
                  )}
                </BodyTextS>

                {subContent(form, field)}
              </Flex>
            );
          }}
        </Field>
      </Box>

      <Flex gap={16}>
        <Tooltip
          content={hasFile ? t('printNewTemplate') : noTemplateFoundContent}
          position="top"
        >
          <Button
            minimal
            small
            disabled={!hasFile}
            onClick={() => handlePrint(type)}
          >
            <Svg
              style={{ width: 18 }}
              src={hasFile ? PrinterIcon : DisabledPrinterIcon}
            />
          </Button>
        </Tooltip>
        <input
          ref={inputRef}
          type="file"
          hidden
          accept="application/pdf"
          onInput={handleUpload}
        />
        <Tooltip content={'Upload'} position="top">
          <Button
            minimal
            small
            disabled={isUploading}
            onClick={() => inputRef.current?.click()}
          >
            <Svg style={{ width: 18 }} src={UploadIcon} />
          </Button>
        </Tooltip>
      </Flex>
    </Flex>
  );
};

export default CheckBoxAction;
