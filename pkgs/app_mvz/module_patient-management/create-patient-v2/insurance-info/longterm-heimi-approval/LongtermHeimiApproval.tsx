import React, { FormEvent, memo, useState } from 'react';
import MomentLocaleUtils from 'react-day-picker/moment';
import moment from 'moment';
import { FieldArray, Field, FormikProps } from 'formik';

import type patientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';

import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { IMvzTheme } from '@tutum/mvz/theme';
import { Flex, Svg } from '@tutum/design-system/components';
import {
  FormGroup,
  Label,
  Intent,
  Classes,
  Button,
  InputGroup,
  Divider,
  Switch,
} from '@tutum/design-system/components/Core';
import { RemediesInput, SearchDiagnosisGroup, SearchICD } from './components';
import { DateInput } from '@tutum/design-system/components/DateTime';

import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import CreatePatientUtil from '@tutum/infrastructure/utils/form.util';
import { LHM } from '@tutum/hermes/bff/patient_profile_common';
import { IInitialValues, initDefaultLHMItem } from '../../CreatePatient.helper';
import { getAge } from '@tutum/design-system/infrastructure/utils';

import {
  DATE_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import { useLHMStore } from './store';
import { scaleSpace } from '@tutum/design-system/styles';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export interface ILongtermHeimiApprovalProps {
  theme?: IMvzTheme;
  className?: string;
  submitCount?: number;
  errors?: any;
  touched?: any;
  patientId?: string;
  isEdit?: boolean;
  values: LHM[];
  form: FormikProps<IInitialValues>;
  disabled: boolean;
  name: string;
  isInPatientInformation?: boolean;
}

const CalendarIcon = '/images/calendar-default.svg';
const RemoveIcon = '/images/minus-circle.svg';

const LongtermHeimiApproval = ({
  t,
  className,
  submitCount,
  errors,
  touched,
  patientId,
  values,
  name,
  isInPatientInformation = true,
}: ILongtermHeimiApprovalProps &
  II18nFixedNamespace<
    keyof typeof patientManagementI18n.InsuranceInformation
  >) => {
  const [removeAction, setRemoveAction] = useState<(() => void) | undefined>(undefined);
  const lhmStore = useLHMStore();
  return (
    <Flex column className={className}>
      <FieldArray
        name={name}
        render={(arrayHelpers) => (
          <>
            {values?.map((_, index) => {
              const isDisplayStandardCombination =
                lhmStore.hasStandardCombination[
                values?.[index]?.diagnosisGroupCode
                ];
              const icdExceptList: string[] = (
                values?.map((lhm) => lhm.firstICDCode) || []
              ).concat(values?.map((lhm) => lhm.secondICDCode!) || []);
              const diagnosisGroupExceptList: string[] =
                values?.map((lhm) => lhm.diagnosisGroupCode) || [];
              return (
                <div className="lhm-group" key={`${name}_lhm-${index}`}>
                  <Flex gap={scaleSpace(4)}>
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched?.[index]?.firstICDCode,
                        errors[`${name}.${index}.firstICDCode`]
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched?.[index]?.firstICDCode,
                        errors[`${name}.${index}.firstICDCode`]
                      )}
                    >
                      <>
                        <Label name={`${name}.${index}.firstICDCode`} required>
                          {t('1stICDCode')}
                        </Label>
                        <Field name={`${name}.${index}.firstICDCode`}>
                          {({ field, form }) => (
                            <SearchICD
                              onChange={(value) => {
                                form.setFieldValue(field.name, value);
                              }}
                              value={field.value}
                              placeholder={t('select')}
                              exceptList={icdExceptList.filter(
                                (item) => item !== field.value
                              )}
                            />
                          )}
                        </Field>
                      </>
                    </FormGroup>

                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched?.[index]?.secondICDCode,
                        errors[`${name}.${index}.secondICDCode`]
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched?.[index]?.secondICDCode,
                        errors[`${name}.${index}.secondICDCode`]
                      )}
                    >
                      <>
                        <Label name={`${name}.${index}.secondICDCode`}>
                          {t('2ndICDCode')}
                        </Label>
                        <Field name={`${name}.${index}.secondICDCode`}>
                          {({ field, form }) => (
                            <SearchICD
                              onChange={(value) => {
                                form.setFieldValue(field.name, value);
                              }}
                              value={field.value}
                              disabled={!values[index]?.firstICDCode}
                              placeholder={t('select')}
                              exceptList={icdExceptList.filter(
                                (item) => item !== field.value
                              )}
                            />
                          )}
                        </Field>
                      </>
                    </FormGroup>
                  </Flex>
                  <Flex auto className="diagnosis-groupcode">
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched?.[index]?.diagnosisGroupCode,
                        errors[`${name}.${index}.diagnosisGroupCode`]
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched?.[index]?.diagnosisGroupCode,
                        errors[`${name}.${index}.diagnosisGroupCode`]
                      )}
                    >
                      <>
                        <Label
                          name={`${name}.${index}.diagnosisGroupCode`}
                          required
                        >
                          {t('diagnosisCode')}
                        </Label>
                        <Field name={`${name}.${index}.diagnosisGroupCode`}>
                          {({ field, form }) => (
                            <SearchDiagnosisGroup
                              onChange={(value) => {
                                form.setFieldValue(field.name, value);
                                form.setFieldValue(
                                  `${name}.${index}.isStandardCombination`,
                                  false
                                );
                                form.setFieldValue(
                                  `${name}.${index}.primaryRemediesPosition`,
                                  []
                                );
                                form.setFieldValue(
                                  `${name}.${index}.complementaryRemediesPosition`,
                                  []
                                );
                              }}
                              diagnoseCode={values[index]?.firstICDCode}
                              secondDiagnoseCode={values[index]?.firstICDCode}
                              exceptList={diagnosisGroupExceptList}
                              patientAge={
                                form?.values?.personalInfo?.dateOfBirth
                                  ?.isValidDOB
                                  ? getAge(
                                    datetimeUtil
                                      .dateToMoment(
                                        form?.values?.personalInfo?.dOB,
                                        DATE_FORMAT
                                      )
                                      .toDate()
                                  )
                                  : -1
                              }
                              value={field?.value}
                              disabled={!values[index]?.firstICDCode}
                            />
                          )}
                        </Field>
                      </>
                    </FormGroup>
                  </Flex>
                  {isDisplayStandardCombination ? (
                    <Flex auto>
                      <Field name={`${name}.${index}.isStandardCombination`}>
                        {({ field, form }) => (
                          <Switch
                            className="sl-switch"
                            checked={field.value}
                            onChange={(evt: FormEvent<HTMLInputElement>) => {
                              form.setFieldValue(
                                field.name,
                                (evt.target as HTMLInputElement).checked
                              );
                            }}
                            label={t('standardCombination')}
                          />
                        )}
                      </Field>
                    </Flex>
                  ) : null}
                  <Flex auto>
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched?.[index]?.primaryRemediesPosition,
                        errors[`${name}.${index}.primaryRemediesPosition`]
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched?.[index]?.primaryRemediesPosition,
                        errors[`${name}.${index}.primaryRemediesPosition`]
                      )}
                    >
                      <>
                        <Label
                          name={`${name}.${index}.primaryRemediesPosition`}
                        >
                          {t('primaryRemedies')}
                        </Label>
                        <Field
                          name={`${name}.${index}.primaryRemediesPosition`}
                        >
                          {({ field, form }) => (
                            <>
                              <RemediesInput
                                placeholder={t('select')}
                                onChange={(value) => {
                                  form.setFieldValue(field.name, value);
                                }}
                                diagnoseCode={values[index]?.firstICDCode}
                                secondDiagnoseCode={
                                  values[index]?.secondICDCode
                                }
                                patientAge={
                                  form?.values?.personalInfo?.dateOfBirth
                                    ?.isValidDOB
                                    ? getAge(
                                      moment(
                                        form?.values?.personalInfo?.dOB,
                                        DATE_FORMAT
                                      ).toDate()
                                    )
                                    : -1
                                }
                                value={field?.value}
                                group={values[index]?.diagnosisGroupCode}
                                patientId={patientId}
                                lhmValue={values[index]}
                                hasStandardCombination={
                                  isDisplayStandardCombination
                                }
                              />
                            </>
                          )}
                        </Field>
                      </>
                    </FormGroup>
                  </Flex>
                  <Flex auto>
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched?.[index]?.complementaryRemediesPosition,
                        errors[`${name}.${index}.complementaryRemediesPosition`]
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched?.[index]?.complementaryRemediesPosition,
                        errors[`${name}.${index}.complementaryRemediesPosition`]
                      )}
                    >
                      <>
                        <Label
                          name={`${name}.${index}.complementaryRemediesPosition`}
                        >
                          {t('complementaryRemedies')}
                        </Label>
                        <Field
                          name={`${name}.${index}.complementaryRemediesPosition`}
                        >
                          {({ field, form }) => (
                            <RemediesInput
                              placeholder={t('select')}
                              onChange={(value) => {
                                form.setFieldValue(field.name, value);
                              }}
                              complementary
                              diagnoseCode={values[index]?.firstICDCode}
                              secondDiagnoseCode={values[index]?.secondICDCode}
                              patientAge={
                                form?.values?.personalInfo?.dateOfBirth
                                  ?.isValidDOB
                                  ? getAge(
                                    moment(
                                      form?.values?.personalInfo?.dOB,
                                      DATE_FORMAT
                                    ).toDate()
                                  )
                                  : -1
                              }
                              value={field?.value}
                              group={values[index]?.diagnosisGroupCode}
                              patientId={patientId}
                              lhmValue={values[index]}
                              hasStandardCombination={
                                isDisplayStandardCombination
                              }
                            />
                          )}
                        </Field>
                      </>
                    </FormGroup>
                  </Flex>
                  <Flex auto className="validUntilDate">
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched?.[index]?.validUntilDate,
                        errors[`${name}.${index}.validUntilDate`]
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched?.[index]?.validUntilDate,
                        errors[`${name}.${index}.validUntilDate`]
                      )}
                    >
                      <>
                        <Label name={`${name}.${index}.validUntilDate`}>
                          {t('validUntil')}
                        </Label>
                        <Field name={`${name}.${index}.validUntilDate`}>
                          {({ field, form }) => (
                            <DateInput
                              className={
                                'datetime-lhm ' +
                                CreatePatientUtil.renderFormClass(
                                  submitCount,
                                  touched?.[index]?.validUntilDate,
                                  errors[`${name}.${index}.validUntilDate`]
                                )
                              }
                              data-tab-id={field.name}
                              formatDate={(date) =>
                                datetimeUtil.dateTimeFormat(date, DATE_FORMAT)
                              }
                              parseDate={(str) =>
                                datetimeUtil.strToDate(str, DATE_FORMAT)
                              }
                              inputProps={{
                                leftElement: (
                                  <Svg
                                    src={CalendarIcon}
                                    className="calendar-icon"
                                  />
                                ),
                              }}
                              popoverProps={{ usePortal: false }}
                              placeholder={DATE_FORMAT}
                              value={
                                field.value
                                  ? datetimeUtil.dateTimeFormat(
                                    new Date(field.value),
                                    YEAR_MONTH_DAY_FORMAT
                                  )
                                  : null
                              }
                              localeUtils={MomentLocaleUtils}
                              initialMonth={
                                !field.value ? datetimeUtil.date() : undefined
                              }
                              minDate={datetimeUtil.add(1, 'days').toDate()}
                              showActionsBar
                              outOfRangeMessage={t('outOfRangeErrorMessage')}
                              maxDate={new Date(2300, 1, 1)}
                              onChange={(dateValue: string) => {
                                form.setFieldValue(field.name, +dateValue);
                              }}
                            />
                          )}
                        </Field>
                      </>
                    </FormGroup>
                  </Flex>
                  <Flex auto>
                    <FormGroup>
                      <>
                        <Label name={`${name}.${index}.note`}>
                          {t('note')}
                        </Label>
                        <Field name={`${name}.${index}.note`}>
                          {({ field }) => {
                            return (
                              <InputGroup
                                {...field}
                                intent={CreatePatientUtil.getFormInputIntent(
                                  submitCount,
                                  touched?.[index]?.note,
                                  errors[`${name}.${index}.note`]
                                )}
                                data-tab-id={field.name}
                                maxLength={51}
                              />
                            );
                          }}
                        </Field>
                      </>
                    </FormGroup>
                  </Flex>
                  {values?.length > 1 && index < (values?.length || 0) - 1 ? (
                    <Divider className={`${Classes.FILL} sl-divider`} />
                  ) : null}
                  {index > 0 ? (
                    <Svg
                      src={RemoveIcon}
                      className={`remove-lhm`}
                      onClick={() => {
                        return setRemoveAction(() => () => {
                          index !== 0 && arrayHelpers.remove(index);
                          setRemoveAction(undefined);
                        });
                      }}
                    />
                  ) : null}
                </div>
              );
            })}
            {isInPatientInformation && (
              <Button
                className="sl-add-lhm"
                intent={Intent.PRIMARY}
                outlined
                onClick={() =>
                  arrayHelpers.push(Object.assign({}, initDefaultLHMItem))
                }
                minimal
              >
                {t('addLHM')}
              </Button>
            )}
            {typeof removeAction === 'function' ? (
              <ConfirmDialog
                close={() => setRemoveAction(undefined)}
                confirm={removeAction}
                text={{
                  btnCancel: t('cancel'),
                  btnOk: t('remove'),
                  title: t('removeLHMDialogTitle'),
                  message: t('weWillNotBeAbleToUndo'),
                }}
              />
            ) : null}
          </>
        )}
      />
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(LongtermHeimiApproval, {
    namespace: 'PatientProfileCreation',
    nestedTrans: 'InsuranceInformation',
  })
);
