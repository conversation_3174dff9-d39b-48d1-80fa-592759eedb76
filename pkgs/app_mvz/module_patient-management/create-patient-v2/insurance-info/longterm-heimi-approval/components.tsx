import React, {
  memo,
  SyntheticEvent,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { Checkbox, MenuItem } from '@tutum/design-system/components/Core';
import { Flex, BodyTextM } from '@tutum/design-system/components';
import {
  ItemRendererProps,
  MultiSelect,
  Select,
  Suggest,
} from '@tutum/design-system/components/Select';
import {
  debounce,
  GetNullUUID,
} from '@tutum/design-system/infrastructure/utils';
import { inputEnterWOSubmit } from './utils';
import { lhmActions } from './store';
import { useEmployeeStore } from '@tutum/mvz/hooks/useEmployee';
import { ProductDetail } from '@tutum/hermes/bff/app_mvz_heimi';
import { LHM } from '@tutum/hermes/bff/patient_profile_common';
import { selectItemKeyValueRenderExtended } from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection';
import { SelectTrigger } from '@tutum/mvz/components/select/Select';
import { SelectorValue } from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { searchDiagnosis } from '@tutum/infrastructure/masterdata-service';
import { IcdSeachItem } from '@tutum/hermes/bff/legacy/sdicd_common';
import { getDiagnoseGroup, getRemedy, GetRemedyResponse } from '@tutum/hermes/bff/legacy/app_mvz_heimi';

const moduleDebounce = 500; /* ms */

export const SearchICDSuggestion = Suggest.ofType<IcdSeachItem>();
interface ISearchICDProps {
  onChange: (val: string) => void;
  value: string; // icd code/id
  disabled?: boolean;
  placeholder: string;
  exceptList?: string[];
}

export const SearchICD = memo(
  ({ onChange, value, disabled, placeholder, exceptList }: ISearchICDProps) => {
    const [query, setQuery] = useState('');
    const [listICD, setListICD] = useState<IcdSeachItem[]>([]);
    const renderDiagnoseData = (dD: IcdSeachItem) => {
      return `${dD.code} ${dD.description}`;
    };
    const handleSearchMedication = (_query: string, overrideQuery = false) => {
      if (_query === query) {
        return;
      }
      setQuery(_query);
      if (
        _query?.length > 2 &&
        `${query || ''}`.toLowerCase() !== `${_query || ''}`.toLowerCase()
      ) {
        debounce(moduleDebounce, () => {
          searchDiagnosis({
            query: _query,
            selectedDate: datetimeUtil.now(),
          }).then((rs: IcdSeachItem[]) => {
            setListICD(rs);
            if (overrideQuery) {
              const dD = rs.find((diagnoseData) => diagnoseData.code === value);
              if (dD) {
                setQuery(renderDiagnoseData(dD));
              }
            }
          });
        })(_query);
      } else if (listICD?.length > 0) {
        setListICD([]);
      }
    };
    useEffect(() => {
      if (value) {
        handleSearchMedication(value, true);
      }
    }, []);

    const icdItemRenderer = (
      item,
      { handleClick, modifiers }: ItemRendererProps
    ) => {
      return (
        <MenuItem
          className={item.group ? 'group' : ''}
          key={item.code}
          onClick={handleClick}
          active={modifiers.active}
          disabled={modifiers.disabled}
          text={
            <Flex auto className="search-icd-menuitem">
              <span>{renderDiagnoseData(item)}</span>
            </Flex>
          }
        />
      );
    };
    const changedHandler = (item: IcdSeachItem) => {
      onChange(item?.code || '');
    };

    return (
      <SearchICDSuggestion
        fill
        query={query}
        // need to run the filter right here
        items={listICD.filter((item) => !exceptList?.includes(item.code)) || []}
        disabled={disabled}
        onItemSelect={(val) => {
          changedHandler(val);
        }}
        inputValueRenderer={(diagnose) =>
          `${diagnose.code} ${diagnose.description}`
        }
        onQueryChange={(query) => {
          handleSearchMedication(query);

          if (!query) {
            changedHandler(null!);
          }
        }}
        itemRenderer={icdItemRenderer}
        popoverProps={{
          minimal: true,
          position: 'bottom-left',
          usePortal: false,
        }}
        inputProps={{
          placeholder: placeholder,
          onKeyDown: inputEnterWOSubmit,
        }}
      />
    );
  }
);

interface ISearchDiagnosisGroupProps {
  onChange: (val: string) => void;
  value: string;
  diagnoseCode?: string;
  secondDiagnoseCode?: string;
  exceptList: string[];
  patientAge: number;
  disabled?: boolean;
}

export const SearchDiagnosisGroup = memo(
  ({
    value,
    onChange,
    diagnoseCode,
    secondDiagnoseCode,
    exceptList,
    patientAge,
    disabled,
  }: ISearchDiagnosisGroupProps) => {
    const employeeStore = useEmployeeStore();
    const [diagnoseGroupList, setDiagnosisGroupList] = useState<
      SelectorValue[]
    >([]);
    const getDiagnosisGroup = useCallback(
      (diagnoseCode, secondDiagnoseCode, patientAge) => {
        getDiagnoseGroup({
          area: null!,
          bsnr: employeeStore?.userProfile?.bsnr!,
          diagnoseCode: diagnoseCode,
          secondDiagnoseCode: secondDiagnoseCode,
          patientAge: patientAge,
          isGetAll: true,
        }).then((data) => {
          setDiagnosisGroupList(
            (data.data?.diagnoses || []).map((d) => ({
              name: d.name,
              value: d.code,
              label: d.diagnoseLabel,
              code: d.code,
              command: undefined,
              hints: d.hints,
              diseases: d.diseases,
              diseaseCode: d.diseaseCode,
              isStandardCombination: d.isStandardCombination,
              isBlankForm: d.isBlankForm,
              disabled: false,
            }))
          );
          if (value) {
            lhmActions.setFlagStandardCombination(
              value,
              !!(data.data?.diagnoses || []).find((item) => item.code === value)
                ?.isStandardCombination
            );
          }
        });
      },
      [diagnoseCode, secondDiagnoseCode, patientAge]
    );
    useEffect(() => {
      getDiagnosisGroup(diagnoseCode, secondDiagnoseCode, patientAge);
    }, [diagnoseCode, secondDiagnoseCode, patientAge]);
    const valueChangedHandler = (
      diagnose: SelectorValue,
      evt: SyntheticEvent<HTMLElement>
    ) => {
      evt?.stopPropagation();
      evt?.preventDefault();
      onChange(diagnose.code!);
      lhmActions.setFlagStandardCombination(
        diagnose.code!,
        !!diagnose?.isStandardCombination
      );
      return false;
    };
    const activeItem = diagnoseGroupList?.find(
      (item) => item.code === value
    );
    const displayText = activeItem
      ? `${activeItem?.code} - ${activeItem?.name}`
      : '';
    const items = (diagnoseGroupList || []).filter(
      (dg) => !exceptList?.includes(dg.code!)
    );
    return (
      <Select
        onItemSelect={valueChangedHandler}
        disabled={disabled}
        itemRenderer={selectItemKeyValueRenderExtended({
          defaultValue: activeItem
            ? { value: activeItem.code!, label: displayText }
            : undefined,
          itemsLength: (items || []).length,
          multiSelect: false,
        })}
        items={items}
        popoverProps={{
          minimal: true,
          position: 'bottom',
          usePortal: false,
        }}
        filterable={false}
      >
        <SelectTrigger disabled={disabled} text={displayText} />
      </Select>
    );
  }
);

interface IRemediesInputProps {
  value: string[];
  onChange: (val: string[]) => void;
  diagnoseCode?: string;
  secondDiagnoseCode?: string;
  patientAge: number;
  group: string;
  complementary?: boolean;
  patientId?: string;
  lhmValue: LHM;
  hasStandardCombination: boolean;
  placeholder?: string;
}

const renderMultipleOptions =
  (defaultValue: string[], isDisabled) =>
    (item: ProductDetail, { handleClick, modifiers }) => {
      if (!modifiers.matchesPredicate) {
        return null;
      }
      const isChecked = (defaultValue || []).includes(item.key);
      return (
        <MenuItem
          multiline
          className={`sl-multi-options ${isChecked ? 'sl-checked' : ''}`}
          key={`${item.key}`}
          disabled={!isChecked && isDisabled}
          text={
            <>
              <Flex align="center" justify="space-between">
                <BodyTextM className="sl-text-item">{item.name}</BodyTextM>
                <Checkbox
                  disabled={!isChecked && isDisabled}
                  checked={isChecked}
                  onClick={(e) => {
                    e.stopPropagation();
                    return false;
                  }}
                />
              </Flex>
            </>
          }
          onClick={handleClick}
          shouldDismissPopover={false}
        />
      );
    };
export const RemediesInput = memo(
  ({
    value,
    onChange,
    diagnoseCode,
    secondDiagnoseCode,
    patientAge,
    group,
    complementary,
    patientId,
    lhmValue,
    hasStandardCombination,
    placeholder,
  }: IRemediesInputProps) => {
    const [remedies, setRemedies] = useState<GetRemedyResponse>();
    const [query, setQuery] = useState('');
    const _value = value || [];
    const getRemedies = useCallback(
      (diagnoseCode, secondDiagnoseCode, patientAge, group) => {
        if (patientAge && diagnoseCode && group) {
          getRemedy({
            area: '',
            firstICDCode: diagnoseCode,
            secondICDCode: secondDiagnoseCode,
            group: group,
            keySymptoms: [],
            isGetAll: true,
            patientAge: patientAge,
            patientId: patientId || GetNullUUID(),
          })
            .then((data) => setRemedies(data.data))
            .catch((e) => console.error(e));
        }
      },
      [diagnoseCode, secondDiagnoseCode, patientAge, group]
    );
    useEffect(() => {
      getRemedies(diagnoseCode, secondDiagnoseCode, patientAge, group);
    }, [diagnoseCode, secondDiagnoseCode, patientAge, group]);
    const listItem =
      (!complementary ? remedies?.remedies : remedies?.complementaryRemedies) ||
      [];

    const onItemRemove = (nameStr: string) => {
      const item = listItem.find((item) => item.name === nameStr);
      onChange(_value.filter((pos) => pos !== item?.key));
    };
    // value
    const itemSelectHandler = (item: ProductDetail) => {
      const isRemove = _value.includes(item.key);
      if (!isRemove) {
        onChange([..._value, item.key]);
      } else {
        onChange(_value.filter((pos) => pos !== item.key));
      }
    };
    let isStopSelect = false;
    if (!lhmValue?.isStandardCombination && !hasStandardCombination) {
      if (!complementary) {
        // primary
        isStopSelect = !!lhmValue?.primaryRemediesPosition?.length && lhmValue.primaryRemediesPosition.length >= 3;
      } else {
        isStopSelect = !!lhmValue?.complementaryRemediesPosition?.length && lhmValue.complementaryRemediesPosition.length >= 1;
      }
    } else if (hasStandardCombination && !lhmValue?.isStandardCombination) {
      isStopSelect =
        // maximum 3 primary
        (!complementary && !!lhmValue?.primaryRemediesPosition?.length && lhmValue.primaryRemediesPosition.length >= 3) ||
        // maximum 1 complementary
        (!!complementary && !!lhmValue?.complementaryRemediesPosition?.length && lhmValue.complementaryRemediesPosition.length >= 1);
      // but at least one of them have selected (error handler)
    }
    return (
      <MultiSelect
        placeholder={placeholder}
        tagRenderer={(item) => item.name}
        items={
          !query
            ? listItem
            : listItem.filter((pd) =>
              pd.name.toLowerCase().includes(query.toLowerCase())
            )
        }
        selectedItems={listItem?.filter((item) => _value.includes(item.key))}
        popoverProps={{
          usePortal: false,
          minimal: true,
        }}
        itemRenderer={renderMultipleOptions(_value, isStopSelect)}
        tagInputProps={{
          onRemove: onItemRemove,
          disabled: !(diagnoseCode && patientAge && group),
        }}
        noResults={<MenuItem disabled={true} text="No results." />}
        onQueryChange={(q) => setQuery(q)}
        itemsEqual={(item1, item2) => item1 === item2}
        resetOnSelect
        onItemSelect={itemSelectHandler}
      />
    );
  }
);
