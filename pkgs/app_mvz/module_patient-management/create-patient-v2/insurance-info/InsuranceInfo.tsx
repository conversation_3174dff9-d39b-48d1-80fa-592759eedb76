import React, { useState, useEffect, useRef, useMemo } from 'react';
import { FieldArray, FastField, Field, FieldProps } from 'formik';
import get from 'lodash/get';
import debounce from 'lodash/debounce';
import MomentLocaleUtils from 'react-day-picker/moment';
import moment from 'moment';

import type patientManagementI18n from '@tutum/mvz/locales/en/PatientProfileCreation.json';
import type SidebarI18n from '@tutum/mvz/locales/en/Sidebar.json';

import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { IMvzTheme } from '@tutum/mvz/theme';
import {
  BodyTextM,
  Flex,
  ReactSelect,
  IMenuItem as IMenuItemReactSelect,
  InfoConfirmDialog,
  FormGroup2,
  Svg,
} from '@tutum/design-system/components';
import {
  FormGroup,
  Label,
  Intent,
  Classes,
  Button,
  InputGroup,
  Checkbox,
  Collapse,
  Icon,
  RadioGroup,
  Radio,
} from '@tutum/design-system/components/Core';
import NumberInput from '@tutum/design-system/components/NumberInput';
import InsuranceType from '../../types/insurance.type';
import DMP_PROGRAMS from '@tutum/design-system/consts/DMP-programs';
import { IMenuItem } from '@tutum/mvz/components/categories-search';
import CreatePatientUtil from '@tutum/infrastructure/utils/form.util';
import {
  initInsuranceItem,
  ICustomInsuranceInfo,
  getDateOfBirth,
  getCopaymentExemptionTillDateOver18y,
  IInitialValues,
} from '../CreatePatient.helper';
import { getInsuranceCompany } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { ValidationType, Order, FieldError } from '@tutum/hermes/bff/common';
import {
  CreatePatientProfileErrorCode,
  FromCardType,
} from '@tutum/hermes/bff/patient_profile_common';
import ConfirmDialog from '../confirm-dialog/ConfirmDialog.styled';
import { DateInput } from '@tutum/design-system/components/DateTime';
import {
  DATE_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import {
  IkNumber,
  SdktCatalog,
  SdktStatus,
} from '@tutum/hermes/bff/catalog_sdkt_common';
import { SearchType } from '@tutum/hermes/bff/legacy/catalog_sdkt_common';
import { checkExpiredCostUnit } from './const';
import LongtermHeimiApproval from './longterm-heimi-approval';
import CreateCostUnitDialog from '@tutum/mvz/module_sdkt/create-cost-unit-dialog/create-cost-unit-dialog';
import AddIkNumberDialog from '@tutum/mvz/module_sdkt/add-ik-number-dialog';
import { useEmployeeStore } from '@tutum/mvz/hooks/useEmployee';
import { backspace2ClearSelectBox } from '@tutum/mvz/_utils/accessibility';
import insuranceInfoUtils, { handleGetCostUnits } from './InsuranceInfo.util';
import InsuranceNameInput from './insurance-name-input';
import { COLOR } from '@tutum/design-system/themes/styles';
import { InsuranceNoResults } from './InsuranceInfo.styled';
import {
  useMutationSaveSetting,
  useQueryGetSetting,
} from '@tutum/hermes/bff/legacy/app_mvz_schein';
import { TypeOfInsurance } from '@tutum/hermes/bff/patient_profile_common';
import { convertMMYYYYtoValue } from './InsuranceInfo.util';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export interface InsuranceInfoProps {
  theme?: IMvzTheme;
  className?: string;
  submitCount?: number;
  errors?: any;
  touched?: any;
  patientId?: string;
  insuranceInfos: ICustomInsuranceInfo[];
  isEdit?: boolean;
  isReadTICard?: boolean;
  isReadMobileCard?: boolean;
  serverErrors?: {
    [key: string]: FieldError;
  };
  setFieldError: (field: string, message: string | undefined) => void;
  setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
  setServerErrors: (errors: Record<string, FieldError>) => void;
  values: IInitialValues;
}

const minusCircle = '/images/minus-circle.svg';
const calendarIcon = '/images/calendar-default.svg';

const InsuranceInfo = ({
  t,
  className,
  submitCount,
  errors,
  touched,
  insuranceInfos,
  patientId,
  isEdit,
  isReadTICard = false,
  isReadMobileCard,
  serverErrors,
  setFieldValue,
  // setServerErrors,
  values,
}: InsuranceInfoProps &
  II18nFixedNamespace<
    keyof typeof patientManagementI18n.InsuranceInformation
  >) => {
  const { t: tCreatePatient } = I18n.useTranslation<
    keyof typeof patientManagementI18n.CreatePatient
  >({
    namespace: 'PatientProfileCreation',
    nestedTrans: 'CreatePatient',
  });

  const { t: tSpecialGroup9 } = I18n.useTranslation<
    keyof typeof SidebarI18n.SpecialGroup09Dialog
  >({
    namespace: 'Sidebar',
    nestedTrans: 'SpecialGroup09Dialog',
  });

  const { data: dataSetting, refetch: refetchSetting } = useQueryGetSetting();
  const { mutate: saveSetting } = useMutationSaveSetting({
    onSuccess: () => {
      refetchSetting();
    },
  });

  const [collapseItems, setCollapseItems] = useState([]);
  const [ikNumbers, setIkNumbers] = useState<IkNumber[][]>([]);
  const [removeAction, setRemoveAction] = useState<() => void>(null);
  const [showCostUnitDialog, setShowCostUnitDialog] = useState<boolean>(false);
  const [showAddIKDialog, setShowAddIKDialog] = useState<boolean>(false);
  const [preFilledFormValues, setPreFilledFormValues] =
    useState<Partial<SdktCatalog>>();
  const [searchIKNumberValue, setSearchIKNumberValue] = useState<string>('');
  const [showHintSpecialGroup09, setShowHintSpecialGroup09] =
    useState<boolean>(false);
  const [preventShowMessageAgain, setPreventShowMessageAgain] =
    useState<boolean>(false);

  const ikNumberRef = useRef([]);

  const { userProfile } = useEmployeeStore();
  const ukv = userProfile?.bsnr?.substr(0, 2);

  useEffect(() => {
    setCollapseItems(
      insuranceInfos?.map((_, index) =>
        collapseItems[index] === false ? false : true
      ) || []
    );
    if (isEdit && !ikNumbers.length) {
      handleGetCostUnits(insuranceInfos).then((res) => {
        if (!res) return;
        const listIkNumber = res?.map((item) => item?.iKNumbers);
        res?.forEach((item, index) => {
          setFieldValue(
            `insuranceInfos.${index}.isTerminated`,
            item?.status === SdktStatus.SdktStatus_Terminated
          );
        });
        setIkNumbers(listIkNumber);
      });
    }
  }, [insuranceInfos]);

  const handleSetCollapse = (index: number) => {
    const collapseItemsTemp = [...collapseItems];
    collapseItemsTemp[index] = !collapseItemsTemp[index];
    setCollapseItems(collapseItemsTemp);
  };

  const debouncedQueryChangeHandler = useMemo(
    () =>
      debounce(async (_query, _form, _searchType, _setInsurances) => {
        try {
          if (!_query) {
            _setInsurances([]);
            setSearchIKNumberValue('');
            return;
          }
          const res = await getInsuranceCompany({
            type:
              (_searchType?.value as SearchType) ||
              SearchType.SearchType_CostUnitName,
            value: _query,
            pagination: {
              page: 1,
              pageSize: 20,
              order: Order.ASC,
              sortBy: 'name',
            },
          });
          const { insuranceCompanies } = res.data;

          const filteredInsCompanies = insuranceCompanies?.filter(
            (item) =>
              !item.restrictKvRegions?.includes(ukv || '') ||
              item.status !== SdktStatus.SdktStatus_Terminated
          );
          _setInsurances(filteredInsCompanies);
          setSearchIKNumberValue(_query);
          setPreFilledFormValues(
            insuranceInfoUtils.parseQueryToCostUnitFormValues(
              _query,
              _searchType?.value as SearchType
            )
          );
        } catch (error) {
          console.error(error);
          throw error;
        }
      }, 500),
    [ukv]
  );

  const handleOnChangeInsuranceNumber = (form, field, value) => {
    form.setFieldValue(field.name, value);
  };

  return (
    <Flex column className={className}>
      <FieldArray
        name="insuranceInfos"
        render={(arrayHelpers) => (
          <>
            {insuranceInfos?.map((item, index) => {
              const isInvalid =
                isEdit &&
                item.isDetail &&
                moment
                  .duration(moment(datetimeUtil.date()).diff(item.endDate))
                  .asYears() > 1;
              const isDisableFromReadCard =
                isReadTICard && item.insuranceType !== TypeOfInsurance.Private;
              const isDisabled =
                !isReadMobileCard && (isDisableFromReadCard || isInvalid);
              const fieldErrorInsuranceCompanyId =
                serverErrors?.[`insuranceInfos.${index}.insuranceCompanyId`];
              const fieldErrorIkNumber =
                serverErrors?.[`insuranceInfos.${index}.ikNumber`];
              const errorServer =
                fieldErrorInsuranceCompanyId?.validationType ===
                  ValidationType.ValidationType_Error
                  ? t(
                    `Error.${fieldErrorInsuranceCompanyId?.errorCode}` as keyof typeof patientManagementI18n.InsuranceInformation
                  )
                  : null;
              const warning =
                fieldErrorInsuranceCompanyId?.errorCode ===
                  CreatePatientProfileErrorCode.CostUnitHasExpired ||
                  item?.isExpiredCostUnit
                  ? t(
                    'Error.CostUnitHasExpired' as keyof typeof patientManagementI18n.InsuranceInformation
                  )
                  : null;
              const warningIkNumberExpired =
                fieldErrorIkNumber &&
                t(
                  'Error.IKNumberHasExpired' as keyof typeof patientManagementI18n.InsuranceInformation
                );

              const isTerminated = item?.isTerminated;
              const isPrivateInsurance =
                values['insuranceInfos'][index].insuranceType ===
                TypeOfInsurance.Private;

              // render public insurance
              const renderPublicInsurance = () => (
                <>
                  <InsuranceNameInput
                    disabled={isDisabled}
                    submitCount={submitCount}
                    touched={
                      isTerminated ||
                      warning ||
                      errorServer ||
                      touched.insuranceInfos?.[index]?.insuranceCompanyName
                    }
                    fieldName={`insuranceInfos.${index}.insuranceCompanyName`}
                    errorText={
                      errorServer ||
                      errors[`insuranceInfos.${index}.insuranceCompanyName`]
                    }
                    warningText={warning}
                    label={
                      <Label
                        onClick={() => handleSetCollapse(index)}
                        style={{ cursor: 'pointer' }}
                        required
                      >
                        {t('insuranceCompany')}
                        <Icon
                          className="expand-icon"
                          icon={
                            collapseItems[index] ? 'chevron-up' : 'chevron-down'
                          }
                          intent={Intent.NONE}
                          style={{
                            position: 'absolute',
                            right: 12,
                            color: COLOR.TEXT_TERTIARY_SILVER,
                          }}
                        />
                      </Label>
                    }
                    onItemSelect={(item, _form, _field, _setSearchType) => {
                      _form.setFieldValue(_field.name, item.name || '');
                      _form.setFieldValue(
                        `insuranceInfos.${index}.insuranceCompanyId`,
                        item.vknr || ''
                      );
                      _form.setFieldValue(
                        `insuranceInfos.${index}.validity`,
                        item.validity
                      );
                      _form.setFieldValue(
                        `insuranceInfos.${index}.locationNames`,
                        item.locationNames
                      );
                      _form.setFieldValue(
                        `insuranceInfos.${index}.isExpiredCostUnit`,
                        checkExpiredCostUnit(item?.validity?.toDate)
                      );
                      _form.setFieldValue(
                        `insuranceInfos.${index}.feeCatalogue`,
                        parseInt(item.feeCatalogue, 10).toString()
                      );
                      const ikNumbersSpread = [...ikNumbers];
                      ikNumbersSpread[index] = item.iKNumbers;
                      setIkNumbers(ikNumbersSpread);
                      if (item.iKNumbers?.length > 0) {
                        const matchedItemByIKNumberValue = item.iKNumbers.find(
                          (iKNumber) =>
                            String(iKNumber.value) === searchIKNumberValue
                        );
                        _form.setFieldValue(
                          `insuranceInfos.${index}.ikNumber`,
                          matchedItemByIKNumberValue
                            ? matchedItemByIKNumberValue.value
                            : item.iKNumbers[0].value
                        );
                      }
                      _setSearchType(null);
                      setSearchIKNumberValue('');
                      ikNumberRef.current[index]?.focus();
                    }}
                    noResult={(_searchType) => (
                      <InsuranceNoResults>
                        <BodyTextM
                          className="sl-insurance-no-results-text"
                          onClick={() => setShowCostUnitDialog(true)}
                          limitLines={1}
                        >
                          {t('createInsurance')}
                        </BodyTextM>
                        {_searchType?.value ===
                          SearchType.SearchType_IkNumbers && (
                            <>
                              <BodyTextM
                                limitLines={1}
                                className="sl-insurance-no-results-divider"
                              >
                                &nbsp;{t('or')}&nbsp;
                              </BodyTextM>
                              <BodyTextM
                                className="sl-insurance-no-results-text"
                                onClick={() => setShowAddIKDialog(true)}
                                limitLines={1}
                              >
                                {t('addIkToCostUnit')}
                              </BodyTextM>
                            </>
                          )}
                      </InsuranceNoResults>
                    )}
                    beforeQueryChange={(_form) => {
                      if (isTerminated) {
                        _form.setFieldValue(
                          `insuranceInfos.${index}.isTerminated`,
                          false
                        );
                      }
                    }}
                    onQueryChange={debouncedQueryChangeHandler}
                  />
                  <Collapse isOpen={collapseItems[index]}>
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched.insuranceInfos?.[index]?.ikNumber,
                        errors[`insuranceInfos.${index}.ikNumber`],
                        warningIkNumberExpired
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched.insuranceInfos?.[index]?.ikNumber,
                        errors[`insuranceInfos.${index}.ikNumber`],
                        warningIkNumberExpired
                      )}
                    >
                      <Label required>{t('insuranceIkNumber')}</Label>
                      <Field name={`insuranceInfos.${index}.ikNumber`}>
                        {({ field, form }) => {
                          return (
                            <ReactSelect
                              ref={(el) => (ikNumberRef.current[index] = el)}
                              isSearchable={false}
                              className={CreatePatientUtil.renderFormClass(
                                submitCount,
                                touched.insuranceInfos?.[index]
                                  ?.insuranceStatus,
                                errors[`insuranceInfos.${index}.ikNumber`]
                              )}
                              onKeyDown={(evt) =>
                                backspace2ClearSelectBox(evt, () =>
                                  form.setFieldValue(field.name, undefined)
                                )
                              }
                              placeholder={t('selectIkNumbers')}
                              selectedValue={field.value}
                              isDisabled={isDisabled}
                              items={ikNumbers?.[index]?.map(
                                (item): IMenuItemReactSelect => ({
                                  label: `${item.value}`,
                                  value: item.value,
                                })
                              )}
                              onItemSelect={(item: IMenuItem) => {
                                form.setFieldValue(
                                  field.name,
                                  item['value'] || undefined
                                );
                              }}
                            />
                          );
                        }}
                      </Field>
                    </FormGroup>
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched.insuranceInfos?.[index]?.insuranceStatus,
                        errors[`insuranceInfos.${index}.insuranceStatus`]
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched.insuranceInfos?.[index]?.insuranceStatus,
                        errors[`insuranceInfos.${index}.insuranceStatus`]
                      )}
                    >
                      <Label name="insuranceStatus" required>
                        {t('insuranceStatus')}
                      </Label>
                      <FastField
                        name={`insuranceInfos.${index}.insuranceStatus`}
                      >
                        {({ field, form }) => {
                          return (
                            <ReactSelect
                              isSearchable={false}
                              className={CreatePatientUtil.renderFormClass(
                                submitCount,
                                touched.insuranceInfos?.[index]
                                  ?.insuranceStatus,
                                errors[
                                `insuranceInfos.${index}.insuranceStatus`
                                ]
                              )}
                              placeholder={t('selectInsuranceStatus')}
                              selectedValue={field.value}
                              isDisabled={isDisabled}
                              items={InsuranceType.INSURANCE_STATUS_LIST?.map(
                                (item): IMenuItemReactSelect => ({
                                  label: item.name,
                                  value: item.value,
                                })
                              )}
                              onItemSelect={(item: IMenuItem) => {
                                form.setFieldValue(
                                  field.name,
                                  item['value'] || undefined
                                );
                              }}
                            />
                          );
                        }}
                      </FastField>
                    </FormGroup>
                    <FormGroup
                      helperText={CreatePatientUtil.renderFormHelperText(
                        submitCount,
                        touched.insuranceInfos?.[index]?.insuranceNumber,
                        errors[`insuranceInfos.${index}.insuranceNumber`],
                        InsuranceType.TEST_INSURANCE_NUMBER.includes(
                          item.insuranceNumber || ''
                        )
                          ? tCreatePatient('warningInsuranceNumber', {
                            insuranceNumber: item.insuranceNumber || '',
                          })
                          : ''
                      )}
                      className={CreatePatientUtil.renderFormClass(
                        submitCount,
                        touched.insuranceInfos?.[index]?.insuranceNumber,
                        errors[`insuranceInfos.${index}.insuranceNumber`],
                        InsuranceType.TEST_INSURANCE_NUMBER.includes(
                          item.insuranceNumber || ''
                        )
                          ? tCreatePatient('warningInsuranceNumber', {
                            insuranceNumber: item.insuranceNumber || '',
                          })
                          : ''
                      )}
                    >
                      <Label name="insuranceNumber">
                        {t('insuranceNumber')}
                      </Label>
                      <Field name={`insuranceInfos.${index}.insuranceNumber`}>
                        {({ field, form }) => {
                          const insuranceInfo =
                            form.values['insuranceInfos'][index];
                          const insuranceCompanyId: string =
                            insuranceInfo['insuranceCompanyId'];
                          const fromCardType: FromCardType =
                            insuranceInfo['fromCardType'];
                          const maxlLength = InsuranceType.isPublicCostUnit(
                            insuranceCompanyId,
                            fromCardType
                          )
                            ? 10
                            : 12;

                          return (
                            <InputGroup
                              {...field}
                              onChange={(e) =>
                                handleOnChangeInsuranceNumber(
                                  form,
                                  field,
                                  e.target.value
                                )
                              }
                              maxLength={maxlLength}
                              intent={CreatePatientUtil.getFormInputIntent(
                                submitCount,
                                touched.insuranceInfos?.[index]
                                  ?.insuranceNumber,
                                errors[
                                `insuranceInfos.${index}.insuranceNumber`
                                ]
                              )}
                              id={field.name}
                              disabled={isDisabled}
                            />
                          );
                        }}
                      </Field>
                    </FormGroup>
                    <Flex gap={16} className="calendar-input-group date">
                      <FormGroup
                        helperText={CreatePatientUtil.renderFormHelperText(
                          submitCount,
                          touched.insuranceInfos?.[index]?.startDate,
                          errors[`insuranceInfos.${index}.startDate`]
                        )}
                        className={CreatePatientUtil.renderFormClass(
                          submitCount,
                          touched.insuranceInfos?.[index]?.startDate,
                          errors[`insuranceInfos.${index}.startDate`]
                        )}
                        style={{ flex: 1 }}
                      >
                        <Label>{t('insuranceStartDate')}</Label>
                        <Field name={`insuranceInfos.${index}.startDate`}>
                          {({ field, form }) => {
                            const { setFieldValue, initialValues } = form;
                            const setDatetimeValue = (dateValue: string) => {
                              setFieldValue(
                                field.name,
                                dateValue ? new Date(+dateValue) : null
                              );
                            };
                            const defaultStartDate = initialValues
                              .insuranceInfos[index]?.validity?.fromDate
                              ? new Date(
                                initialValues.insuranceInfos[
                                  index
                                ].validity?.fromDate
                              )
                              : null;
                            const startDate = field.value || defaultStartDate;

                            return (
                              <DateInput
                                inputProps={{
                                  leftElement: (
                                    <Svg
                                      src={calendarIcon}
                                      className="calendar-icon"
                                    />
                                  ),
                                  className: CreatePatientUtil.renderFormClass(
                                    submitCount,
                                    touched.insuranceInfos?.[index]?.startDate,
                                    errors[`insuranceInfos.${index}.startDate`]
                                  ),
                                  id: field.name,
                                }}
                                invalidDateMessage={t(
                                  'invalidDateErrorMessage'
                                )}
                                outOfRangeMessage={t('outOfRangeErrorMessage')}
                                localeUtils={MomentLocaleUtils}
                                maxDate={new Date(2300, 1, 1)}
                                minDate={new Date(1900, 1, 1)}
                                formatDate={(date) =>
                                  datetimeUtil.dateTimeFormat(date, DATE_FORMAT)
                                }
                                parseDate={(str) =>
                                  datetimeUtil.strToDate(str, DATE_FORMAT)
                                }
                                popoverProps={{
                                  usePortal: false,
                                }}
                                placeholder={DATE_FORMAT}
                                data-tab-id={field.name}
                                value={
                                  startDate
                                    ? datetimeUtil.dateTimeFormat(
                                      startDate,
                                      YEAR_MONTH_DAY_FORMAT
                                    )
                                    : null
                                }
                                onChange={setDatetimeValue}
                                disabled={isDisabled}
                                showActionsBar
                              />
                            );
                          }}
                        </Field>
                      </FormGroup>
                      <FormGroup
                        helperText={CreatePatientUtil.renderFormHelperText(
                          submitCount,
                          touched.insuranceInfos?.[index]?.endDate,
                          errors[`insuranceInfos.${index}.endDate`]
                        )}
                        className={CreatePatientUtil.renderFormClass(
                          submitCount,
                          touched.insuranceInfos?.[index]?.endDate,
                          errors[`insuranceInfos.${index}.endDate`]
                        )}
                        style={{ flex: 1 }}
                      >
                        <Label>{t('insuranceEndDate')}</Label>
                        <Field name={`insuranceInfos.${index}.endDate`}>
                          {({ field, form }) => {
                            const { setFieldValue } = form;
                            const startDate = get(
                              values,
                              `insuranceInfos.${index}.startDate`
                            );
                            const setDatetimeValue = (dateValue: string) => {
                              setFieldValue(
                                field.name,
                                dateValue ? new Date(+dateValue) : null
                              );
                            };

                            return (
                              <DateInput
                                inputProps={{
                                  leftElement: (
                                    <Svg
                                      src={calendarIcon}
                                      className="calendar-icon"
                                    />
                                  ),
                                  className: CreatePatientUtil.renderFormClass(
                                    submitCount,
                                    touched.insuranceInfos?.[index]?.endDate,
                                    errors[`insuranceInfos.${index}.endDate`]
                                  ),
                                  id: field.name,
                                }}
                                invalidDateMessage={t(
                                  'invalidDateErrorMessage'
                                )}
                                outOfRangeMessage={t('outOfRangeErrorMessage')}
                                localeUtils={MomentLocaleUtils}
                                maxDate={new Date(2300, 1, 1)}
                                minDate={startDate || new Date(1900, 1, 1)}
                                initialMonth={
                                  form.values?.insuranceInfos?.[index]
                                    ?.startDate
                                }
                                formatDate={(date) =>
                                  datetimeUtil.dateTimeFormat(date, DATE_FORMAT)
                                }
                                parseDate={(str) =>
                                  datetimeUtil.strToDate(str, DATE_FORMAT)
                                }
                                popoverProps={{
                                  usePortal: false,
                                }}
                                placeholder={DATE_FORMAT}
                                data-tab-id={field.name}
                                value={
                                  field.value
                                    ? datetimeUtil.dateTimeFormat(
                                      field.value,
                                      YEAR_MONTH_DAY_FORMAT
                                    )
                                    : null
                                }
                                onChange={setDatetimeValue}
                                disabled={isDisabled}
                                showActionsBar
                              />
                            );
                          }}
                        </Field>
                      </FormGroup>
                    </Flex>
                    <FormGroup
                      helperText={errors.specialGroup}
                      className={
                        errors.specialGroup ? Classes.INTENT_DANGER : ''
                      }
                    >
                      <Label required>{t('specialGroup')}</Label>
                      <Field name={`insuranceInfos.${index}.specialGroup`}>
                        {({ field, form }) => {
                          return (
                            <ReactSelect
                              isSearchable={false}
                              className={CreatePatientUtil.renderFormClass(
                                submitCount,
                                touched.insuranceInfos?.[index]?.specialGroup,
                                errors[`insuranceInfos.${index}.specialGroup`]
                              )}
                              placeholder={t('selectGroup')}
                              selectedValue={field.value}
                              isDisabled={isDisabled}
                              items={InsuranceType.SPECIAL_GROUP_LIST?.map(
                                (item): IMenuItemReactSelect => ({
                                  label: t(
                                    item.name as keyof typeof patientManagementI18n.InsuranceInformation
                                  ),
                                  value: item.value,
                                })
                              )}
                              onItemSelect={(item: IMenuItem) => {
                                form.setFieldValue(
                                  field.name,
                                  item['value'] || undefined
                                );
                                if (
                                  item.value === '09' &&
                                  dataSetting?.showHintSpecialGroup09
                                ) {
                                  setShowHintSpecialGroup09(true);
                                }
                              }}
                            />
                          );
                        }}
                      </Field>
                    </FormGroup>
                    <FormGroup
                      helperText={errors.dMPLabeling}
                      className={
                        errors.dMPLabeling ? Classes.INTENT_DANGER : ''
                      }
                    >
                      <Label required>{t('dmp')}</Label>
                      <FastField name={`insuranceInfos.${index}.dMPLabeling`}>
                        {({ field, form }) => {
                          return (
                            <ReactSelect
                              isSearchable={false}
                              className={CreatePatientUtil.renderFormClass(
                                submitCount,
                                touched.insuranceInfos?.[index]?.dMPLabeling,
                                errors[`insuranceInfos.${index}.dMPLabeling`]
                              )}
                              placeholder={t('selectDmp')}
                              selectedValue={field.value}
                              isDisabled={isDisabled}
                              items={DMP_PROGRAMS.DMP_LIST.map(
                                (item): IMenuItemReactSelect => ({
                                  label: item.name,
                                  value: item.value,
                                })
                              )}
                              onItemSelect={(item: IMenuItem) => {
                                form.setFieldValue(
                                  field.name,
                                  item['value'] || undefined
                                );
                              }}
                            />
                          );
                        }}
                      </FastField>
                    </FormGroup>
                    <Flex column>
                      <FormGroup
                        style={{
                          marginBottom: insuranceInfos[index]
                            .haveCoPaymentExemptionTill
                            ? 8
                            : 15,
                        }}
                      >
                        <Field
                          name={`insuranceInfos.${index}.haveCoPaymentExemptionTill`}
                        >
                          {({ field, form }) => {
                            return (
                              <Checkbox
                                checked={field.value}
                                onChange={() => {
                                  const fieldValue = field.value;
                                  form.setFieldValue(field.name, !fieldValue);

                                  const { textYear } = getDateOfBirth(
                                    form.values.personalInfo.dateOfBirth,
                                    false
                                  );
                                  const currentYear = datetimeUtil
                                    .date()
                                    .getFullYear();
                                  const isOver18y =
                                    currentYear - +textYear >= 18;

                                  const copaymentExemptionTillDateValue =
                                    !fieldValue && isOver18y
                                      ? getCopaymentExemptionTillDateOver18y(
                                        currentYear
                                      )
                                      : null;

                                  form.setFieldValue(
                                    `insuranceInfos.${index}.copaymentExemptionTillDate`,
                                    copaymentExemptionTillDateValue
                                  );
                                }}
                                disabled={isInvalid}
                              >
                                <span>{t('coPaymentExemptionTill')}</span>
                              </Checkbox>
                            );
                          }}
                        </Field>
                      </FormGroup>
                      {insuranceInfos[index].haveCoPaymentExemptionTill && (
                        <Flex
                          justify="space-between"
                          className="calendar-input-group"
                        >
                          <FormGroup
                            helperText={CreatePatientUtil.renderFormHelperText(
                              submitCount,
                              touched.insuranceInfos?.[index]
                                ?.copaymentExemptionTillDate,
                              errors[
                              `insuranceInfos.${index}.copaymentExemptionTillDate`
                              ]
                            )}
                            className={CreatePatientUtil.renderFormClass(
                              submitCount,
                              touched.insuranceInfos?.[index]
                                ?.copaymentExemptionTillDate,
                              errors[
                              `insuranceInfos.${index}.copaymentExemptionTillDate`
                              ]
                            )}
                            style={{ paddingLeft: 24, width: '100%' }}
                          >
                            <Label>{t('validTill')}</Label>
                            <FastField
                              name={`insuranceInfos.${index}.copaymentExemptionTillDate`}
                            >
                              {({ field, form }) => {
                                const { setFieldValue } = form;
                                const setDatetimeValue = (
                                  dateValue: string
                                ) => {
                                  setFieldValue(
                                    field.name,
                                    dateValue ? new Date(+dateValue) : null
                                  );
                                };
                                return (
                                  <DateInput
                                    inputProps={{
                                      leftElement: (
                                        <Svg
                                          src={calendarIcon}
                                          className="calendar-icon"
                                        />
                                      ),
                                      className:
                                        CreatePatientUtil.renderFormClass(
                                          submitCount,
                                          touched.insuranceInfos?.[index]
                                            ?.copaymentExemptionTillDate,
                                          errors[
                                          `insuranceInfos.${index}.copaymentExemptionTillDate`
                                          ]
                                        ),
                                      id: field.name,
                                    }}
                                    invalidDateMessage={t(
                                      'invalidDateErrorMessage'
                                    )}
                                    outOfRangeMessage={t(
                                      'outOfRangeErrorMessage'
                                    )}
                                    localeUtils={MomentLocaleUtils}
                                    maxDate={new Date(2300, 1, 1)}
                                    minDate={new Date(1900, 1, 1)}
                                    formatDate={(date) =>
                                      datetimeUtil.dateTimeFormat(
                                        date,
                                        DATE_FORMAT
                                      )
                                    }
                                    parseDate={(str) =>
                                      datetimeUtil.strToDate(str, DATE_FORMAT)
                                    }
                                    popoverProps={{
                                      usePortal: true,
                                    }}
                                    placeholder={DATE_FORMAT}
                                    data-tab-id={field.name}
                                    value={
                                      field.value
                                        ? datetimeUtil.dateTimeFormat(
                                          field.value,
                                          YEAR_MONTH_DAY_FORMAT
                                        )
                                        : null
                                    }
                                    onChange={setDatetimeValue}
                                    showActionsBar
                                  />
                                );
                              }}
                            </FastField>
                          </FormGroup>
                        </Flex>
                      )}
                    </Flex>
                    <FormGroup>
                      <FastField
                        name={`insuranceInfos.${index}.havePatientReceipt`}
                      >
                        {({ field, form }) => {
                          return (
                            <Checkbox
                              checked={field.value}
                              onChange={() => {
                                form.setFieldValue(field.name, !field.value);
                              }}
                              disabled={isInvalid}
                            >
                              <span>{t('patientReceipt')}</span>
                            </Checkbox>
                          );
                        }}
                      </FastField>
                      <BodyTextM
                        padding="0 0 0 24px"
                        color={COLOR.TEXT_TERTIARY_SILVER}
                      >
                        {t('patientReceiptDescription')}
                      </BodyTextM>
                    </FormGroup>
                    <FormGroup>
                      <FastField
                        name={`insuranceInfos.${index}.haveHeimiLongTermApproval`}
                      >
                        {({ field, form }) => {
                          return (
                            <Checkbox
                              checked={field.value}
                              onChange={() => {
                                form.setFieldValue(field.name, !field.value);
                              }}
                              disabled={isInvalid}
                            >
                              <span>{t('heimiLongTermApproval')}</span>
                            </Checkbox>
                          );
                        }}
                      </FastField>
                    </FormGroup>
                    {insuranceInfos[index]?.haveHeimiLongTermApproval ? (
                      <FastField name={`insuranceInfos.${index}.lHMs`}>
                        {({ field, form }) => (
                          <LongtermHeimiApproval
                            values={field.value}
                            form={form}
                            name={field.name}
                            submitCount={submitCount}
                            errors={errors}
                            touched={touched.insuranceInfos?.[index]?.lHMs}
                            isEdit={isEdit}
                            disabled={isInvalid}
                            patientId={patientId}
                          />
                        )}
                      </FastField>
                    ) : null}
                  </Collapse>
                  {index ? (
                    <Svg
                      className="sl-remove-insurance-info"
                      tabIndex={0}
                      onClick={() =>
                        setRemoveAction(() => () => {
                          arrayHelpers.remove(index);
                          setRemoveAction(null);
                        })
                      }
                      src={minusCircle}
                      alt="minus-circle-icon"
                    />
                  ) : null}
                </>
              );

              // render private insurance
              const renderPrivateInsurance = () => (
                <>
                  <FormGroup2
                    name={`insuranceInfos.${index}.insuranceCompanyName`}
                    errors={errors}
                    touched={touched}
                    submitCount={submitCount}
                    label={t('insuranceCompany')}
                  >
                    <FastField
                      name={`insuranceInfos.${index}.insuranceCompanyName`}
                    >
                      {({ field }) => {
                        return (
                          <InputGroup
                            {...field}
                            value={field.value || ''}
                            id="insuranceCompanyName"
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                  <FormGroup2
                    errors={errors}
                    touched={touched}
                    submitCount={submitCount}
                    label={t('healInsuranceId')}
                    name={`insuranceInfos.${index}.insuranceCompanyId`}
                  >
                    <FastField
                      name={`insuranceInfos.${index}.insuranceCompanyId`}
                    >
                      {({ field }) => {
                        return (
                          <NumberInput
                            defaultValue={field.value || ''}
                            onValueChange={({ value }) =>
                              setFieldValue(field.name, value || null)
                            }
                            isRaw={true}
                            maxLength={4}
                            minLength={4}
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                  <FormGroup2
                    errors={errors}
                    touched={touched}
                    submitCount={submitCount}
                    isRequired
                    label={t('insuranceNumber')}
                    name={`insuranceInfos.${index}.insuranceNumber`}
                  >
                    <FastField name={`insuranceInfos.${index}.insuranceNumber`}>
                      {({ field }) => {
                        return (
                          <InputGroup
                            {...field}
                            value={field.value || ''}
                            id="insuranceNumber"
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                  <FormGroup2
                    errors={errors}
                    touched={touched}
                    submitCount={submitCount}
                    label={t('validUntil')}
                    name={`insuranceInfos.${index}.validTo`}
                  >
                    <FastField name={`insuranceInfos.${index}.validTo`}>
                      {({ field }) => {
                        return (
                          <NumberInput
                            defaultValue={field.value || ''}
                            format={'##.####'}
                            onChange={(v) => {
                              setFieldValue(field.name, v.target.value || null);
                              setFieldValue(
                                `insuranceInfos.${index}.validUntil`,
                                convertMMYYYYtoValue(v.target.value)
                              );
                            }}
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                  {index ? (
                    <Svg
                      className="sl-remove-insurance-info"
                      tabIndex={0}
                      onClick={() =>
                        setRemoveAction(() => () => {
                          arrayHelpers.remove(index);
                          setRemoveAction(null);
                        })
                      }
                      src={minusCircle}
                      alt="minus-circle-icon"
                    />
                  ) : null}
                </>
              );

              return (
                <Flex
                  className="sl-insurance-box"
                  column
                  key={`insurance-info_${insuranceInfos[index]?.id}_${index}_section`}
                  id={`insurance-info_${insuranceInfos[index]?.id}_section`}
                >
                  <FormGroup>
                    <Label>{t('insuranceType')}</Label>
                    <FastField name={`insuranceInfos.${index}.insuranceType`}>
                      {({ field, form }: FieldProps) => {
                        const { setFieldValue } = form;
                        const onChange = (
                          event: React.FormEvent<HTMLInputElement>
                        ) => {
                          setFieldValue(
                            `insuranceInfos.${index}`,
                            initInsuranceItem
                          );
                          setFieldValue(field.name, event.currentTarget.value);
                        };
                        return (
                          <RadioGroup
                            onChange={onChange}
                            inline
                            selectedValue={
                              field.value ? field.value : TypeOfInsurance.Public
                            }
                            disabled={isReadTICard}
                          >
                            <Radio
                              label={t('publicLb')}
                              value={TypeOfInsurance.Public}
                              data-tab-id="typeOfIsurance_public"
                            />
                            <Radio
                              label={t('privateLb')}
                              value={TypeOfInsurance.Private}
                              data-tab-id="typeOfIsurance_private"
                            />
                            <Radio
                              label={t('bgLb')}
                              value={TypeOfInsurance.BG}
                              data-tab-id="typeOfIsurance_bg"
                            />
                          </RadioGroup>
                        );
                      }}
                    </FastField>
                  </FormGroup>

                  {isPrivateInsurance
                    ? renderPrivateInsurance()
                    : renderPublicInsurance()}
                </Flex>
              );
            })}

            <div className="button-wrapper">
              <Button
                className="sl-add-insurance"
                intent={Intent.PRIMARY}
                outlined
                onClick={() => {
                  arrayHelpers.push({ ...initInsuranceItem });
                }}
                minimal
                disabled={isReadTICard}
              >
                {t('addInsurance')}
              </Button>
            </div>
          </>
        )}
      />

      {typeof removeAction === 'function' ? (
        <ConfirmDialog
          close={() => setRemoveAction(null)}
          confirm={removeAction}
          text={{
            btnCancel: t('cancel'),
            btnOk: t('remove'),
            title: t('removeThisInsuranceInfo'),
            message: t('weWillNotBeAbleToUndo'),
          }}
        />
      ) : null}

      {/* Create Cost Unit Dialog */}
      <CreateCostUnitDialog
        isOpen={showCostUnitDialog}
        onClose={() => setShowCostUnitDialog(false)}
        initialValues={preFilledFormValues}
      />

      {/* Add IK number to exising cost unit dialog */}
      {showAddIKDialog && (
        <AddIkNumberDialog
          isOpen
          onAddConfirm={async () => { }}
          onClose={() => setShowAddIKDialog(false)}
          initialValues={{
            ikNumber: preFilledFormValues?.iKNumbers?.[0]?.value,
          }}
        />
      )}
      {showHintSpecialGroup09 && (
        <InfoConfirmDialog
          type="primary"
          isOpen
          title={tSpecialGroup9('title')}
          cancelText={tSpecialGroup9('cancelText')}
          isCloseButtonShown={false}
          isConfirmButtonShown={false}
          onClose={() => {
            if (preventShowMessageAgain) {
              saveSetting({
                ...dataSetting,
                showHintSpecialGroup09: false,
              });
            }

            setShowHintSpecialGroup09(false);
          }}
        >
          <Flex column gap={16}>
            {tSpecialGroup9('description')}
            <Checkbox
              id="prevent-show-message-again"
              name="prevent-show-message-again"
              label={tSpecialGroup9('noted')}
              checked={preventShowMessageAgain}
              onChange={() =>
                setPreventShowMessageAgain(!preventShowMessageAgain)
              }
            />
          </Flex>
        </InfoConfirmDialog>
      )}
    </Flex>
  );
};

export default I18n.withTranslation(InsuranceInfo, {
  namespace: 'PatientProfileCreation',
  nestedTrans: 'InsuranceInformation',
});
