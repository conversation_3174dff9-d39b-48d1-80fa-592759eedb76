import { SearchType } from '@tutum/hermes/bff/legacy/catalog_sdkt_common';
import { SdktCatalog } from '@tutum/hermes/bff/catalog_sdkt_common';
import {
  getInsuranceCompany,
  InsuranceCompany,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { Order } from '@tutum/hermes/bff/common';
import { InsuranceInfo } from '@tutum/hermes/bff/legacy/patient_profile_common';

export enum DateType {
  startDate = 'startDate',
  endDate = 'endDate',
}

function parseQueryToCostUnitFormValues(
  query: string | number,
  searchType?: SearchType
): Partial<SdktCatalog> {
  switch (searchType) {
    case SearchType.SearchType_VKNR:
      return {
        vknr: `${query}`,
      };
    case SearchType.SearchType_IkNumbers:
      return {
        iKNumbers: [
          {
            value: Number.isNaN(parseInt(`${query}`, 10))
              ? undefined
              : parseInt(`${query}`, 10),
            validity: {
              fromDate: 0,
              toDate: 0,
            },
            valueString: String(query),
            source: null,
          },
        ],
        vknr: '',
      };
    case SearchType.SearchType_LocationName:
      return {};
    default:
      return {
        name: `${query}`,
        vknr: '',
      };
  }
}

export const handleGetCostUnits = async (insuranceInfos: InsuranceInfo[]) => {
  const listCostUnit = Array.from<InsuranceCompany>({
    length: insuranceInfos.length,
  }).fill(null);
  for (let i = 0; i < insuranceInfos.length; i++) {
    const item = insuranceInfos[i];
    if (!item.insuranceCompanyId) return null;
    const { data } = await getInsuranceCompany({
      type: SearchType.SearchType_IkNumbers,
      value: item.ikNumber.toString(),
      pagination: {
        page: 1,
        pageSize: 1,
        order: Order.ASC,
        sortBy: 'name',
      },
    });
    listCostUnit[i] = data.insuranceCompanies?.[0];
  }
  return listCostUnit;
};

export function convertToMMYYYYString(month: number, year: number) {
  // Ensure that month and year are within valid ranges
  if (month < 1 || month > 12 || year < 0 || !year || !month) {
    return '';
  }

  // Convert month and year to strings and format them
  const monthStr: string = month.toString().padStart(2, '0'); // Zero-padding if necessary (e.g., '2' -> '02')
  const yearStr: string = year.toString();

  // Combine month and year with a period and return the result
  const mmYYYY: string = `${monthStr}.${yearStr}`;
  return mmYYYY;
}

export function convertMMYYYYtoValue(dateString: string) {
  if (!dateString) {
    return null;
  }
  // Split the input string by the dot (.) separator
  const parts = dateString.split('.');

  if (parts.length !== 2) {
    return null;
  }

  // Parse the month and year from the parts and convert them to numbers
  const month = parseInt(parts[0], 10);
  const year = parseInt(parts[1], 10);

  // Check if the parsed values are valid
  if (isNaN(month) || isNaN(year)) {
    return null;
  }

  return { month, year };
}

export default {
  parseQueryToCostUnitFormValues,
};
