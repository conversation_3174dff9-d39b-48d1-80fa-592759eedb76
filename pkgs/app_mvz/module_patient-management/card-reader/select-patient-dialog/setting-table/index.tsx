import React from 'react';
import {
  IDataTableStyles,
  IDataTableColumn,
} from '@tutum/design-system/components/Table';
import moment from 'moment';
import { Radio } from '@tutum/design-system/components/Core';
import { BodyTextM } from '@tutum/design-system/components';
import PatientManagementUtil from '@tutum/infrastructure/utils/form.util';
import { PatientInfo } from '@tutum/hermes/bff/patient_profile_common';
import { MatchedPatientProfile } from '@tutum/hermes/bff/app_mvz_patient_profile';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { COLOR } from '@tutum/design-system/themes/styles';

const parsePatientInfoName = (patientInfo: PatientInfo) => {
  return PatientManagementUtil.getFullName(
    patientInfo?.personalInfo?.title,
    patientInfo?.personalInfo?.intendWord,
    patientInfo?.personalInfo?.lastName,
    patientInfo?.personalInfo?.firstName
  );
};

const parseDOB = (dOB: number) => {
  return dOB ? moment(dOB).format(DATE_FORMAT) : '';
};

const conditionalCellStyles = (
  isCompareTable: boolean,
  selectedPatient: MatchedPatientProfile
) => {
  return [
    {
      when: (rowData: PatientInfo & MatchedPatientProfile) =>
        isCompareTable &&
        rowData?.originalPatientId === selectedPatient?.originalPatientId,
      style: {
        background: COLOR.INFO_SECONDARY_PRESSED,
      },
    },
  ];
};

export const genColumns = (
  originalpatientInfo?: PatientInfo,
  isCompareTable?: boolean,
  selectedPatient?: MatchedPatientProfile,
  setSelectedPatient?: (patient: MatchedPatientProfile) => void
): IDataTableColumn<PatientInfo & MatchedPatientProfile>[] => [
    {
      name: '',
      width: '40px',
      cell: (row: PatientInfo & MatchedPatientProfile) =>
        !!isCompareTable ? (
          <Radio
            checked={
              row?.originalPatientId === selectedPatient?.originalPatientId
            }
            onClick={() => setSelectedPatient?.(row)}
            style={{ margin: 0 }}
            readOnly
          />
        ) : null,
      conditionalCellStyles: conditionalCellStyles(
        !!isCompareTable,
        selectedPatient!
      ),
    },
    {
      name: 'Insurance number',
      maxWidth: '210px',
      cell: (row: PatientInfo & MatchedPatientProfile) => {
        const insuranceInfos = isCompareTable
          ? row?.originalPatientInfo?.insuranceInfos
          : row?.insuranceInfos;
        return (
          <BodyTextM fontWeight={isCompareTable ? 600 : 400}>
            {insuranceInfos
              ?.map((insurance) => insurance.insuranceNumber)
              ?.join(', ') || ''}
          </BodyTextM>
        );
      },
      conditionalCellStyles: conditionalCellStyles(
        !!isCompareTable,
        selectedPatient!
      ),
    },
    {
      name: 'Patient name',
      cell: (row: PatientInfo & MatchedPatientProfile) => (
        <BodyTextM
          color={
            isCompareTable &&
              parsePatientInfoName(row?.originalPatientInfo) !==
              parsePatientInfoName(originalpatientInfo!)
              ? COLOR.TEXT_WARNING
              : 'inherit'
          }
        >
          {parsePatientInfoName(isCompareTable ? row?.originalPatientInfo : row)}
        </BodyTextM>
      ),
      conditionalCellStyles: conditionalCellStyles(
        !!isCompareTable,
        selectedPatient!
      ),
    },
    {
      name: 'Birthdate',
      maxWidth: '174px',
      cell: (row: PatientInfo & MatchedPatientProfile) => (
        <BodyTextM
          color={
            isCompareTable &&
              parseDOB(row?.originalPatientInfo?.personalInfo?.dOB) !==
              parseDOB(originalpatientInfo?.personalInfo?.dOB!)
              ? COLOR.TEXT_WARNING
              : 'inherit'
          }
        >
          {parseDOB(
            isCompareTable
              ? row?.originalPatientInfo?.personalInfo?.dOB
              : row?.personalInfo?.dOB
          )}
        </BodyTextM>
      ),
      conditionalCellStyles: conditionalCellStyles(
        !!isCompareTable,
        selectedPatient!
      ),
    },
  ];

export const customStyles: IDataTableStyles = {
  header: {
    style: {
      paddingLeft: 0,
    },
  },
  headRow: {
    style: {
      paddingLeft: 0,
      textTransform: 'uppercase',
      border: 'none',
    },
  },
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
    },
  },
  rows: {
    style: {
      paddingLeft: 0,
      minHeight: '40px',
    },
  },
  cells: {
    style: {
      padding: '8px',
    },
  },
};
