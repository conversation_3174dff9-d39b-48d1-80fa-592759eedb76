import React from 'react';
import { IPatientProfile } from '../../types/profile.type';
import { IContractDoctors } from '../../types/contract.type';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { EncounterCase } from '@tutum/hermes/bff/service_domains_patient_file';
import {
  GetPatientParticipationResponse,
  PatientParticipation,
} from '@tutum/hermes/bff/service_domains_patient_participation';
import { PatientMedicalData } from '@tutum/hermes/bff/patient_profile_common';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import type { AtcDiagnoseCode } from '@tutum/hermes/bff/legacy/app_mvz_form';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { PznAtcForHighPrescription } from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';

export interface ISelectedContractDoctor {
  doctorId?: string;
  bsnrId?: string;
  contractId?: string;
  chargeSystemId?: string;
  availableDoctor: IEmployeeProfile[];
  encounterCase?: EncounterCase;
  moduleChargeSystemId?: string;
  isEnrollment?: boolean;
}

export interface IPatientManagement {
  patientId?: {
    value: string;
  };
  patient?: IPatientProfile;
  loadingPatient: boolean | null;
  ikNumber?: number;
  availableHzvContracts: IContractDoctors[];
  availableFavContracts: IContractDoctors[];
  selectedContractDoctor: ISelectedContractDoctor;
  readyToUsePatientParticipationData: boolean;
  getPatientParticipationResponse?: GetPatientParticipationResponse;
  activeParticipations: PatientParticipation[];
  isShowHzvButton: boolean;
  indicatorActiveIngredients: AtcDiagnoseCode[];
  showHintVSST785: boolean;
  pznAtcForHighPrescriptions: PznAtcForHighPrescription[];
}

export interface IPatientManagementContext {
  patientManagement: IPatientManagement;
  setPatient: (patient?: IPatientProfile) => void;
  setPatientId: (patientId: string) => void;
  setSelectedContractDoctor: (data: ISelectedContractDoctor) => any;
  setMedicalData: (medicalData: PatientMedicalData) => void;
  setMedicalDataUpdatedAt: (medicalDataUpdatedAt: number) => void;
  reloadPatient: () => void;
  refetchPatientProfile: () => Promise<any>;
  setGetPatientParticipationResponse: (
    res: GetPatientParticipationResponse
  ) => void;
  reloadSelectedContractDoctor: (
    patientid: string,
    checkDate: Date,
    currentSchein: ScheinItem,
    doctorList: IEmployeeProfile[]
  ) => Promise<void>;
  setShowHintVSST785: (v: boolean) => void;
  getActiveParticipation: () => Nullable<PatientParticipation>;
  setPznAtcForHighPrescriptions: (value: PznAtcForHighPrescription[]) => void;
}

export const defaultContext: IPatientManagementContext = {
  patientManagement: {
    patient: undefined,
    patientId: undefined,
    ikNumber: undefined,
    loadingPatient: false,
    availableHzvContracts: [],
    availableFavContracts: [],
    selectedContractDoctor: {
      bsnrId: undefined,
      doctorId: undefined,
      contractId: undefined,
      chargeSystemId: undefined,
      availableDoctor: [],
      encounterCase: undefined,
    },
    readyToUsePatientParticipationData: false,
    getPatientParticipationResponse: undefined,
    activeParticipations: [],
    isShowHzvButton: false,
    indicatorActiveIngredients: [],
    showHintVSST785: false,
    pznAtcForHighPrescriptions: [],
  },
  setPatient: () => { },
  setPatientId: () => {
    return;
  },
  setSelectedContractDoctor: () => {
    return;
  },
  setMedicalData: () => {
    return;
  },
  setMedicalDataUpdatedAt: () => {
    return;
  },
  reloadPatient: () => {
    return;
  },
  refetchPatientProfile: async () => { },
  setGetPatientParticipationResponse: () => { },
  reloadSelectedContractDoctor: async () => { },
  setShowHintVSST785: (v: boolean) => { },
  getActiveParticipation: () => {
    return null;
  },
  setPznAtcForHighPrescriptions: (v: PznAtcForHighPrescription[]) => { },
};

export default React.createContext<IPatientManagementContext>(defaultContext);
