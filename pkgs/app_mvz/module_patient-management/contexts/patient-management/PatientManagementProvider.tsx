import {
  ContractType,
  PatientParticipationStatus,
} from '@tutum/hermes/bff/common';
import { getContractsDoctors } from '@tutum/hermes/bff/legacy/app_mvz_enrollment';
import { getPatientParticipation } from '@tutum/hermes/bff/legacy/app_mvz_patient_participation';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { memo, useContext, useEffect, useMemo, useState } from 'react';
import { IContractDoctors } from '../../types/contract.type';
import { IPatientProfile } from '../../types/profile.type';
import PatientManagementContext, {
  IPatientManagement,
  ISelectedContractDoctor,
} from './PatientManagementContext.type';
import { unique } from '@tutum/design-system/infrastructure/utils';
import {
  getListPznAtcForHighPrescription,
  PznAtcForHighPrescription,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { getContractDoctorGroup } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';
import { useQueryGetPatientProfileById } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { PatientMedicalData } from '@tutum/hermes/bff/patient_profile_common';
import { EncounterCase, ScheinItem } from '@tutum/hermes/bff/schein_common';
import { DoctorParticipateStatus } from '@tutum/hermes/bff/service_domains_doctor_participate';
import { GetPatientParticipationResponse } from '@tutum/hermes/bff/service_domains_patient_participation';
import DatetimeUtil, { now } from '@tutum/infrastructure/utils/datetime.util';
import { checkIsSvSchein } from '@tutum/mvz/_utils/scheinFormat';
import type { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { catalogOverviewActions } from '../../patient-file/CatalogsOverview.store';
import {
  patientFileActions,
  patientFileStore,
} from '../../patient-file/PatientFile.store';
import { useRouter } from 'next/router';
import { ROUTING } from '@tutum/mvz/types/route.type';

export default GlobalContext.withContext(
  memo(({ children }: any) => {
    const [patientManagement, setPatientManagement] =
      useState<IPatientManagement>({
        patientId: undefined,
        patient: undefined,
        loadingPatient: null,
        ikNumber: undefined,
        availableHzvContracts: [],
        availableFavContracts: [],
        selectedContractDoctor: {
          bsnrId: undefined,
          doctorId: undefined,
          contractId: undefined,
          chargeSystemId: undefined,
          availableDoctor: [],
          encounterCase: undefined,
        },
        readyToUsePatientParticipationData: false,
        getPatientParticipationResponse: undefined,
        activeParticipations: [],
        isShowHzvButton: false,
        indicatorActiveIngredients: [],
        showHintVSST785: false,
        pznAtcForHighPrescriptions: [],
      });

    const globalContext = useContext(GlobalContext.instance);
    const router = useRouter();
    const currentLoggedInUser = globalContext.useGetLoggedInUserProfile();
    const {
      data: patientProfile,
      isSuccess,
      error,
      refetch: refetchPatientProfile,
    } = useQueryGetPatientProfileById(
      {
        id: patientManagement?.patientId?.value!,
      },
      {
        enabled: !!patientManagement?.patientId?.value,
        throwOnError: false,
      }
    );

    const getActiveParticipation = () => {
      return patientManagement.activeParticipations.find(
        (p) =>
          p.status === PatientParticipationStatus.PatientParticipation_Active
      );
    };

    const activeParticipate = getActiveParticipation();
    useEffect(() => {
      if (activeParticipate?.contractId) {
        getListPznAtcForHighPrescription({
          contractId: activeParticipate.contractId,
        }).then((resp) => {
          setPznAtcForHighPrescriptions(resp.data?.data ?? []);
        });
      }
    }, [activeParticipate?.contractId]);

    const setSelectedContractDoctor = (data: ISelectedContractDoctor) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        selectedContractDoctor: {
          ...patientManagement.selectedContractDoctor,
          ...data,
        },
      }));
    };

    const setPznAtcForHighPrescriptions = (v: PznAtcForHighPrescription[]) => {
      setPatientManagement((prev) => ({
        ...prev,
        pznAtcForHighPrescriptions: v,
      }));
    };

    const reloadSelectedContractDoctor = async (
      patientid: string,
      checkDate: Date,
      currentSchein: ScheinItem,
      doctorList: IEmployeeProfile[]
    ) => {
      if (!currentSchein || !checkIsSvSchein(currentSchein)) {
        const defaultSelectedDoctorId =
          (doctorList?.length > 0 &&
            doctorList.find((doctor) => doctor.id === currentLoggedInUser.id)
              ?.id) ||
          doctorList[0]?.id ||
          undefined;
        const bsnrId = globalContext.getBsnrIdByDoctorId(
          defaultSelectedDoctorId
        );
        const selectedContractDoctor: ISelectedContractDoctor = {
          bsnrId,
          doctorId: defaultSelectedDoctorId,
          contractId: undefined,
          chargeSystemId: undefined,
          availableDoctor: doctorList,
          encounterCase: EncounterCase.AB,
        };
        setSelectedContractDoctor(selectedContractDoctor);
        return;
      }

      const currentContractId = currentSchein?.hzvContractId;
      const {
        data: { participations },
      } = await getPatientParticipation({
        patientId: patientid,
        checkDate: checkDate.getTime(),
      });
      const contractIds = [...new Set(participations.map((p) => p.contractId))];
      const {
        data: { doctorParticipateContracts },
      } = await getContractDoctorGroup({
        contractIds: contractIds,
        doctorIds: [],
        statuses: [DoctorParticipateStatus.Active],
        time: now(),
      });
      if (currentContractId) {
        const doctorParticipateContract = doctorParticipateContracts?.find(
          (c) => {
            return c.contractID === currentContractId;
          }
        );
        const doctors = unique(
          (doctorParticipateContract?.doctors || []).map((doc) => doc.doctorId!)
        ).map((docId) => {
          return doctorList.find((doctor) => doctor.id === docId)!;
        });

        const participate = participations.find(
          (p) => p.contractId == currentContractId
        );
        if (participate) {
          catalogOverviewActions
            .getContractMeta(now(), currentContractId)
            .then((contractMetadata) => {
              const activeContracts = patientManagement.activeParticipations;
              let contract;
              if (activeContracts.length != 0) {
                // check based on chein
                contract = activeContracts?.find(
                  (con) => con.contractId === currentContractId
                );
                // edge of edge case Hzv/FaV active and not active
                if (!contract) {
                  contract = activeContracts[0];
                }
              }

              const doctorId = contract?.doctorId || participate?.doctorId;
              const availableChargeSystems: string[] = [];
              const availableModuleChargeSystems: string[] = [];
              doctorParticipateContract?.doctors
                ?.filter((doc) => doc.doctorId === doctorId)
                .forEach((doc) => {
                  if (
                    contractMetadata.chargeSystems?.indexOf(
                      doc.chargeSystemId
                    ) !== -1
                  ) {
                    availableChargeSystems.push(doc.chargeSystemId);
                  }
                  if (
                    contractMetadata.moduleChargeSystems?.indexOf(
                      doc.chargeSystemId
                    ) !== -1
                  ) {
                    availableModuleChargeSystems.push(doc.chargeSystemId);
                  }
                });
              setSelectedContractDoctor({
                doctorId,
                bsnrId: globalContext.getBsnrIdByDoctorId(doctorId),
                contractId: participate.contractId,
                chargeSystemId: participate.chargeSystemId,
                moduleChargeSystemId: availableModuleChargeSystems[0],
                availableDoctor: doctors,
                encounterCase: EncounterCase.AB,
              });
            });
        }
      } else {
        const participate = participations.find(
          (p) =>
            p.status == PatientParticipationStatus.PatientParticipation_Active
        );
        if (participate) {
          const contractId = participate.contractId;
          const doctors = unique(
            doctorParticipateContracts?.reduce((curr, c) => {
              const contractDoctors = [
                ...c.doctors.map((d) => {
                  return globalContext.getDoctorById(d.doctorId)!;
                }),
              ];
              return [...curr, ...contractDoctors];
            }, [] as IEmployeeProfile[]) ?? []
          );
          const defaultSelectedDoctorId =
            (doctors?.length > 0 &&
              doctors.find((doctor) => doctor.id === currentLoggedInUser.id)
                ?.id) ||
            doctors?.[0].id ||
            undefined;
          const bsnrId = globalContext.getBsnrIdByDoctorId(
            defaultSelectedDoctorId
          );
          setSelectedContractDoctor({
            doctorId: defaultSelectedDoctorId,
            bsnrId: bsnrId,
            contractId: contractId,
            chargeSystemId: participate.chargeSystemId,
            availableDoctor: doctors,
            encounterCase: EncounterCase.AB,
          });
        }
      }
    };

    const setPatient = (patient?: IPatientProfile) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        patient: patient,
        ikNumber: patientFileStore.activeInsurance?.ikNumber,
        loadingPatient: false,
      }));
    };

    const setMedicalData = (medicalData: PatientMedicalData) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        patient: {
          ...patientManagement.patient!,
          patientMedicalData: medicalData,
        },
      }));
    };

    const setMedicalDataUpdatedAt = (medicalDataUpdatedAt: number) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        patient: {
          ...patientManagement.patient!,
          medicalDataUpdatedAt,
        },
      }));
    };

    const setReadyToUsePatientParticipationData = (ready: boolean) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        readyToUsePatientParticipationData: ready,
      }));
    };

    const setPatientId = (patientId: string) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        patient: undefined,
        patientId: {
          value: patientId,
        },
        loadingPatient: patientId ? true : null,
      }));
    };

    const setAvailableHzvContracts = (contracts: IContractDoctors[]) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        availableHzvContracts: [...contracts],
      }));
    };

    const setAvailableFavContracts = (contracts: IContractDoctors[]) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        availableFavContracts: [...contracts],
      }));
    };

    const setIsShowHzvButton = (isShow: boolean) => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        isShowHzvButton: isShow,
      }));
    };

    const setGetPatientParticipationResponse = (
      ppRes: GetPatientParticipationResponse
    ) => {
      const activeParticipations = ppRes.participations.filter(
        (b) =>
          b.status === PatientParticipationStatus.PatientParticipation_Active
      );
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        getPatientParticipationResponse: ppRes,
        activeParticipations: activeParticipations,
        loadingPatient: false,
      }));
      patientFileActions.patient.setActiveParticipations(activeParticipations);
    };

    const reloadPatient = async () => {
      try {
        if (patientManagement.patientId) {
          if (isSuccess) {
            const { data: patientParticipationRes } =
              await getPatientParticipation({
                checkDate: DatetimeUtil.now(),
                patientId: patientManagement.patient?.id!,
              });
            // filter active participate contracts
            const activeParticipations =
              patientParticipationRes.participations.filter(
                (b) =>
                  b.status ===
                  PatientParticipationStatus.PatientParticipation_Active
              );
            setPatientManagement((patientManagement) => ({
              ...patientManagement,
              patient: patientProfile,
              ikNumber: patientFileStore.activeInsurance?.ikNumber,
              loadingPatient: false,
              getPatientParticipationResponse: patientParticipationRes,
              activeParticipations: activeParticipations,
              patientId: {
                value: patientProfile.id,
              },
            }));
            setPatient(patientProfile);
            patientFileActions.patient.setCurrent(patientProfile);
            patientFileActions.patient.setActiveParticipations(
              activeParticipations
            );
          }
        }
      } catch (err) {
        setGetPatientParticipationResponse({
          participations: [],
        });
        throw err;
      }
    };

    const setShowHintVSST785 = (v: boolean) => {
      setPatientManagement((prev) => ({
        ...prev,
        showHintVSST785: v,
      }));
    };

    const value = useMemo(
      () => ({
        patientManagement,
        setPatient,
        setPatientId,
        setSelectedContractDoctor,
        setMedicalData,
        setMedicalDataUpdatedAt,
        reloadPatient,
        refetchPatientProfile,
        setGetPatientParticipationResponse,
        reloadSelectedContractDoctor,
        setShowHintVSST785,
        getActiveParticipation,
        setPznAtcForHighPrescriptions,
      }),
      [patientManagement]
    );

    useEffect(() => {
      if (error) {
        console.error(error);
        router.replace(ROUTING.PAGE_404);
        return;
      }
      if (!patientProfile) {
        return;
      }
      setPatient(patientProfile);
      patientFileActions.patient.setCurrent(patientProfile);
    }, [patientProfile, error]);

    useEffect(() => {
      setPatientManagement((patientManagement) => ({
        ...patientManagement,
        availableFavContracts: [],
        availableHzvContracts: [],
        activeParticipations: [],
        isShowHzvButton: false,
      }));

      if (!patientManagement?.patient?.id) {
        return;
      }
      const ikNumber = patientFileStore.activeInsurance?.ikNumber;
      if (!ikNumber) {
        return;
      }
      getContractsDoctors({
        ikNumber: ikNumber,
        contractType: undefined,
      }).then(({ data: res }) => {
        if (res) {
          const hzvContracts = res.contracts.filter(
            (c) => c.contractType === ContractType.ContractType_HouseDoctorCare
          );
          const favContracts = res.contracts.filter(
            (c) => c.contractType === ContractType.ContractType_SpecialistCare
          );
          const isShowHzvButton = favContracts.some((c) => c.isShowHzvButton);
          setAvailableHzvContracts(hzvContracts);
          setAvailableFavContracts(favContracts);
          setIsShowHzvButton(isShowHzvButton);
        }
      });
      getPatientParticipation({
        checkDate: DatetimeUtil.now(),
        patientId: patientManagement.patient.id,
      }).then(async ({ data: resp }) => {
        setGetPatientParticipationResponse(resp);
        setReadyToUsePatientParticipationData(true);
      });
    }, [
      patientManagement?.patient?.id,
      patientFileStore.activeInsurance?.ikNumber,
      currentLoggedInUser,
    ]);

    useEffect(() => {
      if (
        !patientManagement.selectedContractDoctor.doctorId ||
        !patientManagement.getPatientParticipationResponse ||
        !patientManagement.readyToUsePatientParticipationData
      )
        return;
      setReadyToUsePatientParticipationData(false);
    }, [
      patientManagement.getPatientParticipationResponse,
      patientManagement.selectedContractDoctor,
    ]);

    return (
      <PatientManagementContext.Provider value={value}>
        {children}
      </PatientManagementContext.Provider>
    );
  })
);
