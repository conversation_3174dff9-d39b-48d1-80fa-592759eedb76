import { useState, useEffect } from 'react';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';

export const checkContractSupport = async (
  functionIds: string[],
  contracts: (string | undefined)[]
) => {
  if (!functionIds.length || !contracts.length) {
    return false;
  }

  const contractChecks = await Promise.all(
    contracts.map((contract) =>
      webWorkerServices.doesContractSupportFunctions(
        functionIds,
        contract || ''
      )
    )
  );
  
  return contractChecks.some((result) => result);
}

const useCheckContractSupport = (
  functionIds: string[],
  contracts: (string | undefined)[]
) => {
  const [isSupport, setIsSupport] = useState<boolean>(false);

  const checkContractSupportFunction = async (
    functionIds: string[] = [],
    contracts: (string | undefined)[] = []
  ) => {
    if (!functionIds.length || !contracts.length) {
      setIsSupport(false);
      return;
    }

    const contractChecks = await Promise.all(
      contracts.map((contract) =>
        webWorkerServices.doesContractSupportFunctions(
          functionIds,
          contract || ''
        )
      )
    );
    const isContractSupport = contractChecks.some((result) => result);

    setIsSupport(isContractSupport);
  };

  useEffect(() => {
    checkContractSupportFunction(functionIds, contracts);
  }, [functionIds, contracts]);

  return { isContractSupport: isSupport };
};

export default useCheckContractSupport;
