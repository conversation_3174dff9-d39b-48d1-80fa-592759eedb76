import { Dialog } from '@blueprintjs/core';
import {
  alertSuccessfully,
  alertWarning,
  BodyTextL,
  Box,
  Flex,
  H1,
  Svg,
} from '@tutum/design-system/components';
import {
  Alignment,
  Button,
  Classes,
  Divider,
  Icon,
  Switch,
} from '@tutum/design-system/components/Core';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { LabImportOverview } from '@tutum/hermes/bff/app_mvz_lab';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import DateRangeSingleInput from '@tutum/mvz/components/date-range-single-input';
import type LabImportI18n from '@tutum/mvz/locales/en/LabImport.json';
import {
  labImportActions,
  useLabImportStore,
} from '@tutum/mvz/module_lab/lab-import/LabImport.store';
import ImportResultsDialog from './import-results-dialog/ImportResultsDialog.styled';
import { IMvzTheme } from '@tutum/mvz/theme';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import MomentLocaleUtils from 'react-day-picker/moment';
import {
  HOUR_MIN_TIME_FORMAT,
  DATE_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import PatientManagementUtil from '@tutum/infrastructure/utils/form.util';

const checkCircle = '/images/check-circle-2.svg';

export interface ILabImportProps {
  className?: string;
  theme?: IMvzTheme;
}

const LabImportTableColumn: IDataTableColumn<LabImportOverview>[] = [
  {
    name: 'LAB ORDER ID',
    selector: (row) => row.labOrderNumber,
    minWidth: '200px',
    sortable: false,
  },
  {
    name: 'PATIENT',
    minWidth: '200px',
    sortable: true,
    cell: (row: LabImportOverview) => {
      const patientName = PatientManagementUtil.getFullName(
        row.patientProfile.patientInfo.personalInfo.title || '',
        row.patientProfile.patientInfo.personalInfo.intendWord || '',
        row.patientProfile.patientInfo.personalInfo.lastName || '',
        row.patientProfile.patientInfo.personalInfo.firstName || ''
      );

      return (
        <div>
          <a
            className="table-patient-name"
            href={`/patients/${row.patientProfile?.id}`}
            target="_blank"
            rel="noreferrer"
            title={patientName}
          >
            {patientName}
          </a>
          <div>
            {moment(row.patientProfile?.dateOfBirth).format(DATE_FORMAT)}
          </div>
        </div>
      );
    },
  },
  {
    name: 'DOCTOR',
    minWidth: '200px',
    sortable: true,
    cell: (row: LabImportOverview) => {
      return (
        <>
          <div className="doctor-short-name">
            {row.doctor?.firstName[0]}
            {row.doctor?.lastName[0]}
          </div>
          <div className="doctor-info">
            <div>
              {row.doctor.title} {row.doctor.firstName} {row.doctor.lastName}
            </div>
            <div>{/*{row.doctor}*/}</div>
          </div>
        </>
      );
    },
  },
  {
    name: 'CREATED DATE',
    minWidth: '200px',
    sortable: true,
    cell: (row: LabImportOverview) => {
      const date = `${moment(row.createdDate).format(DATE_FORMAT)} at ${moment(
        row.createdDate
      ).format(HOUR_MIN_TIME_FORMAT)}`;
      return <>{date}</>;
    },
  },
  {
    name: 'IMPORTED DATE',
    minWidth: '200px',
    sortable: true,
    cell: (row: LabImportOverview) => {
      const date = row.importDate
        ? `${moment(row.importDate).format(DATE_FORMAT)} at ${moment(
          row.importDate
        ).format(HOUR_MIN_TIME_FORMAT)}`
        : '--';
      return <>{date}</>;
    },
  },
  {
    name: 'RECALL MESSAGE',
    minWidth: '200px',
    sortable: true,
    cell: (row: LabImportOverview) => {
      const message = row.totalRecallMessage
        ? `${row.totalRecallMessage} recall messages attached`
        : '';
      if (row.totalRecallMessage) {
        return (
          <div className="recall-message">
            <Icon icon="warning-sign" /> {message}
          </div>
        );
      }
    },
  },
  {
    name: 'STATUS',
    minWidth: '300px',
    sortable: false,
    cell: (row: LabImportOverview) => {
      return (
        <div className="import-status">
          <p className={`${row.importStatus.toLowerCase()}`}>
            {row.importStatus}
          </p>
        </div>
      );
    },
  },
];

const LabImport = React.memo(
  ({
    t,
    className,
  }: ILabImportProps & II18nFixedNamespace<keyof typeof LabImportI18n>) => {
    const [isOpen, setIsOpen] = useState(false);

    const store = useLabImportStore();
    const inputFile = useRef<HTMLInputElement | null>(null);

    const handleOpenDialog = () => {
      setIsOpen(true);
    };

    const handleCloseDialog = () => {
      setIsOpen(false);
    };

    const handleShowLabRecall = (event) => {
      labImportActions.toggleOnlyShowRecall(event.target.checked);
    };

    const handleChangeDateRange = (from, to) => {
      labImportActions.changeDateRange(from, to);
    };

    const handleGenerateColumns = () => {
      const columns = LabImportTableColumn.map((col) => ({
        ...col,
        name: t(`TableColumns.${col.selector}` as any),
      }));
      return columns;
    };

    useEffect(() => {
      labImportActions.getLabImportOverview();
    });

    const showToastSuccess = () => {
      alertSuccessfully('Import successfully');
    };

    const showToastError = () => {
      alertWarning('Import error');
    };

    const handleInputUpload = (event) => {
      labImportActions.processImportLDKFile(
        event,
        showToastSuccess,
        showToastError,
        handleOpenDialog
      );
    };

    const LabResultsTable = () => {
      return (
        <Table
          title={false}
          columns={handleGenerateColumns()}
          data={cloneDeep(store.labImportOverviewsData) || []}
          customStyles={{
            headRow: {
              style: {
                textTransform: 'uppercase',
              },
            },
            pagination: {
              style: {
                justifyContent: 'flex-start',
              },
            },
          }}
          noHeader={true}
          selectableRowsNoSelectAll={true}
          highlightOnHover={true}
          selectableRowsHighlight={true}
          responsive={false}
          paginationRowsPerPageOptions={[10, 20, 30]}
          paginationTotalRows={store.totalRecords}
          onChangePage={labImportActions.handlePageChange}
          pagination
          paginationServer
          paginationDefaultPage={store.page}
          paginationPerPage={store.rowsPerPage}
          paginationResetDefaultPage
          onChangeRowsPerPage={labImportActions.handleChangeRowsPerPage}
        />
      );
    };

    const handleOpenFileInput = () => {
      inputFile.current?.click();
    };

    const LabImportLDKAlert = () => {
      return (
        <Dialog
          title={
            <BodyTextL
              fontFamily="Work Sans"
              lineHeight="28px"
              fontSize="15px"
              fontWeight="Bold"
            >
              LDK Test module responded mistakes
            </BodyTextL>
          }
          isOpen={store.openLDKAlert}
          onClose={() => labImportActions.setOpenLDKAlert(false)}
          canOutsideClickClose={false}
        >
          <div style={{ padding: '18px 25px 18px 25px' }}>
            The content validation via LDK Test module responded mistakes, do
            you still want to continue the import?
          </div>

          <Flex justify="space-between" className={Classes.DIALOG_FOOTER}>
            <Box w="45%">
              <Button
                fill
                onClick={() => labImportActions.setOpenLDKAlert(false)}
              >
                No
              </Button>
            </Box>
            <Box w="45%">
              <Button
                fill
                intent="primary"
                onClick={() => {
                  labImportActions.setOpenLDKAlert(false);
                  labImportActions.importLab(
                    showToastSuccess,
                    showToastError,
                    false,
                    handleOpenDialog
                  );
                }}
              >
                Yes
              </Button>
            </Box>
          </Flex>
        </Dialog>
      );
    };

    return (
      <div className={className} style={{ width: '100%' }}>
        <Flex justify="space-between" className="sl-header">
          <H1>{t('title')}</H1>
          <Button
            large
            alignText={Alignment.LEFT}
            text={t('import')}
            intent="primary"
            onClick={handleOpenFileInput}
            style={{ paddingTop: '10px', paddingBottom: '10px' }}
          />
        </Flex>

        <input
          ref={inputFile}
          style={{ display: 'none' }}
          type="file"
          onChange={handleInputUpload}
        // accept=".ldt"
        />

        <Divider className={`sl-divider ${Classes.FILL}`} />

        <Flex justify="flex-end" align="baseline" className="filter-wrapper">
          <div>
            <Switch
              className="sl-switch"
              onChange={handleShowLabRecall}
              label={t('onlyShowLabOrdersWithRecalls')}
            />
          </div>

          <div className="date-range">
            <DateRangeSingleInput
              onChange={(startDate, endDate) => {
                handleChangeDateRange(startDate, endDate);
              }}
              locale={'de'}
              localeUtils={MomentLocaleUtils}
              isCloseCalendarWhenChosen
              fill
            />
          </div>
        </Flex>

        <div className="import-lab-table">
          <LabResultsTable />
        </div>

        <ImportResultsDialog
          isOpen={isOpen}
          handleCloseDialog={handleCloseDialog}
        />

        <LabImportLDKAlert />
      </div>
    );
  }
);

export default I18n.withTranslation(LabImport, {
  namespace: 'LabImport',
});
