import React, { memo, useEffect, useState } from 'react';
import { IMvzTheme } from '@tutum/mvz/theme';
import type LabImportI18n from '@tutum/mvz/locales/en/LabImport.json';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { UpdateType } from '@tutum/hermes/bff/app_mvz_lab';
import {
  Dialog,
  Button,
  Spinner,
  Intent,
  Callout,
} from '@tutum/design-system/components/Core';
import { genColumns, customStyles } from './setting-table';
import Table from '@tutum/design-system/components/Table';
import {
  alertSuccessfully,
  alertWarning,
  BodyTextM,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import {
  useLabImportStore,
  labImportActions,
} from '@tutum/mvz/module_lab/lab-import/LabImport.store';
import LdtContentDialog from './ldt-content-dialog/LdtContentDialog.styled';

export interface IImportResultsDialogProps {
  className?: string;
  theme?: IMvzTheme;
  isOpen: boolean;
  handleCloseDialog: () => void;
}

const alertTriangleIcon = '/images/alert-triangle.svg';
const checkCircle = '/images/check-circle-2.svg';

const ImportResultsDialog = ({
  t,
  className,
  isOpen,
  handleCloseDialog,
}: IImportResultsDialogProps &
  II18nFixedNamespace<keyof typeof LabImportI18n.ImportResultsDialog>) => {
  const [openLdtDialog, setOpenLdtDialog] = useState(false);
  const store = useLabImportStore();

  const [listConflicts, setListConflicts] = useState<{
    labOrderId: string;
    labOrderNumber: string;
    labLDTFileContentId: string;
    labImportPatient: any;
  }[]>([]);

  useEffect(() => {
    const mapImportConflicts = store?.importConflicts?.map((item) => ({
      labOrderId: item?.labOrderId,
      labOrderNumber: item?.labOrderNumber,
      labLDTFileContentId: item?.labLDTFileContentId,
      labImportPatient: null,
    }));
    setListConflicts(mapImportConflicts || []);
  }, [store?.importConflicts]);

  const handleSetListChanged = (rowIndex, patient) => {
    const listChangedTemp = [...listConflicts];
    listChangedTemp[rowIndex].labImportPatient = patient
      ? {
        ...patient,
        dOB: patient?.dateOfBirth,
      }
      : null;
    setListConflicts(listChangedTemp);
  };

  const showToastSuccess = (updateType: UpdateType) => {
    alertSuccessfully(`${updateType} successfully`);
  };

  const showToastError = (updateType: UpdateType) => {
    alertWarning(`${updateType} error`);
  };

  const handleAction = (updateType: UpdateType) => {
    const showToastSuccessCallback = () => showToastSuccess(updateType);
    const showToastErrorCallback = () => showToastError(updateType);
    labImportActions.updateImportFile(
      {
        updateImportFiles: listConflicts?.map((item) => ({
          labOrderId: item?.labOrderId,
          patientId: item?.labImportPatient?.id!,
        })),
        updateType,
      },
      { handleCloseDialog, showToastSuccessCallback, showToastErrorCallback }
    );
  };

  const handleOpenLdtDialog = () => {
    setOpenLdtDialog(true);
  };

  const handleCloseLdtDialog = () => {
    setOpenLdtDialog(false);
  };

  const handleViewLdtContent = (
    labOrderId: string,
    labLDTFileContentId: string,
    labOrderNumber: string
  ) => {
    labImportActions.getLDTContent(
      {
        labOrderId,
        labLDTFileContentId,
      },
      labOrderNumber,
      { handleOpenLdtDialog }
    );
  };

  const checkEmptyPatient = () => {
    return !!listConflicts.find((item) => item.labImportPatient === null);
  };

  return (
    <>
      <Dialog
        className={`bp5-dialog-fullscreen ${className}`}
        title="Manual lab import for patients with unmatching data"
        isOpen={isOpen}
        isCloseButtonShown
        onClose={handleCloseDialog}
        canOutsideClickClose={false}
      >
        <Flex column p="16px 24px">
          <BodyTextM>{t('note')}:</BodyTextM>
          <BodyTextM>• {t('note_1')} </BodyTextM>
          <BodyTextM style={{ marginBottom: 16 }}>• {t('note_2')}</BodyTextM>
          {checkEmptyPatient() && (
            <Callout
              className="sl-error-message"
              intent={Intent.DANGER}
              icon={<Svg className="sl-callout-icon" src={alertTriangleIcon} />}
            >
              {t('error_msg_empty_patient')}
            </Callout>
          )}
        </Flex>
        <Table
          className="sl-table"
          columns={genColumns(
            listConflicts,
            handleSetListChanged,
            handleViewLdtContent,
            store?.importConflicts
          )}
          data={listConflicts || []}
          customStyles={customStyles}
          noHeader
          fixedHeader
          highlightOnHover
        />
        <Flex
          className="sl-action-bottom"
          w="100%"
          justify="flex-end"
          align="center"
          p="12px 20px"
        >
          <Button
            onClick={() => handleAction(UpdateType.Cancel)}
            loading={store?.isLoadingImport}
          >
            Cancel
          </Button>
          <Button
            intent="primary"
            onClick={() => handleAction(UpdateType.Import)}
            loading={store?.isLoadingImport}
            disabled={checkEmptyPatient()}
          >
            Import
          </Button>
        </Flex>
        {store?.lDTDetail?.isLoading && (
          <Flex
            className="sl-spinner-loading"
            w="100%"
            h="100%"
            justify="center"
            align="center"
          >
            <Spinner intent={Intent.PRIMARY} />
          </Flex>
        )}
      </Dialog>
      <LdtContentDialog
        isOpen={openLdtDialog}
        handleCloseDialog={handleCloseLdtDialog}
      />
    </>
  );
};

export default memo(
  I18n.withTranslation(ImportResultsDialog, {
    namespace: 'LabImport',
    nestedTrans: 'ImportResultsDialog',
  })
);
