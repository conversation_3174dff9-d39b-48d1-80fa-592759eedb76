import React, { memo, useState, useEffect } from 'react';
import { IMvzTheme } from '@tutum/mvz/theme';
import type LabImportI18n from '@tutum/mvz/locales/en/LabImport.json';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { Flex, BodyTextM, Svg } from '@tutum/design-system/components';
import { InputGroup, Popover } from '@tutum/design-system/components/Core';
import PatientSearchType, {
  IPatientSearchResult,
  IParsedQuery,
} from '@tutum/mvz/module_patient-management/patient-search/PatientSearch.type';
import { labImportActions } from '@tutum/mvz/module_lab/lab-import/LabImport.store';
import moment from 'moment';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { COLOR } from '@tutum/design-system/themes/styles';

export interface IAssignPatientProps {
  className?: string;
  theme?: IMvzTheme;
  patient: any;
  setPatient: (patient) => void;
}

const UserIcon = '/images/user-icon.svg';
const RemoveIcon = '/images/remove-circle.svg';

const AssignPatient = ({
  className,
  patient,
  setPatient,
}: IAssignPatientProps & II18nFixedNamespace<keyof typeof LabImportI18n>) => {
  const [value, setValue] = useState('');
  const [open, setOpen] = useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const [searchPatientLists, setSearchPatientLists] = useState([]);

  useEffect(() => {
    if (patient?.labImportPatient) {
      setValue(
        `${patient?.labImportPatient?.lastName} ${patient?.labImportPatient?.firstName}`
      );
    }
  }, [patient]);

  const handleSearchPatient = async (query) => {
    setValue(query);
    let resultItems = [];
    const searchQuery: IParsedQuery = {
      keyword: query.toLowerCase().split(' ').join(', '),
      type: PatientSearchType.SearchType.PatientName,
    };
    resultItems = (await labImportActions.searchPatient(searchQuery)) || [];
    setSearchPatientLists(resultItems);
    setOpen(!!query);
  };

  const handleClosePopover = () => {
    setOpen(false);
  };

  const handleOnClick = (patient) => {
    setPatient(patient);
    handleClosePopover();
  };

  const handleRemove = () => {
    setValue('');
    setPatient(null);
    handleClosePopover();
  };

  const renderContent = () => {
    return (
      <Flex column>
        {searchPatientLists?.length ? (
          searchPatientLists?.map((item: IPatientSearchResult) => (
            <Flex
              key={item?.id}
              column
              p="10px 16px"
              onClick={() => handleOnClick(item)}
              style={{ cursor: 'pointer' }}
            >
              <BodyTextM fontSize={13} color={COLOR.TEXT_PRIMARY_BLACK}>
                {item?.lastName}, {item?.firstName}
              </BodyTextM>
              <BodyTextM fontSize={13} color={COLOR.TEXT_SECONDARY_NAVAL}>
                {item?.patientNumber} |{' '}
                {item?.insuranceNumber ? `${item?.insuranceNumber} | ` : ''}
                {moment(item?.dateOfBirth).format(DATE_FORMAT)}
              </BodyTextM>
            </Flex>
          ))
        ) : (
          <Flex p="10px 16px">No results</Flex>
        )}
      </Flex>
    );
  };

  const handleOnFocus = () => {
    setIsFocus(true);
  };

  const handleOnBlur = () => {
    setIsFocus(false);
  };

  return (
    <Flex className={className}>
      <Popover
        isOpen={open}
        content={renderContent()}
        usePortal
        interactionKind="click"
      >
        <InputGroup
          value={value}
          className={!value ? 'sl-input-error' : ''}
          autoComplete="off"
          placeholder={
            isFocus ? 'Search patient by last name' : 'Assign patient'
          }
          onChange={(e) => handleSearchPatient(e.target.value)}
          onFocus={handleOnFocus}
          onBlur={handleOnBlur}
          leftElement={<Svg className="sl-left-icon" src={UserIcon} />}
          rightElement={
            value ? <Svg onClick={handleRemove} src={RemoveIcon} /> : undefined
          }
        />
      </Popover>
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(AssignPatient, {
    namespace: 'LabImport',
  })
);
