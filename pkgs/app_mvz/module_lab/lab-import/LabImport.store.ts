import {
  getLabImportOverview,
  GetLabImportOverviewRequest,
  ImportConfict,
  ImportDetail,
  importLab,
  ImportRequest,
  LabImportOverview,
  updateImportFile,
  UpdateImportFileRequest,
  getLDTContent,
  GetLDTContentRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_lab';
import { LDTContent } from '@tutum/hermes/bff/lab_common';
import { PatientSearchApi } from '@tutum/infrastructure/resource/PatientSearchResource';
import { useEffect } from 'react';
import { proxy, useSnapshot } from 'valtio';

interface ILabImportStore {
  labImportOverviewsData: LabImportOverview[];
  totalRecords: number;
  totalPage: number;
  onlyShowLabWithRecall: boolean;
  fromDate: number;
  toDate: number;
  isImportConflict: boolean;
  importConflicts: ImportConfict[] | null;
  page: number;
  totalInPage: number;
  rowsPerPage: number;
  openLDKAlert: boolean;
  importDetails: ImportDetail[];
  progressPending: boolean;
  isLoadingImport: boolean;
  lDTDetail: {
    isLoading: boolean;
    labOrderNumber: string;
    lDTContent: LDTContent[];
  };
}

interface IImportLabActions {
  importLab: (importSuccess, importError, isCheckLDK, handleOpenDialog) => void;
  getLabImportOverview: () => void;
  toggleOnlyShowRecall: (checked: boolean) => void;
  changeDateRange: (startDate: Date, endDate: Date) => void;
  handlePageChange: (page: number) => void;
  handleChangeRowsPerPage(currentRowsPerPage: number): void;
  setOpenLDKAlert(isOpen: boolean): void;
  processImportLDKFile(
    event,
    importSuccess,
    importError,
    handleOpenDialog
  ): void;
  searchPatient: (parsedQuery) => Promise<any>;
  updateImportFile: (payload: UpdateImportFileRequest, callback) => void;
  getLDTContent: (
    payload: GetLDTContentRequest,
    labOrderNumber: string,
    callback
  ) => void;
}

const initStore: ILabImportStore = {
  labImportOverviewsData: [],
  totalRecords: 0,
  totalPage: 0,
  onlyShowLabWithRecall: false,
  fromDate: 0,
  toDate: 0,
  isImportConflict: false,
  importConflicts: null,
  page: 1,
  totalInPage: 10,
  rowsPerPage: 10,
  openLDKAlert: false,
  importDetails: new Array<ImportDetail>(),
  progressPending: false,
  isLoadingImport: false,
  lDTDetail: {
    isLoading: false,
    labOrderNumber: null!,
    lDTContent: [],
  },
};

export let labImportStore = proxy<ILabImportStore>(initStore);

const getBase64 = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const baseURL = reader.result;
      resolve(baseURL);
    };
  });
};

export const labImportActions: IImportLabActions = {
  getLabImportOverview(): void {
    const requestBody: GetLabImportOverviewRequest = {
      fromDate: labImportStore.fromDate,
      toDate: labImportStore.toDate,
      onlyShowLabWithRecall: labImportStore.onlyShowLabWithRecall,
      page: labImportStore.page,
      totalInPage: labImportStore.totalInPage,
    };
    getLabImportOverview(requestBody)
      .then(({ data: response }) => {
        labImportStore.labImportOverviewsData = response.labImportOverviews;
        labImportStore.totalRecords = response.totalRecords;
        labImportStore.totalPage = response.totalPage;
      })
      .catch((error) => {
        throw new Error(error);
      });
  },

  processImportLDKFile: async (
    event,
    importSuccess,
    importError,
    handleOpenDialog
  ) => {
    const target = event.target as HTMLInputElement;
    const files = target.files as FileList;
    if (files.length) {
      let importDetails = new Array<ImportDetail>();
      for (const file of Array.from(files)) {
        const base64 = await getBase64(file);
        const result: ImportDetail = {
          fileBase64: String(base64).split('base64,')[1],
          isCheckLDK: true,
          fileName: file.name,
        };
        importDetails = [...importDetails, result];
      }
      labImportStore.importDetails = importDetails;
    }

    labImportActions.importLab(
      importSuccess,
      importError,
      true,
      handleOpenDialog
    );
    event.target.value = null;
  },

  importLab: async (
    importSuccess,
    importError,
    isCheckLDK,
    handleOpenDialog
  ) => {
    if (!isCheckLDK) {
      labImportStore.importDetails = labImportStore.importDetails.map(
        (item) => ({
          ...item,
          isCheckLDK: false,
        })
      );
    }
    const requestBody: ImportRequest = {
      importDetails: labImportStore.importDetails,
    };
    importLab(requestBody)
      .then(({ data: response }) => {
        if (response.isFailLDK) {
          importError();
          //Show error log file
          const byteCharacters = atob(response?.errorFileBase64);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const file = new Blob([byteArray], {
            type: 'application/pdf;base64',
          });
          const fileURL = URL.createObjectURL(file);
          window.open(fileURL);

          labImportActions.setOpenLDKAlert(true);
        } else {
          importSuccess();
          labImportActions.getLabImportOverview();
          labImportStore.importDetails = [];
        }

        if (response.importConficts?.length) {
          labImportStore.importConflicts = response.importConficts;
          handleOpenDialog();
        }
      })
      .catch((error) => {
        importError();
        console.error(error);
      });
  },

  toggleOnlyShowRecall(isShow): void {
    labImportStore.onlyShowLabWithRecall = isShow;
    labImportActions.getLabImportOverview();
  },

  changeDateRange(fromDate: Date, toDate: Date): void {
    labImportStore.fromDate = fromDate ? fromDate.getTime() : 0;
    labImportStore.toDate = toDate ? toDate.getTime() : 0;
    // trigger filter if both have value or clear value
    if (
      (labImportStore.fromDate && labImportStore.toDate) ||
      (!labImportStore.fromDate && !labImportStore.toDate)
    ) {
      labImportActions.getLabImportOverview();
    }
  },

  handlePageChange(page: number): void {
    labImportStore.page = page;
    labImportActions.getLabImportOverview();
  },

  handleChangeRowsPerPage(currentRowsPerPage: number): void {
    labImportStore.totalInPage = currentRowsPerPage;
    labImportActions.getLabImportOverview();
  },

  setOpenLDKAlert: (isOpen: boolean) => {
    labImportStore.openLDKAlert = isOpen;
  },
  searchPatient(parsedQuery): Promise<any> {
    if (parsedQuery.keyword && parsedQuery.keyword.trim().length > 0) {
      return PatientSearchApi.searchPatients({
        searchingKeyword: parsedQuery.keyword,
        searchingType: parsedQuery.type,
      }).then((response) => {
        return response?.data?.patients || [];
      });
    } else {
      return Promise.resolve([]);
    }
  },
  updateImportFile: (payload: UpdateImportFileRequest, callback) => {
    labImportStore.isLoadingImport = true;
    updateImportFile(payload)
      .then(() => {
        labImportStore.importConflicts = null;
        const { handleCloseDialog, showToastSuccessCallback } = callback;
        handleCloseDialog();
        showToastSuccessCallback();
      })
      .catch((err) => {
        const { showToastErrorCallback } = callback;
        showToastErrorCallback();
        throw err;
      })
      .finally(() => {
        labImportStore.isLoadingImport = false;
      });
  },
  getLDTContent: (payload, labOrderNumber, callback) => {
    labImportStore.lDTDetail.isLoading = true;
    getLDTContent(payload)
      .then(({ data: res }) => {
        labImportStore.lDTDetail.lDTContent = res?.lDTContents || [];
        labImportStore.lDTDetail.labOrderNumber = labOrderNumber;
        const { handleOpenLdtDialog } = callback;
        handleOpenLdtDialog();
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        labImportStore.lDTDetail.isLoading = false;
      });
  },
};

export function useLabImportStore() {
  useEffect(() => {
    return () => {
      labImportStore = proxy<ILabImportStore>(initStore);
    };
  }, []);
  return useSnapshot(labImportStore);
}
