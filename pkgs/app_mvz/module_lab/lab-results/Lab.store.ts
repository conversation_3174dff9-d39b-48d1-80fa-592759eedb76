import { useEffect } from 'react';
import { get } from 'lodash';
import { proxy, useSnapshot } from 'valtio';
import { getContractTypeByIds } from '@tutum/hermes/bff/legacy/app_mvz_billing';
import {
  createLabForm,
  CreateLabFormResponse,
  KeyValuePair,
  getLDTContent,
  getLDTAttachment,
  GetLDTContentRequest,
  checkLDKStatus,
} from '@tutum/hermes/bff/legacy/app_mvz_lab';
import { FormName } from '@tutum/hermes/bff/form_common';
import { 
  LabParameter,
  LabResultOverview,
  LabResultItem,
  GetLabResultsRequest,
  getLabResultsPDF,
  getLabResults } from '@tutum/hermes/bff/legacy/app_mvz_document_management';
import {
  LabForm,
  LabFormStatus,
  LDTContent,
  File,
} from '@tutum/hermes/bff/lab_common';
import {
  musterFormDialogStore,
  musterFormDialogActions,
  getDoctorFromContractDoctor,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { AvailableDoctor } from '@tutum/mvz/module_himi/himi-doctor-selector/HimiDoctorSelector';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { ContractType } from '@tutum/hermes/bff/common';
import { patientFileStore } from '../../module_patient-management/patient-file/PatientFile.store';
import { onEditTimelineById } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.service';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { PATIENT_HEADER } from '@tutum/mvz/constant/form';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { LabResultsPDFMode } from '@tutum/hermes/bff/legacy/app_mvz_document_management';

interface ILabStore {
  contractDoctor?: ISelectedContractDoctor | AvailableDoctor;
  patient?: IPatientProfile;
  openLDKAlert: boolean;
  lab: {
    labParameters: LabParameter[];
    labResults: LabResultOverview[];
    availableLabParameters: KeyValuePair[];
    isLoading: boolean;
  };
  pdfResultsReview: {
    filePdfUrl: string;
    isLoading: boolean;
    mode: LabResultsPDFMode;
  };
  lDTFiles: {
    labOrderId: string;
    nameFiles: string[];
  };
  lDTDetail: {
    isLoading: boolean;
    lDTContent: LDTContent[];
    lDTAttachments: File[];
  };
}


//now i map filedId on Fe so that the result will be
export interface ILabResultWithFieldId extends LabResultItem {
  fieldId?: string;
}

export interface ILabParameterWithFieldId extends LabParameter {
  fieldId?: string;
}


interface IPrescribeCallBack {
  showToastSuccess: () => void;
}

function buildLabParametersColData(labParameters: LabParameter[]) {
  return [...labParameters].map((item, index) => {
      return {
        ...item,
        isAvailable: true,
        fieldId: (index + 1).toString(),
      }
  })
}

function mapFieldIdToLabResults(
  labResults: LabResultOverview[],
  labParameters: (LabParameter & { fieldId?: string })[]
): LabResultOverview[] {
  const nameToFieldIdMap = new Map<string, string>();
  for (const param of labParameters) {
    if (param.name && param.fieldId) {
      nameToFieldIdMap.set(param.name, param.fieldId);
    }
  }

  return labResults.map(result => {
    const items = result.items.map(item => {
      if (!item.name || !nameToFieldIdMap.has(item.name)) return item;
      return {...item, fieldId: nameToFieldIdMap.get(item.name)};
    });
    return {...result, items};
  });
}

export interface ILabActions {
  prescribeForm: (
    callback?: IPrescribeCallBack,
    formStatus?: LabFormStatus,
    isSkipLDKValidate?: boolean
  ) => void;
  setOpenLDKAlert: (isOpen: boolean) => void;
  setPatient: (patient: IPatientProfile) => void;
  setContractDoctor: (contractDoctor: ISelectedContractDoctor) => void;
  loadLabResults: (payload: GetLabResultsRequest) => void;
  loadLabResultsPDF: (payload: GetLabResultsRequest, callback) => void;
  setlDTFiles: (labOrderId: string, nameFiles: string[]) => void;
  loadLDTContent: (payload: GetLDTContentRequest, callback) => void;
  checkLDKStatus: () => Promise<boolean>;
  prescribe?: () => void;
}

export enum LabPurpose {
  Print = 'Print',
  SendDigital = 'SendDigital',
}

const initStore: ILabStore = {
  openLDKAlert: false,
  lab: {
    labParameters: [],
    labResults: [],
    availableLabParameters: [],
    isLoading: false,
  },
  pdfResultsReview: {
    filePdfUrl: '',
    isLoading: false,
    mode: LabResultsPDFMode.PORTRAIT,
  },
  lDTFiles: {
    labOrderId: '',
    nameFiles: [],
  },
  lDTDetail: {
    isLoading: false,
    lDTContent: [],
    lDTAttachments: [],
  },
};

export let labStore = proxy<ILabStore>(initStore);
// devtools(labStore, 'labStore');

const handlePrescribeError = (
  res: CreateLabFormResponse,
  isCheckLDK: boolean
) => {
  if (res?.createLabFormError?.isDuplicatedLabOrderNumber) {
    musterFormDialogActions.setIsDuplicatedLabId(
      res?.createLabFormError?.isDuplicatedLabOrderNumber || null
    );
  } else {
    if (isCheckLDK) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      labActions.setOpenLDKAlert(true);
      const byteCharacters = atob(
        res?.createLabFormError?.errorFileBase64 || ''
      );
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const file = new Blob([byteArray], { type: 'application/pdf;base64' });
      const fileURL = URL.createObjectURL(file);
      window.open(fileURL);
    }
  }
};

export const labActions: ILabActions = {
  //isSkipLDKValidate === true if choose yes skip LDK dialog and save, print === false
  prescribeForm: async (
    callback,
    formStatus: LabFormStatus,
    isSkipLDKValidate
  ) => {
    let checkLDK;
    if (!isSkipLDKValidate) {
      checkLDK = await labActions.checkLDKStatus();
    }
    musterFormDialogStore.isLoadingPrescribe = true;
    const storeCurrentFormSetting = musterFormDialogStore.currentFormSetting;
    let contractType = ContractType.ContractType_KvContract;
    const contractId = musterFormDialogStore.contractDoctor?.contractId;
    if (contractId) {
      const contractTypeResult = await getContractTypeByIds({
        contractIds: [contractId],
      });
      contractType = contractTypeResult.data.contractIds[contractId];
    }

    const patientHeader = Object.entries(
      musterFormDialogStore?.currentFormSetting!
    ).reduce((acc, [key, value]) => {
      if (PATIENT_HEADER.includes(key)) {
        return { ...acc, [key]: value };
      } else {
        return acc;
      }
    }, {});

    const scheinData = patientFileStore.schein.activatedSchein;
    const payloadLabForm: LabForm = {
      contractId: musterFormDialogStore?.contractDoctor?.contractId!,
      contractType: contractType,
      createdDate: datetimeUtil.now(),
      doctorId: musterFormDialogStore?.contractDoctor?.doctorId as string,
      scheinId: scheinData?.scheinId!,
      encounterCase: musterFormDialogStore?.contractDoctor?.encounterCase!,
      formName: musterFormDialogStore?.currentFormName!,
      ikNumber: patientFileStore?.activeInsurance?.ikNumber!,
      labFormStatus: formStatus,
      labOrderNumber: musterFormDialogStore?.labId!,
      patientId: musterFormDialogStore?.patient?.id!,
      printedDate:
        formStatus === LabFormStatus.Send ? datetimeUtil.now() : undefined,
      prescribeDate: musterFormDialogStore?.prescriptionDate!,
      sendTo: musterFormDialogStore?.formSendToRecipient!,
      treatmentDoctorId: musterFormDialogStore?.contractDoctor?.doctorId!,
      labParameters: [],
      patientHeader: JSON.stringify(patientHeader),
    };

    switch (musterFormDialogStore.currentFormName) {
      case FormName.Muster_10A: {
        const exams = Array.from({ length: 64 }).map((_, index: number) => {
          return !!get(
            storeCurrentFormSetting,
            `label_checkbox_exam-${index + 1}`,
            false
          ) as boolean;
        });

        payloadLabForm.form10A = {
          checkbox_kurativ:
            storeCurrentFormSetting?.checkbox_kurativ as boolean,
          checkbox_praventiv:
            storeCurrentFormSetting?.checkbox_praventiv as boolean,
          checkbox_behandl:
            storeCurrentFormSetting?.checkbox_behandl as boolean,
          checkbox_unfall: storeCurrentFormSetting?.checkbox_unfall as boolean,
          date_label_custom_abnahmedatum: String(
            storeCurrentFormSetting?.date_label_custom_abnahmedatum
          ),
          textbox_knappschafts_kennziffer: String(
            storeCurrentFormSetting?.textbox_knappschafts_kennziffer
          ),
          textbox_zusat: String(storeCurrentFormSetting?.textbox_zusat),
          checkbox_exams: exams,
          textbox_ssw: String(storeCurrentFormSetting?.textbox_ssw),
          textbox_exam61_line1: String(
            storeCurrentFormSetting?.textbox_exam61_line1 || ''
          ),
          textbox_exam61_line2: String(
            storeCurrentFormSetting?.textbox_exam61_line2 || ''
          ),
          label_gender: String(storeCurrentFormSetting?.label_gender),
          date_abnahmezeit: String(storeCurrentFormSetting?.date_abnahmezeit),
        };
        break;
      }
      case FormName.Muster_39A: {
        payloadLabForm.form39 = {
          checkbox_30_34_jahre:
            storeCurrentFormSetting?.checkbox_30_34_jahre as boolean,
          checkbox_ab_35_jahre:
            storeCurrentFormSetting?.checkbox_ab_35_jahre as boolean,
          checkbox_20_29_jahre:
            storeCurrentFormSetting?.checkbox_20_29_jahre as boolean,
          checkbox_primar_screening:
            storeCurrentFormSetting?.checkbox_primar_screening as boolean,
          checkbox_abklarungs_diagnostik:
            storeCurrentFormSetting?.checkbox_abklarungs_diagnostik as boolean,
          checkbox_hpv_test:
            storeCurrentFormSetting?.checkbox_hpv_test as boolean,
          checkbox_ko_testung:
            storeCurrentFormSetting?.checkbox_ko_testung as boolean,
          checkbox_zytologie:
            storeCurrentFormSetting?.checkbox_zytologie as boolean,
          checkbox_ja: storeCurrentFormSetting?.checkbox_ja as boolean,
          checkbox_nein: storeCurrentFormSetting?.checkbox_nein as boolean,
          checkbox_hpv_keine:
            storeCurrentFormSetting?.checkbox_hpv_keine as boolean,
          checkbox_hpv_unklar:
            storeCurrentFormSetting?.checkbox_hpv_unklar as boolean,
          checkbox_hpv_vollstandig:
            storeCurrentFormSetting?.checkbox_hpv_vollstandig as boolean,
          checkbox_hpv_unvollstandig:
            storeCurrentFormSetting?.checkbox_hpv_unvollstandig as boolean,
          checkbox_hpv_hr_liegt_nicht:
            storeCurrentFormSetting?.checkbox_hpv_hr_liegt_nicht as boolean,
          checkbox_hpv_hr_liegt_vor:
            storeCurrentFormSetting?.checkbox_hpv_hr_liegt_vor as boolean,
          checkbox_hpv_hr_positiv:
            storeCurrentFormSetting?.checkbox_hpv_hr_positiv as boolean,
          checkbox_hpv_hr_negativ:
            storeCurrentFormSetting?.checkbox_hpv_hr_negativ as boolean,
          checkbox_hpv_hr_nicht_verwertb:
            storeCurrentFormSetting?.checkbox_hpv_hr_nicht_verwertb as boolean,
          checkbox_op_ja: storeCurrentFormSetting?.checkbox_op_ja as boolean,
          checkbox_op_nein:
            storeCurrentFormSetting?.checkbox_op_nein as boolean,
          checkbox_graviditat_nein:
            storeCurrentFormSetting?.checkbox_graviditat_nein as boolean,
          checkbox_graviditat_ja:
            storeCurrentFormSetting?.checkbox_graviditat_ja as boolean,
          checkbox_ausfluss_nein:
            storeCurrentFormSetting?.checkbox_ausfluss_nein as boolean,
          checkbox_ausfluss_ja:
            storeCurrentFormSetting?.checkbox_ausfluss_ja as boolean,
          checkbox_iup_nein:
            storeCurrentFormSetting?.checkbox_iup_nein as boolean,
          checkbox_iup_ja: storeCurrentFormSetting?.checkbox_iup_ja as boolean,
          checkbox_einnahme_nein:
            storeCurrentFormSetting?.checkbox_einnahme_nein as boolean,
          checkbox_einnahme_ja:
            storeCurrentFormSetting?.checkbox_einnahme_ja as boolean,
          checkbox_unauffallig:
            storeCurrentFormSetting?.checkbox_unauffallig as boolean,
          checkbox_auffallig:
            storeCurrentFormSetting?.checkbox_auffallig as boolean,
          textbox_gruppe: String(storeCurrentFormSetting?.label_gruppe),
          textbox_welche: String(storeCurrentFormSetting?.textbox_welche),
          date_custom_wann: String(storeCurrentFormSetting?.date_custom_wann),
          date_custom_jetzt: String(storeCurrentFormSetting?.date_custom_jetzt),
          date_anamnese: +storeCurrentFormSetting?.date_anamnese!,
          area_text_box_erlauterungen: String(
            storeCurrentFormSetting?.area_text_box_erlauterungen
          ),
        };
        break;
      }
      case FormName.Muster_10: {
        payloadLabForm.form10 = {
          checkbox_kurativ:
            storeCurrentFormSetting?.checkbox_kurativ as boolean,
          checkbox_praventiv:
            storeCurrentFormSetting?.checkbox_praventiv as boolean,
          checkbox_behandl:
            storeCurrentFormSetting?.checkbox_behandl as boolean,
          checkbox_unfall: storeCurrentFormSetting?.checkbox_unfall as boolean,
          textbox_knappschafts_kennziffer: String(
            storeCurrentFormSetting?.textbox_knappschafts_kennziffer
          ),
          date_quartal: Number(storeCurrentFormSetting?.date_quartal),
          checkbox_kontrolluntersuchung:
            storeCurrentFormSetting?.checkbox_kontrolluntersuchung as boolean,
          label_gender: storeCurrentFormSetting?.label_gender as string,
          checkbox_behandlung:
            storeCurrentFormSetting?.checkbox_behandlung as boolean,
          checkbox_eingeschrankter:
            storeCurrentFormSetting?.checkbox_eingeschrankter as boolean,
          checkbox_empfangnisregelung:
            storeCurrentFormSetting?.checkbox_empfangnisregelung as boolean,
          date_label_custom_abnahmedatum: Number(
            storeCurrentFormSetting?.date_label_custom_abnahmedatum
          ),
          date_abnahmezeit: storeCurrentFormSetting?.date_abnahmezeit as number,
          textbox_ssw: storeCurrentFormSetting?.textbox_ssw as string,
          textbox_betri: storeCurrentFormSetting?.textbox_betri as string,
          textbox_arzt: storeCurrentFormSetting?.textbox_arzt as string,
          checkbox_befund_eilt:
            storeCurrentFormSetting?.checkbox_befund_eilt as boolean,
          checkbox_telefon:
            storeCurrentFormSetting?.checkbox_telefon as boolean,
          checkbox_fax: storeCurrentFormSetting?.checkbox_fax as boolean,
          textbox_nr: storeCurrentFormSetting?.textbox_nr as string,
          textbox_diagnose: storeCurrentFormSetting?.textbox_diagnose as string,
          textbox_befund_line1:
            storeCurrentFormSetting?.textbox_befund_line1 as string,
          textbox_befund_line2:
            storeCurrentFormSetting?.textbox_befund_line2 as string,
          textbox_auftrag_line1:
            storeCurrentFormSetting?.textbox_auftrag_line1 as string,
          textbox_auftrag_line2:
            storeCurrentFormSetting?.textbox_auftrag_line2 as string,
          textbox_auftrag_line3:
            storeCurrentFormSetting?.textbox_auftrag_line3 as string,
        };
        break;
      }
      default:
        break;
    }

    if (musterFormDialogStore.isRefill) {
      // TODO: use new timeline service
      const result = await onEditTimelineById(
        musterFormDialogStore.labFormId || '',
        (model: TimelineModel): TimelineModel => {
          return {
            ...model,
            encounterLab: {
              ...model?.encounterLab!,
              labForm: payloadLabForm,
            },
          };
        }
      );

      musterFormDialogStore.isLoadingPrescribe = false;

      if (!result?.data?.timelineModel?.id) {
        return;
      }

      callback?.showToastSuccess();
      musterFormDialogActions.clear();
    } else {
      createLabForm({
        labForm: payloadLabForm,
        isCheckLDK: checkLDK,
      })
        .then((res) => {
          if (!res.data?.createLabFormError) {
            callback?.showToastSuccess();
            musterFormDialogActions.clear();
          } else {
            handlePrescribeError(res.data, checkLDK);
          }
        })
        .catch((err) => {
          throw err;
        })
        .finally(() => {
          musterFormDialogStore.isLoadingPrescribe = false;
        });
    }
  },

  setOpenLDKAlert: (isOpen: boolean) => {
    labStore.openLDKAlert = isOpen;
  },

  setPatient: async (patient: IPatientProfile) => {
    labStore.patient = patient;
  },

  setContractDoctor: (contractDoctor: ISelectedContractDoctor) => {
    if (!contractDoctor) {
      return;
    }

    if (!labStore?.contractDoctor) {
      labStore.contractDoctor = getDoctorFromContractDoctor(contractDoctor);
    }
    labStore.contractDoctor = contractDoctor;
  },
  setlDTFiles: (labOrderId, nameFiles) => {
    labStore.lDTFiles.labOrderId = labOrderId;
    labStore.lDTFiles.nameFiles = nameFiles;
  },
  loadLabResults: (payload) => {
    labStore.lab.isLoading = true;
    getLabResults(payload)
      .then((res) => {
        if (!res.data) {
          return;
        }
        const labParameters = buildLabParametersColData(res.data.labParameters || []);
        labStore.lab.labParameters = payload.fieldNames && payload.fieldNames.length > 0 ? labParameters.filter(item => payload.fieldNames?.includes(item.name)) : labParameters;
        labStore.lab.labResults = mapFieldIdToLabResults(res.data.labResults, labParameters);
        if (res.data.availableLabParameters) {
          const availableLabParameters : KeyValuePair[] = []
          res.data.availableLabParameters.forEach(item => {
            availableLabParameters.push({
              key: item.name, 
              value: item.name,
            })
          })
          availableLabParameters.sort((a, b) => a.value.localeCompare(b.value));
          labStore.lab.availableLabParameters = availableLabParameters;
        }
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        labStore.lab.isLoading = false;
      });
  },
  loadLabResultsPDF: (payload, callback) => {
    labStore.pdfResultsReview.isLoading = true;
    getLabResultsPDF(payload)
      .then((res) => {
        const byteCharacters = atob(`${res?.data.pdf}`);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const file = new Blob([byteArray], { type: 'application/pdf;base64' });
        const fileURL = URL.createObjectURL(file);
        labStore.pdfResultsReview.filePdfUrl = fileURL;
        const { handleOpenPrintReview } = callback;
        handleOpenPrintReview();
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        labStore.pdfResultsReview.isLoading = false;
      });
  },
  loadLDTContent: (payload: GetLDTContentRequest, callback) => {
    labStore.lDTDetail.isLoading = true;
    labStore.lDTFiles.labOrderId = payload.labOrderId;
    getLDTContent(payload)
      .then((res) => {
        labStore.lDTDetail.lDTContent = res.data?.lDTContents || [];
      })
      .then(() => getLDTAttachment(payload))
      .then((res) => {
        labStore.lDTDetail.lDTAttachments = res.data?.files || [];
        const {
          handleOpenLabDetailDialog,
          handleCloseLdtFilesDialog,
          resetItemSelected,
        } = callback;
        handleCloseLdtFilesDialog && handleCloseLdtFilesDialog();
        handleOpenLabDetailDialog && handleOpenLabDetailDialog();
        resetItemSelected && resetItemSelected();
      })
      .catch((err) => {
        throw err;
      })
      .finally(() => {
        labStore.lDTDetail.isLoading = false;
      });
  },

  async checkLDKStatus(): Promise<boolean> {
    try {
      const result = await checkLDKStatus();
      return result.data.isOnline;
    } catch (error) {
      //we also allow skip ldk if check is fail
      return false;
    }
  },
};

export function useLabStore() {
  useEffect(() => {
    return () => {
      labStore = proxy<ILabStore>(initStore);
    };
  }, []);
  return useSnapshot(labStore);
}
