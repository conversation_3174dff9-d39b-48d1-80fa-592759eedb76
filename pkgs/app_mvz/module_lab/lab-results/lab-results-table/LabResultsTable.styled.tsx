import React from 'react';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { scaleSpacePx } from '@tutum/design-system/styles';
import Theme from '@tutum/mvz/theme';
import OriginalLabResultsTable, {
  ILabResultsTableProps,
} from './LabResultsTable';
import { COLOR } from '@tutum/design-system/themes/styles';

const styled = Theme.styled;
const LabResultsTable: React.ComponentType<ILabResultsTableProps> = styled(
  OriginalLabResultsTable
).attrs(({ className }) => ({
  className: getCssClass('sl-LabResultsTable', className),
}))`
  .rdt_Table {
    max-height: calc(100vh - ${scaleSpacePx(33)});
    .no_records {
      padding: ${scaleSpacePx(6)};
      color: ${COLOR.TEXT_SECONDARY_NAVAL};
    }

    .rdt_TableBody {
      width: ${({ labResults }) =>`${((labResults?.length ?? 0) + 2) * 200}`}px;
      height: 100vh;
      padding-bottom: 15px;
      z-index: 0;
      background: ${COLOR.BACKGROUND_PRIMARY_WHITE};
    }

    .rdt_TableCell {
      z-index: 0;
      align-items: center;
      padding: 4px 8px;


      .sl-alert-icon {
        svg {
          width: ${scaleSpacePx(4)};
          height: ${scaleSpacePx(4)};
        }
      }
      .sl-maximize-icon {
        visibility: hidden;
        transition: visibility 0s, opacity 0.5s linear;
        svg {
          width: ${scaleSpacePx(5)};
          height: ${scaleSpacePx(5)};
        }

        &.bp5-active {
          visibility: visible;
        }
      }
      &:hover {
        .sl-maximize-icon {
          cursor: pointer;
          visibility: visible;
        }
      }
    }

    .rdt_TableRow {
      min-height: unset;
    }
    .rdt_TableRow:nth-child(even) {
      background-color: ${COLOR.BACKGROUND_ZEBRA} !important;
    }
    .rdt_TableHeadRow {
      border-bottom: none;
      width: ${({ labResults }) =>`${((labResults?.length ?? 0) + 2) * 200}`}px;
      border-top: 1px solid ${COLOR.BORDER_INPUT} !important;
    }
    .rdt_TableHeadRow .rdt_TableCol {
      max-width: 200px;
      min-width: 200px;
      width: 200px;
    }
    .rdt_TableRow .rdt_TableCell {
      max-width: 200px;
      min-width: 200px;
      width: 200px;
    }
    .rdt_TableHeadRow .rdt_TableCol:nth-of-type(1) {
      position: sticky;
      left: 0;
      z-index: 1;
      max-width: 200px;
      border: none;
    }

    .rdt_TableRow .rdt_TableCell:nth-of-type(1) {
      position: sticky;
      background-color: ${COLOR.BACKGROUND_ZEBRA};
      left: 0;
      z-index: 1;
      max-width: 200px;
      border: none;
    }

    .rdt_TableHeadRow .rdt_TableCol:nth-of-type(2) {
      position: sticky;
      left: 200px;
      max-width: 200px;
      border-left: 1px solid ${COLOR.BORDER_INPUT};
      z-index: 1;
    }

    .rdt_TableRow .rdt_TableCell:nth-of-type(2) {
      position: sticky;
      background-color: ${COLOR.BACKGROUND_ZEBRA};
      border-left: 1px solid ${COLOR.BORDER_INPUT};
      left: 200px;
      max-width: 200px;
      z-index: 1;
    }
    .rdt_TableRow:last-child {
      border-bottom: 1px solid ${COLOR.BORDER_INPUT};
    }

    .sl-table-header-custom {
      .sl-date {
        width: ${scaleSpacePx(20)};
        cursor: pointer;
        color: ${COLOR.TEXT_INFO};
      }
      .sl-number-recall {
        width: ${scaleSpacePx(5)};
        height: ${scaleSpacePx(5)};
        display: flex;
        align-items: center;
        justify-content: center;
        background: ${COLOR.TEXT_NEGATIVE};
        color: ${COLOR.BACKGROUND_PRIMARY_WHITE};
        border-radius: 50%;
      }
    }

    .sl-popover-cell {
      position: relative;

      .bp5-popover-wrapper {
        position: absolute;
        right: 0;
      }
    }

    .sl-error-value {
      color: ${COLOR.TEXT_NEGATIVE};
      font-weight: 600;
    }
  }
`;

export default LabResultsTable;
