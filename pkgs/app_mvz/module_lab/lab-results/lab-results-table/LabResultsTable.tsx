import React, { memo } from 'react';
import { IMvzTheme } from '@tutum/mvz/theme';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import Table from '@tutum/design-system/components/Table';
import { getColumns, customStyles } from './setting-table';
import { LabResultOverview, LabParameter  } from '@tutum/hermes/bff/app_mvz_document_management';
import { ILabResultWithFieldId, ILabParameterWithFieldId } from '@tutum/mvz/module_lab/lab-results/Lab.store';
import { labActions } from '@tutum/mvz/module_lab/lab-results/Lab.store';

export interface ILabResultsTableProps {
  className?: string;
  theme?: IMvzTheme;
  labParameters: LabParameter[];
  labResults: LabResultOverview[];
  isLoading: boolean;
  handleOpenLdtFilesDialog: () => void;
  handleLoadDirectlyLdtFile: (labOrderId: string, fileName: string) => void;
}

const LabResultsTable = ({
  className,
  labParameters,
  labResults,
  isLoading,
  handleOpenLdtFilesDialog,
  handleLoadDirectlyLdtFile,
  t,
}: ILabResultsTableProps &
  II18nFixedNamespace<keyof typeof LabI18n.LabScreen>) => {
  const { t: tCommon } = I18n.useTranslation({
    namespace: 'Common',
    nestedTrans: 'Table',
  });
  const COLUMNS = getColumns(t);
  const handleGenerateColumns = () => {
    const columns: any = [COLUMNS.labParameters, COLUMNS.norm];
    // const handleSetlDTFiles = (labOrderId: string, lDTFiles: string[]) => {
    //   labActions.setlDTFiles(labOrderId, lDTFiles);
    // };

    labResults
      .map((item, index) => ({
        date: item.date,
        selector: `date_${index}`,
        labOrderId: item.labOrderId,
      }))
      .map((item) => {
        columns.push(
          COLUMNS.date(
            item,
            // handleOpenLdtFilesDialog,
            // handleSetlDTFiles,
            // handleLoadDirectlyLdtFile
          )
        );
      });
    return columns;
  };

  const handleMapData = () => {
    const data = labParameters.map((item : ILabParameterWithFieldId) => {
      const objData = labResults.reduce(
        (acc, curr, index) => ({
          ...acc,
          [`date_${index}`]:
            curr.items.find(
              (itemLabResult : ILabResultWithFieldId) => itemLabResult.fieldId === item.fieldId
            ) || null,
        }),
        {}
      );
      return {
        ...item,
        ...objData,
      };
    });
    return data;
  };

  return (
    <div className={className}>
      <Table
        columns={handleGenerateColumns()}
        data={handleMapData()}
        customStyles={customStyles}
        noHeader
        progressPending={isLoading}
        noDataComponent={
          <div className="no_records">{tCommon('noRecords')}</div>
        }
      />
    </div>
  );
};

export default memo(
  I18n.withTranslation(LabResultsTable, {
    namespace: 'Lab',
    nestedTrans: 'LabScreen',
  })
);
