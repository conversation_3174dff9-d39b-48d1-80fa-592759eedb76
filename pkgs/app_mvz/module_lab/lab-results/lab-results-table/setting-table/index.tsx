import React from 'react';
import { Flex, Svg } from '@tutum/design-system/components';
import {
  Tooltip,
  Popover,
  PopoverPosition,
  PopoverInteractionKind,
} from '@tutum/design-system/components/Core';
import { IDataTableStyles } from '@tutum/design-system/components/Table';
import moment from 'moment';
import PopoverDetail from './popover-detail/PopoverDetail.styled';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';

const maximizeIcon = '/images/maximize-icon.svg';
const alertIcon = '/images/alert-triangle-24.svg';

export const getColumns = (
  t: IFixedNamespaceTFunction<keyof typeof LabI18n.LabScreen>
) => ({
  labParameters: {
    name: t('labParameters'),
    width: '33%',
    cell: (row) => (
      <Tooltip
        // targetClassName="sl-target-content"
        position={PopoverPosition.TOP}
        content={<span>{row.name}</span>}
      >
        <span>{row.name}</span>
      </Tooltip>
    ),
  },
  norm: {
    name: 'Norm',
    width: '33%',
    cell: (row) => (
      <Tooltip
        // targetClassName="sl-target-content"
        position={PopoverPosition.TOP}
        content={
          <span>
            {row.min} - {row.max} {row.unit}
          </span>
        }
      >
        <span>
          {row.min} - {row.max} {row.unit}
        </span>
      </Tooltip>
    ),
  },
  date: (
    item
    // handleOpenDialog,
    // handleSetlDTFiles,
    // handleLoadDirectlyLdtFile
  ) => {
    const renderCellValue = (data) => {
      if (!data) {
        return null;
      }

      const { value, testResultText, icon } = data;
      const hasValue = value !== undefined && value !== null && value !== '';
      const hasTestResultText = !!testResultText;

      const formattedValue = hasValue
        ? isNaN(value)
          ? value
          : parseFloat(value).toFixed(2)
        : '';

      const iconText = icon ? ` (${icon})` : '';
      const valueClassName = icon ? 'sl-error-value' : '';

      if (hasTestResultText && hasValue) {
        return (
          <Flex
            align="flex-start"
            justify="space-between"
            column
            gap={8}
            w="100%"
          >
            <span className={valueClassName}>
              {formattedValue}
              {iconText}
            </span>
            <span className={valueClassName}>{testResultText}</span>
          </Flex>
        );
      }

      if (hasTestResultText) {
        return (
          <span className={valueClassName}>
            {testResultText}
            {iconText}
          </span>
        );
      }

      if (hasValue) {
        return (
          <span className={valueClassName}>
            {formattedValue}
            {iconText}
          </span>
        );
      }

      return null;
    };

    return {
      name: (
        <Flex
          className="sl-table-header-custom"
          justify="space-between"
          align="center"
        >
          <span
            className="sl-date"
            // onClick={() => {
            //   if (item?.lDTFiles?.length === 1) {
            //     handleLoadDirectlyLdtFile(item.labOrderId, item.lDTFiles[0]);
            //   } else {
            //     handleSetlDTFiles(item.labOrderId, item.lDTFiles);
            //     handleOpenDialog();
            //   }
            // }}
          >
            {moment(item.date).format(DATE_FORMAT)}
          </span>
          {item.totalRecallMessage ? (
            <Tooltip
              // targetClassName="sl-target-content"
              content={
                <span>Total {item.totalRecallMessage} recall messages</span>
              }
            >
              <div className="sl-number-recall">{item.totalRecallMessage}</div>
            </Tooltip>
          ) : null}
        </Flex>
      ),
      selector: item.selector,
      width: '33%',
      cell: (row) => (
        <Flex
          className="sl-popover-cell"
          w="100%"
          align="center"
          justify="space-between"
        >
          {renderCellValue(row[item.selector])}
          {row[item.selector]?.isRecall && (
            <Svg className="sl-alert-icon" src={alertIcon} />
          )}
          {row[item.selector]?.value ||
          row[item.selector]?.testResultText ||
          row[item.selector]?.testNote ? (
            <Popover
              interactionKind={PopoverInteractionKind.CLICK}
              position={PopoverPosition.BOTTOM}
              content={
                <PopoverDetail
                  date={item.date}
                  parameterName={row?.name}
                  unit={row?.unit}
                  data={row[item.selector]}
                />
              }
            >
              <Svg className="sl-maximize-icon" src={maximizeIcon} />
            </Popover>
          ) : null}
        </Flex>
      ),
    };
  },
});

export const customStyles: IDataTableStyles = {
  rows: {
    style: {
      minHeight: '40px',
    },
  },
  headRow: {
    style: {
      textTransform: 'uppercase',
    },
  },
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
    },
  },
  cells: {
    style: {
      padding: '8px',
    },
  },
};
