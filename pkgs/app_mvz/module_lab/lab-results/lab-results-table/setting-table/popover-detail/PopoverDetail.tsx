import React, { memo } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import { Flex, BodyTextL, H1, H4, Svg } from '@tutum/design-system/components';
import moment from 'moment';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';

export interface IPopoverDetailProps {
  className?: string;
  date: number;
  parameterName: string;
  unit: string;
  data: any;
}

const alertIcon = '/images/alert-triangle-24.svg';

const PopoverDetail = ({
  className,
  date,
  parameterName,
  unit,
  data, 
  t,
}: IPopoverDetailProps &
  II18nFixedNamespace<keyof typeof LabI18n.LabScreen>) => {
  return (
    <Flex className={className} column>
      <div style={{ marginBottom: 16 }}>
        <H1 fontSize={20} style={{ display: 'inline' }}>
          {parameterName}:&nbsp;
        </H1>
        <H1
          fontSize={20}
          className={data?.icon ? 'sl-error-value' : ''}
          style={{ display: 'inline' }}
        >
          {isNaN(data?.value)
            ? data?.value
            : parseFloat(data?.value).toFixed(2)}{' '}
          {unit} {data?.icon ? ` (${data?.icon})` : null}
        </H1>
      </div>
      <Flex column>
        <H4 fontSize={13}>Test Date:</H4>
        <BodyTextL fontSize={13}>{moment(date).format(DATE_FORMAT)}</BodyTextL>
      </Flex>
      {data?.recallMsg && (
        <Flex column mb={16}>
          <H4 fontSize={13}>Recall message:</H4>
          <BodyTextL className="sl-recall-message" fontSize={13}>
            {data?.isRecall && (
              <Svg className="sl-alert-icon" src={alertIcon} />
            )}
            {data?.recallMsg}
          </BodyTextL>
        </Flex>
      )}
      {data?.testNote && (
        <Flex column mb={16}>
          <H4 fontSize={13}>{t('labTestNote')}</H4>
          <BodyTextL fontSize={13}>{data?.testNote}</BodyTextL>
        </Flex>
      )}
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(PopoverDetail, {
    namespace: 'Lab',
    nestedTrans: 'LabScreen',
  })
);
