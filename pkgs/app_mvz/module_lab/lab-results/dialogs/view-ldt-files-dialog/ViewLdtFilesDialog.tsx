import React, { memo, useState } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import {
  BodyTextS,
  BodyTextL,
  Box,
  Flex,
} from '@tutum/design-system/components';
import {
  Classes,
  MenuItem,
  Label,
  Button,
  Dialog,
} from '@tutum/design-system/components/Core';
import { Select } from '@tutum/design-system/components/Select';

export interface IViewLdtFilesDialogProps {
  className?: string;
  isOpen: boolean;
  lDTFiles: {
    labOrderId: string;
    nameFiles: string[];
  };
  isLoading: boolean;
  handleCloseDialog: () => void;
  handleConfirm: (fileName: string, callback) => void;
}

export interface IMenuItem {
  value: string;
  text: string;
}

const selectItemKeyValueRender = (item: IMenuItem, { handleClick }) => {
  return (
    <MenuItem
      key={item.value}
      text={item.text}
      onClick={handleClick}
      shouldDismissPopover={true}
    />
  );
};

const ViewLdtFilesDialog = ({
  t,
  className,
  isOpen,
  lDTFiles,
  isLoading,
  handleCloseDialog,
  handleConfirm,
}: IViewLdtFilesDialogProps &
  II18nFixedNamespace<keyof typeof LabI18n.LabScreen>) => {
  const [itemSelected, setItemSelected] = useState<IMenuItem | null>(null);

  const onClose = () => {
    handleCloseDialog();
    setItemSelected(null);
  };

  const onConfirm = () => {
    const resetItemSelected = () => setItemSelected(null);
    itemSelected && handleConfirm(itemSelected.value, { resetItemSelected });
  };

  return (
    <Dialog
      className={className}
      title={
        <BodyTextL
          fontFamily="Work Sans"
          lineHeight="28px"
          fontSize={15}
          fontWeight="Bold"
        >
          View LDT-files
        </BodyTextL>
      }
      isOpen={isOpen}
      onClose={onClose}
      canOutsideClickClose={false}
    >
      <Flex column p="18px 25px">
        <BodyTextL fontSize={13} margin="0 0 16px 0">
          There are multiple LDT files available for this date. Please select
          which LDT file you want to view.
        </BodyTextL>
        <Flex column>
          <Label>
            <BodyTextS fontWeight={500} fontSize={11}>
              LDT-FILE
            </BodyTextS>
          </Label>
          <Select
            className="sl-selection"
            items={
              lDTFiles?.nameFiles?.map((strItem) => ({
                value: strItem,
                text: strItem,
              })) || []
            }
            itemRenderer={selectItemKeyValueRender}
            onItemSelect={(item: IMenuItem) => {
              setItemSelected(item);
            }}
            filterable={false}
            disabled={isLoading}
            popoverProps={{
              usePortal: false,
            }}
          >
            <Button
              className="sl-select-search"
              text={itemSelected?.text || 'Select'}
              rightIcon="caret-down"
              disabled={isLoading}
            />
          </Select>
        </Flex>
      </Flex>

      <Flex justify="space-between" className={Classes.DIALOG_FOOTER}>
        <Box w="45%">
          <Button fill onClick={onClose} loading={isLoading}>
            {t('cancel')}
          </Button>
        </Box>
        <Box w="45%">
          <Button fill intent="primary" onClick={onConfirm} loading={isLoading}>
            {t('continue')}
          </Button>
        </Box>
      </Flex>
    </Dialog>
  );
};

export default memo(
  I18n.withTranslation(ViewLdtFilesDialog, {
    namespace: 'Lab',
    nestedTrans: 'LabScreen',
  })
);
