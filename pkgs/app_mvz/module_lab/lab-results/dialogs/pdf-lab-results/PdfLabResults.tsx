import React, { memo } from 'react';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import { Dialog } from '@tutum/design-system/components/Core';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { alertSuccessfully } from '@tutum/design-system/components';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import { LabResultsPDFMode } from '@tutum/hermes/bff/legacy/app_mvz_document_management';
import { FormName } from '@tutum/hermes/bff/form_common';

export interface IPdfLabResultsProps {
  className?: string;
  isOpen: boolean;
  handleCloseDialog: () => void;
  pdfUrl: string;
  mode: LabResultsPDFMode;
}

const PdfLabResults = ({
  isOpen,
  className,
  handleCloseDialog,
  pdfUrl,
  mode,
}: IPdfLabResultsProps &
  II18nFixedNamespace<keyof typeof LabI18n.LabScreen>) => {
    const { t: tLab } = I18n.useTranslation<keyof typeof LabI18n.PrintPreviewDialog>({
      namespace: 'Lab',
      nestedTrans: 'PrintPreviewDialog',
    });


  const handlePrintSuccess = async () => {
    handleCloseDialog();
    alertSuccessfully(tLab('printSuccess'));
  };

  return (
    <Dialog
      className={getCssClass(className, 'bp5-dialog-fullscreen')}
      isOpen={isOpen}
      onClose={handleCloseDialog}
      canOutsideClickClose={false}
    >
      <PrintPreviewPdfDialog
          data-testid="view-doctor-letter-pdf-dialog"
          onPrintSuccess={async() => handlePrintSuccess()}
          titleText={tLab('title')}
          formId={mode === LabResultsPDFMode.LANDSCAPE ? FormName.LabResults_Landscape : FormName.LabResults_Portrait}
          file={pdfUrl}
          onClose={handleCloseDialog}
          PageProps={{
            scale: 1.5,
          }}
        />
    </Dialog>
  );
};

export default memo(
  I18n.withTranslation(PdfLabResults, {
    namespace: 'Lab',
    nestedTrans: 'LabScreen',
  })
);
