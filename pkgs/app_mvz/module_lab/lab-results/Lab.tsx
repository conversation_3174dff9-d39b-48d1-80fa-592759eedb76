import React, { memo, useEffect, useState, useMemo } from 'react';
import { alertSuccessfully, Flex, Svg } from '@tutum/design-system/components';
import {
  Position,
  Toaster,
  Spinner,
  Intent,
} from '@tutum/design-system/components/Core';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import useStateCallbackInline from '@tutum/mvz/hooks/useStateWithCallBackInline';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import { musterFormDialogActions } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import {
  labActions,
  useLabStore,
} from '@tutum/mvz/module_lab/lab-results/Lab.store';
import { IMvzTheme } from '@tutum/mvz/theme';
import { FormName } from '@tutum/hermes/bff/form_common';
import { LabFormStatus } from '@tutum/hermes/bff/lab_common';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMenuItem } from './form-filters/FormFilters';
import FormFilters from './form-filters/FormFilters.styled';
import LabResultsTable from './lab-results-table/LabResultsTable.styled';
import LabDetail from './lab-detail/LabDetail.styled';
import FavAlertDialog from './dialogs/fav-alert-dialog/FavAlertDialog.styled';
import LdkResultDialog from './dialogs/ldk-result-dialog/LdkResultDialog.styled';
import ViewLdtFilesDialog from './dialogs/view-ldt-files-dialog/ViewLdtFilesDialog.styled';
import PdfLabResults from './dialogs/pdf-lab-results/PdfLabResults.styled';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import { useMusterFormDialogStore } from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import {
  formOverviewActions,
  useFormOverviewStore,
} from '@tutum/mvz/module_form/form-overview/FormOverview.store';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { FormType } from '@tutum/hermes/bff/form_common';
import {
  printSettingStore as printSettingsStore,
  FormTypeSetting,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';

// const checkCircle = '/images/check-circle-2.svg';

export interface ILabProps {
  className?: string;
  theme?: IMvzTheme;
  selectedContractDoctor: ISelectedContractDoctor;
  patient: IPatientProfile;
  currentTabId: ID_TABS;
}

export interface IFilterValues {
  latestLab?: number;
  fromDate?: Date;
  toDate?: Date;
  fieldIds?: IMenuItem[];
  pathological: boolean;
}

const initFilterValues: IFilterValues = {
  latestLab: 10,
  fromDate: undefined,
  toDate: undefined,
  fieldIds: [],
  pathological: false,
};

export enum OPTION_VALUES {
  LASTED_SELECT = 'one',
  DATE_RANGE = 'two',
}

const Lab = ({
  t,
  className,
  selectedContractDoctor,
  patient,
  currentTabId,
}: ILabProps & II18nFixedNamespace<keyof typeof LabI18n.LabScreen>) => {
  const store = useLabStore();
  const musterFormDialogStore = useMusterFormDialogStore();

  const [selectedFormName, setSelectedFormName] = useState(null);
  const [filterValues, setFilterValues] = useState(initFilterValues);
  const [optionValue, setOptionValue] = useState(OPTION_VALUES.LASTED_SELECT);

  const [isOpenLabDetail, setIsOpenLabDetail] = useStateCallbackInline(false);
  const [openFavAlertDialog, setOpenFavAlertDialog] =
    useStateCallbackInline(false);
  const [openPrintReview, setOpenPrintReview] = useStateCallbackInline(false);
  const [openLdtFiles, setOpenLdtFiles] = useStateCallbackInline(false);

  const { listForms } = useFormOverviewStore();

  const hintM10 = useMemo(() => {
    return listForms.find(
      (form) =>
        form.formType === FormType.FormType_contract_hint &&
        form.title === 'Muster 10/10A'
    );
  }, [listForms]);

  useEffect(() => {
    if (!selectedContractDoctor) {
      return;
    }
    labActions.setContractDoctor(selectedContractDoctor);
    labActions.setPatient(patient);
  }, [selectedContractDoctor, patient]);

  useEffect(() => {
    if (currentTabId !== ID_TABS.LAB) return
    labActions.loadLabResults({
      patientId: patient.id,
      fromDate: filterValues.fromDate?.getTime() || undefined,
      toDate: filterValues.toDate?.getTime() || undefined,
      results: filterValues.latestLab,
      fieldNames: filterValues.fieldIds?.map((item) => item.value) as string[],
      isOnlyPathologicalResults: filterValues.pathological,
    });
  }, [
    patient.id,
    filterValues.fromDate,
    filterValues.toDate,
    filterValues.latestLab,
    filterValues.fieldIds,
    filterValues.pathological,
    currentTabId,
  ]);

  const handleChangeFilters = (name: string, value) => {
    setFilterValues({
      ...filterValues,
      [name]: value,
    });
  };

  const handleChangeDateRange = (fromDate: Date, toDate: Date) => {
    setFilterValues({
      ...filterValues,
      fromDate,
      toDate,
    });
  };

  const handleChangeOption = (value: OPTION_VALUES) => {
    if (value === OPTION_VALUES.LASTED_SELECT) {
      setFilterValues({
        ...filterValues,
        fromDate: undefined,
        toDate: undefined,
        latestLab: 10,
      });
    }
    if (value === OPTION_VALUES.DATE_RANGE) {
      setFilterValues({
        ...filterValues,
        latestLab: undefined,
      });
    }
    setOptionValue(value);
  };

  const handleCreateFormClick = (formName: string) => {
    if (
      [String(FormName.Muster_10A), String(FormName.Muster_10)].includes(
        formName
      ) &&
      (patient.listHpmInformation?.some((item) => item.status === 'Active') ||
        !!hintM10)
    ) {
      setOpenFavAlertDialog(true);
      setSelectedFormName(formName as any);
    } else {
      musterFormDialogActions.setCurrentFormName(formName);
      musterFormDialogActions.setStoreAndAction(null, labActions);
    }
  };

  const handleOpenLabDetailDialog = () => {
    setIsOpenLabDetail(true);
  };

  const handleCloseLabDetailDialog = () => {
    setIsOpenLabDetail(false);
  };

  // const handleCloseFavAlertDialog = () => {
  //   setOpenFavAlertDialog(false);
  // };

  // const handleNextAction = () => {
  //   setOpenFavAlertDialog(false, () => {
  //     musterFormDialogActions.setCurrentFormName(selectedFormName || '');
  //     musterFormDialogActions.setStoreAndAction(null, labActions);
  //   });
  // };

  const handleClosePrintReview = () => {
    setOpenPrintReview(false);
  };

  const handlePrintPdf = () => {
    const handleOpenPrintReview = () => setOpenPrintReview(true);
    labActions.loadLabResultsPDF(
      {
        patientId: patient.id,
        fromDate: filterValues.fromDate?.getTime() || undefined,
        toDate: filterValues.toDate?.getTime() || undefined,
        results: filterValues.latestLab,
        fieldNames: filterValues.fieldIds?.map(
          (item) => item.value
        ) as string[],
        isOnlyPathologicalResults: filterValues.pathological,
      },
      { handleOpenPrintReview }
    );
  };

  const handleOpenLdtFilesDialog = () => {
    setOpenLdtFiles(true);
  };

  const handleCloseLdtFilesDialog = () => {
    setOpenLdtFiles(false);
  };

  const handleConfirmLdtFilesDialog = (
    fileName: string,
    { resetItemSelected }
  ) => {
    labActions.loadLDTContent(
      {
        labOrderId: store?.lDTFiles?.labOrderId,
        lDTFileName: fileName,
      },
      {
        handleOpenLabDetailDialog,
        handleCloseLdtFilesDialog,
        resetItemSelected,
      }
    );
  };

  const handleLoadDirectlyLdtFile = (labOrderId: string, fileName: string) => {
    labActions.loadLDTContent(
      {
        labOrderId: labOrderId,
        lDTFileName: fileName,
      },
      {
        handleOpenLabDetailDialog,
      }
    );
  };

  const formSetting = useMemo(() => {
    return printSettingsStore.formsSetting.find(
      (form) => form.formId === musterFormDialogStore.currentFormName
    );
  }, [musterFormDialogStore.currentFormName]);

  return (
    <div className={className}>
      <FormFilters
        filterValues={filterValues}
        handleChangeFilters={handleChangeFilters}
        handleChangeDateRange={handleChangeDateRange}
        optionValue={optionValue}
        handleChangeOption={handleChangeOption}
        handleCreateFormClick={handleCreateFormClick}
        labParameters={store?.lab?.availableLabParameters?.map((item) => ({
          text: item.value,
          value: item.key,
        }))}
        handlePrintPdf={handlePrintPdf}
        isLoadingPdfResultsReview={store?.pdfResultsReview?.isLoading}
      />
      <LabResultsTable
        labParameters={store?.lab?.labParameters}
        labResults={store?.lab?.labResults}
        isLoading={store?.lab?.isLoading}
        handleOpenLdtFilesDialog={handleOpenLdtFilesDialog}
        handleLoadDirectlyLdtFile={handleLoadDirectlyLdtFile}
      />

      <LabDetail
        isOpen={isOpenLabDetail}
        handleCloseDialog={handleCloseLabDetailDialog}
        patientName={{
          firstName: patient?.firstName,
          lastName: patient?.lastName,
        }}
      />
      {/* <FavAlertDialog
        isOpen={openFavAlertDialog}
        hintM10={hintM10}
        handleCloseDialog={handleCloseFavAlertDialog}
        handleConfirm={handleNextAction}
      /> */}
      <LdkResultDialog
        isOpen={store.openLDKAlert}
        handleCloseDialog={() => labActions.setOpenLDKAlert(false)}
        handleConfirm={() => {
          const showToastSuccess = () => {
            alertSuccessfully(t('FormSaved'));
          };

          labActions.setOpenLDKAlert(false);
          labActions.prescribeForm(
            { showToastSuccess },
            LabFormStatus.Send,
            true
          );
        }}
      />
      <ViewLdtFilesDialog
        isOpen={openLdtFiles}
        lDTFiles={store?.lDTFiles}
        isLoading={store?.lDTDetail?.isLoading}
        handleCloseDialog={handleCloseLdtFilesDialog}
        handleConfirm={handleConfirmLdtFilesDialog}
      />
      <PdfLabResults
        isOpen={openPrintReview}
        pdfUrl={store?.pdfResultsReview?.filePdfUrl}
        mode={store?.pdfResultsReview?.mode}
        handleCloseDialog={handleClosePrintReview}
      />
      {!openLdtFiles && store?.lDTDetail?.isLoading && (
        <Flex
          className="sl-spinner-loading"
          w="100%"
          h="100%"
          justify="center"
          align="center"
        >
          <Spinner intent={Intent.PRIMARY} />
        </Flex>
      )}

      {formSetting?.type === FormTypeSetting.LAB_FORM && (
        <MusterFormDialog
          patient={patient}
          selectedContractDoctor={selectedContractDoctor}
          isOpen={!!musterFormDialogStore.currentFormName}
          onClose={() => {
            musterFormDialogActions.setProduct(undefined);
            musterFormDialogActions.setProductType(undefined);
          }}
          componentActions={formOverviewActions}
          // componentActions={{
          //   ...labActions,
          //   prescribe: () => formOverviewActions.prescribe(datetimeUtil.now()),
          // }}
        />
      )}
    </div>
  );
};

export default memo(
  I18n.withTranslation(Lab, {
    namespace: 'Lab',
    nestedTrans: 'LabScreen',
  })
);
