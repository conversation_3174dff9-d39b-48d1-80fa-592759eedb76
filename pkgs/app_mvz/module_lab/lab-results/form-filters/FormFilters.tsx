import React, { memo } from 'react';
import { IMvzTheme } from '@tutum/mvz/theme';
import I18n, {
  IFixedNamespaceTFunction,
  II18nFixedNamespace,
} from '@tutum/infrastructure/i18n';
import type LabI18n from '@tutum/mvz/locales/en/Lab.json';
import { FormName } from '@tutum/hermes/bff/form_common';
import { Flex } from '@tutum/design-system/components';
import {
  MenuItem,
  Button,
  Divider,
  Icon,
  Intent,
  Popover,
  PopoverInteractionKind,
  Position,
  Radio,
  Switch,
} from '@tutum/design-system/components/Core';
import { Select } from '@tutum/design-system/components/Select';
import DateRangeSingleInput from './date-range-input/DateRangeInput.styled';
import MomentLocaleUtils from 'react-day-picker/moment';
import { IFilterValues, OPTION_VALUES } from '../Lab';
import MultiSelectParameters from './multi-select-parameters/MultiSelectParameters.styled';

export interface IMenuItem {
  value: string | number;
  text: string;
}

export interface IFormFiltersProps {
  className?: string;
  theme?: IMvzTheme;
  filterValues: IFilterValues;
  handleChangeFilters: (name: string, value) => void;
  handleChangeDateRange: (fromDate: Date, toDate: Date) => void;
  optionValue: OPTION_VALUES;
  handleChangeOption: (value: OPTION_VALUES) => void;
  handleCreateFormClick: (formName: FormName) => void;
  labParameters: IMenuItem[];
  handlePrintPdf: () => void;
  isLoadingPdfResultsReview: boolean;
}

const selectItemKeyValueRender = (item: IMenuItem, { handleClick }) => {
  return (
    <MenuItem
      key={item.value}
      text={item.text}
      onClick={handleClick}
      shouldDismissPopover={true}
    />
  );
};

const listLabForms = (
  t: IFixedNamespaceTFunction<keyof typeof LabI18n.LabScreen>
) => [
  {
    formName: FormName.Muster_10,
    text: t('createForm10'),
  },
  {
    formName: FormName.Muster_10A,
    text: t('createForm10A'),
  },
  {
    formName: FormName.Muster_39A,
    text: t('createForm39'),
  },
];

const listOptionLastedResults = (
  t: IFixedNamespaceTFunction<keyof typeof LabI18n.LabScreen>
) => [
  {
    value: 4,
    text: t('4LatestLabResults'),
  },
  {
    value: 10,
    text: t('10LatestLabResults'),
  },
  {
    value: 100,
    text: t('allLabResults'),
  },
];

const FormFilters = ({
  t,
  className,
  filterValues,
  handleChangeDateRange,
  handleChangeFilters,
  optionValue,
  handleChangeOption,
  handleCreateFormClick,
  labParameters,
  handlePrintPdf,
  isLoadingPdfResultsReview,
}: IFormFiltersProps & II18nFixedNamespace<keyof typeof LabI18n.LabScreen>) => {
  return (
    <Flex className={className} justify="space-between">
      <Flex className="sl-left-side" flexWrap justify="space-between">
        <Flex mb={8}>
          <Flex align="center" mr={8}>
            <Radio
              className="sl-radio"
              value={OPTION_VALUES.LASTED_SELECT}
              checked={optionValue === OPTION_VALUES.LASTED_SELECT}
              onChange={(e) => {
                handleChangeOption(e.currentTarget.value as OPTION_VALUES);
              }}
            />
            <Select
              className="sl-selection"
              disabled={optionValue !== OPTION_VALUES.LASTED_SELECT}
              items={listOptionLastedResults(t)}
              itemRenderer={selectItemKeyValueRender}
              onItemSelect={(item: IMenuItem) => {
                handleChangeFilters('latestLab', item.value);
              }}
              filterable={false}
              popoverProps={{
                usePortal: false,
              }}
            >
              <Button
                className="sl-select-search"
                text={
                  listOptionLastedResults?.(t).find(
                    (item) => item.value === filterValues.latestLab
                  )?.text || t('selectLatestResults')
                }
                rightIcon="caret-down"
                disabled={optionValue !== OPTION_VALUES.LASTED_SELECT}
              />
            </Select>
          </Flex>
          <Flex align="center">
            <Radio
              className="sl-radio"
              value={OPTION_VALUES.DATE_RANGE}
              checked={optionValue === OPTION_VALUES.DATE_RANGE}
              onChange={(e) => {
                handleChangeOption(e.currentTarget.value as OPTION_VALUES);
              }}
            />
            <DateRangeSingleInput
              onChange={(startDate, endDate) => {
                handleChangeDateRange(startDate, endDate);
              }}
              disabled={optionValue !== OPTION_VALUES.DATE_RANGE}
              locale="de"
              localeUtils={MomentLocaleUtils}
              dateRangevalue={[filterValues.fromDate!, filterValues.toDate!]}
            />
          </Flex>
        </Flex>
        <Flex mb={8}>
          <MultiSelectParameters
            labParameters={labParameters}
            selectedItems={filterValues.fieldIds!}
            onSetSelectedItems={(items) =>
              handleChangeFilters('fieldIds', items)
            }
          />

          <Flex align="center">
            <Switch
              className="sl-switch"
              checked={filterValues.pathological}
              onChange={() => {
                handleChangeFilters('pathological', !filterValues.pathological);
              }}
              label="Nur pathologische Ergebnisse"
            />
          </Flex>
        </Flex>
      </Flex>
      <Divider className={'divider'} />
      <Flex className="sl-right-side">
        <div className={'form-button'}>
          <Button
            className="sl-btn-print"
            onClick={handlePrintPdf}
            loading={isLoadingPdfResultsReview}
          >
            <Icon icon="print" />
          </Button>
        </div>
        {/* <Popover
          interactionKind={PopoverInteractionKind.CLICK}
          popoverClassName="sl-popover-create-form"
          position={Position.BOTTOM_RIGHT}
          usePortal={false}
          content={
            <div>
              {listLabForms(t).map((item) => (
                <span
                  key={item.formName}
                  className="sl-form-create-menu-item"
                  onClick={() => handleCreateFormClick(item.formName)}
                >
                  {item.text}
                </span>
              ))}
            </div>
          }
        >
          <Button intent={Intent.PRIMARY}>
            <Icon icon="caret-down" />
          </Button>
        </Popover> */}
      </Flex>
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(FormFilters, {
    namespace: 'Lab',
    nestedTrans: 'LabScreen',
  })
);
