import React, { memo, useState } from 'react';
import moment from 'moment';
import { LocaleUtils } from 'react-day-picker';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import I18n from '@tutum/infrastructure/i18n';
import {
  InputGroup,
  Intent,
  Popover,
} from '@tutum/design-system/components/Core';
import {
  DateRangePicker,
  DateRange,
} from '@tutum/design-system/components/DateTime';
import { Flex, Svg } from '@tutum/design-system/components';
import { IMvzTheme } from '@tutum/mvz/theme';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';

export interface IDateRangeInputProps {
  className?: string;
  theme?: IMvzTheme;
  onChange: (startDate: Date, endDate: Date) => void;
  locale?: string;
  localeUtils?: typeof LocaleUtils;
  intent?: Intent;
  dateRangevalue?: [Date, Date];
  disabled?: boolean;
}

const CalendarIcon = '/images/calendar-schein.svg';

export const dateRangeString = (startDate: Date, endDate: Date) => {
  if (!startDate && !endDate) {
    return '';
  }
  const startDateFormated = moment(startDate).format(DATE_FORMAT);
  const endDateFormated = moment(endDate).format(DATE_FORMAT);

  return `${startDateFormated}${endDate ? ` - ${endDateFormated}` : ''}`;
};

const DateRangeInput = ({
  className,
  onChange,
  locale,
  localeUtils,
  intent,
  dateRangevalue,
  disabled,
}: IDateRangeInputProps) => {
  const { t: tCommon } = I18n.useTranslation<
    keyof typeof CommonLocales.DateTimePicker
  >({
    namespace: 'Common',
    nestedTrans: 'DateTimePicker',
  });

  const [openCalendar, setOpenCalendar] = useState(false);

  const handleOpenCalendar = () => {
    setOpenCalendar(true);
  };

  const handleCloseCalendar = () => {
    setOpenCalendar(false);
  };

  const handleOnChange = (selectedDates: DateRange) => {
    const [fromDate, toDate] = selectedDates;
    onChange(fromDate!, toDate!);
    if (toDate) {
      handleCloseCalendar();
    }
  };

  return (
    <Flex auto className={className} align="center">
      <Flex className="sl-date-range">
        <Popover
          content={
            <DateRangePicker
              singleMonthOnly
              allowSingleDayRange
              onChange={handleOnChange}
              locale={locale}
              localeUtils={localeUtils}
              value={dateRangevalue}
            />
          }
          isOpen={openCalendar}
          hasBackdrop
          backdropProps={{ onClick: handleCloseCalendar }}
        >
          <InputGroup
            onClick={handleOpenCalendar}
            autoComplete="off"
            disabled={disabled}
            rightElement={<Svg src={CalendarIcon} className="calendar" />}
            placeholder={tCommon('fromTo')}
            intent={intent}
            value={dateRangeString(dateRangevalue?.[0]!, dateRangevalue?.[1]!)}
          />
        </Popover>
      </Flex>
    </Flex>
  );
};

export default memo(DateRangeInput);
