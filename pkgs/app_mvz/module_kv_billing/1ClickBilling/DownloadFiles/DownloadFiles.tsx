import React from 'react';
import { Box, BodyTextM, Flex, Svg } from '@tutum/design-system/components';
import Link from 'next/link';
import { IKvBillingFile, IKvBillingSummary } from '../../KVBilling.store';
import { DMPBillingFileWitFullURL } from '../../../module_dmp-billing/dmp.helper';
import { getPresignedGetURL } from '@tutum/hermes/bff/legacy/app_mvz_file';

interface DownloadFileProps {
  files: IKvBillingSummary[] | IKvBillingFile[] | DMPBillingFileWitFullURL[];
  description?: string;
}
const downloadIcon = '/images/download.svg';
const pdfIcon = '/images/pdf-icon.svg';
const textIcon = '/images/file-text.svg';

export default ({ files, description }: DownloadFileProps) => {
  const fileRemapping = files
    .map((file) => {
      const url = file.url ?? file.fileDownURL ?? file.filePath;
      let title = file.fileTitle ?? file.name ?? file.fileName;
      if (title === 'KVDT_con_file_encrypted.con.XKM') {
        title = file.filePath.split('__')[1];
      }
      return {
        ...file,
        url,
        title,
      };
    })
    .sort((a, b) => {
      return a.title.localeCompare(b.title);
    });
  const selectIcon = (file) => {
    if (!file.icon) {
      const title = file.fileTitle ?? file.name ?? file.fileName;

      if (title.endsWith('.pdf')) {
        return <Svg src={pdfIcon} style={{ marginLeft: '-2px' }} />;
      }
      return <Svg src={textIcon} />;
    }

    if (typeof file.icon === 'string') {
      return <Svg src={file.icon} />;
    }

    return file.icon;
  };

  return (
    <>
      <Box className="titleFiles" mb="16px">
        <BodyTextM>{description}</BodyTextM>
      </Box>
      <Box ml="9px">
        {fileRemapping.map((file) => {
          return (
            <Link
              onClick={async (e) => {
                if (file?.filePath) {
                  //handle one click billing case
                  e.preventDefault();
                  const [bucketName, fileName] = file.filePath.split('/');
                  const fileNameWithoutUUID = fileName.split('__')[1];
                  const result = await getPresignedGetURL({
                    bucketName: bucketName,
                    objectName: fileName,
                    header: fileNameWithoutUUID
                      ? {
                        'response-content-disposition': `attachment; filename="${fileNameWithoutUUID}"`,
                      }
                      : undefined,
                  });
                  window.open(result.data.presignedURL, '_blank');
                }
              }}
              href={file.url}
              key={file.title}
              target="_blank"
              rel="noreferrer"
            >
              <Flex className="fileItem" mb="6px" align="center">
                {selectIcon(file)}
                <Box m="0 10px">{file.displayName ?? file.title}</Box>
                <Svg src={downloadIcon} />
              </Flex>
            </Link>
          );
        })}
      </Box>
    </>
  );
};
