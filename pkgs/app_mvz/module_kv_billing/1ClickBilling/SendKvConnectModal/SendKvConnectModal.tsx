import React, { memo, useEffect, useContext, useState } from 'react';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { Formik, Field, Form, FastField } from 'formik';
import isEmpty from 'lodash/isEmpty';
import {
  Flex,
  Button,
  MainDialog,
  FormGroup2,
  ReactSelect,
  IMenuItem,
  Svg,
  Tooltip,
  Box,
  MultiSelect,
  alertSuccessfully,
  alertError,
} from '@tutum/design-system/components';
import {
  Divider,
  Radio,
  RadioGroup,
  Checkbox,
  InputGroup,
} from '@tutum/design-system/components/Core';
import I18n from '@tutum/infrastructure/i18n';
import type BillingI18n from '@tutum/mvz/locales/en/Billing.json';
import type Common from '@tutum/mvz/locales/en/Common.json';
import { useKvBillingStore } from '@tutum/mvz/module_kv_billing/KVBilling.store';
import { TypeOfBilling } from '@tutum/hermes/bff/billing_history_common';
import {
  CreateRequest,
  GetSettingForSendingResponse,
  SendToMailType,
} from '@tutum/hermes/bff/billing_history';

import { FIELD_NAMES, INIT_VALUES } from '../1ClickBilling.const';
import { kvBillingActions } from '../../KVBilling.store';
import {
  convertYearQuarterToMenuItem,
  getInitQuarters,
  onValidate,
} from '../1ClickBilling.helper';
import { BSNRName } from '@tutum/hermes/bff/bsnr_common';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useQueryGetSettingForSending } from '@tutum/hermes/bff/legacy/billing_history';
import { MailAccountDto } from '@tutum/hermes/bff/mail_common';
import { Environment } from '@tutum/infrastructure/shared/env';
import { FeatureFlagKey } from '@tutum/hermes/bff/legacy/feature_flag';
import { FFWrapper } from '@tutum/mvz/hooks/useFeatureFlag';

const alertCircleSolid = '/images/information-grey-circle.svg';

export interface Send1ClickModalProps {
  isOpen: boolean;
  kvConnectId?: string;
  kvBillingId: string;
  correctBilling: {
    quarter: string;
    bsnrId: string;
    showPreviousQuarter?: boolean;
  };
  sendVia: SendToMailType;
  className?: string;
  isMultipleQuarter?: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const SendKvConnectModal = ({
  isOpen,
  kvConnectId,
  kvBillingId,
  correctBilling = { quarter: '', bsnrId: '', showPreviousQuarter: false },
  className,
  sendVia,
  isMultipleQuarter,
  onClose,
  onSuccess,
}: Send1ClickModalProps) => {
  const toast = useToaster();

  const patientContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = patientContext.useGetLoggedInUserProfile();
  const { bsnrs, billingSelection, quarters } = useKvBillingStore();
  const { year, quarter } = billingSelection;
  const { t } = I18n.useTranslation<keyof typeof BillingI18n['1ClickBilling']>({
    namespace: 'Billing',
    nestedTrans: '1ClickBilling',
  });
  const { t: tCommon } = I18n.useTranslation<
    keyof typeof Common.FormValidation
  >({
    namespace: 'Common',
    nestedTrans: 'FormValidation',
  });

  const [initValues, setInitValues] = useState(INIT_VALUES);

  const host =
    typeof window != 'undefined' ? window?.location?.hostname : undefined;

  let initBsnr: BSNRName | undefined = undefined;
  let initQuarter: IMenuItem[] = [];

  if (isEmpty(correctBilling.bsnrId)) {
    initBsnr = bsnrs[0];
  } else {
    initBsnr = bsnrs.find((bsnr) => bsnr.id === correctBilling.bsnrId);
  }

  const { data: kimAccounts } = useQueryGetSettingForSending(
    { bsnr: initBsnr?.code! },
    { enabled: Boolean(initBsnr?.code) }
  );

  useEffect(() => {
    if (isEmpty(correctBilling.bsnrId)) {
      initBsnr = bsnrs[0];
      const initYearQuarter = getInitQuarters(
        quarters,
        { year: year!, quarter: quarter! },
        !!isMultipleQuarter
      );

      initQuarter = convertYearQuarterToMenuItem(initYearQuarter);
    } else {
      initBsnr = bsnrs.find((bsnr) => bsnr.id === correctBilling.bsnrId);
      const quarterCorrection = +correctBilling.quarter.split('-')[0];
      const yearCorrection = +correctBilling.quarter.split('-')[1];
      const initYearQuarter = getInitQuarters(
        quarters,
        { year: yearCorrection, quarter: quarterCorrection },
        !!isMultipleQuarter
      );

      initQuarter = convertYearQuarterToMenuItem(initYearQuarter);
    }

    let preselectKim = '';
    if (kimAccounts?.fromAccounts.length === 1) {
      preselectKim = kimAccounts.fromAccounts[0].email;
    } else if (kimAccounts) {
      const userAccounts: MailAccountDto[] = [];
      const bsnrAccounts: MailAccountDto[] = [];

      for (const account of kimAccounts.fromAccounts) {
        if (account.assignedPractice) {
          bsnrAccounts.push(account);
        }
        if (account.assignedUserId) {
          userAccounts.push(account);
        }
      }
      if (bsnrAccounts.length > 0) {
        preselectKim = bsnrAccounts[0].email;
      } else {
        preselectKim =
          userAccounts.find(
            (acc) => acc.assignedUserId === currentLoggedinUser?.id
          )?.email || '';
      }
    }

    setInitValues({
      ...initValues,
      bsnr: initBsnr?.id!,
      quarter: initQuarter,
      kimAccount: preselectKim,
      markAsCompletedBilling: false,
    });
  }, [initBsnr, isMultipleQuarter, correctBilling, currentLoggedinUser]);

  const sendMails = async (values) => {
    const time = values.quarter;

    for (const yearQuarter of time) {
      const oneClickPayload: CreateRequest = {
        quarterMonth: +yearQuarter.split('-')[0],
        quarterYear: +yearQuarter.split('-')[1],
        bsnrId: values.bsnr,
        typeOfBilling: correctBilling.bsnrId
          ? TypeOfBilling.TypeOfBilling_Send_As_Correction_Billing
          : values.typeOfBilling,
        markAsCompletedBilling: values.markAsCompletedBilling,
        kvConnectId: sendVia === SendToMailType.KV ? kvConnectId : undefined,
        billingKvId: kvBillingId,
        sendToMailType: sendVia,
        testBody: values.testBody,
        testMailTo: values.testMailTo,
        kimSendFrom: values.kimAccount,
        showPreviousQuarter: !!correctBilling.showPreviousQuarter,
      };
      try {
        await kvBillingActions.createBillingHistory(oneClickPayload);
        alertSuccessfully(t('billSent'), { toaster: toast });
      } catch (e) {
        alertError(
          e.response?.data?.serverError
            ? t(e.response.data.serverError)
            : `Failed: ${e.message}`,
          { toaster: toast }
        );
      }
    }
  };
  const onSubmitForm = async (
    values: typeof INIT_VALUES,
    { setSubmitting, resetForm }
  ) => {
    if (!values.quarter.length) return;
    try {
      await sendMails(values);
      onClose();
      onSuccess?.();
    } finally {
      setSubmitting(false);
      resetForm();
    }
  };

  const isDisableTypeofBilling = (accounts?: GetSettingForSendingResponse) => {
    if (correctBilling.bsnrId) {
      return { realBill: false, testBillBill: true };
    }
    if (sendVia === SendToMailType.Kim) {
      return {
        realBill: !accounts?.sendToAddress.kimEmail,
        testBillBill: !accounts?.sendToAddress.kimTestEmail,
      };
    }

    return { realBill: false, testBillBill: false };
  };

  const titleKvOrKim =
    sendVia === SendToMailType.Kim ? t('sendViaKim') : t('titleModal');

  const shouldDisableBsnr =
    !!correctBilling.bsnrId || sendVia === SendToMailType.Kim;

  return (
    <Formik
      initialValues={initValues}
      onSubmit={onSubmitForm}
      validate={onValidate({ tCommon, sendVia })}
      validateOnBlur={true}
      validateOnChange={true}
      enableReinitialize
    >
      {({ handleSubmit, submitCount, errors, touched, isSubmitting }) => (
        <Form>
          <MainDialog
            className={className}
            isOpen={isOpen}
            title={titleKvOrKim}
            onClose={onClose}
            actions={
              <>
                <Button
                  intent="primary"
                  outlined
                  minimal
                  onClick={onClose}
                  loading={isSubmitting}
                >
                  {t('cancelSend')}
                </Button>
                <Button
                  intent="primary"
                  disabled={!bsnrs.length}
                  onClick={() => handleSubmit()}
                  loading={isSubmitting}
                >
                  {titleKvOrKim}
                </Button>
              </>
            }
          >
            <Flex w="100%" column gap={16}>
              <FormGroup2
                label={t('Quarter')}
                name={FIELD_NAMES.QUARTER}
                isRequired
              >
                <Field name={FIELD_NAMES.QUARTER}>
                  {({ field, form }) => {
                    const listQuarter = convertYearQuarterToMenuItem(
                      getInitQuarters(
                        quarters,
                        {
                          year: billingSelection.year!,
                          quarter: billingSelection.quarter!,
                        },
                        !!isMultipleQuarter
                      )
                    );
                    return (
                      <MultiSelect
                        menuPosition="absolute"
                        isSearchable={false}
                        value={listQuarter.filter((option) =>
                          field.value?.includes(option.value)
                        )}
                        options={listQuarter}
                        onChange={(newValue: IMenuItem[]) => {
                          form.setFieldValue(
                            field.name,
                            newValue.map((item) => item.value)
                          );
                        }}
                      />
                    );
                  }}
                </Field>
              </FormGroup2>

              <FormGroup2
                label={t('Practice')}
                name={FIELD_NAMES.PRACTICE}
                submitCount={submitCount}
                errors={errors}
                touched={touched}
                isRequired
              >
                <FastField name={FIELD_NAMES.PRACTICE}>
                  {({ field, form }) => (
                    <ReactSelect
                      isSearchable={false}
                      selectedValue={field.value}
                      items={bsnrs.map((b) => ({
                        label: b.name,
                        value: b.id ?? '',
                      }))}
                      isDisabled={shouldDisableBsnr}
                      onItemSelect={(item: IMenuItem) => {
                        form.setFieldValue(field.name, item?.value);
                      }}
                    />
                  )}
                </FastField>
              </FormGroup2>
              {sendVia === SendToMailType.Kim && (
                <FormGroup2
                  label={t('KIMAccount')}
                  name={FIELD_NAMES.KIM_ACCOUNT}
                  errors={errors}
                  touched={touched}
                  isRequired
                >
                  <FastField name={FIELD_NAMES.KIM_ACCOUNT}>
                    {({ field, form }) => (
                      <ReactSelect
                        isSearchable={false}
                        selectedValue={field.value}
                        items={(kimAccounts?.fromAccounts || []).map((account) => ({
                          value: account.email,
                          label: `${account.email} (${account.assignedPractice
                            ? account.assignedPractice
                            : patientContext.getDoctorName(
                              account.assignedUserId!
                            )
                            })`,
                        }))}
                        onItemSelect={(item: IMenuItem) => {
                          form.setFieldValue(field.name, item?.value);
                        }}
                      />
                    )}
                  </FastField>
                </FormGroup2>
              )}
              <FFWrapper ffKey={FeatureFlagKey.FeatureFlagKey_1ClickBilling}>
                <FormGroup2
                  label={t('testMailTo')}
                  name={FIELD_NAMES.TEST_MAIL_TO}
                  submitCount={submitCount}
                  errors={errors}
                  touched={touched}
                >
                  <FastField name={FIELD_NAMES.TEST_MAIL_TO}>
                    {({ field }) => (
                      <InputGroup {...field} id={FIELD_NAMES.TEST_MAIL_TO} />
                    )}
                  </FastField>
                </FormGroup2>
                <FormGroup2
                  label={t('testBody')}
                  name={FIELD_NAMES.TEST_BODY}
                  submitCount={submitCount}
                  errors={errors}
                  touched={touched}
                >
                  <FastField name={FIELD_NAMES.TEST_BODY}>
                    {({ field }) => {
                      return (
                        <InputGroup {...field} id={FIELD_NAMES.TEST_BODY} />
                      );
                    }}
                  </FastField>
                </FormGroup2>
              </FFWrapper>

              <Divider />
              <FormGroup2
                label={t('TypeOfBilling')}
                name={FIELD_NAMES.BILLING_TYPE}
                errors={errors}
                submitCount={submitCount}
                isRequired
              >
                <FastField name={FIELD_NAMES.BILLING_TYPE}>
                  {({ field, form }) => (
                    <RadioGroup
                      onChange={(e) =>
                        form.setFieldValue(field.name, e.currentTarget.value)
                      }
                      selectedValue={field.value}
                    >
                      {sendVia === SendToMailType.Kim &&
                        isDisableTypeofBilling(kimAccounts).realBill ? (
                        <Tooltip position="top" content={t('notSupportKv')}>
                          <Radio
                            labelElement={
                              <Box style={{ display: 'inline-block' }}>
                                <Flex align="flex-end">
                                  {t('SendRealBilling')}
                                  &nbsp;
                                  <Svg
                                    src={alertCircleSolid}
                                    style={{ paddingTop: 3 }}
                                  />
                                </Flex>
                              </Box>
                            }
                            disabled={true}
                            value={
                              TypeOfBilling.TypeOfBilling_Send_As_Real_Billing
                            }
                          />
                        </Tooltip>
                      ) : (
                        <Radio
                          label={t('SendRealBilling')}
                          value={
                            TypeOfBilling.TypeOfBilling_Send_As_Real_Billing
                          }
                        />
                      )}
                      <Radio
                        label={t('SendTestBilling')}
                        disabled={
                          isDisableTypeofBilling(kimAccounts).testBillBill
                        }
                        value={
                          TypeOfBilling.TypeOfBilling_Send_As_Real_Test_Billing
                        }
                      />
                    </RadioGroup>
                  )}
                </FastField>
              </FormGroup2>
              <Divider />
              <FormGroup2 name={FIELD_NAMES.IS_BILLING_COMPLETE}>
                <FastField name={FIELD_NAMES.IS_BILLING_COMPLETE}>
                  {({ field, form }) => (
                    <Checkbox
                      label={t('MarkComplete')}
                      checked={field.value}
                      onChange={() =>
                        form.setFieldValue(field.name, !field.value)
                      }
                    />
                  )}
                </FastField>
              </FormGroup2>
            </Flex>
          </MainDialog>
        </Form>
      )}
    </Formik>
  );
};

export default memo(SendKvConnectModal);
