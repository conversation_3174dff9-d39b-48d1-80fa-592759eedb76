import { isEmpty } from 'lodash';
import dynamic from 'next/dynamic';
import { NextRouter, withRouter } from 'next/router';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import {
  alertSuccessfully,
  BodyTextM,
  Flex,
  H1,
  LoadingState,
  Svg,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import {
  ButtonGroup,
  Divider,
  OverlayToaster,
  Tooltip,
} from '@tutum/design-system/components/Core';
import { getCssClass, unique } from '@tutum/design-system/infrastructure/utils';
import { getError } from '@tutum/hermes/bff/legacy/app_mvz_billing_kv';
import { GetEbmsRequest } from '@tutum/hermes/bff/app_mvz_patient_encounter';
import { useQueryGetPatientParticipation } from '@tutum/hermes/bff/legacy/app_mvz_patient_participation';
import {
  PersonalInfo,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import { EncounterCase, MainGroup } from '@tutum/hermes/bff/schein_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import datetimeUtil, { now } from '@tutum/infrastructure/utils/datetime.util';
import PatientManagementUtil from '@tutum/infrastructure/utils/form.util';
import { checkIsKvSchein } from '@tutum/mvz/_utils/scheinFormat';
import { SV_BILLING_KEY_LOCAL_STORAGE } from '@tutum/mvz/constant/general';
import GlobalContext, {
  IGlobalContext,
} from '@tutum/mvz/contexts/Global.context';
import { bsnrActions } from '@tutum/mvz/hooks/useBsnr.store';
import { formTranslationActions } from '@tutum/mvz/hooks/useFormTranslation';
import useToaster from '@tutum/mvz/hooks/useToaster';
import {
  useValidationBillingStore,
  validationBillingActions,
} from '@tutum/mvz/hooks/useValidationBilling';
import type BillingLocales from '@tutum/mvz/locales/en/Billing.json';
import { ActionChainPanel } from '@tutum/mvz/module_action-chain';
import { actionChainActions } from '@tutum/mvz/module_action-chain/use-action-chain';
import { useKvBillingStore } from '@tutum/mvz/module_kv_billing/KVBilling.store';
import Schein from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.styled';
import CreateSvSchein from '@tutum/mvz/module_kv_hzv_schein/CreateSVSchein/CreateSchein.styled';
import { IMedicationContext } from '@tutum/mvz/module_medication/context/MedicationContext';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import CreatePatient from '@tutum/mvz/module_patient-management/create-patient-v2/CreatePatient.styled';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import { getInsuranceCardByQuarter } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import Allergies from '@tutum/mvz/module_patient-management/patient-file/allergies/Allergies.styled';
import Cave from '@tutum/mvz/module_patient-management/patient-file/cave/Cave.styled';
import {
  EncounterHook,
  GetGroupContractDoctorRequest,
} from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/Encounter.hook';
import { useActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import MedicalData from '@tutum/mvz/module_patient-management/patient-file/medical-data/MedicalData.styled';
import PatientInformation from '@tutum/mvz/module_patient-management/patient-file/patient-information/PatientInformation.styled';
import PermanentDiagnose from '@tutum/mvz/module_patient-management/patient-file/permanent-diagnose/PermanentDiagnose.styled';
import ScheinHistory from '@tutum/mvz/module_patient-management/patient-file/schein-history';
import { SourceScreen } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.hooks';
import Timeline from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.styled';
import { IContractInfo } from '@tutum/mvz/module_patient-management/types/contract.type';
import { ReloadQuarterFunc } from '@tutum/mvz/module_patient-management/types/timeline.type';
import CreatePrivateScheinModal from '@tutum/mvz/module_private_igel_schein';
import { IMvzTheme } from '@tutum/mvz/theme';
import type { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import ValidationList from '../validation-list';
import ValidationListHzv from './validation-list-hzv';
import type { FORM_SECTION } from '@tutum/mvz/module_kv_hzv_schein/FormContent.helper';
import {
  reportActions,
  useReportStore,
} from '@tutum/mvz/module_reports/Report.store';

const MedicationProvider: React.ComponentType<
  React.PropsWithChildren<{
    value: IMedicationContext;
  }>
> = dynamic(
  () => import('@tutum/mvz/module_medication/context/MedicationProvider'),
  {
    loading() {
      return <LoadingState />;
    },
    ssr: false,
  }
);

const IconBack = '/images/back.svg';
const AlertTriangleOutline = '/images/alert-triangle-outline.svg';
const ArrowLeft = '/images/arrow-left-gray.svg';
const ArrowRight = '/images/arrow-right-gray.svg';
const CodingRulesInfo = '/images/icon-info.svg';

export interface IPatientFileProps {
  className?: string;
  theme?: IMvzTheme;
  viewPatientId?: string;
  billingQuarter?: Array<{ year: number; quarter: number }>;
  isCodingRules?: boolean;
  isSvBilling?: boolean;
}

export const toasterRef = React.createRef<OverlayToaster>();

type P = IPatientFileProps &
  IGlobalContext &
  II18nFixedNamespace & { router: NextRouter };

const PatientFile: React.FC<P> = (props) => {
  const {
    className,
    viewPatientId,
    billingQuarter,
    t,
    router,
    isCodingRules,
    isSvBilling,
  } = props;

  const { t: tPatientValidation } = I18n.useTranslation<
    keyof typeof BillingLocales.PatientValidation
  >({
    namespace: 'Billing',
    nestedTrans: 'PatientValidation',
  });
  const { t: tForm } = I18n.useTranslation({
    namespace: 'Form',
  });

  const reportStore = useReportStore();
  const patientFileStore = usePatientFileStore();
  const { billingKvHistoryId } = useKvBillingStore();
  const { fixValidationPatientId, validationList, errorList } =
    useValidationBillingStore();
  const { setFixValidationPatientId, setErrorList } = validationBillingActions;
  const timelineRef = useRef<{ reloadQuarters: ReloadQuarterFunc }>(null);
  const toast = useToaster();
  const {
    patientManagement,
    setPatientId,
    setMedicalData,
    setMedicalDataUpdatedAt,
    setSelectedContractDoctor,
  } = useContext(PatientManagementContext.instance);
  const [isOpenSchein, setIsOpenSchein] = useState<boolean>(false);
  const [showCreateSchein, setShowCreateSchein] = useState<any>(undefined);
  const [scheinIdToEdit, setScheinIdToEdit] = useState<string | undefined>(undefined);
  const [isOpenValidationList, setIsOpenValidationList] = useState(
    !isCodingRules
  );
  const [listPatient, setListPatient] = useState<string[]>([]);
  const [currentPatientIndex, setCurrentPatientIndex] = useState(0);
  const [listErrorHzv, setListErrorHzv] = useState<any[]>([]);

  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();
  const currentSchein = useCurrentSchein();
  const patientId = listPatient[currentPatientIndex];
  const [requestGetGroupContractDoctor, setRequestGetGroupContractDoctor] =
    useState<GetGroupContractDoctorRequest | undefined>(undefined);
  const isEditPatientOrSchein =
    patientFileStore.isEditingProfile || isOpenSchein;
  const currentValidation = validationList.find(
    (datum) => datum.patientId === patientId
  );
  const [scrollToSection, setScrollToSection] = useState<FORM_SECTION>();
  const [checkDate, setCheckDate] = useState<number | null>(null);
  const { encounterDate } = useActionBarStore();

  useEffect(() => {
    const edit = Boolean(router?.query.edit);
    if (edit) {
      patientFileActions.setIsEditPatientProfile(true);
    }
    patientFileActions.materialCosts.getMaterialCosts();

    let listPatientId = validationList
      ?.filter((item) => !item.isChild && item.patientId)
      ?.map((item) => item.patientId!);
    let patientIndex = listPatientId?.findIndex(
      (item) => item === fixValidationPatientId
    );

    if (reportStore.patientIds.length > 0) {
      listPatientId = reportStore.patientIds;
      patientIndex = listPatientId?.findIndex(
        (item) => item === reportStore.patientId
      );
    }

    setListPatient(listPatientId);
    setCurrentPatientIndex(patientIndex);
    return () => {
      setFixValidationPatientId(null!);
      setErrorList([]);
      patientFileActions.resetStore();
      setIsOpenSchein(false);
      patientFileActions.setIsEditPatientProfile(false);
    };
  }, []);

  useEffect(() => {
    setCheckDate(now());
  }, []);

  const { data: responseContracts, isLoading: loadingContracts } =
    useQueryGetPatientParticipation(
      {
        checkDate: checkDate!,
        patientId: patientId,
      },
      {
        enabled: !!patientId && !!checkDate,
        select(data: { data }) {
          const newResult: IContractInfo[] = data.data.participations.map(
            (participation) => {
              return {
                doctorId: String(participation.doctorId),
                id: participation.contractId,
                chargeSystemId: participation.chargeSystemId,
                role: participation.doctorFunctionType,
                status: participation.status,
                type: participation.contractType,
              };
            }
          );

          return newResult;
        },
      }
    );

  const getErrors = async (billingKvHistoryId, patientId: string) => {
    if (!patientId) return;
    const { data } = await getError({
      billingKvHistoryId: billingKvHistoryId,
      patientId: patientId,
    });
    const { fileContentDetails, groupErrorByPatientDetailErrors } = data;
    patientFileActions.setListErrors(fileContentDetails || []);
    patientFileActions.setListHints(groupErrorByPatientDetailErrors || []);
  };

  useEffect(() => {
    if (!patientId) return;

    setPatientId(patientId);
  }, [patientId]);

  useEffect(() => {
    if (!patientId || !billingKvHistoryId) return;
    patientFileActions.schein.resetSchein();
    getErrors(billingKvHistoryId, patientId);
  }, [billingKvHistoryId, patientId]);

  // Hzv/Fav has contracts
  useEffect(() => {
    if (!responseContracts) {
      return;
    }
    const contractIds = responseContracts?.map((c) => c.id);
    setRequestGetGroupContractDoctor({
      contractIds: [...contractIds],
      encounterDate: now(),
    });
    const storagedData = localStorage.getItem(SV_BILLING_KEY_LOCAL_STORAGE);
    if (storagedData) {
      const errorLst = JSON.parse(storagedData ?? `{}`);
      if (isEmpty(errorLst)) {
        setListErrorHzv(errorList);
      } else {
        setListErrorHzv(errorLst);
      }
    } else {
      setListErrorHzv(errorList);
    }
  }, [responseContracts]);

  const groupContractDoctor = EncounterHook.useGetContractDoctorGroup(
    requestGetGroupContractDoctor
  );

  // handle KV case: will not have contract
  useEffect(() => {
    if (checkIsKvSchein(currentSchein)) {
      const defaultSelectedDoctorId =
        doctorList && doctorList.length > 0 ? doctorList[0].id : undefined;
      const bsnrId = globalContext.getBsnrIdByDoctorId(defaultSelectedDoctorId);
      const selectedContractDoctor: ISelectedContractDoctor = {
        bsnrId,
        doctorId: defaultSelectedDoctorId,
        contractId: undefined,
        chargeSystemId: undefined,
        availableDoctor: doctorList,
        encounterCase: EncounterCase.AB,
      };
      setSelectedContractDoctor(selectedContractDoctor);
    }
  }, [currentSchein]);

  // handle SV case
  useEffect(() => {
    if (
      currentSchein &&
      currentSchein.hzvContractId &&
      groupContractDoctor?.doctorParticipateContracts?.length
    ) {
      // NOTE: edge case - pre-enrollment has schein but not reload participated contracts
      if (!loadingContracts && !responseContracts?.length) {
        return;
      }

      const firstDefaultContractId = currentSchein.hzvContractId;
      const firstDefaultContract = (responseContracts || []).find(
        (contract) => contract.id === firstDefaultContractId
      );

      const doctorParticipateContract =
        groupContractDoctor?.doctorParticipateContracts?.find((c) => {
          return c.contractID === firstDefaultContractId;
        });
      const doctors = unique(
        (doctorParticipateContract?.doctors || []).map((doc) => doc.doctorId)
      ).map((docId) => {
        return globalContext.getDoctorById(docId)!;
      });

      catalogOverviewActions
        .getContractMeta(datetimeUtil.nowInUnix(), firstDefaultContractId)
        .then((contractMetadata) => {
          const activeContracts = patientManagement.activeParticipations;
          if (activeContracts.length === 0) return;

          // check based on chein
          let contract = activeContracts?.find(
            (con) => con.contractId === currentSchein.hzvContractId
          );
          // edge of edge case Hzv/FaV active and not active
          if (!contract) {
            contract = activeContracts[0];
          }

          const doctorId = contract.doctorId;
          const availableChargeSystems: string[] = [];
          const availableModuleChargeSystems: string[] = [];
          doctorParticipateContract?.doctors
            ?.filter((doc) => doc.doctorId === doctorId)
            .forEach((doc) => {
              if (
                contractMetadata.chargeSystems?.indexOf(doc.chargeSystemId) !==
                -1
              ) {
                availableChargeSystems.push(doc.chargeSystemId);
              }
              if (
                contractMetadata.moduleChargeSystems?.indexOf(
                  doc.chargeSystemId
                ) !== -1
              ) {
                availableModuleChargeSystems.push(doc.chargeSystemId);
              }
            });
          const bsnrId = globalContext.getBsnrIdByDoctorId(doctorId);
          setSelectedContractDoctor({
            bsnrId,
            doctorId: doctorId,
            contractId: firstDefaultContract?.id,
            chargeSystemId: firstDefaultContract?.chargeSystemId,
            moduleChargeSystemId: availableModuleChargeSystems[0],
            availableDoctor: doctors,
            encounterCase: EncounterCase.AB,
          });
        });
    }
  }, [currentSchein, groupContractDoctor, loadingContracts, responseContracts]);

  useEffect(() => {
    if (!groupContractDoctor || !responseContracts) {
      return;
    }

    const defaultSelectedDoctorId =
      doctorList && doctorList.length > 0 ? doctorList[0].id : undefined;
    const bsnrId = globalContext.getBsnrIdByDoctorId(defaultSelectedDoctorId);
    // default is KV without contract, all doctors system
    let selectedContractDoctor: ISelectedContractDoctor = {
      bsnrId,
      doctorId: defaultSelectedDoctorId,
      contractId: undefined,
      chargeSystemId: undefined,
      availableDoctor: doctorList,
      encounterCase: EncounterCase.AB,
    };
    if (responseContracts.length > 0 && !checkIsKvSchein(currentSchein)) {
      // HVZ/FAV
      const mapContractDoctors = new Map<string, IEmployeeProfile[]>();
      groupContractDoctor?.doctorParticipateContracts?.forEach((c) => {
        const contractDoctors: IEmployeeProfile[] = [
          ...(mapContractDoctors.get[c.contractID] ?? []),
          ...c.doctors.map((d) => {
            return globalContext.getDoctorById(d.doctorId);
          }),
        ];
        const distinctResult: IEmployeeProfile[] = unique(contractDoctors);
        mapContractDoctors.set(c.contractID, distinctResult);
      });

      const firstDefaultContract = responseContracts[0];
      const doctors = Object.values(
        mapContractDoctors.get(firstDefaultContract.id) || []
      );
      selectedContractDoctor = {
        bsnrId: bsnrId,
        doctorId: defaultSelectedDoctorId,
        contractId: firstDefaultContract.id,
        chargeSystemId: firstDefaultContract.chargeSystemId,
        availableDoctor: doctors,
        encounterCase: EncounterCase.AB,
      };
    }
    setSelectedContractDoctor(selectedContractDoctor);
  }, [responseContracts, groupContractDoctor, currentSchein]);

  useEffect(() => {
    patientFileActions.schein.resetSchein();
    actionChainActions.setProgressInfo({
      patientId,
      doctorId: globalContext?.globalData?.userProfile?.id!,
    });
    formTranslationActions.setTranslate(tForm);
    patientFileActions.loadUserSettings();
    bsnrActions.getListBsnr();
  }, [patientId]);

  useEffect(() => {
    if (patientId && !isEmpty(currentLoggedinUser)) {
      patientFileActions.setHistoryViewPatient({
        patientId,
        doctorId: currentLoggedinUser.id!,
      });
    }
  }, [patientId, currentLoggedinUser.id]);

  const isDisabled = useMemo(() => {
    const cardInsurance = getInsuranceCardByQuarter(
      patientManagement?.patient?.patientInfo?.insuranceInfos,
      datetimeUtil.now()
    );

    const isPublicInsurance =
      cardInsurance?.insuranceType === TypeOfInsurance.Public;

    return !!cardInsurance && isPublicInsurance;
  }, [patientManagement?.patient?.patientInfo?.insuranceInfos]);

  const toggleOpenCreateSchein = (scheinId?: string) => {
    if (!isOpenSchein && scheinId && typeof scheinId == 'string') {
      setScheinIdToEdit(scheinId);
    } else {
      setScheinIdToEdit(undefined);
    }

    setIsOpenSchein(!isOpenSchein);
    setScrollToSection(undefined);
  };

  const openEditSchein = (
    scheinId?: string,
    scrollToSection?: FORM_SECTION
  ) => {
    if (!isOpenSchein && scheinId && typeof scheinId == 'string') {
      setScheinIdToEdit(scheinId);
      setScrollToSection(scrollToSection);
    } else {
      setScheinIdToEdit(undefined);
    }

    setIsOpenSchein(!isOpenSchein);
  };

  const onSaveSuccess = () => {
    alertSuccessfully(t('editSuccessLb'), {
      timeout: TOASTER_TIMEOUT_CUSTOM,
      toaster: toast,
    });
    patientFileActions.setIsEditPatientProfile(false);
  };

  const toggleOpenValidationList = () => {
    setIsOpenValidationList(!isOpenValidationList);
  };

  const getFullName = (personalInfo: PersonalInfo) => {
    const { title, intendWord, lastName, firstName } = personalInfo || {};
    return PatientManagementUtil.getFullName(
      title,
      intendWord,
      lastName,
      firstName
    );
  };

  const handleCheckValidNavigate = () => {
    if (patientFileStore.isEditingProfile) {
      patientFileActions.setIsEditPatientProfile(false);
    }
    if (isOpenSchein) {
      toggleOpenCreateSchein(scheinIdToEdit);
    }
  };

  const handleBackPatient = () => {
    handleCheckValidNavigate();
    setCurrentPatientIndex(currentPatientIndex - 1);
  };

  const handleNextPatient = () => {
    handleCheckValidNavigate();
    setCurrentPatientIndex(currentPatientIndex + 1);
  };

  const renderValidationList = () => {
    if (isSvBilling) {
      return (
        <ValidationListHzv patientId={patientId} listErrors={listErrorHzv} />
      );
    }
    return (
      <ValidationList
        patientId={patientId}
        listErrors={patientFileStore.listErrors}
        listHints={patientFileStore.listHints}
      />
    );
  };

  const handleReload = (payload: { year: number; quarter: number }) => {
    if (timelineRef.current) {
      timelineRef.current.reloadQuarters(payload);
    }
  };

  return (
    <Flex column className={className}>
      <Flex className="sl-validation-header">
        <Flex align="center">
          <Svg
            src={IconBack}
            style={{ marginRight: 12, cursor: 'pointer' }}
            onClick={() => {
              setFixValidationPatientId(null!);
              handleCheckValidNavigate();
              setErrorList([]);
              reportActions.setPatientId('');
              reportActions.setPatientIds([]);
            }}
          />
          <H1>
            {tPatientValidation('fixValidationFor')}{' '}
            {getFullName(patientManagement?.patient?.patientInfo?.personalInfo!)}
          </H1>
        </Flex>
        <Flex align="center">
          <ButtonGroup vertical={false}>
            {isCodingRules ? (
              <Tooltip
                content={
                  <BodyTextM>
                    {tPatientValidation(
                      currentValidation?.codingRules?.length > 1
                        ? 'countsOfCodingRules'
                        : 'countOfCodingRules',
                      {
                        count: currentValidation?.codingRules.length || 0,
                      }
                    )}
                  </BodyTextM>
                }
              >
                <span className="sl-btn-validation sl-btn-validation__coding-rules">
                  <Svg src={CodingRulesInfo} />
                  <span className="sl-badge">
                    {currentValidation?.codingRules.length || 0}
                  </span>
                </span>
              </Tooltip>
            ) : (
              <span
                className="sl-btn-validation"
                onClick={toggleOpenValidationList}
              >
                <Svg src={AlertTriangleOutline} />
                <span className="sl-badge">
                  {isSvBilling
                    ? listErrorHzv.length
                    : patientFileStore?.listErrors?.length}
                </span>
              </span>
            )}
            <Divider style={{ margin: '0 16px' }} />
            <Flex align="center" mr={8}>
              <BodyTextM>
                {tPatientValidation('countOfPatients', {
                  currentPatientIndex: currentPatientIndex + 1,
                  lengthPatient: listPatient.length,
                })}
              </BodyTextM>
            </Flex>
            <span
              className={getCssClass(
                'sl-btn-validation',
                currentPatientIndex === 0 ? 'sl-disabled' : null
              )}
              onClick={handleBackPatient}
              style={{ marginRight: 8 }}
            >
              <Svg src={ArrowLeft} />
            </span>
            <span
              className={getCssClass(
                'sl-btn-validation',
                currentPatientIndex + 1 === listPatient.length
                  ? 'sl-disabled'
                  : null
              )}
              onClick={handleNextPatient}
            >
              <Svg src={ArrowRight} />
            </span>
          </ButtonGroup>
        </Flex>
      </Flex>
      {patientManagement?.patient?.id && patientId && (
        <Flex auto>
          <Flex
            className="side-bar"
            style={{
              display: isEditPatientOrSchein ? 'none' : 'flex',
            }}
          >
            <PatientInformation
              patient={patientManagement.patient}
              activeTabId={ID_TABS.TIMELINE}
              onEdit={() => patientFileActions.setIsEditPatientProfile(true)}
              isDisabled={isDisabled}
              isShowDMPEnrollDialog={false}
              openCreateSchein={toggleOpenCreateSchein}
            />
            <Divider className="side-bar__divider" />
            <Cave patient={patientManagement.patient} />
            <Allergies
              patient={patientManagement.patient}
              setMedicalData={setMedicalData}
              setMedicalDataUpdatedAt={setMedicalDataUpdatedAt}
            />
            <ScheinHistory
              patient={patientManagement.patient}
              patientParticipation={
                patientManagement?.getPatientParticipationResponse
                  ?.participations
              }
              openCreateSchein={toggleOpenCreateSchein}
              isBillingValidation
              reloadQuarters={handleReload}
              setShowCreateSchein={setShowCreateSchein}
            />
            <Divider className="side-bar__divider" />
            <PermanentDiagnose
              billingQuarter={billingQuarter}
              patientId={patientManagement.patient.id}
              contracts={patientManagement.availableHzvContracts}
            />
            <MedicalData
              patient={patientManagement.patient}
              setMedicalData={setMedicalData}
              setMedicalDataUpdatedAt={setMedicalDataUpdatedAt}
              isBillingValidation
            />
          </Flex>
          <Flex
            className="sl-timeline"
            column
            auto
            style={{
              display: isEditPatientOrSchein ? 'none' : 'flex',
            }}
          >
            <MedicationProvider value={{} as IMedicationContext}>
              <Timeline
                ref={timelineRef}
                viewTimelineFromOutside={viewPatientId !== undefined}
                patientId={patientManagement.patient.id}
                isCodingRules={isCodingRules}
                openCreateSchein={(scheinMainGroup?: MainGroup) => {
                  if (!scheinMainGroup) {
                    toggleOpenCreateSchein();
                    return;
                  }

                  setShowCreateSchein(scheinMainGroup);
                }}
                sourceScreen={
                  isCodingRules
                    ? SourceScreen.CODING_RULE_OVERVIEW
                    : SourceScreen.KV_BILLING
                }
                billingQuarter={billingQuarter}
                openEditSchein={openEditSchein}
              />
            </MedicationProvider>
          </Flex>
          {patientFileStore.isEditingProfile ? (
            <CreatePatient
              onClose={() => patientFileActions.setIsEditPatientProfile(false)}
              isOpen={patientFileStore.isEditingProfile}
              selectedPatient={patientManagement?.patient}
              onSaveSuccess={onSaveSuccess}
              isBillingValidation
            />
          ) : null}
          {isOpenSchein && (
            <Schein
              isOpen
              patientId={patientManagement.patientId?.value!}
              id={scheinIdToEdit}
              onClose={toggleOpenCreateSchein}
              isBillingValidation
              reloadQuarters={handleReload}
              isCreateReadCard={false}
              setShowDMPEnrollDialog={() => { }}
              scrollToSection={scrollToSection}
            />
          )}

          {[MainGroup.HZV, MainGroup.FAV].includes(showCreateSchein) && (
            <CreateSvSchein
              isOpen
              patientId={patientManagement.patientId?.value}
              id={scheinIdToEdit}
              isBillingValidation
              isCreateReadCard={false}
              isHzv={showCreateSchein === MainGroup.HZV}
              onClose={() => {
                setShowCreateSchein(null);
                setScheinIdToEdit(undefined);
              }}
              reloadQuarters={handleReload}
            />
          )}

          {[MainGroup.PRIVATE, MainGroup.IGEL].includes(showCreateSchein) && (
            <CreatePrivateScheinModal
              patientId={patientManagement.patientId?.value}
              isOpen
              onClose={() => {
                setShowCreateSchein(null);
                setScheinIdToEdit(undefined);
              }}
              typeSchein={showCreateSchein}
              id={scheinIdToEdit}
            />
          )}
          {isOpenValidationList && renderValidationList()}
        </Flex>
      )}
      <ActionChainPanel />
    </Flex>
  );
};

export default GlobalContext.withContext(
  withRouter(
    I18n.withTranslation(PatientFile, {
      namespace: 'PatientManagement',
      nestedTrans: 'PatientFile',
    })
  )
);
