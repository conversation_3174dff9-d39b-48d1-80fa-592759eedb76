import React from 'react';
import { Avatar, BodyTextM, Flex } from '@tutum/design-system/components';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type CodingRulesI18n from '@tutum/mvz/locales/en/CodingRules.json';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import nameUtils from '@tutum/infrastructure/utils/name.utils';

interface IGenColumnsParams {
  t: IFixedNamespaceTFunction<keyof typeof CodingRulesI18n.tableSetting>;
  doctorList: IEmployeeProfile[];
}

export const MAXIMUM_CURRENT_QUARTERS = 4;

export const genColumns = ({ t, doctorList }: IGenColumnsParams) => [
  {
    id: 1,
    name: t('icdCode'),
    minWidth: '272px',
    cell: (row) => {
      return (
        <BodyTextM>{`(${row.icdCode} ${row.icdCertainty}) ${row.icdDescription}`}</BodyTextM>
      );
    },
  },
  {
    id: 2,
    name: t('suggestions'),
    selector: (row) => row.hint,
    minWidth: '772px',
  },
  {
    id: 3,
    name: t('doctor'),
    minWidth: '200px',
    cell: (row) => {
      const doctor = doctorList.find(
        (doctor) => doctor.id === row.lastDocumentedDoctorId
      );
      const doctorName = nameUtils.getDoctorName(doctor);

      return (
        <Flex>
          <Avatar initial={`${doctor?.initial}`} />
          <BodyTextM margin="0 0 0 8px">{doctorName}</BodyTextM>
        </Flex>
      );
    },
  },
];
