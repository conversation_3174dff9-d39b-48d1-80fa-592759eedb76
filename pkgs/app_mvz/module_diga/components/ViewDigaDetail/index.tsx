import { Icon } from '@blueprintjs/core';
import {
  BodyTextM,
  BodyTextS,
  Box,
  Button,
  Collapse,
  Flex,
  H2,
  Svg,
  Dialog,
} from '@tutum/design-system/components';
import { Divider, Drawer } from '@tutum/design-system/components/Core';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { DigaDetail, DigaStatus } from '@tutum/hermes/bff/legacy/app_mvz_diga';
import I18n from '@tutum/infrastructure/i18n';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import {
  GlobalStyleForDigaPortalMenu,
  renderField as renderFieldDetail,
  renderQuestionaires,
} from '@tutum/mvz/module_diga/components/DigaDetail';
import DigaName from '@tutum/mvz/module_diga/components/DigaName';
import {
  bindIcon,
  bindStatus,
} from '@tutum/mvz/module_diga/components/DigaTable/helper';
import { priceFormat } from '@tutum/mvz/module_diga/utils';
import React, { useState } from 'react';

interface IProps {
  digaDetail: DigaDetail;
  onClose: () => void;
}

const currencyEuro = '/images/currency-euro.svg';
const usb = '/images/usb.svg';
const globe = '/images/globe.svg';

const renderField = (
  label: string,
  value: string,
  fullWidth = false,
  labelWidth?: number
) => {
  return renderFieldDetail(label, value, undefined, fullWidth, labelWidth);
};

const ViewDigaDetailDialog = (props: IProps) => {
  const { digaDetail: diga, onClose } = props;
  const { t } = I18n.useTranslation<keyof typeof DigaListLang.Detail>({
    namespace: 'Diga',
    nestedTrans: 'Detail',
  });

  const [isShowContrandication, setIsShowContrandication] = useState(false);

  return (
    <React.Fragment>
      <GlobalStyleForDigaPortalMenu />
      <Drawer
        className="sl-diga-drawer"
        isOpen={true}
        title={
          <Flex align="center" justify="space-between">
            <Flex align="center" alignSelf="center">
              <Box mr={8}>
                <Svg
                  className="diga-detail__icon_title"
                  src={diga.icon.url}
                  width={40}
                  height={40}
                />
              </Box>
              <Box style={{ flexGrow: 1 }}>
                <DigaName width={420} name={diga.title} fontSize={20} />
                <Flex>
                  <Svg src={bindIcon(diga.status)!} width={16} height={16} />
                  <BodyTextS
                    className="diga-detail__status"
                    color={
                      diga.status === DigaStatus.DIGA_STATUS_ACTIVE
                        ? COLOR.POSITIVE_PRESSED
                        : COLOR.TEXT_WARNING
                    }
                  >
                    {bindStatus(diga.status)}
                  </BodyTextS>
                </Flex>
              </Box>
              <Divider style={{ marginLeft: 10, height: 30 }} />
              <Button className="button_close_modal" minimal onClick={onClose}>
                <Icon icon="cross" color={COLOR.TEXT_TERTIARY_SILVER} />
              </Button>
            </Flex>
          </Flex>
        }
        isCloseButtonShown={false}
        size={560}
        onClose={onClose}
        lazy
        usePortal
        autoFocus
        canEscapeKeyClose
        canOutsideClickClose
        shouldReturnFocusOnClose
        transitionDuration={250}
      >
        <Box
          p={16}
          className={getCssClass('bp5-dialog-content-scrollable')}
          style={{ overflowY: 'auto' }}
        >
          <Flex w="100%" mb={12} gap={12}>
            <Flex column gap={5} style={{ flexGrow: 1 }}>
              <H2>{t('description')}</H2>
              {diga.additionalDevice && (
                <Flex gap={12}>
                  <Svg src={usb} />
                  <Box>
                    <BodyTextM>{diga.additionalDevice}</BodyTextM>
                  </Box>
                </Flex>
              )}
              <Flex gap={12}>
                <Svg width={24} src={currencyEuro} />
                <Box>
                  <BodyTextM>{priceFormat(diga.price.amount)}</BodyTextM>
                  <Flex gap={5}>
                    <BodyTextM>{t('additionalCost') + ':'}</BodyTextM>
                    <BodyTextM>
                      {!diga.price.additionalCost && priceFormat(0)}
                    </BodyTextM>
                  </Flex>
                </Box>
              </Flex>
              {diga.languages && (
                <Flex gap={12} align="center">
                  <Svg src={globe} />
                  <BodyTextM>
                    {`${t('availableLanguage')}: ` + diga.languages.join(', ')}
                  </BodyTextM>
                </Flex>
              )}
            </Flex>
          </Flex>
          {diga?.module && (
            <>
              <H2>
                {diga?.module?.name && `${t('module')}: ${diga.module.name}`}
                &nbsp;
              </H2>
              <Flex column gap={5}>
                {renderField(t('moduleDigaPzn'), diga.pzn)}
                {renderField(t('moduleDigaTitle'), diga.title, true)}
                {renderField(t('appName'), diga.name, true)}
                {diga.module.additionalDeviceDetail &&
                  renderField(
                    t('additionalDevice'),
                    diga.module.additionalDeviceDetail,
                    true
                  )}
              </Flex>
            </>
          )}
          <Collapse title={t('manufacturer')}>
            <Flex column gap={5}>
              {renderField(t('name'), diga.manufacturer.name, false, 150)}
              {renderField(
                t('manufacturerContact'),
                nameUtils.getDoctorName(diga.manufacturer.doctorName),
                false,
                150
              )}
              {renderField(
                t('manufacturerAddress'),
                [diga.manufacturer.email, diga.manufacturer.phone]
                  .filter(Boolean)
                  .join(', '),
                false,
                150
              )}
            </Flex>
          </Collapse>
          <Collapse title={t('indication')}>
            <Flex column gap={5}>
              {diga.indications.map((i) =>
                renderField(i.code, i.display, false, 50)
              )}
              {renderField(
                t('geschtskennzeichen'),
                diga.module.genders.join(', '),
                true
              )}
              {renderField(
                t('altersgruppen'),
                diga.module.usage.ageGroup.join(', '),
                true
              )}
              {renderField(
                t('anwendungsdauer'),
                diga.module.usage.duration,
                true
              )}
              <BodyTextM
                fontWeight="Bold"
                padding="10px 0 0 0"
                onClick={() => setIsShowContrandication(true)}
                color={COLOR.BACKGROUND_SELECTED_STRONG}
              >
                {t('moreContraindications')}
              </BodyTextM>
            </Flex>
          </Collapse>
          {diga.questionnaireGroups.map((qg) => {
            if (qg.id === '59' || qg.id === '24') {
              return null;
            }
            return (
              <Collapse key={qg.id} title={qg.title} defaultOpen={false}>
                <Box>{qg.description}</Box>
                {qg.questionnaireGroups?.map((q) => {
                  if (!q.questionnaireInfos?.length) {
                    return null;
                  }
                  return (
                    <>
                      <BodyTextM
                        key={q.id}
                        style={{ lineHeight: '20px', padding: '12px 0' }}
                        fontSize={14}
                        fontWeight="Bold"
                      >
                        {q.title}
                      </BodyTextM>
                      {renderQuestionaires(q.questionnaireInfos)}
                    </>
                  );
                })}
                {renderQuestionaires(qg.questionnaireInfos)}
              </Collapse>
            );
          })}
        </Box>
      </Drawer>
      <Dialog
        isOpen={isShowContrandication}
        onClose={() => {
          setIsShowContrandication(false);
        }}
        title={t('kontraindikationen')}
      >
        <Box
          style={{
            maxHeight: '70vh',
            width: '760px',
            overflowY: 'auto',
          }}
          p={16}
        >
          <BodyTextM padding="0 0 16px 0" fontWeight="Bold">
            {'Nicht indizierte Geschlechtskennzeichen:'}
          </BodyTextM>
          {['Männlich', 'Weiblich', 'Nichtbinäre Geschlechtsidentität']
            .filter((e) => !diga.gender.includes(e))
            .join(', ') || t('empty')}
          <BodyTextM padding="16px 0" fontWeight="Bold">
            {
              'Die DiGA ist nicht anzuwenden bei folgenden Diagnosen (Kontraindikationen gemäß ICD-10):'
            }
          </BodyTextM>
          {diga.module.usage.contraindications.map((e) =>
            renderField(e.code, e.display, false, 50)
          )}
          {diga.module.usage.contraindications.length == 0 && t('empty')}
          <BodyTextM padding="16px 0" fontWeight="Bold">
            {
              'Weitere nicht durch Kontraindikationen abgedeckte Ausschlusskriterien:'
            }
          </BodyTextM>
          <BodyTextM whiteSpace="pre-line">
            {diga.module.usage.contraindicationExcluded || t('empty')}
          </BodyTextM>
        </Box>
      </Dialog>
    </React.Fragment>
  );
};

export default ViewDigaDetailDialog;
