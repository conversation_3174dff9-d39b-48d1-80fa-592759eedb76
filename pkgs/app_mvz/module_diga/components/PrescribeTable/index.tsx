import {
  alertError,
  alertSuccessfully,
  H1,
  Intent,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import {
  DigaDetail,
  PrescribeDetail,
  useMutationUpdatePrescribe,
  useQueryGetPrescribes,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { Order } from '@tutum/hermes/bff/legacy/common';
import I18n from '@tutum/infrastructure/i18n';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { genColumns } from '@tutum/mvz/module_diga/components/PrescribeTable/helper';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import { medicationActions } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import { medicationShoppingBagActions } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import useToaster from '@tutum/mvz/hooks/useToaster';
import { useContext, useEffect, useState } from 'react';
import { mapFormNameToFormType } from '../../Diga.helper';
import { useListenDigaChange } from '@tutum/hermes/bff/app_mvz_diga';
import { DigaActions } from '@tutum/mvz/module_diga/Diga.store';
import ViewDigaDetailDialog from '@tutum/mvz/module_diga/components/ViewDigaDetail';

interface IProps {
  patient: IPatientProfile;
  onActionRow: (rowItem: PrescribeDetail) => void;
}
const PrescribeTable = (props: IProps) => {
  const { t } = I18n.useTranslation<keyof typeof DigaListLang.Overview>({
    namespace: 'Diga',
    nestedTrans: 'Overview',
  });
  const { patient, onActionRow } = props;
  const medicationContext = useContext(MedicationContext);

  const [activeItem, setActiveItem] = useState<string | undefined>(undefined);
  const onUpdateNote = async (id: string, content: string) => {
    await updatePrescribe.mutateAsync({
      id,
      note: content,
    });
    setActiveItem(undefined);
  };

  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [notes, setNotes] = useState<{
    [key: string]: string;
  }>({});
  const [digaDetail, setDigaDetail] = useState<DigaDetail | undefined>(undefined);

  const {
    data: prescribes,
    isLoading,
    refetch,
  } = useQueryGetPrescribes(
    {
      patientId: patient.id,
      pagination: {
        sortBy: '',
        order: Order.ASC,
        page: pageNumber,
        pageSize,
      },
    },
    {
      select(data) {
        return data.data;
      },
    }
  );

  useEffect(() => {
    if (refetch) {
      DigaActions.setRefetchPrescfribe(refetch);
    }
  }, [refetch]);
  const toast = useToaster();

  useListenDigaChange((data) => {
    if (data?.eventType) {
      refetch();
    }
  });

  const updatePrescribe = useMutationUpdatePrescribe({
    onSuccess: async () => {
      alertSuccessfully(t('successNote'), { toaster: toast });
      await refetch();
      setTimeout(() => {
        (
          document.querySelector(
            `[data-test-id="prescribed-note-${activeItem}"`
          ) as any
        )?.blur();
      });
    },
    onError: (_) => {
      alertError(t('failNote'), { toaster: toast });
      refetch();
    },
  });

  const viewDigaPrescribe = (prescribeDetail: PrescribeDetail) => {
    medicationActions.setRefillFormData(undefined);
    medicationContext.setViewFormTimeline({
      treatmentDoctorId: prescribeDetail.prescribe.doctorId,
      assignedToBsnrId: prescribeDetail.prescribe.assignedToBsnrId!,
      formInfo: {
        id: prescribeDetail.prescribe.id!,
        formInfoResponse: [],
        formSetting: JSON.stringify(
          prescribeDetail?.prescribe?.formInfo?.formSetting ?? {}
        ),
        currentFormType: mapFormNameToFormType(
          prescribeDetail?.prescribe.formInfo.formName
        ),
        prescribeDate: new Date().getTime(),
        isNotPicked: false,
        isShowFavHint: false,
      },
    });
    medicationShoppingBagActions.setDigaInfo(
      prescribeDetail?.prescribe,
      prescribeDetail?.prescribe.formInfo.formName,
      prescribeDetail.prescribe.type
    );
  };

  useEffect(() => {
    if (activeItem) {
      const ele = document.querySelector(
        `[data-test-id="prescribed-note-${activeItem}"`
      ) as any;
      ele?.focus();
    }
  }, [activeItem]);

  return (
    <>
      <Table
        progressPending={isLoading}
        customStyles={{
          table: {
            style: {
              maxHeight: 'calc(100vh - 210px)',
            },
          },
          headCells: {
            style: {
              padding: 12,
            },
          },
          cells: {
            style: {
              padding: '4px 12px',
            },
          },
          pagination: {
            style: {
              position: 'fixed',
              bottom: 0,
              height: 40,
              minHeight: 'unset',
            },
          },
          rows: {
            style: {
              minHeight: 'auto',
            },
          },
        }}
        title={
          <H1 className="header" padding="20px 0">
            {t('prescribeTitle')}
          </H1>
        }
        columns={genColumns({
          t,
          viewDigaPrescribe,
          viewDigaDetail: setDigaDetail,
          onActionRow,
          onUpdateNote,
          isMutating: updatePrescribe.isPending,
          activeItem: activeItem!,
          notes,
          setActiveItem: (item) => {
            if (!updatePrescribe.isPending) {
              setActiveItem(item);
            }
          },
          setNotes,
        })}
        data={prescribes?.prescribes ?? []}
        pagination
        paginationServer
        striped
        highlightOnHover
        paginationTotalRows={prescribes?.pagination?.total}
        onChangeRowsPerPage={(currentRowPerPage) => {
          setPageNumber(1);
          setPageSize(currentRowPerPage);
        }}
        onChangePage={setPageNumber}
      />
      {digaDetail && (
        <ViewDigaDetailDialog
          digaDetail={digaDetail}
          onClose={() => {
            setDigaDetail(undefined);
          }}
        />
      )}
    </>
  );
};

export default PrescribeTable;
