import {
  BodyTextM,
  BodyTextS,
  Box,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import {
  DigaDetail,
  PrescribeDetail,
  PrescribeType,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import { COLOR } from '@tutum/design-system/themes/styles';
import { formatUnixToDateString } from '@tutum/design-system/infrastructure/utils';
import {
  Menu,
  MenuItem,
  Popover,
  PopoverPosition,
  Spinner,
} from '@tutum/design-system/components/Core';
import Theme from '@tutum/mvz/theme';
import Tooltip from '@tutum/design-system/components/Tooltip/Tooltip';
import { Dispatch, SetStateAction } from 'react';

const moreVertical = '/images/more-vertical.svg';
const eyeOnIcon = '/images/eye-on.svg';
const refreshCwIcon = '/images/refresh-cw.svg';
const editIcon = '/images/content-editor-edit-3.svg';
const actionIcon = '/images/action-icon.svg';
const alertTriangle = '/images/alert-triangle.svg';
const defaultDigaIcon = '/images/default-diga.svg';

interface IGenColumns {
  t: IFixedNamespaceTFunction<keyof typeof DigaListLang.Overview>;
  viewDigaPrescribe: (digaPrescriptionTimeline: PrescribeDetail) => void;
  viewDigaDetail: Dispatch<SetStateAction<DigaDetail>>;
  onActionRow: (_: PrescribeDetail) => void;
  onUpdateNote: (id: string, content: string) => Promise<void>;
  isMutating: boolean;
  activeItem: string;
  notes: {
    [key: string]: string;
  };
  setActiveItem: (_: string) => void;
  setNotes: Dispatch<
    SetStateAction<{
      [key: string]: string;
    }>
  >;
}

export const StyledTextArea = Theme.styled.textarea`
  resize: none;
  font-family: inherit;
  font-size: inherit;
  border: none;
  background: inherit;
  width: 100%;
  height: 100%;
  padding: 0;
  &:focus {
    border: 1px solid ${COLOR.INFO_PRIMARY_PRESSED};
  }
`;

export function renderDigaIcon(url: string) {
  return (
    <Svg
      style={{ borderRadius: '50%', border: '1px solid rgba(19, 50, 75, 0.1)' }}
      src={url || defaultDigaIcon}
      width={24}
      height={24}
    />
  );
}

export const genColumns = ({
  t,
  viewDigaPrescribe,
  onActionRow,
  onUpdateNote,
  isMutating,
  activeItem,
  notes,
  setActiveItem,
  setNotes,
  viewDigaDetail,
}: IGenColumns): IDataTableColumn<PrescribeDetail>[] => [
  {
    name: t('date'),
    width: '150px',
    cell: (row: PrescribeDetail) => {
      const dateStr = formatUnixToDateString(row.date);
      const date = new Date(row.date);
      const formattedTime = `${String(date.getHours()).padStart(
        2,
        '0'
      )}:${String(date.getMinutes()).padStart(2, '0')}`;
      return (
        <Box p={5}>
          <Flex column={true} gap={10} justify="space-between" w="100%">
            <Box>{dateStr}</Box>
            <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
              {formattedTime}
            </BodyTextS>
          </Flex>
        </Box>
      );
    },
  },
  {
    name: t('DiGA'),
    cell: (row: PrescribeDetail) => (
      <Box pt={5} pb={5} w="100%">
        <Flex justify="space-between" w="100%" className="diga_column_name">
          <Flex align="center">
            <Box mr={8}>{renderDigaIcon(row.diga?.icon?.url!)}</Box>
            <Box style={{ cursor: 'pointer' }}>
              <BodyTextM
                onClick={() => row.diga && viewDigaPrescribe(row)}
                color={COLOR.TEXT_INFO}
                fontWeight="SemiBold"
              >
                {row.prescribe.digaTitle || row.prescribe.digaName}
              </BodyTextM>
              <BodyTextS>{row.diga?.gender?.join(', ')}</BodyTextS>
            </Box>
          </Flex>
        </Flex>
      </Box>
    ),
  },
  {
    name: t('pzn'),
    width: '150px',
    cell: (row) => {
      return (
        <Flex justify="space-between" style={{ width: '100%' }}>
          <BodyTextM>{row.prescribe.digaPzn}</BodyTextM>
          {row.isExpired && (
            <Tooltip content={t('digaExpired')}>
              <Svg src={alertTriangle} />
            </Tooltip>
          )}
        </Flex>
      );
    },
  },
  {
    name: t('note'),
    cell: (row) => {
      if (row.prescribe.id === activeItem && isMutating) {
        return (
          <Flex justify="center" style={{ width: '100%' }}>
            <Spinner size={30} />
          </Flex>
        );
      }
      return (
        <StyledTextArea
          data-test-id={`prescribed-note-${row.prescribe.id}`}
          onBlur={() => {
            setActiveItem(null!);
            setNotes((prevValues) => {
              return {
                ...prevValues,
                [row.prescribe.id!]: row.prescribe.note,
              };
            });
          }}
          onFocus={() => {
            setActiveItem(row.prescribe.id!);
            setNotes((prevValues) => {
              return {
                ...prevValues,
                [row.prescribe.id!]: row.prescribe.note,
              };
            });
          }}
          onKeyDown={(event: any) => {
            if (event.key === 'Enter' && !event.shiftKey) {
              event.preventDefault(); // Prevents the default behavior of adding a new line
              onUpdateNote(row.prescribe.id!, notes[row.prescribe.id!]);
            }
          }}
          onChange={(event) => {
            setNotes((prevValues) => {
              return {
                ...prevValues,
                [row.prescribe.id!]: event.target.value,
              };
            });
          }}
          value={notes[row.prescribe.id!] ?? row.prescribe.note}
        />
      );
    },
  },
  {
    name: t('prescribedBy'),
    selector: (row) => row.doctorName,
  },
  {
    name: '',
    width: '40px',
    cell: (row) => {
      if (activeItem === row.prescribe.id) {
        return <Svg style={{ cursor: 'pointer' }} src={actionIcon} />;
      }
      return (
        <Popover
          usePortal
          position={PopoverPosition.LEFT}
          content={
            <>
              <Menu className="sl-menu-action" key="menu-more">
                {!!row.diga && (
                  <>
                    <MenuItem
                      icon={<Svg src={eyeOnIcon} width={16} height={16} />}
                      text={t('viewPrescribe')}
                      onClick={() => {
                        viewDigaPrescribe(row);
                      }}
                    />
                    {row.prescribe.type === PrescribeType.PRESCRIBED && (
                      <MenuItem
                        icon={<Svg src={eyeOnIcon} width={16} height={16} />}
                        text={t('viewDiga')}
                        onClick={() => {
                          viewDigaDetail(row.diga!);
                        }}
                      />
                    )}
                    {!row.isExpired && (
                      <MenuItem
                        icon={
                          <Svg src={refreshCwIcon} width={16} height={16} />
                        }
                        text={t('refill')}
                        onClick={() => {
                          onActionRow(row);
                        }}
                      />
                    )}
                  </>
                )}

                <MenuItem
                  icon={<Svg src={editIcon} width={16} height={16} />}
                  text={t('addNote')}
                  onClick={() => {
                    setActiveItem(row.prescribe.id!);
                  }}
                />
              </Menu>
            </>
          }
        >
          <Svg
            style={{ cursor: 'pointer' }}
            className={'sl-actions'}
            src={moreVertical}
          />
        </Popover>
      );
    },
  },
];
