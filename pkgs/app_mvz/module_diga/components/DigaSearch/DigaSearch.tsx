import {
  Box,
  Button,
  Flex,
  IMenuItem,
  Svg,
} from '@tutum/design-system/components';
import { Intent } from '@tutum/design-system/components/Core';
import {
  Filter,
  SearchRequest,
  SearchType,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { Order } from '@tutum/hermes/bff/legacy/common';
import i18n from '@tutum/infrastructure/i18n';
import CategoriesSearch from '@tutum/mvz/components/categories-search';
import type DigaLocales from '@tutum/mvz/locales/en/Diga.json';
import {
  defaultSortName,
  defaultSortPrice,
  ISort,
} from '@tutum/mvz/module_diga/utils';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { FormikProps } from 'formik';
import { getCategoryList } from '../../Diga.helper';
import { DigaActions, SearchDiga, useDigaStore } from '../../Diga.store';
import DigaSearchFilter from './DigaSearchFilter';

const SearchIcon = '/images/search-sidebar-disable.svg';

export type DigaSearchProps = {
  className?: string;
  onSearch(payload: SearchRequest): void;
  query: string;
  searchType: SearchType;
  setSearch: Dispatch<SetStateAction<SearchRequest>>;
  triggerSearch: () => void;
  resetSearchBox: () => void;
  isSearching: boolean;
  onSetSort: (_: ISort) => void;
};

const MAX_PRICE_SEARCH = 2100;

export const defaultFilter: Filter = {
  minPrice: 0,
  maxPrice: MAX_PRICE_SEARCH,
  digaStatus: [],
  digaAgeGroups: [],
};

const DigaSearch = (props: DigaSearchProps) => {
  const {
    className,
    onSearch,
    query,
    searchType: initSearchType,
    setSearch,
    triggerSearch,
    resetSearchBox,
    isSearching,
    onSetSort,
  } = props;
  const { t } = i18n.useTranslation<keyof typeof DigaLocales.Digatab>({
    namespace: 'Diga',
    nestedTrans: 'Digatab',
  });

  const catagories = getCategoryList(t);
  const searchType: IMenuItem<SearchType> | undefined = catagories.find(
    (c) => c.value === initSearchType
  );
  const { pagination } = useDigaStore();

  const [filter, setFilter] = useState<Filter>(defaultFilter);
  const formikRef = useRef<FormikProps<any> | null>(null);
  const clearSearchBox = () => {
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // setFilter(defaultFilter);
    resetSearchBox();
  };

  useEffect(() => {
    DigaActions.setResetSearchBox(clearSearchBox);
  }, []);

  useEffect(() => {
    handleSearch({
      query,
      searchType: searchType!,
    });
  }, [pagination.pageNumber, pagination.pageSize, filter]);

  const isEmpty = !searchType && !query;

  const handleSearch = (payload: SearchDiga) => {
    onSearch({
      query: payload?.query || '',
      type: payload?.searchType?.value ?? SearchType.SEARCH_TYPE_UNSPECIFIED,
      filter,
      pagination: {
        sortBy: '',
        order: Order.ASC,
        page: pagination.pageNumber,
        pageSize: pagination.pageSize,
      },
    });
  };

  const handleOnQueryChange = (query: string) => {
    DigaActions.resetPagination();
    handleSearch({
      query,
      searchType: searchType!,
    });
  };

  const renderRightElement = () => {
    if (isEmpty) {
      return <Svg src={SearchIcon} style={{ display: 'inline-flex' }} />;
    }
  };

  const handleSelectItem = (
    item: IMenuItem<SearchType>,
    isCategory: boolean
  ) => {
    if (isCategory) {
      if (item && item.value === SearchType.SEARCH_TYPE_ICD) {
        onSetSort(defaultSortPrice);
      } else {
        onSetSort(defaultSortName);
      }
      setSearch((prev) => ({
        ...prev,
        type: item.value,
      }));
    }
  };

  return (
    <div className={className}>
      <Flex
        align="center"
        style={{
          position: 'relative',
        }}
      >
        {isSearching && (
          <Button
            onClick={clearSearchBox}
            minimal
            intent={Intent.NONE}
            icon={<Svg src="/images/arrow-left-new.svg" />}
          />
        )}
        <CategoriesSearch
          className="sl-categories-search"
          query={query}
          items={[]}
          inputValueRenderer={() => query}
          onQueryChange={handleOnQueryChange}
          listCategories={getCategoryList(t)}
          searchType={searchType!}
          handleSetSearchType={(type: IMenuItem<SearchType>) => {
            setSearch((prev) => ({
              ...prev,
              type: type?.value ?? SearchType.SEARCH_TYPE_UNSPECIFIED,
            }));
          }}
          placeholder={t('inputAnySearch')}
          inputProps={{
            rightElement: renderRightElement(),
            className: isEmpty ? 'sl-is-empty' : '',
            onClick: (_) => {
              triggerSearch();
            },
          }}
          isSearchByPZN={searchType?.value === SearchType.SEARCH_TYPE_PZN}
          onItemSelect={handleSelectItem}
          itemRenderer={() => null}
          noResults={undefined}
          clearCallback={clearSearchBox}
          deleteCallBack={() => {
            onSetSort(defaultSortName);
          }}
          usePortal={true}
        />
      </Flex>
      <Box onClick={triggerSearch}>
        <DigaSearchFilter
          onChange={DigaActions.resetPagination}
          setFilter={setFilter}
          ref={formikRef}
        />
      </Box>
    </div>
  );
};

export default DigaSearch;
