import { Icon } from '@blueprintjs/core';
import _ from 'lodash';
import Link from 'next/link';
import React, { useState, useEffect } from 'react';
import { createGlobalStyle } from '@tutum/design-system/themes';

import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';

import {
  BodyTextM,
  BodyTextS,
  Box,
  Button,
  Collapse,
  Dialog,
  Flex,
  H2,
  MessageBar,
  Svg,
} from '@tutum/design-system/components';
import {
  Classes,
  DialogFooter,
  Divider,
  Drawer,
  Intent,
} from '@tutum/design-system/components/Core';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  AnswerType,
  DigaDetail,
  DigaStatus,
  PlatformItem,
  PrescribeType,
  QuestionnaireInfo,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { FormName } from '@tutum/hermes/bff/legacy/form_common';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import FormSelect from '@tutum/mvz/module_diga/components/FormSelect';
import { useDigaStore } from '@tutum/mvz/module_diga/Diga.store';
import { priceFormat } from '@tutum/mvz/module_diga/utils';
import { useMedication } from '@tutum/mvz/module_medication/context/MedicationProvider';
import { medicationShoppingBagActions } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import DigaName from '../DigaName';
import { bindIcon, bindStatus, isEqualArray } from '../DigaTable/helper';

interface IProps {
  diga: DigaDetail;
  onClose: () => void;
  loading?: boolean;
  onOpenSimilarDiga: () => void;
  cachedDiga: Nullable<DigaDetail>;
  isRefill: boolean;
}
const GlobalStyleHighlightForPortalMenu = createGlobalStyle`
  .diga-detail {
    &__highlighted {
      background-color: ${COLOR.WARNING_LIGHT};
    }
  }
`;

export const GlobalStyleForDigaPortalMenu = createGlobalStyle`
  .sl-diga-drawer {
    h2 {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
  }
  pre {
    margin: 0;
    font-family: inherit;
    white-space: pre-wrap;
  }
  .diga-detail {
    &__icon_title {
      border-radius: 50%;
    }

    &__status {
      font-weight: 600;
    }
  }

  .button_close_modal {
    &.bp5-button .bp5-icon:first-child:last-child, .bp5-button .bp5-spinner + .bp5-icon:last-child {
      margin: 0 -8px;
    }
  }

  .bp5-dialog-content-scrollable {
    .sl-collapse > .sl-collapse__header {
      margin-bottom: 5px;
    }
    .bp5-heading {
      margin-right: 0 !important;
    }
  }

  .border-right {
    border-right: 1px solid ${COLOR.BORDER_INPUT};
  }

  .question {
    &__content {
      white-space: pre-line;
    }
  }

  .sl-collapse {
    margin-top: 12px;
  }
`;

const currencyEuro = '/images/currency-euro.svg';
const usb = '/images/usb.svg';
const appleStore = '/images/apple-store.svg';
const googleStore = '/images/google-play.svg';
const webApp = '/images/monitor-smartphone.svg';
const arrow = '/images/arrow-up-right-blue.svg';
const medicalKit = '/images/medical-device-kit.svg';
const globe = '/images/globe.svg';
const helpIcon = '/images/help-circle.svg';

const renderAnswerByType = (
  answer: QuestionnaireInfo
): String | React.ReactNode => {
  switch (answer.type) {
    case AnswerType.ANSWER_TYPE_DATE:
      return datetimeUtil.dateTimeFormat(answer.answer, DATE_FORMAT);
    case AnswerType.ANSWER_TYPE_HREF:
      return (
        <Link target="_blank" href={answer.href}>
          {answer.answer}
        </Link>
      );
    default:
      return answer.answer;
  }
};
export const renderField = (
  label: string,
  value: string,
  className?: string,
  fullWidth = false,
  labelWidth?: number
) => {
  return (
    <Flex column={fullWidth} key={label}>
      <BodyTextM
        textTransform="uppercase"
        color={COLOR.TEXT_PLACEHOLDER}
        style={{
          minWidth: labelWidth || 'fit-content',
          width: labelWidth,
          flex: '0',
        }}
      >
        {label}:
      </BodyTextM>
      <BodyTextM
        className={className}
        padding={`0 0 0 ${fullWidth ? 0 : '15px'}`}
      >{`${value}`}</BodyTextM>
    </Flex>
  );
};

const DigaDetailDialog = (props: IProps) => {
  const { diga, onClose, isRefill } = props;
  let cachedDiga = props.cachedDiga;
  const medicationContext = useMedication();
  const { t } = I18n.useTranslation<keyof typeof DigaListLang.Detail>({
    namespace: 'Diga',
    nestedTrans: 'Detail',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { clearSearchBoxFunc, refetchPrescribe } = useDigaStore();
  const [formName, setFormName] = useState<FormName | undefined>(undefined);
  const [isShowPlatform, setIsShowPlatform] = useState(false);
  const [isShowContrandication, setIsShowContrandication] = useState(false);

  const [isChange, setIsChange] = useState(false);
  if (!isRefill) {
    cachedDiga = null;
  }
  useEffect(() => {
    if (isChange) {
      setIsChange(false);
    }
  }, [diga, cachedDiga]);

  const highlight = (enable: boolean) => {
    enable = enable && isRefill;
    if (enable && !isChange) {
      setIsChange(true);
    }
    return getCssClass({
      'diga-detail__highlighted': enable,
    });
  };

  const highlightField = (field: string) => {
    const a = _.get(diga, field);
    const b = _.get(cachedDiga, field);
    if (_.isArray(a) && _.isArray(b)) {
      return highlight(!isEqualArray(a, b));
    }
    return highlight(!_.isEqual(a, b));
  };

  return (
    <React.Fragment>
      <GlobalStyleForDigaPortalMenu />
      {cachedDiga && cachedDiga?.id === diga.id && (
        <GlobalStyleHighlightForPortalMenu />
      )}
      <Drawer
        className="sl-diga-drawer"
        isOpen={true}
        title={
          <Flex align="center" justify="space-between">
            <Flex align="center" alignSelf="center">
              <Box mr={8}>
                <Svg
                  className="diga-detail__icon_title"
                  src={diga.icon.url}
                  width={40}
                  height={40}
                />
              </Box>
              <Box style={{ flexGrow: 1 }}>
                <DigaName name={diga.title} fontSize={20} />
                <Flex>
                  <Svg src={bindIcon(diga.status)!} width={16} height={16} />
                  <BodyTextS
                    className="diga-detail__status"
                    color={
                      diga.status === DigaStatus.DIGA_STATUS_ACTIVE
                        ? COLOR.POSITIVE_PRESSED
                        : COLOR.TEXT_WARNING
                    }
                  >
                    {bindStatus(diga.status)}
                  </BodyTextS>
                </Flex>
              </Box>
            </Flex>
            <Flex align="center" justify="flex-end">
              <FormSelect
                onFormChange={(formName) => {
                  setFormName(formName);
                }}
              />
              <Divider style={{ marginLeft: 10, height: 30 }} />
              <Button className="button_close_modal" minimal onClick={onClose}>
                <Icon<SVGSVGElement>
                  icon="cross"
                  color={COLOR.TEXT_TERTIARY_SILVER}
                />
              </Button>
            </Flex>
          </Flex>
        }
        isCloseButtonShown={false}
        size={560}
        onClose={onClose}
        lazy
        usePortal
        autoFocus
        canEscapeKeyClose
        canOutsideClickClose
        shouldReturnFocusOnClose
        transitionDuration={250}
      >
        <Box
          p={16}
          className={getCssClass('bp5-dialog-content-scrollable')}
          style={{ overflowY: 'auto' }}
        >
          {isChange && (
            <MessageBar
              hasBullet={false}
              type="warning"
              content={t('hintChange')}
            />
          )}
          <Box my={12} className={highlightField('summary')}>
            <BodyTextM whiteSpace="pre-wrap">{diga.summary}</BodyTextM>
          </Box>
          <Box mb={12}>
            <Button outlined onClick={props.onOpenSimilarDiga}>
              {t('searchFamiliar')}
            </Button>
          </Box>
          <Flex w="100%" mb={12} gap={12}>
            <Flex
              column
              gap={5}
              className="border-right"
              style={{ flexGrow: 1 }}
            >
              <H2>{t('description')}</H2>
              {diga.additionalDevice && (
                <Flex gap={12}>
                  <Svg src={usb} />
                  <Box className={highlightField('additionalDevice')}>
                    <BodyTextM>{diga.additionalDevice}</BodyTextM>
                  </Box>
                </Flex>
              )}
              <Flex gap={12}>
                <Svg width={24} src={currencyEuro} />
                <Box>
                  <BodyTextM className={highlightField('price.amount')}>
                    {priceFormat(diga.price.amount)}
                  </BodyTextM>
                  <Flex gap={5}>
                    <BodyTextM>{t('additionalCost') + ':'}</BodyTextM>
                    <BodyTextM
                      className={highlightField('price.additionalCost')}
                    >
                      {!diga.price.additionalCost && priceFormat(0)}
                    </BodyTextM>
                  </Flex>
                </Box>
              </Flex>
              <Flex gap={12} align="center">
                <Svg width={24} src={medicalKit} />
                <Box className={highlightField('requireContract')}>
                  <BodyTextM>
                    {diga.requireContract
                      ? t('requireContract')
                      : t('noRequireContract')}
                  </BodyTextM>
                </Box>
              </Flex>
              {diga.languages && (
                <Flex gap={12} align="center">
                  <Svg src={globe} />
                  <BodyTextM className={highlightField('languages')}>
                    {`${t('availableLanguage')}: ` + diga.languages.join(', ')}
                  </BodyTextM>
                </Flex>
              )}
            </Flex>
            <Flex
              column
              gap={10}
              style={{ justifySelf: 'end', minWidth: 'fit-content' }}
            >
              <H2>{t('platform')}</H2>
              {diga.platform.apple && (
                <Flex gap={12}>
                  <Svg width={24} height={24} src={appleStore} />
                  <BodyTextM>{t('appleStore')}</BodyTextM>
                </Flex>
              )}
              {diga.platform.android && (
                <Flex gap={12}>
                  <Svg width={24} height={24} src={googleStore} />
                  <BodyTextM>{t('googleStore')}</BodyTextM>
                </Flex>
              )}
              {diga.platform.web && (
                <Flex gap={12}>
                  <Svg width={24} height={24} src={webApp} />
                  <BodyTextM>{t('webApp')}</BodyTextM>
                </Flex>
              )}
              <Button
                minimal
                onClick={() => setIsShowPlatform(true)}
                style={{ padding: 0, display: 'block' }}
              >
                <Flex gap={5}>
                  <BodyTextM
                    fontWeight="Bold"
                    color={COLOR.BACKGROUND_SELECTED_STRONG}
                  >
                    {t('more')}
                  </BodyTextM>
                  <Svg src={arrow} />
                </Flex>
              </Button>
            </Flex>
          </Flex>
          {diga?.module && (
            <>
              <H2>
                {diga?.module?.name && `${t('module')}: ${diga.module.name}`}
                &nbsp;
              </H2>
              <Flex column gap={5}>
                {renderField(
                  t('moduleDigaPzn'),
                  diga.pzn,
                  highlightField('pzn')
                )}
                {renderField(
                  t('moduleDigaTitle'),
                  diga.title,
                  highlightField('title'),
                  true
                )}
                {renderField(
                  t('appName'),
                  diga.name,
                  highlightField('name'),
                  true
                )}
                {diga.module.additionalDeviceDetail &&
                  renderField(
                    t('additionalDevice'),
                    diga.module.additionalDeviceDetail,
                    highlightField('module.additionalDeviceDetail'),
                    true
                  )}
              </Flex>
            </>
          )}
          <Collapse title={t('manufacturer')}>
            <Flex column gap={5}>
              {renderField(
                t('name'),
                diga.manufacturer.name,
                highlightField('manufacturer.name'),
                false,
                150
              )}
              {renderField(
                t('manufacturerContact'),
                nameUtils.getDoctorName(diga.manufacturer.doctorName),
                highlightField('manufacturer.doctorName.lastName'),
                false,
                150
              )}
              {renderField(
                t('manufacturerAddress'),
                [diga.manufacturer.email, diga.manufacturer.phone]
                  .filter(Boolean)
                  .join(', '),
                highlightField('manufacturer.email'),
                false,
                150
              )}
            </Flex>
          </Collapse>
          <Collapse title={t('indication')}>
            <Flex column gap={5}>
              {diga.indications.map((i) =>
                renderField(i.code, i.display, undefined, false, 50)
              )}
              {renderField(
                t('geschtskennzeichen'),
                diga.module.genders.join(', '),
                highlightField('module.genders'),
                true
              )}
              {renderField(
                t('altersgruppen'),
                diga.module.usage.ageGroup.join(', '),
                highlightField('module.usage.ageGroup'),
                true
              )}
              {renderField(
                t('anwendungsdauer'),
                diga.module.usage.duration,
                highlightField('module.usage.duration'),
                true
              )}
              <BodyTextM
                fontWeight="Bold"
                padding="10px 0 0 0"
                onClick={() => setIsShowContrandication(true)}
                color={COLOR.BACKGROUND_SELECTED_STRONG}
              >
                {t('moreContraindications')}
              </BodyTextM>
            </Flex>
          </Collapse>
          {diga.questionnaireGroups.map((qg) => {
            const cachedQuestionaireGroup =
              cachedDiga?.questionnaireGroups?.find((q) => q.id === qg.id);
            return (
              <Collapse
                key={`${qg.id}_${qg.title}`}
                title={qg.title}
                defaultOpen={false}
              >
                <BodyTextM whiteSpace="pre-wrap">{qg.description}</BodyTextM>
                {qg.questionnaireGroups?.map((q, i) => {
                  if (!q.questionnaireInfos?.length) {
                    return null;
                  }
                  const cached = cachedQuestionaireGroup?.[i];
                  return (
                    <>
                      <BodyTextM
                        key={q.id}
                        className={highlightField('title')}
                        style={{ lineHeight: '20px', padding: '12px 0' }}
                        fontWeight="Bold"
                        fontSize={13}
                      >
                        {q.title}
                      </BodyTextM>
                      {renderQuestionaires(q.questionnaireInfos)}
                    </>
                  );
                })}
                {renderQuestionaires(qg.questionnaireInfos)}
              </Collapse>
            );
          })}
        </Box>
        <div className={Classes.DRAWER_FOOTER}>
          <Flex w="100%" justify="flex-end">
            <Button
              intent={Intent.PRIMARY}
              onClick={() => {
                onClose();
                medicationContext.setShowPrintPreviewState(true);
                medicationShoppingBagActions.setDigaInfo(
                  {
                    digaId: diga.id,
                    digaName: diga.name,
                    digaPzn: diga.pzn,
                    digaTitle: diga.title,
                  },
                  formName!,
                  PrescribeType.PRESCRIBED
                );
                medicationShoppingBagActions.onPrescribe(() => {
                  if (isRefill) {
                    refetchPrescribe?.();
                  } else {
                    clearSearchBoxFunc?.();
                  }
                  medicationShoppingBagActions.onPrescribe(() => {});
                });
              }}
            >
              {isRefill ? t('refill') : t('prescribe')}
            </Button>
          </Flex>
        </div>
      </Drawer>
      <Dialog
        isOpen={isShowPlatform}
        onClose={() => {
          setIsShowPlatform(false);
        }}
        title={t('platform')}
      >
        <Box
          style={{
            maxHeight: '70vh',
            maxWidth: '60vw',
            overflowY: 'auto',
          }}
          p={15}
        >
          <BodyTextM>{t('platformDescription')}</BodyTextM>
          {diga.platform.apple &&
            renderPlatForm(
              t,
              appleStore,
              `${t('appleStore')} ${t('inGermany')}`,
              diga.platform.apple
            )}
          {diga.platform.android &&
            renderPlatForm(
              t,
              googleStore,
              `${t('googleStore')} ${t('inGermany')}`,
              diga.platform.android
            )}
          {diga.platform.web &&
            renderPlatForm(t, webApp, t('webApp'), diga.platform.web)}
        </Box>
        <DialogFooter
          minimal={true}
          actions={
            <Button
              outlined
              intent={Intent.PRIMARY}
              onClick={() => {
                setIsShowPlatform(false);
              }}
            >
              {tButtonActions('close')}
            </Button>
          }
        />
      </Dialog>
      <Dialog
        isOpen={isShowContrandication}
        onClose={() => {
          setIsShowContrandication(false);
        }}
        title={t('kontraindikationen')}
      >
        <Box
          style={{
            maxHeight: '70vh',
            width: '760px',
            overflowY: 'auto',
          }}
          p={16}
        >
          <BodyTextM padding="0 0 16px 0" fontWeight="Bold">
            {'Nicht indizierte Geschlechtskennzeichen:'}
          </BodyTextM>
          {['Männlich', 'Weiblich', 'Nichtbinäre Geschlechtsidentität']
            .filter((e) => !diga.gender.includes(e))
            .join(', ') || t('empty')}
          <BodyTextM padding="16px 0" fontWeight="Bold">
            {
              'Die DiGA ist nicht anzuwenden bei folgenden Diagnosen (Kontraindikationen gemäß ICD-10):'
            }
          </BodyTextM>
          {diga.module.usage.contraindications.map((e) =>
            renderField(e.code, e.display, undefined, false, 50)
          )}
          {diga.module.usage.contraindications.length == 0 && t('empty')}
          <BodyTextM padding="16px 0" fontWeight="Bold">
            {
              'Weitere nicht durch Kontraindikationen abgedeckte Ausschlusskriterien:'
            }
          </BodyTextM>
          <BodyTextM whiteSpace="pre-line">
            {diga.module.usage.contraindicationExcluded || t('empty')}
          </BodyTextM>
        </Box>
      </Dialog>
    </React.Fragment>
  );
};

function renderPlatForm(
  t: IFixedNamespaceTFunction<any>,
  icon: string,
  title: string,
  platform: PlatformItem
) {
  const style = { width: 200, marginRight: 20, flexShrink: 0 };
  const renderField = (field: string, val: string, isUrl = false) => (
    <Flex w="100%" gap={10}>
      <BodyTextM
        textTransform="uppercase"
        color={COLOR.TEXT_TERTIARY_SILVER}
        style={style}
      >
        {`${field}:`}
      </BodyTextM>
      {isUrl ? (
        <Link href={val} target="_blank">
          {val}
        </Link>
      ) : (
        <pre>{val}</pre>
      )}
    </Flex>
  );
  return (
    <Flex my={10} column gap={15}>
      <Flex gap={10} align="center">
        <Svg height={30} src={icon} />
        <BodyTextM fontWeight="Bold">{title}</BodyTextM>
      </Flex>
      {renderField(t('appUrl'), platform.url, true)}
      {renderField(t('appVersion'), platform.appVersion)}
      {renderField(t('hardwareCompat'), platform.hardwareCompat)}
      {renderField(t('softwareCompat'), platform.softwareCompat)}
    </Flex>
  );
}

export function renderQuestionaires(qs: QuestionnaireInfo[]) {
  return qs?.map((q, idx) => {
    return (
      <Flex mt={8} mb={12} gap={4} key={`${idx}_${q.question}`}>
        <Svg
          style={{ position: 'relative', top: 2 }}
          size={16}
          src={helpIcon}
        />
        <Box>
          <BodyTextM color={COLOR.TAG_CONTENT_BLUE}>{q.question}</BodyTextM>
          <BodyTextM className={getCssClass('question__content')}>
            {renderAnswerByType(q)}
          </BodyTextM>
        </Box>
      </Flex>
    );
  });
}

export default DigaDetailDialog;
