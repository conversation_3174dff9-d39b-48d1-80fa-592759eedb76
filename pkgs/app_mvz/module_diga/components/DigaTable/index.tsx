import {
  alertSuccessfully,
  Box,
  Button,
  ConfirmDialog,
  Flex,
  FormGroup2,
  H2,
} from '@tutum/design-system/components';
import { InputGroup, Intent } from '@tutum/design-system/components/Core';
import HelpText from '@tutum/design-system/components/ErrorHelpText';
import Table from '@tutum/design-system/components/Table';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  DigaDetail,
  DigaItem,
  PrescribeType,
  validateFreeText,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { FormName } from '@tutum/hermes/bff/legacy/form_common';
import I18n from '@tutum/infrastructure/i18n';
import { useErrorCodeI18n } from '@tutum/mvz/hooks/useErrorCode';
import useToaster from '@tutum/mvz/hooks/useToaster';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { genColumns } from '@tutum/mvz/module_diga/components/DigaTable/helper';
import FormSelect from '@tutum/mvz/module_diga/components/FormSelect';
import {
  defaultSortName,
  defaultSortPrice,
  ISort,
} from '@tutum/mvz/module_diga/utils';
import { useMedication } from '@tutum/mvz/module_medication/context/MedicationProvider';
import { medicationShoppingBagActions } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import { FastField, Formik } from 'formik';
import React, { useEffect, useState } from 'react';
import { DigaActions, useDigaStore } from '../../Diga.store';

interface IProps {
  digaOriginalId: string;
  digas: DigaItem[];
  totalRow: number;
  loading?: boolean;
  rowLoading?: boolean;
  onActionRow: (id: string) => void;
  patientId: string;
  resetSearchBox: () => void;
  searchText: string;
  onSearchByPzn: (pzn: string) => void;
  footer?: React.ReactNode;
  sort: ISort;
  onSetSort: (_: ISort) => void;
}

interface IFreeText {
  pzn: string;
  name: string;
  formName: FormName;
}

const DigaTable = (props: IProps) => {
  const {
    digaOriginalId,
    digas,
    totalRow,
    onActionRow,
    loading,
    rowLoading,
    searchText,
    onSearchByPzn,
    footer,
    sort,
    onSetSort,
  } = props;
  const { pagination } = useDigaStore();
  const { t } = I18n.useTranslation<keyof typeof DigaListLang.Overview>({
    namespace: 'Diga',
    nestedTrans: 'Overview',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const tError = useErrorCodeI18n();
  const [isOpen, setIsOpen] = useState(false);
  const { clearSearchBoxFunc } = useDigaStore();
  const medicationContext = useMedication();
  const toast = useToaster();

  useEffect(() => {
    if (digaOriginalId) {
      onSetSort(defaultSortPrice);
    } else {
      onSetSort(defaultSortName);
    }
  }, [digaOriginalId]);

  const onSubmitFreetext = async (values: IFreeText, { setFieldError }) => {
    try {
      await validateFreeText({
        pzn: values.pzn.trim(),
        name: values.name.trim(),
      });
    } catch (error) {
      const serverError = error?.response?.data?.serverError;
      switch (serverError) {
        case ErrorCode.ErrorCode_Diga_PznExpired:
        case ErrorCode.ErrorCode_Diga_PznAlreadyExists:
          setFieldError('pzn', tError(serverError));
          return;
        default:
          throw error;
      }
    }
    setIsOpen(false);
    const diga = {
      name: values.name,
      pzn: values.pzn,
    } as DigaDetail;
    medicationContext.setShowPrintPreviewState(true);
    medicationShoppingBagActions.setDigaInfo(
      {
        digaId: diga.id,
        digaName: diga.name,
        digaPzn: diga.pzn,
      },
      values.formName,
      PrescribeType.FREETEXT
    );
    medicationShoppingBagActions.onPrescribe(() => {
      clearSearchBoxFunc?.();
      alertSuccessfully('Prescribed', { toaster: toast });
    });
  };

  const onClose = () => {
    setIsOpen(false);
    clearSearchBoxFunc?.();
  };

  const cssConditional = [
    {
      when: (item: DigaItem) => item['id'] === digaOriginalId,
      style: { background: COLOR.WARNING_LIGHT },
    },
  ];

  return (
    <React.Fragment>
      <Table
        customStyles={{
          table: {
            style: {
              maxHeight: 'calc(100vh - 210px)',
              marginTop: '20px',
            },
          },
          headCells: {
            style: {
              padding: 12,
            },
          },
          cells: {
            style: {
              padding: '4px 12px',
            },
          },
          pagination: {
            style: {
              position: 'fixed',
              bottom: 0,
              height: 40,
              minHeight: 'unset',
            },
          },
          rows: {
            style: {
              minHeight: 'auto',
            },
          },
        }}
        columns={genColumns({
          t,
          onActionRow,
          loading: rowLoading,
          sort,
          onSetSort,
        })}
        conditionalRowStyles={cssConditional}
        data={digas}
        pagination
        paginationServer
        onChangePage={DigaActions.setPageNumber}
        onChangeRowsPerPage={DigaActions.setPageSize}
        paginationDefaultPage={pagination.pageNumber}
        paginationTotalRows={totalRow}
        progressPending={loading}
        noDataComponent={
          <Flex mt={100} gap={10} column align="center">
            <H2 color={COLOR.TEXT_SECONDARY_NAVAL}>{`${t(
              'noSearchResult'
            )}.`}</H2>
            <Button
              intent={Intent.PRIMARY}
              outlined
              onClick={() => setIsOpen(true)}
            >
              {t('addFreeText')}
            </Button>
          </Flex>
        }
        footer={footer}
      />
      <ConfirmDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title={t('createFreeTextDiga')}
      >
        <Formik<IFreeText>
          initialValues={{ pzn: '', name: searchText, formName: undefined! }}
          validate={(values) => {
            const errors: Partial<typeof values> = {};
            const regex = /^\d{6}$|^\d{8}$/;
            if (!regex.test(values.pzn.trim())) {
              errors.pzn = t('invalidPzn');
            }
            if (values.name.trim().length > 94) {
              errors.name = t('invalidName');
            }
            if (values.pzn.trim() == '') {
              errors.pzn = `${t('pzn')} ${t('fieldRequired')}`;
            }
            if (values.name.trim() == '') {
              errors.name = `${t('name')} ${t('fieldRequired')}`;
            }
            return errors;
          }}
          onSubmit={onSubmitFreetext}
        >
          {({ submitForm, errors, touched, submitCount, values }) => {
            return (
              <Box p={12}>
                <Flex justify="space-between" align="center" gap={20}>
                  <FormGroup2
                    name="pzn"
                    isRequired
                    style={{ flexGrow: '1' }}
                    label={t('pzn')}
                    errors={errors}
                    touched={touched}
                    customError={(err, defaultContent) => {
                      if (
                        err !==
                        tError(ErrorCode.ErrorCode_Diga_PznAlreadyExists)
                      ) {
                        return defaultContent;
                      }
                      return (
                        <div className="bp5-form-helper-text">
                          <Flex align="center">
                            <HelpText
                              submitCount={submitCount}
                              touched={!!touched['pzn']}
                              err={err}
                            />
                            <Button
                              onClick={() => {
                                setIsOpen(false);
                                onSearchByPzn(values['pzn']);
                              }}
                              minimal
                              intent={Intent.PRIMARY}
                              small
                            >
                              {t('searchForPzn')}
                            </Button>
                          </Flex>
                        </div>
                      );
                    }}
                  >
                    <FastField name="pzn">
                      {({ field, form }) => {
                        return (
                          <React.Fragment>
                            <InputGroup
                              required
                              defaultValue={field.value || ''}
                              onValueChange={(value) =>
                                form.setFieldValue(field.name, value)
                              }
                            />
                          </React.Fragment>
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                  <FormGroup2
                    isRequired
                    name="formName"
                    label={t('form')}
                    errors={errors}
                    touched={touched}
                  >
                    <FastField name="formName">
                      {({ field, form }) => {
                        return (
                          <FormSelect
                            onFormChange={(value) =>
                              form.setFieldValue(field.name, value)
                            }
                          />
                        );
                      }}
                    </FastField>
                  </FormGroup2>
                </Flex>
                <FormGroup2
                  isRequired
                  name="name"
                  label={t('name')}
                  errors={errors}
                  touched={touched}
                >
                  <FastField name="name">
                    {({ field, form }) => {
                      return (
                        <InputGroup
                          required
                          defaultValue={field.value || ''}
                          onValueChange={(value) =>
                            form.setFieldValue(field.name, value)
                          }
                        />
                      );
                    }}
                  </FastField>
                </FormGroup2>
                <Flex justify="flex-end" gap={12} mt={12}>
                  <Button type="button" onClick={() => onClose()}>
                    {tButtonActions('cancelText')}
                  </Button>
                  <Button
                    intent={Intent.PRIMARY}
                    onClick={async () => await submitForm()}
                  >
                    {tButtonActions('create')}
                  </Button>
                </Flex>
              </Box>
            );
          }}
        </Formik>
      </ConfirmDialog>
    </React.Fragment>
  );
};
export default DigaTable;
