import {
  BodyTextM,
  BodyTextS,
  Box,
  Button,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import { Tooltip } from '@tutum/design-system/components/Core';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { COLOR } from '@tutum/design-system/themes/styles';
import { SortField } from '@tutum/hermes/bff/app_mvz_diga';
import { DigaItem, DigaStatus } from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { ISort, priceFormat } from '@tutum/mvz/module_diga/utils';
import _ from 'lodash';

const plusIcon = '/images/plus.svg';
const halfCircle = '/images/half-circle.svg';
const checkCircleIcon = '/images/check-circle-solid-success.svg';
const sortableIcon = '/images/sortable-icon.svg';
const sortAscIcon = '/images/sort-ascending.svg';
const sortDescIcon = '/images/sort-descending.svg';
const infoIcon = '/images/circle-info.svg';

interface IGenColumns {
  t: IFixedNamespaceTFunction<keyof typeof DigaListLang.Overview>;
  onActionRow: (id: string) => void;
  loading?: boolean;
  sort: ISort;
  onSetSort: (_: ISort) => void;
}

export const genColumns = ({
  t,
  onActionRow,
  loading,
  sort,
  onSetSort,
}: IGenColumns): IDataTableColumn<DigaItem>[] => {
  const renderSortIcon = (currentField: SortField) => {
    if (!sort || sort.field !== currentField) return sortableIcon;
    if (sort.order === Order.ASC) return sortAscIcon;
    return sortDescIcon;
  };

  const onClick = (sortField: SortField) => {
    onSetSort({
      field: sortField,
      order:
        !sort || sort.field !== sortField || sort.order === Order.ASC
          ? Order.DESC
          : Order.ASC,
    });
  };

  return [
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('name')}</span>
          {
            <Svg
              src={renderSortIcon(SortField.SortField_Name)}
              style={{ cursor: 'pointer' }}
              onClick={() => onClick(SortField.SortField_Name)}
            />
          }
        </Flex>
      ),
      cell: (row) => {
        const icon = bindIcon(row.status);
        const status = bindStatus(row.status);
        return (
          <Box pt={5} pb={5} w="100%">
            <Flex justify="space-between" w="100%" className="diga_column_name">
              <Flex>
                <Box mr={8}>
                  <Svg
                    style={{ borderRadius: '50%' }}
                    src={row.icon.url}
                    size={24}
                  />
                </Box>
                <Box>
                  <BodyTextM
                    className="diga_text_name"
                    onClick={() => onActionRow(row.id)}
                    color={COLOR.TEXT_INFO}
                    cursor="pointer"
                    loading={loading}
                  >
                    {row.name}
                  </BodyTextM>
                  <BodyTextS>{row.gender.join(', ')}</BodyTextS>
                </Box>
              </Flex>
              <Flex>
                {icon && (
                  <Tooltip content={status!} position="top-left">
                    <Svg src={icon} size={16} />
                  </Tooltip>
                )}
              </Flex>
            </Flex>
          </Box>
        );
      },
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('pzn')}</span>
          <Svg
            src={renderSortIcon(SortField.SortField_Pzn)}
            style={{ cursor: 'pointer' }}
            onClick={() => onClick(SortField.SortField_Pzn)}
          />
        </Flex>
      ),
      width: '90px',
      selector: (row) => row.pzn,
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('module')}</span>
          <Svg
            src={renderSortIcon(SortField.SortField_Module)}
            style={{ cursor: 'pointer' }}
            onClick={() => onClick(SortField.SortField_Module)}
          />
        </Flex>
      ),
      selector: (row) => row.module,
    },
    {
      name: t('additionalDevice'),
      width: '200px',
      cell: (row) =>
        !!row.additionalDevice ? row.additionalDevice : 'Keine Zusatzgeräte',
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('targetGroup')}</span>
          <Tooltip fill content={t('icd10Allowed')} position="top">
            <Svg style={{ cursor: 'help' }} src={infoIcon} size={16} />
          </Tooltip>
        </Flex>
      ),
      cell: (row) => {
        return (
          <Flex column gap={8} justify="space-between">
            <p>{row.ageGroups.join(', ')}</p>
            <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
              {row.indications.join(', ')}
            </BodyTextS>
          </Flex>
        );
      },
    },
    {
      name: t('duration'),
      width: '100px',
      cell: (row) => row.duration,
    },
    {
      name: (
        <Flex align="center" justify="space-between">
          <span>{t('price')}</span>
          <Svg
            src={renderSortIcon(SortField.SortField_Price)}
            style={{ cursor: 'pointer' }}
            onClick={() => onClick(SortField.SortField_Price)}
          />
        </Flex>
      ),
      width: '90px',
      cell: (row) => (
        <Box w="100%" alignSelf="center">
          <BodyTextM textAlign="right">{priceFormat(row.price)}</BodyTextM>
        </Box>
      ),
    },
    {
      name: '',
      width: '52px',
      cell: (row) => {
        return (
          <Button minimal onClick={() => onActionRow(row.id)}>
            <Svg src={plusIcon} />{' '}
          </Button>
        );
      },
    },
  ];
};

export const bindIcon = (status: DigaStatus): Nullable<string> => {
  switch (status) {
    case DigaStatus.DIGA_STATUS_DRAFT:
      return halfCircle;
    case DigaStatus.DIGA_STATUS_ACTIVE:
      return checkCircleIcon;
    default:
      return undefined;
  }
};

export const bindStatus = (status: DigaStatus): Nullable<string> => {
  switch (status) {
    case DigaStatus.DIGA_STATUS_ACTIVE:
      return 'Dauerhaft aufgenommen';
    case DigaStatus.DIGA_STATUS_DRAFT:
      return 'Vorläufig aufgenommen';
    default:
      return undefined;
  }
};

export function isEqualArray<T>(a: T[], b: T[]): boolean {
  return _.isEqual(_.sortBy(a), _.sortBy(b));
}
