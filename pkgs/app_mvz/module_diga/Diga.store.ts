import { IMenuItem } from '@tutum/design-system/components';
import { SearchType } from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { proxy, useSnapshot } from 'valtio';

const DEFAULT_PAGE_SIZE = 10;

export type SearchDiga = {
  query: string;
  searchType: IMenuItem<SearchType>;
};

export interface ILoading {
  prescribedDigas: boolean;
}

export interface Pagination {
  pageNumber: number;
  pageSize: number;
}

export interface IDigaStore {
  search: SearchDiga;
  pagination: Pagination;
  prescribedDigas: any[];
  loading: ILoading;
  clearSearchBoxFunc?: Function;
  refetchPrescribe?: Function;
}

const initStore: IDigaStore = {
  search: { searchType: null!, query: '' },
  pagination: { pageNumber: 1, pageSize: DEFAULT_PAGE_SIZE },
  prescribedDigas: [],
  loading: {
    prescribedDigas: true,
  },
};

export interface IDigaActions {
  resetAll: () => void;
  setSearch: (searchType: SearchDiga) => void;
  getPrescribedDigas: () => void;
  setPageNumber: (_: number) => void;
  setPageSize: (_: number) => void;
  getPagination: () => Pagination;
  resetPagination: () => void;
  setResetSearchBox: (cb: Function) => void;
  setRefetchPrescfribe: (cb: Function) => void;
}

export const DigaStore = proxy<IDigaStore>(initStore);

export const DigaActions: IDigaActions = {
  setSearch: (searchPayload: SearchDiga) => {
    DigaStore.search = searchPayload;
  },
  getPrescribedDigas: () => {
    DigaStore.prescribedDigas = [];
  },
  setPageNumber: (page: number) => {
    DigaStore.pagination = {
      ...DigaStore.pagination,
      pageNumber: page,
    };
  },
  setPageSize: (pageSize: number) => {
    DigaStore.pagination = {
      ...DigaStore.pagination,
      pageSize: pageSize,
    };
  },
  getPagination: () => DigaStore.pagination,
  resetAll: () => {
    DigaStore.prescribedDigas = [];
  },
  resetPagination: () => {
    DigaStore.pagination = {
      pageNumber: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    };
  },
  setResetSearchBox: (cb: Function) => {
    DigaStore.clearSearchBoxFunc = cb;
  },
  setRefetchPrescfribe: (cb: Function) => {
    DigaStore.refetchPrescribe = cb;
  },
};

export function useDigaStore() {
  return useSnapshot(DigaStore);
}
