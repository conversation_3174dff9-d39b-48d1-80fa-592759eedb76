import {
  Body<PERSON>ext<PERSON>,
  <PERSON>,
  Button,
  Flex,
  Svg,
  Tooltip,
} from '@tutum/design-system/components';
import {
  Divider,
  Drawer,
  InputGroup,
  Intent,
  Spinner,
} from '@tutum/design-system/components/Core';
import {
  PrescribeFilter,
  useMutationExportPrescribes,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { getPresignedGetURL } from '@tutum/hermes/bff/legacy/app_mvz_file';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import fileUtils from '@tutum/infrastructure/utils/file.util';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { IPatientSearchResult } from '@tutum/mvz/module_patient-management/patient-search/PatientSearch.type';
import { useState } from 'react';
import PrescribeFilterComponent from './Filter.styled';

const FilterIcon = '/images/filter.svg';
const FilterActiveIcon = '/images/fill-filter.svg';
const SearchIcon = '/images/search-sidebar-disable.svg';
const DownloadIcon = '/images/download.svg';

export interface IProps {
  search: string;
  setSearch: (_: string) => void;
  filter?: PrescribeFilter;
  setFilter: (_: PrescribeFilter) => void;
  isExport: boolean;
  t: IFixedNamespaceTFunction<keyof typeof DigaListLang.Overview>;
}

const PrescribeSearch = (props: IProps) => {
  const [isShowFilter, setIsShowFilter] = useState(false);
  const [patient, setPatient] = useState<IPatientSearchResult | undefined>(undefined);

  const exportPrescribes = useMutationExportPrescribes({
    onSuccess: async ({ data }) => {
      const { data: responseData } = await getPresignedGetURL({
        bucketName: data.bucketName,
        objectName: data.fileName,
      });
      fileUtils.downloadFile(responseData.presignedURL, data.fileName);
    },
  });

  const handleExport = () => {
    exportPrescribes.mutate({ query: props.search, filter: props.filter });
  };

  return (
    <>
      <Flex p={20} align="stretch">
        <InputGroup
          style={{ width: 250 }}
          value={props.search}
          onChange={(e) => props.setSearch(e.target.value)}
          leftElement={
            <Svg
              width={17}
              style={{ margin: '11px 2px 0 5px' }}
              src={SearchIcon}
            />
          }
        />
        <Divider style={{ margin: '0 20px' }} />
        <Tooltip content={props.t('filter')}>
          <Button
            intent={props.filter ? Intent.PRIMARY : Intent.NONE}
            outlined
            iconOnly
            style={{ height: '100%', aspectRatio: 1 }}
            icon={<Svg src={props.filter ? FilterActiveIcon : FilterIcon} />}
            onClick={() => setIsShowFilter(true)}
          ></Button>
        </Tooltip>

        <Box style={{ marginLeft: 'auto' }}>
          <Button
            outlined
            disabled={!props.isExport}
            icon={
              exportPrescribes.isPending ? (
                <Spinner size={24} />
              ) : (
                <Svg src={DownloadIcon} />
              )
            }
            onClick={handleExport}
          >
            {props.t('export')}
          </Button>
        </Box>
      </Flex>

      <Drawer
        size="40%"
        title={
          <BodyTextL padding="10px 0" fontWeight="ExtraBold">
            {props.t('prescribeTitle')}
          </BodyTextL>
        }
        isOpen={isShowFilter}
        canEscapeKeyClose={false}
        onClose={() => setIsShowFilter(false)}
      >
        <PrescribeFilterComponent
          {...props}
          patient={patient}
          setPatient={setPatient}
          onClose={() => setIsShowFilter(false)}
        />
      </Drawer>
    </>
  );
};

export default PrescribeSearch;
