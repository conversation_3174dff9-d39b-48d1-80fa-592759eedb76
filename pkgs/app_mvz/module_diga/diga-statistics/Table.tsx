import {
  BodyTextM,
  Box,
  alertError,
  alertSuccessfully,
  alertWarning,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import {
  DigaDetail,
  PrescribeDetail,
  PrescribeFilter,
  PrescribeType,
  useMutationUpdatePrescribe,
  useQuerySearchPrescribe,
  validateFreeText,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { getPatientFormProfile } from '@tutum/hermes/bff/legacy/app_mvz_patient_profile';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import useToaster from '@tutum/mvz/hooks/useToaster';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import ViewDigaDetailDialog from '@tutum/mvz/module_diga/components/ViewDigaDetail';
import { genColumns } from '@tutum/mvz/module_diga/diga-statistics/helpers';
import { priceFormat } from '@tutum/mvz/module_diga/utils';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import MedicationProvider from '@tutum/mvz/module_medication/context/MedicationProvider';
import { MedicationPrintPreview } from '@tutum/mvz/module_medication_kbv';
import { medicationActions } from '@tutum/mvz/module_medication_kbv/medication/MedicationKBV.store';
import { medicationShoppingBagActions } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import { patientFileActions } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { ROUTING } from '@tutum/mvz/types/route.type';
import Router from 'next/router';
import { useContext, useState } from 'react';
import { mapFormNameToFormType } from '../Diga.helper';

export interface IProps {
  filter: PrescribeFilter | undefined;
  query: string;
  setExport: (_: boolean) => void;
  t: IFixedNamespaceTFunction<keyof typeof DigaListLang.Overview>;
}

const PrescribeTable = (
  props: IProps & {
    setFormProfile: (_: any) => void;
    setUserId: (_: string) => void;
  }
) => {
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeItem, setActiveItem] = useState<string | undefined>(undefined);
  const [digaDetail, setDigaDetail] = useState<DigaDetail | undefined>(undefined);
  const [notes, setNotes] = useState<{
    [key: string]: string;
  }>({});

  const {
    data: prescribes,
    isLoading,
    refetch,
  } = useQuerySearchPrescribe(
    {
      query: props.query,
      filter: props.filter,
      pagination: {
        sortBy: '',
        order: Order.ASC,
        page: pageNumber,
        pageSize: pageSize,
      },
    },
    {
      select(data) {
        props.setExport(data.data?.prescribes?.length !== 0);
        return data.data;
      },
    }
  );

  const medicationContext = useContext(MedicationContext);

  const viewDigaPrescribe = async (prescribeDetail: PrescribeDetail) => {
    const formProfile = await getPatientFormProfile({
      patientID: prescribeDetail.patient.id,
      doctorID: prescribeDetail.prescribe.doctorId,
      scheinId: prescribeDetail.prescribe.scheinId,
    });
    props.setFormProfile(formProfile.data.profile);
    props.setUserId(prescribeDetail.patient.id);

    medicationActions.setRefillFormData(undefined);
    medicationContext.setViewFormTimeline({
      treatmentDoctorId: prescribeDetail.prescribe.doctorId,
      assignedToBsnrId: prescribeDetail.prescribe.assignedToBsnrId!,
      formInfo: {
        id: prescribeDetail.prescribe.id!,
        formInfoResponse: [],
        formSetting: JSON.stringify(
          prescribeDetail?.prescribe?.formInfo?.formSetting ?? {}
        ),
        currentFormType: mapFormNameToFormType(
          prescribeDetail?.prescribe.formInfo.formName
        ),
        prescribeDate: new Date().getTime(),
        isNotPicked: false,
        isShowFavHint: false,
      },
    });
    medicationShoppingBagActions.setDigaInfo(
      prescribeDetail?.prescribe,
      prescribeDetail?.prescribe.formInfo.formName,
      prescribeDetail?.prescribe.type
    );
    medicationShoppingBagActions.onPrint(refetch);
  };
  const toast = useToaster();

  const refillDiga = async ({ prescribe, diga, patient }: PrescribeDetail) => {
    // freetext flow just like in DigaEntryTimeline
    if (prescribe.type === PrescribeType.FREETEXT) {
      try {
        const formProfile = await getPatientFormProfile({
          patientID: patient.id,
          doctorID: prescribe.doctorId,
          scheinId: prescribe.scheinId,
        });
        props.setFormProfile(formProfile.data.profile);
        props.setUserId(patient.id);
        await validateFreeText({
          name: prescribe.digaName,
          pzn: prescribe.digaPzn,
        });
      } catch (error) {
        const serverError = error?.response?.data?.serverError;
        if (serverError === ErrorCode.ErrorCode_Diga_PznAlreadyExists) {
          alertWarning(props.t('pznExists'), { toaster: toast });
          return;
        }
        throw error;
      }
      medicationContext.setShowPrintPreviewState(true);
      medicationShoppingBagActions.setDigaInfo(
        {
          ...prescribe,
          id: null!,
          formInfo: {
            ...prescribe.formInfo,
            prescribeDate: null!,
          },
        },
        prescribe.formInfo.formName,
        prescribe.type
      );

      medicationShoppingBagActions.onPrescribe(() => {
        refetch();
        setPageNumber(1);
        medicationShoppingBagActions.onPrescribe(() => {});
      });
      return;
    }
    // diga flow
    patientFileActions.setDigaDetail(diga);
    const url =
      ROUTING.PATIENT.replace('[patientId]', patient.id) + ID_TABS.DIGA;
    Router.push(url);
  };

  const updatePrescribe = useMutationUpdatePrescribe({
    onSuccess: async () => {
      alertSuccessfully(props.t('successNote'));
      await refetch();
      setTimeout(() => {
        (
          document.querySelector(
            `[data-test-id="prescribed-note-${activeItem}"`
          ) as any
        )?.blur();
      });
    },
    onError: (_) => {
      alertError(props.t('failNote'));
      refetch();
    },
  });

  const onUpdateNote = async (id: string, content: string) => {
    await updatePrescribe.mutateAsync({
      id,
      note: content,
    });
    setActiveItem(undefined);
  };

  return (
    <>
      <Box style={{ position: 'relative' }}>
        <Table
          progressPending={isLoading}
          customStyles={{
            table: {
              style: {
                maxHeight: 'calc(100vh - 200px)',
              },
            },
            pagination: {
              style: {
                position: 'fixed',
                bottom: 0,
                height: 40,
                minHeight: 'unset',
              },
            },
            rows: {
              style: {
                minHeight: 'auto',
              },
            },
            headCells: {
              style: {
                padding: 12,
              },
            },
            cells: {
              style: {
                padding: '4px 12px',
              },
            },
            header: {
              style: {
                minHeight: 0,
              },
            },
          }}
          columns={genColumns({
            t: props.t,
            viewDigaPrescribe,
            viewDigaDetail: setDigaDetail,
            refillDiga,
            onUpdateNote,
            isMutating: updatePrescribe.isPending,
            notes,
            activeItem: activeItem!,
            setActiveItem: (item) => {
              if (!updatePrescribe.isPending) {
                setActiveItem(item);
              }
            },
            setNotes,
          })}
          data={prescribes?.prescribes ?? []}
          pagination
          paginationServer
          striped
          highlightOnHover
          paginationTotalRows={prescribes?.pagination?.total ?? 0}
          paginationPerPage={pageSize}
          onChangeRowsPerPage={(currentRowPerPage) => {
            setPageNumber(1);
            setPageSize(currentRowPerPage);
          }}
          onChangePage={setPageNumber}
          footer={
            prescribes && prescribes.prescribes.length > 0 ? (
              <BodyTextM
                style={{ position: 'fixed', right: 20, bottom: 10 }}
                fontWeight="Bold"
              >
                {`${props.t('totalPrescribe')}: ${priceFormat(
                  prescribes?.totalPrice
                )}`}
              </BodyTextM>
            ) : null
          }
        ></Table>
      </Box>
      {digaDetail && (
        <ViewDigaDetailDialog
          digaDetail={digaDetail}
          onClose={() => {
            setDigaDetail(undefined);
          }}
        />
      )}
    </>
  );
};

const PrescribeTableWithMedication = (props: IProps) => {
  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();

  const [formProfile, setFormProfile] = useState<{
    [key: string]: string;
  } | undefined>(undefined);
  const [userId, setUserId] = useState<string | null>(null);

  return (
    <MedicationProvider value={undefined!}>
      <PrescribeTable
        {...props}
        setFormProfile={setFormProfile}
        setUserId={setUserId}
      />
      <MedicationPrintPreview
        medicineType="diga"
        bsnr={currentLoggedinUser?.bsnr}
        patient={{ id: userId } as any}
        formProfile={formProfile}
      />
    </MedicationProvider>
  );
};

export default PrescribeTableWithMedication;
