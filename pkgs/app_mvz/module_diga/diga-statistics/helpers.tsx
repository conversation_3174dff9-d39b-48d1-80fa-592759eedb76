import {
  BodyTextM,
  BodyTextS,
  Box,
  Flex,
  Svg,
} from '@tutum/design-system/components';
import {
  Menu,
  MenuItem,
  Popover,
  PopoverPosition,
  Spinner,
} from '@tutum/design-system/components/Core';
import { IDataTableColumn } from '@tutum/design-system/components/Table';
import Tooltip from '@tutum/design-system/components/Tooltip/Tooltip';
import { formatUnixToDateString } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  DigaDetail,
  PrescribeDetail,
  PrescribeType,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import {
  renderDigaIcon,
  StyledTextArea,
} from '@tutum/mvz/module_diga/components/PrescribeTable/helper';
import { priceFormat } from '@tutum/mvz/module_diga/utils';
import { ROUTING } from '@tutum/mvz/types/route.type';
import Router from 'next/router';
import { Dispatch, SetStateAction } from 'react';
const moreVertical = '/images/more-vertical.svg';
const eyeOnIcon = '/images/eye-on.svg';
const userIcon = '/images/user-icon.svg';
const alertTriangle = '/images/alert-triangle.svg';
const refreshCwIcon = '/images/refresh-cw.svg';

interface IGenColumns {
  t: IFixedNamespaceTFunction<keyof typeof DigaListLang.Overview>;
  viewDigaPrescribe: (_: PrescribeDetail) => void;
  viewDigaDetail: Dispatch<SetStateAction<DigaDetail>>;
  refillDiga: (_: PrescribeDetail) => void;
  onUpdateNote: (id: string, content: string) => Promise<void>;
  isMutating: boolean;
  notes: {
    [key: string]: string;
  };
  activeItem: string;
  setActiveItem: (string) => void;
  setNotes: Dispatch<
    SetStateAction<{
      [key: string]: string;
    }>
  >;
}

export const genColumns = ({
  t,
  viewDigaPrescribe,
  viewDigaDetail,
  refillDiga,
  onUpdateNote,
  isMutating,
  notes,
  activeItem,
  setActiveItem,
  setNotes,
}: IGenColumns): IDataTableColumn<PrescribeDetail>[] => [
  {
    name: t('date'),
    width: '150px',
    cell: (row) => {
      const dateStr = formatUnixToDateString(row.date);
      const date = new Date(row.date);
      const formattedTime = `${String(date.getHours()).padStart(
        2,
        '0'
      )}:${String(date.getMinutes()).padStart(2, '0')}`;
      return (
        <Box py={5}>
          <Flex column={true} gap={10} justify="space-between" w="100%">
            <Box>{dateStr}</Box>
            <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
              {formattedTime}
            </BodyTextS>
          </Flex>
        </Box>
      );
    },
  },
  {
    name: t('DiGA'),
    cell: (row) => (
      <Box py={5}>
        <Flex justify="space-between" w="100%" className="diga_column_name">
          <Flex gap={8} align="center">
            <Box>{renderDigaIcon(row.diga?.icon?.url!)}</Box>
            <Box>
              <BodyTextM fontWeight="SemiBold">
                {row.prescribe.digaTitle || row.prescribe.digaName}
              </BodyTextM>
              <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
                {row?.diga?.gender?.join(', ')}
              </BodyTextS>
            </Box>
          </Flex>
        </Flex>
      </Box>
    ),
  },
  {
    name: t('pzn'),
    width: '150px',
    cell: (row) => {
      return (
        <Flex justify="space-between" style={{ width: '100%' }}>
          <BodyTextM>{row.prescribe.digaPzn}</BodyTextM>
          {row.isExpired && (
            <Tooltip content={t('digaExpired')}>
              <Svg src={alertTriangle} />
            </Tooltip>
          )}
        </Flex>
      );
    },
  },
  {
    name: t('prescribedBy'),
    cell: (row) => {
      return <BodyTextM>{row.doctorName}</BodyTextM>;
    },
  },
  {
    name: t('patient'),
    cell: (row) => {
      const dateStr = formatUnixToDateString(row.patient.dateOfBirth);
      let text = dateStr;
      if (row.patient.insuranceNumber) {
        text += ' - ' + row.patient.insuranceNumber;
      }
      return (
        <Flex column gap={5}>
          <BodyTextM
            color={COLOR.TEXT_INFO}
            fontWeight="SemiBold"
            cursor="pointer"
            onClick={() => goToPatientPage(row.patient.id)}
          >
            {row.patient.fullName}
          </BodyTextM>
          <BodyTextS>{text}</BodyTextS>
        </Flex>
      );
    },
  },
  {
    name: t('note'),
    cell: (row: PrescribeDetail) => {
      if (row.prescribe.id === activeItem && isMutating) {
        return (
          <Flex justify="center" style={{ width: '100%' }}>
            <Spinner size={30} />
          </Flex>
        );
      }
      return (
        <StyledTextArea
          data-test-id={`prescribed-note-${row.prescribe.id}`}
          id={row.prescribe.id}
          onBlur={() => {
            setActiveItem(null);
            setNotes((prevValues) => {
              return {
                ...prevValues,
                [row.prescribe.id!]: row.prescribe.note,
              };
            });
          }}
          onFocus={() => {
            setActiveItem(row.prescribe.id);
            setNotes((prevValues) => {
              return {
                ...prevValues,
                [row.prescribe.id!]: row.prescribe.note,
              };
            });
          }}
          onKeyDown={(event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
              event.preventDefault(); // Prevents the default behavior of adding a new line
              onUpdateNote(row.prescribe.id!, event.currentTarget.value);
            }
          }}
          onChange={(event) => {
            setNotes((prevValues) => {
              return {
                ...prevValues,
                [row.prescribe.id!]: event.target.value,
              };
            });
          }}
          value={notes[row.prescribe.id!] ?? row.prescribe.note}
          defaultValue={row.prescribe.note}
        />
      );
    },
  },
  {
    name: t('price'),
    width: '120px',
    cell: (row) => (
      <Box w="100%" alignSelf="center" className="tabular-nums">
        <BodyTextM textAlign="right">
          {priceFormat(row.diga?.price.amount!)}
        </BodyTextM>
      </Box>
    ),
  },
  {
    name: '',
    width: '40px',
    cell: (row) => {
      return (
        <Popover
          usePortal
          position={PopoverPosition.LEFT}
          content={
            <>
              <Menu className="sl-menu-action" key="menu-more">
                <MenuItem
                  icon={<Svg src={eyeOnIcon} size={16} />}
                  text={t('viewPrescribe')}
                  onClick={() => {
                    viewDigaPrescribe(row);
                  }}
                />
                {row.prescribe.type === PrescribeType.PRESCRIBED && (
                  <MenuItem
                    icon={<Svg src={eyeOnIcon} size={16} />}
                    text={t('viewDiga')}
                    onClick={() => {
                      viewDigaDetail(row.diga!);
                    }}
                  />
                )}
                {!row.isExpired && (
                  <MenuItem
                    icon={<Svg src={refreshCwIcon} size={16} />}
                    text={t('refill')}
                    onClick={() => {
                      refillDiga(row);
                    }}
                  />
                )}
                <MenuItem
                  icon={<Svg src={userIcon} size={16} />}
                  text={t('goToPatient')}
                  onClick={() => goToPatientPage(row.patient.id)}
                />
              </Menu>
            </>
          }
        >
          <Svg
            style={{ cursor: 'pointer' }}
            className={'sl-actions'}
            src={moreVertical}
          />
        </Popover>
      );
    },
  },
];

function goToPatientPage(id: string) {
  const url = ROUTING.PATIENT.replace('[patientId]', id);
  Router.push(url);
}
