import { Divider } from '@tutum/design-system/components/Core';
import { H2 } from '@tutum/design-system/components/Typography';
import { PrescribeFilter } from '@tutum/hermes/bff/legacy/app_mvz_diga';
import I18n from '@tutum/infrastructure/i18n';
import { useDebounce } from '@tutum/infrastructure/hook';

import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { useState } from 'react';
import PrescribeSearch from './Search';
import PrescribeTable from './Table';

function DigaPrescribe() {
  const { t } = I18n.useTranslation<keyof typeof DigaListLang.Overview>({
    namespace: 'Diga',
    nestedTrans: 'Overview',
  });

  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState<PrescribeFilter | undefined>(undefined);
  const [isExport, setIsExport] = useState(true);
  const query = useDebounce(search, 500);

  return (
    <>
      <H2 padding={20}>{t('DiGA')}</H2>
      <Divider style={{ margin: '0 10px' }} />
      <PrescribeSearch
        t={t}
        search={search}
        setSearch={setSearch}
        filter={filter}
        setFilter={setFilter}
        isExport={isExport}
      />
      <PrescribeTable
        t={t}
        query={query}
        filter={filter}
        setExport={setIsExport}
      />
    </>
  );
}

export default DigaPrescribe;
