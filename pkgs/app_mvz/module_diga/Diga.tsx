import {
  BodyTextL,
  BodyTextM,
  Box,
  Button,
  Flex,
  Svg,
  alertWarning,
} from '@tutum/design-system/components';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { SearchType } from '@tutum/hermes/bff/app_mvz_diga';
import type {
  PrescribeDetail,
  SearchRequest,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import {
  DigaDetail,
  PrescribeType,
  useQueryGetDigaDetail,
  useQuerySearch,
  useQuerySearchSimilarDiga,
  validateFreeText,
} from '@tutum/hermes/bff/legacy/app_mvz_diga';
import { Order } from '@tutum/hermes/bff/legacy/common';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { useDebounce } from '@tutum/infrastructure/hook';
import I18n from '@tutum/infrastructure/i18n';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import useToaster from '@tutum/mvz/hooks/useToaster';
import type DigaListLang from '@tutum/mvz/locales/en/Diga.json';
import { ISort, defaultSortName } from '@tutum/mvz/module_diga/utils';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import MedicationProvider from '@tutum/mvz/module_medication/context/MedicationProvider';
import { MedicationPrintPreview } from '@tutum/mvz/module_medication_kbv';
import { medicationShoppingBagActions } from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.store';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { useContext, useDeferredValue, useEffect, useState } from 'react';
import { DigaActions, useDigaStore } from './Diga.store';
import { IDigaProps } from './Diga.type';
import DigaDetailComponent from './components/DigaDetail';
import DigaSearch from './components/DigaSearch/DigaSearch.styled';
import DigaTable from './components/DigaTable';
import PrescribeTable from './components/PrescribeTable';

const ArrowLeft = '/images/arrow-left-new.svg';

export type IProps = IDigaProps;

enum TableType {
  Diga,
  Prescribe,
  Similar,
}

const initialSearch: SearchRequest = {
  query: '',
  type: SearchType.SEARCH_TYPE_UNSPECIFIED,
  pagination: { sortBy: '', order: Order.ASC, page: 1, pageSize: 10 },
  filter: undefined,
};
const DigaTab = (props: IProps) => {
  const { t } = I18n.useTranslation<keyof typeof DigaListLang.Overview>({
    namespace: 'Diga',
    nestedTrans: 'Overview',
  });
  const { className, patient } = props;
  const [tableType, setTableType] = useState(TableType.Prescribe);
  const [digaDetailId, setDigaDetailId] = useState<Nullable<string>>(undefined);
  const [similarDiga, setSimilarDiga] = useState<DigaDetail | undefined>(undefined);
  const [cachedDiga, setCachedDiga] = useState<Nullable<DigaDetail>>(undefined);
  const [isRefill, setIsRefill] = useState(false);
  const { digaDetail: digaDetailFromStore } = usePatientFileStore();

  useEffect(() => {
    if (digaDetailFromStore) {
      setIsRefill(true);
      setIsShowDetail(true);
      setDigaDetailId(digaDetailFromStore.id);
      setCachedDiga(digaDetailFromStore);
      patientFileActions.setDigaDetail(undefined);
    }
  }, [digaDetailFromStore]);

  const {
    data: digaDetail,
    fetchStatus,
    isLoading: isLoadingGetDigaDetail,
  } = useQueryGetDigaDetail(
    {
      id: digaDetailId!,
    },
    {
      enabled: !!digaDetailId,
      select(data) {
        return data?.data?.digaDetail;
      },
    }
  );

  const toast = useToaster();
  const medicationContext = useContext(MedicationContext);

  const digaDetailLoading = isLoadingGetDigaDetail && fetchStatus != 'idle';
  const { pagination } = useDigaStore();
  const [search, setSearch] = useState<SearchRequest>(initialSearch);
  const debounceSearch = useDebounce(search?.query || '', 500);
  const deferredSearch = useDeferredValue(debounceSearch);
  const debounceFilter = useDebounce(search?.filter, 500);
  const {
    data,
    isLoading: isSearchLoading,
    refetch,
  } = useQuerySearch(
    {
      ...search,
      query: deferredSearch,
      filter: debounceFilter,
      pagination: {
        ...search.pagination,
        page: pagination.pageNumber,
        pageSize: pagination.pageSize,
      },
    },
    {
      select(data) {
        return data?.data;
      },
      enabled: tableType === TableType.Diga,
    }
  );

  const [sort, setSort] = useState<ISort>(defaultSortName);
  const { data: digasSimilar, isLoading: isDigasSimilarLoading } =
    useQuerySearchSimilarDiga(
      {
        digaId: similarDiga?.id!,
        pagination: {
          sortBy: sort.field,
          order: sort.order,
          page: pagination.pageNumber,
          pageSize: pagination.pageSize,
        },
      },
      {
        select(data) {
          return data?.data;
        },
        enabled: !!similarDiga && tableType === TableType.Similar,
      }
    );
  const [isShowDetail, setIsShowDetail] = useState(false);
  const digas = tableType === TableType.Similar ? digasSimilar : data;
  const isLoading =
    tableType === TableType.Similar ? isDigasSimilarLoading : isSearchLoading;
  const digaSimilarOriginalId =
    tableType === TableType.Similar ? similarDiga?.id : '';

  const resetSearchBox = () => {
    setSearch(initialSearch);
    setTableType(TableType.Prescribe);
    refetch();
  };

  const onSearchByPzn = (pzn: string) => {
    setSearch((prev) => ({
      ...prev,
      type: SearchType.SEARCH_TYPE_PZN,
      query: pzn,
    }));
  };

  const onSetSort = (sort: ISort) => {
    setSort(sort);
    setSearch((prev) => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        sortBy: sort.field,
        order: sort.order,
      },
    }));
  };

  return (
    <Box w="100%" className={className}>
      {tableType === TableType.Similar ? (
        <Flex gap={20} align="center">
          <Button
            style={{ padding: '10px 5px 5px 5px', margin: '0 0 0 10px' }}
            outlined
            onClick={() => {
              DigaActions.resetPagination();
              setTableType(TableType.Diga);
            }}
          >
            <Svg src={ArrowLeft}></Svg>
          </Button>
          <BodyTextL fontWeight="Bold">{`${t('similarTo')} "${
            similarDiga?.title
          }"`}</BodyTextL>
        </Flex>
      ) : (
        <DigaSearch
          className="sl-DigaSearch"
          query={search?.query}
          searchType={search?.type}
          setSearch={setSearch}
          isSearching={tableType === TableType.Diga}
          triggerSearch={() => {
            refetch();
            setTableType(TableType.Diga);
          }}
          resetSearchBox={resetSearchBox}
          onSetSort={onSetSort}
          onSearch={(payload) => {
            setSearch((prev) => ({
              ...payload,
              pagination: {
                ...payload.pagination,
                sortBy: prev?.pagination?.sortBy,
                order: prev?.pagination?.order,
              },
            }));
          }}
        />
      )}
      {tableType !== TableType.Prescribe && (
        <DigaTable
          digaOriginalId={digaSimilarOriginalId!}
          digas={digas?.items || []}
          totalRow={
            (tableType === TableType.Diga && data?.pagination.total) ||
            digasSimilar?.pagination.total ||
            0
          }
          onActionRow={(id) => {
            setIsRefill(false);
            setIsShowDetail(true);
            setDigaDetailId(id);
          }}
          loading={isLoading}
          rowLoading={digaDetailLoading}
          patientId={patient.id}
          resetSearchBox={resetSearchBox}
          searchText={search?.query ?? ''}
          onSearchByPzn={onSearchByPzn}
          footer={
            digas?.items?.length && data?.lastUpdated ? (
              <BodyTextM style={{ position: 'fixed', right: 20, bottom: 10 }}>
                {`${t('LastUpdated')}: ${datetimeUtil.dateTimeFormat(
                  data.lastUpdated,
                  DATE_FORMAT
                )}`}
              </BodyTextM>
            ) : null
          }
          onSetSort={onSetSort}
          sort={sort}
        />
      )}
      {isShowDetail && digaDetail && (
        <DigaDetailComponent
          diga={digaDetail}
          loading={digaDetailLoading}
          isRefill={isRefill}
          onOpenSimilarDiga={() => {
            setSimilarDiga(digaDetail);
            setIsShowDetail(false);
            DigaActions.resetPagination();
            setTableType(TableType.Similar);
          }}
          onClose={() => {
            setIsShowDetail(false);
            refetch();
          }}
          cachedDiga={cachedDiga}
        />
      )}
      {tableType === TableType.Prescribe && (
        <PrescribeTable
          patient={patient}
          onActionRow={async ({ prescribe, diga }: PrescribeDetail) => {
            // freetext flow just like in DigaEntryTimeline
            if (prescribe.type === PrescribeType.FREETEXT) {
              try {
                await validateFreeText({
                  name: prescribe.digaName,
                  pzn: prescribe.digaPzn,
                });
              } catch (error) {
                const serverError = error?.response?.data?.serverError;
                if (serverError === ErrorCode.ErrorCode_Diga_PznAlreadyExists) {
                  alertWarning(t('pznExists'), { toaster: toast });
                  return;
                }
                throw error;
              }
              medicationContext.setShowPrintPreviewState(true);
              medicationShoppingBagActions.setDigaInfo(
                {
                  ...prescribe,
                  id: null!,
                  formInfo: {
                    ...prescribe.formInfo,
                    prescribeDate: null!,
                  },
                },
                prescribe.formInfo.formName,
                prescribe.type
              );
              return;
            }
            // prescribe diga flow
            setIsRefill(true);
            setDigaDetailId(diga?.id);
            setIsShowDetail(true);
            setCachedDiga(diga);
          }}
        />
      )}
    </Box>
  );
};

const DigaTabWithMedication = (props: IProps) => {
  return (
    <MedicationProvider value={undefined!}>
      <DigaTab {...props} />
      <MedicationPrintPreview medicineType="diga" patient={props.patient} />
    </MedicationProvider>
  );
};

export default DigaTabWithMedication;
