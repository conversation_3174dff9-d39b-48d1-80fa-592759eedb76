import React, { memo } from 'react';

import type EDocumentsI18n from '@tutum/mvz/locales/en/EDocuments.json';
import { BodyTextM, Flex } from '@tutum/design-system/components';
import { EAUModel, Error } from '@tutum/hermes/bff/legacy/eau_common';
import { ErrorCode } from '@tutum/hermes/bff/legacy/error_code';
import { COLOR } from '@tutum/design-system/themes/styles';
import i18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { SPACE_NUMBER } from '@tutum/design-system/styles';

export interface EAUErrorProps {
  className?: string;
  eauDoc: EAUModel;
}

const ERROR_CODE_100 = '100';
const ERROR_CODE_101 = '101';
const ERROR_CODE_102 = '102';
const ERROR_DESCRIPTION_100 = 'errorDescription_100';
const ERROR_DESCRIPTION_101_102 = 'errorDescription_101_102';

const EAUError = ({ className, eauDoc }: EAUErrorProps) => {
  const { t: tEAU } = i18n.useTranslation<keyof typeof EDocumentsI18n.EAU>({
    namespace: 'EDocuments',
    nestedTrans: 'EAU',
  });
  const { errors, warning } = eauDoc;
  const errInfo = {
    docId: eauDoc.id,
    insuranceCompanyName: eauDoc.insuranceInfo?.insuranceCompanyName,
    sentOn: datetimeUtil.dateTimeFormat(eauDoc.sentOn, DATE_FORMAT),
    patientName: eauDoc.patientName,
  };

  const getErrorMessage = (err: Error) => {
    if (err.code === ERROR_CODE_100) {
      return tEAU(ERROR_DESCRIPTION_100, errInfo);
    }

    if (err.code === ERROR_CODE_101 || err.code === ERROR_CODE_102) {
      return tEAU(ERROR_DESCRIPTION_101_102);
    }

    if (err.code === ErrorCode.ErrorCode_EAU_NotTransmitted) {
      return tEAU(ErrorCode.ErrorCode_EAU_NotTransmitted, {
        insuranceCompanyName: eauDoc.insuranceInfo?.insuranceCompanyName,
        patientName: eauDoc.patientName,
      });
    }

    return tEAU(err.description as keyof typeof EDocumentsI18n.EAU, errInfo);
  };

  return (
    <Flex className={className} column>
      <Flex
        className="sl-eau-error__body"
        gap={SPACE_NUMBER.SPACE_XS}
        column
        p={SPACE_NUMBER.SPACE_S}
      >
        {errors?.map((err, index) => {
          return (
            <Flex column key={index}>
              <BodyTextM color={COLOR.TAG_BACKGROUND_RED}>
                {getErrorMessage(err)}
              </BodyTextM>
            </Flex>
          );
        })}
        {warning && (
          <Flex align="flex-start" gap={SPACE_NUMBER.SPACE_XS}>
            <BodyTextM color={COLOR.TEXT_WARNING}>
              {tEAU(warning.code, errInfo)}
            </BodyTextM>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
};

export default memo(EAUError);
