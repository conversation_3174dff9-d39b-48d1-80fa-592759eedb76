import React, { memo, useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import type EDocumentsI18n from '@tutum/mvz/locales/en/EDocuments.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';

import {
  BodyTextM,
  BodyTextS,
  Button,
  Flex,
  Svg,
  Tooltip,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { ActionsBar } from '../actions-bar';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { eDocumentsActions, useEDocumentsStore } from '../EDocuments.store';
import {
  DialogInfoType,
  EDocumentActions,
  IButtonsActionType,
} from '../EDocuments.types';
import { EAUModel, StatusText } from '@tutum/hermes/bff/eau_common';
import {
  useMutationCancelEAU,
  useMutationRemoveEAU,
  useMutationSignAndSendEAU,
  useQueryGetEAU,
  useMutationPrint,
} from '@tutum/hermes/bff/legacy/app_mvz_eau';
import { useListenEAUChanged } from '@tutum/hermes/bff/app_mvz_eau';
import i18n from '@tutum/infrastructure/i18n';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/mvz/constant/dateTime';
import { EDocumentStatusTag } from '@tutum/mvz/components/e-document-status-tag';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import { MenuItemClasses } from '@tutum/design-system/components/Core/MenuItem';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { COLOR } from '@tutum/design-system/themes/styles';
import { getPresignedURL } from '../EDocument.helper';
import {
  printSettingsActions,
  usePrintSettingsStore,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import { FormName } from '@tutum/hermes/bff/form_common';
import EAUError from './EAUError.styled';
import { GlobalData } from '@tutum/mvz/contexts/Global.context';
import { DocumentStatus } from '@tutum/hermes/bff/legacy/qes_common';
import { EAUCopier, WarningCode } from '@tutum/hermes/bff/legacy/eau_common';
import { CardTypeType } from '@tutum/hermes/bff/card_common';
import formUtil from '@tutum/infrastructure/utils/form.util';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import type PatientOverview from '@tutum/mvz/locales/en/PatientOverview.json';
import {
  FormPrintPreviewClasses,
  FormPrintPreviewLayout,
} from '@tutum/mvz/components/form-print-preview-layout';
import { FormEAUStylesheet } from '@tutum/mvz/components/form-eau-stylesheet';

const SendIcon = '/images/send.svg';
const PrintIcon = '/images/printer.svg';
const RemoveIcon = '/images/trash-2.svg';
const MoreIcon = '/images/more-vertical.svg';
const EyeIcon = '/images/eye-on.svg';
const CancelIcon = '/images/x-circle.svg';
const ErrorIcon = '/images/alert-triangle-solid.svg';
const WarningIcon = '/images/alert-circle-solid.svg';
const EyeOnIcon = '/images/eye-on.svg';
const UserIcon = '/images/user-gray.svg';
const ERROR_404 = '404';
const readCardIcon = '/images/patient/selector/read-card-icon.svg';

const heightIcon = 16;
const weightIcon = 16;

export interface EAUProps {
  className?: string;
  globalData: GlobalData;
}

const EAU = (props: EAUProps) => {
  const { className, globalData } = props;
  const { pagination, filter, selectedRowId, eAU, signatureInfo } =
    useEDocumentsStore();
  const router = useRouter();

  const { t: tEAU } = i18n.useTranslation<keyof typeof EDocumentsI18n.EAU>({
    namespace: 'EDocuments',
    nestedTrans: 'EAU',
  });
  const { t } = i18n.useTranslation<keyof typeof EDocumentsI18n>({
    namespace: 'EDocuments',
  });
  const { t: tConfirmDialog } = i18n.useTranslation<
    keyof typeof EDocumentsI18n.ConfirmDialog
  >({
    namespace: 'EDocuments',
    nestedTrans: 'ConfirmDialog',
  });
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonI18n.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: t2 } = i18n.useTranslation<
    keyof typeof PatientOverview.PatientOverviewList
  >({
    namespace: 'PatientOverview',
    nestedTrans: 'PatientOverviewList',
  });

  const { isPrinting } = usePrintSettingsStore();
  const [infoDialog, setInfoDialog] = useState<DialogInfoType | undefined>(undefined);
  const [signingCard, setSigningCard] = useState<CardTypeType | undefined>(undefined);
  const [viewingEau, setViewingEau] = useState<EAUModel | undefined>(undefined);

  const { data, isSuccess, isLoading, refetch } = useQueryGetEAU({
    pagination,
    statuses: filter.statuses as DocumentStatus[],
    query: filter.searchKey,
    dateRange: {
      startDate: filter.createdAtBegin!,
      endDate: filter.createdAtEnd!,
    },
  });

  useEffect(() => {
    if (!signatureInfo) {
      return;
    }

    if (signatureInfo.doctorCard?.canSign) {
      setSigningCard(signatureInfo.doctorCard.cardType);
      return;
    }

    if (signatureInfo.practiceCard?.canSign) {
      setSigningCard(signatureInfo.practiceCard.cardType);
    }
  }, [signatureInfo]);

  useListenEAUChanged(() => {
    refetch();
  });

  const {
    mutate: remove,
    isPending: isLoadingRemove,
    isSuccess: isSuccessRemove,
  } = useMutationRemoveEAU({
    onSuccess: () => {
      alertSuccessfully(tEAU('deletedEAU'));
      eDocumentsActions.clearData();
      setInfoDialog(undefined);
      refetch();
    },
    onError: (error) => {
      alertError(error.response.data.message);
    },
  });

  const {
    mutate: signAndSend,
    isPending: isLoadingSignAndSend,
    isSuccess: isSuccessSignAndSend,
  } = useMutationSignAndSendEAU({
    onSuccess: () => {
      alertSuccessfully(tEAU('submitted'));
      eDocumentsActions.clearData();
      refetch();
    },
    onError: (error) => {
      alertError(error.response.data.message);
    },
  });

  const {
    mutate: cancel,
    isPending: isLoadingCancel,
    isSuccess: isSuccessCancel,
  } = useMutationCancelEAU({
    onSuccess: () => {
      alertSuccessfully(tEAU('eauCancelled'));
      eDocumentsActions.clearData();
      setInfoDialog(undefined);
      refetch();
    },
    onError: (error) => {
      alertError(error.response.data.message);
    },
  });

  const { mutate: print } = useMutationPrint({
    onError: (error) => {
      alertError(error.response.data.message);
    },
  });

  const getSelectedRows = (payload?: {
    excludedStatuses?: DocumentStatus[];
    includedStatuses?: DocumentStatus[];
  }) => {
    if (selectedRowId) {
      return data?.eAUItems.filter((item) => item.id === selectedRowId);
    }

    const res = eAU.selectedRowState.selectedRows.filter((selectedRow) => {
      if (!payload) {
        return true;
      }

      if (payload?.excludedStatuses) {
        return !payload?.excludedStatuses?.includes(selectedRow.status);
      }

      return payload?.includedStatuses?.includes(selectedRow.status);
    });

    if (res.length === 0) {
      alertError(tEAU('canNotPerformAction'));
      return [];
    }

    return res;
  };

  const handleDelete = async () => {
    const items = getSelectedRows();
    const ids = (items || []).map((item) => item.id!);
    remove({ documentIds: ids });
  };

  const handleCancel = async () => {
    const items = getSelectedRows({
      includedStatuses: [
        DocumentStatus.Status_Sent,
        DocumentStatus.Status_Delivered,
        DocumentStatus.Status_ConfirmationPending,
      ],
    });
    const ids = (items || []).map((item) => item.id!);
    cancel({
      documentIds: ids,
      bsnrCode: globalData.userProfile?.bsnr,
      cardType: signingCard!,
    });
  };

  const handlePrint = async ({
    forInsurance,
    forPatient,
    forEmployeer,
  }: {
    forInsurance?: boolean;
    forPatient?: boolean;
    forEmployeer?: boolean;
  }) => {
    const items = getSelectedRows({
      includedStatuses: [
        DocumentStatus.Status_Created,
        DocumentStatus.Status_Sent,
        DocumentStatus.Status_SendingError,
        DocumentStatus.Status_Delivered,
        DocumentStatus.Status_ConfirmationPending,
        DocumentStatus.Status_Sending,
        DocumentStatus.Status_SigningFailed,
        DocumentStatus.Status_NotDispatched,
      ],
    }) || [];
    if (items.length === 0) {
      return;
    }

    const promises: Array<Promise<string>> = [];
    const ids: string[] = [];
    items.forEach((item) => {
      ids.push(item.id!);
      if (item.pdfForInsuranceUrl && forInsurance) {
        promises.push(getPresignedURL(item.pdfForInsuranceUrl));
      }

      if (item.pdfForPatientUrl && forPatient) {
        promises.push(getPresignedURL(item.pdfForPatientUrl));
      }

      if (item.pdfForEmployerUrl && forEmployeer) {
        promises.push(getPresignedURL(item.pdfForEmployerUrl));
      }
    });
    print({
      documentIds: ids,
      printOption: {
        printForEmployer: forEmployeer!,
        printForInsurance: forInsurance!,
        printForPatient: forPatient!,
      },
    });
    const files = await Promise.all(promises);
    if (!files.length) {
      return;
    }

    await printSettingsActions.printFile({ formId: FormName.Muster_1, files });
    alertSuccessfully(tEAU('eAUPrinted'));
  };

  const onView = async (data: EAUModel) => {
    setViewingEau(data);
  };

  const getColumns = (): Array<IDataTableColumn<EAUModel>> => {
    return [
      {
        id: 'createdOn',
        name: t('createdOn'),
        maxWidth: '160px',
        cell: ({ createdAt }) => (
          <BodyTextM>
            {datetimeUtil.dateTimeFormat(
              createdAt,
              DATE_TIME_WITHOUT_SECONDS_FORMAT
            )}
          </BodyTextM>
        ),
      },
      {
        id: 'patient',
        name: t('patient'),
        maxWidth: '200px',
        cell: ({ patientProfile, insuranceInfo }) => (
          <div
            className="sl__patient"
            onClick={() => {
              router.push(`/patients/${patientProfile.id}#timeline`);
            }}
            style={{ width: '100%' }}
          >
            <Flex justify="space-between" align="center" w="100%">
              <BodyTextM className="sl__patient-name">
                {formUtil.getFullName(
                  patientProfile.patientInfo.personalInfo.title,
                  patientProfile.patientInfo.personalInfo.intendWord,
                  patientProfile.patientInfo.personalInfo.lastName,
                  patientProfile.patientInfo.personalInfo.firstName
                )}
              </BodyTextM>
              {isEVCase(insuranceInfo, DatetimeUtil.now(), undefined) && (
                <Tooltip content={t2('notreadcard')} position="bottom">
                  <Svg
                    className="icon-card"
                    src={readCardIcon}
                    height={heightIcon}
                    width={weightIcon}
                    style={{ marginTop: '4px' }}
                  />
                </Tooltip>
              )}
            </Flex>
            <BodyTextS className="sl__patient-birthday">
              {datetimeUtil.dateTimeFormat(
                patientProfile.dateOfBirth,
                DATE_FORMAT
              )}
            </BodyTextS>
          </div>
        ),
      },
      {
        id: 'dispatchTo',
        name: tEAU('dispatchTo'),
        cell: ({ receiver }) => <BodyTextM>{receiver}</BodyTextM>,
      },
      {
        id: 'error',
        name: tEAU('error'),
        cell: (eauDoc) => {
          const { errors, warning } = eauDoc;
          let hasErrors = Boolean(errors?.length);
          let hasWarning = Boolean(warning);
          if (!hasErrors && !hasWarning) {
            return null;
          }

          let errorData = errors?.length ? errors[0] : null;
          if (hasWarning) {
            if (warning?.code === WarningCode.DocumentCancellationSentOver24h) {
              errorData = {
                description: '',
                code: warning.code,
              };
              hasWarning = true;
              hasErrors = false;
            }
          }

          let message = '';
          switch (true) {
            case Boolean(errorData?.code && errorData?.description):
              message = tEAU('errorWithCodeAndDescription', {
                code: errorData?.code,
                description: errorData?.description,
              });
              break;
            case Boolean(errorData?.description):
              message = tEAU(
                `Title_${errorData?.description}` as keyof typeof EDocumentsI18n.EAU
              );
              break;
            default:
              message = tEAU('error');
              break;
          }

          const titleNode = (
            <Flex align="center" gap={8}>
              {hasErrors && (
                <Flex flex={1} gap={8} align="center">
                  <Flex mt={2}>
                    <Svg size={16} src={ErrorIcon} />
                  </Flex>
                  <BodyTextM
                    fontWeight={'SemiBold'}
                    color={COLOR.TAG_BACKGROUND_RED}
                  >
                    {message}
                  </BodyTextM>
                </Flex>
              )}
              {hasWarning && (
                <Flex flex={1} align="center" gap={8}>
                  <Svg size={16} src={WarningIcon} />
                  <BodyTextM fontWeight={'SemiBold'} color={COLOR.TEXT_WARNING}>
                    {tEAU('warning')}
                  </BodyTextM>
                </Flex>
              )}
            </Flex>
          );

          return (
            <Flex align="center" justify="space-between" w={'100%'}>
              {titleNode}
              {(hasWarning || hasErrors) && errorData?.code !== ERROR_404 && (
                <Popover
                  position="bottom-right"
                  content={<EAUError eauDoc={eauDoc} />}
                >
                  <Button
                    outlined
                    iconOnly
                    icon={<Svg size={16} src={EyeOnIcon} />}
                  />
                </Popover>
              )}
            </Flex>
          );
        },
      },
      {
        id: 'dispatchOn',
        name: tEAU('dispatchOn'),
        maxWidth: '160px',
        cell: ({ sentOn }) =>
          sentOn && (
            <BodyTextM>
              {datetimeUtil.dateTimeFormat(sentOn, DATE_FORMAT)}
            </BodyTextM>
          ),
      },
      {
        id: 'printStatus',
        name: tEAU('printStatus'),
        maxWidth: '160px',
        cell: ({ settings, status }) => {
          if (!settings || status === DocumentStatus.Status_Created) {
            return null;
          }

          const printOption = settings.printOption;
          const printedPdf: EAUCopier[] = [];
          if (printOption.printForEmployer) {
            printedPdf.push(EAUCopier.EAUCopier_ForEmployer);
          }

          if (printOption.printForPatient) {
            printedPdf.push(EAUCopier.EAUCopier_ForPatient);
          }

          if (printOption.printForInsurance) {
            printedPdf.push(EAUCopier.EAUCopier_ForInsurance);
          }

          return <BodyTextM>{printedPdf.join(', ')}</BodyTextM>;
        },
      },
      {
        id: 'status',
        name: t('status'),
        maxWidth: '165px',
        cell: ({ status, settings: { statusText } }) => {
          if (
            status === DocumentStatus.Status_NotDispatched &&
            statusText === StatusText.StatusText_Sent
          ) {
            return <EDocumentStatusTag status={DocumentStatus.Status_Sent} />;
          }
          return <EDocumentStatusTag status={status} />;
        },
      },
      {
        id: 'actions',
        name: '',
        width: '40px',
        cell: (row) => {
          return (
            <Popover
              content={
                <Menu>
                  {getButtonsAction(row).map(
                    (buttonAction, index) =>
                      buttonAction.visible && (
                        <MenuItem
                          className={
                            buttonAction?.intent === 'danger'
                              ? MenuItemClasses.Danger
                              : ''
                          }
                          key={index}
                          text={buttonAction.title}
                          onClick={() => buttonAction.handleClick()}
                          icon={buttonAction.icon}
                        />
                      )
                  )}
                </Menu>
              }
            >
              <Button
                onClick={() => eDocumentsActions.setSelectedRowId(row.id)}
                minimal
                iconOnly
                icon={<Svg size={16} src={MoreIcon} />}
              />
            </Popover>
          );
        },
      },
    ];
  };

  const checkAllSelectedRowCanSignAndSend = () => {
    const statuses = [
      DocumentStatus.Status_SendingError,
      DocumentStatus.Status_Created,
      DocumentStatus.Status_SigningFailed,
    ];
    const canSignAndSend = eAU?.selectedRowState?.selectedRows?.every((row) => {
      if (!row.insuranceInfo) {
        return false;
      }
      return statuses.includes(row.status);
    });
    return canSignAndSend && Boolean(signingCard);
  };

  const checkAllSelectedRowCanPrint = () => {
    const statuses = [
      DocumentStatus.Status_Created,
      DocumentStatus.Status_Sent,
      DocumentStatus.Status_SendingError,
      DocumentStatus.Status_Delivered,
      DocumentStatus.Status_ConfirmationPending,
      DocumentStatus.Status_Sending,
      DocumentStatus.Status_SigningFailed,
      DocumentStatus.Status_NotDispatched,
    ];
    return eAU?.selectedRowState?.selectedRows?.every((row) =>
      statuses.includes(row.status)
    );
  };

  const checkAllSelectedRowCanCancel = () => {
    const statuses = [
      DocumentStatus.Status_Sent,
      DocumentStatus.Status_Delivered,
      DocumentStatus.Status_ConfirmationPending,
    ];
    const canCancel = eAU?.selectedRowState?.selectedRows?.every((row) =>
      statuses.includes(row.status)
    );

    return canCancel && Boolean(signingCard);
  };

  const getBulkActionButtons = (): IButtonsActionType[] => {
    const canSignAndSend = checkAllSelectedRowCanSignAndSend();
    const canPrint = checkAllSelectedRowCanPrint();
    const canCancel = checkAllSelectedRowCanCancel();

    let buttons: IButtonsActionType[] = [];
    if (canSignAndSend) {
      buttons.push({
        id: 'signAndSend',
        batchActions: true,
        visible: canSignAndSend,
        title: t('signAndSend'),
        icon: <Svg src={SendIcon} />,
        loading: isLoadingSignAndSend,
        handleClick: () => {
          const items = getSelectedRows({
            includedStatuses: [
              DocumentStatus.Status_SendingError,
              DocumentStatus.Status_Created,
              DocumentStatus.Status_SigningFailed,
            ],
          }) || [];
          if (items.length === 0) {
            return;
          }

          const ids = items.map((item) => item.id!);
          signAndSend({
            documentIds: ids,
            bsnrCode: globalData.userProfile?.bsnr!,
            cardType: signingCard!,
          });
        },
      });
    }
    if (canPrint) {
      buttons = [
        ...buttons,
        {
          id: 'printInsurance',
          batchActions: true,
          loading: isPrinting,
          visible: canPrint,
          title: tEAU('printInsurance'),
          icon: <Svg src={PrintIcon} />,
          handleClick: () =>
            handlePrint({
              forInsurance: true,
            }),
        },
        {
          id: 'printInsuranceAndPatient',
          batchActions: true,
          loading: isPrinting,
          visible: canPrint,
          title: tEAU('printInsuranceAndPatient'),
          icon: <Svg src={PrintIcon} />,
          handleClick: () =>
            handlePrint({
              forInsurance: true,
              forPatient: true,
            }),
        },
        {
          id: 'printInsuranceAndEmployeer',
          batchActions: true,
          loading: isPrinting,
          visible: canPrint,
          title: tEAU('printInsuranceAndEmployeer'),
          icon: <Svg src={PrintIcon} />,
          handleClick: () =>
            handlePrint({
              forInsurance: true,
              forEmployeer: true,
            }),
        },
        {
          id: 'print',
          batchActions: true,
          loading: isPrinting,
          visible: canPrint,
          title: tEAU('printAll'),
          icon: <Svg src={PrintIcon} />,
          handleClick: () =>
            handlePrint({
              forInsurance: true,
              forEmployeer: true,
              forPatient: true,
            }),
        },
      ];
    }

    if (canCancel) {
      buttons = [
        ...buttons,
        {
          id: 'cancel',
          batchActions: true,
          visible: canCancel,
          title: t('cancel'),
          icon: <Svg src={CancelIcon} />,
          intent: 'danger',
          loading: isLoadingCancel,
          handleClick: () => {
            setInfoDialog({ action: EDocumentActions.Cancel });
          },
        },
      ];
    }

    return [
      ...buttons,
      {
        id: 'delete',
        batchActions: true,
        visible: true,
        title: t('delete'),
        icon: <Svg src={RemoveIcon} />,
        intent: 'danger',
        handleClick: () => {
          setInfoDialog({ action: EDocumentActions.Remove });
        },
      },
    ];
  };

  const getButtonsAction = (data?: EAUModel): IButtonsActionType[] => {
    const status = data?.status;
    const canSignAndSend =
      [
        DocumentStatus.Status_SendingError,
        DocumentStatus.Status_Created,
        DocumentStatus.Status_SigningFailed,
      ].includes(status!) &&
      Boolean(signingCard) &&
      Boolean(data?.insuranceInfo);

    const canPrint = [
      DocumentStatus.Status_Created,
      DocumentStatus.Status_Sent,
      DocumentStatus.Status_SendingError,
      DocumentStatus.Status_Delivered,
      DocumentStatus.Status_ConfirmationPending,
      DocumentStatus.Status_Sending,
      DocumentStatus.Status_SigningFailed,
      DocumentStatus.Status_NotDispatched,
    ].includes(status!);

    const canCancel =
      Boolean(signingCard) &&
      [
        DocumentStatus.Status_Sent,
        DocumentStatus.Status_Delivered,
        DocumentStatus.Status_ConfirmationPending,
      ].includes(status!);

    return [
      {
        id: 'gotoPatientProfile',
        visible: true,
        title: tButtonActions('gotoPatientProfile'),
        icon: <Svg src={UserIcon} />,
        handleClick: () => {
          router.push(`/patients/${data?.patientProfile.id}`);
        },
      },
      {
        id: 'viewEAU',
        visible: true,
        title: tEAU('viewEAU'),
        icon: <Svg src={EyeIcon} width={20} height={20} />,
        handleClick: async () => {
          if (!data) {
            return;
          }
          await onView(data);
        },
      },
      {
        id: 'signAndSend',
        batchActions: true,
        visible: canSignAndSend,
        title: t('signAndSend'),
        icon: <Svg src={SendIcon} />,
        loading: isLoadingSignAndSend,
        handleClick: () => {
          const items = getSelectedRows({
            includedStatuses: [
              DocumentStatus.Status_SendingError,
              DocumentStatus.Status_Created,
              DocumentStatus.Status_SigningFailed,
            ],
          }) || [];
          if (items.length === 0) {
            return;
          }

          const ids = items.map((item) => item.id!);
          signAndSend({
            documentIds: ids,
            bsnrCode: globalData.userProfile?.bsnr!,
            cardType: signingCard!,
          });
        },
      },
      {
        id: 'printInsurance',
        batchActions: true,
        loading: isPrinting,
        visible: canPrint,
        title: tEAU('printInsurance'),
        icon: <Svg src={PrintIcon} />,
        handleClick: () =>
          handlePrint({
            forInsurance: true,
          }),
      },
      {
        id: 'printInsuranceAndPatient',
        batchActions: true,
        loading: isPrinting,
        visible: canPrint,
        title: tEAU('printInsuranceAndPatient'),
        icon: <Svg src={PrintIcon} />,
        handleClick: () =>
          handlePrint({
            forInsurance: true,
            forPatient: true,
          }),
      },
      {
        id: 'printInsuranceAndEmployeer',
        batchActions: true,
        loading: isPrinting,
        visible: canPrint,
        title: tEAU('printInsuranceAndEmployeer'),
        icon: <Svg src={PrintIcon} />,
        handleClick: () =>
          handlePrint({
            forInsurance: true,
            forEmployeer: true,
          }),
      },
      {
        id: 'print',
        batchActions: true,
        loading: isPrinting,
        visible: canPrint,
        title: tEAU('printAll'),
        icon: <Svg src={PrintIcon} />,
        handleClick: () =>
          handlePrint({
            forInsurance: true,
            forEmployeer: true,
            forPatient: true,
          }),
      },
      {
        id: 'cancel',
        batchActions: true,
        visible: canCancel,
        title: t('cancel'),
        icon: <Svg src={CancelIcon} />,
        intent: 'danger',
        loading: isLoadingCancel,
        handleClick: () => {
          setInfoDialog({ action: EDocumentActions.Cancel });
        },
      },
      {
        id: 'delete',
        batchActions: true,
        visible: true,
        title: t('delete'),
        icon: <Svg src={RemoveIcon} />,
        intent: 'danger',
        handleClick: () => {
          setInfoDialog({ action: EDocumentActions.Remove });
        },
      },
    ];
  };

  const onChangePage = (page: number) => {
    eDocumentsActions.setPagination({
      ...pagination,
      page,
    });
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    eDocumentsActions.setPagination({
      ...pagination,
      page: 1,
      pageSize: currentRowsPerPage,
    });
  };

  const onClose = () => {
    setInfoDialog(undefined);
  };

  const onCloseViewForm = () => {
    setViewingEau(undefined);
  };

  return (
    <Flex column>
      <ActionsBar buttonsAction={getBulkActionButtons()} />
      <Flex column className="sl__table">
        <Table
          clearSelectedRows={
            (isSuccessSignAndSend || isSuccessRemove || isSuccessCancel) &&
            !isPrinting
          }
          noHeader
          selectableRows
          selectableRowsHighlight
          data={isSuccess ? data.eAUItems : []}
          columns={getColumns()}
          pagination
          paginationServer
          paginationPerPage={pagination.pageSize}
          paginationResetDefaultPage
          paginationTotalRows={isSuccess ? data.paginationResponse.total : 0}
          progressPending={isLoading}
          onSelectedRowsChange={(selectedRowState) => {
            eDocumentsActions.setSelectedRowId(undefined);
            eDocumentsActions.setSelectedRowsState(selectedRowState);
          }}
          onChangePage={onChangePage}
          onChangeRowsPerPage={onChangeRowsPerPage}
        />
      </Flex>
      {infoDialog && (
        <DeleteConfirmDialog
          isLoading={isLoadingRemove || isLoadingCancel}
          confirm={
            infoDialog.action === EDocumentActions.Remove
              ? handleDelete
              : handleCancel
          }
          close={onClose}
          isOpen={Boolean(infoDialog)}
          text={
            infoDialog.action === EDocumentActions.Remove
              ? {
                  btnCancel: tButtonActions('no'),
                  btnOk: tButtonActions('yesDelete'),
                  title: tConfirmDialog('deleteEAUTitle'),
                  message: tConfirmDialog('textContent'),
                }
              : {
                  btnCancel: tButtonActions('no'),
                  btnOk: tButtonActions('yesCancel'),
                  title: tConfirmDialog('cancelEAUTitle'),
                  message: tConfirmDialog('textContent'),
                }
          }
        />
      )}
      {viewingEau && (
        <FormPrintPreviewLayout
          onClose={onCloseViewForm}
          title={tEAU('formTitle')}
          className={className}
        >
          <Flex h={'100%'}>
            <Flex className={FormPrintPreviewClasses.contentLeftSide}>
              <Flex w={'100%'} className="form-container">
                <FormEAUStylesheet
                  sentBundleUrl={viewingEau.bundleUrl}
                  cancellationBundleUrl={viewingEau.cancellationBundleUrl}
                  status={viewingEau.status}
                />
              </Flex>
            </Flex>
            <Flex
              className={FormPrintPreviewClasses.contentRightSide}
              column
              justify="flex-end"
            >
              <Flex justify="flex-end">
                <Button intent="primary" onClick={onCloseViewForm}>
                  {tButtonActions('close')}
                </Button>
              </Flex>
            </Flex>
          </Flex>
        </FormPrintPreviewLayout>
      )}
    </Flex>
  );
};

export default memo(EAU);
