import { useRouter } from 'next/router';
import { memo, useCallback, useContext, useMemo, useState } from 'react';

import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import {
  ActionsBar,
  DialogInfoType,
  EDocumentActions,
  IButtonsActionType,
  PreviewERP,
} from '../';
import { getPrescriptionInfo } from '../EDocument.helper';
import { eDocumentsActions, useEDocumentsStore } from '../EDocuments.store';

import {
  Avatar,
  BodyTextM,
  BodyTextS,
  Button,
  Flex,
  Intent,
  Svg,
  Tooltip,
} from '@tutum/design-system/components';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import { MenuItemClasses } from '@tutum/design-system/components/Core/MenuItem';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import Table, {
  IDataTableColumn,
  IDataTableSelectedRowState,
} from '@tutum/design-system/components/Table';
import {
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components/Toaster';
import {
  ErezeptDto,
  useListenErezeptChanged,
} from '@tutum/hermes/bff/app_mvz_erezept';
import { useListenMedicationPrescribe } from '@tutum/hermes/bff/app_mvz_medicine';
import {
  getPdfUrl,
  useMutationRemoveErezept,
  useMutationResendERP,
  useMutationSignAndSendERP,
  useQueryGetErezept,
} from '@tutum/hermes/bff/legacy/app_mvz_erezept';
import { DocumentStatus as Status } from '@tutum/hermes/bff/qes_common';
import i18n from '@tutum/infrastructure/i18n';
import {
  DATE_FORMAT,
  DATE_TIME_WITHOUT_SECONDS_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import {
  default as DateTimeUtil,
  default as datetimeUtil,
} from '@tutum/infrastructure/utils/datetime.util';
import { EDocumentStatusTag } from '@tutum/mvz/components/e-document-status-tag';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import type EDocumentsI18n from '@tutum/mvz/locales/en/EDocuments.json';
import MedicationContext from '@tutum/mvz/module_medication/context/MedicationContext';
import { MedicationPrintPreview } from '@tutum/mvz/module_medication_kbv';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import type PatientOverview from '@tutum/mvz/locales/en/PatientOverview.json';
import { FormName } from '@tutum/hermes/bff/form_common';

const SendIcon = '/images/send.svg';
const PrintIcon = '/images/printer.svg';
const RemoveIcon = '/images/trash-2.svg';
const MoreIcon = '/images/more-vertical.svg';
const EyeIcon = '/images/eye-on.svg';
const UserIcon = '/images/user-gray.svg';
const ReadCardIcon = '/images/patient/selector/read-card-icon.svg';
const QrCode = '/images/qr-code.svg';

const heightIcon = 16;
const weightIcon = 16;

export interface IERezeptProps {
  className?: string;
}

const ERezept = (props: IERezeptProps) => {
  const { className } = props;
  const { t } = i18n.useTranslation<keyof typeof EDocumentsI18n>({
    namespace: 'EDocuments',
  });
  const { t: tConfirmDialog } = i18n.useTranslation<
    keyof typeof EDocumentsI18n.ConfirmDialog
  >({
    namespace: 'EDocuments',
    nestedTrans: 'ConfirmDialog',
  });
  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonI18n.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });
  const { t: t2 } = i18n.useTranslation<
    keyof typeof PatientOverview.PatientOverviewList
  >({
    namespace: 'PatientOverview',
    nestedTrans: 'PatientOverviewList',
  });

  const { eRezept, pagination, filter, selectedRowId } = useEDocumentsStore();
  const { globalData, getDoctorInitial } = GlobalContext.useContext();
  const { isLoadingPrint, selectedRowState } = eRezept;
  const router = useRouter();
  const [infoDialog, setInfoDialog] = useState<DialogInfoType | undefined>(undefined);
  const { showPrintReview, setViewFormTimeline } =
    useContext(MedicationContext);

  const { data, isSuccess, isLoading, refetch } = useQueryGetErezept({
    pagination,
    createdAtBegin: filter.createdAtBegin,
    createdAtEnd: filter.createdAtEnd,
    patientName: filter.searchKey,
    statuses: filter.statuses as Status[],
  });

  const {
    mutate: remove,
    isPending: isLoadingRemove,
    isSuccess: isSuccessRemove,
  } = useMutationRemoveErezept({
    onSuccess: () => {
      alertSuccessfully(t('deletedERezept'));
      eDocumentsActions.clearData();
      setInfoDialog(undefined);
    },
    onError: () => {
      alertError(t('failedToDelete'));
    },
  });

  const {
    mutate: signAndSend,
    isPending: isLoadingSignAndSend,
    isSuccess: isSuccessSign,
  } = useMutationSignAndSendERP({
    onSuccess: () => {
      alertSuccessfully(t('eRezeptSent'));
      eDocumentsActions.clearData();
    },
    onError: () => {
      alertError(t('failedToSignAndSend'));
    },
  });

  const {
    mutate: resend,
    isPending: isLoadingResend,
    isSuccess: isSuccessResend,
  } = useMutationResendERP({
    onSuccess: () => {
      alertSuccessfully(t('eRezeptSent'));
      eDocumentsActions.clearData();
    },
    onError: () => {
      alertError(t('failedToSignAndSend'));
    },
  });

  // Listen event sign and sent erezept successfully
  useListenErezeptChanged((data) => {
    if (data.currentUserId === globalData.userProfile?.id) {
      refetch();
    }
  });

  // Listen event erezept form create successfully
  useListenMedicationPrescribe((data) => {
    if (data.doctorId === globalData.userProfile?.id) {
      refetch();
    }
  });

  const getSelectedRows = useCallback<
    (excludedStatus?: Status) => ErezeptDto[]
  >(
    (excludedStatus) => {
      if (selectedRowId) {
        return (
          data?.erezeptItems.filter(
            (eRezeptDocument) => eRezeptDocument.id === selectedRowId
          ) || []
        );
      }

      return selectedRowState.selectedRows.filter(
        (selectedRow) => selectedRow.status !== excludedStatus
      );
    },
    [selectedRowId, selectedRowState.selectedRows]
  );

  const handleViewERezept = (data: ErezeptDto | undefined) => async () => {
    if (!data) {
      return;
    }
    
    setViewFormTimeline({
      treatmentDoctorId: data.viewFormInfo.treatmentDoctorId,
      formInfo: { ...data.viewFormInfo.formInfo, eRezeptStatus: data.status },
      scheinId: data.scheinId,
      assignedToBsnrId: data.viewFormInfo.assignedToBsnrId,
    });
  };

  const handleSignAndSend = async () => {
    const eRezeptItems = getSelectedRows(Status.Status_Sent);
    const medicineIds = eRezeptItems.map(
      (eRezeptItem) => eRezeptItem.medicineInfo.id!
    );
    if (medicineIds.length) {
      signAndSend({
        medicineIds,
      });
    }
  };

  const handleResend = async () => {
    const eRezeptItems = getSelectedRows(Status.Status_Sent);
    const medicineIds = eRezeptItems.map(
      (eRezeptItem) => eRezeptItem.medicineInfo.id!
    );
    if (medicineIds.length) {
      resend({
        medicineIds,
      });
    }
  };

  const handlePrint = async () => {
    try {
      eDocumentsActions.setLoadingPrint(true);
      const eRezeptItems = getSelectedRows(Status.Status_Created);
      if (eRezeptItems.length === 1) {
        await eDocumentsActions.print({
          formId: FormName.Muster_eRezept,
          files: [eRezeptItems[0].pdfUrl!],
        });
        return;
      }
      const eRezeptIds = eRezeptItems.map((eRezeptItem) => eRezeptItem.id);
      const res = await getPdfUrl({ iDs: eRezeptIds });
      await eDocumentsActions.print({
        formId: FormName.Muster_eRezept,
        files: [...res.data.pdfUrl],
      });
    } catch (error) {
      throw error;
    } finally {
      eDocumentsActions.setLoadingPrint(false);
    }
  };

  const handleDelete = async () => {
    const eRezeptItems = getSelectedRows();
    const eRezeptIds = eRezeptItems.map(
      (eRezeptItem) => eRezeptItem.medicineInfo.id!
    );
    remove({ iDs: eRezeptIds });
  };

  const checkAllSelectedRowCanSignAndSendOrCancel = () => {
    const selectedRows = getSelectedRows();
    const canSignAndSendOrCancel = selectedRows?.every((row) => {
      return (
        row?.status !== Status.Status_Sent &&
        row?.status !== Status.Status_Deleted
      );
    });
    return canSignAndSendOrCancel;
  };

  const checkAllSelectedRowCanResend = () => {
    const selectedRows = getSelectedRows();
    const canPrint = selectedRows?.every((row) => {
      return row?.status === Status.Status_SendingError;
    });
    return canPrint;
  };

  const checkAllSelectedRowCanPrint = () => {
    const selectedRows = getSelectedRows();
    const canPrint = selectedRows?.every((row) => {
      return row?.status === Status.Status_Sent;
    });
    return canPrint;
  };

  const viewQrCode = (data: ErezeptDto | undefined) => () => {
    if (!data) {
      return;
    }

    eDocumentsActions.viewErezeptPDF(data.pdfUrl);
  };

  const getBulkActions = () => {
    const canSignAndSendOrCancel = checkAllSelectedRowCanSignAndSendOrCancel();
    const canPrint = checkAllSelectedRowCanPrint();
    const canResend = checkAllSelectedRowCanResend();
    const buttons: IButtonsActionType[] = [];
    if (canSignAndSendOrCancel) {
      buttons.push({
        id: 'signAndSend',
        title: t('signAndSend'),
        icon: <Svg src={SendIcon} />,
        handleClick: handleSignAndSend,
        visible: canSignAndSendOrCancel,
        batchActions: true,
        loading: isLoadingSignAndSend,
      });
    }
    if (canResend) {
      buttons.push({
        id: 'resend',
        title: t('resend'),
        icon: <Svg src={SendIcon} />,
        handleClick: handleResend,
        visible: canResend,
        batchActions: true,
        loading: isLoadingResend,
      });
    }
    if (canPrint) {
      buttons.push({
        id: 'print',
        title: t('print'),
        icon: <Svg src={PrintIcon} />,
        handleClick: handlePrint,
        visible: canPrint,
        batchActions: true,
        loading: isLoadingPrint,
      });
    }
    buttons.push({
      id: 'delete',
      title: t('delete'),
      icon: <Svg src={RemoveIcon} />,
      intent: Intent.DANGER,
      visible: true,
      batchActions: true,
      handleClick: () =>
        setInfoDialog({
          action: EDocumentActions.Remove,
        }),
    });
    return buttons;
  };

  const getButtonsAction = (data?: ErezeptDto): IButtonsActionType[] => {
    const canViewERezept =
      data?.status !== Status.Status_Cancelled &&
      data?.status !== Status.Status_Sent;
    const canViewQRCode = data?.status === Status.Status_Sent;
    const canSignAndSendOrCancel =
      data?.status !== Status.Status_Sent &&
      data?.status !== Status.Status_Deleted;
    const canPrint = data?.status === Status.Status_Sent;
    const canResend = data?.status === Status.Status_SendingError;

    const actions: IButtonsActionType[] = [];
    if (canResend) {
      actions.push({
        id: 'resend',
        batchActions: true,
        loading: isLoadingSignAndSend,
        visible: canResend,
        title: t('resend'),
        icon: <Svg src={SendIcon} />,
        handleClick: handleResend,
      });
    } else {
      actions.push({
        id: 'signAndSend',
        batchActions: true,
        loading: isLoadingSignAndSend,
        visible: canSignAndSendOrCancel,
        title: t('signAndSend'),
        icon: <Svg src={SendIcon} />,
        handleClick: handleSignAndSend,
      });
    }

    return [
      {
        id: 'gotoPatientProfile',
        visible: true,
        title: tButtonActions('gotoPatientProfile'),
        icon: <Svg src={UserIcon} width={20} height={20} />,
        handleClick: () => {
          router.push(`/patients/${data?.patient.id}`);
        },
      },
      {
        id: 'viewERezept',
        visible: canViewERezept,
        title: t('viewERezept'),
        icon: <Svg src={EyeIcon} width={20} height={20} />,
        handleClick: handleViewERezept(data),
      },
      {
        id: 'viewQrCode',
        visible: canViewQRCode,
        title: t('viewQrCode'),
        icon: <Svg src={QrCode} width={20} height={20} />,
        handleClick: viewQrCode(data),
      },
      ...actions,
      {
        id: 'print',
        batchActions: true,
        loading: isLoadingPrint,
        visible: canPrint,
        title: t('print'),
        icon: <Svg src={PrintIcon} />,
        handleClick: handlePrint,
      },
      {
        id: 'delete',
        batchActions: true,
        visible: true,
        title: t('delete'),
        icon: <Svg src={RemoveIcon} />,
        intent: Intent.DANGER,
        handleClick: () =>
          setInfoDialog({
            action: EDocumentActions.Remove,
          }),
      },
    ];
  };

  const columns = useMemo<Array<IDataTableColumn<ErezeptDto>>>(() => {
    return [
      {
        id: 'createdOn',
        name: t('createdOn'),
        maxWidth: '160px',
        cell: ({ createdDate }) => (
          <BodyTextM>
            {DateTimeUtil.dateTimeFormat(
              createdDate,
              DATE_TIME_WITHOUT_SECONDS_FORMAT
            )}
          </BodyTextM>
        ),
      },
      {
        id: 'patient',
        name: t('patient'),
        maxWidth: '200px',
        cell: ({ patient }) => (
          <div
            className="sl__patient"
            onClick={() => {
              router.push(`/patients/${patient.id}#timeline`);
            }}
            style={{ width: '100%' }}
          >
            <Flex justify="space-between" w="100%" align="center">
              <BodyTextM className="sl__patient-name">{patient.name}</BodyTextM>
              {isEVCase(patient.insurance, DateTimeUtil.now(), undefined) && (
                <Tooltip content={t2('notreadcard')} position="bottom">
                  <Svg
                    className="icon-card"
                    src={ReadCardIcon}
                    height={heightIcon}
                    width={weightIcon}
                    style={{ marginTop: '4px' }}
                  />
                </Tooltip>
              )}
            </Flex>
            <BodyTextS className="sl__patient-birthday">
              {DateTimeUtil.dateTimeFormat(patient.birthday, DATE_FORMAT)}
            </BodyTextS>
          </div>
        ),
      },
      {
        id: 'prescribingDoctor',
        name: t('prescribingDoctor'),
        maxWidth: '350px',
        cell: ({ doctor }) => (
          <Flex align="center">
            <Avatar
              className="sl-e-rezept__doctor-avatar"
              useDefaultBackgroundColor
              initial={getDoctorInitial(doctor.id)}
              size="medium"
            />
            <BodyTextM className="sl-e-rezept__doctor-name">
              {doctor.name}
            </BodyTextM>
          </Flex>
        ),
      },
      {
        id: 'prescription',
        name: t('prescription'),
        cell: ({ medicineInfo }) => (
          <BodyTextM>{getPrescriptionInfo(medicineInfo)}</BodyTextM>
        ),
      },
      {
        id: 'sentOn',
        name: t('sentOn'),
        cell: ({ sentOn, status }) =>
          status === Status.Status_Sent &&
          sentOn && (
            <BodyTextM>
              {datetimeUtil.dateTimeFormat(
                sentOn,
                DATE_TIME_WITHOUT_SECONDS_FORMAT
              )}
            </BodyTextM>
          ),
      },
      {
        id: 'status',
        name: t('status'),
        maxWidth: '120px',
        cell: ({ status }) => <EDocumentStatusTag status={status} />,
      },
      {
        id: 'actions',
        name: '',
        width: '40px',
        cell: (row) => {
          return (
            <Popover
              content={
                <Menu>
                  {getButtonsAction(row).map(
                    (buttonAction, index) =>
                      buttonAction.visible && (
                        <MenuItem
                          className={
                            buttonAction?.intent === Intent.DANGER &&
                            MenuItemClasses.Danger || ''
                          }
                          key={index}
                          text={buttonAction.title}
                          onClick={buttonAction.handleClick}
                          icon={buttonAction.icon}
                        />
                      )
                  )}
                </Menu>
              }
            >
              <Button
                onClick={() => eDocumentsActions.setSelectedRowId(row.id)}
                minimal
                iconOnly
                icon={<Svg src={MoreIcon} />}
              />
            </Popover>
          );
        },
      },
    ];
  }, [getSelectedRows, getButtonsAction]);

  const onSelectedRowsChange = (
    selectedRowState: IDataTableSelectedRowState<ErezeptDto>
  ) => {
    eDocumentsActions.setSelectedRowId(undefined);
    eDocumentsActions.setSelectedRowsState(selectedRowState);
  };

  const onChangePage = (page: number) => {
    eDocumentsActions.setPagination({
      ...pagination,
      page,
    });
  };

  const onChangeRowsPerPage = (pageSize: number) => {
    eDocumentsActions.setPagination({
      ...pagination,
      page: 1,
      pageSize,
    });
  };

  const onClose = () => {
    // eDocumentsActions.clearData();
    setInfoDialog(undefined);
  };

  return (
    <Flex column className={className}>
      <ActionsBar buttonsAction={getBulkActions()} />
      <Flex column className="sl__table">
        <Table
          className={className}
          noHeader
          selectableRows={true}
          selectableRowsHighlight
          data={isSuccess ? data?.erezeptItems : []}
          columns={columns}
          pagination
          paginationServer
          paginationPerPage={pagination.pageSize}
          paginationTotalRows={isSuccess ? data?.total : 0}
          progressPending={isLoading}
          onSelectedRowsChange={onSelectedRowsChange}
          onChangePage={onChangePage}
          onChangeRowsPerPage={onChangeRowsPerPage}
          clearSelectedRows={
            isSuccessRemove || isSuccessSign || isSuccessResend
          }
        />
      </Flex>
      <PreviewERP />
      {showPrintReview && <MedicationPrintPreview medicineType="kbv" />}
      <DeleteConfirmDialog
        isLoading={isLoadingRemove}
        confirm={handleDelete}
        close={onClose}
        isOpen={Boolean(infoDialog)}
        text={{
          btnCancel: tButtonActions('no'),
          btnOk: tButtonActions('yesDelete'),
          title: tConfirmDialog('deletedERezeptTitle'),
          message: tConfirmDialog('textContent'),
        }}
      />
    </Flex>
  );
};

export default memo(ERezept);
