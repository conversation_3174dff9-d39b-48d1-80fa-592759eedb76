import React, { useState } from 'react';
import { useRouter } from 'next/router';

import type EDocumentsI18n from '@tutum/mvz/locales/en/EDocuments.json';
import { Order, type PaginationRequest } from '@tutum/hermes/bff/common';
import type PatientFileSidebarI18n from '@tutum/mvz/locales/en/PatientFileSidebar.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';

import { GlobalData } from '@tutum/mvz/contexts/Global.context';
import i18n from '@tutum/infrastructure/i18n';
import { ActionsBar } from '../actions-bar';
import { eDocumentsActions, useEDocumentsStore } from '../EDocuments.store';
import { alertError, alertSuccessfully, BodyTextM, BodyTextS, Flex, Svg } from '@tutum/design-system/components';
import { IButtonsActionType } from '../EDocuments.types';
import { EABModel } from '@tutum/hermes/bff/eab_common';
import { useQueryGetEAB, updateEAB, UpdateEABRequest, useMutationDeleteEAB, useMutationUpdateStatusEAB } from '@tutum/hermes/bff/legacy/app_mvz_eab';
import { DocumentStatus } from '@tutum/hermes/bff/qes_common';
import { getPresignedGetURL } from '@tutum/hermes/bff/legacy/app_mvz_file';
import { getDoctorLetterById } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_TIME_WITHOUT_SECONDS_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { formatBirthday } from '@tutum/mvz/_utils/formatBirthday';
import formUtil from '@tutum/infrastructure/utils/form.util';
import { EDocumentStatusTag } from '@tutum/mvz/components/e-document-status-tag';
import { PatientType } from '@tutum/hermes/bff/patient_profile_common';
import { Menu, MenuItem, Popover } from '@tutum/design-system/components/Core';
import { MenuItemClasses } from '@tutum/design-system/components/Core/MenuItem';
import { ROUTING } from '@tutum/mvz/types/route.type';
import { mailboxActions } from '@tutum/mvz/module_mailbox/Mailbox.store';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import { DoctorLetterCreateEditDialog } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog';
import { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { DoctorLetter } from '@tutum/hermes/bff/doctor_letter_common';
import { PatientProfileResponse } from '@tutum/hermes/bff/app_mvz_patient_profile';
import { PatientProfile } from '@tutum/hermes/bff/service_domains_profile';
import DeleteConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import { Mode } from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog/types';
import { useListenEABChanged } from '@tutum/hermes/bff/app_mvz_eab';
import { getShortPatientType } from '@tutum/mvz/_utils/patientType';

const RemoveIcon = '/images/trash-2.svg';
const MoreIcon = '/images/more-vertical.svg';
const EyeOnIcon = '/images/eye-on.svg';
const SendIcon = '/images/send.svg';
const PrintIcon = '/images/printer.svg';
const EditIcon = '/images/edit-value.svg';

export interface IEabProps {
  className?: string;
  globalData: GlobalData;
}

type ExtendEABModal = EABModel & {
  entryDoctorLetter: TimelineModel;
}

const EAB = (props: IEabProps) => {
  const { className } = props;
  const [pagination, setPagination] = useState<PaginationRequest>({
    page: 1,
    pageSize: 30,
    sortBy: '',
    order: Order.DESC,
  })
  const [fileUrl, setViewFileUrl] = useState<string>('')
  const [selectedRow, setSelectedRow] = useState<ExtendEABModal | undefined>(undefined)
  const [patientTimeline, setPatient] = useState<PatientProfileResponse & {
    timelineModel: TimelineModel;
  } | undefined>(undefined)
  const [confirmDialog, setConfirmDialog] = useState<boolean>(false)
  const [mode, setMode] = useState<Mode>('view')

  const { t: tEAB } = i18n.useTranslation<keyof typeof EDocumentsI18n.EAB>({
    namespace: 'EDocuments',
    nestedTrans: 'EAB',
  });

  const { t } = i18n.useTranslation<keyof typeof EDocumentsI18n>({
    namespace: 'EDocuments',
  });

  const { t: tBirthday } = i18n.useTranslation<
    keyof typeof PatientFileSidebarI18n.PatientInformationSidebar
  >({
    namespace: 'PatientFileSidebar',
    nestedTrans: 'PatientInformationSidebar',
  });

  const { t: tButtonActions } = i18n.useTranslation<
    keyof typeof CommonI18n.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const { t: tConfirmDialog } = i18n.useTranslation<
    keyof typeof EDocumentsI18n.ConfirmDialog
  >({
    namespace: 'EDocuments',
    nestedTrans: 'ConfirmDialog',
  });


  const { filter, selectedRowId } = useEDocumentsStore();
  const router = useRouter();

  const { data, isSuccess, isLoading, refetch } = useQueryGetEAB({
    pagination,
    statuses: filter.statuses as DocumentStatus[],
    query: filter.searchKey,
    dateRange: {
      startDate: filter.createdAtBegin!,
      endDate: filter.createdAtEnd!,
    },
  });

  useListenEABChanged((data) => {
    refetch();
  })

  const {
    mutate: deleteEAB,
    isPending: isLoadingDelete,
  } = useMutationDeleteEAB({
    onSuccess: () => {
      alertSuccessfully(tEAB('deletedEAB'));
      eDocumentsActions.clearData();
      setConfirmDialog(false);
      setSelectedRow(undefined)
      setPatient(undefined)
      refetch();
    },
    onError: (error) => {
      alertError(error.response.data.message);
    },
  });


  const {
    mutate: updateEABStatus
  } = useMutationUpdateStatusEAB({
    onSuccess: () => {
      refetch();
    },
    onError: (error) => {
      alertError(error.response.data.message);
    },
  })

  const getColumns = (): Array<IDataTableColumn<EABModel>> => {
    return [
      {
        id: 'lastEdited',
        name: t('lastEdited'),
        maxWidth: '160px',
        cell: ({ createdAt }) => (
          <BodyTextM>
            {datetimeUtil.dateTimeFormat(
              createdAt,
              DATE_TIME_WITHOUT_SECONDS_FORMAT
            )}
          </BodyTextM>
        ),
      },
      {
        id: 'patient',
        name: t('patient'),
        maxWidth: '578px',
        cell: ({ patientProfile }) => (
          <div
            className="sl__patient"
            onClick={() => {
              router.push(`/patients/${patientProfile.id}#timeline`);
            }}
            style={{ width: '100%' }}
          >
            <Flex justify="space-between" align="center" w="100%">
              <BodyTextM className="sl__patient-name">
                {formUtil.getFullName(
                  patientProfile.patientInfo.personalInfo.title,
                  patientProfile.patientInfo.personalInfo.intendWord,
                  patientProfile.patientInfo.personalInfo.lastName,
                  patientProfile.patientInfo.personalInfo.firstName
                )}
              </BodyTextM>
            </Flex>
            <Flex gap={2}>
              <BodyTextS className="sl__patient-type">{`${getShortPatientType(patientProfile.patientInfo.genericInfo.patientType)} • `}</BodyTextS>
              <BodyTextS className="sl__patient-birthday">
                {`${formatBirthday(
                  tBirthday,
                  patientProfile.patientInfo.personalInfo.dateOfBirth
                )} • ${patientProfile.patientInfo.personalInfo.gender}`}
              </BodyTextS>
            </Flex>
          </div>
        ),
      },
      {
        id: 'dispatchTo',
        name: tEAB('dispatchTo'),
        cell: ({ receiver }) => {
          if (!receiver || receiver.length === 0) {
            return <BodyTextM>--</BodyTextM>
          }
          return (
            <Flex column>{receiver.map((address, index) => (
              <BodyTextM key={`${index}_${address}`}>{address}</BodyTextM>
            ))}</Flex>
          )
        },
      },
      {
        id: 'status',
        name: t('status'),
        maxWidth: '165px',
        cell: ({ status }) => {
          if (
            status === DocumentStatus.Status_Created
          ) {
            return <EDocumentStatusTag status={DocumentStatus.Status_Saved} />;
          }
          return <EDocumentStatusTag status={status} />;
        },
      },
      {
        id: 'actions',
        name: '',
        width: '40px',
        cell: (row) => {
          return (
            <Popover
              content={
                <Menu>
                  {getButtonsAction(row).map(
                    (buttonAction, index) =>
                      buttonAction.visible && (
                        <MenuItem
                          className={
                            buttonAction?.intent === 'danger'
                              ? MenuItemClasses.Danger
                              : ''
                          }
                          key={index}
                          text={buttonAction.title}
                          onClick={() => buttonAction.handleClick()}
                          icon={buttonAction.icon}
                        />
                      )
                  )}
                </Menu>
              }
            >
              <Svg style={{ cursor: 'pointer' }} src={MoreIcon} onClick={() => eDocumentsActions.setSelectedRowId(row.id)} />
            </Popover>
          );
        },
      },
    ];
  };

  const getDoctorLetter = async (data: EABModel) => {
    try {
      const res = await getDoctorLetterById({ doctorLetterId: data.doctorLetterId })
      if (res.data && res.data.timelineModel) {
        setSelectedRow({
          ...data,
          entryDoctorLetter: res.data.timelineModel
        })
      }
    } catch (error) {
      throw error
    }
  }

  const getButtonsAction = (data?: EABModel): IButtonsActionType[] => {
    const status = data?.status;
    const canView = [DocumentStatus.Status_Printed, DocumentStatus.Status_Sent, DocumentStatus.Status_Sending, DocumentStatus.Status_SendingError, DocumentStatus.Status_Signed].includes(status!)
    const canResend = [DocumentStatus.Status_Sent, DocumentStatus.Status_SendingError, DocumentStatus.Status_Signed].includes(status!)
    return [
      {
        id: 'edit',
        batchActions: true,
        visible: status === DocumentStatus.Status_Saved || status === DocumentStatus.Status_Created && !!data,
        title: tEAB('edit'),
        icon: <Svg src={EditIcon} size={16} />,
        handleClick: async () => {
          setMode('edit')
          data && await getDoctorLetter(data)
        },
      },
      {
        id: 'print',
        batchActions: true,
        visible: status === DocumentStatus.Status_Saved || status === DocumentStatus.Status_Created && !!data?.pDFUrl,
        title: tEAB('print'),
        icon: <Svg src={PrintIcon} size={16} />,
        handleClick: async () => {
          const pdf = data?.pDFUrl || '';
          const [bucketName, objectName] = pdf.split('/');
          try {
            const result = await getPresignedGetURL({
              bucketName: bucketName,
              objectName: objectName,
            });
            setViewFileUrl(result.data.presignedURL)
          } catch (e) {
            throw e;
          }
        },
      },
      {
        id: 'view',
        batchActions: true,
        visible: canView,
        title: tEAB('view'),
        icon: <Svg src={EyeOnIcon} size={16} />,
        handleClick: async () => {
          setMode('view')
          data && await getDoctorLetter(data)
        },
      },
      {
        id: 'resend',
        batchActions: true,
        visible: canResend,
        title: tEAB('resend'),
        icon: <Svg src={SendIcon} size={16} />,
        handleClick: async () => {
          mailboxActions.setEABItem(data);
          const URL = ROUTING.KIM;
          await router.push(URL);
        },
      },
      {
        id: 'remove',
        batchActions: true,
        visible: true,
        title: tEAB('remove'),
        icon: <Svg src={RemoveIcon} />,
        intent: 'danger',
        handleClick: async () => {
          data && await getDoctorLetter(data)
          setConfirmDialog(true)
        },
      },
    ];
  };

  const onChangePage = (page: number) => {
    setPagination({
      ...pagination,
      page,
    });
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination({
      ...pagination,
      page: 1,
      pageSize: currentRowsPerPage,
    });
  };

  const mapPatientToPatientProfiles = (patientProfile: PatientProfile) => {
    if (!patientProfile) return null;
    const patientProfileResponse: PatientProfileResponse = {
      id: patientProfile.id,
      firstName: patientProfile.firstName,
      lastName: patientProfile.lastName,
      dateOfBirth: patientProfile.dateOfBirth,
      patientMedicalData: patientProfile.patientMedicalData,
      patientInfo: patientProfile.patientInfo,
      employmentInfoUpdatedAt: patientProfile.employmentInfoUpdatedAt,
      medicalDataUpdatedAt: patientProfile.medicalDataUpdatedAt,
      email: '',
      listHpmInformation: null!
    }
    return patientProfileResponse;
  }

  const savePatientAndTimelineModel = (patientProfile: PatientProfile, timelineModel: TimelineModel) => {
    const patientProfileResponse = mapPatientToPatientProfiles(patientProfile)
    setPatient({
      ...patientProfileResponse!,
      timelineModel,
    })
  }

  const onSubmit = async (values: DoctorLetter): Promise<TimelineModel | undefined> => {
    if (!selectedRow || !selectedRow.id) return;
    try {
      const payload: UpdateEABRequest = {
        eABId: selectedRow.id,
        doctorLetterId: selectedRow.entryDoctorLetter.id!,
        timelineModel: {
          ...selectedRow.entryDoctorLetter,
          doctorLetter: values
        }
      }
      const res = await updateEAB(payload)
      if (res.data) {
        const { patientProfile } = res.data.eABData
        savePatientAndTimelineModel(patientProfile, res.data.timelineModel)
        return res.data.timelineModel
      }
      return undefined;
    } catch (err) {
      return err
    }
  }

  const onCloseConfirmDialog = () => {
    setConfirmDialog(false);
    setSelectedRow(undefined)
  }

  const handleDelete = () => {
    if (!selectedRow || !selectedRow.id) {
      return;
    }

    deleteEAB({
      id: selectedRow.id,
    })
  }

  return (
    <Flex column className={className}>
      <ActionsBar buttonsAction={getButtonsAction()} />{' '}
      <Flex column className="sl__table">
        <Table
          noHeader
          selectableRowsHighlight
          data={isSuccess ? data.eABItems : []}
          columns={getColumns()}
          pagination
          paginationServer
          paginationPerPage={pagination.pageSize}
          paginationResetDefaultPage
          paginationTotalRows={isSuccess ? data.paginationResponse.total : 0}
          progressPending={isLoading}
          onSelectedRowsChange={(selectedRowState) => {
            eDocumentsActions.setSelectedRowId(undefined);
            eDocumentsActions.setSelectedRowsState(selectedRowState);
          }}
          onChangePage={onChangePage}
          onChangeRowsPerPage={onChangeRowsPerPage}
        />
      </Flex>
      {selectedRow && !confirmDialog &&
        <DoctorLetterCreateEditDialog patientId={selectedRow.patientProfile.id}
          isDisableTemplate
          mode={mode}
          activatedSchein={{
            type: 'public', scheinId: selectedRow.entryDoctorLetter.doctorLetter?.scheinId || undefined
          }}
          defaultDoctorLetterValue={selectedRow.entryDoctorLetter.doctorLetter}
          onPrint={async () => {
            const pdf = selectedRow.pDFUrl || '';
            const [bucketName, objectName] = pdf.split('/');
            try {
              const result = await getPresignedGetURL({
                bucketName: bucketName,
                objectName: objectName,
              });
              setViewFileUrl(result.data.presignedURL)
            } catch (err) {
              throw err
            }
          }}
          successToastMessage={t('saveSuccess')}
          onSubmit={onSubmit}
          onClose={() => {
            setSelectedRow(undefined)
            setPatient(undefined)
          }}
        />
      }
      {confirmDialog && (
        <DeleteConfirmDialog
          isLoading={isLoadingDelete}
          confirm={handleDelete}
          close={onCloseConfirmDialog}
          isOpen={Boolean(confirmDialog)}
          text={
            {
              btnCancel: tButtonActions('no'),
              btnOk: tButtonActions('yesDelete'),
              title: tConfirmDialog('deleteEABTitle'),
              message: tConfirmDialog('textContent'),
            }
          }
        />
      )}
      {Boolean(fileUrl) && (
        <PrintPreviewPdfDialog
          formId="Doctor_Letter"
          file={fileUrl}
          isAllowSendKim
          timelineId={patientTimeline ? patientTimeline.timelineModel.id : undefined}
          patient={patientTimeline}
          onClose={() => {
            setViewFileUrl('');
            setSelectedRow(undefined)
          }}
          onPrintSuccess={async () => {
            updateEABStatus({
              id: selectedRowId!,
              status: DocumentStatus.Status_Printed
            })
          }}
          PageProps={{
            scale: 1.5,
          }}
        />
      )}
    </Flex>
  );
};

export default EAB;
