import React, { memo, useMemo } from 'react';
import debounce from 'lodash/debounce';
import MomentLocaleUtils from 'react-day-picker/moment';

import type EDocumentsI18n from '@tutum/mvz/locales/en/EDocuments.json';

import {
  Button,
  BodyTextM,
  BodyTextS,
  Flex,
  Svg,
  IMenuItem,
  MultiSelect,
  MultiValueContainerComponent,
} from '@tutum/design-system/components';
import { InputGroup } from '@tutum/design-system/components/Core';
import i18n from '@tutum/infrastructure/i18n';
import DateRangeSingleInput from '@tutum/mvz/components/date-range-single-input';
import { eDocumentsActions, useEDocumentsStore } from '../EDocuments.store';
import { DocumentStatus as Status } from '@tutum/hermes/bff/qes_common';
import { IButtonsActionType } from '../EDocuments.types';
import { scaleSpace } from '@tutum/design-system/styles';
import { COLOR } from '@tutum/design-system/themes/styles/color';

const SearchIcon = '/images/search-sidebar-disable.svg';

const commonButtonActionProps = {
  outlined: true,
};

export interface IActionsBarProps {
  className?: string;
  buttonsAction?: IButtonsActionType[];
}

const ActionsBar = (props: IActionsBarProps) => {
  const { className, buttonsAction } = props;

  const { setFilters } = eDocumentsActions;

  const { t } = i18n.useTranslation<keyof typeof EDocumentsI18n>({
    namespace: 'EDocuments',
  });
  const { t: tCommon } = i18n.useTranslation({
    namespace: 'Common',
  });
  const eDocumentStore = useEDocumentsStore();

  const selectedCount = useMemo(() => {
    const selectedTab = eDocumentStore[eDocumentStore.selectedTabId];
    if (selectedTab && selectedTab.selectedRowState) {
      return selectedTab.selectedRowState.selectedCount;
    }

    return 0;
  }, [
    eDocumentStore.selectedTabId,
    eDocumentStore[eDocumentStore.selectedTabId],
  ]);

  const handleSearch = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ searchKey: e.target.value });
  }, 500);

  const handleFilterDates = (startDate: Date, endDate: Date) => {
    if ((startDate && endDate) || (!startDate && !endDate)) {
      const createdAtBegin = startDate ? startDate.getTime() : null!;
      const createdAtEnd = endDate ? endDate.getTime() : null!;
      setFilters({
        createdAtBegin,
        createdAtEnd,
      });
    }
  };

  const handleFilterStatus = (newValue: Array<IMenuItem<string | string>>) => {
    setFilters({ statuses: newValue.map((status) => status.value as Status) });
  };

  const statusesOptions = useMemo<IMenuItem[]>(
    () =>
      Object.keys(Status)
        .map((key) => {
          if (
            Status[key] === Status.Status_Sent ||
            Status[key] === Status.Status_Created
          ) {
            return {
              label: t(Status[key]),
              value: Status[key],
            };
          }
        })
        .filter((status): status is IMenuItem => !!status),
    []
  );

  return (
    <Flex className={className} w="100%">
      <Flex w="100%" gap={scaleSpace(4)} column>
        {selectedCount > 0 ? (
          <Flex justify="space-between" gap={scaleSpace(4)}>
            <Flex gap={8}>
              {buttonsAction?.map(
                (buttonAction, index) =>
                  buttonAction.batchActions && (
                    <Button
                      key={index}
                      {...commonButtonActionProps}
                      {...buttonAction}
                      title={buttonAction.title}
                      onClick={() => buttonAction.handleClick()}
                    >
                      <BodyTextM
                        limitLines={1}
                        fontWeight={600}
                        color={COLOR.TEXT_SECONDARY_NAVAL}
                      >
                        {buttonAction.title}
                      </BodyTextM>
                    </Button>
                  )
              )}
            </Flex>
            <Flex align="center">
              <BodyTextM whiteSpace="nowrap">
                {t('itemsSelected', {
                  count: selectedCount,
                })}
              </BodyTextM>
            </Flex>
          </Flex>
        ) : (
          <Flex gap={scaleSpace(4)} w="100%">
            <Flex className="sl-eDocuments-actions-bar__input-search">
              <InputGroup
                leftIcon={<Svg src={SearchIcon} />}
                placeholder={tCommon('Input.search')}
                onChange={handleSearch}
              />
            </Flex>
            <div className="sl-eDocuments__divider" />
            <Flex className="sl-eDocuments-actions-bar__date-range-picker">
              <BodyTextS fontWeight="Medium">{t('createdOn')}</BodyTextS>
              <DateRangeSingleInput
                onChange={handleFilterDates}
                localeUtils={MomentLocaleUtils}
              />
            </Flex>
            <Flex className="sl-eDocuments-actions-bar__status">
              <BodyTextS fontWeight="Medium">{t('status')}</BodyTextS>
              <MultiSelect
                onChange={handleFilterStatus}
                options={statusesOptions}
                placeholder={tCommon('Select.select')}
                components={{
                  ValueContainer: MultiValueContainerComponent,
                }}
              />
            </Flex>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
};

export default memo(ActionsBar);
