import { proxy, useSnapshot } from 'valtio';

import { ERezeptType, TabIds, EAUType, TabIdsType } from './EDocuments.types';
import { ErezeptDto } from '@tutum/hermes/bff/app_mvz_erezept';
import { PaginationRequest, Order } from '@tutum/hermes/bff/common';
import { IDataTableSelectedRowState } from '@tutum/design-system/components/Table';
import { getPresignedURL } from './EDocument.helper';
import {
  PrintFileTypes,
  printSettingsActions,
} from '../hooks/usePrintSettings.store';

import { EAUModel } from '@tutum/hermes/bff/legacy/eau_common';
import { GetSignatureInfoResponse } from '@tutum/hermes/bff/legacy/app_mvz_qes';
import { FormName } from '@tutum/hermes/bff/legacy/form_common';

type Filter = {
  createdAtBegin?: number;
  createdAtEnd?: number;
  searchKey?: string;
  statuses?: string[];
};

export interface IEDocumentsStore {
  [TabIds.eRezept]: ERezeptType;
  [TabIds.eAU]: EAUType;
  [TabIds.eAB]: EAUType;
  pagination: PaginationRequest;
  filter: Filter;
  selectedTabId: TabIdsType;
  fetchSignatureError: string;
  selectedRowId: string | undefined;
  signatureInfo: GetSignatureInfoResponse | undefined;
}

export interface IEDocumentsActions {
  clearData: () => void;
  viewErezeptPDF: (url?: string) => Promise<void>;
  setSelectedTabId: (newTabId: TabIdsType) => void;
  setPagination: (pagination: PaginationRequest) => void;
  setFilters: (payload: Filter) => void;
  setSelectedRowsState: (
    selectedRowState) => void;
  setSelectedRowId: (selectedRowId: string | undefined) => void;
  setShowPreviewERP: (payload: boolean) => void;
  setLoadingSignAndSend: (isLoading: boolean) => void;
  setLoadingPrint: (isLoading: boolean) => void;
  setSignatureInfo: (payload: GetSignatureInfoResponse) => void;
  print: (payload: PrintFileTypes) => Promise<void>;
  clearFilters: () => void;
}

const PAGINATION_INFO: PaginationRequest = {
  page: 1,
  pageSize: 30,
  sortBy: '',
  order: Order.DESC,
};

export const initSelectedRowState = {
  allSelected: false,
  selectedCount: 0,
  selectedRows: [],
};

/**
 * eDocuments initial state
 */
const initState: IEDocumentsStore = {
  selectedTabId: TabIds.eRezept, // Set default tab
  fetchSignatureError: '',
  pagination: PAGINATION_INFO,
  filter: {
    createdAtBegin: undefined,
    createdAtEnd: undefined,
    searchKey: '',
    statuses: undefined,
  },
  selectedRowId: undefined,
  [TabIds.eRezept]: {
    isLoadingERP: false,
    isLoadingSignAndSend: false,
    isLoadingPrint: false,
    isShowPreviewERP: false,
    eprUrl: '',
    selectedRowState: initSelectedRowState,
  },
  [TabIds.eAU]: {
    selectedRowState: initSelectedRowState,
  },
  [TabIds.eAB]: {
    selectedRowState: initSelectedRowState,
  },
  signatureInfo: undefined,
};

export const eDocumentsStore = proxy<IEDocumentsStore>({ ...initState });

/**
 * Define eDocuments actions
 */
export const eDocumentsActions: IEDocumentsActions = {
  viewErezeptPDF: async (url) => {
    if (!url) {
      return;
    }

    eDocumentsStore[TabIds.eRezept].isLoadingERP = true;
    try {
      eDocumentsStore[TabIds.eRezept].eprUrl = await getPresignedURL(url);
      eDocumentsActions.setShowPreviewERP(true);
    } finally {
      eDocumentsStore[TabIds.eRezept].isLoadingERP = false;
    }
  },
  setSelectedTabId: (newTabId) => {
    eDocumentsStore.selectedTabId = newTabId;
  },
  setPagination: (pagination) => {
    eDocumentsStore.pagination = {
      ...eDocumentsStore.pagination,
      ...pagination,
    };
  },
  setFilters: (payload) => {
    eDocumentsStore.filter = { ...eDocumentsStore.filter, ...payload };
  },
  setSelectedRowsState: (selectedRowState) => {
    eDocumentsStore[eDocumentsStore.selectedTabId].selectedRowState = {
      ...eDocumentsStore[eDocumentsStore.selectedTabId].selectedRowState,
      ...selectedRowState,
    };
  },
  setSelectedRowId: (selectedRowId) => {
    eDocumentsStore.selectedRowId = selectedRowId;
  },
  setShowPreviewERP: (isShowPreviewERP) => {
    if (!isShowPreviewERP) {
      eDocumentsStore[TabIds.eRezept].eprUrl = '';
    }
    eDocumentsStore[TabIds.eRezept].isShowPreviewERP = isShowPreviewERP;
  },
  setLoadingSignAndSend: (isLoading) => {
    eDocumentsStore[TabIds.eRezept].isLoadingSignAndSend = isLoading;
  },
  setLoadingPrint: (isLoading) => {
    eDocumentsStore[TabIds.eRezept].isLoadingPrint = isLoading;
  },
  clearData: () => {
    eDocumentsActions.setSelectedRowsState({ ...initSelectedRowState });
  },
  print: async (payload) => {
    const { files } = payload;
    const filesUrl = await Promise.all(
      files.map((item) => getPresignedURL(item))
    );
    if (filesUrl) {
      await printSettingsActions.printFile({
        files: filesUrl,
        formId: FormName.Muster_eRezept,
      });
    }
  },
  setSignatureInfo: (payload) => {
    eDocumentsStore.signatureInfo = payload;
  },
  clearFilters: () => {
    eDocumentsStore.filter = initState.filter;
  },
};

export function useEDocumentsStore() {
  return useSnapshot(eDocumentsStore);
}
