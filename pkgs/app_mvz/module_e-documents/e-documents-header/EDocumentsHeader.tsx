import React, { memo, useEffect, useMemo } from 'react';

import type EDocumentsI18n from '@tutum/mvz/locales/en/EDocuments.json';

import { TabIds, TabIdsType } from '../EDocuments.types';
import { eDocumentsActions, useEDocumentsStore } from '../EDocuments.store';
import {
  BodyTextM,
  BodyTextS,
  Button,
  Flex,
  H1,
  ITabProps,
  LoadingState,
  Svg,
  Tabs,
  Tooltip,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import i18n from '@tutum/infrastructure/i18n';
import { useErrorCodeI18n } from '@tutum/mvz/hooks/useErrorCode';
import { CardTypeType } from '@tutum/hermes/bff/card_common';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  ComfortSignatureStatus,
  useMutationActivateComfortSignature,
  useMutationDeactivateComfortSignature,
  useQueryGetSignatureInfo,
} from '@tutum/hermes/bff/legacy/app_mvz_qes';
import { ComfortSignature } from '@tutum/mvz/components/comfort-signature';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { useListenEAUChanged } from '@tutum/hermes/bff/app_mvz_eau';
import { useListenQESInputPin } from '@tutum/hermes/bff/app_mvz_qes';
import { PinStatus } from '@tutum/hermes/bff/qes_common';

const CardIcon = '/images/card-chip.svg';

export interface IEDocumentsHeaderProps {
  className?: string;
  doctorId: string;
}

const EDocumentsHeader = (props: IEDocumentsHeaderProps) => {
  const { className, doctorId } = props;
  const { t } = i18n.useTranslation<keyof typeof EDocumentsI18n>({
    namespace: 'EDocuments',
  });

  const tError = useErrorCodeI18n();

  const { selectedTabId } = useEDocumentsStore();
  const { globalData } = GlobalContext.useContext();

  const {
    data: signatureInfo,
    error,
    isSuccess,
    isFetching,
    refetch: refetchSignature,
  } = useQueryGetSignatureInfo(
    {
      bsnr: globalData.userProfile?.bsnr!,
      doctorId: globalData.userProfile?.id!,
    },
    {
      enabled:
        !!globalData.userProfile?.bsnr &&
        !!globalData.userProfile?.id,
      throwOnError: false,
    }
  );

  useListenEAUChanged(() => {
    refetchSignature();
  });

  useListenQESInputPin(({ pinStatus }) => {
    if (
      pinStatus === PinStatus.PinStatus_ActivateConformSignatureSuccessfully
    ) {
      alertSuccessfully(t('comfortSignatureActivated'));
      refetchSignature();
    }
    if (pinStatus === PinStatus.PinStatus_ActivateConformSignatureFailed) {
      alertError(t('comfortSignatureActivateFailed'));
    }
  });

  useEffect(() => {
    if (isSuccess) {
      eDocumentsActions.setSignatureInfo(signatureInfo);
    }
  }, [isSuccess, signatureInfo]);

  useEffect(() => {
    if (error?.response?.data?.message) {
      alertError(error?.response?.data?.message);
    } else {
      if (error?.response?.data?.serverError) {
        alertError(tError(error?.response?.data?.serverError as any));
      }
    }
  }, [error]);

  const { mutateAsync: mutateActivate, isPending: isLoadingActivate } =
    useMutationActivateComfortSignature();

  const { mutateAsync: mutateDeactivate, isPending: isLoadingDeactivate } =
    useMutationDeactivateComfortSignature({
      onSuccess: () => {
        alertSuccessfully(t('comfortSignatureDeactivated'));
        refetchSignature();
      },
    });

  const tabs = useMemo<ITabProps[]>(
    () => [
      {
        id: TabIds.eRezept,
        title: t('eRezept'),
      },
      {
        id: TabIds.eAU,
        title: t('eAU'),
      },
      {
        id: TabIds.eAB,
        title: t('eAB'),
      },
      {
        id: TabIds.comfortSignatureLog,
        title: t('comfortSignatureLog'),
      },
    ],
    []
  );

  const onChangeTab = (newTabId: TabIdsType) => {
    eDocumentsActions.setSelectedTabId(newTabId);
    eDocumentsActions.clearFilters();
  };

  const getContent = (content: string) => {
    return content;
  };

  const cardsInformation = useMemo(() => {
    if (!isSuccess) {
      return null;
    }

    const doctorCards = [
      signatureInfo?.doctorCard,
      signatureInfo?.practiceCard,
    ].filter((card) => !!card);
    if (!doctorCards.length) return null;

    return (
      <Flex gap={8}>
        {doctorCards.map((card) => {
          if (!card) {
            return null;
          }

          const cardType =
            card.cardType === CardTypeType.CardTypeTypeHBA
              ? t(CardTypeType.CardTypeTypeHBA)
              : t(CardTypeType.CardTypeTypeSMCB);

          return (
            <Flex column key={card.cardHolder}>
              <BodyTextM color="white" fontWeight="SemiBold">
                {getContent(`${cardType} • ${card.cardHolder}`)}
              </BodyTextM>
              <BodyTextM
                color={COLOR.TEXT_TERTIARY_SILVER}
                className="sl-eDocuments-header__card-iccsn"
              >
                {card.cardId}
              </BodyTextM>
            </Flex>
          );
        })}
      </Flex>
    );
  }, [isSuccess, signatureInfo]);

  const handleActivateOrDeactivate = async () => {
    if (
      signatureInfo?.comfortSignatureInfo?.status ===
      ComfortSignatureStatus.ComfortSignatureStatus_Activated
    ) {
      await mutateDeactivate({
        doctorId,
        cardId: signatureInfo?.doctorCard?.cardId!,
      });
    } else {
      await mutateActivate({
        doctorId,
        cardId: signatureInfo?.doctorCard?.cardId!,
      });
    }
  };

  return (
    <Flex align="center" justify="space-between" className={className}>
      <Flex className="sl-eDocuments-header">
        <H1 className="sl-eDocuments-header__title">
          {t('eDocumentsOverview')}
        </H1>
        <Tabs
          id="eDocumentsTabs"
          selectedTabId={selectedTabId}
          tabs={tabs}
          onChange={onChangeTab}
        />
      </Flex>
      <Flex gap={16} align="center">
        {isFetching ? (
          <LoadingState size={32} className="sl-eDocuments-header__loading" />
        ) : (
          isSuccess &&
          signatureInfo?.comfortSignatureInfo && (
            <>
              <Button
                outlined
                loading={isLoadingActivate || isLoadingDeactivate}
                onClick={handleActivateOrDeactivate}
              >
                {signatureInfo?.comfortSignatureInfo?.status ===
                ComfortSignatureStatus.ComfortSignatureStatus_Activated
                  ? t('deactivated')
                  : t('activate')}
              </Button>
              <div>
                <BodyTextS className="sl-eDocuments-header__signature--title">
                  {t('comfortSignature')}
                </BodyTextS>
                <ComfortSignature
                  showSelectiveCard={false}
                  signatureInfo={signatureInfo}
                />
              </div>
              <div className="sl-eDocuments__divider" />
            </>
          )
        )}
        {(signatureInfo?.doctorCard || signatureInfo?.practiceCard) && (
          <Tooltip content={cardsInformation!}>
            <Svg className="sl-eDocuments-header__card" src={CardIcon} />
          </Tooltip>
        )}
      </Flex>
    </Flex>
  );
};

export default memo(EDocumentsHeader);
