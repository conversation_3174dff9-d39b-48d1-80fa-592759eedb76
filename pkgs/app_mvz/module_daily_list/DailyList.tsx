import { useState, useMemo } from 'react';
import I18n from '@tutum/infrastructure/i18n';
import type DailyListLang from '@tutum/mvz/locales/en/DailyList.json';
import type ScheinLang from '@tutum/mvz/locales/en/Schein.json';
import type PatientOverview from '@tutum/mvz/locales/en/PatientOverview.json';
import {
  BodyTextL,
  Button,
  Flex,
  H1,
  Svg,
} from '@tutum/design-system/components';
import { Intent, Switch } from '@tutum/design-system/components/Core';
import { dailyListStages } from '@tutum/mvz/stagers/dailyList.stager';
import {
  FilterGroupNames,
  FilterGroupInfo,
  DailyListProps,
  DEFAULT_FILTER_VALUE,
} from './types';
import Table from '@tutum/design-system/components/Table';
import { PAGINATION_DEFAULT } from '@tutum/design-system/consts/table';
import { Order, PaginationRequest } from '@tutum/hermes/bff/common';
import { genColumns } from './helpers';
import { COLOR } from '@tutum/design-system/themes/styles';
import {
  useMutationToggleDone,
  useQueryGetDailyList,
} from '@tutum/hermes/bff/legacy/app_mvz_daily_list';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import FilterDialog from './FilterDialog/FilterDialog.styled';
import DateRangeSingleInput from '@tutum/mvz/components/date-range-single-input';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import { useEmployeeStore } from '@tutum/mvz/hooks/useEmployee';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import DatePickerForm from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-widget/date-picker-form/DatePickerForm.styled';

const { Stager, useListen, dispatch } = dailyListStages;

const FilterIcon = '/images/filter.svg';
const FilterActiveIcon = '/images/fill-filter.svg';

const DailyList = ({ className }: DailyListProps) => {
  const { t } = I18n.useTranslation<keyof typeof DailyListLang>({
    namespace: 'DailyList',
  });
  const { t: tFilter } = I18n.useTranslation<keyof typeof DailyListLang.Filter>(
    {
      namespace: 'DailyList',
      nestedTrans: 'Filter',
    }
  );
  const { t: tTable } = I18n.useTranslation<keyof typeof DailyListLang.Table>({
    namespace: 'DailyList',
    nestedTrans: 'Table',
  });
  const { t: tSchein } = I18n.useTranslation<keyof typeof ScheinLang>({
    namespace: 'Schein',
  });
  const { t: tCommon } = I18n.useTranslation<
    keyof typeof CommonLocales.DateTimePicker
  >({
    namespace: 'Common',
    nestedTrans: 'DateTimePicker',
  });

  const { t: tOverview } = I18n.useTranslation<
    keyof typeof PatientOverview.PatientOverviewList
  >({
    namespace: 'PatientOverview',
    nestedTrans: 'PatientOverviewList',
  });
  const [filterValues, setFilterValues] =
    useState<FilterGroupInfo>(DEFAULT_FILTER_VALUE);
  const [isShowFilterDialog, setShowFilterDialog] = useState<boolean>(false);
  const [pagination, setPagination] = useState<PaginationRequest>({
    ...PAGINATION_DEFAULT,
    order: Order.DESC,
  });
  const { userProfile } = useEmployeeStore();
  const { data, isFetching, error, refetch } = useQueryGetDailyList(
    {
      doctorIds: filterValues[FilterGroupNames.DOCTOR],
      onlyShowTimeline: filterValues[FilterGroupNames.TOGGLE_DOCUMENTED],
      isUnDone: filterValues[FilterGroupNames.TOGGLE_UNDONE],
      patientType: filterValues[FilterGroupNames.PATIENT_TYPE],
      dateRange: filterValues[FilterGroupNames.FROM]
        ? {
            startDate: filterValues[FilterGroupNames.FROM]!,
            endDate: filterValues[FilterGroupNames.TO]!,
          }
        : undefined,
      paginationRequest: pagination,
      showRecentlyViewedPatients:
        filterValues[FilterGroupNames.TOGGLE_SHOW_RECENTLY_VIEWED_PATIENTS],
    },
    {
      select: (res) => res.data,
    }
  );

  const countFilteredValues = useMemo(() => {
    let count = 0;

    if (filterValues[FilterGroupNames.DOCTOR][0]) {
      count += 1;
    }

    if (filterValues[FilterGroupNames.TOGGLE_DOCUMENTED]) {
      count += 1;
    }

    if (filterValues[FilterGroupNames.PATIENT_TYPE][0]) {
      count += filterValues[FilterGroupNames.PATIENT_TYPE].length;
    }

    if (filterValues[FilterGroupNames.TOGGLE_UNDONE]) {
      count += 1;
    }

    return count;
  }, [filterValues]);

  if (error) {
    throw error;
  }

  const mutationToggleUndone = useMutationToggleDone({
    onSuccess: () => {
      refetch();
      dispatch('finish');
    },
  });

  const onChangePage = (page: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      page,
    }));
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      pageSize: currentRowsPerPage,
    }));
  };

  const handleUndone = async (dailyListId: string) => {
    dispatch('toToggleUndone', dailyListId);

    mutationToggleUndone.mutate({
      dailyListId,
    });
  };

  useListen('default', ({ context }) => {
    if (context?.refetch) refetch();
  });

  return (
    <Stager initialStage={{ stage: 'default', context: undefined }}>
      {() => (
        <div className={className}>
          <Flex align="center" className="sl-Header_Section">
            <H1>{t('title')}</H1>
          </Flex>
          <Flex className="sl-daily-list__content" column p="16px 0 0">
            <Flex p="0 16px 16px" gap={8} align="center">
              <Button
                outlined
                intent={countFilteredValues > 0 ? Intent.PRIMARY : Intent.NONE}
                icon={
                  <Svg
                    src={
                      countFilteredValues > 0 ? FilterActiveIcon : FilterIcon
                    }
                  />
                }
                className="filter"
                text={`${tFilter('title')} ${
                  countFilteredValues > 0 ? `(${countFilteredValues})` : ''
                }`.trim()}
                onClick={() => setShowFilterDialog(true)}
              />
              <DateRangeSingleInput
                className="sl-date-range-input"
                placeholder={`${tCommon('DD_MM_YYYY')} - ${tCommon(
                  'DD_MM_YYYY'
                )}`}
                defaultStartDate={datetimeUtil
                  .startOf(datetimeUtil.date(), 'day')
                  .toDate()}
                defaultEndDate={datetimeUtil
                  .endOf(datetimeUtil.date(), 'day')
                  .toDate()}
                isQuarterShortcuts
                quarterLength={8}
                onChange={(fromDate, toDate) => {
                  setFilterValues((prevValues) => ({
                    ...prevValues,
                    [FilterGroupNames.FROM]: fromDate?.getTime()!,
                    [FilterGroupNames.TO]: toDate?.getTime()!,
                  }));
                }}
              />
              <Switch
                label={t('showOnlyRecentlyViewedPatients', {
                  fullName: nameUtils.getDoctorName(userProfile),
                })}
                checked={filterValues.showRecentlyViewedPatients}
                onChange={() =>
                  setFilterValues((prevValues) => ({
                    ...prevValues,
                    showRecentlyViewedPatients:
                      !prevValues.showRecentlyViewedPatients,
                  }))
                }
              />
            </Flex>
            <Table
              className={getCssClass('sl-daily-list__table', {
                'has-value': !!data?.dailyLists?.[0],
              })}
              columns={genColumns({
                t: tTable,
                tSchein,
                tOverview,
                handleUndone,
              })}
              highlightOnHover
              noHeader
              persistTableHead
              striped
              data={data?.dailyLists || []}
              responsive={false}
              progressPending={isFetching || mutationToggleUndone.isPending}
              noDataComponent={
                <Flex my={16} column>
                  <BodyTextL
                    fontSize="20px"
                    fontWeight={700}
                    color={COLOR.TEXT_SECONDARY_NAVAL}
                  >
                    {tTable('noResultFoundTitle')}
                  </BodyTextL>
                </Flex>
              }
              pagination
              paginationServer
              paginationDefaultPage={pagination.page}
              paginationResetDefaultPage
              paginationPerPage={pagination.pageSize}
              paginationTotalRows={data?.paginationResponse.total || 0}
              onChangePage={onChangePage}
              onChangeRowsPerPage={onChangeRowsPerPage}
            />
            <FilterDialog
              isOpen={isShowFilterDialog}
              filteredValues={filterValues}
              onConfirm={(filterValues: FilterGroupInfo) => {
                setFilterValues(filterValues);
                setShowFilterDialog(false);
                onChangePage(1);
              }}
              onClose={() => setShowFilterDialog(false)}
            />
          </Flex>
        </div>
      )}
    </Stager>
  );
};

export default DailyList;
