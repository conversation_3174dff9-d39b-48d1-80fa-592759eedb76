import { IEmployee } from '@tutum/admin/module_employee/Employee.service';
import { Gender } from '@tutum/infrastructure/resource/ProfileResource';
import { UserType } from '@tutum/infrastructure/resource/MvzAppResource';

export { Gender, UserType };

export interface IProfile extends IEmployee {
  id?: string;
  careProviderId: string;
  careProviderName?: string;
  fullName: string;
  firstName: string;
  lastName: string;
  dob?: number;
  gender?: Gender;
  phone?: string;
  email?: string;
  address?: string;
  title?: string;
  initial: string;
  orgId: string;
}

export interface IEmployeeProfile extends IProfile {
  accountId: string;
  types: UserType[];
  doctorStamp: string;
  lanr: string;
  bsnr?: string;
  bsnrCity?: string;
  bsnrFacilityType?: string;
  bsnrPracticeStamp?: string;
  hasHzvContracts?: boolean;
  hasFavContracts?: boolean;
  havgId?: string;
  mediId?: string;
  teamNumbers?: string[];
  bsnrName?: string;
  bsnrNumber?: string;
  bsnrStreet?: string;
  bsnrPostCode?: string;
  bsnrPhoneNumber?: string;
  bsnrFaxNumber?: string;
  bsnrEmail?: string;
  orgId: string;
  deviceId?: string;
  externalId?: string;
  handleClick?: () => void;
  bsnrId?: string;
  bsnrs?: string[];
  bsnrIds?: string[];
}

export default { Gender, UserType };
