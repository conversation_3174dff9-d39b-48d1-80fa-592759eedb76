import React from 'react';
import { OverlayToaster } from '@tutum/design-system/components/Core';
import PDFFrame from '@tutum/mvz/components/pdf-frame';
import { IMvzTheme } from '@tutum/mvz/theme';

export interface IPDFEditorProps {
  className?: string;
  theme?: IMvzTheme;
  formTemplateId?: string;
  patientId?: string;
  doctorId?: string;
  contractId?: string;
  isNewFormWithData?: boolean;
  isReadOnly?: boolean;
  formMeta?: { [key: string]: any };
  formDataId?: string;
  formData?: Array<{ [key: string]: any }>;
  extraData?: { [key: string]: any };
  enrichData?: { [key: string]: any };
  hasChangedFieldData?: (data: any) => void;
  onValidatedErrors?: (errors: any[]) => void;
  onLoadingChanged?: (params: {
    isLoading: boolean;
    actionName: string;
  }) => void;
  onFormLoaded?: (params: any) => void;
  onPagesRendered?: () => void;
  onSaved?: (formData: any) => void;
  onCreated?: (formData: any) => void;
}

export interface IPDFEditorState {
  currentFormData: any;
}

export default class PDFEditor extends React.PureComponent<
  IPDFEditorProps,
  IPDFEditorState
> {
  private _toasterRef = React.createRef<OverlayToaster>();
  private _pdfFrame: PDFFrame;

  state: IPDFEditorState = {
    currentFormData: undefined,
  };

  componentDidMount() {
    if (this.props.formDataId) {
      this._loadFormData(this.props.formDataId);
    } else {
      const { formTemplateId, patientId, contractId, doctorId } = this.props;
      this._loadFormTemplate({
        formTemplateId,
        patientId,
        contractId,
        doctorId,
      });
    }
  }

  componentWillReceiveProps(nextProps: IPDFEditorProps) {
    if (
      nextProps.formDataId &&
      nextProps.formDataId !== this.props.formDataId
    ) {
      this._loadFormData(nextProps.formDataId);
    }
    if (nextProps.doctorId && nextProps.doctorId !== this.props.doctorId) {
      const { formTemplateId, patientId, contractId, doctorId } = nextProps;
      this._loadFormTemplate({
        formTemplateId,
        patientId,
        contractId,
        doctorId,
      });
    }
  }

  render() {
    const { isNewFormWithData } = this.props;
    const url = this._getURL();

    return (
      <div style={{ height: '100%', backgroundColor: '#3F3F3F' }}>
        {!url && <div>Loading...</div>}
        {url && (
          <PDFFrame
            ref={this._setPDFFrameRef}
            id="pdf-iframe"
            url={url}
            onPagesRendered={this._handlePagesRendered}
            onFieldChanged={isNewFormWithData && this._handleFieldChanged || undefined}
          />
        )}
        <OverlayToaster ref={this._toasterRef} />
      </div>
    );
  }

  private _getURL = () => {
    const { formMeta } = this.props;

    if (!formMeta || !formMeta.uri) {
      return undefined;
    }

    return `/scripts/pdf-editor/pdfjs-1.6/web/viewer-altered.html?file=${formMeta.uri}`;
  };

  private _loadFormData = (formDataId: string) => {
    this._setLoading({ isLoading: true, actionName: 'loadMeta' });

    Promise.resolve(() => {
      // Do load form data via service here
    })
      .then((data) => {
        this._handleResponseData(data);
        this._setLoading({ isLoading: false, actionName: 'loadMeta' });
      })
      .catch((error) => {
        console.error(error);
        this._setLoading({ isLoading: false, actionName: 'loadMeta' });
      });
  };

  private _loadFormTemplate = (params: {
    formTemplateId?: string;
    patientId?: string;
    doctorId?: string;
    contractId?: string;
  }) => {
    this._setLoading({ isLoading: true, actionName: 'loadMeta' });

    Promise.resolve(() => {
      // Do load form template via service here
    })
      .then((data) => {
        this._handleResponseData(data);
        this._setLoading({ isLoading: false, actionName: 'loadMeta' });
      })
      .catch((error) => {
        console.error(error);
        this._setLoading({ isLoading: false, actionName: 'loadMeta' });
      });
  };

  private _handleResponseData = (data) => {
    const { doctorId, onFormLoaded } = this.props;
    const { formData, formDataId, extraData, batchIds, ...formMeta } = data;
    const { currentFormData } = this.state;
    const isMergeRequired = doctorId !== formMeta.performingDoctorId;
    const savedFormDataId = this.props.formDataId
      ? this.props.formDataId
      : formDataId;
    const mergedFormData = isMergeRequired
      ? { ...formData, ...currentFormData }
      : formData;

    this.setState({
      currentFormData: mergedFormData,
    });

    if (onFormLoaded) {
      onFormLoaded({
        formMeta,
        mergedFormData,
        savedFormDataId,
        extraData,
        batchIds,
      });
    }
  };

  private _handlePagesRendered = () => {
    const { formMeta, formData, enrichData } = this.props;
    this._pdfFrame
      .enrichFormMeta({ meta: formMeta?.matchingTable, data: enrichData })
      .then(() => {
        this._pdfFrame.deserializeForm(formData);
        this.validateFormData(formData);

        if (this.props.onPagesRendered) {
          this.props.onPagesRendered();
        }
      })
      .then(() => {
        if (this.props.isReadOnly) {
          this._pdfFrame.makeFormReadOnly();
        }
      });
  };

  private _handleFieldChanged = () => {
    if (this.props.isNewFormWithData) {
      this._pdfFrame
        .serializeForm()
        .then((data) => this.props.hasChangedFieldData?.(data));
    }
  };

  private _setLoading = (params: {
    isLoading: boolean;
    actionName: 'loadMeta' | 'save';
  }) => {
    if (this.props.onLoadingChanged) {
      this.props.onLoadingChanged(params);
    }
  };

  validateFormData = (formData) => {
    const { validation, meta } =
      (this.props.formMeta && this.props.formMeta.matchingTable) || {};

    let validationErrors;
    if (validation) {
      // Do validation here;
    }

    this._highlightErrorFields(validationErrors);

    if (this.props.onValidatedErrors) {
      this.props.onValidatedErrors(validationErrors);
    }

    return validationErrors;
  };

  private _highlightErrorFields = (validationErrors) => {
    if (!validationErrors) return;

    const errorFieldSet = new Set<string>();
    validationErrors.forEach((error) => {
      error.info.fieldNames.forEach((fieldName) => {
        errorFieldSet.add(fieldName);
      });
    });

    this._pdfFrame.highlightFieldsWithNames(errorFieldSet.values());
  };

  private _showError = (error: string) => {
    this._toasterRef.current?.show({
      message: error,
      intent: 'danger',
    });
  };

  private _setPDFFrameRef = (frame: PDFFrame) => {
    this._pdfFrame = frame;
  };
}
