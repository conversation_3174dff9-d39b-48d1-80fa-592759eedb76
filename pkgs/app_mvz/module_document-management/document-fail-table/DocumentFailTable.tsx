import {
  alertWarning,
  BodyTextM,
  BodyTextS,
  Svg,
} from '@tutum/design-system/components';
import Button from '@tutum/design-system/components/Button/Button';
import { Intent } from '@tutum/design-system/components/Core';
import Table, { IDataTableColumn } from '@tutum/design-system/components/Table';
import { TableActionItem } from '@tutum/design-system/components/Table/TableAction/TableAction';
import TableAction from '@tutum/design-system/components/Table/TableAction/TableAction.styled';
import { COLOR } from '@tutum/design-system/themes/styles';
import { useListenDocumentManagementChange } from '@tutum/hermes/bff/app_mvz_document_management';
import { DocumentManagementStatus } from '@tutum/hermes/bff/document_management_common';
import {
  deleteFailDocumentManagement,
  ListDocumentManagementRequest,
  reImportFailDocument,
  useQueryListDocumentManagement,
} from '@tutum/hermes/bff/legacy/app_mvz_document_management';
import { DocumentManagementItem } from '@tutum/hermes/bff/legacy/document_management_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/mvz/constant/dateTime';
import useConfirm from '@tutum/mvz/hooks/useConfirm';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import DocumentManagementI18n from '@tutum/mvz/locales/en/DocumentManagement.json';
import DocumentTableFilter from '@tutum/mvz/module_document-management/document-table-filter/DocumentTableFilter.styled';
import { useState } from 'react';

export interface IDocumentFailTableProps {
  [key: string]: any;
}

type I18nKey = keyof typeof DocumentManagementI18n;

const DocumentFailTable = ({ className }: IDocumentFailTableProps) => {
  const { t } = I18n.useTranslation<I18nKey>({
    namespace: 'DocumentManagement',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [isAllDocumentsSelected, setIsAllDocumentsSelected] = useState(false);
  const [selectedDocuments, setSelectedDocuments] =
    useState<DocumentManagementItem[] | undefined>(undefined);
  const [query, setQuery] = useState<ListDocumentManagementRequest>({
    value: '',
    isNotAssigned: false,
    status: DocumentManagementStatus.DocumentManagementStatus_Failed,
    pagination: {
      page: 1,
      pageSize: 30,
      sortBy: null!,
      order: null!,
    },
  });
  const { data, refetch } = useQueryListDocumentManagement(query);
  const documents = data?.data || [];
  const pagination = data?.pagination || { total: 0 };

  const handleReimportDocument = (document: DocumentManagementItem) => {
    reImportFailDocument({
      ids: [document.id],
      isAll: false,
    }).then(() => {
      alertWarning(t('reimportReqSent'));
    });
  };

  const handleSelectDocuments = (data) => {
    setIsAllDocumentsSelected(data?.allSelected);
    setSelectedDocuments(data.selectedRows);
  };

  const handleDeleteDocumentSelect = async (document) => {
    const isYesConfirmed = await askConfirmation();
    if (!isYesConfirmed) return;
    deleteFailDocumentManagement({ ids: [document.id], isAll: false }).then(
      () => {
        refetch();
      }
    );
  };

  const { ConfirmationDialog, askConfirmation } = useConfirm({
    title: t('removeDocumentTitle'),
    content: t('removeDocumentDesc'),
    intent: Intent.DANGER,
    cancelButton: tButtonActions('no'),
    confirmButton: tButtonActions('yesRemove'),
  });

  const handleReimportMultipleDocuments = () => {
    reImportFailDocument({
      ids: (selectedDocuments || []).map((document) => document.id),
      isAll: isAllDocumentsSelected,
    }).then(() => {
      alertWarning(t('reimportReqSent'));
    });
  };

  const handleDeleteMultipleDocuments = async () => {
    const isYesConfirmed = await askConfirmation();
    if (!isYesConfirmed) return;
    deleteFailDocumentManagement({
      ids: (selectedDocuments || []).map((document) => document.id),
      isAll: isAllDocumentsSelected,
    }).then(() => {
      refetch();
    });
  };

  const columns: IDataTableColumn<DocumentManagementItem>[] = [
    {
      id: 'importedDate',
      name: t('imported-date'),
      width: '150px',
      cell: (row) => (
        <BodyTextM>
          {datetimeUtil.dateTimeNumberFormat(row.importedDate, DATE_FORMAT)}
        </BodyTextM>
      ),
    },
    {
      id: 'documentName',
      name: t('document'),
      cell: (row) => (
        <div className={`document-name-cell`}>
          <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL} fontWeight={600}>
            {row.documentName}
          </BodyTextM>
          <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL}>
            {row.documentDirPath === '.' ? '--' : row.documentDirPath}
          </BodyTextS>
        </div>
      ),
    },
    {
      id: 'errorMessage',
      name: t('errorMessage'),
      cell: () => (
        <div className={'document-error-cell'}>
          <Svg size={24} src={'/images/alert-triangle-solid.svg'} />
          <BodyTextM color={COLOR.TAG_BACKGROUND_RED} fontWeight={600}>
            {t('failedImportMessage')}
          </BodyTextM>
        </div>
      ),
    },
    {
      id: 'action',
      width: '40px',
      cell: (row: DocumentManagementItem) => {
        const actions: TableActionItem[] = [
          {
            id: 'edit',
            label: tButtonActions('importAgain'),
            icon: <Svg src={'/images/upload.svg'} size={16} />,
            onClick: () => handleReimportDocument(row),
          },
          {
            id: 'remove',
            hoverColor: 'danger',
            label: tButtonActions('removeText'),
            icon: <Svg src={'/images/delete-value.svg'} />,
            onClick: () => handleDeleteDocumentSelect(row),
          },
        ];

        return <TableAction actions={actions} />;
      },
    },
  ];

  useListenDocumentManagementChange(() => {
    refetch();
  });

  const atLeastOneDocumentSelected = !!selectedDocuments?.length;
  return (
    <div className={className}>
      <div className="table-filter">
        {!atLeastOneDocumentSelected && (
          <DocumentTableFilter query={query} setQuery={setQuery} />
        )}
        {atLeastOneDocumentSelected && (
          <div className="document-bulk-actions">
            <Button
              intent={Intent.NONE}
              onClick={handleReimportMultipleDocuments}
            >
              <Svg src={'/images/upload.svg'} />
              <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL} fontWeight={600}>
                {tButtonActions('importAgain')}
              </BodyTextM>
            </Button>
            <Button
              intent={Intent.DANGER}
              onClick={handleDeleteMultipleDocuments}
            >
              <Svg src={'/images/delete-value.svg'} />
              <BodyTextM color={COLOR.TAG_BACKGROUND_RED} fontWeight={600}>
                Remove
              </BodyTextM>
            </Button>
          </div>
        )}
      </div>
      <Table
        columns={columns}
        data={documents}
        highlightOnHover
        selectableRows
        onSelectedRowsChange={handleSelectDocuments}
        noHeader
        persistTableHead
        striped
        // overflowY
        responsive={false}
        pagination
        paginationServer
        paginationDefaultPage={1}
        paginationResetDefaultPage
        paginationTotalRows={pagination?.total || 0}
        paginationPerPage={query.pagination?.pageSize}
        onChangePage={(page) =>
          setQuery({ ...query, pagination: { ...query.pagination!, page } })
        }
        onChangeRowsPerPage={(currentRowsPerPage) =>
          setQuery({
            ...query,
            pagination: { ...query.pagination!, pageSize: currentRowsPerPage },
          })
        }
      />
      <ConfirmationDialog />
    </div>
  );
};

export default DocumentFailTable;
