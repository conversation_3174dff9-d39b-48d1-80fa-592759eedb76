import { FormGroup2 } from '@tutum/design-system/components';
import TextArea from '@tutum/design-system/components/Core/TextArea/TextArea';
import { Field, useFormikContext } from 'formik';
import DocumentManagementLocales from '@tutum/mvz/locales/en/DocumentManagement.json';
import PatientFormSelect from '@tutum/mvz/module_document-management/patient-form-select/PatientFormSelect.styled';
import SenderFormSelect from '@tutum/mvz/module_document-management/sender-form-select/SenderFormSelect.styled';
import DocumentTypeFormSelect from '@tutum/mvz/module_document-management/document-type-form-select/DocumentTypeFormSelect.styled';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import { DocumentManagementItem } from '@tutum/hermes/bff/legacy/document_management_common';
import RowInfo from '@tutum/mvz/module_document-management/row-info/RowInfo.styled';
import { useMemo } from 'react';

export interface IDocumentFormProps {
  t: IFixedNamespaceTFunction<keyof typeof DocumentManagementLocales>;
  className?: string;
  document?: DocumentManagementItem;
}
export const DocumentForm = ({
  t,
  className,
  document,
}: IDocumentFormProps) => {
  const { submitCount, errors, touched } = useFormikContext();

  const isGdtImport = useMemo(() => {
    return document?.documentType?.name === 'gdtimport';
  }, [document?.documentType?.name]);

  return (
    <div className={className}>
      <PatientFormSelect t={t} name="patient" />
      {isGdtImport ? (
        <RowInfo name={t('sender')} value={document?.gdtSenderName!} />
      ) : (
        <SenderFormSelect t={t} name="sender" />
      )}

      {isGdtImport ? (
        <RowInfo
          name={t('documentType')}
          value={t(
            document?.documentType
              ?.name as keyof typeof DocumentManagementLocales
          )}
        />
      ) : (
        <DocumentTypeFormSelect t={t} name="documentType" />
      )}

      <FormGroup2
        label={t('description')}
        name="description"
        submitCount={submitCount}
        errors={errors}
        touched={touched}
      >
        <Field name="description">
          {({ field, form }) => (
            <TextArea
              style={{ width: '100%' }}
              defaultValue={field.value}
              onBlur={(e) => {
                form.setFieldValue(field.name, e.target.value);
              }}
              placeholder={t('description')}
            />
          )}
        </Field>
      </FormGroup2>
    </div>
  );
};
