import React, { useCallback, useMemo } from 'react';
import {
  usePDF,
  Document,
  Page,
  View,
  Text,
  StyleSheet,
} from '@react-pdf/renderer';
import { COLOR } from '@tutum/design-system/themes/styles';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type PatientBillingLang from '@tutum/mvz/locales/en/PatientBilling.json';
import { Gender } from '@tutum/hermes/bff/patient_profile_common';
import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { isNil } from 'lodash';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import {
  BillingPatientPrintModel,
  ServiceInfo,
} from '@tutum/hermes/bff/billing_patient_common';
import { medicationUtil } from '@tutum/mvz/module_medication_kbv/utils/medication-util';
import { PrinterProfile } from '@tutum/hermes/bff/printer_common';
import { PRICE_EBM } from '@tutum/infrastructure/shared/price-format';

const styles = StyleSheet.create({
  page: {
    paddingLeft: '25mm',
    paddingBottom: '40mm',
    paddingTop: '40mm',
    paddingRight: '10mm',
    fontFamily: 'Courier',
    color: COLOR.TEXT_PRIMARY_BLACK,
    backgroundColor: COLOR.BACKGROUND_PRIMARY_WHITE,
    orientation: 'portrait',
  },
  textBlock: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  textNormal: {
    fontSize: 12,
    letterSpacing: -0.2,
  },
  tableContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
    paddingBottom: 4,
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableColDate: {
    width: '15.5%',
    paddingRight: '8px',
  },
  tableColServiceCode: {
    width: '14%',
    paddingRight: '8px',
  },
  tableColDescription: {
    width: '48.5%',
    paddingRight: '8px',
  },
  tableColPoints: {
    width: '10%',
    paddingRight: '8px',
  },
  tableColPrice: {
    width: '12%',
  },
  pageNumbers: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    textAlign: 'center',
  },
});

interface ServiceInfoExtend extends ServiceInfo {
  count: number;
}

export interface PrintPreviewContentProps {
  isOpen: boolean;
  data: BillingPatientPrintModel[];
  defaultPrinterProfile?: PrinterProfile;
  handlePrintCustomPdf?: (printerProfile: PrinterProfile) => void;
  onClosePrintPreview: () => void;
}

const TableColumns: Array<keyof typeof PatientBillingLang.PrintPreview.Table> =
  ['Date', 'ServiceCode', 'Description', 'Points', 'Price'];

const Table = ({
  serviceInfos,
  t,
}: {
  serviceInfos: ServiceInfoExtend[];
  t: IFixedNamespaceTFunction<
    keyof typeof PatientBillingLang.PrintPreview.Table
  >;
}) => {
  return (
    <View style={styles.tableContainer}>
      <View
        style={{
          ...styles.tableRow,
          paddingBottom: 4,
          borderBottom: `1px solid ${COLOR.TEXT_PRIMARY_BLACK}`,
        }}
      >
        {TableColumns.map((column, index) => {
          return (
            <Text
              key={`header-${index}`}
              style={{
                ...styles.textNormal,
                ...styles[`tableCol${column}`],
              }}
            >
              {t(column)}
            </Text>
          );
        })}
      </View>
      {serviceInfos?.map((info, index) => {
        const isOdd = index % 2 !== 0;

        return (
          <View
            key={index}
            style={{
              ...styles.tableRow,
              marginVertical: 4,
              backgroundColor: isOdd ? COLOR.BACKGROUND_SECONDARY_SHINE : '',
              borderBottom:
                index === serviceInfos.length - 1
                  ? `1px solid ${COLOR.TEXT_PRIMARY_BLACK}`
                  : '',
            }}
            wrap={false}
          >
            <Text
              style={{
                ...styles.textNormal,
                ...styles.tableColDate,
              }}
            >
              {!index || info.date !== serviceInfos[index - 1].date
                ? info.date
                : ''}
            </Text>
            <Text
              style={{
                ...styles.textNormal,
                ...styles.tableColServiceCode,
              }}
            >
              {info.count > 1 ? `${info.count}x ` : ''}
              {info.serviceCode}
            </Text>
            <Text
              style={{
                ...styles.textNormal,
                ...styles.tableColDescription,
              }}
              hyphenationCallback={(word) => {
                return [word];
              }}
            >
              {info.description}
            </Text>
            <Text
              style={{
                ...styles.textNormal,
                ...styles.tableColPoints,
                textAlign: 'right',
              }}
            >
              {info.points}
            </Text>
            <Text
              style={{
                ...styles.textNormal,
                ...styles.tableColPrice,
                textAlign: 'right',
              }}
            >
              {info.price
                ? medicationUtil.transformFormatNumber(info.price, 2, 2)
                : 0}
            </Text>
          </View>
        );
      })}
    </View>
  );
};

const PrintPreviewContent = ({
  isOpen,
  data,
  defaultPrinterProfile,
  handlePrintCustomPdf,
  onClosePrintPreview,
}: PrintPreviewContentProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof PatientBillingLang.PrintPreview
  >({
    namespace: 'PatientBilling',
    nestedTrans: 'PrintPreview',
  });

  const { t: tTable } = I18n.useTranslation<
    keyof typeof PatientBillingLang.PrintPreview.Table
  >({
    namespace: 'PatientBilling',
    nestedTrans: 'PrintPreview.Table',
  });

  const haveDocumentPercentage = useCallback(
    (datum: BillingPatientPrintModel) => {
      return !isNil(datum.qoute);
    },
    []
  );

  const textQuarter = useCallback((datum: BillingPatientPrintModel) => {
    const startDate = DatetimeUtil.dateTimeFormat(
      datum.periodStartDate,
      DATE_FORMAT
    );
    const endDate = DatetimeUtil.dateTimeFormat(
      datum.periodEndDate,
      DATE_FORMAT
    );

    return t('treatmentPeriod', {
      startDate,
      endDate,
    });
  }, []);

  const renderContent = useMemo(() => {
    return (
      <Document>
        {data.map((datum, index) => {
          const genderText =
            datum.patientGender === Gender.M
              ? t('male')
              : datum.patientGender === Gender.W
                ? t('female')
                : '';
          const nameText = `${datum.patientTitle || ''} ${(
            datum.patientFirstName || ''
          ).trim()}${datum.patientIntendWord ? ` ${datum.patientIntendWord}` : ''
            } ${(datum.patientLastName || '').trim()}`.trim();
          const practiceAddressCommaText =
            datum.practiceAddress.street || datum.practiceAddress.houseNumber
              ? ' •'
              : '';
          const practiceAddressText =
            `${datum.practiceAddress.street} ${datum.practiceAddress.houseNumber}${practiceAddressCommaText} ${datum.practiceAddress.postalCode} ${datum.practiceAddress.city}`.trim();
          const patientAddressCommaText =
            datum.patientAddress.street || datum.patientAddress.houseNumber
              ? '\n'
              : '';
          const patientAddressText =
            `${datum.patientAddress.street} ${datum.patientAddress.houseNumber}${patientAddressCommaText} ${datum.patientAddress.postalCode} ${datum.patientAddress.city}`.trim();
          const groupServiceInfos =
            datum.serviceInfos?.reduce(
              (groupServiceCode: ServiceInfoExtend[], serviceInfo) => {
                const existedServiceCodeIndex = groupServiceCode.findIndex(
                  (item) =>
                    item.serviceCode === serviceInfo.serviceCode &&
                    item.date === serviceInfo.date
                );

                if (existedServiceCodeIndex !== -1) {
                  groupServiceCode[existedServiceCodeIndex] = {
                    ...groupServiceCode[existedServiceCodeIndex],
                    count: groupServiceCode[existedServiceCodeIndex].count + 1,
                  };
                } else {
                  groupServiceCode.push({
                    ...serviceInfo,
                    count: 1,
                  });
                }

                return groupServiceCode;
              },
              []
            ) || [];

          return (
            <>
              <Page key={index} size="A4" style={styles.page} wrap>
                <View>
                  <View fixed>
                    <Text style={{ ...styles.textNormal, fontSize: 12 }}>
                      {datum.practiceName} • {practiceAddressText}
                    </Text>
                    <Text style={{ ...styles.textNormal, marginTop: 40 }}>
                      {genderText}
                    </Text>
                    <Text style={styles.textNormal}>{nameText}</Text>
                    <Text style={styles.textNormal}>{patientAddressText}</Text>
                    <Text style={{ ...styles.textNormal, marginTop: 20 }}>
                      {datum.insuranceName}
                    </Text>
                    {datum.insuranceNumber && (
                      <Text style={styles.textNormal}>
                        {t('insuranceNumber', {
                          insuranceNumber: datum.insuranceNumber,
                        })}
                      </Text>
                    )}
                    <Text
                      style={{
                        ...styles.textNormal,
                        alignSelf: 'flex-end',
                        marginTop: 20,
                      }}
                    >
                      {datum.creationDate}
                    </Text>
                    <Text
                      style={{
                        ...styles.textNormal,
                        marginTop: 20,
                        marginBottom: 40,
                        paddingBottom: 8,
                        fontFamily: 'Courier-Bold',
                        borderBottom: `1px solid ${COLOR.TEXT_PRIMARY_BLACK}`,
                      }}
                    >
                      {t('subject')}
                    </Text>
                  </View>
                  <Text style={{ ...styles.textNormal, marginBottom: 20 }}>
                    {t('introduction')}
                  </Text>
                  <Text style={{ ...styles.textNormal, marginBottom: 20 }}>
                    {haveDocumentPercentage(datum)
                      ? t('haveDocumentPercentage', {
                        quote: datum.qoute,
                      })
                      : t('noDocumentPercentage')}
                  </Text>
                  <Text style={{ ...styles.textNormal, marginBottom: 20 }}>
                    {textQuarter(datum)}
                  </Text>
                  <Text style={{ ...styles.textNormal, marginBottom: 20 }}>
                    {t('pointValue', {
                      pointValue: `${datum.pointValue
                        ? medicationUtil.transformFormatNumber(
                          datum.pointValue,
                          0,
                          4
                        )
                        : 0
                        } ${PRICE_EBM.symbol}`,
                    })}
                  </Text>
                  <Table serviceInfos={groupServiceInfos} t={tTable} />
                  <View style={{ ...styles.textBlock, marginBottom: 20 }}>
                    <Text style={styles.textNormal}>{t('totalPrice')}</Text>
                    <Text style={{ ...styles.textNormal, marginLeft: 25 }}>
                      {datum.totalPrice
                        ? medicationUtil.transformFormatNumber(
                          datum.totalPrice,
                          2,
                          2
                        )
                        : 0}
                    </Text>
                  </View>
                  {haveDocumentPercentage(datum) && (
                    <View style={{ ...styles.textBlock, marginBottom: 20 }}>
                      <Text style={styles.textNormal}>
                        {t('reimbursement')}
                      </Text>
                      <Text style={{ ...styles.textNormal, marginLeft: 20 }}>
                        {datum.reimbursement
                          ? medicationUtil.transformFormatNumber(
                            datum.reimbursement,
                            2,
                            2
                          )
                          : 0}
                      </Text>
                    </View>
                  )}
                </View>
                <Text
                  style={{
                    ...styles.pageNumbers,
                    ...styles.textNormal,
                    fontSize: 12,
                  }}
                  render={({ subPageNumber, subPageTotalPages }) => {
                    return `${subPageNumber} / ${subPageTotalPages}`;
                  }}
                  fixed
                />
              </Page>
            </>
          );
        })}
      </Document>
    );
  }, [haveDocumentPercentage, textQuarter]);

  const [instance] = usePDF({ document: renderContent });

  if (!isOpen) {
    return null;
  }

  return (
    <PrintPreviewPdfDialog
      isShowCancelBtn={false}
      titleText={t('title')}
      settingText={t('setting')}
      file={instance.url!}
      formId="Patient_Bill_Print_Preview"
      handlePrintCustomPdf={handlePrintCustomPdf}
      defaultPrinterProfile={defaultPrinterProfile}
      onClose={onClosePrintPreview}
    />
  );
};

export default PrintPreviewContent;
