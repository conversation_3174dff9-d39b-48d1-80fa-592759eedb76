import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Form as FormikForm, Formik, Field, FormikProps } from 'formik';
import { isEmpty } from 'lodash';

import type SdebmI18n from '@tutum/mvz/locales/en/Sdebm.json';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type ErrorI18n from '@tutum/mvz/locales/en/ErrorCode.json';

import {
  Button,
  Flex,
  LeaveConfirmModal,
  FormGroup2,
  Svg,
  alertError,
  H3,
  LoadingState,
} from '@tutum/design-system/components';
import FormUtils from '@tutum/infrastructure/utils/form.util';
import {
  Classes,
  Dialog,
  InputGroup,
  TextArea,
  Intent,
  Checkbox,
} from '@tutum/design-system/components/Core';
import { IMvzTheme } from '@tutum/mvz/theme';
import {
  MONTH_FULL_YEAR_FORMAT,
  YEAR_MONTH_DAY_FORMAT,
} from '@tutum/infrastructure/shared/date-format';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import I18n from '@tutum/infrastructure/i18n';
import NumberInput from '@tutum/design-system/components/NumberInput';
import { GoaDialogStyle } from './CreateUVGoaDialog.styled';
import {
  goaDataTransform,
  initialValues,
  onValidForm,
  parseInitValues,
} from './CreateUVGoaDialog.hepler';
import { SourceType } from '@tutum/hermes/bff/catalog_utils_common';
import { UvGoaCatalog } from '@tutum/hermes/bff/catalog_uv_goa_common';
import {
  UvGoaCatalogItem,
  useMutationCreateUvGoaCatalog,
  useMutationUpdateUvGoaCatalog,
  useMutationIsValidUpdateUvGoa,
  useQueryGetUvGoaCatalogByCode,
} from '@tutum/hermes/bff/legacy/app_mvz_catalog_uv_goa';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { ErrorCode } from '@tutum/hermes/bff/error_code';
import { DateInput } from '@tutum/design-system/components/DateTime';

const CalendarIcon = '/images/calendar-default.svg';

export interface ICreateGoaDialogProps {
  className?: string;
  theme?: IMvzTheme;
  isOpen: boolean | string;
  submitCount?: number;
  goaItem?: UvGoaCatalog;
  createUVGoaDefaultValue?: string;
  onClose: () => void;
  onSaveSuccess?: (sdebm: UvGoaCatalog) => void;
}

export interface IPatientInformationFormState {
  isSubmissionConfirmed: false;
  errors?: any;
}

const CreateUVGoa = ({
  submitCount = 0,
  className,
  isOpen,
  goaItem,
  createUVGoaDefaultValue,
  onClose,
  onSaveSuccess,
}: ICreateGoaDialogProps) => {
  const { t } = I18n.useTranslation<
    keyof typeof SdebmI18n.UVGoaOverview.CreateDialog
  >({
    namespace: 'Sdebm',
    nestedTrans: 'UVGoaOverview.CreateDialog',
  });
  const { t: tError } = I18n.useTranslation<keyof typeof ErrorI18n>({
    namespace: 'ErrorCode',
  });
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const [isConfirmOpen, setConfirmOpen] = useState<boolean>(false);
  const [initValues, setInitValues] = useState<UvGoaCatalogItem>({
    ...initialValues,
    uvGoa: {
      ...initialValues.uvGoa,
      code: createUVGoaDefaultValue ?? '',
    },
  });
  const isEdit = !!goaItem;

  const { mutate: editGoa, isPending: isEditing } =
    useMutationUpdateUvGoaCatalog({
      onSuccess: (res) => {
        onSaveSuccess?.(res.data.uvGoa);
      },
      onError: (error) => {
        alertError(error?.response?.data?.message || error.message);
      },
    });

  const { mutate: createGoa, isPending: isCreating } =
    useMutationCreateUvGoaCatalog({
      throwOnError: false,
      onSuccess: (res) => {
        onSaveSuccess?.(res.data.uvGoa);
      },
      onError: (createError) => {
        const message = createError.response?.data?.serverError
          ? tError(createError.response.data.serverError as any)
          : createError.message;
        alertError(message);
      },
    });
  const { data: selectedGoa, isFetching } = useQueryGetUvGoaCatalogByCode(
    {
      number: goaItem?.code || '',
    },
    {
      enabled: isEdit && !!goaItem?.code,
    }
  );
  const { mutateAsync: isValidUpdateUvGoa } = useMutationIsValidUpdateUvGoa();

  const isDisabled =
    isEdit && selectedGoa?.uvGoa.source !== SourceType.SelfCreated;

  useEffect(() => {
    if (selectedGoa) {
      const initValuesParser = parseInitValues(selectedGoa.uvGoa);
      setInitValues(initValuesParser);
    }
  }, [selectedGoa]);

  const onSaveForm = async (values: UvGoaCatalogItem) => {
    const value = goaDataTransform({
      ...values,
      uvGoa: {
        ...values.uvGoa,
      },
    });
    if (isEdit && selectedGoa) {
      const res = await isValidUpdateUvGoa(value);
      const error = res.data.errors;
      if (error && !isEmpty(error)) {
        alertError(tError(`${ErrorCode.ErrorCode_GOA_Existed}`));
        return;
      }
      editGoa(value);
    } else {
      createGoa(value);
    }
  };

  const formikRef = useRef<FormikProps<UvGoaCatalogItem>>(null);

  const handleClose = () => {
    if (formikRef.current?.dirty) {
      return setConfirmOpen(true);
    }
    onClose();
  };

  const showSaveButton = useMemo(() => {
    if (isFetching && isEdit) return false;
    return initValues?.uvGoa?.source === SourceType.SelfCreated;
  }, [initValues?.uvGoa?.source, isFetching, isEdit]);

  return (
    <>
      <GoaDialogStyle />
      <Dialog
        className={getCssClass('sl-create-goa-dialog')}
        title={isEdit ? t('titleEdit') : t('titleCreate')}
        isOpen={!!isOpen}
        canOutsideClickClose={false}
        onClose={handleClose}
        shouldReturnFocusOnClose
        autoFocus
      >
        <Flex auto column className={className}>
          <Formik<UvGoaCatalogItem>
            innerRef={formikRef}
            initialValues={initValues}
            onSubmit={onSaveForm}
            validate={onValidForm(t)}
            isInitialValid
            enableReinitialize
            validateOnBlur={false}
            validateOnChange={false}
          >
            {({ errors, touched }) => {
              return (
                <FormikForm className="sl-create-ebm-form">
                  <Flex className={Classes.DIALOG_BODY} column gap={16}>
                    {isFetching ? (
                      <LoadingState />
                    ) : (
                      <>
                        <Flex gap={16}>
                          <FormGroup2 name="uvGoa.isNotBillable">
                            <Field name="uvGoa.isNotBillable">
                              {({ field }) => {
                                return (
                                  <Checkbox
                                    label={t('notBillable')}
                                    value={field.value}
                                    {...field}
                                    disabled={isDisabled}
                                    checked={field.value}
                                  />
                                );
                              }}
                            </Field>
                          </FormGroup2>
                        </Flex>
                        <Flex gap={16}>
                          <FormGroup2
                            label={t('serviceCode')}
                            isRequired
                            name="uvGoa.code"
                            submitCount={submitCount}
                            errors={errors}
                            touched={touched}
                          >
                            <Field name="uvGoa.code">
                              {({ field, form }) => (
                                <InputGroup
                                  {...field}
                                  intent={FormUtils.getFormInputIntent(
                                    submitCount,
                                    !!touched.uvGoa?.code,
                                    errors.uvGoa?.code
                                  )}
                                  data-tab-id={field.name}
                                  autoFocus
                                  maxLength={13}
                                  disabled={isDisabled}
                                  onChange={(e) => {
                                    const value =
                                      e.target.value.replace(/^0+/, '') || '';
                                    form.setFieldValue(field.name, value);
                                  }}
                                />
                              )}
                            </Field>
                          </FormGroup2>
                          <FormGroup2
                            className="flex-1"
                            label={t('shortDescription')}
                            isRequired
                            name="uvGoa.description"
                            submitCount={submitCount}
                            errors={errors}
                            touched={touched}
                          >
                            <Field name="uvGoa.description">
                              {({ field, form }) => (
                                <InputGroup
                                  {...field}
                                  intent={FormUtils.getFormInputIntent(
                                    submitCount,
                                    !!touched.uvGoa?.description,
                                    errors.uvGoa?.description
                                  )}
                                  data-tab-id={field.name}
                                  maxLength={40}
                                  disabled={isDisabled}
                                  onChange={(e) => {
                                    form.setFieldValue(
                                      field.name,
                                      e.target.value
                                    );
                                  }}
                                />
                              )}
                            </Field>
                          </FormGroup2>
                        </Flex>
                        <FormGroup2
                          label={t('description')}
                          name="uvGoa.longDescription"
                          submitCount={submitCount}
                          errors={errors}
                          touched={touched}
                        >
                          <Field name="uvGoa.longDescription">
                            {({ field }) => {
                              return (
                                <TextArea
                                  {...field}
                                  className={Classes.FILL}
                                  rows={4}
                                  maxLength={4000}
                                  disabled={isDisabled}
                                  growVertically={false}
                                />
                              );
                            }}
                          </Field>
                        </FormGroup2>
                        <Flex gap={16}>
                          <FormGroup2
                            className="flex-1"
                            label={t('validFrom')}
                            name="uvGoa.validity.fromDate"
                            submitCount={submitCount}
                            errors={errors}
                            isRequired
                            touched={touched}
                          >
                            <Field name="uvGoa.validity.fromDate">
                              {({ field, form }) => {
                                const { setFieldValue } = form;
                                const value = field.value
                                  ? datetimeUtil.dateTimeNumberFormat(
                                      field.value,
                                      YEAR_MONTH_DAY_FORMAT
                                    )
                                  : null;
                                const setDatetimeValue = (
                                  dateValue: string
                                ) => {
                                  setFieldValue(
                                    field.name,
                                    +datetimeUtil.getFirstDayOfMonth(+dateValue)
                                  );
                                };
                                return (
                                  <DateInput
                                    value={value}
                                    formatDate={(date) =>
                                      datetimeUtil.dateTimeFormat(
                                        date,
                                        MONTH_FULL_YEAR_FORMAT
                                      )
                                    }
                                    parseDate={(str) =>
                                      datetimeUtil.strToDate(
                                        str,
                                        MONTH_FULL_YEAR_FORMAT
                                      )
                                    }
                                    inputProps={{
                                      leftElement: <Svg src={CalendarIcon} />,
                                    }}
                                    minDate={new Date(1970, 1, 1)}
                                    placeholder={MONTH_FULL_YEAR_FORMAT}
                                    data-test-id={field.name}
                                    disabled={isDisabled}
                                    onChange={setDatetimeValue}
                                  />
                                );
                              }}
                            </Field>
                          </FormGroup2>
                          <FormGroup2
                            className="flex-1"
                            label={t('validTo')}
                            name="uvGoa.validity.toDate"
                            submitCount={submitCount}
                            errors={errors}
                            touched={touched}
                          >
                            <Field name="uvGoa.validity.toDate">
                              {({ field, form }) => {
                                const { setFieldValue } = form;
                                const value = field.value
                                  ? datetimeUtil.dateTimeNumberFormat(
                                      field.value,
                                      YEAR_MONTH_DAY_FORMAT
                                    )
                                  : null;
                                const setDatetimeValue = (
                                  dateValue: string
                                ) => {
                                  setFieldValue(
                                    field.name,
                                    +datetimeUtil.getFirstDayOfMonth(+dateValue)
                                  );
                                };
                                return (
                                  <DateInput
                                    value={value}
                                    formatDate={(date) =>
                                      datetimeUtil.dateTimeFormat(
                                        date,
                                        MONTH_FULL_YEAR_FORMAT
                                      )
                                    }
                                    parseDate={(str) =>
                                      datetimeUtil.strToDate(
                                        str,
                                        MONTH_FULL_YEAR_FORMAT
                                      )
                                    }
                                    inputProps={{
                                      leftElement: <Svg src={CalendarIcon} />,
                                    }}
                                    placeholder={MONTH_FULL_YEAR_FORMAT}
                                    data-test-id={field.name}
                                    disabled={isDisabled}
                                    onChange={setDatetimeValue}
                                  />
                                );
                              }}
                            </Field>
                          </FormGroup2>
                        </Flex>
                        <Flex column>
                          <Flex column w="100%" gap={16}>
                            <H3 className="label-section-patient-form">
                              {t('evaluation')}
                            </H3>
                            <Flex gap={16}>
                              <FormGroup2
                                className="flex-1"
                                label={t('generalTreatment')}
                              >
                                <Field name="uvGoa.generalTreatmentEvaluation">
                                  {({ form, field }) => {
                                    return (
                                      <NumberInput
                                        isFloat
                                        allowLeadingZeros
                                        placeholder={t('placeHolder')}
                                        data-tab-id={field.name}
                                        defaultValue={field.value}
                                        intent={FormUtils.getFormInputIntent(
                                          submitCount,
                                          !!touched.uvGoa
                                            ?.generalTreatmentEvaluation,
                                          errors.uvGoa
                                            ?.generalTreatmentEvaluation
                                        )}
                                        disabled={isDisabled}
                                        maxLength={10}
                                        rightElement={
                                          <p className="sl-input-right-element">
                                            {t('unit')}
                                          </p>
                                        }
                                        onValueChange={({ value }) =>
                                          form.setFieldValue(
                                            field.name,
                                            +value || null
                                          )
                                        }
                                      />
                                    );
                                  }}
                                </Field>
                              </FormGroup2>

                              <FormGroup2
                                className="flex-1"
                                label={t('specificTreatment')}
                              >
                                <Field name="uvGoa.specificTreatmentEvaluation">
                                  {({ field, form }) => (
                                    <NumberInput
                                      isFloat
                                      allowLeadingZeros
                                      placeholder={t('placeHolder')}
                                      data-tab-id={field.name}
                                      defaultValue={field.value}
                                      intent={FormUtils.getFormInputIntent(
                                        submitCount,
                                        !!touched.uvGoa
                                          ?.specificTreatmentEvaluation,
                                        errors.uvGoa
                                          ?.specificTreatmentEvaluation
                                      )}
                                      disabled={isDisabled}
                                      maxLength={10}
                                      rightElement={
                                        <p className="sl-input-right-element">
                                          {t('unit')}
                                        </p>
                                      }
                                      onValueChange={({ value }) =>
                                        form.setFieldValue(
                                          field.name,
                                          +value || null
                                        )
                                      }
                                    />
                                  )}
                                </Field>
                              </FormGroup2>
                            </Flex>
                            <Flex gap={16} align="center">
                              <FormGroup2
                                className="flex-1"
                                label={t('generalCosts')}
                              >
                                <Field name="uvGoa.generalCosts">
                                  {({ field, form }) => (
                                    <NumberInput
                                      isFloat
                                      allowLeadingZeros
                                      placeholder={t('placeHolder')}
                                      data-tab-id={field.name}
                                      defaultValue={field.value}
                                      intent={FormUtils.getFormInputIntent(
                                        submitCount,
                                        !!touched.uvGoa?.generalCost,
                                        errors.uvGoa?.generalCost
                                      )}
                                      disabled={isDisabled}
                                      maxLength={10}
                                      rightElement={
                                        <p className="sl-input-right-element">
                                          {t('unit')}
                                        </p>
                                      }
                                      onValueChange={({ value }) =>
                                        form.setFieldValue(
                                          field.name,
                                          +value || null
                                        )
                                      }
                                    />
                                  )}
                                </Field>
                              </FormGroup2>
                              <FormGroup2
                                className="flex-1"
                                label={t('specialCosts')}
                              >
                                <Field name="uvGoa.specialCosts">
                                  {({ field, form }) => (
                                    <NumberInput
                                      isFloat
                                      allowLeadingZeros
                                      placeholder={t('placeHolder')}
                                      data-tab-id={field.name}
                                      defaultValue={field.value}
                                      intent={FormUtils.getFormInputIntent(
                                        submitCount,
                                        !!touched.uvGoa?.materialCost,
                                        errors.uvGoa?.materialCost
                                      )}
                                      disabled={isDisabled}
                                      maxLength={10}
                                      rightElement={
                                        <p className="sl-input-right-element">
                                          {t('unit')}
                                        </p>
                                      }
                                      onValueChange={({ value }) =>
                                        form.setFieldValue(
                                          field.name,
                                          +value || null
                                        )
                                      }
                                    />
                                  )}
                                </Field>
                              </FormGroup2>
                            </Flex>
                          </Flex>
                        </Flex>
                      </>
                    )}
                  </Flex>
                  <Flex
                    auto
                    className={Classes.DIALOG_FOOTER}
                    justify="flex-end"
                    gap={16}
                  >
                    <Button
                      large
                      type="button"
                      minimal
                      outlined
                      intent={Intent.PRIMARY}
                      onClick={handleClose}
                    >
                      {tButtonActions('cancelText')}
                    </Button>
                    {initValues &&
                      initValues.uvGoa.source === SourceType.SelfCreated && (
                        <Button
                          large
                          type="submit"
                          intent={Intent.PRIMARY}
                          loading={isEditing || isCreating}
                        >
                          {isEdit
                            ? tButtonActions('saveText')
                            : tButtonActions('create')}
                        </Button>
                      )}
                  </Flex>
                </FormikForm>
              );
            }}
          </Formik>
        </Flex>
      </Dialog>
      <LeaveConfirmModal
        isOpen={isConfirmOpen}
        onConfirm={() => {
          onClose();
          setConfirmOpen(false);
        }}
        onClose={() => setConfirmOpen(false)}
      />
    </>
  );
};

export default CreateUVGoa;
