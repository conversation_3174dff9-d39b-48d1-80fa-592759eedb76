import DatetimeUtil from '@tutum/infrastructure/utils/datetime.util';

export const START_QUARTER = DatetimeUtil.getStartOfQuarter(
  DatetimeUtil.now()
).toDate();

export const END_QUARTER = DatetimeUtil.getEndOfQuarter(
  DatetimeUtil.now()
).toDate();

import { PRICE_EBM } from '@tutum/infrastructure/shared/price-format';

export const getPointValue = (evaluation: number) => {
  return Intl.NumberFormat('de-DE').format(evaluation);
};

export const getEvaluation = (val: number) => {
  return Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR',
    maximumFractionDigits: 2,
  }).format(val);
};

export default { START_QUARTER, PRICE_EBM };
